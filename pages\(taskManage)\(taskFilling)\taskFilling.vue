<!-- 任务填报页 -->
<script setup lang="ts">
    import { useRouter, useRoute } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import { taskFilling, userOrgList, deletes, policySwitch } from "@/api/task";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    import useTableConfig from '@/composables/useTableConfig';

    definePageMeta({
        title: '任务填报'
    })
    const router = useRouter();
    const route = useRoute();
    const modal = inject('modal') as any;
    const message = inject('message') as any;
    const tableDate = ref<any[]>([]);

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    const task_state_options = ref([
        { value: "1", label: "任务填报中" },
        { value: "4", label: "需求审核退回" },
    ]);

    const policySwitchs = ref(0);

    // 添加 loading 变量
    const loading = ref(false);

    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input'
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: (value: string) => getCreatedName(value),
                    clearFields: ['applyOrgId']
                }
            },
            {
                label: '归属机构',
                field: 'applyOrgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input'
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select',
                compConfig: {
                    options: task_state_options.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 获取列表数据
    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await taskFilling({
                appNo: params.appNo,
                applyOrgId: params.applyOrgId,
                businessLine: params.businessLine,
                createdName: params.createdName,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                createdTimeStr: params.createdTimeStr,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取任务列表失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLine_option.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLine_option.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };

    const getCreatedName = (value: string) => {
        let name = "";
        businessLine_option.value.map((i) => {
            if (i.code === value) {
                name = i.name;
            }
        });
        userOrgList({
            roleName: "DEMAND_ROLE_DECLARE",
            businessCode: value,
        }).then((res: any) => {
            model_type_options.value = res.data;
            // 更新搜索配置中的归属机构选项
            const applyOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'applyOrgId');
            if (applyOrgField && applyOrgField.compConfig) {
                // 先清空数组
                applyOrgField.compConfig.options = [];
                if(model_type_options.value){
                    // 使用新数组替换原有options，保证响应式更新
                    const newOptions = model_type_options.value.map(item => ({
                        name: item.orgName,
                        value: item.id
                    }));
                    // 使用nextTick确保DOM更新后再填充数据
                    nextTick(() => {
                        applyOrgField.compConfig.options = [...newOptions];
                    });
                }
            }
        });
    };


    const handleSearch = (formValue: any) => {
        listLayout.value?.refresh();
    };

    const getPolicySwitch = () => {
        policySwitch().then((res: any) => {
            policySwitchs.value = res.data;
        });
    };

    const openDelete = (row: any) => {
        modal.confirm({
            title: '温馨提示',
            content: '您确定删除当前任务吗?',
            okText: '确定',
            cancelText: '取消',
            type: 'warning',
            onOk() {
                deletes({ uuid: row.uuid }).then((res: any) => {
                    if (res.code == 20000) {
                        message.success('删除成功');
                        listLayout.value?.refresh();
                    } else {
                        message.error(res.data);
                    }
                });
            },
            onCancel() {
                message.info('已取消')
            },
        });
    };

    const addInfo = () => {
        navigateTo('taskAdd')
    };

    onMounted(() => {
        getOption();
        getPolicySwitch();
    });

    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type: string, record: any) => {
        modalType.value = type;
        if (type === 'info') {
            datar.value = {
                type: type,
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        };
        if (type === 'update') {
            datar.value = {
                type: type,
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                file: record.file,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                createdId: record.createdId,
                createdName: record.createdName,
            };
        };
        isModalVisible.value = true;
    };

    const close = (flag: any) => {
        if(flag){//如果是数据提交，则刷新表单
            listLayout.value?.refresh();
        }
        isModalVisible.value = false;
    }

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: handleSearch,
        // 添加事件
        addNewEvent: addInfo,
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchTaskList
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: any) => {
        const items = [
            {
                key: 'update',
                label: '更新',
                onClick: () => showModal('update', record)
            }
        ];

        if (policySwitchs.value !== 1) {
            items.push({
                key: 'delete',
                label: '删除',
                onClick: () => openDelete(record)
            });
        }

        return items;
    };

    // 组件引用
    const listLayout = ref(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];
</script>

<template>
    <ListPage
        ref="listLayout"
        title="任务填报"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="policySwitchs !== 1"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('info',record)">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'任务填报'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
