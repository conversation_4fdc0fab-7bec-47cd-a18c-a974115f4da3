<template>
  <span class="actionTypeCom color-red">
    <span v-if="locked" class="actionTypeItem txtItem">{{ txt }}</span>
    <a-cascader
      v-else
      v-bind="option"
      @change="onSelectChange"
    >
      <span class="actionTypeItem txtItem">{{ txt }}</span>
    </a-cascader>
  </span>
</template>

<script setup>
import { findLabel } from "@/components/ruleEditCom/utils/displayUtil.js";

const props = defineProps({
  value: String,
  pos: String,
  locked: Boolean
});

const emit = defineEmits(['onChange']);

const selfData = ref([
  {
    value: "equals",
    label: "等于"
  },
  {
    value: "fromIn",
    label: "在<集合>之中"
  }
]);

const txt = ref("");

watch(
  () => props.value,
  (newValue) => {
    txt.value = newValue === "equals"
      ? findLabel(selfData.value, [newValue])
      : findLabel(selfData.value, [newValue]).substr(0, 1);
  },
  { immediate: true } // 立即执行一次
);

const option = computed(() => ({
  options: selfData.value,
  expandTrigger: "hover",
  value: [props.value]
}));

const onSelectChange = (_value, selectedOptions) => {
  emit("onChange", props.pos, selectedOptions);
};
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
