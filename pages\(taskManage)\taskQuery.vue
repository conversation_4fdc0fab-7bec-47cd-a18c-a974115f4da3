<!-- 任务查询页 -->
<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import { getBaseLine } from '@/api/baseline_Management';
    import { taskQuery, userOrgList } from '@/api/task';
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    import { nextTick } from 'vue';

    definePageMeta({
        title: '任务查询'
    })
    const router = useRouter();
    const message = inject('message');

    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);
    const task_state_options = ref(
        Object.entries(TASK_STATE).map(([value, label]) => ({
            value,
            label,
        }))
    );

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await taskQuery({
                appNo: params.appNo,
                businessLine: params.businessLine,
                createdName: params.createdName,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                applyOrgId: params.applyOrgId,
                orgId: params.orgId,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error?.('获取数据失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getCreatedName = (value: string) => {
        // 手动触发搜索配置中归属机构和审核机构选择器的值更新
        const applyOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'applyOrgId');
        if (applyOrgField && applyOrgField.compConfig) {
            // 先清空选项
            applyOrgField.compConfig.options = [];
        }

        const orgIdField = searchConfig.advancedSearchFields.find(field => field.field === 'orgId');
        if (orgIdField && orgIdField.compConfig) {
            // 先清空选项
            orgIdField.compConfig.options = [];
        }

        userOrgList({
            roleName: 'DEMAND_ROLE_QUERY',
            businessCode: value,
        }).then((res: any) => {
            model_type_options.value = res.data;

            // 更新搜索配置中的归属机构选项
            if (applyOrgField && applyOrgField.compConfig && model_type_options.value) {
                // 使用新数组替换原有options，保证响应式更新
                const newOptions = model_type_options.value.map(item => ({
                    name: item.orgName,
                    value: item.id
                }));
                // 使用nextTick确保DOM更新后再填充数据
                nextTick(() => {
                    applyOrgField.compConfig.options = [...newOptions];
                });
            }

            // 更新搜索配置中的审核机构选项
            if (orgIdField && orgIdField.compConfig && model_type_options.value) {
                // 使用新数组替换原有options，保证响应式更新
                const newOptions = model_type_options.value.map(item => ({
                    name: item.orgName,
                    value: item.id
                }));
                // 使用nextTick确保DOM更新后再填充数据
                nextTick(() => {
                    orgIdField.compConfig.options = [...newOptions];
                });
            }
        });
    };

    //显示详情对话框
    const datar = ref<Record<string, any>>({});
    const isModalVisible = ref<boolean>(false);

    const showModal = (type: string, record: Record<string, any> = {}) => {
        datar.value = {
            type: type,
            uuid: record.uuid,
            appNo: record.appNo,
            bizKey: record.bizKey,
            businessCode: record.businessLine,
            createdTime: record.createdTime,
        };
        isModalVisible.value = true;
    };

    const close = (flag: boolean) => {
        if(flag){//如果是数据提交，则刷新表单
            listLayout.value?.refresh();
        }
        isModalVisible.value = false;
    }

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input'
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: getCreatedName,
                    clearFields: ['applyOrgId', 'orgId']
                }
            },
            {
                label: '归属机构',
                field: 'applyOrgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input'
            },
            {
                label: '审核机构',
                field: 'orgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select',
                compConfig: {
                    options: task_state_options.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: () => {},
        // 添加事件（任务查询页面不需要添加功能，但必须提供这个字段）
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchTaskList
        }
    };

    // 组件引用
    const listLayout = ref(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];

    onMounted(() => {
        getOption();
    });

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLine_option.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLine_option.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };
</script>

<template>
    <ListPage
        ref="listLayout"
        title="任务查询"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :customRenderColumns="customRenderColumns"
        :showAddButton="false"
        :showActionColumn="false"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('Announcement',record)" style="height: 30px">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state as keyof typeof TASK_STATE] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'任务查询'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
