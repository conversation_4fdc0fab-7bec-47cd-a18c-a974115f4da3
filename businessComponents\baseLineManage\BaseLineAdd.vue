<!--基线新增页-->

<script setup lang="ts">
 import {getRuleSel, getBaseLineadd, getInCompleteRuleForEng} from "@/api/baseline_Management";
 import { getLoginType } from "@/api/userManagement";
 import { pubEnvUUid } from "@/api/pub_environment";
 const message = inject('message')
 const modal = inject('modal')
 const emit = defineEmits(['changeSubmitLoading']);
 interface RuleForm {
    ruleName: string;
    isMajor: string;
    isPublish: string;
    descs: string;
    env: string;
    isTimePublish: string;
    taskTime: string;
}
const ruleFormRef = ref();
const ruleForm = reactive<RuleForm>({
    ruleName: '',
    isMajor: '',
    isPublish: '',
    descs: '',
    env: '',
    isTimePublish: '',
    taskTime: '',
});
const pubArr = [
    { name: '是', id: '1' },
    { name: '否', id: '0' },
];
const dataEnv = ref(false);
const EnvText = ref(false);
const obj = ref<any>(null);
const strEnv = ref('');
const envArr = ref<any[]>([]);
const ruleArrsel = ref<any[]>([]);
const loginType = ref('');
const ifTimePublishShow = ref(false);
const pubTimeShow = ref(false);
const pubDate = ref('');
const pubTime = ref('');
const rule = reactive({
    ruleName: [{ required: true, message: '请选择规则库名称', trigger: 'change' }],
    isMajor: [{ required: true, message: '请选择基线版本', trigger: 'change' }],
    isPublish: [{ required: true, message: '请选择是否发布', trigger: 'change' }],
    env: [{ required: true, message: '请选择发布环境', trigger: 'change' }],
    descs: [
        {
            min: 1,
            max: 500,
            message: '长度在 1 到 500 个字符',
            trigger: 'change',
        },
    ],
});
 const getRuleInfosel = () => {
     getRuleSel().then((res) => {
         const choseRule = {
             chineseName: '请选择',
             uuid: '',
         };
         res.data.unshift(choseRule);
         ruleArrsel.value = res.data;
     });
 };
 const getLoginTypes = () => {
     getLoginType().then((res) => {
         loginType.value = res.data;
     });
 };
 const selectGet = (vid: string) => {
     obj.value = ruleArrsel.value.find((item) => item.uuid === vid);
     ruleForm.ruleName = obj.value.uuid;

     if (strEnv.value === '1') {
         ruleForm.env = '';
         const pars = {
             engUuid: ruleForm.ruleName,
         };
         pubEnvUUid(pars).then((res) => {
             envArr.value = res.data;
         });
     }
 };
 const selEnv = (vid: string) => {
     const pars = {
         engUuid: ruleForm.ruleName,
     };
     obj.value = pubArr.find((item) => item.id === vid);
     strEnv.value = obj.value.id;

     if (strEnv.value === '0' && ruleForm.ruleName === '') {
         dataEnv.value = false;
         ifTimePublishShow.value = false;
         pubTimeShow.value = false;
         ruleForm.isTimePublish = '';
         message.warning('请选择规则库');
     } else if (strEnv.value === '0' && ruleForm.ruleName !== '') {
         dataEnv.value = false;
         ifTimePublishShow.value = false;
         pubTimeShow.value = false;
         ruleForm.isTimePublish = '';
         delete rule.env;
     } else {
         pubEnvUUid(pars).then((res) => {
             envArr.value = res.data;
             dataEnv.value = true;
             ifTimePublishShow.value = true;
         });
     }
 };
 const submitForm = (callback) => {
     ruleFormRef.value.validate().then(() => {
         const data = {
             descs: ruleForm.descs,
             engUuid: ruleForm.ruleName,
             isMajor: ruleForm.isMajor,
             isPublish: ruleForm.isPublish,
             environmentId: ruleForm.env,
             isTimePublish: ruleForm.isTimePublish,
             taskTime: `${pubDate.value} ${pubTime.value}:00`,
         };

         if (ruleForm.isTimePublish === '1') {
             if (!pubDate.value || !pubTime.value) {
                 message.error('请选择定时发布时间！');
                 return;
             }
         }
         emit('changeSubmitLoading',true);
         if (ruleForm.isPublish === '1') {
             if (loginType.value === '04') {
                 const param = {
                     engUuid: ruleForm.ruleName,
                 };

                 getInCompleteRuleForEng(param).then((res) => {
                     if (res && res.data > 0) {
                         modal.confirm({
                             title: '提示',
                             content: '规则库中存在未完成的规则，请确认是否发布？',
                             okText: '发布',
                             cancelText: '取消',
                             type: 'warning',
                         }).then(() => {
                             save(data);
                             emit('changeSubmitLoading',false);
                             if (typeof callback === 'function') {
                                 callback();
                             }
                         }).catch(() => {
                             emit('changeSubmitLoading',false);
                             message.info('已取消');
                         });
                     } else {
                         save(data);
                         emit('changeSubmitLoading',false);
                         if (typeof callback === 'function') {
                             callback();
                         }
                     }
                 });
             } else {
                 save(data);
                 emit('changeSubmitLoading',false);
                 if (typeof callback === 'function') {
                     callback();
                 }
             }
         } else {
             save(data);
             emit('changeSubmitLoading',false);
             if (typeof callback === 'function') {
                 callback();
             }
         }
     }).catch(() => {
         message.error('表单验证失败');
     });
 };
 const save = (data: any) => {
     getBaseLineadd(data).then((res) => {
         if (res.code === 20000){}
         message.success('新增成功');
     });
 };
 const selPub = (data: string) => {
     pubTimeShow.value = data === '1';
 };
 const filterOption = (input: string, option: any) => {
     const label = ruleArrsel.value.find(item => item.uuid === option.value)?.chineseName || '';
     // 根据label来过滤
     return label.toLowerCase().includes(input.toLowerCase());
 }

 // 重置表单数据
 const resetForm = () => {
     // 重置表单数据
     Object.keys(ruleForm).forEach(key => {
         ruleForm[key] = '';
     });
     // 重置其他相关状态
     dataEnv.value = false;
     ifTimePublishShow.value = false;
     pubTimeShow.value = false;
     strEnv.value = '';
     pubDate.value = '';
     pubTime.value = '';
     
     // 如果表单引用存在，使用内置的resetFields方法重置验证状态
     if (ruleFormRef.value) {
         ruleFormRef.value.resetFields();
     }
 };

 onMounted(()=>{
     getRuleInfosel();
     getLoginTypes();
 });
 defineExpose({
    submitForm,
    resetForm,
});
</script>
<template>
    <div id="add">
        <a-form :model="ruleForm" ref="ruleFormRef"  :rules="rule" label-align="right" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="规则库名称：" name="ruleName">
                <a-select v-model:value="ruleForm.ruleName" show-search :filter-option="filterOption" placeholder="请选择" @change="selectGet">
                    <a-select-option
                            v-for="item in ruleArrsel"
                            :key="item.uuid"
                            :value="item.uuid"
                    >
                        {{ item.chineseName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="基线版本：" name="isMajor">
                <a-select v-model:value="ruleForm.isMajor" placeholder="请选择">
                    <a-select-option value="1">主要版本</a-select-option>
                    <a-select-option value="0">次要版本</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="是否发布：" name="isPublish">
                <a-select v-model:value="ruleForm.isPublish" placeholder="请选择" @change="selEnv">
                    <a-select-option
                            v-for="item in pubArr"
                            :key="item.id"
                            :value="item.id"
                    >
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="发布环境：" name="env" v-show="dataEnv">
                <a-select v-model:value="ruleForm.env" placeholder="请选择">
                    <a-select-option
                            v-for="item in envArr"
                            :key="item.id"
                            :value="item.id"
                    >
                        {{ item.environmentName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="是否定时发布：" name="isTimePublish" v-show="ifTimePublishShow">
                <a-select v-model:value="ruleForm.isTimePublish" placeholder="请选择" @change="selPub">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="发布时间：" name="taskTime" v-show="pubTimeShow">
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                            v-model:pubDate="pubDate"
                            v-model:pubTime="pubTime"
                    />
                </div>
            </a-form-item>
            <a-form-item label="基线描述：" name="descs">
                <a-textarea
                        v-model:value="ruleForm.descs"
                        :auto-size="{ minRows: 2, maxRows: 8 }"
                        :maxLength="256"
                />
            </a-form-item>
        </a-form>
    </div>
</template>
