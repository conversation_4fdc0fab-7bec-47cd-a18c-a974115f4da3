.djs-container {
  background-image: linear-gradient(90deg,rgba(200,200,200,0.15) 10%,rgba(0,0,0,0) 10%), linear-gradient(rgba(200,200,200,0.15) 10%,rgba(0,0,0,0) 10%);
  background-size: 10px 10px;
}
.djs-connection path {
  width: 100px;
  stroke: #9e9e9e !important;
  marker-end: url(#sequenceflow-arrow-normal) !important;
}
.djs-connection.selected path {
  stroke: #2196f3 !important;
  marker-end: url(#sequenceflow-arrow-active) !important;
}
.djs-drag-active path {
  stroke: #9e9e9e !important;
  marker-end: url(#sequenceflow-arrow-normal) !important;
}
#sequenceflow-arrow-normal {
  fill: #9e9e9e;
  stroke: #9e9e9e;
}
#sequenceflow-arrow-active {
  fill: #2196f3;
  stroke: #2196f3;
}
.djs-palette {
  top: 80px !important;
}
.bpmn-icon-start-event-none.dir-shape:after, .bpmn-icon-gateway-or.dir-shape:after, .bpmn-icon-business-rule.dir-shape:after, .bpmn-icon-gateway-xor.dir-shape:after, .bpmn-icon-end-event-none.dir-shape:after, .bpmn-icon-subflow-event-none.dir-shape:after, .icon-subflow:after, .icon-start:after, .icon-end:after, .icon-policy:after, .icon-convergence:after, .icon-task:after {
  font-size: 12px;
  display: block;
  height: 12px;
  line-height: 12px;
  position: relative;
  top: -6px;
}
.icon-task:after, .icon-subflow:after {
  top: -13px;
}
.bpmn-icon-start-event-none.dir-shape:after, .icon-start.dir-shape:after {
  content: '开始';
}
.bpmn-icon-gateway-or.dir-shape:after, .icon-policy.dir-shape:after {
  content: '决策';
}
.bpmn-icon-business-rule.dir-shape:after, .icon-task.dir-shape:after {
  content: '规则';
}
.bpmn-icon-gateway-xor.dir-shape:after, .icon-convergence.dir-shape:after {
  content: '汇聚';
}
.bpmn-icon-end-event-none.dir-shape:after, .icon-end.dir-shape:after {
  content: '结束';
}
.bpmn-icon-subflow-event-none.dir-shape:after, .icon-subflow.dir-shape:after {
  content: '子流';
}
.entry.dir-shape {
  margin-bottom: 12px;
}
.djs-palette .entry.dir-tool {
  margin-bottom: 0;
  width: 40px;
  height: 40px;
  line-height: 40px;
  position: relative;
  left: 3px;
}
.djs-palette-entries {
  padding-bottom: 5px;
}
.djs-context-pad .entry {
  box-sizing: border-box;
  background-size: 94%;
  transition: all 0.3s;
  margin-right: 5px;
}
.djs-context-pad .entry.bpmn-icon-gateway-none, .djs-context-pad .entry.bpmn-icon-task, .djs-context-pad .entry.bpmn-icon-intermediate-event-none, .djs-context-pad .entry.bpmn-icon-text-annotation, .djs-context-pad .entry.bpmn-icon-screw-wrench, .djs-context-pad .entry.bpmn-icon-trash {
  display: none;
}
.djs-context-pad .entry.iconfont.icon-task {
  position: relative;
  top: -5px;
}
.djs-context-pad .entry.iconfontflow.icon-subflow {
  font-size: 20px;
  color: #000;
}
.djs-context-pad .entry.bpmn-icon-trash.dir {
  display: block;
  position: absolute;
  top: 66px;
  right: 0;
}
.djs-context-pad {
  width: 60px;
}
.djs-context-pad .group {
  margin-bottom: 8px;
}
.highlight .djs-visual > :nth-child(1) {
  stroke: green !important;
}
