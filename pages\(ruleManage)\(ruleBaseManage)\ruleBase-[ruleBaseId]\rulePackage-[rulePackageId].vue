<script setup lang="ts">
import RuleBaseContent from '@/businessComponents/ruleBaseManage/RuleBaseContent.vue'

const route = useRoute()
// 从父组件获取ruleSidebarRef，添加类型断言
const ruleSidebarRef = inject('ruleSidebarRef') as Record<string, any>;

// 添加对RuleBaseContent组件的引用
const ruleBaseContentRef = ref<Record<string, any>>({});

// 获取路由和标题更新方法
const updateRouteTitle = inject('updateRouteTitle') as (path: string, title: string) => void;

// 监听ruleBaseContentRef的变化，获取其中的rulePackageName值
watchEffect(() => {
    if (ruleBaseContentRef.value && ruleBaseContentRef.value.rulePackageName) {
        // 更新面包屑标题
        if (updateRouteTitle && route.query.backFlag !== 'dashboard') {
            updateRouteTitle(route.path, ruleBaseContentRef.value.rulePackageName);
        } else {
            //console.error('未找到updateRouteTitle方法');
        }
    }
});
</script>

<template>
    <!-- 加KeepAlive是为了打开规则详情或规则内容编辑页再返回时，不重新加载组件，以便能保持住表格分页等状态 -->
    <KeepAlive>
        <NuxtPage v-if="route.params.ruleId" />
        <RuleBaseContent
            v-else
            ref="ruleBaseContentRef"
            :rule-sidebar-ref="ruleSidebarRef"
        />
    </KeepAlive>
</template>
