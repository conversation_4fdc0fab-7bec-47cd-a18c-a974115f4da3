/**
 * 规则标签页相关常量
 */

// 标签页类型
export const TAB_TYPE = {
    EDIT: 'edit',
    DETAIL: 'detail',
    DESIGN: 'designView'
} as const;

// 标签页前缀
export const TAB_PREFIX = {
    EDIT: '编辑',
    DETAIL: '详情'
} as const;

// 标签页标题格式
export const TAB_TITLE_FORMAT = {
    EDIT: (ruleName: string) => `${TAB_PREFIX.EDIT}(${ruleName})`,
    DETAIL: (ruleName: string) => `${TAB_PREFIX.DETAIL}(${ruleName})`
} as const;

// 标签页key格式
export const TAB_KEY_FORMAT = {
    EDIT: (uuid: string) => `${TAB_PREFIX.EDIT}${uuid}`,
    DETAIL: (uuid: string) => `${TAB_PREFIX.DETAIL}${uuid}`
} as const; 