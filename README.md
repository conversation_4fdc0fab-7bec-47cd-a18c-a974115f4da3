## 特别注意

### 1. 在本项目中，message和modal的使用需要用特定方法，和ant-design-vue中的一般用法有所不同，在开发时需要注意。

**在vue组件或composable文件中的使用方法：**

```js
const message = inject('message')

message.success("消息内容");
```

```js
const modal = inject('modal')

modal.confirm({
    title: '标题',
    onOk() { },
    onCancel() { },
});
```

**在普通js或ts文件中的使用方法：**

```js
import globalEventEmitter from '@/utils/eventBus'

globalEventEmitter.emit(SHOW_MESSAGE, {
    type: 'error',
    content: '内容',
    duration: 1
})
```

```js
import globalEventEmitter from '@/utils/eventBus'

globalEventEmitter.emit(SHOW_MODAL, {
    type: 'confirm',
    config: {
        content: '内容',
        title: '提示',
        okText: '确定',
        onOk: () => {
        }
    }
})
```

做上述处理的背景介绍见项目wiki：[message、modal的用法及这样用的原因](http://************:8082/erule/erule-web-3.0/-/wikis/message%E3%80%81modal%E7%9A%84%E7%94%A8%E6%B3%95%E5%8F%8A%E8%BF%99%E6%A0%B7%E7%94%A8%E7%9A%84%E5%8E%9F%E5%9B%A0)

### 2. 需要用确认框的地方，统一用 modal.confrim ，不要用 a-popconfirm，否则会显示不正常

### 3. 全局loading

显示全局loading：

```js
showGlobalLoading()

// 或者
showGlobalLoading('加载中，请耐心等待')
```

隐藏全局loading：

```js
hideGlobalLoading()
```
### 4. table自适应高度
```若使用自定义组件，需注释掉本页onMounted查询逻辑，否则会查两遍影响效率

## 技术栈

项目框架: nuxt 3

开发语言: typescript + javascript（从erule2迁移过来的代码）

JS框架: vue 3

组件库: ant-design-vue 4

状态管理库: vuex 4

请求库: axios

css预处理库: sass

日期时间处理库：dayjs

工具函数库：lodash

界面样式及交互：仿语雀

## 如何安装

1. clone 本项目：

```bash
git clone git@************:erule/erule-web-3.0.git
```

2. 进入项目目录：

```bash
cd erule-web-3.0
```

3. 安装依赖：

```bash
npm i
```

用npm官方镜像安装可能会有失败的情况，可以将npm源改为淘宝镜像再安装：

```bash
npm config set registry https://registry.npmmirror.com
```

## 如何运行

```bash
npm run dev
```

## 初始加载动画

为解决Nuxt应用初始加载时出现的白屏问题，项目集成了自定义加载动画。

### 工作原理

加载动画通过静态生成后处理脚本`scripts/postGenerate.mjs`实现，该脚本在Nuxt静态生成完成后执行，向所有生成的HTML文件中注入加载动画代码。

加载动画会在页面加载过程中显示，当Nuxt应用完全渲染后自动淡出并移除。这大大改善了用户体验，特别是在网络较慢或应用较大时。

### 自定义配置

如需修改加载动画的样式或文字：

1. 编辑`scripts/postGenerate.mjs`文件
2. 可以修改CSS样式、加载文本或动画效果
3. 重新执行`npm run generate`应用更改

### 使用方式

无需额外配置，生成静态站点时会自动应用：

```bash
npm run generate
```

**注意**：此脚本仅在执行`npm run generate`命令时生效，在普通的`npm run build`过程中不会执行，因为它需要处理生成的静态HTML文件。

## 如何发布到测试环境

1. 打开项目根目录，切换到master分支，然后执行：

    ```bash
    git pull origin master

    ```
    拉取最新的master代码

2. 运行命令：

    ```bash
    npm run generate
    ```

    进行打包

3. 将打包结果 .output/public/ 下的所有文件上传到服务器 ************* 上的 /home/<USER>/dist/BRMS/erule-web/ 目录下，覆盖原来的文件

4. 刷新页面，就可以看到更新后的结果了

测试环境地址：http://*************:8094/BRMS/erule-web/

## 开发流程

1. 从最新的 master 分支上打出功能分支

2. 在功能分支上进行开发

3. 开发完成后，在gitlab上发合并请求给代码走查人，审核通过后合并到 master 分支

*注意事项*：

每次开始一块新的开发工作前，都从最新的 master 分支上打一个新分支，分支命名规范为：feature-xxx，比如 feature-rule-base-manage

如果开发不能在一天内完成的，第二天开始工作前，先拉取最新的 master 分支，再切回 feature 分支，然后将最新的 master 分支代码合并到 feature 分支上，然后再继续开发

这样能防止开发过程中，代码冲突的问题，同时能减少代码走查的时间

## 开发约定

1. 公共组件放到 components 目录下，组件目录用驼峰格式命名如 decisionTable；具体组件用Pascal格式命名，比如 RuleSet.vue

2. 业务组件放到 businessComponents 目录下，命名约定同公共组件

3. 布局组件放到 layouts 目录下，组件用驼峰格式命名如 layoutRuleBase.vue，除默认布局 default.vue 外，其他组件都要以 layout 开头，以便好识别

4. 页面组件放到 pages 目录下，路由分组及页面组件的命名采用驼峰格式的，如：(fooBar)/fooBar.vue

5. 所有的组件及文件、文件夹命名里都不应出现下划线


## 项目架构

```
.
├── api // 后端接口请求封装
│   ├── audit_history.js
│   ├── baseline_Management.js
│   ├── bulletin.js
│   ├── diction_entry.js
│   └── ...
├── assets // 资源文件
│   └── css // 该目录下面的样式文件目前都是从语雀直接拿过来的，做了少许修改
│       ├── c__AIDocModal.css
│       ├── c__BookContributors.css
│       ├── c__BookOverview.css
│       └── ...
├── businessComponents // 业务组件
├── components // 组件
│   ├── AIDoc.vue
│   ├── AppPage.vue // 页面包裹组件
│   ├── decisionTable // 决策表组件
│   │   └── DecisionTable.vue
│   ├── decisionTree // 决策树组件
│   │   └── DecisionTree.vue
│   ├── ruleFlow // 规则流组件
│   │   └── RuleFlow.vue
│   ├── ruleSet // 普通规则组件
│   │   └── RuleSet.vue
│   └── TemplateCenter.vue
├── layouts // 页面布局
│   ├── default.vue // 默认布局
│   ├── layoutRuleBase.vue // 规则库页面布局
│   └── layoutSearchTable.vue // 由搜索表单和表格组成的列表页布局
├── nuxt.config.ts
├── package.json
├── pages // 页面路由，下面带括号的代表路由分组，只起源码组织作用，不会出现在实际路由路径中
│   ├── (dashboard) // 工作台
│   │   ├── collections.vue // 收藏
│   │   ├── dashboard.vue // 开始
│   │   ├── help.vue // 帮助
│   │   ├── recycles.vue // 回收站
│   │   └── search.vue  // 搜索结果
│   ├── (ruleManage) // 规则管理
│   │   ├── (baseLineManage) // 基线管理
│   │   │   └── baseLineManage.vue // 基线管理
│   │   ├── (publishEnvironmentManage) // 发布环境管理
│   │   │   └── publishEnvironmentManage.vue // 发布环境管理
│   │   ├── (ruleAuditHistory) // 规则审核历史
│   │   │   └── ruleAuditHistory.vue // 规则审核历史
│   │   ├── (ruleAuditManage) // 规则审核管理
│   │   │   └── ruleAuditManage.vue // 规则审核管理
│   │   ├── (ruleAuditSwitchManage) // 规则审核开关管理
│   │   │   └── ruleAuditSwitchManage.vue // 规则审核开关管理
│   │   ├── (ruleBaseManage) // 规则库管理
│   │   │   ├── rule-[id].vue // 规则详情
│   │   │   ├── ruleBase-[id].vue // 规则库内容
│   │   │   ├── ruleBaseManage.vue // 规则库列表
│   │   │   ├── ruleContent-[id].vue // 规则内容编辑
│   │   │   ├── ruleContentAddNew.vue // 规则内容新增
│   │   │   └── rulePackage-[id].vue // 规则包详情
│   │   ├── (ruleBrowse) // 规则浏览
│   │   │   └── ruleBrowse.vue // 规则浏览
│   │   ├── (ruleModelManage) // 规则模型管理
│   │   │   └── ruleModelManage.vue // 规则模型管理
│   │   └── (rulePublishHistory) // 规则发布历史
│   │       └── rulePublishHistory.vue // 规则发布历史
│   ├── (ruleReport) // 规则报表
│   │   ├── ruleExecutionLogReport.vue // 规则执行日志报告
│   │   ├── rulePassRateReport.vue // 规则通过率报告
│   │   ├── rulePerformanceReport.vue // 规则性能报告
│   │   └── ruleRankingReport.vue // 规则排名报告
│   ├── (systemManage) // 系统管理
│   │   ├── bulletinManage.vue // 公告管理
│   │   ├── dictionaryEntry.vue // 字典条目管理
│   │   ├── dictionaryType.vue // 字典类型管理
│   │   ├── logQuery.vue // 日志查询
│   │   ├── menuManage.vue // 菜单管理
│   │   ├── parameterConfig.vue // 参数配置
│   │   ├── postPermission.vue // 岗位权限管理
│   │   ├── roleManage.vue // 角色管理
│   │   ├── systemMonitoring.vue // 系统监控
│   │   └── userManage.vue // 用户管理
│   ├── (taskManage) // 任务管理
│   │   ├── policyAudit.vue // 策略审核
│   │   ├── policyFilling.vue // 策略填写
│   │   ├── requirementAudit.vue // 需求审核
│   │   ├── ruleAdjustment.vue // 规则调整
│   │   ├── ruleTesting.vue // 规则测试
│   │   ├── taskAudit.vue // 任务审核
│   │   ├── taskFilling.vue // 任务填写
│   │   ├── taskPublish.vue // 任务发布
│   │   └── taskQuery.vue // 任务查询
│   ├── (testManage) // 测试管理
│   │   ├── carInsuranceCostTest.vue // 汽车保险费用测试
│   │   ├── carInsuranceUnderwritingTest.vue // 汽车保险核保测试
│   │   └── underwritingTest.vue // 核保测试
│   ├── index.vue
│   └── login.vue // 登录
├── plugins // 插件目录，暂时没有插件
├── public
│   └── js
│       ├── base64js.min.js
│       └── sm4encrypt.js
├── settings.js // 全局配置
├── store // 状态管理
│   ├── getters.js
│   ├── index.js
│   └── store.js
│   └── modules
│       ├── app.js
│       ├── bpmn.js
│       ├── generalRule.js
│       ├── ruleEdit.js
│       ├── settings.js
│       └── user.js
├── tsconfig.json
└── utils // 工具类
    ├── auth.js // 权限校验
    └── request.js // 请求封装
```
