<!-- 规则测试页 -->
<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import { ruleTesting, userOrgList } from "@/api/task";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";

    definePageMeta({
        title: '规则测试'
    })
    const router = useRouter();
    const modal = inject('modal') as any;
    const message = inject('message') as any;

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);
    const task_state_options = ref([
        { value: "7", label: "规则待测试" },
        { value: "8", label: "规则测试中" },
    ]);

    // 获取列表数据
    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await ruleTesting({
                appNo: params.appNo,
                applyOrgId: params.applyOrgId,
                businessLine: params.businessLine,
                createdName: params.createdName,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                createdTimeStr: params.createdTimeStr,
                logcontent: params.logContent,
                subtype: params.subType,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取数据失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getOption = () => {
        getBaseLine().then((res) => {
            businessLine_option.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLine_option.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };

    const getCreatedName = (value: string) => {
        let name = "";
        businessLine_option.value.map((i: any) => {
            if (i.code === value) {
                name = i.name;
            }
        });
        userOrgList({
            roleName: "DEMAND_ROLE_TEST",
            businessCode: value,
        }).then((res) => {
            model_type_options.value = res.data;

            // 更新搜索配置中的归属机构选项
            const applyOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'applyOrgId');
            if (applyOrgField && applyOrgField.compConfig) {
                applyOrgField.compConfig.options = model_type_options.value.map(item => ({
                    name: item.orgName,
                    value: item.id
                }));
            }
        });
    };

    //显示详情对话框
    const datar = ref<Record<string, any>>({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type: string, record: Record<string, any> = {}) => {
        modalType.value = type;
        if (type === 'testing') {
            datar.value = {
                type: "testing",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        if (type === 'submission') {
            datar.value = {
                type: "submission",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                subType: record.subType,
                logContent: record.logContent,
            };
        }
        isModalVisible.value = true;
    };

    const close = (flag: boolean) => {
        if(flag){//如果是数据提交，则刷新表单
            listLayout.value?.refresh();
        }
        isModalVisible.value = false;
    }

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input'
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: getCreatedName,
                    clearFields: ['applyOrgId']
                }
            },
            {
                label: '归属机构',
                field: 'applyOrgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input'
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select',
                compConfig: {
                    options: task_state_options.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: (formValue: any) => {
            listLayout.value?.refresh();
        },
        // 添加事件（规则测试页面不需要添加功能，但必须提供这个字段）
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchTaskList
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: Record<string, any>) => {
        return [
            {
                key: 'process',
                label: '处理',
                onClick: () => showModal('submission', record)
            }
        ];
    };

    // 组件引用
    const listLayout = ref(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];

    onMounted(() => {
        getOption();
    });
</script>

<template>
    <ListPage
        ref="listLayout"
        title="规则测试"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="false"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('testing',record)">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state as keyof typeof TASK_STATE] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'规则测试'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>

