<!--基线导入页-->
<script setup lang="ts">
import {subImport, getPubEnv, getBaseLine} from "@/api/baseline_Management";
import { UploadProps } from "ant-design-vue";
const message = inject('message')
const modal = inject('modal')
const emit = defineEmits(['changeSubmitLoading']);
//导入
interface ImportForm {
    baseLine: string;
    env: string;
}
interface BaseItem {
    code: string;
    name: string;
}
interface PubEnvItem {
    id: string;
    environmentName: string;
}
const importForm = reactive<ImportForm>({
    baseLine: '',
    env: '',
});
const importRules = {
    baseLine: [{ required: true, message: '请选择', trigger: 'change' }],
    env: [{ required: true, message: '请选择', trigger: 'change' }],
};
const baseArr = ref<BaseItem[]>([]);
const pubEnv = ref<PubEnvItem[]>([]);
const isPubEnv = ref(false);
const showPackageFlag = ref(false);
const file = ref<File | null>(null);
const upForm = ref<FormData | null>(null);
const editionValid = ref('');
const ImportFormRef = ref();
const getAllBaseUserId = async () => {
    const res = await getBaseLine();
    const choseBase: BaseItem = {
        name: '请选择',
        code: '',
    };
    baseArr.value = [choseBase, ...res.data];
};
const getSelBase = (vid: string) => {
    const obj = baseArr.value.find((item) => item.code === vid);
    if (obj) {
        importForm.baseLine = obj.code;
        if (importForm.baseLine !== '') {
            isPubEnv.value = true;
            const pars = {
                businessLine: importForm.baseLine,
            };
            getPubEnv(pars).then((res) => {
                pubEnv.value = res.data;
            });
        }
    }
};
const getSelEnvId = (vid: string) => {
    const obj = pubEnv.value.find((item) => item.id === vid);
    if (obj) {
        importForm.env = obj.id;
        showPackageFlag.value = true;
    }
};
const handlePreview: UploadProps['beforeUpload'] = (file) => {
    file = file as File;
    const aExtension = ['zip'];
    const resFil = aExtension.filter((item) => file.name.indexOf(item) !== -1);
    if (resFil.length === 0) {
        message.info('只能上传zip文件！');
        return false;
    }
    upForm.value = new FormData();
    upForm.value.append('environmentId', importForm.env);
    upForm.value.append('file', file);
    file = null;
    return false;
};
const uploadFile: UploadProps['customRequest'] = (params) => {
    const file = params.file as File;
    const aExtension = ['zip'];
    const resFil = aExtension.filter((item) => file.name.indexOf(item) !== -1);
    if (resFil.length === 0) {
        message.info('只能上传zip文件！');
        return;
    }
    upForm.value = new FormData();
    upForm.value.append('environmentId', importForm.env);
    upForm.value.append('file', file);
};
const submitForm = (callback) => {
    upForm.value.append("editionValid",editionValid.value);
    emit('changeSubmitLoading',true);
    ImportFormRef.value.validate().then(() => {
        subImport(upForm.value).then((res) => {
            if (res.code === 20000) {
                if (res.data === '新基线号小于现有基线号') {
                    editionValid.value = 'error';
                    modal.confirm({
                        title: '提示',
                        content: '新基线号小于现有基线号，请确认是否继续导入？',
                        okText: '导入',
                        cancelText: '取消',
                        type: 'warning'
                    }).then(() => {
                        subImport(upForm.value);
                        emit('changeSubmitLoading',false);
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }).catch(() => {
                        message.info('已取消');
                        emit('changeSubmitLoading',false);
                    });
                } else {
                    message.success(res.data);
                    emit('changeSubmitLoading',false);
                    if (typeof callback === 'function') {
                        callback();
                    }
                }
            } else {
                message.error(res.data);
                emit('changeSubmitLoading',false);
            }
        }).catch(() => {
            emit('changeSubmitLoading',false); // 如果请求失败，也要设置加载状态为 false
        });
    }).catch(() => {
        emit('changeSubmitLoading',false); // 如果请求失败，也要设置加载状态为 false
    });;

};
onMounted(()=>{
    getAllBaseUserId();
});
defineExpose({
    submitForm,
    resetForm: () => {
        // 重置表单数据
        importForm.baseLine = '';
        importForm.env = '';
        // 重置文件上传状态
        file.value = null;
        upForm.value = new FormData();
        editionValid.value = '';
        
        // 如果表单引用存在，重置验证状态
        if (ImportFormRef.value) {
            ImportFormRef.value.resetFields();
        }
    },
});
</script>
<template>
    <div id="import">
        <a-form :model="importForm" ref="ImportFormRef"  :rules="importRules" label-align="right" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="业务条线：">
                <a-select v-model:value="importForm.baseLine" placeholder="请选择" @change="getSelBase" :filterOption="filterOption" showSearch>
                    <a-select-option v-for="item in baseArr" :key="item.code" :value="item.code" :name="item.name">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="发布环境：" v-if="isPubEnv">
                <a-select v-model:value="importForm.env" placeholder="请选择" @change="getSelEnvId">
                    <a-select-option v-for="item in pubEnv" :key="item.id" :value="item.id">
                        {{ item.environmentName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则包：" v-if="showPackageFlag" >
                <a-upload
                        class="upload-demo"
                        ref="upload"
                        :action="''"
                        :multiple="false"
                        :before-upload="handlePreview"
                        :auto-upload="false"
                        :custom-request="uploadFile"
                        :limit="1"
                        style="margin-left: 5px"
                >
                    <a-button slot="trigger" size="small" type="primary">选取文件</a-button>
                </a-upload>
            </a-form-item>
        </a-form>
    </div>
</template>
