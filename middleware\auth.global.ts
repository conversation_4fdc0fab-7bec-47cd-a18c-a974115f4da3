/**
 * 登录状态检测中间件
 */

const loginRoutePath = '/login'
const dashboardRoutePath = '/dashboard'

function isAuthenticated(): boolean {
    if (getToken()) {
        return true
    } else {
        return false
    }
}

// ---cut---
export default defineNuxtRouteMiddleware((to, from) => {
    // 如果未登录，保存当前URL
    if (!isAuthenticated() && to.path !== loginRoutePath) {
        // 保存当前URL，用于登录后重定向
        if (to.fullPath && to.fullPath !== '/' && !to.fullPath.includes(loginRoutePath)) {
            localStorage.setItem('loginRedirectUrl', window.location.href)
        }
        return navigateTo(loginRoutePath)
    }
    
    // 已登录访问登录页，跳转到首页
    if (isAuthenticated() && to.path.indexOf(loginRoutePath) !== -1) {
        return navigateTo(dashboardRoutePath)
    }
})
