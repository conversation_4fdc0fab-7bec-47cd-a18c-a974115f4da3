<!-- 规则内容编辑 -->

<script setup lang="ts">
import { recordAccess } from '@/api/dashboardApi'
import RuleTopBar from "@/businessComponents/ruleBaseManage/RuleTopBar";
definePageMeta({
    layout: "layout-rule-base",
});
const props = defineProps({
    obj: {
        type: Object,
        default: () => {
            return {};
        },
    },
})
const ruleInfo = ref();
// 规则标题
const ruleTitle = ref("");
// 规则包名称
const packageName = ref("");

// 规则uuid
const uuid = ref("");
// 规则engUuid
const engUuid = ref("");
// 规则demandUuid
const demandUuid = ref("");
// 规则类型
const type = ref("");
// 是否收藏
const ifCollect = ref(false)
// 规则状态
const status = ref("")

onMounted(() => {
    ruleTitle.value = props.obj.ruleName;
    type.value = props.obj.type;
    uuid.value = props.obj.uuid;
    engUuid.value = props.obj.engUuid;
    demandUuid.value = props.obj.demandUuid;
    packageName.value = props.obj.packageName;
    ifCollect.value = props.obj.ifCollect;
    status.value = props.obj.status;
    // 异步记录访问历史
    recordAccess({ ruleUuid: props.obj.ruleId }).catch(console.error)
});

</script>

<template>
    <!-- 使用提取的顶部栏组件，不再需要传递事件 -->
    <RuleTopBar :rule-title="ruleTitle" :package-name="packageName" :type="type" :uuid="uuid" :status="status"
        :eng-uuid="engUuid" :demand-uuid="demandUuid" :if-collect="ifCollect" :rule-info="ruleInfo" :symbol="true" />
    <!-- 下部栏：规则内容 -->
    <div class="rule-content">
        <RuleEdit v-if="type" :type="type" :uuid="uuid" :engUuid="engUuid" :demandUuid="demandUuid"
            :packageName="packageName" :ruleName="ruleTitle" />
    </div>
</template>

<style scoped>
.rule-path-container {
    position: relative;
}

.rule-top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #ffffff;
}

.rule-path {
    font-size: 14px;
    color: rgb(88, 90, 90);
}

.actions-container {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.nav-buttons {
    display: flex;
    flex-direction: row;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-right: 15px;
    overflow: hidden;
}

.nav-buttons .action-button {
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.nav-buttons .action-button:first-child {
    border-right: 1px solid #e8e8e8;
}

.other-buttons {
    display: flex;
    align-items: center;
}

.action-button {
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
}

.button-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.rule-content {
    padding: 15px;
    padding-bottom: 0;
}

.rule-info-area {
    padding: 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f9f9f9;
}

.rule-info-item {
    font-size: 13px;
    color: #333;
    line-height: 1.6;
    display: flex;
}

.rule-info-item label {
    text-align: right;
    min-width: 80px;
    margin-right: 2px;
}
</style>
