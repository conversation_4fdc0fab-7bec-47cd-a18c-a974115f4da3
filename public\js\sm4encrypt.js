function SM4_Context() { this.mode = 1, this.isPadding = !0, this.sk = new Array(32) } function SM4() { var a, b, c; this.SM4_ENCRYPT = 1, this.SM4_DECRYPT = 0, a = [214, 144, 233, 254, 204, 225, 61, 183, 22, 182, 20, 194, 40, 251, 44, 5, 43, 103, 154, 118, 42, 190, 4, 195, 170, 68, 19, 38, 73, 134, 6, 153, 156, 66, 80, 244, 145, 239, 152, 122, 51, 84, 11, 67, 237, 207, 172, 98, 228, 179, 28, 169, 201, 8, 232, 149, 128, 223, 148, 250, 117, 143, 63, 166, 71, 7, 167, 252, 243, 115, 23, 186, 131, 89, 60, 25, 230, 133, 79, 168, 104, 107, 129, 178, 113, 100, 218, 139, 248, 235, 15, 75, 112, 86, 157, 53, 30, 36, 14, 94, 99, 88, 209, 162, 37, 34, 124, 59, 1, 33, 120, 135, 212, 0, 70, 87, 159, 211, 39, 82, 76, 54, 2, 231, 160, 196, 200, 158, 234, 191, 138, 210, 64, 199, 56, 181, 163, 247, 242, 206, 249, 97, 21, 161, 224, 174, 93, 164, 155, 52, 26, 85, 173, 147, 50, 48, 245, 140, 177, 227, 29, 246, 226, 46, 130, 102, 202, 96, 192, 41, 35, 171, 13, 83, 78, 111, 213, 219, 55, 69, 222, 253, 142, 47, 3, 255, 106, 114, 109, 108, 91, 81, 141, 27, 175, 146, 187, 221, 188, 127, 17, 217, 92, 65, 31, 16, 90, 216, 10, 193, 49, 136, 165, 205, 123, 189, 45, 116, 208, 18, 184, 229, 180, 176, 137, 105, 151, 74, 12, 150, 119, 126, 101, 185, 241, 9, 197, 110, 198, 132, 24, 240, 125, 236, 58, 220, 77, 32, 121, 238, 95, 62, 215, 203, 57, 72], b = [2746333894, 1453994832, 1736282519, 2993693404], c = [462357, 472066609, 943670861, 1415275113, 1886879365, 2358483617, 2830087869, 3301692121, 3773296373, 4228057617, 404694573, 876298825, 1347903077, 1819507329, 2291111581, 2762715833, 3234320085, 3705924337, 4177462797, 337322537, 808926789, 1280531041, 1752135293, 2223739545, 2695343797, 3166948049, 3638552301, 4110090761, 269950501, 741554753, 1213159005, 1684763257], this.GET_ULONG_BE = function (a, b) { return (255 & a[b]) << 24 | (255 & a[b + 1]) << 16 | (255 & a[b + 2]) << 8 | 4294967295 & (255 & a[b + 3]) }, this.PUT_ULONG_BE = function (a, b, c) { var d = 255 & a >> 24, e = 255 & a >> 16, f = 255 & a >> 8, g = 255 & a; b[c] = d > 128 ? d - 256 : d, b[c + 1] = e > 128 ? e - 256 : e, b[c + 2] = f > 128 ? f - 256 : f, b[c + 3] = g > 128 ? g - 256 : g }, this.SHL = function (a, b) { return (4294967295 & a) << b }, this.ROTL = function (a, b) { return this.SHL(a, b), this.SHL(a, b) | a >> 32 - b }, this.sm4Lt = function (a) { var b = 0, c = 0, d = new Array(4), e = new Array(4); return this.PUT_ULONG_BE(a, d, 0), e[0] = this.sm4Sbox(d[0]), e[1] = this.sm4Sbox(d[1]), e[2] = this.sm4Sbox(d[2]), e[3] = this.sm4Sbox(d[3]), b = this.GET_ULONG_BE(e, 0), c = b ^ this.ROTL(b, 2) ^ this.ROTL(b, 10) ^ this.ROTL(b, 18) ^ this.ROTL(b, 24) }, this.sm4F = function (a, b, c, d, e) { return a ^ this.sm4Lt(b ^ c ^ d ^ e) }, this.sm4CalciRK = function (a) { var b = 0, c = 0, d = new Array(4), e = new Array(4); return this.PUT_ULONG_BE(a, d, 0), e[0] = this.sm4Sbox(d[0]), e[1] = this.sm4Sbox(d[1]), e[2] = this.sm4Sbox(d[2]), e[3] = this.sm4Sbox(d[3]), b = this.GET_ULONG_BE(e, 0), c = b ^ this.ROTL(b, 13) ^ this.ROTL(b, 23) }, this.sm4Sbox = function (b) { var c = 255 & b, d = a[c]; return d > 128 ? d - 256 : d }, this.sm4_setkey_enc = function (a, b) { return null == a ? (alert("ctx is null!"), !1) : null == b || 16 != b.length ? (alert("key error!"), !1) : (a.mode = this.SM4_ENCRYPT, this.sm4_setkey(a.sk, b), void 0) }, this.sm4_setkey = function (a, d) { var e = new Array(4), f = new Array(36), g = 0; for (e[0] = this.GET_ULONG_BE(d, 0), e[1] = this.GET_ULONG_BE(d, 4), e[2] = this.GET_ULONG_BE(d, 8), e[3] = this.GET_ULONG_BE(d, 12), f[0] = e[0] ^ b[0], f[1] = e[1] ^ b[1], f[2] = e[2] ^ b[2], f[3] = e[3] ^ b[3], g = 0; 32 > g; g++)f[g + 4] = f[g] ^ this.sm4CalciRK(f[g + 1] ^ f[g + 2] ^ f[g + 3] ^ c[g]), a[g] = f[g + 4] }, this.padding = function (a, b) { var c, d, e; if (null == a) return null; if (c = null, b == this.SM4_ENCRYPT) for (d = parseInt(16 - a.length % 16), c = a.slice(0), e = 0; d > e; e++)c[a.length + e] = d; else d = a[a.length - 1], c = a.slice(0, a.length - d); return c }, this.sm4_one_round = function (a, b, c) { var d = 0, e = new Array(36); for (e[0] = this.GET_ULONG_BE(b, 0), e[1] = this.GET_ULONG_BE(b, 4), e[2] = this.GET_ULONG_BE(b, 8), e[3] = this.GET_ULONG_BE(b, 12); 32 > d;)e[d + 4] = this.sm4F(e[d], e[d + 1], e[d + 2], e[d + 3], a[d]), d++; this.PUT_ULONG_BE(e[35], c, 0), this.PUT_ULONG_BE(e[34], c, 4), this.PUT_ULONG_BE(e[33], c, 8), this.PUT_ULONG_BE(e[32], c, 12) }, this.sm4_crypt_ecb = function (a, b) { var c, d, e, f, g, h; for (null == b && alert("input is null!"), a.isPadding && a.mode == this.SM4_ENCRYPT && (b = this.padding(b, this.SM4_ENCRYPT)), c = 0, d = b.length, e = new Array; d > 0; d -= 16)f = new Array(16), g = b.slice(16 * c, 16 * (c + 1)), this.sm4_one_round(a.sk, g, f), e = e.concat(f), c++; for (h = e, a.isPadding && a.mode == this.SM4_DECRYPT && (h = this.padding(h, this.SM4_DECRYPT)), c = 0; c < h.length; c++)h[c] < 0 && (h[c] = h[c] + 256); return h }, this.sm4_crypt_cbc = function (a, b, c) { var d, e, f, g, h, i, j, k, l; if ((null == b || 16 != b.length) && alert("iv error!"), null == c && alert("input is null!"), a.isPadding && a.mode == this.SM4_ENCRYPT && (c = this.padding(c, this.SM4_ENCRYPT)), d = 0, e = c.length, f = new Array, a.mode == this.SM4_ENCRYPT) for (g = 0; e > 0; e -= 16) { for (h = new Array(16), i = new Array(16), j = c.slice(16 * g, 16 * (g + 1)), d = 0; 16 > d; d++)h[d] = j[d] ^ b[d]; this.sm4_one_round(a.sk, h, i), b = i.slice(0, 16), f = f.concat(i), g++ } else for (k = [], g = 0; e > 0; e -= 16) { for (h = new Array(16), i = new Array(16), j = c.slice(16 * g, 16 * (g + 1)), k = j.slice(0, 16), sm4_one_round(a.sk, j, h), d = 0; 16 > d; d++)i[d] = h[d] ^ b[d]; b = k.slice(0, 16), f = f.concat(i), g++ } for (l = f, a.isPadding && a.mode == this.SM4_DECRYPT && (l = this.padding(l, this.SM4_DECRYPT)), d = 0; d < l.length; d++)l[d] < 0 && (l[d] = l[d] + 256); return l } } function SM4Util() { this.secretKey = "", this.iv = "", this.hexString = !1, this.encryptEcb = function (a) { var b, c, d, e, f; try { return b = new SM4, c = new SM4_Context, c.isPadding = !0, c.mode = b.SM4_ENCRYPT, d = stringToByte(this.secretKey), b.sm4_setkey_enc(c, d), e = b.sm4_crypt_ecb(c, stringToByte(a)), f = base64js.fromByteArray(e), null != f && f.replace(/(^\s*)|(\s*$)/g, "").length > 0 && f.replace(/(\s*|\t|\r|\n)/g, ""), f } catch (g) { return console.error(g), null } }, this.encryptCbc = function (a) { var b, c, d, e, f, g; try { return b = new SM4, c = new SM4_Context, c.isPadding = !0, c.mode = b.SM4_ENCRYPT, d = stringToByte(this.secretKey), e = stringToByte(this.iv), b.sm4_setkey_enc(c, d), f = b.sm4_crypt_cbc(c, e, stringToByte(a)), g = base64js.fromByteArray(f), null != g && g.replace(/(^\s*)|(\s*$)/g, "").length > 0 && g.replace(/(\s*|\t|\r|\n)/g, ""), g } catch (h) { return console.error(h), null } }, stringToByte = function (a) { var d, e, b = new Array, c = a.length; for (e = 0; c > e; e++)d = a.charCodeAt(e), d >= 65536 && 1114111 >= d ? (b.push(240 | 7 & d >> 18), b.push(128 | 63 & d >> 12), b.push(128 | 63 & d >> 6), b.push(128 | 63 & d)) : d >= 2048 && 65535 >= d ? (b.push(224 | 15 & d >> 12), b.push(128 | 63 & d >> 6), b.push(128 | 63 & d)) : d >= 128 && 2047 >= d ? (b.push(192 | 31 & d >> 6), b.push(128 | 63 & d)) : b.push(255 & d); return b }, byteToString = function (a) { var b, c, d, e, f, g, h, i; if ("string" == typeof a) return a; for (b = "", c = a, d = 0; d < c.length; d++)if (e = c[d].toString(2), f = e.match(/^1+?(?=0)/), f && 8 == e.length) { for (g = f[0].length, h = c[d].toString(2).slice(7 - g), i = 1; g > i; i++)h += c[i + d].toString(2).slice(2); b += String.fromCharCode(parseInt(h, 2)), d += g - 1 } else b += String.fromCharCode(c[d]); return b } }
