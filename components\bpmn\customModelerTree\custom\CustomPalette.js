/**
 * A palette that allows you to create BPMN _and_ custom elements.
 */
export default function PaletteProvider(palette, create, elementFactory, bpmnFactory, translate, handTool, lassoTool, globalConnect) {
  this.create = create
  this.elementFactory = elementFactory
  this.bpmnFactory = bpmnFactory
  this.translate = translate;
  this.handTool = handTool;
  this.lassoTool = lassoTool;
  this.globalConnect = globalConnect;

  palette.registerProvider(this)
}

PaletteProvider.$inject = [
  'palette',
  'create',
  'elementFactory',
  'bpmnFactory',
  'translate',
  'handTool',
  'lassoTool',
  'globalConnect'
]

PaletteProvider.prototype.getPaletteEntries = function (element) {
  const {
    create,
    elementFactory,
    bpmnFactory,
    translate,
    handTool,
    lassoTool,
    globalConnect
  } = this;

  // 创建条件节点
  function createCondition() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:ExclusiveGateway', {
        name: '条件节点',
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:ExclusiveGateway',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建动作节点
  function createAction() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:Task', {
        name: '动作节点'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:Task',
        businessObject
      });
      create.start(event, shape);
    }
  }

  return {
    'hand-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-hand-tool dir-tool',
      title: translate('激活抓手工具'),
      action: {
        click: function (event) {
          handTool.activateHand(event);
        }
      }
    },
    'lasso-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-lasso-tool dir-tool',
      title: translate('激活套索工具'),
      action: {
        click: function (event) {
          lassoTool.activateSelection(event);
        }
      }
    },
    'global-connect-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-connection-multi dir-tool',
      title: translate('激活连接工具'),
      action: {
        click: function (event) {
          globalConnect.toggle(event);
        }
      }
    },
    'tool-separator': {
      group: 'tools',
      separator: true
    },
    'create.exclusive-gateway': {
      group: 'model',
      className: 'entry iconfontree icon-condition dir-shape',
      title: translate('创建条件节点'),
      action: {
        dragstart: createCondition(),
        click: createCondition()
      }
    },
    'create.task': {
      group: 'model',
      className: 'entry iconfontree icon-action dir-shape',
      title: translate('创建动作节点'),
      action: {
        dragstart: createAction(),
        click: createAction()
      }
    }
  }
}
