<!--规则包树组件-->
<template>
    <div class="ReaderLayout-module_aside_WX6k7"
        :style="`will-change: width; transition: width 200ms cubic-bezier(0.1, 0, 0, 1); width: ${sidebarWidth-2}px;`">
        <!-- 添加侧边栏展开/收缩按钮 -->
        <div :class="['sidebar-toggle-btn', isCollapsed ? 'btn-collapsed' : '']" @click="toggleSidebar" v-if="isShowToggleBtn">
            <span class="toggle-btn-text" v-if="!isCollapsed"></span>
            <span class="toggle-btn-text" v-else></span>
            <IconMiniDropdown :class="isCollapsed ? 'icon-expanded' : 'icon-collapsed'" class="toggle-icon"/>
        </div>
        <nav class="ReaderLayout-module_asideNav_-Kxml" id="navBox"
            :style="`height:100%;will-change: width; transition: width 200ms cubic-bezier(0.1, 0, 0, 1); width: ${sidebarWidth-2}px; max-width: 480px; min-width: 0px;`">
            <a-spin :spinning="isLoading">
                <div class="BookCatalog-module_asiderTab_0aJLs">
                    <div class="BookCatalog-module_tabBar_BKeYX">
                    </div>
                    <div class="ReaderLayout-module_searchBar_F9DSq">
                        <a-input autocomplete="off" v-model:value="searchValue" @change="onSearchChange"
                            :style="`width: 95%; margin-left: 6px;margin-right:5px; margin-bottom: 5px; ${isCollapsed ? 'padding-left: 24px;' : ''}`"
                            :placeholder="isCollapsed ? '搜索' : '搜索规则包'" />
                        <IconSearch v-if="isCollapsed" class="collapsed-search-icon" />
                    </div>
                    <div class="ant-tabs-content-holder">
                        <div class="ant-tabs-content ant-tabs-content-top">
                            <div role="tabpanel" tabindex="0" aria-hidden="false"
                                class="ant-tabs-tabpane ant-tabs-tabpane-active" id="rc-tabs-0-panel-Catalog"
                                aria-labelledby="rc-tabs-0-tab-Catalog">
                                <div style="display: initial;">
                                    <div class="BookCatalog-module_sideCatalog_wpCdP">
                                        <div style="overflow: visible; height: 0px; width: 0px;">
                                            <div class="lark-virtual-tree" :style="`width: ${sidebarWidth-20}px;`">
                                                <div :style="`height: 85vh;overflow-y: auto;width: ${sidebarWidth-20}px;`"
                                                    v-if="activeName === 'first'">
                                                    <a-tree class="rule-tree-class" v-if="rulePackageTree.length > 0"
                                                        :treeData="rulePackageTree" :loading="isLoading"
                                                        v-model:expandedKeys="expandedKeys" blockNode>
                                                        <template #title="node">
                                                            <div :class="treeChildClick === node.key ? 'selected-tree-style' : ''"
                                                                class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style rule-tree-hover-style">
                                                                <div
                                                                    :class="{'custom-tree-node': true, 'collapsed-tree-node': isCollapsed}">
                                                                    <a-tooltip :title="node.folderName">
                                                                        <!-- 根节点使用不同的链接和点击处理 -->
                                                                        <NuxtLink v-if="node.isRootNode"
                                                                            class="catalogTreeItem-module_title_snpKw nuxt-link-style"
                                                                            :to="`/ruleBase-${ruleBaseId}?clickKey=${clickKey}`"
                                                                            @click="clickTreeChildStyle(node)">
                                                                            <!-- 根节点前的自定义图标 -->
                                                                            <span class="root-title-icon">
                                                                                <IconRuleBase v-if="node.folderName !== RULE_LIBRARY_TEXT"/>
                                                                                <IconAllRuleBase v-else/>
                                                                            </span>
                                                                            <!-- 根据侧边栏状态显示完整标题或简短标题 -->
                                                                            <span v-if="!isCollapsed">{{ node.folderName }}</span>
                                                                            <span v-else class="short-title">{{ node.shortTitle || (node.folderName ? node.folderName.substr(0, 2) : '') }}</span>
                                                                        </NuxtLink>
                                                                        <!-- 非根节点使用原有逻辑 -->
                                                                        <NuxtLink v-else
                                                                            class="catalogTreeItem-module_title_snpKw nuxt-link-style"
                                                                            :to="generateLink(node)"
                                                                            @click="clickTreeChildStyle(node)">
                                                                            <!-- 非根节点前的自定义图标 -->
                                                                            <span class="node-title-icon">
                                                                                <IconRulePackage :size="20" v-if="node.iconCls !== 'ico_rule_project'"/>
                                                                                <IconRuleBase :size="20" v-else/>
                                                                            </span>
                                                                            <!-- 根据侧边栏状态显示完整标题或简短标题 -->
                                                                            <span v-if="!isCollapsed">{{ node.folderName }}</span>
                                                                            <span v-else class="short-title">{{ node.shortTitle || (node.folderName ? node.folderName.substr(0, 2) : '') }}</span>
                                                                        </NuxtLink>
                                                                    </a-tooltip>
                                                                    <!-- 根节点显示按钮 -->
                                                                    <a-dropdown :trigger="['click']"
                                                                                @openChange="openChangeStyle($event, node)"
                                                                                v-if="node.isRootNode && node.folderName !== RULE_LIBRARY_TEXT">
                                                                        <div style="float: right;margin-right: 0px"
                                                                             :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                             class="catalogTreeItem-module_btnItem_HrYZm">
                                                                            <span
                                                                                    class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                <IconMore />
                                                                            </span>

                                                                        </div>
                                                                        <template #overlay>
                                                                            <a-menu>
<!--                                                                                <a-menu-item key="1"-->
<!--                                                                                             @click="dropdownClick('create', ruleBaseData)">-->
<!--                                                                                    <IconAdd />-->
<!--                                                                                    新建一级规则包-->
<!--                                                                                </a-menu-item>-->
                                                                                <a-menu-item key="2" @click="setAlwaysUsed">
                                                                                    <IconPin v-if="ifAlwaysUsed"/>
                                                                                    <IconPinOutlined v-else/>
                                                                                    {{ifAlwaysUsed?'取消常用规则库':'设为常用规则库'}}
                                                                                </a-menu-item>
                                                                                <a-menu-item key="3" @click="importFun">
                                                                                    <IconRuleBaseImport/>
                                                                                    导入规则库
                                                                                </a-menu-item>
                                                                    </a-menu>
                                                                        </template>

                                                                    </a-dropdown>
                                                                    <a-dropdown :trigger="['click']"
                                                                        @openChange="openChangeStyle($event, node)"
                                                                        v-if="node.isRootNode && checkPermi([RULE_PERMISSION.RULE.ADD]) && node.folderName !== RULE_LIBRARY_TEXT">
                                                                        <div style="float: right;margin-right: 0px"
                                                                            :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                            class="catalogTreeItem-module_btnItem_HrYZm">
                                                                            <span
                                                                                class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                <PlusOutlined />
                                                                            </span>
                                                                        </div>
                                                                        <template #overlay>
                                                                            <a-menu>
                                                                                <a-menu-item
                                                                                        v-for="ruleType in ruleTypes"
                                                                                        :key="ruleType.code"
                                                                                        @click="openAddModal(ruleType.code, node)">
                                                                                    <a
                                                                                            class="HeadNewButton-module_menuItemContainer_VLntH">
                                                                                        <div
                                                                                                class="HeadNewButton-module_iconWarp_o9rNt">
                                                                                            <component
                                                                                                    :is="ruleType.icon"
                                                                                                    :size="18"
                                                                                                    class="HeadNewButton-module_iconContainer_HmX2B" />
                                                                                        </div>
                                                                                        <span style="color:black">{{
                                                                                            ruleType.name }}</span>
                                                                                    </a>
                                                                                </a-menu-item>
                                                                                <a-menu-divider></a-menu-divider>
                                                                                <a-menu-item key="1"@click="dropdownClick('create', ruleBaseData)">
                                                                                    <a class="HeadNewButton-module_menuItemContainer_VLntH">

                                                                                        <div class="HeadNewButton-module_iconWarp_o9rNt">
                                                                                            <IconRulePackage class="HeadNewButton-module_iconContainer_HmX2B"/>
                                                                                        </div>
                                                                                        <span style="color:black">规则包</span>
                                                                                    </a>
                                                                                </a-menu-item>
                                                                            </a-menu>
                                                                        </template>
                                                                    </a-dropdown>
                                                                    <!-- 非根节点显示所有按钮 -->
                                                                    <a-dropdown :trigger="['click']"
                                                                        @openChange="openChangeStyle($event, node)"
                                                                        v-if="!node.isRootNode && node.folderName !== RULE_LIBRARY_TEXT && checkPermi([RULE_PERMISSION.RULE.ADD])">
                                                                        <div style="float: right;margin-right: 0px"
                                                                            :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                            class="catalogTreeItem-module_btnItem_HrYZm">
                                                                            <span
                                                                                class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                <IconMore />
                                                                            </span>

                                                                        </div>
                                                                        <template #overlay>
                                                                            <a-menu>
<!--                                                                                <a-menu-item key="1"-->
<!--                                                                                    @click="dropdownClick('create', node)">-->
<!--                                                                                    <IconAdd />-->
<!--                                                                                    新建-->
<!--                                                                                </a-menu-item>-->
                                                                                <div
                                                                                    v-show="node.iconCls !== 'ico_rule_project'">
                                                                                    <a-menu-item key="2"
                                                                                         v-if="checkPermi([RULE_PERMISSION.RULE.EDIT])"
                                                                                        @click="dropdownClick('update', node)">
                                                                                        <IconEdit />
                                                                                        编辑
                                                                                    </a-menu-item>
                                                                                    <a-menu-item key="3"
                                                                                         v-if="checkPermi([RULE_PERMISSION.RULE.COPY])"
                                                                                        @click="dropdownClick('copy', node)">
                                                                                        <IconPencilUnderscore />
                                                                                        复制
                                                                                    </a-menu-item>
                                                                                    <a-menu-item key="4"
                                                                                         v-if="checkPermi([RULE_PERMISSION.RULE.DELETE])"
                                                                                        @click="dropdownClick('delete', node)">
                                                                                        <IconDelete />
                                                                                        删除
                                                                                    </a-menu-item>
                                                                                </div>
                                                                            </a-menu>
                                                                        </template>

                                                                    </a-dropdown>
                                                                    <a-dropdown :trigger="['click']"
                                                                        @openChange="openChangeStyle($event, node)"
                                                                        v-if="!node.isRootNode && node.folderName !== RULE_LIBRARY_TEXT && checkPermi([RULE_PERMISSION.RULE.ADD])">
                                                                        <div style="float: right;margin-right: 0px"
                                                                            :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                            class="catalogTreeItem-module_btnItem_HrYZm">
                                                                            <span
                                                                                class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                <PlusOutlined />
                                                                            </span>
                                                                        </div>
                                                                        <template #overlay>
                                                                            <a-menu>
                                                                                <a-menu-item
                                                                                    v-for="ruleType in ruleTypes"
                                                                                    :key="ruleType.code"
                                                                                    v-if="checkPermi([RULE_PERMISSION.RULE.ADD])"
                                                                                    @click="openAddModal(ruleType.code, node)">
                                                                                    <a
                                                                                        class="HeadNewButton-module_menuItemContainer_VLntH">
                                                                                        <div
                                                                                            class="HeadNewButton-module_iconWarp_o9rNt">
                                                                                            <component
                                                                                                :is="ruleType.icon"
                                                                                                :size="18"
                                                                                                class="HeadNewButton-module_iconContainer_HmX2B" />
                                                                                        </div>
                                                                                        <span style="color:black">{{
                                                                                            ruleType.name }}</span>
                                                                                    </a>
                                                                                </a-menu-item>
                                                                                <a-menu-divider></a-menu-divider>
                                                                                <a-menu-item key="1" @click="dropdownClick('create', node)">
                                                                                <a class="HeadNewButton-module_menuItemContainer_VLntH">

                                                                                    <div class="HeadNewButton-module_iconWarp_o9rNt">
                                                                                        <IconRulePackage class="HeadNewButton-module_iconContainer_HmX2B"/>
                                                                                    </div>
                                                                                    <span style="color:black">规则包</span>
                                                                                </a>
                                                                                </a-menu-item>
                                                                            </a-menu>
                                                                        </template>
                                                                    </a-dropdown>
                                                                </div>


                                                            </div>

                                                        </template>
                                                        <template #switcherIcon="{ switcherCls}">
                                                            <div :class="switcherCls"
                                                                class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                <!-- 根节点的自定义展开图标 -->
                                                                <div v-if="switcherCls.indexOf('root') > -1"
                                                                    class="catalogTreeItem-module_collapseIconWrapper_XcS8B root-node-icon">
                                                                </div>
                                                                <!-- 非根节点的默认展开图标 -->
                                                                <div v-else
                                                                    class="catalogTreeItem-module_collapseIconWrapper_XcS8B"
                                                                >
                                                                    <IconMiniDropdown />
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </a-tree>
                                                </div>

                                                <a-tabs type="card" v-model:activeKey="glossary" @change="tabClick"
                                                    v-if="activeName === 'second'">
                                                    <a-tab-pane tab="模型" key="model">
                                                        <div class="lark-virtual-tree">
                                                            <div class="tree-y-class">
                                                                <a-tree class="rule-tree-class" :treeData="modelTree"
                                                                    :loading="isLoading" blockNode>
                                                                    <template #title="{ viewName }">
                                                                        <div
                                                                            class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style">
                                                                            <div
                                                                                class="catalogTreeItem-module_content_fLFbS custom-tree-node2">
                                                                                <a-tooltip :title="viewName">
                                                                                    <span
                                                                                        class="catalogTreeItem-module_title_snpKw nuxt-link-style ">
                                                                                        {{
                                                                                            viewName
                                                                                        }}
                                                                                    </span>
                                                                                </a-tooltip>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                    <template #switcherIcon="{ switcherCls }">
                                                                        <div :class="switcherCls"
                                                                            class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                            <div
                                                                                class="catalogTreeItem-module_collapseIconWrapper_XcS8B">
                                                                                <IconMiniDropdown />
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </a-tree>

                                                            </div>
                                                        </div>
                                                    </a-tab-pane>
                                                    <a-tab-pane tab="数据字典" key="dataDict">
                                                        <div class="lark-virtual-tree">
                                                            <div class="tree-y-class">
                                                                <a-tree class="rule-tree-class" :treeData="dictTree"
                                                                    :loading="isLoading" blockNode>
                                                                    <template #title="{ viewName }">
                                                                        <div
                                                                            class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style">
                                                                            <div
                                                                                class="catalogTreeItem-module_content_fLFbS custom-tree-node2">
                                                                                <a-tooltip :title="viewName">
                                                                                    <span
                                                                                        class="catalogTreeItem-module_title_snpKw nuxt-link-style">
                                                                                        {{
                                                                                            viewName
                                                                                        }}
                                                                                    </span>
                                                                                </a-tooltip>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                    <template #switcherIcon="{ switcherCls }">
                                                                        <div :class="switcherCls"
                                                                            class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                            <div
                                                                                class="catalogTreeItem-module_collapseIconWrapper_XcS8B">
                                                                                <IconMiniDropdown />
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </a-tree>
                                                            </div>
                                                        </div>
                                                    </a-tab-pane>
                                                </a-tabs>



                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div role="tabpanel" tabindex="-1" aria-hidden="true" class="ant-tabs-tabpane"
                                id="rc-tabs-0-panel-All_Docs" aria-labelledby="rc-tabs-0-tab-All_Docs"
                                style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </a-spin>

        </nav>
    </div>
    <a-modal v-if="dialogUploadVisible" :visible="dialogUploadVisible" title="导入到当前规则库中" @ok="dialogUploadSubmit"
        :confirmLoading="dialogUploadLoading" @cancel="dialogUploadVisible = false">
        <a-form class="dialogForm">
            <a-form-item name="choiceFile">
                <FileUpload :accept="'.erule'" @uploadFile="uploadFile" :uploadHidden="uploadHidden" />
            </a-form-item>
        </a-form>
    </a-modal>
    <a-modal v-if="data.dialogFormVisible" :title="data.dialogFormTitle" :visible="data.dialogFormVisible"
        @ok="saveOrUpdate" @cancel="data.dialogFormVisible = false" :confirmLoading="data.submitLoading">
        <a-form :model="data.form" :rules="data.rules" ref="dialoForm" label-align="right" :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18 }">
            <a-form-item label="规则包名称" name="ruleBaseName">
                <a-input autocomplete="off" v-model:value="data.form.ruleBaseName"></a-input>
            </a-form-item>
            <a-form-item label="上级规则包" v-show="data.copyDomShow">
                <a-tree-select v-model:value="data.form.superiorRuleBase" show-search tree-node-filter-prop="title"
                    :tree-data="data.ruleTreeOptions" placeholder="请输入查询内容" @select="ruleNodeClick">
                </a-tree-select>
            </a-form-item>
            <a-form-item label="上级规则包" v-show="!data.copyDomShow">
                <span>{{ data.form.superiorRuleBase || '无' }}</span>
            </a-form-item>
            <a-form-item label="规则包描述">
                <a-input autocomplete="off" v-model:value="data.form.ruleBaseDesc"></a-input>
            </a-form-item>
        </a-form>
    </a-modal>
    <a-modal v-if="isModalVisible" title="新增规则" :visible="isModalVisible" @ok="handleModalOk"
        @cancel="isModalVisible = false" :width="600">
        <div style="max-height: 60vh; overflow-y: auto;">
            <RuleAdd ref="ruleAddComponent" :ruleBaseId="ruleBaseAddId" :rulePackageId="rulePackageAddId" />
        </div>
    </a-modal>
</template>
<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import {
    engineeringTreeList, engineeringTreeListAll, importEng, getSaveOrUpdateRuleFolderInfo,
    folderSsaveOrUpdate,
    ruleBaseFolderDelete,
    getEngineeringByUuid,
    list,
    treeList,
    ruleBaseFolderCopySave
} from '@/api/rule_base'
import { ruleModalInit } from "@/api/rule_editor";
import qs from 'qs'
import RuleAdd from "@/businessComponents/ruleBaseManage/RuleAdd";
import { collectOrCancelCommonUse, CollectObjectType } from '@/api/dashboardApi'
import { PlusOutlined } from '@ant-design/icons-vue';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";
//规则包相关逻辑
// 给html元素添加特定类，以使规则编辑及详情页面上的可滚动区域在顶部栏之下
document?.querySelector('html')?.classList.add('layout-read-write')
const isLoading = ref(false);
const route = useRoute()
const router = useRouter()
const ruleBaseId = ref('')
//返回页脚
const backPage = ref(1);
//权限判断

// 计算属性：判断是否显示侧边栏切换按钮
const isShowToggleBtn = computed(() => {
    // 获取当前路由路径
    const path = route.path;
    // 检查路径是否只包含ruleBase-xxx格式且不包含rulePackage部分
    // 例如：/ruleBase-123 显示按钮，但 /ruleBase-123/rulePackage-456 不显示按钮
    return /^\/ruleBase-[^\/]+$/.test(path);
});

// 监听路由变化
watch(
    () => route.path,
    () => {
        // 如果从有按钮的页面切换到无按钮的页面，且侧边栏已收起，则自动展开
        if (!isShowToggleBtn.value && isCollapsed.value) {
            isCollapsed.value = false;
            sidebarWidth.value = EXPANDED_WIDTH;
            
            // 通知父组件更新宽度
            if (typeof updateWidth === 'function') {
                updateWidth(sidebarWidth.value);
            }
        }
    }
);

watch(
    () => route.params.ruleBaseId,
    (value, oldValue) => {
        if (typeof value === 'string' && value !== oldValue) {
            ruleBaseId.value = value
            if (route.query.backPage) {
                backPage.value = route.query.backPage
            }
            getRulePackageTree(value)
        }
    },
    {
        immediate: true,
    }
)
onUnmounted(() => {
    localStorage.setItem('ruleTabLists', '');
    localStorage.setItem('detailTabLists', '');
    isLoading.value = false;
})
// 规则包结构树
const rulePackageTree = ref([])
const modelTree = ref([])
const dictTree = ref([])
// 搜索前结构树
const rulePackageTreeAll = ref([])
const modelTreeAll = ref([])
const dictTreeAll = ref([])

// 提取常量以便集中管理和修改
const ALL_RULES_TEXT = '全部规则';
const RULE_LIBRARY_TEXT = '所有规则库';

// 规则库名称
const ruleBaseName = ref('')
//规则库id
const ruleBaseAlwaysUsedId = ref('')
const ruleBaseData = ref({})

// 初始化expandedKeys为空数组，稍后会根据树形结构动态设置
const expandedKeys = ref<string[]>([]);

//设置常用判断开关
const ifAlwaysUsed = ref(false);
// 获取规则包结构树
function getRulePackageTree(uuid = ruleBaseId.value) {
    isLoading.value = true;

    // 统一的树形数据处理函数
    function formatTreeData(data, treeEngUuid = '') {
        let key = 0;

        function processNode(nodes) {
            if (!nodes) return [];

            return nodes.map(node => {
                // 设置节点的key
                node.key = node.isRootNode ? 'root' : key.toString();
                key++;

                // 规范化节点名称
                if (node.folderName === '工作空间') {
                    node.folderName = RULE_LIBRARY_TEXT;
                    if (!node.isRootNode) {
                        node.isRootNode = true;
                        node.key = 'root';
                    }
                }

                // 处理工程UUID
                if (treeEngUuid && node.iconCls === "ico_rule_project") {
                    node.treeEngUuid = node.engUuid;
                } else if (treeEngUuid && node.iconCls === null) {
                    node.treeEngUuid = treeEngUuid;
                } else {
                    node.treeEngUuid = node.engUuid;
                }

                // 确保UUID存在
                if (!node.uuid) {
                    node.uuid = 'all';
                }

                // 递归处理子节点
                if (node.children && node.children.length > 0) {
                    node.children = processNode(node.children);
                }

                return node;
            });
        }

        return processNode(data);
    }

    // 设置默认状态
    const setupDefaultState = (nodeName = ALL_RULES_TEXT) => {
        // 默认展开根节点
        if (!expandedKeys.value.includes('root')) {
            expandedKeys.value = ['root'];
        }
        // 默认选中根节点
        treeChildClick.value = 'root';
        // 设置默认选中节点名称
        selectedNodeName.value = nodeName;
    };

    // 处理全部规则库模式
    if (uuid === 'all') {
        /*const allUuids = "8a2d8e9f7e974458017ebf8485360000,8a2d8e9f83c15c020183c4dea5ab0116,8a2d8e9f7fb15561017fb4cc027300d6,8a2d8e9f809cf2c00180b1b53c1e09d5,8a2d8e9f809cf2c00180b27502200adb,8a2d8e9f7fb15561017fb581bf0100d9,8a2d8e9f8466a2c001849d44eb370799,8a2d8e9f83f01c59018409663d1c00eb,8a2d8e9f83888c180183ba5dbe830067,8a2d8e9f83888c180183ba5c854a0054,8a2d8e9f783a8c660179e4851e710bd8,8a2d8e9f7f7305b9017f7324fcb10036,8a2d8e9f7c6d75cc017c6f684228000b,8a2d8e9f7c6d75cc017c6f6c78f20018,8a2d8e9f7c6d75cc017c6f6e66eb0023,8a2d8e9f7c6d75cc017c6f6678c80000,8a2d8e9f8441a9bb018450c354c6000f,8a2d8e9f87bd1a550187c6607d58002d,8a2d8e9f8441a9bb018450991f480008,8a2d8e9f87bd1a550187c665fd080034,8a2d8e9f885141dd0188c392e6c927d8,";
*/
        engineeringTreeListAll({uuid, state: 0}).then((res) => {
            const treeData = formatTreeData(res.data);

            rulePackageTree.value = treeData;
            rulePackageTreeAll.value = treeData;
            ruleBaseName.value = RULE_LIBRARY_TEXT;
            treeChildClick.value = '0';

            setupDefaultState();
            isLoading.value = false;
        }).catch(() => {
            message.error('获取规则包树失败');
            isLoading.value = false;
        });

        return;
    }

    // 处理需求UUID模式
    if (uuid.indexOf('demandUuid') !== -1) {
        list({
            businessLine: uuid.split('demandUuid-')[1].split('businessUuid-')[1],
            page: 1,
            several: 10000,
        }).then((res) => {
            const engUuids = res.data.data.map(item => item.uuid.split(",")).flat().join(",");

            engineeringTreeList({
                uuid: engUuids,
                state: "0",
                page: 1,
                several: 10000,
            }).then((res) => {
                const treeData = formatTreeData(res.data);

                rulePackageTree.value = treeData;
                rulePackageTreeAll.value = treeData;
                ruleBaseName.value = RULE_LIBRARY_TEXT;
                treeChildClick.value = '0';

                setupDefaultState(RULE_LIBRARY_TEXT);
                isLoading.value = false;
            }).catch(() => {
                message.error('获取规则包树失败');
                isLoading.value = false;
            });
        }).catch(() => {
            message.error('获取规则包列表失败');
            isLoading.value = false;
        });

        return;
    }

    // 处理标准模式
    engineeringTreeList({uuid, state: 0}).then((res) => {
        const treeData = res?.data?.[0]?.children?.[0];

        if (treeData) {
            // 处理基础数据
            ruleBaseData.value = treeData;

            ruleBaseName.value = treeData?.folderName || RULE_LIBRARY_TEXT;
            ruleBaseAlwaysUsedId.value = treeData?.engUuid;
            ifAlwaysUsed.value = treeData?.ifSetCommonUsed;

            // 创建根节点并附加子节点
            const rootNode = {
                key: 'root',
                folderName: treeData?.folderName,
                children: formatTreeData(treeData.children, treeData.engUuid),
                uuid: ruleBaseId.value,
                isRootNode: true,
                treeEngUuid: treeData.engUuid,
                engUuid: treeData.engUuid
            };

            rulePackageTree.value = [rootNode];
            rulePackageTreeAll.value = [rootNode];

            setupDefaultState();
        }

        isLoading.value = false;

        // 加载模型和数据字典
        // loadModelAndDictData(uuid);
    }).catch(() => {
        message.error('获取规则包树失败');
        isLoading.value = false;
    });
}

// 加载模型和数据字典数据
function loadModelAndDictData(uuid) {
    if (uuid === 'all' || uuid.indexOf('demandUuid') !== -1) {
        return;
    }
    ruleModalInit(uuid).then((res) => {
        const data = res.data || {};
        const { complexModels, modelDomains } = data || {};

        // 处理模型数据
        if (complexModels && complexModels.length > 0) {
            const modelTreeData = complexModels.map((model, i) => {
                const childNodes = [...(model.fieldsList || []), ...(model.methodsList || [])].map((field, j) => ({
                    name: field.name + new Date().getTime() + i + j,
                    viewName: field.viewName
                }));

                return {
                    children: childNodes,
                    name: model.name + new Date().getTime() + i,
                    viewName: model.viewName
                };
            });

            modelTree.value = modelTreeData;
            modelTreeAll.value = modelTreeData;
        }

        // 处理数据字典
        if (modelDomains && modelDomains.length > 0) {
            const dictTreeData = modelDomains.map((domain, i) => {
                const childNodes = (domain.domainAttributes || []).map((attr, j) => ({
                    name: attr.name + new Date().getTime() + i + j,
                    viewName: attr.viewName
                }));

                return {
                    children: childNodes,
                    name: domain.name + new Date().getTime() + i,
                    viewName: domain.viewName
                };
            });

            dictTree.value = dictTreeData;
            dictTreeAll.value = dictTreeData;
        }
    }).catch(error => {
        console.error('加载模型数据失败', error);
    });
}

const activeName = ref('first');
const tabClick = (key: string) => {
    //切换tab时重置查询输入内容
    let val = { target: { value: '' } }
    onSearchChange(val);
}
const glossary = ref('model');
const searchValue = ref('');
const onSearchChange = (e) => {
    // 保存根节点的key，以便保持根节点展开
    const rootNodeKey = rulePackageTreeAll.value[0]?.key === 'root' ? 'root' : '0';

    // 重置展开状态，但确保根节点保持展开
    expandedKeys.value = [rootNodeKey];

    searchValue.value = e.target.value;
    rulePackageTree.value = searchTree(rulePackageTreeAll.value, searchValue.value, true)
    modelTree.value = searchTree(modelTreeAll.value, searchValue.value, true)
    dictTree.value = searchTree(dictTreeAll.value, searchValue.value, true)
    /* if(activeName.value === 'first'){//规则包

     }else{//词汇表

     }*/
}
function searchTree(tree, keyword, includeChildren = false) {
    if (!tree || !Array.isArray(tree)) return [];

    const newTree = []

    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        let title = node.viewName ? node.viewName : node.folderName;

        // 处理根节点的特殊情况
        if (node.isRootNode) {
            // 如果根节点本身匹配关键字或者需要包含所有子节点
            if (!keyword || title.includes(keyword)) {
                // 搜索子节点
                const childResults = node.children ? searchTree(node.children, keyword, true) : [];

                // 如果根节点匹配或子节点有匹配结果，则保留根节点
                if (childResults.length > 0 || !keyword || title.includes(keyword)) {
                    newTree.push({
                        ...node,
                        children: childResults
                    });

                    // 确保根节点被展开
                    if (!expandedKeys.value.includes(node.key)) {
                        expandedKeys.value.push(node.key);
                    }
                }
            } else {
                // 根节点不匹配，但需要检查子节点
                const childResults = node.children ? searchTree(node.children, keyword, true) : [];

                // 如果子节点有匹配结果，则保留根节点
                if (childResults.length > 0) {
                    newTree.push({
                        ...node,
                        children: childResults
                    });

                    // 确保根节点被展开
                    if (!expandedKeys.value.includes(node.key)) {
                        expandedKeys.value.push(node.key);
                    }
                }
            }
        } else {
            // 非根节点的普通处理
            if (title && title.includes(keyword)) {
                // 如果当前节点符合条件，则将其复制到新的树形结构中，并根据 includeChildren 参数决定是否将其所有子节点也复制到新的树形结构中
                newTree.push({ ...node, children: includeChildren ? searchTree(node.children || [], '', true) : [] })
            } else if (node.children) {
                // 如果当前节点不符合条件且存在子节点，则递归遍历子节点，以继续搜索
                const result = searchTree(node.children, keyword, true)
                if (result.length > 0) {
                    //如果搜索条件匹配字段，展开节点
                    if (!expandedKeys.value.includes(node.key)) {
                        expandedKeys.value.push(node.key)
                    }
                    // 如果子节点中存在符合条件的节点，则将其复制到新的树形结构中
                    newTree.push({ ...node, children: result })
                }
            }
        }
    }

    return newTree
}

//导入规则包
const uploadHidden = ref(false);
const dialogUploadVisible = ref(false);
const importFun = () => {
    dialogUploadVisible.value = true;
    uploadHidden.value = false;
}





//文件处理
const upForm = ref(null)
const uploadFile = async (params) => {
    const file = params.file,
        aExtension = ["erule"],
        resFil = aExtension.filter((item) => {
            return file.name.indexOf(item) !== -1;
        });

    if (resFil.length === 0) {
        uploadHidden.value = false;
        message.error("只能上传erule文件！");
        return;
    }
    uploadHidden.value = true;
    const upForm1 = new FormData();
    upForm1.append("engUuid", ruleBaseId.value);
    upForm1.append("importFile", file);
    upForm1.append("fileName", file.name);
    //刷新文件上传状态，延时执行才会显示效果
    setTimeout(() => {
        params.onSuccess({ 'code': 200 }, file)
    })

    upForm.value = upForm1;
    // }
}
//确认框加载样式
const dialogUploadLoading = ref(false);
const dialogUploadSubmit = async () => {
    dialogUploadLoading.value = true;
    try {
        let res = await importEng(upForm.value);
        if (res.code === 20000) {
            message.success(res.data)
            //批量导入整个规则模型后需要延时刷新页面使数据全部更新
            setTimeout(() => {
                location.reload();
            }, 2000);

        } else {
            message.error(res.data);
        }
    } finally {
        dialogUploadVisible.value = false;
        dialogUploadLoading.value = false;

    }

}



//编辑左侧菜单栏规则包
const data = ref({
    form: {
        ruleBaseName: "",
        superiorRuleBase: "",
        ruleBaseDesc: "",
        extraProperty: "",
    },
    dialogFormVisible: false,
    dialogFormTitle: "",
    jData: {},
    copyDomShow: false,
    defaultKey: [],
    defaultKey2: [],
    clickTreeData2: null,
    ruleTreeOptions: [],
    rules: {
        ruleBaseName: [
            { required: true, message: "不能为空", trigger: "blur" },
            {
                pattern: /^[a-zA-Z_\u4e00-\u9fa5][\u4e00-\u9fa5a-zA-Z0-9_]*$/,
                message:
                    "规则包名称只能包括中文、数字、字母和下划线,并且不能以数字开头！",
            },
        ],
        ruleTree: [{ required: true, message: "不能为空", trigger: "blur" }],
    },
    submitLoading: false
})
const dropdownClick = (command, node) => {
    buttonShowClick.value = '';
    if (!node.data) {
        node.data = node;
    }
    const dialogFormTitle = {
        create: "新建规则包",
        update: "修改规则包",
        copy: "复制规则包",
    };
    if (command === "delete") {
        ruleBaseFolderDelete(
            qs.stringify({
                uuid: node.data.uuid,
            })
        ).then((res) => {
            if (res.code === 20000) {
                message.success('删除成功')
                // 删除成功后使用window.location.href强制页面刷新
                window.location.href = `/ruleBase-${node.data.engUuid}`;
            } else {
                message.error(res.data);
            }
        });
    } else {
        data.value.copyDomShow = false;
        data.value.dialogFormTitle = dialogFormTitle[command];
        data.value.jData.engUuid = node.data.engUuid;
        if (command === "create") {
            data.value.jData.uuid = "";
            data.value.jData.parentUuid = node.data.uuid;
        } else if (command === "update") {
            data.value.jData.uuid = node.data.uuid;
            data.value.jData.parentUuid = node.data.parent;
        } else if (command === "copy") {
            data.value.copyDomShow = true;
            data.value.jData.uuid = node.data.uuid;
        }
        getSaveOrUpdateRuleFolderInfo(data.value.jData).then((res) => {
            let sJ = 1;
            data.value.form.ruleBaseName = "";
            data.value.form.superiorRuleBase = "";
            data.value.form.ruleBaseDesc = "";
            data.value.jData.status = res.data.status ? res.data.status : "";
            data.value.jData.createdId = res.data.createdId ? res.data.createdId : "";
            data.value.jData.createdTimeStr = res.data.createdTimeStr
                ? res.data.createdTimeStr
                : "";
            data.value.jData.createdTime = res.data.createdTime
                ? res.data.createdTime
                : "";
            if (command === "update") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                data.value.form.ruleBaseName = res.data.folderName;
                data.value.form.ruleBaseDesc = res.data.descs;
                data.value.form.extraProperty = res.data.extraProperty;
                data.value.dialogFormVisible = true;
            } else if (command === "copy") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                // data.value.selectFolderName = res.data.parentFolderName;
                data.value.form.ruleBaseName = res.data.folderName;
                data.value.form.ruleBaseDesc = res.data.descs;
                const _parent = res.data.parent ? res.data.parent : res.data.engUuid;
                // tree select
                getEngineeringByUuid({
                    uuid: res.data.engUuid,
                }).then((res1) => {
                    copyFolderOper(res1.data.businessLine, _parent, sJ);
                });
            } else if (command === "create") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                data.value.dialogFormVisible = true;
            }
        });
    }
}
const setTreeList = (params) => {
    let startIndex = 0;
    _setAttirbute(params);
    function _setAttirbute(item) {
        item.filter((v) => {
            v.key = startIndex;
            startIndex += 1;
            if (v.children && v.children.length) {
                _setAttirbute(v.children);
            }
        });
    }
    return params;
};
const copyFolderOper = (businessLine, _parent, sJ) => {
    treeList({
        businessLine: businessLine,
    }).then((res) => {
        if (!data.value.form.superiorRuleBase) {
            data.value.form.superiorRuleBase = res.data[0].folderName;
            data.value.clickTreeData2 = res.data[0];
        }else {
            function _setDefauktKey(arr, fn, sj) {
                arr.filter((v) => {
                    fn(v);
                    if (v.children && v.children.length) {
                        _setDefauktKey(v.children, fn, sj++);
                    }
                });
            }
            _setDefauktKey(
                    res.data,
                    (v) => {
                        (v.uuid ? v.uuid : v.engUuid) === _parent && (data.value.clickTreeData2 = v);
                    },
                    sJ++
            );
        }
        function formatData(data1) {
            data1.forEach(res => {
                res.title = res.folderName;
                res.value = res.key;
                if (res.children) {
                    formatData(res.children)
                }
            })
        }
        let data1 = setTreeList(res.data);
        formatData(data1);
        data.value.ruleTreeOptions = data1;
        data.value.dialogFormVisible = true;
    });
}
const ruleNodeClick = (value, node) => {
    data.value.form.superiorRuleBase = node.folderName;
    data.value.clickTreeData2 = node;
}
const dialoForm = ref(null);
const saveOrUpdate = (formName) => {
    dialoForm.value.validate().then((valid) => {
        if (valid) {
            data.value.submitLoading = true;
            if (data.value.copyDomShow) {
                if (data.value.form.superiorRuleBase) {
                    /*if (data.value.clickTreeData2) {*/
                        ruleBaseFolderCopySave(
                            {
                                descs: data.value.form.ruleBaseDesc,
                                engUuid: data.value.clickTreeData2.engUuid,
                                folderName: data.value.form.ruleBaseName,
                                parent: data.value.clickTreeData2.uuid === 'all' ? '' : data.value.clickTreeData2.uuid,
                                parentFolderName: data.value.form.superiorRuleBase,
                                uuid: "",
                                status: data.value.jData.status,
                                createdId: data.value.jData.createdId,
                                createdTime: data.value.jData.createdTime,
                                createdTimeStr: data.value.jData.createdTimeStr,
                            },
                            data.value.clickTreeData2.engUuid,
                            data.value.jData.uuid
                        ).then((res) => {
                            data.value.submitLoading = false;
                            if (res.code === 20000) {
                                message.success(res.data);
                                data.value.dialogFormVisible = false;
                                data.value.copyDomShow = false;
                                getRulePackageTree();
                            } else {
                                message.error(res.data);
                            }
                        }).catch((res) => {
                            data.value.submitLoading = false;
                        });
                    /*} else {
                        message.error("请选择其他规则包");
                        data.value.submitLoading = false;
                    }*/
                } else {
                    message.error("请选择规则包");
                    data.value.submitLoading = false;
                }
            } else {
                folderSsaveOrUpdate({
                    descs: data.value.form.ruleBaseDesc,
                    engUuid: data.value.jData.engUuid,
                    folderName: data.value.form.ruleBaseName,
                    parent: data.value.jData.parentUuid === 'all' ? '' : data.value.jData.parentUuid,
                    parentFolderName: data.value.form.superiorRuleBase,
                    uuid: data.value.jData.uuid,
                    status: data.value.jData.status,
                    createdId: data.value.jData.createdId,
                    createdTime: data.value.jData.createdTime,
                    createdTimeStr: data.value.jData.createdTimeStr,
                    extraProperty: data.value.form.extraProperty,
                }).then((res) => {
                    if (res.code === 20000) {
                        message.success(res.data);
                        data.value.dialogFormVisible = false;

                        getRulePackageTree();
                    } else {
                        message.error(res.data);
                    }
                    data.value.submitLoading = false;
                }).catch((res) => {
                    data.value.submitLoading = false;
                });

            }
        }
    });
}

//点击按钮时不隐藏
const buttonShowClick = ref('');
const openChangeStyle = (open, node) => {
    if (open) {
        buttonShowClick.value = node.key;
    } else {
        buttonShowClick.value = '';
    }
}

//选中字体加粗
const treeChildClick = ref('');
//选中节点的名称
const selectedNodeName = ref(ALL_RULES_TEXT);
//生成唯一键，用于刷新查询到的table及返回规则列表
const clickKey = ref('');
const clickTreeChildStyle = (node: any) => {
    if (!node) return;

    if (node.key) {
        treeChildClick.value = node.key;
    }
    // 存储选中节点的名称
    if (node.isRootNode) {
        // 如果是根节点，则名称设置为"全部规则"
        selectedNodeName.value = ALL_RULES_TEXT;
    } else if (node.folderName && node.folderName !== RULE_LIBRARY_TEXT) {
        selectedNodeName.value = node.folderName;
    }
    clickKey.value = Math.random().toString(16).slice(2);

    // 点击节点时，处理展开/收起状态
    // 移除对node.children长度的检查，以确保任何具有children属性的节点都能响应点击
    if (node.key && node.children) {
        // 检查节点是否已经展开
        const isExpanded = expandedKeys.value.includes(node.key);

        // 判断是否为根节点
        const isRootNode = node.isRootNode;

        // 根节点不进行收起操作，只执行展开操作
        if (isRootNode) {
            // 如果是根节点且未展开，则展开
            if (!isExpanded) {
                expandedKeys.value = [...expandedKeys.value, node.key];
            }
        } else {
            // 非根节点正常切换展开/收起状态
            if (isExpanded) {
                // 如果已展开，则收起
                expandedKeys.value = expandedKeys.value.filter(key => key !== node.key);
            } else {
                // 如果未展开，则展开
                expandedKeys.value = [...expandedKeys.value, node.key];
            }
        }
    }
}

//新增规则
const isModalVisible = ref(false); // 对话框显示状态
// 规则新增组件
const ruleAddComponent = ref();

const { ruleTypes } = useRuleTypes()

//新增规则用传递id
const ruleBaseAddId = ref('');
const rulePackageAddId = ref('');
// 缓存node值，如果添加规则后增加左侧树选中样式
const treeChildAddClick = ref({});
const openAddModal = (type, node) => {
    treeChildAddClick.value = {};
    isModalVisible.value = true;
    ruleBaseAddId.value = node.treeEngUuid;
    //如果数id和包id一致，则不传递包id，否则会添加空路径
    if(node.treeEngUuid !== node.uuid){
        rulePackageAddId.value = node.uuid;
    }
    // 延迟执行，以确保 ruleAddComponent 已经挂载
    nextTick(() => {
        ruleAddComponent.value.form.ruleType = type
        //新建规则为规则流时，优先级变为规则流类型
        if (type === '5') {
            ruleAddComponent.value.ruleTypeChange(type);
        }
    })
    //打开对话框后隐藏下拉菜单
    buttonShowClick.value = '';
    treeChildAddClick.value = node;
}

// 处理modal ok 事件
function handleModalOk() {
    let submitFun = ruleAddComponent.value.handleSubmit;
    submitFun && submitFun((res) => {
        isModalVisible.value = false;
        treeChildClick.value = treeChildAddClick.value.key;
        // 更新选中节点名称
        if (treeChildAddClick.value.folderName) {
            selectedNodeName.value = treeChildAddClick.value.folderName;
        }
        router.push(generateLink(treeChildAddClick.value) + '&ruleAddUuid=' + res);
        treeChildAddClick.value = ''
    });
}

//设为/取消常用方法
const setAlwaysUsed = async () => {
    try {
        const res = await collectOrCancelCommonUse({
            uuid: ruleBaseAlwaysUsedId.value as string,
            type: CollectObjectType.RULE_BASE // 使用枚举类型代替硬编码的"1"
        })
        ifAlwaysUsed.value = !ifAlwaysUsed.value
        message.success(res.data)
    } catch (error) {
        message.error(ifAlwaysUsed.value ? '设为常用失败' : '取消常用失败')
        console.error('设为常用操作失败:', error)
    }
}
// 方法来生成 NuxtLink 的地址
const generateLink = (node: any) => {
    if (ruleBaseId.value === 'all' && node.folderName === RULE_LIBRARY_TEXT) {
        //所有规则库链接
        return '/ruleBase-all'
    } else if (ruleBaseId.value.indexOf('demandUuid') !== -1 && node.folderName === RULE_LIBRARY_TEXT) {
        //规则调整首页链接
        return `/ruleBase-${ruleBaseId.value}`
    } else if (ruleBaseId.value === 'all' && node.folderName !== RULE_LIBRARY_TEXT) {
        //规则浏览规则包链接
        return `/ruleBase-all/rulePackage-${node.uuid}?ruleBaseId=${node.treeEngUuid}&clickKey=${clickKey.value}&rulePackageName=${node.folderName}`
    } else if (ruleBaseId.value.indexOf('demandUuid') !== -1 && node.folderName !== RULE_LIBRARY_TEXT) {
        //规则调整规则包链接
        return `/ruleBase-${ruleBaseId.value}/rulePackage-${node.uuid}?ruleBaseId=${node.treeEngUuid}&backPage=${backPage.value}&rulePackageName=${node.folderName}&clickKey=${clickKey.value}`
    } else if (ruleBaseId.value !== 'all' && ruleBaseId.value.indexOf('demandUuid') === -1 && node.folderName !== RULE_LIBRARY_TEXT) {
        //正常链接
        if (ruleBaseId.value === node.uuid) {
            // 当 ruleBaseId 和 node.uuid 相同时，不添加 rulePackage 路径
            return `/ruleBase-${ruleBaseId.value}?clickKey=${clickKey.value}&rulePackageName=${node.folderName}`
        }
        return `/ruleBase-${ruleBaseId.value}/rulePackage-${node.uuid}?clickKey=${clickKey.value}&rulePackageName=${node.folderName}`
    }
    return '#'
}

// 添加侧边栏宽度属性，默认为259px，与原始宽度保持一致
const sidebarWidth = ref(259);
// 侧边栏是否收起的状态
const isCollapsed = ref(false);
// 侧边栏展开和收起的宽度值
const EXPANDED_WIDTH = 259;
const COLLAPSED_WIDTH = 13; // 增加宽度，以便能显示图标和简短内容

// 切换侧边栏展开/收起状态
const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value;
    sidebarWidth.value = isCollapsed.value ? COLLAPSED_WIDTH : EXPANDED_WIDTH;
    
    // 通知父组件侧边栏展开/收起状态变化
    if (typeof updateWidth === 'function') {
        updateWidth(sidebarWidth.value);
    }
    
    // 父元素宽度更新后，需要微调内部元素宽度
    nextTick(() => {
        if (document.querySelector('.lark-virtual-tree')) {
            document.querySelectorAll('.lark-virtual-tree').forEach(el => {
                (el as HTMLElement).style.width = `${sidebarWidth.value-20}px`;
            });
        }
    });
};

// 从本地存储读取之前保存的收缩状态
onMounted(() => {
    // 确保侧边栏默认展开
    isCollapsed.value = false;
    sidebarWidth.value = EXPANDED_WIDTH;
    
    // 通知父组件初始宽度
    if (typeof updateWidth === 'function') {
        updateWidth(sidebarWidth.value);
    }
    
    // 初始化时调整内部元素宽度
    nextTick(() => {
        if (document.querySelector('.lark-virtual-tree')) {
            document.querySelectorAll('.lark-virtual-tree').forEach(el => {
                (el as HTMLElement).style.width = `${sidebarWidth.value-20}px`;
            });
        }
    });
});

// 从父组件获取宽度变化信息
const updateSidebarWidth = (width: number) => {
    if (width && width > 0) {
        sidebarWidth.value = width;
    }
};

// 尝试通过inject获取父组件暴露的currentWidth
const updateWidth = inject('updateSidebarWidth', updateSidebarWidth);

// 暴露方法和属性给父组件
defineExpose({
    setAlwaysUsed,
    importFun,
    ifAlwaysUsed,
    ruleBaseName,
    ruleBaseAlwaysUsedId,
    selectedNodeName,
    treeChildClick,
    updateSidebarWidth // 暴露更新宽度的方法
});

</script>
<style lang="scss" scoped>
.custom-tree-node {
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: auto;
    padding: 4px 2px 4px 2px;
    border-radius: 4px;
    display: flex;
}

.custom-tree-node2 {
    width: 200px;
    white-space: nowrap;
    /* 确保文本不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 使用省略号表示超出的文本 */
}

.nuxt-link-style {
    color: var(--yq-text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    /* 使用省略号表示超出的文本 */
}

.tree-y-class {
    height: calc(100vh - 15vh);
    overflow-y: auto;
    border: 1px solid #d9d9d9;
}

.lark-virtual-tree {
    position: relative;
    height: calc(100vh - 15vh);
    overflow: auto;
}

.catalogTreeItem-module_CatalogItem_xkX7p {
    padding: 0 0 0 0
}
.catalogTreeItem-module_hasChildren_TrI8X {
    line-height: 26px;
    width: 24px;
    margin: 6px 2px 5px 6px;
}

.catalogTreeItem-module_collapseIconWrapper_XcS8B {
    margin-top: 2px;
}

/*.catalogTreeItem-module_hasChildren_TrI8X :hover {*/
/*    background-color: var(--yq-yuque-grey-5);*/
/*    border-radius: 4px;*/
/*}*/

.catalogTreeItem-module_btnItem_HrYZm {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 14px;
    color: var(--yq-text-primary);
    transition: background .35s ease-in-out;
    border-radius: 6px;
}

.catalogTreeItem-module_btnItem_HrYZm:hover {
    background-color: var(--yq-yuque-grey-4)
}

.rule-tree-hover-style :hover .catalogTreeItem-module_btnItem_HrYZm {
    display: flex;
}

.catalogTreeItem-module_btnItem_HrYZm {
    display: none;
}

/**选中字体加粗*/
:deep(.rule-tree-class) {
    .selected-tree-style {
        font-weight: 700;
        background-color: var(--yq-bg-primary-hover);
    }

    .ant-tree-switcher {
        width: 30px;
    }

    /**内容过多时隐藏*/
    .ant-tree-node-content-wrapper {
        padding: 0;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .show-more-button {
        display: flex;
    }

    .hide-more-button {
        display: none;
    }

    /* 覆盖选中节点的样式 */
    .ant-tree-node-selected {
        background: transparent !important;
        color: inherit !important;
    }
}

.upload-file-eruleFile-style .ant-upload .ant-upload-btn {
    display: none;
}

.upload-file-eruleFile-styleShow .ant-upload .ant-upload-btn {
    display: block;
}

.form_flex_div_item {
    margin-bottom: 5px;
}

.upload-file-eruleFile-style .ant-upload .ant-upload-btn {
    display: none;
}

.upload-file-eruleFile-styleShow .ant-upload .ant-upload-btn {
    display: block;
}

.form_flex_div_item {
    margin-bottom: 5px;
}

/* 隐藏拖动条的可拖动图标 */
.ReaderLayout-module_dragbar_NUlrA {
    cursor: default !important;
}

.ReaderLayout-module_dragbar_NUlrA:hover {
    cursor: default !important;
    background-color: transparent !important;
}
.node-title-icon{
    margin-right: 3px;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}
.root-title-icon{
    margin-right: 3px;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
}

:deep(.ant-form-item-explain-error) {
    color: #ff4d4f;
}

:deep(.ant-tree-indent-unit) {
  width: 12px !important; /* 保持缩进距离为12px */
}

:deep(.ant-tree-treenode) {
  padding: 0 !important;
}

:deep(.ant-tree-switcher) {
  width: 24px !important; /* 增加宽度为24px，给箭头更多空间 */
  z-index: 5 !important; /* 确保箭头在较高层级 */
  position: relative !important;
}

:deep(.catalogTreeItem-module_hasChildren_TrI8X) {
  z-index: 10 !important;
  margin-right: 4px !important;
  position: relative !important;
}

:deep(.catalogTreeItem-module_collapseIconWrapper_XcS8B) {
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
}

/* 增加切换图标的可点击区域和鼠标样式 */
:deep(.catalogTreeItem-module_hasChildren_TrI8X) {
  cursor: pointer;
}

/* 侧边栏展开/收缩按钮样式 */
.sidebar-toggle-btn {
  position: absolute;
  right: -12px;
  top: 40px;
  width: 24px;
  height: 24px;
  background-color: var(--yq-bg-primary);
  border: 1px solid var(--yq-yuque-grey-4);
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 收缩状态下按钮的位置调整 */
.btn-collapsed {
  width: 24px;
  right: -12px;
}

.btn-collapsed .toggle-btn-text {
  display: none;
}

.sidebar-toggle-btn:hover {
  background-color: var(--yq-bg-primary-hover);
}

.toggle-btn-text {
  font-size: 12px;
  margin-right: 2px;
}

.sidebar-toggle-btn .toggle-icon {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.sidebar-toggle-btn .icon-expanded {
  transform: rotate(-90deg);
}

/* 收缩状态下的短标题样式 */
.short-title {
  font-weight: bold;
  text-align: center;
  display: inline-block;
  width: 100%;
}

/* 收缩状态下隐藏按钮 */
:deep(.catalogTreeItem-module_btnItem_HrYZm) {
  display: v-bind("isCollapsed ? 'none' : ''");
}

/* 收缩状态下的搜索图标 */
.collapsed-search-icon {
  position: absolute;
  left: 10px;
  top: 58px;
  z-index: 10;
  color: var(--yq-text-secondary);
}

/* 收缩状态下搜索框样式调整 */
:deep(.ant-input[type="text"]) {
  transition: all 0.3s;
}

/* 收缩状态下树节点的样式 */
.collapsed-tree-node {
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: auto;
  padding: 4px 2px 4px 2px;
  border-radius: 4px;
  display: flex;
  justify-content: center; /* 收缩状态下居中显示 */
  align-items: center;
}

/* 收缩状态下树节点图标样式 */
:deep(.ant-tree-treenode-switcher-close),
:deep(.ant-tree-treenode-switcher-open) {
  padding: 0 !important;
}

:deep(.ant-tree-indent) {
  display: v-bind("isCollapsed ? 'none' : 'inline-block'");
}

:deep(.ant-tree-switcher) {
  display: v-bind("isCollapsed ? 'none' : 'inline-block'");
}

/* 调整收缩状态下的图标样式 */
.node-title-icon, .root-title-icon {
  margin-right: v-bind("isCollapsed ? '0' : '3px'");
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.short-title {
  font-weight: bold;
  text-align: center;
  display: inline-block;
  padding-left: 2px;
}

/* 收缩状态下隐藏按钮 */
:deep(.catalogTreeItem-module_btnItem_HrYZm) {
  display: v-bind("isCollapsed ? 'none' : ''");
}
</style>
