<!-- 字典项修改 -->

<template>
    <div id="data_edit">
        <a-form :model="form" :rules="rules" ref="ruleForm" laba-width="120px" class="demo-ruleForm" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item name="typeCode" label="字典类型">
                <a-select v-model:value="form.typeCode" placeholder="请选择">
                    <a-select-option v-for="item in selOption" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item name="code" label="代码">
                <a-input autocomplete="off" v-model:value="form.code" placeholder="代码" />
            </a-form-item>
            <a-form-item name="name" label="名称">
                <a-input autocomplete="off" v-model:value="form.name" placeholder="名称" />
            </a-form-item>
            <a-form-item name="arrmenu1" label="父字典类型">
                <a-select v-model:value="form.arrmenu1" placeholder="请选择" @change="selectGet">
                    <a-select-option v-for="item in sel1Option" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item name="arrmenu2" label="父字典">
                <a-select v-model:value="form.arrmenu2" placeholder="请选择">
                    <a-select-option v-for="item in pardicOption" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item name="isModify" label="允许修改">
            <a-select v-model:value="form.isModify" placeholder="请选择">
                <a-select-option value="0">是</a-select-option>
                <a-select-option value="1">否</a-select-option>
            </a-select>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
    import { dictionSub,dictionSel,dictionParentsel,dictionupd} from '@/api/diction_entry'

    const message = inject('message')
    const selOption = ref([]);
    const sel1Option = ref([]);
    const pardicOption = ref([]);
    const router = useRouter();

    const form = reactive({
        arrmenu: null,
        code: "",
        name: "",
        arrmenu1: null,
        arrmenu2: null,
        isModify:null,
        typeCode:null
    });


    const rules = {
        typeCode: [
            { required: true, message: '字典类型不能为空', trigger: 'change' }
        ],
        code: [
            { required: true, message: '代码不能为空', trigger: 'blur' }
        ],
        name: [
            { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
    };


    const props = defineProps({
        datar: {
            type: Object,
        },
    });
    //字典类型和父字典类型下拉列表
    const getdictionsel = () => {
        dictionSel().then((res)=>{
            let dicpar={
                name:'请选择',
                code:''
            }
            res.data.unshift(dicpar);
            selOption.value = res.data;
            sel1Option.value = res.data
        })
    }
    watch(
        () => props.datar,
        (newValue) => {
            if (newValue) {
                let newForm = {
                    arrmenu: null,
                    code: "",
                    name: "",
                    arrmenu1: null,
                    arrmenu2: null,
                    isModify:null,
                    typeCode:null
                };
                getdictionsel()
                Object.assign(form, newForm);
                let pars = {
                    uuid:props.datar.uuid
                }
                dictionupd(pars).then((res)=>{
                    Object.assign(form, res.data);
                })

            }
        },
        {
            immediate: true,
        }
    );


    const submitFun = (callback) => {
        ruleForm.value.validate().then(() => {
            let par = {
                code:form.code,
                name:form.name,
                parentCode:form.arrmenu2,   //父字典
                parentTypeCode:form.arrmenu1,　//父字典类型
                typeCode:form.typeCode,　　//字典类型
                isModify:form.isModify,
                createdId:form.createdId,
                createdTimeStr:form.createdTimeStr,
                uuid:form.uuid,
            };
            dictionSub(par).then((res) => {
                if (res.code === 20000) {
                    if(res.data == "当前字典类型下存在同名的代码或名称，请确认！"){
                        message.success(res.data);
                        if (typeof callback === 'function') {
                            callback(false);
                        }
                    }else{
                        message.success(res.data);
                        if (typeof callback === 'function') {
                            callback(true);
                        }
                    }
                } else {
                    message.error(res.data);
                    if (typeof callback === 'function') {
                        callback(false);
                    }
                }
            })
        }).catch((error) => {
            console.log(error);
            if (typeof callback === 'function') {
                callback(false);
            }
        })
    };


    const ruleForm = ref(null);

    const selectGet = (vid) => {
        let pars = {
            parentCode: "",
            parentTypeCode: "",
            typeCode: vid,
        };
        form.arrmenu2 = null;
        dictionParentsel(pars).then((res) => {
            pardicOption.value = res.data;
        });
    }
    defineExpose({
        submitFun
    });
</script>

<style lang="scss" scoped>
    #data_edit ::v-deep {

        .ant-input,
        .ant-select,
        .ant-upload-dragger,
        .ant-upload-list,
        .ant-textarea {
            width: 400px;
        }

        .ant-textarea {
            height: 400px;
        }

        .ant-upload-dragger {
            height: 100px;
        }

        .ant-upload-dragger .anticon-upload {
            margin: 0;
            line-height: 50px;
            font-size: 50px;
        }

        .jarUpload.hidden .ant-upload {
            display: none;
        }

        .ant-tabs {
            width: 70%;
            min-height: 100px;
            margin: 30px 30px 30px 120px;
        }

        .ant-textarea,
        textarea {
            height: 150px !important;
        }

        .ant-tabs-card {
            box-shadow: unset;
        }

        .params_form {
            border-bottom: 1px solid #dcdfe6;

            h4 {
                font-weight: unset;

                span {
                    margin-left: 100px;
                }

                span.primary {
                    color: #409eff;
                }

                span.danger {
                    color: #f56c6c;
                }
            }

            label {
                color: #99a9bf;
            }

            span {
                color: #606266;
            }
        }

        .params_form:last-child {
            border-bottom: unset;
        }

        .form_flex_div,
        .form_flex_div .ant-form-item {
            display: flex;
            flex-direction: row;
            flex: 1;

            .ant-form-item {
                overflow: auto;
            }
        }

        .ant-tree {
            font-size: 14px;
        }

        .ant-main {
            padding: 0 0 10px 0;
        }
    }
</style>
