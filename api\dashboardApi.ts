/**
 * @description 工作台相关接口
 */

import request from '@/utils/request'

/**
 * 收藏对象类型枚举
 */
export enum CollectObjectType {
  /**
   * 规则库
   */
  RULE_BASE = "1",

  /**
   * 规则包
   */
  RULE_PACKAGE = "2",

  /**
   * 规则
   */
  RULE = "3"
}

/**
 * 获取收藏对象类型的描述
 * @param type 类型值
 * @returns 类型描述
 */
export function getCollectTypeDesc(type: string): string {
  switch (type) {
    case CollectObjectType.RULE_BASE:
      return '规则库';
    case CollectObjectType.RULE_PACKAGE:
      return '规则包';
    case CollectObjectType.RULE:
      return '规则';
    default:
      return '未知类型';
  }
}

export type TemplateType = 0 | 1 // 0: 我的模版，1: 共享模版

export interface RecentListParams {
  action: 'edit' | 'view'
  engUuid?: string
  ruleType?: string
  loginId?: string
  moreFlag?: boolean
}

/**
 * @description 获取最近编辑过或访问过的规则记录列表
 * @param params 请求参数
 * @returns 最近列表
 */
export function getRecentList(params: RecentListParams): Promise<ApiResponse<any[]>> {
  return request({
    url: '/sys/recent/list',
    method: 'get',
    params
  })
}

export interface DeleteRecentParams {
  uuid: string
}

/**
 * @description 移除最近记录
 * @param params 请求参数
 * @returns 移除结果
 */
export function deleteRecent(params: DeleteRecentParams): Promise<ApiResponse<any>> {
  return request({
    url: '/sys/recent/delete',
    method: 'delete',
    params
  })
}

export interface CollectRuleParams {
  uuid: string; // 对象的唯一标识符
  type: CollectObjectType; // 收藏对象的类型，使用枚举类型
}

/**
 * @description 收藏或取消收藏规则
 * @param params 请求参数，包含收藏对象的uuid和type
 * @param params.uuid 对象的唯一标识符
 * @param params.type 收藏对象的类型，使用 CollectObjectType 枚举
 * @returns 收藏结果
 */
export function collectRule(params: CollectRuleParams): Promise<ApiResponse<any>> {
  return request({
    url: '/sys/collect/collectOrCancelCollect',
    method: 'get',
    params
  });
}

export interface CollectListParams {
  ruleType?: string
  ruleName?: string
}

/**
 * 收藏项接口
 */
export interface CollectItem {
  uuid: string;
  loginId: string;
  userName: string;
  engUuid: string;
  folderUuid: string | null;
  type: string;
  createdTime: string;
  name: string;
  packageNameAll: string;
  ifShowPage: string;
}

/**
 * 收藏列表响应接口
 */
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

/**
 * @description 获取收藏列表
 * @param params 请求参数
 * @returns 收藏列表
 */
export function getCollectList(params?: CollectListParams): Promise<ApiResponse<CollectItem[]>> {
  return request({
    url: '/sys/collect/list',
    method: 'get',
    params
  })
}

export interface SetShowPageParams {
  uuid: string,
  type: string
}

/**
 * @description 设置是否展示在首页常用规则库
 * @param params 请求参数
 * @returns 设置结果
 */
export function collectOrCancelCommonUse(params: SetShowPageParams) {
  return request({
    url: '/sys/commonUse/collectOrCancelCommonUse',
    method: 'get',
    params
  })
}

/**
 * @description 获取首页展示的常用规则库列表
 * @returns 常用规则库列表
 */
export function getCollectPageList() {
  return request({
    url: '/sys/commonUse/list',
    method: 'get'
  })
}

export interface RecyclesListParams {
  ruleName?: string,
  page: number,
  several: number
}

/**
 * @description 获取回收站列表
 * @param params 请求参数
 * @returns 回收站列表
 */
export function getRecyclesList(params?: RecyclesListParams) {
  return request({
    url: '/sys/recycles/list',
    method: 'get',
    params
  })
}

export interface RestoreRecycleParams {
  uuid: string
}

/**
 * @description 还原回收站内容
 * @param params 请求参数
 * @returns 还原结果
 */
export function restoreRecycle(params: RestoreRecycleParams) {
  return request({
    url: '/sys/recycles/restore',
    method: 'get',
    params
  })
}

export interface CompletelyDeleteRecycleParams {
  uuid: string
}

/**
 * @description 彻底删除回收站内容
 * @param params 请求参数
 * @returns 删除结果
 */
export function completelyDeleteRecycle(params: CompletelyDeleteRecycleParams) {
  return request({
    url: '/sys/recycles/comDelete',
    method: 'delete',
    params
  })
}

export interface RecordAccessParams {
  ruleUuid: string
}

/**
 * @description 记录规则访问历史
 * @param params 请求参数
 * @returns 记录结果
 */
export function recordAccess(params: RecordAccessParams) {
  return request({
    url: '/sys/recent/recordAccess',
    method: 'get',
    params
  })
}

export interface GlobalSearchParams {
  ruleName: string,
  type:string,
}

/**
 * @description 全局搜索
 * @param params 请求参数
 * @returns 搜索结果列表
 */
export function globalSearch(params: GlobalSearchParams) {
  return request({
    url: '/sys/globalSearch/list',
    method: 'get',
    params
  })
}

export interface SaveRuleTemplateParams {
  uuid?: string
  engUuid: string
  templateName: string
  ruleType: string
  ifCommonTemplate?: TemplateType // 0: 我的模版，1: 共享模版
  jsonContentObject?: JSON
}

/**
 * @description 新增或编辑规则模版
 * @param data 请求参数
 * @returns 保存结果
 */
export function saveRuleTemplate(data: SaveRuleTemplateParams) {
  return request({
    url: '/sys/template/saveOrUpdate',
    method: 'post',
    data
  })
}

export interface GetTemplateListParams {
  engUuid?: string
  ifCommonTemplate?: TemplateType
}

/**
 * @description 查询规则模版列表
 * @param params 请求参数
 * @returns 模版列表
 */
export function getTemplateList(params?: GetTemplateListParams) {
  return request({
    url: '/sys/template/list',
    method: 'get',
    params
  })
}

export interface GetTemplateDetailParams {
  uuid: string
}

/**
 * @description 获取模版详细信息
 * @param params 请求参数
 * @returns 模版详情
 */
export function getTemplateDetail(params: GetTemplateDetailParams) {
  return request({
    url: '/sys/template/getRuleTemplateDetailByUuid',
    method: 'get',
    params
  })
}

// 删除模板
export function deleteTemplate(params: { uuid: string }) {
  return request({
    url: '/sys/template/delete',
    method: 'delete',
    params
  })
}

export interface SetCommonTemplateParams {
  uuid: string
}

/**
 * @description 设置或取消公共模板
 * @param params 请求参数
 * @returns 设置结果
 */
export function setOrCancelCommonTemplate(params: SetCommonTemplateParams) {
  return request({
    url: '/sys/template/setOrCancelCommonTemplate',
    method: 'get',
    params
  })
}

export interface SaveTemplateContentParams {
  uuid: string
  jsonContentObject: string
}

/**
 * @description 保存模板内容
 * @param data 请求参数
 * @returns 保存结果
 */
export function saveTemplateContent(data: SaveTemplateContentParams) {
  return request({
    url: '/sys/template/saveContent',
    method: 'post',
    data
  })
}
