import defaultSettings from './settings'
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',

  ssr: false, // 禁用SSR，使用客户端渲染

  app: {
    baseURL: defaultSettings.BASE_URL,
    head: {
      link: [
        { rel: "icon", type: "image/x-icon", href: defaultSettings.BASE_URL + "/favicon.ico" }
      ]
    },
    // 启用缓存策略
    rootId: '__nuxt',
  },

  // 生产环境优化配置
  vite: {
    optimizeDeps: {
      include: ['ant-design-vue'] // 预构建常用模块
    },
    css: {
      // 开发环境不压缩，生产环境压缩
      devSourcemap: true,
    },
    build: {
      // 生产环境构建优化
      cssCodeSplit: true,      // 启用CSS代码分割
      minify: 'terser',        // 使用terser进行更高效的JS压缩
      terserOptions: {
        compress: {
          drop_console: true,  // 移除console语句
          drop_debugger: true, // 移除debugger语句
          pure_funcs: ['console.log', 'console.info'], // 删除特定函数调用
          passes: 2,           // 多次优化遍历，提高压缩效果
          unsafe: true,        // 启用不安全优化，可减小体积
          arrows: true,        // 箭头函数优化
          booleans: true,      // 布尔值优化
          collapse_vars: true, // 内联变量
          dead_code: true,     // 移除无法访问的代码
        },
        mangle: {
          safari10: true,      // 兼容Safari 10
        },
        format: {
          comments: false,     // 移除注释
        },
        ecma: 2020,            // 使用更现代的ECMAScript标准
      },
      // 分块策略
      rollupOptions: {
        output: {
          manualChunks: {
            // 将大型库拆分成单独的块
            'ant-design': ['ant-design-vue'],
            // CSS分块策略
            'core-styles': [
              '@/assets/css/deps.css',
              '@/assets/css/larkui.css',
              '@/assets/css/pc.css',
              '@/assets/css/global.css',
              '@/assets/css/eruleStyle.scss'
            ],
            'layout-styles': [
              '@/assets/css/c__SideBarMenu.css',
              '@/assets/css/c__DeskTopDashboardSideBar~c__SideBar.css',
              '@/assets/css/c__ScopedSearch.css'
            ],
            'dashboard-styles': [
              '@/assets/css/p__dashboard__routers__App.css',
              '@/assets/css/c__DashboardRecent.css',
              '@/assets/css/c__DashboardOrgWiki.css'
            ]
          },
          // 限制chunk大小
          experimentalMinChunkSize: 10000, // 合并小于10KB的chunk
        }
      },
      // 减小构建大小的其他优化
      chunkSizeWarningLimit: 1500, // 提高警告阈值
      reportCompressedSize: false, // 提高构建速度
      sourcemap: false,        // 生产环境禁用sourcemap
      target: 'es2020',        // 使用现代JS目标，减小体积
    }
  },

  devServer: {
    // 将端口号设置为 3001 是因为用 npx serve .output/public 启动静态服务器时的默认端口是 3000
    // 为了在开发时能够用固定的端口同时启动静态服务器，以便让外部测试者能用固定地址访问该应用，所以将开发服务器的端口改为 3001
    port: 3001,
    host: '0.0.0.0', // 可选，设置主机地址
  },

  nitro: {
    // 移除预渲染相关配置
    compressPublicAssets: {
      brotli: true, // 启用Brotli压缩
    },
    minify: true, // 服务器代码压缩
    devProxy: {
      [defaultSettings.VUE_APP_BASE_API]: {
        target: 'http://*************:18081' + defaultSettings.VUE_APP_BASE_API, // nitro会把api前缀去掉，所以我们需要手动加上
        //target: 'http://127.0.0.1:18080' + defaultSettings.VUE_APP_BASE_API, // nitro会把api前缀去掉，所以我们需要手动加上
        changeOrigin: true,
      },
      [defaultSettings.AI_API]: {
        target: 'http://*************:8088',
        changeOrigin: true,
      },
    }
  },

  devtools: {
    enabled: false,

    timeline: {
      enabled: true
    }
  },

  modules: ['@ant-design-vue/nuxt'],

  components: [
    {
      path: '~/components',
      pathPrefix: false, // 组件名称不自动添加前缀
    },
  ],

  // 添加缓存优化，移除预渲染相关
  routeRules: {
    '/_nuxt/**': {
      headers: {
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    },
    '/img/**': {
      headers: {
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    },
    // 动态API请求缓存策略
    [defaultSettings.VUE_APP_BASE_API + '/**']: {
      cors: true,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    }
  },

  hooks: {
    'build:done': () => {
      // 只在生产环境构建时执行，开发环境不执行
      if (process.env.NODE_ENV === 'production') {
        const { exec } = require('child_process');
        exec('npm run postgenerate', (err: any, stdout: any, stderr: any) => {
          if (err) {
            console.error(`Error executing postgenerate: ${err}`);
            return;
          }
        });
      }
    }
  },
})
