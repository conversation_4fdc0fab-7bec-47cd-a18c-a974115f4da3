<template>
  <div>
    <div v-if="!isGetData">
      <span style="margin-left: 15px">loading...</span>
      <div class="el-loading-spinner">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
      </div>
    </div>
    <TableRule
      v-if="isGetData"
      :p_locked="false"
      :validateResult="validateResult"
      :predefineList="predefineList"
      :initModelData="{ initModelData }"
      :ruleData="ruleData"
      :ruleDrl="self_ruleDrl"
      :uuid="uuid"
      :demandUuid="demandUuid"
      @save="save"
      @validate="validate"
      @handleExport="handleExport"
      @handleImport="handleImport"
      @changePredefineList="changePredefineList"
      @setCascaderClose="setCascaderClose"
      ref="tableRule"
      :ruleName="ruleName"
    />
  </div>
</template>

<script lang="jsx">
import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import TableRule from "./index.vue";
import globalEventEmitter from '~/utils/eventBus';
import { REFRESH_RULE_LIST } from '@/consts/globalEventConsts';
import {
  ruleSave,
  ruleVerify,
  generateExcel,
  parseExcel,
} from "@/api/rule_editor";
import ErrorMessageFormatter from '@/components/common/ErrorMessageFormatter.vue';
export default {
  name: "DecisionTableRule",
  inject: {
    ruleUuid: { default: "" },
    message: { default: () => {} },
  },
  components: {
    TableRule,
    ErrorMessageFormatter
  },
  props: {
    modelData: {
      type: Object,
      default: () => {},
    },
    ruleContent: {
      type: Object,
      default: () => {},
    },
    uuid: String,
    ruleDrl: String,
    demandUuid: String,
    ruleName: String,
  },
  data() {
    return {
      initModelData: {},
      injectModelData: {},
      predefineList: [],
      validateResult: [],
      ruleData: {},
      self_ruleDrl: "",
      listMap: {
        dictMap: {},
        initModelData: {},
        order: 1,
      },
      // preListMap: {
      //   dictMap: {},
      //   initModelData: {},
      //   order: 1,
      // },
    };
  },
  watch: {
    modelData: {
      handler: function (val, oldVal) {
        if (Object.keys(val).length > 0) {
          this.getModalInit(val, false, false, "init");
          this.initModelData = val;
          this.ruleData = this.ruleContent;
          this.self_ruleDrl = this.ruleDrl;
          this.getDataPredefinedRule(this.ruleContent);
        }
      },
      deep: true,
    },
    ruleContent: {
      handler: function (val, oldVal) {
        if (Object.keys(val).length > 0) {
          this.ruleData = val;
          this.self_ruleDrl = this.ruleDrl;
          this.getDataPredefinedRule(val);
        }
      },
      deep: true,
    },
  },
  computed: {
    isGetData() {
      return Object.keys(this.initModelData).length > 0;
    },
  },
  methods: {
    setCascaderClose() {
      this.$emit("setCascaderClose");
    },
    getModalInit(data, order, pre, isInit, del = false, change = false) {
      const modelData = data;
      const {
        complexModels,
        dicts,
        modelDomains,
        simpleModels,
        sysModels,
        sysOperators,
      } = modelData;
      const operatorTypeList = util.getoperatorTypeList(sysOperators);
      const baseMethodListMap = util.getBaseMethodMapping(sysModels);
      const _datasetGroupList = util.getDatasetGroupList(
        simpleModels,
        baseMethodListMap
      );
      const { fieldsDataList, methodsDataList } =
        util.getModelList(complexModels);
      const dictMap = {};
      dicts.forEach((item) => {
        const { dictName, values } = item;
        dictMap[dictName] = values.map((item2) => {
          return {
            value: item2.key,
            viewName: item2.value,
          };
        });
      });
      modelDomains.map((item) => {
        const { name, domainAttributes } = item;
        dictMap[name] = domainAttributes.map((item2) => {
          return {
            value: item2.value,
            viewName: item2.viewName,
          };
        });
      });
      const firstFieldItemList = util.getFirstFieldItemList(
        fieldsDataList,
        baseMethodListMap,
        fieldsDataList
      );
      let tempObj = {
        dataEntryList: _datasetGroupList,
        fieldsDataList,
        methodsDataList,
        baseMethodListMap,
        operatorTypeList,
        firstFieldItemList,
      };
      if (isInit || !pre || change || del) {
        store.commit('setInitModelData',{ list: tempObj, ruleUuid: this.ruleUuid });
        this.loading = false;
        this.listMap.dictMap = dictMap;
        this.listMap.initModelData = tempObj;
        if (order) {
          this.listMap.order = order;
        }
        this.listMap.del = del;
        this.listMap.change = change;
        store.commit('setListMap',{ list: this.listMap, ruleUuid: this.ruleUuid });
      }
      // store.commit('setPreInitModelData',tempObj);
      this.loading = false;
    },
    getDataPredefinedRule(data) {
      const { predefines = [] } = data;
      const numberList = [
        "Double",
        "Short",
        // "Integer",
        // "Long",
        // "Float",
        "BigDecimal",
      ];
      const _predefines = [];
      predefines &&
        predefines.map((item) => {
          const obj = {
            id: item.name,
            preViewName: item.viewName,
            variableType: numberList.includes(item.variableType)
              ? "Double"
              : item.variableType,
            variableTypeName: item.variableTypeName,
            operatorType: item.operatorType,
            inputOrSelect: item.variable.type,
            inputvariableValue:
              item.variable.type === "Input" ||
              item.variable.type === "BooleanInput"
                ? item.variable.value
                : "",
            selectVariableValue:
              item.variable.type === "Select" ? item.variable.value : {},
            hasConditions: Object.keys(item.conditionsRule).length > 0,
            conditions: item.conditionsRule,
          };
          _predefines.push(obj);
        });
      // return _predefines;
      this.changePredefineList(_predefines, "init");
    },
    changePredefineList(val, isInit, del, change) {
      this.predefineList = val;
      const list = [];
      const includesList = [
        "String",
        "Boolean",
        "Date",
        "Double",
        "Short",
        "Integer",
        "Long",
        "Float",
        "BigDecimal",
      ];
      const numberList = [
        "Double",
        "Short",
        // "Integer",
        // "Long",
        // "Float",
        "BigDecimal",
      ];
      val &&
        val.map((item) => {
          const modelData = {
            valueType: numberList.includes(item.variableType)
              ? "Double"
              : item.variableType,
            name: item.id,
            viewName: item.preViewName,
            status: "01",
            display: true,
          };
          if (item.operatorType === "equals") {
            if (includesList.includes(item.variableType)) {
              modelData.valueType = item.variableType;
            } else {
              const name = this.getEqualsVariableName(
                item.variableType,
                item.selectVariableValue.conditions[0].variable
              );
              modelData.valueType = name;
              modelData.fieldsList = this.initModelData.complexModels.find(
                (items) => items.valueType === name
              ).fieldsList;
              modelData.methodsList = this.initModelData.complexModels.find(
                (items) => items.valueType === name
              ).methodsList;
            }
          } else {
            modelData.valueType = item.variableType;
            const findItem = this.initModelData.complexModels.find(
              (items) => items.valueType === item.variableType
            );
            modelData.fieldsList = findItem ? findItem.fieldsList : [];
            modelData.methodsList = findItem ? findItem.methodsList : [];
            // modelData.methodsList = item.variableType;
          }

          list.push(modelData);
        });
      const newList = [...this.initModelData.complexModels, ...list];
      this.injectModelData = { ...this.initModelData, complexModels: newList };
      this.getModalInit(
        this.injectModelData,
        this.listMap.order + 1,
        true,
        isInit,
        del,
        change
      );
    },
    getEqualsVariableName(sourceName, data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        name.push(obj.valueType ? obj.valueType : obj.name);
        if (obj.next) {
          deepFn(obj.next);
        }
      }
      if (name.includes(sourceName) && name[name.length - 1].includes(".")) {
        return name[name.length - 1];
      }

      return sourceName;
    },
    getVariableName(data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        if (!obj.next) {
          name.push(obj.valueType ? obj.valueType : obj.name);
        } else {
          deepFn(obj.next);
        }
      }
      return name[0];
    },
    getPredefinedRule(list) {
      const predefines = [];
      list &&
        list.map((item) => {
          const obj = {
            name: item.id,
            viewName: item.preViewName,
            variableType: item.variableType,
            variableTypeName: item.variableTypeName,
            operatorType: item.operatorType,
            variable: {
              type: item.inputOrSelect,
              value:
                item.inputOrSelect === "Select"
                  ? item.selectVariableValue
                  : item.inputvariableValue,
            },
            conditionsRule: item.hasConditions ? item.conditions : {},
          };
          predefines.push(obj);
        });

      return predefines;
    },
    save(ruleData) {
      let isConditions = [];
      let isActions = [];
      const { definition = {}, rows = [] } = ruleData;
      const predefines = this.getPredefinedRule(this.predefineList);
      const param = {
        ruleUuid: this.uuid,
        demandUuid: this.demandUuid,
        ruleContent: { predefines, definition, rows },
      };
      const { conditions, actions } = definition;
      // 如果
      param.ruleContent.definition.conditions = this.setExpressionValueType(
        param.ruleContent.definition.conditions
      );
      // 预定义
      for (let i in param.ruleContent.predefines) {
        for (let j in param.ruleContent.predefines[i].conditionsRule
          .conditions) {
          param.ruleContent.predefines[i].conditionsRule.conditions =
            this.setExpressionValueType(
              param.ruleContent.predefines[i].conditionsRule.conditions
            );
        }
      }
      // 那么
      param.ruleContent.definition.actions = this.setExpressionValueType(
        param.ruleContent.definition.actions
      );
      for (let j = 0; j < conditions.length; j++) {
        const { comparator } = conditions[j];
        if (comparator && comparator.operatorName) {
          isConditions.push(true);
        } else {
          isConditions.push(false);
        }
      }
      for (let i = 0; i < actions.length; i++) {
        const { actionParams } = actions[i];
        if (actionParams.length > 0 && actionParams[0].next) {
          isActions.push(true);
        } else {
          isActions.push(false);
        }
      }
      if (isConditions.includes(false) || isActions.includes(false)) {
        message.error('条件列或者动作列必须编辑表头');
      } else {
        ruleSave(param)
          .then((res) => {
            if (res.data.ruleValidates && res.data.ruleValidates[0]) {
              const result = res.data;
              this.validateResult = result.ruleValidates;
              this.self_ruleDrl = result.ruleDrl;
              this.ruleData = ruleData;
              if (result.valid) {
                message.success("保存成功");
                //更新上级列表状态
                globalEventEmitter.emit(REFRESH_RULE_LIST);
              } else {
                message.warning({
                  content:()=>{
                    return (
                      <div>
                        <p style="color:#52c41a;margin-bottom:10px">保存成功123</p>规则校验失败，请重新编写
                      </div>
                    )
                  },
                });
                this.$refs["tableRule"].saveValiChange();
              }
            }
          })
          .catch((error) => {
            //message.error(error);
          });
      }
    },
    setExpressionValueType(data) {
      for (let i in data) {
        if (data[i].variable) {
          this.setExpressionParamsValType(
            data[i],
            data[i].variable,
            "variable"
          );
        }
        if (
          (data[i].comparator && data[i].comparator.operatorParams) ||
          data[i].actionParams
        ) {
          let loopData =
            data[i].actionParams ||
            (data[i].comparator && data[i].comparator.operatorParams);
          for (let j in loopData) {
            this.setExpressionParamsValType(loopData[j], loopData[j]);
          }
        }
      }
      return data;
    },
    setExpressionParamsValType(data, obj, tag) {
      const aParItemValType = [];
      if (obj.variableType === "expression") {
        for (let j in obj.expressionParams) {
          if (obj.expressionParams[j].next) {
            getLastNextValType(obj.expressionParams[j].next);
            function getLastNextValType(data) {
              if (data.next) {
                getLastNextValType(data.next);
              } else {
                aParItemValType.push(data.valueType);
              }
            }
          } else {
            aParItemValType.push(obj.expressionParams[j].valueType);
          }
        }
        let isBigDecimal = aParItemValType.find(
          (item) => item === "BigDecimal"
        );
        let isDouble = aParItemValType.find((item) => item === "Double");
        let isFloat = aParItemValType.find((item) => item === "Float");
        let isLong = aParItemValType.find((item) => item === "Long");
        let isInteger = aParItemValType.find((item) => item === "Integer");
        let isShort = aParItemValType.find((item) => item === "Short");
        if (isBigDecimal) {
          tag === "variable" && (data.leftValueType = "BigDecimal");
          obj.valueType = "BigDecimal";
        } else if (isDouble) {
          tag === "variable" && (data.leftValueType = "Double");
          obj.valueType = "Double";
        } else if (isFloat) {
          tag === "variable" && (data.leftValueType = "Float");
          obj.valueType = "Float";
        } else if (isLong) {
          tag === "variable" && (data.leftValueType = "Long");
          obj.valueType = "Long";
        } else if (isInteger) {
          tag === "variable" && (data.leftValueType = "Integer");
          obj.valueType = "Integer";
        } else if (isShort) {
          tag === "variable" && (data.leftValueType = "Short");
          obj.valueType = "Short";
        }
      } else if (obj.next) {
        // 方法
        let _this = this;
        getLastValType(obj.next);
        function getLastValType(data) {
          if (data.next) {
            getLastValType(data.next);
          } else {
            if (data.variableType === "method") {
              const aParamsType = data.paramsList;
              for (let k in data.methodParams) {
                if (data.methodParams[k].variableType === "expression") {
                  data.methodParams[k].valueType = aParamsType[k].valueType;
                }
                if (data.methodParams[k].expressionParams) {
                  for (let l in data.methodParams[k].expressionParams) {
                    _this.setExpressionParamsValType(
                      data.methodParams[k].expressionParams[l],
                      data.methodParams[k].expressionParams[l]
                    );
                  }
                }
              }
            }
          }
        }
      }
    },
    validate(ruleData) {
      // const { ruleName, ...others } = ruleData;
      const { definition = {}, rows = [] } = ruleData;
      const predefines = this.getPredefinedRule(this.predefineList);
      const param = {
        ruleUuid: this.uuid,
        ruleContent: { predefines, definition, rows },
      };
      // 如果
      param.ruleContent.definition.conditions = this.setExpressionValueType(
        param.ruleContent.definition.conditions
      );
      // 预定义
      for (let i in param.ruleContent.predefines) {
        for (let j in param.ruleContent.predefines[i].conditionsRule
          .conditions) {
          param.ruleContent.predefines[i].conditionsRule.conditions =
            this.setExpressionValueType(
              param.ruleContent.predefines[i].conditionsRule.conditions
            );
        }
      }
      // 那么
      param.ruleContent.definition.actions = this.setExpressionValueType(
        param.ruleContent.definition.actions
      );
      ruleVerify(param)
        .then((res) => {
          if (res.data.ruleValidates && res.data.ruleValidates[0]) {
            const result = res.data;
            this.validateResult = result.ruleValidates;
            this.self_ruleDrl = result.ruleDrl;
            this.ruleData = ruleData;
            if (result.valid) {
              message.success(result.validateMessage);
            } else {
              message.error({
                content: () => <ErrorMessageFormatter errorMessage={result.validateMessage} />
              });
            }
          }
        })
        .catch((error) => {
            //message.error(error);
        });
    },
    handleExport(ruleData) {
      const { definition = {}, rows = [] } = ruleData;
      const predefines = this.getPredefinedRule(this.predefineList);
      const param = {
        ruleUuid: this.uuid,
        ruleContent: { predefines, definition, rows },
      };
      showGlobalLoading('决策表导出中，请耐心等待');
      ruleVerify(param)
        .then((res) => {
          if (res.data.ruleValidates && res.data.ruleValidates[0]) {
            this.validateResult = res.data.ruleValidates;
            this.self_ruleDrl = res.data.ruleDrl;
            this.ruleData = ruleData;
            // message("校验成功");
            generateExcel(param).then((res) => {
              let headers = res.headers;
              let blob = new Blob([res.data], {
                type: headers["content-type"],
              });
              let link = document.createElement("a");
              let url = window.URL.createObjectURL(blob);
              let fileName = headers["content-disposition"]
                .split(";")[1]
                .split("=")[1];
              link.href = url;
              link.download = decodeURI(fileName);
              link.style.display = "none";
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
              message.success("导出成功");
              hideGlobalLoading();
            }).catch(()=>{
              hideGlobalLoading();
            });
          }
        })
        .catch((error) => {
          //message.error(error);
        });
    },
    handleImport(val) {
      showGlobalLoading('决策表导入中，请耐心等待');
      parseExcel(val).then((response) => {
        if (response.code === 20000) {
          hideGlobalLoading();
          message.success("导入成功");
          this.$emit("handleImport");
        } else {
          message({
            message: response.msg || response.data,
            type: "error",
            duration: 0,
            showClose: true
          });
        }
      }).catch(()=>{
        hideGlobalLoading();
      });
    },
  },
  created() {
    const val = this.modelData;
    if (Object.keys(val).length > 0) {
      this.getModalInit(val, false, false, "init");
      this.initModelData = val;
      this.ruleData = this.ruleContent;
      this.self_ruleDrl = this.ruleDrl;
      this.getDataPredefinedRule(this.ruleContent);
    }
  },
};
</script>

