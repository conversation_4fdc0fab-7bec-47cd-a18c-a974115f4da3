import inherits from 'inherits';

import RuleProvider from 'diagram-js/lib/features/rules/RuleProvider';

export default function CustomRules(eventBus) {
  RuleProvider.call(this, eventBus);
}

inherits(CustomRules, RuleProvider);
CustomRules.$inject = ['eventBus']

CustomRules.prototype.is = function (obj, type) {
  return obj.type === type ? true : false
}
CustomRules.prototype.canDrop = function () {
  return false;
};

CustomRules.prototype.canMove = function () {
  return false;
};

CustomRules.prototype.init = function () {
  // there exist a number of modeling actions
  // that are identified by a unique ID. We
  // can hook into each one of them and make sure
  // they are only allowed if we say so

  // this.addRule(['connection.create', 'shape.create'], 1234, function (context) {

  this.addRule('shape.create', 2000, function (context) {
    const {
      shape,
      target,
      source,
      position
    } = context
    // start
    if (CustomRules.prototype.is(shape, 'bpmn:StartEvent')) {
      let elementsList = []
      if (CustomRules.prototype.is(target, 'bpmn:Participant')) {
        elementsList = target.businessObject.processRef.flowElements
      }
      if (CustomRules.prototype.is(target, 'bpmn:Process')) {
        elementsList = target.businessObject.flowElements
      }
      for (let i in elementsList) {
        if (elementsList[i].$type === 'bpmn:StartEvent') {
          return false
        }
      }
    }
    // end
    // if(CustomRules.prototype.is(shape, 'bpmn:EndEvent')){
    //   let elementsList = []
    //   if (CustomRules.prototype.is(target, 'bpmn:Participant')) {
    //     elementsList = target.businessObject.processRef.flowElements
    //   }
    //   if (CustomRules.prototype.is(target, 'bpmn:Process')) {
    //     elementsList = target.businessObject.flowElements
    //   }
    //   for (let i in elementsList) {
    //     if (elementsList[i].$type === 'bpmn:EndEvent') {
    //       return false
    //     }
    //   }
    // }
  })
}
