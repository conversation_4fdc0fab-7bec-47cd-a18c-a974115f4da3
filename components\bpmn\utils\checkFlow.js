// 校验流程
export default function checkFlow(bpmnModeler,flowErrorMsg) {
  const elementRegistry = bpmnModeler.value.get("elementRegistry");
  const startEventList = elementRegistry.filter(
    (item) => item.type === "bpmn:StartEvent"
  );
  const policyList = elementRegistry.filter(
    (item) =>
    (item.type === "bpmn:InclusiveGateway" ||
      item.type === "bpmn:ParallelGateway" ||
      item.type === "bpmn:ExclusiveGateway") &&
    item.businessObject.gatewayDirection === "Diverging"
  );
  const taskList = elementRegistry.filter(
    (item) => item.type === "bpmn:BusinessRuleTask"
  );
  const convergenceList = elementRegistry.filter(
    (item) =>
    (item.type === "bpmn:ParallelGateway" ||
      item.type === "bpmn:ExclusiveGateway") &&
    item.businessObject.gatewayDirection === "Converging"
  );
  const endEventList = elementRegistry.filter(
    (item) => item.type === "bpmn:EndEvent"
  );
  const callActivityList = elementRegistry.filter(
    (item) => item.type === "bpmn:CallActivity"
  );
  flowErrorMsg.value = [];
  // 开始
  if (startEventList.length === 0) {
    flowErrorMsg.value.push(`必须存在一个开始节点！`);
  } else {
    startEventList.map((i) => {
      const iFlag = i.businessObject.name || i.id;
      if (i.incoming.length > 0) {
        flowErrorMsg.value.push(`开始节点：<strong>【${iFlag}】</strong>不允许有接入路由边！`);
      }
      if (i.outgoing.length === 0) {
        flowErrorMsg.value.push(
          `开始节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
        );
      }
      if (i.outgoing.length > 1) {
        flowErrorMsg.value.push(
          `开始节点：<strong>【${iFlag}】</strong>只能存在一条接出路由边！`
        );
      }
    });
  }
  // 决策
  policyList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    if (i.incoming.length === 0) {
      flowErrorMsg.value.push(
        `决策分支节点：<strong>【${iFlag}】</strong>必须存在一条接入路由边！`
      );
    }
    if (i.incoming.length > 1) {
      flowErrorMsg.value.push(
        `决策分支节点：<strong>【${iFlag}】</strong>只能存在一条接入路由边！`
      );
    }
    if (i.outgoing.length < 2) {
      flowErrorMsg.value.push(
        `决策分支节点：<strong>【${iFlag}】</strong>至少存在两条接出路由边！`
      );
    }
  });
  // 汇聚
  convergenceList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    if (i.incoming.length < 2) {
      flowErrorMsg.value.push(
        `汇聚分支节点：<strong>【${iFlag}】</strong>至少存在两条接入路由边！`
      );
    }
    if (i.outgoing.length === 0) {
      flowErrorMsg.value.push(
        `汇聚分支节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
      );
    }
  });
  
  // 规则
  taskList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    const ruleFlow = i.businessObject.$attrs['g:ruleFlowGroup']
    if (!ruleFlow) {
      flowErrorMsg.value.push(
        `规则节点：<strong>【${iFlag}】</strong>必须勾选关联规则！`
      );
    }
    if (i.incoming.length === 0) {
      flowErrorMsg.value.push(
        `规则节点：<strong>【${iFlag}】</strong>必须存在一条接入路由边！`
      );
    }
    if (i.incoming.length > 1) {
      flowErrorMsg.value.push(
        `规则节点：<strong>【${iFlag}】</strong>只能存在一条接入路由边！`
      );
    }
    if (i.outgoing.length === 0) {
      flowErrorMsg.value.push(
        `规则节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
      );
    }
    if (i.outgoing.length > 1) {
      flowErrorMsg.value.push(
        `规则节点：<strong>【${iFlag}】</strong>只能存在一条接出路由边！`
      );
    }
  });

  // 子流
  callActivityList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    const ruleFlow = i.businessObject.$attrs['g:ruleFlowGroup']
    if (!ruleFlow) {
      flowErrorMsg.value.push(
        `子规则流节点：<strong>【${iFlag}】</strong>必须勾选关联子规则流！`
      );
    }
    if (i.incoming.length === 0) {
      flowErrorMsg.value.push(
        `子规则流节点：<strong>【${iFlag}】</strong>必须存在一条接入路由边！`
      );
    }
    if (i.outgoing.length === 0) {
      flowErrorMsg.value.push(
        `子规则流节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
      );
    }
    if (i.outgoing.length > 1) {
      flowErrorMsg.value.push(
        `子规则流节点：<strong>【${iFlag}】</strong>只能存在一条接出路由边！`
      );
    }
  });
  // 结束
  if (endEventList.length === 0) {
    flowErrorMsg.value.push(`至少存在一个结束节点！`);
  } else {
    endEventList.map((i) => {
      const iFlag = i.businessObject.name || i.id;
      if (i.incoming.length === 0) {
        flowErrorMsg.value.push(
          `结束节点：<strong>【${iFlag}】</strong>必须存在一条接入路由边！`
        );
      }
      if (i.incoming.length > 1) {
        flowErrorMsg.value.push(
          `结束节点：<strong>【${iFlag}】</strong>只能存在一条接入路由边！`
        );
      }
      if (i.outgoing.length > 0) {
        flowErrorMsg.value.push(`结束节点：<strong>【${iFlag}】</strong>不允许有接出路由边！`);
      }
    });
  }
}
