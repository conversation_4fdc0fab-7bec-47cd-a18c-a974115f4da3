<!-- 规则库新增/更新 -->

<template>
    <div class="rule-base-edit-container">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-align="right" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="规则库名称" name="ruleBaseName" required>
                <a-input autocomplete="off" v-model:value="form.ruleBaseName" placeholder="规则库名称"></a-input>
            </a-form-item>
            <a-form-item label="规则库标识" name="ruleBaseFlag">
                <a-input autocomplete="off" v-model:value="form.ruleBaseFlag" placeholder="规则库标识"></a-input>
            </a-form-item>
            <a-form-item label="业务条线" name="businessLine">
                <a-select v-model:value="form.businessLine" placeholder="请选择" @change="businessLineChange" :disabled="mode === 'update'" :filterOption="filterOption" showSearch>
                    <a-select-option v-for="item in businessLineOptions" :key="item.uuid"
                        :value="item.code" :name="item.name">{{item.name}}</a-select-option>
                </a-select>
            </a-form-item>

            <a-form-item label="模型类型" name="modelType">
                <a-select v-model:value="form.modelType" placeholder="请选择" @change="modelTypeChange">
                    <a-select-option v-for="item in modelTypeOptions" :key="item.uuid"
                        :value="item.code">{{item.name}}</a-select-option>
                </a-select>
            </a-form-item>

            <a-form-item label="BOM模型" name="bomModelTree">
                <TreeSelectMulti
                    v-model:modelValue="bomModelTreeChecked"
                    :tree-data="bomModelTreedata"
                    :field-names="bomModelFieldNames"
                    :tree-checkable="true"
                    :tree-default-expand-all="true"
                    :tree-node-label-prop="'modelName'"
                    :custom-filter="filterTreeNode"
                    placeholder="请选择 BOM 模型"
                    @change="onBomModelChange"
                >
                    <template #title="{ nodeData }">
                        <span>{{ `${nodeData.bomModelType || ''}${nodeData.bomModelType ? '：' : ''}${nodeData.modelName}` }}</span>
                    </template>
                </TreeSelectMulti>
            </a-form-item>

            <a-form-item label="审核级别" name="auditLevel">
                <a-select v-model:value="form.auditLevel" placeholder="请选择">
                    <a-select-option v-for="item in auditLevelOptions" :key="item.uuid"
                                     :value="item.code">{{item.name}}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="是否模版库" name="ifTemplateLib">
                <a-select v-model:value="form.ifTemplateLib" placeholder="请选择">
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述信息" name="desc">
                <a-textarea :auto-size="{ minRows: 2, maxRows: 5 }" :show-word-limit="true" :maxlength="500"
                    v-model:value="form.desc"></a-textarea>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import {
    getDicValueByTypeCode,
    ruleBomModelListJson,
    saveOrUpdate,
    getAllBusinessByLoginUser,
    getEngineeringByUuid
} from "@/api/rule_base";

const message = inject('message');
const router = useRouter();

const props = defineProps({
    mode: {
        type: String,
        default: 'add', // 'add' 或 'update'
        validator: (value) => ['add', 'update'].includes(value)
    },
    uuid: {
        type: String,
        default: ''
    }
});

const form = reactive({
    businessLine: "",
    ruleBaseName: "",
    modelType: "",
    desc: "",
    auditLevel: "",
    ruleBaseFlag: "",
    status: "",
    checkControlStatus: "",
    checkControlModifiedId: "",
    checkControlModifiedTime: "",
    checkControlModifiedTimeStr: "",
    ifTemplateLib: ""
});

const businessLineOptions = ref([]);
const auditLevelOptions = ref([]);
const modelTypeOptions = ref([]);

const rules = {
    ruleBaseName: [
        {
            required: true,
            message: '不能为空',
            trigger: 'blur'
        },
        {
            validator: (rule, value, callback) => {
                if (value) {
                    if (value.length < 2 || value.length > 32){
                        return callback(new Error('长度在 2 到 32 个字符'));
                    }
                    else if (!/^[\u4e00-\u9fa5a-zA-Z0-9_]+$/.test(value)) {
                        callback(new Error('只支持输入中、英文字母、数字及下划线'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            },
            trigger: 'blur'
        }
    ],
    ruleBaseFlag: [
        { required: true, validator: checkRuleBaseFlag, trigger: "blur" },
    ],
    businessLine: [
        { required: true, message: "不能为空", trigger: "change" }
    ],
    auditLevel: [
        { required: true, message: "不能为空", trigger: "change" }
    ],
    modelType: [
        { required: true, message: "不能为空", trigger: "change" }
    ],
    ifTemplateLib: [
        { required: true, message: "不能为空", trigger: "change" },
    ],
    desc: [
        {
            min: 1,
            max: 500,
            message: "长度在 1 到 500 个字符",
            trigger: "blur",
        },
    ],
    bomModelTree: [
        {
            required: true,
            validator: (rule, value, callback) => {
                if (bomModelTreeChecked.value.length) {
                    callback();
                } else {
                    callback(new Error('不能为空'));
                }
            },
            trigger: 'blur',
        },
    ],
};

// BOM模型相关
const bomModelTreedata = ref([]);
const bomModelTreeChecked = ref([]);
// BOM模型字段映射
const bomModelFieldNames = {
    children: 'children',
    label: 'modelName',
    value: 'uuid',
    key: 'uuid'
};

// 创建者信息（更新模式使用）
const createdId = ref('');
const createdTime = ref('');
const createdTimeStr = ref('');

// 过滤树节点
const filterTreeNode = (inputValue, treeNode) => {
    const modelName = treeNode.modelName || '';
    const bomModelType = treeNode.bomModelType || '';
    return (
        modelName.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0 ||
        bomModelType.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
    );
};

// 过滤选择器选项
const filterOption = (input, option) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

function checkRuleBaseFlag(rule, value, callback) {
    if (!value) {
        return callback(new Error('不能为空'));
    } else {
        if (value.length < 2 || value.length > 48) {
            return callback(new Error('规则库标识长度在2~48之间'));
        } else {
            const checkReg = /^[a-zA-Z0-9]+$/g;
            const checkObj = value.match(checkReg);
            if (checkObj && checkObj.length > 0) {
                callback();
            } else {
                callback(new Error('只支持输入英文字母及数字'));
            }
        }
    }
}

onMounted(() => {
    // 初始化数据
    if (props.mode === 'add') {
        getAllBusinessByLoginUser().then((res) => {
            businessLineOptions.value = res.data;
        });
    }

    // 获取公共字典数据
    getDicValue("businessLine", (arr) => {
        businessLineOptions.value = arr;
    });

    getDicValue("checkLevel", (arr) => {
        auditLevelOptions.value = arr;
    });

    getDicValue("bomModelType", (arr) => {
        modelTypeOptions.value = arr;
    });

    // 如果是更新模式，获取规则库信息
    if (props.mode === 'update' && props.uuid) {
        getEngineeringByUuidFun(props.uuid);
    }
});

// 监听 uuid 变化，更新模式下重新加载数据
watch(
    () => props.uuid,
    (newUuid) => {
        if (props.mode === 'update' && newUuid) {
            getEngineeringByUuidFun(newUuid);
        }
    }
);

const getEngineeringByUuidFun = (uuid) => {
    // 规则库信息
    getEngineeringByUuid({ uuid }).then((res) => {
        form.ruleBaseName = res.data.chineseName;
        form.ruleBaseFlag = res.data.engName;
        form.businessLine = res.data.businessLine;
        form.auditLevel = res.data.checkLevel;
        form.modelType = res.data.bomModelName;
        form.status = res.data.status;
        form.checkControlStatus = res.data.checkControlStatus;
        form.ifTemplateLib = res.data.ifTemplateLib;
        form.desc = res.data.descs;
        createdId.value = res.data.createdId;
        createdTime.value = res.data.createdTime;
        createdTimeStr.value = res.data.createdTimeStr;

        // 设置BOM模型选中值并加载数据
        if (res.data.bomModelUuids) {
            const uuidArray = res.data.bomModelUuids.split(',');
            bomModelTreeChecked.value = uuidArray.filter(item => item);
        }

        // 加载模型数据
        getBomModelListJson();
    });
};

const businessLineChange = () => {
    getBomModelListJson();
};

const modelTypeChange = () => {
    getBomModelListJson();
};

const getBomModelListJson = () => {
    if (form.modelType && form.businessLine) {
        // 保存当前选中的值
        const currentChecked = [...bomModelTreeChecked.value];

        ruleBomModelListJson({
            businessLine: form.businessLine,
            bomModelType: form.modelType,
        }).then((res) => {
            bomModelTreedata.value = res.data;

            // 如果有选中值，验证它们在新数据中是否存在
            if (currentChecked && currentChecked.length) {
                // 获取新加载的模型中所有可用的 uuid
                const flattenNodes = (nodes, result = []) => {
                    for (const node of nodes) {
                        result.push(node.uuid);
                        if (node.children && node.children.length) {
                            flattenNodes(node.children, result);
                        }
                    }
                    return result;
                };

                const availableUuids = flattenNodes(res.data);

                // 只保留在新模型中存在的节点
                const validChecked = currentChecked.filter(uuid =>
                    availableUuids.includes(uuid)
                );

                // 如果有变化，更新选中值
                if (JSON.stringify(validChecked) !== JSON.stringify(currentChecked)) {
                    bomModelTreeChecked.value = validChecked;
                }
            }
        });
    } else {
        // 当业务线或模型类型为空时，清空树数据
        bomModelTreedata.value = [];
    }
};

const onBomModelChange = (value) => {
    bomModelTreeChecked.value = value;
};

const getDicValue = (type, fn) => {
    getDicValueByTypeCode({
        typeCode: type,
    }).then((res) => {
        fn && fn(res.data);
    });
};

const ruleForm = ref(null);

const submitFun = (callback) => {
    ruleForm.value.validate().then(() => {
        const checkedIdArr = bomModelTreeChecked.value;

        if (checkedIdArr && checkedIdArr.length) {
            const uuids = Array.isArray(checkedIdArr) ? checkedIdArr.join(',') : checkedIdArr;

            const params = {
                chineseName: form.ruleBaseName,
                engName: form.ruleBaseFlag,
                businessLine: form.businessLine,
                checkLevel: form.auditLevel,
                bomModelName: form.modelType,
                ifTemplateLib: form.ifTemplateLib,
                descs: form.desc,
                bomModelUuids: uuids,
            };

            // 如果是更新模式，添加更多参数
            if (props.mode === 'update') {
                Object.assign(params, {
                    createdId: createdId.value,
                    createdTime: createdTime.value,
                    createdTimeStr: createdTimeStr.value,
                    uuid: props.uuid,
                    status: form.status,
                    checkControlStatus: form.checkControlStatus,
                    checkControlModifiedId: form.checkControlModifiedId,
                    checkControlModifiedTime: form.checkControlModifiedTime,
                    checkControlModifiedTimeStr: form.checkControlModifiedTimeStr,
                });
            }

            saveOrUpdate(params).then((res) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    if (typeof callback === 'function') {
                        callback(true);
                    }
                } else {
                    message.error(res.data);
                    if (typeof callback === 'function') {
                        callback(false);
                    }
                }
            }).catch((error) => {
                if (typeof callback === 'function') {
                    callback(false);
                }
            });
        } else {
            message.error("请勾选BOM模型再提交！");
            if (typeof callback === 'function') {
                callback(false);
            }
        }
    }).catch((error) => {
        console.log(error);
        if (typeof callback === 'function') {
            callback(false);
        }
    });
};

const resetForm = () => {
    if (props.mode === 'add') {
        router.push({ path: `${process.env.BASE_URL}work_queue/rule_base_management` });
    }
};

defineExpose({
    submitFun,
    resetForm
});
</script>

<style lang="scss" scoped>
.rule-base-edit-container {
    height: auto;
    max-height: 500px;
    overflow-y: auto;
    padding-right: 10px;
    padding-bottom: 10px;
}

:deep(.ant-input),
:deep(.ant-select),
:deep(.ant-upload-dragger),
:deep(.ant-upload-list),
:deep(.ant-textarea) {
    width: 100%;
    max-width: 400px;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

:deep(.ant-form-item:last-child) {
    margin-bottom: 0;
}

:deep(.ant-textarea) {
    height: 120px !important;
    resize: none;
}

:deep(.ant-upload-dragger) {
    height: 100px;
}

:deep(.ant-upload-dragger .anticon-upload) {
    margin: 0;
    line-height: 50px;
    font-size: 50px;
}

:deep(.jarUpload.hidden .ant-upload) {
    display: none;
}

:deep(.ant-tabs) {
    width: 70%;
    min-height: 100px;
    margin: 15px 15px 15px 60px;
}

:deep(.ant-tabs-card) {
    box-shadow: unset;
}

:deep(.params_form) {
    border-bottom: 1px solid #dcdfe6;

    h4 {
        font-weight: unset;

        span {
            margin-left: 100px;
        }

        span.primary {
            color: #409eff;
        }

        span.danger {
            color: #f56c6c;
        }
    }

    label {
        color: #99a9bf;
    }

    span {
        color: #606266;
    }
}

:deep(.params_form:last-child) {
    border-bottom: unset;
}

:deep(.form_flex_div),
:deep(.form_flex_div .ant-form-item) {
    display: flex;
    flex-direction: row;
    flex: 1;

    .ant-form-item {
        overflow: auto;
    }
}

:deep(.ant-tree) {
    font-size: 14px;
    max-height: 200px;
    overflow-y: auto;
}

:deep(.ant-main) {
    padding: 0 0 10px 0;
}

:deep(.ant-form-item-explain-error) {
    color: #ff4d4f !important;
}
</style>
