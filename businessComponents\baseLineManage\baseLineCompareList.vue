<template>
    <!-- 标签页区域 -->
    <a-tabs v-model:activeKey="activeTabKey" type="editable-card" @edit="onTabEdit" hide-add>
        <a-tab-pane key="designView" tab="版本列表" :closable="false">
        <!-- 搜索区域 -->
        <a-form class="baseline-form">
            <a-row>
                <a-col :span="8">
                    <a-form-item label="规则库名称 :">
                        <span>{{ localCompareVal.engName }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="基线版本 ：">
                        <span>{{ localCompareVal.edition }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="基线描述 ：">
                        <span>{{ localCompareVal.desc }}</span>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <!-- 表格区域 -->
            <div ref="basePoint">
                <!-- 骨架屏 -->
                <TableSkeleton 
                    v-if="loading" 
                    :columns="columns" 
                    :limit="pagination.pageSize" 
                    :scrollY="scrollY-30" 
                />
                <!-- 数据表格 -->
                <a-table
                        v-else
                        :data-source="tableData"
                        :loading="loading"
                        :pagination="pagination"
                        ref="compareTable"
                        size="small"
                        :scroll="{y: scrollY-28 }"
                        @change="handlePageChange"
                >
                    <a-table-column title="序号" :width="50" align="center">
                        <template #default="{ index }">
                            {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
                        </template>
                    </a-table-column>
                    <a-table-column title="规则库名称" data-index="engChineseName" :width="300" :ellipsis="true" />
                    <a-table-column title="版本" data-index="edition" :ellipsis="true" :width="80" />
                    <a-table-column title="创建时间" data-index="createdTimeStr" :width="200" :ellipsis="true" />
                    <a-table-column title="是否发布" :ellipsis="true" :width="80">
                        <template #default="{ record }">
                            <span v-if="record.publishStatus === 0">否</span>
                            <span v-if="record.publishStatus === 1">是</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="操作" :width="80" fixed="right">
                        <template #default="{ record }">
                            <a-button type="link" @click="btn_compare(record)">对比</a-button>
                        </template>
                    </a-table-column>
                </a-table>
            </div>
        </a-tab-pane>

        <a-tab-pane v-for="tab in compareTabs" :key="tab.key" :closable="true">
            <template #tab>
                <a-tooltip :title="tab.title">
                    <span>{{ subLongName(tab.title) }}</span>
                </a-tooltip>
            </template>
            <BaseLineCompareResult :compareParams="tab.compareParams" />
        </a-tab-pane>
        <template #tabBarExtraContent>
            <TabRight v-model:editableTabsValue="activeTabKey" v-model:editableTabs="compareTabs" :showClose="compareTabs.length > 0"/>
        </template>
    </a-tabs>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits, markRaw, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSnapCompareList } from '@/api/baseline_Management';
import BaseLineCompareResult from './baseLineCompareResult.vue';
import TableSkeleton from '@/components/TableSkeleton.vue';

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);

const router = useRouter();
const route = useRoute();

const props = defineProps({
    compareVal: {
        type: Object,
        required: true,
        default: () => ({})
    }
});

const emit = defineEmits(['close', 'compare']);
const tableData = ref<any[]>([]);
const localCompareVal = ref<any>({});
const loading = ref<boolean>(false);
const activeTabKey = ref<string>('designView');
const compareTabs = ref<any[]>([]);

const pagination = computed(() => ({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    isLoading: false,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条数据`,
}));

const getSnapCompareLists = async () => {
    if (!localCompareVal.value.engUuid) return;

    loading.value = true;
    try {
        const pars = {
            engUuid: localCompareVal.value.engUuid,
            snapShootUuid: localCompareVal.value.uuid,
        };
        const res = await getSnapCompareList(pars);
        tableData.value = res.data;
        pagination.value.total = res.data.totalCount;

        // 等待DOM更新后再触发resize
        await nextTick();
        window.dispatchEvent(new Event('resize'));
    } finally {
        loading.value = false;
    }
};

const init = () => {
    localCompareVal.value = props.compareVal;
    //getSnapCompareLists();
};

onMounted(async () => {
    init();
    // 等待DOM更新后再触发resize
    await nextTick();
    window.dispatchEvent(new Event('resize'));
    getSnapCompareLists();
});

watch(() => props.compareVal, (newVal) => {
    if (newVal) {
        init();
    }
}, { immediate: true, deep: true });

const handlePageChange = (page) => {
    pagination.value.current = page.current;;
    pagination.value.pageSize = page.pageSize;
}
const btn_compare = (comparedData: any) => {
    const params = {
        compareData_1: localCompareVal.value,
        compareData_2: comparedData,
    };
    // 创建新标签页
    const tabKey = `${localCompareVal.value.uuid || ''}_${comparedData.uuid || ''}`;
    const tabTitle = comparedData.engChineseName || comparedData.engName || "对比结果";

    // 检查是否已存在相同的tabKey
    const existingTabIndex = compareTabs.value.findIndex(tab => tab.key === tabKey);

    if (existingTabIndex === -1) {
        // 只有当不存在相同tabKey时才添加新标签页
        compareTabs.value.push({
            key: tabKey,
            title: tabTitle+'('+localCompareVal.value.edition+'对比'+comparedData.edition+')',
            compareParams: params
        });
    }

    // 切换到对应标签页
    activeTabKey.value = tabKey;
};

// 标签页编辑处理
const onTabEdit = (targetKey: string | MouseEvent, action: string) => {
    if (action === 'remove') {
        removeTab(targetKey as string);
    }
};

// 移除标签页
const removeTab = (targetKey: string) => {
    const targetIndex = compareTabs.value.findIndex(tab => tab.key === targetKey);
    compareTabs.value.splice(targetIndex, 1);

    // 如果关闭的是当前活动标签页，则切换到前一个标签页
    if (activeTabKey.value === targetKey) {
        activeTabKey.value = compareTabs.value.length
            ? compareTabs.value[compareTabs.value.length - 1].key
            : 'designView';
    }
};

const closeDrawer = () => {
    emit('close');
};

// 表格列定义
const columns = ref([
    {
        title: '序号',
        width: 50,
        align: 'center',
        key: 'index'
    },
    {
        title: '规则库名称',
        dataIndex: 'engChineseName',
        key: 'engChineseName',
        width: 300,
        ellipsis: true
    },
    {
        title: '版本',
        dataIndex: 'edition',
        key: 'edition',
        width: 80,
        ellipsis: true
    },
    {
        title: '创建时间',
        dataIndex: 'createdTimeStr',
        key: 'createdTimeStr',
        width: 200,
        ellipsis: true
    },
    {
        title: '是否发布',
        key: 'publishStatus',
        width: 80,
        ellipsis: true
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right'
    }
]);
</script>
<style lang="scss" scoped>
p {
    font-weight: bold;
}
.baseline-form {
    :deep(.ant-form-item) {
        .ant-form-item-label > label {
            font-weight: 600;
            font-size: 14px;
        }
    }
}


:deep(.ant-spin-nested-loading) {
    flex: 1;
}

:deep(.ant-table-pagination.ant-pagination) {
    position: sticky;
    bottom: 0;
    background-color: #fff;
    margin: 0 !important;
    padding: 10px 16px;
    width: 100%;
    z-index: 10;
    display: flex;
    justify-content: flex-end;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
}

/* 统一分页高度 */
:deep(.ant-pagination) {
    li {
        height: 32px;
        line-height: 30px;
    }
    
    .ant-pagination-item-link {
        height: 32px !important;
        line-height: 32px !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .ant-pagination-item {
        height: 32px;
        line-height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        a {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

:deep(.ant-pagination-options) {
    height: 32px;
    line-height: 32px;
}

:deep(.ant-pagination-options-size-changer) {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
    
    .ant-select-selector {
        height: 32px !important;
        line-height: 32px !important;
        display: flex;
        align-items: center;
    }
}

:deep(.ant-select-selector) {
    height: 32px !important;
    line-height: 32px !important;
}

:deep(.ant-pagination-options-quick-jumper) {
    height: 32px;
    line-height: 32px;
    
    input {
        height: 32px !important;
        line-height: 32px !important;
    }
}
</style>
