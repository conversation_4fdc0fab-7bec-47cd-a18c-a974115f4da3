<template>
  <!-- 如果 value 不是数组 -->
  <div v-if="!Array.isArray(value)">
    <a-tooltip placement="top">
      <template #title>
        <span>{{ text }}</span>
      </template>
      <div class="clip-box">
        <p class="clip-text">{{ text }}</p>
      </div>
    </a-tooltip>
  </div>
  <!-- 如果 value 是数组 -->
  <div v-else>
    <a-tooltip v-if="text" placement="top">
      <template #title>
        <span>{{ text }}</span>
      </template>
      <div class="clip-box">
        <p class="clip-text">{{ text }}</p>
      </div>
    </a-tooltip>
    <div v-else class="clip-box">
      <p class="clip-text">{{ text }}</p>
    </div>
  </div>
</template>

<script setup>
import store from "@/store";
import { findViewName } from "@/components/ruleEditCom/utils/displayUtil";

// 注入 ruleUuid
const ruleUuid = inject('ruleUuid', '');

// 定义 props
const props = defineProps({
  enumDictName: String,
  value: {
    type: [String, Array], // 使用数组来定义多个类型
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// 定义响应式数据 text
const text = ref('');

// 计算 text 的值
const label = () => {
  const list = store.getters.listMap[ruleUuid].dictMap;
  const optionData = list[props.enumDictName] || [];
  if (typeof props.value === "string" || !props.value) {
    return findViewName(optionData, props.value) || props.value; // 修正了这里的拼写错误
  } else {
    const nameList =
      props.value &&
      props.value.map &&
      props.value.map((item) => {
        return findViewName(optionData, item);
      });
    return (nameList && nameList.join(",")) || "";
  }
};

// 初始化 text
text.value = label();

// 监听 value 的变化，更新 text
watch(
  () => props.value,
  (newVal) => {
    text.value = label();
  }
);
</script>

<style scoped>
.clip-box {
  text-align: center;
  vertical-align: middle;
}
.clip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  height: 21px;
  line-height: 21px;
  text-align: center;
}
</style>
