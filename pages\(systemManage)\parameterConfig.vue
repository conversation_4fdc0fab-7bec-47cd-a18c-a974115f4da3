<!--参数配置-->

<script setup lang="ts">
    import { parameterList } from "@/api/parameter";
    import ParameterUpdate from "@/businessComponents/parameter/ParameterUpdate";
    definePageMeta({
        title: '参数配置'
    })

    const message = inject('message')
    const modal = inject('modal')
    interface Param {
        paramName: string;
        paramValue: string;
        discription: string;
    }

    const columns = [
        {
            title: '参数名称',
            dataIndex: 'paramName',
            key: 'paramName',
            align: 'left',
            ellipsis: true,
            width:200,
        },
        {
            title: '参数值',
            dataIndex: 'paramValue',
            key: 'paramValue',
            align: 'left',
            width:200,
            ellipsis: true
        },
        {
            title: '说明',
            dataIndex: 'discription',
            key: 'discription',
            align: 'left',
            width:250,
            ellipsis: true
        },
    ];

    const fetchParams = async (params: Record<string, any> = {}) => {
        try {
            const res = await parameterList({
                paramName: params.name,
                paramValue: params.val,
                page: params.page || 1,
                several: params.pageSize || 10,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取参数失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const isModalVisible = ref(false);
    const modalType = ref('');
    const datar = ref(null);
    const isSaving = ref(false);

    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'update') {
            datar.value = record;
        }
        isModalVisible.value = true;
    };

    // 参数更新组件
    const parameterUpdateComponent = ref()
    // 处理modal ok 事件
    function handleModalOk() {
        isSaving.value = true;
        let submitFun
        switch (modalType.value) {
            case 'update':
                submitFun = parameterUpdateComponent.value.submitFun;
                break;
        }
        submitFun && submitFun((success) => {
            if (success) {
                listLayout.value?.refresh();
                isModalVisible.value = false;
            }
            isSaving.value = false;
        });
    }

    const handleCancel = () => {
        isModalVisible.value = false;
        isSaving.value = false;
    };

    // 搜索配置
    const searchConfig = {
        // 简单搜索字段
        simpleSearchField: {
            label: '名称',
            field: 'name'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '名称',
                field: 'name',
                compType: 'input'
            },
            {
                label: '参数值',
                field: 'val',
                compType: 'input'
            }
        ]
    };

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: () => {},
        // 添加事件
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchParams
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record) => {
        return [
            {
                key: 'update',
                label: '更新',
                onClick: () => showModal('update', record)
            }
        ];
    };

    const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

    // 数据加载完成处理函数
    const handleDataLoaded = (response: any) => {
        if (response && response.data) {
            // 可以在这里处理数据加载完成后的逻辑
        }
    };
</script>

<template>
    <ListPage
        ref="listLayout"
        title="参数配置"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="false"
        :tableColumns="columns"
        :queryMethod="fetchParams"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
    >
    </ListPage>

    <!-- 更新对话框 -->
    <a-modal
        v-if="isModalVisible"
        :width='600'
        :visible="isModalVisible"
        :title="'参数更新'"
        @ok="handleModalOk"
        @cancel="handleCancel"
        okText="保存"
        :okButtonProps="{ disabled: isSaving }"
        class="parameter-modal"
    >
        <div style="max-height: 60vh; overflow-y: auto;">
            <ParameterUpdate ref="parameterUpdateComponent" v-if="modalType === 'update'" :datar="datar" />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.parameter-modal {
    :deep(.ant-modal-body) {
        padding: 20px 24px;
        overflow: visible;
    }

    :deep(.ant-modal-footer) {
        border-top: 1px solid #f0f0f0;
        padding: 10px 24px;
        margin-top: 0;
    }
}
</style>
