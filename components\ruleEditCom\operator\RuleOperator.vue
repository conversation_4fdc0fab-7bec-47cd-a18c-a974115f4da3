<template>
  <div id="rule_opera">
    <span class="operator">
      <span v-if="locked || isTrack" class="lockedText">
        <RuleOperatorNode :titleArr="titleArr" :hideEndBtn="true" :locked="locked" :isTrack="isTrack"
          :paramQuantity="dataSource.paramQuantity" :operatorParamsList="dataSource.operatorParams || []" :pos="pos"
          :enumDictName="dataSource.enumDictName" @onChildChange="onChildChange"
          @onOperatorParamChange="onOperatorParamChange" :predefineLine="predefineLine" :predefineCon="predefineCon"
          :preIndex="preIndex" ref="ron_ref" />
      </span>
      <RuleOperatorNode v-else :titleArr="titleArr" :hideEndBtn="true" :locked="locked" :isTrack="isTrack"
        :paramQuantity="dataSource.paramQuantity" :operatorParamsList="dataSource.operatorParams || []" :pos="pos"
        :enumDictName="dataSource.enumDictName" @onChildChange="onChildChange"
        @onOperatorParamChange="onOperatorParamChange" @click="operatorParamsSelector" :predefineLine="predefineLine"
        :predefineCon="predefineCon" :preIndex="preIndex" ref="ron_ref" />
    </span>
    <text-add-vars :textAddisShow="textAddisShow" :elAlertErrInfo="elAlertErrInfo" @textAddHide="textAddHide"
      @addVarsSure="addVarsSure" />
    <check-add-vars :checkListOption="optionData" :checkAddisShow="checkAddisShow" @checkboxHide="checkboxHide"
      @addVarsSure="addVarsSure" @searchChange="searchChange" />
  </div>
</template>
<script>
import store from "@/store";
import RuleOperatorNode from "./RuleOperatorNode.vue";
import { findLabel } from "@/components/ruleEditCom/utils/displayUtil";
import CheckAddVars from "@/components/ruleEditCom/ruleItemList/operationMenu/checkAddVars.vue";
import TextAddVars from "@/components/ruleEditCom/ruleItemList/operationMenu/textAddVars.vue";
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";

export default {
  name: "Operator",
  inject: {
    ruleUuid: { default: "" },
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    operatorData: Object,
    dataSource: {
      type: Object,
    },
    pos: String,
    locked: Boolean,
    isTrack: Boolean,
    onChange: Function,
    predefineLine: String,
    predefineCon: String,
    preIndex: Number,
  },
  data() {
    return {
      operatorParamsList: [],
      optionData: [],
      aError: [],
      elAlertErrInfo: [],
      checkListOption: [],
      cascaderData: {},
      checkAddisShow: false,
      textAddisShow: false,
      checkboxSearchTxt: "",
      _oldOptionD: [],
    };
  },
  components: {
    RuleOperatorNode,
    CheckAddVars,
    TextAddVars,
  },
  created() {
    this.optionData =
      store.getters.listMap[this.ruleUuid].dictMap[
      this.$props.dataSource["enumDictName"]
      ];
  },
  computed: {
    options() {
      if (this.operatorData) {
        let result = [];
        if (this.operatorData[this.type]) {
          result = this.operatorData[this.type];
        } else if (this.type.includes(".") && this.type.includes("List<")) {
          // 当字段是列表类型时
          result = this.operatorData["List"];
          
          // 如果是列表类型，需要过滤掉数组相关的操作符
          if (result && result.length > 0) {
            result = result.filter(item => {
              // 过滤掉含有"数组"关键字的操作符
              return !item.label.includes('<数组>') && !item.paramTypes?.includes('Array');
            });
          }
        } else {
          result = this.operatorData["Object"];
        }
        return result;
      }
      return [];
    },
    showSearchConfig() {
      return {
        filter: this.filter,
        matchInputWidth: false, // 搜索结果列表是否与input同宽；
      };
    },
    txt() {
      return findLabel(this.options, this.dataSource.value);
    },
    titleArr() {
      // ?代表最小可能匹配，（）为捕获标志，有捕获标志时候split同时返回捕获到的内容；
      const reg1 = /#.+?>/g;
      return this.txt ? this.txt.split(reg1) : [];
    },
    showSearchOptionData() { },
  },
  watch: {
    dataSource: {
      handler: function (val, oldVal) {
        this.optionData =
          store.getters.listMap[this.ruleUuid].dictMap[
          this.$props.dataSource["enumDictName"]
          ];
      },
      deep: true,
    },
    operatorData: {
      handler: function (val, oldVal) { },
      deep: true,
    },
    checkboxSearchTxt: {
      handler: function (val, oldVal) {
        this.optionData = JSON.parse(JSON.stringify(this._oldOptionD));
        if (val) {
          if (this.optionData && this.optionData.length) {
            this.optionData = this.optionData.filter((item) => {
              return (
                item["viewName"]
                  .toLowerCase()
                  .indexOf(this.checkboxSearchTxt.toLowerCase()) !== -1
              );
            });
          }
        }
      },
      deep: true,
    },
  },
  methods: {
    operatorParamsSelector(e) {
      globalEventEmitter.emit(SHOW_ACASCADER, {
        e,
        pos: this.pos,
        filterOData: this.options,
        fn: (pos, value, selectedOptions, operatorOptions) => {
          this.onSelectChange(value, selectedOptions);
        },
        flag: 'operator',
        cascaderSelectedValue: this.dataSource.value,
      });
    },
    onOperatorChange(newOperatorData) {
      const _newOperatorData = this.dataSource._derive(newOperatorData);
      this.$emit("onChange", this.pos, _newOperatorData);
    },
    onSelectChange(valueArr, selectedOptions) {
      const { label, paramTypes, paramQuantity } = selectedOptions[0];
      const num = label ? label.split("(").length - 1 : 0;
      const paramTypeArr = paramTypes ? paramTypes.split(",") : ["object"];
      const { enumDictName } = this.dataSource;
      const newOperatorData = {
        enumDictName: enumDictName || null,
        value: valueArr,
        operatorParams: [],
        paramQuantity,
      };

      for (let i = 0; i < num; i++) {
        const newVairable = {
          // variableType: enumDictName ? "constant" : "field",
          // enumDictName: enumDictName || null,
          // value: [],
          variableType: "constant",
          enumDictName: enumDictName || null,
          value: "",
        };
        newVairable.valueType = paramTypeArr[i];
        newOperatorData.operatorParams.push(newVairable);
        // debugger;
      }
      this.onOperatorChange(newOperatorData);
    },
    onChildChange(childPos, variableData, conditionValueType) {
      const pathArr = childPos.split("_");
      const index = Number(pathArr[pathArr.length - 1]);
      const {
        operatorParams: operatorParamsList = [],
        value,
        enumDictName = null,
        paramQuantity,
      } = this.dataSource;

      const newOperatorParamsList = operatorParamsList.map(
        (paramItem, paramIndex) => {
          if (index === paramIndex) {
            return variableData;
          }
          return paramItem;
        }
      );

      const newOperatorData = {
        value,
        paramQuantity,
        enumDictName,
        operatorParams: newOperatorParamsList,
      };
      this.onOperatorChange(newOperatorData);
    },
    onOperatorParamChange(key, index, event) {
      const {
        operatorParams: operatorParamsList = [],
        value,
        enumDictName = null,
        paramQuantity,
      } = this.dataSource;
      const firstParam = operatorParamsList[0];
      const { valueType } = firstParam;
      this.operatorParamsList = operatorParamsList;
      if (key === "addVar") {
        const newVairable = {
          variableType: enumDictName ? "constant" : "field",
          enumDictName,
          valueType,
          value: [],
        };

        const newOperatorData = {
          value,
          paramQuantity,
          enumDictName,
          operatorParams: [...operatorParamsList, newVairable],
        };
        this.onOperatorChange(newOperatorData);
      } else if (key === "addVars") {
        this.textAddisShow = true;
      } else if (key === "checkAddVars") {
        this.checkAddisShow = true;
        this.enumCheckbox();
      } else if (key === "deleteVar") {
        const newOperatorData = {
          value,
          paramQuantity,
          enumDictName,
          operatorParams: operatorParamsList.filter((item, _index) => {
            return _index !== index;
          }),
        };
        this.onOperatorChange(newOperatorData);
      }
    },
    enumCheckbox() {
      let _enumDictName = this.dataSource["enumDictName"];
      if (_enumDictName) {
        !this.optionData &&
          (this.optionData =
            store.getters.listMap[this.ruleUuid].dictMap[
            this.$props.dataSource["enumDictName"]
            ]);
      }
      this.optionData.map((_opItem, index) => {
        this.optionData[index] = {
          value: _opItem.value,
          viewName: _opItem.viewName,
          checked: false,
        };
      });
      this.operatorParamsList.map((item) => {
        this.optionData.map((opItem, index) => {
          if (item.value === opItem.value) {
            this.optionData[index] = {
              value: opItem.value,
              viewName: opItem.viewName,
              checked: true,
            };
          }
        });
      });
      this._oldOptionD = JSON.parse(JSON.stringify(this.optionData));
    },
    addVarsSure(list, checkOpt) {
      let aVars = [];
      let _enumDictName = this.dataSource["enumDictName"];
      let _operatorParamsListLoopStart = 0;
      this.aError = [];
      this.elAlertErrInfo = [];
      if (checkOpt === true) {
        aVars = list;
      } else {
        list && (aVars = list.split(/[,，;；]/g));
      }

      if (_enumDictName && checkOpt !== true) {
        !this.optionData &&
          (this.optionData =
            store.getters.listMap[this.ruleUuid].dictMap[
            this.$props.dataSource["enumDictName"]
            ]);
        for (let i = 0; i < aVars.length; i++) {
          this.aError.push({
            isErr: this.optionData.some((v) => {
              return v.viewName === aVars[i];
            }),
            value: aVars[i],
          });
        }
      }
      if (
        this.aError.some((obj) => {
          return obj.isErr === false;
        })
      ) {
        this.aError.some((item) => {
          if (item.isErr === false) {
            this.elAlertErrInfo.push(item.value);
          }
        });
        return;
      }
      if (checkOpt === true && _enumDictName) {
        if (this.operatorParamsList && this.operatorParamsList.length > 1) {
          this.operatorParamsList.splice(1);
        }
      }

      if (_enumDictName) {
        // domain
        let aItem = aVars.length ? aVars[0] : "";
        let _val = "";

        if (checkOpt === true) {
          _val = aItem;
          _val && (this.operatorParamsList[0]["value"] = _val);
          _operatorParamsListLoopStart = 1;
        } else {
          if (
            this.operatorParamsList.length === 1 &&
            !this.operatorParamsList[0].value
          ) {
            // 没有值
            _val = aItem
              ? this.optionData[
              this.optionData.findIndex((v) => {
                return v.viewName === aItem;
              })
              ]["value"]
              : "";
            _val && (this.operatorParamsList[0]["value"] = _val);
            _operatorParamsListLoopStart = 1;
          }
        }
      } else {
        if (
          this.operatorParamsList.length === 1 &&
          !this.operatorParamsList[0].value
        ) {
          // 没有值
          aVars.length && (this.operatorParamsList[0]["value"] = aVars[0]);
          _operatorParamsListLoopStart = 1;
        }
      }
      if (aVars.length === 1) {
        this.createVars(aVars[0], true, true);
      }
      for (let i = _operatorParamsListLoopStart; i < aVars.length; i++) {
        setTimeout(() => {
          if (checkOpt === true) {
            this.createVars(aVars[i], true);
          } else {
            this.createVars(aVars[i]);
          }
        }, 0);
      }
      this.textAddisShow = false;
      this.checkAddisShow = false;
    },
    // 创建批量数据
    createVars(val, checkOpt, first) {
      const {
        operatorParams: operatorParamsList = [],
        value,
        enumDictName = null,
        paramQuantity,
      } = this.dataSource;
      const firstParam = operatorParamsList[0];
      const { valueType } = firstParam;
      this.operatorParamsList = operatorParamsList;
      const newVairable = {
        variableType: enumDictName ? "constant" : "field",
        enumDictName,
        valueType,
        value: [],
      };
      const newOperatorData = {
        value,
        paramQuantity,
        enumDictName,
        operatorParams: [...operatorParamsList, newVairable],
      };
      !first && this.onOperatorChange(newOperatorData);
      // domain
      enumDictName &&
        this.$nextTick(() => {
          let _val = "";
          if (checkOpt === true) {
            _val = val;
          } else {
            _val =
              this.optionData[
              this.optionData.findIndex((v) => {
                return v.viewName === val;
              })
              ]["value"];
          }
          if (typeof _val === "string" || !isNaN(_val)) {
            const newVariable = {
              value: _val,
              valueType: valueType,
              enumDictName: enumDictName || null,
              variableType: "constant",
            };
            if (valueType === "Boolean") {
              newVariable.domain = "boolean";
            }
            if (first) {
              this.onChildChange(
                `${this.pos}_operator_0`,
                newVariable,
                newVariable.valueType
              );
            } else {
              this.onChildChange(
                `${this.pos}_operator_${this.operatorParamsList.length}`,
                newVariable,
                newVariable.valueType
              );
            }
          }
        });
      // 字符串
      !enumDictName &&
        this.$nextTick(() => {
          const { dataSource } = this;
          const newVariableData = {};
          newVariableData.valueType = "String";
          newVariableData.variableType = "constant";
          newVariableData.value = val;
          if (this.enumDictName) {
            newVariableData.enumDictName = this.enumDictName;
          }
          const _newVariableData = dataSource._derive(newVariableData);
          this.onChildChange(
            `${this.pos}_operator_${this.operatorParamsList.length}`,
            _newVariableData,
            dataSource.valueType
          );
        });
    },
    filter(inputValue, path) {
      return path.some((option) => {
        const opetionLabel = option.label.toLowerCase();
        const _inputValue = inputValue.toLowerCase();
        return opetionLabel.indexOf(_inputValue) > -1;
      });
    },
    searchChange(val) {
      this.checkboxSearchTxt = val;
    },
    checkboxHide() {
      this.checkAddisShow = false;
    },
    textAddHide() {
      this.elAlertErrInfo = [];
      this.textAddisShow = false;
    },
  },
};
</script>
