<template>
    <div class="rule-release-details">
        <a-tabs
                v-model:activeKey="editableTabsValue"
                type="editable-card"
                @edit="onEdit"
                hide-add
        >
            <a-tab-pane
                    v-for="item in editableTabs"
                    :key="item.name"
                    :closable="item.closable"
            >
                <template #tab>
                    <a-tooltip :title="item.title">
                        <span>{{ subLongName(item.title) }}</span>
                    </a-tooltip>
                </template>
                <component
                    :is="item.content"
                    @datailsTest="baseLinedetail"
                    :linkOptions="componentProps"
                />
            </a-tab-pane>
            <template #tabBarExtraContent>
                <TabRight v-model:editableTabsValue="editableTabsValue" v-model:editableTabs="editableTabs" :showClose="editableTabs.length > 1"/>
            </template>
        </a-tabs>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, computed, watch } from 'vue';
import { Tabs } from 'ant-design-vue';
import { defineAsyncComponent } from 'vue';
import TabRight from '@/components/TabRight.vue';
import { subLongName } from "@/utils/ruleUtil"; // 引入截断函数

// 定义props接收从抽屉传入的参数
const props = defineProps({
    detailsId: {
        type: String,
        default: ''
    },
    detailsName: {
        type: String,
        default: ''
    },
    edition: {
        type: String,
        default: ''
    }
});

// 组件属性计算
const componentProps = computed(() => ({
    uuid: props.detailsId,
    detailsId: props.detailsId,
    detailsName: props.detailsName,
    edition: props.edition
}));

const ATabs = Tabs;
const ATabPane = Tabs.TabPane;

const baseLineLeft = defineAsyncComponent(() =>
    import('@/businessComponents/baseLineManage/BaseLineLeft.vue')
);

const ruleHistoryRight = defineAsyncComponent(() =>
    import('@/businessComponents/baseLineManage/RuleHistoryRight.vue')
);
import store from  '@/store';
const tabData = ref([]);
const editableTabsValue = ref('designView');
const arrList = ref(true);
const BaseDataUid = ref('');
const ruleTitle = ref('');
const exportuuid = ref('');
const editableTabs = ref([
    {
        title: '规则列表',
        name: 'designView',
        content: baseLineLeft,
        closable: false,
    },
]);
const tabIndex = ref(1);

const tMouse = (obj: HTMLElement) => {
    obj.setAttribute('title', obj.innerText);
};

const baseLinedetail = (v: any) => {
    const name = `ruleHistoryRight_${v.ruleName}`;
    !editableTabs.value.some((i) => {
        return i.name === name;
    }) && editableTabs.value.push({
        title: v.ruleName,
        name: name,
        content: ruleHistoryRight,
        closable: true,
    });

    // 保存到store，兼容原有逻辑
    store.commit("settings/Base_ID", v);

    editableTabsValue.value = name;
}

const onEdit = (targetKey: any, action: 'add' | 'remove') => {
    if (action === 'remove') {
        removeTab(targetKey);
    }
};

const removeTab = (targetKey: string) => {
    let tabs = editableTabs.value;
    let activeKey = editableTabsValue.value;
    if (activeKey === targetKey) {
        // 找到要关闭的标签在数组中的索引
        let closeIndex = -1;
        tabs.forEach((tab, index) => {
            if (tab.name === targetKey) {
                closeIndex = index;
            }
        });
        
        if (closeIndex >= 0) {
            // 如果还有其他非designView标签，则切换到另一个标签
            const remainingTabs = tabs.filter(tab => tab.name !== targetKey && tab.name !== 'designView');
            if (remainingTabs.length > 0) {
                // 尝试切换到下一个标签，如果没有下一个则切换到上一个
                const nextTab = tabs[closeIndex + 1] || tabs[closeIndex - 1];
                if (nextTab) {
                    activeKey = nextTab.name;
                }
            } else {
                // 如果没有其他标签，返回到designView
                activeKey = 'designView';
            }
        }
    }

    editableTabsValue.value = activeKey;
    editableTabs.value = tabs.filter((tab) => tab.name !== targetKey);
};

// 关闭全部
const changeTab = (val: any) => {
    if(val === 'close'){
        editableTabs.value = editableTabs.value.filter((item) => {return item.name === 'designView'});
    }
    editableTabsValue.value = 'designView'
}

onMounted(() => {
    // 使用从props传入的参数初始化数据
    if (props.detailsId) {
        BaseDataUid.value = props.detailsId;
        ruleTitle.value = props.detailsName;
        // 可以在这里加载数据等操作
    }
});
</script>
<style lang="scss" scoped>
.rule-release-details{
    height: 100%;
    overflow: auto;
}
</style>
