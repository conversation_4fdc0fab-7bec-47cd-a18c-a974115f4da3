<template>
  <div class="ruleUpdate" style="">
    <div v-if="!isGetData">
      <span style="margin-left:15px">loading...</span><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div>
    </div>
    <rule-editor-flow
      v-if="isGetData"
      theme="oneDark_Light"
      ref="ruleEditor"
      :operatorTypeList="operatorTypeList"
      :dataEntryList="dataEntryList"
      :fieldsDataList="fieldsDataList"
      :baseMethodListMap="baseMethodListMap"
      :methodsDataList="methodsDataList"
      :validateResult="validateResult"
      :predefineList="predefineList"
      :initModelData="initModelData"
      :ruleData="ruleData"
      :ruleDrls="self_ruleDrl"
      :injectModelData="injectModelData"
      @save="save"
      @validate="validate"
      @changePredefineList="changePredefineList"
      :locked="false"
      :uuid="uuid"
      :demandUuid="demandUuid"
      mode="update"
    />
  </div>
</template>

<script>
import store from "@/store";
import RuleEditorFlow from "./editor/rule/RuleEditorFlow.vue";
import * as util from "@/components/ruleEditCom/utils/util";

export default {
  name: "RuleUpdate",
  inject: {
    ruleUuid: { default: '' },
  },
  components: {
    "rule-editor-flow": RuleEditorFlow
  },
  props: {
    modelData: {
      type: Object,
      default: () => {}
    },
    ruleContent: {
      type: Object,
      default: () => {}
    },
    uuid: String,
    ruleDrl: String,
    demandUuid: String
  },
  data() {
    return {
      operatorTypeList: null,
      fieldsDataList: [],
      dataEntryList: [],
      baseMethodListMap: {},
      methodsDataList: [],
      validateResult: {},
      initModelData: {},
      injectModelData: {},
      predefineList: [],
      ruleData: {},
      self_ruleDrl: this.ruleDrl,
      listMap: {
        dictMap: {},
        initModelData: {},
        order: 1
      }
    };
  },
  watch: {
    modelData: {
      handler: function(val, oldVal) {
        if (Object.keys(val).length > 0) {
          this.getModalInit(val);
          this.initModelData = val;
          this.getDataPredefinedRule(this.ruleContent["nodeRule"]);
        }
      },
      deep: true
    },
    "ruleContent.nodeId": {
      handler(newValue, oldValue) {
        this.$data.ruleData = this.ruleContent["nodeRule"];
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    isGetData() {
      return Object.keys(this.initModelData).length > 0;
    }
  },
  methods: {
    changePredefineList(val) {
      this.predefineList = val;
      const list = [];
      const includesList = [
        "String",
        "Boolean",
        "Date",
        "Double",
        "Short",
        "Integer",
        "Long",
        "Float",
        "BigDecimal"
      ];
      const numberList = [
        "Double",
        "Short",
        "Integer",
        "Long",
        "Float",
        "BigDecimal"
      ];
      val &&
        val.map(item => {
          const modelData = {
            valueType: numberList.includes(item.variableType)
              ? "Double"
              : item.variableType,
            name: item.id,
            viewName: item.preViewName,
            status: "01",
            display: true
          };
          if (item.operatorType === "equals") {
            if (includesList.includes(item.variableType)) {
              modelData.valueType = item.variableType;
            } else {
              const name = this.getEqualsVariableName(
                item.variableType,
                item.selectVariableValue.conditions[0].variable
              );
              modelData.valueType = name;
              modelData.fieldsList = this.initModelData.complexModels.find(
                items => items.valueType === name
              ).fieldsList;
              modelData.methodsList = this.initModelData.complexModels.find(
                items => items.valueType === name
              ).methodsList;
            }
          } else {
            modelData.valueType = item.variableType;
            const findItem = this.initModelData.complexModels.find(
              items => items.valueType === item.variableType
            );
            modelData.fieldsList = findItem ? findItem.fieldsList : [];
            modelData.methodsList = findItem ? findItem.methodsList : [];
            // modelData.methodsList = item.variableType;
          }

          list.push(modelData);
        });
      const newList = [...this.initModelData.complexModels, ...list];
      this.injectModelData = { ...this.initModelData, complexModels: newList };
      this.getModalInit(this.injectModelData, this.listMap.order + 1);
    },
    getEqualsVariableName(sourceName, data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        name.push(obj.valueType ? obj.valueType : obj.name);
        if (obj.next) {
          deepFn(obj.next);
        }
      }
      if (name.includes(sourceName) && name[name.length - 1].includes(".")) {
        return name[name.length - 1];
      }

      return sourceName;
    },
    getVariableName(data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        if (!obj.next) {
          name.push(obj.valueType ? obj.valueType : obj.name);
        } else {
          deepFn(obj.next);
        }
      }
      return name[0];
    },
    getFieldsType(data) {
      const { baseModelList, datasetGroupList, modelList } = this;
      const baseMethodListMap = util.getBaseMethodMapping(baseModelList);
      const _datasetGroupList = util.getDatasetGroupList(
        datasetGroupList,
        baseMethodListMap
      );
      const { fieldsData, methodsData } = util.getModelList(modelList);
      this.dataEntryList = _datasetGroupList;
      this.fieldsDataList = fieldsData;
      this.methodsDataList = methodsData;
      this.baseMethodListMap = baseMethodListMap;
    },
    getPredefinedRule(list) {
      const predefines = [];
      list &&
        list.map(item => {
          const obj = {
            name: item.id,
            viewName: item.preViewName,
            variableType: item.variableType,
            variableTypeName: item.variableTypeName,
            operatorType: item.operatorType,
            variable: {
              type: item.inputOrSelect,
              value:
                item.inputOrSelect === "Select"
                  ? item.selectVariableValue
                  : item.inputvariableValue
            },
            conditionsRule: item.hasConditions ? item.conditions : {}
          };
          predefines.push(obj);
        });

      return predefines;
    },

    getDataPredefinedRule(data) {
      const { predefines = [] } = data;
      const numberList = [
        "Double",
        "Short",
        "Integer",
        "Long",
        "Float",
        "BigDecimal"
      ];
      const _predefines = [];
      predefines &&
        predefines.map(item => {
          const obj = {
            id: item.name,
            preViewName: item.viewName,
            variableType: numberList.includes(item.variableType)
              ? "Double"
              : item.variableType,
            variableTypeName: item.variableTypeName,
            operatorType: item.operatorType,
            inputOrSelect: item.variable.type,
            inputvariableValue:
              item.variable.type === "Input" ||
              item.variable.type === "BooleanInput"
                ? item.variable.value
                : "",
            selectVariableValue:
              item.variable.type === "Select" ? item.variable.value : {},
            hasConditions: Object.keys(item.conditionsRule).length > 0,
            conditions: item.conditionsRule
          };
          _predefines.push(obj);
        });

      // return _predefines;
      this.changePredefineList(_predefines);
    },

    save(ruleData) {
      const { ruleName, ...others } = ruleData;
      const param = {
        ruleContent: {
          conditionExpression: others.conditionExpression,
          conditions: others.conditions,
          actions: [],
          elseActions: [],
          predefines: [],
          ruleAttributes: []
        }
      };
      this.$emit("save", param);
    },
    validate(ruleData) {
      this.validateResult = ruleData;
    },
    getModalInit(data, order) {
      const modelData = data;
      const {
        complexModels,
        dicts,
        modelDomains,
        simpleModels,
        sysModels,
        sysOperators
      } = modelData;
      const operatorTypeList = util.getoperatorTypeList(sysOperators);
      const baseMethodListMap = util.getBaseMethodMapping(sysModels);
      const _datasetGroupList = util.getDatasetGroupList(
        simpleModels,
        baseMethodListMap
      );
      const { fieldsDataList, methodsDataList } = util.getModelList(
        complexModels
      );
      const dictMap = {};
      dicts.forEach(item => {
        const { dictName, values } = item;
        dictMap[dictName] = values.map(item2 => {
          return {
            value: item2.key,
            viewName: item2.value
          };
        });
      });
      modelDomains.map(item => {
        const { name, domainAttributes } = item;
        dictMap[name] = domainAttributes.map(item2 => {
          return {
            value: item2.value,
            viewName: item2.viewName
          };
        });
      });
      const firstFieldItemList = util.getFirstFieldItemList(
        fieldsDataList,
        baseMethodListMap,
        fieldsDataList
      );
      let tempObj={
        dataEntryList: _datasetGroupList,
        fieldsDataList,
        methodsDataList,
        baseMethodListMap,
        operatorTypeList,
        firstFieldItemList
      }
      store.commit('setInitModelData',{ list: tempObj, ruleUuid: this.ruleUuid });
      this.loading = false;
      this.listMap.dictMap = dictMap;
      this.listMap.initModelData = tempObj;
      if (order) {
        this.listMap.order = order;
      }
      this.dataEntryList = _datasetGroupList;
      this.fieldsDataList = fieldsDataList;
      this.methodsDataList = methodsDataList;
      this.baseMethodListMap = baseMethodListMap;
      this.operatorTypeList = operatorTypeList;
      store.commit('setListMap',{ list: this.listMap, ruleUuid: this.ruleUuid });
    }
  },
};
</script>
