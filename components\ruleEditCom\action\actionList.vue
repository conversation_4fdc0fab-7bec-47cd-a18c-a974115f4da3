<template>
  <div class="action-list-div">
    <!-- 结构化文字 -->
    <p class="structuralWord" style="margin-bottom: 10px;">
      <span class="txtItem structuralWordText">那么</span>
    </p>
    <!-- 动作组件列表 -->
    <div class="actionCom">
      <ActionCom
        v-for="(item, index) in actionData"
        :key="'action' + index"
        :actionData="actionData[index]"
        :pos="pos + '_' + index"
        :actionValid="validList[index] || {}"
        :locked="locked"
        :isTrack="isTrack"
        @addActionItem="handleAddActionItem"
        @deleteActionItem="handleDeleteActionItem"
        @onChange="handleActionComChange"
        @replaceActionItem="handleReplaceActionItem"
        ref="action_com"
      />
    </div>
  </div>
</template>

<script setup>
import ActionCom from "./action.vue";

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  },
  actionData: {
    type: Array,
    default: () => []
  },
  validList: {
    type: Array,
    default: () => []
  }
});

// 定义组件的 emits
const emit = defineEmits([
  'addActionItem',
  'deleteActionItem',
  'actionComChange',
  'replaceActionItem'
]);

// 处理添加动作项事件
const handleAddActionItem = (pos) => {
  emit('addActionItem', 'action', pos);
};

// 处理删除动作项事件
const handleDeleteActionItem = (pos) => {
  emit('deleteActionItem', 'action', pos);
};

// 处理动作组件变化事件
const handleActionComChange = (pos, newActionData, oldRowData) => {
  emit('actionComChange', 'action', pos, newActionData, oldRowData);
};

// 处理替换动作项事件
const handleReplaceActionItem = (pos) => {
  emit('replaceActionItem', 'action', pos);
};

</script>

<style scoped>
.action-highlight:hover {
  border: 1px dotted;
  border-color: #3483f8;
  background-color: rgba(52, 131, 248, 0.1);
  border-radius: 3px;
  cursor: pointer;
}
</style>

