<!--复制按钮组件，可用于文本内容复制，用法：
1.content：传入要复制的文本内容
2.title：复制按钮的提示文本（有默认值）
3.successMessage：复制成功的提示信息（有默认值）-->
<template>
  <a-tooltip placement="top" :title="title" >
    <a-button type="link" class="copy-btn" @click="copyText">
      <IconCopy />
    </a-button>
  </a-tooltip>
</template>

<script setup lang="ts">

const props = defineProps({
  // 要复制的内容
  content: {
    type: String,
    required: true
  },
  // 复制按钮的提示文本
  title: {
    type: String,
    default: '复制内容'
  },
  // 复制成功的提示信息
  successMessage: {
    type: String,
    default: '内容已复制到剪贴板'
  }
});

const message = inject<any>('message');

// 复制文本功能
const copyText = () => {
  const textToCopy = props.content;

  // 创建一个临时的文本区域元素
  const textArea = document.createElement('textarea');
  textArea.value = textToCopy;
  textArea.style.position = 'fixed';  // 避免滚动到底部
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  let success = false;
  try {
    // 尝试使用document.execCommand进行复制
    success = document.execCommand('copy');
    if (success) {
      message.success(props.successMessage);
    } else {
      // 如果execCommand失败，尝试使用clipboard API
      if (navigator && navigator.clipboard) {
        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            message.success(props.successMessage);
          })
          .catch(() => {
            message.error('复制失败，请手动复制');
          });
      } else {
        message.error('复制失败，请手动复制');
      }
    }
  } catch (err) {
    message.error('复制失败，请手动复制');
  } finally {
    // 清理临时元素
    document.body.removeChild(textArea);
  }
};
</script>

<style scoped>
.copy-btn {
  padding: 0;
  color: #0a0a0a;
  font-size: 14px;
}
.copy-btn:hover {
  color: #0c0c0c;
}
</style>
