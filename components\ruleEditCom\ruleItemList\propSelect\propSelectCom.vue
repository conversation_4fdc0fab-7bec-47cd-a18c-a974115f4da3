<template>
  <span class="fieldCom">
    <!-- 根据 display 属性显示不同的内容 -->
    <template v-if="display === true">
      <span class="lockedText" v-if="locked || isTrack">
        <!-- 根据 nameList 的长度显示不同的内容 -->
        <template v-if="nameList && nameList.length > 0">
          <span class="txtItem" v-if="flag === 'dataEntry'">{{
            lastNameObj.name || ""
          }}</span>
          <span class="txtItem" v-else>{{ firstNameObj.name || "" }}</span>
        </template>
        <template v-else>
          <span class="txtItem">{{ txt }}</span>
        </template>
      </span>
      <span v-else class="pointer" @click="handleClick">
        <template v-if="nameList && nameList.length > 0">
          <span class="txtItem" v-if="flag === 'dataEntry'">{{
            lastNameObj.name || ""
          }}</span>
          <span class="txtItem" :title="titleStrD || titleStr" v-else>{{
            firstNameObj.name || ""
          }}</span>
        </template>
        <template v-else>
          <span class="txtItem" :title="titleStrD || titleStr">{{ txt }}</span>
        </template>
      </span>
    </template>
    <!-- 根据 next 属性显示连接词 -->
    <template
      v-if="
        next &&
        display !== false &&
        nextVariableType !== 'method' &&
        nextValueType !== 'Boolean'
      "
    >
      <span class="joiner" :title="titleStrD || titleStr">的</span>
    </template>
    <!-- 根据 next 属性显示 VariableCom 组件 -->
    <template v-if="next">
      <span>
        <VariableCom
          :pos="pos + '_pv_' + '0'"
          :locked="locked"
          :isTrack="isTrack"
          :dataSource="next"
          @onChange="onChildChange"
          signValue=""
          :hideFrontBtn="true"
          :hideEndBtn="true"
          :propOptions="nextOptions"
          :titleStr="titleStrD || titleStr"
          ref="vcv-ref"
          :changeFun="changeFun"
          :refListData="refListData"
          :isTable="isTable"
          :noRuleCellUnit="noRuleCellUnit"
        />
      </span>
    </template>
  </span>
</template>

<script setup>
import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import { getLabelArrByValues } from "@/components/ruleEditCom/utils/displayUtil";

// 注入 ruleUuid
const ruleUuid = inject("ruleUuid", "");

// 定义 props
const props = defineProps({
  nameList: {
    type: Array,
    default: () => [],
  },
  variableData: {
    type: Object,
    default: () => ({}),
  },
  pos: {
    type: String,
    default: "erule",
  },
  flag: String,
  locked: Boolean,
  isTrack: Boolean,
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
  next: {
    type: Object,
    default: () => null,
  },
  nextOptions: {
    type: Array,
    default: () => [],
  },
  titleStrD: String,
  refListData: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义响应式数据
const txt = ref("请选择属性");
const titleStr = ref("");
const firstNameObj = ref({});
const lastNameObj = ref({});
const nameListObj = ref([]);
const changeFun = ref(false);
const display = ref({});
const nextVariableType = ref(null);
const nextValueType = ref(null);

// 方法
const updateNameListObj = (ml) => {
  nameListObj.value = ml;
};

const onSelectChange = (pos, value, selectedOptions,operatorOptions) => {
  changeFun.value = true;
  let ml =
    selectedOptions && value && value.length > 0
      ? getLabelArrByValues(selectedOptions, value)
      : [];
  const { variableData } = props;
  const { newVariableObj, finalValueType } =
    getNewVariableData(selectedOptions);
  const _newVariableData = variableData._derive(newVariableObj);
  emit("onChange", pos, _newVariableData, finalValueType,operatorOptions);
  nameListObj.value = ml;
};

const getVariableData = (option, nextVariableData) => {
  const {
    value,
    valueType,
    paramsList,
    isDatasetItem,
    isMethodItem,
    dictName,
    display,
  } = option;
  let { domain } = option;
  if (valueType === "Boolean") {
    domain = "boolean";
  }
  const newValueObj = {};

  if (isMethodItem) {
    newValueObj.value = [value];
    newValueObj.valueType = valueType;
    newValueObj.domain = domain;
    newValueObj.variableType = "method";
    if (nextVariableData) {
      newValueObj.next = nextVariableData;
    }
    if (paramsList && paramsList.length > 0) {
      newValueObj.paramsList = paramsList;
      newValueObj.methodParams = paramsList.map((item) => {
        const { domain: item_domain, valueType: item_valueType } = item;
        if (item_valueType === "List<String>" && !props.noRuleCellUnit) {
          let _newVar = {};
          if (item_domain) {
            _newVar.singleParams = [
              {
                valueType: "String",
                variableType: "constant",
                value: "",
                enumDictName: item_domain,
              },
            ];
            _newVar.enumDictName = item_domain;
          } else {
            _newVar.singleParams = [
              {
                valueType: "String",
                variableType: "constant",
                value: "",
              },
            ];
          }
          _newVar.valueType = "List<String>";
          _newVar.variableType = "method";
          return _newVar;
        } else if (item_domain) {
          return {
            value: "",
            valueType: item_valueType,
            enumDictName: item_domain,
            variableType: "constant",
          };
        } else if (item_valueType === "String"||(item_valueType === "List<String>" && props.noRuleCellUnit)) {
          return {
            value: "",
            valueType: item_valueType,
            variableType: "constant",
          };
        }
        return {
          value: [],
          valueType: item_valueType,
          variableType: "dataEntry",
        };
      });
    }
  } else if (isDatasetItem) {
    if (nextVariableData && nextVariableData.variableType === "dataEntry") {
      newValueObj.value = nextVariableData.value;
      newValueObj.value.unshift(value);
      newValueObj.valueType = nextVariableData.valueType;
      newValueObj.dictName = nextVariableData.dictName;
      newValueObj.variableType = "dataEntry";
      if (nextVariableData.next) {
        newValueObj.next = nextVariableData.next;
      }
    } else {
      newValueObj.value = [value];
      newValueObj.valueType = valueType;
      newValueObj.variableType = "dataEntry";
      newValueObj.dictName = dictName;
      if (nextVariableData) {
        newValueObj.next = nextVariableData;
      }
    }
  } else {
    newValueObj.value = [value];
    newValueObj.valueType = valueType;
    newValueObj.domain = domain;
    newValueObj.variableType = "field";
    newValueObj.display = display;
    if (nextVariableData) {
      newValueObj.next = nextVariableData;
    }
  }
  return newValueObj;
};

const getNewVariableData = (optionList) => {
  const len = optionList.length;
  let newVariableObj = {};
  let finalValueType;
  for (let i = len; i > 0; i--) {
    const option = optionList[i - 1];
    if (i === len) {
      newVariableObj = getVariableData(option);
      finalValueType = option.valueType;
    } else {
      newVariableObj = getVariableData(option, newVariableObj);
    }
  }
  return { newVariableObj, finalValueType };
};

const handleClick = (e) => {
  emit(
    "calcPropData",
    e,
    props.pos,
    props.preIndex,
    props.predefineLine,
    props.predefineCon,
    props.variableData,
    (pos, value, opt, operatorOptions) => {
      onSelectChange(pos, value, opt, operatorOptions);
    },
    "proselect"
  );
  e.stopPropagation();
};

const onChildChange = (childPos, newValueObj, finalValueType, operatorOptions) => {
  const { variableData, pos } = props;
  const newComData = util.cloneRuleData(variableData);
  const newValueType = finalValueType;
  newComData.next = newValueObj;
  emit("onChange", pos, newComData, newValueType, operatorOptions);
};

const getRealDisplay = (variableData) => {
  const { next, display, __context } = variableData;
  const { prever } = __context;
  const lastVar = variableData._getEndVar();
  let realDisplay = display;
  if (!next) {
    realDisplay = true;
  }
  if (prever && lastVar !== variableData) {
    realDisplay = true;
  }
  if (variableData === lastVar) {
    realDisplay = true;
  }
  return realDisplay;
};

const methodTipInit = (posStr, ownerVar) => {
  if (props.pos.indexOf(posStr) !== -1) {
    let itemIndex = props.pos.split(posStr)[1].split("_");
    if (itemIndex && itemIndex.length === 1) {
      if (
        props.variableData.__context &&
        props.variableData.__context.owner &&
        props.variableData.__context.owner[ownerVar]
      ) {
        let _meData =
          ownerVar === "variable"
            ? props.variableData.__context.owner[ownerVar]
            : props.variableData.__context.owner[ownerVar][0];
        setMethodTip(_meData, itemIndex[0]);
      }
    }
  }
};

const setMethodTip = (_acData, _index) => {
  if (_acData && _acData.next && _acData.next["variableType"] === "method") {
    let valKeyStr = _acData.next["value"] && _acData.next["value"][0];
    let _methodsDataList =
      store.getters.listMap[ruleUuid].initModelData.methodsDataList;
    let resOptionItem = _methodsDataList.find((item) => {
      return item.valueType === _acData.valueType;
    });
    if (resOptionItem && resOptionItem.children) {
      let resItemObj = resOptionItem.children.find((item) => {
        return item.value === valKeyStr;
      });
      let itemTip = resItemObj && resItemObj.label;
      titleStr.value = itemTip;
    }
  }
};

// 定义 emit 事件
const emit = defineEmits(["onChange", "calcPropData"]);

// 监听 nameListObj 的变化
watch(
  nameListObj,
  (newVal) => {
    if (newVal&&newVal.length>0) {
      firstNameObj.value = newVal[0] || {};
      lastNameObj.value = newVal[newVal.length - 1] || {};
    }
  },
  { deep: true }
);

// 监听 nameList 的变化
watch(
  () => {
    // 返回一个标识值，而不是直接监听props.nameList
    if (!props.nameList) return null;
    // 返回一个唯一标识符，只在nameList结构变化时才会变化
    return props.nameList.map(item => item.name || '').join('|');
  },
  (newVal, oldVal) => {
    // 只有当标识值确实发生变化时才处理
    if (newVal === oldVal || oldVal === undefined) return;
    
    // 处理nameList内容更新
    if (props.nameList) {
      firstNameObj.value = props.nameList[0] || {};
      lastNameObj.value = props.nameList[props.nameList.length - 1] || {};
    }
  }
);

watch(
  () => props.variableData,
  (newVal) => {
    if (newVal) {
      display.value = getRealDisplay(newVal);
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => props.next,
  (newVal) => {
    if (newVal) {
      newVal.variableType && (nextVariableType.value = newVal.variableType);
      newVal.valueType && (nextValueType.value = newVal.variableType);
    }
  },
  { immediate: true, deep: true }
);

// 在组件挂载时初始化方法提示
onMounted(() => {
  const { nameList } = props;
  nameListObj.value = nameList;
  firstNameObj.value = nameList[0] || {};
  lastNameObj.value = nameList[nameList.length - 1] || {};
  methodTipInit("_pv_0_method_", "variable");
  methodTipInit("_actionMethod_0_pv_0_method_", "actionParams");
});
</script>
