<!-- 默认任务审核机构页 -->

<script setup lang="ts">

import { useRouter, useRoute } from 'vue-router';
import { getSelect, getPostson, defaultAuditOrg, delDefaultAuditOrg } from '@/api/post_permissions';
definePageMeta({
    title: '默认任务审核机构'
})


interface Option {
    code: string;
    name: string;
}

interface Form {
    province: string;
    land: string;
    county_city: string;
}

interface ArrAll {
    orgName: string;
    orgId: string;
    level: string;
    parentOrgId: string;
    id: string;
    businessLineId: string;
}

const router = useRouter();
const route = useRoute();
const message = inject('message')
const modal = inject('modal')

// 定义props
const props = defineProps({
    record: {
        type: Object,
        required: true
    }
});

// 定义emit
const emit = defineEmits(['close', 'success']);

const form = ref<Form>({
    province: '',
    land: '',
    county_city: '',
});

const arrAll = ref<ArrAll>({
    orgName: '',
    orgId: '',
    level: '',
    parentOrgId: '',
    id: '',
    businessLineId: '',
});

const province = ref<Option[]>([]);
const landarr = ref<Option[]>([]);
const county = ref<Option[]>([]);
const soncode = ref<string>('');
const strordId = ref<string>('');
const strordName = ref<string>('');
const selectprovinceName = ref<string>('');
const selectprovinceId = ref<string>('');
const selectlandName = ref<string>('');
const selectlandId = ref<string>('');
const selectcountyName = ref<string>('');
const selectcountyId = ref<string>('');
const plaseChoose = ref<Option>({
    name: '请选择',
    code: '',
});

onMounted(() => {
    form.value = props.record;
    linkage();
    getcodeInfo();
});

const linkage = () => {
    getSelect({
        parentCode: '',
        parentTypeCode: '',
        typeCode: 'org_province',
    }).then((res) => {
        if (res.data) {
            res.data.unshift(plaseChoose.value);
            province.value = res.data;
        }
    });
};

const getcodeInfo = () => {
    const pars = {
        businessUuid: form.value.businessLineId,
    };
    getPostson(pars).then((res) => {
        soncode.value = res.data;
    });
};

const getInfoByVid = (arr: Option[], vid: string) => {
    return arr.find((i) => (vid ? i.code === vid : false));
};

const selectprovince = (vid: string) => {
    landarr.value = [];
    form.value.land = '';
    selectlandId.value = '';
    selectlandName.value = '';
    county.value = [];
    form.value.county_city = '';
    selectcountyId.value = '';
    selectcountyName.value = '';
    selectprovinceId.value = vid;
    selectprovinceName.value = getInfoByVid(province.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_province',
            typeCode: 'org_prefecture_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                landarr.value = res.data;
            }
        });
    }
};

const selectland = (vid: string) => {
    county.value = [];
    form.value.county_city = '';
    selectcountyId.value = '';
    selectcountyName.value = '';
    selectlandId.value = vid;
    selectlandName.value = getInfoByVid(landarr.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_prefecture_city',
            typeCode: 'org_county_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                county.value = res.data;
            }
        });
    }
};

const selectcounty = (vid: string) => {
    selectcountyId.value = vid;
    selectcountyName.value = getInfoByVid(county.value, vid)?.name || '';
    if (!vid) {
        form.value.county_city = '';
    }
};

const onSubmit = () => {
    strordId.value = selectcountyId.value || selectlandId.value || selectprovinceId.value;
    strordName.value = selectcountyName.value || selectlandName.value || selectprovinceName.value;
    if (strordId.value) {
        defaultAuditOrg({
            id: form.value.id,
            businessLineId: form.value.businessLineId,
            level: form.value.level,
            orgId: form.value.orgId,
            orgName: form.value.orgName,
            parentOrgId: form.value.parentOrgId,
            defaultAuditOrg: strordId.value,
        }).then((res) => {
            if (res.data === '失败，选择的默认审核机构未在当前条线中！') {
                message.warning(res.data);
            } else {
                message.success(res.data);
                emit('success');
                emit('close');
            }
        });
    } else {
        message.error('请选择机构名称');
    }
};

const delDefaultAuditOrgs = () => {
    modal.confirm({
        title: '温馨提示',
        content: '确定要删除默认任务审核机构吗？',
        okText: '确定',
        cancelText: '取消',
        type: 'warning',
        onOk() {
            delDefaultAuditOrg({
                id: form.value.id,
                businessLineId: form.value.businessLineId,
                level: form.value.level,
                orgId: form.value.orgId,
                orgName: form.value.orgName,
                parentOrgId: form.value.parentOrgId,
            }).then((res) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    emit('success');
                    emit('close');
                }
            });
        },
        onCancel() {
        },
    });
};

const resetForm = () => {
    emit('close');
};

const organizationValue = computed(() => {
    return `${selectprovinceName.value}${selectlandName.value}${selectcountyName.value}`;
});


// 暴露方法给父组件
defineExpose({
    onSubmit,
    delDefaultAuditOrgs
});
</script>

<template>
    <a-form :model="form" label-width="100px"  label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="省级" prop="province">
            <a-select v-model:value="form.province" placeholder="请选择" @change="selectprovince" style="width: 100%">
                <a-select-option
                        v-for="item in province"
                        :key="item.code"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="地级" prop="land">
            <a-select v-model:value="form.land" placeholder="请选择" @change="selectland" style="width: 100%">
                <a-select-option
                        v-for="item in landarr"
                        :key="item.code"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="县级市" prop="county_city">
            <a-select v-model:value="form.county_city" placeholder="请选择" @change="selectcounty" style="width: 100%">
                <a-select-option
                        v-for="item in county"
                        :key="item.code"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="当前机构">
            <a-input autocomplete="off" :value="organizationValue" disabled />
        </a-form-item>
    </a-form>
</template>

