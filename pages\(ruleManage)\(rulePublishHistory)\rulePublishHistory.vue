<!-- 规则发布历史 -->
<script setup lang="ts">
    import { getAllBusinessByLoginUser } from "@/api/rule_base";
    import { getPublishEnvir } from "@/api/pub_environment";
    import { getRule_history, musterBatchExportData } from "@/api/rule_release_history";
    import { batchExportNum } from "@/api/parameter";
    import { getPubEnv } from "@/api/baseline_Management";
    import qs from "qs";
    import RuleReleaseDetails from '@/businessComponents/ruleHistory/RuleReleaseDetails.vue';
    import { checkPermi } from "@/directive/permission/permission";

    definePageMeta({
        title: '规则发布历史'
    })

    const message = inject<any>('message')
    const modal = inject<any>('modal')
    const publishEnvir = ref([]);     // 发布环境


    // 表格列配置
    const tableColumns = [
        {
            title: '规则库名称',
            dataIndex: 'engUuid',
            key: 'engUuid',
            width: 250,
            align: 'left',
            ellipsis: true
        },
        {
            title: '基线版本',
            dataIndex: 'snapShootEdition',
            key: 'snapShootEdition',
            width: 100,
            align: 'center'
        },
        {
            title: '规则集版本',
            dataIndex: 'edition',
            key: 'edition',
            width: 100,
            align: 'center'
        },
        {
            title: '发布环境',
            dataIndex: 'descs',
            key: 'descs',
            width: 150,
            align: 'center'
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            align: 'center'
        },
        {
            title: '创建人',
            dataIndex: 'createdId',
            key: 'createdId',
            width: 100,
            align: 'center'
        },
        {
            title: '操作时间',
            dataIndex: 'rlsTimeStr',
            key: 'rlsTimeStr',
            width: 180,
            align: 'center'
        },
        {
            title: '发布异常信息',
            dataIndex: 'exceptionInfo',
            key: 'exceptionInfo',
            width: 200,
            align: 'left'
        }
    ];

    // 搜索配置
    const searchConfig = reactive({
        simpleSearchField: {
            label: '规则库名称',
            field: 'ruleBaseName'
        },
        advancedSearchFields: [
            {
                label: '规则库名称',
                field: 'ruleBaseName',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    showSearch: true,
                    filterOption: (input: string, option: any) => {
                        return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                    onChange: (value: string) => getEnvInfo(value),
                    clearFields: ['publishEnvir']
                }
            },
            {
                label: '发布环境',
                field: 'publishEnvir',
                compType: 'select',
                compConfig: {
                    options: publishEnvir.value.map(item => ({
                        name: item.environmentName,
                        value: item.id
                    }))
                }
            },
            {
                label: '发布状态',
                field: 'publishStatus',
                compType: 'select',
                compConfig: {
                    options: [
                        { name: '发布成功', value: '2' },
                        { name: '发布失败', value: '3' },
                        { name: '发布中', value: '4' }
                    ]
                }
            },
            {
                label: '开始时间',
                field: 'startTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    showTime: false,
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    showTime: false,
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            }
        ]
    });

    // 获取列表数据
    const fetchRuleHistory = async (params: Record<string, any> = {}) => {
        try {
            const res = await getRule_history({
                chineseName: params.ruleBaseName,
                businessLine: params.businessLine,
                startDate: params.startTime,
                endDate: params.endTime,
                environmentId: params.publishEnvir,
                status: params.publishStatus,
                page: params.page || 1,
                several: params.pageSize || 10,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取规则发布历史失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    // 批量导出相关
    const exportForm = reactive({
        format: '',
    });
    const isExporting = ref(false);
    const exportNum = ref(10);
    const selectedRowKeys = ref<string[]>([]);
    const dialogFormVisible = ref(false);

    const rowSelection = computed(() => {
        return {
            selectedRowKeys: selectedRowKeys.value,
            onChange: (keys) => {
                console.log(keys);
                selectedRowKeys.value = keys;
            }
        }
    });

    // 批量导出函数
    const batchExport = () => {
        exportForm.format = "";
        const sum = selectedRowKeys.value.length;
        if (sum === 0) {
            message.warning("请勾选要操作的数据！");
        } else if (sum > exportNum.value) {
            message.warning(`批量导出的数据不能超过${exportNum.value}条`);
        } else {
            dialogFormVisible.value = true;
        }
    };

    // 执行导出
    const executeExport = (format: string) => {
        isExporting.value = true;
        const data = qs.stringify({
            batchExportDatas: selectedRowKeys.value.join(","),
            pattern: format
        });

        const loadingMessage = message.loading('正在导出...', 0);
        musterBatchExportData(data).then(res => {
            dialogFormVisible.value = false;
            const headers = res.headers;
            const blob = new Blob([res.data], {
                type: headers["content-type"],
            });
            const link = document.createElement("a");
            const url = window.URL.createObjectURL(blob);
            const fileName = headers["content-disposition"]
                    .split(";")[1]
                    .split("=")[1];

            link.href = url;
            link.download = decodeURI(fileName);
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            loadingMessage();

            selectedRowKeys.value = [];
            message.success('导出成功');
            isExporting.value = false;
            listLayout.value?.refresh();
        }).catch(err => {
            console.error(err);
            loadingMessage();
            message.error('导出失败');
            isExporting.value = false;
        });
    };

    // 事件配置
    const eventConfig = {
        searchEvent: () => {},
        batchEvent: [
            {
                key: 'batchExport',
                label: '批量导出',
                handler: batchExport,
                disabled: isExporting.value
            }
        ],
        formHandler: {
            dateFields: {
                startField: 'startTime',
                endField: 'endTime',
            },
            queryMethod: fetchRuleHistory
        }
    };

    // 详情抽屉相关
    const detailsDrawerVisible = ref(false);
    const currentName = ref('');
    const currentDetailUid = ref('');

    // 详情处理
    const btn_details = (record: any) => {
        if (record.exceptionInfo === "crossEnviFlag") {
            message.warning("导入的规则无法查看详情");
        } else {
            currentDetailUid.value = record.uuid;
            currentName.value = record.engUuid;
            detailsDrawerVisible.value = true;
        }
    };

    const handleDetailsDrawerClose = () => {
        detailsDrawerVisible.value = false;
        currentName.value = '';
        currentDetailUid.value = '';
    };

    // 异常信息弹窗相关
    const exceptionModalVisible = ref(false);
    const isFullscreen = ref(false);
    const currentException = ref('');

    // 显示异常信息弹窗
    const showExceptionModal = (exception: string) => {
        currentException.value = exception;
        exceptionModalVisible.value = true;
    };

    // 关闭异常信息弹窗
    const closeExceptionModal = () => {
        exceptionModalVisible.value = false;
    };

    // 切换全屏状态
    const toggleFullscreen = () => {
        isFullscreen.value = !isFullscreen.value;
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: any) => {
        return [
            {
                key: 'details',
                label: '详情',
                disabled: record.exceptionInfo === "crossEnviFlag",
                onClick: () => btn_details(record)
            }
        ];
    };

    // 获取环境信息
    const getEnvInfo = async (code) => {
        const pars = {
            businessLine: code,
        };
        try {
            const res = await getPubEnv(pars);
            publishEnvir.value = res.data;
            console.log(res.data);
            // 更新搜索配置中的发布环境选项
            const publishEnvirField = searchConfig.advancedSearchFields.find(field => field.field === 'publishEnvir');
            if (publishEnvirField && publishEnvirField.compConfig) {
                // 先清空数组
                publishEnvirField.compConfig.options = [];
                if(publishEnvir.value){
                    // 使用新数组替换原有options，保证响应式更新
                    const newOptions = publishEnvir.value.map(item => ({
                        name: item.environmentName,
                        value: item.id
                    }));
                    publishEnvirField.compConfig.options = [...newOptions];
                }
            }
        } catch (err) {
            console.log(err);
        }
    };

    // 组件引用
    const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

    // 初始化
    onMounted(() => {
        // 获取业务条线
        getAllBusinessByLoginUser().then((res) => {
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = res.data.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });

        // 获取发布环境
        getPublishEnvir().then((res) => {
            const publishEnvirField = searchConfig.advancedSearchFields.find(field => field.field === 'publishEnvir');
            if (publishEnvirField && publishEnvirField.compConfig) {
                publishEnvirField.compConfig.options = res.data.map(item => ({
                    name: item.environmentName,
                    value: item.id
                }));
            }
        });

        // 获取批量导出数量
        batchExportNum().then((res) => {
            exportNum.value = res.data;
        });
    });

    const customRenderColumns = ['engUuid', 'status', 'exceptionInfo'];

    // 异常信息按钮悬停状态
    const hoverState = ref({});

    // 设置悬停状态
    const setHoverState = (uuid, isHover) => {
        hoverState.value = {
            ...hoverState.value,
            [uuid]: isHover
        };
    };

    const checkRadio = (val) => {
        if (isExporting.value) return; // 如果正在导出，则不执行

        exportForm.format = val.target.value;
        // 添加确认对话框
        modal.confirm({
            title: () => "提示",
            content: () => `您确定要导出选中的${selectedRowKeys.value.length}条数据吗?`,
            okText: () => '确定',
            okType: 'primary',
            cancelText: () => '取消',
            onOk() {
                // 确认后执行导出
                executeExport(val.target.value);
            },
            onCancel() {
                // 取消导出，重置选中的格式
                exportForm.format = "";
            },
        });
    }

    const handleExportCancel = () => {
        dialogFormVisible.value = false;
    }

    // 处理异常信息文本（用于显示）
    const formatExceptionText = (text: string) => {
        if (!text) return '';
        return text
            .replace(/\\n/g, '<br>')
            .replace(/\\r/g, '')
            .replace(/\\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
    };

    // 处理异常信息文本（用于复制）
    const formatExceptionTextForCopy = (text: string) => {
        if (!text) return '';
        return text
            .replace(/\\n/g, '\n')
            .replace(/\\r/g, '')
            .replace(/\\t/g, '\t');
    };

</script>

<template>
    <ListPage
            ref="listLayout"
            title="规则发布历史"
            :searchConfig="searchConfig"
            :eventConfig="eventConfig"
            :tableColumns="tableColumns"
            :batchActions="eventConfig.batchEvent"
            :queryMethod="fetchRuleHistory"
            :actionMenuGetter="getActionMenuItems"
            :customRenderColumns="customRenderColumns"
            :showAddButton="false"
            :rowSelection="rowSelection"
            :rowKey="record => record.uuid + '%' + record.snapShootEdition"
    >
        <template #engUuid="{ record }">
            <a-tooltip :title="record.engUuid">{{ record.engUuid }}</a-tooltip>
        </template>

        <template #status="{ record }">
            <a-tag :color="record.status == 2 ? 'green' : record.status == 3 ? 'red' : record.status == 9 ? 'lightgray' : 'blue'">
                {{ record.status == 2 ? "发布成功" : record.status == 3 ? "发布失败" : record.status == 9 ? "灰度发布" : "发布中" }}
            </a-tag>
        </template>

        <template #exceptionInfo="{ record }">
            <template v-if="record.exceptionInfo && record.exceptionInfo !== 'crossEnviFlag'">
                <div class="exception-info-container" @mouseenter="setHoverState(record.uuid, true)"
                     @mouseleave="setHoverState(record.uuid, false)">
                    <a-button type="text" @click="showExceptionModal(record.exceptionInfo)">
                        {{ subLongName(record.exceptionInfo, 10) }}
                    </a-button>
                    <CopyButton v-if="hoverState[record.uuid]" :content="formatExceptionTextForCopy(record.exceptionInfo)"
                                title="复制到粘贴板" successMessage="复制成功" />
                </div>
            </template>
        </template>
    </ListPage>

    <!-- 导出提示框 -->
    <a-modal v-if="dialogFormVisible" :visible="dialogFormVisible" title="提示信息" width="30%" @cancel="handleExportCancel"
             :footer="null">
        <a-form :model="exportForm">
            <div class="dialogTip">请选择需要导出的格式：</div>
            <a-form-item>
                <a-radio-group :value="exportForm.format" @change="checkRadio" :disabled="isExporting">
                    <a-radio value="Excel">导出excel</a-radio>
                    <a-radio value="Html">导出html</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </a-modal>

    <!-- 异常信息弹出框 -->
    <FullModel :isModalVisible="exceptionModalVisible" :isFullscreen="isFullscreen" :handleCancel="closeExceptionModal"
               :onFullscreenToggle="toggleFullscreen" titleText="发布异常信息详情">
        <template #default>
            <div :style="{ maxHeight: isFullscreen ? '90vh' : '60vh', overflowY: 'auto' }" class="exception-content">
                <div v-html="formatExceptionText(currentException)"></div>
            </div>
        </template>
    </FullModel>

    <!-- 详情抽屉 -->
    <FlexDrawer :visible="detailsDrawerVisible" @close="handleDetailsDrawerClose" :title="`规则集详情 - ${currentName}`"
                :width="1200" v-if="detailsDrawerVisible">
        <RuleReleaseDetails :uuid="currentDetailUid" />
    </FlexDrawer>
</template>

<style lang="scss" scoped>
    .dialogTip {
        margin-bottom: 16px;
    }

    .action-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
    }

    .exception-content {
        padding: 16px;
        max-height: 70vh;
        overflow-y: auto;

        div {
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
            font-family: monospace;
        }
    }

    .exception-info-container {
        display: flex;
        align-items: center;
    }

    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
