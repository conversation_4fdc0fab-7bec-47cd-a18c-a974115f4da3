.lake-svg-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: -.15em;
    text-align: center;
    text-transform: none;
    line-height: 1;
    text-rendering: auto;
    background-repeat: no-repeat;
    background-position: 50%
}

.lake-svg-icon-helper-tips {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/45ba264b-0967-4439-9289-ee41b040a0c2.svg)
}

.lake-svg-icon-alert {
    width: 16px;
    height: 16px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/be5b1e2a-b47e-479c-9217-430229bef84e.svg)
}

.lake-svg-icon-file {
    width: 14px;
    height: 11px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/ecda03cc-fd8e-45d2-834c-c904048f9583.svg)
}

.lake-svg-icon-image {
    width: 14px;
    height: 11px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/44718df0-a6d9-4b93-909d-8482f70476ab.svg)
}

.lake-svg-icon-error-image {
    width: 22px;
    height: 18px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/2295f9e0-1cb2-4d63-86aa-e9799273b28d.svg)
}

.lake-svg-icon-error-file {
    width: 22px;
    height: 18px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/42090c56-d17b-4cf0-be63-59e164e47ba6.svg)
}

.lake-svg-icon-insert {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*o_5bR4s5I4QAAAAAAAAAAABjAQAAAQ/original);
    width: 18px;
    height: 18px;
    margin-bottom: -1px
}

.lake-svg-icon-label {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/20747dac-1cc8-49f6-a91a-0c8c501d189b.svg)
}

.lake-svg-icon-new-card {
    width: 30px;
    height: 12px;
    margin-top: 5px;
    margin-left: 4px;
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/7e255d95-cbe3-4559-a4a3-dbe69cd3e38b.svg)
}

.lake-svg-icon-puml {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/4d2be8de-b7b7-41df-a36f-77ff0b6a5b9f.svg)
}

.lake-svg-icon-flowchart {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/c82343e1-60ab-4b62-8135-d71401191104.svg)
}

.lake-svg-icon-graphviz {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/19178ae8-0b12-463e-b466-e927094e9ea0.svg)
}

.lake-svg-icon-mermaid {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/a15d98ca-089f-4cc2-9c11-032cc56fcacb.svg)
}

.lake-svg-icon-processon {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/5c0fa950-66ff-4a3b-a788-477ea178e6bb.svg)
}

.lake-svg-icon-modao {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/04162a86-307b-409e-8f8c-71f2200891f5.svg)
}

.lake-svg-icon-herbox {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/f6d5ceab-a3e0-413f-a1ab-3c9ded96d632.svg)
}

.lake-svg-icon-deepinsight {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/69f6859c-220e-490c-8c8a-b3bdaa7cbcb3.svg)
}

.lake-svg-icon-ali {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/25837bdf-8082-430b-a8ac-4168cd84e093.svg)
}

.lake-svg-icon-codepen {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/29539843-153d-4c7b-af8f-4475e743398b.svg)
}

.lake-svg-icon-riddle {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/f0328014-fe24-4690-90a4-82c8e150c5bf.svg)
}

.lake-svg-icon-figma {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/d0b58b9f-0ac3-4d0f-958a-c0aa8a3265eb.svg)
}

.lake-svg-icon-xiami {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/c71358bb-d7cc-45dc-96ae-19d28febbb03.svg)
}

.lake-svg-icon-music163 {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/e3416ec0-a688-4160-8772-9bbecad88d28.svg)
}

.lake-svg-icon-gaode {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/02def0ac-858f-4f90-886c-295290725024.svg)
}

.lake-svg-icon-taobao {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/57a7cb99-7f36-4869-8f94-d9c87d069cd6.svg)
}

.lake-svg-icon-preview {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/95b6cdec-29f5-495f-8f1b-8835bcd2c541.svg)
}

.lake-svg-icon-youku {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/4825b4b8-96a3-463f-8e6a-c28ae8b792b0.svg)
}

.lake-svg-icon-bilibili {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/93d6bda1-2ab6-4b20-97e5-c0a73f2a42cd.svg)
}

.lake-svg-icon-mmad {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/5e8132a4-5ec0-4c4e-95c4-7f5b19f23c19.svg)
}

.lake-svg-icon-psd {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/df8dd01c-ecb1-4d33-99f6-8079591bb46c.svg)
}

.lake-svg-icon-sketch {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/ef845454-388c-47b3-8066-298e335a2741.svg)
}

.lake-svg-icon-xmind {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/46f6c8fc-5d56-41d7-b419-a89335f82dfc.svg)
}

.lake-svg-icon-rp {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/f8f40533-79d9-4a3c-8051-31c34390f96d.svg)
}

.lake-svg-icon-key {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/73dc752c-3f7b-4b0b-ada9-eb7e208d9b34.svg)
}

.lake-svg-icon-mindnode {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/3fbedc10-029c-4670-a343-8b224ab0978e.svg)
}

.lake-svg-icon-keynote {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/73dc752c-3f7b-4b0b-ada9-eb7e208d9b34.svg)
}

.lake-svg-icon-xd {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/139561c7-309c-4669-882b-1107e217179c.svg)
}

.lake-svg-icon-numbers {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/1eecfe2b-d250-4575-b3b4-78de3503007c.svg)
}

.lake-svg-icon-pages {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/60e30d6d-9ee0-47b7-8955-cdd4aff6b2ac.svg)
}

.lake-svg-icon-potx,.lake-svg-icon-ppt,.lake-svg-icon-pptx {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/d62af37c-9f13-4ca2-8861-f2b18c98f3a9.svg)
}

.lake-svg-icon-xls,.lake-svg-icon-xlsx {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/1874c8cc-a103-429b-8465-bb3adfe78d43.svg)
}

.lake-svg-icon-doc,.lake-svg-icon-docx {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/d98a673f-ef0f-46a6-83e3-ba6d01a4ec9d.svg)
}

.lake-svg-icon-pdf {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/02e0700b-d617-4afb-b4de-a4ea4c9bf7e7.svg)
}

.lake-svg-icon-eddx {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/e1c51c39-f5b6-4e14-9ba9-0102d3579b9a.svg)
}

.lake-svg-icon-eps {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/84b8024a-c65c-44e3-9be8-269af985af54.svg)
}

.lake-svg-icon-insert-local-doc {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/f54a24bd-f973-4945-978c-abb923e20090.svg)
}

.lake-svg-icon-insert-yuque {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/99e327dd-1cfe-40dd-b3e5-28c9d88d182d.svg)
}

.lake-svg-icon-insert-table {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/a9cf6f64-3741-493b-a112-8952baedd1f6.svg)
}

.lake-svg-icon-insert-image {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/dcf2220d-9226-41c9-b1f6-cc0c0a110dc1.svg)
}

.lake-svg-icon-insert-file {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/2c7fe359-ac3d-493e-998d-f285305d28bd.svg)
}

.lake-svg-icon-insert-video {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/1ef56c12-3adc-42c6-bb7c-9cb13111d8e2.svg)
}

.lake-svg-icon-insert-thirdparty {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/e3ebe9af-b207-4d94-8dc4-2fdf5469f5e7.svg)
}

.lake-svg-icon-insert-youku {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/4825b4b8-96a3-463f-8e6a-c28ae8b792b0.svg)
}

.lake-svg-icon-insert-codeblock {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/7fd18334-db2e-49d0-ba80-b92689b81b3f.svg)
}

.lake-svg-icon-insert-math {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/762f00b4-45d7-47b8-8c7b-2c9f886612b5.svg)
}

.lake-svg-icon-insert-mindmap {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/f6510a5f-33d9-4bcb-8b54-5dc30bd89e79.svg)
}

.lake-svg-icon-insert-puml {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/10a9cc50-3a01-4a5c-8335-9a194fdcb614.svg)
}

.lake-svg-icon-insert-flowchart {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/0e0309c1-2b5c-433c-b9d7-e0d2486ce885.svg)
}

.lake-svg-icon-insert-graphviz {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/44e449b2-9756-4de6-b227-bf4205964d20.svg)
}

.lake-svg-icon-insert-mermaid {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/59cb287f-06f2-4712-a206-e1c4d173830b.svg)
}

.lake-svg-icon-insert-lockedtext {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/bc8adb5a-18d2-4385-8ca4-710b57ad1664.svg)
}

.lake-svg-icon-insert-calendar {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/2dd842ba-ea59-4cd1-994e-c5f31bd1ead2.svg)
}

.lake-svg-icon-check-success {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/4c17d1ec-bd3f-4b86-a77b-dfd91e9c9626.svg)
}

.lake-svg-icon-calendar-arrow-left {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/2f3e5fca-16d2-48b3-a5d2-fff2328df92c.svg)
}

.lake-svg-icon-calendar-arrow-right {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/a03bff1a-3aef-4871-bc74-de2a3f81e3ff.svg)
}

.lake-svg-icon-desc {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/a085f457-6982-434a-808d-372cac6624e7.svg)
}

.lake-svg-icon-error {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/8f8fcc17-b080-421e-b733-7772d62be204.svg)
}

.lake-svg-icon-warning {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/d1b4c5f6-8dcd-4d10-ab31-265abdbb9579.svg)
}

.lake-svg-insert-yuan {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/b1c64e6e-3787-408c-bc49-dd0f9d9b2ebc.svg)
}

.lake-svg-icon-yuan {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/6145eac0-5d9d-4b9b-84be-7ed8bfde32c7.svg)
}

.lake-svg-icon-line-height {
    background-image: url(https://gw.alipayobjects.com/zos/basement_prod/40d4fb04-d3c2-43e8-a99d-7f68345f7f0a.svg)
}

.lake-svg-icon-yuque-doc {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/11d695b4-30c1-478f-940a-e20095ccb2fe.svg)
}

.lake-svg-icon-yuque-thread {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/1f4ca629-5760-465a-9207-7a7f49d01ca6.svg)
}

.lake-svg-icon-yuque-design {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/f0eded1a-a849-44f6-a5e2-3d8f5724ebef.svg)
}

.lake-svg-icon-yuque-sheet {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/34c09a2c-3c80-44c8-a49a-20fa92774b34.svg)
}

.lake-svg-icon-yuque-table {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/0d0a1799-c870-4f66-8164-3b6a788e0f89.svg)
}

.lake-svg-icon-yuque-board {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/d62043aa-bdc3-4d0c-b68c-a771108516a3.svg)
}

.lake-svg-icon-yuque-mind {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/b74e7bab-1935-48ae-80f8-82ea648697e5.svg)
}

.lake-svg-icon-yuque-show {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/de70d404-fec2-4606-aa45-eb4d6560a1cc.svg)
}

.lake-svg-icon-yuque-repo-mind {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/b74e7bab-1935-48ae-80f8-82ea648697e5.svg)
}

.lake-svg-icon-yuque-repo-sheet {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/3be374af-05cc-4115-a9ac-477131b54212.svg)
}

.lake-svg-icon-yuque-repo-doc {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/2a1ba540-5d0d-46a2-98fb-6753300d3749.svg)
}

.lake-svg-icon-yuque-repo-thread {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/3f6aa5c3-e7b1-4d78-932e-b00379e8d38c.svg)
}

.lake-svg-icon-yuque-repo-design {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/48e3d64b-aec0-4f3f-9838-d1f685781dfd.svg)
}

.lake-svg-icon-search {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/086f31be-d7f0-4675-aa2c-8c8cf21c088b.svg)
}

.lake-svg-icon-plus {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/fa994e91-3fa4-421d-b0fc-9823dc5cf330.svg)
}

.lake-svg-icon-video {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/31499aa4-1105-4a4b-a8c7-f33ceaf8e012.svg)
}

.lake-svg-icon-canva {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/c1678a81-2534-4fdd-b38b-a510d69a6661.svg)
}

.lake-svg-icon-yuque-for-embed {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/5dfd3248-3206-41dc-bc2c-1d7fb9847ecb.svg)
}

.lake-svg-icon-file-for-embed {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/6ac1386e-5c04-45a5-a351-7d94a61d7bba.svg)
}

.lake-svg-icon-jinshuju {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/3f11de9a-3da8-402d-a8bc-81df6ec52d2c.svg)
}

.lake-svg-icon-vote {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/929ed4d4-a244-4d78-96e2-f472642daf2a.svg)
}

.lake-svg-icon-cloud {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/58c42f2b-cdf1-49c3-a77a-d5e63c561a36.svg)
}

.lake-svg-icon-folder {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/9890c046-6d10-4d2f-917a-b40a8e92ea7f.svg)
}

.lake-svg-icon-audio {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/4aa43d0f-2881-46ab-a894-afd617b08afc.svg)
}

.lake-svg-icon-audio-icon {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/b9d51288-551f-447e-930f-7e1132a4a90b.svg)
}

.lake-svg-icon-audio-error-icon {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/dc01c10b-e433-4bb3-bf7e-8b679725b823.svg)
}

.lake-svg-icon-regret {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/9436ba3b-352d-4dd2-a97b-b3c525390508.svg)
}

.lake-svg-icon-note-file {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/a8cab1ab-68dd-48be-82ab-c667e0c43556.svg)
}

.lake-svg-icon-note-image {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/6efcc064-f6e7-4823-983e-a09d449fcf55.svg)
}

.lake-svg-icon-note-tasklist {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/32d03522-996b-4c48-bd08-274a2ed15204.svg)
}

.lake-svg-icon-note-link {
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/a232cebb-7862-420f-a220-31eff086e997.svg)
}

.lake-svg-icon-emoji {
    width: 16px;
    height: 16px;
    margin-bottom: 1px;
    background-size: contain;
    background-image: url(https://gw.alipayobjects.com/zos/bmw-prod/72fc758e-01ca-4317-b9c9-1d25e9b3a577.svg)
}

@font-face {
    font-family: lake-icon;
    src: url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.eot);
    src: url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.eot#iefix) format("embedded-opentype"),url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.woff2) format("woff2"),url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.woff) format("woff"),url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.ttf) format("truetype"),url(https://at.alicdn.com/t/a/font_943843_4hllx7w8s1k.svg#iconfont) format("svg")
}

@font-face {
    font-family: Tahoma For Number;
    src: local("Tahoma");
    unicode-range: u+30-39
}

.lake-icon {
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    line-height: 1;
    text-rendering: auto
}

.lake-icon:before {
    display: block;
    font-family: lake-icon!important
}

.lake-icon-svgs {
    width: 16px;
    height: 16px
}

.lake-icon-alert:before {
    content: "\e724"
}

.lake-icon-h1:before {
    content: "\e610"
}

.lake-icon-h2:before {
    content: "\e611"
}

.lake-icon-h3:before {
    content: "\e60f"
}

.lake-icon-h4:before {
    content: "\e60c"
}

.lake-icon-h5:before {
    content: "\e60d"
}

.lake-icon-h6:before {
    content: "\e60e"
}

.lake-icon-bold:before {
    content: "\ea46"
}

.lake-icon-underline:before {
    content: "\ea45"
}

.lake-icon-code:before {
    content: "\ea49"
}

.lake-icon-italic:before {
    content: "\ea44"
}

.lake-icon-strikethrough:before {
    content: "\ea47"
}

.lake-icon-sub:before {
    content: "\ea4b"
}

.lake-icon-sup:before {
    content: "\ea4a"
}

.lake-icon-color:before {
    content: "\ea4d"
}

.lake-icon-background:before {
    content: "\ea4c"
}

.lake-icon-moremark:before {
    content: "\ea48"
}

.lake-icon-toolbar-check:before {
    content: "\e6d0"
}

.lake-icon-link:before {
    content: "\ea58"
}

.lake-icon-emoji:before {
    content: "\ea79"
}

.lake-icon-oList:before {
    content: "\ea53"
}

.lake-icon-uList:before {
    content: "\ea52"
}

.lake-icon-tList:before {
    content: "\ea56"
}

.lake-icon-quote:before {
    content: "\ea57"
}

.lake-icon-hr:before {
    content: "\ea59"
}

.lake-icon-left:before {
    content: "\ea4e"
}

.lake-icon-center:before {
    content: "\ea4f"
}

.lake-icon-right:before {
    content: "\ea50"
}

.lake-icon-justify:before {
    content: "\ea51"
}

.lake-icon-top:before {
    content: "\ea82"
}

.lake-icon-bottom:before {
    content: "\ea81"
}

.lake-icon-middle:before {
    content: "\ea80"
}

.lake-icon-horizontally:before {
    content: "\e66c"
}

.lake-icon-vertically:before {
    content: "\e66d"
}

.lake-icon-image:before {
    content: "\ea78"
}

.lake-icon-image-o:before {
    content: "\e61c"
}

.lake-icon-rotate-left:before {
    content: "\ea71"
}

.lake-icon-rotate-right:before {
    content: "\ea70"
}

.lake-icon-preferences:before {
    content: "\e810"
}

.lake-icon-indent-left:before {
    content: "\ea54"
}

.lake-icon-indent-right:before {
    content: "\ea55"
}

.lake-icon-arrow-up:before {
    content: "\ea1b"
}

.lake-icon-arrow-down:before {
    content: "\ea1a"
}

.lake-icon-arrow-left:before {
    content: "\ea1f"
}

.lake-icon-arrow-right:before {
    content: "\ea1e"
}

.lake-icon-insert-root:before {
    content: "\e61b"
}

.lake-icon-insert-sibling:before {
    content: "\ea72"
}

.lake-icon-insert-child:before {
    content: "\ea73"
}

.lake-icon-expand-subtree:before {
    content: "\ea74"
}

.lake-icon-collapse-subtree:before {
    content: "\ea75"
}

.lake-icon-properties:before {
    content: "\e618"
}

.lake-icon-collapse-node:before {
    content: "\e61d"
}

.lake-icon-line-curve:before {
    content: "\e636"
}

.lake-icon-line-curve-arrow:before {
    content: "\e635"
}

.lake-icon-diagram-cell-note:before {
    content: "\e638"
}

.lake-icon-diagram-toolbar-note:before {
    content: "\e637"
}

.lake-icon-mind-tree-edge-none:before {
    content: "\e69b"
}

.lake-icon-mind-tree-edge-polyline:before {
    content: "\e69d"
}

.lake-icon-insert-shape:before {
    content: "\e629"
}

.lake-icon-insert-text:before {
    content: "\e627"
}

.lake-icon-straight-line:before {
    content: "\e626"
}

.lake-icon-straight-line-arrow:before {
    content: "\e624"
}

.lake-icon-elbow-line:before {
    content: "\e625"
}

.lake-icon-elbow-line-arrow:before {
    content: "\e628"
}

.lake-icon-line-arrow-left:before {
    content: "\e62a"
}

.lake-icon-line-arrow-right:before {
    content: "\e62b"
}

.lake-icon-align-bottom:before {
    content: "\e6ab"
}

.lake-icon-align-right:before {
    content: "\e6b1"
}

.lake-icon-align-h-center:before {
    content: "\e6b3"
}

.lake-icon-align-left:before {
    content: "\e6b2"
}

.lake-icon-align-top:before {
    content: "\e6aa"
}

.lake-icon-align-v-center:before {
    content: "\e6ae"
}

.lake-icon-distribute-horizontally:before {
    content: "\e6f1"
}

.lake-icon-distribute-vertically:before {
    content: "\e6f0"
}

.lake-icon-to-back:before {
    content: "\e65f"
}

.lake-icon-forward:before {
    content: "\e660"
}

.lake-icon-to-front:before {
    content: "\e661"
}

.lake-icon-backward:before {
    content: "\e662"
}

.lake-icon-delete-lane:before {
    content: "\e6bf"
}

.lake-icon-zoom-to-fit:before {
    content: "\e655"
}

.lake-icon-table-merge-cell:before {
    content: "\e6e1"
}

.lake-icon-table-split-cell:before {
    content: "\ea6b"
}

.lake-icon-table-clear:before {
    content: "\ea64"
}

.lake-icon-table-remove-col:before {
    content: "\ea67"
}

.lake-icon-table-remove-row:before {
    content: "\ea66"
}

.lake-icon-table-cut:before {
    content: "\ea65"
}

.lake-icon-table-copy:before {
    content: "\ea61"
}

.lake-icon-table-insert-col-left:before {
    content: "\ea60"
}

.lake-icon-table-insert-col-right:before {
    content: "\ea5f"
}

.lake-icon-table-insert-row-up:before {
    content: "\ea5d"
}

.lake-icon-table-insert-row-down:before {
    content: "\ea5e"
}

.lake-icon-table-remove-table:before {
    content: "\ea63"
}

.lake-icon-table-border:before {
    content: "\e633"
}

.lake-icon-table-delete-file:before {
    content: "\e711"
}

.lake-icon-table-download-file:before {
    content: "\e712"
}

.lake-icon-redo:before {
    content: "\e6e2"
}

.lake-icon-undo:before {
    content: "\e6dc"
}

.lake-icon-clean:before {
    content: "\e6d6"
}

.lake-icon-paintformat:before {
    content: "\ea42"
}

.lake-icon-close:before {
    content: "\e72d"
}

.lake-icon-save:before {
    content: "\e68c"
}

.lake-icon-edit:before {
    content: "\ea6c"
}

.lake-icon-edit-new:before {
    content: "\e63f"
}

.lake-icon-copy:before,.lake-icon-copyContent:before {
    content: "\ea61"
}

.lake-icon-delete:before {
    content: "\ea63"
}

.lake-icon-openlink:before {
    content: "\ea76"
}

.lake-icon-drag:before {
    content: "\ea68"
}

.lake-icon-table-column-hidden:before {
    content: "\e6f5"
}

.lake-icon-table-column-show:before {
    content: "\e6f3"
}

.lake-icon-table-column-config:before {
    content: "\e6f4"
}

.lake-icon-table-x-delete:before {
    content: "\e71b"
}

.lake-icon-table-arrow-down:before {
    content: "\e6ef"
}

.lake-icon-board-iconfont:before {
    content: "\e731"
}

.lake-icon-board-viewport-autofit:before {
    content: "\e723"
}

.lake-icon-board-viewport-option-adapt:before {
    content: "\e710"
}

.lake-icon-board-viewport-option-wysiwyg:before {
    content: "\e70f"
}

.lake-icon-board-properties:before,.lake-icon-board-setting:before {
    content: "\e690"
}

.lake-icon-board-undo:before {
    content: "\e689"
}

.lake-icon-board-redo:before {
    content: "\e68b"
}

.lake-icon-board-save:before {
    content: "\e68c"
}

.lake-icon-board-zoom-out:before {
    content: "\e647"
}

.lake-icon-board-zoom-in:before {
    content: "\e646"
}

.lake-icon-board-hand:before {
    content: "\e648"
}

.lake-icon-board-insert-text:before {
    content: "\e685"
}

.lake-icon-board-mind:before {
    content: "\e67d"
}

.lake-icon-board-insert-shape:before {
    content: "\e64c"
}

.lake-icon-board-upload-image:before {
    content: "\e67c"
}

.lake-icon-board-straight-line-arrow:before {
    content: "\e64a"
}

.lake-icon-board-elbow-line-arrow:before {
    content: "\e649"
}

.lake-icon-board-curve-line-arrow:before {
    content: "\e67a"
}

.lake-icon-board-toolbar-hide:before {
    content: "\e6f6"
}

.lake-icon-board-toolbar-show:before {
    content: "\e6f7"
}

.lake-icon-board-toolbar-right:before {
    content: "\e713"
}

.lake-icon-board-toolbar-left:before {
    content: "\e714"
}

.lake-icon-board-toolbar-free-hand:before {
    content: "\e72c"
}

.lake-icon-board-toolbar-pencil:before {
    content: "\e72a"
}

.lake-icon-board-toolbar-pen:before {
    content: "\e732"
}

.lake-icon-board-toolbar-mark-pencil:before {
    content: "\e72b"
}

.lake-icon-board-toolbar-eraser:before {
    content: "\e72e"
}

.lake-icon-board-toolbar-select:before {
    content: "\e730"
}

.lake-icon-board-toolbar-theme:before {
    content: "\e722"
}

.lake-icon-board-question:before {
    content: "\e734"
}

.lake-icon-contextual-align:before {
    content: "\e6ee"
}

.lake-icon-contextual-group:before {
    content: "\e6eb"
}

.lake-icon-contextual-ungroup:before {
    content: "\e6ec"
}

.lake-icon-contextual-text:before {
    content: "\e691"
}

.lake-icon-contextual-shape:before {
    content: "\e69e"
}

.lake-icon-contextual-link:before {
    content: "\e68e"
}

.lake-icon-contextual-stroke-none:before {
    content: "\e696"
}

.lake-icon-contextual-stroke-dash:before {
    content: "\e698"
}

.lake-icon-contextual-curve-line:before {
    content: "\e697"
}

.lake-icon-contextual-straight-line:before {
    content: "\e694"
}

.lake-icon-contextual-elbow-line:before {
    content: "\e692"
}

.lake-icon-contextual-left-arrow:before {
    content: "\e695"
}

.lake-icon-contextual-right-arrow:before {
    content: "\e693"
}

.lake-icon-contextual-line-swap-endpoint:before {
    content: "\e721"
}

.lake-icon-contextual-abstract:before {
    content: "\e71f"
}

.lake-icon-contextual-mindmap-icon:before {
    content: "\e72f"
}

.lake-icon-contextual-dsl-edit:before {
    content: "\e68f"
}

.lake-icon-contextual-none:before {
    content: "\e6f8"
}

.lake-icon-contextual-open-triangle:before {
    content: "\e6f9"
}

.lake-icon-contextual-one-side-up:before {
    content: "\e6fa"
}

.lake-icon-contextual-one-side-down:before {
    content: "\e6fb"
}

.lake-icon-contextual-arrow:before {
    content: "\e6fc"
}

.lake-icon-contextual-hollow-triangle:before {
    content: "\e704"
}

.lake-icon-contextual-sharp-solid-square:before {
    content: "\e703"
}

.lake-icon-contextual-sharp-hollow-square:before {
    content: "\e705"
}

.lake-icon-contextual-flat-solid-square:before {
    content: "\e6fe"
}

.lake-icon-contextual-flat-hollow-square:before {
    content: "\e6ff"
}

.lake-icon-contextual-solid-circle:before {
    content: "\e6fd"
}

.lake-icon-contextual-hollow-circle:before {
    content: "\e702"
}

.lake-icon-contextual-cross-circle:before {
    content: "\e701"
}

.lake-icon-contextual-half-open-circle:before {
    content: "\e700"
}

.lake-icon-contextual-end-vertical-line:before {
    content: "\e706"
}

.lake-icon-contextual-middle-vertical-line:before {
    content: "\e708"
}

.lake-icon-contextual-three-line:before {
    content: "\e707"
}

.lake-icon-contextual-three-line-plus-vertical:before {
    content: "\e70a"
}

.lake-icon-contextual-double-vertical-line:before {
    content: "\e709"
}

.lake-icon-contextual-single-slash:before {
    content: "\e70c"
}

.lake-icon-contextual-three-line-plus-circle:before {
    content: "\e70b"
}

.lake-icon-contextual-circle-vertical:before {
    content: "\e70d"
}

.lake-icon-mindmap-capsule:before {
    content: "\e699"
}

.lake-icon-mindmap-line:before {
    content: "\e696"
}

.lake-icon-mindmap-rect:before {
    content: "\e69e"
}

.lake-icon-zoom-out:before {
    content: "\ea7e"
}

.lake-icon-zoom-in:before {
    content: "\ea7f"
}

.lake-icon-attachment:before {
    content: "\ea7a"
}

.lake-icon-download:before {
    content: "\ea6e"
}

.lake-icon-upload:before {
    content: "\ea7d"
}

.lake-icon-preview:before {
    content: "\ea6d"
}

.lake-icon-full-screen:before,.lake-icon-maximize:before {
    content: "\ea7c"
}

.lake-icon-exit-full-screen:before {
    content: "\ea8b"
}

.lake-icon-full-screen-mini:before {
    content: "\e6c9"
}

.lake-icon-exit-full-screen-mini:before {
    content: "\e6ca"
}

.lake-icon-toc:before {
    content: "\ea5c"
}

.lake-icon-question:before {
    content: "\e81d"
}

.lake-icon-more:before {
    content: "\e60b"
}

.lake-icon-lock:before {
    content: "\e86a"
}

.lake-icon-unlock:before {
    content: "\e86b"
}

.lake-icon-embedded-preview:before {
    content: "\e686"
}

.lake-icon-compact-display:before {
    content: "\e684"
}

.lake-icon-loading:before {
    content: "\e64d"
}

.lake-icon-confirm:before {
    content: "\e729"
}

.lake-icon-warning:before {
    content: "\e7df"
}

.lake-icon-plus:before {
    content: "\e612"
}

.lake-icon-unlink:before {
    content: "\e613"
}

.lake-icon-calendar-arrow-right:before {
    content: "\e617"
}

.lake-icon-calendar-arrow-left:before {
    content: "\e619"
}

.lake-icon-yuan:before {
    content: "\e68a"
}

.lake-icon-line-height:before {
    content: "\e61a"
}

.lake-icon-doc-title:before {
    content: "\e620"
}

.lake-icon-doc-card:before {
    content: "\e621"
}

.lake-icon-doc-embed:before {
    content: "\e61f"
}

.lake-icon-searchreplace:before {
    content: "\ea5b"
}

.lake-icon-translate:before {
    content: "\ea5a"
}

.lake-icon-reload:before {
    content: "\ea6f"
}

.lake-icon-gotolink:before {
    content: "\ea76"
}

.lake-icon-height-mode-default:before {
    content: "\e622"
}

.lake-icon-height-mode-adaptive:before {
    content: "\e623"
}

.lake-icon-dropdown-arrow:before {
    content: "\e616"
}

.lake-icon-save-folder:before {
    content: "\e62c"
}

.lake-icon-desc:before {
    content: "\e6e7"
}

.lake-icon-asc:before {
    content: "\e6e4"
}

.lake-icon-formula:before {
    content: "\e6de"
}

.lake-icon-filter:before {
    content: "\e6db"
}

.lake-icon-clip:before {
    content: "\e604"
}

.lake-icon-overflow:before {
    content: "\e603"
}

.lake-icon-wrap:before {
    content: "\e602"
}

.lake-icon-select:before {
    content: "\e609"
}

.lake-icon-plus-o:before {
    content: "\e837"
}

.lake-icon-success:before {
    content: "\e60a"
}

.lake-icon-decimal-inc:before {
    content: "\e6e3"
}

.lake-icon-decimal-dec:before {
    content: "\e6e0"
}

.lake-icon-frozen-cell:before {
    content: "\e6da"
}

.lake-icon-frozen-col:before {
    content: "\ea88"
}

.lake-icon-frozen-row:before {
    content: "\ea87"
}

.lake-icon-cancel-frozen:before {
    content: "\e656"
}

.lake-icon-border-all:before {
    content: "\e6d9"
}

.lake-icon-border-style:before {
    content: "\e64b"
}

.lake-icon-protect:before {
    content: "\e6d5"
}

.lake-icon-chart:before {
    content: "\e6df"
}

.lake-icon-chart-menu:before {
    content: "\e64e"
}

.lake-icon-range:before {
    content: "\e64f"
}

.lake-icon-dropdown-list:before {
    content: "\e650"
}

.lake-icon-draggable:before {
    content: "\e651"
}

.lake-icon-keyboard:before {
    content: "\ea86"
}

.lake-icon-line-break:before {
    content: "\e652"
}

.lake-icon-sheet-list:before {
    content: "\e653"
}

.lake-icon-invisible:before {
    content: "\e654"
}

.lake-icon-selections:before {
    content: "\e61e"
}

.lake-icon-conditionformat:before {
    content: "\e6dd"
}

.lake-icon-up-arrow:before {
    content: "\e664"
}

.lake-icon-down-arrow:before {
    content: "\e663"
}

.lake-icon-left-arrow:before {
    content: "\e666"
}

.lake-icon-right-arrow:before {
    content: "\e665"
}

.lake-icon-col-width:before {
    content: "\e667"
}

.lake-icon-row-height:before {
    content: "\e668"
}

.lake-icon-note:before {
    content: "\e669"
}

.lake-icon-highlight-area:before {
    content: "\e66a"
}

.lake-icon-sheet-arrow:before {
    content: "\e66b"
}

.lake-icon-filtered:before {
    content: "\e66e"
}

.lake-icon-search-len:before {
    content: "\e719"
}

.lake-icon-add-filter:before {
    content: "\e71a"
}

.lake-icon-col-input:before {
    content: "\e67e"
}

.lake-icon-col-textarea:before {
    content: "\e673"
}

.lake-icon-col-number:before {
    content: "\e6ea"
}

.lake-icon-col-select:before {
    content: "\e67f"
}

.lake-icon-col-multiselect:before {
    content: "\e683"
}

.lake-icon-col-date:before {
    content: "\e682"
}

.lake-icon-col-createdat:before {
    content: "\e67b"
}

.lake-icon-col-updatedat:before {
    content: "\e717"
}

.lake-icon-col-modifierid:before {
    content: "\e716"
}

.lake-icon-col-userid:before {
    content: "\e715"
}

.lake-icon-col-checkbox:before {
    content: "\e681"
}

.lake-icon-col-image:before {
    content: "\e643"
}

.lake-icon-col-mention:before {
    content: "\e644"
}

.lake-icon-col-file:before {
    content: "\e642"
}

.lake-icon-col-link:before {
    content: "\e641"
}

.lake-icon-col-email:before {
    content: "\e645"
}

.lake-icon-col-phone:before {
    content: "\e640"
}

.lake-icon-sheet:before {
    content: "\e678"
}

.lake-icon-grid-filter:before {
    content: "\e672"
}

.lake-icon-table:before {
    content: "\e679"
}

.lake-icon-circle-remove:before {
    content: "\e834"
}

.lake-icon-grid-sort:before {
    content: "\e671"
}

.lake-icon-grid-form:before {
    content: "\e670"
}

.lake-icon-update-form:before {
    content: "\e674"
}

.lake-icon-move-up:before {
    content: "\e676"
}

.lake-icon-move-down:before {
    content: "\e675"
}

.lake-icon-repository:before {
    content: "\e63e"
}

.lake-icon-no-border:before {
    content: "\e63d"
}

.lake-icon-group:before {
    content: "\e677"
}

.lake-icon-to-right:before {
    content: "\e693"
}

.lake-icon-to-left:before {
    content: "\e695"
}

.lake-icon-card-toolbar-full-screen:before {
    content: "\e6e6"
}

.lake-icon-row-height-check:before {
    content: "\e6d0"
}

.lake-icon-thumb-up:before {
    content: "\e6b5"
}

.lake-icon-manage-catalog:before {
    content: "\e6b6"
}

.lake-icon-open-book:before {
    content: "\e6b7"
}

.lake-icon-catalog-switcher:before {
    content: "\e6b8"
}

.lake-icon-author:before {
    content: "\e6ad"
}

.lake-icon-timer:before {
    content: "\e6af"
}

.lake-icon-more-actions:before {
    content: "\e6ac"
}

.lake-icon-star:before {
    content: "\e6b0"
}

.lake-icon-star-outlined:before {
    content: "\e6b1"
}

.lake-icon-rowheight-low:before {
    content: "\e6ba"
}

.lake-icon-rowheight-middle:before {
    content: "\e6bc"
}

.lake-icon-rowheight-high:before {
    content: "\e6bd"
}

.lake-icon-stop:before {
    content: "\e6c7"
}

.lake-icon-view:before {
    content: "\e6f3"
}

.lake-icon-arrow-down-mini:before {
    content: "\e718"
}

.lake-icon-table-arrow-down-view:before {
    content: "\e71c"
}

.lake-icon-table-expand:before {
    content: "\e6b9"
}

.lake-icon-table-collpse:before {
    content: "\e6bb"
}

.lake-icon-table-up:before {
    content: "\e6c3"
}

.lake-icon-table-down:before {
    content: "\e6c2"
}

.lake-icon-table-lock:before {
    content: "\e6c0"
}

.lake-icon-table-delete:before {
    content: "\e6bf"
}

.lake-icon-table-close:before {
    content: "\e71e"
}

.lake-icon-table-new-delete:before {
    content: "\e71d"
}

.lake-icon-table-grid-view:before {
    content: "\e6d3"
}

.lake-icon-table-card-view:before {
    content: "\e6d4"
}

.lake-icon-table-kanban-view:before {
    content: "\e6d1"
}

.lake-icon-table-plus:before {
    content: "\e646"
}

.lake-icon-table-dropdown:before {
    content: "\e6c4"
}

.lake-icon-table-dot:before {
    content: "\e64e"
}

.lake-icon-table-arrow-right:before {
    content: "\e6c5"
}

.lake-icon-table-drag:before {
    content: "\e6c8"
}

.lake-icon-table-setting:before {
    content: "\e6e8"
}

.lake-icon-table-edit:before {
    content: "\e68f"
}

.lake-icon-material-library:before {
    content: "\e6c6"
}

.lake-icon-pin-tilted:before {
    content: "\e6cb"
}

.lake-icon-pin:before {
    content: "\e6d2"
}

.lake-icon-width-mode-unfold:before {
    content: "\e6cd"
}

.lake-icon-width-mode-fold:before {
    content: "\e6cc"
}

.lake-icon-card-check-in:before {
    content: "\e6e9"
}

.lake-icon-export:before {
    content: "\e6ed"
}

.lake-icon-arrow-right:before {
    content: "\e6f2"
}

.lake-icon-arrow-down:before {
    content: "\e6ef"
}

.lake-icon-table-paste:before {
    content: "\e720"
}

:root {
    --yq-ant-primary-color: var(--yq-theme);
    --yq-ant-primary-color-hover: var(--yq-yuque-green-700);
    --yq-ant-primary-color-active: var(--yq-yuque-green-700);
    --yq-ant-primary-color-outline: rgba(0,185,107,0.2);
    --yq-ant-primary-1: var(--yq-yuque-green-100);
    --yq-ant-primary-2: var(--yq-yuque-green-200);
    --yq-ant-primary-3: var(--yq-yuque-green-300);
    --yq-ant-primary-4: var(--yq-yuque-green-400);
    --yq-ant-primary-5: var(--yq-yuque-green-500);
    --yq-ant-primary-6: var(--yq-theme);
    --yq-ant-primary-7: var(--yq-yuque-green-700);
    --yq-ant-primary-color-deprecated-l-35: var(--yq-theme);
    --yq-ant-primary-color-deprecated-l-20: var(--yq-theme);
    --yq-ant-primary-color-deprecated-t-20: var(--yq-theme);
    --yq-ant-primary-color-deprecated-t-50: var(--yq-theme);
    --yq-ant-primary-color-deprecated-f-12: rgba(0,185,107,0.12);
    --yq-ant-primary-color-active-deprecated-f-30: rgba(218,246,234,0.3);
    --yq-ant-primary-color-active-deprecated-d-02: var(--yq-yuque-green-100);
    --yq-ant-success-color: var(--yq-yuque-green-600);
    --yq-ant-success-color-hover: var(--yq-yuque-green-500);
    --yq-ant-success-color-active: var(--yq-yuque-green-700);
    --yq-ant-success-color-outline: rgba(0,185,107,0.2);
    --yq-ant-success-color-deprecated-bg: var(--yq-yuque-green-100);
    --yq-ant-success-color-deprecated-border: var(--yq-yuque-green-300);
    --yq-ant-error-color: var(--yq-red-600);
    --yq-ant-error-color-hover: var(--yq-red-500);
    --yq-ant-error-color-active: var(--yq-red-700);
    --yq-ant-error-color-outline: rgba(223,42,63,0.2);
    --yq-ant-error-color-deprecated-bg: var(--yq-red-100);
    --yq-ant-error-color-deprecated-border: var(--yq-red-300);
    --yq-ant-warning-color: var(--yq-yellow-600);
    --yq-ant-warning-color-hover: var(--yq-yellow-500);
    --yq-ant-warning-color-active: var(--yq-yellow-700);
    --yq-ant-warning-color-outline: rgba(236,170,4,0.2);
    --yq-ant-warning-color-deprecated-bg: var(--yq-yellow-100);
    --yq-ant-warning-color-deprecated-border: var(--yq-yellow-300);
    --yq-ant-info-color: var(--yq-blue-600);
    --yq-ant-info-color-deprecated-bg: var(--yq-blue-100);
    --yq-ant-info-color-deprecated-border: var(--yq-blue-300);
    --yq-ant-body-background: var(--yq-white);
    --yq-ant-component-background: var(--yq-white);
    --yq-ant-popover-background: var(--yq-white);
    --yq-ant-popover-customize-border-color: var(--yq-ant-border-color-split);
    --yq-ant-text-color-secondary: rgba(0,0,0,0.45);
    --yq-ant-text-color-inverse: var(--yq-white);
    --yq-ant-icon-color: inherit;
    --yq-ant-icon-color-hover: rgba(0,0,0,0.75);
    --yq-ant-heading-color: rgba(0,0,0,0.85);
    --yq-ant-text-color-dark: hsla(0,0%,100%,0.85);
    --yq-ant-text-color-secondary-dark: hsla(0,0%,100%,0.65);
    --yq-ant-text-selection-bg: var(--yq-theme);
    --yq-ant-item-active-bg: var(--yq-yuque-green-100);
    --yq-ant-item-hover-bg: #f5f5f5;
    --yq-ant-link-color: var(--yq-text-link);
    --yq-ant-link-hover-color: var(--yq-text-link-hover);
    --yq-ant-link-active-color: var(--yq-blue-700);
    --yq-ant-border-color-base: #d9d9d9;
    --yq-ant-border-color-split: rgba(0,0,0,0.06);
    --yq-ant-border-color-inverse: var(--yq-white);
    --yq-ant-outline-color: var(--yq-theme);
    --yq-ant-background-color-light: #fafafa;
    --yq-ant-background-color-base: #f5f5f5;
    --yq-ant-disabled-color: var(--yq-text-disable);
    --yq-ant-disabled-bg: var(--yq-ant-background-color-base);
    --yq-ant-disabled-active-bg: var(--yq-black);
    --yq-ant-disabled-color-dark: hsla(0,0%,100%,0.35);
    --yq-ant-shadow-color: rgba(0,0,0,0.15);
    --yq-ant-shadow-color-inverse: var(--yq-white);
    --yq-ant-box-shadow-base: 0 1px 4px -2px rgba(0,0,0,0.13),0 2px 8px 0 rgba(0,0,0,0.08),0 8px 16px 4px rgba(0,0,0,0.04);
    --yq-ant-shadow-1-up: 0 -6px 16px -8px rgba(0,0,0,0.08),0 -9px 28px 0 rgba(0,0,0,0.05),0 -12px 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-down: 0 6px 16px -8px rgba(0,0,0,0.08),0 9px 28px 0 rgba(0,0,0,0.05),0 12px 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-left: -6px 0 16px -8px rgba(0,0,0,0.08),-9px 0 28px 0 rgba(0,0,0,0.05),-12px 0 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-right: 6px 0 16px -8px rgba(0,0,0,0.08),9px 0 28px 0 rgba(0,0,0,0.05),12px 0 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-2: 0 3px 6px -4px rgba(0,0,0,0.12),0 6px 16px 0 rgba(0,0,0,0.08),0 9px 28px 8px rgba(0,0,0,0.05);
    --yq-ant-btn-primary-color: #fff;
    --yq-ant-btn-primary-bg: var(--yq-theme);
    --yq-ant-btn-default-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-bg: var(--yq-white);
    --yq-ant-btn-default-border: var(--yq-border-primary);
    --yq-ant-btn-danger-color: #fff;
    --yq-ant-btn-danger-bg: var(--yq-ant-error-color);
    --yq-ant-btn-danger-border: var(--yq-ant-error-color);
    --yq-ant-btn-disable-color: var(--yq-ant-disabled-color);
    --yq-ant-btn-disable-bg: var(--yq-ant-disabled-bg);
    --yq-ant-btn-disable-border: var(--yq-ant-border-color-base);
    --yq-ant-btn-default-ghost-color: var(--yq-white);
    --yq-ant-btn-default-ghost-bg: transparent;
    --yq-ant-btn-default-ghost-border: var(--yq-white);
    --yq-ant-btn-group-border: var(--yq-yuque-green-500);
    --yq-ant-btn-link-hover-bg: transparent;
    --yq-ant-btn-text-hover-bg: rgba(0,0,0,0.018);
    --yq-ant-checkbox-color: var(--yq-theme);
    --yq-ant-checkbox-check-color: var(--yq-bg-primary);
    --yq-ant-checkbox-check-bg: var(--yq-ant-checkbox-check-color);
    --yq-ant-descriptions-bg: #fafafa;
    --yq-ant-descriptions-extra-color: var(--yq-yuque-grey-900);
    --yq-ant-divider-color: rgba(0,0,0,0.06);
    --yq-ant-dropdown-selected-color: var(--yq-theme);
    --yq-ant-dropdown-menu-submenu-disabled-bg: var(--yq-white);
    --yq-ant-dropdown-selected-bg: var(--yq-ant-item-active-bg);
    --yq-ant-radio-dot-color: var(--yq-theme);
    --yq-ant-radio-dot-disabled-color: rgba(0,0,0,0.2);
    --yq-ant-radio-solid-checked-color: var(--yq-white);
    --yq-ant-radio-button-bg: var(--yq-ant-btn-default-bg);
    --yq-ant-radio-button-checked-bg: var(--yq-ant-btn-default-bg);
    --yq-ant-radio-button-color: var(--yq-ant-btn-default-color);
    --yq-ant-radio-button-hover-color: var(--yq-yuque-green-500);
    --yq-ant-radio-button-active-color: var(--yq-yuque-green-700);
    --yq-ant-radio-disabled-button-checked-bg: var(--yq-black);
    --yq-ant-radio-disabled-button-checked-color: var(--yq-ant-disabled-color);
    --yq-ant-layout-body-background: #f0f2f5;
    --yq-ant-layout-header-background: #001529;
    --yq-ant-layout-header-color: var(--yq-yuque-grey-900);
    --yq-ant-layout-footer-background: var(--yq-ant-layout-body-background);
    --yq-ant-layout-sider-background: var(--yq-ant-layout-header-background);
    --yq-ant-layout-trigger-background: #002140;
    --yq-ant-layout-trigger-color: #fff;
    --yq-ant-layout-sider-background-light: #fff;
    --yq-ant-layout-trigger-background-light: #fff;
    --yq-ant-layout-trigger-color-light: var(--yq-yuque-grey-900);
    --yq-ant-dropdown-menu-bg: var(--yq-white);
    --yq-ant-label-required-color: var(--yq-red-600);
    --yq-ant-label-color: var(--yq-ant-heading-color);
    --yq-ant-form-warning-input-bg: var(--yq-ant-input-bg);
    --yq-ant-form-error-input-bg: var(--yq-ant-input-bg);
    --yq-ant-input-placeholder-color: #bfbfbf;
    --yq-ant-input-color: var(--yq-yuque-grey-900);
    --yq-ant-input-icon-color: var(--yq-ant-input-color);
    --yq-ant-input-border-color: var(--yq-border-primary);
    --yq-ant-input-bg: var(--yq-white);
    --yq-ant-input-number-hover-border-color: var(--yq-ant-input-hover-border-color);
    --yq-ant-input-number-handler-active-bg: #f4f4f4;
    --yq-ant-input-number-handler-hover-bg: var(--yq-yuque-green-500);
    --yq-ant-input-number-handler-bg: var(--yq-white);
    --yq-ant-input-number-handler-border-color: var(--yq-ant-border-color-base);
    --yq-ant-input-addon-bg: var(--yq-ant-background-color-light);
    --yq-ant-input-hover-border-color: var(--yq-yuque-green-500);
    --yq-ant-input-disabled-bg: var(--yq-bg-tertiary);
    --yq-ant-input-outline-offset: 0 0;
    --yq-ant-input-icon-hover-color: rgba(0,0,0,0.85);
    --yq-ant-input-disabled-color: var(--yq-yuque-grey-900);
    --yq-ant-mentions-dropdown-bg: var(--yq-white);
    --yq-ant-mentions-dropdown-menu-item-hover-bg: var(--yq-ant-mentions-dropdown-bg);
    --yq-ant-select-border-color: var(--yq-ant-border-color-base);
    --yq-ant-select-item-selected-color: var(--yq-yuque-grey-900);
    --yq-ant-select-dropdown-bg: var(--yq-white);
    --yq-ant-select-item-selected-bg: var(--yq-yuque-green-100);
    --yq-ant-select-item-active-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-select-background: var(--yq-white);
    --yq-ant-select-clear-background: var(--yq-ant-select-background);
    --yq-ant-select-selection-item-bg: var(--yq-ant-background-color-base);
    --yq-ant-select-selection-item-border-color: var(--yq-ant-border-color-split);
    --yq-ant-select-multiple-disabled-background: var(--yq-ant-input-disabled-bg);
    --yq-ant-select-multiple-item-disabled-color: #bfbfbf;
    --yq-ant-select-multiple-item-disabled-border-color: var(--yq-ant-border-color-base);
    --yq-ant-cascader-bg: var(--yq-white);
    --yq-ant-cascader-item-selected-bg: var(--yq-yuque-green-100);
    --yq-ant-cascader-menu-bg: var(--yq-white);
    --yq-ant-cascader-menu-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-anchor-bg: transparent;
    --yq-ant-anchor-border-color: var(--yq-ant-border-color-split);
    --yq-ant-tooltip-color: #fff;
    --yq-ant-tooltip-bg: rgba(0,0,0,0.75);
    --yq-ant-tooltip-arrow-color: var(--yq-ant-tooltip-bg);
    --yq-ant-popover-bg: var(--yq-white);
    --yq-ant-popover-color: var(--yq-yuque-grey-900);
    --yq-ant-popover-arrow-color: var(--yq-popover-bg);
    --yq-ant-popover-arrow-outer-color: var(--yq-popover-bg);
    --yq-ant-modal-header-bg: var(--yq-white);
    --yq-ant-modal-content-bg: var(--yq-white);
    --yq-ant-modal-heading-color: var(--yq-ant-heading-color);
    --yq-ant-modal-close-color: var(--yq-ant-text-color-secondary);
    --yq-ant-modal-footer-bg: transparent;
    --yq-ant-modal-footer-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-modal-mask-bg: rgba(0,0,0,0.45);
    --yq-ant-progress-default-color: var(--yq-yuque-green-600);
    --yq-ant-progress-remaining-color: rgba(0,0,0,0.04);
    --yq-ant-progress-info-text-color: var(--yq-yuque-grey-900);
    --yq-ant-progress-steps-item-bg: #f3f3f3;
    --yq-ant-progress-text-color: var(--yq-yuque-grey-900);
    --yq-ant-menu-bg: var(--yq-white);
    --yq-ant-menu-popup-bg: var(--yq-white);
    --yq-ant-menu-item-color: var(--yq-yuque-grey-900);
    --yq-ant-menu-inline-submenu-bg: var(--yq-ant-background-color-light);
    --yq-ant-menu-highlight-color: var(--yq-theme);
    --yq-ant-menu-highlight-danger-color: var(--yq-ant-error-color);
    --yq-ant-menu-item-active-bg: var(--yq-yuque-green-100);
    --yq-ant-menu-item-active-danger-bg: var(--yq-red-100);
    --yq-ant-menu-item-group-title-color: var(--yq-ant-text-color-secondary);
    --yq-ant-menu-dark-color: hsla(0,0%,100%,0.65);
    --yq-ant-menu-dark-danger-color: var(--yq-ant-error-color);
    --yq-ant-menu-dark-bg: var(--yq-ant-layout-header-background);
    --yq-ant-menu-dark-arrow-color: #fff;
    --yq-ant-menu-dark-inline-submenu-bg: #000c17;
    --yq-ant-menu-dark-highlight-color: #fff;
    --yq-ant-menu-dark-item-active-bg: var(--yq-theme);
    --yq-ant-menu-dark-item-active-danger-bg: var(--yq-ant-error-color);
    --yq-ant-menu-dark-selected-item-icon-color: var(--yq-white);
    --yq-ant-menu-dark-selected-item-text-color: var(--yq-white);
    --yq-ant-menu-dark-item-hover-bg: transparent;
    --yq-ant-table-bg: var(--yq-ant-body-background);
    --yq-ant-table-header-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-header-color: var(--yq-ant-heading-color);
    --yq-ant-table-header-sort-bg: var(--yq-ant-background-color-base);
    --yq-ant-table-body-sort-bg: #fafafa;
    --yq-ant-table-row-hover-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-selected-row-color: inherit;
    --yq-ant-table-selected-row-bg: var(--yq-yuque-green-100);
    --yq-ant-table-body-selected-sort-bg: var(--yq-yuque-green-100);
    --yq-ant-table-selected-row-hover-bg: var(--yq-primary-color-active-deprecated-d-02);
    --yq-ant-table-expanded-row-bg: #fbfbfb;
    --yq-ant-table-border-color: var(--yq-ant-border-color-split);
    --yq-ant-table-footer-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-footer-color: var(--yq-ant-heading-color);
    --yq-ant-table-header-bg-sm: var(--yq-ant-table-header-bg);
    --yq-ant-table-header-cell-split-color: rgba(0,0,0,0.06);
    --yq-ant-table-header-sort-active-bg: rgba(0,0,0,0.04);
    --yq-ant-table-fixed-header-sort-active-bg: #f5f5f5;
    --yq-ant-table-header-filter-active-bg: rgba(0,0,0,0.04);
    --yq-ant-table-filter-btns-bg: inherit;
    --yq-ant-table-filter-dropdown-bg: var(--yq-white);
    --yq-ant-table-expand-icon-bg: var(--yq-white);
    --yq-ant-table-sticky-scroll-bar-bg: rgba(0,0,0,0.35);
    --yq-ant-tag-default-bg: var(--yq-ant-background-color-light);
    --yq-ant-tag-default-color: var(--yq-yuque-grey-900);
    --yq-ant-picker-bg: var(--yq-white);
    --yq-ant-picker-basic-cell-hover-color: var(--yq-ant-item-hover-bg);
    --yq-ant-picker-basic-cell-active-with-range-color: var(--yq-yuque-green-100);
    --yq-ant-picker-basic-cell-hover-with-range-color: var(--yq-primary-color-deprecated-l-35);
    --yq-ant-picker-basic-cell-disabled-bg: rgba(0,0,0,0.04);
    --yq-ant-picker-border-color: var(--yq-ant-border-color-split);
    --yq-ant-picker-date-hover-range-border-color: var(--yq-primary-color-deprecated-l-20);
    --yq-ant-picker-date-hover-range-color: var(--yq-ant-picker-basic-cell-hover-with-range-color);
    --yq-ant-calendar-bg: var(--yq-white);
    --yq-ant-calendar-input-bg: var(--yq-ant-input-bg);
    --yq-ant-calendar-border-color: var(--yq-white);
    --yq-ant-calendar-item-active-bg: var(--yq-ant-item-active-bg);
    --yq-ant-calendar-column-active-bg: var(--yq-primary-color-active-deprecated-f-30);
    --yq-ant-calendar-full-bg: var(--yq-ant-calendar-bg);
    --yq-ant-calendar-full-panel-bg: var(--yq-ant-calendar-full-bg);
    --yq-ant-badge-text-color: #fff;
    --yq-ant-badge-color: var(--yq-badge);
    --yq-ant-rate-star-color: var(--yq-yellow-600);
    --yq-ant-rate-star-bg: var(--yq-ant-border-color-split);
    --yq-ant-card-head-color: var(--yq-ant-heading-color);
    --yq-ant-card-head-background: transparent;
    --yq-ant-card-actions-background: var(--yq-white);
    --yq-ant-card-skeleton-bg: #cfd8dc;
    --yq-ant-card-background: var(--yq-white);
    --yq-ant-card-head-extra-color: var(--yq-yuque-grey-900);
    --yq-ant-comment-bg: inherit;
    --yq-ant-comment-author-name-color: var(--yq-ant-text-color-secondary);
    --yq-ant-comment-author-time-color: #ccc;
    --yq-ant-comment-action-color: var(--yq-ant-text-color-secondary);
    --yq-ant-comment-action-hover-color: #595959;
    --yq-ant-tabs-card-head-background: var(--yq-ant-background-color-light);
    --yq-ant-tabs-card-active-color: var(--yq-theme);
    --yq-ant-tabs-ink-bar-color: var(--yq-theme);
    --yq-ant-tabs-highlight-color: var(--yq-theme);
    --yq-ant-tabs-hover-color: var(--yq-yuque-green-500);
    --yq-ant-tabs-active-color: var(--yq-yuque-green-700);
    --yq-ant-back-top-color: #fff;
    --yq-ant-back-top-bg: var(--yq-ant-text-color-secondary);
    --yq-ant-back-top-hover-bg: var(--yq-yuque-grey-900);
    --yq-ant-avatar-bg: #ccc;
    --yq-ant-avatar-color: #fff;
    --yq-ant-avatar-group-border-color: #fff;
    --yq-ant-switch-color: var(--yq-theme);
    --yq-ant-switch-bg: var(--yq-white);
    --yq-ant-switch-shadow-color: rgba(0,35,11,0.2);
    --yq-ant-pagination-item-bg: var(--yq-white);
    --yq-ant-pagination-item-bg-active: var(--yq-white);
    --yq-ant-pagination-item-link-bg: var(--yq-white);
    --yq-ant-pagination-item-disabled-color-active: var(--yq-ant-disabled-color);
    --yq-ant-pagination-item-disabled-bg-active: var(--yq-black);
    --yq-ant-pagination-item-input-bg: var(--yq-white);
    --yq-ant-page-header-back-color: #000;
    --yq-ant-page-header-ghost-bg: inherit;
    --yq-ant-breadcrumb-base-color: var(--yq-ant-text-color-secondary);
    --yq-ant-breadcrumb-last-item-color: var(--yq-yuque-grey-900);
    --yq-ant-breadcrumb-link-color: var(--yq-ant-text-color-secondary);
    --yq-ant-breadcrumb-link-color-hover: var(--yq-yuque-grey-900);
    --yq-ant-breadcrumb-separator-color: var(--yq-ant-text-color-secondary);
    --yq-ant-slider-rail-background-color: var(--yq-ant-background-color-base);
    --yq-ant-slider-rail-background-color-hover: #e1e1e1;
    --yq-ant-slider-track-background-color: var(--yq-yuque-green-300);
    --yq-ant-slider-track-background-color-hover: var(--yq-yuque-green-400);
    --yq-ant-slider-handle-background-color: var(--yq-white);
    --yq-ant-slider-handle-color: var(--yq-yuque-green-300);
    --yq-ant-slider-handle-color-hover: var(--yq-yuque-green-400);
    --yq-ant-slider-handle-color-focus: var(--yq-primary-color-deprecated-t-20);
    --yq-ant-slider-handle-color-focus-shadow: var(--yq-primary-color-deprecated-f-12);
    --yq-ant-slider-handle-color-tooltip-open: var(--yq-theme);
    --yq-ant-slider-dot-border-color: var(--yq-ant-border-color-split);
    --yq-ant-slider-dot-border-color-active: var(--yq-primary-color-deprecated-t-50);
    --yq-ant-slider-disabled-color: var(--yq-ant-disabled-color);
    --yq-ant-slider-disabled-background-color: var(--yq-white);
    --yq-ant-tree-bg: var(--yq-white);
    --yq-ant-tree-directory-selected-color: #fff;
    --yq-ant-tree-directory-selected-bg: var(--yq-theme);
    --yq-ant-tree-node-hover-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-tree-node-selected-bg: var(--yq-yuque-green-200);
    --yq-ant-collapse-header-bg: var(--yq-ant-background-color-light);
    --yq-ant-collapse-content-bg: var(--yq-bg-primary);
    --yq-ant-skeleton-color: hsla(0,0%,74.5%,0.2);
    --yq-ant-skeleton-to-color: hsla(0,0%,50.6%,0.24);
    --yq-ant-transfer-disabled-bg: var(--yq-ant-disabled-bg);
    --yq-ant-transfer-item-hover-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-transfer-item-selected-hover-bg: var(--yq-primary-color-active-deprecated-d-02);
    --yq-ant-message-notice-content-bg: var(--yq-white);
    --yq-ant-alert-success-border-color: var(--yq-ant-success-color-deprecated-border);
    --yq-ant-alert-success-bg-color: var(--yq-ant-success-color-deprecated-bg);
    --yq-ant-alert-success-icon-color: var(--yq-ant-success-color);
    --yq-ant-alert-info-border-color: var(--yq-ant-info-color-deprecated-border);
    --yq-ant-alert-info-bg-color: var(--yq-ant-info-color-deprecated-bg);
    --yq-ant-alert-info-icon-color: var(--yq-ant-info-color);
    --yq-ant-alert-warning-border-color: var(--yq-ant-warning-color-deprecated-border);
    --yq-ant-alert-warning-bg-color: var(--yq-ant-warning-color-deprecated-bg);
    --yq-ant-alert-warning-icon-color: var(--yq-ant-warning-color);
    --yq-ant-alert-error-border-color: var(--yq-ant-error-color-deprecated-border);
    --yq-ant-alert-error-bg-color: var(--yq-ant-error-color-deprecated-bg);
    --yq-ant-alert-error-icon-color: var(--yq-ant-error-color);
    --yq-ant-alert-message-color: var(--yq-ant-heading-color);
    --yq-ant-alert-text-color: var(--yq-yuque-grey-900);
    --yq-ant-alert-close-color: var(--yq-ant-text-color-secondary);
    --yq-ant-alert-close-hover-color: var(--yq-ant-icon-color-hover);
    --yq-ant-list-header-background: transparent;
    --yq-ant-list-footer-background: transparent;
    --yq-ant-list-customize-card-bg: var(--yq-white);
    --yq-ant-drawer-bg: var(--yq-white);
    --yq-ant-timeline-color: var(--yq-ant-border-color-split);
    --yq-ant-timeline-dot-color: var(--yq-theme);
    --yq-ant-timeline-dot-bg: var(--yq-white);
    --yq-ant-upload-actions-color: var(--yq-ant-text-color-secondary);
    --yq-ant-process-tail-color: var(--yq-ant-border-color-split);
    --yq-ant-steps-nav-arrow-color: rgba(0,0,0,0.25);
    --yq-ant-steps-background: var(--yq-white);
    --yq-ant-notification-bg: var(--yq-white);
    --yq-ant-image-bg: #f5f5f5;
    --yq-ant-image-color: #fff;
    --yq-ant-image-preview-operation-color: hsla(0,0%,100%,0.85);
    --yq-ant-image-preview-operation-disabled-color: hsla(0,0%,100%,0.85);
    --yq-ant-segmented-bg: rgba(0,0,0,0.04);
    --yq-ant-segmented-hover-bg: rgba(0,0,0,0.06);
    --yq-ant-segmented-selected-bg: var(--yq-white);
    --yq-ant-segmented-label-color: rgba(0,0,0,0.65);
    --yq-ant-segmented-label-hover-color: #262626
}

html[data-kumuhana=pouli] {
    --yq-ant-primary-color: #2ed790;
    --yq-ant-primary-color-hover: var(--yq-yuque-green-400);
    --yq-ant-primary-color-active: var(--yq-yuque-green-600);
    --yq-ant-primary-color-outline: rgba(46,215,144,0.2);
    --yq-ant-primary-1: var(--yq-yuque-green-100);
    --yq-ant-primary-2: var(--yq-yuque-green-200);
    --yq-ant-primary-3: var(--yq-yuque-green-300);
    --yq-ant-primary-4: var(--yq-yuque-green-400);
    --yq-ant-primary-5: var(--yq-yuque-green-500);
    --yq-ant-primary-6: var(--yq-yuque-green-600);
    --yq-ant-primary-7: var(--yq-yuque-green-700);
    --yq-ant-primary-color-deprecated-l-35: #c4f4e0;
    --yq-ant-primary-color-deprecated-l-20: #84e7bd;
    --yq-ant-primary-color-deprecated-t-20: #58dfa6;
    --yq-ant-primary-color-deprecated-t-50: #97ebc8;
    --yq-ant-primary-color-deprecated-f-12: rgba(46,215,144,0.2);
    --yq-ant-primary-color-active-deprecated-f-30: rgba(218,246,234,0.3);
    --yq-ant-primary-color-active-deprecated-d-02: var(--yq-yuque-green-100);
    --yq-ant-success-color: var(--yq-yuque-green-600);
    --yq-ant-success-color-hover: var(--yq-yuque-green-500);
    --yq-ant-success-color-active: var(--yq-yuque-green-700);
    --yq-ant-success-color-outline: rgba(81,184,141,0.2);
    --yq-ant-success-color-deprecated-bg: var(--yq-yuque-green-100);
    --yq-ant-success-color-deprecated-border: var(--yq-yuque-green-300);
    --yq-ant-error-color: var(--yq-red-600);
    --yq-ant-error-color-hover: var(--yq-red-500);
    --yq-ant-error-color-active: var(--yq-red-700);
    --yq-ant-error-color-outline: rgba(202,63,79,0.2);
    --yq-ant-error-color-deprecated-bg: var(--yq-red-100);
    --yq-ant-error-color-deprecated-border: var(--yq-red-300);
    --yq-ant-warning-color: var(--yq-yellow-600);
    --yq-ant-warning-color-hover: var(--yq-yellow-500);
    --yq-ant-warning-color-active: var(--yq-yellow-700);
    --yq-ant-warning-color-outline: rgba(210,166,56,0.2);
    --yq-ant-warning-color-deprecated-bg: var(--yq-yellow-100);
    --yq-ant-warning-color-deprecated-border: var(--yq-yellow-300);
    --yq-ant-info-color: var(--yq-blue-600);
    --yq-ant-info-color-deprecated-bg: var(--yq-blue-100);
    --yq-ant-info-color-deprecated-border: var(--yq-blue-300);
    --yq-ant-popover-background: #1f1f1f;
    --yq-ant-popover-customize-border-color: #3a3a3a;
    --yq-ant-body-background: var(--yq-black);
    --yq-ant-component-background: var(--yq-background-base);
    --yq-ant-text-color: hsla(0,0%,100%,0.85);
    --yq-ant-text-color-secondary: hsla(0,0%,100%,0.45);
    --yq-ant-text-color-inverse: #fff;
    --yq-ant-icon-color-hover: hsla(0,0%,100%,0.75);
    --yq-ant-heading-color: hsla(0,0%,100%,0.85);
    --yq-ant-item-active-bg: hsla(0,0%,100%,0.12);
    --yq-ant-item-hover-bg: hsla(0,0%,100%,0.08);
    --yq-ant-border-color-base: hsla(0,0%,100%,0.12);
    --yq-ant-border-color-split: hsla(0,0%,100%,0.08);
    --yq-ant-background-color-light: hsla(0,0%,100%,0.04);
    --yq-ant-background-color-base: hsla(0,0%,100%,0.08);
    --yq-ant-disabled-bg: var(--yq-ant-background-color-base);
    --yq-ant-disabled-color-dark: hsla(0,0%,100%,0.3);
    --yq-ant-tree-bg: transparent;
    --yq-ant-list-customize-card-bg: transparent;
    --yq-ant-shadow-color: rgba(0,0,0,0.45);
    --yq-ant-shadow-color-inverse: var(--yq-ant-component-background);
    --yq-ant-box-shadow-base: 0 1px 4px -2px rgba(0,0,0,0.13),0 2px 8px 0 rgba(0,0,0,0.08),0 8px 16px 4px rgba(0,0,0,0.04);
    --yq-ant-shadow-1-up: 0 -6px 16px -8px rgba(0,0,0,0.32),0 -9px 28px 0 rgba(0,0,0,0.2),0 -12px 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-1-down: 0 6px 16px -8px rgba(0,0,0,0.32),0 9px 28px 0 rgba(0,0,0,0.2),0 12px 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-1-right: 6px 0 16px -8px rgba(0,0,0,0.32),9px 0 28px 0 rgba(0,0,0,0.2),12px 0 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-2: 0 1px 4px -2px rgba(0,0,0,0.24),0 2px 8px 0 rgba(0,0,0,0.36),0 8px 16px 4px rgba(0,0,0,0.25);
    --yq-ant-btn-shadow: 0 2px 0 rgba(0,0,0,0.015);
    --yq-ant-btn-primary-shadow: 0 2px 0 rgba(0,0,0,0.045);
    --yq-ant-btn-text-shadow: 0 -1px 0 rgba(0,0,0,0.12);
    --yq-ant-btn-default-bg: transparent;
    --yq-ant-btn-default-ghost-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-ghost-border: hsla(0,0%,100%,0.25);
    --yq-ant-btn-text-hover-bg: hsla(0,0%,100%,0.03);
    --yq-ant-btn-primary-color: var(--yq-yuque-grey-100);
    --yq-ant-btn-primary-bg: #2ed790;
    --yq-ant-btn-default-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-border: hsla(0,0%,100%,0.12);
    --yq-ant-btn-danger-color: #fff;
    --yq-ant-btn-danger-bg: var(--yq-ant-error-color);
    --yq-ant-btn-danger-border: var(--yq-ant-error-color);
    --yq-ant-btn-disable-color: var(--yq-ant-disabled-color);
    --yq-ant-btn-disable-bg: var(--yq-ant-disabled-bg);
    --yq-ant-btn-disable-border: var(--yq-ant-border-color-base);
    --yq-ant-btn-default-ghost-bg: transparent;
    --yq-ant-btn-group-border: var(--yq-yuque-green-500);
    --yq-ant-btn-link-hover-bg: transparent;
    --yq-ant-checkbox-check-bg: hsla(0,0%,100%,0.04);
    --yq-ant-descriptions-bg: var(--yq-ant-background-color-light);
    --yq-ant-divider-color: hsla(0,0%,100%,0.12);
    --yq-ant-modal-header-bg: var(--yq-yuque-grey-200);
    --yq-ant-modal-header-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-modal-content-bg: var(--yq-yuque-grey-200);
    --yq-ant-modal-footer-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-radio-solid-checked-color: #fff;
    --yq-ant-radio-dot-disabled-color: hsla(0,0%,100%,0.2);
    --yq-ant-radio-disabled-button-checked-bg: hsla(0,0%,100%,0.2);
    --yq-ant-radio-disabled-button-checked-color: var(--yq-ant-disabled-color);
    --yq-ant-layout-body-background: var(--yq-ant-body-background);
    --yq-ant-layout-header-background: var(--yq-yuque-grey-200);
    --yq-ant-layout-trigger-background: #262626;
    --yq-ant-input-bg: hsla(0,0%,100%,0.04);
    --yq-ant-input-placeholder-color: hsla(0,0%,100%,0.3);
    --yq-ant-input-icon-color: hsla(0,0%,100%,0.3);
    --yq-ant-input-number-handler-active-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-input-icon-hover-color: hsla(0,0%,100%,0.85);
    --yq-ant-select-background: hsla(0,0%,100%,0.04);
    --yq-ant-select-dropdown-bg: var(--yq-yuque-grey-200);
    --yq-ant-select-clear-background: var(--yq-ant-component-background);
    --yq-ant-select-selection-item-bg: hsla(0,0%,100%,0.08);
    --yq-ant-select-selection-item-border-color: var(--yq-ant-border-color-split);
    --yq-ant-select-multiple-disabled-background: var(--yq-ant-component-background);
    --yq-ant-select-multiple-item-disabled-color: #595959;
    --yq-ant-select-multiple-item-disabled-border-color: var(--yq-yuque-grey-200);
    --yq-ant-cascader-bg: hsla(0,0%,100%,0.04);
    --yq-ant-cascader-menu-bg: var(--yq-yuque-grey-200);
    --yq-ant-cascader-menu-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-tooltip-bg: var(--yq-yuque-grey-200);
    --yq-ant-menu-dark-inline-submenu-bg: var(--yq-ant-component-background);
    --yq-ant-menu-dark-bg: var(--yq-yuque-grey-200);
    --yq-ant-menu-popup-bg: var(--yq-yuque-grey-200);
    --yq-ant-message-notice-content-bg: var(--yq-yuque-grey-200);
    --yq-ant-notification-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-header-bg: #1d1d1d;
    --yq-ant-table-body-sort-bg: hsla(0,0%,100%,0.01);
    --yq-ant-table-row-hover-bg: #262626;
    --yq-ant-table-header-cell-split-color: rgba(0,0,0,0.08);
    --yq-ant-table-header-sort-bg: #262626;
    --yq-ant-table-header-filter-active-bg: #434343;
    --yq-ant-table-header-sort-active-bg: #303030;
    --yq-ant-table-filter-btns-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-expanded-row-bg: var(--yq-ant-table-header-bg);
    --yq-ant-table-filter-dropdown-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-expand-icon-bg: transparent;
    --yq-ant-picker-basic-cell-hover-with-range-color: var(--yq-yuque-green-600);
    --yq-ant-picker-basic-cell-disabled-bg: #303030;
    --yq-ant-picker-border-color: var(--yq-ant-border-color-split);
    --yq-ant-picker-bg: hsla(0,0%,100%,0.04);
    --yq-ant-picker-date-hover-range-border-color: var(--yq-yuque-green-600);
    --yq-ant-dropdown-menu-bg: var(--yq-yuque-grey-200);
    --yq-ant-dropdown-menu-submenu-disabled-bg: transparent;
    --yq-ant-steps-nav-arrow-color: hsla(0,0%,100%,0.2);
    --yq-ant-steps-background: transparent;
    --yq-ant-avatar-bg: hsla(0,0%,100%,0.3);
    --yq-ant-progress-steps-item-bg: hsla(0,0%,100%,0.08);
    --yq-ant-calendar-bg: var(--yq-yuque-grey-200);
    --yq-ant-calendar-input-bg: var(--yq-yuque-grey-200);
    --yq-ant-calendar-border-color: transparent;
    --yq-ant-calendar-full-bg: var(--yq-ant-component-background);
    --yq-ant-badge-text-color: var(--yq-yuque-grey-100);
    --yq-ant-badge-color: var(--yq-blue-500);
    --yq-ant-popover-bg: var(--yq-yuque-grey-200);
    --yq-ant-drawer-bg: var(--yq-yuque-grey-200);
    --yq-ant-card-actions-background: var(--yq-ant-component-background);
    --yq-ant-card-skeleton-bg: #303030;
    --yq-ant-card-shadow: 0 1px 2px -2px rgba(0,0,0,0.64),0 3px 6px 0 rgba(0,0,0,0.48),0 5px 12px 4px rgba(0,0,0,0.36);
    --yq-ant-transfer-item-hover-bg: #262626;
    --yq-ant-comment-bg: transparent;
    --yq-ant-comment-author-time-color: hsla(0,0%,100%,0.3);
    --yq-ant-comment-action-hover-color: hsla(0,0%,100%,0.65);
    --yq-ant-rate-star-bg: hsla(0,0%,100%,0.12);
    --yq-ant-switch-bg: #fff;
    --yq-ant-pagination-item-bg: transparent;
    --yq-ant-pagination-item-bg-active: transparent;
    --yq-ant-pagination-item-link-bg: transparent;
    --yq-ant-pagination-item-disabled-bg-active: hsla(0,0%,100%,0.25);
    --yq-ant-pagination-item-disabled-color-active: var(--yq-black);
    --yq-ant-pagination-item-input-bg: var(--yq-ant-pagination-item-bg);
    --yq-ant-page-header-back-color: inherit;
    --yq-ant-page-header-ghost-bg: transparent;
    --yq-ant-slider-handle-background-color: var(--yq-yuque-grey-200);
    --yq-ant-slider-rail-background-color: #262626;
    --yq-ant-slider-rail-background-color-hover: var(--yq-ant-border-color-base);
    --yq-ant-slider-dot-border-color: var(--yq-ant-border-color-split);
    --yq-ant-slider-dot-border-color-active: var(--yq-yuque-green-400);
    --yq-ant-skeleton-to-color: hsla(0,0%,100%,0.16);
    --yq-ant-alert-success-border-color: var(--yq-yuque-green-300);
    --yq-ant-alert-success-bg-color: var(--yq-yuque-green-100);
    --yq-ant-alert-success-icon-color: var(--yq-ant-success-color);
    --yq-ant-alert-info-border-color: var(--yq-blue-300);
    --yq-ant-alert-info-bg-color: var(--yq-blue-100);
    --yq-ant-alert-info-icon-color: var(--yq-ant-info-color);
    --yq-ant-alert-warning-border-color: var(--yq-yellow-300);
    --yq-ant-alert-warning-bg-color: var(--yq-yellow-100);
    --yq-ant-alert-warning-icon-color: var(--yq-ant-warning-color);
    --yq-ant-alert-error-border-color: var(--yq-red-300);
    --yq-ant-alert-error-bg-color: var(--yq-red-100);
    --yq-ant-alert-error-icon-color: var(--yq-ant-error-color);
    --yq-ant-timeline-color: var(--yq-ant-border-color-split);
    --yq-ant-timeline-dot-color: var(--yq-yuque-green-600);
    --yq-ant-mentions-dropdown-bg: var(--yq-yuque-grey-200)
}

:root {
    --yq-white: #fff;
    --yq-black: #000;
    --yq-background-base: #fff;
    --yq-yuque-grey-100: #fafafa;
    --yq-yuque-grey-200: #f4f5f5;
    --yq-yuque-grey-300: #eff0f0;
    --yq-yuque-grey-400: #e7e9e8;
    --yq-yuque-grey-500: #d8dad9;
    --yq-yuque-grey-600: #bec0bf;
    --yq-yuque-grey-700: #8a8f8d;
    --yq-yuque-grey-800: #585a5a;
    --yq-yuque-grey-900: #262626;
    --yq-yuque-grey-1: #fafafa;
    --yq-yuque-grey-2: #f4f5f5;
    --yq-yuque-grey-3: #eff0f0;
    --yq-yuque-grey-4: #e7e9e8;
    --yq-yuque-grey-5: #d8dad9;
    --yq-yuque-grey-6: #bec0bf;
    --yq-yuque-grey-7: #8a8f8d;
    --yq-yuque-grey-8: #585a5a;
    --yq-yuque-grey-9: #262626;
    --yq-pea-green-50: #f3fbe7;
    --yq-pea-green-100: #e8f7cf;
    --yq-pea-green-200: #dbf1b7;
    --yq-pea-green-300: #c1e77e;
    --yq-pea-green-400: #a7dd4b;
    --yq-pea-green-500: #8ccf17;
    --yq-pea-green-600: #74b602;
    --yq-pea-green-700: #5c8d07;
    --yq-pea-green-800: #406105;
    --yq-pea-green-900: #2a4200;
    --yq-pea-green-1: #e8f7cf;
    --yq-pea-green-2: #dbf1b7;
    --yq-pea-green-3: #c1e77e;
    --yq-pea-green-4: #a7dd4b;
    --yq-pea-green-5: #8ccf17;
    --yq-pea-green-6: #74b602;
    --yq-pea-green-7: #5c8d07;
    --yq-pea-green-8: #406105;
    --yq-pea-green-9: #2a4200;
    --yq-yuque-green-50: #ecfaf4;
    --yq-yuque-green-100: #daf6ea;
    --yq-yuque-green-200: #c7f0df;
    --yq-yuque-green-300: #82edc0;
    --yq-yuque-green-400: #45de9d;
    --yq-yuque-green-500: #0bd07d;
    --yq-yuque-green-600: #00b96b;
    --yq-yuque-green-700: #009456;
    --yq-yuque-green-800: #00663b;
    --yq-yuque-green-900: #003d23;
    --yq-yuque-green-1: #daf6ea;
    --yq-yuque-green-2: #c7f0df;
    --yq-yuque-green-3: #82edc0;
    --yq-yuque-green-4: #45de9d;
    --yq-yuque-green-5: #0bd07d;
    --yq-yuque-green-6: #00b96b;
    --yq-yuque-green-7: #009456;
    --yq-yuque-green-8: #00663b;
    --yq-yuque-green-9: #003d23;
    --yq-cyan-50: #e6f9fb;
    --yq-cyan-100: #cef5f7;
    --yq-cyan-200: #b5eff2;
    --yq-cyan-300: #81dfe4;
    --yq-cyan-400: #5ed3d9;
    --yq-cyan-500: #1dc0c9;
    --yq-cyan-600: #01b2bc;
    --yq-cyan-700: #078b92;
    --yq-cyan-800: #07787e;
    --yq-cyan-900: #004347;
    --yq-cyan-1: #cef5f7;
    --yq-cyan-2: #b5eff2;
    --yq-cyan-3: #81dfe4;
    --yq-cyan-4: #5ed3d9;
    --yq-cyan-5: #1dc0c9;
    --yq-cyan-6: #01b2bc;
    --yq-cyan-7: #078b92;
    --yq-cyan-8: #07787e;
    --yq-cyan-9: #004347;
    --yq-blue-50: #ecf4fd;
    --yq-blue-100: #d9eafc;
    --yq-blue-200: #c0ddfc;
    --yq-blue-300: #81bbf8;
    --yq-blue-400: #5fa8f6;
    --yq-blue-500: #2f8ef4;
    --yq-blue-600: #117cee;
    --yq-blue-700: #0c68ca;
    --yq-blue-800: #074a92;
    --yq-blue-900: #00346b;
    --yq-blue-1: #d9eafc;
    --yq-blue-2: #c0ddfc;
    --yq-blue-3: #81bbf8;
    --yq-blue-4: #5fa8f6;
    --yq-blue-5: #2f8ef4;
    --yq-blue-6: #117cee;
    --yq-blue-7: #0c68ca;
    --yq-blue-8: #074a92;
    --yq-blue-9: #00346b;
    --yq-sea-blue-50: #eceefd;
    --yq-sea-blue-100: #d9dffc;
    --yq-sea-blue-200: #c0cafc;
    --yq-sea-blue-300: #96a7fd;
    --yq-sea-blue-400: #6e85f7;
    --yq-sea-blue-500: #4861e0;
    --yq-sea-blue-600: #2f4bda;
    --yq-sea-blue-700: #213bc0;
    --yq-sea-blue-800: #162883;
    --yq-sea-blue-900: #101e60;
    --yq-sea-blue-1: #d9dffc;
    --yq-sea-blue-2: #c0cafc;
    --yq-sea-blue-3: #96a7fd;
    --yq-sea-blue-4: #6e85f7;
    --yq-sea-blue-5: #4861e0;
    --yq-sea-blue-6: #2f4bda;
    --yq-sea-blue-7: #213bc0;
    --yq-sea-blue-8: #162883;
    --yq-sea-blue-9: #101e60;
    --yq-purple-50: #f2edfc;
    --yq-purple-100: #e6dcf9;
    --yq-purple-200: #d9c9f8;
    --yq-purple-300: #ba9bf2;
    --yq-purple-400: #9a6eed;
    --yq-purple-500: #7e45e8;
    --yq-purple-600: #601bde;
    --yq-purple-700: #4c16b1;
    --yq-purple-800: #391084;
    --yq-purple-900: #270070;
    --yq-purple-1: #e6dcf9;
    --yq-purple-2: #d9c9f8;
    --yq-purple-3: #ba9bf2;
    --yq-purple-4: #9a6eed;
    --yq-purple-5: #7e45e8;
    --yq-purple-6: #601bde;
    --yq-purple-7: #4c16b1;
    --yq-purple-8: #391084;
    --yq-purple-9: #270070;
    --yq-magenta-50: #fdeff7;
    --yq-magenta-100: #fbdfef;
    --yq-magenta-200: #f7c4e2;
    --yq-magenta-300: #f297cc;
    --yq-magenta-400: #ec6ab6;
    --yq-magenta-500: #e746a4;
    --yq-magenta-600: #d22d8d;
    --yq-magenta-700: #ae146e;
    --yq-magenta-800: #800f51;
    --yq-magenta-900: #5c0036;
    --yq-magenta-1: #fbdfef;
    --yq-magenta-2: #f7c4e2;
    --yq-magenta-3: #f297cc;
    --yq-magenta-4: #ec6ab6;
    --yq-magenta-5: #e746a4;
    --yq-magenta-6: #d22d8d;
    --yq-magenta-7: #ae146e;
    --yq-magenta-8: #800f51;
    --yq-magenta-9: #5c0036;
    --yq-red-50: #fdf1f3;
    --yq-red-100: #fbe4e7;
    --yq-red-200: #f8ced3;
    --yq-red-300: #f1a2ab;
    --yq-red-400: #ea7583;
    --yq-red-500: #e4495b;
    --yq-red-600: #df2a3f;
    --yq-red-700: #ad1a2b;
    --yq-red-800: #8f0515;
    --yq-red-900: #70000d;
    --yq-red-1: #fbe4e7;
    --yq-red-2: #f8ced3;
    --yq-red-3: #f1a2ab;
    --yq-red-4: #ea7583;
    --yq-red-5: #e4495b;
    --yq-red-6: #df2a3f;
    --yq-red-7: #ad1a2b;
    --yq-red-8: #8f0515;
    --yq-red-9: #70000d;
    --yq-vermilion-50: #fdf3f1;
    --yq-vermilion-100: #fbe8e4;
    --yq-vermilion-200: #f8d6ce;
    --yq-vermilion-300: #f0ad9d;
    --yq-vermilion-400: #eb866f;
    --yq-vermilion-500: #e46549;
    --yq-vermilion-600: #df4b2a;
    --yq-vermilion-700: #ad351a;
    --yq-vermilion-800: #8f1e05;
    --yq-vermilion-900: #701500;
    --yq-vermilion-1: #fbe8e4;
    --yq-vermilion-2: #f8d6ce;
    --yq-vermilion-3: #f0ad9d;
    --yq-vermilion-4: #eb866f;
    --yq-vermilion-5: #e46549;
    --yq-vermilion-6: #df4b2a;
    --yq-vermilion-7: #ad351a;
    --yq-vermilion-8: #8f1e05;
    --yq-vermilion-9: #701500;
    --yq-orange-50: #fef2e9;
    --yq-orange-100: #fde6d3;
    --yq-orange-200: #f8d6b9;
    --yq-orange-300: #f8b881;
    --yq-orange-400: #f6a055;
    --yq-orange-500: #f38f39;
    --yq-orange-600: #ed740c;
    --yq-orange-700: #c75c00;
    --yq-orange-800: #944400;
    --yq-orange-900: #663000;
    --yq-orange-1: #fde6d3;
    --yq-orange-2: #f8d6b9;
    --yq-orange-3: #f8b881;
    --yq-orange-4: #f6a055;
    --yq-orange-5: #f38f39;
    --yq-orange-6: #ed740c;
    --yq-orange-7: #c75c00;
    --yq-orange-8: #944400;
    --yq-orange-9: #663000;
    --yq-yellow-50: #fcf5e6;
    --yq-yellow-100: #f9efcd;
    --yq-yellow-200: #f6e1ac;
    --yq-yellow-300: #f5d480;
    --yq-yellow-400: #f5cb61;
    --yq-yellow-500: #f3bb2f;
    --yq-yellow-600: #ecaa04;
    --yq-yellow-700: #c99103;
    --yq-yellow-800: #8f6600;
    --yq-yellow-900: #664900;
    --yq-yellow-1: #f9efcd;
    --yq-yellow-2: #f6e1ac;
    --yq-yellow-3: #f5d480;
    --yq-yellow-4: #f5cb61;
    --yq-yellow-5: #f3bb2f;
    --yq-yellow-6: #ecaa04;
    --yq-yellow-7: #c99103;
    --yq-yellow-8: #8f6600;
    --yq-yellow-9: #664900;
    --yq-bright-yellow-50: #fdf9e4;
    --yq-bright-yellow-100: #fbf5cb;
    --yq-bright-yellow-200: #fcf1a6;
    --yq-bright-yellow-300: #fdeb78;
    --yq-bright-yellow-400: #fce75a;
    --yq-bright-yellow-500: #fbde28;
    --yq-bright-yellow-600: #edce02;
    --yq-bright-yellow-700: #cdb204;
    --yq-bright-yellow-800: #a58f04;
    --yq-bright-yellow-900: #665800;
    --yq-bright-yellow-1: #fbf5cb;
    --yq-bright-yellow-2: #fcf1a6;
    --yq-bright-yellow-3: #fdeb78;
    --yq-bright-yellow-4: #fce75a;
    --yq-bright-yellow-5: #fbde28;
    --yq-bright-yellow-6: #edce02;
    --yq-bright-yellow-7: #cdb204;
    --yq-bright-yellow-8: #a58f04;
    --yq-bright-yellow-9: #665800;
    --yq-brown-50: #f7f2ea;
    --yq-brown-100: #f0e7d6;
    --yq-brown-200: #e7d7bb;
    --yq-brown-300: #dbc39a;
    --yq-brown-400: #d2b684;
    --yq-brown-500: #c59f5e;
    --yq-brown-600: #b1873f;
    --yq-brown-700: #927035;
    --yq-brown-800: #74592a;
    --yq-brown-900: #523f1e;
    --yq-brown-1: #f0e7d6;
    --yq-brown-2: #e7d7bb;
    --yq-brown-3: #dbc39a;
    --yq-brown-4: #d2b684;
    --yq-brown-5: #c59f5e;
    --yq-brown-6: #b1873f;
    --yq-brown-7: #927035;
    --yq-brown-8: #74592a;
    --yq-brown-9: #523f1e;
    --yq-theme: var(--yq-yuque-green-600);
    --yq-normal: var(--yq-yuque-grey-500);
    --yq-bg-primary: var(--yq-background-base);
    --yq-bg-secondary: var(--yq-yuque-grey-100);
    --yq-bg-tertiary: var(--yq-yuque-grey-200);
    --yq-bg-primary-hover: var(--yq-yuque-grey-300);
    --yq-bg-primary-hover-light: var(--yq-yuque-grey-400);
    --yq-bg-foreground: var(--yq-white);
    --yq-bg-pre-foreground: var(--yq-white);
    --yq-bg-pre-secondary: var(--yq-yuque-grey-200);
    --yq-bg-card-dialog: var(--yq-yuque-grey-100);
    --yq-mask-bg: rgba(0,0,0,0.65);
    --yq-toast-bg: rgba(0,0,0,0.65);
    --yq-border-primary: var(--yq-yuque-grey-400);
    --yq-border-primary-active: var(--yq-yuque-green-600);
    --yq-border-light: rgba(0,0,0,0.04);
    --yq-border-heavy: var(--yq-yuque-grey-300);
    --yq-cardborder-hover: var(--yq-blue-300);
    --yq-cardborder-selected: var(--yq-blue-500);
    --yq-sheetborder: rgba(0,0,0,0.1);
    --yq-text-primary: var(--yq-yuque-grey-900);
    --yq-text-body: var(--yq-yuque-grey-800);
    --yq-text-caption: var(--yq-yuque-grey-700);
    --yq-text-disable: var(--yq-yuque-grey-600);
    --yq-text-link: var(--yq-blue-600);
    --yq-text-link-hover: var(--yq-blue-400);
    --yq-icon-primary: var(--yq-yuque-grey-900);
    --yq-icon-secondary: var(--yq-yuque-grey-800);
    --yq-icon-caption: var(--yq-yuque-grey-700);
    --yq-icon-disable: var(--yq-yuque-grey-600);
    --yq-icon-hover: var(--yq-yuque-grey-500);
    --yq-icon-colorbg: rgba(0,0,0,0.45);
    --yq-function-success: var(--yq-yuque-green-600);
    --yq-function-info: var(--yq-blue-600);
    --yq-function-warning: var(--yq-yellow-600);
    --yq-function-error: var(--yq-red-600);
    --yq-badge: var(--yq-blue-500);
    --yq-popover-bg: var(--yq-white);
    --yq-disable-color: var(--yq-yuque-grey-600)
}

html[data-kumuhana=pouli] {
    --yq-white: #000;
    --yq-black: #fff;
    --yq-background-base: #141414;
    --yq-yuque-grey-100: #141414;
    --yq-yuque-grey-200: #1f1f1f;
    --yq-yuque-grey-300: #292929;
    --yq-yuque-grey-400: #333;
    --yq-yuque-grey-500: #424242;
    --yq-yuque-grey-600: #505050;
    --yq-yuque-grey-700: #848484;
    --yq-yuque-grey-800: #b3b3b3;
    --yq-yuque-grey-900: #e2e2e2;
    --yq-yuque-grey-1: #141414;
    --yq-yuque-grey-2: #1f1f1f;
    --yq-yuque-grey-3: #292929;
    --yq-yuque-grey-4: #333;
    --yq-yuque-grey-5: #424242;
    --yq-yuque-grey-6: #505050;
    --yq-yuque-grey-7: #848484;
    --yq-yuque-grey-8: #b3b3b3;
    --yq-yuque-grey-9: #e2e2e2;
    --yq-pea-green-50: #1b2113;
    --yq-pea-green-100: #242f14;
    --yq-pea-green-200: #445625;
    --yq-pea-green-300: #557915;
    --yq-pea-green-400: #6a9226;
    --yq-pea-green-500: #7aad22;
    --yq-pea-green-600: #94bc4e;
    --yq-pea-green-700: #aed666;
    --yq-pea-green-800: #c2df90;
    --yq-pea-green-900: #d5e1c1;
    --yq-pea-green-1: #242f14;
    --yq-pea-green-2: #445625;
    --yq-pea-green-3: #557915;
    --yq-pea-green-4: #6a9226;
    --yq-pea-green-5: #7aad22;
    --yq-pea-green-6: #94bc4e;
    --yq-pea-green-7: #aed666;
    --yq-pea-green-8: #c2df90;
    --yq-pea-green-9: #d5e1c1;
    --yq-yuque-green-50: #10211a;
    --yq-yuque-green-100: #0e2f22;
    --yq-yuque-green-200: #255641;
    --yq-yuque-green-300: #18774f;
    --yq-yuque-green-400: #298e64;
    --yq-yuque-green-500: #29ad76;
    --yq-yuque-green-600: #51b88d;
    --yq-yuque-green-700: #6bd1a6;
    --yq-yuque-green-800: #97d8bc;
    --yq-yuque-green-900: #bce6d5;
    --yq-yuque-green-1: #0e2f22;
    --yq-yuque-green-2: #255641;
    --yq-yuque-green-3: #18774f;
    --yq-yuque-green-4: #298e64;
    --yq-yuque-green-5: #29ad76;
    --yq-yuque-green-6: #51b88d;
    --yq-yuque-green-7: #6bd1a6;
    --yq-yuque-green-8: #97d8bc;
    --yq-yuque-green-9: #bce6d5;
    --yq-cyan-50: #122324;
    --yq-cyan-100: #123436;
    --yq-cyan-200: #255356;
    --yq-cyan-300: #1a7074;
    --yq-cyan-400: #278b90;
    --yq-cyan-500: #22a8af;
    --yq-cyan-600: #4eb6bc;
    --yq-cyan-700: #76c2c6;
    --yq-cyan-800: #95d7db;
    --yq-cyan-900: #bfe1e3;
    --yq-cyan-1: #123436;
    --yq-cyan-2: #255356;
    --yq-cyan-3: #1a7074;
    --yq-cyan-4: #278b90;
    --yq-cyan-5: #22a8af;
    --yq-cyan-6: #4eb6bc;
    --yq-cyan-7: #76c2c6;
    --yq-cyan-8: #95d7db;
    --yq-cyan-9: #bfe1e3;
    --yq-blue-50: #15212d;
    --yq-blue-100: #193048;
    --yq-blue-200: #253c56;
    --yq-blue-300: #1d4672;
    --yq-blue-400: #245a94;
    --yq-blue-500: #2b6bb1;
    --yq-blue-600: #3b82ce;
    --yq-blue-700: #689fd9;
    --yq-blue-800: #8db5e2;
    --yq-blue-900: #b5d0ed;
    --yq-blue-1: #193048;
    --yq-blue-2: #253c56;
    --yq-blue-3: #1d4672;
    --yq-blue-4: #245a94;
    --yq-blue-5: #2b6bb1;
    --yq-blue-6: #3b82ce;
    --yq-blue-7: #689fd9;
    --yq-blue-8: #8db5e2;
    --yq-blue-9: #b5d0ed;
    --yq-sea-blue-50: #171c33;
    --yq-sea-blue-100: #1c2654;
    --yq-sea-blue-200: #252d56;
    --yq-sea-blue-300: #1b2a74;
    --yq-sea-blue-400: #243694;
    --yq-sea-blue-500: #2b41b1;
    --yq-sea-blue-600: #3b54ce;
    --yq-sea-blue-700: #687bd9;
    --yq-sea-blue-800: #8d9be2;
    --yq-sea-blue-900: #b5beed;
    --yq-sea-blue-1: #1c2654;
    --yq-sea-blue-2: #252d56;
    --yq-sea-blue-3: #1b2a74;
    --yq-sea-blue-4: #243694;
    --yq-sea-blue-5: #2b41b1;
    --yq-sea-blue-6: #3b54ce;
    --yq-sea-blue-7: #687bd9;
    --yq-sea-blue-8: #8d9be2;
    --yq-sea-blue-9: #b5beed;
    --yq-purple-50: #1f172e;
    --yq-purple-100: #2c1c4a;
    --yq-purple-200: #352259;
    --yq-purple-300: #3a1c73;
    --yq-purple-400: #512a98;
    --yq-purple-500: #5a2caf;
    --yq-purple-600: #7551b8;
    --yq-purple-700: #9677cf;
    --yq-purple-800: #b099db;
    --yq-purple-900: #cabce7;
    --yq-purple-1: #2c1c4a;
    --yq-purple-2: #352259;
    --yq-purple-3: #3a1c73;
    --yq-purple-4: #512a98;
    --yq-purple-5: #5a2caf;
    --yq-purple-6: #7551b8;
    --yq-purple-7: #9677cf;
    --yq-purple-8: #b099db;
    --yq-purple-9: #cabce7;
    --yq-magenta-50: #301525;
    --yq-magenta-100: #4e1837;
    --yq-magenta-200: #5b1f42;
    --yq-magenta-300: #701f4e;
    --yq-magenta-400: #942465;
    --yq-magenta-500: #a43776;
    --yq-magenta-600: #be4b8e;
    --yq-magenta-700: #ce78aa;
    --yq-magenta-800: #d996bd;
    --yq-magenta-900: #e6bcd5;
    --yq-magenta-1: #4e1837;
    --yq-magenta-2: #5b1f42;
    --yq-magenta-3: #701f4e;
    --yq-magenta-4: #942465;
    --yq-magenta-5: #a43776;
    --yq-magenta-6: #be4b8e;
    --yq-magenta-7: #ce78aa;
    --yq-magenta-8: #d996bd;
    --yq-magenta-9: #e6bcd5;
    --yq-red-50: #29191c;
    --yq-red-100: #402125;
    --yq-red-200: #5b2027;
    --yq-red-300: #741b25;
    --yq-red-400: #981f2d;
    --yq-red-500: #b62536;
    --yq-red-600: #ca3f4f;
    --yq-red-700: #cc7b84;
    --yq-red-800: #d49199;
    --yq-red-900: #e6bcc1;
    --yq-red-1: #402125;
    --yq-red-2: #5b2027;
    --yq-red-3: #741b25;
    --yq-red-4: #981f2d;
    --yq-red-5: #b62536;
    --yq-red-6: #ca3f4f;
    --yq-red-7: #cc7b84;
    --yq-red-8: #d49199;
    --yq-red-9: #e6bcc1;
    --yq-vermilion-50: #271c19;
    --yq-vermilion-100: #3c2520;
    --yq-vermilion-200: #5b2b20;
    --yq-vermilion-300: #742b1b;
    --yq-vermilion-400: #943824;
    --yq-vermilion-500: #b0432b;
    --yq-vermilion-600: #cd573c;
    --yq-vermilion-700: #d9816d;
    --yq-vermilion-800: #e09685;
    --yq-vermilion-900: #e9c2b9;
    --yq-vermilion-1: #3c2520;
    --yq-vermilion-2: #5b2b20;
    --yq-vermilion-3: #742b1b;
    --yq-vermilion-4: #943824;
    --yq-vermilion-5: #b0432b;
    --yq-vermilion-6: #cd573c;
    --yq-vermilion-7: #d9816d;
    --yq-vermilion-8: #e09685;
    --yq-vermilion-9: #e9c2b9;
    --yq-orange-50: #251e18;
    --yq-orange-100: #382a1e;
    --yq-orange-200: #5e3b1d;
    --yq-orange-300: #774418;
    --yq-orange-400: #9d571b;
    --yq-orange-500: #bc6820;
    --yq-orange-600: #d37f36;
    --yq-orange-700: #de9f68;
    --yq-orange-800: #e0af85;
    --yq-orange-900: #e9cfb9;
    --yq-orange-1: #382a1e;
    --yq-orange-2: #5e3b1d;
    --yq-orange-3: #774418;
    --yq-orange-4: #9d571b;
    --yq-orange-5: #bc6820;
    --yq-orange-6: #d37f36;
    --yq-orange-7: #de9f68;
    --yq-orange-8: #e0af85;
    --yq-orange-9: #e9cfb9;
    --yq-yellow-50: #241f15;
    --yq-yellow-100: #352d17;
    --yq-yellow-200: #564825;
    --yq-yellow-300: #775c18;
    --yq-yellow-400: #9c781c;
    --yq-yellow-500: #c29219;
    --yq-yellow-600: #d2a638;
    --yq-yellow-700: #e8be54;
    --yq-yellow-800: #efcf81;
    --yq-yellow-900: #eedeb4;
    --yq-yellow-1: #352d17;
    --yq-yellow-2: #564825;
    --yq-yellow-3: #775c18;
    --yq-yellow-4: #9c781c;
    --yq-yellow-5: #c29219;
    --yq-yellow-6: #d2a638;
    --yq-yellow-7: #e8be54;
    --yq-yellow-8: #efcf81;
    --yq-yellow-9: #eedeb4;
    --yq-bright-yellow-50: #242215;
    --yq-bright-yellow-100: #353117;
    --yq-bright-yellow-200: #564f25;
    --yq-bright-yellow-300: #776a18;
    --yq-bright-yellow-400: #9c8b1c;
    --yq-bright-yellow-500: #c2ac19;
    --yq-bright-yellow-600: #d2bd38;
    --yq-bright-yellow-700: #e8d454;
    --yq-bright-yellow-800: #efe081;
    --yq-bright-yellow-900: #eee7b4;
    --yq-bright-yellow-1: #353117;
    --yq-bright-yellow-2: #564f25;
    --yq-bright-yellow-3: #776a18;
    --yq-bright-yellow-4: #9c8b1c;
    --yq-bright-yellow-5: #c2ac19;
    --yq-bright-yellow-6: #d2bd38;
    --yq-bright-yellow-7: #e8d454;
    --yq-bright-yellow-8: #efe081;
    --yq-bright-yellow-9: #eee7b4;
    --yq-brown-50: #231e15;
    --yq-brown-100: #342a18;
    --yq-brown-200: #544327;
    --yq-brown-300: #624e2d;
    --yq-brown-400: #7e653a;
    --yq-brown-500: #967845;
    --yq-brown-600: #b29158;
    --yq-brown-700: #c2a87a;
    --yq-brown-800: #d2bf9d;
    --yq-brown-900: #e2d6c0;
    --yq-brown-1: #342a18;
    --yq-brown-2: #544327;
    --yq-brown-3: #624e2d;
    --yq-brown-4: #7e653a;
    --yq-brown-5: #967845;
    --yq-brown-6: #b29158;
    --yq-brown-7: #c2a87a;
    --yq-brown-8: #d2bf9d;
    --yq-brown-9: #e2d6c0;
    --yq-theme: #2ed790;
    --yq-normal: #424242;
    --yq-bg-primary: #141414;
    --yq-bg-secondary: #1f1f1f;
    --yq-bg-tertiary: hsla(0,0%,100%,0.04);
    --yq-bg-primary-hover: hsla(0,0%,100%,0.12);
    --yq-bg-primary-hover-light: #333;
    --yq-bg-foreground: #1f1f1f;
    --yq-bg-pre-foreground: #292929;
    --yq-bg-pre-secondary: #292929;
    --yq-bg-card-dialog: #292929;
    --yq-mask-bg: rgba(0,0,0,0.65);
    --yq-toast-bg: #292929;
    --yq-border-primary: hsla(0,0%,100%,0.12);
    --yq-border-primary-active: #29ad76;
    --yq-border-light: hsla(0,0%,100%,0.08);
    --yq-border-heavy: hsla(0,0%,100%,0.12);
    --yq-cardborder-hover: #253c56;
    --yq-cardborder-selected: #1d4672;
    --yq-sheetborder: hsla(0,0%,100%,0.12);
    --yq-text-primary: #e2e2e2;
    --yq-text-body: #b3b3b3;
    --yq-text-caption: #848484;
    --yq-text-disable: #505050;
    --yq-text-link: #3b82ce;
    --yq-text-link-hover: #245a94;
    --yq-icon-primary: #e2e2e2;
    --yq-icon-secondary: #b3b3b3;
    --yq-icon-caption: #848484;
    --yq-icon-disable: #505050;
    --yq-icon-hover: #424242;
    --yq-icon-colorbg: rgba(0,0,0,0.45);
    --yq-function-success: #51b88d;
    --yq-function-info: #3b82ce;
    --yq-function-warning: #d2a638;
    --yq-function-error: #ca3f4f;
    --yq-badge: #2b6bb1;
    --yq-popover-bg: #1f1f1f;
    --yq-disable-color: #505050
}

::-moz-selection {
    color: inherit;
    background: rgba(27,162,227,.2)
}

::selection {
    color: inherit;
    background: rgba(27,162,227,.2)
}

.larkui-icon {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.larkui-icon>* {
    line-height: 1
}

.larkui-icon svg {
    display: inline-block
}

.larkui-icon:before {
    display: none
}

.larkui-icon .larkui-icon-icon {
    display: block
}

@font-face {
    font-family: larkicon;
    src: url(https://at.alicdn.com/t/font_227976_pllep6r382.eot);
    src: url(https://at.alicdn.com/t/font_227976_pllep6r382.eot#iefix) format("embedded-opentype"),url(https://at.alicdn.com/t/font_227976_pllep6r382.woff2) format("woff2"),url(https://at.alicdn.com/t/font_227976_pllep6r382.woff) format("woff"),url(https://at.alicdn.com/t/font_227976_pllep6r382.ttf) format("truetype"),url(https://at.alicdn.com/t/font_227976_pllep6r382.svg#iconfont) format("svg")
}

@font-face {
    font-family: Chinese Quote;
    src: local("PingFang SC"),local("SimSun");
    unicode-range: u+2018,u+2019,u+201c,u+201d
}

.larkicon {
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    line-height: 1;
    text-rendering: auto
}

.larkicon:before {
    display: block;
    font-family: larkicon,sans-serif!important
}

.larkicon-contents:before {
    content: "\e952"
}

.larkicon-zoom:before {
    content: "\e961"
}

.larkicon-fullscreen:before {
    content: "\e95e"
}

.larkicon-insert:before {
    content: "\e976"
}

.larkicon-mail:before {
    content: "\e950"
}

.larkicon-publish:before {
    content: "\e963"
}

.larkicon-earth:before {
    content: "\e96d"
}

.larkicon-clock:before {
    content: "\e95b"
}

.larkicon-arrowsalt:before {
    content: "\e95f"
}

.larkicon-upload:before {
    content: "\e964"
}

.larkicon-lark:before {
    content: "\e974"
}

.larkicon-app:before {
    content: "\e954"
}

.larkicon-file:before {
    content: "\e957"
}

.larkicon-diamond:before {
    content: "\e965"
}

.larkicon-private:before {
    content: "\e962"
}

.larkicon-group:before {
    content: "\e968"
}

.larkicon-download:before {
    content: "\e975"
}

.larkicon-storehouse:before {
    content: "\e955"
}

.larkicon-team:before {
    content: "\e956"
}

.larkicon-enterprise:before {
    content: "\e966"
}

.larkicon-document:before {
    content: "\e987"
}

.larkicon-setting:before {
    content: "\e953"
}

.larkicon-delete:before {
    content: "\e951"
}

.larkicon-write:before {
    content: "\e963"
}

.larkicon-save:before {
    content: "\e6a1"
}

.larkicon-checkbox:before {
    content: "\e6a2"
}

.larkicon-check-o:before {
    content: "\e98d"
}

.larkicon-error:before {
    content: "\e692"
}

.larkicon-line:before {
    content: "\e93d"
}

.larkicon-eye:before {
    content: "\e973"
}

.larkicon-lock:before {
    content: "\ea3b"
}

.larkicon-unlock:before {
    content: "\e96c"
}

.larkicon-arrow-down:before {
    content: "\e983"
}

.larkicon-arrow-down-sm:before {
    content: "\e983";
    transform: scale(.86)
}

.larkicon-arrow-up:before {
    content: "\e983";
    transform: rotate(180deg)
}

.larkicon-arrow-right:before {
    content: "\e95c"
}

.larkicon-arrow-left:before {
    content: "\e95c";
    transform: rotate(180deg) translateY(1px)
}

.larkicon-add:before {
    content: "\e627"
}

.larkicon-draft:before {
    content: "\e972"
}

.larkicon-search:before {
    content: "\e97f"
}

.larkicon-rank:before {
    content: "\e6a5"
}

.larkicon-bold:before {
    content: "\e694"
}

.larkicon-italic:before {
    content: "\e6a0"
}

.larkicon-through:before {
    content: "\e69b"
}

.larkicon-quote:before {
    content: "\e69e"
}

.larkicon-underline:before {
    content: "\e93f"
}

.larkicon-next:before {
    content: "\e69c"
}

.larkicon-back:before {
    content: "\e691"
}

.larkicon-picture:before {
    content: "\e69a"
}

.larkicon-attachment:before {
    content: "\e690"
}

.larkicon-link:before {
    content: "\e697"
}

.larkicon-code:before {
    content: "\e695"
}

.larkicon-bars:before {
    content: "\e6a5"
}

.larkicon-addcomment:before {
    content: "\e95d"
}

.larkicon-comments:before {
    content: "\e98e"
}

.larkicon-comments-o:before {
    content: "\e98f"
}

.larkicon-plus:before {
    content: "\ea38"
}

.larkicon-cross:before {
    content: "\e984";
    transform: rotate(45deg)
}

.larkicon-close:before {
    content: "\e982"
}

.larkicon-notification:before {
    content: "\e9d8"
}

.larkicon-user:before {
    content: "\e96e"
}

.larkicon-more:before {
    content: "\e944"
}

.larkicon-tips:before {
    content: "\e971"
}

.larkicon-list:before {
    content: "\e6a6"
}

.larkicon-grid:before {
    content: "\e97e"
}

.larkicon-star:before {
    content: "\e970"
}

.larkicon-complete:before {
    content: "\e96b"
}

.larkicon-user-h:before {
    content: "\e959"
}

.larkicon-star-o:before {
    content: "\e76b"
}

.larkicon-like:before {
    content: "\e969"
}

.larkicon-like-o:before {
    content: "\e96a"
}

.larkicon-minus-o:before {
    content: "\e978"
}

.larkicon-plus-o:before {
    content: "\e979"
}

.larkicon-organization:before {
    content: "\e94e"
}

.larkicon-location:before {
    content: "\e98a"
}

.larkicon-job:before {
    content: "\e619"
}

.larkicon-plus-square:before {
    content: "\e988"
}

.larkicon-minus-square:before {
    content: "\e977"
}

.larkicon-top:before {
    content: "\e97a"
}

.larkicon-successful:before {
    content: "\e97b"
}

.larkicon-exclamation:before {
    content: "\e960"
}

.larkicon-exclamation-circle:before {
    content: "\e62d"
}

.larkicon-read:before {
    content: "\e98c"
}

.larkicon-eye-2:before {
    content: "\e640"
}

.larkicon-download-2:before {
    content: "\e63e"
}

.larkicon-triangle-up:before {
    content: "\e642"
}

.larkicon-triangle-up-sw:before {
    content: "\e642";
    transform: scale(.5)
}

.larkicon-triangle-down:before {
    content: "\e641"
}

.larkicon-triangle-down-sw:before {
    content: "\e641";
    transform: scale(.5)
}

.larkicon-triangle-right:before {
    content: "\e642";
    transform: rotate(90deg)
}

.larkicon-triangle-right-sw:before {
    content: "\e642";
    transform: rotate(90deg) scale(.5)
}

.larkicon-cleanup:before {
    content: "\e6ca"
}

.larkicon-topics:before {
    content: "\e997"
}

.larkicon-topics-open:before {
    content: "\e999"
}

.larkicon-topics-closed:before {
    content: "\e998"
}

.larkicon-subscribe:before {
    content: "\e677"
}

.larkicon-edit:before {
    content: "\e75c"
}

.larkicon-file-add:before {
    content: "\e798"
}

.larkicon-zoom-x:before {
    content: "\e9a9"
}

.larkicon-zoom-y:before {
    content: "\e9a8"
}

.larkicon-addcomments:before {
    content: "\e9aa"
}

.larkicon-hotmap:before {
    content: "\e9a2"
}

.larkicon-slice:before {
    content: "\e9bf"
}

.larkicon-measure:before {
    content: "\e9ca"
}

.larkicon-hotspot:before {
    content: "\e9c2"
}

.larkicon-areacomments:before {
    content: "\e9c5"
}

.larkicon-more-h:before {
    content: "\e9c9"
}

.larkicon-comment-reply:before {
    content: "\e6c7"
}

.larkicon-topic-sharp:before {
    content: "\e9ab"
}

.larkicon-edit-contents:before {
    content: "\e9ac"
}

.larkicon-symbol:before {
    content: "\e9ba"
}

.larkicon-upload-file:before {
    content: "\e6cf"
}

.larkicon-editor-back:before {
    content: "\e9c0"
}

.larkicon-launch:before {
    content: "\e9cb"
}

.larkicon-share:before {
    content: "\e768"
}

.larkicon-sharing:before {
    content: "\e9cd"
}

.larkicon-info:before {
    content: "\e629"
}

.larkicon-logout:before {
    content: "\e65a"
}

.larkicon-sort:before {
    content: "\e9a8"
}

.larkicon-public:before {
    content: "\e9cf"
}

.larkicon-dashboard:before {
    content: "\e9d0"
}

.larkicon-help:before {
    content: "\e63c"
}

.larkicon-admin:before {
    content: "\e9d2";
    transform: scale(.86)
}

.larkicon-quit:before {
    content: "\e9d3";
    transform: scale(.86)
}

.larkicon-filter:before {
    content: "\e6b6"
}

.larkicon-toc:before {
    content: "\e6bb"
}

.larkicon-tocclose:before {
    content: "\e6bc"
}

.larkicon-space-public:before {
    content: "\e9d7"
}

.larkicon-space-enterprise:before {
    content: "\e9d5"
}

.larkicon-theme-checked:before {
    content: "\e998"
}

.larkicon-topic-pinned:before {
    content: "\e63e";
    transform: rotate(180deg)
}

.larkicon-back-to-list:before {
    content: "\ea21"
}

.larkicon-back-to-team:before {
    content: "\ea23"
}

.larkicon-topic-close:before {
    content: "\ea06"
}

.larkicon-topic-reopen:before {
    content: "\ea07"
}

.larkicon-board:before {
    content: "\ea0a"
}

.larkicon-label:before {
    content: "\ea0b"
}

.larkicon-disdain:before {
    content: "\ea0c"
}

.larkicon-smile:before {
    content: "\ea0d"
}

.larkicon-kitchen:before {
    content: "\ea1a"
}

.larkicon-shortcut-collection:before {
    content: "\e970";
    color: #ffc53d
}

.larkicon-shortcut-heart:before {
    content: "\e6d0";
    color: #ff4d4f
}

.larkicon-shortcut-subscribe:before {
    content: "\ea26";
    color: #2495ff
}

.larkicon-shortcut-topics:before {
    content: "\ea27";
    color: #597ef7
}

.larkicon-new-headlines:before {
    content: "\ea28"
}

.larkicon-event-follow:before {
    content: "\e76d";
    color: #3bd17c
}

.larkicon-event-like:before {
    content: "\ea20";
    color: #ff5c5f
}

.larkicon-event-wheat:before {
    content: "\ea22";
    color: #ffc53d
}

.larkicon-event-watch:before {
    content: "\ea1f";
    color: #2495ff
}

.larkicon-move:before {
    content: "\ea70"
}

.larkicon-copy:before {
    content: "\e9bc"
}

.larkicon-copy-to:before {
    content: "\ea6f"
}

.larkicon-swap:before {
    content: "\e643"
}

.larkicon-group-lock:before {
    content: "\e757"
}

.larkicon-group-avatar:before {
    content: "\e758"
}

.larkicon-bell:before {
    content: "\e602"
}

.larkicon-header-new:before {
    content: "\e601"
}

.larkicon-members:before {
    content: "\e7e7"
}

.larkicon-book:before {
    content: "\e789"
}

.larkicon-share-earth:before {
    content: "\e681"
}

.larkicon-hotkeys:before {
    content: "\ea08"
}

.larkicon-permission-lock:before {
    content: "\ea3b"
}

.larkicon-permission-lock-o:before {
    content: "\e784"
}

.larkicon-permission-eye:before {
    content: "\ea3a"
}

.larkicon-permission-eye-o:before {
    content: "\e80c"
}

.larkicon-archive:before {
    content: "\ea3c"
}

.larkicon-group-member:before {
    content: "\ea3d"
}

.larkicon-menu:before {
    content: "\e755"
}

.larkicon-fold:before {
    content: "\ea3e"
}

.larkicon-docbook:before {
    content: "\ea16";
    color: #444f59
}

.larkicon-artboard:before {
    content: "\ea15";
    color: #40a9ff
}

.larkicon-sheetsbook:before {
    content: "\ea14";
    color: #25b864
}

.larkicon-doc:before {
    content: "\e957"
}

.larkicon-sheet:before {
    content: "\ea19"
}

.larkicon-sheet-primary:before {
    content: "\ea19";
    color: #25b864
}

.larkicon-doc-app:before {
    content: "\ea5d"
}

.larkicon-sheet-app:before {
    content: "\ea19"
}

.larkicon-list-view:before {
    content: "\ea17"
}

.larkicon-grid-view:before {
    content: "\ea18"
}

.larkicon-department:before {
    content: "\e759"
}

.larkicon-subordinate:before {
    content: "\e753"
}

.larkicon-cursor:before {
    content: "\ea39"
}

.larkicon-linkto:before {
    content: "\e7ee"
}

.larkicon-bar-chart:before {
    content: "\e7af"
}

.larkicon-up:before {
    content: "\ea1c"
}

.larkicon-down:before {
    content: "\ea1b"
}

.larkicon-book-card:before {
    content: "\e731"
}

.larkicon-book-summary:before {
    content: "\e730"
}

.larkicon-book-list:before {
    content: "\e60e"
}

.larkicon-book-pic:before {
    content: "\e606"
}

.larkicon-doc-detail:before {
    content: "\e608"
}

.larkicon-doc-basic:before {
    content: "\e60b"
}

.larkicon-fire:before {
    content: "\e60a"
}

.larkicon-vertical:before {
    content: "\e60d"
}

.larkicon-horizontal:before {
    content: "\e60c"
}

.edit-icon {
    position: relative;
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    line-height: 1
}

.edit-icon:before {
    display: block;
    font-family: larkicon,sans-serif!important
}

.edit-icon-bold:before {
    content: "\e694";
    transform: scale(.9)
}

.edit-icon-link:before {
    content: "\e697"
}

.edit-icon-code:before {
    content: "\e695"
}

.edit-icon-eyes:before {
    content: "\e973";
    transform: scale(.9)
}

.edit-icon-eyes-slash:before {
    content: "\e973"
}

.edit-icon-h1:before {
    content: "\e696"
}

.edit-icon-h2:before {
    content: "\e6a7"
}

.edit-icon-h3:before {
    content: "\e6a9"
}

.edit-icon-image:before {
    content: "\e69a"
}

.edit-icon-italic:before {
    content: "\e6a0";
    transform: scale(.9)
}

.edit-icon-oList:before {
    content: "\e6a6"
}

.edit-icon-uList:before {
    content: "\e6a5"
}

.edit-icon-tList:before {
    content: "\e6a2"
}

.edit-icon-redo:before {
    content: "\e69c"
}

.edit-icon-undo:before {
    content: "\e691"
}

.edit-icon-quote:before {
    content: "\e69e"
}

.edit-icon-del:before {
    content: "\e69b";
    transform: scale(.9)
}

.edit-icon-attachment:before {
    content: "\e690"
}

.edit-icon-hr:before {
    content: "\e93d"
}

.edit-icon-table:before {
    content: "\e69f"
}

.edit-icon-save:before {
    content: "\e6a1"
}

.edit-icon-pdf:before {
    content: "\e636"
}

.edit-icon-video:before {
    content: "\e6a3"
}

.edit-icon-emoji:before {
    content: "\e6c9"
}

.larkicon-svg-check {
    display: block;
    width: 11px;
    height: 9px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/OvLdkKwArTJKCMxndXbO.svg) no-repeat 50%
}

.larkicon-svg-check-blue {
    display: block;
    width: 14px;
    height: 11px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/uvYlejSGEMoXjpQFmtxp.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-asset {
    display: block;
    width: 18px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/JZJTDvlUkhjQNMyTvQFR.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-group-lock {
    display: block;
    width: 14px;
    height: 14px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LcFKqQaIWEfEGOsEWcyq.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-group-intranet {
    display: block;
    width: 14px;
    height: 14px;
    background: url(https://gw.alipayobjects.com/zos/basement_prod/2e32b49e-7d1c-44e0-8743-d487f15cccd3.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-toplines {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/BEvCYPkRACgoeUBwupav.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-toplines-o {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/uRqNsoHnGyBMUfDTHymD.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-groups {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/grDGDjMhcIBxndgBzbIc.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-groups-o {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/EvBXjsQGWEyqHtvEEOmd.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-knowledges {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/nIgSuCzFZrQVTaPKaPjo.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-explore-knowledges-o {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/RRepkaYrykMzalaySygw.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-file-doc {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/TscdKjyuTOHNAmegoizz.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-file-artboard {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/dWBdhAhvftROoQPxwfGQ.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-file-sheet {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/DssHOIWjbBfhYeHLCKaq.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-doc {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/EUSInMsrjBczCIkrMBnr.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-artboard {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/UhXAKkByeKQZHddCaQmW.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-sheet {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/zDFkQSAWCQZaLZbVCxfy.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-doc-private {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/XbyfwGZYHQtIfrytznIO.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-artboard-private {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/dpxMcNHUOVkijrOVVOHi.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-repo-sheet-private {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/IUlzuhOXDbYDgvkHzDCR.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-group {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*_BDhR52Eec4AAAAAAAAAAABjAQAAAQ/original) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-koji {
    display: block;
    width: 42px;
    height: 13px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/CQQUpPramUPjPoPzGfzS.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-earth {
    display: block;
    width: 32px;
    height: 32px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/CZZPgYfmDpHnPajGsozC.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-earth-green {
    display: block;
    width: 32px;
    height: 32px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/MbUAkZfNTIbrbXEKgpTE.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-group-members {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LcQmqEXzBqREnMSbotFF.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-group-home {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/RerOeQKsNsKaTMnyoSEq.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-doc {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LVRBJVaLOXQqgCzvgovy.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-sheet {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/nUUWnJcaCTrIBCtEcFbk.svg) no-repeat 50%;
    background-size: 100%
}

.larkicon-svg-artboard {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/SUUUinUjkyKgxPYJJnkL.svg) no-repeat 50%;
    background-size: 100%
}

@font-face {
    font-family: webfont;
    src: url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.eot);
    src: url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.eot#iefix) format("embedded-opentype"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.woff) format("woff"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.ttf) format("truetype"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.svg#FZQKBYSJW--GB1-0) format("svg")
}

@media only screen and (min-width: 1600px) {
    .lark .main-typo {
        width:960px;
        padding: 90px 100px
    }

    .lark .main-meta,.lark .main-meta-item .directory-affixed {
        right: calc(50% - 720px)
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: none
    }
}

@media only screen and (min-width: 1201px) and (max-width:1279px) {
    .lark .doc-presenter {
        padding-left:0
    }

    .lark .main-wrapper-editor .editor-tool-list-group-hide {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: block
    }

    .lark .main-wrapper-editor .editor-list {
        width: 180px
    }
}

@media only screen and (min-width: 1024px) and (max-width:1200px) {
    .lark .header-container,.lark .main-cover,.lark .main-crumb-mid,.lark .main-wrapper-group .group {
        width:1024px;
        padding-left: 24px;
        padding-right: 24px
    }

    .lark .doc-presenter {
        padding-left: 0!important
    }

    .lark .main-wrapper-editor .editor-main-wrapper {
        padding-left: 0;
        padding-right: 0
    }

    .lark .main-wrapper-editor .editor-tool-list-group {
        padding-left: 10px;
        padding-right: 10px
    }

    .lark .main-wrapper-editor .editor-list {
        width: 180px
    }

    .lark .main-wrapper-editor .editor-tool-list-group-hide {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: block
    }
}

@media only screen and (min-width: 848px) and (max-width:1023px) {
    .lark .main-wrapper-editor .editor-main-wrapper-middle {
        width:100%;
        max-width: 800px
    }

    .lark .main-wrapper-editor .editor-list {
        display: none
    }

    .lark .main-wrapper-editor .editor-wrapper {
        margin-left: 0
    }

    .lark .main-wrapper-editor .editor-tool-list-group-hide {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: block
    }
}

@media only screen and (max-width: 847px) {
    .lark .main .books,.lark .main .users {
        width:100%;
        padding-left: 5%;
        padding-right: 5%
    }

    .lark .main .books-item:nth-child(5n),.lark .main .users-item:nth-child(5n) {
        margin-right: 30px
    }

    .lark .doc {
        width: 100%;
        padding-top: 0;
        padding-left: 0;
        padding-right: 0
    }

    .lark .typo-catalog-empty {
        width: 100%
    }

    .lark .header-nav-item {
        padding-left: 5px
    }

    .lark .header-nav-item-text a {
        padding: 0 6px
    }

    .lark .main-wrapper-group .group-action {
        display: none
    }

    .lark .main .editor-tool-wrapper-middle {
        width: auto;
        text-align: left;
        padding-left: 12px
    }

    .lark .main-wrapper-editor .editor-tool .editor-tool-icon {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group {
        padding-left: 0;
        padding-right: 0
    }

    .lark .main-wrapper-editor .editor-tool-list-group:after {
        border-right: 1px solid transparent
    }

    .lark .main-wrapper-editor .editor-tool .editor-tool-icon-h1,.lark .main-wrapper-editor .editor-tool .editor-tool-icon-h2,.lark .main-wrapper-editor .editor-tool .editor-tool-icon-h3,.lark .main-wrapper-editor .editor-tool .editor-tool-icon-image,.lark .main-wrapper-editor .editor-tool .editor-tool-icon-save,.lark .main-wrapper-editor .editor-tool .editor-tool-icon-tList {
        display: inline-block
    }

    .lark .main .editor-main-wrapper-middle,.lark .main .editor-paper-wrapper {
        width: 100%
    }

    .lark .main-wrapper-editor .editor-list {
        display: none
    }

    .lark .main-wrapper-editor .editor-wrapper {
        margin-left: 0
    }

    .lark .main-wrapper-editor .editor-main-wrapper {
        padding: 0
    }

    .lark .main-wrapper-editor .editor-paper {
        padding: 30px
    }

    .lark .main-wrapper-editor .editor-tool-list-group-hide {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: block
    }

    .lark .explore-body .categories-item {
        width: 100%
    }
}

@media only screen and (max-width: 768px) {
    .lark .ant-card {
        margin-bottom:10px
    }
}

@media only screen and (max-width: 420px) {
    .lark .header-nav-item-text {
        display:none
    }

    .lark .header .logo {
        margin-right: auto
    }

    .lark .header .logo .logo-link .text {
        max-width: 92px
    }

    .lark .main .books,.lark .main .users {
        padding-top: 32px
    }

    .lark .main .books-item,.lark .main .users-item {
        float: none;
        margin: 0 auto 32px auto
    }

    .lark .main .books .link,.lark .main .users-item .link {
        margin: 0 auto
    }

    .lark .main-wrapper-book .book .book-info {
        margin-bottom: 16px
    }

    .lark .main-wrapper-book .book .book-action {
        left: 0;
        margin-top: -8px
    }

    .lark .main-wrapper-book .book .favor-wrapper>.btn-group-info {
        margin-left: 0;
        margin-right: 8px
    }

    .lark .main-wrapper-book .book .doc-create-entry {
        float: right;
        margin-left: 0
    }

    .lark .main-wrapper-book .book .doc-create-entry .ant-dropdown-trigger,.lark .main-wrapper-book .book .doc-create-entry .larkicon {
        display: none
    }

    .lark .main-wrapper-book .book .doc-create-entry .ant-btn-primary {
        border-bottom-right-radius: 4px;
        border-top-right-radius: 4px
    }

    .lark .doc {
        padding-left: 0;
        padding-right: 0
    }

    .lark .main .books-item:nth-child(5n),.lark .main .users-item:nth-child(5n) {
        margin: 0 auto 32px auto
    }

    .lark .main-wrapper-doc .header-crumb .doc-title,.lark .main-wrapper-editor .editor-list,.lark .typo-catalog-detail li a .slug {
        display: none
    }

    .lark .main-wrapper-editor .editor-wrapper {
        margin-left: 0
    }

    .lark .main-wrapper-editor .editor-tool-list-group-hide {
        display: none
    }

    .lark .main-wrapper-editor .editor-tool-list-group-show {
        display: block
    }
}

@media only screen and (max-width: 992px) {
    .main-wrapper-editor .editor-wrapper {
        margin-left:180px
    }

    .main-wrapper-editor .editor-wrapper.editor-wrapper-share {
        margin-left: 0
    }
}

.layout-container {
    max-width: 1056px;
    height: 100%
}

.layout-container,.layout-container.layout-container-full {
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto
}

.layout-container.layout-container-full {
    max-width: 100%
}

.layout-container-wider {
    max-width: 1440px
}

.layout-container-middle,.layout-container-wider {
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    height: 100%
}

.layout-container-middle {
    max-width: 1216px
}

.layout-container-main {
    padding-top: 24px;
    padding-bottom: 32px
}

.lark {
    position: relative;
    background: #fff;
    background: var(--yq-bg-primary);
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column
}

.main-wrapper {
    flex: 1 0;
    width: 100%
}

.main-container {
    max-width: 1056px;
    padding-top: 22px
}

.main-container,.main-container-wider {
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    padding-bottom: 32px
}

.main-container-wider {
    max-width: 1440px;
    padding-top: 24px
}

body {
    width: 100%;
    background-color: #fff;
    background-color: var(--yq-bg-primary);
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    overflow-y: auto
}

body>script+svg {
    position: absolute
}

@media only screen and (max-width: 1199px) {
    body {
        overflow:auto
    }
}

@supports (width: 100vw) {
    body {
        width:100vw
    }
}

html body {
    color: #262626;
    color: var(--yq-yuque-grey-900);
    font-family: Chinese Quote,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 14px
}

html {
    width: 100%
}

@supports (width: 100vw) {
    html {
        width:100vw!important
    }
}

html[data-kumuhana=pouli],html[data-kumuhana=pouli] body {
    background-color: #fff;
    background-color: var(--yq-bg-primary);
    color: #262626;
    color: var(--yq-yuque-grey-900);
    --csstools-color-scheme--dark: initial;
    color-scheme: dark
}

h1,h2,h3,h4,h5,h6 {
    margin-bottom: 0
}

a:focus {
    -webkit-text-decoration: none;
    text-decoration: none
}

ol,ul {
    margin: 0;
    padding: 0;
    list-style: none
}

p {
    margin-bottom: 0
}

.clearfix {
    display: block;
    zoom:1}

.clearfix:after {
    content: " ";
    display: block;
    font-size: 0;
    height: 0;
    clear: both;
    visibility: hidden
}

.hidden {
    display: none
}

.space-word {
    margin-left: 6px;
    margin-right: 6px
}

.blankslate {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 46px 16px;
    text-align: center
}

.blankslate,.blankslate .ant-spin {
    color: rgba(0,0,0,.45);
    color: var(--yq-ant-text-color-secondary)
}

.lark-btn-text {
    border: none;
    padding: 0;
    background: none;
    height: auto
}

.btn-group-info>.info {
    display: inline-block;
    vertical-align: top;
    padding: 0 10px;
    height: 32px;
    line-height: 30px;
    font-size: 14px;
    text-align: center;
    background-color: #fff;
    border: 1px solid #e7e9e8;
    border: 1px solid var(--yq-border-primary);
    border-left: 0;
    border-radius: 0 6px 6px 0;
    color: #262626;
    color: var(--yq-yuque-grey-900)
}

.btn-group-info>.info:hover {
    color: #00b96b;
    color: var(--yq-yuque-green-600)
}

.btn-group-info>.info:focus {
    color: #009456;
    color: var(--yq-yuque-green-700)
}

.default-back-top.ant-back-top {
    right: 24px;
    bottom: 96px;
    width: 42px;
    height: 42px;
    cursor: default
}

.default-back-top.ant-back-top .ant-fade {
    cursor: pointer
}

.default-back-top.ant-back-top .ant-fade-leave {
    display: none
}

.default-back-top.ant-back-top .default-back-top-btn {
    width: 42px;
    height: 42px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-radius: 50%;
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04);
    background: #fafafa;
    background: var(--yq-bg-secondary);
    color: #585a5a;
    color: var(--yq-yuque-grey-800);
    font-size: 20px
}

.default-back-top.ant-back-top .default-back-top-btn:hover {
    background: #eff0f0;
    background: var(--yq-bg-primary-hover)
}

#__SVG_SPRITE_NODE__ {
    opacity: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ant-legacy-form .ant-legacy-form-item-label>label {
    color: #585a5a;
    color: var(--yq-text-body)
}

.lark_doc_embed {
    background-color: #fff;
    background-color: var(--yq-bg-primary)
}
