<template>
  <div class="ReaderLayout-module_wrapper_l-8F+" style="padding-left: 276px;"><div id="headerOverlayContainer" class="ViewerHeader-module_headerOverlayContainer_30mty"></div><div class="BookReader-module_wrapper_s6Jdt BookReader-module_docTypographyClassic_tUB5r" data-testid="content"><div class="BookReader-module_content_BGKYX" id="main"><div class="BookReader-module_docContainer_mQ3Tk"><div class="DocReader-module_wrapper_t3Z8X" data-doc-layout="fixed" data-doc-sidebar="false"><div class=""><div id="doc-reader-content" class="DocReader-module_content_AcIMy "><div class="DocReader-module_header_xAOtU"><div><div class="DocReader-module_title_fXOQi"><h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title">规则编写</h1></div></div></div><div><article id="content" class="article-content" tabindex="0" style="outline-style: none;"><div class="ne-doc-major-viewer"><div class="yuque-doc-content" data-df="lake" style="position: relative;"><div><div class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed" data-viewer-mode="normal" id="u04a3"><div class="ne-viewer-header"><button type="button" class="ne-ui-exit-max-view-btn" style="background-image:url(https://gw.alipayobjects.com/zos/bmw-prod/09ca6e30-fd03-49ff-b2fb-15a2fbd8042a.svg)">返回文档</button></div><div class="ne-viewer-body"><ne-p id="u84303d22" data-lake-id="u84303d22"><ne-text id="u11ac5890">规则编写在规则集中完成，规则集包含一系列规则库，在erule中是由一组普通规则和循环规则构成的规则集合，是使用频率最高、使用方式最为灵活的一种业务规则实现方式。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u5a3e6711" data-lake-id="u5a3e6711"><ne-text id="ua1023856">在erule中规则集有两种类型：一种是普通规则；一种是决策表。所谓的普通规则是指一种由预定义、如果、那么、否则四个部分构成的规则，其中预定义就是可循环的规则，它允许指定一个集合类型的对象，对这个集合中每个对象进行循环迭代，在循环体加入的普通规则相当于如果中加入条件。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u206ab577" data-lake-id="u206ab577"><ne-text id="u8248d72f">规则的定义采用向导方式，一步一步通过鼠标点击就可以完成其中的普通规则的配置，配合高度可视化的向导式规则设计器，可以最大限度将业务规则可视化，降低规则配置的复杂度。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u69efaf0c" data-lake-id="u69efaf0c"><ne-text id="udf745b99">打开erule主界面，在规则库管理节点选择创建一个规则库，可以看到规则库中包含普通规则和决策表，在界面中可对一系列规则进行修改。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u631b5a35" data-lake-id="u631b5a35"><ne-card data-card-name="image" data-card-type="inline" id="u502fa64f" data-event-boundary="card" class="ne-spacing-all"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1536" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743389953292-c693f0a6-8806-4293-abdd-6267534bbe9e.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p></div><div class="ne-inner-overlay-container"><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay"><div class="count-tips-module_tipsContainer___5agc"></div></ne-overlay-tmp></div></div><div style="height: 0px; overflow: hidden;">​</div></div></div></div></article></div></div><div></div><div></div></div><div></div></div></div></div></div></div>
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>