<template>
  <div class="action">
    <!-- 动作类型选择组件 -->
    <ActionTypeCom
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :value="actionType"
      @onChange="changeActionComType"
    />
    <div class="action-content-highlight common-highlight">
      <!-- 根据动作类型显示相应的组件 -->
      <SetValueCom
        v-if="actionType === 'setValue'"
      :key="pos"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :actionData="actionParams"
      @onChange="onSetValueComChange"
    />

    <ActionMethod
      v-if="actionType === 'invokeMethod'"
      :key="pos"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :dataSource="actionParams[0]"
      :hideFrontBtn="true"
      :hideEndBtn="true"
      :signValue="''"
      @onChange="onMehtodChange"
      ref="action_method_com"
    />

    <!-- 添加动作项按钮 -->
    <span
      v-if="!locked && !isTrack"
      class="iconContainer iconfontbtn icon-add"
      style="margin-top: 0px; line-height: 16px; font-size: 14px"
      @click="handleAddActionItem"
      role="button"
      tabindex="-1"
    />

    <!-- 动作操作框 -->
    <ActionOperationBox v-if="!locked && !isTrack" @onChange="actionHandler" />

    <!-- 验证图标 -->
    <a-tooltip v-if="validate === true" :title="msg">
      <CheckCircleOutlined class="icon-validate" :style="{ color: 'green' }" />
    </a-tooltip>
    <a-tooltip v-if="validate === false" :title="msg">
      <CloseCircleOutlined class="icon-validate" :style="{ color: 'red' }" />
    </a-tooltip>
  </div>
  </div>
</template>

<script setup>
import { cloneDeep } from "lodash";
import * as util from "@/components/ruleEditCom/utils/util";

const { ActionGenerate } = util;

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String
  },
  actionData: {
    type: Object,
    default: () => ({}),
  },
  actionValid: {
    type: Object,
    default: () => ({}),
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits([
  "addActionItem",
  "deleteActionItem",
  "replaceActionItem",
  "onChange",
]);
const validate = ref(null);
const msg = ref("");
const actionParams = ref([]);
const actionType = ref("");

const init = () => {
  const { actionType: acType, actionParams: actPar = [] } = props.actionData;
  const { validate: val, msg: msgText } = props.actionValid;
  validate.value = val;
  msg.value = msgText;
  actionParams.value = actPar;
  actionType.value = acType;
};
// 监听 actionData 的变化
watch(
  () => props,
  () => {
    init();
  },
  { deep: true, immediate: true }
);

// 处理动作类型变化
const changeActionComType = (pos,selectOptions) => {
  let newActionData;
  const oldRowData = cloneDeep(props.actionData);
  const { value } = selectOptions[0];
  const initSetValueAction = {
    actionId: props.actionData.actionId,
    actionType: "setValue",
    actionParams: [
      {
        variableType: "field",
        valueType: "String",
        value: [],
      },
      {
        variableType: "constant",
        valueType: "String",
        value: "",
      },
    ],
  };
  const initInvokeMethodActionData = {
    actionId: props.actionData.actionId,
    actionType: "invokeMethod",
    actionParams: [
      {
        variableType: "field",
        valueType: "String",
        value: [],
      },
    ],
  };
  if (value === "invokeMethod") {
    newActionData = new ActionGenerate(initInvokeMethodActionData);
  }
  if (value === "setValue") {
    newActionData = new ActionGenerate(initSetValueAction);
  }
  emit("onChange", pos, newActionData, oldRowData);
};

// 处理设置值组件变化
const onSetValueComChange = (_pos, _data) => {
  const oldRowData = cloneDeep(props.actionData);
  const newActionData = {
    actionId: props.actionData.actionId,
    actionType: "setValue",
    actionParams: _data,
  };
  emit("onChange", _pos, newActionData, oldRowData);
};

// 处理方法组件变化
const onMehtodChange = (_pos, _data) => {
  const oldRowData = cloneDeep(props.actionData);
  const newActionData = {
    actionId: props.actionData.actionId,
    actionType: "invokeMethod",
    actionParams: [_data],
  };
  emit("onChange", _pos, newActionData, oldRowData);
};

// 处理动作操作
const actionHandler = (type) => {
  switch (type) {
    case "delete":
      emit("deleteActionItem", props.pos);
      break;
    case "replaceItem":
      emit("replaceActionItem", props.pos);
      break;
  }
};

// 处理添加动作项
const handleAddActionItem = () => {
  emit("addActionItem", props.pos);
};
// 初始化组件
onMounted(() => {
  init();
});
</script>

<style scoped>
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}
.action {
  display: flex;
  align-items: center;
  line-height: 30px;
  margin-bottom: 5px;
  min-height: 30px;
  border: 1px dotted transparent;
  transition: all 0.2s ease;
  padding: 4px;
  border-radius: 3px;
}

.iconContainer {
  margin-left: 5px;
  cursor: pointer;
}
</style>
