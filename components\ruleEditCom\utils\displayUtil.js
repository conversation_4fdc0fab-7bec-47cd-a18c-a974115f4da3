
export function findLabel (options = [], value) {
  let label = '';
  value && value[0] && options && options.forEach(item => {
    if (item.value === value[0]) {
      label = item.label;
    }
  });
  return label;
};

export function findViewName (options = [], value) {
  let viewName = '';
  value && options && options.forEach(item => {
    if (item.value === value) {
      viewName = item.viewName;
    }
  });
  return viewName;
};

// 方法：通过value数组获取label数组
export function getLabelArrByValues (data = [], value = [], labelArr = []) {
  
  const newValueArr = [...value];
  const targetValue = newValueArr.splice(0, 1)[0];
  data.map(item => {
    if (item.value === targetValue) {
      const labelObj = {
        name: item.label || '',
        valueType: item.valueType
      };

      if (item.isFieldItem) {
        labelObj.nameType = 'field';
      } else if (item.isDatasetItem) {
        labelObj.nameType = 'dataset';
      } else {
        labelObj.nameType = 'method';
      }

      labelArr.push(labelObj);

      if (newValueArr.length > 0) {
        getLabelArrByValues(item.children, newValueArr, labelArr);
      }
    }
  });
  return labelArr;
}

// 方法：通过value获取label
export function getLabelByValue (data, value, label = '') {
  const newValueArr = [...value];
  const targetValue = newValueArr.splice(0, 1)[0];
  data.map(item => {
    if (item.value === targetValue) {
      label = item.label;
      if (newValueArr.length > 0) {
        getLabelByValue(item.children, newValueArr, label);
      }
    }
  });
  return label;
}
