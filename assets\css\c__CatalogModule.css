.DocExport-module_docExport_zwx9b .DocExport-module_title_eGdGG {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.DocExport-module_entry_qjwYu {
    min-height: 192px
}

.DocExport-module_exportOptions_jSIWB {
    margin-top: 24px
}

.DocExport-module_setting_yai1Y .DocExport-module_settingTitle_IjcSw {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.DocExport-module_setting_yai1Y .ant-upload-select,.DocExport-module_setting_yai1Y .DocExport-module_settingUpload_3ymtX {
    width: 100%
}

.DocExport-module_emptyExport_Djom5 {
    padding: 10px 0
}

.CatalogMenuEditNode-module_modal_4focI .CatalogMenuEditNode-module_modalTitle_ZgYhp {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500
}

.CatalogMenuEditNode-module_modal_4focI .CatalogMenuEditNode-module_modalDescription_-7x6D {
    margin-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-text-body)
}

.CatalogMenuEditNode-module_modal_4focI .CatalogMenuEditNode-module_btnCloseModal_ESRS\+ {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.CatalogMenuEditNode-module_modal_4focI .CatalogMenuEditNode-module_btnCloseModal_ESRS\+:hover {
    color: var(--yq-text-caption)
}

.CatalogMenuEditNode-module_modal_4focI .ant-modal-body {
    padding: 24px
}

.CatalogMenuEditNode-module_modal_4focI .ant-modal-confirm-btns {
    display: none
}

.CatalogMenuEditNode-module_menu_IUmmu .CatalogMenuEditNode-module_menuItem_AuSRR {
    display: flex;
    align-items: center
}

.CatalogMenuEditNode-module_menu_IUmmu .CatalogMenuEditNode-module_menuItem_AuSRR .CatalogMenuEditNode-module_icon_9R6mY {
    margin-right: 12px
}

.CatalogMenuEditNode-module_iconBeta_KHwCd {
    color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 5px;
    font-style: normal
}

.CatalogMenuEditNode-module_menuItemContent_Lf9nF {
    display: flex;
    align-items: center;
    width: 118px
}

.CatalogMenuEditNode-module_menuItemContent_Lf9nF.CatalogMenuEditNode-module_disabled_G919H {
    opacity: .4;
    cursor: not-allowed
}

.CatalogMenuEditNode-module_menuItemContent_Lf9nF .CatalogMenuEditNode-module_iconWrapper_IoV7X {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-right: 6px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.index-module_groupName_DppYx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: var(--yq-text-body)
}

.index-module_groupName_DppYx:hover {
    color: var(--yq-text-primary)
}

.index-module_groupName_DppYx>.index-module_groupNameText_XYhrt {
    margin-right: 4px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_groupName_DppYx>.icon-svg {
    color: var(--yq-text-caption)
}

.index-module_groupName_DppYx .index-module_groupNameScope_qP0XF {
    position: relative;
    top: 3px;
    color: var(--yq-text-caption)
}

.index-module_card_MJe8k {
    padding-top: 8px;
    padding-bottom: 8px;
    max-width: 290px;
    min-width: 240px
}

.index-module_cardBody_C-l0H {
    display: flex
}

.index-module_cardAvatar_S1GOD {
    margin-right: 8px
}

.index-module_cardInfo_1BnUz {
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz,.index-module_cardInfo_1BnUz>h6 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_cardInfo_1BnUz>h6 {
    margin-top: 2px;
    font-size: 16px
}

.index-module_cardInfo_1BnUz>h6>a {
    color: var(--yq-text-primary)
}

.index-module_cardInfo_1BnUz>h6>a:hover {
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz>p {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption)
}

.index-module_cardFooter_iT7Xq {
    margin-top: 16px;
    border-top: 1px solid var(--yq-border-primary);
    padding-top: 12px;
    display: flex;
    justify-content: flex-end
}

.index-module_cardFooter_iT7Xq>a {
    color: var(--yq-text-body)
}

.index-module_cardFooter_iT7Xq>a:hover {
    color: var(--yq-text-caption)
}

.index-module_contentArea_0xSfa {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_switchTitle_qW8aM {
    color: var(--yq-text-primary)
}

.index-module_switchButton_xHUSN .ant-switch-small {
    min-width: 32px;
    height: 18px;
    line-height: 18px
}

.index-module_switchButton_xHUSN .ant-switch-small.ant-switch-checked .ant-switch-handle {
    left: calc(100% - 16px)
}

.index-module_switchButton_xHUSN .ant-switch-small .ant-switch-handle {
    width: 14px;
    height: 14px
}

.index-module_tip_eCtT0 {
    background: var(--yq-yuque-grey-1);
    border-radius: 8px;
    padding: 8px;
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    margin-top: -16px;
    margin-bottom: 24px
}

/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:02.731Z
 */
.cropper-container {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.cropper-container img {
    backface-visibility: hidden;
    display: block;
    height: 100%;
    image-orientation: 0deg;
    max-height: none!important;
    max-width: none!important;
    min-height: 0!important;
    min-width: 0!important;
    width: 100%
}

.cropper-canvas,.cropper-crop-box,.cropper-drag-box,.cropper-modal,.cropper-wrap-box {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.cropper-canvas,.cropper-wrap-box {
    overflow: hidden
}

.cropper-drag-box {
    background-color: #fff;
    opacity: 0
}

.cropper-modal {
    background-color: #000;
    opacity: .5
}

.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid #39f;
    outline-color: rgba(51,153,255,.75);
    overflow: hidden;
    width: 100%
}

.cropper-dashed {
    border: 0 dashed #eee;
    display: block;
    opacity: .5;
    position: absolute
}

.cropper-dashed.dashed-h {
    border-bottom-width: 1px;
    border-top-width: 1px;
    height: 33.33333%;
    left: 0;
    top: 33.33333%;
    width: 100%
}

.cropper-dashed.dashed-v {
    border-left-width: 1px;
    border-right-width: 1px;
    height: 100%;
    left: 33.33333%;
    top: 0;
    width: 33.33333%
}

.cropper-center {
    display: block;
    height: 0;
    left: 50%;
    opacity: .75;
    position: absolute;
    top: 50%;
    width: 0
}

.cropper-center:after,.cropper-center:before {
    background-color: #eee;
    content: " ";
    display: block;
    position: absolute
}

.cropper-center:before {
    height: 1px;
    left: -3px;
    top: 0;
    width: 7px
}

.cropper-center:after {
    height: 7px;
    left: 0;
    top: -3px;
    width: 1px
}

.cropper-face,.cropper-line,.cropper-point {
    display: block;
    height: 100%;
    opacity: .1;
    position: absolute;
    width: 100%
}

.cropper-face {
    background-color: #fff;
    left: 0;
    top: 0
}

.cropper-line {
    background-color: #39f
}

.cropper-line.line-e {
    cursor: ew-resize;
    right: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-n {
    cursor: ns-resize;
    height: 5px;
    left: 0;
    top: -3px
}

.cropper-line.line-w {
    cursor: ew-resize;
    left: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-s {
    bottom: -3px;
    cursor: ns-resize;
    height: 5px;
    left: 0
}

.cropper-point {
    background-color: #39f;
    height: 5px;
    opacity: .75;
    width: 5px
}

.cropper-point.point-e {
    cursor: ew-resize;
    margin-top: -3px;
    right: -3px;
    top: 50%
}

.cropper-point.point-n {
    cursor: ns-resize;
    left: 50%;
    margin-left: -3px;
    top: -3px
}

.cropper-point.point-w {
    cursor: ew-resize;
    left: -3px;
    margin-top: -3px;
    top: 50%
}

.cropper-point.point-s {
    bottom: -3px;
    cursor: s-resize;
    left: 50%;
    margin-left: -3px
}

.cropper-point.point-ne {
    cursor: nesw-resize;
    right: -3px;
    top: -3px
}

.cropper-point.point-nw {
    cursor: nwse-resize;
    left: -3px;
    top: -3px
}

.cropper-point.point-sw {
    bottom: -3px;
    cursor: nesw-resize;
    left: -3px
}

.cropper-point.point-se {
    bottom: -3px;
    cursor: nwse-resize;
    height: 20px;
    opacity: 1;
    right: -3px;
    width: 20px
}

@media (min-width: 768px) {
    .cropper-point.point-se {
        height:15px;
        width: 15px
    }
}

@media (min-width: 992px) {
    .cropper-point.point-se {
        height:10px;
        width: 10px
    }
}

@media (min-width: 1200px) {
    .cropper-point.point-se {
        height:5px;
        opacity: .75;
        width: 5px
    }
}

.cropper-point.point-se:before {
    background-color: #39f;
    bottom: -50%;
    content: " ";
    display: block;
    height: 200%;
    opacity: 0;
    position: absolute;
    right: -50%;
    width: 200%
}

.cropper-invisible {
    opacity: 0
}

.cropper-bg {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC")
}

.cropper-hide {
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.cropper-hidden {
    display: none!important
}

.cropper-move {
    cursor: move
}

.cropper-crop {
    cursor: crosshair
}

.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point {
    cursor: not-allowed
}

.index-module_iconListContainer_2kTPj {
    min-width: 324px;
    padding: 0;
    display: flex;
    flex-direction: column
}

.index-module_iconListHeader_zgb1B {
    width: 100%;
    height: 42px;
    display: flex;
    align-items: center;
    padding-left: 27px;
    gap: 8px;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_iconListHeaderTab_Rb7UW {
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.index-module_iconListHeaderTabActive_l9CpK {
    color: var(--yq-text-primary);
    font-weight: 500
}

.index-module_iconListHeaderTabActive_l9CpK:after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    height: 2px;
    width: 100%;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_iconListContent_5acPP {
    height: 170px;
    display: grid;
    grid-template-rows: repeat(3,1fr);
    grid-template-columns: repeat(6,1fr);
    grid-gap: 8px;
    gap: 8px;
    padding: 16px
}

.index-module_iconListIconContainer_W2jPd {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: center;
    justify-self: center;
    width: 27px;
    height: 27px
}

.index-module_iconListIconContainerActive_m\+9aX:after {
    visibility: visible
}

.index-module_groupIconBtn_1pBes {
    width: 40px!important;
    height: 40px!important
}

.index-module_groupIconBtn_1pBes,.index-module_groupIconBtnLarge_PGNAs {
    display: flex!important;
    align-items: center;
    justify-content: center;
    padding: 0!important
}

.index-module_groupIconBtnLarge_PGNAs {
    width: 56px!important;
    height: 56px!important
}

.index-module_uploader_uEToK {
    min-height: 170px;
    padding: 16px
}

.index-module_dragger_yzTL7 {
    height: 138px!important;
    padding: 1px
}

.index-module_dragger_yzTL7 .ant-upload {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_imageContainer_lklS2 {
    padding: 8px 8px 0 8px
}

.index-module_uploaderContainer_Y0VkK {
    display: flex;
    align-items: flex-start
}

.index-module_uploaderContainer_Y0VkK .cropper-container {
    background-color: var(--yq-bg-secondary)
}

.index-module_reupload_3agQP {
    margin: 23px 0
}

.index-module_uploaderFooter_F5A9k {
    height: 50px;
    border-top: 1px solid var(--yq-border-primary);
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    gap: 8px;
    margin: 0 -24px;
    padding: 0 24px
}

.index-module_previewContainer_s7urA {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center
}

.index-module_preview_8GFmF {
    width: 108px;
    height: 108px;
    overflow: hidden;
    background-color: var(--yq-bg-secondary)
}

.index-module_newGroupModal_An\+Qw .ant-modal-header {
    border-bottom: none;
    padding-top: 20px;
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-modal-body {
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-form-vertical .ant-form-item-label>label {
    font-weight: 500
}

.index-module_newGroupModal_An\+Qw .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding: 0 24px 24px 24px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv {
    font-size: 16px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv .index-module_titleDesc_EmDkC {
    margin-top: 8px;
    color: var(--yq-text-caption);
    font-weight: 400;
    font-size: 14px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 {
    height: 40px;
    display: flex;
    margin-bottom: -12px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 .index-module_avatarGroup_olnac {
    border-radius: 44px;
    width: 44px;
    height: 44px;
    margin-right: 8px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 {
    width: 384px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a {
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a .index-module_btnText_6V0fp {
    margin-left: 4px;
    font-weight: 400
}

.index-module_newGroupModal_An\+Qw .index-module_submitBtn_ZJkHv {
    max-width: 100%;
    width: 384px;
    height: 40px
}

.group-selector .ant-select-item-group {
    background: var(--yq-bg-secondary)
}

.group-selector .group-selector-item {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 100%
}

.group-selector .group-selector-item>.larkicon {
    margin-left: 8px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-avatar {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 24px 24px;
    margin-right: 12px
}

.group-selector .group-selector-item .group-avatar .larkicon-svg-group-intranet,.group-selector .group-selector-item .group-avatar .larkicon-svg-group-lock {
    width: 12px;
    height: 12px
}

.group-selector .group-selector-item .group-selector-org-icon {
    margin-right: 12px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-selector-org-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.group-selector-disabled .selector-static {
    background: var(--yq-bg-secondary);
    cursor: not-allowed;
    border: 1px solid var(--yq-border-primary)
}

.group-selector-disabled .selector-static:before {
    display: none
}

.group-selector .ant-select-dropdown {
    width: 280px
}

.group-selector .ant-select-item-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.group-selector .ant-select-item-option-content .larkui-icon-check-outlined {
    margin: 0 0 0 8px;
    color: var(--yq-theme)
}

.group-selector .ant-select-selector {
    font-size: 14px
}

.index-module_name_2g47z {
    max-width: 200px
}

.index-module_repoSelector_2Z0BO .ant-select {
    width: 100%
}

.index-module_repoSelectorGroup_0chHn,.index-module_repoSelectorRepo_nq0Ar {
    width: 160px
}

.index-module_repoSelectorRepo_nq0Ar .ant-select-selection-item {
    height: 100%
}

.index-module_repoSelectorItem_-\+1uF {
    display: flex;
    align-items: center;
    height: 100%
}

.index-module_repoSelectorItem_-\+1uF .book-icon {
    margin: 0 8px 0 0;
    width: 20px;
    height: 20px;
    min-width: 20px
}

.index-module_repoSelectorItem_-\+1uF .book-name-text {
    max-width: 80%
}

.index-module_repoSelectorItem_-\+1uF .book-name .book-name-scope .icon-svg {
    display: block
}

.index-module_loading_6Xjs9 {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    text-align: center;
    font-size: 24px;
    color: var(--yq-icon-secondary);
    padding: 30vh 0 70vh 0;
    z-index: 999
}

.index-module_titleWrapper_NRfy4>div {
    height: 100%
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree .ant-tree-treenode,.index-module_wrapper_G8dbv.aside-catalog .ant-tree .ant-tree-treenode.catalog-treenode-level-1 {
    padding: 2px 0;
    margin: 0 0 0 16px
}

.index-module_wrapper_G8dbv.book-index-wrapper .ant-tree .ant-tree-treenode.catalog-treenode-level-1 {
    padding: 0
}

.index-module_wrapper_G8dbv .ant-tree-drop-indicator {
    bottom: -1px!important
}

.index-module_wrapper_G8dbv .ant-tree-checkbox {
    margin: 15px 4px
}

.index-module_wrapper_G8dbv .ant-tree-checkbox .ant-tree-checkbox-inner {
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree {
    background-color: transparent
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-treenode {
    padding: 0
}

.index-module_wrapper_G8dbv .ant-tree-list:hover .ant-tree-list-scrollbar {
    display: block!important
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-drop-indicator {
    background-color: var(--yq-function-info)
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-drop-indicator:after {
    border-color: var(--yq-function-info)
}

.index-module_wrapper_G8dbv .ant-tree-treenode.ant-tree-treenode-leaf-last.drag-over-gap-bottom .ant-tree-drop-indicator {
    margin-left: -4px
}

.index-module_wrapper_G8dbv .ant-tree-treenode.drop-container>[draggable] {
    box-shadow: none
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher {
    color: var(--yq-icon-secondary);
    text-align: right
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher_close .ant-tree-switcher-icon {
    transform: rotate(270deg)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
    color: var(--yq-text-body)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:before {
    transition: none;
    background: transparent
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
    min-width: 0
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled .ant-tree-node-content-wrapper {
    cursor: not-allowed;
    color: var(--yq-text-caption)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled .ant-tree-switcher {
    color: var(--yq-text-caption)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled:before,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled:hover:before {
    background-color: var(--yq-bg-secondary)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-highlight:before {
    background: var(--yq-bg-secondary)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before {
    background: var(--yq-bg-primary-hover);
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover .name,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tail-info {
    background: transparent
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover {
    border-radius: 4px;
    background: var(--yq-bg-primary-hover)
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected .name,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected .tail-info,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover .name,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tail-info {
    background: var(--yq-bg-primary-hover)
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before {
    background: var(--yq-bg-primary-hover);
    border-radius: 4px
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1 {
    position: relative
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:after,.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:before {
    content: "";
    pointer-events: none;
    position: absolute;
    z-index: 9;
    left: 0;
    right: 0;
    height: 60px;
    opacity: 0;
    transition: opacity .15s ease-in-out
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:before {
    top: 0;
    background: linear-gradient(180deg,var(--yq-bg-secondary),hsla(0,0%,98%,.5) 84%,hsla(0,0%,98%,.13))
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:after {
    bottom: 0;
    background: linear-gradient(180deg,hsla(0,0%,98%,.13),hsla(0,0%,98%,.5) 16%,var(--yq-bg-secondary))
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1.index-module_bottomScrolled_G\+APF:after,.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1.index-module_topScrolled_8VE-9:before {
    opacity: 1
}

.index-module_wrapper_G8dbv.index-module_dragging_fFNee .ant-tree .ant-tree-indent-unit {
    position: relative;
    height: 100%
}

.index-module_wrapper_G8dbv.index-module_dragging_fFNee .ant-tree .ant-tree-indent-unit:before {
    content: "";
    position: absolute;
    top: 0;
    right: 4px;
    bottom: -8px;
    border-right: 1px solid var(--yq-border-primary)
}

.index-module_normalWrapper_9CiWJ>div {
    padding-top: 8px;
    padding-bottom: 8px
}

.index-module_normalWrapper_9CiWJ .ant-tree .ant-tree-node-content-wrapper {
    min-height: 28px;
    line-height: 28px
}

.index-module_normalWrapper_9CiWJ .ant-tree-node-content-wrapper[draggable=true],.index-module_normalWrapper_9CiWJ .ant-tree-switcher {
    line-height: 28px
}

.index-module_normalWrapper_9CiWJ .ant-tree-title>.index-module_titleWrapper_NRfy4 {
    height: 28px
}

.index-module_largeWrapper_SUwil>div {
    padding-bottom: 16px
}

.index-module_largeWrapper_SUwil .ant-tree .ant-tree-node-content-wrapper {
    min-height: 46px;
    line-height: 46px
}

.index-module_largeWrapper_SUwil .ant-tree-node-content-wrapper[draggable=true],.index-module_largeWrapper_SUwil .ant-tree-switcher {
    line-height: 46px
}

.index-module_largeWrapper_SUwil .ant-tree-title>.index-module_titleWrapper_NRfy4 {
    height: 46px
}

.index-module_ListTocWrapper_4wy2S {
    padding-left: 24px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-treenode .ant-tree-drop-indicator {
    margin-left: -28px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-treenode.drag-over-gap-bottom .ant-tree-drop-indicator,.index-module_ListTocWrapper_4wy2S .ant-tree-treenode.drag-over-gap-top .ant-tree-drop-indicator {
    margin-left: -4px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-switcher-noop {
    display: none
}

.SearchCatalogTrigger-module_actionItem_rk-NO {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    width: 20px;
    min-width: 20px;
    height: 20px;
    color: var(--yq-text-caption);
    cursor: pointer;
    transition: background .35s ease-in-out
}

.SearchCatalogTrigger-module_actionItem_rk-NO:hover {
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 2px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH {
    position: relative
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    padding-left: 27px;
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single .ant-select-selector .ant-select-selection-search {
    left: 27px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single .ant-select-selector .ant-select-selection-item .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: 30px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_searchInput_4Tgor {
    position: absolute;
    right: 0;
    top: -2px;
    width: 240px;
    height: 32px;
    font-size: 14px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_prefixIconWrapper_Ub-Vo {
    position: absolute;
    display: flex;
    top: -2px;
    right: 213px;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_searchIcon_RK482 {
    color: var(--yq-text-body)
}

.CatalogSelector-module_card_EnAh7 {
    position: relative
}

.CatalogSelector-module_card_EnAh7 .ant-tree.ant-tree-directory .ant-tree-treenode span.ant-tree-node-content-wrapper.ant-tree-node-selected:before {
    background-color: var(--yq-bg-tertiary)!important
}

.CatalogSelector-module_card_EnAh7 .ant-card-body {
    padding-top: 8px;
    padding-bottom: 8px
}

.CatalogSelector-module_card_EnAh7.CatalogSelector-module_disabled_DdXXq:before {
    content: "";
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-bg-primary);
    opacity: .5
}

.CatalogSelector-module_titleWrapper_mcDOG {
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.CatalogSelector-module_titleWrapper_mcDOG .CatalogSelector-module_title_ixnVK {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: auto;
    min-width: 0
}

.CatalogSelector-module_docCatalogItem_yI0aV {
    padding: 0 8px;
    background-color: var(--yq-bg-tertiary);
    border-radius: 2px;
    height: 24px;
    line-height: 24px
}

.CatalogSelector-module_emptyViewInner_FIALf {
    padding: 16px 0;
    text-align: center;
    color: var(--yq-text-caption)
}

.CatalogSelector-module_placeholder_topjs {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: -2px;
    right: 0;
    height: 1px;
    background-color: var(--yq-blue-6)
}

.CatalogSelector-module_placeholder_topjs .CatalogSelector-module_caret_jxv72 {
    position: absolute;
    top: -3px;
    left: -16px;
    color: var(--yq-text-link)
}

.CatalogSelector-module_placeholder_topjs.CatalogSelector-module_placeholderSibling_-rdTP {
    left: 0;
    right: 0
}

.CatalogSelector-module_placeholder_topjs.CatalogSelector-module_placeholderChild_uP7D6 {
    left: 32px;
    right: 0
}

.CatalogSelector-module_actions_HKHke {
    margin-left: 12px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 22px;
    white-space: nowrap
}

.CatalogSelector-module_actions_HKHke .ant-radio-group {
    margin-left: 8px;
    display: flex;
    align-items: center
}

.CatalogSelector-module_actions_HKHke .ant-radio-wrapper {
    font-size: 12px
}

.CatalogSelector-module_actions_HKHke .ant-radio {
    position: relative;
    top: 3px;
    transform: scale(.75);
    margin: 1px
}

.CatalogSelector-module_actions_HKHke span.ant-radio+* {
    padding-left: 4px;
    padding-right: 4px
}

.CatalogSelector-module_bottomSimulator_HSsiS,.CatalogSelector-module_topSimulator_t0qhG {
    position: relative;
    width: 100%;
    height: 0;
    border-radius: 6px;
    cursor: pointer
}

.CatalogSelector-module_bottomSimulator_HSsiS .CatalogSelector-module_textTip_LK8n1,.CatalogSelector-module_topSimulator_t0qhG .CatalogSelector-module_textTip_LK8n1 {
    display: none;
    padding: 0 8px;
    position: absolute;
    bottom: 0;
    right: 0;
    line-height: 24px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.CatalogSelector-module_bottomSimulator_HSsiS.CatalogSelector-module_selected_JDZUv,.CatalogSelector-module_bottomSimulator_HSsiS:hover,.CatalogSelector-module_topSimulator_t0qhG.CatalogSelector-module_selected_JDZUv,.CatalogSelector-module_topSimulator_t0qhG:hover {
    background-color: var(--yq-bg-tertiary)
}

.CatalogSelector-module_bottomSimulator_HSsiS .CatalogSelector-module_placeholder_topjs,.CatalogSelector-module_topSimulator_t0qhG .CatalogSelector-module_placeholder_topjs {
    left: 18px;
    bottom: -2px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptions_Om8RW {
    display: flex;
    align-items: center
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptionItem_oqA8J {
    margin-right: 8px;
    height: 32px;
    line-height: 32px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptionItem_oqA8J .larkui-icon {
    position: relative;
    top: 1px;
    color: var(--yq-text-caption)
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutTip_BR\+Yo {
    font-size: 14px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 {
    display: flex
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 .CatalogSelector-module_shortcutAction_u\+U2K {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    margin-left: 8px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 .CatalogSelector-module_shortcutActionDisabled_\+nyVB {
    opacity: .3
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_repoSelector_xEuzZ {
    margin-bottom: 25px
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_selectBookTip_Ju7F8 {
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    padding: 16px;
    min-height: 356px;
    color: var(--yq-text-caption);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    flex-direction: column
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_selectBookTip_Ju7F8 .CatalogTransfer-module_emptyIcon_tkaXF {
    width: 200px;
    margin-bottom: 24px
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_loading_MvRPo {
    width: 100%;
    display: flex;
    align-items: center
}

.CatalogTransfer-module_footer_XgWWv {
    margin-top: 24px
}

.CatalogTransfer-module_footer_XgWWv,.CatalogTransfer-module_footer_XgWWv .CatalogTransfer-module_actions_3DruK {
    display: flex;
    justify-content: flex-end;
    align-items: center
}

.CatalogTransfer-module_footer_XgWWv .CatalogTransfer-module_actions_3DruK .ant-btn {
    margin-left: 8px
}

.CatalogTransfer-module_selectorOptions_evk3C {
    position: absolute;
    bottom: 24px
}

.KnowledgePieGuide-module_wrapper_McOcd {
    max-width: 218px;
    height: 76px;
    position: relative;
    border-radius: 8px;
    background-color: var(--yq-yuque-green-600);
    color: var(--yq-white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px
}

.KnowledgePieGuide-module_wrapper_McOcd .KnowledgePieGuide-module_close_Hta-f {
    position: absolute;
    cursor: pointer;
    top: 8px;
    right: 8px
}

.KnowledgePieGuide-module_breathBorder_JnuNa {
    max-width: 259px;
    height: 55px;
    position: absolute;
    background-color: rgba(0,185,107,.1);
    border: 2px solid var(--yq-yuque-green-600);
    animation: KnowledgePieGuide-module_breathe_hzZ03 2s ease-in-out infinite;
    border-radius: 8px;
    left: 224px;
    top: -20px
}

@keyframes KnowledgePieGuide-module_breathe_hzZ03 {
    0% {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }

    50% {
        box-shadow: 0 0 0 2px var(--yq-yuque-green-600)
    }

    to {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }
}

.index-module_container_YifTz {
    white-space: break-spaces;
    z-index: 1020
}

.index-module_container_YifTz .ant-tooltip-inner {
    background-color: var(--yq-yuque-grey-2);
    padding: 0
}

.index-module_container_YifTz .ant-tooltip-arrow-content {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_container_YifTz p {
    font-size: 14px;
    font-weight: 700
}

.index-module_container_YifTz h4,.index-module_container_YifTz p {
    color: var(--yq-white);
    line-height: 22px
}

.index-module_titleContainer_E19Ts {
    position: relative
}

.index-module_content_u6ksX {
    background-color: var(--yq-blue-5);
    padding: 4px 10px;
    padding-right: 30px;
    border-radius: 6px;
    min-width: 250px;
    margin-top: 6px;
    margin-left: -2px
}

.index-module_close_xIF4s {
    position: absolute!important;
    top: 2px;
    right: -2px;
    color: var(--yq-white)
}

.index-module_close_xIF4s :hover {
    color: var(--yq-white)
}

.larkui-feature-guide>div {
    width: 284px!important
}

.larkui-feature-guide>div>div {
    z-index: 999;
    position: absolute
}

.larkui-feature-guide-sign {
    position: relative;
    width: 12px;
    height: 12px;
    line-height: 12px;
    cursor: pointer
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-outer {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 12px;
    height: 12px;
    border-radius: 12px;
    background: rgba(47,142,244,.2);
    animation: fadein 1s ease-in-out 1s infinite alternate
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-inner {
    display: inline-block;
    position: absolute;
    top: 3px;
    left: 3px;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: var(--yq-blue-5)
}

.larkui-feature-guide-mini .larkui-feature-guide-sign {
    display: inline-block;
    vertical-align: -.125em;
    margin-left: 8px
}

.larkui-feature-guide-lite {
    position: absolute;
    display: block;
    height: 12px;
    width: 12px;
    bottom: 0;
    left: 6px
}

.larkui-feature-guide-lite .larkui-feature-guide-sign {
    display: block;
    margin-left: 0
}

.larkui-feature-guide-lite-content {
    cursor: text
}

.larkui-feature-guide-lite-close {
    margin-left: 8px;
    cursor: pointer
}

.larkui-feature-guide-lite-close .icon-svg {
    vertical-align: middle;
    margin-bottom: 3px
}

.larkui-feature-guide-lite-close-multi-line {
    position: absolute;
    right: 12px;
    top: 18px
}

.larkui-feature-guide-lite-multi-line-button {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background: var(--yq-bg-secondary);
    color: var(--yq-function-info);
    min-width: 70px;
    height: 24px;
    margin: 8px 0
}

.larkui-feature-guide-lite-multi-line-button:hover {
    background: var(--yq-bg-tertiary)
}

.larkui-feature-guide-lite-multi-line-button:focus {
    outline: none
}

.larkui-feature-guide-content {
    max-width: 228px
}

.ant-tooltip.larkui-feature-guide-lite-tip {
    max-width: 360px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content,.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    background: var(--yq-blue-5)
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    padding: 8px 16px;
    font-size: 12px;
    line-height: 22px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content {
    width: 8.484px;
    height: 8.484px
}

.ant-tooltip-placement-bottomLeft.larkui-feature-guide-lite-tip .ant-tooltip-arrow {
    left: 15px
}

.ant-tooltip-placement-right {
    padding-left: 4px;
    margin-top: 8px
}

.SaveAsVersion-module_nameVersion_VphI2 {
    margin: 0
}

.SaveAsVersion-module_nameVersion_VphI2 .ant-form-item-label>label.ant-form-item-required:before {
    display: none
}

.modal-without-padding .ant-modal-content .ant-modal-body {
    padding: 0
}

.template-creator-content {
    margin: 0 24px 16px 24px
}

.template-creator-content p {
    margin: 16px 0 8px 0
}

.template-scope {
    display: flex;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 3px
}

.template-scope-more-icon {
    display: flex;
    align-items: center
}

.template-scope-icon {
    margin: 20px 16px 0 20px;
    width: 20px;
    height: 20px
}

.template-scope-text {
    margin: 16px 0;
    width: 234px
}

.template-scope-text p {
    white-space: normal;
    margin: 0
}

.template-scope-menu-item {
    display: flex
}

.template-scope-menu-item .template-scope-icon {
    margin: 15px 16px 0 8px
}

.template-scope-menu-item .template-scope-text {
    width: 230px;
    margin: 11px 0
}

.template-scope-icon .larkicon:before {
    font-size: 20px
}

.DocImport-module_docImport_uDZWD .DocImport-module_title_vylxD {
    color: var(--yq-text-primary);
    font-weight: 700
}

.DocImport-module_docImport_uDZWD .DocImport-module_title2_APgF7 {
    color: var(--yq-text-caption)
}

.DocImport-module_docImport_uDZWD .DocImport-module_tips_gx2\+N {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px;
    margin-bottom: 30px
}

.DocImport-module_docImport_uDZWD .DocImport-module_help_YDms4 {
    color: var(--yq-text-link);
    margin-left: 8px
}

.DocImport-module_processingModal_5vLO\+ .ant-modal-body {
    padding: 0
}

.DocImport-module_setting_6ttU6 {
    padding: 20px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingTitle_EsA43 {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFile_I6\+s\+ {
    margin-top: 16px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileIcon_HBcXz {
    width: 43px;
    margin: 16px 18px 16px 8px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileIcon_HBcXz img {
    width: 100%
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingContent_aLLs- {
    height: 170px;
    overflow-y: auto
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingContent_aLLs- .ant-checkbox-group-item {
    display: block;
    margin-top: 8px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileTypeName_LeTUR {
    color: var(--yq-yuque-grey-9)
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileTypeExt_LWcRn {
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingUpload_WXf0g {
    width: 100%;
    margin-top: 16px
}

.DocImport-module_setting_6ttU6 .ant-upload-select {
    width: 100%
}

.DocImport-module_processing_K3nVL .DocImport-module_processingTitle_UzLg1 {
    font-size: 14px;
    color: var(--yq-text-primary);
    padding: 24px 24px 0
}

.DocImport-module_processing_K3nVL .DocImport-module_processingFooter_xOxG5 {
    padding: 16px 24px
}

.DocImport-module_processing_K3nVL .DocImport-module_fileList_jRvlw {
    margin-top: 26px;
    padding: 0 24px;
    height: 330px;
    overflow-y: auto
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItem_gtT95 {
    margin-top: 10px
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItemIcon_SdTY- {
    width: 24px;
    margin-right: 8px;
    vertical-align: middle
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItemTitle_qprAi {
    font-size: 14px;
    font-weight: 400;
    max-width: 245px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    display: inline-block
}

.DocImport-module_processing_K3nVL a.DocImport-module_fileItemTitle_qprAi:hover {
    color: var(--yq-text-body)
}

.DocImport-module_processing_K3nVL .DocImport-module_spin_aEZeg {
    margin-left: 8px
}

.DocImport-module_processing_K3nVL .DocImport-module_uploadingStatus_UWCen {
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocImport-module_processing_K3nVL .DocImport-module_hr_nD0TF {
    height: 1px;
    background-color: var(--yq-bg-primary-hover-light)
}

.DocImport-module_processing_K3nVL .larkui-icon-close-circle,.DocImport-module_processing_K3nVL .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error)
}

.DocImport-module_processing_K3nVL .larkui-icon-check-outlined-circle {
    color: var(--yq-ant-success-color);
    margin-left: 2px
}

.DocImport-module_processing_K3nVL .ant-spin-spinning {
    color: var(--yq-function-info)
}

.DocImport-module_processing_K3nVL .DocImport-module_footerTitle_2O1eI {
    color: var(--yq-text-primary);
    margin: 0 8px
}

.DocImport-module_subTips_oayTw {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-top: 4px
}

.DocImport-module_radioGroup_klLUa {
    gap: 12px;
    display: flex;
    flex-direction: column;
    margin-top: 12px;
    margin-bottom: 12px
}

.DocImport-module_radioGroup_klLUa label {
    height: 66px;
    display: flex;
    align-items: center;
    background-color: var(--yq-yuque-grey-2);
    border-radius: 12px;
    padding-left: 16px
}

.RecipientSelector-module_recipient_Ds5UW {
    position: relative;
    min-height: 50px
}

.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content .dep,.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content img {
    display: none
}

.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content .name {
    margin-left: 0
}

.RecipientSelector-module_recipient_Ds5UW .ant-select:not(.ant-select-combobox) .ant-select-selector {
    background: var(--yq-bg-primary)
}

.RecipientSelector-module_mention_5r6oN {
    position: absolute;
    right: 0;
    top: -24px;
    cursor: pointer;
    color: var(--yq-text-link)
}

.RecipientSelector-module_userForCheck_a4ZZP {
    position: absolute;
    width: 100%;
    border: 1px solid var(--yq-border-primary);
    background: var(--yq-bg-primary);
    border-radius: 4px;
    z-index: 100;
    overflow: scroll;
    max-height: 300px
}

.RecipientSelector-module_userForCheck_a4ZZP p {
    padding: 8px 16px
}

.RecipientSelector-module_userForCheck_a4ZZP p:hover {
    background: var(--yq-bg-secondary);
    cursor: pointer;
    white-space: normal;
    text-overflow: normal
}

.RecipientSelector-module_mailHistory_-ft0g {
    position: absolute;
    width: 100%;
    border: 1px solid var(--yq-border-primary);
    background: var(--yq-bg-primary);
    border-radius: 4px;
    z-index: 100
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_title_-yKJG {
    padding: 8px 16px;
    height: 40px;
    line-height: 24px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.RecipientSelector-module_mailHistory_-ft0g ul {
    max-height: 300px;
    overflow: scroll
}

.RecipientSelector-module_mailHistory_-ft0g li {
    padding: 8px 16px
}

.RecipientSelector-module_mailHistory_-ft0g li:hover {
    background: var(--yq-bg-secondary);
    cursor: pointer
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_users_ZwnLM {
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_info_eM40g {
    margin-right: 20px;
    margin-top: 6px;
    max-width: 50%;
    display: inline-block;
    color: var(--yq-text-caption);
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.RecipientSelector-module_avatar_lGHzI {
    position: relative;
    top: 10px
}

.RecipientSelector-module_name_HeaN7 {
    margin-left: 10px;
    color: var(--yq-text-primary);
    font-size: 14px
}

.RecipientSelector-module_dep_B2T89 {
    display: block;
    margin-left: 42px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocSendMail-module_sendMail_QBA\+O>p {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 8px
}

.DocSendMail-module_recipient_zqM9E h5,.DocSendMail-module_title_Af2FA h5 {
    padding: 16px 0 10px;
    margin-top: 10px;
    font-size: 14px
}

.DocSendMail-module_submitButton_HEsW- {
    margin-top: 16px
}

.DocSendMail-module_mailResult_AhaV4 {
    text-align: center;
    min-height: 200px;
    padding-top: 60px
}

.DocSendMail-module_mailResult_AhaV4 .larkui-spin {
    transform: scale(1.5)
}

.DocSendMail-module_mailResult_AhaV4 .larkicon {
    font-size: 42px
}

.DocSendMail-module_mailResult_AhaV4 .larkicon-check-o {
    color: var(--yq-theme)
}

.DocSendMail-module_mailResult_AhaV4 .larkicon-error {
    color: var(--yq-function-error)
}

.DocSendMail-module_mailResult_AhaV4 p {
    margin-top: 16px;
    line-height: 2
}

.DocActionMenu-module_wrap_oZQ06 {
    padding: 0 8px
}

.DocActionMenu-module_wrap_oZQ06 .ant-menu.ant-menu-root {
    min-width: 89px;
    border-right: none;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.DocActionMenu-module_wrap_oZQ06 .ant-menu.ant-menu-root .ant-menu-item-divider {
    margin: 4px 0
}

.DocActionMenu-module_wrap_oZQ06 .ant-menu.ant-menu-root .ant-menu-item {
    color: var(--yq-text-body);
    padding: 0;
    height: 36px;
    line-height: 36px;
    border-radius: 6px;
    margin: 0;
    min-width: 148px
}

.DocActionMenu-module_wrap_oZQ06 .ant-menu.ant-menu-root .ant-menu-item:hover {
    background-color: var(--yq-yuque-grey-2)
}

.DocActionMenu-module_wrap_oZQ06 .ant-menu.ant-menu-root .ant-menu-item:not(:last-child) {
    margin: 0
}

.DocActionMenu-module_featureGuide_8ujxi {
    display: flex
}

.DocActionMenu-module_menuItemContainer_pMpjo {
    display: flex;
    align-items: center;
    color: var(--yq-text-primary);
    padding-left: 8px
}

.DocActionMenu-module_menuTileWrapper_WYEAB {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px
}

.DocActionMenu-module_iconContainer_QaDi2 {
    margin-right: 12px
}

.DocActionMenu-module_iconContainer_QaDi2.DocActionMenu-module_icon-ai-active_EoJt6 {
    color: var(--yq-theme)
}

.larkui-popover .DocActionMenu-module_menu_6Bn5J.ant-menu.ant-menu-root {
    box-shadow: none
}

.DocActionMenu-module_iconBeta_9CIl5 {
    color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 5px
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.catalogItemTitle-module_title_GhXzs {
    font-size: 15px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.catalogItemTitle-module_dashline_9S9EN {
    flex: 1;
    margin: 0 16px;
    border-top: 1px dashed var(--yq-yuque-grey-5)
}

.catalogItemTitle-module_tail_NXUHW {
    font-size: 16px;
    color: var(--yq-yuque-green-6)
}

.catalogItemTitle-module_date_t1aj2 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right
}

.catalogTreeItem-module_content_J3D5T:hover {
    background-color: var(--yq-bg-secondary)!important;
    border: 1.5px solid transparent!important
}

.cardStyle-module_cardItem_0zpVC {
    padding: 12px;
    height: 100%
}

.cardStyle-module_cardItem_0zpVC .cardStyle-module_wrapper_-Cdxb {
    height: 100%;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 8px
}

.cardStyle-module_cardItem_0zpVC .cardStyle-module_wrapper_-Cdxb .cardStyle-module_content_1seC0 {
    border-radius: 8px 8px 0 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    cursor: pointer;
    height: 66%;
    background-color: var(--yq-bg-secondary);
    border-bottom: 1px solid var(--yq-border-light);
    background-position: 50%;
    background-repeat: no-repeat
}

.cardStyle-module_cardItem_0zpVC .cardStyle-module_wrapper_-Cdxb .cardStyle-module_bottom_HE9Ie {
    height: 33%;
    padding: 12px 16px
}

.cardStyle-module_cardItem_0zpVC .cardStyle-module_wrapper_-Cdxb .cardStyle-module_bottom_HE9Ie .cardStyle-module_title_47tVW {
    font-size: 15px;
    color: var(--yq-yuque-grey-9);
    letter-spacing: 0;
    text-align: left;
    line-height: 26px;
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.cardStyle-module_cardItem_0zpVC .cardStyle-module_wrapper_-Cdxb .cardStyle-module_bottom_HE9Ie .cardStyle-module_date_dFort {
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    letter-spacing: 0;
    text-align: left;
    line-height: 20px;
    margin: 8px 0 0 0;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX {
    display: flex;
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px;
    text-align: left;
    font-size: 18px;
    color: var(--yq-yuque-grey-8);
    cursor: pointer;
    min-height: 146px
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX .column-module_left_ViMW5 {
    padding: 16px 32px 16px 16px
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX .column-module_left_ViMW5 .column-module_title_6wV1M {
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-yuque-grey-9);
    letter-spacing: 0;
    text-align: left;
    line-height: 26px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX .column-module_left_ViMW5 .column-module_desc_HT7qJ {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-yuque-grey-8);
    letter-spacing: 0;
    text-align: left;
    min-height: 40px;
    display: -webkit-box;
    max-height: 40px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 20px;
    word-break: break-word
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX .column-module_left_ViMW5 .column-module_date_uBxlq {
    margin-top: 16px;
    font-size: 14px;
    line-height: 20px;
    color: var(--yq-yuque-grey-8);
    letter-spacing: 0;
    text-align: left
}

.column-module_columnItem_zzhvS .column-module_content_C\+IlX .column-module_right_pp6ET {
    border-radius: 0 8px 8px 0;
    background-color: var(--yq-bg-secondary);
    background-position: 50%;
    background-size: 50%;
    background-repeat: no-repeat;
    border-left: 1px solid var(--yq-border-light)
}
