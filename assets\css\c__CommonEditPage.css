.StatusView-module_wrap_8c-mq {
    position: fixed;
    top: 56px;
    right: 0;
    left: 0;
    z-index: 1002
}

.StatusView-module_wrap_8c-mq .larkui-icon-info-circle,.StatusView-module_wrap_8c-mq .larkui-icon-information {
    color: var(--yq-function-info)
}

.StatusView-module_wrap_8c-mq .larkui-icon-error-circle {
    color: var(--yq-function-error)
}

.StatusView-module_wrap_8c-mq .larkui-icon-exclamation-circle,.StatusView-module_wrap_8c-mq .larkui-icon-exclamationcircle {
    color: var(--yq-function-warning);
    font-size: 24px;
    line-height: 24px;
    vertical-align: middle;
    margin-right: 12px
}

.StatusView-module_wrap_8c-mq .StatusView-module_tip_DFrDV {
    margin-top: 96px
}

.StatusView-module_wrap_8c-mq .StatusView-module_collab-tip_-YV1P {
    display: inline-block;
    text-align: center;
    background: var(--yq-bg-primary);
    border-radius: 2px;
    margin: 0 auto;
    padding: 4px 8px
}

.StatusView-module_wrap_8c-mq .StatusView-module_error_cvob- {
    border-radius: 4px;
    margin: 0 auto;
    width: 420px;
    padding: 32px;
    background: var(--yq-bg-primary)
}

.StatusView-module_wrap_8c-mq .StatusView-module_title_8Zp5n {
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.StatusView-module_wrap_8c-mq .StatusView-module_content_80A7- {
    line-height: 24px;
    padding-left: 20px
}

.StatusView-module_wrap_8c-mq .StatusView-module_contentIcon_vp-6I {
    line-height: 24px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-9);
    font-size: 14px
}

.StatusView-module_wrap_8c-mq .StatusView-module_contentIcon_vp-6I svg {
    color: var(--yq-yellow-600);
    margin-right: 16px
}

.StatusView-module_wrap_8c-mq .StatusView-module_infoList_JcjpV {
    list-style: inside;
    padding: 10px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 {
    font-size: 16px;
    text-align: right;
    margin-top: 24px;
    color: var(--yq-text-body);
    font-weight: 700
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 button {
    margin-left: 8px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 .ant-btn {
    margin-left: 10px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 .larkicon {
    color: var(--yq-text-caption);
    font-size: 14px
}

.StatusView-module_noticeWrap_LtD\+K {
    width: 100%;
    height: 56px;
    padding: 6px;
    text-align: center;
    position: fixed;
    pointer-events: none;
    top: 0
}

.StatusView-module_noticeWrap_LtD\+K .StatusView-module_notice_73bog {
    display: inline-block;
    padding: 10px 16px;
    background: var(--yq-bg-primary);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,.15);
    pointer-events: all
}

.StatusView-module_mask_CFGUv {
    bottom: 0;
    height: calc(100% - 56px);
    background: rgba(0,0,0,.1)
}

.StatusView-module_actionTips_4SMlq {
    max-width: 160px
}

.StatusView-module_editorMessage_swR0c {
    font-size: 12px
}

.StatusView-module_rightBtns_I4V0J {
    padding: 16px 0;
    text-align: right
}

.StatusView-module_mask_mobile_vHbQp {
    height: 100%;
    top: 0
}

.SaveAsVersion-module_nameVersion_VphI2 {
    margin: 0
}

.SaveAsVersion-module_nameVersion_VphI2 .ant-form-item-label>label.ant-form-item-required:before {
    display: none
}

.index-module_more_sDgGm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--yq-black);
    border-radius: 8px
}

.ant-popover-open.index-module_more_sDgGm,.index-module_more_sDgGm:hover {
    border-radius: 8px
}

.styles-module_popover_v7CCF {
    z-index: 990!important
}

.styles-module_popover_v7CCF .ant-popover-inner-content {
    padding: 0!important
}

.styles-module_disable_OpJcF,.styles-module_disable_OpJcF .styles-module_icon_Y7wj8 {
    cursor: not-allowed
}

.MemberAbleIcon-module_lightIcon_wz4WU {
    height: 16px;
    padding: 0 5px;
    line-height: 16px;
    background: rgba(0,0,0,.04);
    border-radius: 10px;
    text-align: center;
    display: inline-block;
    margin-left: 4px;
    color: #b4b7b5;
    font-size: 12px
}

.ShareQrCodeLazyShow-module_skeletonPanel_OLLRv {
    padding: 20px;
    height: 420px
}

.ShareQrCodeLazyShow-module_skeletonPanel_OLLRv .ShareQrCodeLazyShow-module_skeletonTitle_-9VKp {
    width: 100px;
    height: 24px
}

.ShareQrCodeLazyShow-module_skeletonPanel_OLLRv .ShareQrCodeLazyShow-module_skeletonImage_oIjcM {
    width: 232px;
    height: 232px;
    margin-left: 37px;
    margin-top: 12px;
    margin-bottom: 12px
}

.ShareQrCodeLazyShow-module_skeletonPanel_OLLRv .ShareQrCodeLazyShow-module_skeletonButton_VsITP {
    width: 232px;
    height: 40px;
    margin-left: 74px
}

.ShareQrCodeLazyShow-module_skeletonPanel_OLLRv .ShareQrCodeLazyShow-module_skeletonButton_VsITP .ant-skeleton-content .ant-skeleton-title {
    height: 40px
}

.index-module_description_3zdQm {
    color: var(--yq-text-caption);
    font-size: 14px;
    margin-bottom: 32px
}

.index-module_title_wvDlw {
    font-weight: 500;
    font-size: 18px;
    color: var(--yq-text-primary);
    margin-bottom: 16px
}

.index-module_modalStyle_mcrRn .ant-modal-content {
    border-radius: 8px
}

.index-module_qrCode_tO6RV {
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px;
    padding: 16px
}

.index-module_resultBox_kmldH,.index-module_resultContentBox_DyUmc {
    text-align: center
}

.index-module_checkIcon_R-xBl,.index-module_closeIcon_3nBx0 {
    font-size: 35px;
    margin-bottom: 20px
}

.index-module_checkIcon_R-xBl {
    color: var(--yq-function-success)
}

.index-module_closeIcon_3nBx0 {
    color: var(--yq-function-error)
}

.index-module_img_CCkgm {
    display: none
}

:root {
    --adm-radius-s: 4px;
    --adm-radius-m: 8px;
    --adm-radius-l: 12px;
    --adm-font-size-1: 9px;
    --adm-font-size-2: 10px;
    --adm-font-size-3: 11px;
    --adm-font-size-4: 12px;
    --adm-font-size-5: 13px;
    --adm-font-size-6: 14px;
    --adm-font-size-7: 15px;
    --adm-font-size-8: 16px;
    --adm-font-size-9: 17px;
    --adm-font-size-10: 18px;
    --adm-color-primary: #1677ff;
    --adm-color-success: #00b578;
    --adm-color-warning: #ff8f1f;
    --adm-color-danger: #ff3141;
    --adm-color-yellow: #ff9f18;
    --adm-color-orange: #ff6430;
    --adm-color-wathet: #e7f1ff;
    --adm-color-text: #333;
    --adm-color-text-secondary: #666;
    --adm-color-weak: #999;
    --adm-color-light: #ccc;
    --adm-color-border: #eee;
    --adm-color-background: #fff;
    --adm-color-highlight: var(--adm-color-danger);
    --adm-color-white: #fff;
    --adm-color-box: #f5f5f5;
    --adm-color-text-light-solid: var(--adm-color-white);
    --adm-color-text-dark-solid: #000;
    --adm-color-fill-content: var(--adm-color-box);
    --adm-font-size-main: var(--adm-font-size-5);
    --adm-font-family: -apple-system,blinkmacsystemfont,"Helvetica Neue",helvetica,segoe ui,arial,roboto,"PingFang SC","miui","Hiragino Sans GB","Microsoft Yahei",sans-serif;
    --adm-border-color: var(--adm-color-border)
}

html[data-prefers-color-scheme=dark] {
    --adm-color-primary: #3086ff;
    --adm-color-success: #34b368;
    --adm-color-warning: #ffa930;
    --adm-color-danger: #ff4a58;
    --adm-color-yellow: #ffa930;
    --adm-color-orange: #e65a2b;
    --adm-color-wathet: #0d2543;
    --adm-color-text: #e6e6e6;
    --adm-color-text-secondary: #b3b3b3;
    --adm-color-weak: grey;
    --adm-color-light: #4d4d4d;
    --adm-color-border: #2b2b2b;
    --adm-color-box: #0a0a0a;
    --adm-color-background: #1a1a1a;
    --adm-color-background-body: var(--adm-color-background);
    --adm-border-color: var(--adm-color-border)
}

:root {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html {
    background-color: var(--adm-color-background-body)
}

body {
    color: #333;
    color: var(--adm-color-text);
    font-size: 13px;
    font-size: var(--adm-font-size-main);
    font-family: -apple-system,blinkmacsystemfont,Helvetica Neue,helvetica,segoe ui,arial,roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif;
    font-family: var(--adm-font-family)
}

a,button {
    cursor: pointer
}

a {
    color: #1677ff;
    color: var(--adm-color-primary);
    transition: opacity .2s ease-in-out
}

a:active {
    opacity: .8
}

.adm-plain-anchor {
    color: inherit;
    transition: none
}

.adm-plain-anchor:active {
    opacity: 1
}

body.adm-overflow-hidden {
    overflow: hidden!important
}

div.adm-px-tester {
    --size: 1;
    height: 1px;
    height: calc(var(--size)/2*2px);
    width: 0;
    position: fixed;
    left: -100vw;
    top: -100vh;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none
}

.adm-dialog {
    --z-index: var(--adm-dialog-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-dialog .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-dialog-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-dialog-body>* {
    flex: none
}

.adm-dialog-body>.adm-dialog-content {
    flex: auto
}

.adm-dialog-body:not(.adm-dialog-with-image) {
    padding-top: 20px
}

.adm-dialog-image-container {
    margin-bottom: 12px;
    max-height: 40vh
}

.adm-dialog-header,.adm-dialog-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-dialog-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-dialog-content {
    padding: 0 12px 20px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-dialog-content-empty {
    padding: 0;
    height: 12px
}

.adm-dialog-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-dialog-footer .adm-dialog-action-row {
    display: flex;
    align-items: stretch;
    border-top: .5px solid var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>* {
    flex: 1
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button {
    padding: 10px;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    border-radius: 0;
    border-right: solid .5px var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button-bold {
    font-weight: 700
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button:last-child {
    border-right: none
}

.adm-dialog-image-container {
    overflow-y: auto
}

.adm-image {
    --width: var(--adm-image-width,auto);
    --height: var(--adm-image-height,auto);
    width: auto;
    width: var(--width);
    height: auto;
    height: var(--height);
    display: block;
    overflow: hidden
}

.adm-image-img {
    width: 100%;
    height: 100%
}

.adm-image-tip {
    position: relative;
    background-color: var(--adm-color-fill-content);
    height: 100%;
    min-height: 24px;
    min-width: 24px
}

.adm-image-tip>svg {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    color: var(--adm-color-weak)
}

.adm-auto-center {
    display: flex;
    justify-content: center
}

.adm-auto-center-content {
    flex: 0 1 auto
}

.adm-button {
    --color: var(--adm-color-text-light-solid);
    --text-color: var(--adm-button-text-color,var(--adm-color-text));
    --background-color: var(--adm-button-background-color,var(--adm-color-background));
    --border-radius: var(--adm-button-border-radius,4px);
    --border-width: var(--adm-button-border-width,1px);
    --border-style: var(--adm-button-border-style,solid);
    --border-color: var(--adm-button-border-color,var(--adm-color-border));
    color: var(--text-color);
    background-color: var(--background-color);
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: auto;
    padding: 7px 12px;
    margin: 0;
    font-size: var(--adm-font-size-9);
    line-height: 1.4;
    text-align: center;
    border: 1px solid var(--border-color);
    border: var(--border-width) var(--border-style) var(--border-color);
    border-radius: 4px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: opacity .15s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-button:focus {
    outline: none
}

.adm-button:before {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(calc(var(--border-width)*-1),calc(var(--border-width)*-1));
    width: 100%;
    height: 100%;
    background-color: var(--adm-color-text-dark-solid);
    border: var(--border-width) var(--border-style) var(--adm-color-text-dark-solid);
    border-radius: var(--border-radius);
    opacity: 0;
    content: " ";
    box-sizing: content-box
}

.adm-button:active:before {
    opacity: .08
}

.adm-button-default.adm-button-fill-outline {
    --background-color: transparent;
    --border-color: var(--adm-color-text)
}

.adm-button-default.adm-button-fill-none {
    --background-color: transparent;
    --border-width: 0px
}

.adm-button:not(.adm-button-default) {
    --text-color: var(--adm-color-text-light-solid);
    --background-color: var(--color);
    --border-color: var(--color)
}

.adm-button:not(.adm-button-default).adm-button-fill-outline {
    --text-color: var(--color);
    --background-color: transparent
}

.adm-button:not(.adm-button-default).adm-button-fill-none {
    --text-color: var(--color);
    --background-color: transparent;
    --border-width: 0px
}

.adm-button-primary {
    --color: var(--adm-color-primary)
}

.adm-button-success {
    --color: var(--adm-color-success)
}

.adm-button-danger {
    --color: var(--adm-color-danger)
}

.adm-button-warning {
    --color: var(--adm-color-warning)
}

.adm-button-block {
    display: block;
    width: 100%
}

.adm-button-disabled {
    cursor: not-allowed;
    opacity: .4
}

.adm-button-disabled:active:before {
    display: none
}

.adm-button.adm-button-mini {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-main)
}

.adm-button.adm-button-mini.adm-button-shape-rounded {
    padding-left: 9px;
    padding-right: 9px
}

.adm-button.adm-button-small {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-7)
}

.adm-button.adm-button-large {
    padding-top: 11px;
    padding-bottom: 11px;
    font-size: var(--adm-font-size-10)
}

.adm-button.adm-button-shape-rounded {
    --border-radius: 1000px
}

.adm-button.adm-button-shape-rectangular {
    --border-radius: 0
}

.adm-button-loading {
    vertical-align: bottom
}

.adm-button-loading-wrapper {
    display: flex;
    height: 1.4em;
    align-items: center;
    justify-content: center
}

.adm-button-loading-wrapper>.adm-loading {
    opacity: .6
}

.adm-dot-loading {
    display: inline-block
}

.adm-center-popup {
    --background-color: var(--adm-center-popup-background-color,var(--adm-color-background));
    --border-radius: var(--adm-center-popup-border-radius,8px);
    --max-width: var(--adm-center-popup-max-width,75vw);
    --min-width: var(--adm-center-popup-min-width,280px);
    --z-index: var(--adm-center-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-center-popup .adm-center-popup-mask {
    z-index: 0
}

.adm-center-popup-wrap {
    position: fixed;
    z-index: 1;
    top: 50%;
    left: 50%;
    width: auto;
    min-width: var(--min-width);
    max-width: var(--max-width);
    transform: translate(-50%,-50%)
}

.adm-center-popup-body {
    background-color: var(--background-color);
    border-radius: var(--border-radius)
}

.adm-center-popup-close {
    position: absolute;
    z-index: 100;
    right: 8px;
    top: 8px;
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    color: var(--adm-color-weak)
}

.adm-mask {
    --z-index: var(--adm-mask-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index);
    display: block
}

.adm-mask,.adm-mask-aria-button {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.adm-mask-aria-button {
    position: absolute;
    z-index: 0;
    pointer-events: none
}

.adm-mask-content {
    z-index: 1
}

.adm-modal {
    --z-index: var(--adm-modal-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-modal .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-modal-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-modal-body>* {
    flex: none
}

.adm-modal-body>.adm-modal-content {
    flex: auto
}

.adm-modal-body:not(.adm-modal-with-image) {
    padding-top: 20px
}

.adm-modal-image-container {
    margin-bottom: 12px;
    max-height: 40vh;
    overflow-y: scroll
}

.adm-modal-header,.adm-modal-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-modal-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-modal-content {
    padding: 0 12px 12px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-modal-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    padding: 8px 12px 12px
}

.adm-modal-footer-empty {
    padding: 0;
    height: 8px
}

.adm-modal-footer.adm-space {
    --gap-vertical: 20px
}

.adm-modal-footer .adm-modal-button {
    font-size: var(--adm-font-size-10);
    line-height: 25px
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary) {
    padding-top: 0;
    padding-bottom: 0
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):before {
    display: none
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):active {
    opacity: .7
}

.adm-space-item {
    flex: none
}

.adm-space {
    display: inline-flex;
    --gap: 8px;
    --gap-vertical: var(--gap);
    --gap-horizontal: var(--gap)
}

.adm-space-vertical {
    flex-direction: column
}

.adm-space-vertical>.adm-space-item {
    margin-bottom: var(--gap-vertical)
}

.adm-space-vertical>.adm-space-item:last-child {
    margin-bottom: 0
}

.adm-space-horizontal {
    flex-direction: row
}

.adm-space-horizontal>.adm-space-item {
    margin-right: var(--gap-horizontal)
}

.adm-space-horizontal>.adm-space-item:last-child {
    margin-right: 0
}

.adm-space-horizontal.adm-space-wrap {
    flex-wrap: wrap;
    margin-bottom: calc(var(--gap-vertical)*-1)
}

.adm-space-horizontal.adm-space-wrap>.adm-space-item {
    padding-bottom: var(--gap-vertical)
}

.adm-space.adm-space-block {
    display: flex
}

.adm-space-align-center {
    align-items: center
}

.adm-space-align-start {
    align-items: flex-start
}

.adm-space-align-end {
    align-items: flex-end
}

.adm-space-align-baseline {
    align-items: baseline
}

.adm-space-justify-center {
    justify-content: center
}

.adm-space-justify-start {
    justify-content: flex-start
}

.adm-space-justify-end {
    justify-content: flex-end
}

.adm-space-justify-between {
    justify-content: space-between
}

.adm-space-justify-around {
    justify-content: space-around
}

.adm-space-justify-evenly {
    justify-content: space-evenly
}

.adm-space-justify-stretch {
    justify-content: stretch
}

.adm-notice-bar {
    --background-color: var(--adm-color-weak);
    --border-color: var(--adm-color-weak);
    --text-color: var(--adm-color-text-light-solid);
    --font-size: var(--adm-font-size-7);
    --icon-font-size: var(--adm-font-size-10);
    --height: 40px;
    --adm-notice-bar-border-radius: 4px;
    --adm-notice-bar-border-width: 1px;
    height: 40px;
    height: var(--height);
    box-sizing: border-box;
    font-size: var(--font-size);
    padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: solid 1px var(--border-color);
    border: solid var(--adm-notice-bar-border-width) var(--border-color);
    border-left-width: 0;
    border-right-width: 0;
    background-color: var(--background-color)
}

.adm-notice-bar,.adm-notice-bar>span[role=img] {
    color: var(--text-color)
}

.adm-notice-bar.adm-notice-bar-alert {
    --background-color: #fff9ed;
    --border-color: #fff3e9;
    --text-color: var(--adm-color-orange)
}

.adm-notice-bar.adm-notice-bar-error {
    --background-color: var(--adm-color-danger);
    --border-color: #d9281e;
    --text-color: #fff
}

.adm-notice-bar.adm-notice-bar-info {
    --background-color: #d0e4ff;
    --border-color: #bcd8ff;
    --text-color: var(--adm-color-primary)
}

.adm-notice-bar.adm-notice-bar-success {
    --background-color: #d1fff0;
    --border-color: #a8f0d8;
    --text-color: var(--adm-color-success)
}

.adm-notice-bar .adm-notice-bar-left {
    flex-shrink: 0;
    margin-right: 8px;
    font-size: var(--icon-font-size)
}

.adm-notice-bar .adm-notice-bar-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    width: auto;
    transition-timing-function: linear;
    white-space: nowrap
}

.adm-notice-bar-wrap.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    white-space: normal
}

.adm-notice-bar .adm-notice-bar-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: 12px
}

.adm-notice-bar-close {
    width: 24px;
    height: 24px;
    margin-right: -3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--adm-font-size-10)
}

.adm-notice-bar-wrap {
    height: auto;
    align-items: flex-start;
    padding-top: 8px;
    padding-bottom: 8px;
    line-height: 22px
}

.adm-notice-bar-neutral {
    border-radius: var(--adm-notice-bar-border-radius)
}

.adm-notice-bar-rounded {
    border-radius: 1000px
}

.adm-notice-bar-bordered {
    border-left-width: var(--adm-notice-bar-border-width);
    border-right-width: var(--adm-notice-bar-border-width)
}

.adm-notice-bar-without-border {
    border-top-width: 0;
    border-bottom-width: 0
}

.adm-popup {
    --z-index: var(--adm-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-popup-body {
    position: fixed;
    background-color: var(--adm-color-background);
    z-index: calc(var(--z-index) + 10)
}

.adm-popup-body .adm-popup-close-icon {
    position: absolute;
    z-index: 100
}

.adm-popup-body-position-bottom {
    width: 100%;
    bottom: 0;
    left: 0
}

.adm-popup-body-position-bottom .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-top {
    width: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-top .adm-popup-close-icon {
    right: 8px;
    bottom: 8px
}

.adm-popup-body-position-left {
    height: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-left .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-right {
    height: 100%;
    top: 0;
    right: 0
}

.adm-popup-body-position-right .adm-popup-close-icon {
    left: 8px;
    top: 8px
}

.adm-popup-close-icon {
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    line-height: 1;
    color: var(--adm-color-weak)
}

.adm-toast-mask .adm-toast-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center
}

.adm-toast-mask .adm-toast-main {
    display: inline-block;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    max-width: 204px;
    max-height: 70%;
    overflow: auto;
    color: #fff;
    word-break: break-all;
    background-color: rgba(0,0,0,.7);
    border-radius: 8px;
    pointer-events: all;
    font-size: var(--adm-font-size-7);
    line-height: 1.5;
    box-sizing: border-box;
    text-align: left;
    text-align: initial
}

.adm-toast-mask .adm-toast-main-text {
    padding: 12px;
    min-width: 0
}

.adm-toast-mask .adm-toast-main-icon {
    padding: 35px 12px;
    min-width: 150px
}

.adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
    text-align: center;
    margin-bottom: 8px;
    font-size: 36px;
    line-height: 1
}

.adm-toast-loading {
    --size: 48px;
    margin: 0 auto 8px
}

.adm-spin-loading {
    --color: var(--adm-color-weak);
    --size: 32px;
    width: 32px;
    width: var(--size);
    height: 32px;
    height: var(--size)
}

.adm-spin-loading-svg {
    width: 100%;
    height: 100%;
    animation: adm-spin-loading-rotate .8s linear infinite
}

.adm-spin-loading-svg>.adm-spin-loading-fill {
    stroke: var(--color)
}

@keyframes adm-spin-loading-rotate {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.index-module_larkConfirm_Hedya {
    text-align: center;
    padding-top: 24px;
    background-color: var(--yq-bg-primary)
}

.index-module_larkConfirm_Hedya.index-module_dark_Bco8K {
    background-color: var(--yq-bg-secondary)
}

.index-module_larkConfirm_Hedya .index-module_title_XgoMG {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    line-height: 24px;
    font-weight: 500
}

.index-module_larkConfirm_Hedya .index-module_content_3IPt2 {
    margin-top: 8px;
    padding: 0 24px;
    font-size: 14px;
    color: var(--yq-yuque-grey-8);
    text-align: left;
    line-height: 22px;
    font-weight: 400
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF {
    margin-top: 24px;
    display: flex
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_cancelBtn_InN4Q {
    flex: 1;
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    cursor: pointer;
    font-weight: 400;
    border-top: 1px solid var(--yq-border-light);
    border-right: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_confirmBtn_O-BLZ {
    flex: 1;
    font-size: 16px;
    color: var(--yq-blue-6);
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-top: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_mobileModalContainer_XqHUd.index-module_dark_Bco8K {
    background: var(--yq-bg-secondary)
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-content] {
    padding: 0
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-footer-empty] {
    height: 0
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_qoTbx {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_qoTbx h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_qoTbx p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2tELh {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_384SJ {
    float: right
}

.DocNoteLimitModal-module_actionBtn_CUQkm {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_H0Qas {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_mnoFp {
    float: right
}

.DocNoteLimitModal-module_mobile_Kb9cX {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_limitFigure_\+Vg0M {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_rightTopPos_-olEi {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA {
    top: 38px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC {
    left: 44px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midTopPos_puaum {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_desc_YO-N4 {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_contentRight_enYYT {
    text-align: right;
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt {
    padding-bottom: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_dashline_hmVnG {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingInfo_C4MGv {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingMainTitle_po2Ep {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingSubTitle_rN0U5 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 .paymodal-module_select_YB\+z4 {
    width: 140px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingDesc_vx9fj {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_priceTag_ByB6F {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWith_dtDdE {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_totalInfo_\+pItg {
    margin: 4px 0 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayIcon_df156 {
    width: 24px;
    margin-top: -2px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayText_zlnDZ {
    padding-left: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 {
    min-height: 444px;
    text-align: center
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_qrcodeImage_PfP82 {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc1_v1ln1 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc2_D8ZQ\+ {
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_link_sWcgh {
    margin-top: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV {
    position: relative;
    min-height: 260px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .ant-spin-spinning {
    margin-top: 130px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc img {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_qrcodeCon_b5rk3 {
    width: 260px;
    height: 260px;
    margin: 0 auto;
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    overflow: hidden
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleDesc_cUN4K {
    font-size: 16px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleValue_4k4b0 {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.paymodal-module_payModalContainer_-7P4F .ant-modal-wrap {
    z-index: 9999
}

@media only screen and (max-width: 768px) {
    .alipay-payment-icon {
        width:36px!important;
        height: 36px!important;
        margin-left: -18px!important;
        margin-top: -18px!important
    }
}

.index-module_mainInput_x4D9t.ant-input-affix-wrapper {
    max-width: 300px;
    width: 172px
}

.index-module_verifyResult_pdxsH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5);
    font-weight: 400
}

.index-module_verifyResult_pdxsH.index-module_success_NhSta {
    color: var(--yq-pea-green-6)
}

.index-module_inputVerify_kl-bC {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-weight: 500
}

.index-module_editIcon_eXpln {
    margin-left: 8px
}

.index-module_promoCodeDefault_6iUh6 {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: var(--yq-text-body);
    font-weight: 400;
    text-align: right;
    cursor: pointer
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*EX5TTI_UeRIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: auto 154px;
    background-position: 0 100%
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_docCountFigure_32PxF {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*w9wrT603NQIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom
}

html[data-kumuhana=pouli] .membermodal-module_rightArea_AY\+Cs {
    background-color: var(--yq-bg-tertiary)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH {
    padding: 0 24px;
    padding-bottom: 10px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr {
    margin-bottom: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr .membermodal-module_link_GhyiF {
    margin-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH h3 {
    font-size: 14px;
    font-weight: 700
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 100px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_tips_hkMrX {
    margin: -12px 0 20px 0;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_titleTips_oWQ8t {
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWith_tqEUb {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 24px;
    margin-top: -2px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_dashline_jMarO {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px;
    line-height: 45px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj.membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r {
    margin-left: 8px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: baseline;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r .membermodal-module_discountPriceValue_a83du {
    font-size: 30px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC {
    margin-left: 20px;
    display: inline-block
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC>label {
    padding-top: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalTermsButton_jCPs1 {
    margin-left: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 16px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r {
    width: 140px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r:nth-child(3n) {
    margin-right: 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r.membermodal-module_selectedUploadCard_VMSYV {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(0,185,107,.05)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadSize_t-9U2 {
    font-size: 16px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackagePrice_YwF2\+ {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t,.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 28px 32px;
    border-radius: 8px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*tZQsTYAY-w8AAAAAAAAAAAAAARQnAQ) no-repeat;
    background-color: var(--yq-yuque-grey-2);
    background-size: auto 154px;
    background-position: 0 100%;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO {
    flex: 1;
    padding-right: 20px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO h2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO h2 {
    font-size: 20px;
    font-weight: 500;
    margin-top: 24px;
    margin-bottom: 6px;
    line-height: 1.75
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe {
    font-size: 14px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span {
    margin-right: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF {
    position: relative;
    width: 268px;
    height: 275px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*2kmNT5ZDTMkAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom;
    margin-left: 10px;
    margin-top: 80px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    position: absolute;
    left: 164px;
    font-size: 14px;
    font-weight: 500;
    transform: translateX(-50%);
    white-space: nowrap
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    top: -40px;
    background: var(--yq-black);
    border-radius: 8px;
    color: var(--yq-white);
    padding: 5px 8px;
    box-shadow: 0 8px 22px -6px rgba(38,38,38,0)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF {
    bottom: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP {
    font-size: 14px;
    line-height: 28px;
    margin-top: 12px;
    min-height: 150px;
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 {
    margin-top: 32px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7 {
    margin-bottom: 40px;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 {
    margin-right: 12px;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-white);
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF {
    flex: 1
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    font-weight: 400;
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs {
    width: 420px;
    background-color: var(--yq-white);
    padding: 24px;
    border-radius: 8px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3 {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0 {
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 80px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector {
    background: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 460px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F {
    margin-bottom: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI {
    margin-bottom: 12px;
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC {
    border: 1px solid transparent;
    background-color: var(--yq-yuque-grey-2);
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS {
    opacity: .5
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn {
    margin-top: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 {
    padding: 10px 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7 {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j {
    padding: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7 {
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh {
    display: block
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc {
    position: absolute;
    right: -1px;
    top: -13px;
    background-image: linear-gradient(to top right,#ff8487,#ff5b5d,#ff4d4f);
    color: #fff;
    border-radius: 8px 8px 8px 1px;
    height: 24px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS {
    display: flex;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4 {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA {
    margin-right: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS {
    font-size: 10px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp {
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-black);
    margin-right: 2px;
    margin-left: 7px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    text-align: right;
    line-height: 20px;
    font-weight: 500;
    margin-right: 2px;
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through;
    margin-left: 5px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu {
    text-align: left
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    margin-top: 6px;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE {
    font-size: 10px;
    display: flex;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh {
    display: none;
    width: 340px;
    margin-top: 13px;
    padding-top: 13px;
    border-top: 1px solid var(--yq-border-light);
    line-height: 25px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH {
    width: 185px;
    font-size: 12px;
    color: var(--yq-yuque-grey-8);
    display: inline-block;
    vertical-align: top
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd),.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd) {
    width: 145px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX {
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb {
    margin: 20px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go {
    font-weight: 500;
    color: var(--yq-yuque-grey-9)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 18px;
    margin-top: -2px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ {
    border-top: 1px solid var(--yq-border-light);
    margin: 10px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj {
    font-size: 20px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY {
    display: inline-block;
    margin-top: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF {
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span {
    padding-right: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw {
    height: 40px;
    font-weight: 500
}

.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 0
}

.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 400px
}

.member-modal .ant-modal-body,.membermodal-module_memberBuyModalH5Container_9ZOm4 {
    padding: 0
}

.member-modal .ant-modal-close-x {
    width: 40px;
    height: 40px;
    line-height: 40px
}

.member-modal .ant-modal-header {
    border-bottom: 0
}

.membermodal-module_selectTip_4qIX9 .membermodal-module_text_orjhF {
    margin-right: 8px
}

.index-module_placeholder_3rydn {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_placeholder_3rydn .index-module_loaded_kAsRx {
    opacity: 1
}

.index-module_placeholder_3rydn img {
    opacity: 0;
    transition: opacity .2s ease-in
}

.index-module_placeholderLoaded_0zPqS {
    background-color: transparent
}

.successmodal-module_successModalContainer_ENOHy {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoCon_EYL4y {
    padding: 0 24px 30px 24px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfo_O8N6K {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoTitle_yXIec {
    font-size: 18px;
    margin-top: 24px;
    font-weight: 500
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu {
    margin-top: 40px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu .successmodal-module_checkUploadButton_vFqCf {
    margin: 1px 0 0 12px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalApplyReceiptButton_Yj2F2 {
    margin-left: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalSubTitle_hVcSK {
    font-size: 14px;
    margin: 16px 0 16px;
    font-weight: 500;
    color: var(--yq-black)
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalItem_zxqQ2 {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 12px 0
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalBlank_tgeED {
    border-top: 1px solid var(--yq-border-light);
    margin-top: 16px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_rightChecked_8E2DG {
    margin-top: -4px;
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.processmodal-module_processModalContainer_jrXul {
    text-align: center;
    padding: 10px 0
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalImg_kmc6C {
    width: 120px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalInfo_fOy8- {
    margin-top: 12px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalOrderId_tDK\+c {
    color: var(--yq-white);
    text-align: center
}

.receipt-module_contactInfoTitle_\+WTvl {
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 10px 0
}

.receipt-module_receiptButtons_tBTuB {
    margin-top: 8px
}

.receipt-module_cancelButton_1xXKN {
    margin-left: 10px
}

.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_contactTips_rQ4lg,.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_receiptTypeTitle_u72yN {
    margin-bottom: 10px
}

.receiptmodal-module_receiptDesc_87YQ3 {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

.receiptmodal-module_receiptDesc_87YQ3 a {
    margin-left: 4px
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_SXXvX {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_SXXvX h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_SXXvX p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_7z-6U {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2ctWH {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_IlCRq {
    float: right
}

.DocNoteLimitModal-module_actionBtn_j4wTK {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_3oyZh {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_5PEmm {
    float: right
}

.DocNoteLimitModal-module_mobile_MQEQW {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_limitFigure_7z-6U {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_rightTopPos_CQ7st {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He {
    top: 38px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz {
    left: 44px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_desc_DQeXN {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.index-module_modal_ciiIL .ant-modal-content {
    overflow: hidden
}

.index-module_modal_ciiIL .ant-modal-body {
    padding: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-btns {
    display: none
}

.index-module_modalDesktop_CvNi7 {
    max-width: 100vw!important
}

.index-module_modalDesktop_CvNi7 .ant-modal-content {
    overflow: hidden
}

.index-module_modalDesktop_CvNi7 .ant-modal-body {
    padding: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-btns {
    display: none
}

.head-user-info-popover .member-badge-container {
    padding: 0
}

.member-badge-desc {
    margin-top: -5px;
    margin-bottom: 12px
}

.member-badge-upgrade-action {
    color: var(--yq-text-link)!important
}

.badge-module_memberBadgeContainer_truSH {
    padding: 16px 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 1
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_safeIcon_LvS1y {
    position: relative;
    top: -1px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_unsafeIcon_fj7tW {
    margin-left: 2px;
    position: relative;
    top: -10px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeNameCon_UOZEP {
    line-height: 24px;
    display: inline-block;
    max-width: 450px;
    margin-right: 4px;
    word-break: break-all;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO {
    position: relative;
    top: 0;
    letter-spacing: -1em;
    vertical-align: top;
    height: 100%;
    display: inline-block;
    margin-top: -1px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO:before {
    content: "\3000"
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO a {
    display: inline
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeDesc_nc5JL {
    line-height: 1;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeEnDesc_xoJbF {
    line-height: 1.5;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeDisabled_YFT6S {
    color: var(--yq-text-disable);
    cursor: not-allowed;
    margin-left: 8px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block;
    font-size: 12px;
    color: var(--yq-text-link)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr:hover {
    color: var(--yq-ant-link-hover-color)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.badge-module_memberBadgeTooltip_JvMu7 {
    max-width: 400px
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.OrgCertification-module_wrap_mcHiA {
    display: flex;
    align-items: center
}

.OrgCertification-module_wrap_mcHiA .larkui-tooltip {
    display: flex;
    align-items: center;
    justify-content: center
}

.OrgCertification-module_wrap_mcHiA .larkui-popover-trigger {
    display: flex;
    align-items: center
}

.OrgCertification-module_btn_veUok {
    padding: 0;
    font-size: 14px
}

.OrgCertification-module_icon_mgkVd {
    margin-right: 6px;
    margin-left: 2px;
    cursor: pointer
}

.OrgCertification-module_text_JDkK6 {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 14px
}

.OrgCertification-module_wrap_mcHiA .OrgCertification-module_main_umJwo {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.OrgCertification-module_wrap_mcHiA .OrgCertification-module_main_umJwo a {
    margin: 0;
    line-height: 30px
}

.OrgCertification-module_disabled_DMrOf {
    filter: grayscale(1)
}

.index-module_orgCostDetail_cLnEB {
    cursor: pointer;
    font-weight: 500
}

.index-module_content_oAhH8 {
    width: 378px
}

.index-module_content_oAhH8 .ant-divider {
    margin: 12px 0
}

.index-module_row_by2z2 {
    display: flex;
    justify-content: space-between;
    padding: 6px 0
}

.index-module_row_by2z2:last-child {
    font-weight: 700
}

.index-module_row_by2z2 .index-module_right_3Ce8Y {
    display: flex
}

.index-module_tips_EUcPA {
    padding: 2px 0;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_title_J6b5y {
    padding: 8px 8px 8px 0
}

.index-module_title_J6b5y a {
    color: var(--yq-text-caption)
}

.index-module_hasDiscount_TqEsK {
    color: var(--yq-orange-7)
}

.index-module_rawPrice_2UTkU {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_detailsWrapper_rnkfa .index-module_detailTitle_-gjun {
    color: var(--yq-text-primary)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt {
    height: 72px;
    max-height: 80px;
    overflow-y: auto
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK span:nth-child(2n) {
    color: var(--yq-text-body)
}

.index-module_summary_TYNAM {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_summary_TYNAM .index-module_left_m83qX {
    display: block
}

.index-module_summary_TYNAM .index-module_left_m83qX .index-module_summaryTitle_FTeFf {
    color: var(--yq-text-primary)
}

.index-module_summary_TYNAM .index-module_left_m83qX span {
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_summary_TYNAM .index-module_right_3Ce8Y .index-module_price_5CyQB {
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_divider_Rppou {
    margin: 12px auto
}

.index-module_paymentSelector_60wvq {
    margin: 0 0 24px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_paymentSelector_60wvq h4 {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e {
    position: relative;
    padding: 16px 10px;
    height: 100%;
    border-radius: 8px;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 {
    display: flex;
    justify-content: space-between
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_icon_sjp\+e {
    margin-left: 6px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_paymentType_rBxoj {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceNumber_6EuVJ {
    color: var(--yq-text-primary);
    font-size: 12px;
    font-weight: 500
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceUnit_xqqPQ {
    color: var(--yq-text-caption);
    font-size: 10px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_oldMemberPrice_4QJhr {
    display: block;
    text-align: right;
    font-size: 10px;
    color: var(--yq-text-caption);
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_desc_5wzpx {
    position: absolute;
    bottom: 16px;
    margin-top: 6px;
    width: 160px;
    font-size: 12px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_active_HZTq5 {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-theme)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: hsla(0,0%,100%,.5);
    cursor: not-allowed
}

html[data-kumuhana=pouli] .index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    background-color: rgba(0,0,0,.5)
}

.index-module_tooltip_lMvUZ {
    font-size: 14px;
    white-space: nowrap
}

.index-module_tooltip_lMvUZ a {
    margin-left: 8px
}

.BuyModal-module_row_k9bGD {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyModal-module_row_k9bGD h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 {
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 span {
    color: var(--yq-text-caption)
}

.BuyModal-module_row_k9bGD .BuyModal-module_right_-\+dqF {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq {
    display: flex;
    align-items: center
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq svg {
    margin-right: 6px
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payBtn_JujzZ {
    margin: 12px auto;
    width: 100%
}

.BuyModal-module_promoCode_IT7ha a,.BuyModal-module_promoCode_IT7ha a:hover {
    color: var(--yq-text-primary)
}

.BuyModal-module_tips_fuHYC {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingContainer_tqVEA {
    display: flex;
    justify-content: space-between;
    padding: 32px;
    min-height: 688px;
    background-color: var(--yq-yuque-grey-2);
    background-position: 0 100%;
    background-repeat: no-repeat;
    border-radius: 8px
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*z00vQYi5mvAAAAAAAAAAAAAAARQnAQ);
    background-size: 274px 215px
}

html[data-kumuhana=pouli] .BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*4iv1S4HXxg4AAAAAAAAAAAAADvuFAQ/original)
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_enterprise_KQPTt {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*h_IRR6H2EB8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 238px 170px
}

.BuyModal-module_pricingLeft_X4ns6 {
    width: 324px;
    padding-top: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG .BuyModal-module_icon_UPf-t {
    margin-left: 8px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentDesc_7e1Uv {
    margin: 4px 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL {
    display: inline-block;
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL .BuyModal-module_icon_UPf-t {
    margin-left: 0;
    vertical-align: text-top
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe {
    margin-bottom: 40px;
    position: relative;
    padding-left: 48px;
    height: 46px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_iconWarpper_j2vhb {
    position: absolute;
    top: 50%;
    left: 0;
    width: 36px;
    height: 36px;
    transform: translateY(-50%);
    background-color: var(--yq-bg-primary);
    box-shadow: 0 2px 9px 0 rgba(0,0,0,.02);
    border-radius: 6px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_icon_UPf-t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_title_aoygt {
    margin-bottom: 4px;
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_desc_nWmKR {
    color: var(--yq-text-caption);
    font-size: 12px
}

.BuyModal-module_pricingRight_p54lJ {
    position: relative;
    padding: 24px 24px 98px 24px;
    width: 420px;
    min-height: 624px;
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .BuyModal-module_pricingRight_p54lJ {
    background: var(--yq-bg-tertiary)
}

.BuyModal-module_avatar_SRhTt {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background-color: var(--yq-bg-tertiary)
}

.BuyModal-module_termsContainer_i187T span {
    color: var(--yq-text-body)!important
}

.BuyModal-module_termsContainer_i187T .ant-checkbox+span {
    padding-right: 0
}

.BuyModal-module_statsWrapper_ZDvF1 .BuyModal-module_statsDesc_1iKOA {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 28px
}

.BuyModal-module_compact_qVAW5 h3 {
    margin-bottom: 4px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_active_SC0g\+ {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_settle_6sm-p {
    margin-left: 12px
}

.BuyModal-module_channel_aD1hq button[disabled] {
    padding: 4px 12px;
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyModal-module_channel_aD1hq button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.ant-btn.BuyModal-module_channel_aD1hq {
    padding: 4px 12px
}

.BuyModal-module_name_bL3Ia {
    max-width: 230px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BuyModal-module_memberSizeInput_xIYHO {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.BuyModal-module_memberSizeInput_xIYHO .BuyModal-module_errorMsg_ZbqFE {
    position: absolute;
    bottom: -24px;
    max-width: 200px;
    white-space: nowrap;
    color: var(--yq-function-error);
    font-weight: 400;
    font-size: 14px
}

.BuyModal-module_footer_eF3uw {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 48px);
    transform: translateX(-50%)
}

.BuyMember-module_row_9srYf {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyMember-module_row_9srYf h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og {
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og span {
    color: var(--yq-text-caption)
}

.BuyMember-module_row_9srYf .BuyMember-module_right_Oni7O {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT {
    display: flex;
    align-items: center
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT svg {
    margin-right: 6px
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payBtn_2QBqR {
    margin: 12px auto;
    width: 100%
}

.BuyMember-module_promoCode_3q8my a,.BuyMember-module_promoCode_3q8my a:hover {
    color: var(--yq-text-primary)
}

.BuyMember-module_tips_0MaeW {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyMember-module_pricingContainer_oSImw h2 {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_expiredDesc_kyKaA {
    margin-bottom: 24px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BuyMember-module_channel_oCioT.BuyMember-module_active_hyrgK {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyMember-module_channel_oCioT button[disabled] {
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyMember-module_channel_oCioT button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_right_Oni7O>:not(:first-child) {
    margin-left: 12px
}

.Pay-module_row_02i1B {
    padding-bottom: 24px
}

.Pay-module_row_02i1B:nth-child(6) {
    padding-bottom: 0
}

.Pay-module_row_02i1B .Pay-module_left_u3-Jq {
    padding-bottom: 8px
}

.Pay-module_row_02i1B .Pay-module_name_4PPzr {
    font-size: 16px;
    color: var(--yq-text-primary);
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px
}

.Pay-module_row_02i1B .Pay-module_nameRow_iKvRZ {
    display: flex
}

.Pay-module_row_02i1B .Pay-module_version_C36aU {
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-8);
    border-radius: 4px;
    font-size: 12px;
    padding: 1px 3px;
    background-color: var(--yq-blue-1)
}

.Pay-module_row_02i1B .Pay-module_version_C36aU.Pay-module_paidVersion_pBnh8 {
    color: var(--yq-yellow-7);
    border-color: var(--yq-yellow-7);
    background-color: var(--yq-yellow-1)
}

.Pay-module_cardList_OD1er {
    display: flex;
    margin-bottom: 24px
}

.Pay-module_memberSize_Snqz8 {
    overflow: hidden;
    opacity: 0;
    height: 0;
    transition: all .3s
}

.Pay-module_memberSize_Snqz8.Pay-module_show_u86Cd {
    opacity: 1;
    height: 81px
}

.Pay-module_totalInfo_YM7Fb {
    height: 45px;
    margin: 4px 0 18px 0;
    flex-wrap: wrap;
    font-weight: 600
}

.Pay-module_footer_MsuSK,.Pay-module_totalInfo_YM7Fb {
    display: flex;
    align-items: center
}

.Pay-module_footer_MsuSK .Pay-module_termsContainer_MMlAf {
    margin-left: 12px;
    display: flex
}

.Pay-module_pricingContainer_hjkUd {
    padding-bottom: 8px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_desc_a1fXI {
    margin-top: -20px;
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_dashline_y-S0k {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 24px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingInfo_NRstM {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingMainTitle_pinlM {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingSubTitle_FlFF9 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_inputNumber_Uaz26 {
    height: 32px;
    width: 200px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingDesc_SK8fY {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz {
    font-size: 28px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 45px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz.Pay-module_hasDiscount_KLNoP {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz {
    margin-left: 16px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz .Pay-module_discountPriceValue_CUsg2 {
    font-size: 28px;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWith_vFDxq {
    margin: 8px 0 20px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayIcon_SjV5X {
    width: 24px;
    margin-top: -2px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayText_r3AWK {
    padding-left: 8px
}

.Pay-module_uploadPackages_VO59r {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 32px
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL {
    width: 160px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL:nth-child(3n) {
    margin-right: 0
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL.Pay-module_selectedUploadCard_yWd2Y {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(37,184,100,.05)
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadSize_WpwBc {
    font-size: 16px;
    font-weight: 600
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackagePrice_zQPrm {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_buyMemberCon_CB8\+C {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q,.Pay-module_buyMemberCon_CB8\+C .Pay-module_upgradeMember_6duT1 {
    background-color: var(--yq-bg-secondary);
    padding: 16px;
    width: 244px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q .Pay-module_memberValue_l6w8U {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0
}

.Pay-module_tips_BIW\+T {
    margin-top: 6px;
    color: var(--yq-text-caption);
    width: 500px
}

.Pay-module_discountInfo_Uq4\+K {
    color: var(--yq-text-caption);
    padding-bottom: 8px
}

.Pay-module_promo_sD6ap {
    margin-top: -6px
}

.Pay-module_promo_sD6ap .Pay-module_left_u3-Jq {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 120px
}

.Pay-module_promo_sD6ap .Pay-module_inputNumber_Uaz26 {
    padding-top: 8px;
    padding-bottom: 40px
}

.Qrcode-module_info_XwHqZ {
    flex: auto;
    max-width: 332px
}

.Qrcode-module_infoTitle_nNa5e {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500
}

.Qrcode-module_subInfoTip_DYh05 {
    color: var(--yq-text-body);
    margin-top: 10px
}

.Qrcode-module_infoTip_wp2zv>span {
    margin-right: 28px
}

@media only screen and (max-width: 575px) {
    .Qrcode-module_infoTip_wp2zv>span {
        display:block
    }
}

.Qrcode-module_desc_T1-nf {
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi {
    padding-bottom: 8px
}

.Qrcode-module_pricingContainer_2\+hyi h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_dashline_n20jZ {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingInfo_Pe1Ai {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingMainTitle_D0fu0 {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingSubTitle_3SVt2 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P .Qrcode-module_select_2Topv {
    width: 140px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingDesc_aTeur {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_priceTag_gSsWI {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWith_Orohw {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_totalInfo_-\+rtG {
    margin: 4px 0 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayIcon_Hoi7K {
    width: 24px;
    margin-top: -2px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayText_wbuGd {
    padding-left: 8px
}

.Qrcode-module_processContainer_5BqWV {
    text-align: center;
    padding: 10px 0
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processImg_MgbCi {
    width: 120px
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processInfo_ST\+BP {
    margin-top: 12px
}

.Qrcode-module_termsContainer_Uqaqj {
    margin-bottom: 12px;
    display: flex
}

.Qrcode-module_bindingContainer_b3OnQ {
    padding: 24px 0;
    text-align: center
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingImg_sEWSW {
    width: 80px
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingInfo_qhBjF {
    color: var(--yq-text-primary);
    margin: 24px 0
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingFooter_7PjRS .Qrcode-module_bindingVerify_CgNHw {
    margin-right: 8px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi {
    position: relative
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 img {
    width: 100%
}

@media only screen and (max-width: 768px) {
    .Qrcode-module_qrcodeImageCon_yy\+Gi .alipayIconInQrCode {
        width:36px;
        height: 36px;
        margin-left: -18px;
        margin-top: -18px
    }
}

.Qrcode-module_successContainer_RvR5s {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfoCon_Yt35q {
    padding: 10px 40px 30px 40px
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfo_w7k6O {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.Qrcode-module_qrContainer_K9uEQ {
    margin-bottom: 20px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeImg_GAmpl {
    width: 72px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeInfo_8NXdM {
    font-size: 14px;
    font-weight: 400;
    padding: 24px 0 0 30px;
    color: var(--yq-text-body);
    line-height: 1.8
}

.Qrcode-module_qrcodeContainer_c79eX {
    min-height: 444px;
    text-align: center
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_qrcodeImage_UEIyZ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    width: 55%
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc1_ArZR9 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc2_E5sDl {
    margin-top: 10px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_link_ZEjlM {
    margin-top: 8px
}

.Qrcode-module_receiptTypeTitle_jIYOl {
    margin-bottom: 10px
}

.Qrcode-module_receiptTypeTitleText_8q0XQ {
    margin-bottom: 8px
}

.Qrcode-module_applyReceiptButton_Wfqmq {
    margin-left: 10px
}

.Qrcode-module_contactTips_ETe9S {
    margin-bottom: 10px
}

.Qrcode-module_buttonCon_Jic-k {
    margin-top: 8px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleDesc_OOQby {
    font-size: 16px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleValue_PBwE\+ {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.PayModal-module_noLineModal_r4djn .ant-modal-header {
    border-bottom: 0
}

.PayModal-module_warnContent_TTDov {
    margin-top: 20px;
    margin-bottom: 24px
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-body {
    padding: 0
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px
}

.ProcessingModal-module_processContainer_dOZ9t {
    text-align: center;
    padding: 10px 0
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processImg_Fl\+7o {
    width: 120px
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processInfo_VPh0u {
    margin-top: 12px
}

.paid-success-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.paid-success-modal .ant-modal-body {
    padding: 0
}

.paid-success-modal .ant-modal-body img {
    width: 100%
}

.paid-success-modal .ant-col {
    padding-top: 10px
}

.paid-success-modal .ant-col-18 {
    font-weight: 600
}

.PaidSuccessModal-module_successModalContainer_gb6Iw {
    color: var(--yq-text-body);
    border-radius: 8px;
    overflow: hidden
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoCon_aLoy0 {
    padding: 20px 24px 30px 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfo_TQeHl {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoTitle_fmm0I {
    font-size: 28px;
    margin-top: 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns {
    margin-top: 32px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_applyReceiptButton_BrptN {
    margin-left: 12px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_checkUploadButton_n0Sct {
    margin-left: 12px;
    margin-top: 1px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalApplyReceiptButton_hbmRn {
    margin-left: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalSubTitle_Hq8oy {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalItem_EBnTl {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalBlank_3ENdm {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.ReceiptModal-module_contactTips_f0fAJ,.ReceiptModal-module_receiptTypeTitle_mZi8G {
    margin-bottom: 10px
}

.ReceiptModal-module_receiptTypeTitleText_QQBhY {
    margin-bottom: 8px
}

.ReceiptModal-module_receiptDesc_fY2bw {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

@keyframes SubAccountInfoModal-module_loadingCircle_mPmQ5 {
    to {
        transform: rotate(1turn)
    }
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p:first-child {
    border-bottom: 1px dashed var(--yq-border-primary);
    padding-bottom: 16px;
    margin-bottom: 32px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldTitle_tgwmv {
    display: inline-block;
    font-weight: 600;
    min-width: 80px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p p {
    margin-bottom: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldPaymentAlert_UOLk7 {
    margin-bottom: 20px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e {
    margin-top: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e .SubAccountInfoModal-module_btn_WuNy4 {
    margin-right: 16px
}

.SubAccountInfoModal-module_loading_sNKQL {
    padding: 32px 0;
    text-align: center
}

.SubAccountInfoModal-module_loading_sNKQL .SubAccountInfoModal-module_loadingIcon_M7IJr {
    animation: SubAccountInfoModal-module_loadingCircle_mPmQ5 1s linear infinite
}

.SubAccountInfoModal-module_loading_sNKQL .larkui-icon {
    font-size: 24px
}

.SubAccountInfoModal-module_loadingDesc_QHGbP {
    margin-top: 16px
}

.index-module_buyButton_pN7y0.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_buyButton_pN7y0a.ant-btn span {
    line-height: 1
}

.index-module_upgradeButton_nJqrZ.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_upgradeButton_nJqrZa.ant-btn span {
    line-height: 1
}

.index-module_orgVersionLabel_egPu3 {
    border-width: 1px;
    border-radius: 4px;
    border-style: solid;
    padding: 0 4px;
    margin-left: 16px
}

.index-module_tip_rQx-e {
    margin-top: 2px;
    margin-bottom: 2px;
    display: flex;
    justify-content: space-between
}

.index-module_tip_rQx-e a:before {
    position: relative
}

.index-module_tip_rQx-e span span {
    margin-left: 12px
}

.index-module_tip_rQx-e .index-module_payment_pQExj {
    color: var(--yq-yuque-grey-8)
}

.index-module_tip_rQx-e .index-module_paymentLink_GbvgZ {
    color: var(--yq-blue-6);
    line-height: 60px
}

.index-module_paymentGuideWrapper_2jg39 {
    max-width: 584px;
    margin-left: auto;
    margin-right: auto;
    padding: 24px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_thumb_Lw5zW {
    display: block;
    width: 148px;
    height: 120px;
    margin: 0 auto 16px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100%
}

.index-module_paymentGuideWrapper_2jg39 .index-module_title_EykNL {
    text-align: center;
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 700
}

.index-module_paymentGuideWrapper_2jg39 .index-module_body_\+1-cr {
    margin: 12px auto 32px auto;
    max-width: 400px;
    color: var(--yq-text-body);
    text-align: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_link_zDdoa {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB {
    display: flex;
    align-items: center;
    justify-content: end
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB .index-module_btnTryout_5GVmK {
    margin-right: 8px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_btnTryout_5GVmK {
    margin-bottom: 12px;
    width: 148px;
    height: 32px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_link_zDdoa {
    width: 148px;
    text-align: center
}

.index-module_actions_LRgdL .index-module_btn_vRXZD {
    width: 160px;
    height: 32px
}

.index-module_premiumFeaturesLabel_q\+uFd {
    width: 86px;
    height: 37px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*pIrCQLn06McAAAAAAAAAAAAAARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_alignCenter_h648T {
    display: flex;
    align-items: center;
    position: relative
}

.index-module_orgExpiredTip_k7iLg {
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgExpiredTipOpt_QDhkZ,.index-module_orgExpiredTipText_gQuZw {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    width: 140px
}

.index-module_orgExpiredTipText_gQuZw {
    white-space: nowrap
}

.index-module_orgExpiredTipText_gQuZw span {
    color: var(--yq-yuque-grey-9)
}

.index-module_payflowTitle_LM3aR {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px
}

.index-module_freeWrapper_BlI2K {
    display: flex;
    justify-content: space-between
}

.index-module_payflowLink_Wt3fM {
    margin-left: 0;
    display: inline-block
}

.index-module_isExpired_UgOgs {
    border-bottom: 1px solid var(--yq-yuque-grey-4);
    padding-bottom: 10px
}

.index-module_freeTips_ybaQ- {
    display: flex;
    justify-content: space-between
}

.placeholder-avatar {
    display: inline-block;
    border-radius: 50%;
    color: var(--yq-white);
    text-align: center
}

.index-module_noWrap_glNxF {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%
}

.index-module_center_zSt9A {
    text-align: center
}

.index-module_inherit_TQ-oW {
    text-align: inherit
}

.index-module_justify_BBNTE {
    text-align: justify
}

.index-module_left_melIP {
    text-align: left
}

.index-module_right_BcDRZ {
    text-align: right
}

.index-module_common-link_LkRIl {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_common-link_LkRIl.active,.index-module_common-link_LkRIl:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-link_LkRIl:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_common-tab_EdL1P {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-menu_OiYt2 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_common-menu_OiYt2.active,.index-module_common-menu_OiYt2:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_common-icon_60unB {
    display: inline-flex;
    align-items: center
}

.index-module_common-flex_YpvU1 {
    display: flex
}

.index-module_common-iconButton_9fUSh {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_common-iconButton_9fUSh svg {
    z-index: 1
}

.index-module_common-iconButton_9fUSh:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_common-iconButton_9fUSh:hover {
    color: var(--yq-text-primary)
}

.index-module_common-iconButton_9fUSh:hover:after {
    visibility: visible
}

.index-module_common-menu-item_wENsl {
    display: flex;
    align-items: center
}

.index-module_headline1_gNn3d {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 32px;
    line-height: 40px
}

.index-module_headline1-link_hpiWt {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline1-link_hpiWt.active,.index-module_headline1-link_hpiWt:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-link_hpiWt:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline1-tab_2tnKG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-menu_kn1nZ {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline1-menu_kn1nZ.active,.index-module_headline1-menu_kn1nZ:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline1-icon_Ugsfz {
    display: inline-flex;
    align-items: center
}

.index-module_headline1-flex_XKxAU {
    display: flex
}

.index-module_headline1-iconButton_JxTPN {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline1-iconButton_JxTPN svg {
    z-index: 1
}

.index-module_headline1-iconButton_JxTPN:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline1-iconButton_JxTPN:hover {
    color: var(--yq-text-primary)
}

.index-module_headline1-iconButton_JxTPN:hover:after {
    visibility: visible
}

.index-module_headline1-menu-item_6KtqL {
    display: flex;
    align-items: center
}

.index-module_headline1-secondary_oSaxw {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline2_xyzBy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 24px;
    line-height: 32px
}

.index-module_headline2-link_bkLCB {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline2-link_bkLCB.active,.index-module_headline2-link_bkLCB:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-link_bkLCB:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline2-tab_n1Akb {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-menu_QL2po {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline2-menu_QL2po.active,.index-module_headline2-menu_QL2po:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline2-icon_Ge-vk {
    display: inline-flex;
    align-items: center
}

.index-module_headline2-flex_KZxEZ {
    display: flex
}

.index-module_headline2-iconButton_uBIj0 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline2-iconButton_uBIj0 svg {
    z-index: 1
}

.index-module_headline2-iconButton_uBIj0:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline2-iconButton_uBIj0:hover {
    color: var(--yq-text-primary)
}

.index-module_headline2-iconButton_uBIj0:hover:after {
    visibility: visible
}

.index-module_headline2-menu-item_1iIzo {
    display: flex;
    align-items: center
}

.index-module_headline2-secondary_t1vqD {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline3_6OwWy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 18px;
    line-height: 26px
}

.index-module_headline3-link_wcUG4 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline3-link_wcUG4.active,.index-module_headline3-link_wcUG4:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-link_wcUG4:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline3-tab_GRz8p {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-menu_pdEQ6 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline3-menu_pdEQ6.active,.index-module_headline3-menu_pdEQ6:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline3-icon_maylx {
    display: inline-flex;
    align-items: center
}

.index-module_headline3-flex_EdA3C {
    display: flex
}

.index-module_headline3-iconButton_ljBJY {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline3-iconButton_ljBJY svg {
    z-index: 1
}

.index-module_headline3-iconButton_ljBJY:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline3-iconButton_ljBJY:hover {
    color: var(--yq-text-primary)
}

.index-module_headline3-iconButton_ljBJY:hover:after {
    visibility: visible
}

.index-module_headline3-menu-item_FneW1 {
    display: flex;
    align-items: center
}

.index-module_headline3-secondary_gdmSF {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline4_cCWAs {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 16px;
    line-height: 24px
}

.index-module_headline4-link_pksrg {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline4-link_pksrg.active,.index-module_headline4-link_pksrg:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-link_pksrg:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline4-tab_AZhw7 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-menu_Zh\+Gf {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline4-menu_Zh\+Gf.active,.index-module_headline4-menu_Zh\+Gf:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline4-icon_I8A2c {
    display: inline-flex;
    align-items: center
}

.index-module_headline4-flex_-WCmH {
    display: flex
}

.index-module_headline4-iconButton_unM1q {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline4-iconButton_unM1q svg {
    z-index: 1
}

.index-module_headline4-iconButton_unM1q:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline4-iconButton_unM1q:hover {
    color: var(--yq-text-primary)
}

.index-module_headline4-iconButton_unM1q:hover:after {
    visibility: visible
}

.index-module_headline4-menu-item_HZvZs {
    display: flex;
    align-items: center
}

.index-module_headline4-secondary_mwc2q {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_title_AMQeB {
    font-weight: 400;
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 22px
}

.index-module_title-link_LhWVV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_title-link_LhWVV.active,.index-module_title-link_LhWVV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-link_LhWVV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_title-tab_PEcOM {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-menu_yRd7C {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_title-menu_yRd7C.active,.index-module_title-menu_yRd7C:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_title-icon_jD1r3 {
    display: inline-flex;
    align-items: center
}

.index-module_title-flex_6ud\+f {
    display: flex
}

.index-module_title-iconButton_eKfGH {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_title-iconButton_eKfGH svg {
    z-index: 1
}

.index-module_title-iconButton_eKfGH:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_title-iconButton_eKfGH:hover {
    color: var(--yq-text-primary)
}

.index-module_title-iconButton_eKfGH:hover:after {
    visibility: visible
}

.index-module_title-menu-item_\+2INX {
    display: flex;
    align-items: center
}

.index-module_title-secondary_\+7kF8 {
    color: var(--yq-text-body)
}

.index-module_title-strong_rB2kd {
    font-weight: 500
}

.index-module_body_0t8B1 {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_body-link_1Ezeh {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-link_1Ezeh.active,.index-module_body-link_1Ezeh:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-link_1Ezeh:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-tab_ZArAR {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-menu_rkEZj {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-menu_rkEZj.active,.index-module_body-menu_rkEZj:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-icon_dtRXT {
    display: inline-flex;
    align-items: center
}

.index-module_body-flex_xUh97 {
    display: flex
}

.index-module_body-iconButton_zEAAb {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-iconButton_zEAAb svg {
    z-index: 1
}

.index-module_body-iconButton_zEAAb:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-iconButton_zEAAb:hover {
    color: var(--yq-text-primary)
}

.index-module_body-iconButton_zEAAb:hover:after {
    visibility: visible
}

.index-module_body-menu-item_530BT {
    display: flex;
    align-items: center
}

.index-module_body-secondary_jzKzN {
    color: var(--yq-text-caption)
}

.index-module_body-strong_B8Xyf {
    font-weight: 500
}

.index-module_body-small_M0nB9 {
    font-weight: 400;
    color: var(--yq-text-body);
    font-size: 12px;
    line-height: 20px
}

.index-module_body-small-link_dpTlG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-small-link_dpTlG.active,.index-module_body-small-link_dpTlG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-link_dpTlG:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-small-tab_HAI-k {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-menu_\+sZ91 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-small-menu_\+sZ91.active,.index-module_body-small-menu_\+sZ91:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-small-icon_9yvQH {
    display: inline-flex;
    align-items: center
}

.index-module_body-small-flex_TzPYW {
    display: flex
}

.index-module_body-small-iconButton_Ry5bn {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-small-iconButton_Ry5bn svg {
    z-index: 1
}

.index-module_body-small-iconButton_Ry5bn:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-small-iconButton_Ry5bn:hover {
    color: var(--yq-text-primary)
}

.index-module_body-small-iconButton_Ry5bn:hover:after {
    visibility: visible
}

.index-module_body-small-menu-item_5h34h {
    display: flex;
    align-items: center
}

.index-module_body-small-secondary_dJo56 {
    color: var(--yq-text-caption)
}

.index-module_disable_bsdL7 {
    font-weight: 400;
    color: var(--yq-text-disable);
    font-size: 14px;
    line-height: 22px
}

.index-module_disable-link_fsP5Q {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-link_fsP5Q.active,.index-module_disable-link_fsP5Q:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-link_fsP5Q:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-tab_1L8n1 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-menu_h0UA9 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-menu_h0UA9.active,.index-module_disable-menu_h0UA9:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-icon_szLm2 {
    display: inline-flex;
    align-items: center
}

.index-module_disable-flex_EDI9g {
    display: flex
}

.index-module_disable-iconButton_41jru {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-iconButton_41jru svg {
    z-index: 1
}

.index-module_disable-iconButton_41jru:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-iconButton_41jru:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-iconButton_41jru:hover:after {
    visibility: visible
}

.index-module_disable-menu-item_tolga {
    display: flex;
    align-items: center
}

.index-module_caption_bWso- {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 14px;
    line-height: 22px
}

.index-module_caption-link_iUcKX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-link_iUcKX.active,.index-module_caption-link_iUcKX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-link_iUcKX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-tab_Pahmo {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-menu_FYIeq {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-menu_FYIeq.active,.index-module_caption-menu_FYIeq:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-icon_Y3NyS {
    display: inline-flex;
    align-items: center
}

.index-module_caption-flex_FI2Np {
    display: flex
}

.index-module_caption-iconButton_hWPqs {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-iconButton_hWPqs svg {
    z-index: 1
}

.index-module_caption-iconButton_hWPqs:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-iconButton_hWPqs:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-iconButton_hWPqs:hover:after {
    visibility: visible
}

.index-module_caption-menu-item_1kLnU {
    display: flex;
    align-items: center
}

.index-module_caption-secondary_SFdcH {
    color: var(--yq-text-disable)
}

.index-module_caption-small_B13mr {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 12px;
    line-height: 20px
}

.index-module_caption-small-link_8T\+\+a {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-small-link_8T\+\+a.active,.index-module_caption-small-link_8T\+\+a:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-link_8T\+\+a:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-small-tab_ibUZP {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-menu_G8H-J {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-small-menu_G8H-J.active,.index-module_caption-small-menu_G8H-J:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-small-icon_PtjdH {
    display: inline-flex;
    align-items: center
}

.index-module_caption-small-flex_\+EFJk {
    display: flex
}

.index-module_caption-small-iconButton_33CTf {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-small-iconButton_33CTf svg {
    z-index: 1
}

.index-module_caption-small-iconButton_33CTf:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-small-iconButton_33CTf:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-small-iconButton_33CTf:hover:after {
    visibility: visible
}

.index-module_caption-small-menu-item_cqmp0 {
    display: flex;
    align-items: center
}

.index-module_caption-small-secondary_H2H1r {
    color: var(--yq-text-disable)
}

.index-module_primary-button-default_Vw1-y {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white)
}

.index-module_primary-button-default-secondary_R\+QUT {
    color: var(--yq-text-caption)
}

.index-module_primary-button-default-strong_Htk5A {
    font-weight: 500
}

.index-module_primary-button-default-link_oQhLa {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-default-link_oQhLa.active,.index-module_primary-button-default-link_oQhLa:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-link_oQhLa:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-default-tab_rf8zH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-menu_JESyh {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-default-menu_JESyh.active,.index-module_primary-button-default-menu_JESyh:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-default-icon_E0TCd {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-default-flex_pNGzV {
    display: flex
}

.index-module_primary-button-default-iconButton_bguHS {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-default-iconButton_bguHS svg {
    z-index: 1
}

.index-module_primary-button-default-iconButton_bguHS:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-default-iconButton_bguHS:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-iconButton_bguHS:hover:after {
    visibility: visible
}

.index-module_primary-button-default-menu-item_EY5rA {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong_tCPsX {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white);
    font-weight: 500
}

.index-module_primary-button-strong-secondary_rR6X1 {
    color: var(--yq-text-caption)
}

.index-module_primary-button-strong-strong_KuhtF {
    font-weight: 500
}

.index-module_primary-button-strong-link_FFk3N {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-strong-link_FFk3N.active,.index-module_primary-button-strong-link_FFk3N:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-link_FFk3N:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-strong-tab_iu42A {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-menu_MUMyr {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-strong-menu_MUMyr.active,.index-module_primary-button-strong-menu_MUMyr:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-strong-icon_-xloa {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-strong-flex_uWzkw {
    display: flex
}

.index-module_primary-button-strong-iconButton_f6r63 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-strong-iconButton_f6r63 svg {
    z-index: 1
}

.index-module_primary-button-strong-iconButton_f6r63:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-strong-iconButton_f6r63:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-iconButton_f6r63:hover:after {
    visibility: visible
}

.index-module_primary-button-strong-menu-item_RzDwe {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong-ghost_LfbnS {
    color: var(--yq-yuque-green-600)
}

.index-module_secondary-button-default_nN4ts {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-link_P4JJV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_secondary-button-default-link_P4JJV.active,.index-module_secondary-button-default-link_P4JJV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-link_P4JJV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_secondary-button-default-tab_sTKaj {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-menu_CnLQt {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_secondary-button-default-menu_CnLQt.active,.index-module_secondary-button-default-menu_CnLQt:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-icon_EBrIo {
    display: inline-flex;
    align-items: center
}

.index-module_secondary-button-default-flex_myAK8 {
    display: flex
}

.index-module_secondary-button-default-iconButton_3l8nj {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_secondary-button-default-iconButton_3l8nj svg {
    z-index: 1
}

.index-module_secondary-button-default-iconButton_3l8nj:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_secondary-button-default-iconButton_3l8nj:hover {
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-iconButton_3l8nj:hover:after {
    visibility: visible
}

.index-module_secondary-button-default-menu-item_OZFHO {
    display: flex;
    align-items: center
}

.index-module_secondary-button-default-secondary_iH73C {
    color: var(--yq-text-caption)
}

.index-module_secondary-button-default-strong_U1PcZ {
    font-weight: 500
}

.index-module_disable-button-default_do2Ky {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable)
}

.index-module_disable-button-default-link_Cc4nX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-button-default-link_Cc4nX.active,.index-module_disable-button-default-link_Cc4nX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-link_Cc4nX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-button-default-tab_uzzYS {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-menu_ki5Hs {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-button-default-menu_ki5Hs.active,.index-module_disable-button-default-menu_ki5Hs:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-button-default-icon_\+eAaQ {
    display: inline-flex;
    align-items: center
}

.index-module_disable-button-default-flex_pSS0P {
    display: flex
}

.index-module_disable-button-default-iconButton_Ns-ZT {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-button-default-iconButton_Ns-ZT svg {
    z-index: 1
}

.index-module_disable-button-default-iconButton_Ns-ZT:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover:after {
    visibility: visible
}

.index-module_disable-button-default-menu-item_H7X58 {
    display: flex;
    align-items: center
}

.index-module_disable-button-default-secondary_sJwUn {
    color: var(--yq-text-caption)
}

.index-module_disable-button-default-strong_ApPSw {
    font-weight: 500
}

.index-module_link_4lMzA {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_link-link_MSu6v {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_link-link_MSu6v.active,.index-module_link-link_MSu6v:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-link_MSu6v:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_link-tab_UaSsE {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-menu_X9-jl {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_link-menu_X9-jl.active,.index-module_link-menu_X9-jl:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_link-icon_GlYMK {
    display: inline-flex;
    align-items: center
}

.index-module_link-flex_y87zq {
    display: flex
}

.index-module_link-iconButton_hFUyW {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_link-iconButton_hFUyW svg {
    z-index: 1
}

.index-module_link-iconButton_hFUyW:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_link-iconButton_hFUyW:hover {
    color: var(--yq-text-primary)
}

.index-module_link-iconButton_hFUyW:hover:after {
    visibility: visible
}

.index-module_link-menu-item_QrtKv {
    display: flex;
    align-items: center
}

.index-module_link-secondary_cYp8F {
    color: var(--yq-text-caption)
}

.index-module_link-strong_6cupy {
    font-weight: 500
}

.index-module_link_4lMzA:hover {
    color: var(--yq-blue-6)
}

.index-module_linkBtn_viBrQ {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable);
    padding: 4.5px;
    display: flex;
    align-items: center;
    border-radius: 6px
}

.index-module_linkBtn-link_fRNiK {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_linkBtn-link_fRNiK.active,.index-module_linkBtn-link_fRNiK:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-link_fRNiK:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_linkBtn-tab_xLPRH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-menu_K8vUm {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_linkBtn-menu_K8vUm.active,.index-module_linkBtn-menu_K8vUm:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_linkBtn-icon_bNwRp {
    display: inline-flex;
    align-items: center
}

.index-module_linkBtn-flex_Xds\+s {
    display: flex
}

.index-module_linkBtn-iconButton_nWnGl {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_linkBtn-iconButton_nWnGl svg {
    z-index: 1
}

.index-module_linkBtn-iconButton_nWnGl:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_linkBtn-iconButton_nWnGl:hover {
    color: var(--yq-text-primary)
}

.index-module_linkBtn-iconButton_nWnGl:hover:after {
    visibility: visible
}

.index-module_linkBtn-menu-item_rDBK\+ {
    display: flex;
    align-items: center
}

.index-module_linkBtn-secondary_pxTMi {
    color: var(--yq-text-caption)
}

.index-module_linkBtn-strong_5PlMk {
    font-weight: 500
}

.index-module_linkBtn_viBrQ:hover {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-yuque-grey-9)
}

html[data-kumuhana=pouli] .index-module_linkBtn_viBrQ:hover {
    color: var(--yq-yuque-grey-9)!important
}

.index-module_yuque-green-1_t\+Yes {
    color: var(--yq-yuque-green-1)
}

.index-module_yuque-green-2_PSwCn {
    color: var(--yq-yuque-green-2)
}

.index-module_yuque-green-3_990Pj {
    color: var(--yq-yuque-green-3)
}

.index-module_yuque-green-4_LGQYT {
    color: var(--yq-yuque-green-4)
}

.index-module_yuque-green-5_Wdkpl {
    color: var(--yq-yuque-green-5)
}

.index-module_yuque-green-6_sp\+C1 {
    color: var(--yq-yuque-green-6)
}

.index-module_yuque-green-7_Yz0nb {
    color: var(--yq-yuque-green-7)
}

.index-module_yuque-green-8_TdQoj {
    color: var(--yq-yuque-green-8)
}

.index-module_yuque-green-9_78uX8 {
    color: var(--yq-yuque-green-9)
}

.index-module_yuque-grey-1_MFlzw {
    color: var(--yq-yuque-grey-1)
}

.index-module_yuque-grey-2_1MPla {
    color: var(--yq-yuque-grey-2)
}

.index-module_yuque-grey-3_Xwd3D {
    color: var(--yq-yuque-grey-3)
}

.index-module_yuque-grey-4_Iaalm {
    color: var(--yq-yuque-grey-4)
}

.index-module_yuque-grey-5_dTMGn {
    color: var(--yq-yuque-grey-5)
}

.index-module_yuque-grey-6_9P9mX {
    color: var(--yq-yuque-grey-6)
}

.index-module_yuque-grey-7_fo9a2 {
    color: var(--yq-yuque-grey-7)
}

.index-module_yuque-grey-8_5ZItk {
    color: var(--yq-yuque-grey-8)
}

.index-module_yuque-grey-9_2KrCK {
    color: var(--yq-yuque-grey-9)
}

.index-module_blue-1_9NXmy {
    color: var(--yq-blue-1)
}

.index-module_blue-2_71Xfa {
    color: var(--yq-blue-2)
}

.index-module_blue-3_QzBMA {
    color: var(--yq-blue-3)
}

.index-module_blue-4_eIuv6 {
    color: var(--yq-blue-4)
}

.index-module_blue-5_--rJ2 {
    color: var(--yq-blue-5)
}

.index-module_blue-6_EM\+ye {
    color: var(--yq-blue-6)
}

.index-module_blue-7_dcd5v {
    color: var(--yq-blue-7)
}

.index-module_blue-8_i6IXc {
    color: var(--yq-blue-8)
}

.index-module_blue-9_8WLz4 {
    color: var(--yq-blue-9)
}

.index-module_organge-1_QF-zP {
    color: var(--yq-orange-1)
}

.index-module_organge-2_zYIHH {
    color: var(--yq-orange-2)
}

.index-module_organge-3_EaUTx {
    color: var(--yq-orange-3)
}

.index-module_organge-4_KEzWt {
    color: var(--yq-orange-4)
}

.index-module_organge-5_uWCqi {
    color: var(--yq-orange-5)
}

.index-module_organge-6_SWaCL {
    color: var(--yq-orange-6)
}

.index-module_organge-7_UCnnq {
    color: var(--yq-orange-7)
}

.index-module_organge-8_yxbUr {
    color: var(--yq-orange-8)
}

.index-module_organge-9_Wqbx6 {
    color: var(--yq-orange-9)
}

.index-module_yellow-1_9BdDH {
    color: var(--yq-yellow-1)
}

.index-module_yellow-2_mmpuD {
    color: var(--yq-yellow-2)
}

.index-module_yellow-3_1MgEh {
    color: var(--yq-yellow-3)
}

.index-module_yellow-4_twlST {
    color: var(--yq-yellow-4)
}

.index-module_yellow-5_Z7Kqp {
    color: var(--yq-yellow-5)
}

.index-module_yellow-6_EZhZz {
    color: var(--yq-yellow-6)
}

.index-module_yellow-7_lCkCE {
    color: var(--yq-yellow-7)
}

.index-module_yellow-8_ZQ7hX {
    color: var(--yq-yellow-8)
}

.index-module_yellow-9_NAs4Y {
    color: var(--yq-yellow-9)
}

.index-module_red-1_Z-G\+I {
    color: var(--yq-red-1)
}

.index-module_red-2_ec26p {
    color: var(--yq-red-2)
}

.index-module_red-3_CitaX {
    color: var(--yq-red-3)
}

.index-module_red-4_Rf4VB {
    color: var(--yq-red-4)
}

.index-module_red-5_8DEQO {
    color: var(--yq-red-5)
}

.index-module_red-6_e40Pl {
    color: var(--yq-red-6)
}

.index-module_red-7_ND3LM {
    color: var(--yq-red-7)
}

.index-module_red-8_S4EAE {
    color: var(--yq-red-8)
}

.index-module_red-9_s0fc1 {
    color: var(--yq-red-9)
}

.index-module_magenta-1_BVOfc {
    color: var(--yq-magenta-1)
}

.index-module_magenta-2_LeAiv {
    color: var(--yq-magenta-2)
}

.index-module_magenta-3_anmFf {
    color: var(--yq-magenta-3)
}

.index-module_magenta-4_iyuji {
    color: var(--yq-magenta-4)
}

.index-module_magenta-5_XFO8v {
    color: var(--yq-magenta-5)
}

.index-module_magenta-6_83nvI {
    color: var(--yq-magenta-6)
}

.index-module_magenta-7_EylhI {
    color: var(--yq-magenta-7)
}

.index-module_magenta-8_H7UoN {
    color: var(--yq-magenta-8)
}

.index-module_magenta-9_BZu8L {
    color: var(--yq-magenta-9)
}

.index-module_purple-1_4Mt5K {
    color: var(--yq-purple-1)
}

.index-module_purple-2_ZU8FB {
    color: var(--yq-purple-2)
}

.index-module_purple-3_XZk6m {
    color: var(--yq-purple-3)
}

.index-module_purple-4_RkQAl {
    color: var(--yq-purple-4)
}

.index-module_purple-5_UwSof {
    color: var(--yq-purple-5)
}

.index-module_purple-6_VE5X4 {
    color: var(--yq-purple-6)
}

.index-module_purple-7_4qxQ3 {
    color: var(--yq-purple-7)
}

.index-module_purple-8_34VRx {
    color: var(--yq-purple-8)
}

.index-module_purple-9_S5yo0 {
    color: var(--yq-purple-9)
}

.index-module_cyan-1_XcHSS {
    color: var(--yq-cyan-1)
}

.index-module_cyan-2_8Z0Qc {
    color: var(--yq-cyan-2)
}

.index-module_cyan-3_8EpV- {
    color: var(--yq-cyan-3)
}

.index-module_cyan-4_BTV0V {
    color: var(--yq-cyan-4)
}

.index-module_cyan-5_OOfUa {
    color: var(--yq-cyan-5)
}

.index-module_cyan-6_8F8Ne {
    color: var(--yq-cyan-6)
}

.index-module_cyan-7_tQGo7 {
    color: var(--yq-cyan-7)
}

.index-module_cyan-8_nBcrC {
    color: var(--yq-cyan-8)
}

.index-module_cyan-9_pp0HZ {
    color: var(--yq-cyan-9)
}

.index-module_pea-green-1_YZ7VF {
    color: var(--yq-pea-green-1)
}

.index-module_pea-green-2_cB-wj {
    color: var(--yq-pea-green-2)
}

.index-module_pea-green-3_0aW\+j {
    color: var(--yq-pea-green-3)
}

.index-module_pea-green-4_V3TMD {
    color: var(--yq-pea-green-4)
}

.index-module_pea-green-5_UQ6\+a {
    color: var(--yq-pea-green-5)
}

.index-module_pea-green-6_VBEAH {
    color: var(--yq-pea-green-6)
}

.index-module_pea-green-7_fz-pg {
    color: var(--yq-pea-green-7)
}

.index-module_pea-green-8_vhbae {
    color: var(--yq-pea-green-8)
}

.index-module_pea-green-9_iLSdb {
    color: var(--yq-pea-green-9)
}

.index-module_white_-Ikm2 {
    color: var(--yq-white)
}

.index-module_yuque-color-text-disable_H8og1 {
    color: var(--yq-text-disable)
}

.index-module_flexFix_gj50B {
    width: 0;
    flex: 1
}

.index-module_btnNewOrg_V6nKz {
    display: flex;
    align-items: flex-start;
    line-height: 1.35;
    padding-left: 43px!important
}

.index-module_btnNewOrg_V6nKz h6 {
    position: relative!important
}

.index-module_btnNewOrg_V6nKz .icon-svg {
    position: absolute;
    left: -28px;
    top: 1px;
    color: var(--yq-yuque-grey-9)
}

.index-module_btnNewOrg_V6nKz .index-module_orgAddText_98X0I {
    margin-left: 11px
}

.index-module_btnNewOrg_V6nKz .index-module_tag_DSBkQ {
    display: inline-flex;
    margin-left: 8px;
    padding: 0 6px;
    color: var(--yq-yuque-green-7);
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    background: var(--yq-yuque-green-1);
    border-radius: 3px
}

.index-module_upgrade_qL2nR .ant-btn-link {
    padding: 0;
    height: auto
}

.index-module_wrapper_ulVDd .ant-modal-body {
    padding: 0!important
}

.index-module_wrapper_ulVDd .anticon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-information {
    display: none
}

.index-module_wrapper_ulVDd .ant-modal-confirm-content {
    width: 480px;
    margin-top: 0
}

.index-module_wrapper_ulVDd .ant-modal-confirm-btns {
    display: none
}

.index-module_wrapper_ulVDd .index-module_content_SO1HL {
    margin: 28px 0;
    padding: 0 24px
}

.index-module_wrapper_ulVDd .index-module_title_SVPzd {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_wrapper_ulVDd .index-module_desc_MlCQ9 {
    margin-top: 16px;
    margin-bottom: 32px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_wrapper_ulVDd .index-module_img_SBaEY {
    width: 480px
}

.index-module_trialButton_tH3iD {
    margin-left: 10px
}

.index-module_footer_cL9eu {
    display: flex;
    justify-content: flex-end
}

.index-module_footer_cL9eu .index-module_btn_XNqZC {
    margin-left: 10px
}

.index-module_title_INHx3 {
    font-size: 16px;
    line-height: 18px;
    vertical-align: middle;
    margin-right: 10px
}

.index-module_help_AVFYJ {
    font-size: 14px;
    line-height: 18px;
    vertical-align: middle;
    font-weight: 400
}

.index-module_tip_T1jyO {
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary);
    margin-bottom: 6px
}

.index-module_errMessage_nSIYH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5)
}

.index-module_form_W9V8N .larkui-form-item {
    margin-bottom: 0
}

.index-module_footer_G0PQG {
    margin-top: 16px
}

.index-module_submitBtn_\+TZ\+Q {
    margin-right: 8px
}

.MemberLevelIcon-module_memberLevelIcon_5CHat a {
    margin-left: 5px;
    display: flex;
    align-items: center
}

.MemberLevelIcon-module_memberLevelIcon_5CHat .MemberLevelIcon-module_icon_ivQ-f {
    vertical-align: middle;
    z-index: 1
}

.MemberLevelIcon-module_memberLevelIcon_5CHat .MemberLevelIcon-module_levelName_yHocY {
    font-size: 20px;
    height: 32px;
    line-height: 32px;
    margin-left: -10px;
    padding-left: 22px;
    padding-right: 16px;
    border-radius: 0 30px 30px 0;
    font-weight: 500;
    transform: scale(.5);
    transform-origin: left
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIcon1_HGaOq .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#f4d06f,#fcf0cb);
    color: #6a5619
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIcon2_STP5o .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#424241,#958d7c);
    color: #fbf7ee
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIconExpired_5bwcs .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#dcdcdc,#ebebeb);
    color: #646464
}

.board-module_memberBoardContainer_gN7pi {
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--yq-yuque-grey-2)
}

.board-module_memberBoardContainer_gN7pi .ant-btn-link {
    padding-left: 0;
    padding-right: 0
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardRight_6DlQt {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgIcon_r70ZR {
    margin-right: 16px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgNameWrapper_OCGfs .board-module_orgName_BE6gK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500;
    display: flex
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgNameWrapper_OCGfs .board-module_expiredDesc_AbfLB {
    margin-top: 4px;
    font-size: 14px;
    color: var(--yq-text-caption);
    font-weight: 400
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ a {
    font-size: 14px;
    font-weight: 400;
    margin-left: 4px;
    line-height: 24px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_certification_b5\+ky {
    margin-left: 6px;
    font-size: 14px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_certification_b5\+ky a {
    line-height: 20px;
    height: 20px
}

.board-module_memberBoardContainer_gN7pi .board-module_expiredDesc_AbfLB {
    color: var(--yq-text-caption)
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardNameCon_i3Jda {
    min-height: 32px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardMainName_UiUuz {
    color: var(--yq-yellow-7);
    margin-right: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardGroupIcon_rIq\+p {
    margin-left: -8px;
    position: relative;
    top: 2px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardRenewButton_Gg-zI {
    margin-left: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardCountdown_RqOSU {
    border: 1px solid var(--yq-yuque-green-3);
    border-radius: 2px;
    background: var(--yq-bg-tertiary);
    color: var(--yq-yuque-green-3);
    margin-left: 8px;
    font-size: 10px;
    padding: 1px 4px;
    position: relative;
    top: -1px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardDesc_AnX25 {
    color: var(--yq-yuque-grey-7);
    margin-top: 4px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardLink_uaK6n {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardOrgDesc_yTvNM {
    margin-left: 8px
}

.selector-module_memberSelectorContainer_SzVML {
    position: relative
}

.selector-module_memberSelectorContainer_SzVML .ant-radio-button-wrapper {
    background: transparent!important;
    flex: 1;
    text-align: center;
    height: 56px;
    padding-top: 7px;
    line-height: 20px
}

.selector-module_memberSelectorContainer_SzVML .ant-radio-group {
    display: flex
}

.selector-module_memberSelectorContainer_SzVML .selector-module_memberSelectorTips_vqgUs {
    position: absolute;
    bottom: -36px;
    left: 0;
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocCollaboratorTip-module_contentText_3pLj- {
    color: var(--yq-yuque-grey-8);
    font-size: 14px;
    padding: 8px 20px 0 20px
}

.DocCollaboratorTip-module_contentCountWrapper_-NPtH {
    margin: 14px 0 4px
}

.DocCollaboratorTip-module_contentIcon_o95Dj {
    margin-right: 4px;
    color: var(--yq-text-body)
}

.DocCollaboratorTip-module_contentCount_dj5PU {
    color: var(--yq-text-body);
    font-size: 12px;
    position: relative;
    top: -2px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageAction_9Iy5y {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz {
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz .larkui-icon-close {
    color: var(--yq-text-body);
    top: 0;
    font-size: 14px;
    cursor: pointer
}

.message-module_memberMessageContainer_7bp54 .message-module_showMemberIntro_spXu1 {
    margin-left: 4px;
    margin-right: -8px
}

.message-module_orgPayBtn_DLLTR {
    margin-left: 20px
}

.groupintro-module_groupIntroContainer_XUWAC {
    padding: 80px 40px
}

.groupintro-module_groupIntroContainer_XUWAC .groupintro-module_groupIntroMainImage_wg4Nf img {
    width: 100%
}

.groupintro-module_groupIntroContainer_XUWAC .groupintro-module_groupIntroMore_4N4Va {
    color: var(--yq-text-link);
    margin-top: 40px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteContainer_1caED {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V {
    position: relative;
    top: 8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -8px;
    left: -8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteH5Container_ituBE {
    position: relative;
    left: 2px
}

.badgeIcon-module_container_xawzG {
    position: absolute;
    top: 12px;
    right: 12px;
    text-align: center;
    cursor: pointer
}

.badgeIcon-module_container_xawzG .badgeIcon-module_icon_qa6QH {
    display: block;
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.organizationUsage-module_orgUsageBoard_R68jU h3 {
    font-size: 14px
}

.organizationUsage-module_orgUsageBoard_R68jU a,.organizationUsage-module_orgUsageBoard_R68jU a:active,.organizationUsage-module_orgUsageBoard_R68jU a:focus,.organizationUsage-module_orgUsageBoard_R68jU a:hover {
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn {
    margin-top: 16px;
    width: 100%;
    padding: 16px 20px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.organizationUsage-module_usageBoard_\+jcHn.organizationUsage-module_homepageMode_8ax\+5 {
    margin-top: 24px;
    padding: 13px 20px;
    border-radius: 8px
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu {
    display: flex;
    align-items: center
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_informationIcon_Rp7jc {
    margin-left: 8px;
    transform: translateY(1px)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px;
    transform: translateY(1px);
    margin-right: 12px
}

html[data-kumuhana=pouli] .organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF {
    background: var(--yq-yuque-grey-4)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP {
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary);
    line-height: 18px;
    display: flex;
    align-items: center
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP .organizationUsage-module_iconInfo_cGcpv {
    margin-left: 8px
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP .organizationUsage-module_buyBtn_wyCzR {
    margin-left: 8px;
    color: var(--yq-blue-600);
    font-weight: 400
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 b {
    color: var(--yq-text-primary);
    font-size: 18px;
    line-height: 18px;
    font-weight: 500
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_percent_0izvV {
    font-size: 12px;
    color: var(--yq-yuque-grey-700);
    line-height: 18px;
    font-weight: 400
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_tip_trHRY {
    margin-top: 16px;
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_reachedText_DPIJ7 {
    margin: 16px 0;
    display: flex;
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_reachedText_DPIJ7 .ant-btn-link {
    padding: 0
}

.organizationUsage-module_content_NywO7 {
    font-size: 12px
}

.organizationUsage-module_content_NywO7 b {
    color: var(--yq-text-primary);
    font-size: 20px;
    font-weight: 500
}

.organizationUsage-module_usageBar_1vk-H {
    position: relative;
    margin: 22px auto 22px 0;
    width: 100%;
    height: 6px;
    border-radius: 2px;
    background: var(--yq-yuque-grey-4)
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: var(--yq-blue-4);
    border-radius: 12px 0 0 12px
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J .organizationUsage-module_vernier_WO3Ko {
    position: absolute;
    top: 50%;
    right: 0;
    padding: 3px 4px;
    min-width: 34px;
    height: 18px;
    color: var(--yq-text-primary);
    font-size: 12px;
    line-height: 12px;
    background: var(--yq-white);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12);
    border-radius: 4px;
    transform: translate(100%,-50%);
    text-align: center;
    font-weight: 500
}

html[data-kumuhana=pouli] .organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J .organizationUsage-module_vernier_WO3Ko {
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J.organizationUsage-module_full_hWfHT .organizationUsage-module_vernier_WO3Ko {
    transform: translateY(-50%)
}

.UsagePercentShow-module_usageContainer_jKUGw {
    margin-bottom: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY {
    width: 100%;
    padding: 16px 16px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl {
    display: flex;
    align-items: center
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px
}

html[data-kumuhana=pouli] .UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw {
    background: var(--yq-bg-primary-hover)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- {
    display: flex;
    align-items: center;
    color: var(--yq-text-primary)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_title_qgGNo {
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_count_HLwuB {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_limit_RdfnJ {
    font-size: 14px;
    font-weight: 400
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s {
    width: 92%
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarItem_MxMUl {
    position: absolute;
    top: 0;
    height: 100%
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarItem_MxMUl:first-child {
    border-radius: 5px 0 0 5px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarCon_ny839 {
    background: var(--yq-yuque-grey-4);
    border-radius: 5px;
    height: 8px;
    position: relative;
    margin-top: 21px;
    margin-bottom: 5px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarPercent_2o48U {
    text-align: right;
    position: absolute;
    top: -6px;
    color: var(--yq-black);
    background: var(--yq-white);
    height: 18px;
    font-size: 10px;
    padding: 0 4px 0 3px;
    border-radius: 5px;
    line-height: 18px;
    font-weight: 500;
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12)
}

html[data-kumuhana=pouli] .UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarPercent_2o48U {
    background: var(--yq-mask-bg);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.userUsage-module_userUsageContainer_Vg08f>.userUsage-module_title_tEvgb {
    display: flex;
    align-items: center;
    line-height: 22px;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 16px;
    justify-content: space-between
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8 {
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8>span {
    margin-right: 3px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8 svg {
    transform: translateY(1px)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageRow_5Ni-3 {
    display: flex;
    flex-wrap: wrap
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo {
    width: 100%;
    padding: 16px 16px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq {
    display: flex;
    align-items: center
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px
}

html[data-kumuhana=pouli] .userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY {
    background: var(--yq-bg-primary-hover)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 {
    display: flex;
    align-items: center;
    color: var(--yq-text-primary)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_title_tEvgb {
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_count_Nurz0 {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_limit_rEjzt {
    font-size: 14px;
    font-weight: 400
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_exceedLimit_XKiwc {
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_exceedTip_91hDB {
    margin-top: 16px;
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y {
    width: 100%
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarItem_GU6oR {
    position: absolute;
    top: 0;
    height: 100%
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarItem_GU6oR:first-child {
    border-radius: 5px 0 0 5px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescItems_22Xa0 {
    margin-top: 10px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescItem_LMkQY {
    margin-right: 35px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescIcon_WjuBz {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 2px;
    margin-right: 8px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarCon_29GIy {
    background: var(--yq-yuque-grey-4);
    border-radius: 5px;
    height: 8px;
    position: relative;
    margin-top: 21px;
    margin-bottom: 5px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarPercent_mDaqi {
    text-align: right;
    position: absolute;
    top: -6px;
    color: var(--yq-black);
    background: var(--yq-white);
    height: 18px;
    font-size: 10px;
    padding: 0 4px 0 3px;
    border-radius: 5px;
    line-height: 18px;
    font-weight: 500;
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12)
}

html[data-kumuhana=pouli] .userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarPercent_mDaqi {
    background: var(--yq-mask-bg);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_docAndNoteUsage_fPljT {
    margin-bottom: 16px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_docAndNoteUsage_fPljT .userUsage-module_titleIconWrapper_Kt\+AY {
    color: var(--yq-yuque-green-4)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_dataFlowUsage_6tbSW .userUsage-module_titleIconWrapper_Kt\+AY {
    color: var(--yq-blue-3)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW {
    margin-top: 16px;
    position: relative
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_titleIconWrapper_Kt\+AY {
    margin-right: 16px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcode_pIXqT {
    cursor: pointer;
    display: flex;
    position: absolute;
    right: 16px;
    justify-content: center;
    align-items: center;
    color: var(--yq-text-body)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcode_pIXqT .userUsage-module_qrcodeDesc_8fKRX {
    margin-right: 4px;
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_titleIcon_WBzIt {
    color: var(--yq-orange-4)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcodeIcon_8TtwM {
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_qrcodeWrapper_yTpsS {
    padding: 8px 4px
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_qrcodeContent_gIsg6 {
    width: 212px;
    height: 212px;
    border-radius: 8px;
    border: 1px solid var(--yq-bg-primary-hover-light);
    display: flex;
    justify-content: center;
    align-items: center
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_qrcode_pIXqT {
    width: 180px;
    height: 180px
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_desc_jviUQ {
    margin-top: 16px;
    color: var(--yq-text-primary);
    text-align: center
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card {
    margin: 24px 0 8px;
    background: var(--yq-bg-primary)!important
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-extra {
    padding: 10px 12px
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-extra .larkicon {
    cursor: pointer
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-body {
    border: none;
    padding: 0
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-head {
    position: absolute;
    right: 0;
    top: 0;
    border: none;
    padding: 0
}

.uploadExhaustReminder-module_UploadExhaustReminderEnContainer_WNK2j .uploadExhaustReminder-module_UploadExhaustReminderUsageTitle_ytgI6 {
    min-height: 42px!important
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr {
    color: var(--yq-text-body);
    padding: 20px 20px 20px 66px;
    font-size: 12px;
    min-height: 144px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSubTitle_hv3if {
    margin-top: 8px;
    font-size: 14px;
    width: 520px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderIcon_Y2-xC {
    font-size: 26px;
    color: var(--yq-yellow-6);
    position: absolute;
    left: 21px;
    top: 21px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsage_85c0m {
    margin-top: 24px;
    width: 520px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageTitle_ytgI6 {
    font-size: 14px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageText_qkfd4 {
    margin-top: 8px;
    font-size: 24px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageSubTitle_yLn4D {
    margin-left: 4px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageSubIcon_T0Dse {
    position: relative;
    top: 3px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderButtonCon_mHoxS {
    margin-top: 16px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderContactUs_YLq5Q {
    margin-left: 16px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderImage_zteOX {
    position: absolute;
    right: 30px;
    bottom: 0;
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderImage_zteOX img {
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSmallImage_3HP6j {
    position: absolute;
    right: 40px;
    bottom: 0;
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSmallImage_3HP6j img {
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderOwners_N0DgD {
    margin: 0 8px
}

.order-detail-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.order-detail-modal .ant-modal-body {
    padding: 0
}

.order-detail-modal .ant-modal-body img {
    width: 100%
}

.order-detail-modal .ant-col {
    padding-top: 10px
}

.order-detail-modal .ant-col-18 {
    font-weight: 600
}

.index-module_successModalContainer_-aK4t {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfoCon_7ecQr {
    padding: 20px 40px 30px 40px
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfo_m-ZJc {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfoTitle_ByhnY {
    font-size: 28px;
    margin-top: 4px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY {
    margin-top: 12px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY .index-module_applyReceiptButton_MoqCc {
    margin-left: 12px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY .index-module_checkUploadButton_1BWTN {
    margin-left: 12px;
    margin-top: 1px
}

.index-module_successModalContainer_-aK4t .index-module_successModalApplyReceiptButton_Zqpff {
    margin-left: 10px
}

.index-module_successModalContainer_-aK4t .index-module_successModalSubTitle_kd51r {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.index-module_successModalContainer_-aK4t .index-module_successModalItem_\+QbaR {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.index-module_successModalContainer_-aK4t .index-module_successModalBlank_KXceZ {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.index-module_successModalContainer_-aK4t .index-module_successModalDingQRCon_yUyWK {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.index-module_successModalContainer_-aK4t .index-module_successModalDingQRCon_yUyWK img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.orderReceipt-module_container_0\+bHT {
    padding-bottom: 36px
}

.orderReceipt-module_table_ulliM {
    margin-bottom: 24px
}

.orderReceipt-module_table_ulliM.orderReceipt-module_border_eZgzZ .ant-table-thead>tr>th {
    border-bottom: 0
}

.orderReceipt-module_table_ulliM.orderReceipt-module_border_eZgzZ .ant-table-tbody>tr>td {
    border: 1px solid var(--yq-border-light);
    border-radius: 8px
}

.orderReceipt-module_table_ulliM .ant-table-title {
    background-color: var(--yq-bg-primary)
}

.orderReceipt-module_table_ulliM .ant-table-pagination {
    margin-right: 16px
}

.orderReceipt-module_table_ulliM .ant-btn-link {
    color: var(--yq-text-link);
    padding: 0;
    height: auto
}

.orderReceipt-module_tableHeader_FQ\+zb {
    font-weight: 600
}

.orderReceipt-module_actions_tViEs,.orderReceipt-module_amount_tDmhL,.orderReceipt-module_content_wbMym,.orderReceipt-module_timer_WuSe8,.orderReceipt-module_user_TgHTF {
    color: var(--yq-text-caption)
}

.ant-table.ant-table-middle {
    font-size: 14px
}

.ant-table.ant-table-middle .ant-table-footer,.ant-table.ant-table-middle .ant-table-tbody>tr>td,.ant-table.ant-table-middle .ant-table-thead>tr>th,.ant-table.ant-table-middle .ant-table-title,.ant-table.ant-table-middle tfoot>tr>td,.ant-table.ant-table-middle tfoot>tr>th {
    padding: 12px 8px
}

.ant-table.ant-table-middle .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-middle .ant-table-expanded-row-fixed {
    margin: -12px -8px
}

.ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -12px -8px -12px 25px
}

.ant-table.ant-table-small {
    font-size: 14px
}

.ant-table.ant-table-small .ant-table-footer,.ant-table.ant-table-small .ant-table-tbody>tr>td,.ant-table.ant-table-small .ant-table-thead>tr>th,.ant-table.ant-table-small .ant-table-title,.ant-table.ant-table-small tfoot>tr>td,.ant-table.ant-table-small tfoot>tr>th {
    padding: 8px 8px
}

.ant-table.ant-table-small .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-small .ant-table-expanded-row-fixed {
    margin: -8px -8px
}

.ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -8px -8px -8px 25px
}

.ant-table-small .ant-table-thead>tr>th {
    background-color: var(--yq-ant-table-header-bg)
}

.ant-table-small .ant-table-selection-column {
    width: 46px;
    min-width: 46px
}

.ant-table.ant-table-bordered>.ant-table-container,.ant-table.ant-table-bordered>.ant-table-title {
    border: 1px solid var(--yq-ant-border-color-split);
    border-bottom: 0
}

.ant-table.ant-table-bordered>.ant-table-container {
    border-right: 0
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr:not(:last-child)>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th:before {
    background-color: transparent!important
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>.ant-table-cell-fix-right-first:after {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -16px -17px
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed:after {
    position: absolute;
    top: 0;
    right: 1px;
    bottom: 0;
    border-right: 1px solid var(--yq-ant-border-color-split);
    content: ""
}

.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-expanded-row>td,.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-placeholder>td {
    border-right: 0
}

.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -12px -9px
}

.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -8px -9px
}

.ant-table.ant-table-bordered>.ant-table-footer {
    border: 1px solid var(--yq-ant-border-color-split);
    border-top: 0
}

.ant-table-cell .ant-table-container:first-child {
    border-top: 0
}

.ant-table-cell-scrollbar {
    box-shadow: 0 1px 0 1px var(--yq-ant-table-header-bg)
}

.ant-table-wrapper {
    clear: both;
    max-width: 100%
}

.ant-table-wrapper:before {
    display: table;
    content: ""
}

.ant-table-wrapper:after {
    display: table;
    clear: both;
    content: ""
}

.ant-table {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    font-size: 14px;
    background: var(--yq-ant-component-background);
    border-radius: 6px
}

.ant-table table {
    width: 100%;
    text-align: left;
    border-radius: 6px 6px 0 0;
    border-collapse: separate;
    border-spacing: 0
}

.ant-table-tbody>tr>td,.ant-table-thead>tr>th,.ant-table tfoot>tr>td,.ant-table tfoot>tr>th {
    position: relative;
    padding: 16px 16px;
    word-wrap: break-word
}

.ant-table-cell-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first {
    overflow: visible
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last .ant-table-cell-content,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first .ant-table-cell-content {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis
}

.ant-table-title {
    padding: 16px 16px
}

.ant-table-footer {
    padding: 16px 16px;
    color: var(--yq-ant-heading-color);
    background: var(--yq-ant-background-color-light)
}

.ant-table-thead>tr>th {
    position: relative;
    color: var(--yq-ant-heading-color);
    font-weight: 500;
    text-align: left;
    background: var(--yq-ant-table-header-bg);
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s ease
}

.ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: var(--yq-ant-table-header-cell-split-color);
    transform: translateY(-50%);
    transition: background-color .3s;
    content: ""
}

.ant-table-thead>tr:not(:last-child)>th[colspan] {
    border-bottom: 0
}

.ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table {
    margin: -16px -16px -16px 33px
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td {
    border-bottom: 0
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child {
    border-radius: 0
}

.ant-table-tbody>tr.ant-table-row:hover>td {
    background: var(--yq-ant-table-row-hover-bg)
}

.ant-table-tbody>tr.ant-table-row-selected>td {
    background: var(--yq-yuque-green-100);
    border-color: rgba(0,0,0,.03)
}

.ant-table-tbody>tr.ant-table-row-selected:hover>td {
    background: var(--yq-yuque-grey-300)
}

.ant-table-summary {
    background: var(--yq-ant-component-background)
}

div.ant-table-summary {
    box-shadow: 0 -1px 0 var(--yq-ant-border-color-split)
}

.ant-table-summary>tr>td,.ant-table-summary>tr>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-pagination.ant-pagination {
    margin: 16px 0
}

.ant-table-pagination {
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px
}

.ant-table-pagination>* {
    flex: none
}

.ant-table-pagination-left {
    justify-content: flex-start
}

.ant-table-pagination-center {
    justify-content: center
}

.ant-table-pagination-right {
    justify-content: flex-end
}

.ant-table-thead th.ant-table-column-has-sorters {
    cursor: pointer;
    transition: all .3s
}

.ant-table-thead th.ant-table-column-has-sorters:hover {
    background: var(--yq-ant-table-header-sort-active-bg)
}

.ant-table-thead th.ant-table-column-has-sorters:hover:before {
    background-color: transparent!important
}

.ant-table-thead th.ant-table-column-sort {
    background: var(--yq-ant-table-header-sort-bg)
}

.ant-table-thead th.ant-table-column-sort:before {
    background-color: transparent!important
}

td.ant-table-column-sort {
    background: var(--yq-ant-table-body-sort-bg)
}

.ant-table-column-sorters {
    display: flex;
    flex: auto;
    align-items: center;
    justify-content: space-between
}

.ant-table-column-sorters:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: ""
}

.ant-table-column-sorter {
    color: #bfbfbf;
    font-size: 0;
    transition: color .3s
}

.ant-table-column-sorter-inner {
    display: inline-flex;
    flex-direction: column;
    align-items: center
}

.ant-table-column-sorter-down,.ant-table-column-sorter-up {
    font-size: 11px
}

.ant-table-column-sorter-down.active,.ant-table-column-sorter-up.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-column-sorter-up+.ant-table-column-sorter-down {
    margin-top: -.3em
}

.ant-table-column-sorters:hover .ant-table-column-sorter {
    color: #a5a5a5
}

.ant-table-filter-column {
    display: flex;
    justify-content: space-between
}

.ant-table-filter-trigger {
    position: relative;
    display: flex;
    align-items: center;
    margin: -4px -8px -4px 4px;
    padding: 0 4px;
    color: #bfbfbf;
    font-size: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all .3s
}

.ant-table-filter-trigger:hover {
    color: var(--yq-ant-text-color-secondary);
    background: var(--yq-ant-table-header-filter-active-bg)
}

.ant-table-filter-trigger.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-filter-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    min-width: 120px;
    background-color: var(--yq-ant-table-filter-dropdown-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-table-filter-dropdown .ant-dropdown-menu {
    max-height: 264px;
    overflow-x: hidden;
    border: 0;
    box-shadow: none
}

.ant-table-filter-dropdown-submenu>ul {
    max-height: calc(100vh - 130px);
    overflow-x: hidden;
    overflow-y: auto
}

.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-left: 8px
}

.ant-table-filter-dropdown-btns {
    display: flex;
    justify-content: space-between;
    padding: 7px 8px 7px 3px;
    overflow: hidden;
    background-color: var(--yq-ant-table-filter-btns-bg);
    border-top: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-selection-col {
    width: 32px
}

.ant-table-bordered .ant-table-selection-col {
    width: 50px
}

table tr td.ant-table-selection-column,table tr th.ant-table-selection-column {
    padding-right: 8px;
    padding-left: 8px;
    text-align: center
}

table tr td.ant-table-selection-column .ant-radio-wrapper,table tr th.ant-table-selection-column .ant-radio-wrapper {
    margin-right: 0
}

table tr th.ant-table-selection-column:after {
    background-color: transparent!important
}

.ant-table-selection {
    position: relative;
    display: inline-flex;
    flex-direction: column
}

.ant-table-selection-extra {
    position: absolute;
    top: 0;
    z-index: 1;
    cursor: pointer;
    transition: all .3s;
    margin-left: 100%;
    padding-left: 4px
}

.ant-table-selection-extra .anticon {
    color: #bfbfbf;
    font-size: 10px
}

.ant-table-selection-extra .anticon:hover {
    color: #a5a5a5
}

.ant-table-expand-icon-col {
    width: 48px
}

.ant-table-row-expand-icon-cell {
    text-align: center
}

.ant-table-row-indent {
    float: left;
    height: 1px
}

.ant-table-row-expand-icon {
    color: var(--yq-ant-link-color);
    -webkit-text-decoration: none;
    text-decoration: none;
    cursor: pointer;
    transition: color .3s;
    position: relative;
    display: inline-flex;
    float: left;
    box-sizing: border-box;
    width: 17px;
    height: 17px;
    padding: 0;
    color: inherit;
    line-height: 17px;
    background: var(--yq-ant-table-expand-icon-bg);
    border: 1px solid var(--yq-ant-border-color-split);
    border-radius: 6px;
    outline: none;
    transform: scale(.94117647);
    transition: all .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-table-row-expand-icon:active {
    color: var(--yq-ant-link-active-color)
}

.ant-table-row-expand-icon:active,.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    border-color: currentColor
}

.ant-table-row-expand-icon:after,.ant-table-row-expand-icon:before {
    position: absolute;
    background: currentColor;
    transition: transform .3s ease-out;
    content: ""
}

.ant-table-row-expand-icon:before {
    top: 7px;
    right: 3px;
    left: 3px;
    height: 1px
}

.ant-table-row-expand-icon:after {
    top: 3px;
    bottom: 3px;
    left: 7px;
    width: 1px;
    transform: rotate(90deg)
}

.ant-table-row-expand-icon-collapsed:before {
    transform: rotate(-180deg)
}

.ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

.ant-table-row-expand-icon-spaced {
    background: transparent;
    border: 0;
    visibility: hidden
}

.ant-table-row-expand-icon-spaced:after,.ant-table-row-expand-icon-spaced:before {
    display: none;
    content: none
}

.ant-table-row-indent+.ant-table-row-expand-icon {
    margin-top: 2.5005px;
    margin-right: 8px
}

tr.ant-table-expanded-row:hover>td,tr.ant-table-expanded-row>td {
    background: var(--yq-ant-table-expanded-row-bg)
}

tr.ant-table-expanded-row .ant-descriptions-view {
    display: flex
}

tr.ant-table-expanded-row .ant-descriptions-view table {
    flex: auto;
    width: auto
}

.ant-table .ant-table-expanded-row-fixed {
    position: relative;
    margin: -16px -16px;
    padding: 16px 16px
}

.ant-table-tbody>tr.ant-table-placeholder {
    text-align: center
}

.ant-table-empty .ant-table-tbody>tr.ant-table-placeholder {
    color: var(--yq-ant-disabled-color)
}

.ant-table-tbody>tr.ant-table-placeholder:hover>td {
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left,.ant-table-cell-fix-right {
    position: sticky!important;
    z-index: 2;
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left-first:after,.ant-table-cell-fix-left-last:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: -1px;
    width: 30px;
    transform: translateX(100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table-cell-fix-right-first:after,.ant-table-cell-fix-right-last:after {
    position: absolute;
    top: 0;
    bottom: -1px;
    left: 0;
    width: 30px;
    transform: translateX(-100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:after,.ant-table .ant-table-container:before {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    width: 30px;
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:before {
    left: 0
}

.ant-table .ant-table-container:after {
    right: 0
}

.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container {
    position: relative
}

.ant-table-ping-left .ant-table-cell-fix-left-first:after,.ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container:before {
    box-shadow: inset 10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-ping-left .ant-table-cell-fix-left-last:before {
    background-color: transparent!important
}

.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container {
    position: relative
}

.ant-table-ping-right .ant-table-cell-fix-right-first:after,.ant-table-ping-right .ant-table-cell-fix-right-last:after,.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container:after {
    box-shadow: inset -10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-sticky-holder,.ant-table-sticky-scroll {
    position: sticky;
    z-index: 3
}

.ant-table-sticky-scroll {
    bottom: 0;
    display: flex;
    align-items: center;
    background: var(--yq-ant-border-color-split);
    border-top: 1px solid var(--yq-ant-border-color-split);
    opacity: .6
}

.ant-table-sticky-scroll:hover {
    transform-origin: center bottom
}

.ant-table-sticky-scroll-bar {
    height: 8px;
    background-color: rgba(0,0,0,.35);
    border-radius: 4px
}

.ant-table-sticky-scroll-bar-active,.ant-table-sticky-scroll-bar:hover {
    background-color: rgba(0,0,0,.8)
}

@media (-ms-high-contrast:none) {
    .ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-right .ant-table-cell-fix-right-first:after {
        box-shadow: none!important
    }
}

.ant-table-title {
    border-radius: 6px 6px 0 0
}

.ant-table-title+.ant-table-container {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ant-table-title+.ant-table-container table>thead>tr:first-child th:first-child,.ant-table-title+.ant-table-container table>thead>tr:first-child th:last-child {
    border-radius: 0
}

.ant-table-container {
    border-top-right-radius: 6px
}

.ant-table-container,.ant-table-container table>thead>tr:first-child th:first-child {
    border-top-left-radius: 6px
}

.ant-table-container table>thead>tr:first-child th:last-child {
    border-top-right-radius: 6px
}

.ant-table-footer {
    border-radius: 0 0 6px 6px
}

.ant-table-rtl,.ant-table-wrapper-rtl {
    direction: rtl
}

.ant-table-wrapper-rtl .ant-table table {
    text-align: right
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th {
    text-align: right
}

.ant-table-tbody>tr .ant-table-wrapper:only-child .ant-table.ant-table-rtl {
    margin: -16px 33px -16px -16px
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-left {
    justify-content: flex-end
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-right {
    justify-content: flex-start
}

.ant-table-wrapper-rtl .ant-table-column-sorter {
    margin-right: 8px;
    margin-left: 0
}

.ant-table-wrapper-rtl .ant-table-filter-column-title {
    padding: 16px 16px 16px 2.3em
}

.ant-table-rtl .ant-table-thead tr th.ant-table-column-has-sorters .ant-table-filter-column-title {
    padding: 0 0 0 2.3em
}

.ant-table-wrapper-rtl .ant-table-filter-trigger-container {
    right: auto;
    left: 0
}

.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-right: 8px;
    padding-left: 0
}

.ant-table-wrapper-rtl .ant-table-selection {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon,.ant-table-wrapper-rtl .ant-table-row-indent {
    float: right
}

.ant-table-wrapper-rtl .ant-table-row-indent+.ant-table-row-expand-icon {
    margin-right: 0;
    margin-left: 8px
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon:after {
    transform: rotate(-90deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:before {
    transform: rotate(180deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

@keyframes RegisterTrialModal-module_showIcon_8EOMg {
    0% {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
    }

    to {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original)
    }
}

.RegisterTrialModal-module_modal_hmMaz .ant-modal-body {
    padding: 0
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--yq-icon-colorbg);
    cursor: pointer
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d svg {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 10px;
    fill: #fff;
    transform: translate(-50%,-50%);
    color: #fff
}

.RegisterTrialModal-module_wrapper_v2OGS {
    padding-bottom: 28px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(0) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:first-child .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(2) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(3) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.6s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(4) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(5) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(6) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    width: 188px;
    height: 323px;
    left: 38px;
    top: 0;
    padding-left: 40px;
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_tip_\+NtIs {
    width: 45px;
    height: 21px;
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-text-primary);
    position: absolute;
    right: 0;
    top: 0;
    background: var(--yq-bg-primary-hover);
    border-radius: 0 4px 0 12px;
    text-align: center
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT {
    left: 242px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(0);
    padding-left: 10px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_img_bAwkI {
    width: 480px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_highLight_AL6MB {
    color: var(--yq-theme)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: #fdf9e4;
    text-align: center;
    line-height: 32px;
    font-size: 20px;
    color: #e4495b;
    font-weight: 500;
    margin: 0 9px
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    background: var(--yq-yellow-100)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC .RegisterTrialModal-module_img_bAwkI {
    width: 22px;
    height: 22px;
    margin-right: 12px;
    margin-top: -2px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE {
    height: 323px;
    position: relative;
    top: 0
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    width: 107px;
    height: 55px;
    position: absolute;
    left: 87px;
    top: 170px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1;
    position: absolute;
    left: 32px;
    top: 44px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 12px;
    width: 128px;
    height: 154px;
    padding-left: 28px;
    padding-top: 16px;
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K {
    margin-top: 18px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 8px;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 20px;
    list-style: disc
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    position: absolute;
    left: 168px;
    right: 32px;
    top: 0;
    background-image: linear-gradient(125deg,#fdf9e4,#fff 57%,#fdf9e4);
    border: .5px solid #f5da80;
    border-radius: 12px;
    height: 323px;
    padding: 16px 20px 0;
    transition: all 1s ease
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    background: rgba(245,210,97,.05);
    border: .5px solid #776118
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK span {
    line-height: 24px;
    height: 24px;
    display: inline-block
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_yuan_4C\+Ad {
    font-size: 16px;
    color: #c99b03;
    font-weight: 500;
    margin-left: 6px;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_price_f4gnN {
    font-size: 24px;
    color: #c99b03;
    font-weight: 500;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_time_QNDxs {
    font-size: 14px;
    color: #c99b03;
    margin-left: 5px;
    font-weight: 400
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K {
    margin-top: 20px;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-body);
    line-height: 20px;
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 16px 16px;
    display: inline-block;
    margin-right: 12px;
    width: 16px;
    height: 16px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_bold_rwjpD {
    color: var(--yq-text-primary);
    font-weight: 600
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_bg_xLr\+V {
    width: 69px;
    height: 154px;
    position: absolute;
    right: 0;
    top: 99px
}

.DocNoteLimitIncentiveModal-module_modal_aJrQn .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitIncentiveModal-module_modal_aJrQn .ant-modal-body {
    padding: 0
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv {
    padding: 24px;
    overflow: hidden
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv h1 {
    font-size: 18px;
    margin-top: 6px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv p {
    margin-top: 12px
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv>p {
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ {
    width: 434px;
    height: 326px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*G6NcQYnMre4AAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: cover;
    margin-top: 24px;
    border-radius: 8px;
    padding: 16px;
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardName_NaU7l {
    font-size: 20px;
    color: var(--yq-yuque-grey-9);
    font-weight: 700;
    display: flex;
    align-items: center
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardCoreRight_xCY6c {
    margin-top: 23px;
    color: #585a5a;
    font-weight: 700
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardRight_ILEan {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*eIylQZH5QBsAAAAAAAAAAAAAARQnAQ) no-repeat;
    padding-left: 20px;
    background-size: 13px 12px;
    background-position: 0
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ p {
    display: flex;
    align-items: center
}

.DocNoteLimitIncentiveModal-module_footer_r2nm- {
    margin-top: 24px
}

.DocNoteLimitIncentiveModal-module_footerAction_6SUEP {
    float: right
}

.DocNoteLimitIncentiveModal-module_actionBtn_3odCr {
    margin-left: 8px
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.RoleSelector-module_role_jDwZn {
    color: var(--yq-text-primary);
    padding: 0 4px;
    min-width: 70px;
    border-radius: 4px
}

.RoleSelector-module_trigger_PbJ3K {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--yq-text-primary);
    padding: 2px 0;
    border-radius: 4px;
    white-space: nowrap
}

.RoleSelector-module_trigger_PbJ3K .larkui-icon {
    margin-left: 8px
}

.RoleSelector-module_trigger_PbJ3K.RoleSelector-module_active_lhpS5 {
    color: var(--yq-text-caption);
    background-color: var(--yq-bg-primary-hover)
}

.RoleSelector-module_icon_YSGjG {
    color: var(--yq-yuque-grey-6)
}

.RoleSelector-module_up_BKR9i {
    transform: rotate(180deg)
}

.RoleSelector-module_menu_MwfMA {
    min-width: 216px;
    max-width: 280px;
    padding-bottom: 0;
    padding-top: 0
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item {
    white-space: inherit;
    padding: 0 12px;
    background-color: var(--yq-bg-primary)
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item .larkui-icon {
    visibility: hidden;
    font-size: 14px;
    color: var(--yq-theme)
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item>a {
    padding: 12px 0;
    margin: 0;
    display: flex;
    background-color: var(--yq-bg-primary)
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item>a:hover {
    background-color: var(--yq-bg-primary-hover)
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item:last-child>a {
    border-bottom: none
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-primary)!important
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item-selected .larkui-icon {
    visibility: visible
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item-selected>a {
    background-color: var(--yq-bg-primary)!important
}

.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item-disabled,.RoleSelector-module_menu_MwfMA .ant-dropdown-menu-item-disabled .RoleSelector-module_label_JI2Gx {
    color: var(--yq-text-caption)
}

.RoleSelector-module_menuItem_cwz56 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    font-size: 12px
}

.RoleSelector-module_menuItem_cwz56 .RoleSelector-module_label_JI2Gx {
    display: block;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.RoleSelector-module_menuItem_cwz56 .RoleSelector-module_desc_lnp7h {
    color: var(--yq-text-caption)
}

.Item-module_joinUserItem_0O6yw {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.Item-module_userInfo_F4pgj {
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: center
}

.Item-module_userInfo_F4pgj p {
    margin: 0
}

.Item-module_userInfo_F4pgj.Item-module_showItem_sCn3R {
    margin-right: 40px
}

.Item-module_userInfo_F4pgj .Item-module_avatar_JD1HW {
    display: block;
    margin-right: 12px
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM {
    max-width: 220px;
    margin-right: 4px;
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM.Item-module_selectorItem_UePAS {
    max-width: 232px
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM .Item-module_loginShow_DePxA {
    margin-bottom: 0
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM .Item-module_login_bu7O2 {
    color: var(--yq-text-caption)
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM .Item-module_department_y3daB {
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 20px;
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_name_ff-QM .Item-module_dingtalkGroupTag_lW9PH {
    color: var(--yq-ant-link-color)
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_applyPending_7Va\+N {
    display: inline-block
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_applyPending_7Va\+N .Item-module_pending_KikZW {
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    color: var(--yq-text-caption);
    line-height: 22px
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_applyPending_7Va\+N .Item-module_pending_KikZW.Item-module_pendingRole_mvO-A {
    color: var(--yq-yuque-grey-9);
    margin-left: 4px
}

.Item-module_userInfo_F4pgj .Item-module_userDesc_q4mqR .Item-module_topRightDesc_dQW2s {
    white-space: nowrap;
    background-color: var(--yq-yuque-grey-2);
    border-radius: 10px;
    line-height: 10px;
    border: .5px solid var(--yq-yuque-grey-5);
    font-size: 10px;
    color: var(--yq-yuque-grey-7);
    padding: 3px 4px;
    vertical-align: middle
}

.Item-module_actions_Mfqej {
    height: 32px;
    align-items: center;
    display: flex
}

.Item-module_actionItem_piU3P {
    cursor: pointer;
    display: inline-block;
    color: var(--yq-text-primary);
    padding: 0
}

.Item-module_actionItem_piU3P.Item-module_actionItemReject_rSq40 {
    margin-left: 20px
}

.Item-module_actionItemLabel_kzYeZ {
    padding-right: 16px;
    color: var(--yq-text-primary)
}

.Item-module_deleteActionItem_RJWcd {
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px
}

.Item-module_deleteActionItem_RJWcd .Item-module_label_qLBXy {
    display: block;
    font-size: 14px;
    color: var(--yq-red-6)
}

.Item-module_deleteActionItem_RJWcd .Item-module_desc_-\+chL {
    color: var(--yq-text-caption)
}

.join-user-search-selector .ant-select-dropdown .ant-select-item-option-selected .ant-select-item-option-state,.join-user-search-selector .ant-select-dropdown .ant-select-item-option-selected:hover .ant-select-item-option-state,.join-user-search-selector .ant-select-dropdown .ant-select-item-option .ant-select-item-option-state {
    display: none
}

.join-user-search-selector .ant-select-dropdown .ant-select-item-option {
    padding: 0 8px;
    overflow: visible;
    background: none;
    margin-bottom: 4px
}

.join-user-search-selector .full-item {
    padding: 8px
}

.join-user-search-selector .full-item:hover {
    border-radius: 6px;
    background-color: var(--yq-border-light)
}

.join-user-search-selector .ant-select-selection-item-content .full-item,.join-user-search-selector .simple-item {
    display: none
}

.join-user-search-selector .ant-select-selection-item-content .simple-item {
    display: block
}

.join-user-search-selector .selector .ant-select .ant-select-selector {
    background: none;
    height: 40px
}

.join-user-search-selector .ant-select-dropdown {
    padding: 8px 0;
    overflow: auto;
    overflow-x: hidden;
    min-height: 70px
}

.join-user-search-selector .ant-select-item-option-disabled {
    cursor: default
}

.join-user-search-selector .ant-select-dropdown .ant-select-item-option .larkicon-svg-check,.join-user-search-selector .ant-select-dropdown .ant-select-item-option .larkui-icon-check-outlined {
    visibility: visible
}

.join-user-search-selector .ant-select-item-option:first-child,.join-user-search-selector .ant-select-item-option:last-child {
    border-radius: 0
}

.join-user-search-selector .ant-select-dropdown>div {
    overflow: visible!important
}

.EmptyTip-module_emptyTip_aBtYt {
    text-align: center;
    height: 244px;
    padding-top: 40px;
    padding-bottom: 56px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.EmptyTip-module_emptyImageWrapper_6BIQC {
    width: 110px;
    height: 110px;
    padding: 15.28px 0 11.68px 12.83px;
    margin-bottom: 16px
}

.EmptyTip-module_emptyImage_u42M6 {
    width: 97.78px;
    height: 83.04px
}

.EmptyTip-module_emptyText_JUjEv {
    font-size: 14px;
    color: var(--yq-text-caption);
    text-align: center
}

.List-module_item_Yt8gv {
    padding: 10px
}

.List-module_bookClick_9l4Fn {
    cursor: pointer;
    padding: 10px
}

.List-module_bookClick_9l4Fn:hover {
    background-color: var(--yq-yuque-grey-2);
    border-radius: 8px
}

.Modal-module_title_Fhe\+q {
    font-size: 14px
}

.Modal-module_menu_GTmMW .ant-menu-item {
    padding-left: 24px;
    padding-right: 24px
}

.Modal-module_menu_GTmMW .ant-menu-item:not(.ant-menu-item-selected):hover {
    border-color: transparent;
    color: var(--yq-text-caption)
}

.Modal-module_searchWrapper_G76nZ {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px
}

.Modal-module_searchSelector_qZfrJ {
    width: 100%;
    height: 40px
}

.Modal-module_searchAdd_srKkd {
    flex: none;
    padding: 9px 0 9px 16px;
    color: var(--yq-yuque-grey-7);
    cursor: pointer;
    font-size: 14px
}

.Modal-module_btnLoadMore_\+NLxh {
    text-align: center;
    cursor: pointer
}

.Modal-module_spinContainer_6y18w {
    padding: 0 20px 20px 20px;
    height: 312px
}

.Modal-module_spinContainer_6y18w .ant-skeleton {
    height: 32px;
    padding: 10px
}

.Modal-module_spinContainer_6y18w .ant-skeleton-header {
    padding-right: 12px
}

.Modal-module_spinContainer_6y18w .ant-skeleton.ant-skeleton-active .ant-skeleton-avatar {
    border-radius: 32px;
    height: 32px;
    width: 32px
}

.Modal-module_spinContainer_6y18w .ant-skeleton-with-avatar .ant-skeleton-content .ant-skeleton-title {
    margin-top: 4px
}

.Modal-module_listWrapper_YI-Wn {
    padding: 0 10px 10px 10px;
    max-height: 244px;
    overflow-y: auto;
    overflow-x: clip
}

.index-module_revertLink_csROb {
    margin-left: 4px
}

.SearchNotFound-module_notfound_MSXOw {
    padding: 4px 12px;
    min-height: 200px
}

.SearchNotFound-module_notfoundTitle_Br7DA {
    color: var(--yq-text-body);
    font-size: 14px;
    word-break: break-all;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word
}

.index-module_searchModalWrapper_vtDAq .ant-modal-content {
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-modal-close {
    display: none
}

.index-module_searchModalWrapper_vtDAq .search-dropdown ul {
    padding-top: 0;
    box-shadow: none;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-top: 1px solid var(--yq-yuque-grey-3)
}

.index-module_searchModalWrapper_vtDAq .search-dropdown:before {
    display: none
}

.index-module_searchModalWrapper_vtDAq .ant-modal-body {
    padding: 0;
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-input-affix-wrapper {
    border: 0!important;
    box-shadow: none!important
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 {
    padding: 15px;
    font-size: 16px;
    line-height: 24px;
    box-shadow: none
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 input {
    color: var(--yq-text-primary);
    margin-right: 32px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 svg {
    font-size: 16px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 .ant-input {
    padding-left: 2px!important
}

.index-module_searchInput_MvuM1+div {
    position: relative!important
}

.index-module_navigation_uXN0h {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.index-module_navigation_uXN0h.ant-popover-open,.index-module_navigation_uXN0h:hover {
    transition: all .3;
    background-color: var(--yq-bg-primary-hover)
}

.index-module_navigation_uXN0h .index-module_triggerIcon_qjqIF {
    color: var(--yq-text-caption)
}

.index-module_navigationPopoverOverlay_h0IVJ {
    margin-left: 6px;
    width: 254px
}

.index-module_navigationPopoverOverlay_h0IVJ .ant-popover-inner-content {
    margin-top: 4px
}

.btn-follow .hover-text,.btn-follow:hover .default-text {
    display: none
}

.btn-follow:hover .hover-text {
    display: inline
}

.btn-follow.ant-btn-default:focus,.btn-follow.ant-btn-default:hover {
    color: var(--yq-text-body);
    border-color: var(--yq-border-primary);
    outline: none
}

.btn-follow.ant-btn-clicked:after {
    display: none
}

.btn-plain-follow {
    color: var(--yq-text-caption)
}

.btn-plain-follow .larkui-icon {
    margin-right: 4px;
    color: var(--yq-text-caption)
}

.btn-plain-follow>a,.btn-plain-follow>a .larkui-icon {
    color: var(--yq-text-caption)
}

.btn-plain-follow>a:hover,.btn-plain-follow>a:hover .larkui-icon {
    color: var(--yq-text-body)
}

.index-module_count_pDHQn {
    margin-left: 8px;
    font-weight: 500
}

.index-module_offlineButton_XZkWh {
    cursor: not-allowed
}

.index-module_disabledStyle_MeBOz {
    opacity: .4;
    cursor: not-allowed;
    pointer-events: none
}

.OrgUserInfo-module_departmentInfo_3udmp {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-light)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_cardHeader_502Md {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq {
    margin: 0 24px 0 0
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: center;
    margin-right: 24px;
    line-height: 26px;
    color: var(--yq-text-body)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq .OrgUserInfo-module_icon_okrek,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz .OrgUserInfo-module_icon_okrek {
    margin-right: 8px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_fullWidth_zv0Qo {
    width: 100%;
    line-height: 32px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6:nth-child(n+2) {
    margin-top: 16px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97 {
    display: inline-block;
    margin-left: 4px;
    width: 100%;
    font-weight: 500
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97.OrgUserInfo-module_h5_M\+nrk {
    margin-left: 8px;
    font-weight: 400;
    font-size: 16px
}

.OrgUserInfo-module_divider_igZMz {
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background-color: var(--yq-border-light)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or {
    padding: 16px;
    width: 280px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 8px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 {
    position: relative;
    padding-left: 56px;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_avatar_iMqQ\+ {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_wrapper_cpsZr {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_nameWrapper_u01vS {
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_name_9rYrf {
    max-width: 100%;
    color: var(--yq-text-primary);
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_tag_dWOJO {
    margin-left: 4px;
    padding: 0 4px;
    background-color: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    color: var(--yq-text-body);
    font-size: 12px;
    height: 20px;
    line-height: 20px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_description_4Q94H {
    margin-top: 4px;
    font-size: 14px;
    line-height: 18px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_icon_okrek {
    color: var(--yq-icon-secondary)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: flex-start;
    width: 100%
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6 span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    margin-left: 8px;
    color: var(--yq-text-body);
    font-size: 14px;
    line-height: 22px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    margin-top: 12px;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6:nth-child(n+3) {
    margin-top: 12px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_icon_okrek {
    transform: translateY(4px)
}

.index-module_userCard_NOTbr {
    width: 330px
}

.index-module_userCard_NOTbr .index-module_body_20bgh {
    display: flex;
    padding: 20px 18px 20px 24px
}

.index-module_userCard_NOTbr img.index-module_avatar_OBHFJ {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    flex-shrink: 0
}

.index-module_userCard_NOTbr .index-module_userInfos_B16Pa {
    margin-left: 20px;
    width: 220px
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    word-break: break-all;
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_name_QeZm4 {
    color: var(--yq-text-primary)
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_dingtalk_kYXBj {
    height: 22px;
    vertical-align: middle;
    display: inline-block
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_badge_lsJBP {
    top: -2px;
    height: 22px
}

.index-module_userCard_NOTbr .index-module_dingding_l7pi0 {
    margin-left: 4px;
    width: 18px;
    height: 18px
}

.index-module_userCard_NOTbr .index-module_infoWithBg_JGcdP {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    color: var(--yq-text-body);
    background-color: var(--yq-bg-tertiary);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 400;
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_signature_aQ\+Wz {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-body);
    margin-top: 8px;
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_infoList_Y58li {
    margin-top: 10px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 {
    display: flex;
    margin-bottom: 8px;
    min-height: 20px;
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7:last-child {
    margin-bottom: 0
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 .index-module_icon_MGwU8 {
    margin-top: 2px;
    margin-right: 8px;
    color: var(--yq-icon-primary)
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 span {
    color: var(--yq-text-primary);
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 a {
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_footer_FNHCP {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--yq-border-light);
    padding: 12px 18px 12px 24px
}

.index-module_userCard_NOTbr .index-module_follow_1HgLb {
    display: flex
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j {
    margin-right: 24px;
    color: var(--yq-text-disable);
    margin-top: 6px
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j:last-child {
    margin-right: 0
}

.index-module_userCard_NOTbr .index-module_number_NahUE {
    margin-left: 5px;
    color: var(--yq-text-primary);
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_userLink_i0XYI {
    float: right
}

.index-module_userCard_NOTbr .index-module_skeleton_Z4M4v {
    width: 100%;
    height: 50px;
    margin-top: 16px;
    overflow: hidden
}

.index-module_userCard_NOTbr .index-module_infoDetail_dKXCO {
    word-break: break-all
}

.index-module_popover_Xyidp {
    display: inline-block
}

.index-module_overlay_A0ouW .ant-popover-inner-content {
    padding: 0
}

.index-module_wrap_iKZPE {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body)
}

.index-module_wrap_iKZPE:hover .index-module_close_mZbMN {
    display: flex
}

.index-module_icon_gxtpV {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: none
}

.index-module_close_mZbMN {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_closeIcon_kM1zW {
    font-size: 16px
}

.doc-draft-tip {
    font-weight: 400
}

.doc-draft-tip-content .update-info {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.doc-draft-tip-content .update-info a {
    color: var(--yq-text-body)
}

.ant-tag.doc-template-tag {
    margin: 0 0 0 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.doc-title {
    font-size: 14px;
    line-height: 21px;
    text-overflow: ellipsis;
    color: var(--yq-text-body);
    font-family: Chinese Quote,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji
}

.doc-title-draft {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 4px;
    font-weight: 400
}

.doc-icon {
    margin-right: 4px
}

.doc-access-scope {
    margin-left: 8px
}

.doc-belong,.doc-belong a {
    color: var(--yq-text-caption)
}

.doc-belong a {
    margin: 0 4px
}

.doc-belong a:first-child {
    margin-left: 0
}

.doc-contributors,.doc-contributors span a {
    color: var(--yq-text-caption)
}

.index-module_articleTitle_VJTLJ {
    word-break: break-word
}

.index-module_popover_nfMC3 {
    display: inline
}

.index-module_belongMenu_2QmLB {
    outline: none;
    cursor: pointer
}

.index-module_belongMenu_2QmLB .larkui-icon {
    display: inline-block;
    font-size: 12px;
    margin-left: 4px
}

.index-module_belongText_TkCAl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu {
    min-width: 188px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item {
    display: flex;
    align-items: center
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .larkui-icon-check-outlined {
    margin-right: 6px;
    visibility: hidden;
    font-size: 16px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 280px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>.larkui-icon,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>span {
    display: inline-block;
    vertical-align: middle
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item:hover {
    background-color: var(--yq-bg-tertiary);
    font-weight: 400
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active .larkui-icon-check-outlined {
    visibility: visible
}

@media only screen and (max-width: 575px) {
    .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
        max-width:140px
    }
}

.index-module_privacy_QkaFB {
    display: inline-block;
    padding: 3px 5px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    color: var(--yq-text-body);
    line-height: 14px;
    font-size: 10px;
    font-weight: 400;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
    margin: 0 6px 0
}

.index-module_register_tFrSQ {
    margin-left: 8px
}

.index-module_belongs_A3uam {
    font-size: 12px;
    margin-left: 4px
}

.index-module_meta_Jn6WP {
    display: flex;
    line-height: 22px
}

.index-module_meta_Jn6WP svg {
    margin-right: 8px;
    position: relative;
    top: 3px
}

.index-module_meta_Jn6WP span {
    display: block
}

.index-module_meta_Jn6WP.index-module_truncation_g-6vp span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.toggle-publicity {
    color: var(--yq-text-link)!important;
    margin-left: 12px
}

.index-module_label_qcR\+D {
    border-radius: 2px;
    background-color: var(--yq-bg-tertiary);
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption);
    text-align: center
}

.DocTask-module_docTask_NywmO {
    padding: 30px 0 20px
}

.DocTask-module_docTask_NywmO .larkui-icon-check-outlined-circle {
    color: var(--yq-theme);
    font-size: 22px
}

.DocTask-module_docTask_NywmO .larkui-icon-close-circle,.DocTask-module_docTask_NywmO .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error);
    font-size: 20px
}

.DocTask-module_docTask_NywmO .action,.DocTask-module_docTask_NywmO .error,.DocTask-module_docTask_NywmO .icon,.DocTask-module_docTask_NywmO .tip {
    line-height: 27px;
    text-align: center;
    color: var(--yq-text-primary)
}

.DocTask-module_docTask_NywmO .error {
    color: var(--yq-function-error)
}

.DocTask-module_settingFile_bFy51 {
    margin-top: 16px;
    margin-bottom: 16px
}

.DocTask-module_hr_y8Wz9 {
    background-color: var(--yq-yuque-grey-5);
    height: 1px;
    margin: 15px 0 5px
}

.DocTask-module_settingFileIcon_\+\+xRm {
    width: 64px;
    height: 64px;
    margin-right: 5px
}

.DocTask-module_settingFileIcon_\+\+xRm img {
    width: 100%
}

.DocTask-module_settingContent_leSlb {
    min-height: 140px
}

.DocTask-module_settingContent_leSlb .ant-checkbox-wrapper,.DocTask-module_settingContent_leSlb .ant-radio-wrapper {
    margin-top: 8px;
    margin-left: 0;
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form .ant-legacy-form-item {
    margin-bottom: 0;
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-control {
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-label>label {
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-radio-group {
    border-top: 1px solid var(--yq-yuque-grey-5);
    margin-top: 10px
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label {
    display: block
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label>label.ant-legacy-form-item-no-colon:after {
    display: none
}

.DocTask-module_settingContent_leSlb .DocTask-module_isFirst_RPJNh .ant-radio-group {
    border-top: 0 none;
    margin-top: 0;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    padding-bottom: 10px
}

.DocTask-module_settingTitlt2_OZfjd {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 400;
    margin: 8px 0
}

.DocTask-module_settingFileTypeName_S4Bv1 {
    color: var(--yq-yuque-grey-9)
}

.DocTask-module_settingFileTypeExt_GCoBd {
    color: var(--yq-text-caption);
    font-size: 12px
}

.index-module_fileTypeSelector_73BHW {
    margin: 16px 0
}

.index-module_fileTypeSelector_73BHW .index-module_item_H3xgY {
    height: 152px
}

.index-module_fileTypeSelector_73BHW .index-module_fileType_l65Jm {
    width: 142px;
    text-align: center;
    padding: 16px 0 20px;
    cursor: pointer;
    transition: all .3s linear
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM {
    width: 70px;
    height: 70px;
    margin: 0 auto
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM img {
    width: 100%
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeMeta_4eYr8 {
    text-align: center;
    line-height: 24px;
    margin-top: 4px
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeName_9B0Rn {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeExt_wTTFB {
    font-size: 12px;
    line-height: 1;
    color: var(--yq-text-caption)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeReady_TAUvL {
    filter: grayscale(1);
    cursor: default;
    opacity: .45
}

.EvernoteDirUpload-module_modal_AERn- {
    padding: 16px
}

.EvernoteDirUpload-module_modal_AERn- .ant-upload {
    width: 100%
}

.EvernoteDirUpload-module_modal_AERn- .ant-modal-close-x {
    width: 24px;
    height: 24px;
    line-height: 32px
}

.EvernoteDirUpload-module_dragZone_qrdZx {
    border: dashed 1px var(--yq-border-primary);
    border-radius: 8px;
    width: 100%;
    height: 180px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_tip_FtuKl {
    padding-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption)
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_icon_ZYE-k {
    color: var(--yq-text-caption);
    margin-bottom: 12px
}

.EvernoteDirUpload-module_dragZone_qrdZx.EvernoteDirUpload-module_dragOver_0DaAV {
    border-color: var(--yq-blue-5)
}

.EvernoteDirUpload-module_dragZone_qrdZx:hover {
    border-color: var(--yq-border-primary-active)
}

.BookExport-module_docExport_44PUl .BookExport-module_title_Hezdm {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.BookExport-module_docExport_44PUl .BookExport-module_tips_VJJKo {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.BookExport-module_setting_PXAtw .BookExport-module_settingTitle_pEiZb {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookExport-module_setting_PXAtw .ant-upload-select,.BookExport-module_setting_PXAtw .BookExport-module_settingUpload_sNiNV {
    width: 100%
}

.index-module_content_v7lvp,.index-module_form_5k71F .larkui-form-item {
    margin-bottom: 16px
}

.index-module_form_5k71F .larkui-form-item:last-child {
    margin-bottom: 0
}

.index-module_tip_yNrai {
    line-height: 40px;
    color: var(--yq-text-primary)
}

.BookAction-module_wrap_AiMmc .ant-menu {
    min-width: 89px;
    padding: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.BookAction-module_wrap_AiMmc .ant-menu-item-divider {
    margin: 4px 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item {
    color: var(--yq-text-body);
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:not(:last-child) {
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_bookTitle_lUUrB {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB {
    padding-left: 12px;
    flex: 1;
    overflow: hidden
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_group_xaGsx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption);
    font-size: 13px;
    line-height: 18px
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_text_UrMgl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 21px;
    padding-bottom: 4px
}

.index-module_bookTitle_lUUrB .index-module_checked_3fCif {
    padding: 0 4px;
    color: var(--yq-text-link);
    font-size: 20px;
    width: 28px
}

.index-module_moreActions_YYACs {
    margin-left: 8px
}

.book-link,.book-name,.lark-book-title {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-book-title .book-icon {
    margin-right: 8px
}

.book-name {
    display: flex;
    align-items: center
}

.book-name .book-name-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.book-name .book-name-scope {
    position: relative
}

.book-name .book-name-scope a {
    pointer-events: none
}

.book-name .book-name-scope .icon-svg {
    margin-left: 5px
}

.book-name .book-name-orglabel {
    margin-left: 12px
}

.book-name .icon-svg {
    display: block
}

.book-name-split {
    margin: 0 4px
}

.permission__public__tip {
    margin-top: 12px;
    line-height: 22px
}

.permission__public__tip .highlight {
    color: var(--yq-function-error)
}

.belong-icon {
    font-size: 12px;
    margin-left: 4px
}

.lark-breadcrumb {
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-breadcrumb .book-icon {
    display: block;
    margin-right: 12px;
    min-width: 24px;
    width: 24px;
    height: 24px;
    font-size: 24px
}

.lark-breadcrumb .ant-breadcrumb {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    max-width: 100%
}

.lark-breadcrumb .ant-breadcrumb>span {
    display: flex;
    align-items: center;
    overflow: hidden
}

.lark-breadcrumb .ant-breadcrumb>span:last-child {
    flex: auto
}

.lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 1.35
}

.lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link:hover {
    color: var(--yq-text-caption)
}

.lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link>span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-separator {
    position: relative;
    top: -1px
}

.lark-breadcrumb .icon-svg {
    display: block
}

.lark-breadcrumb .header-crumb-doc {
    display: flex;
    align-items: center
}

.lark-breadcrumb .doc-title {
    line-height: 1.35
}

.lark-breadcrumb .doc-access-scope {
    top: 0
}

.lark-breadcrumb-current {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.lark-breadcrumb-logo {
    margin-right: 24px;
    display: flex;
    align-items: center
}

.lark-breadcrumb-logo svg {
    display: block
}

.lark-breadcrumb-separator {
    margin: 0 8px;
    color: var(--yq-text-caption)
}

.lark-breadcrumb-link-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis
}

.lark-breadcrumb-link-ellipsis .book-name {
    display: flex
}

.lark-breadcrumb-link-ellipsis .book-name .book-name-text-wrapper {
    flex: 1;
    overflow: hidden
}

.lark-breadcrumb-link-ellipsis .book-name .book-name-scope {
    flex: 0 0 auto;
    display: flex;
    align-items: center
}

.index-module_playBtn_5wHuG.ant-btn {
    display: flex;
    align-items: center
}

.index-module_playBtn_5wHuG.ant-btn,.index-module_playBtn_5wHuG.ant-btn:hover {
    background: var(--yq-bg-secondary)
}

.index-module_playBtn_5wHuG.ant-btn .icon-svg {
    margin-right: 6px
}

/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:02.731Z
 */
.cropper-container {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.cropper-container img {
    backface-visibility: hidden;
    display: block;
    height: 100%;
    image-orientation: 0deg;
    max-height: none!important;
    max-width: none!important;
    min-height: 0!important;
    min-width: 0!important;
    width: 100%
}

.cropper-canvas,.cropper-crop-box,.cropper-drag-box,.cropper-modal,.cropper-wrap-box {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.cropper-canvas,.cropper-wrap-box {
    overflow: hidden
}

.cropper-drag-box {
    background-color: #fff;
    opacity: 0
}

.cropper-modal {
    background-color: #000;
    opacity: .5
}

.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid #39f;
    outline-color: rgba(51,153,255,.75);
    overflow: hidden;
    width: 100%
}

.cropper-dashed {
    border: 0 dashed #eee;
    display: block;
    opacity: .5;
    position: absolute
}

.cropper-dashed.dashed-h {
    border-bottom-width: 1px;
    border-top-width: 1px;
    height: 33.33333%;
    left: 0;
    top: 33.33333%;
    width: 100%
}

.cropper-dashed.dashed-v {
    border-left-width: 1px;
    border-right-width: 1px;
    height: 100%;
    left: 33.33333%;
    top: 0;
    width: 33.33333%
}

.cropper-center {
    display: block;
    height: 0;
    left: 50%;
    opacity: .75;
    position: absolute;
    top: 50%;
    width: 0
}

.cropper-center:after,.cropper-center:before {
    background-color: #eee;
    content: " ";
    display: block;
    position: absolute
}

.cropper-center:before {
    height: 1px;
    left: -3px;
    top: 0;
    width: 7px
}

.cropper-center:after {
    height: 7px;
    left: 0;
    top: -3px;
    width: 1px
}

.cropper-face,.cropper-line,.cropper-point {
    display: block;
    height: 100%;
    opacity: .1;
    position: absolute;
    width: 100%
}

.cropper-face {
    background-color: #fff;
    left: 0;
    top: 0
}

.cropper-line {
    background-color: #39f
}

.cropper-line.line-e {
    cursor: ew-resize;
    right: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-n {
    cursor: ns-resize;
    height: 5px;
    left: 0;
    top: -3px
}

.cropper-line.line-w {
    cursor: ew-resize;
    left: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-s {
    bottom: -3px;
    cursor: ns-resize;
    height: 5px;
    left: 0
}

.cropper-point {
    background-color: #39f;
    height: 5px;
    opacity: .75;
    width: 5px
}

.cropper-point.point-e {
    cursor: ew-resize;
    margin-top: -3px;
    right: -3px;
    top: 50%
}

.cropper-point.point-n {
    cursor: ns-resize;
    left: 50%;
    margin-left: -3px;
    top: -3px
}

.cropper-point.point-w {
    cursor: ew-resize;
    left: -3px;
    margin-top: -3px;
    top: 50%
}

.cropper-point.point-s {
    bottom: -3px;
    cursor: s-resize;
    left: 50%;
    margin-left: -3px
}

.cropper-point.point-ne {
    cursor: nesw-resize;
    right: -3px;
    top: -3px
}

.cropper-point.point-nw {
    cursor: nwse-resize;
    left: -3px;
    top: -3px
}

.cropper-point.point-sw {
    bottom: -3px;
    cursor: nesw-resize;
    left: -3px
}

.cropper-point.point-se {
    bottom: -3px;
    cursor: nwse-resize;
    height: 20px;
    opacity: 1;
    right: -3px;
    width: 20px
}

@media (min-width: 768px) {
    .cropper-point.point-se {
        height:15px;
        width: 15px
    }
}

@media (min-width: 992px) {
    .cropper-point.point-se {
        height:10px;
        width: 10px
    }
}

@media (min-width: 1200px) {
    .cropper-point.point-se {
        height:5px;
        opacity: .75;
        width: 5px
    }
}

.cropper-point.point-se:before {
    background-color: #39f;
    bottom: -50%;
    content: " ";
    display: block;
    height: 200%;
    opacity: 0;
    position: absolute;
    right: -50%;
    width: 200%
}

.cropper-invisible {
    opacity: 0
}

.cropper-bg {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC")
}

.cropper-hide {
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.cropper-hidden {
    display: none!important
}

.cropper-move {
    cursor: move
}

.cropper-crop {
    cursor: crosshair
}

.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point {
    cursor: not-allowed
}

.index-module_coverUploaderWrapper_JM8NQ {
    display: flex;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 1.5;
    position: relative
}

.index-module_miniCoverUploaderWrapper_VBkGu {
    display: block
}

.index-module_cropper_FJZMe {
    position: relative;
    overflow: hidden;
    width: 240px;
    min-width: 240px;
    min-height: 148px;
    background-color: var(--yq-bg-tertiary);
    border-radius: 6px
}

.index-module_cropper_FJZMe .ant-upload.ant-upload-drag .ant-upload {
    padding: 0
}

.index-module_cropper_FJZMe .ant-upload.ant-upload-drag {
    border-color: transparent
}

.index-module_miniCropperWrapper_kKArf {
    display: flex;
    align-items: center;
    flex-direction: column
}

.index-module_miniCropper_x-fcL {
    width: 100%;
    min-height: 148px;
    position: relative;
    overflow: hidden;
    background-color: var(--yq-bg-tertiary);
    border-radius: 6px
}

.index-module_miniCropper_x-fcL .ant-upload.ant-upload-drag .ant-upload {
    padding: 0
}

.index-module_miniCropper_x-fcL .ant-upload.ant-upload-drag {
    border-color: transparent
}

.index-module_cropperInner_cE6wj,.index-module_miniCropperInner_rEny\+ {
    min-width: 238px;
    min-height: 143px
}

.index-module_coverThumb_aqkDl {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain
}

.index-module_placeholderWrapper_I8JnT {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: var(--yq-bg-tertiary)
}

.index-module_miniPlaceholderWrapper_CXNui {
    position: relative;
    width: 100%;
    height: 148px;
    background-color: var(--yq-bg-tertiary)
}

.index-module_placeholder_fRP2- {
    position: absolute;
    top: 74px;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    font-size: 12px;
    color: var(--yq-text-body);
    line-height: 1.5;
    text-align: center
}

.index-module_placeholderImg_RI3Xy {
    margin: 0 auto 16px;
    width: 72px;
    height: 72px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*o8puS5nC5Z4AAAAAAAAAAABkARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_preview_0S38r {
    width: 153px;
    height: 99px;
    overflow: hidden;
    border-radius: 6px
}

.index-module_miniPreview_siGvw {
    width: 100%;
    height: 99px;
    overflow: hidden;
    border-radius: 6px
}

.index-module_uploadWrapper_Lddj1 {
    margin-left: 16px
}

.index-module_miniUploadWrapper_kA5F2,.index-module_uploadWrapper_Lddj1 {
    display: flex;
    flex-flow: column;
    justify-content: flex-end;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_uploadTip_-PrVc {
    margin-top: 12px
}

.index-module_uploadAction_IvY3J {
    margin-top: 16px;
    display: flex;
    align-items: center;
    color: var(--yq-text-caption)
}

.index-module_miniUploadAction_zkhyc {
    margin-top: 14px;
    display: flex;
    justify-content: center
}

.index-module_miniUploadAction_zkhyc .index-module_uploadBtn_3NoLt {
    width: 197px
}

.index-module_miniUploadAction_zkhyc .index-module_clearBtn_Jvd1P {
    width: 60px;
    margin-left: 8px
}

.index-module_btnRemove_ewCeL {
    margin-left: 8px;
    transition: color .35s ease;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--yq-text-link)
}

.index-module_btnRemove_ewCeL .index-module_btnRemoveIcon_Kaa0G {
    margin-right: 4px;
    transition: all .35s ease;
    fill: var(--yq-ant-link-color)
}

.index-module_btnRemove_ewCeL .index-module_btnRemoveIcon_Kaa0G svg {
    position: relative;
    top: -1px;
    display: inline-block;
    vertical-align: middle
}

.index-module_btnRemove_ewCeL:hover {
    color: var(--yq-ant-link-hover-color)
}

.index-module_btnRemove_ewCeL:hover .index-module_btnRemoveIcon_Kaa0G {
    fill: var(--yq-ant-link-hover-color)
}

@media only screen and (max-width: 576px) {
    .index-module_coverUploaderWrapper_JM8NQ {
        flex-direction:column
    }

    .index-module_uploadWrapper_Lddj1 {
        margin-left: 0;
        margin-top: 16px
    }
}

.form-container .ant-card,.form .ant-card,.setting-form .ant-card {
    margin-bottom: 16px
}

.card-form>.ant-card-head {
    border-bottom: none
}

.card-form .card-form-title {
    font-size: 24px;
    line-height: 2;
    color: #262626;
    font-weight: 700
}

.ant-legacy-form .ant-legacy-form-item {
    color: var(--yq-text-primary)
}

.ant-legacy-form .ant-legacy-form-item:last-child {
    margin-bottom: 0
}

.ant-legacy-form .ant-legacy-form-item-required:before {
    display: none
}

.ant-legacy-form .ant-legacy-form-item-label .tip {
    font-size: 12px;
    color: var(--yq-ant-text-color-secondary)
}

.ant-legacy-form .ant-legacy-form-explain {
    clear: both
}

.ant-legacy-form .ant-card-body {
    color: var(--yq-text-primary)
}

.ant-legacy-form-extra .lark-btn-text {
    color: var(--yq-ant-link-color)
}

.ant-legacy-form-extra .lark-btn-text:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-legacy-form-extra .lark-btn-text:focus {
    color: var(--yq-text-link)
}

.setting-form .ant-card-body {
    color: var(--yq-text-primary)
}

.DocSummaryInfo-module_modal_WEf3n .ant-modal-header {
    padding: 24px 32px 0;
    border-bottom: 0
}

.DocSummaryInfo-module_modal_WEf3n .ant-modal-body {
    padding: 16px 32px 32px
}

.DocSummaryInfo-module_modal_WEf3n .ant-modal-close-x {
    width: 72px;
    height: 72px;
    line-height: 72px
}

.DocSummaryInfo-module_btnSubmit_-ZmDs {
    margin-right: 24px
}

@keyframes circle {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(1turn)
    }
}

#lake-doc-publish-button .publish-loading-icon {
    margin-top: 2px;
    width: 24px;
    height: 24px;
    animation: circle 1s linear infinite
}

.header-action-publish .publish-button {
    margin: 14px 0
}

.header-action-publish .publish-button .lark-btn {
    min-width: 72px
}

.header-action-publish .lark-dropmenu-body {
    left: auto;
    right: -20px;
    width: 280px;
    margin-top: 11px
}

.header-action-publish .lark-dropmenu-body:after,.header-action-publish .lark-dropmenu-body:before {
    content: "";
    display: block;
    position: absolute;
    top: -16px;
    right: 50%;
    margin-right: -90px
}

.header-action-publish .lark-dropmenu-body:before {
    border: 8px solid var(--yq-border-primary);
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent
}

.header-action-publish .lark-dropmenu-body:after {
    top: -15px;
    border: 8px solid var(--yq-white);
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent
}

.editor-publish-content {
    width: 310px;
    padding: 4px
}

.editor-publish-content>h3 {
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 700
}

.editor-publish-content>p {
    font-size: 14px;
    line-height: 1.5;
    color: var(--yq-text-caption)
}

.editor-publish-content .check-subscriber {
    padding-top: 16px
}

.present-edit-modal .title {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 16px
}

.present-edit-modal .warning-icon {
    margin-right: 12px
}

.present-edit-modal .content {
    margin-top: 8px;
    margin-left: 28px
}

.ant-tag.doc-tag {
    border-radius: 50px;
    line-height: 24px;
    padding: 0 12px;
    background: var(--yq-bg-primary);
    margin-top: 8px;
    cursor: pointer;
    display: flex;
    align-items: center
}

.ant-tag.doc-tag:hover {
    color: var(--yq-text-link);
    border-color: var(--yq-cardborder-selected)
}

.ant-tag.doc-tag .tag-title {
    word-break: normal;
    white-space: pre-wrap
}

.ant-tag.doc-tag .larkui-icon-close {
    color: var(--yq-text-body);
    margin-right: -5px!important;
    width: 14px;
    height: 14px;
    margin-left: 4px!important
}

.ant-tag.doc-tag .larkui-icon-close svg {
    width: 8px
}

.doc-tag-auto-complete {
    margin-top: 8px!important;
    display: inline-block;
    vertical-align: bottom
}

.doc-tag-auto-complete .ant-select-selector {
    background: none!important;
    height: 26px!important
}

.doc-tag-auto-complete input {
    height: 25px!important;
    line-height: 24px!important
}

.Tag-module_wrapper_hMv3z {
    display: inline-block
}

.Tag-module_add_2EgPb {
    display: inline-block;
    width: 26px;
    height: 26px;
    line-height: 26px;
    margin-top: 8px;
    text-align: center;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer
}

.Tag-module_add_2EgPb .anction-plus {
    transform: scale(.8)
}

.doc-meta-edit .ant-legacy-form-item-control {
    line-height: 22px
}

.doc-meta-edit .ant-legacy-form-item-required:before {
    display: none
}

.doc-meta-edit .ant-legacy-form-item {
    margin-bottom: 12px
}

.doc-meta-edit .ant-input-group>.ant-input-group-addon {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: table-cell;
    max-width: 200px
}

.doc-meta-edit .ant-dropdown {
    width: 320px
}

.doc-setting-icon+div .ant-popover-inner-content {
    max-height: calc(100vh - 32px);
    overflow-y: auto
}

.DocMetaEdit-module_tagWrapper_H2QK1 .DocMetaEdit-module_tagTip_ThFbG {
    padding-left: 12px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocMetaEdit-module_cover_JaV7l {
    display: flex;
    justify-content: space-between
}

.DocMetaEdit-module_coverPlaceholder_c80Dd {
    display: flex;
    align-items: center;
    width: 200px;
    min-width: 200px;
    height: 124px;
    background: var(--yq-bg-tertiary);
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer
}

.DocMetaEdit-module_coverActions_CZ\+Ed {
    margin-left: 16px
}

.DocMetaEdit-module_btnRemoveCover_RF2fK {
    margin-top: 8px
}

.DocMetaEdit-module_btnRemoveCover_RF2fK>a {
    transition: all .35s ease;
    fill: var(--yq-ant-link-color)
}

.DocMetaEdit-module_btnRemoveCover_RF2fK>a:hover {
    fill: var(--yq-ant-link-hover-color)
}

.DocMetaEdit-module_btnRemoveCoverIcon_ec2SG {
    position: relative;
    top: 2px
}

.DocMetaEdit-module_moreBtn_c8CgD {
    color: var(--yq-text-link);
    padding-bottom: 16px;
    cursor: pointer
}

.doc-insight {
    color: var(--yq-text-caption)
}

.doc-insight .head {
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    color: var(--yq-text-primary);
    margin-bottom: 20px
}

.doc-insight .meta .word-count {
    line-height: 28px
}

.doc-insight .meta .word-count .num {
    font-size: 20px;
    color: var(--yq-text-body);
    font-weight: 700
}

.doc-insight .meta .word-count .desc {
    margin-left: 4px;
    vertical-align: top
}

.doc-insight .meta ul {
    margin-top: 8px;
    padding: 8px 0;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    line-height: 32px
}

.doc-insight .meta .user {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.doc-insight .contributors {
    margin-top: 12px
}

.doc-insight .contributors .title {
    line-height: 32px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.doc-insight .contributors .user {
    display: inline-block;
    margin-top: 8px
}

.doc-insight .contributors .user a {
    color: var(--yq-yuque-grey-900)
}

.KnowledgePieGuide-module_wrapper_McOcd {
    max-width: 218px;
    height: 76px;
    position: relative;
    border-radius: 8px;
    background-color: var(--yq-yuque-green-600);
    color: var(--yq-white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px
}

.KnowledgePieGuide-module_wrapper_McOcd .KnowledgePieGuide-module_close_Hta-f {
    position: absolute;
    cursor: pointer;
    top: 8px;
    right: 8px
}

.KnowledgePieGuide-module_breathBorder_JnuNa {
    max-width: 259px;
    height: 55px;
    position: absolute;
    background-color: rgba(0,185,107,.1);
    border: 2px solid var(--yq-yuque-green-600);
    animation: KnowledgePieGuide-module_breathe_hzZ03 2s ease-in-out infinite;
    border-radius: 8px;
    left: 224px;
    top: -20px
}

@keyframes KnowledgePieGuide-module_breathe_hzZ03 {
    0% {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }

    50% {
        box-shadow: 0 0 0 2px var(--yq-yuque-green-600)
    }

    to {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }
}

.index-module_container_YifTz {
    white-space: break-spaces;
    z-index: 1020
}

.index-module_container_YifTz .ant-tooltip-inner {
    background-color: var(--yq-yuque-grey-2);
    padding: 0
}

.index-module_container_YifTz .ant-tooltip-arrow-content {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_container_YifTz p {
    font-size: 14px;
    font-weight: 700
}

.index-module_container_YifTz h4,.index-module_container_YifTz p {
    color: var(--yq-white);
    line-height: 22px
}

.index-module_titleContainer_E19Ts {
    position: relative
}

.index-module_content_u6ksX {
    background-color: var(--yq-blue-5);
    padding: 4px 10px;
    padding-right: 30px;
    border-radius: 6px;
    min-width: 250px;
    margin-top: 6px;
    margin-left: -2px
}

.index-module_close_xIF4s {
    position: absolute!important;
    top: 2px;
    right: -2px;
    color: var(--yq-white)
}

.index-module_close_xIF4s :hover {
    color: var(--yq-white)
}

.larkui-feature-guide>div {
    width: 284px!important
}

.larkui-feature-guide>div>div {
    z-index: 999;
    position: absolute
}

.larkui-feature-guide-sign {
    position: relative;
    width: 12px;
    height: 12px;
    line-height: 12px;
    cursor: pointer
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-outer {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 12px;
    height: 12px;
    border-radius: 12px;
    background: rgba(47,142,244,.2);
    animation: fadein 1s ease-in-out 1s infinite alternate
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-inner {
    display: inline-block;
    position: absolute;
    top: 3px;
    left: 3px;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: var(--yq-blue-5)
}

.larkui-feature-guide-mini .larkui-feature-guide-sign {
    display: inline-block;
    vertical-align: -.125em;
    margin-left: 8px
}

.larkui-feature-guide-lite {
    position: absolute;
    display: block;
    height: 12px;
    width: 12px;
    bottom: 0;
    left: 6px
}

.larkui-feature-guide-lite .larkui-feature-guide-sign {
    display: block;
    margin-left: 0
}

.larkui-feature-guide-lite-content {
    cursor: text
}

.larkui-feature-guide-lite-close {
    margin-left: 8px;
    cursor: pointer
}

.larkui-feature-guide-lite-close .icon-svg {
    vertical-align: middle;
    margin-bottom: 3px
}

.larkui-feature-guide-lite-close-multi-line {
    position: absolute;
    right: 12px;
    top: 18px
}

.larkui-feature-guide-lite-multi-line-button {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background: var(--yq-bg-secondary);
    color: var(--yq-function-info);
    min-width: 70px;
    height: 24px;
    margin: 8px 0
}

.larkui-feature-guide-lite-multi-line-button:hover {
    background: var(--yq-bg-tertiary)
}

.larkui-feature-guide-lite-multi-line-button:focus {
    outline: none
}

.larkui-feature-guide-content {
    max-width: 228px
}

.ant-tooltip.larkui-feature-guide-lite-tip {
    max-width: 360px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content,.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    background: var(--yq-blue-5)
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    padding: 8px 16px;
    font-size: 12px;
    line-height: 22px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content {
    width: 8.484px;
    height: 8.484px
}

.ant-tooltip-placement-bottomLeft.larkui-feature-guide-lite-tip .ant-tooltip-arrow {
    left: 15px
}

.ant-tooltip-placement-right {
    padding-left: 4px;
    margin-top: 8px
}

.modal-without-padding .ant-modal-content .ant-modal-body {
    padding: 0
}

.template-creator-content {
    margin: 0 24px 16px 24px
}

.template-creator-content p {
    margin: 16px 0 8px 0
}

.template-scope {
    display: flex;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 3px
}

.template-scope-more-icon {
    display: flex;
    align-items: center
}

.template-scope-icon {
    margin: 20px 16px 0 20px;
    width: 20px;
    height: 20px
}

.template-scope-text {
    margin: 16px 0;
    width: 234px
}

.template-scope-text p {
    white-space: normal;
    margin: 0
}

.template-scope-menu-item {
    display: flex
}

.template-scope-menu-item .template-scope-icon {
    margin: 15px 16px 0 8px
}

.template-scope-menu-item .template-scope-text {
    width: 230px;
    margin: 11px 0
}

.template-scope-icon .larkicon:before {
    font-size: 20px
}

.DocExport-module_docExport_zwx9b .DocExport-module_title_eGdGG {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.DocExport-module_entry_qjwYu {
    min-height: 192px
}

.DocExport-module_exportOptions_jSIWB {
    margin-top: 24px
}

.DocExport-module_setting_yai1Y .DocExport-module_settingTitle_IjcSw {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.DocExport-module_setting_yai1Y .ant-upload-select,.DocExport-module_setting_yai1Y .DocExport-module_settingUpload_3ymtX {
    width: 100%
}

.DocExport-module_emptyExport_Djom5 {
    padding: 10px 0
}

.DocImport-module_docImport_uDZWD .DocImport-module_title_vylxD {
    color: var(--yq-text-primary);
    font-weight: 700
}

.DocImport-module_docImport_uDZWD .DocImport-module_title2_APgF7 {
    color: var(--yq-text-caption)
}

.DocImport-module_docImport_uDZWD .DocImport-module_tips_gx2\+N {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px;
    margin-bottom: 30px
}

.DocImport-module_docImport_uDZWD .DocImport-module_help_YDms4 {
    color: var(--yq-text-link);
    margin-left: 8px
}

.DocImport-module_processingModal_5vLO\+ .ant-modal-body {
    padding: 0
}

.DocImport-module_setting_6ttU6 {
    padding: 20px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingTitle_EsA43 {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFile_I6\+s\+ {
    margin-top: 16px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileIcon_HBcXz {
    width: 43px;
    margin: 16px 18px 16px 8px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileIcon_HBcXz img {
    width: 100%
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingContent_aLLs- {
    height: 170px;
    overflow-y: auto
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingContent_aLLs- .ant-checkbox-group-item {
    display: block;
    margin-top: 8px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileTypeName_LeTUR {
    color: var(--yq-yuque-grey-9)
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingFileTypeExt_LWcRn {
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocImport-module_setting_6ttU6 .DocImport-module_settingUpload_WXf0g {
    width: 100%;
    margin-top: 16px
}

.DocImport-module_setting_6ttU6 .ant-upload-select {
    width: 100%
}

.DocImport-module_processing_K3nVL .DocImport-module_processingTitle_UzLg1 {
    font-size: 14px;
    color: var(--yq-text-primary);
    padding: 24px 24px 0
}

.DocImport-module_processing_K3nVL .DocImport-module_processingFooter_xOxG5 {
    padding: 16px 24px
}

.DocImport-module_processing_K3nVL .DocImport-module_fileList_jRvlw {
    margin-top: 26px;
    padding: 0 24px;
    height: 330px;
    overflow-y: auto
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItem_gtT95 {
    margin-top: 10px
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItemIcon_SdTY- {
    width: 24px;
    margin-right: 8px;
    vertical-align: middle
}

.DocImport-module_processing_K3nVL .DocImport-module_fileItemTitle_qprAi {
    font-size: 14px;
    font-weight: 400;
    max-width: 245px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
    display: inline-block
}

.DocImport-module_processing_K3nVL a.DocImport-module_fileItemTitle_qprAi:hover {
    color: var(--yq-text-body)
}

.DocImport-module_processing_K3nVL .DocImport-module_spin_aEZeg {
    margin-left: 8px
}

.DocImport-module_processing_K3nVL .DocImport-module_uploadingStatus_UWCen {
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocImport-module_processing_K3nVL .DocImport-module_hr_nD0TF {
    height: 1px;
    background-color: var(--yq-bg-primary-hover-light)
}

.DocImport-module_processing_K3nVL .larkui-icon-close-circle,.DocImport-module_processing_K3nVL .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error)
}

.DocImport-module_processing_K3nVL .larkui-icon-check-outlined-circle {
    color: var(--yq-ant-success-color);
    margin-left: 2px
}

.DocImport-module_processing_K3nVL .ant-spin-spinning {
    color: var(--yq-function-info)
}

.DocImport-module_processing_K3nVL .DocImport-module_footerTitle_2O1eI {
    color: var(--yq-text-primary);
    margin: 0 8px
}

.DocImport-module_subTips_oayTw {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-top: 4px
}

.DocImport-module_radioGroup_klLUa {
    gap: 12px;
    display: flex;
    flex-direction: column;
    margin-top: 12px;
    margin-bottom: 12px
}

.DocImport-module_radioGroup_klLUa label {
    height: 66px;
    display: flex;
    align-items: center;
    background-color: var(--yq-yuque-grey-2);
    border-radius: 12px;
    padding-left: 16px
}

.Activity-module_contentHtml_YXsAJ {
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.Activity-module_contentHtml_YXsAJ h4 {
    color: var(--yq-text-primary);
    font-weight: 500;
    margin-bottom: 8px
}

.Activity-module_contentHtml_YXsAJ p {
    margin-bottom: 20px
}

.Activity-module_contentHtml_YXsAJ li {
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 4px;
    color: var(--yq-text-body);
    padding-left: 16px;
    position: relative
}

.Activity-module_contentHtml_YXsAJ li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--yq-yuque-grey-6)
}

.Activity-module_contentHtml_YXsAJ a {
    color: var(--yq-text-link)
}

.Activity-module_title_qmKTC {
    line-height: 27px;
    font-size: 19px;
    color: var(--yq-text-primary);
    font-weight: 500;
    margin-bottom: 12px
}

.Activity-module_bg_nXCHP {
    width: 424px;
    height: 180px;
    position: relative
}

.Activity-module_normalContainer_HmMNc .Activity-module_content_fRHS0 {
    padding: 24px
}

.Activity-module_normalContainer_HmMNc .Activity-module_normalFooter_d7pbA {
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    align-items: center
}

.Activity-module_normalContainer_HmMNc .Activity-module_normalFooter_d7pbA .Activity-module_footerRight_4r2hD .Activity-module_buttonItem_ivUR7 {
    margin-right: 8px
}

.Activity-module_normalContainer_HmMNc .Activity-module_normalFooter_d7pbA .Activity-module_otherLink_ImlX\+ {
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption);
    cursor: pointer;
    margin: 8px 0 2px 0
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrContent_88ama {
    padding: 24px 32px 12px
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrContent_88ama .Activity-module_title_qmKTC {
    text-align: center
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrContent_88ama .Activity-module_qrContentHtml_qC4nd {
    display: flex;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    padding: 0 8px
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrFooter_sNSWT {
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrFooter_sNSWT .Activity-module_qrImageContainer_KFTaP {
    margin: 16px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*tnXtSL-9OgsAAAAAAAAAAAAADvuFAQ/original);
    background-size: 174px 174px;
    width: 174px;
    height: 174px
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrFooter_sNSWT .Activity-module_qrImageContainer_KFTaP .Activity-module_qrImage_6pU6j {
    width: 154px;
    height: 154px
}

.Activity-module_qrCodeContainer_I0s58 .Activity-module_qrFooter_sNSWT .Activity-module_qrInfo_s5zGr {
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption);
    text-align: center;
    padding-bottom: 16px
}

.Announcement-module_contentHtml_-L1wW {
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.Announcement-module_contentHtml_-L1wW h4 {
    color: var(--yq-text-primary);
    font-weight: 500;
    margin-bottom: 8px
}

.Announcement-module_contentHtml_-L1wW p {
    margin-bottom: 20px
}

.Announcement-module_contentHtml_-L1wW li {
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 4px;
    color: var(--yq-text-body);
    padding-left: 16px;
    position: relative
}

.Announcement-module_contentHtml_-L1wW li:before {
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--yq-yuque-grey-6)
}

.Announcement-module_contentHtml_-L1wW a {
    color: var(--yq-text-link)
}

.Announcement-module_title_wB2-b {
    line-height: 27px;
    font-size: 19px;
    color: var(--yq-text-primary);
    font-weight: 500;
    margin-bottom: 12px
}

.Announcement-module_bg_cRpBE {
    width: 424px;
    height: 180px;
    position: relative
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_bgImage_QnLn6 {
    width: 100%;
    height: 192px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_iconImage_9oXmv {
    width: auto;
    margin: 32px 0 0 32px;
    max-height: 120px;
    min-height: 40px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementBodyContainer_xU05v {
    padding: 20px 32px 12px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementBodyContainer_xU05v .Announcement-module_title_wB2-b {
    margin-bottom: 18px;
    margin-top: 6px;
    font-size: 19px;
    line-height: 27px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementBodyContainer_xU05v .Announcement-module_noIconTitle_AwV3Z {
    margin-top: 4px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementBodyContainer_xU05v .Announcement-module_announcementContentHtml_sqbVd {
    margin-bottom: 24px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementFooterContainer_cc\+8b {
    padding: 0 32px 4px
}

.Announcement-module_announcementContainer_Ezu\+h .Announcement-module_announcementFooterContainer_cc\+8b .Announcement-module_buttonItem_4fd5R {
    margin-right: 8px
}

.ActivityContent-module_closeContainer_4EyYi {
    width: 48px;
    height: 48px;
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer
}

.ActivityContent-module_closeContainer_4EyYi .ActivityContent-module_close_qIdFs {
    display: flex;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin: 12px 12px auto auto
}

.ActivityContent-module_closeContainer_4EyYi:hover .ActivityContent-module_close_qIdFs {
    background-color: rgba(0,0,0,.65)
}

.ActivityContent-module_closeContainer_4EyYi:hover .ActivityContent-module_lightCloseIcon_w\+ar9 {
    background-color: var(--yq-yuque-grey-4)
}

.ActivityContent-module_closeContainer_4EyYi:hover .ActivityContent-module_lightCloseIcon_w\+ar9 svg {
    color: var(--yq-yuque-grey-9)
}

.ActivityContent-module_close_qIdFs {
    background-color: rgba(0,0,0,.45);
    transition: all .1s ease-in
}

.ActivityContent-module_close_qIdFs svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    color: #fff
}

.ActivityContent-module_lightCloseIcon_w\+ar9 {
    background-color: var(--yq-yuque-grey-2);
    transition: all .1s ease-in
}

.ActivityContent-module_lightCloseIcon_w\+ar9 svg {
    color: var(--yq-yuque-grey-8)
}

.index-module_modal_BOc7H {
    padding-bottom: 0
}

.index-module_modal_BOc7H .ant-modal-content {
    position: relative;
    overflow: hidden
}

.index-module_modal_BOc7H .ant-modal-body {
    padding: 0 0 24px
}

.index-module_modal_BOc7H .ant-modal-confirm-btns {
    display: none
}

.index-module_modal_BOc7H .ant-modal-confirm-content {
    margin-top: 0
}

.Slider-module_slideTitle_jDMWk {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    color: var(--yq-black)
}

.Slider-module_slideContainer_5puOR {
    flex: 1;
    margin-left: 24px;
    margin-right: 20px
}

.Slider-module_slideContainer_5puOR .ant-slider-track,.Slider-module_slideContainer_5puOR .ant-slider:hover .ant-slider-track {
    background: none
}

.Slider-module_slideContainer_5puOR .ant-slider-with-marks {
    margin-bottom: 8px
}

.Slider-module_slideContainer_5puOR .ant-slider-dot,.Slider-module_slideContainer_5puOR .ant-slider-dot.ant-slider-dot-active {
    background: var(--yq-yuque-grey-5);
    border-color: var(--yq-yuque-grey-5);
    height: 6px;
    width: 6px
}

.Slider-module_slideContainer_5puOR .ant-slider-handle,.Slider-module_slideContainer_5puOR .ant-slider-handle:focus,.Slider-module_slideContainer_5puOR .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
    border: 1px solid var(--yq-border-primary);
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04);
    transition: none;
    margin-top: -6px
}

.Slider-module_slideContainer_5puOR .ant-slider:active .ant-slider-handle,.Slider-module_slideContainer_5puOR .ant-slider:hover .ant-slider-handle:hover {
    border-color: var(--yq-blue-6)!important
}

.Slider-module_slideContainer_5puOR .ant-slider-rail {
    background: var(--yq-yuque-grey-5);
    height: 2px
}

.Slider-module_slideContainer_5puOR .ant-slider.classic .ant-slider-step .ant-slider-dot:nth-child(4),.Slider-module_slideContainer_5puOR .ant-slider.classic .ant-slider-step .ant-slider-dot:nth-child(4).ant-slider-dot-active,.Slider-module_slideContainer_5puOR .ant-slider.traditional .ant-slider-step .ant-slider-dot:nth-child(3),.Slider-module_slideContainer_5puOR .ant-slider.traditional .ant-slider-step .ant-slider-dot:nth-child(3).ant-slider-dot-active {
    background: var(--yq-yuque-grey-7);
    border-color: var(--yq-yuque-grey-7)
}

.Select-module_selectTitle_o273v {
    font-size: 14px;
    height: 22px;
    line-height: 22px;
    margin-bottom: 12px;
    color: var(--yq-black)
}

.Select-module_selectOptions_iZn-b {
    display: flex
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB {
    display: flex;
    flex-direction: row;
    cursor: pointer;
    text-align: center;
    height: 40px;
    width: 140px;
    margin-left: 12px;
    line-height: 40px;
    padding: 5px 9px 6px 9px;
    border-radius: 6px;
    border: 1px solid var(--yq-border-primary)
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB:first-child {
    margin-left: 0
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB .Select-module_iconSvg_3tsh1 {
    color: var(--yq-text-caption)
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB.Select-module_optionActive_HTKqR {
    border: 1px solid var(--yq-blue-2)
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB.Select-module_optionActive_HTKqR .Select-module_iconSvg_3tsh1 {
    color: var(--yq-text-link-hover)
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB:hover {
    background: var(--yq-bg-tertiary)
}

.Select-module_selectOptions_iZn-b .Select-module_optionAction_KeaaB .Select-module_optionTitle_ZbDCu {
    display: flex;
    justify-content: space-around;
    font-size: 14px;
    text-align: center;
    height: 28px;
    line-height: 28px;
    margin-left: 21px;
    color: var(--yq-black)
}

.Switch-module_switchTitle_WJKZB {
    font-size: 14px;
    flex: 1;
    color: var(--yq-black)
}

.Switch-module_switchContainer_Ajcua {
    margin-left: 0;
    margin-top: -2px
}

.RecipientSelector-module_recipient_Ds5UW {
    position: relative;
    min-height: 50px
}

.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content .dep,.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content img {
    display: none
}

.RecipientSelector-module_recipient_Ds5UW .ant-select-selection-item-content .name {
    margin-left: 0
}

.RecipientSelector-module_recipient_Ds5UW .ant-select:not(.ant-select-combobox) .ant-select-selector {
    background: var(--yq-bg-primary)
}

.RecipientSelector-module_mention_5r6oN {
    position: absolute;
    right: 0;
    top: -24px;
    cursor: pointer;
    color: var(--yq-text-link)
}

.RecipientSelector-module_userForCheck_a4ZZP {
    position: absolute;
    width: 100%;
    border: 1px solid var(--yq-border-primary);
    background: var(--yq-bg-primary);
    border-radius: 4px;
    z-index: 100;
    overflow: scroll;
    max-height: 300px
}

.RecipientSelector-module_userForCheck_a4ZZP p {
    padding: 8px 16px
}

.RecipientSelector-module_userForCheck_a4ZZP p:hover {
    background: var(--yq-bg-secondary);
    cursor: pointer;
    white-space: normal;
    text-overflow: normal
}

.RecipientSelector-module_mailHistory_-ft0g {
    position: absolute;
    width: 100%;
    border: 1px solid var(--yq-border-primary);
    background: var(--yq-bg-primary);
    border-radius: 4px;
    z-index: 100
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_title_-yKJG {
    padding: 8px 16px;
    height: 40px;
    line-height: 24px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.RecipientSelector-module_mailHistory_-ft0g ul {
    max-height: 300px;
    overflow: scroll
}

.RecipientSelector-module_mailHistory_-ft0g li {
    padding: 8px 16px
}

.RecipientSelector-module_mailHistory_-ft0g li:hover {
    background: var(--yq-bg-secondary);
    cursor: pointer
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_users_ZwnLM {
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.RecipientSelector-module_mailHistory_-ft0g .RecipientSelector-module_info_eM40g {
    margin-right: 20px;
    margin-top: 6px;
    max-width: 50%;
    display: inline-block;
    color: var(--yq-text-caption);
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.RecipientSelector-module_avatar_lGHzI {
    position: relative;
    top: 10px
}

.RecipientSelector-module_name_HeaN7 {
    margin-left: 10px;
    color: var(--yq-text-primary);
    font-size: 14px
}

.RecipientSelector-module_dep_B2T89 {
    display: block;
    margin-left: 42px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocSendMail-module_sendMail_QBA\+O>p {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 8px
}

.DocSendMail-module_recipient_zqM9E h5,.DocSendMail-module_title_Af2FA h5 {
    padding: 16px 0 10px;
    margin-top: 10px;
    font-size: 14px
}

.DocSendMail-module_submitButton_HEsW- {
    margin-top: 16px
}

.DocSendMail-module_mailResult_AhaV4 {
    text-align: center;
    min-height: 200px;
    padding-top: 60px
}

.DocSendMail-module_mailResult_AhaV4 .larkui-spin {
    transform: scale(1.5)
}

.DocSendMail-module_mailResult_AhaV4 .larkicon {
    font-size: 42px
}

.DocSendMail-module_mailResult_AhaV4 .larkicon-check-o {
    color: var(--yq-theme)
}

.DocSendMail-module_mailResult_AhaV4 .larkicon-error {
    color: var(--yq-function-error)
}

.DocSendMail-module_mailResult_AhaV4 p {
    margin-top: 16px;
    line-height: 2
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub {
    width: 240px;
    border-right: none;
    padding: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item-divider,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item-divider {
    margin: 4px 0
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item-divider.menu-sub,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item-divider.menu-sub {
    margin: 16px 24px 8px 24px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:not(:last-child),.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:not(:last-child),.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:not(:last-child),.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:not(:last-child) {
    margin: 0
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu {
    color: var(--yq-text-primary);
    padding: 0 12px 0 20px;
    height: 36px;
    line-height: 36px;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_sceneItem_sDxCm {
    height: 99px;
    padding: 13px 10px 6px 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: auto
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj {
    margin-left: 4px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_checkbox_kHMEj .ant-checkbox-wrapper,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj .ant-checkbox-wrapper,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_checkbox_kHMEj .ant-checkbox-wrapper,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj .ant-checkbox-wrapper {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_checkbox_kHMEj .ant-checkbox-inner,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj .ant-checkbox-inner,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_checkbox_kHMEj .ant-checkbox-inner,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_checkbox_kHMEj .ant-checkbox-inner {
    width: 14px;
    height: 14px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_selectItem_Ga0F3 {
    height: 92px;
    padding: 8px 22px 12px;
    cursor: auto
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_submenuSwitchItem_AK8z3 {
    padding: 8px 24px 12px;
    display: flex;
    align-items: center;
    cursor: auto
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_slideItem_zrnG8 {
    height: 50px;
    padding: 6px 24px;
    display: flex;
    align-items: center;
    cursor: auto;
    background: none!important
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_switchItem_vBijY {
    height: 56px;
    padding-top: 6px;
    padding-bottom: 6px;
    display: flex;
    align-items: center;
    cursor: auto
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu.DocActionMore-module_infoItem_pGDVA {
    height: auto;
    padding-top: 8px;
    padding-bottom: 8px;
    cursor: auto
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu {
    height: auto;
    padding: 0
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu .ant-menu-submenu-title,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu .ant-menu-submenu-title {
    height: auto;
    line-height: 24px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu .ant-menu-submenu-title .DocActionMore-module_submenuContainer_mgSTF,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu .ant-menu-submenu-title .DocActionMore-module_submenuContainer_mgSTF {
    padding: 4px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu .ant-menu-submenu-arrow,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu .ant-menu-submenu-arrow {
    display: none
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu .DocActionMore-module_submenuIcon_BidTJ,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu .DocActionMore-module_submenuIcon_BidTJ {
    position: absolute;
    right: 10px
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-text-primary)
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover .ant-menu-submenu-arrow,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover .ant-menu-submenu-title,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover .ant-menu-submenu-arrow,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover .ant-menu-submenu-title,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover .ant-menu-submenu-arrow,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover .ant-menu-submenu-title,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover .ant-menu-submenu-arrow,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover .ant-menu-submenu-title {
    color: var(--yq-text-primary)
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-item:hover.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-root .ant-menu-submenu:hover.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-item:hover.DocActionMore-module_switchItem_vBijY,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_checkbox_kHMEj,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_infoItem_pGDVA,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_sceneItem_sDxCm,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_selectItem_Ga0F3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_slideItem_zrnG8,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_submenuSwitchItem_AK8z3,.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub .ant-menu-submenu:hover.DocActionMore-module_switchItem_vBijY {
    background: none!important
}

.DocActionMore-module_wrap_le9aB .ant-menu.ant-menu-sub {
    width: 340px;
    padding-bottom: 12px
}

.DocActionMore-module_sceneAction_KTXPK {
    display: flex;
    flex-direction: column;
    justify-content: center;
    cursor: pointer;
    text-align: center;
    align-items: center;
    line-height: normal;
    width: 68px;
    height: 100%;
    padding-top: 2px
}

.DocActionMore-module_sceneAction_KTXPK:hover {
    background-color: var(--yq-bg-primary-hover);
    border-radius: 8px
}

.DocActionMore-module_sceneAction_KTXPK .DocActionMore-module_sceneTitle_GJRbH {
    font-size: 14px;
    text-align: center;
    height: 22px;
    line-height: 22px;
    margin-top: 6px;
    color: var(--yq-text-body)
}

.DocActionMore-module_switchTitle_oFkGc {
    flex: 1;
    height: 44px;
    line-height: 20px;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.DocActionMore-module_switchTitle_oFkGc .DocActionMore-module_switchDesc_pWX8R {
    height: 20px;
    margin-top: 4px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.DocActionMore-module_switchAction_Yfn-p {
    width: 28px
}

.DocActionMore-module_infoContent_PBXyF>div {
    line-height: 22px;
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: break-spaces
}

.DocActionMore-module_menuItemContainer_sQ4rU {
    display: flex;
    align-items: center
}

.DocActionMore-module_menuDescribe_g8JML {
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 24px;
    padding-left: 24px
}

.DocActionMore-module_iconContainer_Jwqih {
    display: inline-flex;
    width: 16px;
    margin-right: 8px
}

.larkui-popover .DocActionMore-module_menu_\+zp\+S.ant-menu.ant-menu-root {
    box-shadow: none
}

.DocActionMore-module_iconBeta_hbg2z {
    color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 5px
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.EditorHeaderCollabUsers-module_avatar_humc6 {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    height: 24px;
    line-height: 24px;
    margin-right: 8px;
    cursor: pointer
}

.EditorHeaderCollabUsers-module_avatar_humc6 img {
    vertical-align: baseline;
    vertical-align: initial
}

.EditorHeaderCollabUsers-module_name_RmtES,.EditorHeaderCollabUsers-module_name_RmtES:hover {
    color: var(--yq-text-body)
}

.EditorHeaderCollabUsers-module_sign_f2lq4 {
    display: block;
    position: absolute;
    height: 10px;
    width: 10px;
    border-radius: 6px;
    background: var(--yq-bg-primary);
    bottom: -2px;
    right: -2px
}

.EditorHeaderCollabUsers-module_signInner_UJ3E3 {
    display: block;
    margin: 2px 0 0 2px;
    border-radius: 3px;
    width: 6px;
    height: 6px
}

.EditorHeaderCollabUsers-module_more_x6mXq {
    height: 24px;
    width: 24px;
    border-radius: 12px;
    background: var(--yq-yuque-grey-5);
    line-height: 24px;
    font-size: 10px;
    text-align: center;
    color: var(--yq-text-body)
}

.EditorHeaderCollabUsers-module_more_x6mXq:hover {
    background: var(--yq-bg-primary-hover-light);
    color: var(--yq-text-body)
}

.EditorHeaderCollabUsers-module_more_x6mXq:active {
    background: var(--yq-yuque-grey-6);
    color: var(--yq-text-body)
}

.EditorHeaderCollabUsers-module_moreUsers_2uEpx li {
    padding: 4px 0
}

.lark-editor-header {
    position: relative;
    height: 60px;
    padding-right: 24px;
    background: var(--yq-bg-primary);
    z-index: 999
}

.lark-editor-header[type=Doc] {
    border-bottom: 1px solid var(--yq-border-light)
}

.lark-editor-header .lark-editor-header-content {
    width: 100%;
    position: relative;
    height: 60px;
    display: flex;
    flex: 1 1 auto
}

.lark-editor-header .lark-editor-header-back {
    display: flex;
    width: 60px;
    border-right: 1px solid var(--yq-border-light);
    align-items: center;
    justify-content: center
}

.lark-editor-header .lark-editor-header-action {
    position: absolute;
    top: 14px;
    right: 0;
    z-index: 301;
    width: auto;
    display: flex
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-users {
    padding: 4px 0
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-wrapper {
    display: flex;
    justify-content: center;
    align-items: center
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-wrapper .lark-editor-collab-icon {
    width: 20px;
    height: 20px;
    position: relative;
    cursor: pointer;
    color: var(--yq-black)
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-action-item {
    padding-left: 8px
}

.lark-editor-header .lark-editor-header-action .book-custom-index-publish-button {
    padding-right: 12px
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-more {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    margin-left: 20px
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-more-trigger {
    padding: 0 4px;
    line-height: 1
}

.lark-editor-header .editor-publish-button .ant-btn-primary[disabled] {
    color: var(--yq-text-caption)
}

.lark-editor-header-info {
    padding: 8px 0;
    margin-left: 16px;
    width: 40%
}

.lark-editor-header-title {
    display: flex;
    align-items: center;
    align-self: center;
    height: 24px;
    line-height: 24px
}

.lark-editor-header-title .name-title {
    cursor: pointer;
    font-weight: 700;
    font-size: 16px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.lark-editor-header-title .larkicon-lock {
    color: var(--yq-text-caption)
}

.lark-editor-header-title .doc-access-scope {
    line-height: 24px
}

.lark-editor-header-title .lark-editor-header-title-input {
    padding: 2px 3px;
    margin-left: -4px;
    font-weight: 700;
    font-size: 16px;
    min-width: 200px;
    max-width: 300px
}

.lark-editor-header-crumb-status {
    display: flex;
    height: 20px
}

.lark-editor-header-crumb-status .lark-breadcrumb .lark-breadcrumb-current {
    font-weight: 400
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb {
    height: 20px;
    line-height: 20px;
    max-width: 300px
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb>span:last-child a {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link:hover,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb:hover,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb>span:last-child a:hover {
    color: var(--yq-text-body)
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-separator,.lark-editor-header-crumb-status .split {
    color: var(--yq-yuque-grey-5);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.lark-editor-header-crumb-status .split {
    position: relative;
    top: -1px;
    line-height: 20px;
    font-size: 12px;
    margin: 0 8px
}

.lark-editor-header-history {
    display: flex;
    align-items: center;
    padding: 4px 0 4px 12px
}

.lark-editor-save-tip {
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    min-width: 85px
}

.lark-editor-save-tip,.lark-editor-save-tip a:hover {
    color: var(--yq-text-caption)
}

.lark-editor-save-tip>.icon-svg {
    margin-left: 4px
}

.lark-editor-net-status {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-disable)
}

.lark-editor-net-status .larkui-icon-delete-solid {
    margin-right: 5px;
    color: var(--yq-function-error)
}

.book-custom-index-save-tip {
    padding: 7px 0 7px 21px;
    line-height: 1;
    font-size: 14px;
    color: var(--yq-text-caption);
    display: flex;
    align-items: center
}

.book-custom-index-save-tip .icon-svg {
    margin-right: 8px
}

.book-custom-index-header-title {
    padding-left: 18px;
    display: flex;
    align-items: center
}

.book-custom-index-header-title a {
    color: var(--yq-text-body);
    display: inline-flex;
    align-items: center
}

.book-custom-index-header-title a:hover {
    color: var(--yq-text-disable)
}

.book-custom-index-header-title span {
    padding-left: 6px
}

.book-custom-index-header-title .book-custom-index-file-title {
    border-left: 1px solid var(--yq-border-primary);
    margin-left: 12px;
    padding-left: 12px;
    font-weight: 500
}

.lark-editor-header .doc-title-edit-view {
    float: left
}

.lark-editor-conflict-tip {
    position: absolute;
    top: 0;
    width: 100%
}

.lark-editor-conflict-tip .ant-alert {
    margin: 0 auto;
    width: 400px
}

.lark-editor-header .lark-editor-user {
    position: relative;
    display: inline-block
}

.template-editor-header-doc {
    border-bottom: 1px solid var(--yq-border-primary)
}

.template-editor-header .lark-editor-header-action .lark-editor-header-more {
    padding: 7px 0 7px 21px
}

.template-header-delete-confirmation {
    display: flex
}

.template-header-delete-confirmation span {
    margin: 4px 8px 0 0
}

.templateTag .ant-tag {
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.template-header-use-template {
    margin-right: 8px
}

.template-header-title {
    display: flex;
    align-items: center;
    margin-left: 24px
}

.template-header-title .name-split {
    margin: 0 4px
}

.template-header-title .book-name-scope {
    position: relative;
    top: 1px
}

.template-header-title .book-name-scope .icon-svg {
    margin-left: 2px
}

.template-header-title .lark-breadcrumb .icon-svg {
    display: inline
}

.template-header-title a {
    color: var(--yq-text-body)
}

.template-header-title a:hover {
    color: var(--yq-text-caption)
}

.template-header-title input {
    width: auto
}

.template-header-title .header-crumb {
    padding-right: 8px
}

.index-module_wordCount_Wzj9E {
    position: fixed;
    bottom: 0;
    font-size: 12px;
    padding: 2px 4px;
    opacity: .6;
    background-color: var(--yq-bg-primary);
    color: var(--yq-text-body)
}

.index-module_wordCount_Wzj9E:hover {
    opacity: 1
}

.CommonEditPage-module_creationGuide_AaTbI {
    position: fixed;
    top: 317px;
    z-index: 1;
    width: 100%
}

.CommonEditPage-module_creationGuide_AaTbI .CommonEditPage-module_guideWrapper_\+9pCK {
    position: absolute;
    top: 0;
    width: 100%
}

.CommonEditPage-module_creationGuide_AaTbI:not(.CommonEditPage-module_adaptMode_Tq\+Y\+) .CommonEditPage-module_guideWrapper_\+9pCK {
    left: 50%;
    transform: translateX(-50%)
}

.CommonEditPage-module_creationGuide_AaTbI.CommonEditPage-module_adaptMode_Tq\+Y\+ .CommonEditPage-module_guideWrapper_\+9pCK {
    left: 40px;
    transform: translateX(0)
}

.CommonEditPage-module_creationGuide_AaTbI.CommonEditPage-module_editMode_pYkEm {
    position: absolute;
    top: 257px
}

.CommonEditPage-module_creationGuide_AaTbI.CommonEditPage-module_editMode_pYkEm:not(.CommonEditPage-module_adaptMode_Tq\+Y\+) .CommonEditPage-module_guideWrapper_\+9pCK {
    width: 100%;
    position: relative;
    margin: 0 auto;
    margin-right: var(--center-editor-margin-right)!important;
    left: auto;
    transform: none
}

.CommonEditPage-module_commonEditPage_Cwjj2 {
    top: 0;
    min-height: calc(100vh - 52px)
}
