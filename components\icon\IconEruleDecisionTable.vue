<!-- 决策表图标组件 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 16,
    class: ''
})
</script>

<template>
    <svg :width="props.size" :height="props.size" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'icon-svg',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path d="M928 160h-832A32 32 0 0 0 64 192v640a32 32 0 0 0 32 32h832a32 32 0 0 0 32-32V192a32 32 0 0 0-32-32z m-40 432h-212.032v-160h212.032v160z m-476.032-160h200v160H412.032v-160z m-275.968 0h212.032v160H135.936v-160z m0-200h752V368H136V232z m0 424h212.032v136H135.936V656z m276.032 0h200v136H411.968V656z m475.968 136h-212.032V656h212.032v136z" fill="#000000"></path>
    </svg>
</template> 