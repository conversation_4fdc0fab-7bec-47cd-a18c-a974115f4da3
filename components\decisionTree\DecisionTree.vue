<template>
  <div class="bpmn decision-tree">
    <a-tabs :activeKey="viewStatus" size="small" @change="changeViewStatus">
      <a-tab-pane tab="规则内容" key="designView">
        <!-- 画布 -->
        <div class="canvas" ref="canvas"></div>
      </a-tab-pane>
      <a-tab-pane tab="DRL" key="codeView">
        <rule-tree
          v-if="drlRefreshFlag"
          ref="codeNodeRule"
          :ruleInfo="ruleInfo"
          :rule-drl="ruleDrl"
          :key="drlRefreshKey"
          view-status="codeView"
          active-show="code"
          class="tree-rule"
        />
      </a-tab-pane>
      <template #rightExtra>
        <Tool
          slot="tabBarExtraContent"
          @download-process-as-xml="downloadProcessAsXml"
          @download-process-as-svg="downloadProcessAsSvg"
          @download-process-as-bpmn="downloadProcessAsBpmn"
          @to-save-flow="toSaveFlow"
          @elements-align="elementsAlign"
          @process-zoom-out="processZoomOut"
          @process-zoom-in="processZoomIn"
          @process-re-zoom="processReZoom"
          @process-undo="processUndo"
          @process-redo="processRedo"
          @process-restart="processRestart"
          @import-local-file="importLocalFile"
          @check-tree="checkTree"
          @fullScreen="fullScreen"
          @handleSubmitRule="handleSubmitRule"
          :default-zoom="defaultZoom"
          :recoverable="recoverable"
          :revocable="revocable"
          :type="type"
          :showFullScreen="!isFullscreenMode"
        />
      </template>
    </a-tabs>

    <!-- 条件对话框 -->
    <a-drawer
      title="条件节点设置"
      :width="drawerWidth"
      placement="right"
      :open="conditionDrawer"
      wrap-class-name="ant-drawer-dir"
      :closable="true"
      :push="false"
      @close="() => (conditionDrawer = false)"
    >
      <a-form>
        <a-form-item label="节点名称">
          <a-input autocomplete="off" v-model:value="form.name" />
        </a-form-item>
        <a-form-item label="节点描述">
          <a-input autocomplete="off" v-model:value="form.desc" />
        </a-form-item>
        <a-form-item label="编辑条件">
          <rule-tree
            ref="conditionsNodeRule"
            :ruleInfo="ruleInfo"
            @set-conditions="setConditions"
            :conditions="conditions"
            :predefines="predefines"
            :active-shape-id="activeShape ? activeShape.id : ''"
            view-status="designView"
            active-show="condition"
            class="tree-rule"
          />
        </a-form-item>
        <div style="text-align: right;">
          <a-button type="primary" @click="routerDialogSave">保存</a-button>
        </div>
      </a-form>
    </a-drawer>

    <!-- 连接线对话框 -->
    <a-drawer
      title="条件路由属性设置"
      :width="drawerWidth"
      placement="right"
      :open="flowArrDrawer"
      wrap-class-name="ant-drawer-dir"
      :closable="true"
      :push="false"
      @close="() => (flowArrDrawer = false)"
    >
      <a-form>
        <a-form-item label="路由描述">
          <a-input autocomplete="off" v-model:value="routerDesc" />
        </a-form-item>
        <a-form-item label="编辑条件">
          <rule-tree
            ref="flowNodeRule"
            :ruleInfo="ruleInfo"
            @set-conditions="setConditions"
            :conditions="conditions"
            :predefines="predefines"
            :active-shape-id="activeShape ? activeShape.id : ''"
            view-status="designView"
            active-show="flow"
            class="tree-rule"
          />
        </a-form-item>
        <div style="text-align: right;">
          <a-button type="primary" @click="flowAttrDialogSave">保存</a-button>
        </div>
      </a-form>
    </a-drawer>

    <!-- 动作对话框 -->
    <a-drawer
      title="动作节点设置"
      :width="drawerWidth"
      placement="right"
      :open="activeDrawer"
      wrap-class-name="ant-drawer-dir"
      :closable="true"
      :push="false"
      @close="() => (activeDrawer = false)"
    >
      <a-form>
        <a-form-item label="节点名称">
          <a-input autocomplete="off" v-model:value="form.name" />
        </a-form-item>
        <a-form-item label="节点描述">
          <a-input autocomplete="off" v-model:value="form.desc" />
        </a-form-item>
        <a-form-item label="编辑动作">
          <rule-tree
            ref="activeNodeRule"
            :ruleInfo="ruleInfo"
            @set-conditions="setConditions"
            :conditions="conditions"
            :predefines="predefines"
            :active-shape-id="activeShape ? activeShape.id : ''"
            view-status="designView"
            active-show="action"
            class="tree-rule"
          />
        </a-form-item>
        <div style="text-align: right;">
          <a-button type="primary" @click="activeDialogSave">保存</a-button>
        </div>
      </a-form>
    </a-drawer>
    <a-modal v-model:open="confirmOpen" title="提示" @ok="confirmOk">
      <p style="color: rgb(250, 173, 20)">
        <ExclamationCircleOutlined /> 决策树绘制错误, 是否继续保存?
      </p>
    </a-modal>
    <!-- 使用FullModel组件实现全屏展示 -->
    <FullModel
        :isModalVisible="isModalVisible"
        :isFullscreen="isFullscreen"
        :titleText="titleText"
        :handleCancel="handleModalCancel"
        :onFullscreenToggle="onFullscreenToggle"
        :showFullBtn="false"
        :showFooter="false"
    >
      <template #default>
        <div class="fulltab" :style="{maxHeight: isFullscreen ? '80vh' : '60vh'}">
          <DecisionTree
                  :ruleInfo="ruleInfo"
                  :predefineList="predefineList"
                  :p_locked="false"
                  :validateResult="validateResult"
                  :ruleData="ruleData"
                  :initModelData="initModelData"
                  :uuid="uuid"
                  :isFullscreenMode="true"
                  @commandStack-changed="$emit('commandStack-changed', $event)"
                  @input="$emit('input', $event)"
                  @change="$emit('change', $event)"
                  @canvas-viewbox-changed="$emit('canvas-viewbox-changed', $event)"
                  @resetData="resetData"
          />
        </div>
      </template>
    </FullModel>
  </div>
</template>

<script setup>
import {
  append as svgAppend,
  attr as svgAttr,
  create as svgCreate,
} from "tiny-svg";
import { query as domQuery } from "min-dom";
import customRules from "@/components/bpmn/rules";
import CustomModeler from "@/components/bpmn/customModelerTree";
import defaultEmptyXML from "@/components/bpmn/xmlData";
import customTranslate from "@/components/bpmn/customTranslate/customTranslate";
import checkTree from "@/components/bpmn/utils/checkTree";
import { ruleDetail, ruleSave } from "@/api/rule_editor";
import StringUtils from "@/components/ruleEditCom/utils/stringUtils";
import globalEventEmitter from '~/utils/eventBus';
import { REFRESH_RULE_LIST, RULE_SUBMIT_ACTION } from '@/consts/globalEventConsts';
import { getRuleDrl } from "@/api/rule_editor";
import FullModel from "@/components/FullModel.vue";
import qs from "qs";
import {getRuleByUuid, submitRule} from "@/api/rule_base";
const message = inject("message");
const emit = defineEmits([
  "commandStack-changed",
  "input",
  "change",
  "canvas-viewbox-changed",
  "resetData"
]);

// 注册组件
const Tool = defineAsyncComponent(() => import("./Tool"));
const RuleTree = defineAsyncComponent(() => import("./RuleTree/TreeRule/index.vue"));

// 定义props
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
  predefineList: {
    type: Array,
    default: () => [],
  },
  p_locked: {
    type: Boolean,
    default: true,
  },
  ruleData: {
    type: Object,
    default: () => ({}),
  },
  validateResult: {
    type: Array,
    default: () => [],
  },
  uuid: String,
  initModelData: {
    type: Object,
    default: () => ({}),
  },
  isFullscreenMode: {
    type: Boolean,
    default: false
  }
});

// 定义data
const type = ref("4");
const activeTab = ref("rule");
const bpmnModeler = ref(null);
const container = ref(null);
const canvas = ref(null);
let activeShape = ref(null);
const activeE = ref(null);
const processId = ref("");
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isRuleContaiter = ref("");
const ruleUuid = ref("");
const form = reactive({
  id: "",
  name: "",
  desc: "",
  type: "",
  gatewayDirection: "",
});
const routerSet = ref(false);
const isEdit = ref(false);
const conditionName = ref("");
const conditionDesc = ref("");
const routerDesc = ref("");
const defaultZoom = ref(1);

const confirmOpen = ref(false);
const nodeRuleTree = ref([]);
const treeTransferObj = ref([]);
const conditionDrawer = ref(false);
const errorDrawer = ref(false);
const previewResult = ref("");
const flowErrorMsg = ref([]);
const drawerWidth = ref("60%");
const conditions = ref([]);
const ruleDrl = ref("");

const ruleError = ref([]);
const transferValue = ref("");
const transKeys = ref([]);
const treeAction = ref("");
const nodeRuleTreeOld = ref([]);
const toDataChange = ref([]);
const treeTransferObjOld = ref([]);
const defaultProps = ref({
  label: (data, node) => {
    return data;
  },
});
const valiRes = ref([]);
const allParentIds = ref([]);
const selectIdAndParentId = ref({});
const addKeyIsParentId = ref([]);
const parentDisabled = ref({});
const gatewayChangedType = ref(null);
const conditionRadioShow = ref(true);
const listenerElement = ref(null);
const activeShapeType = ref("");
const gatewayTypeAlert = ref(false);
const flowArrDrawer = ref(false);
const activeDrawer = ref(false);
const predefines = ref([]);

const conditionsNodeRule = ref(null);
const flowNodeRule = ref(null);
const activeNodeRule = ref(null);
const codeNodeRule = ref(null);
const recoverable = ref(false);
const revocable = ref(false);
const viewStatus = ref("designView");
const errContent = ref("");
const drlRefreshFlag = ref(true);
const drlRefreshKey = ref(0);

const isModalVisible = ref(false);
const isFullscreen = ref(true);
const titleText = ref('');
const showFullScreen = ref(true);

// 组件的生命周期钩子
onMounted(() => {
  init();
});

// 定义watch
watch(
  () => props.validateResult,
  (newVal) => {
    if (newVal.length > 0) {
      valiRes.value = newVal;
    }
  },
  { deep: true }
);

watch(
  () => props.ruleData,
  (newVal) => {
    defaultRuleContent.value = newVal;
  },
  { deep: true }
);

watch(
  () => props.predefineList,
  (newVal) => {
    self_predefineList.value = newVal;
  },
  { deep: true }
);

// 定义computed
const self_valida = computed(() => props.validateResult);

// 定义methods
const changeViewStatus = (val) => {
  if (val === "codeView") {
    viewStatus.value = "codeView";
    // 每次切换到DRL视图时，强制重新获取最新的DRL数据
    drlRefreshFlag.value = false;  // 先销毁组件
    getRuleDrl(ruleUuid.value).then((res) => {
      if (res && res.data) {
        ruleDrl.value = res.data.ruleDrl;
        // 然后生成新key并重建组件
        drlRefreshKey.value = Date.now();
        nextTick(() => {
          drlRefreshFlag.value = true;
        });
      }
    });
  } else {
    viewStatus.value = "designView";
  }
  if (val !== "predefineView") {
    // 触发生成规则
    if (codeNodeRule.value) {
      let objEditor = null;
      let obj =
        codeNodeRule.value.$refs.ruleCreate ||
        codeNodeRule.value.$refs.ruleUpdate;
      obj && (objEditor = obj.$refs.ruleEditor);
      objEditor && objEditor.toSave();
    }
  }
};

const init = () => {
  // 获取到属性ref为"canvas"的dom节点
  const canvasElement = canvas.value;
  // 建模
  bpmnModeler.value = new CustomModeler({
    container: canvasElement,
    // 开启键盘快捷
    keyboard: {
      bindTo: document,
    },
    // 扩展
    additionalModules: [
      {
        // 禁止双击节点出现label编辑框
        labelEditingProvider: ["value", ""],
      },
      {
        translate: ["value", customTranslate],
      },
      // 自定义规则
      customRules,
    ],
  });
  ruleUuid.value = props.ruleInfo.uuid;

  // 初始化
  getData(props.ruleInfo.uuid,props.ruleInfo.type);
};

const createNewDiagram = async (xml) => {
  try {
    // 加载或处理process
    let xmlStr = "";
    if (xml) {
      let resObj = [];
      let _xmlStr = xml;
      const processReg = /<process.*?>/g;
      const bpmndiReg = /<bpmndi:BPMNPlane.*?>/g;
      const processObj = _xmlStr.match(processReg);
      const bpmndiObj = _xmlStr.match(bpmndiReg);
      const sequenceFlowReg = /<sequenceFlow.*?>/g;
      const sequenceFloObj = _xmlStr.match(sequenceFlowReg);

      if (processObj && processObj.length > 0) {
        processObj.map((item, i) => {
          let processA = item.split(/id=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${processA[0]} id="Process.${props.ruleInfo.uuid}"${processA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }

      if (bpmndiObj && bpmndiObj.length > 0) {
        bpmndiObj.map((item, i) => {
          let bpmndiA = item.split(/bpmnElement=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${bpmndiA[0]} bpmnElement="Process.${props.ruleInfo.uuid}"${bpmndiA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }

      // 处理业务描述
      if (sequenceFloObj && sequenceFloObj.length > 0) {
        sequenceFloObj.find((resItem) => {
          if (resItem.indexOf('name="to') !== -1) {
            resObj.push(resItem);
          }
        });
        resObj.map((item, i) => {
          let sequenceFloA = item.split(/name=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${sequenceFloA[0]} name="业务描述"${sequenceFloA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }
    } else {
      xmlStr = defaultEmptyXML(
        props.ruleInfo.uuid,
        props.ruleInfo.ruleName,
        props.ruleInfo.packageNameAll,
        4
      );
    }
    processId.value = `Process.${props.ruleInfo.uuid}`;
    // 将字符串转换成图显示出来
    const result = await bpmnModeler.value.importXML(xmlStr);
    const { warnings } = result;
    success();
  } catch (err) {}
};

const getData = (uuid,type) => {
  // 规则详情
  ruleDetail(uuid,type).then((result) => {
    const detail = result.data;

    if (detail) {
      let ruleCon = detail.dtTreeXml || "";
      conditions.value = detail.ruleContent?.nodeRuleContents || [];
      ruleDrl.value = detail.ruleDrl ?? "";

      // 初始化 流程图
      createNewDiagram(ruleCon);
    }
  });

  createNewDiagram();
};

const removeSequenceFlow = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const elementRegistry = bpmnModeler.value.get("elementRegistry");
  const sequenceFlowList = elementRegistry.filter(
    (item) => item.type === "bpmn:SequenceFlow"
  );
  for (let i = sequenceFlowList.length - 1; i >= 0; i--) {
    listenerElement.value.map((item) => {
      if (sequenceFlowList[i].id === item.id) {
        modeling.removeConnection(sequenceFlowList[i]);
      }
    });
  }
};

const success = () => {
  // 调整与正中间
  bpmnModeler.value.get("canvas").zoom("fit-viewport", "auto");
  // 绑定事件
  addModelerListener();
  addEventBusListener();
  // 初始化箭头
  initArrow("sequenceflow-arrow-normal");
  initArrow("sequenceflow-arrow-active");
};

const dialogClose = () => {
  dialogVisible.value = false;
};

const routerClose = () => {
  routerSet.value = false;
};

const initArrow = (id) => {
  const marker = svgCreate("marker");

  svgAttr(marker, {
    id,
    viewBox: "0 0 20 20",
    refX: "11",
    refY: "10",
    markerWidth: "10",
    markerHeight: "10",
    orient: "auto",
  });

  const path = svgCreate("path");

  svgAttr(path, {
    d: "M 1 5 L 11 10 L 1 15 Z",
    style:
      " stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; ",
  });

  const defs = domQuery("defs");
  svgAppend(marker, path);
  svgAppend(defs, marker);
};

const gatewayTypeChange = (type) => {
  if (type === "bpmn:ParallelGateway") {
    conditionRadioShow.value = false;
  } else {
    conditionRadioShow.value = true;
  }
  // 模型对象
  const elementFactory = bpmnModeler.value.get("elementFactory"),
    elementRegistry = bpmnModeler.value.get("elementRegistry"),
    modeling = bpmnModeler.value.get("modeling");

  // 获取当前网关信息
  const shape = activeShape;
  const shapeX = shape.x;
  const shapeY = shape.y;
  const shapeIncom = shape.incoming;
  const shapeOutgo = shape.outgoing;
  // 创建新的网关
  const process = elementRegistry.get(processId.value);
  const gateway = elementFactory.createShape({
    type,
  });

  // 有incoming链接源
  if (shapeIncom.length > 0) {
    modeling.createShape(gateway, { x: shapeX + 25, y: shapeY + 25 }, process);
    shapeIncom.forEach((item, i) => {
      // 获取网关incom链接线对象的源
      const incomObj = item;
      const incomObjSource = incomObj.source;
      modeling.connect(incomObjSource, gateway);
    });

    // 有outgo链接线对象
    if (shapeOutgo.length > 0) {
      shapeOutgo.forEach((item, i) => {
        // 获取网关outgo链接线对象
        const outgoObj = item;
        const outgoObjTarget = outgoObj.target;
        modeling.connect(gateway, outgoObjTarget);
        // 一条outgoing时需要手动删除
        if (shapeOutgo.length === 1) {
          modeling.removeConnection(outgoObj);
        }
      });
    }
  } else {
    // 没有incoming链接源直接创建
    modeling.createShape(gateway, { x: shapeX + 25, y: shapeY + 25 }, process);
  }
  activeShape = gateway;
  _updateProperties();
  modeling.removeShape(shape);
  routerDesc.value = "";
};

const setGatewayType = (type) => {
  gatewayChangedType.value = type;
  if (
    gatewayChangedType.value &&
    gatewayChangedType.value !== activeShapeType.value
  ) {
    gatewayTypeAlert.value = true;
  } else {
    gatewayTypeAlert.value = false;
  }
};

// 更新对象属性
const _updateProperties = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: form.name,
    desc: form.desc,
    gatewayDirection: form.gatewayDirection,
  };
  // g:ruleFlowGroup
  if (dialogTitle.value === "规则节点") {
    propertiesJson["g:ruleFlowGroup"] = `${ruleUuid.value}_${activeShape.id}`;
  }
  modeling.updateProperties(activeShape, propertiesJson);
};
// 条件
const routerDialogSave = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: form.name,
    desc: form.desc,
  };

  let objEditor = null;
  let obj =
    (conditionsNodeRule && conditionsNodeRule.value.$refs.ruleCreate) ||
    (conditionsNodeRule && conditionsNodeRule.value.$refs.ruleUpdate);
  obj && (objEditor = obj.$refs.ruleEditor);
  objEditor && objEditor.toSave();

  modeling.updateProperties(activeShape, propertiesJson);
  conditionDrawer.value = false;
};
// 连接线
const flowAttrDialogSave = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: routerDesc.value,
  };

  // 添加条件
  const moddle = bpmnModeler.value._moddle;
  const conditionExpression = moddle.create("bpmn:FormalExpression", {
    language: "http://www.jboss.org/drools/rule",
  });
  propertiesJson["conditionExpression"] = conditionExpression;
  // 触发生成规则
  let objEditor = null;
  let obj =
    (flowNodeRule && flowNodeRule.value.$refs.ruleCreate) ||
    (flowNodeRule && flowNodeRule.value.$refs.ruleUpdate);
  obj && (objEditor = obj.$refs.ruleEditor);
  objEditor && objEditor.toSave();

  modeling.updateProperties(activeShape, propertiesJson);
  flowArrDrawer.value = false;
};
// 动作
const activeDialogSave = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: form.name,
    desc: form.desc,
  };
  // 触发生成规则
  let objEditor = null;
  let obj = activeNodeRule && activeNodeRule.value.$refs.ruleCreate || activeNodeRule && activeNodeRule.value.$refs.ruleUpdate;
  obj && (objEditor = obj.$refs.ruleEditor);
  objEditor && objEditor.toSave();

  modeling.updateProperties(activeShape, propertiesJson);
  activeDrawer.value = false;
};
const isInvalid = (param) => {
  // 判断是否是无效的值
  return param === null || param === undefined || param === "";
};
const isSequenceFlow = (type) => {
  // 判断是否是线
  return type === "bpmn:SequenceFlow";
};
const getShape = (id) => {
  let elementRegistry = bpmnModeler.value.get("elementRegistry");
  return elementRegistry.get(id);
};
const addModelerListener = () => {
  // 监听 modeler
  const bpmnjs = bpmnModeler.value;
  const EventBus = bpmnModeler.value.get("eventBus");
  const events = ["element.click", "shape.removed"];
  events.forEach((event) => {
    bpmnModeler.value.on(event, (e) => {
      let elementRegistry = bpmnjs.get("elementRegistry");
      let shape = e.element ? elementRegistry.get(e.element.id) : e.shape;

      if (event === "element.click") {
        if (shape && shape.type === "bpmn:BusinessRuleTask") {
          listenerElement.value = JSON.parse(JSON.stringify(shape.incoming));
        }
      }

      if (event === "shape.removed") {
        nextTick(() => {
          if (shape.type === "bpmn:BusinessRuleTask") {
            removeSequenceFlow();
          }
        });
      }
    });
  })
  // 监听图形改变返回xml
  EventBus.on("commandStack.changed", async (event) => {
    try {
      recoverable.value = bpmnModeler.value.get("commandStack").canRedo();
      revocable.value = bpmnModeler.value.get("commandStack").canUndo();
      let { xml } = await bpmnModeler.value.saveXML({ format: true });
      emit("commandStack-changed", event);
      emit("input", xml);
      emit("change", xml);
    } catch (e) {
      console.error(`[Process Designer Warn]: ${e.message || e}`);
    }
  });
  // 监听视图缩放变化
  bpmnModeler.value.on("canvas.viewbox.changed", ({ viewbox }) => {
    emit("canvas-viewbox-changed", { viewbox });
    const { scale } = viewbox;
    defaultZoom.value = Math.floor(scale * 100) / 100;
  });
};
// 绑定事件
const addEventBusListener = () => {
  // 监听 element
  const eventBus = bpmnModeler.value.get("eventBus");
  const eventTypes = [
    "element.dblclick",
    "element.changed",
    "connection.changed",
  ];
  eventTypes.forEach((eventType) => {
    eventBus.on(eventType, (e) => {
      if (!e || e.element.type == "bpmn:Process") return;
      if (eventType === "element.dblclick") {
        nextTick(() => {
          elementDBLclick(e);
        });
      }
      if (eventType === "connection.changed") {
        nextTick(() => {
          connectionUpdateProperties(e);
        });
      }
    });
  });
};
// 连线添加'业务描述''优先级''FormalExpression'
const connectionUpdateProperties = (e) => {
  if (!e || e.element.type == "bpmn:Process") return;
  const shape = e.element;
  if (
    shape.source &&
    shape.source &&
    shape.source.businessObject &&
    !shape.businessObject.name
  ) {
    const moddle = bpmnModeler.value._moddle;
    const modeling = bpmnModeler.value.get("modeling");
    const conditionExpression = moddle.create("bpmn:FormalExpression", {
      body: "",
    });

    modeling.updateProperties(shape, {
      name: "业务描述",
      conditionExpression,
    });
  }
  return;
};
// 查看所有可用事件
const getEventBusAll = () => {
  const eventBus = bpmnModeler.value.get("eventBus");
  const eventTypes = Object.keys(eventBus._listeners);
  return eventTypes;
};
// 双击节点
const elementDBLclick = (() => {
  let errorTimer = null;

  const showErr = (msg) => {
    if(errorTimer) return;
    message.error(msg);
    errorTimer = setTimeout(() => {
      errorTimer = null;
    }, 300);
  };

  return (e) => {
    const elementRegistry = bpmnModeler.value.get("elementRegistry");
    activeE.value = e;
    activeShape = e.element ? elementRegistry.get(e.element.id) : e.shape;
    const shapeType = activeShape && activeShape.type;
    activeShapeType.value = shapeType;

    // 条件网关节点
    if (shapeType === "bpmn:ExclusiveGateway") {
      conditionDrawer.value = true;
    }

    // 连线节点
    if (shapeType === "bpmn:SequenceFlow" &&
        activeShape.source.type === "bpmn:ExclusiveGateway") {
      routerDesc.value = activeShape.businessObject.name;
      const condition = conditions.value.find((x) => activeShape.id === x.nodeId);

      if (condition) {
        flowArrDrawer.value = true;
      } else {
        const sourceNodeCondition = conditions.value.find(
          (x) => x.nodeId === activeShape.source.id
        );
        if (sourceNodeCondition) {
          const sourceNodeConditionColoned = JSON.parse(JSON.stringify({
            ...sourceNodeCondition,
            nodeId: activeShape.id,
          }));
          conditions.value.push(sourceNodeConditionColoned);
          flowArrDrawer.value = true;
        } else {
          // 使用节流后的错误提示
          showErr("请先设置该条件路由的条件节点的条件表达式");
        }
      }
    }

    // 任务节点
    if (shapeType === "bpmn:Task") {
      activeDrawer.value = true;
    }

    // 更新表单数据
    form.id = activeShape.id;
    form.name = activeShape.businessObject.name;
    form.gatewayDirection = activeShape.businessObject.gatewayDirection;
    form.type = activeShape.type;
    form.desc = activeShape.businessObject.$attrs.desc;
  };
})();
// 校验
const check = () => {
  checkFlow(bpmnModeler, flowErrorMsg);
};
// 错误提示
const showError = () => {
  let ruleErrHtml = "";
  let flowErrHtml = "";
  let flowErr = "";
  let ruleErr = "";
  let errHtml = "";

  flowErrorMsg.value.map((item, i) => {
    flowErr += `<p style="padding:8px 0"><strong>${
      i + 1
    }. </strong>${item}</p>`;
  });
  ruleError.value.map((item, i) => {
    if (!item.nodeRule.valid) {
      let shape = getShape(item.nodeId);
      let shapeName = shape?.businessObject?.name || item.nodeId;
      let ruleErrIndex = `<p style="padding:8px 0"><strong>路由边：${shapeName}</strong></p>`;
      ruleErr += ruleErrIndex;
      item.nodeRule.ruleValidates.map((err, errI) => {
        if (!err.validate) {
          let errA = err.msg.split("\n");
          errA.map((er, erI) => {
            er.trim() &&
              (ruleErr += `<p style="padding:8px 0"><strong>${
                erI + 1
              }. </strong>${er.trim()}</p>`);
          });
        }
      });
    }
  });
  flowErrHtml = `<div><strong style="font-size:16px;margin:20px 0 10px;display:block;color:#F56C6C">绘制错误</strong>${flowErr}</div>`;
  ruleErrHtml = `<div><strong style="font-size:16px;margin:20px 0 10px;display:block;color:#F56C6C">规则错误</strong>${ruleErr}</div>`;
  errHtml =
    (flowErr ? flowErrHtml : flowErr) + (ruleErr ? ruleErrHtml : ruleErr);
  errContent.value = errHtml;
  errorDrawer.value = true;

  notification.error({
    message: '错误',
    description: h('div', { innerHTML: errHtml }),
    duration: 0,
    placement: 'center',
    class: 'diyErr',
  });
};

/**
 * 校验流程
 */
const checkFlow = () => {
  const elementRegistry = bpmnModeler.value.get("elementRegistry");

  // 获取所有元素
  const allElements = elementRegistry.getAll();

  // 开始条件节点
  const startElements = allElements.filter(
    (item) =>
      item.type === "bpmn:ExclusiveGateway" && item.incoming.length === 0
  );
  if (startElements.length !== 1) {
    message.error("决策树中只能存在一个开始条件节点!");
    return false;
  }

  // 出边为0的条件节点
  const conditionElementWithNoOutgoing = allElements.find(
    (item) =>
      item.type === "bpmn:ExclusiveGateway" && item.outgoing.length === 0
  );
  if (conditionElementWithNoOutgoing) {
    message.error(
      `条件节点【${conditionElementWithNoOutgoing.businessObject.name}】至少要有一条接出路由边!`
    );
    return false;
  }

  // 入边大于一的动作节点
  const actionElementWithMoreThanOneIncoming = allElements.find(
    (item) => item.type === "bpmn:Task" && item.incoming.length > 1
  );
  if (actionElementWithMoreThanOneIncoming) {
    message.error(
      `动作节点【${actionElementWithMoreThanOneIncoming.businessObject.name}】只能有一条接入路由边!`
    );
    return false;
  }

  // 入边为0的动作节点
  const actionElementWithNoIncoming = allElements.find(
    (item) => item.type === "bpmn:Task" && item.incoming.length === 0
  );
  if (actionElementWithNoIncoming) {
    message.error(
      `动作节点【${actionElementWithNoIncoming.businessObject.name}】至少要有一条接入路由边!`
    );
    return false;
  }

  // 出边不为0的动作节点
  const actionElementWithOutgoing = allElements.find(
    (item) => item.type === "bpmn:Task" && item.outgoing.length > 0
  );
  if (actionElementWithOutgoing) {
    message.error(
      `动作节点【${actionElementWithOutgoing.businessObject.name}】不能有接出路由边!`
    );
    return false;
  }

  // 所有连线
  const allEdges = allElements.filter(
    (item) => item.type === "bpmn:SequenceFlow"
  );
  for (const item of allEdges) {
    const hasRule = conditions.value.find((item2) => item2.nodeId === item.id);
    if (!hasRule) {
      message.error(`路由边【${item.businessObject.name}】没有设置条件!`);
      return false;
    }
  }

  // 所有动作节点
  const actionElements = allElements.filter(
    (item) => item.type === "bpmn:Task"
  );
  for (const item of actionElements) {
    const hasRule = conditions.value.find((item2) => item2.nodeId === item.id);
    if (!hasRule) {
      message.error(`动作节点【${item.businessObject.name}】没有设置动作!`);
      return false;
    }
  }

  return true;
};

const saveFlow = () => {
  const checkResult = checkFlow();
  if (!checkResult) return;

  let obj = {};
  obj.ruleContent = {};
  bpmnModeler.value.saveXML({ format: true }).then(({ xml }) => {
    previewResult.value = StringUtils.formatXml(xml);
    obj.ruleUuid = ruleUuid.value;
    obj.demandUuid = "";
    obj.dtTreeXml = previewResult.value;
    obj.ruleContent.predefines = predefines.value;
    obj.ruleContent.nodeRuleContents = conditions.value;

    ruleSave(obj).then((res) => {
      if (res?.code === 20000) {
        ruleDrl.value = res.data?.ruleDrl ?? "";
        emit('resetData')

        const data = res?.data;

        if (data?.valid === true) {
          message.success("保存成功");
          // 如果当前在DRL视图，强制刷新
          if (viewStatus.value === "codeView") {
            drlRefreshFlag.value = false;
            drlRefreshKey.value = Date.now();
            nextTick(() => {
              drlRefreshFlag.value = true;
            });
          }
        } else {
          ruleError.value = data?.treeNodeValidData;
          showError();
          message.warning("保存成功，但是规则编写有误，未能生成DRL");
        }
        //更新上级列表状态
        globalEventEmitter.emit(REFRESH_RULE_LIST);
      } else {
        message.error(res.mag);
      }
    });
  });
};
// 生成conditions
const setConditions = (param) => {
  if (param.viewStatus === "predefineView") {
    predefines.value = param.ruleContent.predefines;
    if (conditions && conditions.value.length > 0) {
      conditions.value.map((item, i) => {
        conditions.value[i].nodeRule.predefines = param.ruleContent.predefines;
      });
    }
  }
  if (param.viewStatus === "designView") {
    let nodeId = activeShape.id;
    if (conditions.value && conditions.value.length > 0) {
      let activeIndex = -1;
      conditions.value.map((item, i) => {
        if (nodeId === item.nodeId) {
          activeIndex = i;
        }
      });
      if (activeIndex != "-1") {
        conditions.value[activeIndex].nodeRule = param.ruleContent;
      } else {
        conditions.value.push({
          nodeId,
          nodeRule: param.ruleContent,
        });
      }
    } else {
      conditions.value.push({
        nodeId,
        nodeRule: param.ruleContent,
      });
    }
  }
};
const treeDrawerClosed = () => {
  toData.value = [];
  toSubFlowData.value = [];
  gatewayChangedType.value = null;
  gatewayTypeAlert.value = false;
  dialogVisible.value = false;
};
const downloadProcessAsXml = () => {
  downloadProcess("xml");
};
const downloadProcessAsBpmn = () => {
  downloadProcess("bpmn");
};
const downloadProcessAsSvg = () => {
  downloadProcess("svg");
};
// 下载流程图到本地
const downloadProcess = async (type, name) => {
  try {
    // 按需要类型创建文件并下载
    if (type === "xml" || type === "bpmn") {
      const { err, xml } = await bpmnModeler.value.saveXML();
      // 读取异常时抛出异常
      if (err) {
        console.error(`[Process Designer Warn ]: ${err.message || err}`);
      }
      let { href, filename } = setEncoded(type.toUpperCase(), name, xml);
      downloadFunc(href, filename);
    } else {
      const { err, svg } = await bpmnModeler.value.saveSVG();
      // 读取异常时抛出异常
      if (err) {
        return console.error(err);
      }
      let { href, filename } = setEncoded("SVG", name, svg);
      downloadFunc(href, filename);
    }
  } catch (e) {
    console.error(`[Process Designer Warn ]: ${e.message || e}`);
  }
  // 文件下载方法
  function downloadFunc(href, filename) {
    if (href && filename) {
      let a = document.createElement("a");
      a.download = filename; //指定下载的文件名
      a.href = href; //  URL对象
      a.click(); // 模拟点击
      URL.revokeObjectURL(a.href); // 释放URL 对象
    }
  }
};
const confirmOk = () => {
  saveFlow();
  confirmOpen.value = false;
};
// 保存
const toSaveFlow = () => {
  if (type.value === "4") {
    saveFlow();
  } else {
    checkFlow();
    if (flowErrorMsg.value.length !== 0) {
      showError();
      confirmOpen.value = true;
    } else {
      saveFlow();
    }
  }
};
const elementsAlign = (align) => {
  const Align = bpmnModeler.value.get("alignElements");
  const Selection = bpmnModeler.value.get("selection");
  const SelectedElements = Selection.get();
  if (!SelectedElements || SelectedElements.length <= 1) {
    message.warning("请框选 或 按住Ctrl键选择多个元素对齐");
    return;
  }
  Align.trigger(SelectedElements, align);
};
const processZoomIn = (zoomStep = 0.1) => {
  let newZoom = Math.floor(defaultZoom.value * 100 + zoomStep * 100) / 100;
  if (newZoom > 4) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be greater than 4"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(defaultZoom.value);
};
const processZoomOut = (zoomStep = 0.1) => {
  let newZoom = Math.floor(defaultZoom.value * 100 - zoomStep * 100) / 100;
  if (newZoom < 0.2) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be less than 0.2"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(defaultZoom.value);
};
const processZoomTo = (newZoom = 1) => {
  if (newZoom < 0.2) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be less than 0.2"
    );
  }
  if (newZoom > 4) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be greater than 4"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(newZoom);
};
const processReZoom = () => {
  defaultZoom.value = 1;
  bpmnModeler.value.get("canvas").zoom("fit-viewport", "auto");
};
const processRedo = () => {
  bpmnModeler.value.get("commandStack").redo();
};
const processUndo = () => {
  bpmnModeler.value.get("commandStack").undo();
};
const processRestart = () => {
  recoverable.value = false;
  revocable.value = false;
  createNewDiagram(null).then(() =>
    bpmnModeler.value.get("canvas").zoom(1, "auto")
  );
};
// 加载本地文件
const importLocalFile = () => {
  const file = refFile && refFile.value.files[0];
  const reader = new FileReader();
  reader.readAsText(file);
  reader.onload = () => {
    let xmlStr = result.value;
    createNewDiagram(xmlStr);
  };
};
// 根据所需类型进行转码并返回下载地址
const setEncoded = (type, filename = "diagram", data) => {
  const encodedData = encodeURIComponent(data);
  return {
    filename: `${filename}.${type}`,
    href: `data:application/${
      type === "svg" ? "text/xml" : "bpmn20-xml"
    };charset=UTF-8,${encodedData}`,
    data: data,
  };
};

// 处理弹窗取消
const handleModalCancel = () => {
  isModalVisible.value = false;
  showFullScreen.value = true;
};

// 处理全屏切换
const onFullscreenToggle = () => {
  isFullscreen.value = !isFullscreen.value;
};

//打开全屏编辑
const fullScreen = () => {
  isModalVisible.value = true;
  showFullScreen.value = false;
};
//全屏保存刷新父页面参数
const resetData = () => {
  getData(props.ruleInfo.uuid,props.ruleInfo.type);
}

// 提交规则
const urlFlag = ref(true);
const ruleInfoSub = ref()
// 监听
watch(() => window.location.href, (newUrl) => {
  urlFlag.value = !newUrl.includes('ruleContent');
}, {
  immediate: true
});
// 提交数据前状态判断
function checkSubmitStatus(ruleInfo, type) {
  let uuids = '';
  //状态判断
  let status = '';

  if (ruleInfo && ruleInfo.uuid) {
    uuids = ruleInfo.uuid;
    if (ruleInfo.status === '已删除') {
      status = ruleInfo.status;
    }
    //锁定/解锁数据不可再次提交
    if ((ruleInfo.lockId && type === '已锁定') || (!ruleInfo.lockId && type === '已解锁')) {
      status = type;
    }
  }
  return [uuids, status];
}
// 提交规则
// 添加一个防抖变量
const isSubmitting = ref(false);

function handleSubmitRule() {
  // 1. 先检查是否正在提交
  if(isSubmitting.value) {
    console.log('[DEBUG] 提交操作被阻止：正在提交中');
    return;
  }

  // 2. 设置提交状态
  isSubmitting.value = true;

  try {
    if(urlFlag.value) {
      // 3. 检查必要的属性是否存在
      if (!props.ruleInfo?.uuid) {
        console.warn('[DEBUG] 提交失败：缺少必要的uuid属性');
        return;
      }

      // 4. 构建事件数据
      const eventData = {
        record: {
          uuid: props.ruleInfo.uuid,
          status: props.ruleInfo.status || '',
          ruleName: props.ruleInfo.ruleTitle || ''
        },
        tabKey: '编辑' + props.ruleInfo.uuid
      };

      console.log('[DEBUG] 准备发送事件，数据：', eventData);

      // 5. 使用Promise包装事件发送
      Promise.resolve().then(() => {
        globalEventEmitter.emit(RULE_SUBMIT_ACTION, eventData);
        console.log('[DEBUG] 事件发送完成');
      }).catch(error => {
        console.error('[DEBUG] 事件发送失败：', error);
      });

    } else {
      // 先获取最新的规则信息，然后再进行状态检查和提交
      getRuleByUuid({
        uuids: props.ruleInfo.uuid,
      }).then((res) => {
        if (res.code === 20000) {
          const ruleData = res.data;

          // 检查状态
          let [uuids, status] = checkSubmitStatus(ruleData, '');

          if (status) {
            message.error('状态为' + status + '规则不可提交');
            return;
          }

          if (uuids) {
            let pars = qs.stringify({
              uuids: uuids,
            });

            submitRule(pars).then((res) => {
              if (res.code === 20000) {
                if (res.data == '规则置为提交待审核成功！') {
                  message.success('规则置为提交待审核成功，不允许操作，即将关闭页面！');
                  // 添加2秒延时，让用户有时间看到提示信息
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                } else {
                  message.success(res.data);
                }
              } else {
                message.error(res.data);
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[DEBUG] 提交过程发生错误：', error);
  } finally {
    // 6. 确保状态重置
    setTimeout(() => {
      isSubmitting.value = false;
      console.log('[DEBUG] 提交状态已重置');
    }, 1000);
  }
}
</script>
<style lang="scss" scoped>
.bpmn.decision-tree ::v-deep {
  & {
    width: 100%;
    height: calc(100vh - 85px);
    position: relative;
    .canvas {
      width: 100%;
      height: 80vh
    }
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-list {
      margin-left: 17px;
    }
    .tool {
      position: absolute;
      height: 36px;
      z-index: 1;
      line-height: 36px;
      top: 0;
      right: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 13px;
      .ant-btn-group {
        margin-left: 16px;
      }
    }
    .a-drawer,
    .ant-drawer {
      overflow: auto !important;
      header {
        span {
          font-size: 18px;
          color: #303133;
        }
      }
      .a-form {
        padding: 20px;
      }
      .a-input,
      .a-textarea {
        width: 336px;
      }
      .filter-tree.a-input {
        width: 100%;
      }
    }
    .ant-tabs-content-holder{
      overflow: auto;
      height: calc(100vh - 100px);
      padding-bottom: 8px;
    }
  }
  .a-alert {
    margin-bottom: 10px;
  }
  .a-form-item__content {
    line-height: unset;
  }
  .ant-btn {
    text-align: center;
    font-size: 12px;
    span {
      font-size: 12px;
    }
  }
  .ant-btn-group {
    margin: 4px;
  }
  .a-tooltip__popper {
    .ant-btn {
      width: 100%;
      text-align: left;
      padding-left: 8px;
      padding-right: 8px;
    }
    .ant-btn:hover {
      background: rgba(64, 158, 255, 0.8);
      color: #ffffff;
    }
  }
  .align {
    position: relative;
    i {
      &:after {
        content: "|";
        position: absolute;
        transform: rotate(90deg) translate(200%, 60%);
      }
    }
  }
  .align.align-left i {
    transform: rotate(90deg);
  }
  .align.align-right i {
    transform: rotate(-90deg);
  }
  .align.align-top i {
    transform: rotate(180deg);
  }
  .align.align-bottom i {
    transform: rotate(0deg);
  }
  .align.align-center i {
    transform: rotate(90deg);
    &:after {
      transform: rotate(90deg) translate(0, 60%);
    }
  }
  .align.align-middle i {
    transform: rotate(0deg);
    &:after {
      transform: rotate(90deg) translate(0, 60%);
    }
  }
  .a-tree {
    background: unset;
  }
  .wl-transfer .transfer-title {
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
  .a-icon-document.blue {
    color: #409eff;
  }
  .bjs-powered-by {
    display: none;
  }
  .wl-transfer {
    overflow: auto;
  }
  .ant-tabs-top > .ant-tabs-nav {
    margin: 0;
  }
}
</style>
