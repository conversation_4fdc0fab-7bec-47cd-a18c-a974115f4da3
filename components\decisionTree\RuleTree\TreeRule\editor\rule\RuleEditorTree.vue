<script lang="jsx">
import store from "@/store";
import DesignViewTree from "./com/DesignViewTree.vue";
import { cloneDeep, isEqual } from "lodash";
import * as util from "@/components/ruleEditCom/utils/util.js";

const { ConditionGenerate, ActionGenerate } = util;

const initRuleAttribute = [
  { enabled: "true" },
  { "lock-on-active": "false" },
  { salience: "0" },
];
const initVariable = {
  variableType: "field",
  valueType: "Object",
  value: [],
};
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};
const initAction = {
  actionType: "invokeMethod",
  actionParams: [
    {
      variableType: "field",
      valueType: "String",
      viewName: "",
      value: [],
    },
  ],
};
const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
  actions: [initAction],
};

export default {
  name: "RuleEditorTree",
  inject: {
    ruleUuid: { default: "" },
    message: { default: () => {} },
  },
  props: {
    operatorTypeList: {
      type: Object,
    },
    fieldsDataList: {
      type: Array,
    },
    dataEntryList: {
      type: Array,
    },
    predefineList: {
      type: Array,
    },
    baseMethodListMap: {
      type: Object,
    },
    methodsDataList: {
      type: Array,
    },
    mode: String,
    locked: {
      type: Boolean,
      default: true,
    },
    theme: String,
    isTrack: Boolean,
    ruleNamesBeHited: Boolean,
    ruleData: {
      type: Object,
    },
    ruleDrls: String,
    mode: String,
    uuid: String,
    validateResult: {
      type: Object,
      default: () => {},
    },
    initModelData: {
      type: Object,
      default: () => {},
    },
    injectModelData: {
      type: Object,
      default: () => {},
    },
    viewStatus: {
      type: String,
      default: "",
    },
    activeShow: {
      type: String,
      default: "",
    },
},
  data() {
    return {
      ruleAttributes: {},
      currentDataIndex: 0,
      // locked: true,
      // lockedInner: props.mode === 'create',
      self_validateResult: {},
      lockedInner: false,
      ruleDrl: null,
      ruleText: null,
      conditionId: null,
      hisDataUpdateTimes: 0,
      ruleDrlUpdateTimes: 0,
      initRuleAttribute: [
        { enabled: "true" },
        { "lock-on-active": "false" },
        { salience: "0" },
      ],
      editData: {},
      ruleStore: [],
    };
  },
  watch: {
    validateResult: {
      handler: function (newVal, oldVal) {
        this.self_validateResult = newVal;
      },
      deep: true,
    },
    ruleData: {
      deep: true,
      handler: function () {
        this.init();
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    // 添加
    conditionInit() {
      const { conditionData, currentData } = this.getCurrentData();
      const arr = conditionData.children;
      util.updateLayers(1, conditionData, 1);
      arr.splice(0, 1, {
        indent: 1,
        ruleCondition: {
          layer: 1,
          showLayer: 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    init() {
      const { ruleData, ruleAttributes, ruleDrl, ruleText } = this.$props;
      const newRuleData = this.getNewRuleData(ruleData);
      this.conditionId = ruleData ? this.getConditionsNum(ruleData) + 1 : 2;
      this.ruleDrl = ruleDrl;
      this.ruleText = ruleText;
      this.ruleAttributes = ruleAttributes;
      this.currentDataIndex = 0;
      this.editData = {
        ruleData: newRuleData,
      };
      store.commit("setCurrentEdit", this.editData);
    },
    getConditionsNum(ruleData = {}, num = 0) {
      const { conditions } = ruleData;
      if (conditions) {
        num = num + conditions.length;
      }
      return num;
    },
    getNewRuleData(ruleData = initRuleData) {
      if (ruleData.actions && ruleData.actions.length === 0) {
        ruleData.actions = initRuleData.actions;
      }

      const _RuleData = cloneDeep(ruleData);
      const { conditionExpression, conditions, actions, elseActions } =
        _RuleData;
      
      // 处理 actionData 和 ConditionData
      const conditionData = util.getConditionTree(
        conditionExpression,
        conditions,
        this.ruleUuid
      );
      const actionData = util.getActionData(actions, this.ruleUuid);
      const elseActionData = util.getActionData(elseActions, this.ruleUuid);
      this.updateIndex(actionData);
      this.updateIndex(elseActionData);
      const newRuleData = {
        conditionData,
        actionData,
        elseActionData,
      };
      return newRuleData;
    },
    isPreCondition() {
      return false;
    },
    getCurrentData() {
      const currentData = this.editData;
      const { ruleData } = currentData;
      const { conditionData, actionData, elseActionData } = ruleData;
      return { conditionData, actionData, currentData, elseActionData };
    },
    setRuleStore(obj) {
      const index = this.currentDataIndex;
      const {
        id,
        key,
        oldData,
        newData,
        layer,
        newDataClone,
        delPar,
        delArr,
        exceptForFirst,
      } = obj;
      const resObj = { id, key, oldData, newData };
      layer && (resObj.layer = layer);
      newDataClone && (resObj.newDataClone = newDataClone);
      delPar && (resObj.delPar = delPar);
      delArr && (resObj.delArr = delArr);
      exceptForFirst && (resObj.exceptForFirst = exceptForFirst);
      if (index === this.ruleStore.length) {
        this.ruleStore.push(resObj);
      } else {
        this.ruleStore.splice(index);
        this.ruleStore.push(resObj);
      }
    },
    updateRuleStore(
      currentData,
      id,
      key,
      oldData,
      action,
      delPar,
      delArr,
      exceptForFirst
    ) {
      // id: conditionId=rowId
      // currentDataIndex: ruleStore->index
      let newData = null;
      let newDataClone = null;
      const contextData = util.generateContextRuleData(currentData, key, id);
      if (key === "conditionData") {
        if (action !== "del") {
          const { targetNode, parentNode } = util.findTargetNodeInfoById(
            contextData.ruleData[key],
            id
          );
          if (action === "add" || action === "change") {
            newData = targetNode;
            newDataClone = cloneDeep(newData);
          }
          if (action === "addChildCondition") {
            newData = parentNode;
            newDataClone = cloneDeep(parentNode);
          }
          if (action === "addUp") {
            newData = targetNode;
            newDataClone = cloneDeep(newData);
          }
        }
      } else {
        if (action !== "del") {
          newData = contextData.ruleData[key][id];
          newDataClone = cloneDeep(newData);
        }
      }
      this.setRuleStore({
        id,
        key,
        oldData,
        newData,
        newDataClone,
        delPar,
        delArr,
        exceptForFirst,
      });
      this.currentDataIndex++;
      this.hisDataUpdateTimes += 1;
      if (Object.keys(this.self_validateResult).length > 0) {
        this.self_validateResult = {};
      }
    },

    addUp(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      let arr = conditionData.children;

      upArrIndentLayer(arr);
      function upArrIndentLayer(data) {
        for (let i in data) {
          data[i].indent && (data[i].indent += 1);
          if (data[i].ruleCondition && data[i].ruleCondition.layer) {
            data[i].ruleCondition.layer += 1;
            data[i].ruleCondition.showLayer += 1;
          }
          if (data[i].children) {
            upArrIndentLayer(data[i].children);
          }
        }
      }
      const resArr = [
        {
          indent: 1,
          ruleCondition: {
            layer: layer,
            showLayer: layer,
            conditionId: this.conditionId++,
            contents: new ConditionGenerate(initConditionData),
          },
        },
        {
          indent: 1,
          logicalSymbol: "and",
          fold: false,
          children: arr,
        },
      ];
      arr = resArr;
      conditionData.children = arr;
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "addUp",
        null,
        null,
        cloneDeep(resArr[1].children)
      );
    },
    addChildCondition(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;
      util.updateLayers(layer, conditionData, 2);
      arr.splice(arrIndex + 1, 0, {
        indent: targetNode.indent,
        logicalSymbol: "and",
        fold: false,
        children: [
          {
            indent: targetNode.indent + 1,
            ruleCondition: {
              layer: layer + 1,
              showLayer: layer + 1,
              conditionId: this.conditionId++,
              contents: new ConditionGenerate(initConditionData),
            },
          },
          {
            indent: targetNode.indent + 1,
            logicalSymbol: "and",
            ruleCondition: {
              layer: layer + 2,
              showLayer: layer + 2,
              conditionId: this.conditionId++,
              contents: new ConditionGenerate(initConditionData),
            },
          },
        ],
      });
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 2,
        "conditionData",
        null,
        "addChildCondition"
      );
    },
    addRule(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { children } = conditionData;
      let length = 0;
      for (let i = 0; i < children.length; i++) {
        length += this.getConLength(children[i], 0);
      }
      if (children.length >= this.conditionId) {
        if (length < children.length + 1) {
          this.conditionId = children.length + 1;
        } else {
          this.conditionId = length;
        }
      }
      if(this.conditionId<length){
        this.conditionId = length;
      }
      
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;

      util.updateLayers(layer, conditionData, 1);
      arr.splice(arrIndex + 1, 0, {
        indent: targetNode.indent,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    getConLength(data, length) {
      if (data.children) {
        length = length + data.children.length + 1;
        this.getConLength(data.children, length);
      } else {
        length++;
      }
      return length;
    },
    // 添加条件至队尾
    addTailItem(pos, conditionId) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;
      const lastCondition = util.getLastNode(parentNode);
      const { layer } = lastCondition;
      util.updateLayers(layer, conditionData, 1);

      arr.push({
        indent: targetNode.indent,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    decreaseRule({ pos, conditionId, layer }, updateRuleStore = true) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const oldData = cloneDeep(targetNode);
      const arr = parentNode.children;
      if (arr.length === 1) {
        arr.splice(arrIndex, 1);
      } else if (arr.length > 2) {
        arr.splice(arrIndex, 1);
      } else if (arr.length === 2) {
        arr.splice(arrIndex, 1);
        const _logicalSymbol = parentNode.logicalSymbol;
        // 如果数组中只有两组数据，删除后，子节点逻辑组合，将转化为一个普通的子节点；
        if (parentNode !== conditionData || !!arr[0].children) {
          if (arr[0].ruleCondition) {
            for (const i in arr[0]) {
              parentNode[i] = arr[0][i];
            }
            delete parentNode.children;
          } else if (arr[0].children) {
            for (const i in arr[0]) {
              parentNode[i] = arr[0][i];
            }
          }
          parentNode.logicalSymbol = _logicalSymbol;
          util.updateIndent(parentNode, -1);
        }
      }
      if (arr && arrIndex / 1 === 0) {
        arr[0]?.logicalSymbol && delete arr[0].logicalSymbol;
      }
      this.self_validateResult = {};
      util.updateLayers(layer, conditionData, -1);
      updateRuleStore &&
        this.updateRuleStore(
          currentData,
          Number(conditionId),
          "conditionData",
          oldData,
          "del",
          parentNode,
          arrIndex
        );
    },
    onSwitcherChange(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      this.self_validateResult = {};
      util.updateNodeFold(conditionData, conditionId, layer);
      this.updateRuleStore(currentData);
    },
    onLogicBtnClick(pos, val) {
      const [index, targetIndent, targetLayer] = pos.split("_"); // eslint-disable-line
      const { conditionData, currentData } = this.getCurrentData();
      const logicNode = util.findLogicNode(
        conditionData,
        targetIndent,
        targetLayer
      );
      const oldData = cloneDeep(logicNode);
      logicNode.logicalSymbol = val || null;
      this.updateRuleStore(
        currentData,
        Number(logicNode.ruleCondition.conditionId),
        "conditionData",
        oldData,
        "change"
      );
    },
    // 通过pos寻找当前condition节点信息：rule、ruleItem
    findConditionNodeByPosition(pos) {
      const [index, conditionId, path] = pos.split("_"); // eslint-disable-line
      const pathArr = path.split(".");
      const { conditionData, currentData } = this.getCurrentData();
      const { targetNode } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const { node, key } = util.findDataByPath(
        targetNode.ruleCondition.contents.list,
        pathArr
      );
      return { targetNode, ruleItem: node, ruleItemIndex: key, currentData };
    },
    // 通过pos寻找当前Action节点信息
    findActionNodeByPosition(pos, flag) {
      const [index, path] = pos.split("_"); // eslint-disable-line
      const { actionData, currentData, elseActionData } = this.getCurrentData();
      const pathArr = path.split(".");
      const _actionData = flag === "action" ? actionData : elseActionData;
      const { node, key } = util.findDataByPath(_actionData, pathArr);
      return {
        actionItem: node,
        actionItemIndex: key,
        actionData,
        elseActionData,
        currentData,
      };
    },
    onConditionChange(pos, newContents, type, flag) {
      const [index, conditionId] = pos.split("_");
      const { conditionData, currentData } = this.getCurrentData();
      const { targetNode } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      let oldData = cloneDeep(targetNode);
      targetNode.ruleCondition.contents = newContents;
      this.updateRuleStore(
        currentData,
        Number(conditionId),
        "conditionData",
        oldData,
        "change"
      );
    },
    updateIndex(data) {
      data.map((item, index) => {
        data[index].actionId = index;
      });
    },
    onActionComChange(flag = "action", pos, data, oldData) {
      let key = flag === "action" ? "actionData" : "elseActionData";
      const [index, actionIndex] = pos.split("_"); // eslint-disable-line
      const { actionData, currentData, elseActionData } = this.getCurrentData();
      if (flag === "action") {
        actionData[actionIndex] = data;
      } else if (flag === "elseAction") {
        elseActionData[actionIndex] = data;
      }
      this.updateRuleStore(currentData, actionIndex, key, oldData, "change");
    },
    addActionItem(flag = "action", pos) {
      let key = flag === "action" ? "actionData" : "elseActionData";
      const newActionData = new ActionGenerate(initAction);
      if (!pos) {
        const { currentData, elseActionData } = this.getCurrentData();
        elseActionData.push(newActionData);
        this.updateRuleStore(currentData, 0, key, null, "add");
      } else {
        const { currentData, actionItemIndex, actionData, elseActionData } =
          this.findActionNodeByPosition(pos, flag);
        if (flag === "action") {
          actionData.splice(Number(actionItemIndex) + 1, 0, newActionData);
        } else if (flag === "elseAction") {
          elseActionData.splice(Number(actionItemIndex) + 1, 0, newActionData);
        }
        this.self_validateResult = {};
        this.updateIndex(this.editData.ruleData[key]);
        this.updateRuleStore(
          currentData,
          Number(actionItemIndex) + 1,
          key,
          null,
          "add"
        );
      }
    },
    deleteActionItem(flag = "action", pos) {
      let key = flag === "action" ? "actionData" : "elseActionData";
      const { currentData, actionItemIndex, actionData, elseActionData } =
        this.findActionNodeByPosition(pos, flag);
      this.updateIndex(this.editData.ruleData[key]);
      if (flag === "action") {
        const length = actionData.length;
        const oldData = cloneDeep(actionData[actionItemIndex]);
        if (length > 1) {
          actionData.splice(actionItemIndex, 1);
          this.updateRuleStore(
            currentData,
            actionItemIndex,
            key,
            oldData,
            "del"
          );
        } else {
          this.$antMessage.info("请至少保留一条数据！");
        }
      } else if (flag === "elseAction") {
        const oldData = cloneDeep(elseActionData[actionItemIndex]);
        elseActionData.splice(actionItemIndex, 1);
        this.updateRuleStore(currentData, actionItemIndex, key, oldData, "del");
      }
      this.self_validateResult = {};
    },
    undo() {
      // id-rowId
      // currentDataIndex: ruleStore-index
      let { currentDataIndex } = this;
      if (currentDataIndex > 0) {
        let layer = null;
        const {
          id,
          key,
          oldData,
          newData,
          newDataClone,
          delPar,
          delArr,
          exceptForFirst,
        } = this.ruleStore[currentDataIndex - 1];
        const _oldData = cloneDeep(oldData);
        const { children } = this.editData.ruleData[key];
        let _targetNode,
          _parentNode,
          _grandparentNode,
          _arrIndex,
          _parentArrIndex;
        if (key === "conditionData" && newData) {
          const {
            targetNode,
            parentNode,
            grandparentNode,
            arrIndex,
            parentArrIndex,
          } = util.findTargetNodeInfoById(this.editData.ruleData[key], id);
          _targetNode = targetNode;
          _parentNode = parentNode;
          _grandparentNode = grandparentNode;
          _arrIndex = arrIndex;
          _parentArrIndex = parentArrIndex;
        }
        const { conditionData } = this.getCurrentData();
        if (oldData && newData) {
          // change
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            _parentNode.children.splice(_arrIndex, 1, _oldData);
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 1, _oldData);
          }
        } else if (oldData) {
          // del
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            util.updateLayers(layer - 1, conditionData, 1);
            if (delPar.children) {
              delPar.children.splice(delArr, 0, _oldData);
            } else {
              const { targetNode, arrIndex } = util.findTargetNodeInfoById(
                this.editData.ruleData[key],
                delPar.ruleCondition.conditionId
              );
              this.$set(targetNode, "children", [
                {
                  indent: targetNode.indent + 1,
                  ruleCondition: targetNode.ruleCondition,
                },
              ]);
              // delete delPar.ruleCondition
              delete targetNode.ruleCondition;
              targetNode.children.splice(delArr, 0, _oldData);
            }
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 0, _oldData);
          }
        } else {
          if (key === "conditionData") {
            if (exceptForFirst && newDataClone && newDataClone.ruleCondition) {
              // addUp
              layer = newDataClone.ruleCondition.layer;
              this.ruleStore[currentDataIndex - 1].delPar = _parentNode;
              this.ruleStore[currentDataIndex - 1].delArr = _arrIndex;
              this.ruleStore[currentDataIndex - 1].delConId =
                _targetNode.ruleCondition.conditionId;
              // return
              _parentNode.children.splice(_arrIndex, 1);
              _parentNode.children = _parentNode.children[_arrIndex].children;
              upArrIndentIndent(_parentNode.children);
              function upArrIndentIndent(data) {
                for (let i in data) {
                  data[i].indent && data[i].indent--;
                  if (data[i].children) {
                    upArrIndentIndent(data[i].children);
                  }
                }
              }
              util.updateLayers(
                layer - 1,
                this.editData.ruleData.conditionData,
                -1
              );
            } else if (newDataClone && newDataClone.ruleCondition) {
              // add
              layer = newDataClone.ruleCondition.layer;
              this.ruleStore[currentDataIndex - 1].delPar = _parentNode;
              this.ruleStore[currentDataIndex - 1].delArr = _arrIndex;
              this.ruleStore[currentDataIndex - 1].delConId =
                _targetNode.ruleCondition.conditionId;
              _parentNode.children.splice(_arrIndex, 1);
              util.updateLayers(
                layer - 1,
                this.editData.ruleData.conditionData,
                -1
              );
            } else if (newDataClone && newDataClone.children) {
              // 联合条件
              layer = _targetNode.ruleCondition.layer;
              this.ruleStore[currentDataIndex - 1].undo = {
                _grandparentNode,
                _parentArrIndex,
              };
              _grandparentNode.children.splice(_parentArrIndex, 1);
              util.updateLayers(layer - 1, conditionData, -2);
            }
          } else {
            this.editData.ruleData[key].splice(newDataClone.actionId, 1);
          }
        }
        this.hisDataUpdateTimes += 1;
        this.currentDataIndex -= 1;
      }
    },
    setValByNewDataClone(data, cloneData) {
      const { children, ruleCondition } = data;
      const { children: childrenClone, ruleCondition: ruleConditionClone } =
        cloneData;
      if (children && children.length > 0) {
        data.children = setValByNewDataClone(children, childrenClone);
      }
      if (ruleCondition) {
        ruleCondition.contents = ruleConditionClone.contents;
      }
      return data;
    },
    redo() {
      let { currentDataIndex, ruleStore } = this;
      const len = ruleStore.length;
      if (currentDataIndex < len) {
        let layer = null;
        const {
          id,
          key,
          oldData,
          newData,
          undo,
          newDataClone,
          delPar,
          delArr,
          delConId,
          exceptForFirst,
        } = ruleStore[currentDataIndex];
        const _newDataClone = cloneDeep(newDataClone);
        const { children } = this.editData.ruleData[key];
        let _targetNode,
          _parentNode,
          _grandparentNode,
          _arrIndex,
          _parentArrIndex;
        if (key === "conditionData" && oldData) {
          const {
            targetNode,
            parentNode,
            grandparentNode,
            arrIndex,
            parentArrIndex,
          } = util.findTargetNodeInfoById(this.editData.ruleData[key], id);
          _targetNode = targetNode;
          _parentNode = parentNode;
          _grandparentNode = grandparentNode;
          _arrIndex = arrIndex;
          _parentArrIndex = parentArrIndex;
        }
        const { conditionData } = this.getCurrentData();
        if (oldData && newData) {
          // change
          if (key === "conditionData") {
            layer = newDataClone.ruleCondition.layer;
            const _data = cloneDeep(newData);
            _parentNode.children.splice(
              _arrIndex,
              1,
              this.setValByNewDataClone(_data, newDataClone)
            );
          } else {
            this.editData.ruleData[key].splice(newData.actionId, 1, newData);
          }
        } else if (newData) {
          if (key === "conditionData") {
            if (exceptForFirst && newDataClone && newDataClone.ruleCondition) {
              // addUp
              const _exceptForFirst = cloneDeep(exceptForFirst);
              if (
                delPar.children.length > 0 &&
                delPar.children[0].ruleCondition &&
                Object.keys(delPar.children[0].ruleCondition).length > 0
              ) {
                const targetNodet = util.findTargetNodeInfoById(
                  this.editData.ruleData[key],
                  delPar.children[0].ruleCondition.conditionId
                );
                const resArr = [
                  _newDataClone,
                  {
                    indent: 1,
                    logicalSymbol: "and",
                    fold: false,
                    children: _exceptForFirst,
                  },
                ];
                targetNodet.parentNode.children = resArr;
              } else {
                const _exceptForFirst = cloneDeep(exceptForFirst);
                const resArr = [
                  _newDataClone,
                  {
                    indent: 1,
                    logicalSymbol: "and",
                    fold: false,
                    children: _exceptForFirst,
                  },
                ];
                this.editData.ruleData[key].children = resArr;
              }
            } else if (newDataClone && newDataClone.ruleCondition) {
              // add
              layer = newDataClone.ruleCondition.layer;
              util.updateLayers(layer - 1, conditionData, 1);

              if (
                delPar.children.length > 0 &&
                Object.keys(delPar.children[0].ruleCondition).length > 0
              ) {
                const targetNodet = util.findTargetNodeInfoById(
                  this.editData.ruleData[key],
                  delPar.children[0].ruleCondition.conditionId
                );
                if (
                  Object.keys(targetNodet.parentNode).length > 0 &&
                  targetNodet.parentNode.children
                ) {
                  targetNodet.parentNode.children.splice(
                    delArr,
                    0,
                    _newDataClone
                  );
                }
              } else {
                delPar.children.splice(delArr, 0, _newDataClone);
              }
            } else if (newDataClone && newDataClone.children) {
              // 联合条件
              const { targetNode } = util.findTargetNodeInfoById(
                newDataClone,
                id
              );
              layer = targetNode.ruleCondition.layer;
              util.updateLayers(layer - 1, conditionData, 2);
              let targetNodet = {};
              if (
                undo._grandparentNode.children.length > 0 &&
                undo._grandparentNode.children[0].ruleCondition
              ) {
                targetNodet = util.findTargetNodeInfoById(
                  this.editData.ruleData[key],
                  undo._grandparentNode.children[0].ruleCondition.conditionId
                );
              }
              if (
                targetNodet.parentNode &&
                Object.keys(targetNodet.parentNode).length > 0 &&
                targetNodet.parentNode.children
              ) {
                targetNodet.parentNode.children.splice(
                  undo._parentArrIndex,
                  0,
                  _newDataClone
                );
              } else {
                undo._grandparentNode.children.splice(
                  undo._parentArrIndex,
                  0,
                  _newDataClone
                );
              }
            }
          } else {
            this.editData.ruleData[key].splice(
              newDataClone.actionId,
              0,
              _newDataClone
            );
            this.updateIndex(this.editData.ruleData[key]);
          }
        } else {
          // del
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            _parentNode.children.splice(_arrIndex, 1);
            util.updateLayers(layer - 1, conditionData, -1);
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 1);
            this.updateIndex(this.editData.ruleData[key]);
          }
        }
        this.currentDataIndex += 1;
        this.hisDataUpdateTimes += 1;
      }
    },
    getRulesData() {
      const { editData, currentDataIndex, ruleAttributes = {} } = this;
      const { ruleName = "save", ...otherAttributes } = ruleAttributes;
      const currentData = editData;
      const { ruleData } = currentData;
      const data = { ...ruleData, ruleName, ruleAttributes: otherAttributes };
      const newRuleData = util.getBackEndData(data, this.ruleUuid);
      return newRuleData;
    },
    toSave() {
      const { complexModels } = this.injectModelData;
      const { fieldsDataList } = util.getModelList(complexModels);
      store.commit("setFieldsDataList", {
        list: fieldsDataList,
        ruleUuid: this.ruleUuid,
      });
      const ruleData = this.getRulesData();
      this.$emit("save", ruleData);
    },
    toValidate() {
      const { complexModels } = this.injectModelData;
      const { fieldsDataList } = util.getModelList(complexModels);
      store.commit("setFieldsDataList", {
        list: fieldsDataList,
        ruleUuid: this.ruleUuid,
      });
      const ruleData = this.getRulesData();
      this.$emit("validate", ruleData);
    },
    changeLockState() {
      if (this.$props.onLock) {
        this.$props.onLock(this.locked);
      }
    },
    changeLockInner() {
      this.lockedInner = !this.lockedInner;
    },
  },
  render() {
    const {
      theme,
    } = this.$props;
    const { ruleDrls } = this.$props;
    const { ruleData = {} } = this.editData;

    return (
      <div class={`eRuleEditorContainer ${this.theme || "oneDark"}`}>
        {ruleData ? (
          <div class="eRuleEditor">
            <div class="editorBody">
              <DesignViewTree
                style={
                  this.viewStatus === "designView"
                    ? { display: "block" }
                    : { display: "none" }
                }
                locked={!this.locked}
                ruleNamesBeHited={this.ruleNamesBeHited}
                isTrack={this.isTrack}
                ruleData={this.editData.ruleData}
                validateResult={this.self_validateResult}
                onAddRule={this.addRule} // 添加条件
                onAddUp={this.addUp} // 添加上级条件
                onAddChildCondition={this.addChildCondition} // 添加联合条件
                onAddTailItem={this.addTailItem} // 添加至队尾
                onDecreaseRule={this.decreaseRule} // 删除条件
                onAddActionItem={this.addActionItem} // 添加ActionItem
                onDeleteActionItem={this.deleteActionItem} // 删除ActionItem
                onSwitcherChange={this.onSwitcherChange} // 收起或展开条件集
                onLogicBtnClick={this.onLogicBtnClick} // 更改逻辑运算符
                onConditionChange={this.onConditionChange} // 条件组件变化
                onActionComChange={this.onActionComChange} // 行为组件--setValueCom组件内部值变化
                onConditionInit={this.conditionInit} // 添加初始化条件
                class="rule-design"
                ref="view_status_com"
                viewStatus={this.viewStatus}
                activeShow={this.activeShow}
              />
              {this.viewStatus === "codeView" ? (
                <CodeView ruleDrl={ruleDrls} class="rule-code" />
              ) : null}
            </div>
          </div>
        ) : null}
      </div>
    );
  },
};
</script>

<style lang="scss">
@use "@/assets/css/ruleEditor.scss";
.ant-drawer-dir {
  .eRuleEditorContainer {
    width: 100%;
    height: auto;
    border-radius: 3px;
    .eRuleEditor {
      height: auto;
    }
  }
}
</style>
