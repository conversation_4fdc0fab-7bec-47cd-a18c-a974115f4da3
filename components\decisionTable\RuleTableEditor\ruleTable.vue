<template>
  <div>
    <TableEditor 
      :props_columns="columns"
      :props_data="data"
      :isEditing="isEditing"
      :props_predefine="propsPredefine"
      @handleRowSave="handleRowSave"
      @colInsertrowLeft="onColInsertrowLeft"
      @colInsertrowRight="onColInsertrowRight"
      @colDelete="onColDelete"
      @rowInsertrowAbove="onRowInsertrowAbove"
      @rowInsertrowBelow="onRowInsertrowBelow"
      @rowDelete="onRowDelete"
      @colEdit="onColEdit"
      @getPredefine="getPredefine"
    />
    <ModalForm 
      :colEditingKey="colEditingKey === 0 ? '' : colEditingKey"
      :properties="getProperties(cloneDeep(columns), colEditingKey)"
      @ok="ruleOnOk"
      @cancel="ruleOnCancel"
      @setComplexSelect="setComplexSelect"
      @setAllCellUnit="setAllCellUnit"
    />
  </div>
</template>

<script setup>
import { cloneDeep } from 'lodash'
import * as tableUtil from './util'
const message = inject("message");
// 常量定义
const CONDITION = 'condition'
const ACTION = 'action'

// 注入依赖
const ruleUuid = inject('ruleUuid', '')

// Props定义
const props = defineProps({
  ruleContent: {
    type: Object,
    default: () => ({}),
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  predefineDom: String,
  viewStatus: {
    type: String,
    default: 'designView'
  }
})

// Emits定义
const emit = defineEmits(['onChange', 'getPredefine'])

// 状态变量定义
const colConditionKey = ref(1001)
const colActionKey = ref(2001)
const rowKey = ref(10001)
const colMap = ref([])
const newCondition = ref(1)
const newAction = ref(1)
const _newCol = ref([])
const columns = ref([])
const data = ref([])
const colEditingKey = ref(0)
const oldColumnsRule = ref({})
const propsPredefine = ref('')
const ruleCellUnit = ref(undefined)
const ruleCellKey = ref('')
const listStringNameList = ref(null)
const allCellUnit = ref(undefined)
const comSelect = ref(false)
const express = ref(false)

// 工具方法
const getProperties = (cols, key) => {
  return tableUtil.getProperties(cols, key)
}

// 获取预定义
const getPredefine = () => {
  emit('getPredefine')
}

// 获取内容方法参数
const getContentMethodParams = (data) => {
  const { next, methodParams } = data
  if (next) {
    return getContentMethodParams(next)
  }
  if (methodParams) {
    return methodParams
  }
}

// 获取表格参数
const getTableParams = (methodParams = [], aExpressParam = [], objVal = {}) => {
  express.value = false

  return methodParams.map((item, index) => {
    const { value = false, valueType } = item
    if (item.variableType && item.variableType === "expression") {
      const expressionTreeData = item.expressionTreeData
      if (expressionTreeData && expressionTreeData.params) {
        loopExpressionTreeData(expressionTreeData.params)
      }
    } else {
      if (!valueType.includes(".")) {
        if (express.value) {
          aExpressParam.push(!!(value && value.toString().length > 0))
        } else {
          const { next, value = false } = item
          if (next) {
            const _methodParams = next.methodParams
            if (_methodParams && _methodParams.length > 0) {
              express.value = true
              return getTableParams(_methodParams, aExpressParam, objVal)
            } else {
              const { value = false } = next
              if (express.value) {
                aExpressParam.push(!!(value && value.toString().length > 0))
              } else {
                aExpressParam.push(!!(value && value.toString().length > 0))
                return !!(value && value.toString().length > 0)
              }
            }
          } else {
            if (express.value) {
              aExpressParam.push(!!(value && value.toString().length > 0))
            } else {
              aExpressParam.push(!!(value && value.toString().length > 0))
              return !!(value && value.toString().length > 0)
            }
          }
        }
        return !!(value && value.toString().length > 0)
      } else {
        const { next, value = false } = item
        if (next) {
          const _methodParams = next.methodParams
          if (_methodParams && _methodParams.length > 0) {
            express.value = true
            return getTableParams(_methodParams, aExpressParam, objVal)
          } else {
            const { value = false } = next
            if (express.value) {
              aExpressParam.push(!!(value && value.toString().length > 0))
            } else {
              aExpressParam.push(!!(value && value.toString().length > 0))
              return !!(value && value.toString().length > 0)
            }
          }
        } else {
          if (express.value) {
            aExpressParam.push(!!(value && value.toString().length > 0))
          } else {
            aExpressParam.push(!!(value && value.toString().length > 0))
            return !!(value && value.toString().length > 0)
          }
        }
      }
    }
    objVal[index] = aExpressParam
    aExpressParam = []
    express.value = false
    if (!valueType.includes(".")) {
      return !!(value && value.toString().length > 0)
    }
    return true
  })
}

// 遍历表达式树数据
const loopExpressionTreeData = (data) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].data && data[i].data.next) {
      const { next } = data[i].data
      if (next) {
        const _methodParams = next && next.methodParams
        if (_methodParams && _methodParams.length > 0) {
          express.value = true
          getTableParams(_methodParams, [], {})
        } else {
          const { value = false } = next
          aExpressParam.push(!!(value && value.toString().length > 0))
        }
      }
    } else if (data[i].type === "variable") {
      const _data = data[i].data
      if (!_data.valueType.includes(".")) {
        aExpressParam.push(!!(_data.value && _data.value.toString().length > 0))
      }
    } else if (data[i].type === "expression") {
      loopExpressionTreeData(data[i].params)
    }
  }
}

// 列转换方法
const colConverter = (val) => {
  const { ruleContent = {} } = val ? { ruleContent: { ...val } } : props
  const { definition = {} } = ruleContent
  let cols

  if (Object.keys(definition).length > 0) {
    cols = [
      ...prospConditions(ruleContent),
      ...prospActions(ruleContent),
    ]
  } else {
    cols = [...initConditions(), ...initActions()]
  }
  _newCol.value = cols
  return cols
}

// 处理条件列
const prospConditions = (ruleContent) => {
  const data = tableUtil.getPropsTbCondition(
    colConditionKey.value,
    colMap.value,
    ruleContent,
    ruleUuid
  )
  const { colConditionKey: newColKey, colMap: newColMap, initCondition } = data
  colConditionKey.value = newColKey
  colMap.value = newColMap
  return initCondition
}

// 处理动作列
const prospActions = (ruleContent) => {
  const data = tableUtil.getPropsTbAction(
    colActionKey.value,
    colMap.value,
    ruleContent,
    ruleUuid
  )
  const { colActionKey: newColKey, colMap: newColMap, initAction } = data
  colActionKey.value = newColKey
  colMap.value = newColMap
  return initAction
}

// 初始化条件列
const initConditions = () => {
  const data = tableUtil.getInitCondition(
    colConditionKey.value,
    newCondition.value,
    colMap.value,
    ruleUuid
  )
  const { 
    colConditionKey: newCol, 
    newCondition: newCon, 
    colMap: newMap, 
    initCondition 
  } = data
  colConditionKey.value = newCol
  newCondition.value = newCon
  colMap.value = newMap
  return initCondition
}

// 初始化动作列
const initActions = () => {
  const data = tableUtil.getInitActions(
    colActionKey.value,
    newAction.value,
    colMap.value,
    ruleUuid
  )
  const { 
    colActionKey: newCol, 
    newAction: newAct, 
    colMap: newColMap, 
    initAction 
  } = data
  colActionKey.value = newCol
  newAction.value = newAct
  colMap.value = newColMap
  return initAction
}

// 行数据转换
const rowConverter = (val) => {
  const { ruleContent = {} } = val ? { ruleContent: { ...val } } : props
  const { definition = {} } = ruleContent
  let tableData

  if (Object.keys(definition).length > 0) {
    tableData = propsDatas(_newCol.value)
  } else {
    tableData = initDatas()
  }
  return tableData
}

// 处理已有的行数据
const propsDatas = (columns) => {
  const { rows } = props.ruleContent
  const data = tableUtil.getPropsTbData(
    columns,
    rowKey.value,
    colMap.value,
    rows
  )
  const { rowKey: newRowKey, colMap: newColMap, initData } = data
  rowKey.value = newRowKey
  colMap.value = newColMap
  return initData
}

// 初始化行数据
const initDatas = () => {
  const initData = [{}]
  colMap.value.forEach((item) => {
    initData[0][item] = ""
  })
  initData[0].key = "rule" + rowKey.value
  rowKey.value += 1
  return initData
}

// 处理行保存
const handleRowSave = (newData) => {
  data.value = newData
  const toBEData = tableUtil.getToBEData(newData, columns.value, ruleUuid)
  listStringNameList.value = []
  emit('onChange', toBEData)
}

// 左侧插入列
const onColInsertrowLeft = (key) => {
  const editIndex = columns.value.findIndex((item) => key === item.key)
  const addType = columns.value[editIndex].colType === CONDITION
  const viewName = addType
    ? `条件${newCondition.value}`
    : `动作${newAction.value}`
  const colKey = addType
    ? "rule" + colConditionKey.value
    : "rule" + colActionKey.value

  const newCols = tableUtil.getLeftCols(
    columns.value,
    editIndex,
    viewName,
    colKey,
    addType,
    ruleUuid
  )

  if(addType) {
    colConditionKey.value += 1
    newCondition.value += 1
  } else {
    colActionKey.value += 1
    newAction.value += 1
  }

  const newData = tableUtil.getLeftData(data.value, colKey)
  columns.value = newCols
  data.value = newData
  message.success("新增列成功")

  // 多重延迟同步，确保DOM更新完成
  nextTick(() => {
    triggerScrollSync()
  })
  
  // 额外的延迟同步，处理异步渲染情况
  setTimeout(() => {
    triggerScrollSync()
  }, 50)
  
  setTimeout(() => {
    triggerScrollSync()
  }, 200)

  const toBEData = tableUtil.getToBEData(newData, newCols, ruleUuid)
  emit('onChange', toBEData)
}

// 右侧插入列
const onColInsertrowRight = (key) => {
  const editIndex = columns.value.findIndex((item) => key === item.key)
  const addType = columns.value[editIndex].colType === CONDITION
  const viewName = addType
    ? `条件${newCondition.value}`
    : `动作${newAction.value}`
  const colKey = addType
    ? "rule" + colConditionKey.value
    : "rule" + colActionKey.value

  const newCols = tableUtil.getRightCols(
    columns.value,
    editIndex,
    viewName,
    colKey,
    addType,
    ruleUuid
  )

  if(addType) {
    colConditionKey.value += 1
    newCondition.value += 1
  } else {
    colActionKey.value += 1
    newAction.value += 1
  }

  const newData = tableUtil.getRightData(data.value, colKey)
  columns.value = newCols
  data.value = newData
  message.success("新增列成功")

  // 多重延迟同步，确保DOM更新完成
  nextTick(() => {
    triggerScrollSync()
  })
  
  // 额外的延迟同步，处理异步渲染情况
  setTimeout(() => {
    triggerScrollSync()
  }, 50)
  
  setTimeout(() => {
    triggerScrollSync()
  }, 200)

  const toBEData = tableUtil.getToBEData(newData, newCols, ruleUuid)
  emit('onChange', toBEData)
}

// 触发滚动同步 - 直接同步表头和表身的滚动位置
const triggerScrollSync = () => {
  try {
    // 在决策表编辑器容器内查找表格元素
    const editorContainer = document.querySelector('.eRuleEditorContainer .table-design')
    
    let tableBody, tableHeader
    
    if (editorContainer) {
      // 在编辑器容器内查找
      tableBody = editorContainer.querySelector('.ant-table-body')
      tableHeader = editorContainer.querySelector('.ant-table-header')
    } else {
      // 后备方案：直接查找
      tableBody = document.querySelector('.ant-table-body')
      tableHeader = document.querySelector('.ant-table-header')
    }
    
    if (!tableBody) {
      return
    }
    
    if (!tableHeader) {
      return
    }
    
    // 直接同步滚动位置
    if (tableHeader.scrollLeft !== tableBody.scrollLeft) {
      tableHeader.scrollLeft = tableBody.scrollLeft
    }
    
    // 尝试多种方式触发事件
    const scrollEvent = new Event('scroll', { bubbles: true, cancelable: true })
    tableBody.dispatchEvent(scrollEvent)
    
    // 强制重新计算布局
    tableBody.offsetHeight
    tableHeader.offsetHeight
    
  } catch (error) {
  }
}

// 删除列
const onColDelete = (key) => {
  const editIndex = columns.value.findIndex((item) => key === item.key)
  if (key === colEditingKey.value) {
    message.info("该条规则正在编辑，无法删除!")
    return
  }
  
  if (editIndex > -1) {
    const copyData = cloneDeep(columns.value)
    copyData.splice(editIndex, 1)
    const conditionCol = copyData.findIndex(
      (item) => item.colType === CONDITION
    )
    const actionCol = copyData.findIndex(
      (item) => item.colType === ACTION
    )
    if (conditionCol === -1 || actionCol === -1) {
      message.info("请至少拥有一个条件列和一个动作列!")
      return
    }

    const copyTbData = cloneDeep(data.value)
    for (let j = 0; j < copyTbData.length; j++) {
      const element = copyTbData[j]
      let pattern = new RegExp(`${key}\\d*child`)
      for(let elK in element){
        if(pattern.test(elK)){
          delete element[elK]
        }
      }
      delete element[key]
    }

    columns.value = copyData
    data.value = copyTbData
    message.success("删除列成功")

    // 多重延迟同步，确保DOM更新完成
    nextTick(() => {
      triggerScrollSync()
    })
    
    // 额外的延迟同步，处理异步渲染情况
    setTimeout(() => {
      triggerScrollSync()
    }, 50)
    
    setTimeout(() => {
      triggerScrollSync()
    }, 200)

    const toBEData = tableUtil.getToBEData(copyTbData, copyData, ruleUuid)
    emit('onChange', toBEData)
  }
}

// 上方插入行
const onRowInsertrowAbove = (key) => {
  const copyData = cloneDeep(data.value)
  const editIndex = data.value.findIndex((item) => key === item.key)
  const newItem = resetRowItem(copyData[editIndex])
  copyData.splice(editIndex, 0, newItem)
  data.value = copyData
  message.success("新增行成功")

  const toBEData = tableUtil.getToBEData(
    copyData,
    columns.value,
    ruleUuid,
    listStringNameList.value,
    true
  )
  emit('onChange', toBEData)
}

// 下方插入行
const onRowInsertrowBelow = (key) => {
  const copyData = cloneDeep(data.value)
  const editIndex = data.value.findIndex((item) => key === item.key)
  const newItem = resetRowItem(copyData[editIndex])
  copyData.splice(editIndex + 1, 0, newItem)
  data.value = copyData
  message.success("新增行成功")

  const toBEData = tableUtil.getToBEData(copyData, columns.value, ruleUuid)
  emit('onChange', toBEData)
}

// 重置行项目
const resetRowItem = (item) => {
  const newItem = {}
  Object.keys(item).forEach((i) => {
    newItem[i] = item[i] === "-" ? item[i] : ""
  })
  newItem.key = "rule" + rowKey.value
  rowKey.value += 1
  return newItem
}

// 删除行
const onRowDelete = (key) => {
  const index = data.value.findIndex((item) => key === item.key)
  const copyData = cloneDeep(data.value)
  if (index > -1) {
    copyData.splice(index, 1)
    if (copyData.length === 0) {
      message.info("请至少拥有一行数据")
      return
    }
    data.value = copyData
    message.success("删除行成功")

    const toBEData = tableUtil.getToBEData(copyData, columns.value, ruleUuid)
    emit('onChange', toBEData)
  }
}

// 编辑列
const onColEdit = (key) => {
  if (colEditingKey.value === key) return
  colEditingKey.value = key
  const cCol = columns.value.find(item => item.key === key)
  if (cCol) {
    oldColumnsRule.value = cloneDeep(cCol.properties.rule)
  }
}

// 规则确认
const ruleOnOk = (ruleValue, nameList, conList) => {
  if (nameList) {
    listStringNameList.value = nameList
  } else {
    listStringNameList.value = null
  }

  const { aliasName, conditionData = {}, actionData = {}, complexModel } = ruleValue
  const copyColumns = cloneDeep(columns.value)
  const isAction = colEditingKey.value && colEditingKey.value[4] === "2"
  const editIndex = tableUtil.findEditIndex(columns.value, colEditingKey.value)
  
  let newData = {}
  if (comSelect.value && ruleCellUnit.value) {
    conditionData.cellUnit = true
    conditionData.complexSelect = true
  } else if(comSelect.value && ruleCellUnit.value === false) {
    conditionData.cellUnit = false
    conditionData.complexSelect = false
  }

  if (!isAction) {
    newData = tableUtil.getRuleCondition(
      columns.value,
      conditionData,
      aliasName,
      colEditingKey.value,
      ruleUuid,
      nameList,
      conList
    )
  } else {
    newData = tableUtil.getRuleAction(
      columns.value,
      actionData,
      aliasName,
      colEditingKey.value,
      nameList
    )
  }

  if (complexModel) {
    newData.valueType = "Boolean"
  }

  if (ruleCellKey.value === newData.key) {
    if (comSelect.value && ruleCellUnit.value) {
      newData.properties.rule.cellUnit = true
      newData.properties.rule.complexSelect = true
    } else if(comSelect.value && ruleCellUnit.value === false) {
      newData.properties.rule.cellUnit = false
      newData.properties.rule.complexSelect = false
    }
    if (allCellUnit.value !== undefined) {
      newData.properties.rule.allCellUnit = allCellUnit.value
    }
  }

  copyColumns.splice(editIndex, 1, newData)

  const nCol = copyColumns.find(item => item.key === colEditingKey.value)?.properties.rule
  if (!oldColumnsRule.value.complexSelect) {
    oldColumnsRule.value.cellUnit = false
    oldColumnsRule.value.complexSelect = false
  } else {
    oldColumnsRule.value.cellUnit = true
    oldColumnsRule.value.complexSelect = true
  }
  if (!oldColumnsRule.value.allCellUnit) {
    oldColumnsRule.value.allCellUnit = undefined
  }

  if (!isObjectValueEqual(toRaw(oldColumnsRule.value), toRaw(nCol))) {
    data.value = data.value.map(item => ({...item, colEditingKey: ""}))
  }

  let newDataVoid = data.value
  if (nameList === "void") {
    newDataVoid = data.value.map(item => ({
      ...item,
      [colEditingKey.value]: "-"
    }))
  } else {
    newDataVoid = data.value.map(dataItem => {
      const cur = { ...dataItem }
      if (Array.isArray(nameList)) {
        nameList.forEach(nameListItem => {
          const key = nameListItem.keyI
          if (key !== undefined) {
            const childKey = colEditingKey.value + key + "child"
            cur[childKey] = ""
          }
        })
      }
      return cur
    })
  }

  columns.value = copyColumns
  colEditingKey.value = 0
  data.value = newDataVoid
  
  const toBEData = tableUtil.getToBEData(newDataVoid, copyColumns, ruleUuid)
  emit('onChange', toBEData)
  
  ruleCellUnit.value = false
  comSelect.value = false
}

// 规则取消
const ruleOnCancel = () => {
  colEditingKey.value = 0
}

// 对象值比较
const isObjectValueEqual = (a, b, visited = new WeakMap()) => {
  // 基础类型直接比较
  if (a === b) return true
  
  // 如果不是对象或为null，直接返回false
  if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {
    return false
  }
  
  // 检查循环引用
  if (visited.has(a)) {
    return visited.get(a) === b
  }
  visited.set(a, b)
  
  // 获取两个对象的所有属性
  const aKeys = Object.keys(a)
  const bKeys = Object.keys(b)
  
  // 属性数量不同，直接返回false
  if (aKeys.length !== bKeys.length) {
    return false
  }
  
  // 比较每个属性
  for (const key of aKeys) {
    // 检查b是否有该属性
    if (!b.hasOwnProperty(key)) {
      return false
    }
    
    // 递归比较属性值
    if (typeof a[key] === 'object' && a[key] !== null) {
      if (!isObjectValueEqual(a[key], b[key], visited)) {
        return false
      }
    } else if (a[key] !== b[key]) {
      return false
    }
  }
  
  return true
}

// 设置复杂选择
const setComplexSelect = (val, colEditingKey) => {
  comSelect.value = true
  if (val) {
    ruleCellUnit.value = true
    ruleCellKey.value = colEditingKey
  } else {
    ruleCellUnit.value = false
    ruleCellKey.value = ""
  }
}

// 设置所有单元格单位
const setAllCellUnit = (val) => {
  allCellUnit.value = val
}

// 获取字符串名称列表
const getStringNameList = () => {
  const { definition } = props.ruleContent
  const { conditions = [] } = definition
  
  for (let coni = 0; coni < conditions.length; coni++) {
    const { variable } = conditions[coni]
    let _methodParams = getContentMethodParams(variable)
    let objVal = {}
    let tableParams = getTableParams(_methodParams, [], objVal)
    
    if (Object.keys(objVal).length > 0) {
      for (let i = 0; i < tableParams.length; i++) {
        if (objVal[i] && objVal[i].length > 0) {
          tableParams[i] = objVal[i]
        }
      }
    }
    
    const flattenArray = (arr) => {
      return arr.reduce((flat, item) => {
        return flat.concat(Array.isArray(item) ? flattenArray(item) : item)
      }, [])
    }
    
    tableParams = flattenArray(tableParams)
    const length = tableParams.filter(item => item === false).length
    
    if (length > 0) {
      const nameList = tableParams.map((item, index) => {
        if (!item) {
          return {
            key: "参数" + (index + 1),
            name: "参数" + (index + 1),
            value: "参数" + (index + 1),
            keyI: index,
          }
        }
      }).filter(Boolean)

      listStringNameList.value = nameList
      return
    }
  }
}

// 监听属性变化
watch(() => props.ruleContent, (val) => {
  // 只在非校验视图下执行
  if (props.viewStatus !== 'ValidateView') {
    const colVal = colConverter(val)
    const rowVal = rowConverter(val)
    columns.value = colVal
    data.value = rowVal
    colEditingKey.value = 0
    const toBEData = tableUtil.getToBEData(rowVal, colVal, ruleUuid)
    emit('onChange', toBEData)
  }
}, { deep: true })

watch(() => props.predefineDom, (val) => {
  propsPredefine.value = val
}, { deep: true })

// 组件挂载时的处理
onMounted(() => {
  const toBEData = tableUtil.getToBEData(data.value, columns.value, ruleUuid)
  emit('onChange', toBEData)
})

// 初始化数据
columns.value = colConverter()
data.value = rowConverter()
</script>

<style lang="scss">
.action-style {
  background-color: #f7f5ee;
}
</style>
