<template>
  <div>
    <!-- 渲染 RuleUpdate 组件，并传递必要的 props -->
    <RuleUpdate
      :modelData="modelInit"
      :ruleContent="ruleContent"
      :uuid="ruleUuid"
      :ruleDrl="ruleDrl"
      :demandUuid="demandUuid"
      :eng_uuid="eng_uuid"
      :packageNameAll="ruleInfo.packageNameAll"
      :ruleName="ruleInfo.ruleName"
      :type="ruleInfo.type"
      :isTemplate="isTemplate"
      @setCascaderClose="setCascaderClose"
      ref="ruleCom"
    />
  </div>
</template>

<script setup>
// 导入必要的 API 和子组件
import { ruleModalInit, ruleDetail } from "@/api/rule_editor";
import RuleUpdate from "./ruleUpdate.vue";
import store from '@/store';

// 定义 props
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
  isTemplate: {
    type: Boolean,
    default: false,
  },
});

// 定义响应式数据
const modelInit = ref({});
const ruleContent = ref({
  actions: [],
  conditionExpression: null,
  conditions: [],
  elseActions: [],
  predefines: [],
  ruleAttributes: []
});
const ruleUuid = ref("");
const ruleDrl = ref("");
const demandUuid = ref("");
const eng_uuid = ref("");

// 获取子组件的引用
const ruleCom = ref(null);

const emit = defineEmits(["setCascaderClose"]);

// 暴露获取规则数据的方法
const getRulesData = () => {
  if (ruleCom.value) {
    return ruleCom.value.getRulesData();
  }
  return null;
};

// 将方法暴露给父组件
defineExpose({
  getRulesData
});
const setCascaderClose = () => {
  emit("setCascaderClose");
};
// 监听 ruleInfo 变化
watch(
  () => props.ruleInfo,
  (newValue) => {
    const { engUuid, uuid: newUuid, jsonContentObject } = newValue;
    getData(engUuid, newUuid,jsonContentObject);
  },
  { deep: true }
);

// 获取数据的方法
const getData = async (engUuid, uuid, jsonContentObject) => {
  try {
    // 清除缓存的文本规则
    if (uuid) {
      store.commit('setTextRule', {
        ruleUuid: uuid,
        text: '',
      });
    }
    
    if (jsonContentObject === undefined) {
      const [initResult, detailResult] = await Promise.all([
        ruleModalInit(engUuid),
        ruleDetail(uuid,props.ruleInfo.type),
      ]);
      modelInit.value = initResult.data;
      ruleContent.value = detailResult.data.ruleContent || {
        actions: [],
        conditionExpression: '',
        conditions: [],
        elseActions: [],
        predefines: [],
        ruleAttributes: []
      };
      ruleDrl.value = detailResult.data.ruleDrl;
    } else {
      const initResult = await ruleModalInit(engUuid);
      modelInit.value = initResult.data;
      // 如果传入了 jsonContentObject，使用它
      // 如果是新建规则，确保 ruleContent 有正确的结构
      ruleContent.value = jsonContentObject || {
        actions: [],
        conditionExpression: '',
        conditions: [],
        elseActions: [],
        predefines: [],
        ruleAttributes: []
      };
    }
    ruleUuid.value = uuid;
    eng_uuid.value = engUuid;
  } catch (error) {
    // 错误处理
    console.error("获取规则数据失败:", error);
    // 确保即使在出错情况下，ruleContent 也有正确的结构
    if (!ruleContent.value || Object.keys(ruleContent.value).length === 0) {
      ruleContent.value = {
        actions: [],
        conditionExpression: '',
        conditions: [],
        elseActions: [],
        predefines: [],
        ruleAttributes: []
      };
    }
  }
};

// 组件挂载时执行的逻辑
onMounted(() => {
  demandUuid.value = props.ruleInfo.demandUuid;
  const { engUuid, uuid: newUuid,jsonContentObject } = props.ruleInfo;
  eng_uuid.value = engUuid;
  getData(engUuid, newUuid,jsonContentObject);
});

// 提供数据给子组件
provide("providEngUuid", props.ruleInfo.engUuid);
provide("ruleUuid", props.ruleInfo.uuid);
</script>

<style lang="scss" scoped>
/* 这里可以添加 SCSS 样式 */
div {
  padding: 5px;
}
</style>
