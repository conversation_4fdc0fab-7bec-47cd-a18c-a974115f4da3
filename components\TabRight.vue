<!--tab通用功能，用于全部关闭或返回首个tab使用-->
<template>
    <a-tooltip title="返回">
        <a-button shape="circle" size="small" @click="backClick" v-show="showClose && editableTabsValue !== 'designView'"><template #icon><RollbackOutlined /></template></a-button>
    </a-tooltip>
    <a-tooltip title="关闭全部">
        <a-button shape="circle" size="small" @click="closeClick" v-show="showClose"><template #icon><CloseOutlined /></template></a-button>
    </a-tooltip>
</template>

<script setup>
    import {watch} from "../.nuxt/imports";

    const editableTabsValue = defineModel('editableTabsValue');
    const editableTabs = defineModel('editableTabs');
    const detailTabLists = defineModel('detailTabLists');
    //判断关闭全部显示隐藏
    const showClose = defineModel('showClose');
    
    // 接收ruleBaseId作为prop
    const props = defineProps({
        ruleBaseId: {
            type: String,
            default: ''
        }
    });
    
    const modal = inject('modal')
    const route = useRoute();
    const router = useRouter();

    //返回列表
    const backClick = () => {
        editableTabsValue.value = 'designView'
    }

    // 关闭全部
    const closeClick = () => {
        modal.confirm({
            title: '确定关闭全部标签页么？',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                // 设置活动标签页为设计视图
                editableTabsValue.value = 'designView'
                
                // 清空规则标签列表 (ruleTabLists)
                editableTabs.value = [];
                
                // 清空详情标签列表
                detailTabLists.value = [];
                
                // 获取ruleBaseId前缀
                const ruleBasePrefix = props.ruleBaseId ? `${props.ruleBaseId}_` : '';
                
                // 清除本地存储
                localStorage.removeItem(`${ruleBasePrefix}ruleTabLists`);
                localStorage.removeItem(`${ruleBasePrefix}detailTabLists`);

                // 清理URL参数
                const currentUrlUuid = route.query.ruleUuid;
                if (currentUrlUuid) {
                    // 创建一个新的query对象，不包含与标签页相关的参数
                    const newQuery = { ...route.query };
                    delete newQuery.ruleUuid;
                    delete newQuery.activeTab;
                    delete newQuery.tabType;

                    // 更新路由，但不触发新的导航
                    router.replace({ query: newQuery });
                }
            }
        })
    }
</script>
