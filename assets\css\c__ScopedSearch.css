.index-module_popMenu_aOUBY {
    border-radius: 12px 12px 0px 0px;
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j {
    background-color: var(--yq-bg-tertiary);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    color: var(--yq-text-primary);
    user-select: none;
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_item_-0ClC {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0px;
    border-bottom: 1px solid var(--yq-border-primary);
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_cancel_i36-H {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0px;
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 8px;
}

.NotificationItem-module_content_XeuM0 {
    margin-left: 12px;
    font-size: 14px;
    overflow-wrap: break-word;
    color: var(--yq-text-body);
    overflow: hidden;
    width: 100%;
}

.NotificationItem-module_content_XeuM0 > time {
    font-size: 12px;
    color: var(--yq-text-caption);
}

.NotificationItem-module_content_XeuM0 > p {
    margin-bottom: 0px;
    max-width: 534px;
}

.NotificationItem-module_content_XeuM0 > p .ant-btn {
    position: absolute;
    right: 16px;
    top: 20px;
}

.NotificationItem-module_mobile_77e-m {
    color: var(--yq-text-caption);
}

.NotificationItem-module_item_T8nNI {
    display: flex;
    background-color: var(--yq-bg-primary);
    transition: background-color 0.3s;
    border-top: 1px solid var(--yq-border-light);
    word-break: break-word;
    position: relative;
    padding: 16px 12px;
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m {
    padding: 12px;
    display: flex;
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m .NotificationItem-module_content_XeuM0 {
    margin-left: 13px;
    font-size: 15px;
    line-height: 21px;
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m .NotificationItem-module_content_XeuM0 > time {
    font-size: 13px;
    line-height: 18px;
    color: var(--yq-text-caption);
}

.NotificationItem-module_item_T8nNI:hover {
    background-color: var(--yq-bg-secondary);
}

.notification-list .ant-list-item:first-child .NotificationItem-module_item_T8nNI {
    border-top: none;
}

.NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0, a:visited .NotificationItem-module_content_XeuM0 {
    color: var(--yq-text-caption);
}

.NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0 .NotificationItem-module_actor_n5\+y-, .NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0 .NotificationItem-module_subject_DpAt3, a:visited .NotificationItem-module_content_XeuM0 .NotificationItem-module_actor_n5\+y-, a:visited .NotificationItem-module_content_XeuM0 .NotificationItem-module_subject_DpAt3 {
    color: var(--yq-text-body);
}

.NotificationItem-module_itemDeleted_d2M2z .NotificationItem-module_content_XeuM0 {
    color: var(--yq-text-caption);
}

.NotificationItem-module_itemSystem_5zHlV {
    overflow: hidden;
}

.NotificationItem-module_gallary_Q6-bs {
    margin-top: 8px;
    display: flex;
}

.NotificationItem-module_gallary_Q6-bs .NotificationItem-module_artboard_ln6q3 {
    margin-right: 8px;
    width: 144px;
    height: 104px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px;
}

.NotificationItem-module_gallary_Q6-bs .NotificationItem-module_artboard_ln6q3 > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.NotificationItem-module_asset_Yvf7u {
    display: inline-block;
    border: 1px solid var(--yq-border-primary);
    padding: 10px 16px;
    border-radius: 4px;
}

.NotificationItem-module_asset_Yvf7u .NotificationItem-module_title_9tpK8 {
    color: var(--yq-text-primary);
}

.NotificationItem-module_asset_Yvf7u .larkicon-svg-asset {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.NotificationItem-module_resource_IE846 {
    margin-top: 8px;
    display: flex;
    align-items: center;
}

.NotificationItem-module_resource_IE846 > svg {
    margin-right: 8px;
}

.NotificationItem-module_listAvatar_i95DX {
    width: 32px;
    min-width: 32px;
    height: 32px;
    border-radius: 16px;
}

.NotificationItem-module_transferGroupList_oc7BE {
    display: flex;
}

.NotificationItem-module_transferGroupList_oc7BE .NotificationItem-module_group_nPupO {
    margin-right: 16px;
    color: var(--yq-text-primary);
}

.NotificationItem-module_transferGroupList_oc7BE .NotificationItem-module_groupName_8YrGV {
    margin-left: 4px;
}

.context-module_actor_n76K5 {
    color: var(--yq-text-primary);
    font-weight: 600;
}

.context-module_actor_n76K5:hover {
    color: var(--yq-text-body);
}

.context-module_subject_0TOZC {
    color: var(--yq-text-primary);
    font-weight: 400;
}

.context-module_subject_0TOZC:hover {
    color: var(--yq-text-body);
}

.context-module_actions_b-TCi {
    margin-left: 16px;
    float: right;
    background: none;
    margin-top: 5px;
}

.context-module_upgrade_odBAm {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 8px;
}

.m-notifications .context-module_actions_b-TCi {
    display: none;
}

.index-module_searchModalWrapper_vtDAq .ant-modal-content {
    border-radius: 12px;
}

.index-module_searchModalWrapper_vtDAq .ant-modal-close {
    display: none;
}

.index-module_searchModalWrapper_vtDAq .search-dropdown ul {
    padding-top: 0px;
    box-shadow: none;
    border-radius: 0px 0px 10px 10px;
    border-top: 1px solid var(--yq-yuque-grey-3);
}

.index-module_searchModalWrapper_vtDAq .search-dropdown::before {
    display: none;
}

.index-module_searchModalWrapper_vtDAq .ant-modal-body {
    padding: 0px;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 8px 16px 4px;
    border-radius: 12px;
}

.index-module_searchModalWrapper_vtDAq .ant-input-affix-wrapper {
    border: 0px !important;
    box-shadow: none !important;
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 {
    padding: 15px;
    font-size: 16px;
    line-height: 24px;
    box-shadow: none;
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 input {
    color: var(--yq-text-primary);
    margin-right: 32px;
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 svg {
    font-size: 16px;
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 .ant-input {
    padding-left: 2px !important;
}

.index-module_searchInput_MvuM1 + div {
    position: relative !important;
}

.ant-popover-message-title {
    max-width: 200px;
}

.notifications-header-button {
    margin-left: 10px;
    line-height: 50px;
}

.notifications-header-button-disable {
    color: var(--yq-text-caption);
    cursor: default;
}

.notification-list .blankslate {
    padding: 32px 16px;
    text-align: center;
    color: var(--yq-text-disable);
}

.notification-list .blankslate-icon {
    margin: 0px auto 10px;
    width: 63px;
    height: 60px;
    background-image: ;
    background-position-x: ;
    background-position-y: ;
    background-repeat: ;
    background-attachment: ;
    background-origin: ;
    background-clip: ;
    background-color: ;
    background-size: 100%;
}

.notification-list.ant-list.larkui-list .ant-list-item {
    padding: 0px;
    border: none;
}

.notification-list.ant-list.larkui-list .ant-list-item:last-child {
    border-bottom: none;
}

.system-list time {
    display: inline-block;
    margin-top: 8px;
}

.card-notifications .ant-card-head .ant-card-extra {
    padding: 0px;
}

.card-notifications .ant-card-head-title .num, .card-notifications .ant-card-head-title .text {
    display: inline-block;
    vertical-align: middle;
}

.card-notifications .ant-card-head-title .text {
    color: var(--yq-text-caption);
}

.card-notifications .ant-card-head-title .text:hover {
    color: var(--yq-text-body);
}

.card-notifications .ant-card-head-title .text.active {
    color: var(--yq-text-primary);
}

.card-notifications .ant-card-head-title .readed {
    margin-left: 24px;
}

.card-notifications .ant-card-head-title .num {
    border-radius: 10px;
    margin-left: 8px;
    padding: 2px 8px;
    background-color: var(--yq-bg-tertiary);
    font-size: 12px;
    line-height: 1.2;
    font-weight: 400;
    color: var(--yq-text-body);
    min-width: 24px;
    text-align: center;
}

.mobile {
    border-top: none;
}

.mobile, .mobile .ant-card-head-title {
    color: var(--yq-text-body);
    font-size: 13px;
    line-height: 18px;
}

.mobile .ant-card-head-title {
    padding: 16px 0px 4px;
    font-weight: 400;
}

.mobile .ant-card-head {
    border-bottom: none;
    min-height: auto;
}

.mobile .ant-card .ant-card-head-title {
    font-size: 14px;
    line-height: 32px;
    color: var(--yq-text-body);
}

.Notifications-module_header_STB3- {
    background-color: var(--yq-bg-primary);
    padding: 8px 12px;
    color: var(--yq-text-body);
    font-size: 13px;
    line-height: 18px;
    display: flex;
    justify-content: space-between;
}

.Notifications-module_header_STB3- .Notifications-module_num_Gsr4A {
    margin-right: 4px;
}

.Notifications-module_header_STB3- .Notifications-module_action_JP\+m1 {
    line-height: 21px;
    color: var(--yq-text-primary);
    font-size: 15px;
    min-height: 21px;
}

.Notifications-module_list_qCvTE, .Notifications-module_list_qCvTE .Notifications-module_ant-list_Z9sHe {
    background-color: var(--yq-bg-primary);
}

.Notifications-module_popMenu_kxa\+3 .Notifications-module_tips_PBC5H {
    color: var(--yq-text-caption);
}

.Notifications-module_emptyWrapper_OnwXY {
    height: 250px;
    margin-top: 100px;
}

.Notifications-module_emptyWrapper_OnwXY p {
    line-height: 20px;
}

.Notifications-module_extraLinkWrapper_CV4OS {
    margin-right: 44px;
}

.Notifications-module_extraLinkWrapper_CV4OS a {
    color: var(--yq-yuque-grey-8);
}

.Notifications-module_extraLinkWrapper_CV4OS a:hover {
    color: var(--yq-yuque-grey-9);
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM {
    cursor: not-allowed;
    color: var(--yq-text-disable);
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM:hover {
    color: var(--yq-text-disable);
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM .Notifications-module_iconActionRead_55WNa {
    opacity: 0.4;
}

.Notifications-module_extraLinkWrapper_CV4OS svg {
    margin-right: 4px;
    position: relative;
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_iconActionDelete_PVcOx {
    margin-right: 2px;
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_iconActionRead_55WNa {
    margin-right: -1px;
}

.Notifications-module_setting_qVm6t {
    margin-left: 12px;
    color: var(--yq-text-body);
}

.Notifications-module_setting_qVm6t .Notifications-module_settingIcon_dLIc4 {
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    top: -2px;
}

.Notifications-module_mButton_Ai9DS {
    font-size: 14px;
}

.Notifications-module_mHeaderReaded_eUdT1, .Notifications-module_mHeaderUnread_SsQUk {
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    background: var(--yq-bg-tertiary);
    color: var(--yq-text-body);
    padding: 4px 10px;
    font-size: 14px;
}

.Notifications-module_mHeaderReaded_eUdT1 .text, .Notifications-module_mHeaderUnread_SsQUk .text {
    color: var(--yq-text-body);
}

.Notifications-module_mHeaderUnread_SsQUk {
    margin-right: 6px;
}

.Notifications-module_mHeaderUnread_SsQUk .num {
    color: var(--yq-text-body);
    display: inline-block;
    margin-left: 4px;
}

.Notifications-module_mHeaderActive_nyNrW {
    color: var(--yq-text-link);
    background: var(--yq-bg-tertiary);
}

.Notifications-module_mHeaderActive_nyNrW .text {
    color: var(--yq-text-link);
}

.Notifications-module_webappChannel_ZA4Sr {
    padding-top: 15px;
}

.Notifications-module_selectType_sfCA8 {
    color: var(--yq-yuque-grey-8);
    cursor: pointer;
}

.Notifications-module_selectType_sfCA8.Notifications-module_active_Azaid {
    font-weight: 600;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
}

.Notifications-module_leftSelect_j4iaj {
    margin-right: 19px;
}

.card-notifications .ant-card-head {
    padding: 0px;
    border: 0px;
}

.card-notifications .ant-card-body .ant-list {
    max-height: 558px;
    height: 558px;
    padding-right: 18px;
    border-radius: 0px;
}

.card-notifications .ant-card-body .ant-list .ant-list-items {
    width: 724px;
}

.lark-nav {
    border: 1px solid var(--yq-border-light);
}

.lark-nav.ant-menu-vertical > .ant-menu-item {
    margin-left: 0px;
    padding-left: 0px;
    border-bottom: 1px solid var(--yq-border-light);
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-900);
    margin-bottom: 0px;
}

.lark-nav.ant-menu-vertical > .ant-menu-item:last-child {
    border-bottom: 0px;
}

.lark-nav.ant-menu-vertical > .ant-menu-item > a {
    display: block;
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-900);
    padding-left: 24px;
    border-left: 2px solid transparent;
}

.lark-nav.ant-menu-vertical > .ant-menu-item > a:hover {
    color: var(--yq-ant-text-color-secondary);
}

.lark-nav.ant-menu-vertical > .ant-menu-item > a.active {
    border-left-color: var(--yq-yuque-green-600);
    font-weight: 500;
    color: var(--yq-ant-heading-color);
}

.lark-nav.ant-menu-vertical > .ant-menu-item > a.active:hover {
    color: var(--yq-ant-heading-color);
}

.lark-nav.ant-menu-vertical.ant-menu > .ant-menu-item-active, .lark-nav.ant-menu-vertical.ant-menu > .ant-menu-item-selected {
    background: transparent;
}

.lark-nav.ant-menu-vertical > .ant-menu-item-selected > a {
    border-left-color: var(--yq-yuque-green-600);
    font-weight: 500;
    color: var(--yq-ant-heading-color);
}

.lark-nav.ant-menu-vertical > .ant-menu-item-selected > a:hover {
    color: var(--yq-ant-heading-color);
}

.lark-nav.ant-menu-horizontal {
    border: 0px;
    margin-left: -10px;
    margin-right: -10px;
}

.lark-nav.ant-menu-horizontal > .ant-menu-item {
    border-bottom: 0px;
    padding: 0px;
    margin: 0px 10px;
}

.lark-nav.ant-menu-horizontal > .ant-menu-item.ant-menu-item-active, .lark-nav.ant-menu-horizontal > .ant-menu-item:hover {
    border: 0px;
}

.lark-nav.ant-menu-horizontal > .ant-menu-item > a {
    display: block;
    padding: 0px 16px;
    border: none;
    line-height: 48px;
    color: var(--yq-yuque-grey-900);
}

.lark-nav.ant-menu-horizontal > .ant-menu-item > a:hover {
    color: var(--yq-ant-text-color-secondary);
}

.lark-nav.ant-menu-horizontal > .ant-menu-item > a.active {
    font-weight: 500;
    border-bottom-color: var(--yq-yuque-grey-9);
    color: var(--yq-ant-heading-color);
}

.lark-nav.ant-menu-horizontal > .ant-menu-item-selected > a {
    border-bottom-color: var(--yq-yuque-grey-9);
}

@media only screen and (max-width: 575px) {
    .lark-nav.ant-menu-horizontal > .ant-menu-item {
        margin: 0px;
    }
}

.Nav-module_nav_iFpRT {
    float: right;
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 10px;
    line-height: 21px;
}

.Nav-module_dot_w2N16 {
    display: inline-block;
    vertical-align: middle;
    width: 5px;
    height: 5px;
    background-color: var(--yq-yuque-green-6);
    border-radius: 100%;
    box-shadow: 0 0 0 1px var(--yq-white);
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav::before {
    border-bottom: 0px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-content-left {
    border-radius: 10px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0px;
}

.Nav-module_tabsWrapper_KmVeN .ant-card-head {
    padding-left: 26px;
    padding-right: 4px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-tab {
    padding: 0px 8px;
    height: 32px;
    border-radius: 6px;
    min-width: 132px;
    margin-right: 12px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-tab.ant-tabs-tab-active, .Nav-module_tabsWrapper_KmVeN .ant-tabs-tab:hover {
    background-color: var(--yq-bg-primary-hover);
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-ink-bar {
    display: none;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav-list {
    margin-top: 64px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav-wrap .ant-tabs-nav-list .ant-tabs-tab + .ant-tabs-tab {
    margin: 8px 12px 0px 0px;
    padding: 8px 12px;
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav {
    height: 610px;
}

.Nav-module_notificationsWrapper_Uflnw {
    min-height: 400px;
}

.Nav-module_tabTitle_d9DlL {
    display: flex;
    justify-content: space-between;
    width: 108px;
}

.Nav-module_tabTitle_d9DlL span {
    display: block;
    cursor: pointer;
}

.Nav-module_setting_M8Bx8 {
    margin-left: 12px;
    margin-bottom: 14px;
    position: absolute;
    bottom: 4px;
    color: var(--yq-yuque-grey-8);
}

.Nav-module_setting_M8Bx8 svg {
    font-size: 16px;
}

.Nav-module_setting_M8Bx8:hover {
    color: var(--yq-yuque-grey-9);
}

.index-module_wrapper_pEEnH .ant-modal-body {
    padding: 0px 0px 0px 12px;
}

.index-module_wrapper_pEEnH .ant-modal-body .ant-tabs-content {
    max-height: 610px;
    overflow-y: auto;
}

.index-module_wrapper_pEEnH .ant-modal-header {
    position: absolute;
}

.index-module_wrapper_pEEnH .ant-modal-close-x {
    line-height: 51px;
}

.index-module_notifications_hZhLx {
    cursor: pointer;
    margin-right: 3px;
    text-align: center;
    padding: 10px 5px 5px 11px;
    height: 28px;
    width: 28px;
    border-radius: 6px;
    display: block;
}

.index-module_notifications_hZhLx .index-module_bell_KxNIQ {
    display: inline-block;
    position: relative;
    right: 6px;
    color: var(--yq-yuque-grey-9);
    font-size: 16px;
    top: -3px;
}

.index-module_notifications_hZhLx .index-module_countBell_-bMDE .ant-scroll-number {
    background: var(--yq-blue-6);
    height: 14px;
    line-height: 14px;
    padding: 0px 3px;
}

.index-module_notifications_hZhLx:hover {
    background-color: var(--yq-bg-primary-hover);
}

.index-module_notifications_hZhLx:hover a {
    color: var(--yq-text-primary);
}

.index-module_notifications_hZhLx.index-module_showCountBell_0JA2J {
    left: 0px;
}

.index-module_notifications_hZhLx .ant-badge {
    display: block;
}

.index-module_notifications_hZhLx .ant-badge-dot {
    background: var(--yq-red-4);
    top: 0px;
    right: 4px;
}

.index-module_notifications_hZhLx .larkicon-notification {
    font-size: 16px;
}

.index-module_notifications_hZhLx .ant-scroll-number {
    background: var(--yq-blue-6);
    color: var(--yq-white);
}

.popover-notifications.ant-popover .ant-menu .notifications-menu-item {
    height: 40px;
    line-height: 40px;
    margin-bottom: 0px;
    padding-left: 10px;
    padding-right: 10px;
    position: relative;
}

.sidebar-notifications {
    height: 40px;
    line-height: 40px;
    margin-bottom: 0px;
    position: relative;
}

.index-module_notificationMenuBell_tUL61 .ant-badge {
    position: absolute;
    right: 30px;
    top: 50%;
    margin-top: -3px;
}

.index-module_notificationMenuBell_tUL61 .ant-badge-dot {
    background: var(--yq-theme);
    top: 0px;
    right: 4px;
}

.index-module_menu_rIOsP {
    width: 250px;
    max-height: 410px;
    overflow-y: auto;
}

.index-module_menuName_Qh\+-p {
    display: inline-block;
    margin-left: 6px;
    vertical-align: middle;
    max-width: 136px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.index-module_menuUnread_zwOuN {
    float: right;
    background-color: var(--yq-bg-primary-hover);
    font-size: 12px;
    color: var(--yq-text-primary);
    border-radius: 11px;
    height: 22px;
    line-height: 14px;
    padding: 4px 8px;
    font-weight: 600;
    margin-top: 10px;
    transition: background-color 1s;
}

.notifications-menu-item.ant-menu-item-active .index-module_menuUnread_zwOuN {
    background-color: var(--yq-bg-primary);
}

.index-module_sidebarNotifications_tBF-x {
    margin-left: 4px;
    margin-top: -8px;
    text-align: center;
    padding: 9px 5px 5px 13px;
    height: 32px;
    width: 32px;
    border-radius: 6px;
    display: block;
}

.index-module_sidebarNotifications_tBF-x .index-module_bell_KxNIQ {
    font-size: 18px;
    top: -2px;
    color: var(--yq-yuque-grey-9);
}

.index-module_sidebarNotifications_tBF-x:hover {
    background-color: var(--yq-bg-primary-hover);
}

.index-module_sidebarNotifications_tBF-x:hover a {
    color: var(--yq-text-primary);
}

.index-module_sidebarNotifications_tBF-x.index-module_showCountBell_0JA2J {
    left: 0px;
}

.SearchHistory-module_container_9eGYG {
    background: var(--yq-bg-primary);
    padding: 12px 0px 16px;
}

.SearchHistory-module_container_9eGYG.SearchHistory-module_pc_XsAvr {
    border: 1px solid var(--yq-border-primary);
    box-shadow: rgba(0, 0, 0, 0.13) 0px 1px 4px -2px, rgba(0, 0, 0, 0.08) 0px 2px 8px 0px, rgba(0, 0, 0, 0.04) 0px 8px 16px 4px;
    border-radius: 4px;
    width: 290px;
}

.SearchHistory-module_container_9eGYG.SearchHistory-module_pc_XsAvr .SearchHistory-module_list_68hvx {
    margin-top: 4px;
}

.SearchHistory-module_container_9eGYG.SearchHistory-module_pc_XsAvr .SearchHistory-module_list_68hvx .SearchHistory-module_item_0xP6c:hover {
    background: var(--yq-bg-tertiary);
}

.SearchHistory-module_container_9eGYG.SearchHistory-module_searchHistoryModal_hiZEO {
    border-right: 0px;
    border-bottom: 0px;
    border-left: 0px;
    border-image: initial;
    box-shadow: none;
    width: 600px;
    border-radius: 0px 0px 12px 12px;
    border-top: 1px solid var(--yq-yuque-grey-3);
}

.SearchHistory-module_container_9eGYG .SearchHistory-module_header_ydI8l {
    padding: 0px 16px;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 20px;
    display: flex;
    justify-content: space-between;
}

.SearchHistory-module_container_9eGYG .SearchHistory-module_header_ydI8l .SearchHistory-module_delete_JBaOp {
    cursor: pointer;
    display: flex;
    align-items: center;
}

.SearchHistory-module_container_9eGYG .SearchHistory-module_header_ydI8l .SearchHistory-module_delete_JBaOp > span {
    display: block;
}

.SearchHistory-module_container_9eGYG .SearchHistory-module_list_68hvx {
    margin-top: 4px;
}

.SearchHistory-module_container_9eGYG .SearchHistory-module_list_68hvx .SearchHistory-module_item_0xP6c {
    font-size: 14px;
    padding: 8px 16px;
    color: var(--yq-text-body);
    line-height: 22px;
    cursor: pointer;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.Search-module_wrapper_Scz\+2 {
    display: flex;
    flex-flow: column;
    justify-content: center;
    font-size: 14px;
    color: var(--yq-text-caption);
}

.Search-module_wrapper_Scz\+2 .Search-module_img_CAujI {
    margin: 0px auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50% center;
    background-repeat: no-repeat;
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0px !important;
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none !important;
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0px !important;
}

.styles-module_selector_Z3\+Wx {
    padding: 24px;
    position: relative;
}

.styles-module_title_Misjv {
    font-size: 16px;
    line-height: 24px;
}

.index-module_wrap_iKZPE {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body);
}

.index-module_wrap_iKZPE:hover .index-module_close_mZbMN {
    display: flex;
}

.index-module_icon_gxtpV {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: 0 0 auto;
}

.index-module_close_mZbMN {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px;
}

.index-module_closeIcon_kM1zW {
    font-size: 16px;
}

.doc-draft-tip {
    font-weight: 400;
}

.doc-draft-tip-content .update-info {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px;
}

.doc-draft-tip-content .update-info a {
    color: var(--yq-text-body);
}

.ant-tag.doc-template-tag {
    margin: 0px 0px 0px 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px;
}

.doc-title {
    font-size: 14px;
    line-height: 21px;
    text-overflow: ellipsis;
    color: var(--yq-text-body);
    font-family: "Chinese Quote", "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji";
}

.doc-title-draft {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 4px;
    font-weight: 400;
}

.doc-icon {
    margin-right: 4px;
}

.doc-access-scope {
    margin-left: 8px;
}

.doc-belong, .doc-belong a {
    color: var(--yq-text-caption);
}

.doc-belong a {
    margin: 0px 4px;
}

.doc-belong a:first-child {
    margin-left: 0px;
}

.doc-contributors, .doc-contributors span a {
    color: var(--yq-text-caption);
}

.index-module_articleTitle_VJTLJ {
    word-break: break-word;
}

.index-module_popover_nfMC3 {
    display: inline;
}

.index-module_belongMenu_2QmLB {
    outline: none;
    cursor: pointer;
}

.index-module_belongMenu_2QmLB .larkui-icon {
    display: inline-block;
    font-size: 12px;
    margin-left: 4px;
}

.index-module_belongText_TkCAl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu {
    min-width: 188px;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item {
    display: flex;
    align-items: center;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .larkui-icon-check-outlined {
    margin-right: 6px;
    visibility: hidden;
    font-size: 16px;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 280px;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q > .larkui-icon, .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q > span {
    display: inline-block;
    vertical-align: middle;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active, .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item:hover {
    background-color: var(--yq-bg-tertiary);
    font-weight: 400;
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active .larkui-icon-check-outlined {
    visibility: visible;
}

@media only screen and (max-width: 575px) {
    .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
        max-width: 140px;
    }
}

.index-module_privacy_QkaFB {
    display: inline-block;
    padding: 3px 5px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    color: var(--yq-text-body);
    line-height: 14px;
    font-size: 10px;
    font-weight: 400;
    width: fit-content;
    white-space: nowrap;
    margin: 0px 6px;
}

.GlobalDocCreate-module_menuItemContainer_\+DgHG {
    display: flex;
    align-items: center;
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu {
    padding: 8px 0px;
    min-width: 110px;
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu .ant-menu-item {
    height: 32px;
    line-height: 32px;
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-popover-inner-content {
    padding: 0px;
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-popover-inner-content {
    padding: 8px;
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-menu-item {
    border-radius: 8px;
}

.GlobalDocCreate-module_iconContainer_eX6jK {
    margin-right: 12px;
}

.GlobalDocCreate-module_iconBeta_hDUuJ {
    background-color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 3px;
    color: var(--yq-white);
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    text-align: center;
    border-radius: 8px;
    padding: 0px 5px;
    position: absolute;
    top: 6px;
    left: 50%;
}

.GlobalDocCreate-module_menuList_s1r7J {
    display: flex;
    justify-content: space-between;
}

.GlobalDocCreate-module_menuListItem_hC68r {
    text-align: center;
    cursor: pointer;
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    background: none !important;
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE > svg {
    margin-right: 8px;
}

.GlobalDocCreate-module_desc_6gQhM {
    color: var(--yq-text-caption);
    margin-left: 16px;
}

.JumpMenu-module_title_XgpJ5 {
    color: var(--yq-yuque-grey-700);
    cursor: default;
    margin-bottom: -2px;
}

.JumpMenu-module_title_XgpJ5:focus, .JumpMenu-module_title_XgpJ5:hover {
    background-color: transparent;
}

.JumpMenu-module_menu_m-ETp {
    outline: none !important;
    box-shadow: none !important;
}

.JumpMenu-module_noSelected_ITXT- {
    cursor: default;
    outline: none;
    background-color: transparent !important;
}

.JumpMenu-module_jumpIconButton_PYy2Z {
    color: var(--yq-yuque-grey-900);
    width: 18px;
    height: 18px;
    margin-right: 16px;
}

.JumpMenu-module_jumpIconButton_PYy2Z .ant-spin-dot {
    width: 18px !important;
    height: 18px !important;
}

.JumpMenu-module_jumpScope_bJVyA {
    color: var(--yq-yuque-grey-800);
    font-size: 14px;
    cursor: default;
}

.JumpMenu-module_jumpScope_bJVyA em {
    display: inline-block;
    vertical-align: top;
    font-style: normal;
    margin: 0px 6px;
    position: relative;
    top: -1px;
    color: var(--yq-yuque-grey-600);
}

.JumpMenu-module_jumpMenu_Fr5h9 .JumpMenu-module_jumpMenuList_OJGJg {
    background-color: var(--yq-bg-primary);
}

.JumpMenu-module_jumpMenu_Fr5h9 .JumpMenu-module_jumpMenuList_OJGJg.JumpMenu-module_jumpMenuListDark_Mjlei {
    background-color: var(--yq-bg-secondary);
}

.JumpMenu-module_jumpMenu_Fr5h9 .JumpMenu-module_jumpMenuList_OJGJg li.JumpMenu-module_menu_m-ETp {
    height: 32px;
    line-height: 22px;
    margin: 4px 8px;
    padding: 5px 8px;
    border-radius: 6px;
    outline: none;
}

.JumpMenu-module_jumpMenu_Fr5h9 .JumpMenu-module_jumpMenuList_OJGJg li.JumpMenu-module_menu_m-ETp.JumpMenu-module_searchContent_bTQ4J {
    height: 56px;
}

.JumpMenu-module_jumpMenu_Fr5h9 .JumpMenu-module_jumpMenuList_OJGJg li.ant-menu-item-divider {
    margin: 8px 0px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO {
    display: flex;
    align-items: center;
    position: relative;
    flex-wrap: wrap;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemLabelText_oCOLn {
    color: var(--yq-yuque-grey-800);
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemLabel_OPbMQ {
    color: var(--yq-yuque-grey-800);
    width: 525px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemLabel_OPbMQ em {
    display: inline-block;
    vertical-align: middle;
    margin: 0px 4px;
    font-weight: 700;
    font-style: normal;
    color: var(--yq-yuque-grey-800);
    position: relative;
    top: -1px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemLabel_OPbMQ .JumpMenu-module_vipComponent_kIAME {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    left: 8px;
    top: -2px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemAbstract_h6CeZ {
    display: block;
    width: 100%;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemAbstract_h6CeZ span {
    width: 525px;
    color: var(--yq-yuque-grey-700);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 30px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemAbstract_h6CeZ span em {
    display: inline-block;
    vertical-align: middle;
    margin: 0px 4px;
    font-weight: 700;
    font-style: normal;
    color: var(--yq-yuque-grey-900);
    position: relative;
    top: -1px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemHasFirstEm_pAZhQ em {
    margin-left: 0px !important;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemIcon_bOWlR {
    margin-right: 10px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_groupIcon_pOoFp {
    border-radius: 4px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemKey_jwWab, .JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemTime_inSO2 {
    position: absolute;
    right: 4px;
    top: 0px;
    color: var(--yq-yuque-grey-700);
    font-size: 14px;
    cursor: default;
    display: none;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemKey_jwWab em, .JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemTime_inSO2 em {
    display: inline-block;
    vertical-align: top;
    font-style: normal;
    margin: 0px 6px;
    position: relative;
    top: 3px;
    border: 1px solid var(--yq-yuque-grey-500);
    color: var(--yq-yuque-grey-700);
    border-radius: 4px;
    width: 18px;
    height: 18px;
    line-height: 18px;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemKey_jwWab em svg, .JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemTime_inSO2 em svg {
    position: relative;
    top: 1px;
    color: var(--yq-yuque-grey-700);
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemTime_inSO2 {
    display: block;
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_upgrade_MONgw {
    position: absolute;
    right: -14px;
    font-size: 12px;
    color: var(--yq-yuque-grey-700);
}

.JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_upgrade_MONgw a {
    font-size: 12px;
    margin-left: -10px;
    color: var(--yq-ant-link-color);
}

.JumpMenu-module_menuActive_o-LPE .JumpMenu-module_jumpItemWrapper_vfjfO .JumpMenu-module_jumpItemKey_jwWab {
    display: block;
}

.JumpMenu-module_emptyWrapper_USemz {
    padding: 48px 0px 78px;
    text-align: center;
    color: var(--yq-yuque-grey-600);
    border-top: 1px solid var(--yq-yuque-grey-400);
}

.index-module_wrapper_s12qp {
    width: 200px;
    transition: width 0.1s ease-in-out;
}

.index-module_wrapper_s12qp.index-module_mini_wf4zr {
    width: 290px;
    transition: null;
}

.index-module_wrapper_s12qp.index-module_focus_uZVhr {
    width: 290px;
}

.ant-dropdown-menu .index-module_option_RE4I8 {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 12px 32px;
}

.ant-dropdown-menu .index-module_option_RE4I8.index-module_mobile_EbUYP {
    width: 100%;
}

.index-module_optionText_3\+q2f {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.index-module_optionScope_KR\+io {
    color: var(--yq-text-caption);
    display: flex;
    align-items: center;
}

.index-module_optionScope_KR\+io .icon-svg {
    margin-right: 8px;
}

.index-module_ant-input-affix-wrapper_ba\+dX {
    border-color: var(--yq-border-primary);
    cursor: default !important;
}

.index-module_ant-input-affix-wrapper_ba\+dX.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme);
}

.index-module_ant-input-affix-wrapper_ba\+dX.ant-dropdown-trigger.ant-input-affix-wrapper .ant-input-prefix .larkui-icon-help-search {
    font-size: 14px;
    color: var(--yq-text-body);
}

.index-module_ant-input-affix-wrapper_ba\+dX.index-module_mobile_EbUYP {
    border-radius: 32px;
}

.index-module_pageSearch_AvXFY .ant-input-affix-wrapper {
    width: 380px;
    height: 40px;
    line-height: 40px;
}

.index-module_pageSearch_AvXFY .search-history {
    width: 100%;
}

.index-module_input_U7ZlY.index-module_themeGrey_Ua9SX .ant-input {
    background: none;
}

.index-module_input_U7ZlY.index-module_themeGrey_Ua9SX .ant-input-prefix .larkui-icon-help-search {
    color: var(--yq-text-caption);
}

.index-module_input_U7ZlY.index-module_themeGrey_Ua9SX .index-module_dropdown_SVXx9 {
    width: 100%;
}

.index-module_dropdown_SVXx9 {
    width: 290px;
}

.index-module_dropdown_SVXx9 .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-primary-hover);
}

.index-module_inputBoxWrapper_Le-8d {
    position: relative;
}

.index-module_inputBoxWrapper_Le-8d .index-module_inputBoxIcon_EezCf {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    position: absolute;
    right: 0px;
    top: 1px;
    z-index: 1;
    cursor: pointer;
}

.index-module_inputBoxWrapper_Le-8d .index-module_inputBoxIcon_EezCf:hover {
    background-color: var(--yq-bg-primary-hover);
}

.index-module_inputBoxWrapper_Le-8d .index-module_searchIcon_jJone {
    pointer-events: none;
}

.index-module_inputBoxWrapper_Le-8d .index-module_inputBox_IgU21 {
    width: 0px;
    opacity: 0;
    overflow: hidden;
    float: right;
}

.index-module_inputBoxWrapperActive_bvbyh .index-module_inputBoxIcon_EezCf {
    left: -9999px;
}

.index-module_inputBoxWrapperActive_bvbyh .index-module_inputBox_IgU21 {
    opacity: 1;
    width: 100%;
}

.header-search {
    background: none;
}

.ant-input-prefix .larkui-icon-help-search {
    color: var(--yq-text-caption);
}

.index-module_modalSearch_ypoIP {
    overflow: hidden;
}

.index-module_modalSearch_ypoIP .index-module_optionText_3\+q2f {
    max-width: 400px;
}

.index-module_modalSearch_ypoIP .ant-input-affix-wrapper:focus {
    box-shadow: none;
}

.index-module_modalSearch_ypoIP .ant-input {
    font-size: 14px;
}

.index-module_modalSearch_ypoIP .ant-input-suffix {
    position: absolute;
    right: 16px;
    top: 19px;
}

.index-module_modalSearch_ypoIP .index-module_dropdown_SVXx9 {
    max-height: calc(-220px + 100vh);
    transition: height 0.1s;
    overflow: auto;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    top: 0px !important;
}

.index-module_jumpInput_FRqFC {
    font-size: 14px;
}

.index-module_inputKeys_\+w5se {
    color: var(--yq-yuque-grey-600);
    font-size: 14px;
    cursor: default;
}

.index-module_inputKeys_\+w5se em {
    font-size: 12px;
    display: inline-block;
    vertical-align: baseline;
    border: 1px solid var(--yq-yuque-grey-400);
    background-color: var(--yq-yuque-grey-100);
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    font-style: normal;
    margin-right: 8px;
    border-radius: 4px;
}

.index-module_inputKeys_\+w5se em svg {
    position: relative;
    top: 1px;
}

.index-module_inputKeys_\+w5se .index-module_upArrow_eoqSB {
    transform: rotate(180deg);
}
