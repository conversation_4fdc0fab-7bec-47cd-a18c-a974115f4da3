.ReaderMeta-module_wrapper_GsEmw {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 24px
}

.ReaderMeta-module_wrapper_GsEmw .ReaderMeta-module_readCount_y6mlm {
    padding: 0 4px;
    border-radius: 6px
}

.ReaderMeta-module_wrapper_GsEmw .ReaderMeta-module_readCount_y6mlm.ReaderMeta-module_canHover_t0DQC:hover {
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover)
}

.ReaderMeta-module_wrapper_GsEmw .meta-pages {
    border-top: 1px dashed var(--yq-yuque-grey-5);
    padding: 18px 0 0 0
}

.ReaderMeta-module_wrapper_GsEmw .meta-next {
    text-align: right
}

.ReaderMeta-module_wrapper_GsEmw .meta-next-title {
    display: block;
    color: var(--yq-text-caption);
    text-align: right
}

.ReaderMeta-module_wrapper_GsEmw .meta-next-link {
    width: 180px;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: var(--yq-text-body)
}

.ReaderMeta-module_wrapper_GsEmw .meta-next-link:hover {
    color: var(--yq-text-caption)
}

.ReaderMeta-module_wrapper_GsEmw .meta-item {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
    max-width: 240px;
    color: var(--yq-text-caption);
    cursor: default
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .larkicon {
    color: var(--yq-text-caption)
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .author {
    margin-right: 8px
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .item {
    color: var(--yq-text-caption)
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .item-text {
    padding-left: 8px
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .item-text a {
    color: var(--yq-text-caption)
}

.ReaderMeta-module_wrapper_GsEmw .meta-item .item-text a:hover {
    color: var(--yq-text-link)
}

.ReaderMeta-module_wrapper_GsEmw .meta-left .meta-item+.meta-item,.ReaderMeta-module_wrapper_GsEmw .meta-right .meta-item {
    margin-left: 18px
}

.ReaderMeta-module_wrapper_GsEmw .meta-contributors {
    float: left;
    width: 32px;
    height: 32px;
    display: block;
    margin-right: 10px
}

.ReaderMeta-module_wrapper_GsEmw .meta-contributors a {
    display: inline-block;
    width: 32px;
    height: 32px
}

.ReaderMeta-module_wrapper_GsEmw .meta-contributors a img {
    width: 32px;
    height: 32px;
    border-radius: 32px
}

.ReaderMeta-module_wrapper_GsEmw .ReaderMeta-module_reportBtn_aC9km {
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    align-items: center
}

.ReaderMeta-module_wrapper_GsEmw .ReaderMeta-module_reportBtn_aC9km:before {
    content: " ";
    display: block;
    width: 1px;
    height: 12px;
    background-color: var(--yq-yuque-grey-500);
    margin-right: 12px
}

.ReaderMeta-module_viewBtn_Vo9dK {
    margin-left: 4px
}

.ReaderMeta-module_readers_LNdps {
    margin-top: 2px;
    font-size: 12px;
    line-height: 18px;
    word-break: break-all;
    display: -webkit-box;
    max-height: 34px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 17px
}

@media only print {
    .ReaderMeta-module_wrapper_GsEmw {
        display: none
    }
}
