<script lang="jsx">
import store from "@/store";
import DesignView from "./com/designView.vue";
import { cloneDeep } from "lodash";
import * as util from "@/components/ruleEditCom/utils/util";
import ReplaceItem from "@/components/ruleEditCom/ruleItemList/operationMenu/replaceItem.vue";
import { mxReplace } from "@/components/ruleEditCom/utils/ruleReplace";

const { ConditionGenerate, ActionGenerate } = util;

const initRuleAttribute = [
  { enabled: "true" },
  { "lock-on-active": "false" },
  { salience: "0" },
];
const initVariable = {
  variableType: "field",
  valueType: "Object",
  value: [],
};
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};
const initAction = {
  actionType: "invokeMethod",
  actionParams: [
    {
      variableType: "field",
      valueType: "String",
      viewName: "",
      value: [],
    },
  ],
};
const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
  actions: [initAction],
};

export default {
  name: "RuleEditor",
  mixins: [mxReplace],
  inject: {
    ruleUuid: { default: "" },
  },
  props: {
    operatorTypeList: {
      type: Object,
    },
    fieldsDataList: {
      type: Array,
    },
    dataEntryList: {
      type: Array,
    },
    predefineList: {
      type: Array,
    },
    baseMethodListMap: {
      type: Object,
    },
    methodsDataList: {
      type: Array,
    },
    mode: String,
    locked: {
      type: Boolean,
      default: true,
    },
    theme: String,
    isTrack: Boolean,
    ruleNamesBeHited: Boolean,
    ruleData: {
      type: Object,
    },
    ruleDrls: String,
    mode: String,
    uuid: String,
    validateResult: {
      type: Object,
      default: () => {},
    },
    initModelData: {
      type: Object,
      default: () => {},
    },
    injectModelData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ruleAttributes: {},
      currentDataIndex: 0,
      // locked: true,
      // lockedInner: props.mode === 'create',
      self_validateResult: {},
      lockedInner: true,
      viewStatus: "designView",
      ruleDrl: null,
      ruleText: null,
      conditionId: null,
      hisDataUpdateTimes: 0,
      ruleDrlUpdateTimes: 0,
      initRuleAttribute: [
        { enabled: "true" },
        { "lock-on-active": "false" },
        { salience: "0" },
      ],
      editData: {},
      ruleStore: [],
      updateD: true,
    };
  },
  watch: {
    validateResult: {
      handler: function (newVal, oldVal) {
        this.self_validateResult = newVal;
      },
      deep: true,
    },
    ruleData: {
      deep: true,
      handler: function () {
        this.init();
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    isPreCondition(){
      return false
    },
    // 添加
    conditionInit() {
      const { conditionData, currentData } = this.getCurrentData();
      const arr = conditionData.children;
      util.updateLayers(1, conditionData, 1);
      arr.splice(0, 1, {
        indent: 1,
        ruleCondition: {
          layer: 1,
          showLayer: 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    init() {
      const {
        operatorTypeList,
        fieldsDataList,
        dataEntryList,
        baseMethodListMap,
        methodsDataList,
        ruleData,
        ruleAttributes,
        ruleDrl,
        ruleText,
        mode,
      } = this.$props;
      const newRuleData = this.getNewRuleData(ruleData);
      this.conditionId = ruleData ? this.getConditionsNum(ruleData) + 1 : 2;
      this.ruleDrl = ruleDrl;
      this.ruleText = ruleText;
      this.ruleAttributes = ruleAttributes;
      this.currentDataIndex = 0;
      this.editData = {
        ruleData: newRuleData,
      };
    },
    getConditionsNum(ruleData = {}, num = 0) {
      const { conditions } = ruleData;
      if (conditions) {
        num = num + conditions.length;
      }
      return num;
    },
    getNewRuleData(ruleData = initRuleData) {
      const _RuleData = cloneDeep(ruleData);
      const { conditionExpression, conditions, actions, elseActions } =
        _RuleData;
      // 处理 actionData 和 ConditionData
      const conditionData = util.getConditionTree(
        conditionExpression,
        conditions,
        this.ruleUuid
      );
      const actionData = util.getActionData(actions, this.ruleUuid);
      const elseActionData = util.getActionData(elseActions, this.ruleUuid);
      this.updateIndex(actionData);
      this.updateIndex(elseActionData);
      const newRuleData = {
        conditionData,
        actionData,
        elseActionData,
      };
      return newRuleData;
    },
    getCurrentData() {
      const currentData = this.editData;
      const { ruleData } = currentData;
      const { conditionData, actionData, elseActionData } = ruleData;
      return { conditionData, actionData, currentData, elseActionData };
    },
    setRuleStore(obj) {
      const index = this.currentDataIndex;
      const { id, key, oldData, newData, layer, newDataClone, delPar, delArr } =
        obj;
      const resObj = { id, key, oldData, newData };
      layer && (resObj.layer = layer);
      newDataClone && (resObj.newDataClone = newDataClone);
      delPar && (resObj.delPar = delPar);
      delArr && (resObj.delArr = delArr);
      if (index === this.ruleStore.length) {
        this.ruleStore.push(resObj);
      } else {
        this.ruleStore.splice(index);
        this.ruleStore.push(resObj);
      }
    },
    updateRuleStore(currentData, id, key, oldData, action, delPar, delArr) {
      // id: conditionId=rowId
      // currentDataIndex: ruleStore->index
      let newData = null;
      let newDataClone = null;
      const contextData = util.generateContextRuleData(currentData, key, id);
      if (key === "conditionData") {
        if (action !== "del") {
          const { targetNode, parentNode } = util.findTargetNodeInfoById(
            contextData.ruleData[key],
            id
          );
          if (action === "add" || action === "change") {
            newData = targetNode;
            newDataClone = cloneDeep(newData);
          }
          if (action === "addChildCondition") {
            newData = parentNode;
            newDataClone = cloneDeep(parentNode);
          }
        }
      } else {
        if (action !== "del") {
          newData = contextData.ruleData[key][id];
          newDataClone = cloneDeep(newData);
        }
      }
      this.setRuleStore({
        id,
        key,
        oldData,
        newData,
        newDataClone,
        delPar,
        delArr,
      });
      this.currentDataIndex++;
      this.hisDataUpdateTimes += 1;
      if (Object.keys(this.self_validateResult).length > 0) {
        this.self_validateResult = {};
      }
    },
    addUp(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      let arr = conditionData.children;
      upArrIndentLayer(arr);
      function upArrIndentLayer(data) {
        for (let i in data) {
          data[i].indent && (data[i].indent += 1);
          if (data[i].ruleCondition && data[i].ruleCondition.layer) {
            data[i].ruleCondition.layer += 1;
            data[i].ruleCondition.showLayer += 1;
          }
          if (data[i].children) {
            upArrIndentLayer(data[i].children);
          }
        }
      }
      const resArr = [
        {
          indent: 1,
          ruleCondition: {
            layer: layer,
            showLayer: layer,
            conditionId: this.conditionId ++,
            contents: new ConditionGenerate(initConditionData),
          },
        },
        {
          indent: 1,
          logicalSymbol: "and",
          fold: false,
          children: arr,
        },
      ];
      arr = resArr;
      conditionData.children = arr;
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    addChildCondition(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;
      util.updateLayers(layer, conditionData, 2);
      arr.splice(arrIndex + 1, 0, {
        indent: targetNode.indent,
        logicalSymbol: "and",
        fold: false,
        children: [
          {
            indent: targetNode.indent + 1,
            ruleCondition: {
              layer: layer + 1,
              showLayer: layer + 1,
              conditionId: this.conditionId++,
              contents: new ConditionGenerate(initConditionData),
            },
          },
          {
            indent: targetNode.indent + 1,
            logicalSymbol: "and",
            ruleCondition: {
              layer: layer + 2,
              showLayer: layer + 2,
              conditionId: this.conditionId++,
              contents: new ConditionGenerate(initConditionData),
            },
          },
        ],
      });
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 2,
        "conditionData",
        null,
        "addChildCondition"
      );
    },
    addRule(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;
      util.updateLayers(layer, conditionData, 1);
      arr.splice(arrIndex + 1, 0, {
        indent: targetNode.indent,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    // 添加条件至队尾
    addTailItem(pos, conditionId) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const arr = parentNode.children;
      const lastCondition = util.getLastNode(parentNode);
      const { layer } = lastCondition;
      util.updateLayers(layer, conditionData, 1);

      arr.push({
        indent: targetNode.indent,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: this.conditionId++,
          contents: new ConditionGenerate(initConditionData),
        },
      });
      this.self_validateResult = {};
      this.updateRuleStore(
        currentData,
        Number(this.conditionId) - 1,
        "conditionData",
        null,
        "add"
      );
    },
    decreaseRule({ pos, conditionId, layer }, updateRuleStore = true) {
      const { conditionData, currentData } = this.getCurrentData();
      const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const oldData = cloneDeep(targetNode);
      const arr = parentNode.children;
      if (arr.length === 1) {
        arr.splice(arrIndex, 1);
      } else if (arr.length > 2) {
        arr.splice(arrIndex, 1);
      } else if (arr.length === 2) {
        arr.splice(arrIndex, 1);
        const _logicalSymbol = parentNode.logicalSymbol;
        // 如果数组中只有两组数据，删除后，子节点逻辑组合，将转化为一个普通的子节点；
        if (parentNode !== conditionData || !!arr[0].children) {
          if (arr[0].ruleCondition) {
            for (const i in arr[0]) {
              parentNode[i] = arr[0][i];
            }
            delete parentNode.children;
          } else if (arr[0].children) {
            for (const i in arr[0]) {
              parentNode[i] = arr[0][i];
            }
          }
          parentNode.logicalSymbol = _logicalSymbol;
          util.updateIndent(parentNode, -1);
        }
      }
      if (arr && arrIndex / 1 === 0) {
        arr[0]?.logicalSymbol && delete arr[0].logicalSymbol;
      }
      this.self_validateResult = {};
      util.updateLayers(layer, conditionData, -1);
      updateRuleStore &&
        this.updateRuleStore(
          currentData,
          Number(conditionId),
          "conditionData",
          oldData,
          "del",
          parentNode,
          arrIndex
        );
    },
    onSwitcherChange(pos, conditionId, layer) {
      const { conditionData, currentData } = this.getCurrentData();
      this.self_validateResult = {};
      util.updateNodeFold(conditionData, conditionId, layer);
      this.updateRuleStore(currentData);
    },
    onLogicBtnClick(pos, val) {
      const [index, targetIndent, targetLayer] = pos.split("_"); // eslint-disable-line
      const { conditionData, currentData } = this.getCurrentData();
      const logicNode = util.findLogicNode(
        conditionData,
        targetIndent,
        targetLayer
      );
      const oldData = cloneDeep(logicNode);
      logicNode.logicalSymbol = val || null;
      this.updateRuleStore(
        currentData,
        Number(logicNode.ruleCondition.conditionId),
        "conditionData",
        oldData,
        "change"
      );
    },
    // 通过pos寻找当前condition节点信息：rule、ruleItem
    findConditionNodeByPosition(pos) {
      const [index, conditionId, path] = pos.split("_"); // eslint-disable-line
      const pathArr = path.split(".");
      const { conditionData, currentData } = this.getCurrentData();
      const { targetNode } = util.findTargetNodeInfoById(
        conditionData,
        conditionId
      );
      const { node, key } = util.findDataByPath(
        targetNode.ruleCondition.contents.list,
        pathArr
      );
      return { targetNode, ruleItem: node, ruleItemIndex: key, currentData };
    },
    // 通过pos寻找当前Action节点信息
    findActionNodeByPosition(pos, flag) {
      const [index, path] = pos.split("_"); // eslint-disable-line
      const { actionData, currentData, elseActionData } = this.getCurrentData();
      const pathArr = path.split(".");
      const _actionData = flag === "action" ? actionData : elseActionData;
      const { node, key } = util.findDataByPath(_actionData, pathArr);
      return {
        actionItem: node,
        actionItemIndex: key,
        actionData,
        elseActionData,
        currentData,
      };
    },
    onConditionChange(pos, newContents) {
      if (this.updateD) {
        const [index, conditionId] = pos.split("_");
        const { conditionData, currentData } = this.getCurrentData();
        const { targetNode } = util.findTargetNodeInfoById(
          conditionData,
          conditionId
        );
        const oldData = cloneDeep(targetNode);
        targetNode.ruleCondition.contents = newContents;
        this.updateRuleStore(
          currentData,
          Number(conditionId),
          "conditionData",
          oldData,
          "change"
        );
      }
    },
    updateIndex(data) {
      data.map((item, index) => {
        data[index].actionId = index;
      });
    },
    undo() {
      this.updateD = false;
      // id-rowId
      // currentDataIndex: ruleStore-index
      let { currentDataIndex } = this;
      if (currentDataIndex > 0) {
        let layer = null;
        const { id, key, oldData, newData, newDataClone, delPar, delArr } =
          this.ruleStore[currentDataIndex - 1];
        const { children } = this.editData.ruleData[key];
        let _targetNode,
          _parentNode,
          _grandparentNode,
          _arrIndex,
          _parentArrIndex;
        if (key === "conditionData" && newData) {
          const {
            targetNode,
            parentNode,
            grandparentNode,
            arrIndex,
            parentArrIndex,
          } = util.findTargetNodeInfoById(this.editData.ruleData[key], id);
          _targetNode = targetNode;
          _parentNode = parentNode;
          _grandparentNode = grandparentNode;
          _arrIndex = arrIndex;
          _parentArrIndex = parentArrIndex;
        }
        const { conditionData } = this.getCurrentData();
        if (oldData && newData) {
          // change
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            _parentNode.children.splice(_arrIndex, 1, oldData);
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 1, oldData);
          }
        } else if (oldData) {
          // del
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            util.updateLayers(layer - 1, conditionData, 1);
            if (delPar.children) {
              delPar.children.splice(delArr, 0, oldData);
            } else {
              const { targetNode, arrIndex } = util.findTargetNodeInfoById(
                this.editData.ruleData[key],
                delPar.ruleCondition.conditionId
              );
              this.$set(targetNode, "children", [
                {
                  indent: targetNode.indent + 1,
                  ruleCondition: targetNode.ruleCondition,
                },
              ]);
              // delete delPar.ruleCondition
              delete targetNode.ruleCondition;
              targetNode.children.splice(delArr, 0, oldData);
            }
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 0, oldData);
          }
        } else {
          if (key === "conditionData") {
            // add
            if (newDataClone && newDataClone.ruleCondition) {
              layer = newDataClone.ruleCondition.layer;
              this.ruleStore[currentDataIndex - 1].delPar = _parentNode;
              this.ruleStore[currentDataIndex - 1].delArr = _arrIndex;
              _parentNode.children.splice(_arrIndex, 1);
              util.updateLayers(
                layer - 1,
                this.editData.ruleData.conditionData,
                -1
              );
            } else if (newDataClone && newDataClone.children) {
              // 联合条件
              layer = _targetNode.ruleCondition.layer;
              this.ruleStore[currentDataIndex - 1].undo = {
                _grandparentNode,
                _parentArrIndex,
              };
              _grandparentNode.children.splice(_parentArrIndex, 1);
              util.updateLayers(layer - 1, conditionData, -2);
            }
          } else {
            this.editData.ruleData[key].splice(newDataClone.actionId, 1);
          }
        }
        this.hisDataUpdateTimes += 1;
        this.currentDataIndex -= 1;
        this.updateD = true;
      }
    },
    setValByNewDataClone(data, cloneData) {
      const { children, ruleCondition } = data;
      const { children: childrenClone, ruleCondition: ruleConditionClone } =
        cloneData;
      if (children && children.length > 0) {
        data.children = setValByNewDataClone(children, childrenClone);
      }
      if (ruleCondition) {
        ruleCondition.contents = ruleConditionClone.contents;
      }
      return data;
    },
    redo() {
      this.updateD = false;
      let { currentDataIndex, ruleStore } = this;
      const len = ruleStore.length;
      if (currentDataIndex < len) {
        let layer = null;
        const {
          id,
          key,
          oldData,
          newData,
          undo,
          newDataClone,
          delPar,
          delArr,
        } = ruleStore[currentDataIndex];
        const { children } = this.editData.ruleData[key];
        let _targetNode,
          _parentNode,
          _grandparentNode,
          _arrIndex,
          _parentArrIndex;
        if (key === "conditionData" && oldData) {
          const {
            targetNode,
            parentNode,
            grandparentNode,
            arrIndex,
            parentArrIndex,
          } = util.findTargetNodeInfoById(this.editData.ruleData[key], id);
          _targetNode = targetNode;
          _parentNode = parentNode;
          _grandparentNode = grandparentNode;
          _arrIndex = arrIndex;
          _parentArrIndex = parentArrIndex;
        }
        const { conditionData } = this.getCurrentData();
        if (oldData && newData) {
          // change
          if (key === "conditionData") {
            layer = newDataClone.ruleCondition.layer;
            const _data = cloneDeep(newData);
            _parentNode.children.splice(
              _arrIndex,
              1,
              this.setValByNewDataClone(_data, newDataClone)
            );
          } else {
            this.editData.ruleData[key].splice(newData.actionId, 1, newData);
          }
        } else if (newData) {
          if (key === "conditionData") {
            // add
            if (newDataClone && newDataClone.ruleCondition) {
              layer = newDataClone.ruleCondition.layer;
              util.updateLayers(layer - 1, conditionData, 1);
              delPar.children.splice(delArr, 0, newDataClone);
            } else if (newDataClone && newDataClone.children) {
              // 联合条件
              const { targetNode } = util.findTargetNodeInfoById(
                newDataClone,
                id
              );
              layer = targetNode.ruleCondition.layer;
              util.updateLayers(layer - 1, conditionData, 2);
              undo._grandparentNode.children.splice(
                undo._parentArrIndex,
                0,
                newDataClone
              );
            }
          } else {
            this.editData.ruleData[key].splice(
              newDataClone.actionId,
              0,
              newDataClone
            );
            this.updateIndex(this.editData.ruleData[key]);
          }
        } else {
          // del
          if (key === "conditionData") {
            layer = oldData.ruleCondition.layer;
            _parentNode.children.splice(_arrIndex, 1);
            util.updateLayers(layer - 1, conditionData, -1);
          } else {
            this.editData.ruleData[key].splice(oldData.actionId, 1);
            this.updateIndex(this.editData.ruleData[key]);
          }
        }
        this.currentDataIndex += 1;
        this.hisDataUpdateTimes += 1;
        this.updateD = true;
      }
    },
    getRulesData() {
      const { editData, currentDataIndex, ruleAttributes = {} } = this;
      const { ruleName = "save", ...otherAttributes } = ruleAttributes;
      const currentData = editData;
      const { ruleData } = currentData;
      const data = { ...ruleData, ruleName, ruleAttributes: otherAttributes };
      const newRuleData = util.getBackEndData(data, this.ruleUuid);
      return newRuleData;
    },
    toSave() {
      const { complexModels } = this.injectModelData;
      const { fieldsDataList } = util.getModelList(complexModels);
      store.commit('setFieldsDataList',{ list: fieldsDataList, ruleUuid: this.ruleUuid });
      const ruleData = this.getRulesData();
      this.$emit("save", ruleData);
    },
    toValidate() {
      const ruleData = this.getRulesData();
      store.commit('setFieldsDataList',{ list: fieldsDataList, ruleUuid: this.ruleUuid });
      this.$emit("validate", ruleData);
    },
    changeLockState() {
      if (this.$props.onLock) {
        this.$props.onLock(this.locked);
      }
    },
    changeLockInner() {
      this.lockedInner = !this.lockedInner;
    }
  },
  render() {
    const {
      theme,
      isTrack,
      ruleNamesBeHited,
    } = this.$props;
    const {
      viewStatus,
      lockedInner,
      self_validateResult: validateResult,
    } = this;
    const { ruleData = {} } = this.editData;
    return (
      <div class={`eRuleEditorContainer ${theme || "oneDark"}`}>
        {ruleData ? (
          <div class="eRuleEditor">
            <div class="editorBody">
              <DesignView
                style={
                  viewStatus === "designView"
                    ? { display: "block" }
                    : { display: "none" }
                }
                locked={!lockedInner}
                ruleNamesBeHited={ruleNamesBeHited}
                isTrack={isTrack}
                ruleData={ruleData}
                validateResult={validateResult}
                onAddRule={this.addRule}
                onAddUp={this.addUp}
                onAddChildCondition={this.addChildCondition}
                onAddTailItem={this.addTailItem}
                onDecreaseRule={this.decreaseRule}
                onLogicBtnClick={this.onLogicBtnClick}
                onConditionChange={this.onConditionChange}
                onConditionInit={this.conditionInit}
                onReplaceItem={this.replaceItem}
              />
              <ReplaceItem 
                replaceIShow={this.replaceIShow} replaceIErrInfo={this.replaceIErrInfo} onReplaceItemHide={this.replaceItemHide} onReplaceItemSure={this.replaceItemSure}
               />
            </div>
          </div>
        ) : null}
      </div>
    );
  },
};
</script>

<style lang="scss">
@use "@/assets/css/ruleEditor.scss";
.ant-drawer-dir {
  .eRuleEditorContainer {
    width: 100%;
    height: auto;
    border-radius: 3px;
    .eRuleEditor {
      height: auto;
    }
  }
}
</style>
