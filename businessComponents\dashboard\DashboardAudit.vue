<!-- 无规则编辑权限人员开始页 -->
<script setup lang="ts">
    import {
        getRuleTypeStatistics,
        getDemandApplyStatistics,
        getTotalStatistics,
        getRuleStatusDayStatistics,
    } from "@/api/page";
    import { findLastBulletin } from "@/api/bulletin";
    //基础信息
    const foundation = ref({
        userCount:0,
        engCount:0,
        ruleCount:0,
        modelCount:0,
    });
    //规则数量
    const statistics = ref({
        ordinary: 0,
        decision:0,
        decisionTree:0,
        flow:0,
        effective:0,
        invalid:0,
    });
    //任务数量
    const mission = ref({
        demandTotal:0,
        demandAddCount:0,
        demandFinishCount:0,
    });
    //公告
    const bulletin = ref('');
    //当日规则操作次数统计
    const ruleStatus = ref({
        addRuleCount:0,
        editRuleCount:0,
        delRuleCount:0,
        checkRuleCount:0
    });
    onMounted(()=>{
        //规则统计
        getRuleTypeStatistics().then((res) => {
            statistics.value.ordinary = res.data.generalRuleCount
            statistics.value.decision = res.data.decisionTableCount
            statistics.value.flow = res.data.ruleFlowCount
        })
        //任务统计
        getDemandApplyStatistics().then((res) => {
            mission.value = {...res.data};
        })
        //基础信息
        getTotalStatistics().then((res) => {
            foundation.value = {...res.data};
        });
        //公告
        findLastBulletin().then((res) => {
            bulletin.value = res.data.content;
        })
        getRuleStatusDayStatistics().then((res) => {
            ruleStatus.value = {...res.data}
        })
    })
</script>

<template>
    <div class="dashboard dashboard-container">
        <a-row>
            <div class="bulletin"> <div class="bulletin-title">{{bulletin}}</div></div>
            <a-col :span="24">
                <div class="foundation">

                    <div class="title">基础信息</div>
                    <div class="bottom">
                        <div class="foundation-msg-div">
                            <div class="foundation-msg">
                                <div class="foundation-title">
                                    用户数量
                                </div>
                                <div class="foundation-footer">{{foundation.userCount}}</div>
                            </div>
                            <div class="foundation-msg">
                                <div class="foundation-title">
                                    规则库数量
                                </div>
                                <div class="foundation-footer">{{foundation.engCount}}</div>
                            </div>
                            <div class="foundation-msg">
                                <div class="foundation-title">
                                    规则数量
                                </div>
                                <div class="foundation-footer">{{foundation.ruleCount}}</div>
                            </div>
                            <div class="foundation-msg">
                                <div class="foundation-title">
                                    模型数量
                                </div>
                                <div class="foundation-footer">{{foundation.modelCount}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </a-col>
            <a-col :span="19">
                <div class="statistics">
                    <div class="title">规则统计</div>
                    <div class="bottom">
                        <div class="statistics-msg-div">
                            <div class="statistics-msg">
                                <div class="statistics-title">
                                    普通规则
                                </div>
                                <div class="statistics-footer">{{statistics.ordinary}}</div>
                            </div>
                            <div class="statistics-msg">
                                <div class="statistics-title">
                                    决策表
                                </div>
                                <div class="statistics-footer">{{statistics.decision}}</div>
                            </div>
                            <div class="statistics-msg">
                                <div class="statistics-title">
                                    规则流
                                </div>
                                <div class="statistics-footer">{{statistics.flow}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="missionStatistics">
                    <div class="title">任务统计</div>
                    <div class="bottom">
                        <a-badge show-zero :count="mission.demandTotal" class="item" :number-style="{ backgroundColor: '#52c41a' }" :overflow-count="999">
                            <a-button>任务总数</a-button>
                        </a-badge>
                        <a-badge show-zero :count="mission.demandAddCount" class="item" type="warning" :number-style="{ backgroundColor: 'orange' }">
                            <a-button>当日新增</a-button>
                        </a-badge>
                        <a-badge show-zero :count="mission.demandFinishCount" class="item" type="item" :number-style="{ backgroundColor: 'bule' }">
                            <a-button>当日完成</a-button>
                        </a-badge>
                    </div>
                </div>
                <div class="missionStatistics">
                    <div class="title">当日规则操作次数统计</div>
                    <div class="bottom">
                        <a-badge show-zero :count="ruleStatus.addRuleCount" class="item" :number-style="{ backgroundColor: '#52c41a' }">
                            <a-button>新增</a-button>
                        </a-badge>
                        <a-badge show-zero :count="ruleStatus.editRuleCount" class="item" type="warning" :number-style="{ backgroundColor: 'orange' }">
                            <a-button>修改</a-button>
                        </a-badge>
                        <a-badge show-zero :count="ruleStatus.delRuleCount" class="item" type="item" :number-style="{ backgroundColor: 'red' }">
                            <a-button>删除</a-button>
                        </a-badge>
                        <a-badge show-zero :count="ruleStatus.checkRuleCount" class="item" type="item" :number-style="{ backgroundColor: 'bule' }">
                            <a-button>审核</a-button>
                        </a-badge>
                    </div>
                </div>
            </a-col>

            <a-col :span="5">
                <div class="quick">
                    <div class="title">快捷方式</div>
                    <div class="bottom">
<!--
                            <a-col :span="12"><div class="quick-btn" @click="toPath('/BRMS/erule-web/work_queue/rule_base_management')">规则编写</div></a-col>
                            <a-col :span="12"><div class="quick-btn" @click="toPath('/BRMS/erule-web/work_queue/rule_base_management')">规则测试</div></a-col>
                            <a-col :span="12"><div @click="toPath('/BRMS/erule-web/taskManagement/taskFilling')" class="quick-btn">任务填报</div></a-col>
                        <a-col :span="12"><div @click="toPath('/BRMS/erule-web/taskManagement/taskFilling')" class="quick-btn">规则发布</div></a-col>
-->
                    </div>
                </div>

            </a-col>
        </a-row>

    </div>
</template>

<style lang="scss" scoped>
    .dashboard {
        & {
            // background: radial-gradient(ellipse at center, #29387d 0%, #1a234f 100%);
            min-height: calc(100vh - 50px);
            width: 100%;
            padding: 20px;
        }
        .title{
            font-size: 20px;
            font-weight:bold;
            margin-left: 15px;
        }
        .title .title-link{
            margin-left: 15px;
            font-weight:bold
        }
        .text {
            font-size: 14px;
        }

        .item {
            margin-bottom: 18px;
        }
        .bulletin{
            color:gray;
            margin-left: 15px;
            margin-bottom: 10px;
            width: 100%;
            .bulletin-title{
                text-align: center;
                font-size:28px;
                font-weight:bold
            }
        }
        .foundation{
            height: 170px;
            .bottom{
                border: 1px solid black; /* 1px宽度，实线，黑色边框 */
                border-radius: 8px;
                width: 98%;
                height: 120px;
                margin-left: 15px;
                margin-top: 10px;
                .foundation-msg-div{
                    height: 70px;
                    margin: 30px 30px 30px 40px;
                    .foundation-msg{
                        text-align: center;
                        display: inline-block;
                        width: 25%;
                        .foundation-title{
                            font-size: 20px;
                            font-weight:bold
                        }
                        .foundation-footer{
                            font-size: 20px;
                        }
                    }
                }

            }
        }

        .statistics{
            height: 200px;
            .bottom{
                border: 1px solid black; /* 1px宽度，实线，黑色边框 */
                border-radius: 8px;
                width: 95%;
                height: 150px;
                margin-left: 15px;
                margin-top: 10px;
                .statistics-msg-div{
                    width: 80%;
                    height: 70px;
                    margin: 40px 30px 30px 60px;
                    .statistics-msg{
                        text-align: center;
                        display: inline-block;
                        width: 33%;
                        .statistics-title{
                            font-size: 20px;
                            font-weight:bold
                        }
                        .statistics-footer{
                            font-size: 20px;
                        }
                    }
                }

            }
        }
        .missionStatistics{
            .bottom{
                border: 1px solid black; /* 1px宽度，实线，黑色边框 */
                border-radius: 8px;
                width: 95%;
                height: 80px;
                margin-left: 15px;
                margin-top: 10px;
                .item {
                    margin-top: 20px;
                    margin-left: 40px;
                }
            }
        }
        .quick{
            .bottom{
                height: 400px;
                width: 90%;
                border: 1px solid black; /* 1px宽度，实线，黑色边框 */
                border-radius: 8px;
                margin-left: 15px;
                margin-top: 10px;
                overflow: auto;
                .quick-btn{
                    white-space: nowrap;
                    border: 1px solid black; /* 1px宽度，实线，黑色边框 */
                    border-radius: 8px;
                    margin: 20px 0px 20px 40px;
                    font-size: 20px;
                    font-weight:bold;
                    width: 120px;
                    height: 60px;
                    text-align: center;
                    line-height: 60px;
                    cursor: pointer;
                }
            }
        }

    }
</style>
