import * as util from "@/components/ruleEditCom/utils/util";
import {
  cloneDeep,
  isArray
} from "lodash";
const CONDITION = 'condition'
const ACTION = 'action'
let paramIndex = 0
let addFirst = false
let globalIndex = 0
let globalI = 0
export const scoredKey = "cardScored";

export function parseChildName(conditionData, paramQuantity, ruleUuid, isDomainStringList, childrenArraLength, oI = 0, nameList = []) {
  const {
    conditions
  } = util.getConditionDataToBE(conditionData, {
      conditions: [],
      conditionExpression: "",
    },
    ruleUuid);
  const {
    comparator: {
      operatorViewName = "",
      operatorParams = []
    }
  } = conditions[oI];
  const name = operatorViewName.replace(/\(#[0-9]+,/g, "").replace(/\)/g, "");
  if (isDomainStringList) {
    if (paramQuantity > 1) {
      if (name.includes(" ")) {
        const nameArr = []
        const nameStrArr = name.split(" ");
        for (let index = 0; index < paramQuantity; index++) {
          if (operatorParams && ((!operatorParams[index].value&&operatorParams[index].value!==0) || operatorParams[index].value.toString().length <= 0)) {
            nameArr.push(nameStrArr[index]);
          }
        }
        return nameArr;
      }
      const nameArr = [];
      for (let index = 0; index < paramQuantity; index++) {
        if (operatorParams && ((!operatorParams[0].value&&operatorParams[0].value!==0) || operatorParams[0].value.toString().length <= 0)) {
          nameArr.push(`参数${Number(childrenArraLength+index) + 1}`);
        }
      }
      return nameArr;
    } else {
      if (operatorParams && operatorParams.length > 0) {
        const {
          next
        } = operatorParams[0]
        if (next) {

          let _operatorParams = getOperatorParams(
            next,
            []
          );
          let res = []
          _operatorParams && _operatorParams.map((itemV, i) => {
            if (itemV.expressionParams) {
              itemV.expressionParams.map((itemEx, itemI) => {
                if (itemEx.variableType === 'constant' && (!itemEx.value || itemEx.value.length <= 0)) {
                  let _name = `参数${nameList.length+1+itemI}`
                  res = [_name]
                  return
                }
              })

            } else {
              if (itemV.variableType === 'constant' && (!itemV.value || itemV.value.length <= 0)) {
                let _name = `参数${nameList.length+1+i}`
                res = [_name]
                return
              }
            }
          })
          return res
        } else {
          if ((operatorParams[0].value || operatorParams[0].value === 0) && operatorParams[0].value.toString().length > 0) {
            return []
          } else {
            let res = []
            operatorParams.map((itemV, i) => {
              if (itemV.expressionParams) {
                itemV.expressionParams.map(itemEx => {
                  if (itemEx.variableType === 'constant' && (!itemEx.value || itemEx.value.length <= 0)) {
                    res = [name]
                    return
                  }
                })

              } else {
                if (itemV.variableType === 'constant' && (!itemV.value || itemV.value.length <= 0)) {
                  res = [name]
                  return
                }
              }
            })
            return res
          }
        }
      } else {
        return []
      }
    }
  } else {
    if (paramQuantity > 1) {
      if (name.includes(" ")) {
        const nameArr = []
        const nameStrArr = name.split(" ");
        for (let index = 0; index < paramQuantity; index++) {
          if (operatorParams && ((!operatorParams[index].value&&operatorParams[index].value!==0) || operatorParams[index].value.toString().length <= 0)) {
            nameArr.push(nameStrArr[index]);
          }
        }
        return nameArr;
      }
      const nameArr = [];

      for (let index = 0; index < paramQuantity; index++) {
        if (operatorParams && ((!operatorParams[index].value&&operatorParams[index].value!==0)  || operatorParams[0].value.toString().length <= 0)) {
          nameArr.push(`参数${Number(childrenArraLength+index) + 1}`);
        }
      }
      return nameArr;
      // for (let index = 0; index < paramQuantity; index++) {
      //   nameArr.push(`参数${index + 1}`);
      // }
      // return nameArr;
    }
    if (paramQuantity === 1) {
      if (operatorParams && ((!operatorParams[0].value&&operatorParams[0].value!==0) || operatorParams[0].value.toString().length <= 0)) {
        return [name];
      } else {
        return [];
      }
    }
  }
  return name;
}

export function getProperties(columns, keys) {
  if (keys === 0) {
    return {
      aliasName: "",
      rule: {}
    };
  }
  const findItem = columns.find(item => item.key === keys);
  return findItem.properties;
}
export function getOperatorParams(data, res = []) {
  const {
    next,
    methodParams
  } = data;
  if (methodParams) {
    if (!next) {
      res.push(...methodParams);
      return res;
    } else {
      res.push(...methodParams);
      return getOperatorParams(next, res);
    }
  } else if (res.length > 0 && !next) {
    return res;
  }

  if (next) {
    return getOperatorParams(next, res);
  }
}

export function findEditIndex(columns, colEditingKey) {
  return columns.findIndex(item => colEditingKey === item.key);
}
// export function getRuCon(data) {

// }
function loopChildren(data, conditionData, aliasName, ruleUuid, nameList, conList, complexSelect, childrenArr, childrenArra, tableParams, itemTempSum, oChildren, copyData, editIndex, newData, oI, colEditingKey, parseI, loopEnd = undefined,isChildren) {
  let ruleCondition = {}
  let contents = {}
  let comparator = {}
  let variable = {}
  const {
    children
  } = data;
  
  if (children) {
    for (let ci = 0; ci < children.length; ci++) {
      const {indent} = children[ci]
      if (oI === 0) {
        parseI = ci
      } else {
        parseI++
        // if(ci>0){
        // }
      }
      if (ci === children.length - 1&&indent===2&&oI===oChildren.length-1) {
        loopEnd = true
      } else {
        loopEnd = false
      }
      newData = loopChildren(
        children[ci], conditionData, aliasName, ruleUuid, nameList, conList, complexSelect, childrenArr, childrenArra, tableParams, itemTempSum, oChildren, copyData, editIndex, newData, oI, colEditingKey, parseI, loopEnd,isChildren
      );
      
    }
  } else {
    if(oI>0&&!isChildren){
      parseI++
    }
    if (data.ruleCondition) {
      ruleCondition = data.ruleCondition;
      contents = ruleCondition.contents;
      comparator = contents.comparator;
      variable = contents.variable;
    }else{
      comparator = data.comparator;
      variable = data.variable;
    }
    const {
      paramQuantity = 1,
        operatorParams = [{
          valueType: "String"
        }]
    } = comparator;

    newData = {
      ...copyData[editIndex],
      viewName: aliasName,
      title: aliasName,
      valueType: operatorParams[0] ? operatorParams[0].valueType : "String",
      properties: {
        aliasName,
        rule: conditionData
      }
    };

    if (nameList && isArray(nameList) && nameList.length > 0) {
      const methodParams = getLastMethodParams(variable, []);

      tableParams=getLoopMethodPar(methodParams,tableParams,oI)

      if (!tableParams[oI]) {
        tableParams[oI] = {
          outI: oI,
          temp: true
        }
      }

      if (tableParams.length > 0) {
        newData = {
          ...copyData[editIndex],
          viewName: aliasName,
          title: aliasName,
          properties: {
            aliasName,
            rule: conditionData
          },
          editable: true
        };

        let tempIndex = 0;
        let express = false
        tableParams.map((item, index) => {
          if (item.outI === oI && item.temp) {
            itemTempSum++
          }
          if ((item.outI===parseI && !item.temp)||(item.parseI === index && parseI < index && !item.temp)||(Number(item.parseI+oI) === index && parseI < index && !item.temp)) {

            // if ((parseI === index && !item.temp)||(item.parseI === index && parseI < index && !item.temp))

          // if ((!item.temp&&item.outI===parseI)||(item.parseI === index && parseI < index && !item.temp)||(Number(item.parseI+oI) === index && parseI < index && !item.temp)) {
          // if (item.outI === oI && !item.temp) {
            tempIndex = express ? tempIndex + index : index
            if (item.variableType && item.variableType === "expression") {
              const expressionTreeData = item.expressionTreeData;
              if (expressionTreeData && expressionTreeData.params) {
                loopExpressionTreeData(
                  expressionTreeData.params
                );
              }

              function loopExpressionTreeData(data) {
                for (let i = 0; i < data.length; i++) {
                  if (data[i].data && data[i].data.next) {
                    // 函数
                    const methodParams = data[i].data.next.methodParams;
                    const tableParams = [];
                    methodParams && methodParams.map((item, index) => {
                      const {
                        value = [], valueType
                      } = item;
                      if (!valueType.includes(".")) {
                        if (
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        )
                          tableParams.push(item);
                      }
                    });
                    const _newData = getNewData(tableParams, aliasName, conditionData, editIndex, copyData, nameList, colEditingKey, tempIndex)
                    tempIndex = paramIndex
                    if (_newData.children && _newData.children.length > 0) {
                      childrenArra.push(..._newData.children)
                    }
                  } else if (data[i].type === "variable") {
                    const _data = data[i].data;
                    if (!_data.valueType.includes(".")) {
                      if (!(_data.value && _data.value.toString().length > 0)) {
                        const temObj = {
                          align: "center",
                          className: "condition-style",
                          width: "130px",
                          ellipsis: true,
                          title: nameList ? nameList[tempIndex].value : '参数1',
                          valueType: _data.valueType,
                          dataIndex: colEditingKey + (tempIndex).toString() + "child",
                          key: colEditingKey + (tempIndex).toString() + "child",
                          editable: true
                        }
                        globalIndex = tempIndex
                        childrenArra.push(temObj);
                        tempIndex++
                        express = true
                      }
                    }
                  } else if (data[i].type === "expression") {
                    loopExpressionTreeData(data[i].params);
                  }
                }
              }
            } else {
              const temObj = {
                align: "center",
                className: "condition-style",
                width: "130px",
                ellipsis: true,
                // title: nameList ? nameList[index].value : '参数1',
                title: nameList ? nameList[index - itemTempSum].value : '参数1',
                valueType: item.valueType,
                dataIndex: colEditingKey + childrenArra.length.toString() + "child",
                key: colEditingKey + childrenArra.length.toString() + "child",
                editable: true
              };
              if (item.enumDictName) {
                temObj.enumDictName = item.enumDictName
              }
              childrenArra.push(temObj);
            }
            tempIndex++
            express = false
            // tableParams=tableParams.splice(index,1)
          }
        });
        // newData.children = childrenArra;
      } else {
        newData = {
          ...copyData[editIndex],
          viewName: aliasName,
          title: aliasName,
          properties: {
            aliasName,
            rule: conditionData
          },
          editable: true
        };
        let tempIndex = 0;
        let express = false
        let tabLength = tableParams.length
        globalI = 0;
        tableParams.map((item, index) => {
          tempIndex = express ? tempIndex + index : index
          tempIndex = addFirst ? tempIndex + index : index
          if (item.variableType && item.variableType === "expression") {
            const expressionTreeData = item.expressionTreeData;
            if (expressionTreeData && expressionTreeData.params) {
              loopExpressionTreeData(
                expressionTreeData.params
              );
            }

            function loopExpressionTreeData(data) {
              for (let i = 0; i < data.length; i++) {
                if (data[i].data && data[i].data.next) {
                  // 函数
                  const methodParams = data[i].data.next.methodParams;
                  if (methodParams && methodParams.length > 0) {
                    const next = data[i].data.next
                    const tableParams = [];
                    methodParams && methodParams.map((item, index) => {
                      const {
                        value = [], valueType
                      } = item;
                      if (!valueType.includes(".")) {
                        if (
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        )
                          tableParams.push(item);
                      }
                    });
                    const _newData = getNewData(tableParams, aliasName, conditionData, editIndex, copyData, nameList, colEditingKey, tempIndex)
                    tempIndex = paramIndex
                    if (_newData.children && _newData.children.length > 0) {
                      childrenArra.push(..._newData.children)
                    }
                    if (index !== tabLength - 1) {
                      addFirst = true
                    } else {
                      addFirst = false
                    }
                  } else {
                    const next = data[i].data.next
                    const {
                      value = false, valueType
                    } = next;
                    if (next) {
                      if (!valueType.includes(".")) {
                        if (value && value.toString().length > 0) {
                          paramIndex = paramIndex > 0 ? paramIndex - 1 : 0
                          tempIndex = paramIndex
                          paramIndex++
                          addFirst = false
                        } else {
                          tempIndex = paramIndex
                          paramIndex++
                          addFirst = false
                        }
                      } else {

                        tempIndex = paramIndex
                        paramIndex++
                        addFirst = false
                      }
                    } else {

                      tempIndex = paramIndex
                      paramIndex++
                      addFirst = false
                    }
                    // if (index !== tabLength - 1) {
                    //   addFirst = true
                    // } else {
                    //   addFirst = false
                    // }
                  }
                } else if (data[i].type === "variable") {
                  if (addFirst) {
                    // paramI = globalIndex + 1
                  }
                  const _data = data[i].data;
                  if (!_data.valueType.includes(".")) {
                    if (!(_data.value && _data.value.toString().length > 0)) {
                      const temObj = {
                        align: "center",
                        className: "condition-style",
                        width: "130px",
                        ellipsis: true,
                        title: nameList ? nameList[tempIndex].value : '参数1',
                        valueType: _data.valueType,
                        dataIndex: colEditingKey + (tempIndex).toString() + "child",
                        key: colEditingKey + (tempIndex).toString() + "child",
                        editable: true
                      }
                      globalI++
                      globalIndex = tempIndex
                      childrenArra.push(temObj);
                      tempIndex++
                      express = true
                      if (index !== tabLength - 1) {
                        addFirst = true
                      } else {
                        addFirst = false
                      }
                    }
                  }
                } else if (data[i].type === "expression") {
                  loopExpressionTreeData(data[i].params);
                }
              }
            }
          } else {
            let paramI = index
            if (addFirst) {
              paramI = globalIndex + 1
              globalIndex += 1
            }
            const temObj = {
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: nameList ? nameList[paramI].value : '参数1',
              valueType: item.valueType,
              dataIndex: colEditingKey + paramI.toString() + "child",
              key: colEditingKey + paramI.toString() + "child",
              editable: true
            };
            if (item.enumDictName) {
              temObj.enumDictName = item.enumDictName
            }
            globalI++
            // const temObj = {
            //   align: "center",
            //   className: "condition-style",
            //   width: "130px",
            //   ellipsis: true,
            //   title: nameList ? nameList[index].value : '参数1',
            //   valueType: item.valueType,
            //   dataIndex: colEditingKey + index.toString() + "child",
            //   key: colEditingKey + index.toString() + "child",
            //   editable: true
            // };
            tempIndex++
            childrenArra.push(temObj);
          }
          express = false
        })
        // newData.children = childrenArra;
      }
      // for(let i=0;i<operatorParams.length;i++){

      // }
      // debugger
      // return
      const childName = parseChildName(conditionData, paramQuantity, ruleUuid, true, childrenArra.length, parseI, nameList);
      // const childName = parseChildName(conditionData, paramQuantity, ruleUuid, true, childrenArra.length, oI, nameList);
      operatorParams && operatorParams.length > 0 && childName.map((item, index) => {
        const {
          next
        } = operatorParams[index]
        if (next) {
          let _operatorParams = getOperatorParams(
            next,
            []
          );
          if (_operatorParams) {
            const temObj = {
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: item,
              valueType: _operatorParams[index].valueType,
              dataIndex: colEditingKey + (childrenArra.length).toString() + "child",
              key: colEditingKey + (childrenArra.length).toString() + "child",
              editable: true,
              paramQuantity
            };
            if (_operatorParams[index].enumDictName) {
              temObj.enumDictName = _operatorParams[index].enumDictName
            }
            childrenArra.push(temObj);
          }
        } else {

          const temObj = {
            align: "center",
            className: "condition-style",
            width: "130px",
            ellipsis: true,
            title: item,
            valueType: operatorParams[index].valueType,
            dataIndex: colEditingKey + (childrenArra.length).toString() + "child",
            key: colEditingKey + (childrenArra.length).toString() + "child",
            editable: true,
            paramQuantity
          };
          if (operatorParams[index].enumDictName) {
            temObj.enumDictName = operatorParams[index].enumDictName
          }
          childrenArra.push(temObj);
        }
      });
      if (!conditionData.allCellUnit && oI === oChildren.length - 1 && complexSelect) {
        if (loopEnd !== undefined) {
          if (loopEnd) {
            childrenArra.push({
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: '是否成立',
              valueType: "Boolean",
              dataIndex: colEditingKey + (childrenArra.length).toString() + "child",
              key: colEditingKey + (childrenArra.length).toString() + "child",
              editable: true
            });
          }
        }else{
          childrenArra.push({
            align: "center",
            className: "condition-style",
            width: "130px",
            ellipsis: true,
            title: '是否成立',
            valueType: "Boolean",
            dataIndex: colEditingKey + (childrenArra.length).toString() + "child",
            key: colEditingKey + (childrenArra.length).toString() + "child",
            editable: true
          });
        }
      }
      newData.children = childrenArra;
    } else {
      if (paramQuantity > 0 || paramQuantity == -1) {
        // else if (paramQuantity > 1 && !complexSelect) {
        const childName = parseChildName(conditionData, paramQuantity, ruleUuid, true, childrenArr.length, parseI, nameList);
        // const childName = parseChildName(conditionData, paramQuantity, ruleUuid, true, childrenArr.length, oI, nameList);

        childName.map((item, index) => {
          const {
            next
          } = operatorParams[index]
          if (next) {
            let _operatorParams = getOperatorParams(
              next,
              []
            );
            if (_operatorParams) {
              const temObj = {
                align: "center",
                className: "condition-style",
                width: "130px",
                ellipsis: true,
                title: item,
                valueType: _operatorParams[index].valueType,
                dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
                key: colEditingKey + (childrenArr.length).toString() + "child",
                editable: true,
                paramQuantity
              };
              if (_operatorParams[index].enumDictName) {
                temObj.enumDictName = _operatorParams[index].enumDictName
              }
              childrenArr.push(temObj);
            }
          } else {

            const temObj = {
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: item,
              valueType: operatorParams[index].valueType,
              dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
              key: colEditingKey + (childrenArr.length).toString() + "child",
              editable: true,
              paramQuantity
            };
            if (operatorParams[index].enumDictName) {
              temObj.enumDictName = operatorParams[index].enumDictName
            }
            childrenArr.push(temObj);
          }
        });
        if (!conditionData.allCellUnit && oI === oChildren.length - 1 && complexSelect) {
          if (loopEnd !== undefined) {
            if (loopEnd) {
              childrenArr.push({
                align: "center",
                className: "condition-style",
                width: "130px",
                ellipsis: true,
                title: '是否成立',
                valueType: "Boolean",
                dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
                key: colEditingKey + (childrenArr.length).toString() + "child",
                editable: true
              });
            }
          } else {
            childrenArr.push({
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: '是否成立',
              valueType: "Boolean",
              dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
              key: colEditingKey + (childrenArr.length).toString() + "child",
              editable: true
            });
          }
        }
        newData.children = childrenArr;
      } else {
        if (!conditionData.allCellUnit && oI === oChildren.length - 1 && complexSelect) {
          if (loopEnd !== undefined) {
            if (loopEnd) {
              childrenArr.push({
                align: "center",
                className: "condition-style",
                width: "130px",
                ellipsis: true,
                title: '是否成立',
                valueType: "Boolean",
                dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
                key: colEditingKey + (childrenArr.length).toString() + "child",
                editable: true
              });
            }
          }else{
            childrenArr.push({
              align: "center",
              className: "condition-style",
              width: "130px",
              ellipsis: true,
              title: '是否成立',
              valueType: "Boolean",
              dataIndex: colEditingKey + (childrenArr.length).toString() + "child",
              key: colEditingKey + (childrenArr.length).toString() + "child",
              editable: true
            });
          }
        }
        newData.children = childrenArr;
      }
    }
  }
  newData.parseI=parseI
  newData.itemTempSum=itemTempSum
  return newData
}
export function getRuleCondition(
  columns,
  conditionData,
  aliasName,
  colEditingKey,
  ruleUuid,
  nameList,
  conList
) {
  console.log(columns,conditionData,aliasName,colEditingKey,ruleUuid,nameList,conList,'----------------getrulecondition')
  let newData = {};
  const copyData = cloneDeep(columns);
  const editIndex = findEditIndex(columns, colEditingKey);
  const {
    children,
    complexSelect
  } = conditionData;
  const conditions=getLayerConditions(conditionData,ruleUuid)
  const childrenArr = [];
  const childrenArra = [];
  let tableParams = [];
  let itemTempSum = 0;
  let parseI = 0;
  console.log(editIndex,conditions,'-----------------')
  for(let oI = 0; oI < conditions.length; oI++){
    const _children=conditions[oI].children
    let isChildren=false
    if(_children){
      isChildren=true
    }
    newData = loopChildren(conditions[oI],conditionData, aliasName, ruleUuid, nameList, conList, complexSelect, childrenArr, childrenArra, tableParams, itemTempSum, conditions, copyData, editIndex, newData, oI, colEditingKey, parseI,undefined,isChildren)
    parseI=newData.parseI
    itemTempSum=newData.itemTempSum
  }
  // if (children && children.length > 0) {
  //   const childrenArr = [];
  //   const childrenArra = [];
  //   let tableParams = [];
  //   let itemTempSum = 0;
  //   let parseI = 0;
  //   for (let oI = 0; oI < children.length; oI++) {
  //     const _children=children[oI].children
  //     let isChildren=false
  //     if(_children){
  //       isChildren=true
  //     }
  //     newData = loopChildren(children[oI], conditionData, aliasName, ruleUuid, nameList, conList, complexSelect, childrenArr, childrenArra, tableParams, itemTempSum, children, copyData, editIndex, newData, oI, colEditingKey, parseI,undefined,isChildren)
  //     parseI=newData.parseI
  //     itemTempSum=newData.itemTempSum
  //   }
  // }
  console.log(newData,'-----------------')
  return newData;
}
function getLayerConditions(conditionData,ruleUuid){
  const {
    conditions
  } = util.getConditionDataToBE(conditionData, {
      conditions: [],
      conditionExpression: "",
    },
    ruleUuid);
  return conditions
}
function getLoopMethodPar(methodParams,tableParams,oI){
  methodParams && methodParams.map((item, index) => {
    const {
      value = [], valueType, next, variableType
    } = item;
    if (variableType && variableType === "expression") {
      const expressionTreeData = item.expressionTreeData;
      if (expressionTreeData && expressionTreeData.params) {
        loopExpressionTreeData(
          expressionTreeData.params
        );
      }

      function loopExpressionTreeData(data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].data && data[i].data.next) {
            // 函数
            const methodParams = data[i].data.next.methodParams;
            if (methodParams && methodParams.length > 0) {
              const next = data[i].data.next
              methodParams && methodParams.map((item, index) => {

                if (!valueType.includes(".")) {
                  const {
                    value = [], valueType
                  } = item;
                  if (
                    !value || value.length === 0 ||
                    (value.length > 0 && value[0] === undefined)
                  ) {
                    item.outI = oI
                    item.parseI = index
                    tableParams.push(item);
                  }
                } else {
                  const {
                    value,
                    valueType
                  } = item;
                  if (
                    !value || value.length === 0
                  ) {
                    item.outI = oI
                    item.parseI = index
                    tableParams.push(item);
                  }
                }
              });
            } else {
              const next = data[i].data.next
              const {
                value = false, valueType
              } = next;
              if (next) {
                if (!valueType.includes(".")) {
                  if (
                    !value || value.length === 0 ||
                    (value.length > 0 && value[0] === undefined)
                  ) {
                    item.outI = oI
                    item.parseI = index
                    tableParams.push(item);
                  }
                }
              } else {
                if (!valueType.includes(".")) {
                  if (
                    !value || value.length === 0 ||
                    (value.length > 0 && value[0] === undefined)
                  ) {
                    item.outI = oI
                    item.parseI = index
                    tableParams.push(item);
                  }

                }
              }
            }
          } else if (data[i].type === "variable") {
            const {
              valueType,
              value
            } = data[i].data
            if (!valueType.includes(".")) {
              if (
                !value || value.length === 0 ||
                (value.length > 0 && value[0] === undefined)
              ) {
                data[i].data.outI = oI
                data[i].data.parseI = index
                tableParams.push(data[i].data);
              }

            }
          } else if (data[i].type === "expression") {
            loopExpressionTreeData(data[i].params);
          }
        }
      }
    } else if (!valueType.includes(".")) {
      if (
        !value || value.length === 0 ||
        (value.length > 0 && value[0] === undefined)
      ) {
        item.outI = oI
        item.parseI = index
        tableParams.push(item);
      }

    } else if (next) {
      const {next: _next, methodParams}=next
      if(_next){
        const _method=getLastMethodParams(_next, []);
        getLoopMethodPar(_method,tableParams,oI)
      }else{
        if (methodParams && methodParams.length > 0) {
          methodParams && methodParams.map((item, index) => {
            const {
              value = [], valueType
            } = item;
            if (!valueType.includes(".")) {
              if (
                !value || value.length === 0 ||
                (value.length > 0 && value[0] === undefined)
              ) {
                item.outI = oI
                item.parseI = index
                tableParams.push(item);
              }

            } else {
              if (
                !value || value.length === 0 ||
                (value.length > 0 && value[0] === undefined)
              ) {
                item.outI = oI
                item.parseI = index
                tableParams.push(item);
              }

            }
          });
        }
      }
    }
  });
  return tableParams
}
function getLastMethodParams(data, res = []) {
  if (!data) return {};
  const {
    next,
    methodParams, expressionTreeData,params ,expressionParams
  } = data;
  if(data.variableType==="constant" && !next){
    res.push(data)
  }
  if (methodParams) {
    if (!next) {
      res.push(...methodParams)
      return res;
    } else {
      res.push(...methodParams)
      return getLastMethodParams(next, res);
    }
  } else if (res.length > 0 && !next) {
    return res
  }
  
  if(expressionTreeData){
    return getLastMethodParams(expressionTreeData, res);
  }
  if (params&&isArray(params)) {
    for(let i=0;i<params.length;i++){
      if(params[i].params){
        if(i===params.length-1){
          return getLastMethodParams(params[i].params, res);
        }else{
          getLastMethodParams(params[i].params, res);
        }
      }else if(params[i].data){
        if(i===params.length-1){
          return getLastMethodParams(params[i].data, res);
        }else{
          getLastMethodParams(params[i].data, res);
        }
      }
    }
  }
  if (expressionParams&&isArray(expressionParams)) {
    for(let i=0;i<expressionParams.length;i++){
      if(expressionParams[i]){
        if(i===expressionParams.length-1){
          return getLastMethodParams(expressionParams[i], res);
        }else{
          getLastMethodParams(expressionParams[i], res);
        }
      }
    }
  }
  if (next) {
    return getLastMethodParams(next, res);
  }
  if(isArray(data)){
    for(let i=0;i<data.length;i++){
      if(data[i].params){
        if(i===data.length-1){
          return getLastMethodParams(data[i].params, res);
        }else{
          getLastMethodParams(data[i].params, res);
        }
      }else if(data[i].data){
        if(i===data.length-1){
          return getLastMethodParams(data[i].data, res);
        }else{
          getLastMethodParams(data[i].data, res);
        }
        
      }
    }
  }
  return null
}

export function getRuleAction(
  columns,
  actionData,
  aliasName,
  colEditingKey,
  nameList
) {
  let newData = {};
  const copyData = cloneDeep(columns);
  const editIndex = findEditIndex(columns, colEditingKey);
  addFirst = false
  const {
    actionParams,
    actionType,
    titleComplete
  } = actionData[0];


  if (actionType !== "invokeMethod") {
    newData = {
      ...copyData[editIndex],
      viewName: aliasName,
      title: aliasName,
      properties: {
        aliasName,
        rule: actionData
      }
    };
    if (actionParams[1] && Object.keys(actionParams[1]).length > 0) {
      const {
        valueType = "String"
      } = actionParams[1];
      newData.valueType = valueType;
    }
    newData.children = "";
  } else {
    if (nameList === "void") {
      newData = {
        ...copyData[editIndex],
        viewName: aliasName,
        title: aliasName,
        properties: {
          aliasName,
          rule: actionData
        },
        editable: false,
        valueType: "String",
        children: ""
      };
    } else {
      const methodParams = getLastMethodParams(actionParams[0])
      let tableParams = [];
      methodParams && methodParams.map((item, index) => {
        const {
          value = [], valueType, next, variableType
        } = item;
        if (variableType && variableType === "expression") {
          const expressionTreeData = item.expressionTreeData;
          if (expressionTreeData && expressionTreeData.params) {
            loopExpressionTreeData(
              expressionTreeData.params
            );
          }

          function loopExpressionTreeData(data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].data && data[i].data.next) {
                // 函数
                const methodParams = data[i].data.next.methodParams;
                if (methodParams && methodParams.length > 0) {
                  const next = data[i].data.next
                  methodParams && methodParams.map((item, index) => {

                    if (!valueType.includes(".")) {
                      const {
                        value = [], valueType
                      } = item;
                      if (
                        !value || value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      )
                        tableParams.push(item);
                    } else {
                      const {
                        value,
                        valueType
                      } = item;
                      if (
                        !value || value.length === 0
                      )
                        tableParams.push(item);
                    }
                  });
                } else {
                  const next = data[i].data.next
                  const {
                    value = false, valueType
                  } = next;
                  if (next) {
                    if (!valueType.includes(".")) {
                      if (
                        !value || value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      ) {
                        tableParams.push(item);
                      }
                    }
                  } else {
                    if (!valueType.includes(".")) {
                      if (
                        !value || value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      ) {
                        tableParams.push(item);
                      }

                    }
                  }
                }
              } else if (data[i].type === "variable") {
                const {
                  valueType,
                  value
                } = data[i].data
                if (!valueType.includes(".")) {
                  if (
                    !value || value.length === 0 ||
                    (value.length > 0 && value[0] === undefined)
                  ) {
                    tableParams.push(data[i].data);
                  }

                }
              } else if (data[i].type === "expression") {
                loopExpressionTreeData(data[i].params);
              }
            }
          }
        } else if (!valueType.includes(".")) {
          if (
            !value || value.length === 0 ||
            (value.length > 0 && value[0] === undefined)
          )
            tableParams.push(item);
        } else if (next) {
          const methodParams = next.methodParams;
          if (methodParams && methodParams.length > 0) {
            methodParams && methodParams.map((item, index) => {
              const {
                value = [], valueType
              } = item;
              if (!valueType.includes(".")) {
                if (
                  !value || value.length === 0 ||
                  (value.length > 0 && value[0] === undefined)
                )
                  tableParams.push(item);
              } else {
                if (
                  !value || value.length === 0 ||
                  (value.length > 0 && value[0] === undefined)
                )
                  tableParams.push(item);
              }
            });
          }
        }
      });

      if (tableParams.length === 1) {
        const childrenArra = [];
        newData = {
          ...copyData[editIndex],
          viewName: aliasName,
          title: aliasName,
          properties: {
            aliasName,
            rule: actionData
          },
          editable: true
        };

        let tempIndex = 0;
        let express = false
        tableParams.map((item, index) => {
          tempIndex = express ? tempIndex + index : index
          if (item.variableType && item.variableType === "expression") {
            const expressionTreeData = item.expressionTreeData;
            if (expressionTreeData && expressionTreeData.params) {
              loopExpressionTreeData(
                expressionTreeData.params
              );
            }

            function loopExpressionTreeData(data) {
              for (let i = 0; i < data.length; i++) {
                if (data[i].data && data[i].data.next) {
                  // 函数
                  const methodParams = data[i].data.next.methodParams;
                  const tableParams = [];
                  methodParams && methodParams.map((item, index) => {
                    const {
                      value = [], valueType
                    } = item;
                    if (!valueType.includes(".")) {
                      if (
                        value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      )
                        tableParams.push(item);
                    }
                  });
                  const _newData = getNewData(tableParams, aliasName, actionData, editIndex, copyData, nameList, colEditingKey, tempIndex)
                  tempIndex = paramIndex
                  if (_newData.children && _newData.children.length > 0) {
                    childrenArra.push(..._newData.children)
                  }
                } else if (data[i].type === "variable") {
                  const _data = data[i].data;
                  if (!_data.valueType.includes(".")) {
                    if (!(_data.value && _data.value.toString().length > 0)) {
                      const temObj = {
                        align: "center",
                        className: "action-style",
                        width: "130px",
                        ellipsis: true,
                        title: nameList ? nameList[tempIndex].value : '参数1',
                        valueType: _data.valueType,
                        dataIndex: colEditingKey + (tempIndex).toString() + "child",
                        key: colEditingKey + (tempIndex).toString() + "child",
                        editable: true
                      }
                      globalIndex = tempIndex
                      childrenArra.push(temObj);
                      tempIndex++
                      express = true
                    }
                  }
                } else if (data[i].type === "expression") {
                  loopExpressionTreeData(data[i].params);
                }
              }
            }
          } else {
            const temObj = {
              align: "center",
              className: "action-style",
              width: "130px",
              ellipsis: true,
              title: nameList ? nameList[index].value : '参数1',
              valueType: item.valueType,
              dataIndex: colEditingKey + nameList[index].keyI + "child",
              key: colEditingKey + nameList[index].keyI + "child",
              editable: true
            };
            childrenArra.push(temObj);
          }
          tempIndex++
          express = false
        });
        newData.children = childrenArra;
      } else {
        const childrenArra = [];
        newData = {
          ...copyData[editIndex],
          viewName: aliasName,
          title: aliasName,
          properties: {
            aliasName,
            rule: actionData
          },
          editable: true
        };
        let tempIndex = 0;
        let express = false
        let tabLength = tableParams.length
        globalI = 0;
        tableParams.map((item, index) => {
          tempIndex = express ? tempIndex + index : index
          tempIndex = addFirst ? tempIndex + index : index
          if (item.variableType && item.variableType === "expression") {
            const expressionTreeData = item.expressionTreeData;
            if (expressionTreeData && expressionTreeData.params) {
              loopExpressionTreeData(
                expressionTreeData.params
              );
            }

            function loopExpressionTreeData(data) {
              for (let i = 0; i < data.length; i++) {
                if (data[i].data && data[i].data.next) {
                  // 函数
                  const methodParams = data[i].data.next.methodParams;
                  if (methodParams && methodParams.length > 0) {
                    const next = data[i].data.next
                    const tableParams = [];
                    methodParams && methodParams.map((item, index) => {
                      const {
                        value = [], valueType
                      } = item;
                      if (!valueType.includes(".")) {
                        if (
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        )
                          tableParams.push(item);
                      }
                    });
                    const _newData = getNewData(tableParams, aliasName, actionData, editIndex, copyData, nameList, colEditingKey, tempIndex)
                    tempIndex = paramIndex
                    if (_newData.children && _newData.children.length > 0) {
                      childrenArra.push(..._newData.children)
                    }
                    if (index !== tabLength - 1) {
                      addFirst = true
                    } else {
                      addFirst = false
                    }
                  } else {
                    const next = data[i].data.next
                    const {
                      value = false, valueType
                    } = next;
                    if (next) {
                      if (!valueType.includes(".")) {
                        if (value && value.toString().length > 0) {
                          paramIndex = paramIndex > 0 ? paramIndex - 1 : 0
                          tempIndex = paramIndex
                          paramIndex++
                          addFirst = false
                        } else {
                          tempIndex = paramIndex
                          paramIndex++
                          addFirst = false
                        }
                      } else {

                        tempIndex = paramIndex
                        paramIndex++
                        addFirst = false
                      }
                    } else {

                      tempIndex = paramIndex
                      paramIndex++
                      addFirst = false
                    }
                    // if (index !== tabLength - 1) {
                    //   addFirst = true
                    // } else {
                    //   addFirst = false
                    // }
                  }
                } else if (data[i].type === "variable") {
                  if (addFirst) {
                    // paramI = globalIndex + 1
                  }
                  const _data = data[i].data;
                  if (!_data.valueType.includes(".")) {
                    if (!(_data.value && _data.value.toString().length > 0)) {
                      const temObj = {
                        align: "center",
                        className: "action-style",
                        width: "130px",
                        ellipsis: true,
                        title: nameList ? nameList[tempIndex].value : '参数1',
                        valueType: _data.valueType,
                        dataIndex: colEditingKey + (tempIndex).toString() + "child",
                        key: colEditingKey + (tempIndex).toString() + "child",
                        editable: true
                      }
                      globalI++
                      globalIndex = tempIndex
                      childrenArra.push(temObj);
                      tempIndex++
                      express = true
                      if (index !== tabLength - 1) {
                        addFirst = true
                      } else {
                        addFirst = false
                      }
                    }
                  }
                } else if (data[i].type === "expression") {
                  loopExpressionTreeData(data[i].params);
                }
              }
            }
          } else {
            let paramI = index
            if (addFirst) {
              paramI = globalIndex + 1
              globalIndex += 1
            }
            const temObj = {
              align: "center",
              className: "action-style",
              width: "130px",
              ellipsis: true,
              title: nameList ? nameList[paramI].value : '参数1',
              valueType: item.valueType,
              dataIndex: colEditingKey + paramI.toString() + "child",
              key: colEditingKey + paramI.toString() + "child",
              editable: true
            };
            globalI++
            // const temObj = {
            //   align: "center",
            //   className: "action-style",
            //   width: "130px",
            //   ellipsis: true,
            //   title: nameList ? nameList[index].value : '参数1',
            //   valueType: item.valueType,
            //   dataIndex: colEditingKey + index.toString() + "child",
            //   key: colEditingKey + index.toString() + "child",
            //   editable: true
            // };
            tempIndex++
            childrenArra.push(temObj);
          }
          express = false
        })
        newData.children = childrenArra;
      }
    }
  }

  newData.titleComplete = titleComplete;

  return newData;
}

function getNewData(tableParams, aliasName, actionData, editIndex, copyData, nameList, colEditingKey, tempIndex = 0) {
  let newData = {};
  if (tableParams.length > 0) {
    if (tableParams.length === 1) {
      const childrenArra = [];
      newData = {
        ...copyData[editIndex],
        viewName: aliasName,
        title: aliasName,
        properties: {
          aliasName,
          rule: actionData
        },
        editable: true
      };
      // let tempIndex = 0;
      paramIndex = tempIndex
      tableParams.map((item, index) => {
        paramIndex += index
        if (item.variableType && item.variableType === "expression") {
          const expressionTreeData = item.expressionTreeData;
          if (expressionTreeData && expressionTreeData.params) {
            loopExpressionTreeData(
              expressionTreeData.params
            );
          }

          function loopExpressionTreeData(data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].data && data[i].data.next) {
                const methodParams = data[i].data.next.methodParams;
                const tableParams = [];
                methodParams && methodParams.map((item, index) => {
                  const {
                    value = [], valueType
                  } = item;
                  if (!valueType.includes(".")) {
                    if (
                      value.length === 0 ||
                      (value.length > 0 && value[0] === undefined)
                    )
                      tableParams.push(item);
                  }
                });
              } else if (data[i].type === "variable") {
                const _data = data[i].data;
                if (!_data.valueType.includes(".")) {
                  if (!(_data.value && _data.value.toString().length > 0)) {
                    const temObj = {
                      align: "center",
                      className: "action-style",
                      width: "130px",
                      ellipsis: true,
                      title: nameList ? nameList[paramIndex].value : '参数1',
                      valueType: _data.valueType,
                      dataIndex: colEditingKey + (paramIndex).toString() + "child",
                      key: colEditingKey + (paramIndex).toString() + "child",
                      editable: true
                    }
                    globalIndex = paramIndex
                    childrenArra.push(temObj);
                    paramIndex++
                  }
                }
              } else if (data[i].type === "expression") {
                loopExpressionTreeData(data[i].params);
              }
            }
          }
        } else {
          const temObj = {
            align: "center",
            className: "action-style",
            width: "130px",
            ellipsis: true,
            title: nameList ? nameList[paramIndex].value : '参数1',
            valueType: item.valueType,
            dataIndex: colEditingKey + paramIndex.toString() + "child",
            key: colEditingKey + paramIndex.toString() + "child",
            editable: true
          };
          globalIndex = paramIndex
          paramIndex++
          childrenArra.push(temObj);
        }
      });
      newData.children = childrenArra;
    } else {
      const childrenArra = [];
      newData = {
        ...copyData[editIndex],
        viewName: aliasName,
        title: aliasName,
        properties: {
          aliasName,
          rule: actionData
        },
        editable: true
      };
      // let tempIndex = 0;
      paramIndex = tempIndex
      tableParams.map((item, index) => {
        if (item.variableType && item.variableType === "expression") {
          paramIndex += index
          const expressionTreeData = item.expressionTreeData;
          if (expressionTreeData && expressionTreeData.params) {
            loopExpressionTreeData(
              expressionTreeData.params
            );
          }

          function loopExpressionTreeData(data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].type === "variable") {
                const _data = data[i].data;
                if (!_data.valueType.includes(".")) {
                  if (!(_data.value && _data.value.toString().length > 0)) {
                    const temObj = {
                      align: "center",
                      className: "action-style",
                      width: "130px",
                      ellipsis: true,
                      title: nameList ? nameList[paramIndex].value : '参数1',
                      valueType: _data.valueType,
                      dataIndex: colEditingKey + (paramIndex).toString() + "child",
                      key: colEditingKey + (paramIndex).toString() + "child",
                      editable: true
                    }
                    globalIndex = paramIndex
                    childrenArra.push(temObj);
                    paramIndex++
                  }
                }
              } else if (data[i].type === "expression") {
                loopExpressionTreeData(data[i].params);
              }
            }
          }
        } else {
          const temObj = {
            align: "center",
            className: "action-style",
            width: "130px",
            ellipsis: true,
            title: nameList ? nameList[paramIndex].value : '参数1',
            valueType: item.valueType,
            dataIndex: colEditingKey + paramIndex.toString() + "child",
            key: colEditingKey + paramIndex.toString() + "child",
            editable: true
          };
          globalIndex = paramIndex
          paramIndex++
          childrenArra.push(temObj);
        }
      })
      newData.children = childrenArra;
    }
  } else {
    globalIndex = paramIndex
    paramIndex++
  }
  return newData;
}

function getRightTypeFn(item) {
  let res = "";

  function getRightType(obj) {
    if (obj.next) {
      getRightType(obj.next);
    } else {
      res = obj.valueType;
    }
  }
  getRightType(item);
  return res;
}

export function getToBEData(data = [], columns = [], ruleUuid, nameList, conList) {
  const typeList = ["Float", "Integer", "Double", "Short", "BigDecimal"];
  const toBEruleAttributes = [{
    name: "enabled",
    dict: "true"
  }];
  const toBEdefinition = {
    conditions: [],
    actions: []
  };
  const toBErows = [];

  for (let index = 0; index < data.length; index++) {
    toBErows.push({
      cells: []
    });
  }
  for (let j = 0; j < toBErows.length; j++) {
    const element = toBErows[j];
    for (let k = 0; k < columns.length; k++) {
      element.cells.push(cloneDeep({
        variables: []
      }));
    }
  }
  columns.map((item, index) => {
    if (item.colType === "condition") {
      const {
        complexSelect,
        allCellUnit
      } = item.properties.rule
      const toBECondition2 = util.getConditionDataToBE(item.properties.rule, {
          conditions: [],
          conditionExpression: "",
        },
        ruleUuid)
      const toBECondition = toBECondition2
        .conditions;
      const itemkey = item.dataIndex;
      if (toBECondition && toBECondition.length > 0) {
        let allNameList = []
        for (let tbI = 0; tbI < toBECondition.length; tbI++) {
          const copyCondition = cloneDeep(toBECondition[tbI]);
          copyCondition.aliasName = item.viewName;
          let comparator = {}
          if (item.children.length > 0) {
            copyCondition.nameList = item.children.map(item => item.title);
            allNameList = copyCondition.nameList
          }
          comparator = toBECondition[tbI].comparator;
          !complexSelect && toBEdefinition.conditions.push(copyCondition);
          const {
            variable
          } = copyCondition
          const te_methodPar = getLastMethodParams(variable,[])
          let methodPar=[]
          if(te_methodPar){
            for(let mp=0;mp<te_methodPar.length;mp++){
              const {next}=te_methodPar[mp]
              if(next){
                if(getLastMethodParams(next,[])){
                  methodPar.push(te_methodPar[mp])
                }
              }else{
                methodPar.push(te_methodPar[mp])
              }
            }
          }else{
            methodPar = te_methodPar
          }
          if (methodPar && methodPar.length > 0) {
            for (let j = 0; j < toBErows.length; j++) {
              for (let i = 0; i < methodPar.length; i++) {
                const cloMe = cloneDeep(methodPar[i])
                const {
                  variableType
                } = cloMe
                if (variableType === "constant") {

                  const tempJ = {
                    variableType: cloMe.variableType,
                    valueType: cloMe.valueType,
                    value: ""
                  }
                  if (cloMe.enumDictName) {
                    tempJ.enumDictName = cloMe.enumDictName
                  }
                  if (cloMe.value!==0&&(!cloMe.value || cloMe.value.length === 0)) {
                    toBErows[j].cells[index].variables.push(tempJ);
                  }
                }
              }
            }
          }
          if (comparator && Object.keys(comparator).length > 0 && comparator.operatorName) {

            for (let j = 0; j < toBErows.length; j++) {
              const valueArr = cloneDeep(
                comparator.operatorParams
              );
              if (valueArr && valueArr[0]) {
                delete valueArr[0].name;
                delete valueArr[0].instanceName;
                delete valueArr[0].viewName;
                valueArr[0].variableType = "constant";
              }
              const newArr = cloneDeep(valueArr);
              if (valueArr) {

                valueArr.map((itemV, i) => {
                  const {
                    next
                  } = itemV
                  if (next) {
                    let _operatorParams = getOperatorParams(
                      next,
                      []
                    );
                    _operatorParams && _operatorParams.map((itemV, i) => {
                      if (itemV.expressionParams) {
                        itemV.expressionParams.map((itemEx, itemI) => {
                          if (itemEx.variableType === 'constant' && (itemEx.value!==0&&(!itemEx.value || itemEx.value.length <= 0))) {
                            toBErows[j].cells[index].variables.push(itemEx);
                          }
                        })

                      } else {
                        if (itemV.variableType === 'constant' && (itemV.value!==0&&(!itemV.value || itemV.value.length <= 0))) {
                          toBErows[j].cells[index].variables.push(itemV);
                        }
                      }
                    })
                  } else {

                    if (itemV.expressionParams) {
                      itemV.expressionParams.find(itemEx => {
                        if (itemEx.variableType === 'constant' && (itemEx.value!==0&&(!itemEx.value || itemEx.value.length <= 0))) {
                          toBErows[j].cells[index].variables.push(itemEx);
                        }
                      })

                    } else {
                      if (itemV.variableType === 'constant' && (itemV.value!==0&&(!itemV.value || itemV.value.length <= 0))) {
                        toBErows[j].cells[index].variables.push(newArr[i]);
                      }
                    }
                  }
                })
              }

            }
          } else {
            for (let j = 0; j < toBErows.length; j++) {
              const element = toBErows[j];
              element.cells[index].variables[0] = {
                variableType: "constant",
                valueType: "",
                value: ""
              };
            }
          }

        }
        if (complexSelect) {
          let vari = {
            cellUnitConExpression: toBECondition2.conditionExpression.slice(1, -1),
            cellUnitConParams: cloneDeep(toBECondition),
            valueType: "Boolean",
            variableType: "cellUnitCon"
          }
          let compa = {
            operatorName: "eq",
            paramQuantity: 1,
            operatorViewName: "等于(#1,<布尔值>)",
            operatorParams: [{
              valueType: "Boolean",
              variableType: "constant",
              enumDictName: "boolean"
            }],
            enumDictName: "boolean"
          }
          if (allCellUnit) {
            compa.operatorParams[0].value = allCellUnit
          }
          toBEdefinition.conditions.push({
            variable: vari,
            leftValueType: "Boolean",
            comparator: compa,
            aliasName: item.viewName,
            nameList: allNameList
          })
          // let conComparator = copyCondition.comparator
          // if (conComparator && Object.keys(conComparator).length > 0 && conComparator.operatorName) {
          //   const itemkey = item.dataIndex;
          //   for (let j = 0; j < toBErows.length; j++) {
          //     const valueArr = cloneDeep(
          //       conComparator.operatorParams
          //     );
          //     if (valueArr && valueArr[0]) {
          //       delete valueArr[0].name;
          //       delete valueArr[0].instanceName;
          //       delete valueArr[0].viewName;
          //       valueArr[0].variableType = "constant";
          //     }
          //     const newArr = cloneDeep(valueArr);
          //     if (valueArr) {
          //       valueArr.map((itemV, i) => {
          //         if (!itemV.value || itemV.value.length <= 0) {
          //           toBErows[j].cells[index].variables.push(newArr[i]);
          //         }
          //       })
          //     }
          //   }
          // }

        }
        for (let j = 0; j < toBErows.length; j++) {
          if (!allCellUnit && complexSelect) {
            toBErows[j].cells[index].variables.push({
              enumDictName: "boolean",
              value: "",
              valueType: "Boolean",
              variableType: "constant"
            });
          }
          // 单元格数据加坐标
          const {cells}=toBErows[j]
          if(cells&&cells.length>0){
            for(let ci=0;ci<cells.length;ci++){
              const {variables} = cells[ci]
              if(variables && variables.length>0){
                variables.map((item,vi)=>{
                  item.rowIndex=j
                  if(vi===0){
                    if(ci===0){
                      item.colIndex=ci
                    }else{
                      item.colIndex=Number(cells[ci-1].variables[cells[ci-1].variables.length-1].colIndex+1)
                    }
                  }else{
                    item.colIndex=Number(cells[ci].variables[vi-1].colIndex+1)
                  }
                })
              }
            }
          }
          
          if (item.children.length > 0) {
            for (let i = 0; i < item.children.length; i++) {
              if (
                typeList.includes(
                  toBErows[j].cells[index].variables[i].valueType
                )
              ) {
                const value = data[j][itemkey + i + "child"];
                toBErows[j].cells[index].variables[i].value = value;
              } else {
                toBErows[j].cells[index].variables[i].value =
                  data[j][itemkey + i + "child"] || "";
              }
              if (i > 0) {
                delete toBErows[j].cells[index].variables[i].viewName;
              }
              toBErows[j].cells[index].variables[i].variableType = "constant";
              if(data[j][itemkey + i + "childelseFlag"]){
                toBErows[j].cells[index].variables[i].elseFlag=true;
              }else{
                // delete toBErows[j].cells[index].variables[i].elseFlag;
              }
              // if(item.children[i].elseObj && Object.keys(item.children[i].elseObj).length>0){
              //   for(let elseKey in item.children[i].elseObj){
              //     if(elseKey==j){
              //       if(item.children[i].elseObj[j]){
              //         toBErows[j].cells[index].variables[i].elseFlag=true;
              //       }else{
              //         delete toBErows[j].cells[index].variables[i].elseFlag;
              //       }
              //     }
              //   }
              // }
            }
          } else {
            if (toBErows[j].cells[index].variables && toBErows[j].cells[index].variables.length > 0) {
              toBErows[j].cells[index].variables[0].value = data[j][itemkey];
            } else {
              toBErows[j].cells[index].variables[0] = {
                variableType: "constant",
                valueType: "String",
                value: data[j][itemkey]
              }
            }
          }
        }
      }


    }
    if (item.colType === "action") {
      const toBEAction = util.getActionDataToBE(item.properties.rule, ruleUuid);
      const newObj = cloneDeep(toBEAction);
      const leftItem = toBEAction[0].actionParams[0];
      newObj[0].actionParams = toBEAction[0].actionParams;
      newObj[0].titleComplete = toBEAction[0].titleComplete;
      newObj[0].aliasName = item.viewName;
      toBEdefinition.actions.push(newObj[0]);
      const itemkey = item.dataIndex;
      if (toBEAction[0].actionType === "setValue") {
        for (let j = 0; j < toBErows.length; j++) {
          toBErows[j].cells[index].variables = [{
            variableType: "constant",
            valueType: "",
            value: ""
          }];
          toBErows[j].cells[index].variables[0].valueType = getRightTypeFn(
            leftItem
          );
          toBErows[j].cells[index].variables[0].value = data[j][itemkey];
        }
      } else {
        for (let j = 0; j < toBErows.length; j++) {
          const paramNext = toBEAction[0].actionParams[0] && toBEAction[0].actionParams[0].next;
          let methodPar = null
          if (paramNext) {
            methodPar = getLastMethodParams(paramNext)
          }
          if (!paramNext || (paramNext && !methodPar)) {
            toBErows[j].cells[index].variables = [{
              variableType: "void",
              valueType: "String",
              value: ""
            }];
          } else {
            const valueArr = cloneDeep(methodPar);
            const tableParams = [];
            valueArr.map((item) => {
              const {
                value = [], valueType, variableType, next
              } = item;
              if (variableType === 'expression') {
                item.expressionParams.map((item_exp) => {
                  if (!item_exp.valueType.includes(".")) {
                    if (!(item_exp.value && item_exp.value.toString().length > 0)) {
                      tableParams.push(item_exp);
                    }
                  } else if (item_exp.next) {
                    const methodParams = item_exp.next.methodParams;
                    methodParams && methodParams.map((item, index) => {
                      const {
                        value = [], valueType
                      } = item;
                      if (!valueType.includes(".")) {
                        if (
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        )
                          tableParams.push(item);
                      }
                    });
                  }
                })
              } else {
                if (!valueType.includes(".")) {
                  if (
                    value.length === 0 ||
                    (value.length > 0 && value[0] === undefined)
                  )
                    tableParams.push(item);
                } else if (next) {
                  const methodParams = next.methodParams;
                  methodParams && methodParams.map((item, index) => {
                    const {
                      value = [], valueType
                    } = item;
                    if (!valueType.includes(".")) {
                      if (
                        value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      )
                        if(item.name&&!item.name.includes('predefine')){
                          tableParams.push(item);
                        }
                    }
                  });
                }
              }
            });
            const cloneTableParams = cloneDeep(tableParams);
            if (cloneTableParams.length > 1) {
              newObj[0].nameList =
                item.children && item.children.map(item => item.title);
              for (let k = 0; k < cloneTableParams.length; k++) {
                if (cloneTableParams[k].enumDictName) {
                  delete cloneTableParams[k].enumDictName;
                }
                if (
                  cloneTableParams[k].viewName ||
                  cloneTableParams[k].viewName === ""
                ) {
                  delete cloneTableParams[k].viewName;
                }
                cloneTableParams[k].variableType = "constant";
              }
              toBErows[j].cells[index].variables = [...cloneTableParams];
            } else {
              toBErows[j].cells[index].variables = [{
                variableType: "constant",
                valueType: cloneTableParams[0] && cloneTableParams[0].valueType,
                value: ""
              }];
            }
            if (item.children.length > 0) {
              for (let i = 0; i < item.children.length; i++) {
                const valChild = data[j][item.children[i].dataIndex];
                toBErows[j].cells[index].variables[i].value =
                  valChild || valChild === 0 ? valChild : "";
                toBErows[j].cells[index].variables[i].variableType = "constant";
              }
            } else {
              toBErows[j].cells[index].variables[0].value = data[j][itemkey];
            }
          }
        }
      }
    }
  });
 
  const toBEData = {
    // ruleAttributes: toBEruleAttributes,
    definition: toBEdefinition,
    rows: toBErows
  };
  return toBEData;
}

export function getInitCondition(key, nameNum, colMapList, ruleUuid) {
  let colConditionKey = key;
  let newCondition = nameNum;
  const colMap = cloneDeep(colMapList);
  const initCondition = [{}, {}, {}];
  const initConditionData = {
    variable: {
      valueType: 'String',
      variableType: 'dataEntry',
    },
    leftValueType: 'String',
  };
  const initAction = {
    actionType: "invokeMethod",
    actionParams: [{
      variableType: "field",
      valueType: "String",
      viewName: '',
      value: []
    }, ],
  };
  const initRuleData = {
    conditions: [initConditionData],
    conditionExpression: '#',
    actions: [initAction],
  };
  const _conditionData = util.getConditionTree(
    initRuleData.conditionExpression,
    initRuleData.conditions,
    ruleUuid,
    true
  );
  initCondition.map((item, index) => {
    const initItem = {
      align: "center",
      width: "130px",
      className: "condition-style",
      ellipsis: true,
      dataIndex: "rule" + colConditionKey,
      key: "rule" + colConditionKey,
      viewName: `条件${newCondition}`,
      editable: true,
      title: `条件${newCondition}`,
      colType: CONDITION,
      valueType: "String",
      children: "",
      properties: {
        aliasName: `条件${newCondition}`,
        rule: _conditionData
      }
    };
    colMap.push("rule" + colConditionKey);
    colConditionKey += 1;
    newCondition += 1;
    initCondition[index] = initItem;
  });

  return {
    colConditionKey,
    newCondition,
    colMap,
    initCondition
  };
}

export function getInitActions(key, nameNum, colMapList, ruleUuid) {
  let colActionKey = key;
  let newAction = nameNum;
  const colMap = cloneDeep(colMapList);
  const initConditionData = {
    variable: {
      valueType: 'String',
      variableType: 'dataEntry',
    },
    leftValueType: 'String',
  };
  const _initAction = {
    actionType: "invokeMethod",
    actionParams: [{
      variableType: "field",
      valueType: "String",
      viewName: '',
      value: []
    }, ],
  };
  const initRuleData = {
    conditions: [initConditionData],
    conditionExpression: '#',
    actions: [_initAction],
  };
  const _actionData = util.getActionData(
    initRuleData.actions,
    ruleUuid
  )
  const initAction = [{}];
  initAction.forEach((item, index) => {
    const initItem = {
      align: "center",
      className: "action-style",
      width: "130px",
      ellipsis: true,
      children: "",
      dataIndex: "rule" + colActionKey,
      key: "rule" + colActionKey,
      viewName: `动作${newAction}`,
      editable: true,
      title: `动作${newAction}`,
      colType: ACTION,
      valueType: "String",
      // children: '',
      properties: {
        aliasName: `动作${newAction}`,
        rule: _actionData
      }
    };
    colMap.push("rule" + colActionKey);
    colActionKey += 1;
    newAction += 1;
    initAction[index] = initItem;
  });

  return {
    colActionKey,
    newAction,
    colMap,
    initAction
  };
}

export function getInitCardActions(key, nameNum, colMapList) {
  let colActionKey = key;
  let newAction = nameNum;
  const colMap = cloneDeep(colMapList);

  const initAction = [{}];
  initAction.forEach((item, index) => {
    const initItem = {
      align: "center",
      width: "130px",
      ellipsis: true,
      children: "",
      dataIndex: scoredKey,
      key: scoredKey,
      viewName: "分数",
      editable: true,
      title: `分数`,
      colType: ACTION,
      valueType: "String",
      // children: '',
      properties: {
        aliasName: "分数",
        rule: cardActionData
      }
    };
    colMap.push(scoredKey);
    colActionKey += 1;
    newAction += 1;
    initAction[index] = initItem;
  });

  return {
    colActionKey,
    newAction,
    colMap,
    initAction
  };
}

export function getLeftCols(columns, editIndex, viewName, colKey, addType, ruleUuid) {
  const copyData = cloneDeep(columns);
  const initConditionData = {
    variable: {
      valueType: 'String',
      variableType: 'dataEntry',
    },
    leftValueType: 'String',
  };
  const initAction = {
    actionType: "invokeMethod",
    actionParams: [{
      variableType: "field",
      valueType: "String",
      viewName: '',
      value: []
    }, ],
  };
  const initRuleData = {
    conditions: [initConditionData],
    conditionExpression: '#',
    actions: [initAction],
  };
  const _conditionData = util.getConditionTree(
    initRuleData.conditionExpression,
    initRuleData.conditions,
    ruleUuid,
    true
  );
  const _actionData = util.getActionData(
    initRuleData.actions,
    ruleUuid
  )
  const newData = {
    ...copyData[editIndex],
    viewName,
    dataIndex: colKey,
    key: colKey,
    title: viewName,
    children: "",
    properties: {
      aliasName: viewName,
      rule: addType ? _conditionData : _actionData
    }
  };
  copyData.splice(editIndex, 0, newData);

  return copyData;
}

export function getLeftData(data, colKey) {
  const copyTbData = cloneDeep(data);
  for (let j = 0; j < copyTbData.length; j++) {
    const element = copyTbData[j];
    element[colKey] = "";
  }

  return copyTbData;
}

export function getRightCols(columns, editIndex, viewName, colKey, addType, ruleUuid) {
  const copyData = cloneDeep(columns);
  const initConditionData = {
    variable: {
      valueType: 'String',
      variableType: 'dataEntry',
    },
    leftValueType: 'String',
  };
  const initAction = {
    actionType: "invokeMethod",
    actionParams: [{
      variableType: "field",
      valueType: "String",
      viewName: '',
      value: []
    }, ],
  };
  const initRuleData = {
    conditions: [initConditionData],
    conditionExpression: '#',
    actions: [initAction],
  };
  const _conditionData = util.getConditionTree(
    initRuleData.conditionExpression,
    initRuleData.conditions,
    ruleUuid,
    true
  );
  const _actionData = util.getActionData(
    initRuleData.actions,
    ruleUuid
  )
  const newData = {
    ...copyData[editIndex],
    viewName,
    dataIndex: colKey,
    key: colKey,
    title: viewName,
    children: "",
    properties: {
      aliasName: viewName,
      rule: addType ? _conditionData : _actionData
    }
  };
  copyData.splice(editIndex + 1, 0, newData);

  return copyData;
}

export function getRightData(data, colKey) {
  const copyTbData = cloneDeep(data);
  for (let j = 0; j < copyTbData.length; j++) {
    const element = copyTbData[j];
    element[colKey] = "";
  }

  return copyTbData;
}

function getTableCondition(currentData, ruleUuid) {
  const {
    conditions,
    conditionExpression,
    cellUnit,
    allCellUnit
  } = currentData
  if (conditions) {
    const _conditionData = util.getConditionTree(
      conditionExpression,
      conditions,
      ruleUuid,
      true
    );
    if (cellUnit) {
      _conditionData.cellUnit = true
      _conditionData.complexSelect = true
    }
    if (allCellUnit) {
      _conditionData.allCellUnit = allCellUnit
    }
    return _conditionData;

  } else {

    const newData = cloneDeep(currentData);
    delete newData.aliasName;
    const initRuleData = {
      conditions: [newData],
      conditionExpression: "#"
    };

    const _conditionData = util.getConditionTree(
      initRuleData.conditionExpression,
      initRuleData.conditions,
      ruleUuid,
      true
    );
    if (cellUnit) {
      _conditionData.cellUnit = true
      _conditionData.complexSelect = true
    }
    return _conditionData;
  }
}

function getViewName(name, length, currentData) {
  if (name.includes(" ")) {
    let namA = name.split(" ")
    if (namA.length === length) {
      return name.split(" ");
    }
  }
  const nameArr = [];
  for (let index = 0; index < length; index++) {
    nameArr.push(currentData && currentData[index] ? currentData[index] : `参数${index + 1}`);
  }
  return nameArr;
}
export function getPropsTbCondition(key, colMapList, tableContent, ruleUuid, listStringNameList) {
  let colConditionKey = key;
  const colMap = cloneDeep(colMapList);
  const initCondition = [];
  const {
    definition = {}, rows
  } = tableContent;
  const {
    conditions: propsConditions = []
  } = definition;

  propsConditions.map(() => {
    initCondition.push({});
  });
  initCondition.map((item, index) => {
    const currentData = propsConditions[index];
    const initItem = {
      align: "center",
      className: "condition-style",
      width: "130px",
      ellipsis: true,
      dataIndex: "rule" + colConditionKey,
      key: "rule" + colConditionKey,
      viewName: currentData.aliasName,
      editable: true,
      title: currentData.aliasName,
      colType: CONDITION,
      valueType: currentData.variable.valueType || "String",
      children: "",
      properties: {
        aliasName: currentData.aliasName,
        rule: getTableCondition(currentData, ruleUuid)
      }
    };
    const variables = rows[0].cells[index].variables
    
    const length = variables.length;
    if (length > 0) {
      const childrenArr = [];
      const conditions=currentData.conditions
      const name = currentData.comparator.operatorViewName
        .replace(/\(#[0-9]+,/g, "")
        .replace(/\)/g, "");
      const childName = getViewName(name, length, currentData.nameList);
      childName.map((item, index) => {
        const temObj = {
          align: "center",
          width: "130px",
          className: "condition-style",
          ellipsis: true,
          title: item,
          valueType: variables[index].valueType,
          // valueType: currentData.variable.valueType,
          dataIndex: "rule" + colConditionKey + index.toString() + "child",
          key: "rule" + colConditionKey + index.toString() + "child",
          editable: true
        };
        if (variables[index].enumDictName) {
          temObj.enumDictName = variables[index].enumDictName
        }
        if(conditions&&conditions.length>0&&conditions[index]?.comparator?.paramQuantity){
          temObj.paramQuantity=conditions[index]?.comparator?.paramQuantity
        }
        childrenArr.push(temObj);
        initItem.children = childrenArr;
      });
    }
    colMap.push("rule" + colConditionKey);
    colConditionKey += 1;
    initCondition[index] = initItem;
  });
  // rows.map((rowItem,rowI)=>{
  //   const {cells}=rowItem
  //   if(cells&&cells.length>0){
  //     for(let ci=0;ci<cells.length;ci++){
  //       const {variables}=cells[ci]
  //       if(variables&&variables.length>0){
  //         for(let vi=0;vi<variables.length;vi++){
  //           if(variables[vi].elseFlag){
  //             if(initCondition[ci].children){
 
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }
  // })
  return {
    colConditionKey,
    colMap,
    initCondition
  };
}

function getTableAction(currentData, ruleUuid, isTable) {
  const newData = cloneDeep(currentData);
  delete newData.aliasName;
  const initRuleData = {
    actions: [newData]
  };

  const _actionData = util.getActionData(initRuleData.actions, ruleUuid, isTable);
  return _actionData;
}

export function getPropsTbAction(key, colMapList, tableContent, ruleUuid, isTable) {
  let colActionKey = key;
  const colMap = cloneDeep(colMapList);
  const initAction = [];
  const {
    definition = {}, rows
  } = tableContent;
  const {
    actions: propsActions = []
  } = definition;

  propsActions.map(() => {
    initAction.push({});
  });
  initAction.map((item, index) => {
    const currentData = propsActions[index];
    const initItem = {
      align: "center",
      className: "action-style",
      width: "130px",
      ellipsis: true,
      dataIndex: "rule" + colActionKey,
      key: "rule" + colActionKey,
      viewName: currentData.aliasName,
      editable: true,
      title: currentData.aliasName,
      colType: ACTION,
      valueType: currentData.actionParams[0] && currentData.actionParams[0].valueType || "String",
      children: "",
      properties: {
        aliasName: currentData.aliasName,
        rule: getTableAction(currentData, ruleUuid, isTable)
      }
    };
    const length =
      rows[0].cells[rows[0].cells.length - propsActions.length + index] && rows[0].cells[rows[0].cells.length - propsActions.length + index]
      .variables.length;
    if (length > 1) {
      const childrenArr = [];
      for (let i = 0; i < length; i++) {
        const temObj = {
          align: "center",
          className: "action-style",
          title: "参数" + (i + 1),
          width: "130px",
          ellipsis: true,
          // valueType: currentData.variable.valueType,
          valueType: "String",
          dataIndex: "rule" + colActionKey + i.toString() + "child",
          key: "rule" + colActionKey + i.toString() + "child",
          editable: true,
        };
        if (
          initItem.key[4] === "2" &&
          currentData.actionType === "invokeMethod"
        ) {
          temObj.title = currentData.nameList ?
            currentData.nameList[i] :
            "参数" + (i + 1);
        }
        childrenArr.push(temObj);
      }
      initItem.children = childrenArr;
    }
    colMap.push("rule" + colActionKey);
    colActionKey += 1;
    initAction[index] = initItem;
  });
  return {
    colActionKey,
    colMap,
    initAction
  };
}

export function getPropsCardAction(key, colMapList) {
  return {
    ...getInitCardActions(key, 1, colMapList)
  };
}

export function getPropsTbData(columns, propsKey, colMapList, row) {
  let rowKey = propsKey;
  const colMap = cloneDeep(colMapList);

  const initData = [];

  for (let j = 0; j < row.length; j++) {
    initData.push({
      key: rowKey
    });
    rowKey += 1;
    const el = row[j].cells;
    columns.map((item, index) => {
      if (el[index]) {
        const length = el[index] && el[index].variables.length;
        if (length > 0) {
          for (let k = 0; k < length; k++) {
            initData[j][item.dataIndex + k + "child"] =
              el[index].variables[k].value;
            if(el[index].variables[k].elseFlag){
              
              initData[j][item.dataIndex + k + "childelseFlag"]=el[index].variables[k].elseFlag;
              for (let ko = 0; ko < length; ko++) {
                if(!initData[j][item.dataIndex + ko + "childelseFlag"]){
                  initData[j][item.dataIndex + ko + "childReadOnly"]=true
                }
              }
            }
          }
        }
        // else {
        
        if(el[index]){
          initData[j][item.dataIndex] = el[index].variables[0].value;
          if(el[index].variables[0].elseFlag){
            initData[j][item.dataIndex+'elseFlag'] = el[index].variables[0].elseFlag;
          }
        }
        // }
      }
    });
  }
  return {
    rowKey,
    colMap,
    initData
  };
}

export function getPropsValiDate(columns, propsKey, colMapList, row, validate) {
  let rowKey = propsKey;
  const colMap = cloneDeep(colMapList);

  const initData = [];

  for (let j = 0; j < row.length; j++) {
    initData.push({
      key: rowKey
    });
    rowKey += 1;
    const el = row[j].cells;
    columns.map((item, index) => {
      const length = el[index].variables.length;
      if (length > 1) {
        for (let k = 0; k < length; k++) {
          if (validate[j] && validate[j][index].operatorValids) {
            initData[j][item.dataIndex + k + "child"] = {
              value: el[index].variables[k].value,
              ...validate[j][index].operatorValids[k]
            };
          } else {
            initData[j][item.dataIndex + k + "child"] = {
              value: el[index].variables[k].value,
              msg: "校验通过",
              validate: true
            };
          }
        }
      } else {
        if (validate[j]) {
          initData[j][item.dataIndex] = {
            value: el[index].variables[0].value,
            ...validate[j][index]
          };
        } else {
          initData[j][item.dataIndex] = {
            value: el[index].variables[0].value
          };
        }
      }
    });
  }

  return {
    rowKey,
    colMap,
    initData
  };
}

export function getPropsTrackData(
  columns,
  propsKey,
  colMapList,
  row = [],
  trackData
) {
  let rowKey = propsKey;
  const colMap = cloneDeep(colMapList);

  const initData = [];

  for (let j = 0; j < row.length; j++) {
    initData.push({
      key: rowKey
    });
    rowKey += 1;
    const el = row[j].cells;
    columns.map((item, index) => {
      const length = el[index].variables.length;
      if (length > 1) {
        for (let k = 0; k < length; k++) {
          initData[j][item.dataIndex + k + "child"] = {
            value: el[index].variables[k].value,
            ...trackData[j][index]
          };
        }
      } else {
        if (trackData[j]) {
          initData[j][item.dataIndex] = {
            value: el[index].variables[0].value,
            ...trackData[j][index]
          };
        } else {
          initData[j][item.dataIndex] = {
            value: el[index].variables[0].value
          };
        }
      }
    });
  }

  return {
    rowKey,
    colMap,
    initData
  };
}
