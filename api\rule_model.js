import request from '@/utils/request'
// 获得规则模型列表信息
export function list(params) {
  return request({
    url: 'erule/manage/ruleBomModel/list',
    method: 'get',
    params,
  })
}

// 根据参数查询数据字典项信息
export function getDicValueByTypeCode(params) {
  return request({
    url: 'sys/dictionaryValue/getDicValueByTypeCode',
    method: 'get',
    params,
  })
}

// 查询登陆用户的所有的业务条线
export function getAllBusinessByLoginUser(params) {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get',
    params,
  })
}

// 检查jar包是否存在
export function checkJarName(params) {
  return request({
    url: 'erule/manage/ruleBomModel/checkJarName',
    method: 'get',
    params,
  })
}

// 解析jar包内容获取信息
export function ruleBomModelConvertor(params) {
  return request({
    url: 'erule/manage/ruleBomModel/ruleBomModelConvertor',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: params,
  })
}

// 加载模型属性页面数据
export function treeList(params) {
  return request({
    url: 'erule/manage/ruleBomModel/treeList',
    method: 'get',
    params,
  })
}

// 加载数据字典页面数据
export function domainTreeList(params) {
  return request({
    url: 'erule/manage/ruleBomModel/domainTreeList',
    method: 'get',
    params,
  })
}

// 根据uuid查询规则模型信息
export function getRuleBomModelByUuid(params) {
  return request({
    url: 'erule/manage/ruleBomModel/getRuleBomModelByUuid',
    method: 'get',
    params,
  })
}

// 提交bom模型信息
export function submitTree(params) {
  return request({
    url: 'erule/manage/ruleBomModel/submitTree?commitFlag=0',
    method: 'post',
    data: params
  })
}

// 规则模型历史列表
export function hisList(params) {
  return request({
    url: 'erule/manage/bomModelHis/list',
    method: 'get',
    params,
  })
}

// 导出规则模型历史jar包
export function bomExport(params) {
  return request({
    url: 'erule/manage/bomModelHis/export',
    method: 'get',
    params,
    responseType: 'arraybuffer'
  }, 'response')
}

// /获取上传jar包后的参数列表
export function getRuleBomArgList(params) {
  return request({
    url: 'erule/manage/ruleBomModel/getRuleBomArgList',
    method: 'get',
    params,
  })
}
// 规则模型导出
export function ruleBomExport(params) {
  return request({
    url: 'erule/manage/ruleBomModel/export',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}
// 规则模型历史导出
export function ruleBomHisExport(params) {
  return request({
    url: 'erule/manage/bomModelHis/export',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}
// 查询关联关系
export function bomModelRelation(params) {
  return request({
    url: 'erule/manage/ruleBomModel/bomModelRelation',
    method: 'post',
    data: params
  })
}

// /获取上传jar包后的参数列表
export function updateJarFile(params) {
  return request({
    url: 'erule/manage/ruleBomModel/updateJarFile',
    method: 'post',
    data: params,
  })
}
