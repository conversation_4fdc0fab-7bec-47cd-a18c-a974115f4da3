<template>
  <!-- 根据 locked 或 isTrack 的状态决定显示内容 -->
  <span class="actionTypeCom">
    <span v-if="locked || isTrack" class="actionTypeItem txtItem">{{ txt }}</span>
    <a-cascader
      v-else
      :options="selfData"
      :expand-trigger="'hover'"
      :value="[value]"
      :noRuleCellUnit="noRuleCellUnit"
      :isTable="isTable"
      @change="onSelectChange"
    >
      <span class="actionTypeItem txtItem">{{ txt }}</span>
    </a-cascader>
  </span>
</template>

<script setup>
import { findLabel } from "@/components/ruleEditCom/utils/displayUtil";

// 定义组件的 props
const props = defineProps({
  value: {
    type: String,
    default: 'invokeMethod'
  },
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的数据
const selfData = ref([
  { value: "invokeMethod", label: "调用方法" },
  { value: "setValue", label: "设置" },
]);

// 计算属性，用于获取当前选中的标签文本
const txt = computed(() => {
  return findLabel(selfData.value, [props.value]);
});

// 定义事件处理函数
const onSelectChange = (_value, selectedOptions) => {
  emit('onChange', props.pos, selectedOptions);
};

// 定义组件的 emits
const emit = defineEmits(['onChange']);
</script>

