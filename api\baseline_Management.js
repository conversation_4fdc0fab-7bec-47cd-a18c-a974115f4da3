import request from '@/utils/request'
// 基线管理列表
export function getBaseLineInfo(params) {
  return request({
    url: 'erule/manage/snapshoot/list',
    method: 'get',
    params,
  })
}

export function getBaseLineRuleInfo(params) {
  return request({
    url: 'erule/manage/snapshoot/ruleList',
    method: 'get',
    params,
  })
}

// 获取发布信息
export function getBaseLineRel(params) {
  return request({
    url: 'erule/manage/snapshoot/getSnapShootByUuid',
    method: 'get',
    params,
  })
}
export function getSuffix() {
  return request({
    url: 'sys/parameter/getRuleMusterSuffix',
    method: 'get',
  })
}
//新增
export function getBaseLineadd(data) {
  return request({
    url: 'erule/manage/snapshoot/saveOrUpdate',
    method: 'post',
    data
  })
}
// 基线新增(规则库名称下拉列表)
export function getRuleSel() {
  return request({
    url: 'erule/manage/engineering/getEngineeringListByLoginUser',
    method: 'get',
  })
}
// 发布提交
export function getRelSub(data) {
  return request({
    url: 'erule/manage/snapshoot/publishedSave',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
//发布列表
export function getSnapList(params) {
  return request({
    url: 'erule/manage/ruleMuster/getSnapMusterList',
    method: 'get',
    params,
  })
}
// 获取复制文本框信息
export function getcopytex(params) {
  return request({
    url: 'erule/manage/snapshoot/getSnapShootByUuid',
    method: 'get',
    params,
  })
}

//基线复制列表
export function getCopyListInfo(params) {
  return request({
    url: 'erule/manage/snapshoot/getCopyRuleHisList',
    method: 'get',
    params,
  })
}
//复制执行操作(提交)
export function getCopySub(data) {
  return request({
    url: 'erule/manage/snapshoot/copySave',
    method: 'post',
    data,
  })
}
//复制详情
export function getCopyDatails(params) {
  return request({
    url: 'erule/manage/rulelHis/compareRule',
    method: 'get',
    params,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
// 导出
export function getExport(data) {
  return request({
    url: 'erule/manage/snapshoot/publishedExport',
    method: 'post',
    data
  })
}
//导出管理列表
export function getImportList(params) {
  return request({
    url: 'erule/manage/snapShootExportInfo/getSnapShootExportInfoBySnapShootUuid',
    method: 'get',
    params
  })
}
 // 导出规则
 export function getExportRule(params) {
  return request({
    url: '/erule/manage/snapshoot/exportData',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}
// 导出规则(excel)
export function getExportExcelRule(params) {
  return request({
    url: '/erule/manage/snapshoot/exportExcelData',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}
// 下载
export function getExportDown(data) {
  return request({
    url: '/erule/manage/snapShootExportInfo/download',
    method: 'post',
    data,
    responseType: "arraybuffer",
    headers: {
      response: true,
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    }
  })
}
// 导入提交
export function subImport(data) {
  return request({
    url: '/erule/manage/snapshoot/publishedImport',
    method: 'post',
    data,

  })
}
// 导入(业务条线)
export function getBaseLine(params) {
  return request({
    url: '/sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get',
    params,
  })
}
// 导入(发布环境)
export function getPubEnv(params) {
  return request({
    url: '/erule/manage/environment/getEnvironmentListByBusinessLine',
    method: 'get',
    params,
  })
}
// 导入审批意见
export function getApproval(params) {
  return request({
    url: '/erule/demand2/finalAudit/getAuditTypeList',
    method: 'get',
    params,
  })
}
// 需求审核审批意见
export function audit(params) {
  return request({
    url: '/erule/demand2/check/getAuditTypeList',
    method: 'get',
    params,
  })
}
// 获取需要版本比较的基线列表
export function getSnapCompareList(params) {
  return request({
    url: 'erule/manage/snapshoot/getSnapShootByEngUuid',
    method: 'get',
    params,
  })
}
// 基线对比
export function snapShootCompare(params) {
  return request({
    url: 'erule/manage/snapshoot/snapShootCompare',
    method: 'get',
    params,
  })
}
// 通过uuids获取历史规则列表
export function getRuleHisListByUuids(params) {
  return request({
    url: 'erule/manage/rulelHis/getRuleHisListByUuids',
    method: 'get',
    params,
  })
}
// 通过ruleHisUuid获取基线列表
export function getSnapShootListByRuleHisUuid(params) {
  return request({
    url: 'erule/manage/snapshoot/snapShootListByRuleHisUuid',
    method: 'get',
    params,
  })
}

// 批量导出 
export function batchExportData(params) {
  return request({
    url: '/erule/manage/snapshoot/batchExport',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}

// 判断是否有编辑中的规则
export function getInCompleteRuleForEng(params) {
  return request({
    url: 'erule/manage/snapshoot/inCompleteRuleForEng',
    method: 'get',
    params
  })
}

// 批量发布判断
export function batchPublishJudge(params) {
  return request({
    url: 'erule/manage/snapshoot/batchPublishJudge',
    method: 'get',
    params
  })
}

// 批量发布
export function batchPublish(data) {
  return request({
    url: 'erule/manage/snapshoot/batchPublishedSave',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}

// 批量判断是否有编辑中的规则
export function getInCompleteRuleForBatchEng(params) {
  return request({
    url: 'erule/manage/snapshoot/inCompleteRuleForBatchEng',
    method: 'get',
    params
  })
}
