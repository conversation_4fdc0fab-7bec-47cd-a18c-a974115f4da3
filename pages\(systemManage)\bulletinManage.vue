<!--公告管理-->
<script setup lang="ts">
const modal = inject('modal');
import { bulletinList, deleteBulletin } from "@/api/bulletin";
import BulletinAdd from "@/businessComponents/bulletin/BulletinAdd";
import BulletinUpdate from "@/businessComponents/bulletin/BulletinUpdate";
const message = inject('message')
import qs from "qs";
definePageMeta({
    title: '公告管理'
})

const columns = reactive([
    {
        title: '标题',
        dataIndex: 'title',
        key: 'title',
        align: 'left',
        ellipsis: true,
        width:200,
    },
    {
        title: '作者',
        dataIndex: 'writer',
        key: 'writer',
        align: 'center',
        ellipsis: true,
        width:200,
    },
    {
        title: '发布时间',
        dataIndex: 'publishTimeStr',
        key: 'publishTimeStr',
        align: 'center',
        ellipsis: true,
        width:180,
    },
    {
        title: '发布状态',
        dataIndex: 'bulState',
        key: 'bulState',
        align: 'center',
        ellipsis: true,
        width:150,
    }
]);

const fetchBulletin = async (params: Record<string, any> = {}) => {
    try {
        const res = await bulletinList({
            title: params.title,
            epublishTime: params.epublishTime,
            spublishTime: params.spublishTime,
            page: params.page || 1,
            several: params.pageSize || 10,
        });
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        if (message && typeof message === 'object' && 'error' in message) {
            message.error('获取公告失败');
        }
        return {
            data: [],
            totalCount: 0
        };
    }
};

const handleDelete = (record: Record<string, any>) => {
    if (modal && typeof modal === 'object' && 'confirm' in modal) {
        modal.confirm({
            title: '确定删除吗？',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                deleteBulletin(
                    qs.stringify({
                        id: record.id,
                    })
                ).then(() => {
                    if (message && typeof message === 'object' && 'success' in message) {
                        message.success("删除成功");
                    }
                    listLayout.value?.refresh();
                });
            }
        });
    }
};

const isModalVisible = ref(false);
const modalType = ref('');
const datar = ref<Record<string, any> | null>(null);
const isSaving = ref(false);

// 公告新增组件
const bulletinAddComponent = ref()
// 公告更新组件
const bulletinUpdateComponent = ref()

const showModal = (type: string, record: Record<string, any> = {}) => {
    modalType.value = type;
    if (type === 'update') {
        datar.value = record;
    }
    isModalVisible.value = true;
};

// 处理modal ok 事件
function handleModalOk() {
    isSaving.value = true;
    let submitFun
    switch (modalType.value) {
        case 'add':
            submitFun = bulletinAddComponent.value.submitFun;
            break;
        case 'update':
            submitFun = bulletinUpdateComponent.value.submitFun;
            break;
    }
    submitFun && submitFun((success) => {
        if (success) {
            listLayout.value?.refresh();
            isModalVisible.value = false;
        }
        isSaving.value = false;
    });
}

const handleCancel = () => {
    isModalVisible.value = false;
    isSaving.value = false;
};

// 搜索配置
const searchConfig = reactive({
    // 简单搜索字段
    simpleSearchField: {
        label: '标题',
        field: 'title'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '标题',
            field: 'title',
            compType: 'input'
        },
        {
            label: '开始时间',
            field: 'spublishTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD 00:00:00'
            }
        },
        {
            label: '结束时间',
            field: 'epublishTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD 23:59:59'
            }
        }
    ]
});

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加事件
    addNewEvent: () => showModal('add'),
    // 表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchBulletin
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: Record<string, any>) => {
    return [
        {
            key: 'update',
            label: '更新',
            onClick: () => showModal('update', record)
        },
        {
            key: 'delete',
            label: '删除',
            onClick: () => handleDelete(record)
        }
    ];
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

// 数据加载完成处理函数
const handleDataLoaded = (response: any) => {
    if (response && response.data) {
        // 可以在这里处理数据加载完成后的逻辑
    }
};

// 需要自定义渲染的列
const customRenderColumns = ['bulState'];
</script>

<template>
    <ListPage
        ref="listLayout"
        title="公告管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="true"
        :tableColumns="columns"
        :queryMethod="fetchBulletin"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
        :customRenderColumns="customRenderColumns"
    >
        <template #bulState="{ record }">
            <span v-if="record.bulState === 'writting'">编辑中</span>
            <span v-if="record.bulState === 'publishing'">已发布</span>
            <span v-if="record.bulState === 'cancel'">无效</span>
        </template>
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal
        v-if="isModalVisible"
        :visible="isModalVisible"
        :title="modalType === 'add' ? '新增公告' : '更新公告'"
        @ok="handleModalOk"
        @cancel="handleCancel"
        okText="保存"
        :okButtonProps="{ disabled: isSaving }"
    >
        <div style="max-height: 60vh; overflow-y: auto;">
            <BulletinAdd ref="bulletinAddComponent" v-if="modalType === 'add'" />
            <BulletinUpdate ref="bulletinUpdateComponent" v-if="modalType === 'update'" :datar="datar" />
        </div>
    </a-modal>
</template>
