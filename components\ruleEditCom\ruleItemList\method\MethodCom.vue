<script setup>
import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import { getLabelArrByValues } from "@/components/ruleEditCom/utils/displayUtil";
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";

const ruleUuid = inject('ruleUuid', '');

// 定义 emit
const emit = defineEmits(["onChange"]);

const props = defineProps({
  locked: Boolean,
  isTrack: Boolean,
  pos: String,
  variableData: {
    type: Object,
    default: () => ({}),
  },
  methodData: {
    type: Array,
    default: () => ([]),
  },
  dataForLoad: {
    type: Array,
    default: () => ([]),
  },
  titleStr: String,
  refListData: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

const selfData = ref(props.methodData);

const txt = ref("请选择方法");

const FilterTypeList = computed(() => {
  const { paramsList = [] } = props.variableData;
  const typeArr = paramsList ? paramsList.map((item) => item.valueType) : "null";
  return typeArr;
});

const onMethodChange = (newVariableData, finalValueType) => {
  const { pos } = props;
  emit('onChange', pos, newVariableData, finalValueType)
};

const onSelectChange = (pos,value, selectedOptions,operatorOptions) => {
  const [firstOption, lastOption] = selectedOptions;
  const { isFieldItem, valueType } = firstOption;
  let newVariableData = {};
  let finalValueType = valueType;
  if (isFieldItem) {
    newVariableData = getFieldVariable(firstOption);
  } else {
    newVariableData = getMethodVariable(firstOption);
  }

  if (lastOption) {
    lastOption.isMethodItem &&
      (newVariableData.next = getMethodVariable(lastOption));
    lastOption.isFieldItem &&
      (newVariableData.next = getFieldVariable(lastOption));
    finalValueType = lastOption.valueType;
  }

  onMethodChange(newVariableData, finalValueType);
};

const getMethodVariable = (option) => {
  const { paramsList = [], valueType, value } = option;
  const newVariableData = {
    value: [value],
    valueType,
    variableType: "method",
    paramsList,
  };
  newVariableData.methodParams = paramsList.map((item) => {
    const { valueType: param_valueType, domain } = item;
    return {
      value: [],
      valueType: param_valueType,
      enumDictName: domain,
      variableType: "field",
    };
  });
  return newVariableData;
};

const getFieldVariable = (option) => {
  const { valueType, value } = option;
  const newVariableData = {
    value: [value],
    valueType,
    variableType: "field",
  };
  return newVariableData;
};

const filter = (inputValue, path) => {
  return path.some((option) => {
    if (typeof option.label !== "string") {
      return false;
    }
    const opetionLabel = option.label.toLowerCase();
    const _inputValue = inputValue.toLowerCase();
    return opetionLabel.indexOf(_inputValue) > -1;
  });
};

const getChildModelOptions = (valueType) => {
  const { dataForLoad = [] } = props;
  let childModelOptions;
  dataForLoad.forEach((item) => {
    if (
      item.valueType === valueType &&
      item.children &&
      item.children.length > 0
    ) {
      childModelOptions = filterChildOptions(item.children);
    }
  });
  return childModelOptions || [];
};

const filterChildOptions = (options = []) => {
  const { data } = props;
  const { __context } = data || {};
  const { owner = {} } = __context || {};
  const { actionType } = owner;
  return options.filter((item) => {
    if (item.isMethodItem === undefined) {
      return true;
    }
    if (actionType === "setValue") {
      return false;
    }
    if (actionType === "invokeMethod") {
      return item.position === "2";
    }
    if (item.isMethodItem === true) {
      return true;
    }
    return item.position === "1";
  });
};

const getNextOptions = (valueType) => {
  const childModelOptions = getChildModelOptions(valueType);
  const baseMethodList = getBaseMethodList(valueType);
  const nextOptions = [];
  childModelOptions.forEach((item) => {
    if (item.isMethodItem !== true) {
      item.isLeaf = false;
    }
    nextOptions.push(item);
  });
  baseMethodList.forEach((item) => {
    item.isLeaf = true;
    nextOptions.push(item);
  });
  return nextOptions;
};

const getBaseMethodList = (valueType) => {
  const baseMethodListMap =
    store.getters.listMap[ruleUuid].initModelData.baseMethodListMap;
  const _baseMethodList = baseMethodListMap[valueType]
    ? baseMethodListMap[valueType]
    : valueType.includes("List<")
      ? baseMethodListMap["List"]
      : [];
  return _baseMethodList;
};

const loadData = (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1];
  targetOption.loading = true;
  const { valueType = "" } = targetOption;
  setTimeout(() => {
    const baseMethodList = getBaseMethodList(valueType);
    let childrenOptions = [];
    if (valueType.indexOf(".") > 0) {
      childrenOptions = getChildModelOptions(valueType);
    }
    if (baseMethodList.length > 0 || childrenOptions.length > 0) {
      targetOption.children = [];
      targetOption.isLeaf = false;
      childrenOptions.forEach((item) => {
        item.isLeaf = false;
        targetOption.children.push(item);
      });
      baseMethodList.forEach((item) => {
        item.isLeaf = true;
        targetOption.children.push(item);
      });
    } else {
      targetOption.isLeaf = true;
    }

    targetOption.loading = false;
    selfData.value = props.methodData;
  }, 0);
};

const getMethodNameArr = (data, value) => {
  const nameList =
    data && value && value.length > 0
      ? getLabelArrByValues(data, value)
      : [];
  const targetNameObj = nameList.length > 0 ? nameList.splice(0, 1)[0] : {};
  const nameStr = targetNameObj.name || "";
  const reg1 = /#.+?>/g;
  const nameArr = nameStr ? nameStr.split(reg1) : [];
  return nameArr;
};

const onParamItemChange = (
  childPos,
  newChildVariableData,
  dataSourceValueType,
  type
) => {
  const { variableData } = props;
  const newVariableData = variableData;
  const { methodParams } = newVariableData;
  const valueType = util.getRealValueType(newVariableData);
  let pathArr = [];
  let index = null;
  let signIndex = null;
  if (childPos.indexOf('_singleParams_') !== -1) {
    let tempArrPath = [];
    pathArr = childPos.split("_singleParams_");
    tempArrPath = pathArr[0].split("_");
    index = Number(tempArrPath[tempArrPath.length - 1]);
    signIndex = Number(pathArr[pathArr.length - 1]);
  } else {
    pathArr = childPos.split("_");
    index = Number(pathArr[pathArr.length - 1]);
  }
  const { singleParams } = methodParams[index];
  if (singleParams) {
    if (type === "propSelect") {
      methodParams[index] = newChildVariableData;
      delete methodParams[index].singleParams;
    } else {
      singleParams[signIndex] = newChildVariableData;
    }
    onMethodChange(newVariableData, valueType);
  } else {
    methodParams[index] = newChildVariableData;
    onMethodChange(newVariableData, valueType);
  }
};

const onChildChange = (childPos, newValueObj, finalValueType, operatorOptions) => {
  const { variableData, pos } = props;
  const newComData = util.cloneRuleData(variableData);
  const newValueType = finalValueType;
  newComData.next = newValueObj;
  emit('onChange', pos, newComData, newValueType, operatorOptions)
};



const next = computed(() => {
  return props.variableData.next || null;
});

const nextOptions = computed(() => {
  return props.variableData.next ? getNextOptions(props.variableData.valueType) : null;
});

const nextVariableType = computed(() => {
  return props.variableData.next ? props.variableData.next.variableType : null;
});

const nextValueType = computed(() => {
  return props.variableData.next ? props.variableData.next.valueType : null;
});

const nameArr = computed(() => {
  return getMethodNameArr(props.methodData, props.variableData.value);
});

const paramsSelector = (e) => {
  // 去重逻辑
  const uniqueData = selfData.value.filter((item, index, self) => {
    return index === self.findIndex((t) => t.value === item.value);
  });
  
  globalEventEmitter.emit(SHOW_ACASCADER, {
    e,
    pos: props.pos,
    filterOData: uniqueData,
    fn: (pos, value, selectedOptions, operatorOptions) => {
      onSelectChange(pos, value, selectedOptions, operatorOptions);
    },
    flag: 'method',
    cascaderSelectedValue: props.variableData.value,
  });
}

</script>

<template>
  <span class="methodCom">
    <span v-if="locked || isTrack" class="lockedText">
      <span v-for="(item, index) in nameArr" :key="`${pos}_method_${index}_wrapperrSpan`">
        <span class="txtItem">{{ item }}</span>
        <VariableCom v-if="variableData.methodParams && variableData.methodParams[index]"
          :key="`${pos}_method_${index}`" :pos="`${pos}_method_${index}`" :locked="locked" :isTrack="isTrack"
          :dataSource="variableData.methodParams[index]" hideEndBtn
          :enumDictName="variableData.methodParams[index].enumDictName"
          :methodFilterType="FilterTypeList[index] || 'null'" :titleStr="titleStr" :refListData="refListData"
          :variableData="variableData" :isTable="isTable" :noRuleCellUnit="noRuleCellUnit" @change="onParamItemChange"
          :ref="'method_variable_com-' + `${pos}_method_${index}`" />
      </span>
      <span v-if="!nameArr.length" class="txtItem">{{ txt }}</span>
    </span>
    <span v-else @click="paramsSelector">
      <span class="pointer" role="button" tabIndex="-1">
        <span v-for="(item, index) in nameArr" :key="`${pos}_method_${index}_wrapperrSpan`">
          <span class="txtItem">{{ item }}</span>
          <VariableCom v-if="variableData.methodParams && variableData.methodParams[index]"
            :key="`${pos}_method_${index}`" :pos="`${pos}_method_${index}`" :locked="locked" :isTrack="isTrack"
            :dataSource="variableData.methodParams[index]" hideEndBtn
            :enumDictName="variableData.methodParams[index].enumDictName"
            :methodFilterType="FilterTypeList[index] || 'null'" :titleStr="titleStr" :refListData="refListData"
            :variableData="variableData" :isTable="isTable" :noRuleCellUnit="noRuleCellUnit" @change="onParamItemChange"
            :ref="'method_variable_com-' + `${pos}_method_${index}`" />
        </span>
        <span v-if="!nameArr.length" class="txtItem">{{ txt }}</span>
      </span>
    </span>

    <span v-if="next">
      <span v-if="!(nextVariableType === 'method' || nextValueType === 'Boolean')">的</span>
      <VariableCom :pos="pos + '_methodVar_0'" :locked="locked" :isTrack="isTrack" :dataSource="next"
        @change="onChildChange" signValue="" hideFrontBtn hideEndBtn :propOptions="nextOptions" :titleStr="titleStr"
        :refListData="refListData" :isTable="isTable" :noRuleCellUnit="noRuleCellUnit" />
    </span>
  </span>
</template>
