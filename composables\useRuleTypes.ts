import IconRuleTypeOrdinaryRule from '~/components/icon/IconRuleTypeOrdinaryRule.vue'
import IconRuleTypeDecisionTable from '~/components/icon/IconRuleTypeDecisionTable.vue'
import IconRuleTypeDecisionTree from '~/components/icon/IconRuleTypeDecisionTree.vue'
import IconRuleTypeRuleFlow from '~/components/icon/IconRuleTypeRuleFlow.vue'

/**
 * @file 定义规则类型名称与其值及图标的映射
 */
export default function () {
    const ruleTypes = [
        {
            name: '普通规则',
            code: '1',
            icon: IconRuleTypeOrdinaryRule,
            desc: '一种由如果、那么、否则三个部分构成的规则，适用于简单的业务场景。'
        },
        {
            name: '决策表',
            code: '2',
            icon: IconRuleTypeDecisionTable,
            desc: '决策表是一种以表格形式表现规则的工具，适用于复杂的业务场景。'
        },
        {
            name: '决策树',
            code: '4',
            icon: IconRuleTypeDecisionTree,
            desc: '决策树是一种以树状结构表现规则的工具，适用于复杂的业务场景。'
        },
        {
            name: '规则流',
            code: '5',
            icon: IconRuleTypeRuleFlow,
            desc: '规则流是一种以流程图形式表现规则的工具，适用于复杂的业务场景。'
        }
    ]

    return {
        ruleTypes
    }
}