import request from '@/utils/request'

// 用户管理列表
export function list(params) {
  return request({
    url: 'sys/user/list',
    method: 'get',
    params
  })
}

// 修改获取用户信息
export function getUserById(params) {
  return request({
    url: 'sys/user/getUserById',
    method: 'get',
    params
  })
}

// 根据loginId判断用户是否存在
export function checkUserByLoginId(params) {
    return request({
      url: 'sys/user/checkUserByLoginId',
      method: 'get',
      params
    })
  }
// 查询系统所有的业务条线
// export function getAllBusiness() {
//   return request({
//     url: 'sys/dictionaryValue/getAllBusiness',
//     method: 'get'
//   })
// }
// 获取登录用户所有的业务条线
export function getAllBusinessByLoginUser() {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get'
  })
}
//根据用户id查询所有业务条线
export function getAllBusinessByUserId(params) {
  return request({
    url: '/sys/dictionaryValue/getAllBusinessByUserId',
    method: 'get',
    params
  })
}
// 添加或更新用户到数据库
export function saveOrUpdate(params) {
  return request({
    url: 'sys/user/saveOrUpdate',
    method: 'post',
    data: params
  })
}
// 复制用户
export function copySave(params, userId) {
  return request({
    url: `sys/user/copySave?userId=${userId}`,
    method: 'post',
    data: params
  })
}
// 删除用户
export function deleteUser(params) {
  return request({
    url: 'sys/user/delete',
    method: 'post',
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    data: params
  })
}

// 根据用户id获取用户拥有的业务条线
export function getUserBusinessIdsById(params) {
  return request({
    url: 'sys/user/getUserBusinessIdsById',
    method: 'get',
    params
  })
}

// 得到用户所有角色下的所有规则库
export function getEnginesByUserId(params) {
  return request({
    url: 'erule/manage/engineering/getEnginesByUserId',
    method: 'get',
    params
  })
}

// 通过用户id和业务条线id获取权限
// export function getPermissionByIdAndBusinessId(params) {
//   return request({
//     url: 'sys/user/getPermissionByIdAndBusinessId',
//     method: 'get',
//     params
//   })
// }

// 获得机构岗位树
export function listOrgTree(params) {
  return request({
    url: 'sys/org/listOrgTree',
    method: 'get',
    params
  })
}

// 通过业务条线id获得可分配角色列表
export function getRoleByBusinessUuid(params) {
  return request({
    url: 'sys/role/getRoleByBusinessUuid',
    method: 'get',
    params
  })
}

// 根据用户机构和条线获得拥有的角色ids
export function getRoleIdsByUserAndOrgAndBusiness(params) {
  return request({
    url: 'sys/role/getRoleIdsByUserAndOrgAndBusiness',
    method: 'get',
    params
  })
}

// 给用户下某条线的机构设置角色
export function setOrgAction(params) {
  return request({
    url: 'sys/user/setOrgAction',
    method: 'post',
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    data: params
  })
}

// 根据用户id设置用户个性化规则库
export function setEngAction(params) {
  return request({
    url: 'sys/user/setEngAction',
    method: 'post',
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    data: params
  })
}

// 根据用户id获取未勾选的规则库ids
export function getPersonalEngIdsById(params) {
  return request({
    url: 'sys/user/getPersonalEngIdsById',
    method: 'get',
    params
  })
}
// 查询系统登陆模式
export function getLoginType() {
  return request({
    url: '/sys/parameter/getLoginType',
    method: 'get',
  })
}

// 获取当前登录用户信息
export function loginUserInfo() {
  return request({
    url: 'sys/user/loginUserInfo',
    method: 'get'
  })
}
// 修改用户密码
export function changePwd(params) {
  return request({
    url: 'sys/user/saveChangePwd',
    method: 'post',
    data: params
  })
}
// 用户注册校验
export function checkUser(params) {
  return request({
    url: 'sys/user/checkUser',
    method: 'get',
    params
  })
}
// 获取所有用户通知信息
export function getUsersWithNotification(params) {
  return request({
    url: 'sys/user/getUsersWithNotification',
    method: 'get',

  })
}
