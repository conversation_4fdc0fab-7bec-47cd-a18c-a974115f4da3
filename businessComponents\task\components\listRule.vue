<!-- 关联规则 -->
<script setup lang="ts">
    import { demandRuleRelList, revoke, editAuditFlag } from "@/api/task";
    const route = useRoute();
    const message = inject('message');
    const modal = inject('modal')
    const tableData = ref<any[]>([]);
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });
    const props = defineProps({
        infoData: {
            type: Object,
        },
    });
    const columns = [
      {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`,
        ellipsis: true,
        align: 'center',
      },
      { title: '关联规则', dataIndex: 'ruleName', key: 'ruleName', width: '35%', ellipsis: true, align: 'left' },
      { title: '当前版本', dataIndex: 'ruleEdition', key: 'ruleEdition', width: '15%', ellipsis: true, align: 'center' },
      { title: '规则状态', dataIndex: 'ruleStatus', key: 'ruleStatus', width: '15%', ellipsis: true, align: 'center' },
      { title: '测试状态', dataIndex: 'demandTestRuleStatus', key: 'demandTestRuleStatus', width: '15%', ellipsis: true, align: 'center' },
      { title: '历史版本', dataIndex: 'historyVersion', key: 'historyVersion', width: '15%', ellipsis: true, align: 'center' },
      { title: '初始版本', dataIndex: 'originalEdition', key: 'originalEdition', width: '15%', ellipsis: true, align: 'center' },
      { title: '初始状态', dataIndex: 'revokeState', key: 'revokeState', width: '15%', ellipsis: true, align: 'center' },
      { title: '下发标识', dataIndex: 'auditFlag', key: 'auditFlag', width: '15%', ellipsis: true, align: 'center' },
      { title: '操作', dataIndex: 'action', key: 'action', width: '25%', ellipsis: true, align: 'center' },
    ];

    onMounted(() => {
        fetchRules();
    });

    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        fetchRules();
    };
    const emit= defineEmits(["addHis",'addTabs'])
    const updateFun = (row: any) => {
        // 处理详情逻辑
        emit("addTabs",row)
    };


    const version = (row: any) => {
        // 处理历史版本逻辑
        emit("addHis",row)
    };

    const revoke1 = (row: any) => {
        // 处理撤销逻辑
        modal.confirm({
            title: '温馨提示',
            content: '您确定撤销吗?',
            okText: '确定',
            cancelText: '取消',
            type: 'listRule',
            onOk() {
                revoke({ uuid: row.uuid }).then((res) => {
                    if (res.code == 20000) {
                        message.success("撤销成功");
                        fetchRules();
                    }
                });
            },
            onCancel() {
                message.info('已取消');
                fetchRules();
            },
        });
    };

    const fetchRules = () => {
        pagination.value.isLoading = true;
        try {
            const params = {
                demandUuid: props.infoData.uuid,
                page: pagination.value.current,
                several: pagination.value.pageSize,
            };
            demandRuleRelList(params).then((res: any) => {
                if (res.code === 20000) {
                    tableData.value = res.data.data;
                    pagination.value.total = res.data.totalCount;
                    pagination.value.isLoading = false;
                }
            });
        }catch (e) {
            pagination.value.isLoading = false;
            message.error('获取关联规则失败');
        }
    };

    const handleAuditFlagChange = (checked: boolean, row: any) => {
        const flag = checked ? '1' : '0';
        // 处理下发标识逻辑
        modal.confirm({
            title: '温馨提示',
            content: '是否确定将该规则标记为' +
                (flag === "0" ? "通过" : "下发修改") +
                '么？',
            okText: '确定',
            cancelText: '取消',
            type: 'listRule',
            onOk() {
                editAuditFlag({ uuid: row.uuid, auditFlag: flag }).then((res) => {
                    if (res.code == 20000) {
                        message.success("成功");
                        fetchRules();
                    } else {
                       message.error("下发失败");
                    }
                });
            },
            onCancel() {
                message.info('已取消');
            },
        });
    };
</script>
<template>
    <a-table :data-source="tableData" :columns="columns" size="small" :scroll="{y:'100%'}" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading"
             @change="handlePageChange" style="width: 100%">
        <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ruleStatus'">
                <p v-if="record.ruleStatus === '0'">规则已创建</p>
                <p v-if="record.ruleStatus === '1'">规则删除状态</p>
                <p v-if="record.ruleStatus === '2'">规则编辑</p>
            </template>
            <template v-if="column.key === 'demandTestRuleStatus'">
                <p v-if="record.demandTestRuleStatus === '0'">回退</p>
                <p v-if="record.demandTestRuleStatus === '1'">通过</p>
                <p v-if="record.demandTestRuleStatus === '2'">待测试</p>
            </template>
            <template v-if="column.key === 'historyVersion'">
                <a href="javascript:;" @click="version(record)" style="color: #409eff">历史版本</a>
            </template>
            <template v-if="column.key === 'auditFlag'">
                <a-checkbox
                        v-if="props.infoData.type === 'updateRuleCheck'"
                        :checked="record.auditFlag === '1'"
                        @change="(e) => handleAuditFlagChange(e.target.checked, record)"
                ></a-checkbox>
                <div v-else :style="record.auditFlag === '1' ? 'color:red' : ''">
                    <span v-if="record.auditFlag === '1'">下发</span>
                    <span v-else>通过</span>
                </div>
            </template>
            <template v-if="column.key === 'action'">
                <a-button type="link" @click="updateFun(record)">详情</a-button>
                <a-button
                        v-if="props.infoData.type === 'adjustment'"
                        type="link"
                        @click="revoke1(record)"
                >撤销</a-button>
            </template>
        </template>
    </a-table>
</template>
