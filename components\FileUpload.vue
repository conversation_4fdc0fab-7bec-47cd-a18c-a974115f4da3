<!--文件上传组件-->
<template>
    <!--文件上传需再嵌套一层，否则label也可触发上传事件-->
    <a-form-item class="no-margin-form-item">
    <a-upload-dragger
            :accept="acceptType"
            :multiple="false"
            name="file"
            :customRequest="uploadFile"
            :remove="fileRemove"
            :class="uploadHidden?'upload-file-jarFile-style':'upload-file-jarFile-styleShow'"
            :fileList="fileList"
            @change="handleChange"
    >
        <p class="ant-upload-drag-icon">
            <CloudUploadOutlined />
        </p>
        <p class="ant-upload-text">
            将文件拖到此处，或
            <em>点击上传</em>
        </p>

        <!-- 使用插槽自定义文件列表展示 -->
        <template #itemRender="{ file }">
            <div class="custom-upload-item">
                <div class="file-info">
                    <div class="file-status">
                        <loading-outlined v-if="file.status === 'uploading'" class="upload-loading" />
                        <PaperClipOutlined class="upload-success" v-else-if="file.status === 'done'"/>
                    </div>
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                </div>
                <div class="file-status">
                    <DeleteOutlined @click="(e) => { e.stopPropagation(); handleRemove(file); }"/>
                </div>
            </div>
        </template>
    </a-upload-dragger>
    </a-form-item>
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import type { UploadProps, UploadFile } from 'ant-design-vue';
    import { CloudUploadOutlined, FileOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons-vue';

    //定义类型
    type FileCustomRequest = Parameters<NonNullable<UploadProps['customRequest']>>[0];

    const emit = defineEmits(['uploadFile']);
    const uploadHidden = defineModel<boolean>('uploadHidden');
    const accept = defineModel<string | undefined>('accept');
    const acceptType = ref<string>('');

    // 初始化acceptType
    if (accept && typeof accept.value === 'string') {
        acceptType.value = accept.value;
    }

    // 文件列表
    const fileList = ref<UploadFile[]>([]);

    // 文件变化事件处理
    const handleChange: UploadProps['onChange'] = (info) => {
        fileList.value = [...info.fileList];

        // 当文件上传成功时
        if (info.file.status === 'done') {
            uploadHidden.value = true;
        }
    };

    // 手动移除文件
    const handleRemove = (file: UploadFile) => {
        fileList.value = fileList.value.filter(item => item.uid !== file.uid);
        uploadHidden.value = false;
    };

    //文件移除（组件内置移除方法）
    const fileRemove = (file: UploadFile) => {
        uploadHidden.value = false;
        fileList.value = [];
        return true;
    };

    //文件上传
    const uploadFile = (options: FileCustomRequest) => {
        uploadHidden.value = true;
        emit('uploadFile', options);
    };
</script>

<!-- 样式部分 -->
<style lang="scss">
    .no-margin-form-item {
        margin-bottom: 0 !important;
    }

    .upload-file-jarFile-style .ant-upload .ant-upload-btn{
        display: none;
    }

    /* 上传文件后去除边框 */
    .upload-file-jarFile-style .ant-upload.ant-upload-drag {
        border: none;
        background: transparent;
    }

    .upload-file-jarFile-styleShow .ant-upload .ant-upload-btn {
        display: block;
    }

    .custom-upload-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #fafafa;
        border-radius: 4px;
        border: 1px solid #f0f0f0;

        .file-info {
            display: flex;
            align-items: center;
            max-width: 80%;

            .file-icon {
                color: #1890ff;
                margin-right: 8px;
            }

            .file-name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .file-status {
            display: flex;
            align-items: center;

            .upload-loading {
                margin-right: 8px;
            }

            .upload-success {
                margin-right: 8px;
            }

            .upload-remove {
                cursor: pointer;
                &:hover {
                    opacity: 0.8;
                }
            }
        }
    }
</style>
