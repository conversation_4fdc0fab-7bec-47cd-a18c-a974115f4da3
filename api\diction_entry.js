import request from '@/utils/request'

// 字典项列表
export function dicEctryList(params) {
  return request({
    url: 'sys/dictionaryValue/list',
    method: 'get',
    params, 
  })
}
//新增/修改字典项提交
export function dictionSub(data) {
    return request({
      url: 'sys/dictionaryValue/saveOrUpdate',
      method: 'post',
      data, 
    })
  }
// 字典类型/ 父子典类型下拉列表
export function dictionSel() {
    return request({
      url: 'sys/dictionaryType/getAllDicType',
      method: 'get',
    })
  }

//父字典下拉列表
export function dictionParentsel(params) {
    return request({
      url: 'sys/dictionaryValue/getDicValueByTypeCode',
      method: 'get',
      params
    })
  }
//获取修改文本框的值
export function dictionupd(params) {
  return request({
    url: 'sys/dictionaryValue/getDicValueByUuid',
    method: 'get',
    params
  })
}
// 删除
export function dicEntryDel(data) {
  return request({
    url: 'sys/dictionaryValue/delete',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
  },
  })
}



