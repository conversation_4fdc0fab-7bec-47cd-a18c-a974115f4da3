<template>
  <span class="actionMethod">
    <!-- 变量组件 -->
    <VariableCom
      :pos="pos + '_actionMethod_0'"
      :locked="locked"
      :isTrack="isTrack"
      :dataSource="dataSource"
      :noRuleCellUnit="noRuleCellUnit"
      :isTable="isTable"
      :hideFrontBtn="true"
      :hideEndBtn="true"
      :signValue="''"
      @onChange="onActionMethodChange"
      ref="action_method_variable_com"
      :key="pos"
    />
  </span>
</template>

<script setup>
const message = inject("message");
// 定义组件的 props
const props = defineProps({
  pos: {
    type: String
  },
  dataSource: {
    type: Object,
    default: () => ({})
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits([
  'onChange'
]);


// 获取最后一个变量类型
const getLastVariableType = (variableData) => {
  const { next, variableType } = variableData;
  if (next) {
    return getLastVariableType(next);
  }
  return variableType;
};

// 处理动作方法变化
const onActionMethodChange = (_pos, newVariableData) => {
  const variableType = getLastVariableType(newVariableData);
  if (variableType === "method") {
    emit('onChange', _pos, newVariableData);
  } else {
    message.warning("请选择一个方法！");
  }
};
</script>
