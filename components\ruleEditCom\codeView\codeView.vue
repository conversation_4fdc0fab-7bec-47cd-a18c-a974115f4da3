<template>
  <codemirror
    class="code"
    ref="mycode"
    :modelValue="content"
    @update:modelValue="(val) => (content = val)"
    :basic="true"
    :extensions="extensions"
    :disabled="true"
  />
</template>

<script setup>
import { Codemirror } from "vue-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { basicSetup } from "codemirror";
import { EditorView } from "@codemirror/view";
import { EditorState } from "@codemirror/state";
import {
  syntaxHighlighting,
  defaultHighlightStyle,
  HighlightStyle
} from "@codemirror/language";
import { tags } from "@lezer/highlight";

const props = defineProps({
  ruleDrl: {
    type: String,
    default: "暂无数据",
  },
  ruleName: String
});

const content = ref(props.ruleDrl || "暂无数据");

// 自定义语法高亮样式
const myHighlightStyle = HighlightStyle.define([
  { tag: tags.keyword, color: "#ffc107" },
  { tag: tags.atom, color: "#67C23A" },
  { tag: tags.number, color: "#67C23A" },
  { tag: tags.definition, color: "#8DA6CE" },
  { tag: tags.variableName, color: "#F56C6C" },
  { tag: tags.operator, color: "#7a7979" },
  { tag: tags.comment, color: "#AEAEAE" },
  { tag: tags.string, color: "#67C23A" },
  { tag: tags.meta, color: "#67C23A" },
  { tag: tags.typeName, color: "#8DA6CE" },
  { tag: tags.tagName, color: "#8DA6CE" },
  { tag: tags.attributeName, color: "#8DA6CE" },
  { tag: tags.heading, color: "#F56C6C" },
  { tag: tags.link, color: "#8DA6CE" },
  { tag: tags.invalid, background: "#9D1E15", color: "#409EFF" },
  { tag: tags.function, color: "#409EFF" },        // 方法调用
  { tag: tags.attributeName, color: "#409EFF" },   // 替代方法定义
  { tag: tags.propertyName, color: "#409EFF" },    // 对象属性
  { tag: tags.paren, color: "#409EFF" },          // 括号与方法名同色
  { tag: tags.bracket, color: "#409EFF" },        // 方括号
  { tag: tags.brace, color: "#409EFF" },          // 大括号
]);

const extensions = [
  basicSetup,
  javascript(),
  syntaxHighlighting(myHighlightStyle),
  EditorState.readOnly.of(true),
  EditorView.lineWrapping,
  EditorView.theme({
    "&": {
      height: "auto",
      minHeight: "300px",
      backgroundColor: "#ffffff",
      overflow: "auto",
    },
    // 选中区域的样式
    "&.cm-focused .cm-selectionBackground, .cm-selectionBackground": {
      background: "#e3f2fd !important"  // 使用浅蓝色背景
    },
    // 选中文本的样式
    "&.cm-focused .cm-selectionMatch": {
      backgroundColor: "#e3f2fd"
    },
    ".cm-content": {
      whiteSpace: "pre-wrap",
      padding: "10px",
      fontSize: "12px",
      color: "#24292e", 
    },
    ".cm-line": {
      padding: "0 4px",
    },
    // 行号样式
    ".cm-gutters": {
      backgroundColor: "#f6f8fa",
      borderRight: "1px solid #fff",
      color: "#6e7781",
      padding: "0 4px",
    },
    ".cm-lineNumbers": {
      minWidth: "30px",
    },
    // 滚动条样式
    "&.cm-focused": {
      outline: "none",
    },
    ".cm-scroller": {
      overflow: "auto !important",
      fontFamily: "Consolas, Monaco, 'Courier New', monospace",
    },
  }),
]

watch(
  () => props.ruleDrl,
  (newRuleDrl) => {
    if(newRuleDrl){
      // 检查是否是有效的DRL内容（不是"暂无数据"且包含实际内容）
      if(newRuleDrl === "暂无数据" || newRuleDrl.trim() === "") {
        content.value = "暂无数据";
        return;
      }
      
      if(props.ruleName){
        let aDrl = newRuleDrl.split('when\n')
        for(let i=0;i<aDrl.length;i++){
          if(aDrl[i].trim().length>0){
            aDrl[i] = `【${props.ruleName}_${i}】\n\nwhen\n${aDrl[i]}`
          }
        }
        content.value = aDrl.join('') || "暂无数据";
      } else {
        content.value = newRuleDrl || "暂无数据";
      }
    } else {
      content.value = "暂无数据";
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">

.code {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;

  :deep(.cm-editor) {
    height: calc(100vh - 220px);
    min-height: 300px;
    overflow: auto;

    .cm-scroller {
      overflow: auto !important;

      &:hover {
        cursor: default;
      }
    }

    .cm-content {
      font-family: Consolas, Monaco, "Courier New", monospace;
      line-height: 1.5;
      overflow-x: auto;
    }
  }
}
</style>

