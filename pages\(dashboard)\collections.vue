<!-- 收藏 -->
<script setup lang="tsx">
import { getCollectList, collectRule, CollectObjectType } from '@/api/dashboardApi'
import type { CollectItem, ApiResponse } from '@/api/dashboardApi'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { openRulePage } from '@/utils/ruleUtil'

dayjs.extend(relativeTime)

definePageMeta({
    title: '收藏'
})

const message = inject<{
    success: (message: string) => void;
    error: (message: string) => void;
}>('message')

// 格式化时间为相对时间
const formatRelativeTime = (time: string) => {
    return dayjs(time).fromNow()
}

// 处理取消收藏
const handleCancelCollect = async (record: CollectItem) => {
    try {
        await collectRule({
            uuid: record.uuid,
            type: CollectObjectType.RULE // 使用枚举类型代替硬编码的"3"
        })
        message?.success('取消收藏成功')
        // 从列表中移除该条记录
        dataSource.value = dataSource.value.filter(item => item.uuid !== record.uuid)
    } catch (error) {
        message?.error('取消收藏失败')
        console.error('取消收藏失败:', error)
    }
}

// 表格列定义
const columns = [
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 250,
        align: 'left' as const,
    },
    {
        title: '归属',
        dataIndex: 'packageNameAll',
        key: 'packageNameAll',
        width: 300,
        align: 'left' as const,
    },
    {
        title: '收藏时间',
        dataIndex: 'createdTime',
        key: 'createdTime',
        width: 180,
        align: 'center' as const,
    },
    {
        title: '',
        key: 'action',
        width: 50,
    },
]
//收藏规则跳转
const info = async (record: CollectItem) => {
    return await openRulePage(record)
}
// 数据源
const dataSource = ref<CollectItem[]>([])
const loading = ref(false)

// 获取收藏列表数据
const fetchCollectList = async () => {
    loading.value = true
    try {
        const res = await getCollectList()
        dataSource.value = res.data as CollectItem[]
    } catch (error) {
        console.error('获取收藏列表失败:', error)
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    fetchCollectList()
})
</script>

<template>
    <div class="Recycles-module_wrapper_jPSDM Recycles-module_mytable_1fj45">
        <a-card title="收藏" :bordered="false">
            <a-table :columns="columns" :data-source="dataSource" :loading="loading" :pagination="false">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'name'">
                        <a-button @click="info(record as CollectItem)" type="link" size="small">
                            {{ record.name }}
                        </a-button>
                    </template>
                    <template v-if="column.key === 'packageNameAll'">
                        <span style="color: #8c8c8c;">
                            <RulePath :path="record.packageNameAll" />
                        </span>
                    </template>
                    <template v-if="column.key === 'createdTime'">
                        <span class="ant-table-cell" :title="formatRelativeTime(record.createdTime)"
                            style="color: #8c8c8c;">
                            {{ formatRelativeTime(record.createdTime) }}
                        </span>
                    </template>
                    <template v-if="column.key === 'action'">
                        <div class="Marks-module_collectionOperation_IIbvc" title="取消收藏">
                            <IconStar size="18" class="cursor-pointer"
                                @click="handleCancelCollect(record as CollectItem)" />
                        </div>
                    </template>
                </template>
            </a-table>
        </a-card>
    </div>
</template>

<style scoped>
.cursor-pointer {
    cursor: pointer;
}
</style>
