<!-- 横向三个点表示的更多 -->
<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-more-horizontal icon-svg index-module_size_wVASz" data-name="MoreHorizontal"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave">
        <path
            d="M227.008 128c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20ZM148 128c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20Zm-78.992 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20Z"
            :fill="isHovered && enableHover ? hoverColor : color" fill-rule="nonzero"></path>
    </svg>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    size: {
        type: [Number, String],
        default: 16
    },
    color: {
        type: String,
        default: 'currentColor'
    },
    hoverColor: {
        type: String,
        default: 'var(--yq-yuque-green-600)' // 默认悬停时为蓝色
    },
    enableHover: {
        type: Boolean,
        default: false // 默认不启用悬停效果
    }
});

const isHovered = ref(false);

const handleMouseEnter = () => {
    isHovered.value = true;
};

const handleMouseLeave = () => {
    isHovered.value = false;
};
</script>

<style scoped>
.larkui-icon {
    cursor: pointer;
    transition: all 0.3s;
}
</style>
