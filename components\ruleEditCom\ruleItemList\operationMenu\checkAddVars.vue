<template>
  <div id="check_add_vars">
    <a-drawer
      title="批量勾选增加"
      :open="checkboxShow"
      :get-container="false"
      placement="right"
      :width="enumCheckboxBtnWidth"
      :z-index="1100"
      :push="false"
      @close="enumCheckboxClose"
      class="diy_drawer diy_drawer_check"
    >
      <div
        :style="
          'position: fixed; top: 46px; text-align: left; background: #fff; padding:30px 20px 10px; z-index: 2; width:' +
          enumCheckboxBtnWidth +
          'px;'
        "
      >
        <div style="padding: 10px 0">
          <a-checkbox v-model:checked="checkAll" @change="handleCheckAllChange">
            全选
          </a-checkbox>
          <a-checkbox v-model:checked="checkReverse" @change="handleCheckReverseChange">
            反选
          </a-checkbox>
        </div>
        <a-input
          placeholder="请输入内容"
          v-model:value="searchTxt"
          @input="searchChange"
          allow-clear
        >
          <template #suffix>
            <SearchOutlined />
          </template>
        </a-input>
      </div>
      <a-form>
        <a-checkbox-group v-model:value="checkList">
          <a-form-item style="margin-bottom: 50px">
            <a-checkbox
              v-for="(item, index) in checkListOption"
              :key="index + '_' + item.value"
              :value="item.value"
              style="display: block"
            >
              {{ item.viewName }}
            </a-checkbox>
          </a-form-item>
        </a-checkbox-group>
      </a-form>
      <div
        :style="
          'position: fixed; bottom: 0; text-align: right; background: #fff; padding:10px 20px; z-index: 1; width:' +
          enumCheckboxBtnWidth +
          'px;'
        "
      >
        <a-button @click="enumCheckboxClose" style="margin-right: 15px">取 消</a-button>
        <a-button type="primary" @click="enumCheckboxSave">确定</a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
const message = inject("message");
// 定义 props
const props = defineProps({
  checkAddisShow: {
    type: Boolean,
    default: false,
  },
  checkListOption: {
    type: Array,
    default: () => [],
  },
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit 事件
const emit = defineEmits(['checkboxHide', 'addVarsSure', 'searchChange']);

// 定义响应式数据
const checkList = ref([]);
const checkboxShow = ref(false);
const searchTxt = ref("");
const enumCheckboxBtnWidth = ref("");
const checkAll = ref(false);
const checkReverse = ref(false);

// 监听 checkAddisShow 的变化
watch(
  () => props.checkAddisShow,
  (val) => {
    checkList.value=[]
    checkAll.value = false;
    checkReverse.value = false;
    searchTxt.value = "";
    if (val===true) {
      checkboxShow.value = true;
      let checkedStatus = props.checkListOption.map((item) => {
        if (item.checked && !checkList.value.includes(item.value)) {
          checkList.value.push(item.value);
        }
        return item.checked;
      });
      if (checkedStatus.includes(false)) {
        checkAll.value = false;
      } else {
        checkAll.value = true;
      }
    }else{
      checkboxShow.value = false;
    }
  }
);

// 监听 checkListOption 的变化
// watch(
//   () => props.checkListOption,
//   (val) => {
//     if (val&&val.length>0&&checkboxShow.value) {
//       let checkedStatus = val.map((item) => {
//         if (item.checked && !checkList.value.includes(item.value)) {
//           checkList.value.push(item.value);
//         }
//         return item.checked;
//       });
//       if (checkedStatus.includes(false)) {
//         checkAll.value = false;
//       } else {
//         checkAll.value = true;
//       }
//     }
//   },
//   { deep: true }
// );

// 在组件挂载时初始化按钮宽度
onMounted(() => {
  if (typeof document !== 'undefined' && document.body) {
    enumCheckboxBtnWidth.value = Math.floor(document.body.clientWidth * 0.3);
  } else {
    enumCheckboxBtnWidth.value = 400; // 默认宽度
  }
});

// 方法
const enumCheckboxClose = () => {
  checkList.value = [];
  searchTxt.value = "";
  checkAll.value = false;
  checkReverse.value = false;
  searchChange();
  emit("checkboxHide");
};
const enumCheckboxSave = () => {
  if (checkList.value.length) {
    checkboxShow.value = false;
    emit("addVarsSure", checkList.value, true);
  } else {
    if (props.isTable) {
      checkboxShow.value = false;
      emit("addVarsSure", checkList.value, true);
    } else {
      message.warning("请至少选择一项");
    }
  }
};

const searchChange = () => {
  emit("searchChange", searchTxt.value);
};

const handleCheckAllChange = (e) => {
  checkReverse.value = false;
  if (e.target.checked) {
    props.checkListOption.map((item) => {
      checkList.value.push(item.value);
    });
    checkList.value = Array.from(new Set(checkList.value));
  } else {
    checkList.value = arrSubtract(
      checkList.value,
      props.checkListOption,
      "secend",
      checkList.value
    );
  }
};

const handleCheckReverseChange = (e) => {
  checkAll.value = false;
  if (e.target.checked) {
    let _oldOptionList = JSON.parse(JSON.stringify(props.checkListOption));
    if (!searchTxt.value) {
      _oldOptionList = arrSubtract(
        _oldOptionList,
        checkList.value,
        "first",
        _oldOptionList
      );
      checkList.value = [];
    } else {
      _oldOptionList = arrSubtract(
        _oldOptionList,
        checkList.value,
        "first",
        _oldOptionList,
        true
      );
    }
    _oldOptionList.map((item) => {
      checkList.value.push(item.value);
    });
  } else {
    let _oldOptionList = JSON.parse(JSON.stringify(props.checkListOption));
    if (_oldOptionList.length < checkList.value.length) {
      arrSubtract(
        checkList.value,
        _oldOptionList,
        "secend",
        checkList.value
      );
    } else {
      if (!searchTxt.value) {
        arrSubtract(
          _oldOptionList,
          checkList.value,
          "first",
          _oldOptionList
        );
        checkList.value = [];
      } else {
        _oldOptionList = arrSubtract(
          _oldOptionList,
          checkList.value,
          "first",
          _oldOptionList,
          true
        );
      }
      _oldOptionList.map((item) => {
        checkList.value.push(item.value);
      });
    }
  }
  checkList.value = Array.from(new Set(checkList.value));
};

const arrSubtract = (arr1, arr2, arrV, arrSplice, dobuleSplice) => {
  // 数组相减
  for (let i = arr1.length - 1; i >= 0; i--) {
    for (let j = arr2.length - 1; j >= 0; j--) {
      if (arrV === "first") {
        if (arr1[i].value == arr2[j]) {
          arrSplice.splice(i, 1);
          dobuleSplice && arr2.splice(j, 1);
          break;
        }
      }
      if (arrV === "secend") {
        if (arr1[i] == arr2[j].value) {
          arrSplice.splice(i, 1);
          dobuleSplice && arr2.splice(j, 1);
          break;
        }
      }
    }
  }
  return arrSplice;
};
</script>

<style lang="scss" scoped>
#check_add_vars ::v-deep {
  .ant-drawer {
    overflow: auto !important;
    header {
      span {
        font-size: 18px;
        color: #303133;
      }
    }
    .ant-drawer-header {
      text-align: left;
    }
    .ant-form {
      padding: 111px 20px 20px;
    }
  }
  .ant-drawer-body{
    padding: 0;
  }
  .ant-input{
    border: none !important;
  }
  .ant-form label{
    display: flex !important;
    margin-left: 0 !important;
    margin-bottom: 10px;
  }
}
</style>
