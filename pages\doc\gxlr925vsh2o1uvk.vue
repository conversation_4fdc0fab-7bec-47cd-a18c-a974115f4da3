<template>
  <div class="ReaderLayout-module_wrapper_l-8F+" style="padding-left: 276px;"><div id="headerOverlayContainer" class="ViewerHeader-module_headerOverlayContainer_30mty"></div><div class="BookReader-module_wrapper_s6Jdt BookReader-module_docTypographyClassic_tUB5r" data-testid="content"><div class="BookReader-module_content_BGKYX" id="main"><div class="BookReader-module_docContainer_mQ3Tk"><div class="DocReader-module_wrapper_t3Z8X" data-doc-layout="fixed" data-doc-sidebar="false" data-doc-toc="true"><div class=""><div id="doc-reader-content" class="DocReader-module_content_AcIMy "><div class="DocReader-module_header_xAOtU"><div><div class="DocReader-module_title_fXOQi"><h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title">决策表</h1></div></div></div><div><article id="content" class="article-content" tabindex="0" style="outline-style: none;"><div class="ne-doc-major-viewer"><div class="yuque-doc-content" data-df="lake" style="position: relative;"><div><div class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed" data-viewer-mode="normal" id="ud9fa"><div class="ne-viewer-header"><button type="button" class="ne-ui-exit-max-view-btn" style="background-image:url(https://gw.alipayobjects.com/zos/bmw-prod/09ca6e30-fd03-49ff-b2fb-15a2fbd8042a.svg)">返回文档</button></div><div class="ne-viewer-body"><ne-p id="ce6477b19f9d5e48b765f4bcb7ed7ae7" data-lake-id="ce6477b19f9d5e48b765f4bcb7ed7ae7"><ne-text id="u088ef048">决策表是一种以表格形式表现规则的工具，它非常适用于描述处理判断条件较多，各条件又相互组合、有多种决策方案的情况，决策表提供精确而简洁描述复杂逻辑的方式，可将多个条件及与这些条件满足后要执行动作以图形化形式进行对应，对于决策表的定义，我们提供的是全可视化、图形化的操作方式，通过简单的鼠标点击就可以快速定义出与业务相匹配的决策表。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ue6d5ceb7" data-lake-id="ue6d5ceb7"><ne-text id="u8675ba2b">普通决策表将一个表格拆分成左右两部分，中间以一条灰黑色的竖线分隔，左边部分为条件列部分；右边部分为动作列部分。条件列可以有多个，多个条件列可以进行组合；同样动作列也可以有多个。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="uc487e4fc" data-lake-id="uc487e4fc"><ne-text id="ud904be30">表是系统性的展示形式的表现，能实现实现导入导出本地操作。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u88f8fa93" data-lake-id="u88f8fa93"><ne-text id="u37eda112">决策表是用同类条件且表中添加有限条件，满足表中的所有场景配置表。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u14f19eb2" data-lake-id="u14f19eb2"><ne-text id="u3453ccae">在要求的规则包下新增决策表</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="2696b7e0935f963c11af711570b39714" data-lake-id="2696b7e0935f963c11af711570b39714"><ne-text id="u477303eb">点击“新建”</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="3f51a518a6daab1ada97048b6b82af5d" data-lake-id="3f51a518a6daab1ada97048b6b82af5d"><ne-card data-card-name="image" data-card-type="inline" id="u0def9011" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1483.2" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743390533064-3b76f2c9-7046-43d1-8cf7-989ceae08b30.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="28c7c0059c9ea9e9c65653a466f04ffb" data-lake-id="28c7c0059c9ea9e9c65653a466f04ffb"><ne-text id="u33041de1">填写规则名称，选择规则类型：决策表</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="3e92547b160fa1dbc2892f5cb0515463" data-lake-id="3e92547b160fa1dbc2892f5cb0515463"><ne-card data-card-name="image" data-card-type="inline" id="tGIt5" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="810" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737572852-e35c4b5b-08c6-49c5-8c50-f9b04c208a77.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u85542073" data-lake-id="u85542073"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="667786c8adda726cf568be76d96ca652" data-lake-id="667786c8adda726cf568be76d96ca652"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="7ac501276db6d6d074e1db621c9b55c6" data-lake-id="7ac501276db6d6d074e1db621c9b55c6"><ne-text id="u246ddda7">创建完决表后 对表头和动作列进行定义。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="5e6bd2294a77768aab69e102c5c82b8b" data-lake-id="5e6bd2294a77768aab69e102c5c82b8b"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="4e98132fc94d6529d72036023c278b01" data-lake-id="4e98132fc94d6529d72036023c278b01"><ne-card data-card-name="image" data-card-type="inline" id="brzVt" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1785" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737573062-f76bff21-2422-45cb-bbdf-55bde9ae23c5.png"></div></div></div></ne-card><ne-card data-card-name="image" data-card-type="inline" id="V6gQu" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1269" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737573347-5bb8adb0-d0a9-4f83-900e-53d45676ad62.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol" ne-bold="true"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="dec02fdbfa8b47f28516b8f6336129f1" data-lake-id="dec02fdbfa8b47f28516b8f6336129f1"><ne-text id="ua4ec98e1" ne-bold="true">常规单语句定义</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="4146594e4149d091f4987f3f141cdc59" data-lake-id="4146594e4149d091f4987f3f141cdc59"><ne-text id="ud7f2d596">①对空白的列进行定义。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="79eb7d9d955dd01a82ddf851959d90e5" data-lake-id="79eb7d9d955dd01a82ddf851959d90e5"><ne-text id="u2b67882a">点击列，</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="38e83edb7c7bf2a4298e4a05e23768ed" data-lake-id="38e83edb7c7bf2a4298e4a05e23768ed"><ne-card data-card-name="image" data-card-type="inline" id="E5Qrb" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1268" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737573557-c7b186e3-9082-49a6-971f-6c7b9672a57f.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="bd6ad7168a3ca789ece1334de5d9fa00" data-lake-id="bd6ad7168a3ca789ece1334de5d9fa00"><ne-text id="uc00b1c2d">填写“列名” 和编写表头列定义。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="6b154968ae32577c359a9a75884f53a7" data-lake-id="6b154968ae32577c359a9a75884f53a7"><ne-text id="uefe03c08">那么单元格内容就可以填写列定义需要填写的内容。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ceda95415f88f1fd89e4ab2df5715781" data-lake-id="ceda95415f88f1fd89e4ab2df5715781"><ne-card data-card-name="image" data-card-type="inline" id="bsDN3" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="861" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737573803-d0923f67-eda2-4eec-a0f9-2629eb56898d.png"></div></div></div></ne-card><ne-text id="ud990a4da">●添加列：默认表的列数量不够或者删除 ，可将鼠标方式某列上根据提示进行编辑、新增和删除</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="8a006284916065fbf6986dd5f146d655" data-lake-id="8a006284916065fbf6986dd5f146d655"><ne-card data-card-name="image" data-card-type="inline" id="LJdJQ" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1815" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737574017-02927b51-1d4c-4ca9-a2c1-bc374f387c9d.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="c2fb091eeccf8831a99ba758333011bd" data-lake-id="c2fb091eeccf8831a99ba758333011bd"><ne-text id="u0fe40436">●添加行：将鼠标放在行的末尾 “编辑”处 根据提示进行新增和删除行</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="4e5438d944aeb9c2f5f3be14d14d8ea4" data-lake-id="4e5438d944aeb9c2f5f3be14d14d8ea4"><ne-text id="uacf2012a">·</ne-text><ne-card data-card-name="image" data-card-type="inline" id="T8F1C" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1795" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737574236-9c4e7def-36f9-4854-8818-637813877437.png"></div></div></div></ne-card><ne-card data-card-name="image" data-card-type="inline" id="ayB7U" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1737" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737574631-f0b90ba3-0146-415f-8265-2ed54163beb2.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="5c2b982638df13a6647aac42bf431d2e" data-lake-id="5c2b982638df13a6647aac42bf431d2e"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="c623e3358ce5a9deb4171b028d82ef5a" data-lake-id="c623e3358ce5a9deb4171b028d82ef5a"><ne-text id="u88794cbb">根据表头定义将单元格填充上 ，然后添加”确定”</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="31f35d1e5c6d47f79c248af266b41b20" data-lake-id="31f35d1e5c6d47f79c248af266b41b20"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="dc8ee821917e22b5a360cb8fe90333c8" data-lake-id="dc8ee821917e22b5a360cb8fe90333c8"><ne-card data-card-name="image" data-card-type="inline" id="VDPNT" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1812" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737574916-735debec-9369-4d5b-a028-304359466e1b.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol" ne-bold="true"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="33d0f712fa62872b293edcedd67b4232" data-lake-id="33d0f712fa62872b293edcedd67b4232"><ne-text id="u33451cba" ne-bold="true">复杂编辑模式</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="a7446d7905327c889d8d821dd6ee750a" data-lake-id="a7446d7905327c889d8d821dd6ee750a"><ne-text id="u0399b022">多语句编辑模式，复合条件定义列，可以如普通规则常规写法，多语句逻辑放在表列定义中定义使用。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="b763e874ab73177a8767eb68b1752d23" data-lake-id="b763e874ab73177a8767eb68b1752d23"><ne-text id="ua869b15d">点击列，打开“复杂编辑模式”</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="12b99ebe6ff1807071817681082a9fd3" data-lake-id="12b99ebe6ff1807071817681082a9fd3"><ne-card data-card-name="image" data-card-type="inline" id="fqf7V" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1269" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737575122-eb077944-baf1-44e7-b950-595138af57c5.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="2316b1b5bed6daa6e8797672658b34b5" data-lake-id="2316b1b5bed6daa6e8797672658b34b5"><ne-card data-card-name="image" data-card-type="inline" id="y6rBn" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="99" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737575363-2ed65739-5307-449e-b5fc-18f7b1fca6f6.png"></div></div></div></ne-card><ne-text id="u7edb1532">选择等于&lt;布尔型&gt;，根据实际列定义选择布尔值或者不选择。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="cc86b3d1c6ff3b9c15612d37c7e92ae3" data-lake-id="cc86b3d1c6ff3b9c15612d37c7e92ae3"><ne-card data-card-name="image" data-card-type="inline" id="aOh6d" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1790" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737575546-3db6d6c3-75da-46b3-9ce2-ec777847640b.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="ff632d95f3b398fe477043cbe2ddbc53" data-lake-id="ff632d95f3b398fe477043cbe2ddbc53"><ne-text id="u6a072005">以上可以进行多语句的编写定义：如例</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="67d738a3bf1d86d30c35b19a52974659" data-lake-id="67d738a3bf1d86d30c35b19a52974659"><ne-card data-card-name="image" data-card-type="inline" id="c1cxM" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1795" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737575750-fe7e7c70-e1a2-4fd2-97b4-a21b9b4f0afc.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="2be4af731c0d9fc0c95acf933d4f9df5" data-lake-id="2be4af731c0d9fc0c95acf933d4f9df5"><ne-text id="ufb3f4207">未定义的列、定义不完整或者一行中没有选择列且动作列无填充值，视为规则不完整，校验不通过。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="fb8c3171a88cf2fb321a54383cc87ebf" data-lake-id="fb8c3171a88cf2fb321a54383cc87ebf"><ne-card data-card-name="image" data-card-type="inline" id="HLozn" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1756" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737575969-e7ae021f-3425-490b-a5f8-2b1447f75cf2.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="6674668f69c0b1627bc61cca7a59f4af" data-lake-id="6674668f69c0b1627bc61cca7a59f4af"><ne-text id="ub4f4ffdf">符合编写要求的提示规则校验通过</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="b9483fd867386a74f895d398b60d66e2" data-lake-id="b9483fd867386a74f895d398b60d66e2"><ne-card data-card-name="image" data-card-type="inline" id="Lalb5" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1795" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737576203-404faec7-5626-4e12-bd1e-263a23799df6.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-uli index-type="0"><ne-uli-i><span class="ne-list-symbol" ne-bold="true"><span>●</span></span></ne-uli-i><ne-uli-c class="ne-uli-content" id="d02fbbe58ae8b6b3ef39b7b6ac7773e6" data-lake-id="d02fbbe58ae8b6b3ef39b7b6ac7773e6"><ne-text id="ud7c0459b" ne-bold="true">决策表：导入、导出</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-uli-c></ne-uli><ne-p id="0abdb00b38b98a58577e849d5f9fdd1d" data-lake-id="0abdb00b38b98a58577e849d5f9fdd1d"><ne-card data-card-name="image" data-card-type="inline" id="UaTdt" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1821" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737576470-73026ab5-b19e-4e8c-839f-ca4ca98a4014.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>1</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="36adc21251e58c0f668bddd8f9515ada" data-lake-id="36adc21251e58c0f668bddd8f9515ada"><ne-text id="u8113d223">导出：将校验通过的决策表 导出到本地环境中，进行数据的更新：在要求的列定义下填写指定内容。不能存在公式单元值。行内容必须完整，</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>2</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="8bf2a95ae6ca1106b43144eab0e9b571" data-lake-id="8bf2a95ae6ca1106b43144eab0e9b571"><ne-text id="ud4da53dd">导入：点击导入，将本地更新完毕的决策表导入到系统中，提示导入成功否则根据提示重新审阅下决策内容是否行内和表头定义类型一致。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-h6 id="mRX0F" data-lake-id="mRX0F"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h6" data-name="card-h6"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h6"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u056772f9">决策表支持预定义功能</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h6><ne-p id="ud64af44c" data-lake-id="ud64af44c"><ne-text id="u04263a97">决策表预定义与普通规则预定义功能相同。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u7536daaf" data-lake-id="u7536daaf"><ne-card data-card-name="image" data-card-type="inline" id="ue9f613d5" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1000" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1742883412159-df852bb7-b63c-4a51-853e-503340e5738b.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h6 id="h7NtN" data-lake-id="h7NtN"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h6" data-name="card-h6"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h6"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u4cc40536">决策表对否则写法（取反）的支持</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h6><ne-p id="ubd2844a1" data-lake-id="ubd2844a1"><ne-text id="u092229a6">erule设置了互斥组后，整个决策表从上到下计算时，遇到需要取反可以直接加上否则，以实现决策表中否则的功能效果，如下图所示：</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u880021e6" data-lake-id="u880021e6"><ne-text id="u394c815a">注：加入否则时不能是第一行，否则的前列必须有多行相同的数据。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ue60b3775" data-lake-id="ue60b3775"><ne-card data-card-name="image" data-card-type="inline" id="u1c665541" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1389" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1742883430386-24b28789-b2a7-4bc2-a555-571bc53fafd4.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ua23dc384" data-lake-id="ua23dc384"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p></div><div class="ne-inner-overlay-container"><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay"><div class="count-tips-module_tipsContainer___5agc"></div></ne-overlay-tmp></div></div><div style="height: 0px; overflow: hidden;">​</div></div></div></div></article></div></div><div ne-viewer-toc-pin="true"></div><div></div></div><div></div></div></div></div></div></div>
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>