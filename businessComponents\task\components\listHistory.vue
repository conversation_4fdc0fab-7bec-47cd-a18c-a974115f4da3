<!-- 关联规则历史组件 -->

<template>
    <a-tabs type="card" v-model:activeKey="activeName" @change="tabClick">
        <a-tab-pane
                tab="规则历史信息"
                key="ruleCompare"
                style="marbin-bottom: 30px"
                :forceRender="true"
        >
            <div ref="basePoint">
                <a-table :columns="columns" :data-source="tableData" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading"
                         @change="handlePageChange" :rowSelection="rowSelection">
                    <template v-slot:bodyCell="{column,record,index}">
                        <template v-if="column.dataIndex == 'action'">
                            <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
                        </template>
                    </template>
                </a-table>
            </div>
        </a-tab-pane>
        <a-tab-pane
                tab="审核历史信息"
                key="hisExamine"
                style="marbin-bottom: 30px"
                :forceRender="true"
        >
            <a-table :columns="columnsT" :data-source="checkTableData" :pagination="paginationT" row-key="uuid" :loading="paginationT.isLoading"
                     @change="handlePageChangeT">
                <template v-slot:bodyCell="{column,record,index}">
                    <template v-if="column.dataIndex == 'action'">
                        <a-button type="link" size="small" @click="handleCheckDetail(record)">详情</a-button>
                    </template>
                </template>
            </a-table>
        </a-tab-pane>

        <a-tab-pane
                tab="规则版本对比"
                key="ruleEditionCompare"
                style="marbin-bottom: 30px"
                :forceRender="true"
        >
            <!-- 使用RuleCompareContent组件显示规则比对内容 -->
            <RuleCompareContent :rule-compare-data="ruleCompareData" />
        </a-tab-pane>
    </a-tabs>
    <!-- 详情对话框 -->
    <FullModel :isFullscreen="isFullscreen" :onFullscreenToggle="onFullscreenToggle" :titleText="titleText" :handleCancel="handleCancel" :isModalVisible="isModalVisible">
        <template #default>
            <div :style="{maxHeight: isFullscreen?'90vh':'60vh', overflowY: 'auto'}">
                <RuleBaseHistoryInfo v-if="modalType === 'detail'" :dataId="dataId" />
                <RuleBaseCheckHistoryInfo v-if="modalType === 'checkDetail'" :dataId="dataId" />
            </div>
        </template>
    </FullModel>
</template>

<script setup lang="ts">
    import { historyList, ruleCheckList } from "@/api/task";
    import qs from 'qs'
    import { compareRule } from "@/api/task";
    import type { TableProps } from 'ant-design-vue';
    import { Message } from '@/types/global';
    const message = inject<Message>('message')
    const isModalVisible = ref(false); // 对话框显示状态
    const titleText = ref('');
    import RuleBaseHistoryInfo from "@/businessComponents/ruleBaseManage/RuleBaseHistoryInfo.vue";
    import RuleBaseCheckHistoryInfo from "@/businessComponents/ruleBaseManage/RuleBaseCheckHistoryInfo.vue";
    //计算自适应高度
    const basePoint = ref();
    const { scrollY } = useResize(basePoint);
    const props = defineProps({
        obj: {
            type: Object,
            default: () => {
                return {};
            },
        },
    })
    const handleCancel = () => {
        isModalVisible.value = false;
    };
    const modalType = ref(''); // 对话框类型
    const activeName = ref('ruleCompare');
    //规则历史信息
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            align: 'center',
            customRender: ({ index }: { index: number }) => `${(paginationT.value.current - 1) * paginationT.value.pageSize + index + 1}`
        },
        {
            title: '名称',
            dataIndex: 'ruleName',
            key: 'ruleName',
            align: 'left',
            customRender: ({ record }) => {
                return h('div', [
                    h('a', {
                        onClick: () => handleDetail(record)
                    }, record.ruleName)
                ])
            }
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center',
        },
        {
            title: '版本',
            dataIndex: 'edition',
            key: 'edition',
            align: 'center',
        },
        {
            title: '修改时间',
            dataIndex: 'lastModifiedTimeStr',
            key: 'lastModifiedTimeStr',
            align: 'left',
            width:180,
        }
    ];

    interface TablePaginationConfig {
        current: number;
        pageSize: number;
        showSizeChanger: boolean;
        total: number;
    }

    const pagination = ref({
        current: 1,
        pageSize: 9,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total: number) => `共 ${total} 条数据`,
    })
    const handlePageChange = (page: any) => {
        pagination.value.current = page.current;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        getRuleHistoryList1();
    };
    const getRuleHistoryList1 = async () => {
        pagination.value.isLoading = true;
        try {
            const res = await historyList({
                ruleUuid: props.obj.needId,
                demandUuid: props.obj.demandUuid,
                page: pagination.value.current,
                several: pagination.value.pageSize,
            });
            pagination.value.isLoading = false;
            tableData.value = res.data.data;
            pagination.value.total = res.data.totalCount;
        } catch (error) {
            pagination.value.isLoading = false;
            message?.error?.('获取数据失败');
        }
    }

    interface RuleHistory {
        ruleName: string;
        checkId: string;
        checkStatus: string;
        checkTimeStr: string;
        uuid?: string;
        edition?: string;
        type?: string;
        lastModifiedTimeStr?: string;
    }

    const handleDetail = (record: RuleHistory) => {
        showModal('detail', record)
    }
    //审核历史信息
    const tableData = ref<RuleHistory[]>([]);
    const columnsT = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: 80,
            align: 'center',
            customRender: ({ index }) => `${(paginationT.value.current - 1) * paginationT.value.pageSize + index + 1}`
        },
        {
            title: '名称',
            dataIndex: 'ruleName',
            key: 'ruleName',
            align: 'left',
            customRender: ({ record }) => {
                return h('div', [
                    h('a', {
                        onClick: () => handleCheckDetail(record)
                    }, record.ruleName)
                ])
            }
        },
        {
            title: '审核人',
            dataIndex: 'checkId',
            key: 'checkId',
            align: 'center',
        },
        {
            title: '审核状态',
            dataIndex: 'checkStatus',
            key: 'checkStatus',
            align: 'center',
        },
        {
            title: '审核时间',
            dataIndex: 'checkTimeStr',
            key: 'checkTimeStr',
            align: 'left',
            width:180,
        }
    ];

    const form = reactive({
        ruleName: [],
    });

    const ruleForm = ref(null);
    const formData = ref([]);
    // todo 下面这两个ref感觉不需要，待进一步确认
    const BaseId = ref('');
    const demandUuid = ref('');

    const emit = defineEmits(['removeTab', 'addTab']);

    const handleSubmit = (callback: () => void) => {
        callback();
    };
    const paginationT = ref({
        current: 1,
        pageSize: 9,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total: number) => `共 ${total} 条数据`,
    })

    const checkTableData = ref<RuleHistory[]>([]);


    const handlePageChangeT = (page: any) => {
        paginationT.value.current = page.current;
        paginationT.value.pageSize = page.pageSize;
        paginationT.value.showSizeChanger = page.showSizeChanger;
        paginationT.value.total = page.total;
        ruleCheckList1();
    };

    const ruleCheckList1 = async () => {
        paginationT.value.isLoading = true;
        try {
            const res = await ruleCheckList({
                ruleUuid: props.obj.needId,
                page: paginationT.value.current,
                several: paginationT.value.pageSize,
            })
            paginationT.value.isLoading = false;
            checkTableData.value = res.data.data;
            paginationT.value.total = res.data.totalCount;
        } catch (error) {
            paginationT.value.isLoading = false;
            message?.error?.('获取数据失败');
        }
    }
    const dataId = ref('');
    const handleCheckDetail = (record: RuleHistory) => {
        showModal('checkDetail', record)
    }

    interface ModalRecord {
        uuid?: string;
        ruleName?: string;
        edition?: string;
    }

    const showModal = (type: string, record: ModalRecord = {}) => {
        modalType.value = type;
        dataId.value = record.uuid || '';
        if(type === 'detail' || type === 'shoot'){
            titleText.value =  `${record.ruleName || ''}（${record.edition || ''}）`
        }else{
            titleText.value = `${record.ruleName || ''}`;
        }

        isModalVisible.value = true;
    }
    onMounted(async () => {
        getRuleHistoryList1();
        ruleCheckList1();

    });

    const checkedData = ref<RuleHistory[]>([])
    const rowSelection = {
        onChange: (selectedRowKeys: string[], selectedRows: RuleHistory[]) => {
            checkedData.value = [];
            selectedRows.forEach((item: RuleHistory) => {
                checkedData.value.push(item)
            });
        }
    };
    // 检查选中记录条数
    const checkSelectedRow = (single: boolean) => {
        if (checkedData.value.length) {
            if (single) {
                if (checkedData.value.length > 1) {
                    message?.error?.("只能选择一条记录");
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } else {
            message?.error?.("请选择要操作的记录");
            return false;
        }
    }

    interface RuleHistoryVO {
        edition?: string;
        textDtl?: string;
        type?: string;
        uuid?: string;
        tableContent?: string;
        tableContentHis?: string;
        validStatus?: string;
        ruleUuid?: string;
    }

    interface RuleCompareDataType {
        ruleHisVO1: RuleHistoryVO;
        ruleHisVO2: RuleHistoryVO;
        uuid1?: string;
        uuid2?: string;
    }

    const ruleCompareData = ref<RuleCompareDataType>({
        ruleHisVO1: {
            edition: "",
            textDtl: "",
            type: "",
            uuid: "",
            tableContent: "",
            tableContentHis: "",
            validStatus: "",
        },
        ruleHisVO2: {
            edition: "",
            textDtl: "",
            type: "",
            uuid: "",
            tableContent: "",
            tableContentHis: "",
            validStatus: "",
        },
    });

    const tabClick = (key: string) => {
        ruleCompareData.value = {
            ruleHisVO1: {
                edition: "",
                textDtl: "",
                type: "",
                uuid: "",
                tableContent: "",
                tableContentHis: "",
                validStatus: "",
            },
            ruleHisVO2: {
                edition: "",
                textDtl: "",
                type: "",
                uuid: "",
                tableContent: "",
                tableContentHis: "",
                validStatus: "",
            },
        };

        if(key === "ruleEditionCompare") {
            if (checkSelectedRow(false)) {
                if (checkedData.value.length === 2) {
                    setTimeout(() => {
                        // 根据版本对比
                        let uuid1 = "";
                        let uuid2 = "";
                        if (
                            Number(checkedData.value[0].edition) >
                            Number(checkedData.value[1].edition)
                        ) {
                            uuid1 = checkedData.value[0].uuid || '';
                            uuid2 = checkedData.value[1].uuid || '';
                        } else {
                            uuid1 = checkedData.value[1].uuid || '';
                            uuid2 = checkedData.value[0].uuid || '';
                        }
                        compareRule({
                            uuid1: uuid1,
                            uuid2: uuid2,
                        }).then((res) => {
                            if (res.data) {
                                ruleCompareData.value = res.data;
                                ruleCompareData.value.uuid1 = uuid1;
                                ruleCompareData.value.uuid2 = uuid2;
                            }
                        });
                    });
                } else {
                    message?.error?.("请选择两条记录");
                    activeName.value = "ruleCompare"
                }
            }else{
              activeName.value = "ruleCompare"
            }
        }
    }

    //全屏切换
    const isFullscreen = ref(false)
    const onFullscreenToggle = () => {
        isFullscreen.value = !isFullscreen.value
    }
</script>
<style lang="scss" scoped>
    .rule_audit ::v-deep {
        table {
            width: 100%;
        }
        th,
        td {
            max-width: 90px !important;
            text-overflow: ellipsis;
            overflow-x: hidden;
        }

        .fixed-table th.xh {
            z-index: 2; /*表头的首列要在上面*/
            background-color: #f2f2f2;
            width: 40px;
            min-width: 40px;
        }

        .fullrow {
            background: #fff;
        }
        .biaoColor {
            color: red;
        }
        .a-form {
            margin-bottom: 30px;
        }
        .a-form-item {
            margin-bottom: 0px;
        }
        .a-row {
            margin-top: 0px;
        }
    }
</style>

