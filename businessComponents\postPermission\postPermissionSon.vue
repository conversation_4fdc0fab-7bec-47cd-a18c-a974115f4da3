<!-- 添加子机构页 -->

<script setup lang="ts">

import { useRouter, useRoute } from 'vue-router';
import { getSelect, getOrd, getSaveInfo, getPostson } from '@/api/post_permissions';
definePageMeta({
    title: '添加子机构'
})


interface Option {
    code: string;
    name: string;
}

interface Form {
    province: string;
    land: string;
    county_city: string;
}

interface ArrAll {
    orgName: string;
    orgId: string;
    level: string;
}

const router = useRouter();
const route = useRoute();
const message = inject('message')

// 定义props
const props = defineProps({
    record: {
        type: Object,
        required: true
    }
});

// 定义emit
const emit = defineEmits(['close', 'success']);

const form = ref<Form>({
    province: '',
    land: '',
    county_city: '',
});

const arrAll = ref<ArrAll>({
    orgName: '',
    orgId: '',
    level: '',
});

const province = ref<Option[]>([]);
const landarr = ref<Option[]>([]);
const county = ref<Option[]>([]);
const soncode = ref<string>('');
const selectprovinceName = ref<string>('');
const selectprovinceId = ref<string>('');
const selectlandName = ref<string>('');
const selectlandId = ref<string>('');
const selectcountyName = ref<string>('');
const selectcountyId = ref<string>('');
const plaseChoose = ref<Option>({
    name: '请选择',
    code: '',
});

onMounted(() => {
    form.value = props.record;
    linkage();
    getInpinfo();
    getcodeInfo();
});

const linkage = () => {
    getSelect({
        parentCode: '',
        parentTypeCode: '',
        typeCode: 'org_province',
    }).then((res) => {
        if (res.data) {
            res.data.unshift(plaseChoose.value);
            province.value = res.data;
        }
    });
};

const getcodeInfo = () => {
    const pars = {
        businessUuid: form.value.businessLineId,
    };
    getPostson(pars).then((res) => {
        soncode.value = res.data;
    });
};

const getInpinfo = () => {
    const pars = {
        id: form.value.id,
    };
    getOrd(pars).then((res) => {
        arrAll.value = res.data;
    });
};

const getInfoByVid = (arr: Option[], vid: string) => {
    return arr.find((i) => (vid ? i.code === vid : false));
};

const selectprovince = (vid: string) => {
    selectprovinceId.value = vid;
    selectprovinceName.value = getInfoByVid(province.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_province',
            typeCode: 'org_prefecture_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                landarr.value = res.data;
            }
        });
    } else {
        landarr.value = [];
        form.value.land = '';
        selectlandId.value = '';
        selectlandName.value = '';
        county.value = [];
        form.value.county_city = '';
        selectcountyId.value = '';
        selectcountyName.value = '';
    }
};

const selectland = (vid: string) => {
    selectlandId.value = vid;
    selectlandName.value = getInfoByVid(landarr.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_prefecture_city',
            typeCode: 'org_county_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                county.value = res.data;
            }
        });
    } else {
        county.value = [];
        form.value.county_city = '';
        selectcountyId.value = '';
        selectcountyName.value = '';
    }
};

const selectcounty = (vid: string) => {
    selectcountyId.value = vid;
    selectcountyName.value = getInfoByVid(county.value, vid)?.name || '';
    if (!vid) {
        form.value.county_city = '';
    }
};

const onSubmit = () => {
    const strordId = selectcountyId.value || selectlandId.value || selectprovinceId.value;
    const strordName = selectcountyName.value || selectlandName.value || selectprovinceName.value;
    if (strordId) {
        getSaveInfo({
            businessLineId: form.value.businessLineId,
            level: Number(form.value.level) + 1,
            orgId: strordId,
            orgName: strordName,
            parentOrgId: form.value.orgId,
            type: '0',
        }).then((res) => {
            if (res.data === '失败，该机构在该条线已经被创建过！') {
                message.warning(res.data);
            } else {
                message.success(res.data);
                emit('success');
                emit('close');
                router.push({
                    name: 'postPermission',
                    params: { dataline: soncode.value },
                });
            }
        });
    } else {
        message.error('请选择机构名称');
    }
};

const resetForm = () => {
    emit('close');
};

const organizationValue = computed(() => {
    return `${selectprovinceName.value}${selectlandName.value}${selectcountyName.value}`;
});


// 暴露方法给父组件
defineExpose({
    onSubmit
});

</script>

<template>
    <!-- 搜索区域 -->
    <a-form :model="form" label-width="100px" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="父机构名称">
            <a-input autocomplete="off" v-model:value="arrAll.orgName" disabled />
        </a-form-item>
        <a-form-item label="父机构代码">
            <a-input autocomplete="off" v-model:value="arrAll.orgId" disabled />
        </a-form-item>
        <a-form-item label="父级别">
            <a-input autocomplete="off" v-model:value="arrAll.level" disabled />
        </a-form-item>
        <a-form-item label="省级" name="province">
            <a-select v-model:value="form.province" placeholder="请选择" @change="selectprovince">
                <a-select-option
                        v-for="item in province"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="地级" name="land">
            <a-select v-model:value="form.land" placeholder="请选择" @change="selectland">
                <a-select-option
                        v-for="item in landarr"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="县级市" name="county_city">
            <a-select v-model:value="form.county_city" placeholder="请选择" @change="selectcounty">
                <a-select-option
                        v-for="item in county"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="当前机构">
            <a-input autocomplete="off" :value="organizationValue" disabled />
        </a-form-item>
    </a-form>
</template>
