<!-- 岗位权限页 -->

<script setup lang="ts">

import { postQuery, postLine, postDel } from '@/api/post_permissions';
import PostPermissionSon from '@/businessComponents/postPermission/postPermissionSon.vue';
import PostPermissionTransfer from '@/businessComponents/postPermission/postPermissionTransfer.vue';
import PostPermissionAuditorg from '@/businessComponents/postPermission/postPermissionAuditorg.vue';
import qs from 'qs';
import FlexDrawer from '@/components/FlexDrawer.vue';
import PostPermissionAdd from '@/businessComponents/postPermission/postPermissionAdd.vue';
import PostPermissionCopy from '@/businessComponents/postPermission/postPermissionCopy.vue';
import TableSkeleton from '@/components/TableSkeleton';
definePageMeta({
    title: '岗位权限'
})

const message = inject('message')
const modal = inject('modal')
const router = useRouter();
const route = useRoute();
const form = reactive({
    business: '',
});

// 添加modal相关的状态
const addSonModalVisible = ref(false);
const transferModalVisible = ref(false);
const auditOrgModalVisible = ref(false);
const currentRecord = ref(null);

// 添加三个 ref 的定义
const addSonRef = ref(null);
const transferRef = ref(null);
const auditOrgRef = ref(null);

const menusList = ref([]);
const tab = ref([]);
const copyuuid = ref();
const key = ref(0); // 添加key以便强制刷新表格
const expandAll = ref(true); // 控制表格是否默认展开所有行

// 添加抽屉相关的状态
const addDrawerVisible = ref(false);
const copyDrawerVisible = ref(false);
const addDrawerRef = ref(null);
const copyDrawerRef = ref(null);

// 添加加载状态控制
const loading = ref(false);

// 过滤选项的方法
const filterOption = (input: string, option: any) => {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const selectGet = (vid: string) => {
    const obj = menusList.value.find((item) => item.code === vid);
    if (obj) {
        form.business = obj.code;
        copyuuid.value = obj.uuid;
        // 保存当前选择的业务条线到localStorage
        localStorage.setItem('selectedBusiness', form.business);
    }
};

const selpost = () => {
    if (!form.business) {
        message.info('请先选择业务条线');
        return;
    }
    loading.value = true;
    const pars = {
        businessCode: form.business,
        type: '',
    };
    postQuery(pars).then((res) => {
        tab.value = res.data;
        // 保存当前选择的业务条线到localStorage
        localStorage.setItem('selectedBusiness', form.business);
        // 更新key以刷新表格，确保默认展开生效
        key.value += 1;
        // 设置expandAll为true确保表格展开
        expandAll.value = true;
        // 使用nextTick确保DOM更新后再触发展开
        nextTick(() => {
            expandAll.value = true;
            loading.value = false;
        });
    }).catch(() => {
        loading.value = false;
    });
};
const postsel = () => {
    loading.value = true;
    postLine().then((res) => {
        const choose = {
            name: '请选择',
            code: '',
        };
        menusList.value = [choose, ...res.data];

        // 恢复之前选择的业务条线
        const savedBusiness = localStorage.getItem('selectedBusiness');
        if (savedBusiness) {
            form.business = savedBusiness;
            // 恢复copyuuid的值
            const obj = menusList.value.find((item) => item.code === savedBusiness);
            if (obj) {
                copyuuid.value = obj.uuid;
            }
            // 加载数据
            selpost();
        } else {
            loading.value = false;
        }
    }).catch(() => {
        loading.value = false;
    });
};
const addline = () => {
    addDrawerVisible.value = true;
};

const addson = (strid: any) => {
    currentRecord.value = strid;
    addSonModalVisible.value = true;
};

const delson = (uid: any, index: number) => {
    modal.confirm({
        title: '温馨提示',
        content: '确定要删除所选机构条线吗？',
        okText: '确定',
        cancelText: '取消',
        type: 'warning',
        onOk() {
            postDel(qs.stringify({ id: uid.id })).then((res) => {
                message.success('删除成功')
                selpost();
            });
        },
        onCancel() {
        },
    });

};

const transferbtn = (vid: any) => {
    currentRecord.value = vid;
    transferModalVisible.value = true;
};

const defaultAuditOrgBtn = (vid: any) => {
    currentRecord.value = vid;
    auditOrgModalVisible.value = true;
};

const copyline = () => {
    if (!form.business) {
        message.info('请先选择业务条线');
    } else if (!tab.value.length) {
        message.info('失败，该条线没有机构树');
    } else {
        copyDrawerVisible.value = true;
    }
};

const rest = () => {
    tab.value = [];
    form.business = '';
    // 清除保存的业务条线
    localStorage.removeItem('selectedBusiness');
};

// 添加modal关闭处理方法
const handleAddSonModalClose = () => {
    addSonModalVisible.value = false;
    currentRecord.value = null;
};

const handleTransferModalClose = () => {
    transferModalVisible.value = false;
    currentRecord.value = null;
};

const handleAuditOrgModalClose = () => {
    auditOrgModalVisible.value = false;
    currentRecord.value = null;
};

// 添加抽屉关闭处理方法
const handleAddDrawerClose = () => {
    addDrawerVisible.value = false;
};

const handleCopyDrawerClose = () => {
    copyDrawerVisible.value = false;
};

onMounted(() => {
    loading.value = true;
    postsel();
});

</script>

<template>
    <ListPageLayout >

        <template #title>
            <!-- 页面标题 -->
            岗位权限
        </template>

        <template #search>
            <!-- 搜索区域 -->
           <a-form :model="form" label-width="100px" layout="inline">
                    <a-form-item label="业务条线">
                        <a-select
                                v-model:value="form.business"
                                placeholder="请选择"
                                style="width:200px"
                                @change="selectGet"
                                :filterOption="filterOption" showSearch
                        >
                            <a-select-option
                                    v-for="item in menusList"
                                    :key="item.code"
                                    :value="item.code"
                                    :name="item.name"
                            >
                                {{ item.name }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item class="buttonItem">
                        <a-button type="primary" @click="selpost" style="margin-right: 8px">查询</a-button>
                        <a-button @click="rest"  style="margin-right: 8px">重置</a-button>
                        <a-button @click="addline"  style="margin-right: 8px">新增机构条线</a-button>
                        <a-button @click="copyline" >复制本业务条线机构</a-button>
                    </a-form-item>
                </a-form>
        </template>

        <template #table="slotProps">
            <!-- 表格区域 -->
            <!-- 加载中显示骨架屏 -->
            <TableSkeleton
                v-if="loading"
                :columns="[
                    { title: '机构名称', key: 'orgName', width: 200 },
                    { title: '机构代码', key: 'orgId', width: 120 },
                    { title: '级别', key: 'level', width: 80 },
                    { title: '默认任务审核机构', key: 'defaultAuditOrg', width: 120 },
                    { title: '操作', key: 'action', width: 480 }
                ]"
                :limit="10"
                :scrollY="slotProps.scrollY"
            />
            <!-- 加载完成显示表格数据 -->
            <a-table
                v-else
                :scroll="{ y: slotProps.scrollY }"
                :data-source="tab"
                :key="key"
                style="width: 100%; margin-bottom: 20px"
                :defaultExpandAllRows="expandAll"
                :pagination="false"
                size="small"
                row-key="id"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
                <a-table-column  align="left"  ellipsis="true" key="orgName" title="机构名称" data-index="orgName" />
                <a-table-column  align="center"  ellipsis="true" key="orgId" title="机构代码" data-index="orgId" />
                <a-table-column  align="center"  ellipsis="true" key="level" title="级别" data-index="level" />
                <a-table-column  align="center"  ellipsis="true" key="defaultAuditOrg" title="默认任务审核机构" data-index="defaultAuditOrg"  />
                <a-table-column  align="center"  ellipsis="true" title="操作" :width="480">
                    <template #default="scope">
                        <a-button type="link" @click="addson(scope.record)" size="small">添加子机构</a-button>
                        <a-button type="link" @click="delson(scope.record, scope.$index)" size="small" style="margin-left: 10px">删除</a-button>
                        <a-button
                                v-if="scope.record.level !== '1'"
                                type="link"
                                @click="transferbtn(scope.record)"
                                size="small"
                                style="margin-left: 10px"
                        >转移机构</a-button>
                        <a-button
                                v-if="scope.record.level !== '1'"
                                type="link"
                                @click="defaultAuditOrgBtn(scope.record)"
                                size="small"
                                style="margin-left: 10px"
                        >设置默认任务审核机构</a-button>
                    </template>
                </a-table-column>
            </a-table>
        </template>
    </ListPageLayout>

    <!-- 添加子机构弹框 -->
    <a-modal
        v-model:visible="addSonModalVisible"
        title="添加子机构"
        @cancel="handleAddSonModalClose"
        width="800px"
        v-if="addSonModalVisible"
    >
        <PostPermissionSon
            ref="addSonRef"
            :record="currentRecord"
            @close="handleAddSonModalClose"
            @success="selpost"
        />
        <template #footer>
            <a-button @click="handleAddSonModalClose">取消</a-button>
            <a-button type="primary" @click="addSonRef?.onSubmit">保存</a-button>
        </template>
    </a-modal>

    <!-- 转移机构弹框 -->
    <a-modal
        v-model:visible="transferModalVisible"
        title="转移机构"
        @cancel="handleTransferModalClose"
        width="800px"
        v-if="transferModalVisible"
    >
        <PostPermissionTransfer
            ref="transferRef"
            :record="currentRecord"
            @close="handleTransferModalClose"
            @success="selpost"
        />
        <template #footer>
            <a-button @click="handleTransferModalClose">取消</a-button>
            <a-button type="primary" @click="transferRef?.onSubmit">保存</a-button>
        </template>
    </a-modal>

    <!-- 设置默认任务审核机构弹框 -->
    <a-modal
        v-model:visible="auditOrgModalVisible"
        title="设置默认任务审核机构"
        @cancel="handleAuditOrgModalClose"
        width="800px"
        v-if="auditOrgModalVisible"
    >
        <PostPermissionAuditorg
            ref="auditOrgRef"
            :record="currentRecord"
            @close="handleAuditOrgModalClose"
            @success="selpost"
        />
        <template #footer>
            <a-button type="primary" @click="auditOrgRef?.delDefaultAuditOrgs">删除默认任务审核机构</a-button>
            <a-button type="primary" @click="auditOrgRef?.onSubmit">保存</a-button>
        </template>
    </a-modal>

    <!-- 新增机构条线抽屉 -->
    <FlexDrawer
        v-model:visible="addDrawerVisible"
        title="新增机构条线"
        @close="handleAddDrawerClose"
        width="800px"
        v-if="addDrawerVisible"
    >
        <PostPermissionAdd
            ref="addDrawerRef"
            @close="handleAddDrawerClose"
            @success="selpost"
        />
        <template #footer>
            <a-button type="primary" @click="addDrawerRef?.onSubmit">提交</a-button>
        </template>
    </FlexDrawer>

    <!-- 复制本业务条线机构抽屉 -->
    <FlexDrawer
        v-model:visible="copyDrawerVisible"
        title="复制本业务条线机构"
        @close="handleCopyDrawerClose"
        width="800px"
        v-if="copyDrawerVisible"
    >
        <PostPermissionCopy
            ref="copyDrawerRef"
            :copyUuid="copyuuid"
            @close="handleCopyDrawerClose"
            @success="selpost"
        />
        <template #footer>
            <a-button type="primary" @click="copyDrawerRef?.onSubmit">复制</a-button>
        </template>
    </FlexDrawer>
</template>
