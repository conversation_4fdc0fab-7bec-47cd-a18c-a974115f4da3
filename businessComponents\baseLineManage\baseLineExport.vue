<!-- 基线导出组件 -->
<script setup lang="ts">

const message = inject('message')

import {getBaseLineRel, getSuffix, getExport, getImportList, getExportDown,} from '@/api/baseline_Management';
import { pubEnvUUid } from '@/api/pub_environment';
import type { FormInstance } from 'ant-design-vue';
import qs from "qs";
import { ref, reactive, watch, onBeforeUnmount, inject, h } from 'vue';
import useResize from '@/composables/useResize';
import TableSkeleton from '@/components/TableSkeleton.vue';
import Pagination from '@/components/Pagination.vue';

// 定义props，接收父组件传递的参数
const props = defineProps({
    u: {
        type: String,
        default: ''
    },
    rel: {
        type: String,
        default: ''
    }
});



interface RuleForm {
    engChineseName: string;
    engName: string;
    createdTimeStr: string;
    edition: string;
    descs: string;
    ruleSet: string;
    status: string;
}

interface EnvOption {
    id: string;
    environmentName: string;
}

const ruleForm = ref<RuleForm>({
    engChineseName: '',
    engName: '',
    createdTimeStr: '',
    edition: '',
    descs: '',
    ruleSet: '',
    status: '',
});

const ruleForm1 = reactive({
    env: '',
    ruleSetdec: '',
});

const ruleSet = ref<string>('');
const relId = ref<string>('');
const baseEngUuid = ref<string>('');
const envArr = ref<EnvOption[]>([]);
const strEnv = ref<string>('');
const rules = {
    env: [{ required: true, message: '请选择发布环境', trigger: 'change' }],
    ruleSetdec: [
        { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' },
    ],
};
const paginations = ref({
    loading: true,
    total: 0, // 总数
    limit: 10, // 一页显示都是条
    page: 1, // 当前页
    showSizeChanger:true,
    showTotal: (total: number) => `共 ${total} 条数据`,
});

const customRenderExportStatus = ({ text }: any) => {
    if (text === '4') {
        return '导出中';
    } else if (text === '2') {
        return '导出成功';
    } else {
        return '导出失败';
    }
};

// 添加自定义渲染函数处理导出路径显示
const customRenderExportPath = ({ text }: any) => {
    if (!text) return '';
    
    // 隐藏/home/<USER>/前缀，只显示后面部分
    const prefix = '/home/<USER>/';
    
    if (text.startsWith(prefix)) {
        // 移除前缀部分
        const pathWithoutPrefix = text.substring(prefix.length);
        // 悬浮显示也隐藏前缀
        return h('a-tooltip', { title: pathWithoutPrefix }, 
            h('span', {}, pathWithoutPrefix)
        );
    }
    
    // 如果路径不是以"/home/<USER>/"开头，直接返回原路径
    return text;
};

const tableDate = ref<any[]>([]);
const timer = ref<NodeJS.Timeout | null>(null);
const tableLoading = ref(false);

// 表格列定义
const columns = ref([
    {
        title: '基线版本',
        dataIndex: 'snapShootEdition',
        key: 'snapShootEdition',
        align: 'center',
        ellipsis: true
    },
    {
        title: '规则集版本',
        dataIndex: 'ruleMusterEdition',
        key: 'ruleMusterEdition',
        align: 'center',
        ellipsis: true
    },
    {
        title: '导出状态',
        dataIndex: 'exportStatus',
        key: 'exportStatus',
        align: 'center',
        ellipsis: true
    },
    {
        title: '导出路径',
        dataIndex: 'exportPath',
        key: 'exportPath',
        align: 'center',
        ellipsis: true
    },
    {
        title: '创建时间',
        dataIndex: 'createdTimeStr',
        key: 'createdTimeStr',
        width: 180,
        align: 'center',
        ellipsis: true
    },
    {
        title: '操作',
        key: 'action',
        align: 'center'
    }
]);

const getBaseLinText = async (fn?: () => void) => {
    try {
        const [baseLineRes, suffixRes] = await Promise.all([
            getBaseLineRel({ uuid: relId.value }),
            getSuffix(),
        ]);
        ruleForm.value = baseLineRes.data;
        const SuffixData = suffixRes.data;
        ruleSet.value = ruleForm.value.engName + SuffixData;
        if (fn) fn();
    } catch (error) {
        console.error(error);
    }
};

const getEnvInfo = async () => {
    try {
        const res = await pubEnvUUid({ engUuid: baseEngUuid.value });
        const choseSel = { environmentName: '请选择', id: '' };
        res.data.unshift(choseSel);
        envArr.value = res.data;
        ruleForm1.env = res.data[1].environmentName;
        strEnv.value = res.data[1].id;
        if (
            ruleForm1.env !== '' &&
            res.data[0].environmentName === '请选择'
        ) {
            strEnv.value = res.data[1].id;
        }
    } catch (error) {
        console.error(error);
    }
};

const selEnv = (vid: string) => {
    const obj = envArr.value.find((item) => item.id === vid);
    if (obj) {
        strEnv.value = obj.id;
    }
};

const exportBtn = () => {
    if (!ruleSet.value) {
        getBaseLinText(() => toExport());
    } else {
        toExport();
    }
};

const ruleFormRef = ref();

const toExport = () => {
    ruleFormRef.value.validate().then((valid: boolean) => {
        if (valid) {
            const params = qs.stringify({
                descs: ruleForm1.ruleSetdec,
                engUuid: baseEngUuid.value,
                environmentId: strEnv.value,
                recordType: "isNotRecord",
                ruleMusterName: ruleSet.value,
                snapShootUuid: relId.value,
            });
            getExport(params).then((res) => {
                message.info('正在导出');
                getListInfo();
            });
        }
    });
};

const getListInfo = async () => {
    tableLoading.value = true;
    try {
        const params = {
            engUuid: baseEngUuid.value,
            snapShootUuid: relId.value,
            page: paginations.value.page,
            several: paginations.value.limit,
        };
        const res = await getImportList(params);
        tableDate.value = res.data.data;
        paginations.value.total = res.data.totalCount;
    } catch (error) {
        console.error(error);
    } finally {
        tableLoading.value = false;
    }
};

const pagin = (cur: number) => {
    paginations.value.page = cur;
    getListInfo();
};

const download = (u: any) => {
    const data = qs.stringify({
        path: u.exportPath,
    });
    getExportDown(data).then((res) => {
        const headers = res.headers;

        const blob = new Blob([res.data], {
            type: headers["content-type"],
        });

        const link = document.createElement("a");
        const url = window.URL.createObjectURL(blob);

        const fileName = headers["content-disposition"]
            .split(";")[1]
            .split("=")[1];

        link.href = url;
        link.download = decodeURI(fileName);
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
    });
};

const initialize = () => {
    // 初始化组件数据
    relId.value = props.u;
    baseEngUuid.value = props.rel;

    // 获取基础数据
    getBaseLinText();
    getEnvInfo();
    getListInfo();

    // 设置定时刷新
    timer.value = setInterval(() => {
        getListInfo();
    }, 10000);
};

// 监听props变化，初始化组件
watch(() => [props.u, props.rel], () => {
    if (props.u && props.rel) {
        initialize();
    }
}, { immediate: true });

onBeforeUnmount(() => {
    if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
    }
});

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);
</script>

<template>
    <div class="baseline-export-container">
        <div class="form-section">
            <a-collapse class="base-info-collapse">
                <a-collapse-panel key="1" header="基线信息详情">
                    <a-form :label-col="{ style: { width: '100px', textAlign: 'right' } }">
                        <a-row :gutter="16">
                            <a-col :span="12">
                                <a-form-item label="规则库名称">
                                    <a-typography-text>{{ ruleForm.engChineseName }}</a-typography-text>
                                </a-form-item>
                                <a-form-item label="创建时间">
                                    <a-typography-text>{{ ruleForm.createdTimeStr }}</a-typography-text>
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="基线版本">
                                    <a-typography-text>{{ ruleForm.edition }}</a-typography-text>
                                </a-form-item>
                                <a-form-item label="基线描述">
                                    <a-typography-text>{{ ruleForm.descs }}</a-typography-text>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </a-collapse-panel>
            </a-collapse>
        </div>

        <div class="export-form-section">
            <a-form
                ref="ruleFormRef"
                :model="ruleForm1"
                :rules="rules"
                :label-col="{ span: 5 }"
                :wrapper-col="{ span: 18 }"
                class="export-form"
            >
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="导出环境" name="env">
                            <a-select v-model:value="ruleForm1.env" placeholder="请选择" @change="selEnv">
                                <a-select-option v-for="item in envArr" :key="item.id" :value="item.id">
                                    {{ item.environmentName }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="导出基线描述" name="ruleSetdec">
                            <a-input placeholder="请输入导出基线描述" v-model:value="ruleForm1.ruleSetdec" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-form-item class="form-actions">
                    <a-button type="primary" @click="exportBtn">导出</a-button>
                </a-form-item>
            </a-form>
        </div>

        <div class="table-section">
            <h3>导出历史</h3>
            <div ref="basePoint">
                <!-- 骨架屏 -->
                <TableSkeleton
                    v-if="tableLoading"
                    :columns="columns"
                    :limit="paginations.limit"
                    :scrollY="scrollY-30"
                />
                <!-- 数据表格 -->
                <a-table
                    v-else
                    :data-source="tableDate"
                    width="100%"
                    :pagination="false"
                    :scroll="{y: scrollY-30 }"
                >
                    <a-table-column align="center" key="snapShootEdition" title="基线版本" data-index="snapShootEdition" :ellipsis="true" />
                    <a-table-column align="center" key="ruleMusterEdition" title="规则集版本" data-index="ruleMusterEdition" :ellipsis="true" />
                    <a-table-column align="center" key="exportStatus" title="导出状态" data-index="exportStatus" :ellipsis="true" :customRender="customRenderExportStatus" />
                    <a-table-column align="center" key="exportPath" title="导出路径" data-index="exportPath" :ellipsis="true" :customRender="customRenderExportPath" />
                    <a-table-column align="center" key="createdTimeStr" title="创建时间" data-index="createdTimeStr" width="180" :ellipsis="true" />
                    <a-table-column align="center" key="action" title="操作">
                        <template #default="{ record }">
                            <a-button
                                    type="link"
                                    @click="download(record)"
                                    :disabled="record.exportStatus !== '2'"
                            >
                                下载
                            </a-button>
                        </template>
                    </a-table-column>
                </a-table>
                <Pagination
                        :paginations="paginations"
                        @change="pagin"
                        :scrollY="scrollY-30"
                />
            </div>

        </div>
    </div>
</template>

<style lang="scss" scoped>
.baseline-export-container {

    .form-section, .export-form-section, .table-section {
        margin-bottom: 5px;
        background-color: #fff;
        padding: 10px 20px;
        border-radius: 4px;
    }

    .base-info-collapse {
        margin: 0;

        :deep(.ant-collapse-header) {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            font-weight: 500;
        }

        :deep(.ant-collapse-content-box) {
            padding: 16px;

            .ant-form-item {
                margin-bottom: 8px;
            }

            .ant-form-item-label > label {
                font-weight: bold;
            }

            .ant-typography {
                line-height: 32px;
            }
        }
    }

    .export-form {
        :deep(.ant-form-item-label > label) {
            font-weight: bold;
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
    }

    .table-section {
        h3 {
            margin-bottom: 16px;
            font-weight: 500;
        }
    }
}
</style>
