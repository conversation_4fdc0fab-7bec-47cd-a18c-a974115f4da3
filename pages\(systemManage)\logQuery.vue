<!--日志查询-->
<script setup lang="ts">
import { logList } from "@/api/log_query";
definePageMeta({
    title: '日志查询'
})

const message = inject('message') as { error: (msg: string) => void };

interface SearchField {
    label: string;
    field: string;
    compType: 'input' | 'select' | 'datePicker';
    compConfig?: Record<string, any>;
}

interface SearchConfig {
    simpleSearchField: {
        label: string;
        field: string;
    };
    advancedSearchFields: SearchField[];
}

interface Log {
    loginId: string;
    name: string;
    ipAddress: string;
    eventLevel: string;
    taskCategory: string;
    event: string;
    happendDateStr: string;
    remark: string;
    uuid: string;
}

interface FormState {
    id: string;
    name: string;
    fristTime: string;
    endTime: string;
    ipAddress: string;
    eventLevel: string | null;
    taskCategory: string;
    event: string;
    remark: string;
}

const columns = reactive([
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
        align: 'left',
        ellipsis: true,
        width:150,
    },
    {
        title: '登录ID',
        dataIndex: 'loginId',
        key: 'loginId',
        align: 'left',
        ellipsis: true,
        width:150,
    },
    {
        title: '来源',
        dataIndex: 'ipAddress',
        key: 'ipAddress',
        align: 'left',
        ellipsis: true,
        width:200,
    },
    {
        title: '级别',
        dataIndex: 'eventLevel',
        key: 'eventLevel',
        align: 'center',
        ellipsis: true,
        width:200,
    },
    {
        title: '任务类别',
        dataIndex: 'taskCategory',
        key: 'taskCategory',
        align: 'center',
        ellipsis: true,
        width:120,
    },
    {
        title: '关键字',
        dataIndex: 'event',
        key: 'event',
        align: 'center',
        ellipsis: true,
        width:150,
    },
    {
        title: '时间',
        dataIndex: 'happendDateStr',
        key: 'happendDateStr',
        align: 'center',
        ellipsis: true,
        width:180,
    },
    {
        title: '结果',
        dataIndex: 'remark',
        key: 'remark',
        width:200,
        align: 'left',
        ellipsis: true,
    },
]);

const fetchLogs = async (params: Record<string, any> = {}) => {
    try {
        const res = await logList({
            loginId: params.id,
            name: params.name,
            startTime: params.fristTime,
            endTime: params.endTime,
            ipAddress: params.ipAddress,
            eventLevel: params.eventLevel,
            taskCategory: params.taskCategory,
            event: params.event,
            remark: params.remark,
            page: params.page || 1,
            several: params.pageSize || 10,
        });
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取日志失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

// selPub函数
const selPub = (val: string) => {
    console.log("级别选择变更:", val);
};

// 搜索配置
const searchConfig = reactive<SearchConfig>({
    // 简单搜索字段
    simpleSearchField: {
        label: '姓名',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '登录ID',
            field: 'id',
            compType: 'input' as const
        },
        {
            label: '姓名',
            field: 'name',
            compType: 'input' as const
        },
        {
            label: '级别',
            field: 'eventLevel',
            compType: 'select' as const,
            compConfig: {
                options: [
                    { name: '一级', value: '一级' },
                    { name: '二级', value: '二级' },
                    { name: '三级', value: '三级' }
                ],
                onChange: selPub
            }
        },
        {
            label: '任务类别',
            field: 'taskCategory',
            compType: 'input' as const
        },
        {
            label: '关键字',
            field: 'event',
            compType: 'input' as const
        },
        {
            label: '结果',
            field: 'remark',
            compType: 'input' as const
        },
        {
            label: '来源',
            field: 'ipAddress',
            compType: 'input' as const
        },
        {
            label: '开始时间',
            field: 'fristTime',
            compType: 'datePicker' as const,
            compConfig: {
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD 00:00:00'
            }
        },
        {
            label: '结束时间',
            field: 'endTime',
            compType: 'datePicker' as const,
            compConfig: {
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD 23:59:59'
            }
        }
    ]
});

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加事件
    addNewEvent: () => {},
    // 表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchLogs
    }
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);
// 需要自定义渲染的列
const customRenderColumns = ['name'];
</script>

<template>
    <ListPage
        ref="listLayout"
        title="日志查询"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="false"
        :tableColumns="columns"
        :queryMethod="fetchLogs"
        rowKey="uuid"
        :customRenderColumns="customRenderColumns"
        :showActionColumn="false"
    >
        <template #name="{ record }">
            <div style="height: 32px;">
                {{record.name}}
            </div>
        </template>
    </ListPage>
</template>
