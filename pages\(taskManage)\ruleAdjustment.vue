<!--规则调整-->

<script setup lang="ts">
    import { ruleAdjustment, userOrgList } from "@/api/task";
    import { getBaseLine } from "@/api/baseline_Management";
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';

    definePageMeta({
        title: '规则调整'
    })

    const message = inject('message') as any;
    interface Rule {
        appNo: string;
        demandName: string;
        applyOrgName: string;
        orgName: string;
        businessLineName: string;
        createdName: string;
        createdTimeStr: string;
        state: string;
    }

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    const taskStateOptions = ref([
        { value: "5", label: "规则待编写" },
        { value: "6", label: "规则编写中" },
        { value: "15", label: "规则编写完成" },
        { value: "16", label: "发布测试环境失败" },
        { value: "17", label: "发布测试环境等待" },
        { value: "9", label: "规则测试验证错误，退回" },
        { value: "23", label: "任务发布退回" },
        { value: "18", label: "任务审核退回" },
    ]);

    const businessLineOptions = ref<any[]>([]);
    const modelTypeOptions = ref<any[]>([]);

    // 获取列表数据
    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await ruleAdjustment({
                appNo: params.appNo,
                applyOrgId: params.applyOrgId,
                businessLine: params.businessLine,
                createdName: params.createdName,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                createdTimeStr: params.createdTimeStr,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取规则失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getOption = () => {
        getBaseLine().then((res) => {
            businessLineOptions.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };

    const getCreatedName = (value: string) => {
        // 手动触发搜索配置中归属机构选择器的值更新
        const applyOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'applyOrgId');
        if (applyOrgField && applyOrgField.compConfig) {
            // 先清空选项
            applyOrgField.compConfig.options = [];
        }

        userOrgList({
            roleName: " DEMAND_ROLE_EDIT",
            businessCode: value,
        }).then((res: any) => {
            modelTypeOptions.value = res.data;
            // 更新搜索配置中的归属机构选项
            if (applyOrgField && applyOrgField.compConfig) {
                if(modelTypeOptions.value){
                    // 使用新数组替换原有options，保证响应式更新
                    const newOptions = modelTypeOptions.value.map(item => ({
                        name: item.orgName,
                        value: item.id
                    }));
                    // 使用nextTick确保DOM更新后再填充数据
                    nextTick(() => {
                        applyOrgField.compConfig.options = [...newOptions];
                    });
                }
            }
        });
    }

    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type: string, record: any = {}) => {
        modalType.value = type;
        if (type === 'info') {
            datar.value = {
                type: "info",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        if (type === 'adjustment') {
            datar.value = {
                type: "adjustment",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                createdId: record.createdId,
                createdName: record.createdName,
            };
        }
        isModalVisible.value = true;
    };

    const close = (flag: boolean) => {
        isModalVisible.value = false;
        //提交数据后刷新页面
        if(flag === true){
            listLayout.value?.refresh();
        }
    }

    // 规则调整
    const ruleAdjust = (record: any = {}) => {
        navigateTo('/ruleBase-demandUuid-'+record.uuid+'-businessUuid-'+record.businessLine+'?backPage='+listLayout.value?.currentPage)
    };

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input'
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input'
            },
            {
                label: '来源',
                field: 'ipAddress',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: getCreatedName,
                    clearFields: ['applyOrgId']
                }
            },
            {
                label: '归属机构',
                field: 'applyOrgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input'
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select',
                compConfig: {
                    options: taskStateOptions.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: (formValue: any) => {
            listLayout.value?.refresh();
        },
        // 添加事件
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchTaskList
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: any) => {
        return [
            {
                key: 'edit',
                label: '编辑',
                onClick: () => showModal('adjustment', record)
            },
            {
                key: 'adjust',
                label: '规则调整',
                onClick: () => ruleAdjust(record)
            }
        ];
    };

    // 组件引用
    const listLayout = ref(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];

    onMounted(() => {
        getOption();
    });
</script>

<template>
    <ListPage
        ref="listLayout"
        title="规则调整"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="false"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('info',record)">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state as keyof typeof TASK_STATE] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'规则调整'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
