<template>
  <div class="RuleFlow">
    <!-- 渲染 bpmn 组件，并传递 ruleInfo 属性 -->
    <bpmn :ruleInfo="ruleInfo"></bpmn>
  </div>
</template>

<script setup>
import bpmn from "@/components/bpmn";

// 定义 props
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
});

</script>

<style lang="scss">
// 导入 bpmn-js 的样式文件
@use 'bpmn-js/dist/assets/diagram-js.css';
@use 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
@use 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
@use 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';

// 导入自定义的样式文件
@use '@/components/ruleFlow/css/diagram-js.scss' as *;
@use '@/components/ruleFlow/css/bpmn.scss' as *;
@use "@/components/ruleFlow/css/iconfont.css";
</style>
