<template>
  <div class="designView" style="font-weight: 500">
    <div class="ruleBody" style="padding-left: 0; padding-top: 10px">
      <!-- 条件列表组件 -->
      <ConditionList
        hasTitle
        :showAddUp="false"
        pos="r"
        :conditionData="conditionData"
        :validList="validList"
        :locked="locked"
        @conditionChange="onConditionChange"
        @addRule="addRule"
        @addChildCondition="addChildCondition"
        @addTailItem="addTailItem"
        @decreaseRule="decreaseRule"
        @onSwitcherChange="onSwitcherChange"
        @logicBtnClick="onLogicBtnClick"
        :isTable="isTable"
        :noRuleCellUnit="noRuleCellUnit"
      />
      <!-- 下拉选择器或输入框 -->
      <span
        v-if="cascaderSelect"
        style="color: rgb(65, 8, 246); font-size: 12px; font-weight: bold; margin-left: 20px;"
        class="select-enum-multiple"
      >
        等于(
        <a-select
          style="width: 110px; height: 21px; color: #000; font-weight: bold;"
          class="booleanSelect"
          v-model:value="enumValue"
          :allowClear="isTable"
          showSearch
          placeholder="一个布尔值"
          @change="onEnumChange"
          :filterOption="filterOption"
          :options="[
            { value: 'true', label: '是' },
            { value: 'false', label: '否' },
          ]"
        ></a-select>
        )
      </span>
      <a-dropdown
        v-else
        :trigger="['click']"
        style="margin-left: 20px"
        @select="onSelectChange"
      >
        <a class="el-dropdown-link" @click.prevent style="color: rgb(65, 8, 246); font-size: 12px; cursor: pointer; font-weight: bold;">
          请选择
        </a>
        <template #overlay>
          <a-menu @click="onSelectChange('eq')">
            <a-menu-item key="eq">
              等于(#1,<布尔值>)
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";
import ConditionList from "@/components/ruleEditCom/condition/conditionList.vue";
const message = inject("message");
const { ConditionGenerate } = util;

// 初始化条件数据
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};

// 初始化规则数据
const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
};

// 生成唯一ID
const idRef = {
  current: 1000,
};

// 注入 ruleUuid
const ruleUuid = inject('ruleUuid', '');

// 定义 props
const props = defineProps({
  conditions: {
    type: Object,
    default: () => ({}),
  },
  validList: {
    type: Array,
    default: () => [],
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义 data
const conditionData = ref(props.conditions);
const cascaderVal = ref([]);
const cascaderOpt = ref([
  {
    label: "等于(#1,<布尔值>)",
    paramQuantity: 1,
    paramTypes: "Boolean",
    value: "eq",
  },
]);
const cascaderSelect = ref(props.conditions.complexSelect || false);
const enumValue = ref(props.conditions.allCellUnit || undefined);
// 生命周期钩子
onMounted(() => {
  const { allCellUnit } = props.conditions;
  if (allCellUnit !== undefined && allCellUnit !== '') {
    enumValue.value = allCellUnit;
  }
});

// 方法
const onConditionChange = (pos, newContents) => {
  const [, conditionId] = pos.split("_");
  const { targetNode } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  targetNode.ruleCondition.contents = new ConditionGenerate(newContents);
  emit("onchange", conditionData.value);
};

const addRule = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 1);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData),
    },
  });
  emit("onchange", conditionData.value);
};

const addChildCondition = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 2);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    fold: false,
    children: [
      {
        indent: targetNode.indent + 1,
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData),
        },
      },
      {
        indent: targetNode.indent + 1,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 2,
          showLayer: layer + 2,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData),
        },
      },
    ],
  });
  emit("onchange", conditionData.value);
};

const addTailItem = (pos, conditionId) => {
  const { parentNode, targetNode } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  const lastCondition = util.getLastNode(parentNode);
  const { layer } = lastCondition;
  util.updateLayers(layer, conditionData.value, 1);

  arr.push({
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData),
    },
  });
  emit("onchange", conditionData.value);
};

const decreaseRule = ({ pos, conditionId, layer }) => {
  const { parentNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  if (arr.length === 1) {
    // 提示信息
    return message.error("请至少保留一条数据！");
  }
  if (arr.length > 2) {
    arr.splice(arrIndex, 1);
  } else if (arr.length === 2) {
    arr.splice(arrIndex, 1);
    const _logicalSymbol = parentNode.logicalSymbol;
    // 如果数组中只有两组数据，删除后，子节点逻辑组合，将转化为一个普通的子节点；
    if (parentNode !== conditionData.value || !!arr[0].children) {
      if (arr[0].ruleCondition) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
        delete parentNode.children;
      } else if (arr[0].children) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
      }
      parentNode.logicalSymbol = _logicalSymbol;
      util.updateIndent(parentNode, -1);
    }
  }
  if (arr && arrIndex / 1 === 0) {
    delete arr[0].logicalSymbol;
  }
  util.updateLayers(layer, conditionData.value, -1);
  emit("onchange", conditionData.value);
};

const onSwitcherChange = (pos, conditionId, layer) => {
  util.updateNodeFold(conditionData.value, conditionId, layer);
  emit("onchange", conditionData.value);
};

const onLogicBtnClick = (pos, val) => {
  const [, targetIndent, targetLayer] = pos.split("_");
  const logicNode = util.findLogicNode(
    conditionData.value,
    targetIndent,
    targetLayer
  );
  logicNode.logicalSymbol = val || null;
  emit("onchange", conditionData.value);
};

const onSelectChange = (value) => {
  if (value === "eq") {
    cascaderSelect.value = true;
  } else {
    cascaderSelect.value = false;
  }
  conditionData.value.complexSelect = cascaderSelect.value;
  conditionData.value.cellUnit = cascaderSelect.value;
  conditionData.value.allCellUnit = enumValue.value;
  emit("setComplexSelect", cascaderSelect.value);
};

const onEnumChange = (value) => {
  enumValue.value = value;
  conditionData.value.allCellUnit = enumValue.value;
  emit("setAllCellUnit", value);
};

// 定义 emit
const emit = defineEmits(['onchange', 'setComplexSelect', 'setAllCellUnit']);
</script>

<style scoped>
/* 添加样式 */
</style>
