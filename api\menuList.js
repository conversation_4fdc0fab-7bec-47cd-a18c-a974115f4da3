import request from '@/utils/request'
// 菜单列表
export function menu(params) {
  return request({
    url: 'sys/menu/list',
    method: 'get',
    params,
    headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
    
  })
}
  // 启用/禁用
  export function menuEnable(data) {
    return request({
      url: 'sys/menu/changeState',
      method: 'post',
      data,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
    })
  }
  // 新增
  export function menuAddsub(data) {
    return request({
      url: 'sys/menu/addAction',
      method: 'post',
      data,
    })
  }
  // 获取新增页面中的上级菜单
  export function ParentMenu() {
    return request({
      url: 'sys/menu/getAllParentMenus',
      method: 'get',
    })
  }
  // 修改提交
  export function menuUpdate(data) {
    return request({
      url: 'sys/menu/updateAction',
      method: 'post',
      data
    })
  }
  // 

  export function menuById(params) {
    return request({
      url: 'sys/menu/getMenuById',
      method: 'get',
      params
    })
  }

  // 修改顺序
  export function changeOrder(data) {
    return request({
      url: 'sys/menu/changeOrder',
      method: 'post',
      data,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
    })
  }
