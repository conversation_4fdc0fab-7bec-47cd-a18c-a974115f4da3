<!-- 所有规则库图标样式 -->

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 20
})
</script>

<template>
  <svg :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }" class="icon" style="vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11546"><path d="M128.175262 649.055226v174.561387C128.175262 934.732999 300.049293 1024 512 1024S895.824738 934.732999 895.824738 823.616613V649.055226z" fill="#99C9F2" p-id="11547"></path><path d="M128.175262 649.055226a383.824738 200.383387 0 1 0 767.649476 0 383.824738 200.383387 0 1 0-767.649476 0Z" fill="#E0EFFB" p-id="11548"></path><path d="M128.175262 424.719306v174.561388c0 110.649019 171.87403 200.383387 383.824738 200.383386S895.824738 709.929712 895.824738 599.280694V424.719306z" fill="#99C9F2" p-id="11549"></path><path d="M128.175262 424.719306a383.824738 200.383387 0 1 0 767.649476 0 383.824738 200.383387 0 1 0-767.649476 0Z" fill="#E0EFFB" p-id="11550"></path><path d="M128.175262 200.383387v174.561387c0 110.649019 171.87403 200.383387 383.824738 200.383387S895.824738 485.593793 895.824738 374.944774V200.383387z" fill="#99C9F2" p-id="11551"></path><path d="M128.175262 200.383387a383.824738 200.383387 0 1 0 767.649476 0 383.824738 200.383387 0 1 0-767.649476 0Z" fill="#E0EFFB" p-id="11552"></path></svg>
</template>
