import dayjs from "dayjs";

export function calculateWidth(val, falg) {
  const text = val ? val.toString() : "";
  const textDom = document.createElement("span");
  textDom.innerText = text;
  textDom.style.position = "absolute";
  textDom.style.visibility = "hidden";
  document.body.append(textDom);
  const width = textDom.offsetWidth;
  document.body.removeChild(textDom);
  if (falg && falg === "enum") {
    const res = width >= 60 ? width + 50 : 150;
    return res > 9000 ? "9000px" : res + "px";
  }
  // 22 为 input 的 padding 距离；
  const inputRes = width >= 60 ? width + 22 : 82;
  return inputRes > 9000 ? "9000px" : inputRes + "px";
}

export function getDayjsDate(value, format, type) {
  const flag = dayjs(value).isValid();
  if (value && flag) {
    if (format) {
      return dayjs(value).format(type);
    } else {
      return dayjs(value);
    }
  } else {
    return null;
  }
}

export function getRefValueType(valueType, enumDictName) {
  let refType = "String";
  // const types = ['Short', 'Integer', 'Long', 'Float', 'Double', 'BigDecimal', 'String', 'Character', 'Boolean', 'Date', 'Enum'];
  if (valueType === "String") {
    refType = "String";
  } else if (["Short", "Integer", "Float", "Double", "BigDecimal", "Long"].includes(valueType)) {
    refType = "Number";
  } else if (valueType === "Date") {
    refType = "Date";
  } else if (valueType === "Enum") {
    refType = "Enum";
  } else if (valueType === "List<String>") {
    if (enumDictName) {
      refType = "EnumMultiple";
    } else {
      refType = "String";
    }
  } else {
    // 在actionSet 中，左值发生变化，可能产生 XXX.XXX.XXX 的情况，这种情况默认给个String；
    refType = "String";
  }
  return refType;
}
