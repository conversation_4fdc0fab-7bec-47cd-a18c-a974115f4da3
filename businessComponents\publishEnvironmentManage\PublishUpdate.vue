<!-- 规则修改 -->

<template>
    <div id="publish_update">
        <a-form :model="form" :rules="rules" ref="formRef" label-align="right" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="环境名称" name="environmentName">
                <a-input autocomplete="off" v-model:value="form.environmentName" placeholder="规则名称" />
            </a-form-item>
            <a-form-item label="数据源名称" name="jndiName">
                <a-input autocomplete="off" v-model:value="form.jndiName" placeholder="数据源名称"></a-input>
            </a-form-item>
            <a-form-item label="业务条线" name="businessLine">
                <a-select v-model:value="form.businessLine" placeholder="请选择" :filterOption="filterOption" showSearch>
                    <a-select-option v-for="item in menusList" :key="item.code" :value="item.code" :name="item.name">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="环境类型" name="environmentType">
                <a-select v-model:value="form.environmentType" placeholder="请选择">
                    <a-select-option :key="0" value="0">测试环境</a-select-option>
                    <a-select-option :key="1" value="1">生产环境</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述" name="desc">
                <a-textarea
                        v-model:value="form.desc"
                        :auto-size="{ minRows: 2, maxRows: 6 }"
                        :show-word-limit="true"
                        :maxlength="60" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {postLine} from '@/api/post_permissions'
import {pubadd} from '@/api/pub_environment'

const menusList = ref([]);//业务条线
const message = inject('message')
// 业务条线
const pubLine = () => {
    postLine().then((res)=>{
        //console.log(res)
        menusList.value =  res.data
    })
}
const props = defineProps({data:Object})

const form = ref({
    environmentName:"",
    database:"",
    jndiName:"",
    isSql:"",
    businessLine:"",  //业务条线
    environmentType:"",
    isUse:"",
    desc:""
});

const rules = {
    environmentName: [
        { required: true, message: '环境名称不能为空', trigger: 'change' }
    ],
    database: [
        { required: true, message: '数据库不能为空', trigger: 'change' }
    ],
    jndiName: [
        { required: true, message: '数据源名称不能为空', trigger: 'change' }
    ],
    isSql: [
        { required: true, message: '请选择', trigger: 'change' }
    ],
    businessLine: [
        { required: true, message: '请选择业务条线', trigger: 'change' }
    ],
    environmentType: [
        { required: true, message: '请选择环境类型', trigger: 'change' }
    ],
    isUse: [
        { required: true, message: '请选择', trigger: 'change' }
    ],
}



const formRef = ref(null);

const submitFun = (callback) => {
    formRef.value.validate()
        .then(() => {
            let data = {
                "businessLine":form.value.businessLine, //业务条线code
                // "dbDialect": form.value.database,  //数据库
                "desc": form.value.desc,  //描述
                "environmentName": form.value.environmentName,   //环境名称
                "environmentType": form.value.environmentType,   //环境类型
                "id": form.value.id,
                "isDefault":"",
                // "isSql": form.value.isSql,   //是否执行建表语句
                "isUse": form.value.isUse,  //是否启用
                "jndiName": form.value.jndiName,  //数据源名称
            }
            pubadd(data).then((res)=>{
                if (res.code === 20000) {
                    message.success('修改成功！');
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    message.error(res.data);
                }

            })
        })
        .catch((error) => {
            console.log(error);
        });
};

onMounted(() => {
    form.value = props.data;
    pubLine();
});

defineExpose({
    submitFun,
});
watch(
    () => props.data,
    (newData) => {
        if (newData) {
            form.value = newData;
        }
    },
    {
        immediate: true,
    }
);
</script>

