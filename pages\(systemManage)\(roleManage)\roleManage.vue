<!-- 角色管理页 -->

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { getAvailableMenus, getMenuIdsById, setEngAction, list, roleDel } from '@/api/role';
import { getAllBusinessByLoginUser } from '@/api/rule_base';
import qs from 'qs';
import type { TreeProps } from 'ant-design-vue';
import RoleAdd from "@/businessComponents/roleManage/RoleAdd";
import RoleUpdate from "@/businessComponents/roleManage/RoleUpdate";
import { ref, reactive, computed, onMounted } from "vue";
import { RULE_PERMISSION } from '@/consts/rulePermissionConsts';
import { checkPermi } from "@/directive/permission/permission";
import MoreActionMenu from "@/components/MoreActionMenu.vue";
import FlexDrawer from "@/components/FlexDrawer";
import roleDistributionButton from '@/businessComponents/roleManage/roleDistributionButton.vue';
import roleRuleBase from '@/businessComponents/roleManage/roleRuleBase.vue';

definePageMeta({
    title: '角色管理'
})

const message = inject('message')
const modal = inject('modal')
const router = useRouter();
const businessLineOptions = ref([]);
const tableDate = ref([]);
const menu_role = ref(false);
const menuParentId = ref([]);
const strall = ref([]);
const rowid = ref('');
const tabdata = ref([]);
const expandAll = ref(true);

// 抽屉相关状态
const buttonDrawerVisible = ref(false);
const ruleBaseDrawerVisible = ref(false);
const currentRoleId = ref({});
const currentRole = ref();

// 分配按钮组件引用
const roleDistributionButtonRef = ref();

// 分配规则库组件引用
const roleRuleBaseRef = ref();

// 处理按钮抽屉关闭
const handleButtonDrawerClose = () => {
    buttonDrawerVisible.value = false;
    currentRoleId.value = '';
};

// 处理规则库抽屉关闭
const handleRuleBaseDrawerClose = () => {
    ruleBaseDrawerVisible.value = false;
    currentRole.value = {};
};

// 处理按钮提交
const handleButtonSubmit = () => {
    if (roleDistributionButtonRef.value) {
        roleDistributionButtonRef.value.submitBtn();
    }
};

// 处理按钮重置
const handleButtonReset = () => {
    if (roleDistributionButtonRef.value) {
        roleDistributionButtonRef.value.resetForm();
    }
};

// 处理规则库提交
const handleRuleBaseSubmit = () => {
    if (roleRuleBaseRef.value) {
        roleRuleBaseRef.value.submit();
    }
};

// 处理规则库重置
const handleRuleBaseReset = () => {
    if (roleRuleBaseRef.value) {
        roleRuleBaseRef.value.checkReset();
    }
};

// 定义表格列
const tableColumns = [
    {
        title: "名称",
        dataIndex: "name",
        key: "name",
        width: 300,
        align: "left",
        ellipsis: true,
    },
    {
        title: "描述",
        dataIndex: "description",
        key: "description",
        width: 300,
        align: "left",
        ellipsis: true,
    },
    {
        title: "角色等级",
        dataIndex: "lever",
        key: "lever",
        align: "center",
        ellipsis: true,
    }
];

onMounted(() => {
    getAllBusinessByLoginUser().then((res) => {
        businessLineOptions.value = res.data;
        // 更新搜索配置中的业务条线选项
        searchConfig.advancedSearchFields.find(field => field.field === 'businessLineId').compConfig.options =
            businessLineOptions.value.map(item => ({
                name: item.name,
                value: item.uuid
            }));
    });
});

const loading = ref(false);
const getroleInfo = (params: Record<string, any> = {}) => {
    loading.value = true;
    return list({
        businessLineId: params.businessLineId,
        name: params.name,
        page: params.page || 1,
        several: params.pageSize || 10,
    }).then((res) => {
        loading.value = false;
        tableDate.value = res.data.data;
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    });
};

const delrole = (del_id: any) => {
    modal.confirm({
        title: '温馨提示',
        content: '确定要删除所选角色吗？',
        okText: '确定',
        cancelText: '取消',
        type: 'warning',
        onOk() {
            roleDel(qs.stringify({ id: del_id.id })).then((res) => {
                message.success("删除成功");
                if (listLayout.value) {
                    listLayout.value.refresh();
                }
            })
        },
        onCancel() {
        },
    });
};

const checkedKeys = ref<number[]>([]);
const menucli = async (oid: any) => {
    strall.value = [];
    rowid.value = oid.id;
    try {
        let res = await getAvailableMenus();
        tabdata.value = res.data;
        menuParentId.value = [];
        tabdata.value.forEach((item) => {
            if (item.menuLevel === 0) {
                menuParentId.value.push(item.id);
            }
            item.children.forEach((childItem: any) => {
                if (childItem.menuLevel === 1) {
                    menuParentId.value.push(childItem.id);
                }
            });
        });

        let res1 = await getMenuIdsById({ id: rowid.value });
        if (res1.data) {
            if (res1.data.length) {
                let _strall = res1.data.split(',');
                let _resultStrall = [..._strall];
                let test = [];
                let k = 0;
                _strall.forEach((item, i) => {
                    test = menuParentId.value.filter((item2) => {
                        if (item.toString() === item2.toString()) {
                            _resultStrall.splice(i - k, 1);
                            k++;
                        }
                        return true;
                    });
                });
                strall.value = _resultStrall.filter((item) => item !== 'del');
            }
            checkedKeys.value = strall.value.map(Number);
        }
        menu_role.value = true;
    } catch (error) {
        console.error("Error loading menu data:", error);
        message.error("加载菜单数据失败，请重试");
    }
};

const currentChecked = (checkedKey: any) => {
    checkedKeys.value = checkedKey;
};

const dyhandleClose = () => {
    menu_role.value = false;
    checkedKeys.value = [];
    strall.value = [];
    rowid.value = '';
    tabdata.value = [];
};

const getMenusub = () => {
    const checkedKeysData = checkedKeys.value
    const tabData = tabdata.value
    let halfCheckedArr = checkedKeysData.length > 0 ? [1] : [];
    function formatData(data1) {
        data1.forEach(res => {
            if (res.children) {
                let checked = res.children.find(item => checkedKeysData.indexOf(item.id) !== -1)
                if (checked && halfCheckedArr.indexOf(res.id) === -1) {
                    halfCheckedArr.push(res.id)
                }
                formatData(res.children)
            }
        })
    }
    formatData(tabData);
    const str = checkedKeysData.concat(halfCheckedArr).toString();
    setEngAction(qs.stringify({ id: rowid.value, menuIds: str })).then((res) => {
        if (res.code === 20000) {
            message.success("分配菜单成功")
            menu_role.value = false;
        }
    });
};

const menu_btn = (mid: any) => {
    currentRoleId.value = mid.id;
    buttonDrawerVisible.value = true;
};

const base_btn = (record: any) => {
    currentRole.value = record;
    ruleBaseDrawerVisible.value = true;
};

const modalType = ref('');
const id = ref({});
const isModalVisible = ref(false);

const showModal = (type, record = {}) => {
    modalType.value = type;
    if (type === 'update') {
        id.value = record.id;
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    isModalVisible.value = false;
};

const roleAddComponent = ref()
const roleUpdateComponent = ref()

function handleModalOk() {
    let submitFun
    switch (modalType.value) {
        case 'add':
            submitFun = roleAddComponent.value.submitForm;
            break;
        case 'update':
            submitFun = roleUpdateComponent.value.submitForm;
            break;
    }
    submitFun && submitFun(() => {
        if (listLayout.value) {
            listLayout.value.refresh();
        }
        isModalVisible.value = false;
    });
}

const fieldNames: TreeProps['fieldNames'] = {
    children: 'children',
    title: 'name',
    key: 'id'
};

// 搜索配置
const searchConfig = reactive({
    // 简单搜索字段
    simpleSearchField: {
        label: '角色名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '业务条线',
            field: 'businessLineId',
            compType: 'select',
            compConfig: {
                options: businessLineOptions.value.map(item => ({
                    name: item.name,
                    value: item.uuid
                }))
            }
        },
        {
            label: '角色名称',
            field: 'name',
            compType: 'input'
        }
    ]
});

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: (formValue) => {
        if (listLayout.value) {
            listLayout.value.refresh();
        }
    },
    // 添加事件
    addNewEvent: () => showModal('add'),
    // 新增按钮权限
    addPermission: RULE_PERMISSION.SYS_ROLE.ADD
};

// 获取操作菜单项
const getActionMenuItems = (record) => {
    const items = [];

    // 修改操作
    if (checkPermi([RULE_PERMISSION.SYS_ROLE.UPDATE])) {
        items.push({
            key: 'update',
            label: '修改',
            onClick: () => showModal('update', record)
        });
    }

    // 分配菜单操作
    if (checkPermi([RULE_PERMISSION.SYS_ROLE.ASSIGN])) {
        items.push({
            key: 'menu',
            label: '分配菜单',
            onClick: () => menucli(record)
        });
    }

    // 分配按钮操作
    if (checkPermi([RULE_PERMISSION.SYS_ROLE.BUTTON])) {
        items.push({
            key: 'button',
            label: '分配按钮',
            onClick: () => menu_btn(record)
        });
    }

    // 分配规则库操作
    if (checkPermi([RULE_PERMISSION.SYS_ROLE.RULE_BASE])) {
        items.push({
            key: 'ruleBase',
            label: '分配规则库',
            onClick: () => base_btn(record)
        });
    }

    // 删除操作
    if (checkPermi([RULE_PERMISSION.SYS_ROLE.DELETE])) {
        items.push({
            key: 'delete',
            label: '删除',
            onClick: () => delrole(record)
        });
    }

    return items;
};

// 处理规则库全选
const handleRuleBaseAllChecked = () => {
    if (roleRuleBaseRef.value) {
        roleRuleBaseRef.value.allChecked();
    }
};

const listLayout = ref();
</script>

<template>
    <ListPage
        ref="listLayout"
        title="角色管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="true"
        :tableColumns="tableColumns"
        :queryMethod="getroleInfo"
        rowKey="id"
        :actionMenuGetter="getActionMenuItems"
    >
    </ListPage>

    <!-- 权限设置对话框 -->
    <a-modal title="权限设置" v-model:visible="menu_role" @cancel="dyhandleClose">
        <span>当前设置角色:</span>
        <a-tree v-if="tabdata && tabdata.length" ref="tree" :tree-data="tabdata" checkable
            v-model:checkedKeys="checkedKeys" :height="400" @check="currentChecked" :key="expandAll"
            :defaultExpandAll="expandAll" :field-names="fieldNames" />
        <template #footer>
            <a-button type="primary" size="small" @click="expandAll = true" style="margin-right: 8px">
                展开
            </a-button>
            <a-button type="primary" size="small" @click="expandAll = false" style="margin-right: 8px">
                收起
            </a-button>
            <a-button type="primary" size="small" @click="getMenusub" style="margin-right: 8px">提交</a-button>
            <a-button type="primary" size="small" @click="menu_role = false">取消</a-button>
        </template>
    </a-modal>

    <!-- 新增和更新对话框 -->
    <a-modal v-if="isModalVisible" :visible="isModalVisible" :title="modalType === 'add' ? '新增角色' : '修改角色'"
        @ok="handleModalOk" @cancel="handleCancel" okText="保存">
        <div style="max-height: 60vh; overflow-y: auto;">
            <RoleAdd ref="roleAddComponent" v-if="modalType === 'add'" />
            <RoleUpdate ref="roleUpdateComponent" :key="modalType + '-update-' + id" v-if="modalType === 'update'"
                :id="id" />
        </div>
    </a-modal>

    <!-- 分配按钮抽屉 -->
    <FlexDrawer v-model:visible="buttonDrawerVisible" title="分配按钮" @close="handleButtonDrawerClose"
        v-if="buttonDrawerVisible">
        <roleDistributionButton ref="roleDistributionButtonRef" :roleId="currentRoleId"
            @close="handleButtonDrawerClose" />
        <template #footer>
            <div style="text-align: right">
                <a-button type="primary" @click="handleButtonSubmit" style="margin-right: 8px;">提交</a-button>
            </div>
        </template>
    </FlexDrawer>

    <!-- 分配规则库抽屉 -->
    <FlexDrawer v-model:visible="ruleBaseDrawerVisible" title="分配规则库" @close="handleRuleBaseDrawerClose"
        v-if="ruleBaseDrawerVisible">
        <roleRuleBase ref="roleRuleBaseRef" :role="currentRole" @close="handleRuleBaseDrawerClose" />
        <template #footer>
            <div style="text-align: right">
                <a-button style="margin-right: 8px" @click="handleRuleBaseAllChecked">全选</a-button>
                <a-button style="margin-right: 8px" @click="handleRuleBaseReset">重置</a-button>
                <a-button type="primary" style="margin-right: 8px" @click="handleRuleBaseSubmit">提交</a-button>
            </div>
        </template>
    </FlexDrawer>
</template>
