<template>
  <div class="span-con-else" style="display: inline-flex">
    <a-input
      v-model:value="innerValue"
      :disabled="disabled"
      :title="titleStr"
      @blur="(e) => handleChange(e.target.value)"
      v-if="(delElseFlag && !readonly) || (delRead && !elseFlag) || (!elseFlag && !readonly)"
    />
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <TableConditionOperationBox
      class="table-opera-box"
      :index="index"
      :col="col"
      :rowData="rowData"
      :colData="colData"
      :record="record"
      :elseFlag="elseFlag"
      :delElseFlag="delElseFlag"
      :readonly="readonly"
      v-if="col && col[4] !== '2' && !readonly"
      @operaChange="operaChange"
    />
  </div>
</template>

<script>
import TableConditionOperationBox from "../operationMenu/tableConditionOperationBox";

export default {
  name: "TableStrInput",
  components: {
    TableConditionOperationBox,
  },
  props: {
    isTable: {
      type: Boolean,
      default: false,
    },
    value: String,
    disabled: Boolean,
    titleStr: String,
    index: Number,
    col: String,
    elseFlag: {
      type: Boolean,
      default: false,
    },
    delElseFlag: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    delRead: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Array,
      default: () => [],
    },
    colData: {
      type: Array,
      default: () => [],
    },
    record: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      innerValue: this.value,
    };
  },
  methods: {
    operaChange(action) {
      if (this.comElse && action === "otherwise") {
        return;
      } else if (action === "otherwise") {
        this.$emit("onChange", {value: "否则", elseFlag: true});
      } else {
        this.$emit("onChange", "");
      }
      let dataIndex = this.rowData.findIndex(
        (item) => item.key === this.record.key
      );
      this.$emit("operaChange", {
        action,
        index: dataIndex,
        col: this.col,
      });
    },
    handleChange(value = null) {
      this.$emit("onChange", value);
    },
    onInputKeyDown(e) {
      e && e.stopPropagation && e.stopPropagation();
    },
  }
};
</script>
