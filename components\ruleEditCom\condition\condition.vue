<template>
  <div v-if="dataSource" :style="conStyle" :class="`condition common-highlight ${classFlag}`">
    <VariableCom
      :key="pos + '_va'"
      ref="variable_com"
      :pos="pos"
      :dataSource="variable"
      :signValue="''"
      :locked="locked"
      :isTrack="isTrack"
      :isLastOne="true"
      :preIndex="preIndex"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
      @onChange="onVariableComChange"
      @onCalSignChange="changeCalculateSign"
    />
    <RuleOperator
      v-if="comparator"
      :key="pos + '_op'"
      :pos="pos"
      :operatorData="operatorTypeList"
      :type="conditionValueType"
      :dataSource="comparator"
      :locked="locked"
      :isTrack="isTrack"
      :preIndex="preIndex"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      @onChange="onComparatorChange"
    />
    <span
      v-if="!fold && !locked && !isTrack"
      class="iconContainer iconfontbtn icon-add"
      :style="{ lineHeight: '16px', fontSize: '14px', marginLeft: '60px' }"
      @click="() => $emit('addRule', pos, conditionId, layer)"
    ></span>
    <ConditionOperationBox
      v-if="!fold && !locked && !isTrack"
      :showAddUp="showAddUp"
      @onChange="conditionHandler"
      :layer="layer"
    />
    <a-tooltip v-if="validate === true" :title="msg">
      <CheckCircleOutlined class="icon-validate" :style="{ color: 'green' }" />
    </a-tooltip>
    <a-tooltip v-if="validate === false" :title="msg">
      <CloseCircleOutlined class="icon-validate" :style="{ color: 'red' }" />
    </a-tooltip>
  </div>
</template>

<script setup>
import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import { cloneDeep } from "lodash";
// 定义组件的 props
const props = defineProps({
  dataSource: {
    type: Object,
    default: () => ({}),
  },
  indentLength: {
    type: Number,
    default: 0,
  },
  fold: {
    type: Boolean,
    default: false,
  },
  pos: {
    type: String
  },
  style: {
    type: Object,
    default: () => ({}),
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  conditionValid: {
    type: Object,
    default: () => ({}),
  },
  predefineLine: {
    type: String,
    default: "",
  },
  predefineCon: {
    type: String,
    default: "",
  },
  preIndex: {
    type: Number,
    default: 0,
  },
  showAddUp: {
    type: Boolean,
    default: true,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits([
  "conditionChange",
  "decreaseRule",
  "addRule",
  "addUp",
  "addChildCondition",
  "addTailItem",
  "replaceItem",
]);

// 注入 ruleUuid
const ruleUuid = inject("ruleUuid", "");

// 定义响应式数据
const operatorTypeList = ref({});
const oldVari = ref(null);
const variable = ref(null);
const comparator = ref(null);
const layer = ref(null);
const validate = ref(null);
const msg = ref("");
const conditionValueType = ref("");
const conditionId = ref("");
const conStyle = ref("");
const title = ref("");
const classFlag = ref("");

// 定义 aOldValue 和 aNewValue
let aOldValue = null;
let aNewValue = null;

// 初始化方法
const init = (init = true) => {
  const { dataSource, conditionValid = {}, style } = props;
  const { ruleCondition } = dataSource || {};
  const { contents, conditionId: condId, layer: lay } = ruleCondition || {};
  const {
    variable: varData,
    conditionValueType: condValueType,
    comparator: comp,
  } = contents || {};
  const { validate: val, msg: msgText } = conditionValid;
  if (oldVari.value === null) {
    oldVari.value = cloneDeep(varData);
  }
  init &&
    (operatorTypeList.value =
      store.getters.listMap[ruleUuid].initModelData.operatorTypeList);
  variable.value = varData; // 初始化 variable
  comparator.value = comp; // 初始化 comparator
  layer.value = lay; // 初始化 layer
  validate.value = val; // 初始化 validate
  msg.value = msgText; // 初始化 validate 的提示信息
  conditionValueType.value = condValueType;
  conditionId.value = condId;
  conStyle.value = style;
};

// 监听 dataSource 的变化
watch(
  () => props,
  (newVal) => {
    if (newVal) {
      init(false);
    }
  },
  { deep: true, immediate: true }
);

// 计算路径的方法
const caculatePath = (indexPath = "") => {
  const arr = indexPath.split("|").reverse();
  let num = 0;
  for (let i = 0, len = arr.length; i < len; i++) {
    if (arr[i] / 1 === 0) {
      num++;
    } else {
      break;
    }
  }
  return {
    num,
    arrIndex: arr[0],
  };
};

// 变量组件变化时触发的方法
const onVariableComChange = (
  pos,
  newVariableData,
  conditionValueType,
  operatorOptions
) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator } = contents;
  const isBool = conditionValueType === "Boolean";
  const { dictName } = newVariableData;
  const domain = util.getRealDomain(newVariableData);
  if (isBool) {
    if (newVariableData.next) {
      newVariableData.next.domain = "boolean";
    }
  }
  const newContents = {
    variable: newVariableData,
    conditionValueType: conditionValueType,
  };
  let comparatorData = null;
  if (isBool) {
    comparatorData = {
      operatorName: "",
      enumDictName: "boolean",
    };
  } else if (dictName || domain) {
    comparatorData = {
      operatorName: "",
      enumDictName: dictName || domain || null,
    };
  } else {
    comparatorData = {
      operatorName: "",
    };
  }
  aOldValue = null;
  aNewValue = null;
  if (oldVari.value) {
    getLastOV(oldVari.value);
    getLastNV(newVariableData);
    const comparaRes = getComparaRes(aOldValue, aNewValue);
    if (comparator && comparator.value && comparaRes) {
      newContents.comparator = comparator;
    } else {
      oldVari.value = cloneDeep(newVariableData);
      newContents.comparator = comparatorData;
    }
  }
  emit("conditionChange", pos, newContents);
  if(operatorOptions&&operatorOptions.length>0){
    customOperator(pos,operatorOptions)
  }
};

const customOperator = (pos,operatorOptions) => {
  const { label, paramTypes, paramQuantity,value } = operatorOptions[0];
  const num = label ? label.split("(").length - 1 : 0;
  const paramTypeArr = paramTypes ? paramTypes.split(",") : ["object"];
  const { enumDictName } = comparator.value;
  const newOperatorData = {
    enumDictName: enumDictName || null,
    value: [value],
    operatorParams: [],
    paramQuantity,
  };

  for (let i = 0; i < num; i++) {
    const newVairable = {
      variableType: "constant",
      enumDictName: enumDictName || null,
      value: "",
    };
    newVairable.valueType = paramTypeArr[i];
    newOperatorData.operatorParams.push(newVairable);
  }
  const _newOperatorData = comparator.value._derive(newOperatorData);
  onComparatorChange(pos,_newOperatorData)
}

// 获取比较结果的方法
const getComparaRes = (oVal, nVal) => {
  let bRes = false;
  const aO = oVal;
  const aN = nVal;
  const aR = [];
  if (aO && aN) {
    if (aO.length !== aN.length) {
      bRes = false;
    } else {
      for (let i = 0; i < aO.length; i++) {
        if (aO[i] === aN[i]) {
          aR.push(true);
        } else {
          aR.push(false);
        }
      }
      if (aR.includes(false)) {
        bRes = false;
      } else {
        bRes = true;
      }
    }
  }
  return bRes;
};

// 比较器组件变化时触发的方法
const onComparatorChange = (pos, newOperatorData) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const newContents = {
    variable: contents.variable,
    conditionValueType: contents.conditionValueType,
    comparator: newOperatorData,
  };
  emit("conditionChange", pos, newContents);
};

// 处理条件操作的方法
const conditionHandler = (type) => {
  const { pos, dataSource } = props;
  const { ruleCondition } = dataSource;
  const { conditionId, layer } = ruleCondition;
  switch (type) {
    case "delete":
      emit("decreaseRule", { pos, conditionId, layer });
      break;
    case "addUp":
      emit("addUp", pos, conditionId, layer);
      break;
    case "addChildren":
      emit("addChildCondition", pos, conditionId, layer);
      break;
    case "addTailItem":
      emit("addTailItem", pos, conditionId);
      break;
    case "replaceItem":
      emit("replaceItem", pos, conditionId, layer);
      break;
  }
};

// 改变计算符号的方法
const changeCalculateSign = (pos, sign, newParamItem) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator, conditionValueType } = contents;
  const targetOption = variable;
  const valueType = targetOption._getRealValueType();
  if (sign === "delete" || !newParamItem) {
    return;
  }
  const newVariable = {
    valueType: valueType,
    variableType: "expression",
    expressionTreeData: {
      type: "expression",
      symbols: [sign],
      params: [
        {
          type: "variable",
          data: {
            ...targetOption,
          },
        },
        newParamItem,
      ],
    },
  };
  const newComparator = comparator || { operatorName: "" };
  const _newVariable = targetOption._derive(newVariable, {
    owner: targetOption._getOwner(),
    isRootVar: true,
  });
  const _newComparator = targetOption._derive(newComparator, {
    owner: targetOption._getOwner(),
  });
  const newContents = {
    variable: _newVariable,
    conditionValueType: conditionValueType,
    comparator: _newComparator,
  };
  emit("conditionChange", pos, newContents);
};

// 获取最后一个旧值的方法
const getLastOV = (data) => {
  if (data.next) {
    getLastOV(data.next);
  } else {
    aOldValue = data.value;
  }
};

// 获取最后一个新值的方法
const getLastNV = (data) => {
  if (data.next) {
    getLastNV(data.next);
  } else {
    aNewValue = data.value;
  }
};

// 初始化组件
onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}
</style>
