<!--
# ListPage 列表页面布局组件

## 组件说明
ListPage 是一个通用的列表页面布局组件，提供了标准的列表页面结构，包括搜索、筛选、表格展示和分页等功能。

## Props 说明

### 必需属性
- `title`: String - 页面标题
- `searchConfig`: Object - 搜索配置
  - `simpleSearchField`: Object - 简单搜索字段配置
  - `advancedSearchFields`: Array - 高级搜索字段配置
- `eventConfig`: Object - 事件配置
  - `searchEvent`: Function - 搜索事件处理函数
  - `addNewEvent`: Function - 新建事件处理函数
- `queryMethod`: Function - 数据查询方法
- `tableColumns`: Array - 表格列配置

### 可选属性
- `showAddButton`: Boolean - 是否显示新建按钮，默认 true
- `showSidebarButton`: Boolean - 是否显示侧边栏按钮，默认 false
- `batchActions`: Array - 批量操作配置
- `moreEvent`: Array - 更多创建操作配置
- `customRenderColumns`: Array - 自定义渲染列
- `actionMenuGetter`: Function - 操作菜单获取函数
- `exportConfig`: Object - 导出配置
- `drawerConfig`: Object - 抽屉配置
- `columnConfig`: Object - 表格列配置扩展
- `showActionColumn`: Boolean - 是否显示操作列，默认 true

## 事件
- `@change`: 分页变化时触发
- `@search`: 搜索时触发

## 插槽
- 默认插槽：自定义列内容渲染
- `action`: 操作列自定义渲染

## 方法
- `resetForm()`: 重置表单
- `clearAllFilters()`: 清除所有筛选条件
- `query(params)`: 执行查询
- `refresh()`: 刷新当前数据
- `getTableData()`: 获取表格数据
- `getPagination()`: 获取分页信息

## 注意事项
1. 组件会自动处理分页逻辑
2. 搜索条件变更时会自动重置到第一页
3. 支持简单搜索和高级搜索两种模式
4. 可以通过 actionMenuGetter 配置统一的操作菜单
-->
<template>
  <div class="list-page-layout">
    <!-- 上部栏：标题、筛选、批量操作和新建按钮 -->
    <div class="top-bar">
      <!-- 统一的标题区域 -->
      <div class="title-area">
        <!-- 默认标题显示 -->
        <h2 class="page-title" v-if="!hasActiveFilters">
          {{ showSidebarButton ? ruleSidebarRef?.selectedNodeName : title }}
        </h2>

        <!-- 搜索筛选条件标签 -->
        <div class="filter-tags" v-else>
          <div class="filter-tag-group">
            <template v-for="(value, field) in getActiveFilters()" :key="field">
              <a-tag class="filter-tag" closable @close="clearFilter(field)">
                {{ getFieldLabel(field) }} 包含 "{{ formatDisplayValue(field, value) }}"
              </a-tag>
            </template>
            <a class="clear-all-link" @click="clearAllFilters">清空</a>
          </div>
        </div>
      </div>

      <div class="search-area">
        <!-- 简单搜索 -->
        <div class="simple-search">
          <a-input
                  v-model:value="simpleSearchValue"
                  :placeholder="`输入${searchConfig.simpleSearchField.label}，回车搜索`"
                  @search="handleSimpleSearch"
                  @change="handleSimpleSearchChange"
                  @keydown.enter="handleSimpleSearch"
                  style="width: 220px"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>

          <!-- 高级筛选按钮 -->
          <a-popover
                  v-model:open="advancedSearchVisible"
                  trigger="click"
                  placement="bottomRight"
                  :overlay-style="{ width: '400px' }"
          >
            <template #content>
              <div class="advanced-search-panel">
                <a-form layout="horizontal" :model="formValue" :label-col="{ style: { width: '100px', textAlign: 'right' } }" @keypress.enter.prevent="handleAdvancedSearch">
                  <a-form-item
                          v-for="item in searchConfig.advancedSearchFields"
                          :key="item.field"
                          :label="`${item.label}：`"
                  >
                    <!-- 输入框 -->
                    <a-input
                            v-if="item.compType === 'input'"
                            v-model:value="formValue[item.field]"
                            :placeholder="`请输入${item.label}`"
                            @keydown.enter="handleAdvancedSearch"
                    />

                    <!-- 下拉选择框 -->
                    <a-select
                            v-else-if="item.compType === 'select'"
                            v-model:value="formValue[item.field]"
                            :placeholder="`请选择${item.label}`"
                            style="width: 100%"
                            :options="item.compConfig?.options?.map(option => ({
                        label: option.name,
                        value: option.value
                      })) || []"
                            :default-value="item.compConfig?.defaultValue"
                            :show-search="true"
                            :filter-option="(input: string, option: any) => {
                        return option?.label?.toString().toLowerCase().includes(input.toLowerCase())
                      }"
                            @change="(value) => item.compConfig?.onChange && item.compConfig.onChange(value)"
                            @keydown.enter="handleAdvancedSearch"
                    />

                    <!-- 日期选择器 -->
                    <a-date-picker
                            v-else-if="item.compType === 'datePicker'"
                            v-model:value="formValue[item.field]"
                            :placeholder="`请选择${item.label}`"
                            style="width: 100%"
                            valueFormat="YYYY-MM-DD 00:00:00"
                            :format="item.compConfig?.format || 'YYYY-MM-DD'"
                            :show-time="item.compConfig?.showTime"
                            @keydown.enter="handleAdvancedSearch"
                    />
                  </a-form-item>

                  <div class="form-actions" style="text-align: right">
                    <a-button @click="resetForm" style="margin-right: 8px">重置</a-button>
                    <a-button type="primary" @click="handleAdvancedSearch">筛选</a-button>
                  </div>
                </a-form>
              </div>
            </template>
            <a-button class="filter-button">
              <template #icon><filter-outlined :style="{ color: isFiltered ? '#1890ff' : undefined }" /></template>
              高级筛选
            </a-button>
          </a-popover>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-area">
          <!-- 批量操作下拉按钮 -->
          <a-dropdown :trigger="['click']" v-if="batchActions && batchActions.length > 0" :dropdown-style="{ minWidth: '120px' }">
            <template #overlay>
              <a-menu style="min-width: 120px">
                <template v-for="action in batchActions">
                  <a-menu-item
                          :key="action.key"
                          @click="action.handler"
                          v-if="action.permission ? checkPermi([action.permission]) : true"
                  >
                    {{ action.label }}
                  </a-menu-item>
                </template>
              </a-menu>
            </template>
            <a-button style="margin-right: 16px; min-width: 120px;">
              批量操作 <down-outlined />
            </a-button>
          </a-dropdown>

          <!-- 新建按钮 -->
          <div v-if="showAddButton" class="create-button-group" v-hasPermi="eventConfig.addPermission ? [eventConfig.addPermission] : []">
            <!-- 主新建按钮 - 保持原有功能 -->
            <a-button type="primary" @click="handleAddNew" :class="{'main-create-button': moreEvent && moreEvent.length > 0}">
              <template #icon><plus-outlined /></template>
              新建
            </a-button>

            <!-- 下拉菜单按钮 - 仅当有额外创建操作时显示 -->
            <a-dropdown v-if="moreEvent && moreEvent.length > 0" placement="bottomRight" :dropdown-style="{ minWidth: '110px' }">
              <template #overlay>
                <a-menu style="min-width: 110px">
                  <template v-for="event in moreEvent">
                    <a-menu-item
                            :key="event.key"
                            @click="event.handler"
                            v-if="event.permission ? checkPermi([event.permission]) : true"
                    >
                      {{ event.label }}
                    </a-menu-item>
                  </template>
                </a-menu>
              </template>
              <a-button type="primary" class="dropdown-create-button">
                <down-outlined />
              </a-button>
            </a-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 下部区域：分为表格和分页两个独立区域 -->
    <div class="content-container" ref="basePoint">
      <!-- 表格区域 -->
      <div class="table-container">
        <template v-if="loading">
          <TableSkeleton
            :columns="columns"
            :limit="pagination.pageSize"
            :scrollY="scrollY"
          />
        </template>
        <a-table
          v-else
          :columns="columns"
          :dataSource="tableData"
          :loading="loading"
          :pagination="false"
          :rowKey="rowKey"
          :scroll="{ y: scrollY }"
          v-bind="tableProps"
          size="small"
          :column-config="columnConfig"
          :rowSelection="rowSelection"
        >
          <!-- 动态列内容渲染 -->
          <template #bodyCell="{ column, record, index }">
            <!-- 使用命名插槽处理自定义列 -->
            <slot
              v-if="customRenderColumns.includes(column.key)"
              :name="`${column.key}`"
              :text="record[column.dataIndex]"
              :record="record"
              :index="index"
            ></slot>
            <!-- 操作列 - 改为内置MoreActionMenu -->
            <template v-else-if="column.key === 'action'">
              <!-- 如果提供了actionMenuGetter函数，使用内置的MoreActionMenu -->
              <template v-if="actionMenuGetter">
                <MoreActionMenu :menuItems="actionMenuGetter(record)" />
              </template>
              <!-- 否则使用action插槽 -->
              <slot v-else name="action" :record="record" :index="index"></slot>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 分页区域 - 完全独立的容器 -->
      <div class="pagination-container">
        <Pagination
                :paginations="{
            page: pagination.current,
            limit: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: pagination.showSizeChanger,
            showTotal: pagination.showTotal,
            isLoading: pagination.isLoading
          }"
                @change="handlePageChange"
                :scrollY="scrollY-100"
                :loading="loading"
                :pageNum="pagination.current"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { SearchOutlined, FilterOutlined, PlusOutlined, DownOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import TableSkeleton from '@/components/TableSkeleton.vue'
//权限判断
import { checkPermi } from "@/directive/permission/permission";
// 定义搜索字段接口
interface SearchField {
    label: string;
    field: string;
    compType?: 'input' | 'select' | 'datePicker';
    defaultValue?: any;
    compConfig?: {
        options?: Array<{
            name: string;
            value: string | number;
        }>;
        format?: string;
        showTime?: boolean;
        defaultValue?: string | number;
        // 添加依赖关系配置，当该字段值变化时需要清空的其他字段
        clearFields?: string[];
        onChange?: (value: any) => void;
        [key: string]: any;
    };
}

// 定义搜索配置接口
interface SearchConfig {
    simpleSearchField: SearchField;
    advancedSearchFields: SearchField[];
}

// 定义批量操作接口
interface BatchAction {
    key: string;
    label: string;
    handler: () => void;
    permission?: string;
}

// 定义创建操作接口
interface CreateAction {
    key: string;
    label: string;
    handler: () => void;
    permission?: string;
}

// 定义事件配置接口
interface EventConfig {
    searchEvent: (formValue: Record<string, any>) => void;
    addNewEvent: () => void;
    // 新增：用于处理表单值改变时的事件
    formHandler?: {
        // 表单字段映射，将searchConfig字段映射到实际form字段
        fieldMap?: Record<string, string>;
        // 日期字段特殊处理
        dateFields?: {
            startField?: string;
            endField?: string;
            startFormat?: string;
            endFormat?: string;
        };
        // 查询方法
        queryMethod: () => void;
    };
    // 新增按钮权限
    addPermission?: string;
}

// 新增操作菜单项接口
interface ActionMenuItem {
    key: string;
    label: string;
    permission?: string;
    icon?: string;
    disabled?: boolean;
    danger?: boolean;
    onClick: (record: any) => void;
    children?: ActionMenuItem[];
}

// 在 script setup 部分添加以下接口定义
interface ColumnConfig {
  title: string;
  align?: 'left' | 'center' | 'right';
  dataIndex: string;
  key: string;
  width?: number;
  fixed?: boolean | 'left' | 'right';
  ellipsis?: boolean;
  sorter?: boolean;
}

interface ExportConfig {
  exportApi: (params: any) => Promise<any>;
  getFileName?: (headers: any) => string;
  formatExportParams?: (record: any) => any;
  onExportSuccess?: () => void;
  onExportError?: () => void;
}

interface DrawerConfig {
  title: string;
  width?: number;
  placement?: 'left' | 'right' | 'top' | 'bottom';
  afterClose?: () => void;
  closable?: boolean;
}

// 定义组件属性
const props = defineProps({
    // 页面标题
    title: {
        type: String,
        required: true
    },
    // RuleSidebar组件引用
    ruleSidebarRef: {
        type: Object,
        default: null
    },
    // 搜索配置
    searchConfig: {
        type: Object as () => SearchConfig,
        required: true,
        default: () => ({
            // 简单搜索字段
            simpleSearchField: {
                label: '名称',
                field: 'name'
            },
            // 高级搜索字段
            advancedSearchFields: []
        })
    },
    // 事件配置
    eventConfig: {
        type: Object as () => EventConfig,
        required: true,
        default: () => ({
            // 搜索事件
            searchEvent: () => {},
            // 新建事件
            addNewEvent: () => {},
            // 表单处理器
            formHandler: {
                queryMethod: () => {}
            },
            // 新建按钮权限
            addPermission: {
                type: String,
                default: ''
            },
        })
    },
    // 批量操作配置
    batchActions: {
        type: Array as () => BatchAction[],
        default: () => []
    },
    // 创建操作配置
    moreEvent: {
        type: Array as () => CreateAction[],
        default: () => []
    },

    // 是否显示新建按钮
    showAddButton: {
        type: Boolean,
        default: true
    },
    // 是否显示侧边栏按钮
    showSidebarButton: {
        type: Boolean,
        default: false
    },
    // 是否显示操作列
    showActionColumn: {
        type: Boolean,
        default: true
    },

    // 表格相关属性
    tableColumns: {
        type: Array,
        default: () => []
    },
    queryMethod: {
        type: Function,
        required: true
    },
    customRenderColumns: {
        type: Array as PropType<string[]>,
        default: () => []
    },
    rowKey: {
        type: [String, Function] as PropType<string | ((record: any) => string)>,
        default: 'id'
    },
    tableProps: {
        type: Object,
        default: () => ({})
    },
    // 新增: 行选择配置
    rowSelection: {
        type: Object,
        default: null
    },

    // 新增: 操作菜单获取函数
    actionMenuGetter: {
        type: Function,
        default: null
    },

    // 导出配置
    exportConfig: {
        type: Object as PropType<ExportConfig>,
        default: null
    },

    // 抽屉配置
    drawerConfig: {
        type: Object as PropType<DrawerConfig>,
        default: () => ({
            width: 720,
            placement: 'right',
            closable: true
        })
    },

    // 表格列配置扩展
    columnConfig: {
        type: Object,
        default: () => ({
            showEllipsis: true,
            defaultAlign: 'left'
        })
    }
})

// 简单搜索值
const simpleSearchValue = ref('')

// 高级搜索面板可见性
const advancedSearchVisible = ref(false)

// 添加一个新的状态变量，用于跟踪是否已执行搜索操作
const hasSearchedOrFiltered = ref(false)

// 表单值
const formValue = reactive<Record<string, any>>({})

// 添加一个新的状态，用于跟踪已提交的筛选条件
const submittedFilters = reactive<Record<string, any>>({})

// 表格相关状态
const loading = ref(false);
const tableData = ref([]);
const lastQueryParams = ref({});

// 分页状态
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    isLoading: false,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条数据`
});

// 添加列显示/隐藏相关逻辑
const handleColumnChange = (visibleColumns: any[]) => {
    columns.value = tableColumnsObj.updateVisibleColumns(visibleColumns);
};

const tableColumnsObj = useTableConfig(
    props.tableColumns,
    handleColumnChange,
    props.showActionColumn       // 使用 props.showActionColumn 控制操作列显示
);

// 直接使用tableColumnsObj的列配置
const columns = ref(tableColumnsObj.columns);

// 初始化表单值
const initFormValue = () => {
    if (props.searchConfig.advancedSearchFields) {
        props.searchConfig.advancedSearchFields.forEach((field: SearchField) => {
            // 设置默认值（如果有）
            if (field.compType === 'select' && field.compConfig?.defaultValue !== undefined) {
                formValue[field.field] = field.compConfig.defaultValue;
            } else {
                formValue[field.field] = undefined;
            }
        })
    }
    // 初始化简单搜索字段
    formValue[props.searchConfig.simpleSearchField.field] = ''

    // 清空已提交筛选条件
    Object.keys(submittedFilters).forEach(key => {
        delete submittedFilters[key];
    });
}

// 手动更新序号列的计算逻辑
// 在表格组件挂载后对序号列进行修改
onMounted(() => {
    // 初始化和设置依赖关系
    setupFieldDependencies();
    initFormValue();
    defaultSearchValues.value = getFormDefaultValues();
    
    // 添加序号计算的逻辑
    if (columns.value.length > 0 && columns.value[0].key === 'index') {
        // 使用as any来避免TypeScript的类型检查错误
        (columns.value[0] as any).customRender = ({ index }: { index: any }) => {
            // 使用ListPage的分页配置进行序号计算
            return (pagination.value.current - 1) * pagination.value.pageSize + index + 1;
        };
    }
});

// 添加监听字段变化的逻辑，处理字段依赖关系
const setupFieldDependencies = () => {
    // 获取所有定义了clearFields的字段
    const fieldsWithDependencies = props.searchConfig.advancedSearchFields.filter(
        field => field.compConfig?.clearFields && field.compConfig.clearFields.length > 0
    );

    // 为每个字段添加监听
    fieldsWithDependencies.forEach(field => {
        watch(() => formValue[field.field], (newValue, oldValue) => {
            // 如果值发生变化且不为空，执行清空操作
            if (newValue !== oldValue && newValue !== undefined && newValue !== null && newValue !== '') {
                // 获取需要清空的字段
                const clearFields = field.compConfig?.clearFields || [];

                // 清空每个依赖字段
                clearFields.forEach(fieldName => {
                    // 设置字段值为undefined
                    formValue[fieldName] = undefined;

                    // 如果有表单处理器，同步更新后台表单
                    if (props.eventConfig.formHandler) {
                        const formField = props.eventConfig.formHandler.fieldMap?.[fieldName] || fieldName;
                        // 从searchConfig中获取默认值
                        const defaultField = props.searchConfig.advancedSearchFields.find(f => f.field === fieldName);
                        if (defaultField?.compConfig?.defaultValue !== undefined) {
                            formValue[fieldName] = defaultField.compConfig.defaultValue;
                        }
                    }
                });
            }
        });
    });
};
//搜索表单默认值
const defaultSearchValues = ref({});

// 处理简单搜索变更
const handleSimpleSearchChange = (e: Event) => {
    const target = e.target as HTMLInputElement
    formValue[props.searchConfig.simpleSearchField.field] = target.value
}

// 处理简单搜索
const handleSimpleSearch = async () => {
    // 将简单搜索值同步到表单值
    formValue[props.searchConfig.simpleSearchField.field] = simpleSearchValue.value

    // 更新已提交的筛选条件
    updateSubmittedFilters();

    // 设置已执行搜索标志
    hasSearchedOrFiltered.value = true

    // 重要：直接重置分页到第一页
    pagination.value.current = 1;

    try {
        // 如果使用内部表格和分页，直接使用executeQuery并重置页码
        if (props.queryMethod) {
            executeQuery(formatFormValues(formValue), true);
        }
        // 如果提供了表单处理器，则使用表单处理器
        else if (props.eventConfig.formHandler) {
            await new Promise<void>((resolve) => {
                handleFormValueChange(formatFormValues(formValue), true);
                // 使用setTimeout模拟异步等待后台方法完成
                setTimeout(() => {
                    resolve();
                }, 100);
            });
        } else {
            // 否则调用原有的搜索事件
            props.eventConfig.searchEvent(formatFormValues(formValue));
            // 使用setTimeout模拟异步等待后台方法完成
            await new Promise<void>((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 100);
            });
        }
    } catch (error) {
        console.error('搜索出错:', error);
    }
}

// 处理高级搜索
const handleAdvancedSearch = () => {
    // 准备过滤条件对象
    const filters: Record<string, any> = {};

    // 处理表单字段
    props.searchConfig.advancedSearchFields.forEach(field => {
        const { field: fieldName } = field;
        const value = formValue[fieldName];

        if (value !== undefined && value !== null && value !== '') {
            filters[fieldName] = value;
            submittedFilters[fieldName] = value;
        } else {
            delete submittedFilters[fieldName];
        }
    });

    // 设置搜索状态
    if (Object.keys(submittedFilters).length > 0) {
        hasSearchedOrFiltered.value = true;
    } else {
        hasSearchedOrFiltered.value = false;
    }

    // 强制重置分页到第一页
    pagination.value.current = 1;

    // 执行搜索，重置分页到第一页
    executeQuery(submittedFilters, true);
    // 关闭高级搜索面板
    advancedSearchVisible.value = false;
};

// 更新已提交的筛选条件
const updateSubmittedFilters = () => {
    // 清空原有条件
    Object.keys(submittedFilters).forEach(key => {
        delete submittedFilters[key];
    });

    // 添加新条件
    Object.keys(formValue).forEach(key => {
        const value = formValue[key];
        // 检查值是否有效 - 不为空且不为undefined/null
        if (value !== undefined && value !== null && value !== '') {
            // 检查是否是默认值，若是select类型且值等于默认值，则不添加到筛选条件
            const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === key);
            const isDefaultValue = fieldConfig?.compType === 'select' &&
                fieldConfig.compConfig?.defaultValue !== undefined &&
                fieldConfig.compConfig.defaultValue === value;

            if (!isDefaultValue) {
                submittedFilters[key] = value;
            }
        }
    });

    // 设置已执行搜索标志
    hasSearchedOrFiltered.value = Object.keys(submittedFilters).length > 0;
}

// 格式化表单值，特别处理日期类型
const formatFormValues = (values: Record<string, any>) => {
    const formattedValues: Record<string, any> = {};

    // 只保留有效值
    Object.keys(values).forEach(key => {
        const value = values[key];
        if (value !== undefined && value !== null && value !== '') {
            // 检查是否是默认值，若是select类型且值等于默认值，则不添加
            const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === key);
            const isDefaultValue = fieldConfig?.compType === 'select' &&
                fieldConfig.compConfig?.defaultValue !== undefined &&
                fieldConfig.compConfig.defaultValue === value;

            if (!isDefaultValue) {
                formattedValues[key] = value;
            }
        }
    });

    return formattedValues;
}

// 重置表单
const resetForm = () => {
    initFormValue();
    simpleSearchValue.value = '';
    // 设置已执行搜索标志为false
    hasSearchedOrFiltered.value = false;
    // 关闭高级搜索面板
    if (props.eventConfig.formHandler) {
        executeQuery(defaultSearchValues.value, true);
    }
    setTimeout(() => {
        advancedSearchVisible.value = false;
    }, 150);
};

// 处理新建
const handleAddNew = () => {
    props.eventConfig.addNewEvent()
}

// 处理设置常用
const handleSetAlwaysUsed = () => {
    if (props.ruleSidebarRef) {
        props.ruleSidebarRef.setAlwaysUsed();
    }
}

// 处理导入
const handleImportFun = () => {
    if (props.ruleSidebarRef) {
        props.ruleSidebarRef.importFun();
    }
}

// 判断是否有筛选条件
const isFiltered = computed(() => {
    return Object.values(formValue).some(value => {
        if (value === undefined || value === null || value === '') {
            return false
        }
        if (Array.isArray(value) && value.length === 0) {
            return false
        }
        return true
    })
})

// 路由监听
const router = useRouter()
const route = useRoute()
// 保存上一次的查询参数
const previousQuery = ref({})
// 监听路由变化
watch(route, (newRoute, oldRoute) => {
    // 检查查询参数是否改变
    const queryChanged = JSON.stringify(newRoute.query) !== JSON.stringify(previousQuery.value)
    // 更新前一次查询参数
    previousQuery.value = { ...newRoute.query }
    // 如果查询参数改变并且不是首次加载(immediate触发)，则重置表单
    if (queryChanged && oldRoute) {
        resetForm()
    }
}, { deep: true, immediate: true })

// 计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);

// 新增的过滤相关逻辑
const getActiveFilters = () => {
    // 修改为使用已提交的筛选条件，而不是当前表单值
    return submittedFilters;
}

const getFieldLabel = (field: string) => {
    if (field === props.searchConfig.simpleSearchField.field) {
        return props.searchConfig.simpleSearchField.label
    }
    const fieldInfo = props.searchConfig.advancedSearchFields.find(f => f.field === field)
    return fieldInfo?.label || field
}

// 格式化显示值，根据不同类型显示不同格式
const formatDisplayValue = (field: string, value: any) => {
    // 查找字段定义
    const fieldInfo = props.searchConfig.advancedSearchFields.find(f => f.field === field)

    if (!fieldInfo) {
        return value
    }

    // 根据组件类型处理显示
    if (fieldInfo.compType === 'datePicker') {
        // 如果是日期，尝试格式化
        const format = fieldInfo.compConfig?.format || 'YYYY-MM-DD'
        // 移除时间部分，只显示日期
        return value.split(' ')[0]
    } else if (fieldInfo.compType === 'select') {
        // 如果是下拉选择，尝试显示label
        const option = fieldInfo.compConfig?.options?.find((opt: any) => opt.value === value)
        return option ? option.name : value
    }

    return value
}

// 修改清除过滤器函数，确保更好的同步
const clearFilter = async (field: string) => {
    // 清除表单值
    formValue[field] = undefined

    // 同时从已提交筛选条件中移除
    delete submittedFilters[field];

    // 如果是简单搜索字段，同时更新简单搜索框的值
    if (field === props.searchConfig.simpleSearchField.field) {
        simpleSearchValue.value = ''
    }

    // 查找字段配置，以便特殊处理
    const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === field)
    if (fieldConfig?.compType === 'select' && fieldConfig.compConfig?.defaultValue !== undefined) {
        // 如果是下拉选择且有默认值，恢复默认值
        formValue[field] = fieldConfig.compConfig.defaultValue
    }

    // 强制重置分页到第一页
    pagination.value.current = 1;

    // 准备搜索条件 - 只包含有效的剩余条件
    const remainingFilters = Object.keys(submittedFilters).reduce((acc, key) => {
        // 只保留有效的条件
        if (submittedFilters[key] !== undefined && submittedFilters[key] !== null && submittedFilters[key] !== '') {
            acc[key] = submittedFilters[key];
        }
        return acc;
    }, {} as Record<string, any>);

    try {
        // 如果使用内部表格和分页，直接使用executeQuery并重置页码
        if (props.queryMethod) {
            executeQuery(remainingFilters, true);
        }
        // 如果提供了表单处理器，则使用表单处理器
        else if (props.eventConfig.formHandler) {
            await new Promise<void>((resolve) => {
                handleFormValueChange(remainingFilters, true);
                // 使用setTimeout模拟异步等待后台方法完成
                setTimeout(() => {
                    resolve();
                }, 100);
            });
        } else {
            // 否则使用搜索事件
            props.eventConfig.searchEvent(remainingFilters);
            // 使用setTimeout模拟异步等待后台方法完成
            await new Promise<void>((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 100);
            });
        }

        // 如果没有过滤条件了，则重置搜索状态
        if (Object.keys(submittedFilters).length === 0) {
            hasSearchedOrFiltered.value = false
        }
    } catch (error) {
        console.error('清除过滤器出错:', error);
    }
}

// 修改计算属性，只有在有过滤条件且执行过搜索时才返回true
const hasActiveFilters = computed(() => {
    return hasSearchedOrFiltered.value && Object.keys(getActiveFilters()).length > 0
})

// 监听formValue中的simpleSearchField值变化，同步到simpleSearchValue
watch(() => formValue[props.searchConfig.simpleSearchField.field], (newValue) => {
    // 避免循环引用，检查值是否不同才更新
    if (newValue !== simpleSearchValue.value) {
        simpleSearchValue.value = newValue || '';
    }
});

// 监听simpleSearchValue变化，同步到formValue
watch(simpleSearchValue, (newValue) => {
    // 避免循环引用，检查值是否不同才更新
    if (newValue !== formValue[props.searchConfig.simpleSearchField.field]) {
        formValue[props.searchConfig.simpleSearchField.field] = newValue;
    }
});

// 处理清除所有过滤器
const clearAllFilters = async () => {
    // 重置表单所有字段
    initFormValue();
    // 清空简单搜索值
    simpleSearchValue.value = '';
    // 设置搜索状态为false
    hasSearchedOrFiltered.value = false;

    // 强制重置分页到第一页
    pagination.value.current = 1;

    try {
        // 如果使用内部表格和分页，直接使用executeQuery并重置页码
        if (props.queryMethod) {
            executeQuery(defaultSearchValues.value, true);
            advancedSearchVisible.value = false;
        }
        // 如果提供了表单处理器，则重置表单并调用查询方法
        else if (props.eventConfig.formHandler) {
            // 重置所有字段为默认值
            props.searchConfig.advancedSearchFields.forEach(field => {
                if (field.compConfig?.defaultValue !== undefined) {
                    formValue[field.field] = field.compConfig.defaultValue;
                } else {
                    formValue[field.field] = undefined;
                }
            });

            const queryMethod = props.eventConfig.formHandler.queryMethod;
            if (typeof queryMethod === 'function') {
                await new Promise<void>((resolve) => {
                    // 调用查询方法
                    queryMethod();
                    // 使用setTimeout模拟异步等待后台方法完成
                    setTimeout(() => {
                        resolve();
                    }, 100);
                });
            }
            // 关闭高级搜索面板
            advancedSearchVisible.value = false;
        } else {
            // 没有提供表单处理器，调用重新加载函数
            await new Promise<void>((resolve) => {
                // 再次检查确保函数存在
                resetForm();
                // 使用setTimeout模拟异步等待后台方法完成
                setTimeout(() => {
                    resolve();
                }, 100);
            });
            // 关闭高级搜索面板
            advancedSearchVisible.value = false;
        }
    } catch (error) {
        console.error('清除所有过滤器出错:', error);
        // 即使出错也关闭面板
        advancedSearchVisible.value = false;
    }
};

// 新增：处理表单值变更 - 统一处理表单值同步和查询调用
const handleFormValueChange = (formValues: Record<string, any>, resetPage = false) => {
    if (!props.eventConfig.formHandler) return;

    const { fieldMap = {}, dateFields, queryMethod } = props.eventConfig.formHandler;

    // 遍历处理表单值
    Object.keys(formValues).forEach(key => {
        if (formValues[key] === undefined || formValues[key] === null || formValues[key] === '') {
            return; // 跳过空值
        }

        // 获取实际的表单字段名（使用映射表或原始字段名）
        const formField = fieldMap[key] || key;

        // 特殊处理日期字段
        if (dateFields &&
            ((dateFields.startField && formField === dateFields.startField) ||
                (dateFields.endField && formField === dateFields.endField))) {

            const isStartField = formField === dateFields.startField;
            const format = isStartField ? (dateFields.startFormat || ' 00:00:00') : (dateFields.endFormat || ' 23:59:59');

            // 处理日期格式
            formValue[formField] = typeof formValues[key] === 'string' ?
                formValues[key] :
                `${formValues[key]}${format}`;
        } else {
            // 常规字段处理
            formValue[formField] = formValues[key];
        }
    });

    // 调用查询方法
    queryMethod();

    // 重置分页
    if (resetPage) {
        pagination.value.current = 1;
    }
};

/**
 * 处理分页变化
 */
const handlePageChange = (page: number, pageSize: number) => {
    console.log(page, pageSize)
    // 更新分页值
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;

    // 使用当前查询参数刷新数据
    executeQuery(lastQueryParams.value, false);
};

/**
 * 执行查询
 * @param params 查询参数
 * @param resetPage 是否重置页码
 */
const executeQuery = async (params: Record<string, any> = {}, resetPage = false) => {
    // 获取默认值
    const defaultValues = props.searchConfig.advancedSearchFields.reduce((acc, field) => {
        const fieldName = field.field as string;
        if (fieldName && field.compConfig?.defaultValue !== undefined) {
            acc[fieldName] = field.compConfig.defaultValue;
        }
        return acc;
    }, {} as Record<string, any>);

    // 合并默认值和传入的参数
    const mergedParams = {
        ...defaultValues,
        ...params
    };

    // 保存最后的查询参数
    lastQueryParams.value = { ...mergedParams };
    // 关键：自动处理分页重置
    if (resetPage) {
        pagination.value.current = 1;
        // 确保立即应用分页变更
        await nextTick();
    }
    // 优化查询参数
    const queryParams = {
        ...mergedParams,
        page: pagination.value.current,
        pageSize: pagination.value.pageSize
    };

    // 设置加载状态
    loading.value = true;
    pagination.value.isLoading = true;

    try {
        // 调用查询方法
        const res = await props.queryMethod(queryParams);

        // 处理响应数据
        if (res && typeof res === 'object') {
            // 兼容不同的API响应格式
            const list = res.data || res.list || res.records || [];
            const total = res.total || res.totalCount || 0;

            // 更新表格数据
            tableData.value = list;

            // 更新分页状态
            pagination.value = {
                ...pagination.value,
                total: total
            };
        }

        return res;
    } catch (error) {
        console.error('查询失败:', error);
        throw error;
    } finally {
        loading.value = false;
        pagination.value.isLoading = false;
    }
};

// 新增：获取表单默认值配置的方法
const getFormDefaultValues = () => {
    const defaultValues: Record<string, any> = {};

    // 遍历高级搜索字段，获取默认值配置
    props.searchConfig.advancedSearchFields.forEach(field => {
        if (field.defaultValue !== undefined) {
            defaultValues[field.field] = field.defaultValue;
        }
    });
    return defaultValues;
};

// 添加导出处理方法
const handleExport = async (record: any) => {
    if (!props.exportConfig?.exportApi) return;

    try {
        const params = props.exportConfig.formatExportParams?.(record) || { uuid: record.uuid };
        const res = await props.exportConfig.exportApi(params);

        const headers = res.headers;
        const blob = new Blob([res.data], {
            type: headers["content-type"]
        });

        const link = document.createElement("a");
        const url = window.URL.createObjectURL(blob);
        const fileName = props.exportConfig.getFileName?.(headers) ||
            headers["content-disposition"]?.split(';')[1]?.split('=')[1] ||
            'export.xlsx';

        link.href = url;
        link.download = fileName;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        props.exportConfig.onExportSuccess?.();
    } catch (error) {
        console.error('导出失败:', error);
        props.exportConfig.onExportError?.();
    }
}

// 暴露方法给父组件
defineExpose({
    resetForm,
    clearAllFilters,
    handleFormValueChange,

    // 表格相关方法
    query: (params: Record<string, any>) => executeQuery(params, true),
    refresh: () => executeQuery(lastQueryParams.value, false),
    getTableData: () => tableData.value,
    getPagination: () => pagination.value,

    // 新增：获取表单默认值配置的方法
    getFormDefaultValues,
    // 新增：初始化方法
    init: () => {
        defaultSearchValues.value = getFormDefaultValues();
        executeQuery(defaultSearchValues.value);
    }
})
</script>

<style lang="scss" scoped>
.list-page-layout {
  display: flex;
  flex-direction: column;
  height: 100%;

  .top-bar {
    background-color: #fff;
    padding: 16px 16px;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .title-area {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      max-width: 60%;
      
      .page-title {
        color: #555;
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
      .vertical-divider {
        height: 16px;
        width: 1px;
        background-color: #d0cece;
        margin: 0 4px;
      }

      .filter-tags {
        display: flex;
        align-items: center;

        .filter-tag-group {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;
          max-width: 100%;

          .filter-tag {
            margin-right: 0;
            background-color: rgb(246, 247, 249);
            border-color: rgba(0, 0, 0, 0);
            color: rgb(125, 128, 137);
            border-radius: 2px;
          }

          .clear-all-link {
            color: rgb(0, 106, 212);
            cursor: pointer;
            margin-left: 8px;
            font-size: 14px;

            &:hover {
              color: #40a9ff;
            }
          }
        }
      }
    }

    .search-area {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      min-width: 360px;

      .simple-search {
        display: flex;
        align-items: center;
        margin-right: 16px;

        .filter-button {
          margin-left: 8px;
        }
      }
      
      .action-area {
        display: flex;
        align-items: center;
        white-space: nowrap;
      }
    }
  }

  .content-container {
    flex: 1;
    background-color: #fff;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    border: none;
    box-shadow: none;
    position: relative;
    height: calc(100vh - 120px);
    min-height: 400px;

    .table-container {
      flex: 1;
      overflow: auto;
      position: relative;
      width: 100%;
    }

    .pagination-container {
      position: fixed;
      bottom: 0;
      left: 200px;
      right: 0;
      background: #fff;
      z-index: 999;
      padding: 8px 16px;
    }
  }

  .advanced-search-panel {
    padding: 8px;

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;

      button {
        margin-left: 8px;
      }
    }
  }

  .create-button-group {
    display: inline-flex;

    .main-create-button {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .dropdown-create-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: 1px solid rgba(255, 255, 255, 0.5);
      padding: 0 8px;
    }
  }
}
</style>
