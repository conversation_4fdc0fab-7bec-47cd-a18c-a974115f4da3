<template>
  <div id="text_add_vars">
    <a-drawer
      title="批量文本增加"
      :get-container="false"
      v-model:open="textAddsShow"
      placement="right"
      :width="enumTextAddBtnWidth"
      :z-index="1100"
      :push="false"
      @close="textAddClose"
      class="diy_drawer"
    >
      <a-form>
        <a-form-item>
          <a-textarea
            v-model:value="addVars"
            :rows="10"
            placeholder="请输入内容，多个内容之间以逗号或分号分隔"
          />
        </a-form-item>
        <a-alert
          :message="'错误值：' + elAlertErrInfo.join('，')"
          type="error"
          show-icon
          :closable="false"
          v-show="elAlert"
          style="margin-top: 10px"
        />
        <div style="margin-top: 30px; text-align: right;">
          <a-button @click="textAddsShow = false" style="margin-right: 15px;">取 消</a-button>
          <a-button type="primary" @click="textAddsSave">确定</a-button>
        </div>
      </a-form>
    </a-drawer>
  </div>
</template>

<script setup>

// 定义 props
const props = defineProps({
  textAddisShow: {
    type: Boolean,
    default: false,
  },
  elAlertErrInfo: {
    type: Array,
    default: () => [],
  },
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit 事件
const emit = defineEmits(['textAddHide', 'addVarsSure']);

// 定义响应式数据
const addVars = ref("");
const elAlert = ref(false);
const textAddsShow = ref(false);
const enumTextAddBtnWidth = ref("");

// 监听 textAddisShow 的变化
watch(
  () => props.textAddisShow,
  (val) => {
    textAddsShow.value = val;
  }
);

// 监听 elAlertErrInfo 的变化
watch(
  () => props.elAlertErrInfo,
  (val) => {
    if (val && val.length) {
      elAlert.value = true;
    } else {
      textAddsShow.value = false;
    }
  }
);

// 在组件挂载时初始化按钮宽度
onMounted(() => {
  if (typeof document !== 'undefined' && document.body) {
    enumTextAddBtnWidth.value = Math.floor(document.body.clientWidth * 0.3);
  } else {
    enumTextAddBtnWidth.value = 400; // 默认宽度
  }
});

// 方法
const textAddClose = () => {
  addVars.value = "";
  elAlert.value = false;
  emit("textAddHide");
};

const textAddsSave = () => {
  if (addVars.value.length) {
    elAlert.value = false;
    if (props.isTable) {
      emit("addVarsSure", { target: { value: addVars.value, isPush: true } });
    } else {
      emit("addVarsSure", addVars.value);
    }
  }
};
</script>

<style lang="scss" scoped>
#text_add_vars ::v-deep {
  .ant-drawer {
    overflow: auto !important;
    header {
      span {
        font-size: 18px;
        color: #303133;
      }
    }
    .ant-drawer-header {
      text-align: left;
    }
    .ant-btn{
      margin-right: 15px;
    }
    textarea.ant-input{
      padding: 5px 10px !important;
    }
  }
}
</style>
