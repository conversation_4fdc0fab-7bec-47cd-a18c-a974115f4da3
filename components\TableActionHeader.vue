<!-- 表格操作列标题组件 -->
<template>
  <div class="action-header">
    <span class="title-text">{{ title }}</span>
    <TableColumnSelector
      :columns="columns"
      @changeTableList="handleColumnChange"
    />
  </div>
</template>

<script setup lang="ts">
import TableColumnSelector from '@/components/TableColumnSelector.vue';

const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: '操作'
  },
  // 表格列配置
  columns: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['changeTableList']);

// 处理列显示/隐藏
const handleColumnChange = (visibleColumns) => {
  emit('changeTableList', visibleColumns);
};
</script>

<style lang="scss" scoped>
.action-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.title-text {
  font-weight: bold;
  color: #000;
}
</style>
