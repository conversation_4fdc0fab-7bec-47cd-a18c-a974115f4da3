// 可在此插件中注册全局指令
import { getButtonRole } from '~/utils/auth'

export default defineNuxtPlugin((nuxtApp) => {
  // 注册 v-focus 指令
  nuxtApp.vueApp.directive('focus', {
    mounted(el) {
      el.focus()
    }
  })

  // 注册 v-hasPermi 权限控制指令
  nuxtApp.vueApp.directive('hasPermi',
    (el, binding) => {
      const { value } = binding
      const all_permission = "*:*:*";

      const btnRole = getButtonRole()
      const permissions = btnRole === "admin" ? ["*:*:*"] : JSON.parse(btnRole);

      if (value && value instanceof Array && value.length > 0) {
        const permissionFlag = value

        const hasPermissions = permissions.some(permission => {
          return all_permission === permission || permissionFlag.includes(permission)
        })

        if (!hasPermissions) {
          el.parentNode && el.parentNode.removeChild(el)
        }
      } else {
        //throw new Error(`请设置操作权限标签值`)
      }

    }) 
})
