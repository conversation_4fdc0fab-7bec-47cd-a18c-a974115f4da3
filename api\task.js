import request from '@/utils/request'
// 任务填报列表
export function taskFilling(params) {
  return request({
    url: 'erule/demand2/apply/list',
    method: 'get',
    params,
  })
}
//根据登录用户和条线查询机构树
export function userOrgList(params) {
  return request({
    url: 'sys/org/userOrgList',
    method: 'get',
    params,
  })
}
// 新增修改任务
export function saveOrUpdate(data, type) {
  return request({
    url: '/erule/demand2/apply/saveOrUpdate?subType=' + type,
    method: 'post',
    data:data,
  })
}
// 任务填报详情
export function getDemandApplyByUuid(params) {
  return request({
    url: '/erule/demand2/apply/getDemandApplyByUuid',
    method: 'get',
    params,
  })
}
// 任务填报删除
export function deletes(params) {
  return request({
    url: '/erule/demand2/apply/delete',
    method: 'post',
    params,
  })
}
// 需求审核列表获取
export function Requirement(params) {
  return request({
    url: '/erule/demand2/check/list',
    method: 'get',
    params,
  })
}
// 需求审核操作按钮
export function operation(params) {
  return request({
    url: '/erule/demand2/check/update',
    method: 'post',
    params,
  })
}
// 需求审核操提交
export function submitCheck(params) {
  return request({
    url: '/erule/demand2/check/submitCheck',
    method: 'post',
    params,
  })
}

// 规则调整列表获取
export function ruleAdjustment(params) {
  return request({
    url: '/erule/demand2/edit/list',
    method: 'get',
    params,
  })
}
// 规则调整详情
export function save(params) {
  return request({
    url: '/erule/demand2/edit/save',
    method: 'post',
    params,
  })
}
// 规则调整提交任务修改的规则发布到测试环境
export function adjustment(params) {
  return request({
    url: '/erule/demand2/edit/save',
    method: 'post',
    params,
  })
}
// 发布环境接口
export function getEnvironmentIdsOption(params) {
  return request({
    url: '/erule/manage/environment/getEnvironmentListByBusinessLine',
    method: 'get',
    params,
  })
}
// 检查任务规则关联的所有状态
export function chenckState(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/checkState',
    method: 'get',
    params,
  })
}
// 规则调整撤销接口
export function revoke(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/revoke',
    method: 'post',
    params,
  })
}
// 规则调整关联规则列表
export function demandRuleRelList(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/demandRuleRelList',
    method: 'get',
    params,
  })
}

// 规则测试列表获取接口
export function ruleTesting(params) {
  return request({
    url: '/erule/demand2/test/list',
    method: 'get',
    params,
  })
}
// 规则测试提交
export function submission(params) {
  return request({
    url: '/erule/demand2/test/save',
    method: 'post',
    params,
  })
}
// 获取需求审核列表
export function taskAudit(params) {
  return request({
    url: '/erule/demand2/finalAudit/list',
    method: 'get',
    params,
  })
}

// 获得审批意见
export function getAuditTypeList(params) {
  return request({
    url: '/erule/demand2/finalAudit/getAuditTypeList',
    method: 'get',
    params,
  })
}
// 任务审核提交
export function updateRuleCheck(params) {
  return request({
    url: '/erule/demand2/finalAudit/updateRuleCheck',
    method: 'post',
    params,
  })
}
// 获取任务发布列表
export function taskDetails(params) {
  return request({
    url: '/erule/demand2/task/list',
    method: 'get',
    params,
  })
}
// 校验需求关联规则的版本是否冲突
export function checkDemandAudit(params) {
  return request({
    url: '/erule/demand2/task/checkDemandAudit',
    method: 'post',
    params,
  })
}
// 任务发布提交
export function updatePublish(params) {
  return request({
    url: '/erule/demand2/task/updatePublish',
    method: 'post',
    params,
  })
}
// 任务查询列表获取
export function taskQuery(params) {
  return request({
    url: '/erule/demand2/query/list',
    method: 'get',
    params,
  })
}
// 关联规则详情获取
export function rules(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/demandRuleRelDetail',
    method: 'get',
    params,
  })
}
// 文件列表获取
export function demandFileList(params) {
  return request({
    url: '/erule/demand2/file/demandFileList',
    method: 'get',
    params,
  })
}
// 文件上传接口
export function fileUpLoad(data, no) {
  return request({
    url: '/erule/demand2/file/fileUpLoad?appNo=' + no,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}
// 流程日志获取列表接口
export function Process(params) {
  return request({
    url: '/bpm/log/logList',
    method: 'get',
    params,
  })
}
// 文件列表删除
export function fileDelete(id) {
  return request({
    url: '/erule/demand2/file/delete?uuid='+id,
    method: 'post',
    //data,
  })
}
// 文件列表下载
export function download(id) {
  return request({
    url: '/erule/demand2/file/download?uuid=' + id,
    method: 'post',
    responseType: 'arraybuffer'
  })
}
// 历史列表
export function historyList(params) {
  return request({
    url: '/erule/manage/rulelHis/list',
    method: 'get',
    params,
  })
}
// 历史审核信息
export function ruleCheckList(params) {
  return request({
    url: '/erule/manage/ruleCheck/ruleCheckList',
    method: 'get',
    params,
  })
}
// 历史详情
export function hisdetails(params) {
  return request({
    url: '/erule/manage/rulelHis/read2',
    method: 'get',
    params,
  })
}
// 历史版本对比
export function compareRule(params) {
  return request({
    url: '/erule/manage/rulelHis/compareRule',
    method: 'get',
    params,
  })
}
// 新增任务随机编号获取
export function getDemandAppNo(params) {
  return request({
    url: '/erule/demand2/apply/getDemandAppNo ',
    method: 'get',
    params,
  })
}


// 规则调整撤销接口
export function editAuditFlag(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/editAuditFlag',
    method: 'post',
    params,
  })
}

// 政策填报列表
export function policyFilling(params) {
  return request({
    url: 'erule/demand2/policyFilling/list',
    method: 'get',
    params,
  })
}

// 新增修改政策
export function policySaveOrUpdate(data, type) {
  return request({
    url: '/erule/demand2/policyFilling/policySaveOrUpdate?subType=' + type,
    method: 'post',
    data:data,
  })
}

// 政策填报删除
export function deletePolicy(params) {
  return request({
    url: '/erule/demand2/policyFilling/delete',
    method: 'post',
    params,
  })
}

// 政策审核列表获取
export function policyCheckList(params) {
  return request({
    url: '/erule/demand2/policyCheck/list',
    method: 'get',
    params,
  })
}

// 政策审核意见
export function policyAudit(params) {
  return request({
    url: '/erule/demand2/policyCheck/getPolicyAuditTypeList',
    method: 'get',
    params,
  })
}

// 政策审核操提交
export function submitPolicyCheck(params) {
  return request({
    url: '/erule/demand2/policyCheck/submitPolicyCheck',
    method: 'post',
    params,
  })
}

// 获取政策填报开关
export function policySwitch() {
  return request({
    url: '/sys/parameter/getPolicySwitch',
    method: 'get'
  })
}
