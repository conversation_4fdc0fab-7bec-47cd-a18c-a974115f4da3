<template>
  <span
    class="span-con-else"
    style="display: inline-flex"
    @keydown="handleKeyDown"
  >
    <a-date-picker
      class="date-picker"
      :disabled="disabled"
      v-model:value="dataValue"
      @change="handleChange"
      :title="titleStr"
      :show-time="showTime"
      :format="displayFormat"
      :showToday="false"
      v-if="
        (delElseFlag && !readonly) ||
        (delRead && !elseFlag) ||
        (!elseFlag && !readonly)
      "
      style="min-width: unset !important; width: 100%"
    >
      <template #renderExtraFooter>
        <a-button
          size="small"
          type="link"
          @click="toggleShowTime"
          style="padding: 0"
        >
          {{ showTime ? "取消时间" : "选择时间" }}
        </a-button>
      </template>
    </a-date-picker>
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <TableConditionOperationBox
      class="table-opera-box"
      :index="index"
      :col="col"
      :rowData="rowData"
      :colData="colData"
      :record="record"
      :elseFlag="elseFlag"
      :delElseFlag="delElseFlag"
      :readonly="readonly"
      v-if="col && col[4] !== '2' && !readonly"
      @operaChange="operaChange"
    />
  </span>
</template>

<script setup>
import { getDayjsDate } from "@/components/ruleEditCom/utils/inputItmeUtil";
import TableConditionOperationBox from "@/components/ruleEditCom/ruleItemList/operationMenu/tableConditionOperationBox";

// 定义组件的 props
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  value: {
    type: String,
    default: "",
  },
  titleStr: {
    type: String,
    default: "",
  },
  index: {
    type: Number,
    default: 0,
  },
  col: {
    type: String,
    default: "",
  },
  elseFlag: {
    type: Boolean,
    default: false,
  },
  delElseFlag: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  delRead: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Array,
    default: () => [],
  },
  colData: {
    type: Array,
    default: () => [],
  },
  record: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件的 emits
const emit = defineEmits(["onChange", "operaChange"]);

// 定义响应式数据
const dataValue = ref(getDayjsDate(props.value));

const displayFormat = ref("YYYY-MM-DD");
const showTime = ref(false); // 控制时间选择的显示状态


// 监听 value 的变化
watch(
  () => props.value,
  (newVal) => {
    if (newVal && newVal.length > 10) {
      displayFormat.value = "YYYY-MM-DD HH:mm:ss";
      showTime.value = true;
    } else {
      displayFormat.value = "YYYY-MM-DD";
      showTime.value = false;
    }
    dataValue.value = getDayjsDate(newVal);
  },
  { deep: true, immediate: true }
);

// 处理操作变化的方法
const operaChange = (action) => {
  if (props.elseFlag && action === "otherwise") {
    return;
  } else if (action === "otherwise") {
    emit("onChange", { value: "否则", elseFlag: true });
  } else {
    emit("onChange", "");
  }
  let dataIndex = props.rowData.findIndex(
    (item) => item.key === props.record.key
  );
  emit("operaChange", {
    action,
    index: dataIndex,
    col: props.col,
  });
};

// 处理日期变化的方法
const handleChange = (date, dateString) => {
  dataValue.value = getDayjsDate(date, true, displayFormat.value);
  emit("onChange", dataValue.value);
};

// 处理键盘事件的方法
const handleKeyDown = (e) => {
  e && e.stopPropagation && e.stopPropagation();
};

// 切换时间选择的显示状态
const toggleShowTime = () => {
  if (showTime.value) {
    displayFormat.value = "YYYY-MM-DD";
    dataValue.value = getDayjsDate(
      displayFormat.value,
      true,
      displayFormat.value
    );
    emit("onChange", dataValue.value);
  } else {
    displayFormat.value = "YYYY-MM-DD HH:mm:ss";
  }
  showTime.value = !showTime.value;
};
</script>

<style scoped lang="scss">
.date-picker {
  width: 125px;
}
</style>
