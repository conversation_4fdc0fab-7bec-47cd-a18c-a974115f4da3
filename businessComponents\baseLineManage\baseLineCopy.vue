<template>
    <div class="baseline-copy-container">
        <a-form
            :label-col="{ span: 5 }"
            :model="ruleForm"
            :rules="rules"
            :wrapper-col="{ span: 18 }"
            class="copy-form" label-width="100px"
            ref="ruleFormRef"
        >
            <div class="form-section">
                <a-collapse class="base-info-collapse">
                    <a-collapse-panel header="基线信息详情" key="1">
                        <a-row :gutter="16">
                            <a-col :span="12">
                                <a-form-item label="规则库名称" prop="engUuid">
                                    <a-typography-text>{{ ruleForm.engChineseName }}</a-typography-text>
                                </a-form-item>
                                <a-form-item label="基线创建时间">
                                    <a-typography-text>{{ ruleForm.createdTimeStr }}</a-typography-text>
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="基线版本号" prop="edition">
                                    <a-typography-text>{{ ruleForm.edition }}</a-typography-text>
                                </a-form-item>
                                <a-form-item label="基线描述">
                                    <a-typography-text>{{ ruleForm.descs }}</a-typography-text>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-collapse-panel>
                </a-collapse>
            </div>

            <div class="copy-options-section">
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="基线版本" name="edition1">
                            <a-select placeholder="请选择" style="width: 100%" v-model:value="isbusin">
                                <a-select-option :key="item.value" :value="item.value" v-for="item in busin">
                                    {{ item.name }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="基线描述" name="descs">
                            <a-input placeholder="请输入基线描述" v-model:value="ruleForm.descs" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="是否发布" name="issue">
                            <a-select @change="selEnv" placeholder="请选择" style="width: 100%" v-model:value="issue">
                                <a-select-option :key="item.id" :value="item.id" v-for="item in pubArr">
                                    {{ item.name }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="发布环境" name="pubenv" v-show="dataEnv">
                            <a-select style="width: 100%" v-model:value="ruleForm.pubenv">
                                <a-select-option :key="item.id" :value="item.id" v-for="item in envArr">
                                    {{ item.environmentName }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="是否定时发布" name="isTimePublish" v-show="ifTimePublishShow">
                            <a-select @change="selPub" placeholder="请选择" v-model:value="ruleForm.isTimePublish">
                                <a-select-option value="1">是</a-select-option>
                                <a-select-option value="0">否</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-show="pubTimeShow">
                        <a-form-item label="发布时间" name="taskTime">
                            <DateTimePicker
                                v-model:pubDate="pubDate"
                                v-model:pubTime="pubTime"
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="12" v-show="dataEnv">
                        <a-form-item label="发布方式" prop="grayscaleReleaseType" v-show="dataEnv">
                            <a-select v-model:value="ruleForm.grayscaleReleaseType" placeholder="请选择">
                                <a-select-option value="1">正式发布</a-select-option>
                                <a-select-option value="2">灰度发布</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="是否开启通知" name="enableNotification">
                            <a-select v-model:value="ruleForm.enableNotification" placeholder="请选择">
                                <a-select-option value="1">是</a-select-option>
                                <a-select-option value="0">否</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12" v-show="notifyConfigShow">
                        <a-form-item label="选择通知用户" name="notifyConfigIds">
                            <NotifyUserSelect
                                v-model="ruleForm.notifyConfigIds"
                                :notifyConfigList="notifyConfigList"
                                :autoSelect="false"
                            />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-form-item class="form-actions">
                    <a-button :loading="submitLoading" @click="executeBtn" type="primary">复制</a-button>
                </a-form-item>
            </div>
        </a-form>

        <!-- 规则变更清单 -->
        <div class="rule-list-section">
            <div class="section-header">
                <div class="title">规则变更清单</div>
            </div>
            <div ref="basePoint">
                <!-- 当加载中显示骨架屏 -->
                <TableSkeleton
                    v-if="loading"
                    :columns="columns"
                    :limit="pagination.limit"
                    :scrollY="scrollY"
                />
                <!-- 数据加载完成后显示实际表格 -->
                <a-table
                        v-else
                        :data-source="tableData"
                        :loading="loading"
                        :pagination="false"
                        :row-selection="{ selectedRowKeys, onChange: onSelectionChange }"
                        ref="copyTable"
                        row-key="ruleUuid"
                        size="small"
                        :scroll="{y: scrollY }"
                >
                    <a-table-column :width="50" align="center" title="序号">
                        <template #default="{ index }">
                            {{ index + 1 }}
                        </template>
                    </a-table-column>
                    <a-table-column :ellipsis="true" align="left" data-index="ruleName" key="ruleName" title="规则名称">
                        <template #default="{ record }">
                            <a @click="btn_details(record)">{{ record.ruleName }}</a>
                        </template>
                    </a-table-column>
                    <a-table-column :width="300" align="left" data-index="packageNameAll" key="packageNameAll" title="规则路径">
                        <template #default="{ record }">
                            <RulePath
                                    :path="record.packageNameAll"
                                    showCopyButton
                            />
                        </template>
                    </a-table-column>
                    <a-table-column :ellipsis="true" align="center" data-index="descs" key="descs" title="描述" />
                    <a-table-column :ellipsis="true" align="center" data-index="status" key="status" title="变更状态">
                        <template #default="{ record }">
                            <span v-if="record.status === '0'">新增</span>
                            <span v-if="record.status === '1'">变更</span>
                            <span v-if="record.status === '2'">已删除</span>
                        </template>
                    </a-table-column>
                </a-table>
                <Pagination
                    :paginations="pagination"
                    @change="pagin"
                />
            </div>

        </div>

        <!-- 规则详情抽屉 -->
        <RuleDetailDrawer
            :baselineRuleInfoData="baselineRuleInfoData"
            :baselineValidStatus="baselineValidStatus"
            :currentRuleDetail="currentRuleDetail"
            :detailData="detailData"
            :newValidStatus="newValidStatus"
            :ruleCompareData="ruleCompareData"
            :ruleNameColor="ruleNameColor"
            :salienceColor="salienceColor"
            :title="detailDrawerTitle"
            :visible="detailDrawerVisible"
            @close="closeDetailDrawer"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, computed, watch, nextTick, inject } from 'vue';
import { useRouter } from 'vue-router';
import { getcopytex, getCopyListInfo, getCopySub, getInCompleteRuleForEng, getCopyDatails } from '@/api/baseline_Management';
import { pubEnvUUid } from '@/api/pub_environment';
import { getLoginType } from '@/api/userManagement';
import { saveOrUpdateNotifyConfig, allNotifyConfig, getNotifyConfigById } from '@/api/notifyConfig';
import type { TableProps } from "ant-design-vue";
import type { Rule, RuleObject } from 'ant-design-vue/es/form';
import dayjs from "dayjs";
import RulePath from '@/components/ruleDetailCom/RulePath.vue';
import { defineAsyncComponent } from 'vue';
import RuleDetailDrawer from './RuleDetailDrawer.vue';
import type { MessageInstance } from 'ant-design-vue/es/message/interface';
import type { ModalFunc, ModalFuncProps } from 'ant-design-vue/es/modal/Modal';
import Pagination from '@/components/Pagination.vue';
import TableSkeleton from '@/components/TableSkeleton.vue';
import DateTimePicker from '@/components/DateTimePicker.vue';
import { getUsersWithNotification } from "@/api/userManagement";
import NotifyUserSelect from '@/components/NotifyUserSelect.vue';

const props = defineProps({
    copyData: {
        type: Object,
        required: true
    }
});

//计算自适应高度
const basePoint = ref();
// 定义固定高度
const scrollY = ref(270);

const emit = defineEmits(['on-success', 'add-rule-detail-tab']);

const message = inject<MessageInstance>('message');
const modal = inject<ModalFunc>('modal');
const router = useRouter();

interface RuleForm {
    engUuid: string;
    engName: string;
    engChineseName: string;
    edition: string;
    edition1: string;
    descs: string;
    pubenv: string;
    isTimePublish: string;
    taskTime: string;
    createdTimeStr: string;
    enableNotification: string;
    notifyConfigIds: string[];
    grayscaleReleaseType: string;
}

// 初始化表单数据
const ruleForm = ref<RuleForm>({
    engUuid: '',
    engName: '',
    engChineseName: '',
    edition: '',
    edition1: '1',
    descs: '',
    pubenv: '',
    isTimePublish: '0',
    taskTime: '',
    createdTimeStr: '',
    enableNotification: '0',
    notifyConfigIds: [],
    grayscaleReleaseType:'1'
});

const pubArr = [
    { name: '是', id: '1' },
    { name: '否', id: '0' },
];

const issue = ref('0');

const busin = [
    { name: '主要版本', value: '1' },
    { name: '次要版本', value: '0' },
];

const isbusin = ref('1');

const strEnv = ref('');
const dataEnv = ref(false);
const tableData = ref<any[]>([]);
const loading = ref(false);
const submitLoading = ref(false);
const ifTimePublishShow = ref(false);
const pubTimeShow = ref(false);
const pubDate = ref('');
const pubTime = ref('');
const envArr = ref<any[]>([]);
const ruleUuids = ref<string[]>([]);
const loginType = ref('');
const pagination = ref({
    loading: true,
    total: 0,
    limit: 10,
    page: 1,
    showSizeChanger: true,
    showTotal: (total) => `共 ${total} 条数据`,
});

// 表单引用
const ruleFormRef = ref();

const rules = {};

// 表格行选择配置
const selectedRowKeys = ref<string[]>([]);
const ruleHis = ref('');
const ruleHisCheck = ref<string[]>([]);
const onSelectionChange = (selectedKeys: string[], selectedRows: any[]) => {
    selectedRowKeys.value = selectedKeys;
    // 更新规则历史选择记录
    ruleHisCheck.value = selectedRows.map((item: any) => `${item.ruleUuid}_${item.status}`);
};

// 当发布状态改变时
const selEnv = (value: string) => {
    const vid_str = String(value);
    const obj = pubArr.find((item) => item.id === vid_str);
    strEnv.value = obj?.id || '';

    if (strEnv.value === '1') {
        const pars = { engUuid: ruleForm.value.engUuid };
        dataEnv.value = true;
        ifTimePublishShow.value = true;
        pubEnvUUid(pars).then((res) => {
            envArr.value = res.data;
            // 如果有环境数据，自动设置第一项为默认值
            if (envArr.value && envArr.value.length > 0) {
                ruleForm.value.pubenv = envArr.value[0].id;
                // 手动触发表单验证更新
                nextTick(() => {
                    ruleFormRef.value?.validateFields(['pubenv']);
                });
            }
        });
    } else {
        dataEnv.value = false;
        ifTimePublishShow.value = false;
        pubTimeShow.value = false;
        ruleForm.value.isTimePublish = '';
        ruleForm.value.pubenv = ''; // 清空发布环境值
    }
};

// 是否定时发布改变
const selPub = (value: string) => {
    const val_str = String(value);
    if (val_str === '1') {
        pubTimeShow.value = true;
    } else {
        pubTimeShow.value = false;
    }
};

// 表格列定义
const columns = ref([
    {
        title: '序号',
        width: 50,
        align: 'center',
        key: 'index'
    },
    {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        align: 'left',
        ellipsis: true
    },
    {
        title: '规则路径',
        dataIndex: 'packageNameAll',
        key: 'packageNameAll',
        width: 300,
        align: 'left'
    },
    {
        title: '描述',
        dataIndex: 'descs',
        key: 'descs',
        align: 'center',
        ellipsis: true
    },
    {
        title: '变更状态',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        ellipsis: true
    }
]);

// 获取复制列表数据
const getCopyList = async (noChange?: boolean) => {
    loading.value = true;
    const pars = {
        uuid: props.copyData.uuid,
        page: pagination.value.page,
        several: pagination.value.limit,
    };
    try {
        const res = await getCopyListInfo(pars);
        tableData.value = res.data.ruleHisVOList;
        ruleUuids.value = res.data.ruleHisVOList.map((item: any) => item.ruleUuid);
        // 设置规则历史值
        ruleHis.value = res.data.ruleHisUuids;
        pagination.value.total = res.data.totalCount || 0;
    } catch (err) {
        message?.error('获取复制列表失败');
    } finally {
        loading.value = false;
    }
};

// 分页变化处理
const pagin = (cur: number) => {
    pagination.value.page = cur;
    getCopyList();
};

// 规则详情抽屉相关
const detailDrawerVisible = ref(false);
const detailDrawerTitle = ref('规则详情');
const currentRuleDetail = ref<any>(null);
const detailData = ref<any>({
    ruleHisVO1: {},
    ruleHisVO2: {}
});
const ruleNameColor = ref(false);
const newValidStatus = ref(false);
const baselineValidStatus = ref(false);
const salienceColor = ref(false);

// 规则比对数据
const ruleCompareData = computed(() => ({
    ruleHisVO1: detailData.value.ruleHisVO1,
    ruleHisVO2: detailData.value.ruleHisVO2,
    uuid1: currentRuleDetail.value?.uuid,
    uuid2: currentRuleDetail.value?.snapRuleHisUuid
}));

// 基线规则详情数据
const baselineRuleInfoData = computed(() => ({
    type: detailData.value.ruleHisVO2?.type,
    uuid: detailData.value.ruleHisVO2?.uuid,
    tableContent: detailData.value.ruleHisVO2?.tableContent,
    tableContentHis: detailData.value.ruleHisVO2?.tableContentHis,
    textDtl: detailData.value.ruleHisVO2?.textDtl,
    validStatus: detailData.value.ruleHisVO2?.validStatus
}));

// 查看规则详情
const btn_details = (record: any) => {
    // 设置当前规则详情数据
    currentRuleDetail.value = record;
    detailDrawerTitle.value = `规则详情: ${record.ruleName}`;

    // 获取详细数据
    const pars = {
        uuid1: record.status === "2" ? "" : record.uuid,
        uuid2: record.snapRuleHisUuid === null ? "" : record.snapRuleHisUuid,
    };

    getCopyDatails(pars).then((res) => {
        detailData.value = res.data;

        // 显示抽屉
        detailDrawerVisible.value = true;

        // 高亮差异
        nextTick(() => {
            const ruleHisVO1 = detailData.value.ruleHisVO1 || {};
            const ruleHisVO2 = detailData.value.ruleHisVO2 || {};

            if (ruleHisVO2.ruleName && ruleHisVO2.ruleName !== ruleHisVO1.ruleName) {
                ruleNameColor.value = true;
            } else {
                ruleNameColor.value = false;
            }

            if (ruleHisVO2.salience && ruleHisVO2.salience !== ruleHisVO1.salience) {
                salienceColor.value = true;
            } else {
                salienceColor.value = false;
            }

            const valid = ruleHisVO1.validStatus;
            const validHis = ruleHisVO2.validStatus;

            newValidStatus.value = valid === "无效";
            baselineValidStatus.value = validHis === "无效";

            if (validHis && validHis !== valid) {
                newValidStatus.value = true;
            }
        });
    });
};

// 关闭规则详情抽屉
const closeDetailDrawer = () => {
    detailDrawerVisible.value = false;
    // 清空详情数据
    detailData.value = {
        ruleHisVO1: {},
        ruleHisVO2: {}
    };
    currentRuleDetail.value = null;
    ruleNameColor.value = false;
    newValidStatus.value = false;
    baselineValidStatus.value = false;
    salienceColor.value = false;
};

// 添加通知相关的状态
const notifyConfigList = ref<NotifyConfig[]>([]);
const notifyConfigShow = ref(false);

// 监听是否开启通知
watch(() => ruleForm.value.enableNotification, async (newVal: string) => {
    notifyConfigShow.value = newVal === '1';
    if (newVal === '1') {
        try {
            const res = await getUsersWithNotification();
            if (res.data) {
                notifyConfigList.value = res.data;
            }
        } catch (err) {
            console.error('获取通知配置失败:', err);
            message?.error('获取通知配置失败');
        }
    } else {
        ruleForm.value.notifyConfigIds = [];
    }
});

// 更新通知配置的异步方法
const updateNotifyConfig = async () => {
    try {
        // 遍历所有选中的通知配置ID
        for (const notifyId of ruleForm.value.notifyConfigIds) {
            const notifyRes = await getNotifyConfigById({id:parseInt(notifyId, 10)});
            if (notifyRes.data == null) {
                await saveOrUpdateNotifyConfig({
                    userId:notifyId,
                    enablePublishNotification: '1'
                });
            }else {
                await saveOrUpdateNotifyConfig({
                    ...notifyRes.data,
                    enablePublishNotification: '1'
                });
            }
        }
    } catch (err) {
        console.error('更新通知配置失败:', err);
        throw err;
    }
};

// 提交 executeFun 方法
const executeFun = async () => {
    submitLoading.value = true;
    try {
        const data = {
            ruleUuids: ruleUuids.value.join(','),
            ruleHisUuids: ruleHis.value,
            selectUuids: ruleHisCheck.value.join(','),
            descs: ruleForm.value.descs,
            engUuid: ruleForm.value.engUuid,
            environmentId: ruleForm.value.pubenv,
            isMajor: isbusin.value,
            isPublish: issue.value,
            uuid: props.copyData.uuid,
            isTimePublish: ruleForm.value.isTimePublish,
            taskTime: ruleForm.value.isTimePublish === '1' && pubDate.value && pubTime.value ?
                `${pubDate.value} ${pubTime.value}:00` : '',
            grayscaleReleaseType:ruleForm.value.grayscaleReleaseType
        };

        const res = await getCopySub(data);
        if (res.code === 20000) {
            message?.success("复制成功");
            // 如果开启了通知，先更新通知配置
            if (ruleForm.value.enableNotification === '1' && ruleForm.value.notifyConfigIds.length > 0) {
                await updateNotifyConfig();
            }
            nextTick(() => {
                emit('on-success');
            });
        }
    } catch (error) {
        message?.error("复制失败");
    } finally {
        submitLoading.value = false;
    }
};

// 检查时间是否有效
const isTimeValid = () => {
    if (ruleForm.value.isTimePublish === '1' && (!pubDate.value || !pubTime.value)) {
        message?.info('请选择定时发布时间！');
        return false;
    }
    return true;
};

// 检查管理员用户未完成规则
const checkInCompleteRules = async () => {
    if (loginType.value !== '04') {
        return true; // 非管理员用户直接通过
    }

    submitLoading.value = true;
    try {
        const param = { engUuid: ruleForm.value.engUuid };
        const res = await getInCompleteRuleForEng(param);

        if (res && res.data > 0) {
            return new Promise(resolve => {
                if (modal) {
                    modal.confirm({
                        title: '确认发布',
                        content: '规则库中存在未完成的规则，请确认是否发布？',
                        onOk() {
                            resolve(true);
                        },
                        onCancel() {
                            message?.info('已取消');
                            submitLoading.value = false;
                            resolve(false);
                        },
                    });
                } else {
                    // 如果modal不存在，默认继续
                    resolve(true);
                }
            });
        }
        return true;
    } catch (error) {
        submitLoading.value = false;
        return false;
    }
};

// 执行复制操作
const executeBtn = async () => {
    // 检查是否选择了规则
    if (selectedRowKeys.value.length === 0) {
        message?.warning('请选择要复制的规则');
        return;
    }

    // 不需要发布，直接执行复制
    if (issue.value !== '1') {
        executeFun();
        return;
    }

    // 需要发布，验证表单
    const valid = await ruleFormRef.value.validate().catch(() => false);
    if (!valid) return;

    // 检查定时发布时间
    if (!isTimeValid()) return;

    // 检查未完成规则
    const canContinue = await checkInCompleteRules();
    if (canContinue) {
        executeFun();
    }
};

// 获取用户登录类型
const getLoginTypes = () => {
    getLoginType().then((res) => {
        loginType.value = res.data;
    });
};

// 初始化数据
onMounted(() => {
    // 设置基本信息
    ruleForm.value.engUuid = props.copyData.engUuid || '';
    ruleForm.value.engName = props.copyData.engName || '';
    ruleForm.value.edition = props.copyData.edition || '';

    // 获取规则库名称等信息
    const pars = { uuid: props.copyData.uuid };
    getcopytex(pars).then((res) => {
        if (res.data) {
            ruleForm.value.engChineseName = res.data.engChineseName || '';
            ruleForm.value.createdTimeStr = res.data.createdTimeStr || '';
            ruleForm.value.descs = '';  // 清空描述，让用户重新填写
        }
        getCopyList();
    }).catch(() => {
        message?.error('获取基本信息失败');
    });

    // 获取用户登录类型
    getLoginTypes();
});
</script>

<style lang="scss" scoped>
.baseline-copy-container {
    .form-section, .copy-options-section, .rule-list-section {
        background-color: #fff;
        padding: 10px 16px;
        border-radius: 4px;
    }

    .base-info-collapse {
        margin: 0;

        :deep(.ant-collapse-header) {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            font-weight: 500;
        }

        :deep(.ant-collapse-content-box) {
            padding: 16px;

            .ant-form-item-label > label {
                font-weight: bold;
            }
        }
    }

    .copy-options-section {
        h3 {
            margin-bottom: 16px;
            font-weight: 500;
        }

        :deep(.ant-form-item-label > label) {
            font-weight: bold;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 0px;
        }
    }

    .rule-list-section {
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .title {
                font-size: 16px;
                font-weight: bold;
            }
        }
    }
}
</style>
