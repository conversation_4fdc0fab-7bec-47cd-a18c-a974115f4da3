<!-- 错误消息格式化组件 -->
<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 错误消息文本，通常是按行分隔的多条错误
  errorMessage: {
    type: String,
    required: true
  }
});

// 简单处理：将错误消息按行分割
const errorLines = computed(() => {
  if (!props.errorMessage) return [];
  return props.errorMessage.split('\n').filter(line => line.trim());
});
</script>

<template>
  <div style="text-align: left;">
    <div v-for="(line, index) in errorLines" :key="index">
      {{ line }}
    </div>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
div {
  max-width: 500px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
}
</style> 