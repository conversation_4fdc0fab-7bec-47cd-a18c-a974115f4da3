<!-- erule logo图标 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg :width="`${size}px`" :height="`${size}px`" viewBox="0 0 26 26"
         xmlns="http://www.w3.org/2000/svg"
         xmlns:xlink="http://www.w3.org/1999/xlink"
         class="larkui-icon larkui-icon-yuque"
         :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <defs>
            <radialGradient cx="60.212%" cy="64.196%" fx="60.212%"
                            fy="64.196%" r="51.003%"
                            gradientTransform="scale(.85248 1) rotate(-68.415 .654 .565)"
                            id="erule-gradient-1">
                <stop stop-color="#57CB86" offset="0%"></stop>
                <stop stop-color="#FFF" stop-opacity="0.8"
                      offset="100%"></stop>
            </radialGradient>
            <linearGradient x1="82.746%" y1="0%" x2="20.277%"
                            y2="79.406%" id="erule-gradient-2">
                <stop stop-color="var(--yq-yuque-green-500)" offset="0%"/>
                <stop stop-color="var(--yq-yuque-green-600)" offset="100%"/>
            </linearGradient>
            <rect id="erule-rect" x="0" y="0" width="26" height="26"
                  rx="6.24"></rect>
            <path
                d="M9.855 0h.269l.068.001h.07l.07.002h.071l.049.001h.049l.074.002.076.002.076.001.052.001.079.002.08.002.08.003.055.001.082.003.084.003.056.002.057.002.058.002.057.002.059.002.058.003.09.004.06.002.09.005.06.003.062.003.093.005.063.003.063.003.063.004.064.004.064.004.065.004.065.004.065.004.066.004.066.005c2.38.162 3.01 1.87 3.078 2.074l.003.011.003.01 1.033.055a.1.1 0 0 1 .044.19c-1.06.571-1.385 1.733-1.194 2.49.061.244.156.454.263.676l.067.14c.246.512.521 1.132.567 2.38.1 2.796-2.365 5.308-5.282 5.308H10.97l-.134.001h-.097l-.103.002h-.164l-.177.002-.19.002-.274.003-.22.002-.396.004-.43.005-.468.006-.297.003-.417.005-.553.007-.588.008-.497.007-.788.01-.553.008-1.025.014-1.094.016-.994.014-.516.008 6.238-7.07.057-.064.056-.064.056-.064.278-.314.055-.062.055-.062.054-.062.027-.03.054-.062.027-.03.054-.062.053-.061c.619-.715 1.17-1.412 1.561-2.212.435-1.224-.007-2.136-.459-2.69A3.31 3.31 0 0 0 9.057.53C8.903.333 9.007.005 9.3.005h.073L9.43.002h.093L9.58 0H9.793L9.855 0Z"
                id="erule-path"></path>
        </defs>
        <g fill="none" fill-rule="evenodd">
            <mask id="erule-mask" fill="#fff">
            </mask>
            <use fill="url(#erule-gradient-2)" xlink:href="#erule-rect">
            </use>
            <g mask="url(#erule-mask)">
                <g transform="translate(3.77 5.46)">
                    <use fill="#E5FBEF" xlink:href="#erule-path">
                    </use>
                    <use fill-opacity="0.3" fill="url(#erule-gradient-1)"
                         xlink:href="#erule-path"></use>
                </g>
                <path
                    d="M11.003 11.95c-2.7 3.027-5.913 6.711-7.133 8.095-.235.267-.007.518.175.55 9.1 1.559 11.989-2.715 12.632-4.669.881-2.673-.363-3.977-1.067-4.402-2.388-1.443-4.16-.077-4.607.425Z"
                    fill="#FAFFFC"></path>
            </g>
            <text x="5" y="19" font-family="Helvetica, Arial, sans-serif"
                 font-size="24" font-weight="bold" fill="white" font-style="italic">e</text>
        </g>
    </svg>
</template>
