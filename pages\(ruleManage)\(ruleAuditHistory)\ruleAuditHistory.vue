<!-- 规则审核历史 -->

<script setup lang="ts">
    import { auditList, auditstate } from "@/api/audit_history";
    import { getAllBusinessByLoginUser } from "@/api/rule_base";
    import useTableConfig from '@/composables/useTableConfig';
    import RuleAuditHistoryDetail from '@/businessComponents/ruleAudit/RuleAuditHistoryDetail.vue';

    definePageMeta({
        title: '规则审核历史',
        path: '/ruleAuditHistory',
        name: 'ruleAuditHistory'
    })

    const message = inject<any>("message");
    // 添加抽屉控制状态
    const drawerVisible = ref(false);
    const currentDetailData = ref<{uuid: string, checkStatus: string} | null>(null);

    interface Rule {
        ruleName: string;
        engName: string;
        checkStatus: string;
        checkId: string;
        checkTimeStr: string;
        uuid: string;
    }

    const rules = ref<Rule[]>([]);

    // 定义不包含序号列和操作列的表格列
    const tableColumns = [
        {
            title: '规则名称',
            dataIndex: 'ruleName',
            key: 'ruleName',
            align: 'left',
            width: 250,
        },
        {
            title: '规则库',
            dataIndex: 'engName',
            key: 'engName',
            align: 'left',
            width: 250,
        },
        {
            title: '审核状态',
            dataIndex: 'checkStatus',
            key: 'checkStatus',
            align: 'center',
            width: 100,
        },
        {
            title: '审核人',
            dataIndex: 'checkId',
            key: 'checkId',
            align: 'center',
            width: 100,
        },
        {
            title: '审核时间',
            dataIndex: 'checkTimeStr',
            key: 'checkTimeStr',
            align: 'center',
            width: 180,
        },
    ];

    const fetchRules = async (params: Record<string, any> = {}) => {
        try {
            let pars = {
                checkId: params.auditor,
                checkStatus: params.state,
                startDate: params.fristTime,
                endDate: params.endTime,
                ruleName: params.rule_name,
                chineseName: params.rule_Basename,
                businessLine: params.businessLine,
                page: params.page || 1,
                several: params.pageSize || 10,
            };
            const res = await auditList(pars);
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取规则审核历史失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    //字典值
    const businessLineOptions = ref<Array<{name: string, code: string}>>([]);
    const auditStatusOptions = ref<Array<{name: string, code: string}>>([]);

    //组件挂载后
    onMounted(() => {
        getBusinessAndAuditOptions();
    });

    // 获取业务条线和审核状态选项
    const getBusinessAndAuditOptions = async () => {
        try {
            // 获取审核状态选项
            const statusRes = await auditstate({
                typeCode: "status_check",
            });
            auditStatusOptions.value = statusRes.data.filter((item: any) => (item.code !== '2' && item.code !== '3'));

            // 获取业务条线选项
            const businessRes = await getAllBusinessByLoginUser();
            businessLineOptions.value = businessRes.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }

            // 更新搜索配置中的审核状态选项
            const stateField = searchConfig.advancedSearchFields.find(field => field.field === 'state');
            if (stateField && stateField.compConfig) {
                stateField.compConfig.options = auditStatusOptions.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        } catch (error) {
            message.error('获取选项数据失败');
        }
    }

    //修改详情查看逻辑
    const handleDetail = (record: any) => {
        currentDetailData.value = {
            uuid: record.uuid,
            checkStatus: record.checkStatus
        };
        drawerVisible.value = true;
    }

    // 添加抽屉关闭处理
    const handleDrawerClose = () => {
        drawerVisible.value = false;
        currentDetailData.value = null;
    }

    // 搜索配置
    const searchConfig = {
        // 简单搜索字段
        simpleSearchField: {
            label: '规则名称',
            field: 'rule_name'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '规则名称',
                field: 'rule_name',
                compType: 'input' as const
            },
            {
                label: '规则库名称',
                field: 'rule_Basename',
                compType: 'input' as const
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select' as const,
                compConfig: {
                    options: [] as {name: string, value: string}[]
                }
            },
            {
                label: '审核状态',
                field: 'state',
                compType: 'select' as const,
                compConfig: {
                    options: [] as {name: string, value: string}[]
                }
            },
            {
                label: '审核人',
                field: 'auditor',
                compType: 'input' as const
            },
            {
                label: '开始时间',
                field: 'fristTime',
                compType: 'datePicker' as const,
                compConfig: {
                    format: 'YYYY-MM-DD',
                    showTime: false,
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker' as const,
                compConfig: {
                    format: 'YYYY-MM-DD',
                    showTime: false,
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            }
        ]
    };

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: () => {},
        // 添加空的新建事件以满足接口要求
        addNewEvent: () => {},
        // 不显示新建按钮的权限
        addPermission: '',
        // 新增：表单处理器配置
        formHandler: {
            // 日期字段特殊处理
            dateFields: {
                startField: 'fristTime',
                endField: 'endTime',
            },
            // 查询方法
            queryMethod: fetchRules
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: any) => {
        return [
            {
                key: 'detail',
                label: '详情',
                onClick: () => handleDetail(record)
            }
        ];
    };

    const listLayout = ref<InstanceType<typeof ListPage> | null>(null);
    // 数据加载完成处理函数
    const handleDataLoaded = (response: any) => {
        if (response && response.data) {
            rules.value = response.data;
        }
    };
</script>

<template>
    <ListPage
        ref="listLayout"
        title="规则审核历史"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="false"
        :tableColumns="tableColumns"
        :queryMethod="fetchRules"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="['checkStatus']"
        @dataLoaded="handleDataLoaded"
    >
        <template #checkStatus="{ record }">
            <span v-if="record.checkStatus == '提交审核通过'">提交审核通过</span>
            <span v-else-if="record.checkStatus == '提交'">提交</span>
            <span v-else-if="record.checkStatus == '删除'">删除</span>
            <span v-else-if="record.checkStatus == '删除审核通过'">删除审核通过</span>
            <span v-else-if="record.checkStatus == '删除审核未通过'">删除审核未通过</span>
        </template>
    </ListPage>

    <!-- 添加抽屉组件 -->
    <FlexDrawer
        v-model:visible="drawerVisible"
        title="规则审核历史详情"
        :width="1000"
        @close="handleDrawerClose"
    >
        <RuleAuditHistoryDetail
            v-if="currentDetailData"
            :uuid="currentDetailData.uuid"
            :checkStatus="currentDetailData.checkStatus"
        />
    </FlexDrawer>
</template>

<style lang="scss" scoped>
.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}
</style>
