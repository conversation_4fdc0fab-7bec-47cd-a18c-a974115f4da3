import diff_match_patch from "./diff_match_patch";
import $ from 'jquery';
/**
 * 美化表格，处理rolSpan,colSpan
 * @param tableID 表格id
 */
let timer = null;
export function beautyTable(tableID, fun, tag,outFun) {
  // console.log(tableID,'----tableID')
  let title0 = $("#" + tableID).find("tr").eq(1);
  let title1 = $("#" + tableID).find("tr").eq(2);
  let title2 = $("#" + tableID).find("tr").eq(3);
  // console.log(title0.html(),'-------0')
  // console.log(title1.html(),'-------0')
  // console.log(title2.html(),'-------0')
  let width = title1.children().length;
  let reTd=$("#" + tableID).find("tr").eq(0).find("td")
  // reTd.attr('title',reTd.html())
  // console.log(reTd.html())
  //设置宽度为7个单元格，以此实现左右拖动头部固定效果
  $("#" + tableID).find("tr").eq(0).find("td").eq(0).attr("colSpan", 7);
  $("#" + tableID).find("tr").eq(0).find("td").eq(0).css({ 'border': 'none'})
  $("#" + tableID).css({ 'border': '1px solid #c7d8ee'})
  let xuhao0 = '<th class="xh">序号</th>' + title0.html();
  let xuhao1 = '<th></th>' + title1.html();
  let xuhao2 = '<th></th>' + title2.html();
  title0.html(xuhao0);
  title1.html(xuhao1);
  title2.html(xuhao2);
  let title0Th = title0.find("th");
  let title1Th = title1.find("th");
  let title2Th = title2.find("th");
  let colSpan = 1;
  for(let k = reTd.length - 1; k > -1; k--){
    if (tag) {

      reTd[k].addEventListener('click', function (e) {
        throttle(e, 'TD', fun)
      })
    }
  }
  for (let i = title0Th.length - 1; i > -1; i--) //处理第一行th
  {
    if (title0Th.eq(i).html() !== '序号') {
      title0Th.eq(i).attr("class", 'th1');
    }
    if (title0Th.eq(i).html() == '') {
      colSpan = colSpan + 1;
      title0Th.eq(i).remove(); //hide();
    } else {
      title0Th.eq(i).attr("colSpan", colSpan);
      colSpan = 1;
    }
    if (tag) {

      title0Th[i].addEventListener('click', function (e) {
        throttle(e, 'TH', fun)
      })
    }
  }
  colSpan = 1;
  for (let i = title1Th.length - 1; i > -1; i--) //处理第一行th
  {
    if (title1Th.eq(i).html() !== '序号') {
      title1Th.eq(i).attr("class", 'th2');
    }
    if (title1Th.eq(i).html() == '') {
      colSpan = colSpan + 1;
      title1Th.eq(i).remove(); //hide();
    } else {
      title1Th.eq(i).attr("colSpan", colSpan);
      colSpan = 1;
    }
    if (tag) {

      title1Th[i].addEventListener('click', function (e) {
        throttle(e, 'TH', fun)
      })
    }
  }
  if (title2Th.length > 0) //处理第二行th
  {
    for (let i = 0; i < title2Th.length; i++) {
      if (title2Th.eq(i).html() == '') {
        if (title1Th.eq(i).html() == '') {
          title0Th.eq(i).attr("rowSpan", 3);
          //title1Th.eq(i).remove();//hide();
        } else {
          title1Th.eq(i).attr("rowSpan", 2);
        }
        title2Th.eq(i).remove(); //hide();
      } else {
        title2Th.eq(i).attr("class", 'th3');
      }
      if (tag) {

        title2Th[i].addEventListener('click', function (e) {
          throttle(e, 'TH', fun)
        })
      }
    }
  }
  /**增加序号*/
  let rows = $("#" + tableID).find("tr");
  for (let i = 4; i < rows.length; i++) {
    if (rows.eq(i).find('th').length == 0) {
      let tdArr = []
      let temp = '<td>' + (i - 3) + '</td>' + rows.eq(i).html();
      rows.eq(i).html(temp);
      tdArr = rows.eq(i).find('td')
      if (tag) {

        for (let j = 0; j < tdArr.length; j++) {
          tdArr[j].addEventListener('click', function (e) {
            throttle(e, 'TD', fun)
          })
        }
      }
    }
  }
}

function throttle(e, targetName, fun) {
  if (!timer) {
    timer = setTimeout(function () {
      let htmlStr = ''
      if (e.target.tagName === targetName) {
        htmlStr = e.target.innerHTML
      } else {
        htmlStr = e.target.parentNode.innerHTML
      }
      fun(e, htmlStr)
      timer = null;
    }, 60);
  }
}
/**
 *
 * @param tid1 新版本表格
 * @param tid2 原始版本表格
 */
export function differ(tid1, tid2, his) {
  let dmp = new diff_match_patch();;
  let currentRuleContent = "";
  let historyRuleContent = "";
  let tempLine1 = "";
  let tempLine2 = "";
  // console.log($("#" + tid1).html())
  // console.log($("#" + tid2).html())

  $("#" + tid1).find("tr").each(function (j) {
    currentRuleContent = currentRuleContent + "<tr>";
    $(this).children().each(function (i) {
      currentRuleContent = currentRuleContent + $(this).prop("outerHTML");
    });
    currentRuleContent = currentRuleContent + "</tr>";
  });
  $("#" + tid2).find("tr").each(function (j) {
    historyRuleContent = historyRuleContent + "<tr>";
    $(this).children().each(function (i) {
      historyRuleContent = historyRuleContent + $(this).prop("outerHTML");
    });
    historyRuleContent = historyRuleContent + "</tr>";
  });
  // console.log(historyRuleContent)
  // console.log(currentRuleContent)
  let d = dmp.diff_main(historyRuleContent, currentRuleContent);
  //let ds = dmp.diff_prettyHtml(d);
  let compareResult = "";
  for (let i = 0; i < d.length; i++) {
    let tempResult = "";
    if (d[i][0] == '0') {
      tempResult = d[i][1];
      compareResult = compareResult + tempResult;
    }
    if (d[i][0] == '1') //插入
    {
      //   alert("插入："+d[i][1]);
      let chars = d[i][1].split('');

      for (let j = 0; j < chars.length; j++) {
        // alert("chars[i]："+chars[i]);
        if (escapeChar(chars[j])) {
          tempResult = chars[j];
        } else {
          tempResult = "<ins style='background:#99FFFF;'>" + chars[j] + "</ins>";
        }
        compareResult = compareResult + tempResult;
      }
    }
    if (d[i][0] == '-1') { //删除
      let chars = d[i][1].split("");
      for (let j = 0; j < chars.length; j++) {
        if (escapeChar(chars[j])) {
          tempResult = chars[j];
        } else {
          tempResult = "<del style='background:#FFCC99;'>" + chars[j] + "</del>";
        }
        compareResult = compareResult + tempResult;
      }
    }

  }
  !his && $("#" + tid1).html(compareResult);
  return compareResult
}
/**
 * 是否忽略的字符 br th td
 * @param ch
 * @returns {Boolean}
 */
function escapeChar(ch) {
  if (ch == '<' || ch == 't' || ch == 'd' || ch == 'h' || ch == 'r' || ch == '/' || ch == '>' || ch == 'b') {
    return true;
  }
  return false;
}
