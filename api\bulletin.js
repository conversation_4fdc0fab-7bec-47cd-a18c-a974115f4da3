import request from '@/utils/request'
//参数配置
//列表展示
export function bulletinList(params) {
    return request({
      url: 'sys/bulletin/list',
      method: 'get',
      params,
    })
  }
//获取修改文本框的值
export function addBulletin(data) {
    return request({
      url: 'sys/bulletin/addBulletin',
      method: 'post',
      data,
    })
  }
//修改提交
export function updateBulletin(data) {
    return request({
      url: 'sys/bulletin/updateBulletin',
      method: 'post',
      data,
    })
  }
// 删除(逻辑删除)
export function deleteBulletin(params) {
  return request({
    url: 'sys/bulletin/delBulletin',
    method: 'post',
    data: params
  })
}
//查询最后一条公告
export function findLastBulletin() {
  return request({
    url: 'sys/bulletin/findLastBulletin',
    method: 'get',
  })
}
