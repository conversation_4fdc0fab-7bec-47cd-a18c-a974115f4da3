/**
 * 所见即所得文本编辑器组件
 */
<template>
  <!-- 编辑器容器 -->
  <div class="text-editor-container">
    <div class="editor-wrapper">
      <!-- 行号区域 -->
      <div class="line-numbers" ref="lineNumbersRef">
        <div v-for="num in lineCount" :key="num" class="line-number">{{ num }}</div>
      </div>
      <!-- 编辑区域 -->
      <div
        ref="editorRef"
        class="rich-editor"
        :contenteditable="!readOnly"
        :class="{ 'read-only': readOnly }"
        @input="handleInput"
        @paste="handlePaste"
        @blur="handleBlur"
        @scroll="handleScroll"
        @keydown="handleKeyDown"
        @click="handleEditorClick"
        @keyup="handleEditorKeyUp"
      ></div>
      <!-- 智能提示弹窗 -->
      <div v-if="showSuggestions" class="suggestions-popup" :style="suggestionStyle">
        <ul class="suggestions-list">
          <li
            v-for="(item, index) in filteredSuggestions"
            :key="index"
            :class="{ 
              active: index === activeSuggestionIndex, 
              'logic-suggestion': item.type === 'logic',
              'action-suggestion': item.type === 'action',
              'structure-suggestion': item.type === 'structure'
            }"
            @click="(event) => handleSuggestionClick(item, event)"
          >
            <span class="suggestion-label">{{ item.label }}</span>
            <span v-if="item.description" class="suggestion-desc">{{ item.description }}</span>
          </li>
        </ul>
      </div>
      
      <!-- 操作符建议弹窗 -->
      <div v-if="showOperatorList" class="suggestions-popup operator-popup" :style="suggestionStyle">
        <!-- <div class="operator-title">选择操作符 ({{ selectedItemDataType }})</div> -->
        <div v-if="operatorSuggestions.length === 0" class="no-operators">
          没有找到匹配的操作符
        </div>
        <ul class="suggestions-list" v-else>
          <li
            v-for="(operator, index) in operatorSuggestions"
            :key="index"
            :class="{ active: index === activeOperatorIndex }"
            @click="(event) => handleOperatorClick(operator, event)"
          >
            <span class="suggestion-label">{{ operator.label }}</span>
            <span v-if="operator.description" class="suggestion-desc">{{ operator.description }}</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 标签展示区域 -->
    <div class="tags-container">
      <!-- 分类标签区域 -->
      <div class="category-section">
        <div class="category-tags">
          <div 
            v-for="(category, index) in categoryTags" 
            :key="index"
            class="tag-item category-tag"
            :class="{ active: selectedCategory === category.fieldType }"
            @mouseenter="handleCategorySelect(category.fieldType)"
          >
            {{ category.name }}
          </div>
        </div>
      </div>
      
      <!-- 详细标签区域 -->
      <div class="detail-section">
        <div v-if="selectedCategory" class="detail-tags">
          <a-dropdown 
            v-for="(group, index) in detailTags" 
            :key="index"
            :trigger="['hover']"
            placement="bottomLeft"
          >
            <div class="tag-item detail-tag">
              {{ group.name }}
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item 
                  v-for="(item, idx) in group.items" 
                  :key="`${index}-${idx}`"
                  @click="insertTagText(item.text)"
                >
                  {{ item.name || item.text }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div v-if="!selectedCategory && categoryTags.length > 0" class="hover-tip">
          点击分类标签查看详细标签
        </div>
      </div>
      
      <div v-if="tagsLoading" class="tags-loading">
        <a-spin size="small" />
      </div>
      
      <div v-if="tagsError" class="tags-error">
        {{ tagsError }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, inject, computed, nextTick } from 'vue'
import { getRuleTextTags, findByEngUuid } from '@/api/rule_editor'
import store from '@/store'

// 定义补全项的数据结构
interface SuggestionItem {
  label: string      // 显示标签
  value: string     // 实际插入值
  type?: string      // 类型
  description?: string // 描述
  children?: SuggestionItem[] // 子项列表
  paramQuantity?: number // 参数数量
  paramTypes?: any[] // 参数类型列表
}

// 标签项数据结构
interface TagItem {
  id?: string
  name: string
  code?: string
  engId?: string
  ruleText?: string
  fieldType?: string // 添加 fieldType 字段用于分类
}

// 业务字段补全项数据结构
interface BusinessFieldItem {
  id: number
  dataType: string
  description: string
  engUuid: string
  field: string
  keyword: string
  ruleDesc: string
}

// 接口响应类型
interface ApiResponse<T> {
  code: number
  message?: string
  data: T
}

// 分组后的标签项结构
interface ProcessedTagGroup {
  name: string
  fieldType?: string // 添加可选的fieldType属性用于分类视图
  items: Array<{
    name?: string
    text: string
  }>
}

// 组件属性定义
const props = defineProps({
  // 编辑器内容
  modelValue: {
    type: String,
    default: ''
  },
  // 是否只读模式
  readOnly: {
    type: Boolean,
    default: false
  },
  // 引擎ID，用于获取标签
  uuid: {
    type: String,
    default: '8a2d8e9f7e327546017ee288feee0fd6'
  },
  viewStatus: {
    type: String,
    required: true
  }
})

// 编辑器引用
const editorRef = ref<HTMLElement | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// 行号相关
const lineNumbersRef = ref<HTMLElement | null>(null)
const lineCount = ref(1)

// 智能提示相关
const showSuggestions = ref(false)
const activeSuggestionIndex = ref(0)
const suggestionStyle = ref({
  top: '0px',
  left: '0px'
})

// 定义逻辑关键词建议列表
const logicKeywordSuggestions = ref<SuggestionItem[]>([
  {
    label: '并且',
    value: '并且',
    type: 'logic',
    description: '同时满足多个条件'
  },
  {
    label: '或者',
    value: '或者', 
    type: 'logic',
    description: '或者满足某条件'
  },
  {
    label: '并且非',
    value: '并且非',
    type: 'logic',
    description: '同时不满足某条件'
  },
  {
    label: '或者非',
    value: '或者非',
    type: 'logic',
    description: '或者不满足某条件'
  }
])

// 定义执行动作关键词建议列表
const actionKeywordSuggestions = ref<SuggestionItem[]>([
  {
    label: '调用方法',
    value: '调用方法',
    type: 'action',
    description: '调用指定的方法'
  },
  {
    label: '设置',
    value: '设置',
    type: 'action',
    description: '设置变量的值'
  }
])

// 定义规则结构关键词建议列表
const structureKeywordSuggestions = ref<SuggestionItem[]>([
  {
    label: '条件',
    value: '条件',
    type: 'structure',
    description: '定义规则的条件部分'
  },
  {
    label: '执行动作',
    value: '执行动作',
    type: 'structure',
    description: '定义规则的执行动作部分'
  },
  {
    label: '否则',
    value: '否则',
    type: 'structure',
    description: '定义规则的否则分支'
  },
  {
    label: '预定义',
    value: '预定义',
    type: 'structure',
    description: '定义预定义变量或方法'
  }
])

// 当前输入的关键词
const currentInputWord = ref('')

// 标签列表 - 改为动态加载
const tagsList = ref<TagItem[]>([])
const tagsLoading = ref(false)
const tagsError = ref<string | null>(null)

// 业务字段列表
const businessFields = ref<BusinessFieldItem[]>([])
const businessFieldsLoading = ref(false)
const businessFieldsError = ref<string | null>(null)
const localContent = ref<string>('')

// 处理后的分类标签数据
const categoryTags = computed(() => {
  const categories: Record<string, { name: string; count: number; fieldType: string }> = {}
  
  tagsList.value.forEach(tag => {
    if (!tag.fieldType) return
    
    if (!categories[tag.fieldType]) {
      categories[tag.fieldType] = {
        name: tag.fieldType,
        count: 0,
        fieldType: tag.fieldType
      }
    }
    categories[tag.fieldType].count++
  })
  
  return Object.values(categories).map(category => ({
    name: `${category.name} (${category.count})`,
    fieldType: category.fieldType,
    items: []
  }))
})

// 当前悬浮分类下的详细标签
const detailTags = computed(() => {
  if (!selectedCategory.value) return []
  
  const groups: Record<string, ProcessedTagGroup> = {}
  
  tagsList.value
    .filter(tag => tag.fieldType === selectedCategory.value)
    .forEach(tag => {
      if (!tag.name) return
      
      if (!groups[tag.name]) {
        groups[tag.name] = {
          name: tag.name,
          items: []
        }
      }
      
      // 添加子项
      if (tag.ruleText) {
        groups[tag.name].items.push({
          text: tag.ruleText
        })
      }
    })
  
  return Object.values(groups)
})

// 标签分类和选中状态管理
const selectedCategory = ref<string>('') // 当前选中的分类

// 格式化文本，在逻辑操作符前添加换行
const formatTextContent = () => {
  if (!editorRef.value) return
  
  // 获取纯文本内容
  const textContent = editorRef.value.innerText
  
  // 定义逻辑操作符，按长度降序排列以避免匹配冲突
  const logicOperators = ['并且非', '或者非', '并且', '或者']
  
  let formattedText = textContent
  
  // 对每个逻辑操作符进行处理
  logicOperators.forEach(operator => {
    // 使用正则表达式替换，只在操作符不在行首的情况下添加换行符
    // 匹配条件：操作符前面有非换行符的字符，且操作符不在行首
    const regex = new RegExp(`([^\\n\\s])\\s*${operator}`, 'g')
    formattedText = formattedText.replace(regex, `$1\n${operator}`)
  })
  
  // 将换行符转换为HTML格式并更新编辑器
  const htmlContent = formattedText.split('\n').map(line => {
    // 处理每一行，如果是逻辑操作符开头，添加样式
    const trimmedLine = line.trim()
    if (logicOperators.some(op => trimmedLine.startsWith(op))) {
      const operator = logicOperators.find(op => trimmedLine.startsWith(op))
      const restContent = trimmedLine.substring(operator!.length).trim()
      return `<span class="condition-and" style="color: #444693; font-weight: bold;">${operator}</span> ${restContent}`
    }
    return line
  }).join('<br>')
  
  // 更新编辑器内容
  editorRef.value.innerHTML = htmlContent
  
  // 触发内容更新
  const content = editorRef.value.innerHTML
  emit('update:modelValue', content)
  emit('change', content)
  calculateLineCount()
  
  // 更新本地缓存和store
  localContent.value = content
  store.commit('setTextRule', {
    ruleUuid: props.uuid,
    text: content,
  })
}

// 将标签文本插入编辑器
const insertTagText = (text: string) => {
  if (props.readOnly || !editorRef.value) {
    return
  }
  
  
  try {
    // 确保编辑器获取焦点
    editorRef.value.focus()
    
    // 恢复之前保存的选择
    restoreSelection()
    
    // 尝试使用execCommand插入文本
    const success = document.execCommand('insertText', false, text)
    
    // 如果execCommand失败，使用Range API
    if (!success) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const textNode = document.createTextNode(text)
        range.deleteContents()
        range.insertNode(textNode)
        range.setStartAfter(textNode)
        range.setEndAfter(textNode)
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }
    
    // 触发内容更新
    const content = editorRef.value.innerHTML
    emit('update:modelValue', content)
    emit('change', content)
    calculateLineCount()
    
    // 更新本地缓存和store
    localContent.value = content
    store.commit('setTextRule', {
      ruleUuid: props.uuid,
      text: content,
    })
    
    // 保存新的光标位置
    saveSelection()
    
    // 添加格式化逻辑
    setTimeout(() => {
      formatTextContent()
    }, 100)
  } catch (err) {
  }
}

// 获取标签列表
const fetchTags = async () => {
  // 使用固定的 engId
  const engId = '8a2d8e9f7e327546017ee288feee0fd6'
  
  // 首先检查store中是否有缓存，如果有就直接返回不执行任何操作
  const cachedTags = store.getters.ruleTextTags[engId]
  
  if (cachedTags && Array.isArray(cachedTags) && cachedTags.length > 0) {
    tagsList.value = cachedTags
    return Promise.resolve() // 直接返回，不执行任何请求
  }
  
  // 如果正在加载中，不再重复请求
  if (tagsLoading.value) {
    return Promise.resolve()
  }
  
  
  // 设置一个请求标记，避免重复请求
  tagsLoading.value = true
  tagsError.value = null
  
  try {
    const response = await getRuleTextTags({
      engId: engId
    })
    
    // 确保类型安全
    const res = response as unknown as ApiResponse<TagItem[]>
    
    if (res.code === 20000) {
      // 处理返回的标签数据
      const tags = res.data || []
      tagsList.value = tags
      
      // 存储到store缓存
      store.commit('setRuleTextTags', {
        engId: engId,
        tags: tags
      })
      
    }
  } catch (err: any) {
    tagsError.value = err.message || '获取标签数据失败'
  } finally {
    tagsLoading.value = false
  }
}

// 获取业务字段信息
const fetchBusinessFields = async () => {
  
  // 使用固定的 engUuid
  const engUuid = '8a2d8e9f8828a24901882cb8ead9000b'
  
  // 首先检查store中是否有缓存，如果有就直接返回不执行任何操作
  const cachedFields = store.getters.businessFields[engUuid]
  
  
  if (cachedFields && Array.isArray(cachedFields) && cachedFields.length > 0) {
    businessFields.value = cachedFields
    return Promise.resolve() // 直接返回，不执行任何请求
  }
  
  // 如果正在加载中，不再重复请求
  if (businessFieldsLoading.value) {
    
    return Promise.resolve()
  }
  
  
  
  // 设置请求标记
  businessFieldsLoading.value = true
  businessFieldsError.value = null
  
  try {
    // 显示全局loading
    showGlobalLoading('获取业务字段信息中，请稍候...')
    
    const response = await findByEngUuid({
      engUuid: engUuid
    })
    
    // 确保类型安全
    const res = response as unknown as ApiResponse<BusinessFieldItem[]>
    
    if (res.code === 20000) {
      // 处理返回的业务字段数据
      const fields = res.data || []
      businessFields.value = fields
      
      // 存储到store缓存
      store.commit('setBusinessFields', {
        engUuid: engUuid,
        fields: fields
      })
      
      
    }
  } catch (err: any) {
    businessFieldsError.value = err.message || '获取业务字段信息失败'
  } finally {
    // 隐藏全局loading
    hideGlobalLoading()
    businessFieldsLoading.value = false
  }
}

// 保存最后光标位置
const lastRange = ref<Range | null>(null)

// 保存当前光标位置
const saveSelection = () => {
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    lastRange.value = selection.getRangeAt(0).cloneRange()
  }
}

// 恢复保存的光标位置
const restoreSelection = () => {
  if (lastRange.value && editorRef.value) {
    const selection = window.getSelection()
    selection?.removeAllRanges()
    selection?.addRange(lastRange.value)
  }
}

// 处理编辑器点击事件，保存光标位置
const handleEditorClick = () => {
  saveSelection()
}

// 处理编辑器键盘事件，保存光标位置
const handleEditorKeyUp = () => {
  saveSelection()
}

// 插入标签到编辑器
const insertTag = (tag: string) => {
  if (props.readOnly || !editorRef.value) return
  
  // 确保编辑器获取焦点
  editorRef.value.focus()
  
  // 恢复之前保存的选择
  restoreSelection()
  
  // 使用execCommand插入文本
  document.execCommand('insertText', false, tag)
  
  // 触发内容更新
  const content = editorRef.value.innerHTML
  emit('update:modelValue', content)
  emit('change', content)
  calculateLineCount()
  
  // 更新本地缓存和store
  localContent.value = content
  store.commit('setTextRule', {
    ruleUuid: props.uuid,
    text: content,
  })
  
  // 保存新的光标位置
  saveSelection()
}
// 获取运算符映射
const operatorMap = computed(() => {
  try {
    const listMap = store.getters.listMap || {}
    
    const operatorTypeList = listMap[props.uuid]?.initModelData?.operatorTypeList || {}
    return operatorTypeList
  } catch (error) {
    console.error('Error getting operatorMap:', error)
    return {}
  }
})

// 从store获取补全建议列表
const suggestions = computed(() => {
  const listMap = store.getters.listMap || {}
  const firstFieldItemList = listMap[props.uuid]?.initModelData?.firstFieldItemList || []
  return firstFieldItemList
})

// 根据输入过滤建议列表
const filteredSuggestions = computed(() => {
  if (!currentInputWord.value) return suggestions.value
  
  const inputLower = currentInputWord.value.toLowerCase()
  
  // 1. 检查是否匹配逻辑关键词（条件部分）
  const logicResults = logicKeywordSuggestions.value.filter((item: SuggestionItem) => 
    item.label.includes(currentInputWord.value) || 
    item.value.includes(currentInputWord.value)
  )

  // 2. 检查是否匹配执行动作关键词（执行动作部分）
  const actionResults = actionKeywordSuggestions.value.filter((item: SuggestionItem) => 
    item.label.includes(currentInputWord.value) || 
    item.value.includes(currentInputWord.value)
  )

  // 3. 检查是否匹配规则结构关键词（全局）
  const structureResults = structureKeywordSuggestions.value.filter((item: SuggestionItem) => 
    item.label.includes(currentInputWord.value) || 
    item.value.includes(currentInputWord.value)
  )

  // 4. 从业务字段中匹配
  const businessResults = businessFields.value
    .filter((field: BusinessFieldItem) => 
      field.description.toLowerCase().includes(inputLower) ||
      field.keyword.toLowerCase().includes(inputLower)
    )
    .map((field: BusinessFieldItem) => {
      return {
        label: field.keyword,       // 显示keyword而不是description
        value: field.keyword,      // 插入值仍然是keyword
        description: '', // 右侧描述改为显示description
        type: field.dataType       // 保持dataType作为type
      }
    })
  
  // 5. 合并结果，关键词优先显示
  return [...structureResults, ...logicResults, ...actionResults, ...businessResults]
})

// 获取当前光标位置
const getCursorPosition = () => {
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    
    // 创建一个临时的 span 元素作为标记
    const tempSpan = document.createElement('span')
    tempSpan.textContent = '|' // 使用一个字符作为标记
    
    // 在当前光标位置插入临时元素
    range.insertNode(tempSpan)
    
    // 获取临时元素的位置
    const rect = tempSpan.getBoundingClientRect()
    const editorRect = editorRef.value?.getBoundingClientRect()
    
    // 移除临时元素，恢复原始内容
    tempSpan.remove()
    
    // 重新设置光标位置
    range.collapse(true)
    selection.removeAllRanges()
    selection.addRange(range)
    
    if (editorRect) {
      // 计算相对于编辑器的位置
      let top = rect.top - editorRect.top + rect.height
      let left = rect.left - editorRect.left
      
      // 确保位置值为正数
      top = Math.max(5, top)
      left = Math.max(5, left)
      
      // 确保弹窗不会超出编辑器右侧边界
      const maxLeft = editorRect.width - 200 // 假设弹窗宽度为200px
      left = Math.min(maxLeft, left)
      
      return { top, left }
    }
  }
  
  // 返回默认位置（编辑器左上角）
  return { top: 5, left: 5 }
}

// 计算行数
const calculateLineCount = () => {
  if (editorRef.value) {
    // 获取编辑器内容的所有行（包括空行）
    const lines = editorRef.value.innerText.split('\n')
    lineCount.value = Math.max(1, lines.length)
  }
}

// 同步滚动
const handleScroll = () => {
  if (editorRef.value && lineNumbersRef.value) {
    lineNumbersRef.value.scrollTop = editorRef.value.scrollTop
  }
}

// 存储当前选中项的dataType
const selectedItemDataType = ref<string>('')

// 操作符列表
const operatorSuggestions = ref<SuggestionItem[]>([])

// 是否显示操作符建议
const showOperatorList = ref(false)

// 激活的操作符索引
const activeOperatorIndex = ref(0)

// 检查当前是否在执行动作部分
const checkIfInActionSection = (range?: Range) => {
  try {
    const currentRange = range || window.getSelection()?.getRangeAt(0)
    if (!editorRef.value || !currentRange) {
      return false
    }
    
    // 创建一个临时的range来获取光标位置前的所有文本
    const tempRange = document.createRange()
    tempRange.selectNodeContents(editorRef.value)
    tempRange.setEnd(currentRange.startContainer, currentRange.startOffset)
    const textBeforeCursor = tempRange.toString()
    
    // 将文本按行分割
    const lines = textBeforeCursor.split('\n').map(line => line.trim())
    
    // 从光标位置向上逐行查找，找到第一个匹配的关键词
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i]
      if (!line) continue
      
      // 检查是否包含"执行动作"关键词
      if (line.includes('执行动作')) {
        return true
      }
      
      // 检查是否包含"条件"关键词
      if (line.includes('条件')) {
        return false
      }
    }
    
    // 如果都没找到，默认不在执行动作部分
    return false
    
  } catch (error) {
    console.error('Error checking action section:', error)
    return false
  }
}

// 获取当前行的操作类型（设置 或 调用方法）
const getCurrentActionType = () => {
  try {
    const selection = window.getSelection()
    if (!selection || !selection.rangeCount || !editorRef.value) return null
    
    const range = selection.getRangeAt(0)
    let currentNode = range.startContainer
    
    // 如果是文本节点，获取其父元素
    if (currentNode.nodeType === Node.TEXT_NODE) {
      currentNode = currentNode.parentNode as Node
    }
    
    // 向上查找到行级别的容器（通常是div或者有特定class的元素）
    while (currentNode && currentNode !== editorRef.value) {
      if (currentNode.nodeType === Node.ELEMENT_NODE) {
        const element = currentNode as HTMLElement
        
        // 检查是否是行级别的元素
        if (element.tagName === 'DIV' || 
            element.style.display === 'block' ||
            element.getBoundingClientRect().width > editorRef.value!.getBoundingClientRect().width * 0.8) {
          
          // 获取这个元素的文本内容作为当前行
          const currentLineText = element.innerText || element.textContent || ''
          

          
          // 检查当前行是否包含操作关键词
          if (currentLineText.includes('设置')) {
            return '设置'
          } else if (currentLineText.includes('调用方法')) {
            return '调用方法'
          }
          
          // 如果当前行没有找到，检查前面的兄弟元素
          let prevElement = element.previousElementSibling
          while (prevElement) {
            const prevLineText = (prevElement as HTMLElement).innerText || prevElement.textContent || ''
            
            if (prevLineText.includes('调用方法')) {
              return '调用方法'
            } else if (prevLineText.includes('设置')) {
              return '设置'
            }
            
            // 如果遇到其他结构关键词，停止查找
            if (prevLineText.includes('条件') || prevLineText.includes('执行动作') || prevLineText.includes('否则')) {
              break
            }
            
            prevElement = prevElement.previousElementSibling
          }
          
          break
        }
      }
      currentNode = currentNode.parentNode as Node
    }
    
    // 如果上述方法失败，使用备用方法：简单的文本分析
    const allText = editorRef.value.innerText || ''
    const lines = allText.split('\n')
    
    // 从最后一行开始向上查找
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].trim()
      if (!line) continue
      
      if (line.includes('调用方法')) {
        return '调用方法'
      } else if (line.includes('设置')) {
        return '设置'
      }
      
      // 只检查最近的几行
      if (i < lines.length - 3) break
    }
    return null
  } catch (error) {
    console.error('Error getting action type:', error)
    return null
  }
}

// 显示操作符建议
const showOperatorSuggestions = (dataType: string) => {
  // 保存当前选中项的dataType
  selectedItemDataType.value = dataType
  
  // 检查是否在执行动作部分
  const isInAction = checkIfInActionSection()
  
  if (isInAction) {
    const actionType = getCurrentActionType()
    
    if (actionType === '设置') {
      // 如果是设置操作，直接提供等于操作符
      operatorSuggestions.value = [{
        label: '等于""',
        value: '等于""',
        paramTypes: [],
        paramQuantity: 0
      }]
      
      showOperatorList.value = true
      activeOperatorIndex.value = 0
      
      setTimeout(() => {
        const position = getCursorPosition()
        if (position) {
          suggestionStyle.value = {
            top: `${position.top}px`,
            left: `${position.left}px`
          }
        }
      }, 0)
      return
    } else if (actionType === '调用方法') {
      // 如果是调用方法，从baseMethodListMap获取方法列表
      try {
        const listMap = store.getters.listMap || {}
        const initModelData = listMap[props.uuid]?.initModelData || {}
        const baseMethodListMap = initModelData.baseMethodListMap || {}
        

        
        // 将baseMethodListMap转换为建议列表
        const methodSuggestions: SuggestionItem[] = []
        
        // 尝试不同的数据结构解析方式
        if (typeof baseMethodListMap === 'object' && baseMethodListMap !== null) {
          // 方式1: 直接遍历所有值
          Object.values(baseMethodListMap).forEach((methodGroup: any) => {
            if (Array.isArray(methodGroup)) {
              methodGroup.forEach((method: any) => {
                if (method.label && method.value) {
                  methodSuggestions.push({
                    label: method.label,
                    value: method.value,
                    paramTypes: method.paramTypes || [],
                    paramQuantity: method.paramQuantity || 0
                  })
                }
              })
            } else if (methodGroup && typeof methodGroup === 'object') {
              // 方式2: 如果是对象，尝试提取属性
              if (methodGroup.label && methodGroup.value) {
                methodSuggestions.push({
                  label: methodGroup.label,
                  value: methodGroup.value,
                  paramTypes: methodGroup.paramTypes || [],
                  paramQuantity: methodGroup.paramQuantity || 0
                })
              }
            }
          })
          
          // 方式3: 如果上述方式都没有数据，尝试直接作为数组处理
          if (methodSuggestions.length === 0 && Array.isArray(baseMethodListMap)) {
            baseMethodListMap.forEach((method: any) => {
              if (method.label && method.value) {
                methodSuggestions.push({
                  label: method.label,
                  value: method.value,
                  paramTypes: method.paramTypes || [],
                  paramQuantity: method.paramQuantity || 0
                })
              }
            })
          }
        }
        

        
        if (methodSuggestions.length > 0) {
          operatorSuggestions.value = methodSuggestions
          showOperatorList.value = true
          activeOperatorIndex.value = 0
          
          setTimeout(() => {
            const position = getCursorPosition()
            if (position) {
              suggestionStyle.value = {
                top: `${position.top}px`,
                left: `${position.left}px`
              }
            }
          }, 0)
          return
        } else {
          showOperatorList.value = false
          return
        }
      } catch (error) {
        console.error('Error getting method list:', error)
        showOperatorList.value = false
        return
      }
    }
  }
  
  // 原有逻辑：从operatorMap中获取对应dataType的操作符列表（用于条件部分）
  let operators = operatorMap.value[dataType] || []
  
  // 如果直接找不到，尝试特殊处理
  if (operators.length === 0 && dataType) {
    
    // 检查dataType是否包含在operatorMap的键中（不区分大小写）
    const lowerDataType = dataType.toLowerCase()
    for (const key of Object.keys(operatorMap.value)) {
      if (key.toLowerCase() === lowerDataType) {
        operators = operatorMap.value[key] || []
        break
      }
    }
    
    // 尝试处理对象和列表类型
    if (operators.length === 0) {
      if (dataType.includes(".") && dataType.includes("List<")) {
        operators = operatorMap.value['List'] || []
      } else if (dataType.includes(".")) {
        operators = operatorMap.value['Object'] || []
      }
      
      // 如果是基本类型尝试精确匹配
      const basicTypes = ['String', 'Integer', 'Long', 'Double', 'Float', 'Boolean', 'Date']
      if (operators.length === 0 && basicTypes.includes(dataType)) {
        operators = operatorMap.value[dataType] || []
      }
    }
    
  }
  
  if (operators.length > 0) {
    // 转换为SuggestionItem格式
    operatorSuggestions.value = operators.map((op: any) => ({
      label: op.label,
      value: op.value,
      paramTypes: op.paramTypes,
      paramQuantity: op.paramQuantity
    }))
    
    
    // 显示操作符建议
    showOperatorList.value = true
    activeOperatorIndex.value = 0
    
    // 设置操作符弹窗位置
    setTimeout(() => {
      const position = getCursorPosition()
      if (position) {
        suggestionStyle.value = {
          top: `${position.top}px`,
          left: `${position.left}px`
        }
      }
    }, 0);
  } else {
    showOperatorList.value = false
  }
}

// 处理操作符label的显示
const processOperatorLabel = (operator: SuggestionItem) => {
  // 如果没有label，直接返回原label
  if (!operator.label) {
    return operator.label
  }
  
  
  // 从operator对象中获取paramQuantity，如果没有则尝试从description中提取
  let paramQuantity = -999 // 默认值，表示未找到
  
  // 首先尝试从operator对象的paramQuantity属性获取
  if (operator.paramQuantity !== undefined) {
    paramQuantity = operator.paramQuantity
  } 
  // 如果没有paramQuantity属性，尝试从description中提取
  else if (operator.description) {
    const match = operator.description.match(/-?\d+/)
    if (match) {
      paramQuantity = parseInt(match[0])
    }
  }
  
  
  // 检查是否包含括号
  if (operator.label.includes('(') && operator.label.includes(')')) {
    if (paramQuantity < 0) {
      // 参数为<0时：将整个()及其内容替换为("")
      const result = operator.label.replace(/\([^)]*\)/, '("")')
      return result
    } 
    if (paramQuantity > 0) {
      // 其他情况：将整个()及其内容替换为""
      const result = operator.label.replace(/\([^)]*\)/, '""')
      return result
    }
  }
  
  // 如果没有括号，返回原label
  return operator.label
}

// 插入操作符
const insertOperator = (operator: SuggestionItem) => {
  // 确保编辑器获取焦点
  if (editorRef.value) {
    editorRef.value.focus()
  }
  
  // 恢复上次保存的选择位置
  restoreSelection()
  
  // 插入操作符
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    
    // 处理操作符label并插入
    const processedLabel = processOperatorLabel(operator)
    const textNode = document.createTextNode(` ${processedLabel} `)
    range.insertNode(textNode)
    
    // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承任何样式
    const emptyTextNode = document.createTextNode('')
    range.setStartAfter(textNode)
    range.insertNode(emptyTextNode)
    
    // 移动光标到空文本节点
    range.setStart(emptyTextNode, 0)
    range.setEnd(emptyTextNode, 0)
    selection.removeAllRanges()
    selection.addRange(range)
    
    // 保存选择位置
    saveSelection()
  }
  
  // 隐藏操作符建议
  showOperatorList.value = false
  
  // 触发内容更新
  if (editorRef.value) {
    const content = editorRef.value.innerHTML
    emit('update:modelValue', content)
    emit('change', content)
    
    // 更新本地缓存和store
    localContent.value = content
    store.commit('setTextRule', {
      ruleUuid: props.uuid,
      text: content,
    })
  }
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 处理操作符建议的键盘事件
  if (showOperatorList.value) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        activeOperatorIndex.value = (activeOperatorIndex.value + 1) % operatorSuggestions.value.length
        // 确保选中项可见
        nextTick(() => {
          const container = document.querySelector('.operator-popup') as HTMLElement
          const activeItem = container?.querySelector('li.active') as HTMLElement
          if (container && activeItem) {
            const containerTop = container.scrollTop
            const containerBottom = containerTop + container.clientHeight
            const itemTop = activeItem.offsetTop
            const itemBottom = itemTop + activeItem.offsetHeight
            
            if (itemTop < containerTop) {
              container.scrollTop = itemTop
            } else if (itemBottom > containerBottom) {
              container.scrollTop = itemBottom - container.clientHeight
            }
          }
        })
        break
      case 'ArrowUp':
        event.preventDefault()
        activeOperatorIndex.value = (activeOperatorIndex.value - 1 + operatorSuggestions.value.length) % operatorSuggestions.value.length
        // 确保选中项可见
        nextTick(() => {
          const container = document.querySelector('.operator-popup') as HTMLElement
          const activeItem = container?.querySelector('li.active') as HTMLElement
          if (container && activeItem) {
            const containerTop = container.scrollTop
            const containerBottom = containerTop + container.clientHeight
            const itemTop = activeItem.offsetTop
            const itemBottom = itemTop + activeItem.offsetHeight
            
            if (itemTop < containerTop) {
              container.scrollTop = itemTop
            } else if (itemBottom > containerBottom) {
              container.scrollTop = itemBottom - container.clientHeight
            }
          }
        })
        break
      case 'Enter':
        event.preventDefault()
        if (operatorSuggestions.value.length > 0) {
          insertOperator(operatorSuggestions.value[activeOperatorIndex.value])
        }
        break
      case 'Escape':
        showOperatorList.value = false
        break
    }
    return
  }

  // 处理智能提示的键盘事件
  if (showSuggestions.value) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        activeSuggestionIndex.value = (activeSuggestionIndex.value + 1) % filteredSuggestions.value.length
        // 确保选中项可见
        nextTick(() => {
          const container = document.querySelector('.suggestions-popup') as HTMLElement
          const activeItem = container?.querySelector('li.active') as HTMLElement
          if (container && activeItem) {
            const containerTop = container.scrollTop
            const containerBottom = containerTop + container.clientHeight
            const itemTop = activeItem.offsetTop
            const itemBottom = itemTop + activeItem.offsetHeight
            
            if (itemTop < containerTop) {
              container.scrollTop = itemTop
            } else if (itemBottom > containerBottom) {
              container.scrollTop = itemBottom - container.clientHeight
            }
          }
        })
        break
      case 'ArrowUp':
        event.preventDefault()
        activeSuggestionIndex.value = (activeSuggestionIndex.value - 1 + filteredSuggestions.value.length) % filteredSuggestions.value.length
        // 确保选中项可见
        nextTick(() => {
          const container = document.querySelector('.suggestions-popup') as HTMLElement
          const activeItem = container?.querySelector('li.active') as HTMLElement
          if (container && activeItem) {
            const containerTop = container.scrollTop
            const containerBottom = containerTop + container.clientHeight
            const itemTop = activeItem.offsetTop
            const itemBottom = itemTop + activeItem.offsetHeight
            
            if (itemTop < containerTop) {
              container.scrollTop = itemTop
            } else if (itemBottom > containerBottom) {
              container.scrollTop = itemBottom - container.clientHeight
            }
          }
        })
        break
      case 'Enter':
        event.preventDefault()
        if (filteredSuggestions.value.length > 0) {
          insertSuggestion(filteredSuggestions.value[activeSuggestionIndex.value])
        }
        break
      case 'Escape':
        showSuggestions.value = false
        break
    }
    return
  }

  // 处理回车事件
  if (event.key === 'Enter') {
    event.preventDefault()
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      
      // 检查是否在条件部分或执行动作部分
      const isInConditionSection = checkIfInConditionSection(range)
      const isInActionSection = checkIfInActionSection(range)
      
      
      // 创建换行
      const br = document.createElement('br')
      range.deleteContents()
      range.insertNode(br)
      
      // 如果在条件部分，添加"并且"
      if (isInConditionSection) {
        // 先添加4个空格的缩进
        const indentText = document.createTextNode('    ')
        range.setStartAfter(br)
        range.insertNode(indentText)
        
        // 创建带样式的"并且"span元素
        const andSpan = document.createElement('span')
        andSpan.className = 'condition-and'
        andSpan.textContent = '并且'
        andSpan.style.color = '#444693'
        andSpan.style.fontWeight = 'bold'
        
        // 插入"并且"span
        range.setStartAfter(indentText)
        range.insertNode(andSpan)
        
        // 在"并且"后面添加一个空格（普通文本，无样式）
        const spaceText = document.createTextNode(' ')
        range.setStartAfter(andSpan)
        range.insertNode(spaceText)
        
        // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承span的样式
        const emptyTextNode = document.createTextNode('')
        range.setStartAfter(spaceText)
        range.insertNode(emptyTextNode)
        
        // 设置光标位置到空文本节点
        range.setStart(emptyTextNode, 0)
        range.setEnd(emptyTextNode, 0)
      } else if (isInActionSection) {
        // 如果在执行动作部分，添加"调用方法"
        // 先添加4个空格的缩进
        const indentText = document.createTextNode('    ')
        range.setStartAfter(br)
        range.insertNode(indentText)
        
        // 创建带样式的"调用方法"span元素
        const methodSpan = document.createElement('span')
        methodSpan.className = 'action-keyword'
        methodSpan.textContent = '调用方法'
        methodSpan.style.color = '#444693'
        methodSpan.style.fontWeight = 'bold'
        
        // 插入"调用方法"span
        range.setStartAfter(indentText)
        range.insertNode(methodSpan)
        
        // 在"调用方法"后面添加一个空格（普通文本，无样式）
        const spaceText = document.createTextNode(' ')
        range.setStartAfter(methodSpan)
        range.insertNode(spaceText)
        
        // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承span的样式
        const emptyTextNode = document.createTextNode('')
        range.setStartAfter(spaceText)
        range.insertNode(emptyTextNode)
        
        // 设置光标位置到空文本节点
        range.setStart(emptyTextNode, 0)
        range.setEnd(emptyTextNode, 0)
      } else {
        // 普通换行，设置光标到换行后
        range.setStartAfter(br)
        range.setEndAfter(br)
      }
      
      // 确保选择范围被正确设置
      selection.removeAllRanges()
      selection.addRange(range)
    }
    
    // 更新内容到store
    if (editorRef.value) {
      const content = editorRef.value.innerHTML
      emit('update:modelValue', content)
      emit('change', content)
      
      // 更新本地缓存和store
      localContent.value = content
      store.commit('setTextRule', {
        ruleUuid: props.uuid,
        text: content,
      })
    }
    
    calculateLineCount()
  }
}


// 处理输入事件
const handleInput = (event: Event) => {
  const content = (event.target as HTMLElement).innerHTML
  emit('update:modelValue', content)
  emit('change', content)
  calculateLineCount()

  // 调试快捷键检测
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    const text = range.startContainer.textContent || ''
    const cursorPosition = range.startOffset
    
    const wordBeforeCursor = text.slice(0, cursorPosition).split(/\s+/).pop() || ''
    
    // 检查是否在条件部分或执行动作部分
    const isInConditionSection = checkIfInConditionSection(range)
    const isInActionSection = checkIfInActionSection(range)
    
    // 当用户输入至少1个字符时触发智能提示（对关键词更敏感）
    if (wordBeforeCursor.startsWith('$') || wordBeforeCursor.length >= 1) {
      currentInputWord.value = wordBeforeCursor.replace('$', '')
      
      // 检查关键词匹配
      let hasKeywordMatch = false
      
      // 首先检查结构关键词（全局匹配）
      hasKeywordMatch = structureKeywordSuggestions.value.some(item => 
        item.label.includes(currentInputWord.value) || 
        item.value.includes(currentInputWord.value)
      )
      
      // 如果没有结构关键词匹配，再检查特定区域的关键词
      if (!hasKeywordMatch) {
        if (isInConditionSection) {
          // 在条件部分，检查逻辑关键词匹配
          hasKeywordMatch = logicKeywordSuggestions.value.some(item => 
            item.label.includes(currentInputWord.value) || 
            item.value.includes(currentInputWord.value)
          )
        } else if (isInActionSection) {
          // 在执行动作部分，检查执行动作关键词匹配
          hasKeywordMatch = actionKeywordSuggestions.value.some(item => 
            item.label.includes(currentInputWord.value) || 
            item.value.includes(currentInputWord.value)
          )
        }
      }
      
      if (filteredSuggestions.value.length > 0 && (hasKeywordMatch || wordBeforeCursor.length >= 2)) {
        showSuggestions.value = true
        activeSuggestionIndex.value = 0
        
        const position = getCursorPosition()
        if (position) {
          suggestionStyle.value = {
            top: `${position.top}px`,
            left: `${position.left}px`
          }
        }
      } else {
        showSuggestions.value = false
      }
    } else {
      showSuggestions.value = false
    }
  }
  // 更新本地缓存
  localContent.value = content
  store.commit('setTextRule', {
    ruleUuid: props.uuid,
    text: content,
  })
}

// 插入建议项
const insertSuggestion = (item: SuggestionItem) => {
  // 确保编辑器获取焦点
  if (editorRef.value) {
    editorRef.value.focus()
  }
  
  // 恢复上次保存的选择位置
  restoreSelection()
  
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    
    // 检查光标是否在带样式的span元素内部
    let currentNode = range.startContainer
    let isInStyledSpan = false
    let styledSpanParent = null
    
    // 向上查找是否在样式span内部
    while (currentNode && currentNode !== editorRef.value) {
      if (currentNode.nodeType === Node.ELEMENT_NODE) {
        const element = currentNode as HTMLElement
        if (element.classList.contains('condition-and') || 
            element.classList.contains('action-keyword') || 
            element.classList.contains('structure-keyword')) {
          isInStyledSpan = true
          styledSpanParent = element
          break
        }
      }
      currentNode = currentNode.parentNode as Node
    }
    
    // 如果在样式span内部，需要移出到span外部
    if (isInStyledSpan && styledSpanParent) {
      // 删除已输入的文本（从span开始位置删除）
      range.selectNode(styledSpanParent)
      const spanText = styledSpanParent.textContent || ''
      const inputLength = currentInputWord.value.length
      
      // 如果输入的文本长度小于span文本长度，说明是在原有关键词基础上输入
      if (inputLength < spanText.length) {
        // 保留原有关键词，只删除新输入的部分
        const originalKeyword = spanText.substring(0, spanText.length - inputLength)
        styledSpanParent.textContent = originalKeyword
        range.setStartAfter(styledSpanParent)
        range.setEndAfter(styledSpanParent)
      } else {
        // 完全替换
        range.deleteContents()
      }
    } else {
      // 正常情况，删除已输入的文本
      range.setStart(range.startContainer, range.startOffset - currentInputWord.value.length)
      range.deleteContents()
    }
    
    // 对于关键词，创建带样式的span元素
    if (item.type === 'logic' || item.type === 'action' || item.type === 'structure') {
      const keywordSpan = document.createElement('span')
      
      // 根据类型设置不同的className
      if (item.type === 'logic') {
        keywordSpan.className = 'condition-and'
      } else if (item.type === 'action') {
        keywordSpan.className = 'action-keyword'
      } else if (item.type === 'structure') {
        keywordSpan.className = 'structure-keyword'
      }
      
      keywordSpan.textContent = item.value
      keywordSpan.style.color = '#444693'
      keywordSpan.style.fontWeight = 'bold'
      
      // 插入带样式的关键词
      range.insertNode(keywordSpan)
      
      // 在关键词后面添加一个空格
      const spaceText = document.createTextNode(' ')
      range.setStartAfter(keywordSpan)
      range.insertNode(spaceText)
      
      // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承span的样式
      const emptyTextNode = document.createTextNode('')
      range.setStartAfter(spaceText)
      range.insertNode(emptyTextNode)
      
      // 移动光标到空文本节点
      range.setStart(emptyTextNode, 0)
      range.setEnd(emptyTextNode, 0)
      selection.removeAllRanges()
      selection.addRange(range)
    } else {
      // 普通建议项的处理
      const textNode = document.createTextNode(item.value)
      range.insertNode(textNode)
      
      // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承任何样式
      const emptyTextNode = document.createTextNode('')
      range.setStartAfter(textNode)
      range.insertNode(emptyTextNode)
      
      // 移动光标到空文本节点
      range.setStart(emptyTextNode, 0)
      range.setEnd(emptyTextNode, 0)
      selection.removeAllRanges()
      selection.addRange(range)
    }
    
    // 保存选择位置
    saveSelection()
    
    // 根据dataType类型，从operatorMap筛选对应操作符并弹出提示
    if (item.type && item.type !== 'logic' && item.type !== 'action' && item.type !== 'structure') {
      // 使用 nextTick 确保 DOM 已更新
      nextTick(() => {
        // 获取新的光标位置
        const position = getCursorPosition()
        if (position) {
          suggestionStyle.value = {
            top: `${position.top}px`,
            left: `${position.left}px`
          }
        }
        showOperatorSuggestions(item.type || '')
      })
    }
  }
  
  showSuggestions.value = false
  
  // 触发内容更新
  if (editorRef.value) {
    const content = editorRef.value.innerHTML
    emit('update:modelValue', content)
    emit('change', content)
    
    // 更新本地缓存和store
    localContent.value = content
    store.commit('setTextRule', {
      ruleUuid: props.uuid,
      text: content,
    })
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  const selection = window.getSelection()
  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    const textNode = document.createTextNode(text)
    range.deleteContents()
    range.insertNode(textNode)
    
    // 创建一个空的文本节点作为光标停靠点，确保后续输入不会继承任何样式
    const emptyTextNode = document.createTextNode('')
    range.setStartAfter(textNode)
    range.insertNode(emptyTextNode)
    
    // 移动光标到空文本节点
    range.setStart(emptyTextNode, 0)
    range.setEnd(emptyTextNode, 0)
    selection.removeAllRanges()
    selection.addRange(range)
    
    // 触发内容更新和保存到store
    if (editorRef.value) {
      const content = editorRef.value.innerHTML
      emit('update:modelValue', content)
      emit('change', content)
      calculateLineCount()
      
      // 更新本地缓存和store
      localContent.value = content
      store.commit('setTextRule', {
        ruleUuid: props.uuid,
        text: content,
      })
    }
    
    // 添加格式化逻辑
    setTimeout(() => {
      formatTextContent()
    }, 100)
  }
}

// 处理失焦事件
const handleBlur = () => {
  if (editorRef.value) {
    emit('update:modelValue', editorRef.value.innerHTML)
    
    // 添加格式化逻辑
    setTimeout(() => {
      formatTextContent()
    }, 100)
  }
}

// 获取最新编辑内容的方法
const getLatestContent = () => {
  if (editorRef.value) {
    return editorRef.value.innerText
  }
  return props.modelValue
}

// 定义事件
const emit = defineEmits(['update:modelValue', 'change'])

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (editorRef.value && newValue !== editorRef.value.innerHTML) {
    editorRef.value.innerHTML = newValue
    calculateLineCount() // 更新行号
  }
}, { immediate: true })

// 监听视图状态变化
// watch(() => props.viewStatus, (newVal) => {
//   if (newVal === 'ruleTextView') {
//     console.log('ruleTextView1')
//     // 在视图为规则文本时，获取标签和业务字段
//     if (!store.getters.textRule[props.uuid]) {
//       fetchTags()
//       fetchBusinessFields()
//     } else {
//       // 使用本地缓存内容
//       if (editorRef.value) {
//         editorRef.value.innerHTML = store.getters.textRule[props.uuid]
//         calculateLineCount()
//       }
//     }
//   }
// }, { immediate: true })

// 监听引擎ID变化（由于使用固定值，主要用于视图状态变化时的数据同步）
watch(() => props.uuid, (newUuid, oldUuid) => {
  // 当UUID变化且在规则文本视图时，确保数据已加载
  if (newUuid && newUuid !== oldUuid && props.viewStatus === 'ruleTextView') {
    // 直接调用函数，函数内部会处理缓存逻辑
    fetchTags()
    fetchBusinessFields()
  }
}, { immediate: false })

// 监听分类标签变化，默认选中第一个
watch(categoryTags, (newCategories) => {
  if (newCategories.length > 0 && !selectedCategory.value) {
    selectedCategory.value = newCategories[0].fieldType
  }
}, { immediate: true })

// 全局点击事件处理函数
const handleGlobalClick = (event: Event) => {
  const target = event.target as HTMLElement
  
  // 检查是否点击在智能提示弹窗上
  const suggestionPopup = document.querySelector('.suggestions-popup')
  const isClickInsideSuggestionPopup = suggestionPopup?.contains(target)
  
  // 检查是否点击在操作符弹窗上
  const operatorPopup = document.querySelector('.operator-popup')
  const isClickInsideOperatorPopup = operatorPopup?.contains(target)
  
  // 如果点击在弹窗内部，不做任何处理
  if (isClickInsideSuggestionPopup || isClickInsideOperatorPopup) {
    return
  }
  
  // 如果点击在弹窗外部的任何地方，都隐藏所有建议
  showSuggestions.value = false
  showOperatorList.value = false
}

// 组件挂载时初始化
onMounted(() => {
  // 初始化编辑器内容
  if (editorRef.value && props.modelValue) {
    editorRef.value.innerHTML = props.modelValue
    calculateLineCount()
  }
  
  // 只在规则文本视图时初始化数据
  if (props.viewStatus === 'ruleTextView') {
    // 使用本地缓存内容
    if (store.getters.textRule[props.uuid] && editorRef.value) {
      editorRef.value.innerHTML = store.getters.textRule[props.uuid]
      calculateLineCount()
    }
    
    // 直接调用函数，函数内部会处理缓存逻辑
    fetchTags()
    fetchBusinessFields()
  }
  
  // 添加全局点击事件监听器，点击弹框外部时隐藏所有智能提示
  document.addEventListener('click', handleGlobalClick)
})

// 添加清除本地缓存逻辑
const clearLocalContent = () => {
  localContent.value = ''
  store.commit('setTextRule', {
    ruleUuid: props.uuid,
    text: '',
  })
}

// 在适当的地方调用 clearLocalContent 方法，例如在组件卸载时
onBeforeUnmount(() => {
  localContent.value = ''
  // 移除全局点击事件监听器
  document.removeEventListener('click', handleGlobalClick)
})


// 暴露方法给父组件
defineExpose({
  getLatestContent,
  clearLocalContent,
  focus: () => {
    editorRef.value?.focus()
  }
})

// 智能提示弹窗中的列表项点击事件处理
const handleSuggestionClick = (item: SuggestionItem, event: MouseEvent) => {
  // 阻止事件冒泡，防止触发外部点击事件
  event.preventDefault()
  event.stopPropagation()
  
  // 插入建议项
  insertSuggestion(item)
}

// 操作符弹窗中的列表项点击事件处理
const handleOperatorClick = (operator: SuggestionItem, event: MouseEvent) => {
  // 阻止事件冒泡，防止触发外部点击事件
  event.preventDefault()
  event.stopPropagation()
  
  // 添加点击反馈
  const target = event.currentTarget as HTMLElement;
  if (target) {
    target.style.backgroundColor = '#bae7ff';
    setTimeout(() => {
      // 插入操作符
      insertOperator(operator)
    }, 150);
  } else {
    // 直接插入操作符
    insertOperator(operator)
  }
}

// 处理分类标签选中事件
const handleCategorySelect = (fieldType: string) => {
  selectedCategory.value = fieldType
}

// 检查是否在条件部分
const checkIfInConditionSection = (range: Range) => {
  try {
    if (!editorRef.value) return false
    
    // 创建一个临时的range来获取光标位置前的所有文本
    const tempRange = document.createRange()
    tempRange.selectNodeContents(editorRef.value)
    tempRange.setEnd(range.startContainer, range.startOffset)
    const textBeforeCursor = tempRange.toString()
    
    // 将文本按行分割
    const lines = textBeforeCursor.split('\n').map(line => line.trim())
    
    // 从光标位置向上逐行查找，找到第一个匹配的关键词
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i]
      if (!line) continue
      
      // 检查是否包含"执行动作"关键词
      if (line.includes('执行动作')) {
        return false
      }
      
      // 检查是否包含"条件"关键词
      if (line.includes('条件')) {
        return true
      }
    }
    
    // 如果都没找到，默认不添加"并且"
    return false
    
  } catch (error) {
    console.error('Error checking condition section:', error)
    return false
  }
}


</script>

<style lang="scss" scoped>
.text-editor-container {
  width: 100%;
  height: calc(100vh - 230px) !important;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.editor-wrapper {
  display: flex;
  height: 100%; 
  background-color: #fff;
  overflow: hidden;
}

.line-numbers {
  width: 40px;
  padding: 6px 8px;
  background-color: #f6f8fa;  // 改为浅灰色背景
  border-right: 1px solid #fff;  // 改为浅灰色边框
  overflow-y: hidden;
  user-select: none;
  font-family: monospace;
  font-size: 14px;
  color: #6e7781;  // 改为灰色文字
  text-align: right;
}

.line-number {
  height: 24px;
  line-height: 24px;
  padding-right: 4px;
}
.rich-editor{
  flex: 1;
  padding: 6px 16px;
  height:100%;
  overflow-y: auto;
  background-color: #fff;
  outline: none;
  font-size: 14px;
  line-height: 24px;
  color: #333;
  white-space: pre-wrap;

  // 选中文本的背景色
  ::selection {
    background-color: #e3f2fd;
  }

  // 当前行高亮
  div:focus-within {
    background-color: #e8f2ff !important;  // 浅蓝色高亮，与DRL一致
  }

  :deep(div) {
    padding: 0 8px;
    margin: 0 -8px;
    border-radius: 2px;
    background-color: #fff !important;
  }

  &.read-only {
    cursor: text;
    pointer-events: auto;
  }

  // 保持原有样式
  :deep(strong) {
    font-weight: bold;
  }

  :deep(font) {
    font-family: inherit;
  }

  :deep(br) {
    display: block;
    content: '';
    margin: 0;
    height: 24px; // 与行号高度保持一致
  }

  :deep([style*="color: #444693"]) {
    color: #444693 !important;
  }

  :deep([style*="background-color: #f6f5ec"]) {
    background-color: #fff !important;
  }
  
  // 条件部分"并且"的样式
  :deep(.condition-and) {
    color: #444693 !important;
    font-weight: bold !important;
    display: inline;
    background-color: transparent !important;
  }
  
  // 执行动作关键词的样式
  :deep(.action-keyword) {
    color: #444693 !important;
    font-weight: bold !important;
    display: inline;
    background-color: transparent !important;
  }
  
  // 结构关键词的样式
  :deep(.structure-keyword) {
    color: #444693 !important;
    font-weight: bold !important;
    display: inline;
    background-color: transparent !important;
  }
}

.tags-container {
  display: flex;
  flex-direction: column; // 确保垂直布局
  flex-wrap: wrap;
  padding: 8px 4px;
  background-color: #fff;
  overflow-y: hidden;
  align-content: flex-start;
  align-items: flex-start;
  height: 100%;
  width: 100%;
}

/* 添加自定义滚动条样式 */
.tags-container::-webkit-scrollbar {
  width: 4px;
}

.tags-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.tags-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.tags-container::-webkit-scrollbar-thumb:hover {
  background: #1890ff;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 1px;
  margin-bottom: 3px;
  padding: 3px 10px;
  color: #1890ff;
  border: 1px solid #d8effb;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  height: 22px;
  line-height: 1;
  white-space: nowrap;
  
  &:hover {
    color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  }
}

// 分类标签样式
.category-tag {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #e6f7ff;
    border-color: #40a9ff;
    color: #096dd9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }
  
  &.active {
    background-color: #e6f7ff;
    border-color: #40a9ff;
    color: #096dd9;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }
}

// 详细标签样式  
.detail-tag {
  background-color: #fff;
}

// 分类区域样式
.category-section {
  margin-bottom: 16px;
  width: 100%;
  
  .section-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
  }
}

// 详细标签区域样式
.detail-section {
  margin-bottom: 16px;
  width: 100%;
  clear: both; // 确保不会和上方元素在同一行
  
  .section-title {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
  }
}

// 提示信息样式
.hover-tip {
  font-size: 12px;
  color: #999;
  text-align: center;
  padding: 16px;
  font-style: italic;
}

.tags-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.tags-error {
  color: #ff4d4f;
  font-size: 12px;
  margin: 0 8px;
}

.suggestions-popup {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  min-width: 200px;
  scroll-behavior: smooth; // 添加平滑滚动效果
}

.suggestions-list {
  list-style: none;
  margin: 0;
  padding: 0;

  li {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &:hover, &.active {
      background-color: #e6f7ff;
    }
    
    // 逻辑关键词的特殊样式
    &.logic-suggestion {
      border-left: 3px solid #444693;
      
      .suggestion-label {
        color: #444693;
        font-weight: bold;
      }
    }
    
    // 执行动作关键词的特殊样式
    &.action-suggestion {
      border-left: 3px solid #444693;
      
      .suggestion-label {
        color: #444693;
        font-weight: bold;
      }
    }
    
    // 结构关键词的特殊样式
    &.structure-suggestion {
      border-left: 3px solid #52c41a;
      
      .suggestion-label {
        color: #52c41a;
        font-weight: bold;
      }
    }
  }
}

.suggestion-label {
  font-weight: 500;
}

.suggestion-desc {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}
</style> 
