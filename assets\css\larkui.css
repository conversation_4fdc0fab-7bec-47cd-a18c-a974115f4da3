.ant-tooltip {
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    font-size: 12px
}

.ant-tooltip-arrow {
    display: none
}

.ant-btn {
    line-height: 1.5715;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: var(--yq-ant-btn-shadow);
    cursor: pointer;
    transition: all .3s cubic-bezier(.645,.045,.355,1);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    color: var(--yq-ant-btn-default-color);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-btn-default-border)
}

.ant-btn>.anticon {
    line-height: 1
}

.ant-btn,.ant-btn:active,.ant-btn:focus {
    outline: 0
}

.ant-btn:not([disabled]):hover {
    -webkit-text-decoration: none;
    text-decoration: none
}

.ant-btn:not([disabled]):active {
    outline: 0;
    box-shadow: none
}

.ant-btn[disabled] {
    cursor: not-allowed
}

.ant-btn[disabled]>* {
    pointer-events: none
}

.ant-btn-lg {
    height: 40px;
    padding: 6.4px 15px;
    font-size: 16px;
    border-radius: 6px
}

.ant-btn-sm {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 6px
}

.ant-btn:focus,.ant-btn:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn:active {
    color: #000;
    border-color: #000
}

.ant-btn[disabled],.ant-btn[disabled]:active,.ant-btn[disabled]:focus,.ant-btn[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn>a:only-child {
    color: currentColor
}

.ant-btn>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn:focus,.ant-btn:hover {
    color: var(--yq-ant-primary-color-hover);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn:focus>a:only-child,.ant-btn:hover>a:only-child {
    color: currentColor
}

.ant-btn:focus>a:only-child:after,.ant-btn:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn:active {
    color: var(--yq-ant-primary-color-active);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn:active>a:only-child {
    color: currentColor
}

.ant-btn:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn[disabled],.ant-btn[disabled]:active,.ant-btn[disabled]:focus,.ant-btn[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn[disabled]:active>a:only-child,.ant-btn[disabled]:focus>a:only-child,.ant-btn[disabled]:hover>a:only-child,.ant-btn[disabled]>a:only-child {
    color: currentColor
}

.ant-btn[disabled]:active>a:only-child:after,.ant-btn[disabled]:focus>a:only-child:after,.ant-btn[disabled]:hover>a:only-child:after,.ant-btn[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn:active,.ant-btn:focus,.ant-btn:hover {
    -webkit-text-decoration: none;
    text-decoration: none;
    background: var(--yq-ant-btn-default-bg)
}

.ant-btn>span {
    display: inline-block
}

.ant-btn-primary:focus,.ant-btn-primary:hover {
    background: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-primary:active {
    background: #000;
    border-color: #000
}

.ant-btn-primary[disabled],.ant-btn-primary[disabled]:active,.ant-btn-primary[disabled]:focus,.ant-btn-primary[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-primary:active,.ant-btn-primary:focus,.ant-btn-primary:hover {
    background: var(--yq-yuque-green-600);
    border-color: var(--yq-yuque-green-600)
}

.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child) {
    border-right-color: var(--yq-yuque-green-500);
    border-left-color: var(--yq-yuque-green-500)
}

.ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child):disabled {
    border-color: var(--yq-ant-btn-default-border)
}

.ant-btn-group .ant-btn-primary:first-child:not(:last-child) {
    border-right-color: var(--yq-yuque-green-500)
}

.ant-btn-group .ant-btn-primary:first-child:not(:last-child)[disabled] {
    border-right-color: var(--yq-ant-btn-default-border)
}

.ant-btn-group .ant-btn-primary+.ant-btn-primary,.ant-btn-group .ant-btn-primary:last-child:not(:first-child) {
    border-left-color: var(--yq-yuque-green-500)
}

.ant-btn-group .ant-btn-primary+.ant-btn-primary[disabled],.ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled] {
    border-left-color: var(--yq-ant-btn-default-border)
}

.ant-btn-ghost {
    color: var(--yq-yuque-grey-900);
    background: transparent;
    border-color: var(--yq-ant-border-color-base)
}

.ant-btn-ghost:focus,.ant-btn-ghost:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-ghost:active {
    color: #000;
    border-color: #000
}

.ant-btn-ghost[disabled],.ant-btn-ghost[disabled]:active,.ant-btn-ghost[disabled]:focus,.ant-btn-ghost[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-ghost>a:only-child {
    color: currentColor
}

.ant-btn-ghost>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-ghost:focus,.ant-btn-ghost:hover {
    color: var(--yq-ant-primary-color-hover);
    background: transparent;
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-ghost:focus>a:only-child,.ant-btn-ghost:hover>a:only-child {
    color: currentColor
}

.ant-btn-ghost:focus>a:only-child:after,.ant-btn-ghost:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-ghost:active {
    color: var(--yq-ant-primary-color-active);
    background: transparent;
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-ghost:active>a:only-child {
    color: currentColor
}

.ant-btn-ghost:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-ghost[disabled],.ant-btn-ghost[disabled]:active,.ant-btn-ghost[disabled]:focus,.ant-btn-ghost[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-ghost[disabled]:active>a:only-child,.ant-btn-ghost[disabled]:focus>a:only-child,.ant-btn-ghost[disabled]:hover>a:only-child,.ant-btn-ghost[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-ghost[disabled]:active>a:only-child:after,.ant-btn-ghost[disabled]:focus>a:only-child:after,.ant-btn-ghost[disabled]:hover>a:only-child:after,.ant-btn-ghost[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dashed {
    border-style: dashed
}

.ant-btn-danger:focus,.ant-btn-danger:hover {
    background: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-danger:active {
    background: #000;
    border-color: #000
}

.ant-btn-danger[disabled],.ant-btn-danger[disabled]:active,.ant-btn-danger[disabled]:focus,.ant-btn-danger[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-danger:active,.ant-btn-danger:focus,.ant-btn-danger:hover {
    background: var(--yq-yuque-green-600);
    border-color: var(--yq-yuque-green-600)
}

.ant-btn-link:hover {
    background: var(--yq-ant-btn-link-hover-bg)
}

.ant-btn-text {
    color: var(--yq-yuque-grey-900)
}

.ant-btn-text:active,.ant-btn-text:focus,.ant-btn-text:hover {
    color: var(--yq-yuque-grey-900);
    background: var(--yq-ant-btn-text-hover-bg);
    border-color: transparent
}

.ant-btn-dangerous:focus,.ant-btn-dangerous:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-dangerous:active {
    color: #000;
    border-color: #000
}

.ant-btn-dangerous[disabled],.ant-btn-dangerous[disabled]:active,.ant-btn-dangerous[disabled]:focus,.ant-btn-dangerous[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-dangerous.ant-btn-text:focus,.ant-btn-dangerous.ant-btn-text:hover {
    background: var(--yq-ant-btn-text-hover-bg)
}

.ant-btn-icon-only {
    width: 32px;
    height: 32px;
    padding: 2.4px 0;
    font-size: 16px;
    border-radius: 6px;
    vertical-align: -1px
}

.ant-btn-icon-only>* {
    font-size: 16px
}

.ant-btn-icon-only.ant-btn-lg {
    width: 40px;
    height: 40px;
    padding: 4.9px 0;
    font-size: 18px;
    border-radius: 6px
}

.ant-btn-icon-only.ant-btn-lg>* {
    font-size: 18px
}

.ant-btn-icon-only.ant-btn-sm {
    width: 24px;
    height: 24px;
    padding: 0 0;
    font-size: 14px;
    border-radius: 6px
}

.ant-btn-icon-only.ant-btn-sm>* {
    font-size: 14px
}

.ant-btn-round {
    height: 32px;
    padding: 4px 16px;
    font-size: 14px;
    border-radius: 32px
}

.ant-btn-round.ant-btn-lg {
    height: 40px;
    padding: 6.4px 20px;
    font-size: 16px;
    border-radius: 40px
}

.ant-btn-round.ant-btn-sm {
    height: 24px;
    padding: 0 12px;
    font-size: 14px;
    border-radius: 24px
}

.ant-btn-round.ant-btn-icon-only {
    width: auto
}

.ant-btn-circle {
    min-width: 32px;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
    border-radius: 50%
}

.ant-btn-circle.ant-btn-lg {
    min-width: 40px;
    border-radius: 50%
}

.ant-btn-circle.ant-btn-sm {
    min-width: 24px;
    border-radius: 50%
}

.ant-btn:before {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    z-index: 1;
    display: none;
    background: var(--yq-ant-component-background);
    border-radius: inherit;
    opacity: .35;
    transition: opacity .2s;
    content: "";
    pointer-events: none
}

.ant-btn .anticon {
    transition: margin-left .3s cubic-bezier(.645,.045,.355,1)
}

.ant-btn .anticon.anticon-minus>svg,.ant-btn .anticon.anticon-plus>svg {
    shape-rendering: optimizeSpeed
}

.ant-btn.ant-btn-loading {
    position: relative
}

.ant-btn.ant-btn-loading:not([disabled]) {
    pointer-events: none
}

.ant-btn.ant-btn-loading:before {
    display: block
}

.ant-btn>.ant-btn-loading-icon {
    transition: all .3s cubic-bezier(.645,.045,.355,1)
}

.ant-btn>.ant-btn-loading-icon .anticon {
    padding-right: 8px;
    animation: none
}

.ant-btn>.ant-btn-loading-icon .anticon svg {
    animation: loadingCircle 1s linear infinite
}

.ant-btn-group {
    display: inline-flex
}

.ant-btn-group,.ant-btn-group>.ant-btn,.ant-btn-group>span>.ant-btn {
    position: relative
}

.ant-btn-group>.ant-btn:active,.ant-btn-group>.ant-btn:focus,.ant-btn-group>.ant-btn:hover,.ant-btn-group>span>.ant-btn:active,.ant-btn-group>span>.ant-btn:focus,.ant-btn-group>span>.ant-btn:hover {
    z-index: 2
}

.ant-btn-group>.ant-btn[disabled],.ant-btn-group>span>.ant-btn[disabled] {
    z-index: 0
}

.ant-btn-group .ant-btn-icon-only {
    font-size: 14px
}

.ant-btn-group-lg>.ant-btn,.ant-btn-group-lg>span>.ant-btn {
    height: 40px;
    padding: 6.4px 15px;
    font-size: 16px;
    border-radius: 0
}

.ant-btn-group-lg .ant-btn.ant-btn-icon-only {
    width: 40px;
    height: 40px;
    padding-right: 0;
    padding-left: 0
}

.ant-btn-group-sm>.ant-btn,.ant-btn-group-sm>span>.ant-btn {
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border-radius: 0
}

.ant-btn-group-sm>.ant-btn>.anticon,.ant-btn-group-sm>span>.ant-btn>.anticon {
    font-size: 14px
}

.ant-btn-group-sm .ant-btn.ant-btn-icon-only {
    width: 24px;
    height: 24px;
    padding-right: 0;
    padding-left: 0
}

.ant-btn+.ant-btn-group,.ant-btn-group+.ant-btn,.ant-btn-group+.ant-btn-group,.ant-btn-group .ant-btn+.ant-btn,.ant-btn-group .ant-btn+span,.ant-btn-group>span+span,.ant-btn-group span+.ant-btn {
    margin-left: -1px
}

.ant-btn-group .ant-btn-primary+.ant-btn:not(.ant-btn-primary):not([disabled]) {
    border-left-color: transparent
}

.ant-btn-group .ant-btn {
    border-radius: 0
}

.ant-btn-group>.ant-btn:first-child,.ant-btn-group>span:first-child>.ant-btn {
    margin-left: 0
}

.ant-btn-group>.ant-btn:only-child,.ant-btn-group>span:only-child>.ant-btn {
    border-radius: 6px
}

.ant-btn-group>.ant-btn:first-child:not(:last-child),.ant-btn-group>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-btn-group>.ant-btn:last-child:not(:first-child),.ant-btn-group>span:last-child:not(:first-child)>.ant-btn {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.ant-btn-group-sm>.ant-btn:only-child,.ant-btn-group-sm>span:only-child>.ant-btn {
    border-radius: 6px
}

.ant-btn-group-sm>.ant-btn:first-child:not(:last-child),.ant-btn-group-sm>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-btn-group-sm>.ant-btn:last-child:not(:first-child),.ant-btn-group-sm>span:last-child:not(:first-child)>.ant-btn {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.ant-btn-group>.ant-btn-group {
    float: left
}

.ant-btn-group>.ant-btn-group:not(:first-child):not(:last-child)>.ant-btn {
    border-radius: 0
}

.ant-btn-group>.ant-btn-group:first-child:not(:last-child)>.ant-btn:last-child {
    padding-right: 8px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.ant-btn-group>.ant-btn-group:last-child:not(:first-child)>.ant-btn:first-child {
    padding-left: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.ant-btn-group-rtl.ant-btn+.ant-btn-group,.ant-btn-group-rtl.ant-btn-group+.ant-btn,.ant-btn-group-rtl.ant-btn-group+.ant-btn-group,.ant-btn-group-rtl.ant-btn-group .ant-btn+.ant-btn,.ant-btn-group-rtl.ant-btn-group .ant-btn+span,.ant-btn-group-rtl.ant-btn-group>span+span,.ant-btn-group-rtl.ant-btn-group span+.ant-btn,.ant-btn-rtl.ant-btn+.ant-btn-group,.ant-btn-rtl.ant-btn-group+.ant-btn,.ant-btn-rtl.ant-btn-group+.ant-btn-group,.ant-btn-rtl.ant-btn-group .ant-btn+.ant-btn,.ant-btn-rtl.ant-btn-group .ant-btn+span,.ant-btn-rtl.ant-btn-group>span+span,.ant-btn-rtl.ant-btn-group span+.ant-btn {
    margin-right: -1px;
    margin-left: auto
}

.ant-btn-group.ant-btn-group-rtl {
    direction: rtl
}

.ant-btn-group-rtl.ant-btn-group>.ant-btn:first-child:not(:last-child),.ant-btn-group-rtl.ant-btn-group>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 0
}

.ant-btn-group-rtl.ant-btn-group>.ant-btn:last-child:not(:first-child),.ant-btn-group-rtl.ant-btn-group>span:last-child:not(:first-child)>.ant-btn {
    border-top-left-radius: 6px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 6px
}

.ant-btn-group-rtl.ant-btn-group-sm>.ant-btn:first-child:not(:last-child),.ant-btn-group-rtl.ant-btn-group-sm>span:first-child:not(:last-child)>.ant-btn {
    border-top-left-radius: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 0
}

.ant-btn-group-rtl.ant-btn-group-sm>.ant-btn:last-child:not(:first-child),.ant-btn-group-rtl.ant-btn-group-sm>span:last-child:not(:first-child)>.ant-btn {
    border-top-left-radius: 6px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 6px
}

.ant-btn:active>span,.ant-btn:focus>span {
    position: relative
}

.ant-btn>.anticon+span,.ant-btn>span+.anticon {
    margin-left: 8px
}

.ant-btn-background-ghost {
    color: var(--yq-ant-btn-default-ghost-color);
    background: var(--yq-ant-btn-default-ghost-bg)!important;
    border-color: var(--yq-ant-btn-default-ghost-border)
}

.ant-btn-background-ghost.ant-btn-primary {
    color: var(--yq-ant-btn-primary-bg);
    background: transparent;
    border-color: var(--yq-ant-btn-primary-bg);
    text-shadow: none
}

.ant-btn-background-ghost.ant-btn-primary>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-primary>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-primary:focus,.ant-btn-background-ghost.ant-btn-primary:hover {
    color: #0d0c0c;
    background: transparent;
    border-color: #0d0c0c
}

.ant-btn-background-ghost.ant-btn-primary:focus>a:only-child,.ant-btn-background-ghost.ant-btn-primary:hover>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-primary:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-primary:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-primary:active {
    color: #000;
    background: transparent;
    border-color: #000
}

.ant-btn-background-ghost.ant-btn-primary:active>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-primary:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-primary[disabled],.ant-btn-background-ghost.ant-btn-primary[disabled]:active,.ant-btn-background-ghost.ant-btn-primary[disabled]:focus,.ant-btn-background-ghost.ant-btn-primary[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-background-ghost.ant-btn-primary[disabled]:active>a:only-child,.ant-btn-background-ghost.ant-btn-primary[disabled]:focus>a:only-child,.ant-btn-background-ghost.ant-btn-primary[disabled]:hover>a:only-child,.ant-btn-background-ghost.ant-btn-primary[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-primary[disabled]:active>a:only-child:after,.ant-btn-background-ghost.ant-btn-primary[disabled]:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-primary[disabled]:hover>a:only-child:after,.ant-btn-background-ghost.ant-btn-primary[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-danger {
    color: var(--yq-ant-btn-danger-border);
    background: transparent;
    border-color: var(--yq-ant-btn-danger-border);
    text-shadow: none
}

.ant-btn-background-ghost.ant-btn-danger>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-danger>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-danger:focus,.ant-btn-background-ghost.ant-btn-danger:hover {
    color: #0d0c0c;
    background: transparent;
    border-color: #0d0c0c
}

.ant-btn-background-ghost.ant-btn-danger:focus>a:only-child,.ant-btn-background-ghost.ant-btn-danger:hover>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-danger:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-danger:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-danger:active {
    color: #000;
    background: transparent;
    border-color: #000
}

.ant-btn-background-ghost.ant-btn-danger:active>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-danger:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-danger[disabled],.ant-btn-background-ghost.ant-btn-danger[disabled]:active,.ant-btn-background-ghost.ant-btn-danger[disabled]:focus,.ant-btn-background-ghost.ant-btn-danger[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-background-ghost.ant-btn-danger[disabled]:active>a:only-child,.ant-btn-background-ghost.ant-btn-danger[disabled]:focus>a:only-child,.ant-btn-background-ghost.ant-btn-danger[disabled]:hover>a:only-child,.ant-btn-background-ghost.ant-btn-danger[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-danger[disabled]:active>a:only-child:after,.ant-btn-background-ghost.ant-btn-danger[disabled]:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-danger[disabled]:hover>a:only-child:after,.ant-btn-background-ghost.ant-btn-danger[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous {
    color: var(--yq-ant-btn-danger-border);
    background: transparent;
    border-color: var(--yq-ant-btn-danger-border);
    text-shadow: none
}

.ant-btn-background-ghost.ant-btn-dangerous>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous:focus,.ant-btn-background-ghost.ant-btn-dangerous:hover {
    color: #0d0c0c;
    background: transparent;
    border-color: #0d0c0c
}

.ant-btn-background-ghost.ant-btn-dangerous:focus>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous:hover>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous:active {
    color: #000;
    background: transparent;
    border-color: #000
}

.ant-btn-background-ghost.ant-btn-dangerous:active>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous[disabled],.ant-btn-background-ghost.ant-btn-dangerous[disabled]:active,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-background-ghost.ant-btn-dangerous[disabled]:active>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous[disabled]:active>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link {
    color: var(--yq-ant-btn-danger-border);
    background: transparent;
    border-color: transparent;
    text-shadow: none
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover {
    color: #0d0c0c
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active {
    color: #000
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover {
    color: var(--yq-yuque-green-600);
    background: transparent;
    border-color: transparent
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active {
    color: var(--yq-yuque-green-600);
    background: transparent;
    border-color: transparent
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled],.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover>a:only-child,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover>a:only-child:after,.ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-two-chinese-chars:first-letter {
    letter-spacing: .34em
}

.ant-btn-two-chinese-chars>:not(.anticon) {
    margin-right: -.34em;
    letter-spacing: .34em
}

.ant-btn-block {
    width: 100%
}

.ant-btn:empty {
    display: inline-block;
    width: 0;
    visibility: hidden;
    content: "\a0"
}

a.ant-btn {
    padding-top: .01px!important;
    line-height: 30px
}

a.ant-btn-lg {
    line-height: 38px
}

a.ant-btn-sm {
    line-height: 22px
}

.ant-btn-rtl {
    direction: rtl
}

.ant-btn-group-rtl.ant-btn-group .ant-btn-primary+.ant-btn-primary,.ant-btn-group-rtl.ant-btn-group .ant-btn-primary:last-child:not(:first-child) {
    border-right-color: var(--yq-yuque-green-500);
    border-left-color: var(--yq-ant-btn-default-border)
}

.ant-btn-group-rtl.ant-btn-group .ant-btn-primary+.ant-btn-primary[disabled],.ant-btn-group-rtl.ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled] {
    border-right-color: var(--yq-ant-btn-default-border);
    border-left-color: var(--yq-yuque-green-500)
}

.ant-btn-rtl.ant-btn>.ant-btn-loading-icon .anticon {
    padding-right: 0;
    padding-left: 8px
}

.ant-btn>.ant-btn-loading-icon:only-child .anticon {
    padding-right: 0;
    padding-left: 0
}

.ant-btn-rtl.ant-btn>.anticon+span,.ant-btn-rtl.ant-btn>span+.anticon {
    margin-right: 8px;
    margin-left: 0
}

.ant-btn {
    line-height: 1.15;
    box-shadow: none;
    text-shadow: none;
    border-radius: 6px;
    font-weight: 500
}

.ant-btn.ant-btn-link,.ant-btn.ant-btn-text {
    font-weight: 400
}

.ant-btn.ant-dropdown-trigger .anticon {
    height: 1em
}

.ant-btn.ant-dropdown-trigger {
    vertical-align: bottom
}

.ant-btn,.ant-btn:focus,.ant-btn:hover {
    box-shadow: none;
    background-image: none
}

.ant-btn.ant-btn-dangerous,.ant-btn.ant-btn-dashed,.ant-btn.ant-btn-link,.ant-btn.ant-btn-primary,.ant-btn.ant-btn-text {
    background-image: none
}

.ant-btn.ant-btn-danger {
    background: none;
    border: 1px solid var(--yq-border-primary);
    color: var(--yq-ant-error-color)
}

.ant-btn.ant-btn-danger:hover {
    border: 1px solid var(--yq-ant-error-color)
}

.ant-btn.ant-btn-danger:disabled {
    color: rgba(207,19,34,.25);
    border: 1px solid var(--yq-border-primary)
}

.ant-btn>.larkicon,.ant-btn>.larkui-icon {
    display: inline-block;
    vertical-align: middle
}

.ant-btn>.larkicon+.text,.ant-btn>.larkui-icon+.text {
    display: inline-block;
    vertical-align: middle;
    margin-left: 4px
}

.ant-btn-link:focus,.ant-btn-link:hover {
    color: #333
}

.ant-btn>.anticon+span,.ant-btn>span+.anticon {
    margin-left: 4px
}

.ant-btn-lg {
    font-size: 14px
}

.ant-btn,.ant-btn-default {
    color: var(--yq-ant-btn-default-color);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-btn-default-border)
}

.ant-btn-default:focus,.ant-btn-default:hover,.ant-btn:focus,.ant-btn:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-default:active,.ant-btn:active {
    color: #000;
    border-color: #000
}

.ant-btn-default[disabled],.ant-btn-default[disabled]:active,.ant-btn-default[disabled]:focus,.ant-btn-default[disabled]:hover,.ant-btn[disabled],.ant-btn[disabled]:active,.ant-btn[disabled]:focus,.ant-btn[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-default>a:only-child,.ant-btn>a:only-child {
    color: currentColor
}

.ant-btn-default>a:only-child:after,.ant-btn>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-default:focus,.ant-btn-default:hover,.ant-btn:focus,.ant-btn:hover {
    color: var(--yq-ant-primary-color-hover);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-default:focus>a:only-child,.ant-btn-default:hover>a:only-child,.ant-btn:focus>a:only-child,.ant-btn:hover>a:only-child {
    color: currentColor
}

.ant-btn-default:focus>a:only-child:after,.ant-btn-default:hover>a:only-child:after,.ant-btn:focus>a:only-child:after,.ant-btn:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-default:active,.ant-btn:active {
    color: var(--yq-ant-primary-color-active);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-default:active>a:only-child,.ant-btn:active>a:only-child {
    color: currentColor
}

.ant-btn-default:active>a:only-child:after,.ant-btn:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-default[disabled],.ant-btn-default[disabled]:active,.ant-btn-default[disabled]:focus,.ant-btn-default[disabled]:hover,.ant-btn[disabled],.ant-btn[disabled]:active,.ant-btn[disabled]:focus,.ant-btn[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-default[disabled]:active>a:only-child,.ant-btn-default[disabled]:focus>a:only-child,.ant-btn-default[disabled]:hover>a:only-child,.ant-btn-default[disabled]>a:only-child,.ant-btn[disabled]:active>a:only-child,.ant-btn[disabled]:focus>a:only-child,.ant-btn[disabled]:hover>a:only-child,.ant-btn[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-default[disabled]:active>a:only-child:after,.ant-btn-default[disabled]:focus>a:only-child:after,.ant-btn-default[disabled]:hover>a:only-child:after,.ant-btn-default[disabled]>a:only-child:after,.ant-btn[disabled]:active>a:only-child:after,.ant-btn[disabled]:focus>a:only-child:after,.ant-btn[disabled]:hover>a:only-child:after,.ant-btn[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover {
    color: var(--yq-text-primary);
    background: hsla(0,0%,100%,.12);
    border-color: transparent
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover>a:only-child {
    color: currentColor
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled],[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled],[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled],[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled],[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled],[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled],[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]>a:only-child {
    color: currentColor
}

[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-default:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):active[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):focus[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-btn-dangerous):not(.ant-btn-link):not(.ant-btn-text):hover[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-primary {
    color: var(--yq-ant-btn-primary-color);
    background: var(--yq-ant-btn-primary-bg);
    border-color: var(--yq-ant-btn-primary-bg);
    text-shadow: var(--yq-ant-btn-text-shadow);
    box-shadow: var(--yq-ant-btn-primary-shadow)
}

.ant-btn-primary>a:only-child {
    color: currentColor
}

.ant-btn-primary>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-primary:focus,.ant-btn-primary:hover {
    color: var(--yq-ant-btn-primary-color);
    background: var(--yq-ant-primary-color-hover);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-primary:focus>a:only-child,.ant-btn-primary:hover>a:only-child {
    color: currentColor
}

.ant-btn-primary:focus>a:only-child:after,.ant-btn-primary:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-primary:active {
    color: var(--yq-ant-btn-primary-color);
    background: var(--yq-ant-primary-color-active);
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-primary:active>a:only-child {
    color: currentColor
}

.ant-btn-primary:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-primary[disabled],.ant-btn-primary[disabled]:active,.ant-btn-primary[disabled]:focus,.ant-btn-primary[disabled]:hover {
    color: var(--yq-ant-btn-primary-color);
    background: var(--yq-ant-btn-primary-bg);
    border-color: var(--yq-ant-btn-primary-bg);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-primary[disabled]:active>a:only-child,.ant-btn-primary[disabled]:focus>a:only-child,.ant-btn-primary[disabled]:hover>a:only-child,.ant-btn-primary[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-primary[disabled]:active>a:only-child:after,.ant-btn-primary[disabled]:focus>a:only-child:after,.ant-btn-primary[disabled]:hover>a:only-child:after,.ant-btn-primary[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-primary[disabled] {
    opacity: .3
}

.ant-btn-background-ghost,.ant-btn-ghost {
    color: var(--yq-yuque-grey-900);
    background: transparent;
    border-color: var(--yq-ant-border-color-base)
}

.ant-btn-background-ghost:focus,.ant-btn-background-ghost:hover,.ant-btn-ghost:focus,.ant-btn-ghost:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-background-ghost:active,.ant-btn-ghost:active {
    color: #000;
    border-color: #000
}

.ant-btn-background-ghost[disabled],.ant-btn-background-ghost[disabled]:active,.ant-btn-background-ghost[disabled]:focus,.ant-btn-background-ghost[disabled]:hover,.ant-btn-ghost[disabled],.ant-btn-ghost[disabled]:active,.ant-btn-ghost[disabled]:focus,.ant-btn-ghost[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-background-ghost>a:only-child,.ant-btn-ghost>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost>a:only-child:after,.ant-btn-ghost>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost:focus,.ant-btn-background-ghost:hover,.ant-btn-ghost:focus,.ant-btn-ghost:hover {
    color: var(--yq-ant-primary-color-hover);
    background: transparent;
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-background-ghost:focus>a:only-child,.ant-btn-background-ghost:hover>a:only-child,.ant-btn-ghost:focus>a:only-child,.ant-btn-ghost:hover>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost:focus>a:only-child:after,.ant-btn-background-ghost:hover>a:only-child:after,.ant-btn-ghost:focus>a:only-child:after,.ant-btn-ghost:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost:active,.ant-btn-ghost:active {
    color: var(--yq-ant-primary-color-active);
    background: transparent;
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-background-ghost:active>a:only-child,.ant-btn-ghost:active>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost:active>a:only-child:after,.ant-btn-ghost:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-background-ghost[disabled],.ant-btn-background-ghost[disabled]:active,.ant-btn-background-ghost[disabled]:focus,.ant-btn-background-ghost[disabled]:hover,.ant-btn-ghost[disabled],.ant-btn-ghost[disabled]:active,.ant-btn-ghost[disabled]:focus,.ant-btn-ghost[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-background-ghost[disabled]:active>a:only-child,.ant-btn-background-ghost[disabled]:focus>a:only-child,.ant-btn-background-ghost[disabled]:hover>a:only-child,.ant-btn-background-ghost[disabled]>a:only-child,.ant-btn-ghost[disabled]:active>a:only-child,.ant-btn-ghost[disabled]:focus>a:only-child,.ant-btn-ghost[disabled]:hover>a:only-child,.ant-btn-ghost[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-background-ghost[disabled]:active>a:only-child:after,.ant-btn-background-ghost[disabled]:focus>a:only-child:after,.ant-btn-background-ghost[disabled]:hover>a:only-child:after,.ant-btn-background-ghost[disabled]>a:only-child:after,.ant-btn-ghost[disabled]:active>a:only-child:after,.ant-btn-ghost[disabled]:focus>a:only-child:after,.ant-btn-ghost[disabled]:hover>a:only-child:after,.ant-btn-ghost[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

[data-kumuhana=pouli] .ant-btn-background-ghost:active,[data-kumuhana=pouli] .ant-btn-background-ghost:focus,[data-kumuhana=pouli] .ant-btn-background-ghost:hover,[data-kumuhana=pouli] .ant-btn-ghost:active,[data-kumuhana=pouli] .ant-btn-ghost:focus,[data-kumuhana=pouli] .ant-btn-ghost:hover {
    background-color: hsla(0,0%,100%,.12)!important
}

[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled],[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:active,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:focus,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:hover,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled],[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:active,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:focus,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:hover,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled],[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:active,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:focus,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:hover,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled],[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:active,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:focus,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:hover,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled],[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:active,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:focus,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:hover,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled],[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:active,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:focus,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:active>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:hover>a:only-child,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]>a:only-child {
    color: currentColor
}

[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:active[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled]>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:active>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]:hover>a:only-child:after,[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

[data-kumuhana=pouli] .ant-btn-background-ghost:active[disabled],[data-kumuhana=pouli] .ant-btn-background-ghost:focus[disabled],[data-kumuhana=pouli] .ant-btn-background-ghost:hover[disabled],[data-kumuhana=pouli] .ant-btn-ghost:active[disabled],[data-kumuhana=pouli] .ant-btn-ghost:focus[disabled],[data-kumuhana=pouli] .ant-btn-ghost:hover[disabled] {
    background-color: transparent!important
}

.ant-btn-dashed {
    color: var(--yq-ant-btn-default-color);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-btn-default-border)
}

.ant-btn-dashed:focus,.ant-btn-dashed:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-dashed:active {
    color: #000;
    border-color: #000
}

.ant-btn-dashed[disabled],.ant-btn-dashed[disabled]:active,.ant-btn-dashed[disabled]:focus,.ant-btn-dashed[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-dashed>a:only-child {
    color: currentColor
}

.ant-btn-dashed>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dashed:focus,.ant-btn-dashed:hover {
    color: var(--yq-ant-primary-color-hover);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-dashed:focus>a:only-child,.ant-btn-dashed:hover>a:only-child {
    color: currentColor
}

.ant-btn-dashed:focus>a:only-child:after,.ant-btn-dashed:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dashed:active {
    color: var(--yq-ant-primary-color-active);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-dashed:active>a:only-child {
    color: currentColor
}

.ant-btn-dashed:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dashed[disabled],.ant-btn-dashed[disabled]:active,.ant-btn-dashed[disabled]:focus,.ant-btn-dashed[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-dashed[disabled]:active>a:only-child,.ant-btn-dashed[disabled]:focus>a:only-child,.ant-btn-dashed[disabled]:hover>a:only-child,.ant-btn-dashed[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-dashed[disabled]:active>a:only-child:after,.ant-btn-dashed[disabled]:focus>a:only-child:after,.ant-btn-dashed[disabled]:hover>a:only-child:after,.ant-btn-dashed[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-danger {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-btn-danger-bg);
    border-color: var(--yq-ant-btn-danger-bg);
    text-shadow: var(--yq-ant-btn-text-shadow);
    box-shadow: var(--yq-ant-btn-primary-shadow)
}

.ant-btn-danger>a:only-child {
    color: currentColor
}

.ant-btn-danger>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-danger:focus,.ant-btn-danger:hover {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-error-color-hover);
    border-color: var(--yq-ant-error-color-hover)
}

.ant-btn-danger:focus>a:only-child,.ant-btn-danger:hover>a:only-child {
    color: currentColor
}

.ant-btn-danger:focus>a:only-child:after,.ant-btn-danger:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-danger:active {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-error-color-active);
    border-color: var(--yq-ant-error-color-active)
}

.ant-btn-danger:active>a:only-child {
    color: currentColor
}

.ant-btn-danger:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-danger[disabled],.ant-btn-danger[disabled]:active,.ant-btn-danger[disabled]:focus,.ant-btn-danger[disabled]:hover {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-btn-danger-bg);
    border-color: var(--yq-ant-btn-danger-bg)
}

.ant-btn-danger[disabled] {
    opacity: .3
}

.ant-btn-danger[disabled],.ant-btn-danger[disabled]:active,.ant-btn-danger[disabled]:focus,.ant-btn-danger[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-danger[disabled]:active>a:only-child,.ant-btn-danger[disabled]:focus>a:only-child,.ant-btn-danger[disabled]:hover>a:only-child,.ant-btn-danger[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-danger[disabled]:active>a:only-child:after,.ant-btn-danger[disabled]:focus>a:only-child:after,.ant-btn-danger[disabled]:hover>a:only-child:after,.ant-btn-danger[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-link {
    color: var(--yq-ant-link-color);
    background: transparent;
    border-color: transparent;
    box-shadow: none
}

.ant-btn-link:focus,.ant-btn-link:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-link:active {
    color: #000;
    border-color: #000
}

.ant-btn-link[disabled],.ant-btn-link[disabled]:active,.ant-btn-link[disabled]:focus,.ant-btn-link[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-link>a:only-child {
    color: currentColor
}

.ant-btn-link>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-link:focus,.ant-btn-link:hover {
    color: var(--yq-ant-primary-color-hover);
    background: transparent;
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-link:focus>a:only-child,.ant-btn-link:hover>a:only-child {
    color: currentColor
}

.ant-btn-link:focus>a:only-child:after,.ant-btn-link:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-link:active {
    color: var(--yq-ant-primary-color-active);
    background: transparent;
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-link:active>a:only-child {
    color: currentColor
}

.ant-btn-link:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-link[disabled],.ant-btn-link[disabled]:active,.ant-btn-link[disabled]:focus,.ant-btn-link[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-link:hover {
    color: var(--yq-ant-link-hover-color);
    background: transparent
}

.ant-btn-link:active,.ant-btn-link:focus {
    color: var(--yq-ant-link-active-color)
}

.ant-btn-link:active,.ant-btn-link:focus,.ant-btn-link:hover {
    border-color: transparent
}

.ant-btn-link[disabled],.ant-btn-link[disabled]:active,.ant-btn-link[disabled]:focus,.ant-btn-link[disabled]:hover {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    border-color: transparent;
    text-shadow: none;
    box-shadow: none
}

.ant-btn-link[disabled]:active>a:only-child,.ant-btn-link[disabled]:focus>a:only-child,.ant-btn-link[disabled]:hover>a:only-child,.ant-btn-link[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-link[disabled]:active>a:only-child:after,.ant-btn-link[disabled]:focus>a:only-child:after,.ant-btn-link[disabled]:hover>a:only-child:after,.ant-btn-link[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-text {
    color: var(--yq-text-primary);
    background: transparent;
    border-color: transparent;
    box-shadow: none
}

.ant-btn-text:focus,.ant-btn-text:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-text:active {
    color: #000;
    border-color: #000
}

.ant-btn-text[disabled],.ant-btn-text[disabled]:active,.ant-btn-text[disabled]:focus,.ant-btn-text[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-text>a:only-child {
    color: currentColor
}

.ant-btn-text>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-text:focus,.ant-btn-text:hover {
    color: var(--yq-ant-primary-color-hover);
    background: transparent;
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-text:focus>a:only-child,.ant-btn-text:hover>a:only-child {
    color: currentColor
}

.ant-btn-text:focus>a:only-child:after,.ant-btn-text:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-text:active {
    color: var(--yq-ant-primary-color-active);
    background: transparent;
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-text:active>a:only-child {
    color: currentColor
}

.ant-btn-text:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-text[disabled],.ant-btn-text[disabled]:active,.ant-btn-text[disabled]:focus,.ant-btn-text[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-text:active,.ant-btn-text:focus,.ant-btn-text:hover {
    color: rgba(38,38,38,.7);
    background: transparent;
    border-color: transparent
}

[data-kumuhana=pouli] .ant-btn-text:active,[data-kumuhana=pouli] .ant-btn-text:focus,[data-kumuhana=pouli] .ant-btn-text:hover {
    color: hsla(0,0%,88.6%,.7)
}

.ant-btn-text[disabled],.ant-btn-text[disabled]:active,.ant-btn-text[disabled]:focus,.ant-btn-text[disabled]:hover {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    border-color: transparent;
    text-shadow: none;
    box-shadow: none
}

.ant-btn-text[disabled]:active>a:only-child,.ant-btn-text[disabled]:focus>a:only-child,.ant-btn-text[disabled]:hover>a:only-child,.ant-btn-text[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-text[disabled]:active>a:only-child:after,.ant-btn-text[disabled]:focus>a:only-child:after,.ant-btn-text[disabled]:hover>a:only-child:after,.ant-btn-text[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous {
    color: var(--yq-ant-error-color);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-error-color)
}

.ant-btn-dangerous>a:only-child {
    color: currentColor
}

.ant-btn-dangerous>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous:focus,.ant-btn-dangerous:hover {
    color: var(--yq-ant-error-color-hover);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-error-color-hover)
}

.ant-btn-dangerous:focus>a:only-child,.ant-btn-dangerous:hover>a:only-child {
    color: currentColor
}

.ant-btn-dangerous:focus>a:only-child:after,.ant-btn-dangerous:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

[data-kumuhana=pouli] .ant-btn-dangerous:focus,[data-kumuhana=pouli] .ant-btn-dangerous:hover {
    color: var(--yq-ant-error-color-hover);
    background: var(--yq-red-1);
    border-color: var(--yq-ant-error-color-hover)
}

[data-kumuhana=pouli] .ant-btn-dangerous:focus>a:only-child,[data-kumuhana=pouli] .ant-btn-dangerous:hover>a:only-child {
    color: currentColor
}

[data-kumuhana=pouli] .ant-btn-dangerous:focus>a:only-child:after,[data-kumuhana=pouli] .ant-btn-dangerous:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous:active {
    color: var(--yq-ant-error-color-active);
    background: var(--yq-ant-btn-default-bg);
    border-color: var(--yq-ant-error-color-active)
}

.ant-btn-dangerous:active>a:only-child {
    color: currentColor
}

.ant-btn-dangerous:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous[disabled],.ant-btn-dangerous[disabled]:active,.ant-btn-dangerous[disabled]:focus,.ant-btn-dangerous[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: transparent;
    border-color: var(--yq-ant-btn-disable-border);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-dangerous[disabled]:active>a:only-child,.ant-btn-dangerous[disabled]:focus>a:only-child,.ant-btn-dangerous[disabled]:hover>a:only-child,.ant-btn-dangerous[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-dangerous[disabled]:active>a:only-child:after,.ant-btn-dangerous[disabled]:focus>a:only-child:after,.ant-btn-dangerous[disabled]:hover>a:only-child:after,.ant-btn-dangerous[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-primary {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-btn-danger-bg);
    border-color: var(--yq-ant-btn-danger-bg);
    text-shadow: var(--yq-ant-btn-text-shadow);
    box-shadow: var(--yq-ant-btn-primary-shadow)
}

.ant-btn-dangerous.ant-btn-primary:focus,.ant-btn-dangerous.ant-btn-primary:hover {
    background: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-dangerous.ant-btn-primary:active {
    background: #000;
    border-color: #000
}

.ant-btn-dangerous.ant-btn-primary[disabled],.ant-btn-dangerous.ant-btn-primary[disabled]:active,.ant-btn-dangerous.ant-btn-primary[disabled]:focus,.ant-btn-dangerous.ant-btn-primary[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    background: var(--yq-ant-btn-disable-bg);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-dangerous.ant-btn-primary>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-primary>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-primary:focus,.ant-btn-dangerous.ant-btn-primary:hover {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-yuque-green-600);
    border-color: var(--yq-yuque-green-600)
}

.ant-btn-dangerous.ant-btn-primary:focus>a:only-child,.ant-btn-dangerous.ant-btn-primary:hover>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-primary:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-primary:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-primary:active {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-yuque-green-600);
    border-color: var(--yq-yuque-green-600)
}

.ant-btn-dangerous.ant-btn-primary:active>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-primary:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-primary[disabled],.ant-btn-dangerous.ant-btn-primary[disabled]:active,.ant-btn-dangerous.ant-btn-primary[disabled]:focus,.ant-btn-dangerous.ant-btn-primary[disabled]:hover {
    color: var(--yq-ant-btn-danger-color);
    background: var(--yq-ant-btn-danger-bg);
    border-color: var(--yq-ant-btn-danger-bg);
    text-shadow: none;
    box-shadow: none
}

.ant-btn-dangerous.ant-btn-primary[disabled]:active>a:only-child,.ant-btn-dangerous.ant-btn-primary[disabled]:focus>a:only-child,.ant-btn-dangerous.ant-btn-primary[disabled]:hover>a:only-child,.ant-btn-dangerous.ant-btn-primary[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-primary[disabled]:active>a:only-child:after,.ant-btn-dangerous.ant-btn-primary[disabled]:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-primary[disabled]:hover>a:only-child:after,.ant-btn-dangerous.ant-btn-primary[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-primary[disabled] {
    opacity: .3
}

.ant-btn-dangerous.ant-btn-link {
    color: var(--yq-ant-error-color);
    background: transparent;
    border-color: transparent;
    box-shadow: none
}

.ant-btn-dangerous.ant-btn-link:focus,.ant-btn-dangerous.ant-btn-link:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-dangerous.ant-btn-link:active {
    color: #000;
    border-color: #000
}

.ant-btn-dangerous.ant-btn-link[disabled],.ant-btn-dangerous.ant-btn-link[disabled]:active,.ant-btn-dangerous.ant-btn-link[disabled]:focus,.ant-btn-dangerous.ant-btn-link[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-dangerous.ant-btn-link>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-link>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-link:focus,.ant-btn-dangerous.ant-btn-link:hover {
    color: var(--yq-ant-primary-color-hover);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-dangerous.ant-btn-link:active {
    color: var(--yq-ant-primary-color-active);
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-dangerous.ant-btn-link[disabled],.ant-btn-dangerous.ant-btn-link[disabled]:active,.ant-btn-dangerous.ant-btn-link[disabled]:focus,.ant-btn-dangerous.ant-btn-link[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-dangerous.ant-btn-link:focus,.ant-btn-dangerous.ant-btn-link:hover {
    color: var(--yq-ant-error-color-hover);
    background: transparent;
    border-color: transparent
}

.ant-btn-dangerous.ant-btn-link:focus>a:only-child,.ant-btn-dangerous.ant-btn-link:hover>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-link:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-link:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-link:active {
    color: var(--yq-ant-error-color-active);
    background: transparent;
    border-color: transparent
}

.ant-btn-dangerous.ant-btn-link:active>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-link:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-link[disabled],.ant-btn-dangerous.ant-btn-link[disabled]:active,.ant-btn-dangerous.ant-btn-link[disabled]:focus,.ant-btn-dangerous.ant-btn-link[disabled]:hover {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    border-color: transparent;
    text-shadow: none;
    box-shadow: none
}

.ant-btn-dangerous.ant-btn-link[disabled]:active>a:only-child,.ant-btn-dangerous.ant-btn-link[disabled]:focus>a:only-child,.ant-btn-dangerous.ant-btn-link[disabled]:hover>a:only-child,.ant-btn-dangerous.ant-btn-link[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-link[disabled]:active>a:only-child:after,.ant-btn-dangerous.ant-btn-link[disabled]:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-link[disabled]:hover>a:only-child:after,.ant-btn-dangerous.ant-btn-link[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-text {
    color: var(--yq-ant-error-color);
    background: transparent;
    border-color: transparent;
    box-shadow: none
}

.ant-btn-dangerous.ant-btn-text:focus,.ant-btn-dangerous.ant-btn-text:hover {
    color: #0d0c0c;
    border-color: #0d0c0c
}

.ant-btn-dangerous.ant-btn-text:active {
    color: #000;
    border-color: #000
}

.ant-btn-dangerous.ant-btn-text[disabled],.ant-btn-dangerous.ant-btn-text[disabled]:active,.ant-btn-dangerous.ant-btn-text[disabled]:focus,.ant-btn-dangerous.ant-btn-text[disabled]:hover {
    background: var(--yq-ant-btn-disable-bg)
}

.ant-btn-dangerous.ant-btn-text>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-text>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-text:focus,.ant-btn-dangerous.ant-btn-text:hover {
    color: var(--yq-ant-primary-color-hover);
    border-color: var(--yq-ant-primary-color-hover)
}

.ant-btn-dangerous.ant-btn-text:active {
    color: var(--yq-ant-primary-color-active);
    background: transparent;
    border-color: var(--yq-ant-primary-color-active)
}

.ant-btn-dangerous.ant-btn-text[disabled],.ant-btn-dangerous.ant-btn-text[disabled]:active,.ant-btn-dangerous.ant-btn-text[disabled]:focus,.ant-btn-dangerous.ant-btn-text[disabled]:hover {
    color: var(--yq-ant-btn-disable-color);
    border-color: var(--yq-ant-btn-disable-border)
}

.ant-btn-dangerous.ant-btn-text:focus,.ant-btn-dangerous.ant-btn-text:hover {
    color: var(--yq-ant-error-color-hover);
    background: transparent;
    border-color: transparent
}

.ant-btn-dangerous.ant-btn-text:focus>a:only-child,.ant-btn-dangerous.ant-btn-text:hover>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-text:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-text:hover>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-text:active {
    color: var(--yq-ant-error-color-active);
    background: var(--yq-ant-btn-text-hover-bg);
    border-color: transparent
}

.ant-btn-dangerous.ant-btn-text:active>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-text:active>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-btn-dangerous.ant-btn-text[disabled],.ant-btn-dangerous.ant-btn-text[disabled]:active,.ant-btn-dangerous.ant-btn-text[disabled]:focus,.ant-btn-dangerous.ant-btn-text[disabled]:hover {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    border-color: transparent;
    text-shadow: none;
    box-shadow: none
}

.ant-btn-dangerous.ant-btn-text[disabled]:active>a:only-child,.ant-btn-dangerous.ant-btn-text[disabled]:focus>a:only-child,.ant-btn-dangerous.ant-btn-text[disabled]:hover>a:only-child,.ant-btn-dangerous.ant-btn-text[disabled]>a:only-child {
    color: currentColor
}

.ant-btn-dangerous.ant-btn-text[disabled]:active>a:only-child:after,.ant-btn-dangerous.ant-btn-text[disabled]:focus>a:only-child:after,.ant-btn-dangerous.ant-btn-text[disabled]:hover>a:only-child:after,.ant-btn-dangerous.ant-btn-text[disabled]>a:only-child:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: transparent;
    content: ""
}

.ant-input-affix-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s;
    display: inline-flex
}

.ant-input-affix-wrapper::-moz-placeholder {
    opacity: 1
}

.ant-input-affix-wrapper::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-input-affix-wrapper:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-affix-wrapper:placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-affix-wrapper:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-input-rtl .ant-input-affix-wrapper:hover {
    border-right-width: 0;
    border-left-width: 1px!important
}

.ant-input-affix-wrapper-focused,.ant-input-affix-wrapper:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

.ant-input-rtl .ant-input-affix-wrapper-focused,.ant-input-rtl .ant-input-affix-wrapper:focus {
    border-right-width: 0;
    border-left-width: 1px!important
}

html[data-kumuhana=pouli] .ant-input-affix-wrapper-focused,html[data-kumuhana=pouli] .ant-input-affix-wrapper:focus {
    border-color: var(--yq-theme)
}

.ant-input-affix-wrapper-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input-affix-wrapper-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input-affix-wrapper[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input-affix-wrapper[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input-affix-wrapper-borderless,.ant-input-affix-wrapper-borderless-disabled,.ant-input-affix-wrapper-borderless-focused,.ant-input-affix-wrapper-borderless:focus,.ant-input-affix-wrapper-borderless:hover,.ant-input-affix-wrapper-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-input-affix-wrapper {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-input-affix-wrapper-lg {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-input-affix-wrapper-sm {
    padding: 0 7px
}

.ant-input-affix-wrapper-rtl {
    direction: rtl
}

.ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important;
    z-index: 1
}

.ant-input-rtl .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-right-width: 0;
    border-left-width: 1px!important
}

.ant-input-search-with-button .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    z-index: 0
}

.ant-input-affix-wrapper-focused,.ant-input-affix-wrapper:focus {
    z-index: 1
}

.ant-input-affix-wrapper-disabled .ant-input[disabled] {
    background: transparent
}

.ant-input-affix-wrapper>input.ant-input {
    padding: 0;
    border: none;
    outline: none
}

.ant-input-affix-wrapper>input.ant-input:focus {
    box-shadow: none
}

.ant-input-affix-wrapper:before {
    width: 0;
    visibility: hidden;
    content: "\a0"
}

.ant-input-prefix,.ant-input-suffix {
    display: flex;
    flex: none;
    align-items: center
}

.ant-input-prefix {
    margin-right: 4px
}

.ant-input-suffix {
    margin-left: 4px
}

.ant-input-clear-icon {
    margin: 0 4px;
    color: var(--yq-ant-disabled-color);
    font-size: 12px;
    vertical-align: -1px;
    cursor: pointer;
    transition: color .3s
}

.ant-input-clear-icon:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-input-clear-icon:active {
    color: var(--yq-yuque-grey-900)
}

.ant-input-clear-icon-hidden {
    visibility: hidden
}

.ant-input-clear-icon:last-child {
    margin-right: 0
}

.ant-input-affix-wrapper-textarea-with-clear-btn {
    padding: 0!important;
    border: 0!important
}

.ant-input-affix-wrapper-textarea-with-clear-btn .ant-input-clear-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1
}

.ant-input {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    display: inline-block;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s
}

.ant-input::-moz-placeholder {
    opacity: 1
}

.ant-input::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-input:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-input:placeholder-shown {
    text-overflow: ellipsis
}

.ant-input:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-input-rtl .ant-input:hover {
    border-right-width: 0;
    border-left-width: 1px!important
}

.ant-input-focused,.ant-input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

.ant-input-rtl .ant-input-focused,.ant-input-rtl .ant-input:focus {
    border-right-width: 0;
    border-left-width: 1px!important
}

html[data-kumuhana=pouli] .ant-input-focused,html[data-kumuhana=pouli] .ant-input:focus {
    border-color: var(--yq-theme)
}

.ant-input-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input-borderless,.ant-input-borderless-disabled,.ant-input-borderless-focused,.ant-input-borderless:focus,.ant-input-borderless:hover,.ant-input-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-input-lg {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-input-sm {
    padding: 0 7px
}

.ant-input-rtl {
    direction: rtl
}

.ant-input-group {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    display: table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0
}

.ant-input-group[class*=col-] {
    float: none;
    padding-right: 0;
    padding-left: 0
}

.ant-input-group>[class*=col-] {
    padding-right: 8px
}

.ant-input-group>[class*=col-]:last-child {
    padding-right: 0
}

.ant-input-group-addon,.ant-input-group-wrap,.ant-input-group>.ant-input {
    display: table-cell
}

.ant-input-group-addon:not(:first-child):not(:last-child),.ant-input-group-wrap:not(:first-child):not(:last-child),.ant-input-group>.ant-input:not(:first-child):not(:last-child) {
    border-radius: 0
}

.ant-input-group-addon,.ant-input-group-wrap {
    width: 1px;
    white-space: nowrap;
    vertical-align: middle
}

.ant-input-group-wrap>* {
    display: block!important
}

.ant-input-group .ant-input {
    float: left;
    width: 100%;
    margin-bottom: 0;
    text-align: inherit
}

.ant-input-group .ant-input:focus,.ant-input-group .ant-input:hover {
    z-index: 1;
    border-right-width: 1px
}

.ant-input-search-with-button .ant-input-group .ant-input:hover {
    z-index: 0
}

.ant-input-group-addon {
    position: relative;
    padding: 0 11px;
    color: var(--yq-ant-input-color);
    font-weight: 400;
    font-size: 14px;
    text-align: center;
    background-color: var(--yq-ant-input-addon-bg);
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s
}

.ant-input-group-addon .ant-select {
    margin: -5px -11px
}

.ant-input-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    background-color: inherit;
    border: 1px solid transparent;
    box-shadow: none
}

.ant-input-group-addon .ant-select-focused .ant-select-selector,.ant-input-group-addon .ant-select-open .ant-select-selector {
    color: var(--yq-yuque-green-600)
}

.ant-input-group-addon:first-child,.ant-input-group-addon:first-child .ant-select .ant-select-selector,.ant-input-group>.ant-input:first-child,.ant-input-group>.ant-input:first-child .ant-select .ant-select-selector {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.ant-input-group>.ant-input-affix-wrapper:not(:first-child) .ant-input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.ant-input-group>.ant-input-affix-wrapper:not(:last-child) .ant-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.ant-input-group-addon:first-child {
    border-right: 0
}

.ant-input-group-addon:last-child {
    border-left: 0
}

.ant-input-group-addon:last-child,.ant-input-group-addon:last-child .ant-select .ant-select-selector,.ant-input-group>.ant-input:last-child,.ant-input-group>.ant-input:last-child .ant-select .ant-select-selector {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.ant-input-group-lg .ant-input,.ant-input-group-lg>.ant-input-group-addon {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-input-group-sm .ant-input,.ant-input-group-sm>.ant-input-group-addon {
    padding: 0 7px
}

.ant-input-group-lg .ant-select-single .ant-select-selector {
    height: 40px
}

.ant-input-group-sm .ant-select-single .ant-select-selector {
    height: 24px
}

.ant-input-group .ant-input-affix-wrapper:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.ant-input-group .ant-input-affix-wrapper:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.ant-input-search .ant-input-group .ant-input-affix-wrapper:not(:last-child) {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-input-group.ant-input-group-compact {
    display: block
}

.ant-input-group.ant-input-group-compact:before {
    display: table;
    content: ""
}

.ant-input-group.ant-input-group-compact:after {
    display: table;
    clear: both;
    content: ""
}

.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child),.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child),.ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child) {
    border-right-width: 1px
}

.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):focus,.ant-input-group.ant-input-group-compact-addon:not(:first-child):not(:last-child):hover,.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):focus,.ant-input-group.ant-input-group-compact-wrap:not(:first-child):not(:last-child):hover,.ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child):focus,.ant-input-group.ant-input-group-compact>.ant-input:not(:first-child):not(:last-child):hover {
    z-index: 1
}

.ant-input-group.ant-input-group-compact>* {
    display: inline-block;
    float: none;
    vertical-align: top;
    border-radius: 0
}

.ant-input-group.ant-input-group-compact>.ant-input-affix-wrapper,.ant-input-group.ant-input-group-compact>.ant-picker-range {
    display: inline-flex
}

.ant-input-group.ant-input-group-compact>:not(:last-child) {
    margin-right: -1px;
    border-right-width: 1px
}

.ant-input-group.ant-input-group-compact .ant-input {
    float: none
}

.ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input,.ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input,.ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input,.ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector {
    border-right-width: 1px;
    border-radius: 0
}

.ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:focus,.ant-input-group.ant-input-group-compact>.ant-cascader-picker .ant-input:hover,.ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:focus,.ant-input-group.ant-input-group-compact>.ant-input-group-wrapper .ant-input:hover,.ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:focus,.ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input:hover,.ant-input-group.ant-input-group-compact>.ant-select-focused,.ant-input-group.ant-input-group-compact>.ant-select>.ant-select-arrow,.ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector:focus,.ant-input-group.ant-input-group-compact>.ant-select>.ant-select-selector:hover {
    z-index: 1
}

.ant-input-group.ant-input-group-compact>.ant-cascader-picker:first-child .ant-input,.ant-input-group.ant-input-group-compact>.ant-select-auto-complete:first-child .ant-input,.ant-input-group.ant-input-group-compact>.ant-select:first-child>.ant-select-selector,.ant-input-group.ant-input-group-compact>:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-input-group.ant-input-group-compact>.ant-cascader-picker-focused:last-child .ant-input,.ant-input-group.ant-input-group-compact>.ant-cascader-picker:last-child .ant-input,.ant-input-group.ant-input-group-compact>.ant-select:last-child>.ant-select-selector,.ant-input-group.ant-input-group-compact>:last-child {
    border-right-width: 1px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.ant-input-group.ant-input-group-compact>.ant-select-auto-complete .ant-input {
    vertical-align: top
}

.ant-input-group.ant-input-group-compact .ant-input-group-wrapper+.ant-input-group-wrapper {
    margin-left: -1px
}

.ant-input-group.ant-input-group-compact .ant-input-group-wrapper+.ant-input-group-wrapper .ant-input-affix-wrapper,.ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search>.ant-input-group>.ant-input-group-addon>.ant-input-search-button {
    border-radius: 0
}

.ant-input-group.ant-input-group-compact .ant-input-group-wrapper:not(:last-child).ant-input-search>.ant-input-group>.ant-input {
    border-radius: 6px 0 0 6px
}

.ant-input-group-rtl .ant-input-group-addon:first-child,.ant-input-group>.ant-input-rtl:first-child {
    border-radius: 0 6px 6px 0
}

.ant-input-group-rtl .ant-input-group-addon:first-child {
    border-right: 1px solid var(--yq-ant-input-border-color);
    border-left: 0
}

.ant-input-group-rtl .ant-input-group-addon:last-child {
    border-right: 0;
    border-left: 1px solid var(--yq-ant-input-border-color)
}

.ant-input-group-rtl.ant-input-group-addon:last-child,.ant-input-group-rtl.ant-input-group .ant-input-affix-wrapper:not(:first-child),.ant-input-group-rtl.ant-input-group>.ant-input:last-child {
    border-radius: 6px 0 0 6px
}

.ant-input-group-rtl.ant-input-group .ant-input-affix-wrapper:not(:last-child) {
    border-radius: 0 6px 6px 0
}

.ant-input-group-rtl.ant-input-group.ant-input-group-compact>:not(:last-child) {
    margin-right: 0;
    margin-left: -1px;
    border-left-width: 1px
}

.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-cascader-picker:first-child .ant-input,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-select-auto-complete:first-child .ant-input,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-select:first-child>.ant-select-selector,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>:first-child {
    border-radius: 0 6px 6px 0
}

.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-cascader-picker-focused:last-child .ant-input,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-cascader-picker:last-child .ant-input,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-select-auto-complete:last-child .ant-input,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>.ant-select:last-child>.ant-select-selector,.ant-input-group-rtl.ant-input-group.ant-input-group-compact>:last-child {
    border-left-width: 1px;
    border-radius: 6px 0 0 6px
}

.ant-input-group.ant-input-group-compact .ant-input-group-wrapper-rtl+.ant-input-group-wrapper-rtl {
    margin-right: -1px;
    margin-left: 0
}

.ant-input-group.ant-input-group-compact .ant-input-group-wrapper-rtl:not(:last-child).ant-input-search>.ant-input-group>.ant-input {
    border-radius: 0 6px 6px 0
}

.ant-input-group-wrapper {
    display: inline-block;
    width: 100%;
    text-align: left;
    vertical-align: top
}

.ant-input-password-icon {
    color: var(--yq-ant-text-color-secondary);
    cursor: pointer;
    transition: all .3s
}

.ant-input-password-icon:hover {
    color: var(--yq-ant-input-icon-hover-color)
}

.ant-input[type=color] {
    height: 32px
}

.ant-input[type=color].ant-input-lg {
    height: 40px
}

.ant-input[type=color].ant-input-sm {
    height: 24px;
    padding-top: 3px;
    padding-bottom: 3px
}

.ant-input-textarea-show-count:after {
    float: right;
    color: var(--yq-ant-text-color-secondary);
    white-space: nowrap;
    content: attr(data-count);
    pointer-events: none
}

.ant-input-search .ant-input:focus,.ant-input-search .ant-input:hover {
    border-color: var(--yq-ant-input-hover-border-color)
}

.ant-input-search .ant-input:focus+.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary),.ant-input-search .ant-input:hover+.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {
    border-left-color: var(--yq-ant-input-hover-border-color)
}

.ant-input-search .ant-input-affix-wrapper {
    border-radius: 0
}

.ant-input-search .ant-input-lg {
    line-height: 1.5713
}

.ant-input-search>.ant-input-group>.ant-input-group-addon:last-child {
    left: -1px;
    padding: 0;
    border: 0
}

.ant-input-search>.ant-input-group>.ant-input-group-addon:last-child .ant-input-search-button {
    padding-top: 0;
    padding-bottom: 0;
    border-radius: 0 6px 6px 0
}

.ant-input-search>.ant-input-group>.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary) {
    color: var(--yq-ant-text-color-secondary)
}

.ant-input-search>.ant-input-group>.ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary).ant-btn-loading:before {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.ant-input-search-button {
    height: 32px
}

.ant-input-search-button:focus,.ant-input-search-button:hover {
    z-index: 1
}

.ant-input-search-large .ant-input-search-button {
    height: 40px
}

.ant-input-search-small .ant-input-search-button {
    height: 24px
}

.ant-input-group-rtl,.ant-input-group-wrapper-rtl {
    direction: rtl
}

.ant-input-affix-wrapper.ant-input-affix-wrapper-rtl>input.ant-input {
    border: none;
    outline: none
}

.ant-input-affix-wrapper-rtl .ant-input-prefix {
    margin: 0 0 0 4px
}

.ant-input-affix-wrapper-rtl .ant-input-suffix {
    margin: 0 4px 0 0
}

.ant-input-textarea-rtl {
    direction: rtl
}

.ant-input-textarea-rtl.ant-input-textarea-show-count:after {
    text-align: left
}

.ant-input-affix-wrapper-rtl .ant-input-clear-icon:last-child {
    margin-right: 4px;
    margin-left: 0
}

.ant-input-affix-wrapper-rtl .ant-input-clear-icon {
    right: auto;
    left: 8px
}

.ant-input-search-rtl {
    direction: rtl
}

.ant-input-search-rtl .ant-input:focus+.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary),.ant-input-search-rtl .ant-input:hover+.ant-input-group-addon .ant-input-search-button:not(.ant-btn-primary) {
    border-right-color: var(--yq-ant-input-hover-border-color);
    border-left-color: var(--yq-ant-border-color-base)
}

.ant-input-search-rtl>.ant-input-group>.ant-input-affix-wrapper-focused,.ant-input-search-rtl>.ant-input-group>.ant-input-affix-wrapper:hover {
    border-right-color: var(--yq-ant-input-hover-border-color)
}

.ant-input-search-rtl>.ant-input-group>.ant-input-group-addon {
    right: -1px;
    left: auto
}

.ant-input-search-rtl>.ant-input-group>.ant-input-group-addon .ant-input-search-button {
    border-radius: 6px 0 0 6px
}

@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active) {
    .ant-input {
        height: 32px
    }

    .ant-input-lg {
        height: 40px
    }

    .ant-input-sm {
        height: 24px
    }

    .ant-input-affix-wrapper>input.ant-input {
        height: auto
    }
}

.larkui-input.ant-input-focused,.larkui-input.ant-input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

.ant-input-rtl .larkui-input.ant-input-focused,.ant-input-rtl .larkui-input.ant-input:focus {
    border-right-width: 0;
    border-left-width: 1px!important
}

html[data-kumuhana=pouli] .larkui-input.ant-input-focused,html[data-kumuhana=pouli] .larkui-input.ant-input:focus {
    border-color: var(--yq-theme)
}

.larkui-input-search.ant-input-affix-wrapper-focused,.larkui-input-search.ant-input-affix-wrapper:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

.ant-input-rtl .larkui-input-search.ant-input-affix-wrapper-focused,.ant-input-rtl .larkui-input-search.ant-input-affix-wrapper:focus {
    border-right-width: 0;
    border-left-width: 1px!important
}

html[data-kumuhana=pouli] .larkui-input-search.ant-input-affix-wrapper-focused,html[data-kumuhana=pouli] .larkui-input-search.ant-input-affix-wrapper:focus {
    border-color: var(--yq-theme)
}

[data-kumuhana=pouli] .ant-input-affix-wrapper-disabled,[data-kumuhana=pouli] .ant-input[disabled] {
    opacity: .5
}

.ant-input-affix-wrapper .ant-input-prefix,.ant-input-affix-wrapper .ant-input-suffix,.ant-input-affix-wrapper>input.ant-input {
    background-color: transparent
}

.ant-input-affix-wrapper .ant-input-prefix {
    color: var(--yq-ant-text-color-secondary)
}

.ant-input-affix-wrapper .ant-input-prefix .anticon {
    color: var(--yq-icon-primary)
}

input:-internal-autofill-selected,input:-webkit-autofill,input:-webkit-autofill:focus {
    -webkit-transition: background-color 600000s 0s,color 600000s 0s;
    transition: background-color 600000s 0s,color 600000s 0s
}

.ant-popover .ant-popover-arrow {
    display: none
}

.ant-popover .ant-popover-inner {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-popover .ant-popover-content {
    min-width: 140px
}

.ant-popover .ant-menu {
    border: none;
    box-shadow: none;
    background: transparent
}

.ant-popover .ant-menu .ant-menu-item {
    color: var(--yq-yuque-grey-900)
}

.ant-popover .ant-menu .ant-menu-item:hover {
    color: var(--yq-yuque-grey-900);
    background-color: var(--yq-bg-primary-hover)
}

.ant-popover .ant-menu .ant-menu-item a,.ant-popover .ant-menu .ant-menu-item a:hover {
    color: var(--yq-yuque-grey-900)
}

.ant-popover .ant-menu .ant-menu-item-selected,.ant-popover .ant-menu .ant-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.ant-popover .ant-menu .ant-menu-item-divider {
    margin: 4px 12px
}

.ant-popover .ant-menu .text {
    margin-left: 8px
}

.ant-popover-message>.larkui-icon {
    position: absolute;
    top: 8px;
    color: var(--yq-ant-warning-color);
    font-size: 14px
}

.ant-popover-trigger.ant-popover-open {
    color: var(--yq-yuque-green-600)
}

.ant-popover-trigger.ant-popover-open.ant-btn-primary {
    color: var(--yq-white)
}

.larkui-popover-menu .ant-popover-inner-content {
    padding: 0
}

.larkui-popover-menu .ant-popover-content {
    min-width: 96px
}

.larkui-popover-menu .ant-menu {
    padding: 8px 0
}

.larkui-popover-menu .ant-menu .ant-menu-item {
    height: 32px;
    line-height: 32px
}

.larkui-popover-menu .ant-menu .ant-menu-item>a,.larkui-popover-menu .ant-menu .ant-menu-item>span {
    display: block;
    padding: 0 16px;
    margin-left: -16px;
    margin-right: -16px
}

.larkui-popover-no-arrow {
    padding-top: 0
}

.larkui-popover-no-arrow .ant-popover-arrow {
    display: none
}

.larkui-popover-no-arrow .ant-popover-placement-bottom,.larkui-popover-no-arrow .ant-popover-placement-bottomLeft,.larkui-popover-no-arrow .ant-popover-placement-bottomRight {
    padding-top: 0
}

.ant-popover-placement-bottom,.ant-popover-placement-bottomLeft,.ant-popover-placement-bottomRight {
    padding-top: 4px
}

.ant-modal-content {
    border-radius: 8px
}

.ant-modal-header {
    border-bottom: none;
    border-radius: 8px 8px 0 0
}

.ant-modal-footer {
    padding: 16px;
    border-radius: 0 0 8px 8px
}

.ant-modal-confirm-body>.larkui-icon {
    float: left;
    margin-right: 16px;
    font-size: 22px
}

.ant-modal-confirm-body>.larkui-icon+.ant-modal-confirm-title+.ant-modal-confirm-content {
    margin-left: 38px
}

.ant-modal-confirm-error .ant-modal-confirm-body>.larkui-icon {
    color: var(--yq-ant-error-color)
}

.ant-modal-confirm-confirm .ant-modal-confirm-body>.larkui-icon,.ant-modal-confirm-warning .ant-modal-confirm-body>.larkui-icon {
    color: var(--yq-ant-warning-color)
}

.ant-modal-confirm-info .ant-modal-confirm-body>.larkui-icon {
    color: var(--yq-ant-info-color)
}

.ant-modal-confirm-success .ant-modal-confirm-body>.larkui-icon {
    color: var(--yq-ant-success-color)
}

.larkui-input-search-close-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    transition: background-color .35s;
    background-color: var(--yq-yuque-grey-5);
    border-radius: 50%;
    text-align: center;
    cursor: pointer
}

.larkui-input-search-close-icon>.anticon {
    display: block;
    transform: scale(.6);
    font-size: 12px;
    font-weight: 700;
    color: var(--yq-white)
}

.larkui-input-search-close-icon:hover {
    background-color: var(--yq-yuque-grey-6)
}

.ant-dropdown-trigger.ant-input-affix-wrapper .ant-input-prefix .anticon-search {
    font-size: 14px;
    color: var(--yq-ant-text-color-secondary)
}

.ant-checkbox-checked:after,.ant-checkbox-inner {
    border-radius: 4px
}

@keyframes circle {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(1turn)
    }
}

.larkui-spin .larkui-spin-indicator {
    margin: 0 auto;
    width: 24px;
    height: 24px;
    border: 2px solid var(--yq-ant-text-color-secondary);
    border-color: var(--yq-ant-text-color-secondary) transparent transparent transparent;
    border-radius: 50%;
    animation: circle 1s linear infinite
}

.larkui-spin-block {
    display: block
}

.ant-empty .ant-empty-image {
    display: none
}

.ant-badge-dot {
    background: var(--yq-badge)
}

.ant-notification code {
    position: relative;
    top: -1px;
    display: inline-block;
    font-family: monospace;
    background-color: var(--yq-yuque-grey-2);
    border: 1px solid var(--yq-yuque-grey-4);
    padding: 0 4px;
    margin: 0 1px;
    border-radius: 2px 2px;
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
    word-wrap: break-word;
    word-break: break-all;
    text-indent: 0
}

.ant-menu {
    background: var(--yq-bg-primary)
}

.ant-menu.ant-menu-vertical .ant-menu-item {
    margin-top: 0;
    margin-bottom: 0
}

.ant-menu.ant-menu-horizontal>.ant-menu-item>a:before,.ant-menu.ant-menu-horizontal>.ant-menu-submenu>a:before {
    top: auto
}

.ant-menu>.ant-menu-item-divider {
    margin-left: 12px;
    margin-right: 12px
}

.ant-menu-horizontal {
    color: var(--yq-text-caption)
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: var(--yq-bg-primary-hover)
}

.ant-menu-submenu-popup {
    background: transparent
}

.ant-menu-submenu-popup>.ant-menu {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-menu-submenu-popup>.ant-menu .ant-menu {
    background-color: transparent
}

.ant-input-number {
    box-sizing: border-box;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    transition: all .3s;
    display: inline-block;
    width: 90px;
    margin: 0;
    padding: 0;
    border: 1px solid var(--yq-ant-border-color-base);
    border-radius: 6px
}

.ant-input-number::-moz-placeholder {
    opacity: 1
}

.ant-input-number::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-input-number:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-number:placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-number:hover {
    border-color: var(--yq-ant-input-hover-border-color)
}

.ant-input-number-focused,.ant-input-number:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme)
}

html[data-kumuhana=pouli] .ant-input-number-focused,html[data-kumuhana=pouli] .ant-input-number:focus {
    border-color: var(--yq-theme)
}

.ant-input-number[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input-number[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input-number-borderless,.ant-input-number-borderless-disabled,.ant-input-number-borderless-focused,.ant-input-number-borderless:focus,.ant-input-number-borderless:hover,.ant-input-number-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-input-number {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-input-number-lg {
    padding: 6.5px 11px
}

.ant-input-number-sm {
    padding: 0 7px
}

.ant-input-number-handler {
    position: relative;
    display: block;
    width: 100%;
    height: 50%;
    overflow: hidden;
    color: var(--yq-ant-text-color-secondary);
    font-weight: 700;
    line-height: 0;
    text-align: center;
    transition: all .1s linear
}

.ant-input-number-handler:active {
    background: var(--yq-ant-input-number-handler-active-bg)
}

.ant-input-number-handler:hover .ant-input-number-handler-down-inner,.ant-input-number-handler:hover .ant-input-number-handler-up-inner {
    color: var(--yq-ant-input-number-handler-hover-bg)
}

.ant-input-number-handler-down-inner,.ant-input-number-handler-up-inner {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    right: 4px;
    width: 12px;
    height: 12px;
    color: var(--yq-ant-text-color-secondary);
    line-height: 12px;
    transition: all .1s linear;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ant-input-number-handler-down-inner>*,.ant-input-number-handler-up-inner>* {
    line-height: 1
}

.ant-input-number-handler-down-inner svg,.ant-input-number-handler-up-inner svg {
    display: inline-block
}

.ant-input-number-handler-down-inner:before,.ant-input-number-handler-up-inner:before {
    display: none
}

.ant-input-number-handler-down-inner .ant-input-number-handler-down-inner-icon,.ant-input-number-handler-down-inner .ant-input-number-handler-up-inner-icon,.ant-input-number-handler-up-inner .ant-input-number-handler-down-inner-icon,.ant-input-number-handler-up-inner .ant-input-number-handler-up-inner-icon {
    display: block
}

.ant-input-number:hover {
    border-color: var(--yq-ant-input-number-hover-border-color);
    border-right-width: 1px!important
}

.ant-input-number:hover+.ant-form-item-children-icon {
    opacity: 0;
    transition: opacity .24s linear .24s
}

.ant-input-number-focused {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    box-shadow: none
}

.ant-input-number-focused,html[data-kumuhana=pouli] .ant-input-number-focused {
    border-color: var(--yq-theme)
}

.ant-input-number-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-input-number-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-input-number-disabled .ant-input-number-input {
    cursor: not-allowed
}

.ant-input-number-disabled .ant-input-number-handler-wrap,.ant-input-number-readonly .ant-input-number-handler-wrap {
    display: none
}

.ant-input-number-input {
    width: 100%;
    height: 30px;
    padding: 0 11px;
    text-align: left;
    background-color: transparent;
    border: 0;
    border-radius: 6px;
    outline: 0;
    transition: all .3s linear;
    -moz-appearance: textfield!important
}

.ant-input-number-input::-moz-placeholder {
    opacity: 1
}

.ant-input-number-input::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-input-number-input:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-number-input:placeholder-shown {
    text-overflow: ellipsis
}

.ant-input-number-input[type=number]::-webkit-inner-spin-button,.ant-input-number-input[type=number]::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none
}

.ant-input-number-lg {
    padding: 0;
    font-size: 16px
}

.ant-input-number-lg input {
    height: 38px
}

.ant-input-number-sm {
    padding: 0
}

.ant-input-number-sm input {
    height: 22px;
    padding: 0 7px
}

.ant-input-number-handler-wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 100%;
    background: var(--yq-ant-input-number-handler-bg);
    border-left: 1px solid var(--yq-ant-input-number-handler-border-color);
    border-radius: 0 6px 6px 0;
    opacity: 0;
    transition: opacity .24s linear .1s
}

.ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-down-inner,.ant-input-number-handler-wrap .ant-input-number-handler .ant-input-number-handler-up-inner {
    min-width: auto;
    margin-right: 0;
    font-size: 7px
}

.ant-input-number-borderless .ant-input-number-handler-wrap {
    border-left-width: 0
}

.ant-input-number-handler-wrap:hover .ant-input-number-handler {
    height: 40%
}

.ant-input-number:hover .ant-input-number-handler-wrap {
    opacity: 1
}

.ant-input-number-handler-up {
    border-top-right-radius: 6px;
    cursor: pointer
}

.ant-input-number-handler-up-inner {
    top: 50%;
    margin-top: -5px;
    text-align: center
}

.ant-input-number-handler-up:hover {
    height: 60%!important
}

.ant-input-number-handler-down {
    top: 0;
    border-top: 1px solid var(--yq-ant-border-color-base);
    border-bottom-right-radius: 6px;
    cursor: pointer
}

.ant-input-number-handler-down-inner {
    top: 50%;
    text-align: center;
    transform: translateY(-50%)
}

.ant-input-number-handler-down:hover {
    height: 60%!important
}

.ant-input-number-borderless .ant-input-number-handler-down {
    border-top-width: 0
}

.ant-input-number-handler-down-disabled,.ant-input-number-handler-up-disabled {
    cursor: not-allowed
}

.ant-input-number-handler-down-disabled:hover .ant-input-number-handler-down-inner,.ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner {
    color: var(--yq-ant-disabled-color)
}

.ant-input-number-borderless {
    box-shadow: none
}

.ant-input-number-out-of-range input {
    color: var(--yq-ant-error-color)
}

.ant-input-number-rtl {
    direction: rtl
}

.ant-input-number-rtl .ant-input-number-handler-wrap {
    right: auto;
    left: 0;
    border-right: 1px solid var(--yq-ant-input-number-handler-border-color);
    border-left: 0;
    border-radius: 6px 0 0 6px
}

.ant-input-number-rtl.ant-input-number-borderless .ant-input-number-handler-wrap {
    border-right-width: 0
}

.ant-input-number-rtl .ant-input-number-input {
    direction: ltr;
    text-align: right
}

.ant-input-number-focused,.ant-input-number:focus {
    box-shadow: none
}

.ant-input-number-handler-down-inner,.ant-input-number-handler-up-inner {
    color: var(--yq-icon-primary)
}

:root {
    --yq-ant-primary-color: var(--yq-theme);
    --yq-ant-primary-color-hover: var(--yq-yuque-green-700);
    --yq-ant-primary-color-active: var(--yq-yuque-green-700);
    --yq-ant-primary-color-outline: rgba(0,185,107,0.2);
    --yq-ant-primary-1: var(--yq-yuque-green-100);
    --yq-ant-primary-2: var(--yq-yuque-green-200);
    --yq-ant-primary-3: var(--yq-yuque-green-300);
    --yq-ant-primary-4: var(--yq-yuque-green-400);
    --yq-ant-primary-5: var(--yq-yuque-green-500);
    --yq-ant-primary-6: var(--yq-theme);
    --yq-ant-primary-7: var(--yq-yuque-green-700);
    --yq-ant-primary-color-deprecated-l-35: var(--yq-theme);
    --yq-ant-primary-color-deprecated-l-20: var(--yq-theme);
    --yq-ant-primary-color-deprecated-t-20: var(--yq-theme);
    --yq-ant-primary-color-deprecated-t-50: var(--yq-theme);
    --yq-ant-primary-color-deprecated-f-12: rgba(0,185,107,0.12);
    --yq-ant-primary-color-active-deprecated-f-30: rgba(218,246,234,0.3);
    --yq-ant-primary-color-active-deprecated-d-02: var(--yq-yuque-green-100);
    --yq-ant-success-color: var(--yq-yuque-green-600);
    --yq-ant-success-color-hover: var(--yq-yuque-green-500);
    --yq-ant-success-color-active: var(--yq-yuque-green-700);
    --yq-ant-success-color-outline: rgba(0,185,107,0.2);
    --yq-ant-success-color-deprecated-bg: var(--yq-yuque-green-100);
    --yq-ant-success-color-deprecated-border: var(--yq-yuque-green-300);
    --yq-ant-error-color: var(--yq-red-600);
    --yq-ant-error-color-hover: var(--yq-red-500);
    --yq-ant-error-color-active: var(--yq-red-700);
    --yq-ant-error-color-outline: rgba(223,42,63,0.2);
    --yq-ant-error-color-deprecated-bg: var(--yq-red-100);
    --yq-ant-error-color-deprecated-border: var(--yq-red-300);
    --yq-ant-warning-color: var(--yq-yellow-600);
    --yq-ant-warning-color-hover: var(--yq-yellow-500);
    --yq-ant-warning-color-active: var(--yq-yellow-700);
    --yq-ant-warning-color-outline: rgba(236,170,4,0.2);
    --yq-ant-warning-color-deprecated-bg: var(--yq-yellow-100);
    --yq-ant-warning-color-deprecated-border: var(--yq-yellow-300);
    --yq-ant-info-color: var(--yq-blue-600);
    --yq-ant-info-color-deprecated-bg: var(--yq-blue-100);
    --yq-ant-info-color-deprecated-border: var(--yq-blue-300);
    --yq-ant-body-background: var(--yq-white);
    --yq-ant-component-background: var(--yq-white);
    --yq-ant-popover-background: var(--yq-white);
    --yq-ant-popover-customize-border-color: var(--yq-ant-border-color-split);
    --yq-ant-text-color-secondary: rgba(0,0,0,0.45);
    --yq-ant-text-color-inverse: var(--yq-white);
    --yq-ant-icon-color: inherit;
    --yq-ant-icon-color-hover: rgba(0,0,0,0.75);
    --yq-ant-heading-color: rgba(0,0,0,0.85);
    --yq-ant-text-color-dark: hsla(0,0%,100%,0.85);
    --yq-ant-text-color-secondary-dark: hsla(0,0%,100%,0.65);
    --yq-ant-text-selection-bg: var(--yq-theme);
    --yq-ant-item-active-bg: var(--yq-yuque-green-100);
    --yq-ant-item-hover-bg: #f5f5f5;
    --yq-ant-link-color: var(--yq-text-link);
    --yq-ant-link-hover-color: var(--yq-text-link-hover);
    --yq-ant-link-active-color: var(--yq-blue-700);
    --yq-ant-border-color-base: #d9d9d9;
    --yq-ant-border-color-split: rgba(0,0,0,0.06);
    --yq-ant-border-color-inverse: var(--yq-white);
    --yq-ant-outline-color: var(--yq-theme);
    --yq-ant-background-color-light: #fafafa;
    --yq-ant-background-color-base: #f5f5f5;
    --yq-ant-disabled-color: var(--yq-text-disable);
    --yq-ant-disabled-bg: var(--yq-ant-background-color-base);
    --yq-ant-disabled-active-bg: var(--yq-black);
    --yq-ant-disabled-color-dark: hsla(0,0%,100%,0.35);
    --yq-ant-shadow-color: rgba(0,0,0,0.15);
    --yq-ant-shadow-color-inverse: var(--yq-white);
    --yq-ant-box-shadow-base: 0 1px 4px -2px rgba(0,0,0,0.13),0 2px 8px 0 rgba(0,0,0,0.08),0 8px 16px 4px rgba(0,0,0,0.04);
    --yq-ant-shadow-1-up: 0 -6px 16px -8px rgba(0,0,0,0.08),0 -9px 28px 0 rgba(0,0,0,0.05),0 -12px 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-down: 0 6px 16px -8px rgba(0,0,0,0.08),0 9px 28px 0 rgba(0,0,0,0.05),0 12px 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-left: -6px 0 16px -8px rgba(0,0,0,0.08),-9px 0 28px 0 rgba(0,0,0,0.05),-12px 0 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-1-right: 6px 0 16px -8px rgba(0,0,0,0.08),9px 0 28px 0 rgba(0,0,0,0.05),12px 0 48px 16px rgba(0,0,0,0.03);
    --yq-ant-shadow-2: 0 3px 6px -4px rgba(0,0,0,0.12),0 6px 16px 0 rgba(0,0,0,0.08),0 9px 28px 8px rgba(0,0,0,0.05);
    --yq-ant-btn-primary-color: #fff;
    --yq-ant-btn-primary-bg: var(--yq-theme);
    --yq-ant-btn-default-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-bg: var(--yq-white);
    --yq-ant-btn-default-border: var(--yq-border-primary);
    --yq-ant-btn-danger-color: #fff;
    --yq-ant-btn-danger-bg: var(--yq-ant-error-color);
    --yq-ant-btn-danger-border: var(--yq-ant-error-color);
    --yq-ant-btn-disable-color: var(--yq-ant-disabled-color);
    --yq-ant-btn-disable-bg: var(--yq-ant-disabled-bg);
    --yq-ant-btn-disable-border: var(--yq-ant-border-color-base);
    --yq-ant-btn-default-ghost-color: var(--yq-white);
    --yq-ant-btn-default-ghost-bg: transparent;
    --yq-ant-btn-default-ghost-border: var(--yq-white);
    --yq-ant-btn-group-border: var(--yq-yuque-green-500);
    --yq-ant-btn-link-hover-bg: transparent;
    --yq-ant-btn-text-hover-bg: rgba(0,0,0,0.018);
    --yq-ant-checkbox-color: var(--yq-theme);
    --yq-ant-checkbox-check-color: var(--yq-bg-primary);
    --yq-ant-checkbox-check-bg: var(--yq-ant-checkbox-check-color);
    --yq-ant-descriptions-bg: #fafafa;
    --yq-ant-descriptions-extra-color: var(--yq-yuque-grey-900);
    --yq-ant-divider-color: rgba(0,0,0,0.06);
    --yq-ant-dropdown-selected-color: var(--yq-theme);
    --yq-ant-dropdown-menu-submenu-disabled-bg: var(--yq-white);
    --yq-ant-dropdown-selected-bg: var(--yq-ant-item-active-bg);
    --yq-ant-radio-dot-color: var(--yq-theme);
    --yq-ant-radio-dot-disabled-color: rgba(0,0,0,0.2);
    --yq-ant-radio-solid-checked-color: var(--yq-white);
    --yq-ant-radio-button-bg: var(--yq-ant-btn-default-bg);
    --yq-ant-radio-button-checked-bg: var(--yq-ant-btn-default-bg);
    --yq-ant-radio-button-color: var(--yq-ant-btn-default-color);
    --yq-ant-radio-button-hover-color: var(--yq-yuque-green-500);
    --yq-ant-radio-button-active-color: var(--yq-yuque-green-700);
    --yq-ant-radio-disabled-button-checked-bg: var(--yq-black);
    --yq-ant-radio-disabled-button-checked-color: var(--yq-ant-disabled-color);
    --yq-ant-layout-body-background: #f0f2f5;
    --yq-ant-layout-header-background: #001529;
    --yq-ant-layout-header-color: var(--yq-yuque-grey-900);
    --yq-ant-layout-footer-background: var(--yq-ant-layout-body-background);
    --yq-ant-layout-sider-background: var(--yq-ant-layout-header-background);
    --yq-ant-layout-trigger-background: #002140;
    --yq-ant-layout-trigger-color: #fff;
    --yq-ant-layout-sider-background-light: #fff;
    --yq-ant-layout-trigger-background-light: #fff;
    --yq-ant-layout-trigger-color-light: var(--yq-yuque-grey-900);
    --yq-ant-dropdown-menu-bg: var(--yq-white);
    --yq-ant-label-required-color: var(--yq-red-600);
    --yq-ant-label-color: var(--yq-ant-heading-color);
    --yq-ant-form-warning-input-bg: var(--yq-ant-input-bg);
    --yq-ant-form-error-input-bg: var(--yq-ant-input-bg);
    --yq-ant-input-placeholder-color: #bfbfbf;
    --yq-ant-input-color: var(--yq-yuque-grey-900);
    --yq-ant-input-icon-color: var(--yq-ant-input-color);
    --yq-ant-input-border-color: var(--yq-border-primary);
    --yq-ant-input-bg: var(--yq-white);
    --yq-ant-input-number-hover-border-color: var(--yq-ant-input-hover-border-color);
    --yq-ant-input-number-handler-active-bg: #f4f4f4;
    --yq-ant-input-number-handler-hover-bg: var(--yq-yuque-green-500);
    --yq-ant-input-number-handler-bg: var(--yq-white);
    --yq-ant-input-number-handler-border-color: var(--yq-ant-border-color-base);
    --yq-ant-input-addon-bg: var(--yq-ant-background-color-light);
    --yq-ant-input-hover-border-color: var(--yq-yuque-green-500);
    --yq-ant-input-disabled-bg: var(--yq-bg-tertiary);
    --yq-ant-input-outline-offset: 0 0;
    --yq-ant-input-icon-hover-color: rgba(0,0,0,0.85);
    --yq-ant-input-disabled-color: var(--yq-yuque-grey-900);
    --yq-ant-mentions-dropdown-bg: var(--yq-white);
    --yq-ant-mentions-dropdown-menu-item-hover-bg: var(--yq-ant-mentions-dropdown-bg);
    --yq-ant-select-border-color: var(--yq-ant-border-color-base);
    --yq-ant-select-item-selected-color: var(--yq-yuque-grey-900);
    --yq-ant-select-dropdown-bg: var(--yq-white);
    --yq-ant-select-item-selected-bg: var(--yq-yuque-green-100);
    --yq-ant-select-item-active-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-select-background: var(--yq-white);
    --yq-ant-select-clear-background: var(--yq-ant-select-background);
    --yq-ant-select-selection-item-bg: var(--yq-ant-background-color-base);
    --yq-ant-select-selection-item-border-color: var(--yq-ant-border-color-split);
    --yq-ant-select-multiple-disabled-background: var(--yq-ant-input-disabled-bg);
    --yq-ant-select-multiple-item-disabled-color: #bfbfbf;
    --yq-ant-select-multiple-item-disabled-border-color: var(--yq-ant-border-color-base);
    --yq-ant-cascader-bg: var(--yq-white);
    --yq-ant-cascader-item-selected-bg: var(--yq-yuque-green-100);
    --yq-ant-cascader-menu-bg: var(--yq-white);
    --yq-ant-cascader-menu-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-anchor-bg: transparent;
    --yq-ant-anchor-border-color: var(--yq-ant-border-color-split);
    --yq-ant-tooltip-color: #fff;
    --yq-ant-tooltip-bg: rgba(0,0,0,0.75);
    --yq-ant-tooltip-arrow-color: var(--yq-ant-tooltip-bg);
    --yq-ant-popover-bg: var(--yq-white);
    --yq-ant-popover-color: var(--yq-yuque-grey-900);
    --yq-ant-popover-arrow-color: var(--yq-popover-bg);
    --yq-ant-popover-arrow-outer-color: var(--yq-popover-bg);
    --yq-ant-modal-header-bg: var(--yq-white);
    --yq-ant-modal-content-bg: var(--yq-white);
    --yq-ant-modal-heading-color: var(--yq-ant-heading-color);
    --yq-ant-modal-close-color: var(--yq-ant-text-color-secondary);
    --yq-ant-modal-footer-bg: transparent;
    --yq-ant-modal-footer-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-modal-mask-bg: rgba(0,0,0,0.45);
    --yq-ant-progress-default-color: var(--yq-yuque-green-600);
    --yq-ant-progress-remaining-color: rgba(0,0,0,0.04);
    --yq-ant-progress-info-text-color: var(--yq-yuque-grey-900);
    --yq-ant-progress-steps-item-bg: #f3f3f3;
    --yq-ant-progress-text-color: var(--yq-yuque-grey-900);
    --yq-ant-menu-bg: var(--yq-white);
    --yq-ant-menu-popup-bg: var(--yq-white);
    --yq-ant-menu-item-color: var(--yq-yuque-grey-900);
    --yq-ant-menu-inline-submenu-bg: var(--yq-ant-background-color-light);
    --yq-ant-menu-highlight-color: var(--yq-theme);
    --yq-ant-menu-highlight-danger-color: var(--yq-ant-error-color);
    --yq-ant-menu-item-active-bg: var(--yq-yuque-green-100);
    --yq-ant-menu-item-active-danger-bg: var(--yq-red-100);
    --yq-ant-menu-item-group-title-color: var(--yq-ant-text-color-secondary);
    --yq-ant-menu-dark-color: hsla(0,0%,100%,0.65);
    --yq-ant-menu-dark-danger-color: var(--yq-ant-error-color);
    --yq-ant-menu-dark-bg: var(--yq-ant-layout-header-background);
    --yq-ant-menu-dark-arrow-color: #fff;
    --yq-ant-menu-dark-inline-submenu-bg: #000c17;
    --yq-ant-menu-dark-highlight-color: #fff;
    --yq-ant-menu-dark-item-active-bg: var(--yq-theme);
    --yq-ant-menu-dark-item-active-danger-bg: var(--yq-ant-error-color);
    --yq-ant-menu-dark-selected-item-icon-color: var(--yq-white);
    --yq-ant-menu-dark-selected-item-text-color: var(--yq-white);
    --yq-ant-menu-dark-item-hover-bg: transparent;
    --yq-ant-table-bg: var(--yq-ant-body-background);
    --yq-ant-table-header-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-header-color: var(--yq-ant-heading-color);
    --yq-ant-table-header-sort-bg: var(--yq-ant-background-color-base);
    --yq-ant-table-body-sort-bg: #fafafa;
    --yq-ant-table-row-hover-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-selected-row-color: inherit;
    --yq-ant-table-selected-row-bg: var(--yq-yuque-green-100);
    --yq-ant-table-body-selected-sort-bg: var(--yq-yuque-green-100);
    --yq-ant-table-selected-row-hover-bg: var(--yq-primary-color-active-deprecated-d-02);
    --yq-ant-table-expanded-row-bg: #fbfbfb;
    --yq-ant-table-border-color: var(--yq-ant-border-color-split);
    --yq-ant-table-footer-bg: var(--yq-ant-background-color-light);
    --yq-ant-table-footer-color: var(--yq-ant-heading-color);
    --yq-ant-table-header-bg-sm: var(--yq-ant-table-header-bg);
    --yq-ant-table-header-cell-split-color: rgba(0,0,0,0.06);
    --yq-ant-table-header-sort-active-bg: rgba(0,0,0,0.04);
    --yq-ant-table-fixed-header-sort-active-bg: #f5f5f5;
    --yq-ant-table-header-filter-active-bg: rgba(0,0,0,0.04);
    --yq-ant-table-filter-btns-bg: inherit;
    --yq-ant-table-filter-dropdown-bg: var(--yq-white);
    --yq-ant-table-expand-icon-bg: var(--yq-white);
    --yq-ant-table-sticky-scroll-bar-bg: rgba(0,0,0,0.35);
    --yq-ant-tag-default-bg: var(--yq-ant-background-color-light);
    --yq-ant-tag-default-color: var(--yq-yuque-grey-900);
    --yq-ant-picker-bg: var(--yq-white);
    --yq-ant-picker-basic-cell-hover-color: var(--yq-ant-item-hover-bg);
    --yq-ant-picker-basic-cell-active-with-range-color: var(--yq-yuque-green-100);
    --yq-ant-picker-basic-cell-hover-with-range-color: var(--yq-primary-color-deprecated-l-35);
    --yq-ant-picker-basic-cell-disabled-bg: rgba(0,0,0,0.04);
    --yq-ant-picker-border-color: var(--yq-ant-border-color-split);
    --yq-ant-picker-date-hover-range-border-color: var(--yq-primary-color-deprecated-l-20);
    --yq-ant-picker-date-hover-range-color: var(--yq-ant-picker-basic-cell-hover-with-range-color);
    --yq-ant-calendar-bg: var(--yq-white);
    --yq-ant-calendar-input-bg: var(--yq-ant-input-bg);
    --yq-ant-calendar-border-color: var(--yq-white);
    --yq-ant-calendar-item-active-bg: var(--yq-ant-item-active-bg);
    --yq-ant-calendar-column-active-bg: var(--yq-primary-color-active-deprecated-f-30);
    --yq-ant-calendar-full-bg: var(--yq-ant-calendar-bg);
    --yq-ant-calendar-full-panel-bg: var(--yq-ant-calendar-full-bg);
    --yq-ant-badge-text-color: #fff;
    --yq-ant-badge-color: var(--yq-badge);
    --yq-ant-rate-star-color: var(--yq-yellow-600);
    --yq-ant-rate-star-bg: var(--yq-ant-border-color-split);
    --yq-ant-card-head-color: var(--yq-ant-heading-color);
    --yq-ant-card-head-background: transparent;
    --yq-ant-card-actions-background: var(--yq-white);
    --yq-ant-card-skeleton-bg: #cfd8dc;
    --yq-ant-card-background: var(--yq-white);
    --yq-ant-card-head-extra-color: var(--yq-yuque-grey-900);
    --yq-ant-comment-bg: inherit;
    --yq-ant-comment-author-name-color: var(--yq-ant-text-color-secondary);
    --yq-ant-comment-author-time-color: #ccc;
    --yq-ant-comment-action-color: var(--yq-ant-text-color-secondary);
    --yq-ant-comment-action-hover-color: #595959;
    --yq-ant-tabs-card-head-background: var(--yq-ant-background-color-light);
    --yq-ant-tabs-card-active-color: var(--yq-theme);
    --yq-ant-tabs-ink-bar-color: var(--yq-theme);
    --yq-ant-tabs-highlight-color: var(--yq-theme);
    --yq-ant-tabs-hover-color: var(--yq-yuque-green-500);
    --yq-ant-tabs-active-color: var(--yq-yuque-green-700);
    --yq-ant-back-top-color: #fff;
    --yq-ant-back-top-bg: var(--yq-ant-text-color-secondary);
    --yq-ant-back-top-hover-bg: var(--yq-yuque-grey-900);
    --yq-ant-avatar-bg: #ccc;
    --yq-ant-avatar-color: #fff;
    --yq-ant-avatar-group-border-color: #fff;
    --yq-ant-switch-color: var(--yq-theme);
    --yq-ant-switch-bg: var(--yq-white);
    --yq-ant-switch-shadow-color: rgba(0,35,11,0.2);
    --yq-ant-pagination-item-bg: var(--yq-white);
    --yq-ant-pagination-item-bg-active: var(--yq-white);
    --yq-ant-pagination-item-link-bg: var(--yq-white);
    --yq-ant-pagination-item-disabled-color-active: var(--yq-ant-disabled-color);
    --yq-ant-pagination-item-disabled-bg-active: var(--yq-black);
    --yq-ant-pagination-item-input-bg: var(--yq-white);
    --yq-ant-page-header-back-color: #000;
    --yq-ant-page-header-ghost-bg: inherit;
    --yq-ant-breadcrumb-base-color: var(--yq-ant-text-color-secondary);
    --yq-ant-breadcrumb-last-item-color: var(--yq-yuque-grey-900);
    --yq-ant-breadcrumb-link-color: var(--yq-ant-text-color-secondary);
    --yq-ant-breadcrumb-link-color-hover: var(--yq-yuque-grey-900);
    --yq-ant-breadcrumb-separator-color: var(--yq-ant-text-color-secondary);
    --yq-ant-slider-rail-background-color: var(--yq-ant-background-color-base);
    --yq-ant-slider-rail-background-color-hover: #e1e1e1;
    --yq-ant-slider-track-background-color: var(--yq-yuque-green-300);
    --yq-ant-slider-track-background-color-hover: var(--yq-yuque-green-400);
    --yq-ant-slider-handle-background-color: var(--yq-white);
    --yq-ant-slider-handle-color: var(--yq-yuque-green-300);
    --yq-ant-slider-handle-color-hover: var(--yq-yuque-green-400);
    --yq-ant-slider-handle-color-focus: var(--yq-primary-color-deprecated-t-20);
    --yq-ant-slider-handle-color-focus-shadow: var(--yq-primary-color-deprecated-f-12);
    --yq-ant-slider-handle-color-tooltip-open: var(--yq-theme);
    --yq-ant-slider-dot-border-color: var(--yq-ant-border-color-split);
    --yq-ant-slider-dot-border-color-active: var(--yq-primary-color-deprecated-t-50);
    --yq-ant-slider-disabled-color: var(--yq-ant-disabled-color);
    --yq-ant-slider-disabled-background-color: var(--yq-white);
    --yq-ant-tree-bg: var(--yq-white);
    --yq-ant-tree-directory-selected-color: #fff;
    --yq-ant-tree-directory-selected-bg: var(--yq-theme);
    --yq-ant-tree-node-hover-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-tree-node-selected-bg: var(--yq-yuque-green-200);
    --yq-ant-collapse-header-bg: var(--yq-ant-background-color-light);
    --yq-ant-collapse-content-bg: var(--yq-bg-primary);
    --yq-ant-skeleton-color: hsla(0,0%,74.5%,0.2);
    --yq-ant-skeleton-to-color: hsla(0,0%,50.6%,0.24);
    --yq-ant-transfer-disabled-bg: var(--yq-ant-disabled-bg);
    --yq-ant-transfer-item-hover-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-transfer-item-selected-hover-bg: var(--yq-primary-color-active-deprecated-d-02);
    --yq-ant-message-notice-content-bg: var(--yq-white);
    --yq-ant-alert-success-border-color: var(--yq-ant-success-color-deprecated-border);
    --yq-ant-alert-success-bg-color: var(--yq-ant-success-color-deprecated-bg);
    --yq-ant-alert-success-icon-color: var(--yq-ant-success-color);
    --yq-ant-alert-info-border-color: var(--yq-ant-info-color-deprecated-border);
    --yq-ant-alert-info-bg-color: var(--yq-ant-info-color-deprecated-bg);
    --yq-ant-alert-info-icon-color: var(--yq-ant-info-color);
    --yq-ant-alert-warning-border-color: var(--yq-ant-warning-color-deprecated-border);
    --yq-ant-alert-warning-bg-color: var(--yq-ant-warning-color-deprecated-bg);
    --yq-ant-alert-warning-icon-color: var(--yq-ant-warning-color);
    --yq-ant-alert-error-border-color: var(--yq-ant-error-color-deprecated-border);
    --yq-ant-alert-error-bg-color: var(--yq-ant-error-color-deprecated-bg);
    --yq-ant-alert-error-icon-color: var(--yq-ant-error-color);
    --yq-ant-alert-message-color: var(--yq-ant-heading-color);
    --yq-ant-alert-text-color: var(--yq-yuque-grey-900);
    --yq-ant-alert-close-color: var(--yq-ant-text-color-secondary);
    --yq-ant-alert-close-hover-color: var(--yq-ant-icon-color-hover);
    --yq-ant-list-header-background: transparent;
    --yq-ant-list-footer-background: transparent;
    --yq-ant-list-customize-card-bg: var(--yq-white);
    --yq-ant-drawer-bg: var(--yq-white);
    --yq-ant-timeline-color: var(--yq-ant-border-color-split);
    --yq-ant-timeline-dot-color: var(--yq-theme);
    --yq-ant-timeline-dot-bg: var(--yq-white);
    --yq-ant-upload-actions-color: var(--yq-ant-text-color-secondary);
    --yq-ant-process-tail-color: var(--yq-ant-border-color-split);
    --yq-ant-steps-nav-arrow-color: rgba(0,0,0,0.25);
    --yq-ant-steps-background: var(--yq-white);
    --yq-ant-notification-bg: var(--yq-white);
    --yq-ant-image-bg: #f5f5f5;
    --yq-ant-image-color: #fff;
    --yq-ant-image-preview-operation-color: hsla(0,0%,100%,0.85);
    --yq-ant-image-preview-operation-disabled-color: hsla(0,0%,100%,0.85);
    --yq-ant-segmented-bg: rgba(0,0,0,0.04);
    --yq-ant-segmented-hover-bg: rgba(0,0,0,0.06);
    --yq-ant-segmented-selected-bg: var(--yq-white);
    --yq-ant-segmented-label-color: rgba(0,0,0,0.65);
    --yq-ant-segmented-label-hover-color: #262626
}

html[data-kumuhana=pouli] {
    --yq-ant-primary-color: #2ed790;
    --yq-ant-primary-color-hover: var(--yq-yuque-green-400);
    --yq-ant-primary-color-active: var(--yq-yuque-green-600);
    --yq-ant-primary-color-outline: rgba(46,215,144,0.2);
    --yq-ant-primary-1: var(--yq-yuque-green-100);
    --yq-ant-primary-2: var(--yq-yuque-green-200);
    --yq-ant-primary-3: var(--yq-yuque-green-300);
    --yq-ant-primary-4: var(--yq-yuque-green-400);
    --yq-ant-primary-5: var(--yq-yuque-green-500);
    --yq-ant-primary-6: var(--yq-yuque-green-600);
    --yq-ant-primary-7: var(--yq-yuque-green-700);
    --yq-ant-primary-color-deprecated-l-35: #c4f4e0;
    --yq-ant-primary-color-deprecated-l-20: #84e7bd;
    --yq-ant-primary-color-deprecated-t-20: #58dfa6;
    --yq-ant-primary-color-deprecated-t-50: #97ebc8;
    --yq-ant-primary-color-deprecated-f-12: rgba(46,215,144,0.2);
    --yq-ant-primary-color-active-deprecated-f-30: rgba(218,246,234,0.3);
    --yq-ant-primary-color-active-deprecated-d-02: var(--yq-yuque-green-100);
    --yq-ant-success-color: var(--yq-yuque-green-600);
    --yq-ant-success-color-hover: var(--yq-yuque-green-500);
    --yq-ant-success-color-active: var(--yq-yuque-green-700);
    --yq-ant-success-color-outline: rgba(81,184,141,0.2);
    --yq-ant-success-color-deprecated-bg: var(--yq-yuque-green-100);
    --yq-ant-success-color-deprecated-border: var(--yq-yuque-green-300);
    --yq-ant-error-color: var(--yq-red-600);
    --yq-ant-error-color-hover: var(--yq-red-500);
    --yq-ant-error-color-active: var(--yq-red-700);
    --yq-ant-error-color-outline: rgba(202,63,79,0.2);
    --yq-ant-error-color-deprecated-bg: var(--yq-red-100);
    --yq-ant-error-color-deprecated-border: var(--yq-red-300);
    --yq-ant-warning-color: var(--yq-yellow-600);
    --yq-ant-warning-color-hover: var(--yq-yellow-500);
    --yq-ant-warning-color-active: var(--yq-yellow-700);
    --yq-ant-warning-color-outline: rgba(210,166,56,0.2);
    --yq-ant-warning-color-deprecated-bg: var(--yq-yellow-100);
    --yq-ant-warning-color-deprecated-border: var(--yq-yellow-300);
    --yq-ant-info-color: var(--yq-blue-600);
    --yq-ant-info-color-deprecated-bg: var(--yq-blue-100);
    --yq-ant-info-color-deprecated-border: var(--yq-blue-300);
    --yq-ant-popover-background: #1f1f1f;
    --yq-ant-popover-customize-border-color: #3a3a3a;
    --yq-ant-body-background: var(--yq-black);
    --yq-ant-component-background: var(--yq-background-base);
    --yq-ant-text-color: hsla(0,0%,100%,0.85);
    --yq-ant-text-color-secondary: hsla(0,0%,100%,0.45);
    --yq-ant-text-color-inverse: #fff;
    --yq-ant-icon-color-hover: hsla(0,0%,100%,0.75);
    --yq-ant-heading-color: hsla(0,0%,100%,0.85);
    --yq-ant-item-active-bg: hsla(0,0%,100%,0.12);
    --yq-ant-item-hover-bg: hsla(0,0%,100%,0.08);
    --yq-ant-border-color-base: hsla(0,0%,100%,0.12);
    --yq-ant-border-color-split: hsla(0,0%,100%,0.08);
    --yq-ant-background-color-light: hsla(0,0%,100%,0.04);
    --yq-ant-background-color-base: hsla(0,0%,100%,0.08);
    --yq-ant-disabled-bg: var(--yq-ant-background-color-base);
    --yq-ant-disabled-color-dark: hsla(0,0%,100%,0.3);
    --yq-ant-tree-bg: transparent;
    --yq-ant-list-customize-card-bg: transparent;
    --yq-ant-shadow-color: rgba(0,0,0,0.45);
    --yq-ant-shadow-color-inverse: var(--yq-ant-component-background);
    --yq-ant-box-shadow-base: 0 1px 4px -2px rgba(0,0,0,0.13),0 2px 8px 0 rgba(0,0,0,0.08),0 8px 16px 4px rgba(0,0,0,0.04);
    --yq-ant-shadow-1-up: 0 -6px 16px -8px rgba(0,0,0,0.32),0 -9px 28px 0 rgba(0,0,0,0.2),0 -12px 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-1-down: 0 6px 16px -8px rgba(0,0,0,0.32),0 9px 28px 0 rgba(0,0,0,0.2),0 12px 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-1-right: 6px 0 16px -8px rgba(0,0,0,0.32),9px 0 28px 0 rgba(0,0,0,0.2),12px 0 48px 16px rgba(0,0,0,0.12);
    --yq-ant-shadow-2: 0 1px 4px -2px rgba(0,0,0,0.24),0 2px 8px 0 rgba(0,0,0,0.36),0 8px 16px 4px rgba(0,0,0,0.25);
    --yq-ant-btn-shadow: 0 2px 0 rgba(0,0,0,0.015);
    --yq-ant-btn-primary-shadow: 0 2px 0 rgba(0,0,0,0.045);
    --yq-ant-btn-text-shadow: 0 -1px 0 rgba(0,0,0,0.12);
    --yq-ant-btn-default-bg: transparent;
    --yq-ant-btn-default-ghost-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-ghost-border: hsla(0,0%,100%,0.25);
    --yq-ant-btn-text-hover-bg: hsla(0,0%,100%,0.03);
    --yq-ant-btn-primary-color: var(--yq-yuque-grey-100);
    --yq-ant-btn-primary-bg: #2ed790;
    --yq-ant-btn-default-color: var(--yq-yuque-grey-900);
    --yq-ant-btn-default-border: hsla(0,0%,100%,0.12);
    --yq-ant-btn-danger-color: #fff;
    --yq-ant-btn-danger-bg: var(--yq-ant-error-color);
    --yq-ant-btn-danger-border: var(--yq-ant-error-color);
    --yq-ant-btn-disable-color: var(--yq-ant-disabled-color);
    --yq-ant-btn-disable-bg: var(--yq-ant-disabled-bg);
    --yq-ant-btn-disable-border: var(--yq-ant-border-color-base);
    --yq-ant-btn-default-ghost-bg: transparent;
    --yq-ant-btn-group-border: var(--yq-yuque-green-500);
    --yq-ant-btn-link-hover-bg: transparent;
    --yq-ant-checkbox-check-bg: hsla(0,0%,100%,0.04);
    --yq-ant-descriptions-bg: var(--yq-ant-background-color-light);
    --yq-ant-divider-color: hsla(0,0%,100%,0.12);
    --yq-ant-modal-header-bg: var(--yq-yuque-grey-200);
    --yq-ant-modal-header-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-modal-content-bg: var(--yq-yuque-grey-200);
    --yq-ant-modal-footer-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-radio-solid-checked-color: #fff;
    --yq-ant-radio-dot-disabled-color: hsla(0,0%,100%,0.2);
    --yq-ant-radio-disabled-button-checked-bg: hsla(0,0%,100%,0.2);
    --yq-ant-radio-disabled-button-checked-color: var(--yq-ant-disabled-color);
    --yq-ant-layout-body-background: var(--yq-ant-body-background);
    --yq-ant-layout-header-background: var(--yq-yuque-grey-200);
    --yq-ant-layout-trigger-background: #262626;
    --yq-ant-input-bg: hsla(0,0%,100%,0.04);
    --yq-ant-input-placeholder-color: hsla(0,0%,100%,0.3);
    --yq-ant-input-icon-color: hsla(0,0%,100%,0.3);
    --yq-ant-input-number-handler-active-bg: var(--yq-ant-item-hover-bg);
    --yq-ant-input-icon-hover-color: hsla(0,0%,100%,0.85);
    --yq-ant-select-background: hsla(0,0%,100%,0.04);
    --yq-ant-select-dropdown-bg: var(--yq-yuque-grey-200);
    --yq-ant-select-clear-background: var(--yq-ant-component-background);
    --yq-ant-select-selection-item-bg: hsla(0,0%,100%,0.08);
    --yq-ant-select-selection-item-border-color: var(--yq-ant-border-color-split);
    --yq-ant-select-multiple-disabled-background: var(--yq-ant-component-background);
    --yq-ant-select-multiple-item-disabled-color: #595959;
    --yq-ant-select-multiple-item-disabled-border-color: var(--yq-yuque-grey-200);
    --yq-ant-cascader-bg: hsla(0,0%,100%,0.04);
    --yq-ant-cascader-menu-bg: var(--yq-yuque-grey-200);
    --yq-ant-cascader-menu-border-color-split: var(--yq-ant-border-color-split);
    --yq-ant-tooltip-bg: var(--yq-yuque-grey-200);
    --yq-ant-menu-dark-inline-submenu-bg: var(--yq-ant-component-background);
    --yq-ant-menu-dark-bg: var(--yq-yuque-grey-200);
    --yq-ant-menu-popup-bg: var(--yq-yuque-grey-200);
    --yq-ant-message-notice-content-bg: var(--yq-yuque-grey-200);
    --yq-ant-notification-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-header-bg: #1d1d1d;
    --yq-ant-table-body-sort-bg: hsla(0,0%,100%,0.01);
    --yq-ant-table-row-hover-bg: #262626;
    --yq-ant-table-header-cell-split-color: rgba(0,0,0,0.08);
    --yq-ant-table-header-sort-bg: #262626;
    --yq-ant-table-header-filter-active-bg: #434343;
    --yq-ant-table-header-sort-active-bg: #303030;
    --yq-ant-table-filter-btns-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-expanded-row-bg: var(--yq-ant-table-header-bg);
    --yq-ant-table-filter-dropdown-bg: var(--yq-yuque-grey-200);
    --yq-ant-table-expand-icon-bg: transparent;
    --yq-ant-picker-basic-cell-hover-with-range-color: var(--yq-yuque-green-600);
    --yq-ant-picker-basic-cell-disabled-bg: #303030;
    --yq-ant-picker-border-color: var(--yq-ant-border-color-split);
    --yq-ant-picker-bg: hsla(0,0%,100%,0.04);
    --yq-ant-picker-date-hover-range-border-color: var(--yq-yuque-green-600);
    --yq-ant-dropdown-menu-bg: var(--yq-yuque-grey-200);
    --yq-ant-dropdown-menu-submenu-disabled-bg: transparent;
    --yq-ant-steps-nav-arrow-color: hsla(0,0%,100%,0.2);
    --yq-ant-steps-background: transparent;
    --yq-ant-avatar-bg: hsla(0,0%,100%,0.3);
    --yq-ant-progress-steps-item-bg: hsla(0,0%,100%,0.08);
    --yq-ant-calendar-bg: var(--yq-yuque-grey-200);
    --yq-ant-calendar-input-bg: var(--yq-yuque-grey-200);
    --yq-ant-calendar-border-color: transparent;
    --yq-ant-calendar-full-bg: var(--yq-ant-component-background);
    --yq-ant-badge-text-color: var(--yq-yuque-grey-100);
    --yq-ant-badge-color: var(--yq-blue-500);
    --yq-ant-popover-bg: var(--yq-yuque-grey-200);
    --yq-ant-drawer-bg: var(--yq-yuque-grey-200);
    --yq-ant-card-actions-background: var(--yq-ant-component-background);
    --yq-ant-card-skeleton-bg: #303030;
    --yq-ant-card-shadow: 0 1px 2px -2px rgba(0,0,0,0.64),0 3px 6px 0 rgba(0,0,0,0.48),0 5px 12px 4px rgba(0,0,0,0.36);
    --yq-ant-transfer-item-hover-bg: #262626;
    --yq-ant-comment-bg: transparent;
    --yq-ant-comment-author-time-color: hsla(0,0%,100%,0.3);
    --yq-ant-comment-action-hover-color: hsla(0,0%,100%,0.65);
    --yq-ant-rate-star-bg: hsla(0,0%,100%,0.12);
    --yq-ant-switch-bg: #fff;
    --yq-ant-pagination-item-bg: transparent;
    --yq-ant-pagination-item-bg-active: transparent;
    --yq-ant-pagination-item-link-bg: transparent;
    --yq-ant-pagination-item-disabled-bg-active: hsla(0,0%,100%,0.25);
    --yq-ant-pagination-item-disabled-color-active: var(--yq-black);
    --yq-ant-pagination-item-input-bg: var(--yq-ant-pagination-item-bg);
    --yq-ant-page-header-back-color: inherit;
    --yq-ant-page-header-ghost-bg: transparent;
    --yq-ant-slider-handle-background-color: var(--yq-yuque-grey-200);
    --yq-ant-slider-rail-background-color: #262626;
    --yq-ant-slider-rail-background-color-hover: var(--yq-ant-border-color-base);
    --yq-ant-slider-dot-border-color: var(--yq-ant-border-color-split);
    --yq-ant-slider-dot-border-color-active: var(--yq-yuque-green-400);
    --yq-ant-skeleton-to-color: hsla(0,0%,100%,0.16);
    --yq-ant-alert-success-border-color: var(--yq-yuque-green-300);
    --yq-ant-alert-success-bg-color: var(--yq-yuque-green-100);
    --yq-ant-alert-success-icon-color: var(--yq-ant-success-color);
    --yq-ant-alert-info-border-color: var(--yq-blue-300);
    --yq-ant-alert-info-bg-color: var(--yq-blue-100);
    --yq-ant-alert-info-icon-color: var(--yq-ant-info-color);
    --yq-ant-alert-warning-border-color: var(--yq-yellow-300);
    --yq-ant-alert-warning-bg-color: var(--yq-yellow-100);
    --yq-ant-alert-warning-icon-color: var(--yq-ant-warning-color);
    --yq-ant-alert-error-border-color: var(--yq-red-300);
    --yq-ant-alert-error-bg-color: var(--yq-red-100);
    --yq-ant-alert-error-icon-color: var(--yq-ant-error-color);
    --yq-ant-timeline-color: var(--yq-ant-border-color-split);
    --yq-ant-timeline-dot-color: var(--yq-yuque-green-600);
    --yq-ant-mentions-dropdown-bg: var(--yq-yuque-grey-200)
}

:root {
    --yq-white: #fff;
    --yq-black: #000;
    --yq-background-base: #fff;
    --yq-yuque-grey-100: #fafafa;
    --yq-yuque-grey-200: #f4f5f5;
    --yq-yuque-grey-300: #eff0f0;
    --yq-yuque-grey-400: #e7e9e8;
    --yq-yuque-grey-500: #d8dad9;
    --yq-yuque-grey-600: #bec0bf;
    --yq-yuque-grey-700: #8a8f8d;
    --yq-yuque-grey-800: #585a5a;
    --yq-yuque-grey-900: #262626;
    --yq-yuque-grey-1: #fafafa;
    --yq-yuque-grey-2: #f4f5f5;
    --yq-yuque-grey-3: #eff0f0;
    --yq-yuque-grey-4: #e7e9e8;
    --yq-yuque-grey-5: #d8dad9;
    --yq-yuque-grey-6: #bec0bf;
    --yq-yuque-grey-7: #8a8f8d;
    --yq-yuque-grey-8: #585a5a;
    --yq-yuque-grey-9: #262626;
    --yq-pea-green-50: #f3fbe7;
    --yq-pea-green-100: #e8f7cf;
    --yq-pea-green-200: #dbf1b7;
    --yq-pea-green-300: #c1e77e;
    --yq-pea-green-400: #a7dd4b;
    --yq-pea-green-500: #8ccf17;
    --yq-pea-green-600: #74b602;
    --yq-pea-green-700: #5c8d07;
    --yq-pea-green-800: #406105;
    --yq-pea-green-900: #2a4200;
    --yq-pea-green-1: #e8f7cf;
    --yq-pea-green-2: #dbf1b7;
    --yq-pea-green-3: #c1e77e;
    --yq-pea-green-4: #a7dd4b;
    --yq-pea-green-5: #8ccf17;
    --yq-pea-green-6: #74b602;
    --yq-pea-green-7: #5c8d07;
    --yq-pea-green-8: #406105;
    --yq-pea-green-9: #2a4200;
    --yq-yuque-green-50: #ecfaf4;
    --yq-yuque-green-100: #daf6ea;
    --yq-yuque-green-200: #c7f0df;
    --yq-yuque-green-300: #82edc0;
    --yq-yuque-green-400: #45de9d;
    --yq-yuque-green-500: #0bd07d;
    --yq-yuque-green-600: #00b96b;
    --yq-yuque-green-700: #009456;
    --yq-yuque-green-800: #00663b;
    --yq-yuque-green-900: #003d23;
    --yq-yuque-green-1: #daf6ea;
    --yq-yuque-green-2: #c7f0df;
    --yq-yuque-green-3: #82edc0;
    --yq-yuque-green-4: #45de9d;
    --yq-yuque-green-5: #0bd07d;
    --yq-yuque-green-6: #00b96b;
    --yq-yuque-green-7: #009456;
    --yq-yuque-green-8: #00663b;
    --yq-yuque-green-9: #003d23;
    --yq-cyan-50: #e6f9fb;
    --yq-cyan-100: #cef5f7;
    --yq-cyan-200: #b5eff2;
    --yq-cyan-300: #81dfe4;
    --yq-cyan-400: #5ed3d9;
    --yq-cyan-500: #1dc0c9;
    --yq-cyan-600: #01b2bc;
    --yq-cyan-700: #078b92;
    --yq-cyan-800: #07787e;
    --yq-cyan-900: #004347;
    --yq-cyan-1: #cef5f7;
    --yq-cyan-2: #b5eff2;
    --yq-cyan-3: #81dfe4;
    --yq-cyan-4: #5ed3d9;
    --yq-cyan-5: #1dc0c9;
    --yq-cyan-6: #01b2bc;
    --yq-cyan-7: #078b92;
    --yq-cyan-8: #07787e;
    --yq-cyan-9: #004347;
    --yq-blue-50: #ecf4fd;
    --yq-blue-100: #d9eafc;
    --yq-blue-200: #c0ddfc;
    --yq-blue-300: #81bbf8;
    --yq-blue-400: #5fa8f6;
    --yq-blue-500: #2f8ef4;
    --yq-blue-600: #117cee;
    --yq-blue-700: #0c68ca;
    --yq-blue-800: #074a92;
    --yq-blue-900: #00346b;
    --yq-blue-1: #d9eafc;
    --yq-blue-2: #c0ddfc;
    --yq-blue-3: #81bbf8;
    --yq-blue-4: #5fa8f6;
    --yq-blue-5: #2f8ef4;
    --yq-blue-6: #117cee;
    --yq-blue-7: #0c68ca;
    --yq-blue-8: #074a92;
    --yq-blue-9: #00346b;
    --yq-sea-blue-50: #eceefd;
    --yq-sea-blue-100: #d9dffc;
    --yq-sea-blue-200: #c0cafc;
    --yq-sea-blue-300: #96a7fd;
    --yq-sea-blue-400: #6e85f7;
    --yq-sea-blue-500: #4861e0;
    --yq-sea-blue-600: #2f4bda;
    --yq-sea-blue-700: #213bc0;
    --yq-sea-blue-800: #162883;
    --yq-sea-blue-900: #101e60;
    --yq-sea-blue-1: #d9dffc;
    --yq-sea-blue-2: #c0cafc;
    --yq-sea-blue-3: #96a7fd;
    --yq-sea-blue-4: #6e85f7;
    --yq-sea-blue-5: #4861e0;
    --yq-sea-blue-6: #2f4bda;
    --yq-sea-blue-7: #213bc0;
    --yq-sea-blue-8: #162883;
    --yq-sea-blue-9: #101e60;
    --yq-purple-50: #f2edfc;
    --yq-purple-100: #e6dcf9;
    --yq-purple-200: #d9c9f8;
    --yq-purple-300: #ba9bf2;
    --yq-purple-400: #9a6eed;
    --yq-purple-500: #7e45e8;
    --yq-purple-600: #601bde;
    --yq-purple-700: #4c16b1;
    --yq-purple-800: #391084;
    --yq-purple-900: #270070;
    --yq-purple-1: #e6dcf9;
    --yq-purple-2: #d9c9f8;
    --yq-purple-3: #ba9bf2;
    --yq-purple-4: #9a6eed;
    --yq-purple-5: #7e45e8;
    --yq-purple-6: #601bde;
    --yq-purple-7: #4c16b1;
    --yq-purple-8: #391084;
    --yq-purple-9: #270070;
    --yq-magenta-50: #fdeff7;
    --yq-magenta-100: #fbdfef;
    --yq-magenta-200: #f7c4e2;
    --yq-magenta-300: #f297cc;
    --yq-magenta-400: #ec6ab6;
    --yq-magenta-500: #e746a4;
    --yq-magenta-600: #d22d8d;
    --yq-magenta-700: #ae146e;
    --yq-magenta-800: #800f51;
    --yq-magenta-900: #5c0036;
    --yq-magenta-1: #fbdfef;
    --yq-magenta-2: #f7c4e2;
    --yq-magenta-3: #f297cc;
    --yq-magenta-4: #ec6ab6;
    --yq-magenta-5: #e746a4;
    --yq-magenta-6: #d22d8d;
    --yq-magenta-7: #ae146e;
    --yq-magenta-8: #800f51;
    --yq-magenta-9: #5c0036;
    --yq-red-50: #fdf1f3;
    --yq-red-100: #fbe4e7;
    --yq-red-200: #f8ced3;
    --yq-red-300: #f1a2ab;
    --yq-red-400: #ea7583;
    --yq-red-500: #e4495b;
    --yq-red-600: #df2a3f;
    --yq-red-700: #ad1a2b;
    --yq-red-800: #8f0515;
    --yq-red-900: #70000d;
    --yq-red-1: #fbe4e7;
    --yq-red-2: #f8ced3;
    --yq-red-3: #f1a2ab;
    --yq-red-4: #ea7583;
    --yq-red-5: #e4495b;
    --yq-red-6: #df2a3f;
    --yq-red-7: #ad1a2b;
    --yq-red-8: #8f0515;
    --yq-red-9: #70000d;
    --yq-vermilion-50: #fdf3f1;
    --yq-vermilion-100: #fbe8e4;
    --yq-vermilion-200: #f8d6ce;
    --yq-vermilion-300: #f0ad9d;
    --yq-vermilion-400: #eb866f;
    --yq-vermilion-500: #e46549;
    --yq-vermilion-600: #df4b2a;
    --yq-vermilion-700: #ad351a;
    --yq-vermilion-800: #8f1e05;
    --yq-vermilion-900: #701500;
    --yq-vermilion-1: #fbe8e4;
    --yq-vermilion-2: #f8d6ce;
    --yq-vermilion-3: #f0ad9d;
    --yq-vermilion-4: #eb866f;
    --yq-vermilion-5: #e46549;
    --yq-vermilion-6: #df4b2a;
    --yq-vermilion-7: #ad351a;
    --yq-vermilion-8: #8f1e05;
    --yq-vermilion-9: #701500;
    --yq-orange-50: #fef2e9;
    --yq-orange-100: #fde6d3;
    --yq-orange-200: #f8d6b9;
    --yq-orange-300: #f8b881;
    --yq-orange-400: #f6a055;
    --yq-orange-500: #f38f39;
    --yq-orange-600: #ed740c;
    --yq-orange-700: #c75c00;
    --yq-orange-800: #944400;
    --yq-orange-900: #663000;
    --yq-orange-1: #fde6d3;
    --yq-orange-2: #f8d6b9;
    --yq-orange-3: #f8b881;
    --yq-orange-4: #f6a055;
    --yq-orange-5: #f38f39;
    --yq-orange-6: #ed740c;
    --yq-orange-7: #c75c00;
    --yq-orange-8: #944400;
    --yq-orange-9: #663000;
    --yq-yellow-50: #fcf5e6;
    --yq-yellow-100: #f9efcd;
    --yq-yellow-200: #f6e1ac;
    --yq-yellow-300: #f5d480;
    --yq-yellow-400: #f5cb61;
    --yq-yellow-500: #f3bb2f;
    --yq-yellow-600: #ecaa04;
    --yq-yellow-700: #c99103;
    --yq-yellow-800: #8f6600;
    --yq-yellow-900: #664900;
    --yq-yellow-1: #f9efcd;
    --yq-yellow-2: #f6e1ac;
    --yq-yellow-3: #f5d480;
    --yq-yellow-4: #f5cb61;
    --yq-yellow-5: #f3bb2f;
    --yq-yellow-6: #ecaa04;
    --yq-yellow-7: #c99103;
    --yq-yellow-8: #8f6600;
    --yq-yellow-9: #664900;
    --yq-bright-yellow-50: #fdf9e4;
    --yq-bright-yellow-100: #fbf5cb;
    --yq-bright-yellow-200: #fcf1a6;
    --yq-bright-yellow-300: #fdeb78;
    --yq-bright-yellow-400: #fce75a;
    --yq-bright-yellow-500: #fbde28;
    --yq-bright-yellow-600: #edce02;
    --yq-bright-yellow-700: #cdb204;
    --yq-bright-yellow-800: #a58f04;
    --yq-bright-yellow-900: #665800;
    --yq-bright-yellow-1: #fbf5cb;
    --yq-bright-yellow-2: #fcf1a6;
    --yq-bright-yellow-3: #fdeb78;
    --yq-bright-yellow-4: #fce75a;
    --yq-bright-yellow-5: #fbde28;
    --yq-bright-yellow-6: #edce02;
    --yq-bright-yellow-7: #cdb204;
    --yq-bright-yellow-8: #a58f04;
    --yq-bright-yellow-9: #665800;
    --yq-brown-50: #f7f2ea;
    --yq-brown-100: #f0e7d6;
    --yq-brown-200: #e7d7bb;
    --yq-brown-300: #dbc39a;
    --yq-brown-400: #d2b684;
    --yq-brown-500: #c59f5e;
    --yq-brown-600: #b1873f;
    --yq-brown-700: #927035;
    --yq-brown-800: #74592a;
    --yq-brown-900: #523f1e;
    --yq-brown-1: #f0e7d6;
    --yq-brown-2: #e7d7bb;
    --yq-brown-3: #dbc39a;
    --yq-brown-4: #d2b684;
    --yq-brown-5: #c59f5e;
    --yq-brown-6: #b1873f;
    --yq-brown-7: #927035;
    --yq-brown-8: #74592a;
    --yq-brown-9: #523f1e;
    --yq-theme: var(--yq-yuque-green-600);
    --yq-normal: var(--yq-yuque-grey-500);
    --yq-bg-primary: var(--yq-background-base);
    --yq-bg-secondary: var(--yq-yuque-grey-100);
    --yq-bg-tertiary: var(--yq-yuque-grey-200);
    --yq-bg-primary-hover: var(--yq-yuque-grey-300);
    --yq-bg-primary-hover-light: var(--yq-yuque-grey-400);
    --yq-bg-foreground: var(--yq-white);
    --yq-bg-pre-foreground: var(--yq-white);
    --yq-bg-pre-secondary: var(--yq-yuque-grey-200);
    --yq-bg-card-dialog: var(--yq-yuque-grey-100);
    --yq-mask-bg: rgba(0,0,0,0.65);
    --yq-toast-bg: rgba(0,0,0,0.65);
    --yq-border-primary: var(--yq-yuque-grey-400);
    --yq-border-primary-active: var(--yq-yuque-green-600);
    --yq-border-light: rgba(0,0,0,0.04);
    --yq-border-heavy: var(--yq-yuque-grey-300);
    --yq-cardborder-hover: var(--yq-blue-300);
    --yq-cardborder-selected: var(--yq-blue-500);
    --yq-sheetborder: rgba(0,0,0,0.1);
    --yq-text-primary: var(--yq-yuque-grey-900);
    --yq-text-body: var(--yq-yuque-grey-800);
    --yq-text-caption: var(--yq-yuque-grey-700);
    --yq-text-disable: var(--yq-yuque-grey-600);
    --yq-text-link: var(--yq-blue-600);
    --yq-text-link-hover: var(--yq-blue-400);
    --yq-icon-primary: var(--yq-yuque-grey-900);
    --yq-icon-secondary: var(--yq-yuque-grey-800);
    --yq-icon-caption: var(--yq-yuque-grey-700);
    --yq-icon-disable: var(--yq-yuque-grey-600);
    --yq-icon-hover: var(--yq-yuque-grey-500);
    --yq-icon-colorbg: rgba(0,0,0,0.45);
    --yq-function-success: var(--yq-yuque-green-600);
    --yq-function-info: var(--yq-blue-600);
    --yq-function-warning: var(--yq-yellow-600);
    --yq-function-error: var(--yq-red-600);
    --yq-badge: var(--yq-blue-500);
    --yq-popover-bg: var(--yq-white);
    --yq-disable-color: var(--yq-yuque-grey-600)
}

html[data-kumuhana=pouli] {
    --yq-white: #000;
    --yq-black: #fff;
    --yq-background-base: #141414;
    --yq-yuque-grey-100: #141414;
    --yq-yuque-grey-200: #1f1f1f;
    --yq-yuque-grey-300: #292929;
    --yq-yuque-grey-400: #333;
    --yq-yuque-grey-500: #424242;
    --yq-yuque-grey-600: #505050;
    --yq-yuque-grey-700: #848484;
    --yq-yuque-grey-800: #b3b3b3;
    --yq-yuque-grey-900: #e2e2e2;
    --yq-yuque-grey-1: #141414;
    --yq-yuque-grey-2: #1f1f1f;
    --yq-yuque-grey-3: #292929;
    --yq-yuque-grey-4: #333;
    --yq-yuque-grey-5: #424242;
    --yq-yuque-grey-6: #505050;
    --yq-yuque-grey-7: #848484;
    --yq-yuque-grey-8: #b3b3b3;
    --yq-yuque-grey-9: #e2e2e2;
    --yq-pea-green-50: #1b2113;
    --yq-pea-green-100: #242f14;
    --yq-pea-green-200: #445625;
    --yq-pea-green-300: #557915;
    --yq-pea-green-400: #6a9226;
    --yq-pea-green-500: #7aad22;
    --yq-pea-green-600: #94bc4e;
    --yq-pea-green-700: #aed666;
    --yq-pea-green-800: #c2df90;
    --yq-pea-green-900: #d5e1c1;
    --yq-pea-green-1: #242f14;
    --yq-pea-green-2: #445625;
    --yq-pea-green-3: #557915;
    --yq-pea-green-4: #6a9226;
    --yq-pea-green-5: #7aad22;
    --yq-pea-green-6: #94bc4e;
    --yq-pea-green-7: #aed666;
    --yq-pea-green-8: #c2df90;
    --yq-pea-green-9: #d5e1c1;
    --yq-yuque-green-50: #10211a;
    --yq-yuque-green-100: #0e2f22;
    --yq-yuque-green-200: #255641;
    --yq-yuque-green-300: #18774f;
    --yq-yuque-green-400: #298e64;
    --yq-yuque-green-500: #29ad76;
    --yq-yuque-green-600: #51b88d;
    --yq-yuque-green-700: #6bd1a6;
    --yq-yuque-green-800: #97d8bc;
    --yq-yuque-green-900: #bce6d5;
    --yq-yuque-green-1: #0e2f22;
    --yq-yuque-green-2: #255641;
    --yq-yuque-green-3: #18774f;
    --yq-yuque-green-4: #298e64;
    --yq-yuque-green-5: #29ad76;
    --yq-yuque-green-6: #51b88d;
    --yq-yuque-green-7: #6bd1a6;
    --yq-yuque-green-8: #97d8bc;
    --yq-yuque-green-9: #bce6d5;
    --yq-cyan-50: #122324;
    --yq-cyan-100: #123436;
    --yq-cyan-200: #255356;
    --yq-cyan-300: #1a7074;
    --yq-cyan-400: #278b90;
    --yq-cyan-500: #22a8af;
    --yq-cyan-600: #4eb6bc;
    --yq-cyan-700: #76c2c6;
    --yq-cyan-800: #95d7db;
    --yq-cyan-900: #bfe1e3;
    --yq-cyan-1: #123436;
    --yq-cyan-2: #255356;
    --yq-cyan-3: #1a7074;
    --yq-cyan-4: #278b90;
    --yq-cyan-5: #22a8af;
    --yq-cyan-6: #4eb6bc;
    --yq-cyan-7: #76c2c6;
    --yq-cyan-8: #95d7db;
    --yq-cyan-9: #bfe1e3;
    --yq-blue-50: #15212d;
    --yq-blue-100: #193048;
    --yq-blue-200: #253c56;
    --yq-blue-300: #1d4672;
    --yq-blue-400: #245a94;
    --yq-blue-500: #2b6bb1;
    --yq-blue-600: #3b82ce;
    --yq-blue-700: #689fd9;
    --yq-blue-800: #8db5e2;
    --yq-blue-900: #b5d0ed;
    --yq-blue-1: #193048;
    --yq-blue-2: #253c56;
    --yq-blue-3: #1d4672;
    --yq-blue-4: #245a94;
    --yq-blue-5: #2b6bb1;
    --yq-blue-6: #3b82ce;
    --yq-blue-7: #689fd9;
    --yq-blue-8: #8db5e2;
    --yq-blue-9: #b5d0ed;
    --yq-sea-blue-50: #171c33;
    --yq-sea-blue-100: #1c2654;
    --yq-sea-blue-200: #252d56;
    --yq-sea-blue-300: #1b2a74;
    --yq-sea-blue-400: #243694;
    --yq-sea-blue-500: #2b41b1;
    --yq-sea-blue-600: #3b54ce;
    --yq-sea-blue-700: #687bd9;
    --yq-sea-blue-800: #8d9be2;
    --yq-sea-blue-900: #b5beed;
    --yq-sea-blue-1: #1c2654;
    --yq-sea-blue-2: #252d56;
    --yq-sea-blue-3: #1b2a74;
    --yq-sea-blue-4: #243694;
    --yq-sea-blue-5: #2b41b1;
    --yq-sea-blue-6: #3b54ce;
    --yq-sea-blue-7: #687bd9;
    --yq-sea-blue-8: #8d9be2;
    --yq-sea-blue-9: #b5beed;
    --yq-purple-50: #1f172e;
    --yq-purple-100: #2c1c4a;
    --yq-purple-200: #352259;
    --yq-purple-300: #3a1c73;
    --yq-purple-400: #512a98;
    --yq-purple-500: #5a2caf;
    --yq-purple-600: #7551b8;
    --yq-purple-700: #9677cf;
    --yq-purple-800: #b099db;
    --yq-purple-900: #cabce7;
    --yq-purple-1: #2c1c4a;
    --yq-purple-2: #352259;
    --yq-purple-3: #3a1c73;
    --yq-purple-4: #512a98;
    --yq-purple-5: #5a2caf;
    --yq-purple-6: #7551b8;
    --yq-purple-7: #9677cf;
    --yq-purple-8: #b099db;
    --yq-purple-9: #cabce7;
    --yq-magenta-50: #301525;
    --yq-magenta-100: #4e1837;
    --yq-magenta-200: #5b1f42;
    --yq-magenta-300: #701f4e;
    --yq-magenta-400: #942465;
    --yq-magenta-500: #a43776;
    --yq-magenta-600: #be4b8e;
    --yq-magenta-700: #ce78aa;
    --yq-magenta-800: #d996bd;
    --yq-magenta-900: #e6bcd5;
    --yq-magenta-1: #4e1837;
    --yq-magenta-2: #5b1f42;
    --yq-magenta-3: #701f4e;
    --yq-magenta-4: #942465;
    --yq-magenta-5: #a43776;
    --yq-magenta-6: #be4b8e;
    --yq-magenta-7: #ce78aa;
    --yq-magenta-8: #d996bd;
    --yq-magenta-9: #e6bcd5;
    --yq-red-50: #29191c;
    --yq-red-100: #402125;
    --yq-red-200: #5b2027;
    --yq-red-300: #741b25;
    --yq-red-400: #981f2d;
    --yq-red-500: #b62536;
    --yq-red-600: #ca3f4f;
    --yq-red-700: #cc7b84;
    --yq-red-800: #d49199;
    --yq-red-900: #e6bcc1;
    --yq-red-1: #402125;
    --yq-red-2: #5b2027;
    --yq-red-3: #741b25;
    --yq-red-4: #981f2d;
    --yq-red-5: #b62536;
    --yq-red-6: #ca3f4f;
    --yq-red-7: #cc7b84;
    --yq-red-8: #d49199;
    --yq-red-9: #e6bcc1;
    --yq-vermilion-50: #271c19;
    --yq-vermilion-100: #3c2520;
    --yq-vermilion-200: #5b2b20;
    --yq-vermilion-300: #742b1b;
    --yq-vermilion-400: #943824;
    --yq-vermilion-500: #b0432b;
    --yq-vermilion-600: #cd573c;
    --yq-vermilion-700: #d9816d;
    --yq-vermilion-800: #e09685;
    --yq-vermilion-900: #e9c2b9;
    --yq-vermilion-1: #3c2520;
    --yq-vermilion-2: #5b2b20;
    --yq-vermilion-3: #742b1b;
    --yq-vermilion-4: #943824;
    --yq-vermilion-5: #b0432b;
    --yq-vermilion-6: #cd573c;
    --yq-vermilion-7: #d9816d;
    --yq-vermilion-8: #e09685;
    --yq-vermilion-9: #e9c2b9;
    --yq-orange-50: #251e18;
    --yq-orange-100: #382a1e;
    --yq-orange-200: #5e3b1d;
    --yq-orange-300: #774418;
    --yq-orange-400: #9d571b;
    --yq-orange-500: #bc6820;
    --yq-orange-600: #d37f36;
    --yq-orange-700: #de9f68;
    --yq-orange-800: #e0af85;
    --yq-orange-900: #e9cfb9;
    --yq-orange-1: #382a1e;
    --yq-orange-2: #5e3b1d;
    --yq-orange-3: #774418;
    --yq-orange-4: #9d571b;
    --yq-orange-5: #bc6820;
    --yq-orange-6: #d37f36;
    --yq-orange-7: #de9f68;
    --yq-orange-8: #e0af85;
    --yq-orange-9: #e9cfb9;
    --yq-yellow-50: #241f15;
    --yq-yellow-100: #352d17;
    --yq-yellow-200: #564825;
    --yq-yellow-300: #775c18;
    --yq-yellow-400: #9c781c;
    --yq-yellow-500: #c29219;
    --yq-yellow-600: #d2a638;
    --yq-yellow-700: #e8be54;
    --yq-yellow-800: #efcf81;
    --yq-yellow-900: #eedeb4;
    --yq-yellow-1: #352d17;
    --yq-yellow-2: #564825;
    --yq-yellow-3: #775c18;
    --yq-yellow-4: #9c781c;
    --yq-yellow-5: #c29219;
    --yq-yellow-6: #d2a638;
    --yq-yellow-7: #e8be54;
    --yq-yellow-8: #efcf81;
    --yq-yellow-9: #eedeb4;
    --yq-bright-yellow-50: #242215;
    --yq-bright-yellow-100: #353117;
    --yq-bright-yellow-200: #564f25;
    --yq-bright-yellow-300: #776a18;
    --yq-bright-yellow-400: #9c8b1c;
    --yq-bright-yellow-500: #c2ac19;
    --yq-bright-yellow-600: #d2bd38;
    --yq-bright-yellow-700: #e8d454;
    --yq-bright-yellow-800: #efe081;
    --yq-bright-yellow-900: #eee7b4;
    --yq-bright-yellow-1: #353117;
    --yq-bright-yellow-2: #564f25;
    --yq-bright-yellow-3: #776a18;
    --yq-bright-yellow-4: #9c8b1c;
    --yq-bright-yellow-5: #c2ac19;
    --yq-bright-yellow-6: #d2bd38;
    --yq-bright-yellow-7: #e8d454;
    --yq-bright-yellow-8: #efe081;
    --yq-bright-yellow-9: #eee7b4;
    --yq-brown-50: #231e15;
    --yq-brown-100: #342a18;
    --yq-brown-200: #544327;
    --yq-brown-300: #624e2d;
    --yq-brown-400: #7e653a;
    --yq-brown-500: #967845;
    --yq-brown-600: #b29158;
    --yq-brown-700: #c2a87a;
    --yq-brown-800: #d2bf9d;
    --yq-brown-900: #e2d6c0;
    --yq-brown-1: #342a18;
    --yq-brown-2: #544327;
    --yq-brown-3: #624e2d;
    --yq-brown-4: #7e653a;
    --yq-brown-5: #967845;
    --yq-brown-6: #b29158;
    --yq-brown-7: #c2a87a;
    --yq-brown-8: #d2bf9d;
    --yq-brown-9: #e2d6c0;
    --yq-theme: #2ed790;
    --yq-normal: #424242;
    --yq-bg-primary: #141414;
    --yq-bg-secondary: #1f1f1f;
    --yq-bg-tertiary: hsla(0,0%,100%,0.04);
    --yq-bg-primary-hover: hsla(0,0%,100%,0.12);
    --yq-bg-primary-hover-light: #333;
    --yq-bg-foreground: #1f1f1f;
    --yq-bg-pre-foreground: #292929;
    --yq-bg-pre-secondary: #292929;
    --yq-bg-card-dialog: #292929;
    --yq-mask-bg: rgba(0,0,0,0.65);
    --yq-toast-bg: #292929;
    --yq-border-primary: hsla(0,0%,100%,0.12);
    --yq-border-primary-active: #29ad76;
    --yq-border-light: hsla(0,0%,100%,0.08);
    --yq-border-heavy: hsla(0,0%,100%,0.12);
    --yq-cardborder-hover: #253c56;
    --yq-cardborder-selected: #1d4672;
    --yq-sheetborder: hsla(0,0%,100%,0.12);
    --yq-text-primary: #e2e2e2;
    --yq-text-body: #b3b3b3;
    --yq-text-caption: #848484;
    --yq-text-disable: #505050;
    --yq-text-link: #3b82ce;
    --yq-text-link-hover: #245a94;
    --yq-icon-primary: #e2e2e2;
    --yq-icon-secondary: #b3b3b3;
    --yq-icon-caption: #848484;
    --yq-icon-disable: #505050;
    --yq-icon-hover: #424242;
    --yq-icon-colorbg: rgba(0,0,0,0.45);
    --yq-function-success: #51b88d;
    --yq-function-info: #3b82ce;
    --yq-function-warning: #d2a638;
    --yq-function-error: #ca3f4f;
    --yq-badge: #2b6bb1;
    --yq-popover-bg: #1f1f1f;
    --yq-disable-color: #505050
}

::-moz-selection {
    color: inherit;
    background: rgba(27,162,227,.2)
}

::selection {
    color: inherit;
    background: rgba(27,162,227,.2)
}

.larkui-icon {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.larkui-icon>* {
    line-height: 1
}

.larkui-icon svg {
    display: inline-block
}

.larkui-icon:before {
    display: none
}

.larkui-icon .larkui-icon-icon {
    display: block
}

.ant-select-auto-complete {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum"
}

.ant-select-auto-complete .ant-select-clear {
    right: 13px
}

.ant-table-tbody>tr>.ant-table-cell,.ant-table-thead>tr>.ant-table-cell,.ant-table tfoot>tr>.ant-table-cell {
    padding: 12px 16px
}

.ant-table-thead>tr>.ant-table-cell {
    color: var(--yq-ant-text-color-secondary);
    font-weight: 400;
    font-size: 14px
}

.ant-table-thead>tr>.ant-table-cell .ant-table-column-sorters {
    padding-top: 0;
    padding-bottom: 0
}

table tr td.ant-table-selection-column,table tr th.ant-table-selection-column {
    padding-right: 16px;
    padding-left: 16px
}

.ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before,.ant-table .ant-empty-image {
    display: none
}

.ant-table .ant-table-column-sorters {
    justify-content: flex-start
}

.ant-table .ant-table-column-sorter {
    padding: 0 8px
}

.ant-picker {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    padding: 4px 11px 4px;
    position: relative;
    display: inline-flex;
    align-items: center;
    background: var(--yq-ant-picker-bg);
    border: 1px solid var(--yq-ant-border-color-base);
    border-radius: 6px;
    transition: border .3s,box-shadow .3s
}

.ant-picker-focused,.ant-picker:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-picker-focused {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    box-shadow: none
}

.ant-picker-focused,html[data-kumuhana=pouli] .ant-picker-focused {
    border-color: var(--yq-theme)
}

.ant-picker.ant-picker-disabled {
    background: var(--yq-ant-input-disabled-bg);
    border-color: var(--yq-ant-border-color-base);
    cursor: not-allowed
}

.ant-picker.ant-picker-disabled .ant-picker-suffix {
    color: var(--yq-ant-disabled-color)
}

.ant-picker.ant-picker-borderless {
    background-color: transparent!important;
    border-color: transparent!important;
    box-shadow: none!important
}

.ant-picker-input {
    position: relative;
    display: inline-flex;
    align-items: center;
    width: 100%
}

.ant-picker-input>input {
    position: relative;
    display: inline-block;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s;
    flex: auto;
    min-width: 1px;
    height: auto;
    padding: 0;
    background: transparent;
    border: 0
}

.ant-picker-input>input::-moz-placeholder {
    opacity: 1
}

.ant-picker-input>input::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-picker-input>input:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-picker-input>input:placeholder-shown {
    text-overflow: ellipsis
}

.ant-picker-input>input:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input-focused,.ant-picker-input>input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-picker-input>input-focused,html[data-kumuhana=pouli] .ant-picker-input>input:focus {
    border-color: var(--yq-theme)
}

.ant-picker-input>input-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-picker-input>input-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-picker-input>input[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input-borderless,.ant-picker-input>input-borderless-disabled,.ant-picker-input>input-borderless-focused,.ant-picker-input>input-borderless:focus,.ant-picker-input>input-borderless:hover,.ant-picker-input>input-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-picker-input>input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-picker-input>input-lg {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-picker-input>input-sm {
    padding: 0 7px
}

.ant-picker-input>input:focus {
    box-shadow: none
}

.ant-picker-input>input[disabled] {
    background: transparent
}

.ant-picker-input:hover .ant-picker-clear {
    opacity: 1
}

.ant-picker-input-placeholder>input {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-picker-large {
    padding: 6.5px 11px 6.5px
}

.ant-picker-large .ant-picker-input>input {
    font-size: 16px
}

.ant-picker-small {
    padding: 0 7px 0
}

.ant-picker-suffix {
    align-self: center;
    margin-left: 4px;
    color: var(--yq-ant-disabled-color);
    line-height: 1;
    pointer-events: none
}

.ant-picker-suffix>* {
    vertical-align: top
}

.ant-picker-clear {
    position: absolute;
    top: 50%;
    right: 0;
    color: var(--yq-ant-disabled-color);
    line-height: 1;
    background: var(--yq-ant-component-background);
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0;
    transition: opacity .3s,color .3s
}

.ant-picker-clear>* {
    vertical-align: top
}

.ant-picker-clear:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-picker-separator {
    position: relative;
    display: inline-block;
    width: 1em;
    height: 16px;
    color: var(--yq-ant-disabled-color);
    font-size: 16px;
    vertical-align: top;
    cursor: default
}

.ant-picker-focused .ant-picker-separator {
    color: var(--yq-ant-text-color-secondary)
}

.ant-picker-disabled .ant-picker-range-separator .ant-picker-separator {
    cursor: not-allowed
}

.ant-picker-range {
    position: relative;
    display: inline-flex
}

.ant-picker-range .ant-picker-clear {
    right: 11px
}

.ant-picker-range:hover .ant-picker-clear {
    opacity: 1
}

.ant-picker-range .ant-picker-active-bar {
    bottom: -1px;
    height: 2px;
    margin-left: 11px;
    background: var(--yq-yuque-green-600);
    opacity: 0;
    transition: all .3s ease-out;
    pointer-events: none
}

.ant-picker-range.ant-picker-focused .ant-picker-active-bar {
    opacity: 1
}

.ant-picker-range-separator {
    align-items: center;
    padding: 0 8px;
    line-height: 1
}

.ant-picker-range.ant-picker-small .ant-picker-clear {
    right: 7px
}

.ant-picker-range.ant-picker-small .ant-picker-active-bar {
    margin-left: 7px
}

.ant-picker-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: absolute;
    z-index: 1050
}

.ant-picker-dropdown-hidden {
    display: none
}

.ant-picker-dropdown-placement-bottomLeft .ant-picker-range-arrow {
    top: 1.66666667px;
    display: block;
    transform: rotate(-45deg)
}

.ant-picker-dropdown-placement-topLeft .ant-picker-range-arrow {
    bottom: 1.66666667px;
    display: block;
    transform: rotate(135deg)
}

.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-topRight,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-topRight {
    animation-name: antSlideDownIn
}

.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-bottomRight,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-bottomRight {
    animation-name: antSlideUpIn
}

.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-topRight {
    animation-name: antSlideDownOut
}

.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-bottomRight {
    animation-name: antSlideUpOut
}

.ant-picker-dropdown-range {
    padding: 6.66666667px 0
}

.ant-picker-dropdown-range-hidden {
    display: none
}

.ant-picker-dropdown .ant-picker-panel>.ant-picker-time-panel {
    padding-top: 4px
}

.ant-picker-ranges {
    margin-bottom: 0;
    padding: 4px 12px;
    overflow: hidden;
    line-height: 34px;
    text-align: left;
    list-style: none
}

.ant-picker-ranges>li {
    display: inline-block
}

.ant-picker-ranges .ant-picker-preset>.ant-tag-blue {
    color: var(--yq-yuque-green-600);
    background: var(--yq-yuque-green-100);
    border-color: var(--yq-yuque-green-300);
    cursor: pointer
}

.ant-picker-ranges .ant-picker-ok {
    float: right;
    margin-left: 8px
}

.ant-picker-range-wrapper {
    display: flex
}

.ant-picker-range-arrow {
    position: absolute;
    z-index: 1;
    display: none;
    width: 10px;
    height: 10px;
    margin-left: 16.5px;
    box-shadow: 2px -2px 6px rgba(0,0,0,.06);
    transition: left .3s ease-out
}

.ant-picker-range-arrow:after {
    position: absolute;
    top: 1px;
    right: 1px;
    width: 10px;
    height: 10px;
    border: 5px solid var(--yq-ant-border-color-split);
    border-color: var(--yq-ant-calendar-bg) var(--yq-ant-calendar-bg) transparent transparent;
    content: ""
}

.ant-picker-panel-container {
    overflow: hidden;
    vertical-align: top;
    background: var(--yq-ant-calendar-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base);
    transition: margin .3s
}

.ant-picker-panel-container .ant-picker-panels {
    display: inline-flex;
    flex-wrap: nowrap;
    direction: ltr
}

.ant-picker-panel-container .ant-picker-panel {
    vertical-align: top;
    background: transparent;
    border-width: 0 0 1px 0;
    border-radius: 0
}

.ant-picker-panel-container .ant-picker-panel-focused {
    border-color: var(--yq-ant-border-color-split)
}

.ant-picker-panel {
    display: inline-flex;
    flex-direction: column;
    text-align: center;
    background: var(--yq-ant-calendar-bg);
    border: 1px solid var(--yq-ant-picker-border-color);
    border-radius: 6px;
    outline: none
}

.ant-picker-panel-focused {
    border-color: var(--yq-yuque-green-600)
}

.ant-picker-date-panel,.ant-picker-decade-panel,.ant-picker-month-panel,.ant-picker-quarter-panel,.ant-picker-time-panel,.ant-picker-week-panel,.ant-picker-year-panel {
    display: flex;
    flex-direction: column;
    width: 280px
}

.ant-picker-header {
    display: flex;
    padding: 0 8px;
    color: var(--yq-ant-heading-color);
    border-bottom: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-header>* {
    flex: none
}

.ant-picker-header button {
    padding: 0;
    color: var(--yq-ant-disabled-color);
    line-height: 40px;
    background: transparent;
    border: 0;
    cursor: pointer;
    transition: color .3s
}

.ant-picker-header>button {
    min-width: 1.6em;
    font-size: 14px
}

.ant-picker-header>button:hover {
    color: var(--yq-yuque-grey-900)
}

.ant-picker-header-view {
    flex: auto;
    font-weight: 500;
    line-height: 40px
}

.ant-picker-header-view button {
    color: inherit;
    font-weight: inherit
}

.ant-picker-header-view button:not(:first-child) {
    margin-left: 8px
}

.ant-picker-header-view button:hover {
    color: var(--yq-yuque-green-600)
}

.ant-picker-next-icon,.ant-picker-prev-icon,.ant-picker-super-next-icon,.ant-picker-super-prev-icon {
    position: relative;
    display: inline-block;
    width: 7px;
    height: 7px
}

.ant-picker-next-icon:before,.ant-picker-prev-icon:before,.ant-picker-super-next-icon:before,.ant-picker-super-prev-icon:before {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 7px;
    height: 7px;
    border: 0 solid currentColor;
    border-width: 1.5px 0 0 1.5px;
    content: ""
}

.ant-picker-super-next-icon:after,.ant-picker-super-prev-icon:after {
    position: absolute;
    top: 4px;
    left: 4px;
    display: inline-block;
    width: 7px;
    height: 7px;
    border: 0 solid currentColor;
    border-width: 1.5px 0 0 1.5px;
    content: ""
}

.ant-picker-prev-icon,.ant-picker-super-prev-icon {
    transform: rotate(-45deg)
}

.ant-picker-next-icon,.ant-picker-super-next-icon {
    transform: rotate(135deg)
}

.ant-picker-content {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse
}

.ant-picker-content td,.ant-picker-content th {
    position: relative;
    min-width: 24px;
    font-weight: 400
}

.ant-picker-content th {
    height: 30px;
    color: var(--yq-yuque-grey-900);
    line-height: 30px
}

.ant-picker-cell {
    padding: 3px 0;
    color: var(--yq-ant-disabled-color);
    cursor: pointer
}

.ant-picker-cell-in-view {
    color: var(--yq-yuque-grey-900)
}

.ant-picker-cell-disabled {
    cursor: not-allowed
}

.ant-picker-cell:before {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    z-index: 1;
    height: 24px;
    transform: translateY(-50%);
    content: ""
}

.ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,.ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner:before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    border: 1px solid var(--yq-yuque-green-600);
    border-radius: 6px;
    content: ""
}

.ant-picker-cell-in-view.ant-picker-cell-in-range {
    position: relative
}

.ant-picker-cell-in-view.ant-picker-cell-in-range:before {
    background: var(--yq-yuque-green-100)
}

.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
    color: var(--yq-ant-text-color-inverse);
    background: var(--yq-yuque-green-600)
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):before,.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):before {
    background: var(--yq-yuque-green-100)
}

.ant-picker-cell-in-view.ant-picker-cell-range-start:before {
    left: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:before {
    right: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range):after {
    position: absolute;
    top: 50%;
    z-index: 0;
    height: 24px;
    border-top: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-bottom: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    transform: translateY(-50%);
    content: ""
}

.ant-picker-cell-range-hover-end:after,.ant-picker-cell-range-hover-start:after,.ant-picker-cell-range-hover:after {
    right: 0;
    left: 2px
}

.ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end:before,.ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start:before,.ant-picker-panel>:not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end:before,.ant-picker-panel>:not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start:before {
    background: var(--yq-ant-picker-basic-cell-hover-with-range-color)
}

.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {
    border-radius: 6px 0 0 6px
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {
    border-radius: 0 6px 6px 0
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after,.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: -1;
    background: var(--yq-ant-picker-basic-cell-hover-with-range-color);
    content: ""
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    right: -6px;
    left: 0
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after {
    right: 0;
    left: -6px
}

.ant-picker-cell-range-hover.ant-picker-cell-range-start:after {
    right: 50%
}

.ant-picker-cell-range-hover.ant-picker-cell-range-end:after {
    left: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:after,.ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child:after {
    left: 6px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child:after {
    right: 6px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.ant-picker-cell-disabled {
    pointer-events: none
}

.ant-picker-cell-disabled .ant-picker-cell-inner {
    color: var(--yq-ant-disabled-color);
    background: transparent
}

.ant-picker-cell-disabled:before {
    background: var(--yq-ant-picker-basic-cell-disabled-bg)
}

.ant-picker-cell-disabled.ant-picker-cell-today .ant-picker-cell-inner:before {
    border-color: var(--yq-ant-disabled-color)
}

.ant-picker-decade-panel .ant-picker-content,.ant-picker-month-panel .ant-picker-content,.ant-picker-quarter-panel .ant-picker-content,.ant-picker-year-panel .ant-picker-content {
    height: 264px
}

.ant-picker-decade-panel .ant-picker-cell-inner,.ant-picker-month-panel .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-inner {
    padding: 0 8px
}

.ant-picker-decade-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-month-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-disabled .ant-picker-cell-inner {
    background: var(--yq-ant-picker-basic-cell-disabled-bg)
}

.ant-picker-quarter-panel .ant-picker-content {
    height: 56px
}

.ant-picker-footer {
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content;
    min-width: 100%;
    line-height: 38px;
    text-align: center;
    border-bottom: 1px solid transparent
}

.ant-picker-panel .ant-picker-footer {
    border-top: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-footer-extra {
    padding: 0 12px;
    line-height: 38px;
    text-align: left
}

.ant-picker-footer-extra:not(:last-child) {
    border-bottom: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-now {
    text-align: left
}

.ant-picker-today-btn {
    color: var(--yq-ant-link-color)
}

.ant-picker-today-btn:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-picker-today-btn:active {
    color: var(--yq-ant-link-active-color)
}

.ant-picker-today-btn.ant-picker-today-btn-disabled {
    color: var(--yq-ant-disabled-color);
    cursor: not-allowed
}

.ant-picker-decade-panel .ant-picker-cell-inner {
    padding: 0 4px
}

.ant-picker-decade-panel .ant-picker-cell:before {
    display: none
}

.ant-picker-month-panel .ant-picker-body,.ant-picker-quarter-panel .ant-picker-body,.ant-picker-year-panel .ant-picker-body {
    padding: 0 8px
}

.ant-picker-month-panel .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-inner {
    width: 60px
}

.ant-picker-month-panel .ant-picker-cell-range-hover-start:after,.ant-picker-quarter-panel .ant-picker-cell-range-hover-start:after,.ant-picker-year-panel .ant-picker-cell-range-hover-start:after {
    left: 14px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px 0 0 6px
}

.ant-picker-month-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-start:after,.ant-picker-quarter-panel .ant-picker-cell-range-hover-end:after,.ant-picker-year-panel .ant-picker-cell-range-hover-end:after {
    right: 14px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 0 6px 6px 0
}

.ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-end:after {
    left: 14px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px 0 0 6px
}

.ant-picker-week-panel .ant-picker-body {
    padding: 8px 12px
}

.ant-picker-week-panel .ant-picker-cell-selected .ant-picker-cell-inner,.ant-picker-week-panel .ant-picker-cell .ant-picker-cell-inner,.ant-picker-week-panel .ant-picker-cell:hover .ant-picker-cell-inner {
    background: transparent!important
}

.ant-picker-week-panel-row td {
    transition: background .3s
}

.ant-picker-week-panel-row:hover td {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-week-panel-row-selected:hover td,.ant-picker-week-panel-row-selected td {
    background: var(--yq-yuque-green-600)
}

.ant-picker-week-panel-row-selected:hover td.ant-picker-cell-week,.ant-picker-week-panel-row-selected td.ant-picker-cell-week {
    color: var(--yq-ant-text-color-inverse)
}

.ant-picker-week-panel-row-selected:hover td.ant-picker-cell-today .ant-picker-cell-inner:before,.ant-picker-week-panel-row-selected td.ant-picker-cell-today .ant-picker-cell-inner:before {
    border-color: var(--yq-ant-text-color-inverse)
}

.ant-picker-week-panel-row-selected:hover td .ant-picker-cell-inner,.ant-picker-week-panel-row-selected td .ant-picker-cell-inner {
    color: var(--yq-ant-text-color-inverse)
}

.ant-picker-date-panel .ant-picker-body {
    padding: 8px 12px
}

.ant-picker-date-panel .ant-picker-content {
    width: 252px
}

.ant-picker-date-panel .ant-picker-content th {
    width: 36px
}

.ant-picker-datetime-panel {
    display: flex
}

.ant-picker-datetime-panel .ant-picker-time-panel {
    border-left: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-datetime-panel .ant-picker-date-panel,.ant-picker-datetime-panel .ant-picker-time-panel {
    transition: opacity .3s
}

.ant-picker-datetime-panel-active .ant-picker-date-panel,.ant-picker-datetime-panel-active .ant-picker-time-panel {
    opacity: .3
}

.ant-picker-datetime-panel-active .ant-picker-date-panel-active,.ant-picker-datetime-panel-active .ant-picker-time-panel-active {
    opacity: 1
}

.ant-picker-time-panel {
    width: auto;
    min-width: auto
}

.ant-picker-time-panel .ant-picker-content {
    display: flex;
    flex: auto;
    height: 224px
}

.ant-picker-time-panel-column {
    flex: 1 0 auto;
    width: 56px;
    margin: 0;
    padding: 0;
    overflow-y: hidden;
    text-align: left;
    list-style: none;
    transition: background .3s
}

.ant-picker-time-panel-column:after {
    display: block;
    height: 196px;
    content: ""
}

.ant-picker-datetime-panel .ant-picker-time-panel-column:after {
    height: 198px
}

.ant-picker-time-panel-column:not(:first-child) {
    border-left: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-time-panel-column-active {
    background: var(--yq-ant-item-active-bg)
}

.ant-picker-time-panel-column:hover {
    overflow-y: auto
}

.ant-picker-time-panel-column>li {
    margin: 0;
    padding: 0
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
    display: block;
    width: 100%;
    height: 28px;
    margin: 0;
    padding: 0 0 0 14px;
    color: var(--yq-yuque-grey-900);
    line-height: 28px;
    border-radius: 0;
    cursor: pointer;
    transition: background .3s
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner:hover {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background: var(--yq-ant-item-active-bg)
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    cursor: not-allowed
}

:root .ant-picker-range-wrapper .ant-picker-month-panel .ant-picker-cell,:root .ant-picker-range-wrapper .ant-picker-year-panel .ant-picker-cell,_:-ms-fullscreen .ant-picker-range-wrapper .ant-picker-month-panel .ant-picker-cell,_:-ms-fullscreen .ant-picker-range-wrapper .ant-picker-year-panel .ant-picker-cell {
    padding: 21px 0
}

.ant-picker-rtl {
    direction: rtl
}

.ant-picker-rtl .ant-picker-suffix {
    margin-right: 4px;
    margin-left: 0
}

.ant-picker-rtl .ant-picker-clear {
    right: auto;
    left: 0
}

.ant-picker-rtl .ant-picker-separator {
    transform: rotate(180deg)
}

.ant-picker-panel-rtl .ant-picker-header-view button:not(:first-child) {
    margin-right: 8px;
    margin-left: 0
}

.ant-picker-rtl.ant-picker-range .ant-picker-clear {
    right: auto;
    left: 11px
}

.ant-picker-rtl.ant-picker-range .ant-picker-active-bar {
    margin-right: 11px;
    margin-left: 0
}

.ant-picker-rtl.ant-picker-range.ant-picker-small .ant-picker-active-bar {
    margin-right: 7px
}

.ant-picker-dropdown-rtl .ant-picker-ranges {
    text-align: right
}

.ant-picker-dropdown-rtl .ant-picker-ranges .ant-picker-ok {
    float: left;
    margin-right: 8px;
    margin-left: 0
}

.ant-picker-panel-rtl {
    direction: rtl
}

.ant-picker-panel-rtl .ant-picker-prev-icon,.ant-picker-panel-rtl .ant-picker-super-prev-icon {
    transform: rotate(135deg)
}

.ant-picker-panel-rtl .ant-picker-next-icon,.ant-picker-panel-rtl .ant-picker-super-next-icon {
    transform: rotate(-45deg)
}

.ant-picker-cell .ant-picker-cell-inner {
    position: relative;
    z-index: 2;
    display: inline-block;
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 6px;
    transition: background .3s,border .3s
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start:before {
    right: 50%;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-end:before {
    right: 0;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-end:before {
    right: 50%;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    right: 0;
    left: -6px
}

.ant-picker-panel-rtl .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after {
    right: -6px;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-range-hover.ant-picker-cell-range-start:after {
    right: 0;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-cell-range-hover.ant-picker-cell-range-end:after {
    right: 50%;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {
    border-radius: 0 6px 6px 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {
    border-radius: 6px 0 0 6px
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):first-child:after {
    right: 6px;
    left: 0;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-left: none;
    border-top-left-radius: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range:after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):last-child:after {
    right: 0;
    left: 6px;
    border-right: none;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-left-radius: 6px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 6px
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-start.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-end.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-end:first-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-start:last-child:after {
    right: 6px;
    left: 6px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px
}

.ant-picker-dropdown-rtl .ant-picker-footer-extra {
    direction: rtl;
    text-align: right
}

.ant-picker-panel-rtl .ant-picker-time-panel {
    direction: ltr
}

.ant-picker.ant-picker-focused {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme)
}

html[data-kumuhana=pouli] .ant-picker.ant-picker-focused {
    border-color: var(--yq-theme)
}

.ant-picker-panel-container {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-picker-panel-container .ant-picker-panel {
    border-width: 0
}

.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
    color: var(--yq-bg-primary)
}

.ant-upload.ant-upload-drag {
    background: transparent
}

.ant-list-item {
    padding-top: 16px;
    padding-bottom: 16px
}

.ant-list.ant-list-bordered {
    border-color: var(--yq-border-primary)
}

.ant-list.ant-list-vertical .ant-list-item-content {
    margin-bottom: 0
}

.ant-list-pagination {
    text-align: left
}

.ant-list {
    background: var(--yq-bg-primary)
}

.ant-list .anticon {
    color: var(--yq-ant-text-color-secondary)
}

.ant-list .ant-empty-image {
    display: none
}

.ant-list .ant-list-item .title {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.ant-list .ant-list-item .title:hover {
    color: var(--yq-text-caption)
}

.ant-list .ant-list-item-content {
    display: block
}

.ant-list .ant-list-pagination {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: -1px;
    padding: 16px;
    text-align: left
}

.larkui-list.larkui-list-loading {
    position: relative;
    min-height: 180px
}

.larkui-list.larkui-list-loading .ant-spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-70%)
}

.larkui-list .larkui-list-block-more {
    position: relative;
    padding: 16px
}

.larkui-list .larkui-list-block-more-btn {
    display: block;
    cursor: pointer;
    color: var(--yq-ant-text-color-secondary)
}

.larkui-list .larkui-list-block-more-btn:hover {
    color: var(--yq-yuque-grey-900)
}

.larkui-list-justify-block .ant-list-item {
    display: block
}

.larkui-card-with-list .ant-list-item {
    padding-left: 16px;
    padding-right: 16px
}

.larkui-sortable [draggable=true] {
    cursor: -webkit-grab;
    cursor: grab
}

.larkui-sortable [draggable=true]:active {
    cursor: -webkit-grabbing;
    cursor: grabbing;
    transform: translate(0)
}

.larkui-sortable .placeholder>* {
    display: none
}

.ant-card .ant-card-head-title {
    line-height: 32px
}

.ant-card .ant-card-head-title,.ant-card .ant-card-head-title h1,.ant-card .ant-card-head-title h2,.ant-card .ant-card-head-title h3,.ant-card .ant-card-head-title h4,.ant-card .ant-card-head-title h5,.ant-card .ant-card-head-title h6 {
    font-size: 14px;
    color: var(--yq-yuque-grey-900)
}

.ant-card .ant-card-extra {
    padding: 10px 0
}

.ant-card .ant-card-extra .ant-btn {
    margin-left: 8px
}

.larkui-card-with-list .ant-card-head {
    margin-bottom: 0
}

.larkui-card-with-list>.ant-card-body {
    padding: 0
}

.larkui-card-with-list .ant-list {
    background: transparent;
    border-radius: 8px
}

.larkui-card-transparent-head {
    border: 0;
    background: transparent
}

.larkui-card-transparent-head .ant-card-head {
    background: transparent;
    border-bottom: 0;
    padding: 0
}

.larkui-card-transparent-head .ant-card-body {
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

.ant-mentions {
    box-sizing: border-box;
    margin: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s;
    position: relative;
    display: inline-block;
    height: auto;
    padding: 0;
    overflow: hidden;
    line-height: 1.5715;
    white-space: pre-wrap;
    vertical-align: bottom
}

.ant-mentions::-moz-placeholder {
    opacity: 1
}

.ant-mentions::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-mentions:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-mentions:placeholder-shown {
    text-overflow: ellipsis
}

.ant-mentions:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-mentions-focused,.ant-mentions:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-mentions-focused,html[data-kumuhana=pouli] .ant-mentions:focus {
    border-color: var(--yq-theme)
}

.ant-mentions-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-mentions-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-mentions[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-mentions[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-mentions-borderless,.ant-mentions-borderless-disabled,.ant-mentions-borderless-focused,.ant-mentions-borderless:focus,.ant-mentions-borderless:hover,.ant-mentions-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-mentions {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-mentions-lg {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-mentions-sm {
    padding: 0 7px
}

.ant-mentions-disabled>textarea {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-mentions-disabled>textarea:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-mentions-focused {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    box-shadow: none
}

.ant-mentions-focused,html[data-kumuhana=pouli] .ant-mentions-focused {
    border-color: var(--yq-theme)
}

.ant-mentions-measure,.ant-mentions>textarea {
    min-height: 30px;
    margin: 0;
    padding: 4px 11px;
    overflow: inherit;
    overflow-x: hidden;
    overflow-y: auto;
    font-weight: inherit;
    font-size: inherit;
    font-family: inherit;
    font-style: inherit;
    font-feature-settings: inherit;
    font-variant: inherit;
    font-size-adjust: inherit;
    font-stretch: inherit;
    line-height: inherit;
    direction: inherit;
    letter-spacing: inherit;
    white-space: inherit;
    text-align: inherit;
    vertical-align: top;
    word-wrap: break-word;
    word-break: inherit;
    -moz-tab-size: inherit;
    -o-tab-size: inherit;
    tab-size: inherit
}

.ant-mentions>textarea {
    width: 100%;
    border: none;
    outline: none;
    resize: none
}

.ant-mentions>textarea::-moz-placeholder {
    opacity: 1
}

.ant-mentions>textarea::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-mentions>textarea:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-mentions>textarea:placeholder-shown {
    text-overflow: ellipsis
}

.ant-mentions-measure {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    color: transparent;
    pointer-events: none
}

.ant-mentions-measure>span {
    display: inline-block;
    min-height: 1em
}

.ant-mentions-dropdown {
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum",;
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1050;
    box-sizing: border-box;
    font-size: 14px;
    font-variant: normal;
    background-color: var(--yq-ant-mentions-dropdown-bg);
    border-radius: 6px;
    outline: none;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-mentions-dropdown-hidden {
    display: none
}

.ant-mentions-dropdown-menu {
    max-height: 250px;
    margin-bottom: 0;
    padding-left: 0;
    overflow: auto;
    list-style: none;
    outline: none
}

.ant-mentions-dropdown-menu-item {
    position: relative;
    display: block;
    min-width: 100px;
    padding: 5px 12px;
    overflow: hidden;
    color: var(--yq-yuque-grey-900);
    font-weight: 400;
    line-height: 1.5715;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: background .3s ease
}

.ant-mentions-dropdown-menu-item:hover {
    background-color: var(--yq-ant-item-hover-bg)
}

.ant-mentions-dropdown-menu-item:first-child {
    border-radius: 6px 6px 0 0
}

.ant-mentions-dropdown-menu-item:last-child {
    border-radius: 0 0 6px 6px
}

.ant-mentions-dropdown-menu-item-disabled,.ant-mentions-dropdown-menu-item-disabled:hover {
    color: var(--yq-ant-disabled-color);
    cursor: not-allowed
}

.ant-mentions-dropdown-menu-item-disabled:hover {
    background-color: var(--yq-ant-mentions-dropdown-bg)
}

.ant-mentions-dropdown-menu-item-selected {
    color: var(--yq-yuque-grey-900);
    font-weight: 600;
    background-color: var(--yq-ant-background-color-light)
}

.ant-mentions-dropdown-menu-item-active {
    background-color: var(--yq-ant-item-hover-bg)
}

.ant-mentions-rtl {
    direction: rtl
}

.ant-mentions>textarea {
    background-color: var(--yq-bg-tertiary)
}

.ant-picker.ant-picker-focused {
    box-shadow: none
}

.ant-picker .ant-picker-suffix {
    color: var(--yq-icon-primary)
}

.ant-select-single .ant-select-selector {
    display: flex
}

.ant-select-single .ant-select-selector .ant-select-selection-search {
    position: absolute;
    top: 0;
    right: 11px;
    bottom: 0;
    left: 11px
}

.ant-select-single .ant-select-selector .ant-select-selection-search-input {
    width: 100%
}

.ant-select-single .ant-select-selector .ant-select-selection-item,.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    padding: 0;
    line-height: 30px;
    transition: all .3s
}

@supports (-moz-appearance: meterbar) {
    .ant-select-single .ant-select-selector .ant-select-selection-item,.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
        line-height:30px
    }
}

.ant-select-single .ant-select-selector .ant-select-selection-item {
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    pointer-events: none
}

.ant-select-single .ant-select-selector .ant-select-selection-item:after,.ant-select-single .ant-select-selector .ant-select-selection-placeholder:after,.ant-select-single .ant-select-selector:after {
    display: inline-block;
    width: 0;
    visibility: hidden;
    content: "\a0"
}

.ant-select-single.ant-select-show-arrow .ant-select-selection-search {
    right: 25px
}

.ant-select-single.ant-select-show-arrow .ant-select-selection-item,.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
    padding-right: 18px
}

.ant-select-single.ant-select-open .ant-select-selection-item {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    width: 100%;
    height: 32px;
    padding: 0 11px
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    height: 30px
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector:after {
    line-height: 30px
}

.ant-select-single.ant-select-customize-input .ant-select-selector:after {
    display: none
}

.ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-search {
    position: static;
    width: 100%
}

.ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder {
    position: absolute;
    right: 0;
    left: 0;
    padding: 0 11px
}

.ant-select-single.ant-select-customize-input .ant-select-selector .ant-select-selection-placeholder:after {
    display: none
}

.ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector {
    height: 40px
}

.ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-item,.ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-placeholder,.ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector:after {
    line-height: 38px
}

.ant-select-single.ant-select-lg:not(.ant-select-customize-input):not(.ant-select-customize-input) .ant-select-selection-search-input {
    height: 38px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector {
    height: 24px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-item,.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-placeholder,.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector:after {
    line-height: 22px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input):not(.ant-select-customize-input) .ant-select-selection-search-input {
    height: 22px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selection-search {
    right: 7px;
    left: 7px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector {
    padding: 0 7px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-search {
    right: 28px
}

.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-item,.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-placeholder {
    padding-right: 21px
}

.ant-select-single.ant-select-lg:not(.ant-select-customize-input) .ant-select-selector {
    padding: 0 11px
}

.ant-select-selection-overflow {
    position: relative;
    display: flex;
    flex: auto;
    flex-wrap: wrap;
    max-width: 100%
}

.ant-select-selection-overflow-item {
    flex: none;
    align-self: center;
    max-width: 100%
}

.ant-select-multiple .ant-select-selector {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 1px 4px
}

.ant-select-show-search.ant-select-multiple .ant-select-selector {
    cursor: text
}

.ant-select-disabled.ant-select-multiple .ant-select-selector {
    background: var(--yq-ant-select-multiple-disabled-background);
    cursor: not-allowed
}

.ant-select-multiple .ant-select-selector:after {
    display: inline-block;
    width: 0;
    margin: 2px 0;
    line-height: 24px;
    content: "\a0"
}

.ant-select-multiple.ant-select-allow-clear .ant-select-selector,.ant-select-multiple.ant-select-show-arrow .ant-select-selector {
    padding-right: 24px
}

.ant-select-multiple .ant-select-selection-item {
    position: relative;
    display: flex;
    flex: none;
    box-sizing: border-box;
    max-width: 100%;
    height: 24px;
    margin-top: 2px;
    margin-bottom: 2px;
    line-height: 22px;
    background: var(--yq-ant-select-selection-item-bg);
    border: 1px solid var(--yq-ant-select-selection-item-border-color);
    border-radius: 6px;
    cursor: default;
    transition: font-size .3s,line-height .3s,height .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    margin-right: 4px;
    padding-left: 8px;
    padding-right: 4px
}

.ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    color: var(--yq-ant-select-multiple-item-disabled-color);
    border-color: var(--yq-ant-select-multiple-item-disabled-border-color);
    cursor: not-allowed
}

.ant-select-multiple .ant-select-selection-item-content {
    display: inline-block;
    margin-right: 4px;
    overflow: hidden;
    white-space: pre;
    text-overflow: ellipsis
}

.ant-select-multiple .ant-select-selection-item-remove {
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-align: center;
    text-transform: none;
    vertical-align: -.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: inline-block;
    color: var(--yq-ant-text-color-secondary);
    font-weight: 700;
    font-size: 10px;
    line-height: inherit;
    cursor: pointer
}

.ant-select-multiple .ant-select-selection-item-remove>* {
    line-height: 1
}

.ant-select-multiple .ant-select-selection-item-remove svg {
    display: inline-block
}

.ant-select-multiple .ant-select-selection-item-remove:before {
    display: none
}

.ant-select-multiple .ant-select-selection-item-remove .ant-select-multiple .ant-select-selection-item-remove-icon {
    display: block
}

.ant-select-multiple .ant-select-selection-item-remove>.anticon {
    vertical-align: -.2em
}

.ant-select-multiple .ant-select-selection-item-remove:hover {
    color: var(--yq-ant-icon-color-hover)
}

.ant-select-multiple .ant-select-selection-overflow-item+.ant-select-selection-overflow-item .ant-select-selection-search {
    margin-left: 0
}

.ant-select-multiple .ant-select-selection-search {
    position: relative;
    max-width: 100%;
    margin-top: 2px;
    margin-bottom: 2px;
    margin-left: 7px
}

.ant-select-multiple .ant-select-selection-search-input,.ant-select-multiple .ant-select-selection-search-mirror {
    height: 24px;
    font-family: Chinese Quote,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
    line-height: 24px;
    transition: all .3s
}

.ant-select-multiple .ant-select-selection-search-input {
    width: 100%;
    min-width: 4.1px
}

.ant-select-multiple .ant-select-selection-search-mirror {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    white-space: pre;
    visibility: hidden
}

.ant-select-multiple .ant-select-selection-placeholder {
    position: absolute;
    top: 50%;
    right: 11px;
    left: 11px;
    transform: translateY(-50%);
    transition: all .3s
}

.ant-select-multiple.ant-select-lg .ant-select-selector:after {
    line-height: 32px
}

.ant-select-multiple.ant-select-lg .ant-select-selection-item {
    line-height: 30px
}

.ant-select-multiple.ant-select-lg .ant-select-selection-search {
    height: 32px;
    line-height: 32px
}

.ant-select-multiple.ant-select-lg .ant-select-selection-search-input,.ant-select-multiple.ant-select-lg .ant-select-selection-search-mirror {
    height: 32px;
    line-height: 30px
}

.ant-select-multiple.ant-select-sm .ant-select-selector:after {
    line-height: 16px
}

.ant-select-multiple.ant-select-sm .ant-select-selection-item {
    height: 16px;
    line-height: 14px
}

.ant-select-multiple.ant-select-sm .ant-select-selection-search {
    height: 16px;
    line-height: 16px
}

.ant-select-multiple.ant-select-sm .ant-select-selection-search-input,.ant-select-multiple.ant-select-sm .ant-select-selection-search-mirror {
    height: 16px;
    line-height: 14px
}

.ant-select-multiple.ant-select-sm .ant-select-selection-placeholder {
    left: 7px
}

.ant-select-multiple.ant-select-sm .ant-select-selection-search {
    margin-left: 3px
}

.ant-select-multiple.ant-select-lg .ant-select-selection-item {
    height: 32px;
    line-height: 32px
}

.ant-select-disabled .ant-select-selection-item-remove {
    display: none
}

.ant-select {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    display: inline-block;
    cursor: pointer
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    position: relative;
    background-color: var(--yq-ant-select-background);
    border: 1px solid var(--yq-ant-border-color-base);
    border-radius: 6px;
    transition: all .3s cubic-bezier(.645,.045,.355,1)
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector input {
    cursor: pointer
}

.ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    cursor: text
}

.ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector input {
    cursor: auto
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: var(--yq-theme)
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: var(--yq-ant-disabled-color);
    background: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed
}

.ant-select-multiple.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: var(--yq-ant-select-multiple-disabled-background)
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector input {
    cursor: not-allowed
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    margin: 0;
    padding: 0;
    background: transparent;
    border: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input::-webkit-search-cancel-button {
    display: none;
    -webkit-appearance: none
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-select-selection-item {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

@media (-ms-high-contrast:none) {
    .ant-select-selection-item,.ant-select-selection-item ::-ms-backdrop {
        flex: auto
    }
}

.ant-select-selection-placeholder {
    flex: 1;
    overflow: hidden;
    color: var(--yq-ant-input-placeholder-color);
    white-space: nowrap;
    text-overflow: ellipsis;
    pointer-events: none
}

@media (-ms-high-contrast:none) {
    .ant-select-selection-placeholder,.ant-select-selection-placeholder ::-ms-backdrop {
        flex: auto
    }
}

.ant-select-arrow {
    display: inline-block;
    color: inherit;
    font-style: normal;
    line-height: 0;
    text-transform: none;
    vertical-align: -.125em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    top: 53%;
    right: 11px;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: var(--yq-ant-disabled-color);
    font-size: 12px;
    line-height: 1;
    text-align: center;
    pointer-events: none
}

.ant-select-arrow>* {
    line-height: 1
}

.ant-select-arrow svg {
    display: inline-block
}

.ant-select-arrow:before {
    display: none
}

.ant-select-arrow .ant-select-arrow-icon {
    display: block
}

.ant-select-arrow .anticon {
    vertical-align: top;
    transition: transform .3s
}

.ant-select-arrow .anticon>svg {
    vertical-align: top
}

.ant-select-arrow .anticon:not(.ant-select-suffix) {
    pointer-events: auto
}

.ant-select-disabled .ant-select-arrow {
    cursor: not-allowed
}

.ant-select-clear {
    position: absolute;
    top: 50%;
    right: 11px;
    z-index: 1;
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: var(--yq-ant-disabled-color);
    font-size: 12px;
    font-style: normal;
    line-height: 1;
    text-align: center;
    text-transform: none;
    background: var(--yq-ant-select-clear-background);
    cursor: pointer;
    opacity: 0;
    transition: color .3s ease,opacity .15s ease;
    text-rendering: auto
}

.ant-select-clear:before {
    display: block
}

.ant-select-clear:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-select:hover .ant-select-clear {
    opacity: 1
}

.ant-select-dropdown {
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum",;
    position: absolute;
    top: -9999px;
    left: -9999px;
    z-index: 1050;
    box-sizing: border-box;
    padding: 4px 0;
    overflow: hidden;
    font-size: 14px;
    font-variant: normal;
    background-color: var(--yq-ant-select-dropdown-bg);
    border-radius: 6px;
    outline: none;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-select-dropdown.slide-up-appear.slide-up-appear-active.ant-select-dropdown-placement-bottomLeft,.ant-select-dropdown.slide-up-enter.slide-up-enter-active.ant-select-dropdown-placement-bottomLeft {
    animation-name: antSlideUpIn
}

.ant-select-dropdown.slide-up-appear.slide-up-appear-active.ant-select-dropdown-placement-topLeft,.ant-select-dropdown.slide-up-enter.slide-up-enter-active.ant-select-dropdown-placement-topLeft {
    animation-name: antSlideDownIn
}

.ant-select-dropdown.slide-up-leave.slide-up-leave-active.ant-select-dropdown-placement-bottomLeft {
    animation-name: antSlideUpOut
}

.ant-select-dropdown.slide-up-leave.slide-up-leave-active.ant-select-dropdown-placement-topLeft {
    animation-name: antSlideDownOut
}

.ant-select-dropdown-hidden {
    display: none
}

.ant-select-dropdown-empty {
    color: var(--yq-ant-disabled-color)
}

.ant-select-item-empty {
    color: var(--yq-yuque-grey-900);
    color: var(--yq-ant-disabled-color)
}

.ant-select-item,.ant-select-item-empty {
    position: relative;
    display: block;
    min-height: 32px;
    padding: 5px 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px
}

.ant-select-item {
    color: var(--yq-yuque-grey-900);
    cursor: pointer;
    transition: background .3s ease
}

.ant-select-item-group {
    color: var(--yq-ant-text-color-secondary);
    font-size: 12px;
    cursor: default
}

.ant-select-item-option {
    display: flex
}

.ant-select-item-option-content {
    flex: auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ant-select-item-option-state {
    flex: none
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background-color: var(--yq-ant-item-hover-bg)
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: var(--yq-yuque-grey-900);
    font-weight: 600;
    background-color: var(--yq-yuque-grey-300)
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state {
    color: var(--yq-yuque-green-600)
}

.ant-select-item-option-disabled {
    color: var(--yq-ant-disabled-color);
    cursor: not-allowed
}

.ant-select-item-option-grouped {
    padding-left: 24px
}

.ant-select-lg {
    font-size: 16px
}

.ant-select-borderless .ant-select-selector {
    background-color: transparent!important;
    border-color: transparent!important;
    box-shadow: none!important
}

.ant-select-rtl {
    direction: rtl
}

.ant-select-rtl .ant-select-arrow,.ant-select-rtl .ant-select-clear {
    right: auto;
    left: 11px
}

.ant-select-dropdown-rtl {
    direction: rtl
}

.ant-select-dropdown-rtl .ant-select-item-option-grouped {
    padding-right: 24px;
    padding-left: 12px
}

.ant-select-rtl.ant-select-multiple.ant-select-allow-clear .ant-select-selector,.ant-select-rtl.ant-select-multiple.ant-select-show-arrow .ant-select-selector {
    padding-right: 4px;
    padding-left: 24px
}

.ant-select-rtl.ant-select-multiple .ant-select-selection-item {
    text-align: right
}

.ant-select-rtl.ant-select-multiple .ant-select-selection-item-content {
    margin-right: 0;
    margin-left: 4px;
    text-align: right
}

.ant-select-rtl.ant-select-multiple .ant-select-selection-search-mirror {
    right: 0;
    left: auto
}

.ant-select-rtl.ant-select-multiple .ant-select-selection-placeholder {
    right: 11px;
    left: auto
}

.ant-select-rtl.ant-select-multiple.ant-select-sm .ant-select-selection-placeholder {
    right: 7px
}

.ant-select-rtl.ant-select-single .ant-select-selector .ant-select-selection-item,.ant-select-rtl.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    right: 0;
    left: 9px;
    text-align: right
}

.ant-select-rtl.ant-select-single.ant-select-show-arrow .ant-select-selection-search {
    right: 11px;
    left: 25px
}

.ant-select-rtl.ant-select-single.ant-select-show-arrow .ant-select-selection-item,.ant-select-rtl.ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
    padding-right: 0;
    padding-left: 18px
}

.ant-select-rtl.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-search {
    right: 6px
}

.ant-select-rtl.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-item,.ant-select-rtl.ant-select-single.ant-select-sm:not(.ant-select-customize-input).ant-select-show-arrow .ant-select-selection-placeholder {
    padding-right: 0;
    padding-left: 21px
}

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector .anticon-check,.ant-select-single:not(.ant-select-customize-input) .ant-select-selector .larkicon-svg-check {
    display: none
}

.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    opacity: .5
}

.ant-select-arrow {
    color: var(--yq-icon-primary)
}

.ant-select-dropdown {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-select-dropdown .ant-select-item-option .anticon-check,.ant-select-dropdown .ant-select-item-option .larkicon-svg-check {
    display: inline-block;
    margin-right: 8px;
    visibility: hidden
}

.ant-select-dropdown .ant-select-item-option-selected .anticon-check,.ant-select-dropdown .ant-select-item-option-selected .larkicon-svg-check {
    visibility: visible
}

.ant-select-arrow,.ant-select-clear {
    width: 16px;
    height: 16px;
    margin-top: -5px;
    font-size: 11px;
    color: var(--yq-yuque-grey-700);
}

.ant-select-clear {
    margin-top: -8px
}

.ant-form-item .ant-mentions,.ant-form-item textarea.ant-input {
    height: auto
}

.ant-form-item .ant-upload {
    background: transparent
}

.ant-form-item .ant-upload.ant-upload-drag {
    background: var(--yq-ant-background-color-light)
}

.ant-form-item input[type=checkbox],.ant-form-item input[type=radio] {
    width: 14px;
    height: 14px
}

.ant-form-item .ant-checkbox-inline,.ant-form-item .ant-radio-inline {
    display: inline-block;
    margin-left: 8px;
    font-weight: 400;
    vertical-align: middle;
    cursor: pointer
}

.ant-form-item .ant-checkbox-inline:first-child,.ant-form-item .ant-radio-inline:first-child {
    margin-left: 0
}

.ant-form-item .ant-checkbox-vertical,.ant-form-item .ant-radio-vertical {
    display: block
}

.ant-form-item .ant-checkbox-vertical+.ant-checkbox-vertical,.ant-form-item .ant-radio-vertical+.ant-radio-vertical {
    margin-left: 0
}

.ant-form-item .ant-input-number+.ant-form-text {
    margin-left: 8px
}

.ant-form-item .ant-input-number-handler-wrap {
    z-index: 2
}

.ant-form-item .ant-cascader-picker,.ant-form-item .ant-select {
    width: 100%
}

.ant-form-item .ant-input-group .ant-cascader-picker,.ant-form-item .ant-input-group .ant-select,.ant-form-item .ant-picker-calendar-month-select,.ant-form-item .ant-picker-calendar-year-select {
    width: auto
}

.ant-form-inline {
    display: flex;
    flex-wrap: wrap
}

.ant-form-inline .ant-form-item {
    flex: none;
    flex-wrap: nowrap;
    margin-right: 16px;
    margin-bottom: 0
}

.ant-form-inline .ant-form-item-with-help {
    margin-bottom: 24px
}

.ant-form-inline .ant-form-item>.ant-form-item-control,.ant-form-inline .ant-form-item>.ant-form-item-label {
    display: inline-block;
    vertical-align: top
}

.ant-form-inline .ant-form-item>.ant-form-item-label {
    flex: none
}

.ant-form-inline .ant-form-item .ant-form-item-has-feedback,.ant-form-inline .ant-form-item .ant-form-text {
    display: inline-block
}

.ant-form-horizontal .ant-form-item-label {
    flex-grow: 0
}

.ant-form-horizontal .ant-form-item-control {
    flex: 1 1 0
}

.ant-form-vertical .ant-form-item {
    flex-direction: column
}

.ant-form-vertical .ant-form-item-label>label {
    height: auto
}

.ant-col-24.ant-form-item-label,.ant-col-xl-24.ant-form-item-label,.ant-form-vertical .ant-form-item-label {
    padding: 0 0 8px;
    line-height: 1.5715;
    white-space: normal;
    text-align: left
}

.ant-col-24.ant-form-item-label>label,.ant-col-xl-24.ant-form-item-label>label,.ant-form-vertical .ant-form-item-label>label {
    margin: 0
}

.ant-col-24.ant-form-item-label>label:after,.ant-col-xl-24.ant-form-item-label>label:after,.ant-form-vertical .ant-form-item-label>label:after {
    display: none
}

.ant-form-rtl.ant-col-24.ant-form-item-label,.ant-form-rtl.ant-col-xl-24.ant-form-item-label,.ant-form-rtl.ant-form-vertical .ant-form-item-label {
    text-align: right
}

@media (max-width: 575px) {
    .ant-form-item .ant-form-item-label {
        padding:0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-form-item .ant-form-item-label>label {
        margin: 0
    }

    .ant-form-item .ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-form-item .ant-form-item-label {
        text-align: right
    }

    .ant-form .ant-form-item {
        flex-wrap: wrap
    }

    .ant-form .ant-form-item .ant-form-item-control,.ant-form .ant-form-item .ant-form-item-label {
        flex: 0 0 100%;
        max-width: 100%
    }

    .ant-col-xs-24.ant-form-item-label {
        padding: 0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-col-xs-24.ant-form-item-label>label {
        margin: 0
    }

    .ant-col-xs-24.ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-col-xs-24.ant-form-item-label {
        text-align: right
    }
}

@media (max-width: 767px) {
    .ant-col-sm-24.ant-form-item-label {
        padding:0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-col-sm-24.ant-form-item-label>label {
        margin: 0
    }

    .ant-col-sm-24.ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-col-sm-24.ant-form-item-label {
        text-align: right
    }
}

@media (max-width: 991px) {
    .ant-col-md-24.ant-form-item-label {
        padding:0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-col-md-24.ant-form-item-label>label {
        margin: 0
    }

    .ant-col-md-24.ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-col-md-24.ant-form-item-label {
        text-align: right
    }
}

@media (max-width: 1199px) {
    .ant-col-lg-24.ant-form-item-label {
        padding:0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-col-lg-24.ant-form-item-label>label {
        margin: 0
    }

    .ant-col-lg-24.ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-col-lg-24.ant-form-item-label {
        text-align: right
    }
}

@media (max-width: 1599px) {
    .ant-col-xl-24.ant-form-item-label {
        padding:0 0 8px;
        line-height: 1.5715;
        white-space: normal;
        text-align: left
    }

    .ant-col-xl-24.ant-form-item-label>label {
        margin: 0
    }

    .ant-col-xl-24.ant-form-item-label>label:after {
        display: none
    }

    .ant-form-rtl.ant-col-xl-24.ant-form-item-label {
        text-align: right
    }
}

.ant-form-item-explain.ant-form-item-explain-error {
    color: var(--yq-ant-error-color)
}

.ant-form-item-explain.ant-form-item-explain-warning {
    color: var(--yq-ant-warning-color)
}

.ant-form-item-has-feedback .ant-input {
    padding-right: 24px
}

.ant-form-item-has-feedback .ant-input-affix-wrapper .ant-input-suffix {
    padding-right: 18px
}

.ant-form-item-has-feedback .ant-input-search:not(.ant-input-search-enter-button) .ant-input-suffix {
    right: 28px
}

.ant-form-item-has-feedback .ant-switch {
    margin: 2px 0 4px
}

.ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-arrow,.ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-clear,.ant-form-item-has-feedback>.ant-select .ant-select-arrow,.ant-form-item-has-feedback>.ant-select .ant-select-clear {
    right: 32px
}

.ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-selection-selected-value,.ant-form-item-has-feedback>.ant-select .ant-select-selection-selected-value {
    padding-right: 42px
}

.ant-form-item-has-feedback .ant-cascader-picker-arrow {
    margin-right: 19px
}

.ant-form-item-has-feedback .ant-cascader-picker-clear {
    right: 32px
}

.ant-form-item-has-feedback .ant-picker,.ant-form-item-has-feedback .ant-picker-large {
    padding-right: 29.2px
}

.ant-form-item-has-feedback .ant-picker-small {
    padding-right: 25.2px
}

.ant-form-item-has-feedback.ant-form-item-has-error .ant-form-item-children-icon,.ant-form-item-has-feedback.ant-form-item-has-success .ant-form-item-children-icon,.ant-form-item-has-feedback.ant-form-item-has-warning .ant-form-item-children-icon,.ant-form-item-has-feedback.ant-form-item-is-validating .ant-form-item-children-icon {
    position: absolute;
    top: 50%;
    right: 0;
    z-index: 1;
    width: 32px;
    height: 20px;
    margin-top: -10px;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    visibility: visible;
    animation: zoomIn .3s cubic-bezier(.12,.4,.29,1.46);
    pointer-events: none
}

.ant-form-item-has-success.ant-form-item-has-feedback .ant-form-item-children-icon {
    color: var(--yq-ant-success-color);
    animation-name: diffZoomIn1!important
}

.ant-form-item-has-warning .ant-form-item-split {
    color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input,.ant-form-item-has-warning .ant-input-affix-wrapper,.ant-form-item-has-warning .ant-input-affix-wrapper:hover,.ant-form-item-has-warning .ant-input:hover {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-affix-wrapper-focused,.ant-form-item-has-warning .ant-input-affix-wrapper:focus,.ant-form-item-has-warning .ant-input-focused,.ant-form-item-has-warning .ant-input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input-affix-wrapper-focused,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input-affix-wrapper:focus,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input-focused,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input:focus {
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-affix-wrapper-disabled,.ant-form-item-has-warning .ant-input-affix-wrapper-disabled:hover,.ant-form-item-has-warning .ant-input-disabled,.ant-form-item-has-warning .ant-input-disabled:hover {
    background-color: var(--yq-ant-input-disabled-bg);
    border-color: var(--yq-ant-input-border-color)
}

.ant-form-item-has-warning .ant-input-affix-wrapper-disabled:hover input:focus,.ant-form-item-has-warning .ant-input-affix-wrapper-disabled input:focus {
    box-shadow: none!important
}

.ant-form-item-has-warning .ant-calendar-picker-open .ant-calendar-picker-input {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-calendar-picker-open .ant-calendar-picker-input {
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-prefix {
    color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-group-addon {
    color: var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .has-feedback {
    color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning.ant-form-item-has-feedback .ant-form-item-children-icon {
    color: var(--yq-ant-warning-color);
    animation-name: diffZoomIn3!important
}

.ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-warning-color)!important
}

.ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector,.ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector {
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-number,.ant-form-item-has-warning .ant-picker {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-number-focused,.ant-form-item-has-warning .ant-input-number:focus,.ant-form-item-has-warning .ant-picker-focused,.ant-form-item-has-warning .ant-picker:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input-number-focused,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-input-number:focus,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-picker-focused,html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-picker:focus {
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-input-number:not([disabled]):hover,.ant-form-item-has-warning .ant-picker:not([disabled]):hover {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-warning .ant-cascader-picker:focus .ant-cascader-input {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-warning-color);
    border-color: var(--yq-ant-warning-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-warning .ant-cascader-picker:focus .ant-cascader-input {
    border-color: var(--yq-ant-warning-color)
}

.ant-form-item-has-error .ant-form-item-split {
    color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input,.ant-form-item-has-error .ant-input-affix-wrapper,.ant-form-item-has-error .ant-input-affix-wrapper:hover,.ant-form-item-has-error .ant-input:hover {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-affix-wrapper-focused,.ant-form-item-has-error .ant-input-affix-wrapper:focus,.ant-form-item-has-error .ant-input-focused,.ant-form-item-has-error .ant-input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input-affix-wrapper-focused,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input-affix-wrapper:focus,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input-focused,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input:focus {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-affix-wrapper-disabled,.ant-form-item-has-error .ant-input-affix-wrapper-disabled:hover,.ant-form-item-has-error .ant-input-disabled,.ant-form-item-has-error .ant-input-disabled:hover {
    background-color: var(--yq-ant-input-disabled-bg);
    border-color: var(--yq-ant-input-border-color)
}

.ant-form-item-has-error .ant-input-affix-wrapper-disabled:hover input:focus,.ant-form-item-has-error .ant-input-affix-wrapper-disabled input:focus {
    box-shadow: none!important
}

.ant-form-item-has-error .ant-calendar-picker-open .ant-calendar-picker-input {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-error .ant-calendar-picker-open .ant-calendar-picker-input {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-prefix {
    color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-group-addon {
    color: var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .has-feedback {
    color: var(--yq-ant-error-color)
}

.ant-form-item-has-error.ant-form-item-has-feedback .ant-form-item-children-icon {
    color: var(--yq-ant-error-color);
    animation-name: diffZoomIn2!important
}

.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-error-color)!important
}

.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector,.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open .ant-select-selector {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-group-addon .ant-select.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    background-color: inherit;
    border: 0;
    box-shadow: none
}

.ant-form-item-has-error .ant-select.ant-select-auto-complete .ant-input:focus {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-number,.ant-form-item-has-error .ant-picker {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-number-focused,.ant-form-item-has-error .ant-input-number:focus,.ant-form-item-has-error .ant-picker-focused,.ant-form-item-has-error .ant-picker:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input-number-focused,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-input-number:focus,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-picker-focused,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-picker:focus {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-input-number:not([disabled]):hover,.ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor,.ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):hover,.ant-form-item-has-error .ant-picker:not([disabled]):hover {
    background-color: var(--yq-ant-input-bg);
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-mention-wrapper.ant-mention-active:not([disabled]) .ant-mention-editor,.ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

.ant-form-item-has-error .ant-cascader-picker:hover .ant-cascader-picker-label:hover+.ant-cascader-input.ant-input,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-mention-wrapper.ant-mention-active:not([disabled]) .ant-mention-editor,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor:not([disabled]):focus {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-cascader-picker:focus .ant-cascader-input {
    background-color: var(--yq-ant-input-bg);
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-error-color);
    border-color: var(--yq-ant-error-color);
    box-shadow: none
}

.ant-form-item-has-error .ant-transfer-list,html[data-kumuhana=pouli] .ant-form-item-has-error .ant-cascader-picker:focus .ant-cascader-input {
    border-color: var(--yq-ant-error-color)
}

.ant-form-item-has-error .ant-transfer-list-search:not([disabled]) {
    border-color: var(--yq-ant-input-border-color)
}

.ant-form-item-has-error .ant-transfer-list-search:not([disabled]):hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-form-item-has-error .ant-transfer-list-search:not([disabled]):focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-form-item-has-error .ant-transfer-list-search:not([disabled]):focus {
    border-color: var(--yq-theme)
}

.ant-form-item-has-error .ant-radio-button-wrapper {
    border-color: var(--yq-ant-error-color)!important
}

.ant-form-item-has-error .ant-radio-button-wrapper:not(:first-child):before {
    background-color: var(--yq-ant-error-color)
}

.ant-form-item-is-validating.ant-form-item-has-feedback .ant-form-item-children-icon {
    display: inline-block;
    color: var(--yq-yuque-green-600)
}

.ant-form {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum"
}

.ant-form legend {
    display: block;
    width: 100%;
    margin-bottom: 20px;
    padding: 0;
    color: var(--yq-ant-text-color-secondary);
    font-size: 16px;
    line-height: inherit;
    border: 0;
    border-bottom: 1px solid var(--yq-ant-border-color-base)
}

.ant-form label {
    font-size: 14px
}

.ant-form input[type=search] {
    box-sizing: border-box
}

.ant-form input[type=checkbox],.ant-form input[type=radio] {
    line-height: normal
}

.ant-form input[type=file] {
    display: block
}

.ant-form input[type=range] {
    display: block;
    width: 100%
}

.ant-form select[multiple],.ant-form select[size] {
    height: auto
}

.ant-form input[type=checkbox]:focus,.ant-form input[type=file]:focus,.ant-form input[type=radio]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.ant-form output {
    display: block;
    padding-top: 15px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715
}

.ant-form .ant-form-text {
    display: inline-block;
    padding-right: 8px
}

.ant-form-small .ant-form-item-label>label {
    height: 24px
}

.ant-form-small .ant-form-item-control-input {
    min-height: 24px
}

.ant-form-large .ant-form-item-label>label {
    height: 40px
}

.ant-form-large .ant-form-item-control-input {
    min-height: 40px
}

.ant-form-item {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    margin-bottom: 24px;
    vertical-align: top
}

.ant-form-item-with-help {
    margin-bottom: 0
}

.ant-form-item-hidden,.ant-form-item-hidden.ant-row {
    display: none
}

.ant-form-item-label {
    display: inline-block;
    flex-grow: 0;
    overflow: hidden;
    white-space: nowrap;
    text-align: right;
    vertical-align: middle
}

.ant-form-item-label-left {
    text-align: left
}

.ant-form-item-label>label {
    position: relative;
    display: inline-flex;
    align-items: center;
    height: 32px;
    color: var(--yq-ant-heading-color);
    font-size: 14px
}

.ant-form-item-label>label>.anticon {
    font-size: 14px;
    vertical-align: top
}

.ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional):before {
    display: inline-block;
    margin-right: 4px;
    color: var(--yq-red-600);
    font-size: 14px;
    font-family: SimSun,sans-serif;
    line-height: 1;
    content: "*"
}

.ant-form-hide-required-mark .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional):before {
    display: none
}

.ant-form-item-label>label .ant-form-item-optional {
    display: inline-block;
    margin-left: 4px;
    color: var(--yq-ant-text-color-secondary)
}

.ant-form-hide-required-mark .ant-form-item-label>label .ant-form-item-optional {
    display: none
}

.ant-form-item-label>label .ant-form-item-tooltip {
    color: var(--yq-ant-text-color-secondary);
    cursor: help;
    writing-mode: horizontal-tb;
    margin-left: 4px
}

.ant-form-item-label>label:after {
    content: ":";
    position: relative;
    top: -.5px;
    margin: 0 8px 0 2px
}

.ant-form-item-label>label.ant-form-item-no-colon:after {
    content: " "
}

.ant-form-item-control {
    display: flex;
    flex-direction: column;
    flex-grow: 1
}

.ant-form-item-control:first-child:not([class^=ant-col-]):not([class*=" ant-col-"]) {
    width: 100%
}

.ant-form-item-control-input {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 32px
}

.ant-form-item-control-input-content {
    flex: auto;
    max-width: 100%
}

.ant-form-item-explain,.ant-form-item-extra {
    clear: both;
    min-height: 24px;
    color: var(--yq-ant-text-color-secondary);
    font-size: 14px;
    line-height: 1.5715;
    transition: color .3s cubic-bezier(.215,.61,.355,1);
    padding-top: 0
}

.ant-form-item .ant-input-textarea-show-count:after {
    margin-bottom: -22px
}

.ant-show-help-appear,.ant-show-help-enter,.ant-show-help-leave {
    animation-duration: .3s;
    animation-fill-mode: both;
    animation-play-state: paused
}

.ant-show-help-appear.ant-show-help-appear-active,.ant-show-help-enter.ant-show-help-enter-active {
    animation-name: antShowHelpIn;
    animation-play-state: running
}

.ant-show-help-leave.ant-show-help-leave-active {
    animation-name: antShowHelpOut;
    animation-play-state: running;
    pointer-events: none
}

.ant-show-help-appear,.ant-show-help-enter {
    opacity: 0
}

.ant-show-help-appear,.ant-show-help-enter,.ant-show-help-leave {
    animation-timing-function: cubic-bezier(.645,.045,.355,1)
}

@keyframes antShowHelpIn {
    0% {
        transform: translateY(-5px);
        opacity: 0
    }

    to {
        transform: translateY(0);
        opacity: 1
    }
}

@keyframes antShowHelpOut {
    to {
        transform: translateY(-5px);
        opacity: 0
    }
}

@keyframes diffZoomIn1 {
    0% {
        transform: scale(0)
    }

    to {
        transform: scale(1)
    }
}

@keyframes diffZoomIn2 {
    0% {
        transform: scale(0)
    }

    to {
        transform: scale(1)
    }
}

@keyframes diffZoomIn3 {
    0% {
        transform: scale(0)
    }

    to {
        transform: scale(1)
    }
}

.ant-form-rtl {
    direction: rtl
}

.ant-form-rtl .ant-form-item-label {
    text-align: left
}

.ant-form-rtl .ant-form-item-label>label.ant-form-item-required:before {
    margin-right: 0;
    margin-left: 4px
}

.ant-form-rtl .ant-form-item-label>label:after {
    margin: 0 2px 0 8px
}

.ant-form-rtl .ant-form-item-label>label .ant-form-item-optional {
    margin-right: 4px;
    margin-left: 0
}

.ant-col-rtl .ant-form-item-control:first-child {
    width: 100%
}

.ant-form-rtl .ant-form-item-has-feedback .ant-input {
    padding-right: 11px;
    padding-left: 24px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-input-affix-wrapper .ant-input-suffix {
    padding-right: 11px;
    padding-left: 18px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-input-affix-wrapper .ant-input {
    padding: 0
}

.ant-form-rtl .ant-form-item-has-feedback .ant-input-search:not(.ant-input-search-enter-button) .ant-input-suffix {
    right: auto;
    left: 28px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-input-number {
    padding-left: 18px
}

.ant-form-rtl .ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-arrow,.ant-form-rtl .ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-clear,.ant-form-rtl .ant-form-item-has-feedback>.ant-select .ant-select-arrow,.ant-form-rtl .ant-form-item-has-feedback>.ant-select .ant-select-clear {
    right: auto;
    left: 32px
}

.ant-form-rtl .ant-form-item-has-feedback :not(.ant-input-group-addon)>.ant-select .ant-select-selection-selected-value,.ant-form-rtl .ant-form-item-has-feedback>.ant-select .ant-select-selection-selected-value {
    padding-right: 0;
    padding-left: 42px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-cascader-picker-arrow {
    margin-right: 0;
    margin-left: 19px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-cascader-picker-clear {
    right: auto;
    left: 32px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-picker,.ant-form-rtl .ant-form-item-has-feedback .ant-picker-large {
    padding-right: 11px;
    padding-left: 29.2px
}

.ant-form-rtl .ant-form-item-has-feedback .ant-picker-small {
    padding-right: 7px;
    padding-left: 25.2px
}

.ant-form-rtl .ant-form-item-has-feedback.ant-form-item-has-error .ant-form-item-children-icon,.ant-form-rtl .ant-form-item-has-feedback.ant-form-item-has-success .ant-form-item-children-icon,.ant-form-rtl .ant-form-item-has-feedback.ant-form-item-has-warning .ant-form-item-children-icon,.ant-form-rtl .ant-form-item-has-feedback.ant-form-item-is-validating .ant-form-item-children-icon {
    right: auto;
    left: 0
}

.ant-form-rtl.ant-form-inline .ant-form-item {
    margin-right: 0;
    margin-left: 16px
}

.ant-legacy-form .ant-legacy-form-item,form .ant-cascader-picker,form .ant-select {
    display: block
}

.larkui-legacy-form .has-error .ant-input:not([disabled]):focus {
    box-shadow: none
}

.has-error .ant-input:not([disabled]),.has-error .ant-input:not([disabled]):focus,.has-error .ant-input:not([disabled]):hover {
    border-color: var(--yq-border-primary)
}

.has-error .ant-input:not([disabled]):focus {
    box-shadow: none
}

.has-error .ant-input-group-addon {
    color: var(--yq-yuque-grey-900);
    background-color: var(--yq-bg-secondary)
}

.has-error .ant-input-group-addon,.has-error .ant-select-selection {
    border-color: var(--yq-border-primary)
}

.has-error .ant-form-split {
    color: var(--yq-yuque-grey-900)
}

.has-error .ant-select-arrow {
    color: var(--yq-text-caption)
}

[data-kumuhana=pouli] .has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper,[data-kumuhana=pouli] .has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:hover,[data-kumuhana=pouli] .has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input,[data-kumuhana=pouli] .has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover {
    background-color: var(--yq-bg-tertiary)
}

[data-kumuhana=pouli] .has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper-focused,[data-kumuhana=pouli] .has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:focus,[data-kumuhana=pouli] .has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused,[data-kumuhana=pouli] .has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus {
    border-color: var(--yq-function-error)
}

.ant-dropdown-trigger {
    cursor: pointer
}

.ant-dropdown .check-item {
    position: relative;
    padding-left: 32px
}

.ant-dropdown .check-item .larkicon-svg-check {
    position: absolute;
    left: 14px;
    top: 50%;
    margin-top: -4px
}

.ant-dropdown .ant-dropdown-menu-item:hover,.ant-dropdown .ant-dropdown-menu-submenu-title:hover {
    background-color: var(--yq-bg-primary-hover)
}

.ant-dropdown .ant-dropdown-menu-item-selected,.ant-dropdown .ant-dropdown-menu-item-selected>a {
    color: inherit;
    background-color: var(--yq-bg-primary-hover)
}

.ant-dropdown .ant-dropdown-menu-item-selected:hover,.ant-dropdown .ant-dropdown-menu-item-selected>a:hover {
    background-color: var(--yq-bg-primary-hover)
}

.ant-dropdown .anticon-check {
    color: var(--yq-yuque-green-600);
    margin-left: 6px
}

.ant-dropdown-menu-submenu-popup .ant-dropdown-menu,.ant-dropdown>.ant-dropdown-menu {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-dropdown-menu-item-divider {
    margin-left: 12px;
    margin-right: 12px
}

.larkui-dropdown-selector .larkui-dropdown-selector-trigger {
    font-size: 14px;
    color: var(--yq-yuque-grey-900)
}

.larkui-dropdown-selector .larkui-dropdown-selector-trigger-icon {
    margin-left: 4px
}

.larkui-dropdown-selector-menu.ant-dropdown {
    min-width: 128px
}

.larkui-dropdown-selector-menu.ant-dropdown .ant-dropdown-menu-item {
    position: relative;
    padding-right: 32px
}

.larkui-dropdown-selector-menu .larkui-dropdown-selector-option {
    display: block
}

.larkui-dropdown-selector-menu .larkui-dropdown-selector-option-name {
    font-weight: 400
}

.larkui-dropdown-selector-menu .larkui-dropdown-selector-option-checked {
    position: absolute;
    top: 50%;
    right: 4px;
    margin-top: -11px;
    color: var(--yq-yuque-green-600)
}

.ant-tabs-tab {
    color: var(--yq-text-body)
}

.ant-tabs-tab .ant-tabs-tab-btn,.ant-tabs-tab:hover {
    color: var(--yq-text-caption)
}

.ant-tabs-tab.ant-tabs-tab-active {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn,.ant-tabs-tab.ant-tabs-tab-active:hover {
    color: var(--yq-text-primary)
}

.ant-tabs-ink-bar {
    background: var(--yq-yuque-grey-9)
}

.ant-tabs-extra-content .ant-btn {
    margin-left: 8px
}

html[data-kumuhana=pouli] .larkui-segmented {
    background-color: var(--yq-bg-secondary)
}

html[data-kumuhana=pouli] .larkui-segmented .rc-segmented-item-selected,html[data-kumuhana=pouli] .larkui-segmented .rc-segmented-thumb {
    background-color: var(--yq-bg-primary-hover)
}

.larkui-segmented {
    border-radius: 8px;
    overflow: hidden
}

.larkui-segmented .rc-segmented-item {
    color: var(--yq-text-primary);
    border-radius: 6px;
    line-height: 1;
    display: flex;
    align-items: center
}

.larkui-segmented .rc-segmented-item:hover {
    color: var(--yq-text-primary)
}

.larkui-segmented .rc-segmented-item-selected {
    color: var(--yq-text-primary);
    font-weight: 500;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02)
}

.larkui-segmented .rc-segmented-item-selected:hover {
    color: var(--yq-text-primary)
}

.larkui-segmented .rc-segmented-item-label {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 100%
}

.larkui-segmented .rc-segmented-item-label:after {
    font-weight: 500;
    content: attr(title);
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none
}

.larkui-segmented .rc-segmented-item svg {
    display: block
}

.larkui-segmented-small .rc-segmented-item {
    padding: 2px 8px;
    font-size: 12px
}

.larkui-segmented-middle .rc-segmented-item {
    padding: 6px 8px;
    font-size: 14px
}

.larkui-segmented-large .rc-segmented-item {
    padding: 9px 8px;
    font-size: 16px
}

.larkui-segmented-block {
    display: flex;
    width: 100%
}

.larkui-segmented-block .rc-segmented-item {
    flex: 1
}

.larkui-segmented .rc-segmented-thumb {
    border-radius: 6px;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02)
}

.larkui-segmented .larkui-segmented-label {
    display: flex;
    align-items: center
}

.larkui-segmented .larkui-segmented-label svg {
    margin-right: 3px
}

.larkui-segmented .rc-segmented-thumb-motion-appear-active,.larkui-segmented .rc-segmented-thumb-motion-enter-active {
    transition: transform .22s ease-in-out,width .22s ease-in-out;
    will-change: transform,width
}

.larkui-segemented-multiple .rc-segmented-item {
    margin: 0 1px
}

.larkui-segemented-multiple .rc-segmented-item:first-child {
    margin: 0 1px 0 0
}

.larkui-segemented-multiple .rc-segmented-item:last-child {
    margin: 0 0 0 1px
}

.ant-segmented {
    border-radius: 8px;
    overflow: hidden
}

.ant-segmented .ant-segmented-item {
    color: var(--yq-text-primary);
    border-radius: 6px;
    line-height: 1;
    display: flex;
    align-items: center
}

.ant-segmented .ant-segmented-item:hover {
    color: var(--yq-text-primary)
}

.ant-segmented .ant-segmented-item-selected {
    color: var(--yq-text-primary);
    font-weight: 500;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02)
}

.ant-segmented .ant-segmented-item-selected:hover {
    color: var(--yq-text-primary)
}

.ant-segmented .ant-segmented-item-label {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 100%
}

.ant-segmented .ant-segmented-item-label:after {
    font-weight: 500;
    content: attr(title);
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none
}

.ant-segmented .ant-segmented-item svg {
    display: block
}

.ant-segmented-small .ant-segmented-item {
    padding: 2px 8px;
    font-size: 12px
}

.ant-segmented-middle .ant-segmented-item {
    padding: 6px 8px;
    font-size: 14px
}

.ant-segmented-large .ant-segmented-item {
    padding: 9px 8px;
    font-size: 16px
}

.ant-segmented-block {
    display: flex;
    width: 100%
}

.ant-segmented-block .ant-segmented-item {
    flex: 1
}

.ant-segmented .ant-segmented-thumb {
    border-radius: 6px;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02)
}

.ant-segmented .ant-segmented-label {
    display: flex;
    align-items: center
}

.ant-segmented .ant-segmented-label svg {
    margin-right: 3px
}

.ant-segmented .ant-segmented-thumb-motion-appear-active,.ant-segmented .ant-segmented-thumb-motion-enter-active {
    transition: transform .22s ease-in-out,width .22s ease-in-out;
    will-change: transform,width
}

.ant-segemented-multiple .ant-segmented-item {
    margin: 0 1px
}

.ant-segemented-multiple .ant-segmented-item:first-child {
    margin: 0 1px 0 0
}

.ant-segemented-multiple .ant-segmented-item:last-child {
    margin: 0 0 0 1px
}

.ant-switch:focus {
    outline: 0;
    box-shadow: 0 0 2px hsla(0,0%,100%,.2)
}

.ant-cascader {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum"
}

.ant-cascader-input.ant-input {
    position: static;
    width: 100%;
    padding-right: 24px;
    background-color: transparent!important;
    cursor: pointer
}

.ant-cascader-picker-show-search .ant-cascader-input.ant-input {
    position: relative
}

.ant-cascader-picker {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    display: inline-block;
    background-color: var(--yq-ant-cascader-bg);
    border-radius: 6px;
    outline: 0;
    cursor: pointer;
    transition: color .3s
}

.ant-cascader-picker-with-value .ant-cascader-picker-label {
    color: transparent
}

.ant-cascader-picker-disabled {
    color: var(--yq-ant-disabled-color);
    background: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed
}

.ant-cascader-picker-disabled .ant-cascader-input {
    cursor: not-allowed
}

.ant-cascader-picker:focus .ant-cascader-input {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color);
    border-color: var(--yq-theme);
    box-shadow: none
}

html[data-kumuhana=pouli] .ant-cascader-picker:focus .ant-cascader-input {
    border-color: var(--yq-theme)
}

.ant-cascader-picker-borderless .ant-cascader-input {
    border-color: transparent!important;
    box-shadow: none!important
}

.ant-cascader-picker-show-search.ant-cascader-picker-focused {
    color: var(--yq-ant-disabled-color)
}

.ant-cascader-picker-label {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 20px;
    margin-top: -10px;
    padding: 0 20px 0 12px;
    overflow: hidden;
    line-height: 20px;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ant-cascader-picker-clear {
    position: absolute;
    top: 50%;
    right: 12px;
    z-index: 2;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: var(--yq-ant-disabled-color);
    font-size: 12px;
    line-height: 12px;
    background: var(--yq-ant-component-background);
    cursor: pointer;
    opacity: 0;
    transition: color .3s ease,opacity .15s ease
}

.ant-cascader-picker-clear:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-cascader-picker:hover .ant-cascader-picker-clear {
    opacity: 1
}

.ant-cascader-picker-arrow {
    position: absolute;
    top: 50%;
    right: 12px;
    z-index: 1;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    color: var(--yq-ant-disabled-color);
    font-size: 12px;
    line-height: 12px
}

.ant-cascader-picker-label:hover+.ant-cascader-input:not(.ant-cascader-picker-disabled .ant-cascader-picker-label:hover+.ant-cascader-input) {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-cascader-picker-small .ant-cascader-picker-arrow,.ant-cascader-picker-small .ant-cascader-picker-clear {
    right: 8px
}

.ant-cascader-menus {
    position: absolute;
    z-index: 1050;
    font-size: 14px;
    white-space: nowrap;
    background: var(--yq-ant-cascader-menu-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-cascader-menus ol,.ant-cascader-menus ul {
    margin: 0;
    list-style: none
}

.ant-cascader-menus-empty,.ant-cascader-menus-hidden {
    display: none
}

.ant-cascader-menus.slide-up-appear.slide-up-appear-active.ant-cascader-menus-placement-bottomLeft,.ant-cascader-menus.slide-up-enter.slide-up-enter-active.ant-cascader-menus-placement-bottomLeft {
    animation-name: antSlideUpIn
}

.ant-cascader-menus.slide-up-appear.slide-up-appear-active.ant-cascader-menus-placement-topLeft,.ant-cascader-menus.slide-up-enter.slide-up-enter-active.ant-cascader-menus-placement-topLeft {
    animation-name: antSlideDownIn
}

.ant-cascader-menus.slide-up-leave.slide-up-leave-active.ant-cascader-menus-placement-bottomLeft {
    animation-name: antSlideUpOut
}

.ant-cascader-menus.slide-up-leave.slide-up-leave-active.ant-cascader-menus-placement-topLeft {
    animation-name: antSlideDownOut
}

.ant-cascader-menu {
    display: inline-block;
    min-width: 111px;
    height: 180px;
    margin: 0;
    padding: 4px 0;
    overflow: auto;
    vertical-align: top;
    list-style: none;
    border-right: 1px solid var(--yq-ant-cascader-menu-border-color-split);
    -ms-overflow-style: -ms-autohiding-scrollbar
}

.ant-cascader-menu:first-child {
    border-radius: 6px 0 0 6px
}

.ant-cascader-menu:last-child {
    margin-right: -1px;
    border-right-color: transparent;
    border-radius: 0 6px 6px 0
}

.ant-cascader-menu:only-child {
    border-radius: 6px
}

.ant-cascader-menu-item {
    padding: 5px 12px;
    overflow: hidden;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: all .3s
}

.ant-cascader-menu-item:hover {
    background: var(--yq-ant-item-hover-bg)
}

.ant-cascader-menu-item-disabled {
    color: var(--yq-ant-disabled-color);
    cursor: not-allowed
}

.ant-cascader-menu-item-disabled:hover {
    background: transparent
}

.ant-cascader-menu-empty .ant-cascader-menu-item {
    color: var(--yq-ant-disabled-color);
    cursor: default;
    pointer-events: none
}

.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    font-weight: 600;
    background-color: var(--yq-yuque-green-100)
}

.ant-cascader-menu-item-expand {
    position: relative;
    padding-right: 24px
}

.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,.ant-cascader-menu-item-loading-icon {
    position: absolute;
    right: 12px;
    color: var(--yq-ant-text-color-secondary);
    font-size: 10px
}

.ant-cascader-menu-item-disabled.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,.ant-cascader-menu-item-disabled.ant-cascader-menu-item-loading-icon {
    color: var(--yq-ant-disabled-color)
}

.ant-cascader-menu-item .ant-cascader-menu-item-keyword {
    color: var(--yq-red-600)
}

.ant-cascader-picker-rtl .ant-cascader-input.ant-input {
    padding-right: 11px;
    padding-left: 24px;
    text-align: right
}

.ant-cascader-picker-rtl {
    direction: rtl
}

.ant-cascader-picker-rtl .ant-cascader-picker-label {
    padding: 0 12px 0 20px;
    text-align: right
}

.ant-cascader-picker-rtl .ant-cascader-picker-arrow,.ant-cascader-picker-rtl .ant-cascader-picker-clear {
    right: auto;
    left: 12px
}

.ant-cascader-picker-rtl.ant-cascader-picker-small .ant-cascader-picker-arrow,.ant-cascader-picker-rtl.ant-cascader-picker-small .ant-cascader-picker-clear {
    right: auto;
    left: 8px
}

.ant-cascader-menu-rtl .ant-cascader-menu {
    direction: rtl;
    border-right: none;
    border-left: 1px solid var(--yq-ant-border-color-split)
}

.ant-cascader-menu-rtl .ant-cascader-menu:first-child {
    border-radius: 0 6px 6px 0
}

.ant-cascader-menu-rtl .ant-cascader-menu:last-child {
    margin-right: 0;
    margin-left: -1px;
    border-left-color: transparent;
    border-radius: 6px 0 0 6px
}

.ant-cascader-menu-rtl .ant-cascader-menu:only-child {
    border-radius: 6px
}

.ant-cascader-menu-rtl .ant-cascader-menu-item-expand {
    padding-right: 12px;
    padding-left: 24px
}

.ant-cascader-menu-rtl .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,.ant-cascader-menu-rtl .ant-cascader-menu-item-loading-icon {
    right: auto;
    left: 12px
}

.ant-cascader-menu-rtl .ant-cascader-menu-item-loading-icon {
    transform: scaleY(-1)
}

.ant-cascader-menus {
    background-color: var(--yq-popover-bg);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    background-color: var(--yq-bg-primary-hover)
}

.ant-cascader-picker-arrow {
    color: var(--yq-icon-primary)
}

.ant-skeleton-content .ant-skeleton-title {
    height: 24px
}

.ant-skeleton-with-avatar .ant-skeleton-content .ant-skeleton-title {
    margin-top: 8px
}

.larkui-skeleton-title-sm .ant-skeleton-content .ant-skeleton-title {
    height: 16px
}

.larkui-skeleton-title-sm .ant-skeleton-with-avatar .ant-skeleton-content .ant-skeleton-title {
    margin-top: 12px
}

.ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-paragraph>li,.ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-title {
    background-image: linear-gradient(90deg,var(--yq-yuque-grey-2) 25%,var(--yq-yuque-grey-4) 37%,var(--yq-yuque-grey-2) 63%)
}

html[data-kumuhana=pouli] .ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-paragraph>li,html[data-kumuhana=pouli] .ant-skeleton.ant-skeleton-active .ant-skeleton-content .ant-skeleton-title {
    background-image: linear-gradient(90deg,hsla(0,0%,100%,.04) 25%,hsla(0,0%,100%,.08) 37%,hsla(0,0%,100%,.04) 63%)
}

.ant-skeleton-content .ant-skeleton-paragraph>li,.ant-skeleton-content .ant-skeleton-title,.ant-skeleton-element .ant-skeleton-avatar,.ant-skeleton-element .ant-skeleton-button,.ant-skeleton-element .ant-skeleton-image,.ant-skeleton-element .ant-skeleton-input,.ant-skeleton-header .ant-skeleton-avatar {
    background-color: var(--yq-bg-tertiary);
    border-radius: 6px
}

.ant-skeleton-header .ant-skeleton-avatar-sm {
    width: 16px;
    height: 16px;
    line-height: 16px
}

.ant-breadcrumb .ant-breadcrumb-link {
    font-weight: 700;
    color: var(--yq-ant-heading-color)
}

.ant-breadcrumb .ant-breadcrumb-link>a {
    font-weight: 400;
    color: var(--yq-yuque-grey-900)
}

.ant-breadcrumb .ant-breadcrumb-link>a:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-breadcrumb-separator {
    color: var(--yq-border-primary)
}

.larkui-synopsis-wrap {
    overflow: hidden
}

.larkui-synopsis-wrap:hover {
    overflow: auto
}

.larkui-synopsis-wrap::-webkit-scrollbar {
    height: 4px;
    width: 4px
}

.larkui-synopsis-wrap::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,.26)
}

.larkui-toc-sidebar .larkui-sidebar-content {
    margin-left: -22px
}

.larkui-synopsis {
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 12px;
    list-style: none;
    padding: 0;
    margin: 0
}

.larkui-synopsis .larkui-synopsis-item {
    line-height: 24px;
    color: var(--yq-yuque-grey-900)
}

.larkui-synopsis .larkui-synopsis-item-collapsible-link {
    width: calc(100% - 8px);
    padding: 0 10px 0 0;
    margin-left: 8px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.larkui-synopsis .larkui-synopsis-item-collapsible-link a {
    color: var(--yq-yuque-grey-900)
}

.larkui-synopsis .larkui-synopsis-item-collapsible-link a:hover {
    color: var(--yq-ant-text-color-secondary)
}

.larkui-synopsis .larkui-synopsis-item-link {
    margin: 0 10px 0 28px;
    width: calc(100% - 28px);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.larkui-synopsis .larkui-synopsis-item-link a {
    color: var(--yq-yuque-grey-900)
}

.larkui-synopsis .larkui-synopsis-item-link a:hover {
    color: var(--yq-ant-text-color-secondary)
}

.larkui-synopsis .larkui-synopsis-item-link-1 {
    padding-left: 0
}

.larkui-synopsis .larkui-synopsis-item-link-2 {
    padding-left: 1.2em
}

.larkui-synopsis .larkui-synopsis-item-link-3 {
    padding-left: 2.4em
}

.larkui-synopsis .larkui-synopsis-item-link-4 {
    padding-left: 3.6em
}

.larkui-synopsis .larkui-synopsis-item-link-5 {
    padding-left: 4.8em
}

.larkui-synopsis .larkui-synopsis-item-active a {
    color: var(--yq-yuque-green-6)
}

.larkui-synopsis .larkui-synopsis-item-active a:hover {
    color: var(--yq-yuque-green-4)
}

.larkui-synopsis .larkui-synopsis-progress-bar {
    border-left: 2px solid var(--yq-border-primary)
}

.larkui-synopsis .larkui-synopsis-progress-bar-highlighted {
    border-left: 2px solid var(--yq-yuque-green-600)
}