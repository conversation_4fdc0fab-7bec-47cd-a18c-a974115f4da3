<template>
  <span>
    <span :key="pos + 'wrapperrSpan'" v-if="paramQuantity === -1">
      <span class="txtItem" style="color: #4108f6">{{ titleArr[0] }}</span>
      <span
        v-for="(paramItem, index) in operatorParamsList"
        :key="pos + '_operator_' + index + 'wrapper'"
      >
        <span v-if="index !== 0">,</span>
        <OperatorVariable
          :key="pos + '_operator_' + index"
          :pos="pos + '_operator_' + index"
          :dataSource="paramItem"
          :locked="locked"
          :isTrack="isTrack"
          :enumDictName="enumDictName"
          @onChange="onChildChange"
          :hideFrontBtn="false"
          :hideEndBtn="true"
          :signValue="''"
          :predefineLine="predefineLine"
          :predefineCon="predefineCon"
          :preIndex="preIndex"
          ref="ov_ref"
        />
        <OperatorParamTool
          v-if="!locked"
          @click.stop
          @onChange="onOperatorParamChange"
          :paramLength="operatorParamsList.length"
          :enumDictName="enumDictName"
          :index="index"
        />
      </span>
      <span class="txtItem" style="color: #4108f6">{{ titleArr[1] }}</span>
    </span>
    <span
      v-else-if="
        paramQuantity !== -1 &&
        titleArr.length > 0 &&
        operatorParamsList &&
        operatorParamsList.length >= 0
      "
    >
      <span
        v-for="(item, index) in titleArr"
        :key="pos + '_operator_' + index + 'wrapperrSpan'"
      >
        <span class="txtItem" style="color: #4108f6">{{ item }}</span>
        <OperatorVariable
          v-if="operatorParamsList[index]"
          :key="pos + '_operator_' + index"
          :pos="pos + '_operator_' + index"
          :dataSource="operatorParamsList[index]"
          :locked="locked"
          :isTrack="isTrack"
          :enumDictName="enumDictName"
          @onChange="onChildChange"
          :hideEndBtn="true"
          :signValue="''"
          :predefineLine="predefineLine"
          :predefineCon="predefineCon"
          :preIndex="preIndex"
          ref="ov_ref"
        />
      </span>
    </span>
    <span v-else class="txtItem">请选择</span>
  </span>
</template>

<script setup>
import OperatorParamTool from "@/components/ruleEditCom/ruleItemList/operationMenu/operatorParamTool.vue";
import OperatorVariable from "./operatorVariable.vue";
import { defineProps, defineEmits, computed } from "vue";

// 定义组件的 props
const props = defineProps({
  titleArr: {
    type: Array,
    default: () => [],
  },
  hideEndBtn: {
    type: Boolean,
    default: false,
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  paramQuantity: {
    type: Number,
  },
  operatorParamsList: {
    type: Array,
    default: () => [],
  },
  pos: {
    type: String,
  },
  enumDictName: {
    type: String,
    default: "",
  },
  predefineLine: {
    type: String,
    default: "",
  },
  predefineCon: {
    type: String,
    default: "",
  },
  preIndex: {
    type: Number,
    default: 0,
  },
});

// 定义组件的 emits
const emit = defineEmits(["onChildChange", "onOperatorParamChange"]);

const onChildChange = (pos, newValueObj, finalValueType) => {
  emit("onChildChange", pos, newValueObj, finalValueType);
};
const onOperatorParamChange = (key, index) => {
  emit("onOperatorParamChange", key, index);
};
</script>
