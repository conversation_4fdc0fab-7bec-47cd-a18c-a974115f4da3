<template>
  <a-table
    class="editor-table vali"
    :columns="columns"
    :data-source="validata"
    bordered
    :pagination="pagination"
    size="small"
    :rowKey="(r, i) => i.toString()"
    :scroll="{ x: '100%', y: 'calc(100vh - 270px)' }"
  >
    <template #default="{ text, record }">
      <span>text:{{ text }} record:{{ record }}</span>
    </template>
  </a-table>
</template>

<script setup>
import { cloneDeep } from "lodash";

// 定义 props
const props = defineProps({
  validate: {
    type: Array,
    default: () => [],
  },
  props_columns: {
    type: Array,
    default: () => [],
  },
  props_data: {
    type: Array,
    default: () => [],
  },
});

// 定义响应式数据
const data = ref(cloneDeep(props.props_data));
const validata = ref(getVali(props.validate));
const columns = ref(getCol());
const pagination = ref({
  defaultPageSize: 10,
  showTotal: (total) => `共${total}条`,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ["10", "20", "50", "100", "200", "500", "1000"],
});

// 监听 props_columns 的变化
watch(
  () => props.props_columns,
  (val) => {
    columns.value = getCol();
  },
  { deep: true }
);

// 监听 props_data 的变化
watch(
  () => props.props_data,
  (val) => {
    data.value = cloneDeep(val);
  },
  { deep: true }
);

// 监听 validate 的变化
watch(
  () => props.validate,
  (val) => {
    validata.value = getVali(val);
  },
  { deep: true }
);

// 获取验证结果
function getVali(validate = []) {
  const keylist = [];
  Object.keys(data.value[0]).map((item) =>
    keylist.push(item.substring(0, 8))
  );
  keylist.shift();
  const setKeyList = [...new Set(keylist)];

  const list = [];

  for (let i = 0; i < validate.length; i++) {
    const element = validate[i];
    const obj = {};
    for (let index = 0; index < element.length; index++) {
      const ele = element[index];
      obj[setKeyList[index]] = ele.msg;
    }
    list.push(obj);
  }

  return list;
}

// 获取列配置
function getCol() {
  let col = [];
  col = props.props_columns;
  col.map((item) => (item.children = ""));
  return col;
}
</script>

<style scoped lang="scss">
.editor-table.vali ::v-deep {
  .editable-row-operations a {
    margin-right: 8px;
  }
  .ant-table-pagination.ant-pagination {
    position: absolute !important;
    bottom: 11px !important;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
