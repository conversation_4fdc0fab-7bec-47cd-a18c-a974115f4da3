<template>
  <div class="bpmn">
    <div class="tool">
      <a-tooltip effect="light">
        <template #title>全屏编辑</template>
        <a-button
                size="default"
                @click="fullScreen"
                v-if="!isFullscreenMode"
        >
          <template #icon>
            <FullscreenOutlined />
          </template>
        </a-button>
      </a-tooltip>
      <a-button-group key="align-control">
        <a-tooltip effect="light">
          <template #title>向左对齐</template>
          <a-button
            size="default"
            class="align align-left"
            @click="elementsAlign('left')"
          >
            <template #icon>
              <BorderLeftOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>向右对齐</template>
          <a-button
            size="default"
            class="align align-right"
            @click="elementsAlign('right')"
          >
            <template #icon>
              <BorderRightOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>向上对齐</template>
          <a-button
            size="default"
            class="align align-top"
            @click="elementsAlign('top')"
          >
            <template #icon>
              <BorderTopOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>向下对齐</template>
          <a-button
            size="default"
            class="align align-bottom"
            @click="elementsAlign('bottom')"
          >
            <template #icon>
              <BorderBottomOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>水平居中</template>
          <a-button
            size="default"
            class="align align-center"
            @click="elementsAlign('center')"
          >
            <template #icon>
              <BorderHorizontalOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>垂直居中</template>
          <a-button
            size="default"
            class="align align-middle"
            @click="elementsAlign('middle')"
          >
            <template #icon>
              <BorderVerticleOutlined />
            </template>
          </a-button>
        </a-tooltip>
      </a-button-group>
      <a-button-group key="scale-control">
        <a-tooltip effect="light">
          <template #title>缩小视图</template>
          <a-button
            size="default"
            :disabled="defaultZoom < 0.2"
            @click="processZoomOut()"
          >
            <template #icon>
              <ZoomOutOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-button size="default">{{
          Math.floor(defaultZoom * 10 * 10) + "%"
        }}</a-button>
        <a-tooltip effect="light">
          <template #title>放大视图</template>
          <a-button
            size="default"
            :disabled="defaultZoom > 4"
            @click="processZoomIn()"
          >
            <template #icon>
              <ZoomInOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>重置视图并居中</template>
          <a-button size="default" @click="processReZoom()">
            <template #icon>
              <OneToOneOutlined />
            </template>
          </a-button>
        </a-tooltip>
      </a-button-group>
      <a-button-group key="stack-control">
        <a-tooltip effect="light">
          <template #title>撤销</template>
          <a-button
            size="default"
            :disabled="!revocable"
            @click="processUndo()"
          >
            <template #icon>
              <ArrowLeftOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>恢复</template>
          <a-button
            size="default"
            :disabled="!recoverable"
            @click="processRedo()"
          >
            <template #icon>
              <ArrowRightOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>重新绘制</template>
          <a-button size="default" @click="processRestart">
            <template #icon>
              <SyncOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>导入xml文件</template>
          <a-button
            size="default"
            @click="refFile.click()"
          >
            <template #icon>
              <ImportOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip effect="light">
          <template #title>导出</template>

          <a-dropdown>
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item key="xml">导出为XML文件</a-menu-item>
              <a-menu-item key="svg">导出为SVG文件</a-menu-item>
              <a-menu-item key="bpmn">导出为BPMN文件</a-menu-item>
            </a-menu>
          </template>

          <a-button
            size="default"
            @click="handleExport"
            ><template #icon> <ExportOutlined /> </template
          ></a-button>
        </a-dropdown>
        </a-tooltip>
      </a-button-group>
      <a-button size="default" type="primary" @click="toSaveFlow"
          style="margin-left: 15px;" >保存</a-button>
      <a-button
              v-if="checkPermi([RULE_PERMISSION.RULE.SUBMIT])"
              type="primary"
              size="default"
              style="margin-left: 5px"
              @click="handleSubmitRule()"
      >提交</a-button>
      <input
        type="file"
        id="files"
        ref="refFile"
        style="display: none"
        accept=".xml"
        :value="fileList"
        @change="importLocalFile"
      />
    </div>
    <div class="canvas" ref="canvas"></div>

    <a-drawer
      title="路由条件设置"
      :width="drawerWidth"
      placement="right"
      :push="false"
      :open="drawer"
      wrapClassName="ant-drawer-dir"
      @close="closeAntDrawer"
    >
      <a-form>
        <a-form-item label="业务描述">
          <a-input autocomplete="off" v-model:value="conditionDesc"></a-input>
        </a-form-item>
        <a-form-item label="优先级" style="margin-top: 20px">
          <a-input
            v-model:value="conditionPriority"
          ></a-input>
        </a-form-item>
        <a-row v-show="conditionRadioShow">
          <a-col>
            <a-form-item>
              <a-radio-group v-model:value="radio" @change="agreeChange">
                <a-radio value="pass">总是通过</a-radio>
                <a-radio value="condition">编辑条件</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item v-if="isEdit">
          <a-form-item label="编辑条件"></a-form-item>
          <FlowRule
            ref="flowRule"
            :ruleInfo="ruleInfo"
            @setConditions="setConditions"
            :conditions="conditions"
            :activeShapeId="activeShape ? activeShape.id : ''"
          />
        </a-form-item>
        <div style="position: absolute; bottom: 24px; right: 24px;">
          <a-button style="margin-right: 8px;" @click="closeAntDrawer">取消</a-button>
          <a-button type="primary" @click="routerDialogSave">保存</a-button>
        </div>
      </a-form>
    </a-drawer>

    <a-drawer
      :title="dialogTitle + '信息'"
      :open="dialogVisible"
      placement="right"
      class="bottom node-info-drawer"
      @close="treeDrawerClosed"
      :width="isShowTreeContaiter || isShowSubFlowTreeContaiter ? '55%' : '30%'"
    >
      <a-form class="demo-form-inline" style="padding-bottom: 0">
        <a-row style="width: 100%; margin-bottom: 12px">
          <a-col :span="12" style="padding-right: 8px">
            <a-form-item label="节点标识">
              <a-input
                autocomplete="off"
                v-model:value="form.id"
                disabled="disabled"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12" style="padding-left: 8px">
            <a-form-item label="节点名称">
              <a-input
                autocomplete="off"
                v-model:value="form.name"
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row
          v-show="isShowTreeContaiter"
          style="width: 100%; margin-bottom: 12px"
        >
          <a-col style="width: 100%">
            <a-form-item label="绑定规则"></a-form-item>
            <treeTransfer
              :from-data="fromData"
              :to-data="toData"
              :tree-transfer-obj="treeTransferObj"
              :is-internal-change="isInternalChange"
              @onChange="handleTreeTransferChange"
            />
          </a-col>
        </a-row>
        <a-row
          v-show="isShowSubFlowTreeContaiter"
          style="width: 100%; margin-bottom: 12px"
        >
          <a-col style="width: 100%">
            <a-form-item label="绑定子规则流"></a-form-item>
            <treeTransfer
              :from-data="fromSubFlowData"
              :to-data="toSubFlowData"
              :tree-transfer-obj="treeTransferObj"
              :is-internal-change="isInternalChange"
              :is-sub-flow="true"
              @onChange="handleTreeTransferChange"
            />
          </a-col>
        </a-row>
        <a-row v-show="isShowTypeContaiter">
          <a-form-item label="分支类型" laba-width="100">
            <a-select v-model:value="form.type" @change="setGatewayType">
              <a-select-option
                v-for="item in gatewayTypeOption"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
            <a-alert
              title="修改分支类型会重置该节点相关联的路由条件，需重新设置相关路由条件"
              type="warning"
              :closable="false"
              v-show="gatewayTypeAlert"
              style="font-size: 12px; width: 336px; padding: 5px 0;"
            />
          </a-form-item>
        </a-row>
        <a-row>
          <a-form-item label="">
            <div style="position: relative; width: 100%;margin-top: 15px">
              <a-textarea
                :rows="4"
                :maxlength="500"
                v-model:value="form.desc"
                style="width: 100%"
                placeholder="请输入节点描述"
              />
              <div style="position: absolute; bottom: -15px; left: 8px; color: #999; font-size: 12px;">
                {{ form.desc ? form.desc.length : 0 }}/500
              </div>
            </div>
          </a-form-item>
        </a-row>
        <div style="position: absolute; bottom: 24px; right: 24px;">
          <a-button style="margin-right: 8px;" @click="treeDrawerClosed">取消</a-button>
          <a-button type="primary" @click="dialogSave">保存</a-button>
        </div>
      </a-form>
    </a-drawer>

    <a-drawer
      :open="errorDrawer"
      placement="right"
      @close="errorCloseDrawer"
      :mask="false"
      :wrapperClosable="false"
      :modal-append-to-body="false"
      ref="errDrawer"
    >
      <template #title>
        <span style="color: #ff0000; margin-right: 10px; font-size: 16px"
          ><CloseCircleOutlined /></span
        >错误
      </template>
      <template #default>
        <!-- <a-alert
          v-for="(item, index) in flowErrorMsg"
          :key="index + '-' + item"
          :title="item"
          type="error"
          show-icon
        /> -->
        <div v-html="errContent"></div>
      </template>
    </a-drawer>
    <a-modal v-model:open="confirmOpen" title="提示" @ok="confirmOk">
      <p style="color: rgb(250, 173, 20)">
        <ExclamationCircleOutlined /> 规则流绘制错误, 是否继续保存?
      </p>
    </a-modal>
    <!-- 使用FullModel组件实现全屏展示 -->
    <FullModel
        :isModalVisible="isModalVisible"
        :isFullscreen="isFullscreen"
        :titleText="titleText"
        :handleCancel="handleModalCancel"
        :onFullscreenToggle="onFullscreenToggle"
        :showFullBtn="false"
        :showFooter="false"
    >
      <template #default>
        <div class="fulltab" :style="{maxHeight: isFullscreen ? '90vh' : '60vh'}">
          <index
                  :ruleInfo="ruleInfo"
                  :isFullscreenMode="true"
                  @commandStack-changed="$emit('commandStack-changed', $event)"
                  @input="$emit('input', $event)"
                  @change="$emit('change', $event)"
                  @canvas-viewbox-changed="$emit('canvas-viewbox-changed', $event)"
                  @resetData="resetData"
          />
        </div>
      </template>
    </FullModel>
  </div>
</template>

<script setup>
import customRules from "./rules";
import CustomModeler from "./customModeler";
import defaultEmptyXML from "./xmlData";
import customTranslate from "./customTranslate/customTranslate";
import treeTransfer from "./treeTransfer/treeTransfer.vue";
import {
  append as svgAppend,
  attr as svgAttr,
  create as svgCreate,
} from "tiny-svg";
import { query as domQuery } from "min-dom";
import { ruleDetail, flowTreeList, ruleSave } from "@/api/rule_editor";
import StringUtils from "@/components/ruleEditCom/utils/stringUtils";
import checkFlow from "./utils/checkFlow";
import FlowRule from "@/components/ruleFlow/FlowRule/index.vue";
import { cloneDeep } from "lodash";
import globalEventEmitter from '~/utils/eventBus';
import { REFRESH_RULE_LIST } from '@/consts/globalEventConsts';
import { RULE_SUBMIT_ACTION } from '@/consts/globalEventConsts';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";
import {submitRule, getRuleByUuid,} from "@/api/rule_base";
import qs from 'qs';
const message = inject("message");
// 组件的状态和数据
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
  isFullscreenMode: {
    type: Boolean,
    default: false
  }
});

const isModalVisible = ref(false);
const isFullscreen = ref(true);
const titleText = ref('');

const emit = defineEmits([
  "commandStack-changed",
  "input",
  "change",
  "canvas-viewbox-changed",
]);

const bpmnModeler = ref(null);
const container = ref(null);
const canvas = ref(null);
let activeShape = null;
const activeE = ref(null);
const processId = ref("");
const dialogVisible = ref(false);
const dialogTitle = ref("");
const ruleUuid = ref("");
const form = ref({
  id: "",
  name: "",
  desc: "",
  type: "",
  gatewayDirection: "",
});
const isShowTreeContaiter = ref(false);
const isShowTypeContaiter = ref(false);
const confirmOpen = ref(false);
const isShowSubFlowTreeContaiter = ref(false);
const routerSet = ref(false);
const isEdit = ref(false);
const conditionPriority = ref(1);
const conditionDesc = ref("");
const radio = ref("pass");
const treeRoot = ref([]);
const treeTitle = ref(["未选", "已选"]);
const gatewayTypeOption = ref([]);
const policyGatewayTypeOption = ref([
  { value: "bpmn:InclusiveGateway", label: "所有分支根据条件执行" },
  { value: "bpmn:ParallelGateway", label: "所有分支总是执行" },
  {
    value: "bpmn:ExclusiveGateway",
    label: "只有最先满足条件-条分支被执行,其他分支关闭",
  },
]);
const convergenceGatewayTypeOption = ref([
  {
    value: "bpmn:ExclusiveGateway",
    label: "分支汇聚后立即执行，不等待其它分支",
  },
  { value: "bpmn:ParallelGateway", label: "等待所有分支都汇聚后执行" },
]);
const fromDataOld = ref([]);
const fromData = ref([]);
const fromSubFlowDataOld = ref([]);
const fromSubFlowData = ref([]);
const toData = ref([]);
const toSubFlowData = ref([]);
const nodeRuleTree = ref([]);
const drawer = ref(false);
const errorDrawer = ref(false);
const defaultZoom = ref(1);
const previewResult = ref("");
const recoverable = ref(false);
const revocable = ref(false);
const flowErrorMsg = ref([]);
const drawerWidth = ref("30%");
const conditions = ref([]);
const ruleError = ref([]);
const transferValue = ref("");
const transKeys = ref([]);
const treeAction = ref("");
const nodeRuleTreeOld = ref([]);
const toDataChange = ref([]);
const treeTransferObj = ref([]);
const treeTransferObjOld = ref([]);
const subFlowTreeTransferObj = ref([]);
const subFlowTreeTransferObjOld = ref([]);
const defaultProps = ref({
  label: (data, node) => data,
});
const treeIcon = ref({
  "ico-guize": "ruleiconfont icon-guize",
  "ico-biaoge": "ruleiconfont icon-biaoge",
  "ico-folder": "ruleiconfont icon-wenjianjia",
  "ico-tree": "ruleiconfont icon-tree",
  "ico-flow": "ruleiconfont icon-flow",
  null: "ruleiconfont icon-wenjianjia",
});
const valiRes = ref([]);
const allParentIds = ref([]);
const selectIdAndParentId = ref({});
const addKeyIsParentId = ref([]);
const parentDisabled = ref({});
const gatewayChangedType = ref(null);
const conditionRadioShow = ref(true);
const listenerElement = ref(null);
const activeShapeType = ref("");
const gatewayTypeAlert = ref(false);
const flowRule = ref(null);
const refFile = ref(null);
const fileList = ref([]);
const errContent = ref("");

// 添加状态存储
const nodeSelectedKeys = ref(new Map()); // 存储每个节点的选中状态
const nodeRightTreeData = ref(new Map()); // 存储每个节点的右侧树数据

const isInternalChange = ref(false)

// 组件的生命周期钩子
onMounted(() => {
  init();
});

// 组件的方法
const init = () => {
  const canvasElement = canvas.value;
  bpmnModeler.value = new CustomModeler({
    container: canvasElement,
    keyboard: {
      bindTo: document,
    },
    additionalModules: [
      {
        labelEditingProvider: ["value", ""],
      },
      {
        translate: ["value", customTranslate],
      },
      customRules,
    ],
  });
  ruleUuid.value = props.ruleInfo.uuid;
  getData(props.ruleInfo.uuid,props.ruleInfo.type);
};
const createNewDiagram = async (xml) => {
  try {
    const { ruleInfo } = props;
    // 加载或处理process
    let xmlStr = "";
    if (xml) {
      let resObj = [];
      let _xmlStr = xml;
      const processReg = /<process.*?>/g;
      const bpmndiReg = /<bpmndi:BPMNPlane.*?>/g;
      const processObj = _xmlStr.match(processReg);
      const bpmndiObj = _xmlStr.match(bpmndiReg);
      const sequenceFlowReg = /<sequenceFlow.*?>/g;
      const sequenceFloObj = _xmlStr.match(sequenceFlowReg);

      if (processObj && processObj.length > 0) {
        processObj.map((item, i) => {
          let processA = item.split(/id=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${processA[0]} id="Process.${ruleInfo.uuid}"${processA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }

      if (bpmndiObj && bpmndiObj.length > 0) {
        bpmndiObj.map((item, i) => {
          let bpmndiA = item.split(/bpmnElement=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${bpmndiA[0]} bpmnElement="Process.${ruleInfo.uuid}"${bpmndiA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }

      // 处理业务描述
      if (sequenceFloObj && sequenceFloObj.length > 0) {
        sequenceFloObj.find((resItem) => {
          if (resItem.indexOf('name="to') !== -1) {
            resObj.push(resItem);
          }
        });
        resObj.map((item, i) => {
          let sequenceFloA = item.split(/name=".*?".*?/);
          let _itemReg = new RegExp(`${item}.*?`);
          _xmlStr = _xmlStr.replace(
            _itemReg,
            `${sequenceFloA[0]} name="业务描述"${sequenceFloA[1]}`
          );
        });
        xmlStr = _xmlStr;
      }
    } else {
      xmlStr = defaultEmptyXML(
        ruleInfo.uuid,
        ruleInfo.ruleName,
        ruleInfo.packageNameAll
      );
    }
    processId.value = `Process.${ruleInfo.uuid}`; // 使用 ref 的 value
    // 将字符串转换成图显示出来
    const result = await bpmnModeler.value.importXML(xmlStr); // 使用 ref 的 value
    const { warnings } = result;
    success(); // 直接调用 success 函数
  } catch (err) {}
};
const getData = (uuid,type) => {
  // 规则详情
  let getRuleDetail = ruleDetail(uuid,type);
  // 规则树列表
  let getFlowTreeList = flowTreeList(uuid);
  Promise.all([getRuleDetail, getFlowTreeList]).then((result) => {
    const detail = result[0].data;
    const treeList = result[1].data;
    // nextTick(() => {
    //   loadingInstance.close();
    // });
    if (detail) {
      let ruleCon = detail.ruleContent || "";
      conditions.value = detail.conditions;
      // 初始化 流程图
      createNewDiagram(ruleCon);
    }
    if (treeList) {
      fromData.value = treeList.allTree;
      fromDataOld.value = JSON.parse(JSON.stringify(fromData.value));
      nodeRuleTree.value = treeList.nodeRuleTree.filter((item) => {
        return !item.nodeId.includes("CallActivity");
      });
      nodeRuleTree.value = nodeRuleTree.value.concat(
        treeList.ruleFlowSelectTree
      );
      nodeRuleTreeOld.value = JSON.parse(JSON.stringify(nodeRuleTree.value));
      treeList.nodeRuleTree.map((item) => {
        if (!item.nodeId.includes("CallActivity")) {
          treeTransferObj.value.push({
            nodeId: item.nodeId,
            checkedId: getCheckedNodeId([item.nodeSelectTree], []),
          });
        }
      });
      treeList.ruleFlowSelectTree.map((item) => {
        if (item.nodeId.includes("CallActivity")) {
          subFlowTreeTransferObj.value.push({
            nodeId: item.nodeId,
            checkedId: getCheckedNodeId([item.nodeSelectTree], []),
          });
        }
      });
      treeTransferObjOld.value = JSON.parse(
        JSON.stringify(treeTransferObj.value)
      );
      fromSubFlowData.value = treeList.ruleFlowTree;
      fromSubFlowDataOld.value = JSON.parse(
        JSON.stringify(fromSubFlowData.value)
      );
      subFlowTreeTransferObjOld.value = JSON.parse(
        JSON.stringify(subFlowTreeTransferObj.value)
      );
    }

    // 检查规则节点上是否有绑定规则，如果没有则删除g:ruleFlowGroup属性
    nextTick(() => {
      checkAndRemoveEmptyRuleFlowGroup();
    });
  });
};
// 获取addKeys的父节点id
const getAddKeysParentId = (node, keyItem) => {
  node.map((i) => {
    if (i.id === keyItem) {
      addKeyIsParentId.value.push(i.parent);
    }
    if (i.children && i.children.length > 0) {
      getAddKeysParentId(i.children, keyItem);
    }
  });
};
// 删除多余连接线
const removeSequenceFlow = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const elementRegistry = bpmnModeler.value.get("elementRegistry");
  const sequenceFlowList = elementRegistry.filter(
    (item) => item.type === "bpmn:SequenceFlow"
    // item.type === "bpmn:SequenceFlow" && item.businessObject.name
  );
  for (let i = sequenceFlowList.length - 1; i >= 0; i--) {
    listenerElement.value.map((item) => {
      if (sequenceFlowList[i].id === item.id) {
        modeling.removeConnection(sequenceFlowList[i]);
      }
    });
  }
};
// 如果字节点全选设置父节点disabled
const setParentDisabled = (node, itemId, child) => {
  node.map((i, index) => {
    if (!itemId) {
      if (!child) {
        // i.disabled = true;
        parentDisabled.value[i.id] = [];
      }

      if (child) {
        if (
          parentDisabled.value[i.parent] &&
          parentDisabled.value[i.parent].length > 0
        ) {
          parentDisabled.value[i.parent].push(i.disabled);
        } else {
          if (
            parentDisabled.value[i.parent] &&
            parentDisabled.value[i.parent].constructor === Array
          ) {
            parentDisabled.value[i.parent].push(i.disabled);
          } else {
            parentDisabled.value[i.parent] = [i.disabled];
          }
        }
      }
      if (i.children && i.children.length > 0) {
        setParentDisabled(i.children, "", true);
      }
    } else {
      if (i.id === itemId) {
        if (i.disabled) {
          return;
        }
        setParentDisabled([i]);
      }
      if (i.children && i.children.length > 0) {
        setParentDisabled(i.children, itemId);
      }
    }
  });
};
const success = () => {
  // 调整与正中间
  bpmnModeler.value.get("canvas").zoom("fit-viewport", "auto");
  // 绑定事件
  addModelerListener();
  addEventBusListener();
  // 初始化箭头
  initArrow("sequenceflow-arrow-normal");
  initArrow("sequenceflow-arrow-active");
};
const routerClose = () => {
  routerSet.value = false;
};
const agreeChange = () => {
  if (radio.value === "condition") {
    isEdit.value = true;
    drawerWidth.value = "55%";
  } else {
    isEdit.value = false;
    drawerWidth.value = "30%";
  }
};
// 初始化自定义箭头
const initArrow = (id) => {
  const marker = svgCreate("marker");

  svgAttr(marker, {
    id,
    viewBox: "0 0 20 20",
    refX: "11",
    refY: "10",
    markerWidth: "10",
    markerHeight: "10",
    orient: "auto",
  });

  const path = svgCreate("path");

  svgAttr(path, {
    d: "M 1 5 L 11 10 L 1 15 Z",
    style:
      " stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; ",
  });

  const defs = domQuery("defs");
  svgAppend(marker, path);
  svgAppend(defs, marker);
};
// 修改网关类型
const gatewayTypeChange = (type) => {
  if (type === "bpmn:ParallelGateway") {
    conditionRadioShow.value = false;
  } else {
    conditionRadioShow.value = true;
  }
  // 模型对象
  const elementFactory = bpmnModeler.value.get("elementFactory"),
    elementRegistry = bpmnModeler.value.get("elementRegistry"),
    modeling = bpmnModeler.value.get("modeling");

  // 获取当前网关信息
  const shape = activeShape;
  const shapeX = shape.x;
  const shapeY = shape.y;
  const shapeIncom = activeShape.incoming;
  const shapeOutgo = activeShape.outgoing;
  // 创建新的网关
  const process = elementRegistry.get(processId.value);
  const gateway = elementFactory.createShape({
    type,
  });

  // 有incoming链接源
  if (shapeIncom.length > 0) {
    modeling.createShape(gateway, { x: shapeX + 25, y: shapeY + 25 }, process);
    shapeIncom.forEach((item, i) => {
      // 获取网关incom链接线对象的源
      const incomObj = item;
      const incomObjSource = incomObj.source;
      modeling.connect(incomObjSource, gateway);
    });

    // 有outgo链接线对象
    if (shapeOutgo.length > 0) {
      shapeOutgo.forEach((item, i) => {
        // 获取网关outgo链接线对象
        const outgoObj = item;
        const outgoObjTarget = outgoObj.target;
        modeling.connect(gateway, outgoObjTarget);
        // 一条outgoing时需要手动删除
        if (shapeOutgo.length === 1) {
          modeling.removeConnection(outgoObj);
        }
      });
    }
  } else {
    // 没有incoming链接源直接创建
    modeling.createShape(gateway, { x: shapeX + 25, y: shapeY + 25 }, process);
  }
  activeShape = gateway;
  _updateProperties();
  modeling.removeShape(shape);
  conditionPriority.value = 1;
};
const setGatewayType = (type) => {
  gatewayChangedType.value = type;
  if (
    gatewayChangedType.value &&
    gatewayChangedType.value !== activeShapeType.value
  ) {
    gatewayTypeAlert.value = true;
  } else {
    gatewayTypeAlert.value = false;
  }
};
// 更新对象属性
const _updateProperties = (id) => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: form.value.name,
    desc: form.value.desc,
    gatewayDirection: form.value.gatewayDirection,
  };
  // g:ruleFlowGroup
  if (dialogTitle.value === "规则节点") {
    propertiesJson["g:ruleFlowGroup"] = `${ruleUuid.value}_${activeShape.id}`;
  }
  if (dialogTitle.value === "子规则流节点") {
    propertiesJson["g:ruleFlowGroup"] = `${ruleUuid.value}_${activeShape.id}`;
    propertiesJson["g:subProcessId"] = id;
    propertiesJson.calledElement = `callActivity.${id}`;
  }
  modeling.updateProperties(activeShape, propertiesJson);
};
// 不同节点对应的选中规则对象
const setTreeTransferObj = (obj, action, treeTransferObj) => {
  let _nodeId = `${ruleUuid.value}_${activeShape.id}`;
  let _obj = cloneDeep(obj);
  if (treeTransferObj.length > 0) {
    let hasId = treeTransferObj.some((item) => {
      return item.nodeId === _nodeId;
    });
    treeTransferObj.map((item, i) => {
      if (item.nodeId === _nodeId) {
        if (action === "add") {
          // 不同节点对应的选中规则的id数组
          treeTransferObj[i].checkedId =
            treeTransferObj[i].checkedId.concat(_obj);
        }
        if (action === "remove") {
          // 数组相减
          for (let j = treeTransferObj[i].checkedId.length - 1; j >= 0; j--) {
            let a = treeTransferObj[i].checkedId[j];
            for (let k = _obj.length - 1; k >= 0; k--) {
              let b = _obj[k];
              if (a == b) {
                treeTransferObj[i].checkedId.splice(j, 1);
                _obj.splice(k, 1);
                break;
              }
            }
          }
        }
      }
    });
    if (!hasId && action === "add") {
      treeTransferObj.push({
        nodeId: _nodeId,
        checkedId: _obj,
      });
    }
  } else {
    if (action === "add") {
      treeTransferObj.push({
        nodeId: _nodeId,
        checkedId: _obj,
      });
    }
  }
};
const leftCheckChange = (nodeObj, treeObj, checkAll) => {
  const { checkedKeys } = treeObj;
  let isDisabled = false;
  if (checkedKeys.length > 0) {
    isDisabled = true;
  } else {
    isDisabled = false;
  }
  setSubFlowTreeNodeValue(
    fromSubFlowData.value,
    "disabled",
    isDisabled,
    [
      {
        nodeId: `${ruleUuid.value}_${activeShape.id}`,
        checkedId: checkedKeys,
      },
    ],
    "left"
  );
};
// 监听穿梭框组件添加
const treeAdd = (rightTreeData, obj) => {
  // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
  // 通讯录模式addressList时，返回参数为右侧收件人列表、右侧抄送人列表、右侧密送人列表
  if (isShowSubFlowTreeContaiter.value) {
    toDataChange.value = rightTreeData;
    toSubFlowData.value = toDataChange.value;
    transKeys.value = obj;
    treeAction.value = "add";
    setTreeTransferObj(
      transKeys.value,
      treeAction.value,
      subFlowTreeTransferObj.value
    );
    fromSubFlowData.value = cloneDeep(fromSubFlowDataOld.value);
    setSubFlowTreeNodeValue(
      fromSubFlowData.value,
      "disabled",
      true,
      subFlowTreeTransferObj.value
    );
  } else {
    toDataChange.value = rightTreeData;
    transKeys.value = obj;
    treeAction.value = "add";
    setTreeTransferObj(
      transKeys.value,
      treeAction.value,
      treeTransferObj.value
    );
    fromData.value = cloneDeep(fromDataOld.value);
    setTreeNodeValue(fromData.value, "disabled", true, treeTransferObj.value);

    // 如果父节点的子节点都是选中状态，则添加父节点id
    transKeys.value.map((keyItem, keyIndex) => {
      // 获取add的父节点
      getAddKeysParentId(fromData.value, keyItem);
    });
    // addkey对应的父节点数组，去重
    addKeyIsParentId.value = Array.from(new Set(addKeyIsParentId.value));
    addKeyIsParentId.value.map((addKPItem) => {
      setParentDisabled(fromData.value, addKPItem);
      let isNotAllCheck = parentDisabled.value[addKPItem].some((i) => {
        return i === false;
      });
      let _nodeId = `${ruleUuid.value}_${activeShape.id}`;

      let transObjIndex = null;
      treeTransferObj.value.map((transI, transIn) => {
        if (transI.nodeId === _nodeId) {
          transObjIndex = transIn;
        }
      });
      let transObjHasParId = treeTransferObj.value[
        transObjIndex
      ].checkedId.some((_i) => {
        return _i === addKPItem;
      });
      if (isNotAllCheck) {
        // 非全选，如果有parentId删掉parentId
        if (transObjHasParId) {
          for (
            let i = treeTransferObj.value[transObjIndex].checkedId.length - 1;
            i >= 0;
            i--
          ) {
            if (
              treeTransferObj.value[transObjIndex].checkedId[i] === addKPItem
            ) {
              treeTransferObj.value[transObjIndex].checkedId.splice(i, 1);
            }
          }
        }
      } else {
        // 全选
        !transObjHasParId &&
          treeTransferObj.value[transObjIndex].checkedId.push(addKPItem);
      }
    });

    fromData.value = cloneDeep(fromDataOld.value);
    setTreeNodeValue(fromData.value, "disabled", true, treeTransferObj.value);
  }
};
// 监听穿梭框组件移除
const treeRemove = (rightTreeData, obj) => {
  // 树形穿梭框模式transfer时，返回参数为左侧树移动后数据、右侧树移动后数据、移动的{keys,nodes,halfKeys,halfNodes}对象
  toDataChange.value = rightTreeData;
  transKeys.value = obj;
  treeAction.value = "remove";

  if (isShowSubFlowTreeContaiter.value) {
    toSubFlowData.value = rightTreeData; // 关键：同步清空右侧数据
    // 如果父节点是选中状态并且删除了子节点，则删除父节点id
    transKeys.value.map((keyItem, keyIndex) => {
      getParentIdById(fromSubFlowData.value, keyItem);
      let _nodeId = `${ruleUuid.value}_${activeShape.id}`;
      let _parent = selectIdAndParentId.value[keyItem];

      for (let i = subFlowTreeTransferObj.value.length - 1; i >= 0; i--) {
        if (subFlowTreeTransferObj.value[i].nodeId === _nodeId) {
          for (
            let j = subFlowTreeTransferObj.value[i].checkedId.length - 1;
            j >= 0;
            j--
          ) {
            if (_parent === subFlowTreeTransferObj.value[i].checkedId[j]) {
              // 删除父节点id
              subFlowTreeTransferObj.value[i].checkedId.splice(j, 1);
            }
          }
          if (subFlowTreeTransferObj.value[i].checkedId.length === 0) {
            subFlowTreeTransferObj.value.splice(i, 1);
          }
        }
      }
    });

    setTreeTransferObj(
      transKeys.value,
      treeAction.value,
      subFlowTreeTransferObj.value
    );
    setSubFlowTreeNodeValue(fromSubFlowData.value, "disabled", false, [
      {
        nodeId: `${ruleUuid.value}_${activeShape.id}`,
        checkedId: [true],
      },
    ]);
  } else {
    // 规则节点处理
    toData.value = rightTreeData; // 关键：同步更新右侧数据，确保UI立即更新

    // 如果父节点是选中状态并且删除了子节点，则删除父节点id
    transKeys.value.map((keyItem, keyIndex) => {
      getParentIdById(fromData.value, keyItem);
      let _nodeId = `${ruleUuid.value}_${activeShape.id}`;
      let _parent = selectIdAndParentId.value[keyItem];

      for (let i = treeTransferObj.value.length - 1; i >= 0; i--) {
        if (treeTransferObj.value[i].nodeId === _nodeId) {
          for (
            let j = treeTransferObj.value[i].checkedId.length - 1;
            j >= 0;
            j--
          ) {
            if (_parent === treeTransferObj.value[i].checkedId[j]) {
              // 删除父节点id
              treeTransferObj.value[i].checkedId.splice(j, 1);
            }
          }
        }
      }
    });
    nextTick(() => {
      for (let i = treeTransferObj.value.length - 1; i >= 0; i--) {
        if (treeTransferObj.value[i].checkedId.length === 0) {
          treeTransferObj.value.splice(i, 1);
        }
      }
    });

    setTreeTransferObj(
      transKeys.value,
      treeAction.value,
      treeTransferObj.value
    );
    setTreeNodeValue(fromData.value, "disabled", true, treeTransferObj.value);
  }
};
// 规则节点绑定规则回显
const setNodeToData = () => {
  // 重置数据
  nodeRuleTree.value = cloneDeep(nodeRuleTreeOld.value);
  treeTransferObj.value = cloneDeep(treeTransferObjOld.value);
  // 判断当前节点是否绑定规则
  if (activeShape.businessObject?.$attrs?.["g:ruleFlowGroup"]) {
    let _nodeId = activeShape.businessObject.$attrs["g:ruleFlowGroup"];
    let _nodeRuleTreeItem = nodeRuleTree.value.find(
      (i) => _nodeId === i.nodeId
    );
    if (
      _nodeRuleTreeItem?.nodeSelectTree &&
      Object.keys(_nodeRuleTreeItem.nodeSelectTree).length > 0
    ) {
      toData.value = [_nodeRuleTreeItem.nodeSelectTree];
      // 更新当前节点的选中状态
      // nodeSelectedKeys.value.set(_nodeId, [_nodeRuleTreeItem.nodeSelectTree.id]);
      // nodeRightTreeData.value.set(_nodeId, [_nodeRuleTreeItem.nodeSelectTree]);
    }
  }

  // 禁用选项
  setTreeNodeValue(fromData.value, "disabled", true, treeTransferObj.value);
};
// 子规则流节点绑定回显
const setSubFlowNodeToData = () => {
  nodeRuleTree.value = JSON.parse(JSON.stringify(nodeRuleTreeOld.value));
  subFlowTreeTransferObj.value = JSON.parse(
    JSON.stringify(subFlowTreeTransferObjOld.value)
  );
  // 判断是否绑定规则
  if (
    activeShape.businessObject &&
    activeShape.businessObject.$attrs &&
    activeShape.businessObject.$attrs["g:ruleFlowGroup"]
  ) {
    let _nodeId = activeShape.businessObject.$attrs["g:ruleFlowGroup"];
    let _nodeRuleTreeItem = nodeRuleTree.value.find((i) => {
      return _nodeId === i.nodeId;
    });
    if (
      _nodeRuleTreeItem &&
      _nodeRuleTreeItem.nodeSelectTree &&
      Object.keys(_nodeRuleTreeItem.nodeSelectTree).length > 0
    ) {
      // set toSubFlowData
      toSubFlowData.value = [_nodeRuleTreeItem.nodeSelectTree];
    }
  }
  // 禁用选项
  setSubFlowTreeNodeValue(
    fromSubFlowData.value,
    "disabled",
    true,
    subFlowTreeTransferObj.value
  );
};
// 设置子流程树节点值
const setSubFlowTreeNodeValue = (node, key, val, obj, left) => {
  node.map((item, index) => {
    obj.map((objI) => {
      if (`${ruleUuid.value}_${activeShape.id}` === objI.nodeId) {
        if (left) {
          // 使用传入的 key 作为属性名
          node[index][key] = val;
          objI.checkedId &&
            objI.checkedId.length > 0 &&
            objI.checkedId.map((checkI) => {
              if (item.id === checkI) {
                node[index][key] = false;
              }
            });
        } else {
          if (objI.checkedId && objI.checkedId.length > 0) {
            // 使用传入的 key 作为属性名
            node[index][key] = val;
          }
        }
      }
    });

    if (key === "disabled") {
      if (item.folder) {
        // 使用传入的 key 作为属性名
        node[index][key] = true;
      }
    }

    if (item.children && item.children.length > 0) {
      setSubFlowTreeNodeValue(item.children, key, val, obj, left);
    }
  });
};
// 设置属性值
const setTreeNodeValue = (node, key, val, obj) => {
  node.map((item, index) => {
    obj.map((objI, objIndex) => {
      if (`${ruleUuid.value}_${activeShape.id}` === objI.nodeId) {
        // 使用传入的 key 作为属性名
        node[index][key] = false;
        obj[objIndex].checkedId.map((checkI) => {
          if (item.id === checkI) {
            node[index][key] = val;
          }
        });
      }
    });

    if (key === "disabled") {
      if (item.folder && item.children.length === 0) {
        node[index][key] = true;
      }
    }

    if (item.children && item.children.length > 0) {
      setTreeNodeValue(item.children, key, val, obj);
    }
  });
};
// 获取选中的规则id
const getCheckedNodeId = (node, checkedIdArr) => {
  node.map((item) => {
    if (item.checked === true) {
      checkedIdArr.push(item.id);
      selectIdAndParentId.value[item.id] = item.parent;
    }
    if (item.children && item.children.length > 0) {
      getCheckedNodeId(item.children, checkedIdArr);
    }
  });
  return checkedIdArr;
};
// 根据id获取父id
const getParentIdById = (node, keyItem) => {
  node.map((i) => {
    if (i.id === keyItem) {
      selectIdAndParentId.value[keyItem] = i.parent;
    }
    if (i.children && i.children.length > 0) {
      getParentIdById(i.children, keyItem);
    }
  });
};
// tree
const dialogSave = () => {
  // 保存前先完成所有数据操作
  if (isShowSubFlowTreeContaiter.value) {
    if (
      gatewayChangedType.value &&
      gatewayChangedType.value !== activeShapeType.value
    ) {
      nextTick(() => {
        gatewayTypeChange(gatewayChangedType.value);
      });
    }
    let subFlowId = "";
    if (toSubFlowData.value.length > 0) {
      const { children } = toSubFlowData.value[0];
      if (children && children.length > 0) {
        subFlowId = getLastId(children[0]);
      }
    }
    // 更新xml属性值
    _updateProperties(subFlowId);

    // 设置checked值
    setSubFlowTreeNodeValue(
      toSubFlowData.value,
      "checked",
      true,
      subFlowTreeTransferObj.value
    );
    let hasNode = nodeRuleTree.value.some((i) => {
      return activeShape.businessObject.$attrs["g:ruleFlowGroup"] === i.nodeId;
    });
    if (hasNode) {
      nodeRuleTree.value.map((item, i) => {
        if (
          activeShape.businessObject.$attrs["g:ruleFlowGroup"] === item.nodeId
        ) {
          if (toSubFlowData.value && toSubFlowData.value.length > 0) {
            nodeRuleTree.value[i].nodeSelectTree = toSubFlowData.value[0];
          } else {
            nodeRuleTree.value[i].nodeSelectTree = {};
          }
        }
      });
    } else {
      if (toSubFlowData.value && toSubFlowData.value.length > 0) {
        nodeRuleTree.value.push({
          nodeId: activeShape.businessObject.$attrs["g:ruleFlowGroup"],
          nodeSelectTree: toSubFlowData.value[0],
        });
      } else {
        nodeRuleTree.value.push({
          nodeId: activeShape.businessObject.$attrs["g:ruleFlowGroup"],
          nodeSelectTree: {},
        });
      }
    }

    // 数据处理
    for (let i = nodeRuleTree.value.length - 1; i >= 0; i--)
    {
      if (Object.keys(nodeRuleTree.value[i].nodeSelectTree).
      length === 0) {
        delete activeShape.businessObject.$attrs
        ["g:ruleFlowGroup"];
        delete activeShape.businessObject.$attrs
        ["g:subProcessId"];
        delete activeShape.businessObject.$attrs
        ["calledElement"];
        delete activeShape.businessObject.calledElement;
        nodeRuleTree.value.splice(i, 1);
      }
    }
    // 更新nodeRuleTreeOld
    nodeRuleTreeOld.value = cloneDeep(nodeRuleTree.value);
    // toData
    toData.value = cloneDeep(toDataChange.value);
    // 更新treeTransferObjOld
    treeTransferObjOld.value = cloneDeep(treeTransferObj.value);
    // 更新subFlowTreeTransferObjOld
    subFlowTreeTransferObjOld.value = cloneDeep(subFlowTreeTransferObj.value);

    // 最后统一关闭UI
    treeDrawerClosed();

    // 检查并删除空的规则流组
    checkAndRemoveEmptyRuleFlowGroup();
  } else {
    if (
      gatewayChangedType.value &&
      gatewayChangedType.value !== activeShapeType.value
    ) {
      nextTick(() => {
        gatewayTypeChange(gatewayChangedType.value);
      });
    }
    // 更新xml属性值
    _updateProperties();
    // 设置checked值
    setTreeNodeValue(toDataChange.value, "checked", true, treeTransferObj.value);
    let hasNode = nodeRuleTree.value.some((i) => {
      return activeShape.businessObject.$attrs["g:ruleFlowGroup"] === i.nodeId;
    });
    if (hasNode) {
      nodeRuleTree.value.map((item, i) => {
        if (
          activeShape.businessObject.$attrs["g:ruleFlowGroup"] === item.nodeId
        ) {
          if (toDataChange.value && toDataChange.value.length > 0) {
            nodeRuleTree.value[i].nodeSelectTree = toDataChange.value[0];
          } else {
            nodeRuleTree.value[i].nodeSelectTree = {};
          }
        }
      });
    } else {
      if (toDataChange.value && toDataChange.value.length > 0) {
        nodeRuleTree.value.push({
          nodeId: activeShape.businessObject.$attrs["g:ruleFlowGroup"],
          nodeSelectTree: toDataChange.value[0],
        });
      } else {
        nodeRuleTree.value.push({
          nodeId: activeShape.businessObject.$attrs["g:ruleFlowGroup"],
          nodeSelectTree: {},
        });
      }
    }

    // 数据处理
    for (let i = nodeRuleTree.value.length - 1; i >= 0; i--) {
      if (Object.keys(nodeRuleTree.value[i].nodeSelectTree).length === 0) {
        delete activeShape.businessObject.$attrs["g:ruleFlowGroup"];
        nodeRuleTree.value.splice(i, 1);
      }
    }
    // 更新nodeRuleTreeOld
    nodeRuleTreeOld.value = cloneDeep(nodeRuleTree.value);
    // toData
    toData.value = cloneDeep(toDataChange.value);
    // 更新treeTransferObjOld
    treeTransferObjOld.value = cloneDeep(treeTransferObj.value);

    // 最后统一关闭UI
    treeDrawerClosed();

    // 检查并删除空的规则流组
    checkAndRemoveEmptyRuleFlowGroup();
  }
};
const treeDrawerClosed = () => {
  toData.value = [];
  toSubFlowData.value = [];
  gatewayChangedType.value = null;
  gatewayTypeAlert.value = false;
  dialogVisible.value = false;

  // 检查并删除空的规则流组
  checkAndRemoveEmptyRuleFlowGroup();
};
// 条件
const routerDialogSave = () => {
  const modeling = bpmnModeler.value.get("modeling");
  const propertiesJson = {
    name: conditionDesc.value,
    ["tns:priority"]: conditionPriority.value,
  };
  if (isEdit.value) {
    // 添加条件
    const moddle = bpmnModeler.value._moddle;
    const conditionExpression = moddle.create("bpmn:FormalExpression", {
      language: "http://www.jboss.org/drools/rule",
    });
    propertiesJson["conditionExpression"] = conditionExpression;
    // 触发生成规则
    let objEditor = null;
    let obj =
      (flowRule && flowRule.value.$refs.ruleCreate) ||
      (flowRule && flowRule.value.$refs.ruleUpdate);
    obj && (objEditor = obj.$refs.ruleEditor);
    objEditor && objEditor.toSave();
  } else {
    for (let i = conditions.value.length - 1; i >= 0; i--) {
      if (conditions.value[i].nodeId === activeShape.id) {
        conditions.value.splice(i, 1);
      }
    }
  }
  modeling.updateProperties(activeShape, propertiesJson);
  drawer.value = false;
  isEdit.value = false;
};
const getLastId = (data) => {
  const { children } = data;
  if (children && children.length > 0) {
    return getLastId(children[0]);
  } else {
    return data.id;
  }
};
const shapeAdded = (e) => {
  let shape = getShape(e.element.id);
  if (
    shape &&
    shape.businessObject &&
    shape.businessObject.$type === "bpmn:CallActivity"
  ) {
    const modeling = bpmnModeler.value.get("modeling");
    if (!shape.id.includes("CallActivity_")) {
      modeling.updateProperties(shape, {
        id: shape.id.replace("Activity_", "CallActivity_"),
      });
    }
  }
};

const isInvalid = (param) => {
  // 判断是否是无效的值
  return param === null || param === undefined || param === "";
};
const isSequenceFlow = (type) => {
  // 判断是否是线
  return type === "bpmn:SequenceFlow";
};
const getShape = (id) => {
  let elementRegistry = bpmnModeler.value.get("elementRegistry");
  return elementRegistry.get(id);
};
const addModelerListener = () => {
  // 监听 modeler
  const bpmnjs = bpmnModeler.value;
  const EventBus = bpmnModeler.value.get("eventBus");
  // 'shape.removed', 'connect.end', 'connect.move'
  // const events = ["shape.added", "shape.move.end", "shape.removed"];
  const events = ["element.click", "shape.removed"];
  events.forEach((event) => {
    bpmnModeler.value.on(event, (e) => {
      let elementRegistry = bpmnjs.get("elementRegistry");
      let shape = e.element ? elementRegistry.get(e.element.id) : e.shape;
      if (event === "element.click") {
        if (shape && shape.type === "bpmn:BusinessRuleTask") {
          listenerElement.value = JSON.parse(JSON.stringify(shape.incoming));
        }
      }
      if (event === "shape.removed") {
        nextTick(() => {
          if (shape.type === "bpmn:BusinessRuleTask") {
            removeSequenceFlow();
          }
        });
      }
    });
  });
  // 监听图形改变返回xml
  EventBus.on("commandStack.changed", async (event) => {
    try {
      recoverable.value = bpmnModeler.value.get("commandStack").canRedo();
      revocable.value = bpmnModeler.value.get("commandStack").canUndo();
      let { xml } = await bpmnModeler.value.saveXML({ format: true });
      emit("commandStack-changed", event);
      emit("input", xml);
      emit("change", xml);
    } catch (e) {
      console.error(`[Process Designer Warn]: ${e.message || e}`);
    }
  });
  // 监听视图缩放变化
  bpmnModeler.value.on("canvas.viewbox.changed", ({ viewbox }) => {
    emit("canvas-viewbox-changed", { viewbox });
    const { scale } = viewbox;
    defaultZoom.value = Math.floor(scale * 100) / 100;
  });
};
// 绑定事件
const addEventBusListener = () => {
  // getEventBusAll()
  // 监听 element
  const eventBus = bpmnModeler.value.get("eventBus");
  // const eventTypes = ["element.dblclick"];
  const eventTypes = [
    "element.dblclick",
    "element.changed",
    "connection.changed",
    "shape.added",
  ];
  eventTypes.forEach((eventType) => {
    eventBus.on(eventType, (e) => {
      if (!e || e.element.type == "bpmn:Process") return;
      if (eventType === "element.dblclick") {
        nextTick(() => {
          elementDBLclick(e);
        });
      }
      if (eventType === "connection.changed") {
        nextTick(() => {
          connectionUpdateProperties(e);
        });
      }
      if (eventType === "shape.added") {
        nextTick(() => {
          shapeAdded(e);
        });
      }
    });
  });
};
// 连线添加'业务描述''优先级''FormalExpression'
const connectionUpdateProperties = (e) => {
  if (!e || e.element.type == "bpmn:Process") return;
  const shape = e.element;
  if (
    (shape.source &&
      shape.source &&
      shape.source.businessObject &&
      shape.source.businessObject.gatewayDirection &&
      shape.source.businessObject.gatewayDirection === "Diverging" &&
      shape.target &&
      shape.target.type === "bpmn:BusinessRuleTask" &&
      !shape.businessObject.name) ||
    (shape.source &&
      shape.source &&
      shape.source.businessObject &&
      shape.source.businessObject.gatewayDirection &&
      shape.source.businessObject.gatewayDirection === "Diverging" &&
      !shape.businessObject.name) ||
    (shape.target &&
      shape.target &&
      shape.target.businessObject &&
      shape.target.businessObject.$type &&
      shape.target.businessObject.$type === "bpmn:CallActivity" &&
      !shape.businessObject.name)
  ) {
    const moddle = bpmnModeler.value._moddle;
    const modeling = bpmnModeler.value.get("modeling");
    const conditionExpression = moddle.create("bpmn:FormalExpression", {
      body: "",
    });

    modeling.updateProperties(shape, {
      name: "业务描述",
      ["tns:priority"]: conditionPriority.value,
      conditionExpression,
    });
    // modeling.setColor(shape,{
    //   fill: 'red',
    //   stroke: 'red'
    // })
  }
  return;
};
// 查看所有可用事件
const getEventBusAll = () => {
  const eventBus = bpmnModeler.value.get("eventBus");
  const eventTypes = Object.keys(eventBus._listeners);
  return eventTypes;
};
// 双击节点
const elementDBLclick = (e) => {
  const elementRegistry = bpmnModeler.value.get("elementRegistry");
  activeE.value = e;
  activeShape = e.element ? elementRegistry.get(e.element.id) : e.shape;
  const shapeType = activeShape && activeShape.type;
  activeShapeType.value = shapeType;

  if (shapeType === "bpmn:StartEvent") {
    dialogVisible.value = true;
    isShowTypeContaiter.value = false;
    isShowTreeContaiter.value = false;
    isShowSubFlowTreeContaiter.value = false;
    dialogTitle.value = "开始节点";
  }
  if (
    (shapeType === "bpmn:InclusiveGateway" ||
      shapeType === "bpmn:ParallelGateway" ||
      shapeType === "bpmn:ExclusiveGateway") &&
    activeShape.businessObject.gatewayDirection === "Diverging"
  ) {
    gatewayTypeOption.value = policyGatewayTypeOption.value;
    dialogVisible.value = true;
    isShowTypeContaiter.value = true;
    isShowTreeContaiter.value = false;
    isShowSubFlowTreeContaiter.value = false;
    dialogTitle.value = "决策分支节点";
  }
  if (
    (shapeType === "bpmn:ParallelGateway" ||
      shapeType === "bpmn:ExclusiveGateway") &&
    activeShape.businessObject.gatewayDirection === "Converging"
  ) {
    gatewayTypeOption.value = convergenceGatewayTypeOption.value;
    dialogVisible.value = true;
    isShowTypeContaiter.value = true;
    isShowTreeContaiter.value = false;
    isShowSubFlowTreeContaiter.value = false;
    dialogTitle.value = "汇聚分支节点";
  }
  if (shapeType === "bpmn:BusinessRuleTask") {
    dialogVisible.value = true;
    isShowTypeContaiter.value = false;
    isShowTreeContaiter.value = true;
    isShowSubFlowTreeContaiter.value = false;
    dialogTitle.value = "规则节点";
    isInternalChange.value = true

    // 初始化规则树数据
    fromData.value = cloneDeep(fromDataOld.value);

    // 设置绑定规则toData
    setNodeToData();
  }
  if (shapeType === "bpmn:CallActivity") {
    dialogVisible.value = true;
    isShowTypeContaiter.value = false;
    isShowTreeContaiter.value = false;
    isShowSubFlowTreeContaiter.value = true;
    dialogTitle.value = "子规则流节点";
    isInternalChange.value = true
    // 初始化fromSubFlowData
    fromSubFlowData.value = cloneDeep(fromSubFlowDataOld.value);
    // 设置绑定规则toSubFlowData
    setSubFlowNodeToData();
  }
  if (
    shapeType === "bpmn:SequenceFlow" &&
    (activeShape.source.businessObject.gatewayDirection === "Diverging" ||
      activeShape.target.businessObject.$type === "bpmn:CallActivity")
  ) {
    dialogTitle.value = "条件节点";
    conditionDesc.value = activeShape.businessObject.name;
    conditionPriority.value = activeShape.businessObject.$attrs["tns:priority"];

    let isCondition = conditions.value.some((i) => {
      return activeShape.id === i.nodeId;
    });
    if (isCondition) {
      radio.value = "condition";
      isEdit.value = true;
      drawerWidth.value = "55%";
      // 错误提示
      valiRes.value.map((item, i) => {
        if (activeShape.id === item.nodeId) {
          nextTick(() => {
            if (flowRule.value && flowRule.value.$refs.ruleUpdate) {
              flowRule.value.$refs.ruleUpdate.validate(
                item.nodeRule.ruleValidates[0]
              );
            }
          });
        }
      });
    } else {
      radio.value = "pass";
      isEdit.value = false;
      drawerWidth.value = "30%";
    }
    drawer.value = true;
  }
  if (shapeType === "bpmn:EndEvent") {
    dialogVisible.value = true;
    isShowTypeContaiter.value = false;
    isShowTreeContaiter.value = false;
    isShowSubFlowTreeContaiter.value = false;
    dialogTitle.value = "结束节点";
  }
  form.value.id = activeShape.id;
  form.value.name = activeShape.businessObject.name;
  form.value.gatewayDirection = activeShape.businessObject.gatewayDirection;
  form.value.type = activeShape.type;
  form.value.desc = activeShape.businessObject.$attrs.desc;
};

const errorCloseDrawer = () => {
  errorDrawer.value = false;
};
const closeAntDrawer = () => {
  drawer.value = false;
  isEdit.value = false;
};
// 校验
const check = () => {
  checkFlow(bpmnModeler, flowErrorMsg);
};
// 错误提示
const showError = () => {
  let ruleErrHtml = "";
  let flowErrHtml = "";
  let flowErr = "";
  let ruleErr = "";
  let errHtml = "";

  flowErrorMsg.value.map((item, i) => {
    flowErr += `<p style="padding:8px 0"><strong>${
      i + 1
    }. </strong>${item}</p>`;
  });
  ruleError.value.map((item, i) => {
    if (!item.nodeRule.valid) {
      let shape = getShape(item.nodeId);
      let shapeName = shape.businessObject.name || item.nodeId;
      let ruleErrIndex = `<p style="padding:8px 0"><strong>路由边：${shapeName}</strong></p>`;
      ruleErr += ruleErrIndex;
      item.nodeRule.ruleValidates.map((err, errI) => {
        if (!err.validate) {
          let errA = err.msg.split("\n");
          errA.map((er, erI) => {
            er.trim() &&
              (ruleErr += `<p style="padding:8px 0"><strong>${
                erI + 1
              }. </strong>${er.trim()}</p>`);
          });
        }
      });
    }
  });
  flowErrHtml = `<div><strong style="font-size:16px;margin:20px 0 10px;display:block;color:#F56C6C">绘制错误</strong>${flowErr}</div>`;
  ruleErrHtml = `<div><strong style="font-size:16px;margin:20px 0 10px;display:block;color:#F56C6C">规则错误</strong>${ruleErr}</div>`;
  errHtml =
    (flowErr ? flowErrHtml : flowErr) + (ruleErr ? ruleErrHtml : ruleErr);
  errContent.value = errHtml;
  errorDrawer.value = true;
};
const confirmOk = () => {
  saveFlow();
  confirmOpen.value = false;
};
// 保存
const toSaveFlow = () => {
  check();
  if (flowErrorMsg.value.length !== 0) {
    showError();
    confirmOpen.value = true;
  } else {
    saveFlow();
  }
};
/**
 * 直接处理树结构中的checked属性，根据transKeys判断选中状态
 * @param {Array|Object} data - 树结构数据或单个节点
 * @returns {Array|Object} 处理后的数据
 */
const prepareDataForAPI = (data) => {
  // 递归设置checked属性
  const processCheckedStatus = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;

    // 递归处理children数组
    if (obj.children && Array.isArray(obj.children)) {
      obj.children.forEach(child => processCheckedStatus(child));
    }

    // 处理其他对象类型属性
    for (const key in obj) {
      if (key !== 'key' && key !== 'children' && typeof obj[key] === 'object' && obj[key] !== null) {
        processCheckedStatus(obj[key]);
      }
    }

    // 删除originData属性
    if ('originData' in obj) {
      delete obj.originData;
    }
    if ('title' in obj) {
      delete obj.title;
    }
    if ('parentId' in obj) {
      delete obj.parentId;
    }
    if ('key' in obj) {
      delete obj.key;
    }
    if ('isLeaf' in obj) {
      delete obj.isLeaf;
    }
    if ('className' in obj) {
      delete obj.className;
    }

    return obj;
  };

  // 处理数据并返回
  return processCheckedStatus(data);
};
const saveFlow = () => {
  let obj = {};
  bpmnModeler.value.saveXML({ format: true }).then(({ xml }) => {
    previewResult.value = StringUtils.formatXml(xml);
    obj.ruleUuid = ruleUuid.value;
    obj.demandUuid = "";
    obj.ruleContent = previewResult.value;
    const elementRegistry = bpmnModeler.value.get("elementRegistry");
    // 业务描述路由边
    const sequenceFlowList = elementRegistry.filter(
      (item) => item.type === "bpmn:SequenceFlow" && item.businessObject.name
    );
    // 规则节点
    const taskList = elementRegistry.filter(
      (item) =>
        item.type === "bpmn:BusinessRuleTask" ||
        item.type === "bpmn:CallActivity"
    );
    // 去除多余条件数据
    for (let i = conditions.value.length - 1; i >= 0; i--) {
      let isFlow = sequenceFlowList.some((flowItem) => {
        return conditions.value[i].nodeId === flowItem.id;
      });
      if (!isFlow) {
        conditions.value.splice(i, 1);
      }
    }
    // 条件
    for (let i in conditions.value) {
      conditions.value[i] &&
        conditions.value[i].nodeRule &&
        (conditions.value[i].nodeRule.conditions = setExpressionValueType(
          conditions.value[i].nodeRule.conditions
        ));
    }
    obj.conditions = conditions.value;
    // 去除多余规则节点绑定规则 数据
    for (let i = nodeRuleTree.value.length - 1; i >= 0; i--) {
      let isTask = taskList.some((taskItem) => {
        return (
          nodeRuleTree.value[i].nodeId === `${ruleUuid.value}_${taskItem.id}`
        );
      });
      if (!isTask) {
        nodeRuleTree.value.splice(i, 1);
      }
    }
    obj.nodeRuleTree = prepareDataForAPI(nodeRuleTree.value);
    // obj.nodeRuleTree = nodeRuleTree.value;
    // return;
    ruleSave(obj).then((res) => {
      let code = res.code;
      let data = res.data;
      if (code === 20000) {
        if (data && data.length === 0) {
          message.success("保存成功");
        } else {
          let isError = data.some((i) => {
            return i["nodeRule"].valid === false;
          });
          valiRes.value = data;
          if (isError) {
            ruleError.value = data;
            showError();
            message.warning("保存成功，但是条件规则编写有误");
          } else {
            message.success("保存成功");
          }
        }
        emit('resetData')
        //更新上级列表状态
        globalEventEmitter.emit(REFRESH_RULE_LIST);
      } else {
        message.error(res.mag);
      }
    });
  });
};
const setExpressionValueType = (data) => {
  for (let i in data) {
    if (data[i].variable) {
      setExpressionParamsValType(data[i], data[i].variable, "variable");
    }
    if (
      (data[i].comparator && data[i].comparator.operatorParams) ||
      data[i].actionParams
    ) {
      let loopData =
        data[i].actionParams ||
        (data[i].comparator && data[i].comparator.operatorParams);
      for (let j in loopData) {
        setExpressionParamsValType(loopData[j], loopData[j]);
      }
    }
  }
  return data;
};
const setExpressionParamsValType = (data, obj, tag) => {
  const aParItemValType = [];
  if (obj.variableType === "expression") {
    for (let j in obj.expressionParams) {
      if (obj.expressionParams[j].next) {
        getLastNextValType(obj.expressionParams[j].next);
        function getLastNextValType(data) {
          if (data.next) {
            getLastNextValType(data.next);
          } else {
            aParItemValType.push(data.valueType);
          }
        }
      } else {
        aParItemValType.push(obj.expressionParams[j].valueType);
      }
    }
    let isBigDecimal = aParItemValType.find((item) => item === "BigDecimal");
    let isDouble = aParItemValType.find((item) => item === "Double");
    let isFloat = aParItemValType.find((item) => item === "Float");
    let isLong = aParItemValType.find((item) => item === "Long");
    let isInteger = aParItemValType.find((item) => item === "Integer");
    let isShort = aParItemValType.find((item) => item === "Short");
    if (isBigDecimal) {
      tag === "variable" && (data.leftValueType = "BigDecimal");
      obj.valueType = "BigDecimal";
    } else if (isDouble) {
      tag === "variable" && (data.leftValueType = "Double");
      obj.valueType = "Double";
    } else if (isFloat) {
      tag === "variable" && (data.leftValueType = "Float");
      obj.valueType = "Float";
    } else if (isLong) {
      tag === "variable" && (data.leftValueType = "Long");
      obj.valueType = "Long";
    } else if (isInteger) {
      tag === "variable" && (data.leftValueType = "Integer");
      obj.valueType = "Integer";
    } else if (isShort) {
      tag === "variable" && (data.leftValueType = "Short");
      obj.valueType = "Short";
    }
  } else if (obj.next) {
    // 方法
    getLastValType(obj.next);
  }
};
const getLastValType = (data) => {
  if (data.next) {
    getLastValType(data.next);
  } else {
    if (data.variableType === "method") {
      const aParamsType = data.paramsList;
      for (let k in data.methodParams) {
        if (data.methodParams[k].variableType === "expression") {
          data.methodParams[k].valueType = aParamsType[k].valueType;
        }
        if (data.methodParams[k].expressionParams) {
          for (let l in data.methodParams[k].expressionParams) {
            setExpressionParamsValType(
              data.methodParams[k].expressionParams[l],
              data.methodParams[k].expressionParams[l]
            );
          }
        }
      }
    }
  }
};
// 生成conditions
const setConditions = (param) => {
  let nodeId = activeShape.id;
  if (conditions.value && conditions.value.length > 0) {
    let activeIndex = -1;
    conditions.value.map((item, i) => {
      if (nodeId === item.nodeId) {
        activeIndex = i;
      }
    });
    if (activeIndex != "-1") {
      conditions.value[activeIndex].nodeRule = param.ruleContent;
    } else {
      conditions.value.push({
        nodeId,
        nodeRule: param.ruleContent,
      });
    }
  } else {
    conditions.value.push({
      nodeId,
      nodeRule: param.ruleContent,
    });
  }
};
const downloadProcessAsXml = () => {
  downloadProcess("xml");
};
const downloadProcessAsBpmn = () => {
  downloadProcess("bpmn");
};
const downloadProcessAsSvg = () => {
  downloadProcess("svg");
};
// 加载本地文件
const importLocalFile = () => {
  const file = refFile && refFile.value.files[0];
  const reader = new FileReader();
  reader.readAsText(file);
  reader.onload = (res) => {
    const { result } = res?.target;
    if(result){
      message.success('导入成功')
      createNewDiagram(result);
      fileList.value = [];
    }else{
      message.error('导入失败')
    }
  };
};
// 下载流程图到本地
const downloadProcess = async (type, name) => {
  try {
    // 按需要类型创建文件并下载
    if (type === "xml" || type === "bpmn") {
      const { err, xml } = await bpmnModeler.value.saveXML();
      // 读取异常时抛出异常
      if (err) {
        console.error(`[Process Designer Warn ]: ${err.message || err}`);
      }
      let { href, filename } = setEncoded(type.toUpperCase(), name, xml);
      downloadFunc(href, filename);
    } else {
      const { err, svg } = await bpmnModeler.value.saveSVG();
      // 读取异常时抛出异常
      if (err) {
        return console.error(err);
      }
      let { href, filename } = setEncoded("SVG", name, svg);
      downloadFunc(href, filename);
    }
  } catch (e) {
    console.error(`[Process Designer Warn ]: ${e.message || e}`);
  }
  // 文件下载方法
  function downloadFunc(href, filename) {
    if (href && filename) {
      let a = document.createElement("a");
      a.download = filename; //指定下载的文件名
      a.href = href; //  URL对象
      a.click(); // 模拟点击
      URL.revokeObjectURL(a.href); // 释放URL 对象
    }
  }
};

// 根据所需类型进行转码并返回下载地址
const setEncoded = (type, filename = "diagram", data) => {
  const encodedData = encodeURIComponent(data);
  return {
    filename: `${filename}.${type}`,
    href: `data:application/${
      type === "svg" ? "text/xml" : "bpmn20-xml"
    };charset=UTF-8,${encodedData}`,
    data: data,
  };
};
const elementsAlign = (align) => {
  const Align = bpmnModeler.value.get("alignElements");
  const Selection = bpmnModeler.value.get("selection");
  const SelectedElements = Selection.get();
  if (!SelectedElements || SelectedElements.length <= 1) {
    message.warning("请框选 或 按住Ctrl键选择多个元素对齐");
    return;
  }
  Align.trigger(SelectedElements, align);
};
const processZoomIn = (zoomStep = 0.1) => {
  let newZoom = Math.floor(defaultZoom.value * 100 + zoomStep * 100) / 100;
  if (newZoom > 4) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be greater than 4"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(defaultZoom.value);
};
const processZoomOut = (zoomStep = 0.1) => {
  let newZoom = Math.floor(defaultZoom.value * 100 - zoomStep * 100) / 100;
  if (newZoom < 0.2) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be less than 0.2"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(defaultZoom.value);
};
const processZoomTo = (newZoom = 1) => {
  if (newZoom < 0.2) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be less than 0.2"
    );
  }
  if (newZoom > 4) {
    throw new Error(
      "[Process Designer Warn ]: The zoom ratio cannot be greater than 4"
    );
  }
  defaultZoom.value = newZoom;
  bpmnModeler.value.get("canvas").zoom(newZoom);
};
const processReZoom = () => {
  defaultZoom.value = 1;
  bpmnModeler.value.get("canvas").zoom("fit-viewport", "auto");
};
const processRestart = () => {
  recoverable.value = false;
  revocable.value = false;
  createNewDiagram(null).then(() =>
    bpmnModeler.value.get("canvas").zoom(1, "auto")
  );
};
const processRedo = () => {
  bpmnModeler.value.get("commandStack").redo();
};
const processUndo = () => {
  bpmnModeler.value.get("commandStack").undo();
};
const handleMenuClick = (event) => {
  const key = event.key;
  switch (key) {
    case "xml":
      downloadProcessAsXml();
      break;
    case "svg":
      downloadProcessAsSvg();
      break;
    case "bpmn":
      downloadProcessAsBpmn();
      break;
  }
};

// 处理树穿梭框变化
const handleTreeTransferChange = (data) => {
  isInternalChange.value = false
  const {direction}=data
  if(direction==='right'){
    treeAdd(data.rightTreeData, data.targetKeys);
  }else{
    treeRemove(data.rightTreeData, data.targetKeys);
  }
  // 更新节点绑定
  // if (data && data.rightTreeData && data.rightTreeData.length > 0) {
  //   const ruleNode = {
  //     nodeId: activeShape.businessObject.$attrs["g:ruleFlowGroup"],
  //     nodeSelectTree: data.rightTreeData[0]
  //   };

  //   const existIndex = nodeRuleTree.value.findIndex(
  //     item => item.nodeId === ruleNode.nodeId
  //   );

  //   if (existIndex > -1) {
  //     nodeRuleTree.value[existIndex] = ruleNode;
  //   } else {
  //     nodeRuleTree.value.push(ruleNode);
  //   }
  // } else {
  //   // 清除绑定
  //   const nodeId = activeShape.businessObject.$attrs["g:ruleFlowGroup"];
  //   const index = nodeRuleTree.value.findIndex(item => item.nodeId === nodeId);
  //   if (index > -1) {
  //     nodeRuleTree.value.splice(index, 1);
  //     delete activeShape.businessObject.$attrs["g:ruleFlowGroup"];
  //   }
  // }

  // // 更新状态
  // nextTick(() => {
  //   nodeRuleTreeOld.value = JSON.parse(JSON.stringify(nodeRuleTree.value));
  //   treeTransferObjOld.value = JSON.parse(JSON.stringify(treeTransferObj.value));
  // });
};

// 处理弹窗取消
const handleModalCancel = () => {
  isModalVisible.value = false;
};

// 处理全屏切换
const onFullscreenToggle = () => {
  isFullscreen.value = !isFullscreen.value;
};

//打开全屏编辑
const fullScreen = () => {
  isModalVisible.value = true;
};

//全屏保存刷新父页面参数
const resetData = () => {
  getData(props.ruleInfo.uuid,props.ruleInfo.type);
}

// 检查规则节点上是否有绑定规则，如果没有则删除g:ruleFlowGroup属性
const checkAndRemoveEmptyRuleFlowGroup = () => {
  if (!bpmnModeler.value) return;

  console.log('开始检查规则节点的绑定状态...');
  const elementRegistry = bpmnModeler.value.get("elementRegistry");
  const allElements = elementRegistry.getAll();

  // 遍历所有元素
  allElements.forEach(element => {
    if (element.businessObject && element.businessObject.$attrs && element.businessObject.$attrs["g:ruleFlowGroup"]) {
      const ruleFlowGroupId = element.businessObject.$attrs["g:ruleFlowGroup"];

      // 检查该节点在nodeRuleTree中是否有对应数据且有绑定规则
      const hasBindRule = nodeRuleTree.value.some(item => {
        return item.nodeId === ruleFlowGroupId &&
               item.nodeSelectTree &&
               Object.keys(item.nodeSelectTree).length > 0;
      });

      // 如果没有绑定规则，则删除g:ruleFlowGroup属性
      if (!hasBindRule) {
        console.log(`删除节点 ${element.id} 的g:ruleFlowGroup属性(${ruleFlowGroupId})，因为没有绑定规则`);
        delete element.businessObject.$attrs["g:ruleFlowGroup"];
      } else {
        console.log(`节点 ${element.id} 的g:ruleFlowGroup属性(${ruleFlowGroupId})有绑定规则，保留`);
      }
    }
  });
  console.log('规则节点绑定状态检查完成');
};
const urlFlag = ref(true);
const ruleInfoSub = ref()
// 监听
watch(() => window.location.href, (newUrl) => {
  urlFlag.value = !newUrl.includes('ruleContent');
}, {
  immediate: true
});
// 提交数据前状态判断
function checkSubmitStatus(ruleInfo, type) {
    let uuids = '';
    //状态判断
    let status = '';

    if (ruleInfo && ruleInfo.uuid) {
        uuids = ruleInfo.uuid;
        if (ruleInfo.status === '已删除') {
            status = ruleInfo.status;
        }
        //锁定/解锁数据不可再次提交
        if ((ruleInfo.lockId && type === '已锁定') || (!ruleInfo.lockId && type === '已解锁')) {
            status = type;
        }
    }
    return [uuids, status];
}
// 提交规则
// 添加一个防抖变量
const isSubmitting = ref(false);

function handleSubmitRule() {
  // 1. 先检查是否正在提交
  if(isSubmitting.value) {
    console.log('[DEBUG] 提交操作被阻止：正在提交中');
    return;
  }

  // 2. 设置提交状态
  isSubmitting.value = true;

  try {
    if(urlFlag.value) {
      // 3. 检查必要的属性是否存在
      if (!props.ruleInfo?.uuid) {
        console.warn('[DEBUG] 提交失败：缺少必要的uuid属性');
        return;
      }

      // 4. 构建事件数据
      const eventData = {
        record: {
          uuid: props.ruleInfo.uuid,
          status: props.ruleInfo.status || '',
          ruleName: props.ruleInfo.ruleTitle || ''
        },
        tabKey: '编辑' + props.ruleInfo.uuid
      };

      console.log('[DEBUG] 准备发送事件，数据：', eventData);

      // 5. 使用Promise包装事件发送
      Promise.resolve().then(() => {
        globalEventEmitter.emit(RULE_SUBMIT_ACTION, eventData);
        console.log('[DEBUG] 事件发送完成');
      }).catch(error => {
        console.error('[DEBUG] 事件发送失败：', error);
      });

    } else {
      // 先获取最新的规则信息，然后再进行状态检查和提交
      getRuleByUuid({
        uuids: props.ruleInfo.uuid,
      }).then((res) => {
        if (res.code === 20000) {
          const ruleData = res.data;

          // 检查状态
          let [uuids, status] = checkSubmitStatus(ruleData, '');

          if (status) {
            message.error('状态为' + status + '规则不可提交');
            return;
          }

          if (uuids) {
            let pars = qs.stringify({
              uuids: uuids,
            });

            submitRule(pars).then((res) => {
              if (res.code === 20000) {
                if (res.data == '规则置为提交待审核成功！') {
                  message.success('规则置为提交待审核成功，不允许操作，即将关闭页面！');
                  // 添加2秒延时，让用户有时间看到提示信息
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                } else {
                  message.success(res.data);
                }
              } else {
                message.error(res.data);
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[DEBUG] 提交过程发生错误：', error);
  } finally {
    // 6. 确保状态重置
    setTimeout(() => {
      isSubmitting.value = false;
      console.log('[DEBUG] 提交状态已重置');
    }, 1000);
  }
}
</script>

<style lang="scss" scoped>
.bpmn ::v-deep {
  & {
    width: 100%;
    height: calc(100vh - 85px);
    position: relative;
    .canvas {
      width: 100%;
      height: 90vh;
    }
    .tool {
      position: absolute;
      width: 100%;
      height: 36px;
      z-index: 1;
      line-height: 36px;
      top: 0;
      left: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding-right: 13px;
      .ant-btn-group {
        margin-left: 16px;
      }
    }
    .a-drawer,
    .ant-drawer {
      overflow: auto !important;
      header {
        span {
          font-size: 18px;
          color: #303133;
        }
      }
      .a-form {
        padding: 20px;
      }
      .a-input,
      .a-textarea {
        width: 336px;
      }
      .filter-tree.a-input {
        width: 100%;
      }

    }

  }
  .a-alert {
    margin-bottom: 10px;
  }
  .a-form-item__content {
    line-height: unset;
  }
  .ant-btn {
    text-align: center;
    font-size: 12px;
    span {
      font-size: 12px;
    }
  }
  .ant-btn-group {
    // margin: 4px;
      /* 按钮放大20% */
 .ant-btn{
  // transform: scale(1.2);
  // margin: 0 8px; /* 增加间距 */
  font-size: 12px; /* 添加较小的字体大小 */
}

/* 为第一个按钮添加左边距 */
 .ant-btn:first-child{
  // margin-left: 4px;
}

/* 为最后一个按钮添加右边距 */
.ant-btn:last-child{
  // margin-right: 4px;
}
  }
  .a-tooltip__popper {
    .ant-btn {
      width: 100%;
      text-align: left;
      padding-left: 8px;
      padding-right: 8px;
    }
    .ant-btn:hover {
      background: rgba(64, 158, 255, 0.8);
      color: #ffffff;
    }
  }
  .align {
    position: relative;
    i {
      &:after {
        content: "|";
        position: absolute;
        transform: rotate(90deg) translate(200%, 60%);
      }
    }
  }
  .align.align-left i {
    transform: rotate(90deg);
  }
  .align.align-right i {
    transform: rotate(-90deg);
  }
  .align.align-top i {
    transform: rotate(180deg);
  }
  .align.align-bottom i {
    transform: rotate(0deg);
  }
  .align.align-center i {
    transform: rotate(90deg);
    &:after {
      transform: rotate(90deg) translate(0, 60%);
    }
  }
  .align.align-middle i {
    transform: rotate(0deg);
    &:after {
      transform: rotate(90deg) translate(0, 60%);
    }
  }
  .a-tree {
    background: unset;
  }
  .wl-transfer .transfer-title {
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
  .a-icon-document.blue {
    color: #409eff;
  }
  .bjs-powered-by {
    display: none;
  }
  .wl-transfer {
    overflow: auto;
  }

}
</style>
<style>
.node-info-drawer {
  .ant-form-item {
    margin-bottom: 0 !important;
    width: 100%;
  }
}
</style>
