<!-- 规则新增 -->

<template>
    <div id="publish_add">
        <a-form :model="ruleForm" :rules="rules" ref="formRef" label-align="right" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="环境名称" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" placeholder="环境名称" />
            </a-form-item>
            <a-form-item label="数 据 库" name="database">
                <a-select v-model:value="ruleForm.database" placeholder="请选择">
                    <a-select-option :key="1" value="org.hibernate.dialect.MySQL5InnoDBDialect" selected="selected">MySQL</a-select-option>
                    <a-select-option :key="2" value="org.hibernate.dialect.Oracle10gDialect">Oracle</a-select-option>
                    <a-select-option :key="3" value="org.hibernate.dialect.Oracle12cDialect">Oracle12</a-select-option>
                    <a-select-option :key="4" value="org.hibernate.dialect.PostgreSQLDialect">PostgreSQL</a-select-option>
                    <a-select-option :key="5"  value="org.hibernate.dialect.DB2Dialect">DB2</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="数据源名称" name="dataName">
                <a-input autocomplete="off" v-model:value="ruleForm.dataName" placeholder="数据源名称"></a-input>
            </a-form-item>
            <a-form-item label="是否执行建表语句" name="execute">
                <a-select v-model:value="ruleForm.execute" placeholder="请选择">
                    <a-select-option :key="1" value="1">是</a-select-option>
                    <a-select-option :key="0" value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="业务条线" name="business">
                <a-select v-model:value="ruleForm.business" placeholder="请选择" :filterOption="filterOption" showSearch>
                    <a-select-option v-for="item in menusList" :key="item.code" :value="item.code" :name="item.name">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="环境类型" name="execute">
                <a-select v-model:value="ruleForm.env_type" placeholder="请选择">
                    <a-select-option :key="0" value="0">测试环境</a-select-option>
                    <a-select-option :key="1" value="1">生产环境</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="是否启用" name="start">
                <a-select v-model:value="ruleForm.start" placeholder="请选择">
                    <a-select-option :key="1" value="1">是</a-select-option>
                    <a-select-option :key="0" value="0">否</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述" name="describe">
                <a-textarea
                v-model:value="ruleForm.describe"
                :auto-size="{ minRows: 2, maxRows: 6 }"
                :show-word-limit="true"
                :maxlength="60" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {postLine} from '@/api/post_permissions'
import {pubadd} from '@/api/pub_environment'

const menusList = ref([]);//业务条线
// 业务条线
const pubLine = () => {
    postLine().then((res)=>{
        //console.log(res)
        menusList.value =  res.data
    })
}
const props = defineProps({
})
const message = inject('message')
const ruleForm = ref({
    name:"",
    database:"",
    dataName:"",
    execute:"",
    business:"",  //业务条线
    env_type:"",
    start:"",
    describe:""
});

const rules = {
    name: [
        { required: true, message: '环境名称不能为空', trigger: 'change' }
    ],
    database: [
        { required: true, message: '数据库不能为空', trigger: 'change' }
    ],
    dataName: [
        { required: true, message: '数据源名称不能为空', trigger: 'change' }
    ],
    execute: [
        { required: true, message: '请选择', trigger: 'change' }
    ],
    business: [
        { required: true, message: '请选择业务条线', trigger: 'change' }
    ],
    env_type: [
        { required: true, message: '请选择环境类型', trigger: 'change' }
    ],
    start: [
        { required: true, message: '请选择', trigger: 'change' }

    ],
    describe:[
        {
            min: 1,
            max: 60,
            message: "长度在 1 到 255 个字符",
            trigger: "blur",
        },
    ],
}


const formRef = ref(null);

const submitFun = (callback) => {
    formRef.value.validate()
        .then(() => {
            let data = {
                "businessLine":ruleForm.value.business, //业务条线code
                "dbDialect": ruleForm.value.database,  //数据库
                "desc": ruleForm.value.describe,  //描述
                "environmentName": ruleForm.value.name,   //环境名称
                "environmentType": ruleForm.value.env_type,   //环境类型
                "isDefault":"",
                "isSql": ruleForm.value.execute,   //是否执行建表语句
                "isUse": ruleForm.value.start,  //是否启用
                "jndiName": ruleForm.value.dataName,  //数据源名称
            }
            pubadd(data).then((res)=>{
                if (res.code === 20000) {
                    message.success('新增成功！');
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    message.error(res.data);
                }

            })
        })
        .catch((error) => {
            console.log(error);
        });
};


onMounted(() => {
    pubLine();
});

defineExpose({
    submitFun,
});

</script>


