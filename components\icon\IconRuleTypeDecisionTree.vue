<!-- 决策树图标组件 -->
<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 18,
    class: ''
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'larkui-icon',
            'larkui-icon-doc-type-table',
            'icon-svg',
            'index-module_size_wVASz',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <g fill="none" fill-rule="evenodd">
            <path
                d="M4.75 1.267h10.5a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-14a2 2 0 0 1 2-2Z"
                stroke="#22869B" stroke-width="0.976" fill="#FFF" stroke-linecap="round"
                stroke-linejoin="round"></path>
            <path
                d="M15 3.9a.5.5 0 0 1 .09.992L15 4.9H9a.5.5 0 0 1-.09-.992L9 3.9h6Zm-8.2 0a.5.5 0 0 1 .09.992L6.8 4.9H5.2a.5.5 0 0 1-.09-.992L5.2 3.9h1.6Z"
                fill="#A9DAE5" fill-rule="nonzero"></path>
            <path
                d="M7.25 7.05v5.9a.8.8 0 0 1-.8.8h-.9a.8.8 0 0 1-.8-.8v-5.9a.8.8 0 0 1 .8-.8h.9a.8.8 0 0 1 .8.8ZM11.25 7.05v8.4a.8.8 0 0 1-.8.8h-.9a.8.8 0 0 1-.8-.8v-8.4a.8.8 0 0 1 .8-.8h.9a.8.8 0 0 1 .8.8ZM15.25 7.05v3.4a.8.8 0 0 1-.8.8h-.9a.8.8 0 0 1-.8-.8v-3.4a.8.8 0 0 1 .8-.8h.9a.8.8 0 0 1 .8.8Z"
                fill="#29BDDB"></path>
        </g>
    </svg>
</template> 