<template>
  <div>
    <div class="structuralWord" style="margin-top: 20px;">
      <span>
        <p class="structuralWord">
          <span>否则</span>
        </p>
        <!-- 如果锁定或 elseActionData 有数据，则不显示添加按钮 -->
        <div v-if="!locked && elseActionData.length === 0">
          <a-button 
            type="link" 
            @click="handleAddActionItem(undefined)" 
            :style="{ color: 'rgba(0,0,0,0.4)', fontSize: '12px', marginBottom: '8px' }"
          >
            <EditOutlined />添加
          </a-button>
        </div>
      </span>
    </div>
    <div class="actionCom" style="min-width: 10000px">
      <!-- 遍历 elseActionData 并渲染 ActionCom 组件 -->
      <ActionCom
        v-for="(item, index) in elseActionData"
        :key="'elseAction' + index"
        :pos="pos + '_' + index"
        :actionData="item"
        :actionValid="validList[index]"
        @addActionItem="handleAddActionItem"
        @deleteActionItem="handleDeleteActionItem"
        @onChange="handleActionComChange"
        @replaceActionItem="handleReplaceActionItem"
      />
    </div>
  </div>
</template>

<script setup>
import ActionCom from "./action.vue";


// 定义组件的 props
const props = defineProps({
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  },
  elseActionData: {
    type: Array,
    default: () => []
  },
  validList: {
    type: Array,
    default: () => []
  }
});

// 定义组件的 emits
const emit = defineEmits([
  'addActionItem',
  'deleteActionItem',
  'actionComChange',
  'replaceActionItem'
]);

// 处理添加动作项
const handleAddActionItem = (pos) => {
  emit('addActionItem', 'elseAction', pos);
};

// 处理删除动作项
const handleDeleteActionItem = (pos) => {
  emit('deleteActionItem', 'elseAction', pos);
};

// 处理动作变化
const handleActionComChange = (pos, newActionData, oldRowData) => {
  emit('actionComChange', 'elseAction', pos, newActionData, oldRowData);
};

// 处理替换动作项
const handleReplaceActionItem = (pos) => {
  emit('replaceActionItem', 'elseAction', pos);
};
</script>

<style scoped>
.structuralWord {
  margin-bottom: 10px;
}

.actionCom {
  min-width: 10000px;
}
</style>
