<!-- 主导航菜单组件 -->

<script setup lang="ts">
// 路由常量
const ROUTES = {
  DASHBOARD: '/dashboard',
  COLLECTIONS: '/collections'
} as const

// 动画持续时间常量
const NO_TRANSITION_DURATION = '0ms'
const NORMAL_TRANSITION_DURATION = '300ms'

import {
  login,
  logout,
  getAvailableMenuTree
} from '@/api/user';
import { getUserButtons } from "@/api/role";

// 导入菜单图标组件
import {
  SlidersOutlined,
  ExperimentOutlined,
  FundOutlined,
  ScheduleOutlined,
  SettingOutlined,
  SlidersFilled,
  ExperimentFilled,
  FundFilled,
  ScheduleFilled,
  SettingFilled,
  ContainerOutlined,
  FolderAddOutlined,
  AlertOutlined
} from '@ant-design/icons-vue';

interface MenuItem {
  name: string;
  routePath: string;
  children?: MenuItem[];
  hidden?: boolean;
  ordernum?: number;
}

interface MenuGroup {
  name: string;
  children: MenuItem[];
  hidden?: boolean;
  ordernum?: number;
}

const route = useRoute()
const router = useRouter();

// 从父组件获取collapsed状态
const props = defineProps<{
  collapsed: boolean;
}>();

// 当前激活的子菜单
const activeSubmenu = ref('');

// 下拉菜单的显示状态
const dropdownVisible = reactive(new Map());

// 鼠标是否在弹出菜单上
const isMouseOnPopover = reactive(new Map());

// 鼠标是否在菜单触发区上
const isMouseOnTrigger = reactive(new Map());

// 关闭菜单的定时器
const closeTimers = reactive(new Map());

// 监听路由变化，关闭所有悬浮菜单
watch(() => route.path, () => {
  // 关闭所有打开的菜单
  for (const [name, visible] of dropdownVisible.entries()) {
    if (visible) {
      dropdownVisible.set(name, false);

      // 清除该菜单的定时器
      if (closeTimers.get(name)) {
        clearTimeout(closeTimers.get(name));
        closeTimers.delete(name);
      }
    }
  }

  // 清除当前活跃的子菜单
  activeSubmenu.value = '';

  // 立即隐藏所有弹出菜单
  document.querySelectorAll('.ant-popover').forEach(el => {
    (el as HTMLElement).style.display = 'none';
  });

  // 设置CSS变量，让Vue过渡动画立即完成
  document.documentElement.style.setProperty('--menu-transition-duration', NO_TRANSITION_DURATION);

  // 在下一个事件循环恢复正常过渡时间
  setTimeout(() => {
    document.documentElement.style.setProperty('--menu-transition-duration', NORMAL_TRANSITION_DURATION);
  }, 50);
}, { immediate: true });

// 安全获取弹出容器，避免服务端渲染问题
const getPopupContainer = (triggerNode: HTMLElement): HTMLElement => {
  // 确保只在客户端执行
  if (typeof window !== 'undefined' && document) {
    return document.body;
  }
  // 确保总是返回一个 HTMLElement
  return triggerNode;
};

// 判断菜单项是否被选中
const isMenuItemActive = (path: string) => {
  // 特殊处理：如果路径以ruleBase-开头且不包含all和demandUuid，选中规则库管理菜单
  const paths = route.path.split('/')
  const lastPath = paths[paths.length - 1]
  if (route.path.indexOf('ruleBase-') !== -1 &&
      lastPath.indexOf('all') === -1 &&
      lastPath.indexOf('demandUuid') === -1 &&
      path === '/ruleBaseManage') {
    return true
  }

  // 特殊处理：如果路径包含demandUuid，选中规则调整菜单
  if (route.path.indexOf('demandUuid') !== -1 && path === '/ruleAdjustment') {
    return true
  }

  return route.path.indexOf(path) !== -1
}

// 判断菜单组是否有任何子项被选中
const isMenuGroupActive = (menuItem: MenuGroup) => {
  if (!menuItem.children) return false;

  // 检查菜单组的任何子项路径是否与当前路由匹配
  return menuItem.children.some(child => isMenuItemActive(child.routePath));
};

// 立即关闭所有其他菜单
const closeAllMenusExcept = (exceptName: string) => {
  // 遍历所有显示中的菜单
  for (const [name, visible] of dropdownVisible.entries()) {
    if (name !== exceptName && visible) {
      // 清除该菜单的定时器
      if (closeTimers.get(name)) {
        clearTimeout(closeTimers.get(name));
        closeTimers.delete(name);
      }

      // 立即关闭该菜单
      dropdownVisible.set(name, false);

      // 如果当前激活的是这个菜单，清除激活状态
      if (activeSubmenu.value === name) {
        activeSubmenu.value = '';
      }
    }
  }
};

// 设置悬停的子菜单组
const setActiveSubmenu = (name: string) => {
  if (props.collapsed) {
    isMouseOnTrigger.set(name, true);
    // 先关闭其他所有菜单
    closeAllMenusExcept(name);

    // 清除之前的定时器（如果存在）
    if (closeTimers.get(name)) {
      clearTimeout(closeTimers.get(name));
      closeTimers.delete(name);
    }

    activeSubmenu.value = name;
    // 设置下拉菜单显示状态
    dropdownVisible.set(name, true);
  }
};

// 清除悬停的子菜单组
const clearActiveSubmenu = (name: string) => {
  if (props.collapsed) {
    isMouseOnTrigger.set(name, false);

    // 如果鼠标在弹出菜单上，不关闭
    if (isMouseOnPopover.get(name)) {
      return;
    }

    // 设置延迟关闭的定时器
    closeTimers.set(name, setTimeout(() => {
      // 如果此时鼠标又回到了菜单上或触发器上，取消关闭
      if (isMouseOnPopover.get(name) || isMouseOnTrigger.get(name)) {
        closeTimers.delete(name);
        return;
      }

      // 清除当前激活的子菜单
      if (activeSubmenu.value === name) {
        activeSubmenu.value = '';
      }

      // 关闭对应的下拉菜单
      dropdownVisible.set(name, false);

      // 清除定时器引用
      closeTimers.delete(name);
    }, 300)); // 增加延迟时间到300毫秒
  }
};

// 处理下拉菜单可见状态变化
const handleVisibleChange = (visible: boolean, name: string) => {
  // 如果要显示菜单，先关闭其他菜单
  if (visible) {
    closeAllMenusExcept(name);
  }

  // 更新下拉菜单可见状态
  dropdownVisible.set(name, visible);

  // 同步更新activeSubmenu状态
  if (visible) {
    activeSubmenu.value = name;
  } else if (activeSubmenu.value === name) {
    activeSubmenu.value = '';
  }
};

// 鼠标进入弹出菜单
const handlePopoverMouseEnter = (name: string) => {
  isMouseOnPopover.set(name, true);

  // 清除关闭定时器
  if (closeTimers.get(name)) {
    clearTimeout(closeTimers.get(name));
    closeTimers.delete(name);
  }
};

// 鼠标离开弹出菜单
const handlePopoverMouseLeave = (name: string) => {
  isMouseOnPopover.set(name, false);

  // 如果同时鼠标已经不在触发器上，才开始关闭
  if (!isMouseOnTrigger.get(name)) {
    clearActiveSubmenu(name);
  }
};


const menus = ref([]as MenuGroup[]);
// 处理菜单点击事件
const handleArrowClick = (event: Event) => {
  // 阻止事件冒泡，避免触发父元素的点击事件
  event.stopPropagation()

  const menuTitle = event.currentTarget as HTMLElement
  const li = menuTitle.closest('.ant-menu-submenu-inline')

  if (!li) return

  // 切换菜单展开状态
  li.classList.toggle('ant-menu-submenu-open')

  // 获取子菜单
  const ul = li.querySelector('.ant-menu.ant-menu-sub')
  if (ul) {
    ul.classList.toggle('ant-menu-hidden')
  }
}

// 添加全局点击事件处理函数
const handleDocumentClick = (event: MouseEvent) => {
  // 检查点击是否在菜单内部
  const clickedInsideMenu = !!((event.target as HTMLElement).closest('.menu-popover-container') ||
                            (event.target as HTMLElement).closest('.menu-trigger-item'));

  if (!clickedInsideMenu) {
    // 关闭所有打开的菜单
    for (const [name, visible] of dropdownVisible.entries()) {
      if (visible) {
        dropdownVisible.set(name, false);

        // 清除该菜单的定时器
        if (closeTimers.get(name)) {
          clearTimeout(closeTimers.get(name));
          closeTimers.delete(name);
        }
      }
    }

    // 清除当前活跃的子菜单
    activeSubmenu.value = '';
  }
};

// 初始化菜单状态
onMounted(() => {
    // 初始化CSS过渡时间变量
    document.documentElement.style.setProperty('--menu-transition-duration', NORMAL_TRANSITION_DURATION);

    // 添加全局点击事件监听器
    document.addEventListener('click', handleDocumentClick);

    // get user info 获取可用菜单树
    let res = getAvailableMenuTree().then((res) =>{
                menus.value = res.data
              // 创建名称到ordernum的映射
              const nameToOrdernumMap = new Map();

              function formatData(data1: any[]) {
                data1.forEach(res => {
                  resultData.push(res.name);
                  // 保存名称和ordernum的对应关系
                  if (res.ordernum !== undefined) {
                    nameToOrdernumMap.set(res.name, res.ordernum);
                  }
                  if (res.children) {
                    formatData(res.children);
                  }
                });
              }

              // 获取到所有名称集合
              let resultData: string[] = [];
              formatData(res.data);

              // 过滤arr2中包含arr1相同的名称数组，并应用ordernum排序值
              function formatData2(data1: any[]) {
                for (let i = 0; i < data1.length; i++) {
                  let res = data1[i];
                  data1[i].hidden = false;

                  // 如果在返回的数据中存在此菜单项，设置可见并应用ordernum
                  if (resultData.includes(res.name)) {
                    // 应用API返回的ordernum值覆盖本地定义的值
                    if (nameToOrdernumMap.has(res.name)) {
                      data1[i].ordernum = nameToOrdernumMap.get(res.name);
                    }
                  } else {
                    data1[i].hidden = true;
                  }

                  if (res.children) {
                    formatData2(res.children);
                  }
                }
              }

              formatData2(menus);

              getUserButtons().then((res) =>{
                if (res.data === 'admin') {//如果为管理员账号则权限全开
                  setButtonRole('admin')
                } else {
                  let roleGroup: string[] = [];
                  let dataArr = Object.values(res.data);
                  for (let i = 0; i < dataArr.length; i++) {
                    let menu: any = dataArr[i];
                    for (let j = 0; j < menu.length; j++) {
                      roleGroup.push(menu[j].menuPath + '|' + menu[j].name);
                    }
                  }
                  setButtonRole(JSON.stringify(roleGroup));
                }
              });
      // 获取所有菜单组
      const menuGroups = document.querySelectorAll('.ant-menu-submenu-inline')

      menuGroups.forEach(li => {
        // 如果菜单组包含当前路由，则展开该菜单组
        const menuItems = li.querySelectorAll('.ant-menu-item')
        menuItems.forEach(item => {
          const link = item.querySelector('a')
          if (link && link.getAttribute('href') === route.path) {
            li.classList.add('ant-menu-submenu-open')
          }
        })
      })
    })
})

// 组件卸载时，移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleDocumentClick);
});

// 修改计算属性：过滤掉不需要的菜单项并按ordernum排序，以及过滤权限
const getFilteredChildren = (menuItem: MenuGroup | MenuItem): MenuItem[] => {
  if (!menuItem.children) return [];
  return menuItem.children
    .filter(item => item.name !== '政策填报' && item.name !== '政策审核' && !item.hidden)
    .sort((a, b) => {
      if (a.ordernum === undefined && b.ordernum === undefined) return 0;
      if (a.ordernum === undefined) return 1;
      if (b.ordernum === undefined) return -1;
      return a.ordernum - b.ordernum;
    });
};

// 处理菜单项点击事件，关闭悬浮菜单并导航
const handleMenuItemClick = (name: string, path: string, event: Event) => {
  // 阻止默认行为
  event.preventDefault();

  // 立即关闭菜单
  dropdownVisible.set(name, false);
  activeSubmenu.value = '';

  // 清除定时器（如果存在）
  if (closeTimers.get(name)) {
    clearTimeout(closeTimers.get(name));
    closeTimers.delete(name);
  }

  // 立即关闭所有菜单
  document.querySelectorAll('.ant-popover').forEach(el => {
    (el as HTMLElement).style.display = 'none';
  });

  // 设置CSS变量，让Vue过渡动画立即完成
  document.documentElement.style.setProperty('--menu-transition-duration', NO_TRANSITION_DURATION);

  // 先处理UI关闭，然后再导航
  nextTick(() => {
    // 使用路由导航
    router.push(path);
  });

  // 在下一个事件循环恢复正常过渡时间
  setTimeout(() => {
    document.documentElement.style.setProperty('--menu-transition-duration', NORMAL_TRANSITION_DURATION);
  }, 50);
};
</script>

<template>
  <ul
    class="ant-menu ant-menu-root ant-menu-inline ant-menu-light index-module_menuWrapper_Y2ft1 index-module_personalSubMenuWrapper_fQkuG"
    :class="{ 'menu-collapsed': collapsed }"
    role="menu" tabindex="0" data-menu-list="true" data-testid="sidebar-sidebar-menu">
    <!-- 开始菜单项 -->
    <li class="ant-menu-item"
        :class="{ 'ant-menu-item-selected': isMenuItemActive(ROUTES.DASHBOARD) }"
        role="menuitem"
        tabindex="-1"
        data-testid="sidebar-sidebar-menu-first-item"
        data-menu-id="rc-menu-uuid-77669-1-/dashboard"
        style="padding-left: 10px;">
      <svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-nav-clock icon">
        <path
          d="M128 16c61.856 0 112 50.144 112 112s-50.144 112-112 112S16 189.856 16 128 66.144 16 128 16Zm0 20c-50.81 0-92 41.19-92 92s41.19 92 92 92 92-41.19 92-92-41.19-92-92-92Zm-7 27.5c5.43 0 9.848 4.327 9.996 9.72l.004.28v53.568a10 10 0 0 0 3.086 7.224l.24.223 32.226 28.883c4.113 3.686 4.458 10.008.772 14.12-3.619 4.039-9.779 4.446-13.894.97l-.227-.197-32.226-28.883a30 30 0 0 1-9.973-21.858l-.004-.482V73.501c0-5.523 4.477-10 10-10Z"
          fill="currentColor" fill-rule="nonzero"></path>
      </svg><svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-time icon-active">
        <defs>
          <clipPath id="2087421936a">
            <path
              d="M112 0c61.856 0 112 50.144 112 112s-50.144 112-112 112S0 173.856 0 112 50.144 0 112 0Zm-7 47.5c-5.523 0-10 4.478-10 10v53.568l.004.482a30 30 0 0 0 9.973 21.858l32.226 28.883.227.197c4.115 3.476 10.275 3.069 13.894-.97 3.686-4.112 3.34-10.434-.772-14.12l-32.226-28.883-.24-.223a10 10 0 0 1-3.086-7.224V57.501l-.004-.28c-.148-5.394-4.567-9.72-9.996-9.72Z">
            </path>
          </clipPath>
        </defs>
        <g clip-path="url(#2087421936a)" transform="translate(16 16)">
          <path fill="var(--yq-yuque-green-600)" d="M0 0h224v224H0V0z"></path>
        </g>
      </svg><span class="ant-menu-title-content">
        <NuxtLink :to="ROUTES.DASHBOARD">开始</NuxtLink>
      </span></li>

    <!-- 收藏菜单项 -->
    <li class="ant-menu-item"
        :class="{ 'ant-menu-item-selected': isMenuItemActive(ROUTES.COLLECTIONS) }"
        role="menuitem"
        tabindex="-1"
        aria-disabled="false"
        data-menu-id="rc-menu-uuid-77669-1-/dashboard/collections"
        style="padding-left: 10px;">
      <svg width="16" height="16"
        viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-star-outlined icon">
        <path
          d="m109.698 24.732-22.514 51.09-55.546 5.624a20 20 0 0 0-12.899 6.573c-7.36 8.236-6.649 20.88 1.587 28.24l41.632 37.2-11.815 54.565a20 20 0 0 0 2.265 14.298l.185.313c5.643 9.302 17.723 12.402 27.163 6.904L128 201.44l48.244 28.1a20 20 0 0 0 14.298 2.264c10.796-2.338 17.652-12.984 15.315-23.78l-11.815-54.566 41.632-37.2a20 20 0 0 0 6.572-12.898c1.113-10.99-6.894-20.8-17.884-21.914l-55.546-5.625-22.514-51.09a20 20 0 0 0-10.237-10.235c-10.107-4.455-21.912.128-26.367 10.236ZM128 32.798l22.515 51.089A20 20 0 0 0 166.8 95.719l55.546 5.626-41.631 37.2a20 20 0 0 0-6.22 19.146l11.814 54.566-48.244-28.099a20 20 0 0 0-20.132 0L69.69 212.257l11.815-54.566a20 20 0 0 0-6.221-19.146l-41.631-37.2 55.546-5.626a20 20 0 0 0 16.286-11.832L128 32.798Z"
          fill="currentColor" fill-rule="nonzero"></path>
      </svg><svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-nav-star2 icon-active">
        <path
          d="M134.643 17.23a16.011 16.011 0 0 1 8.649 8.643l23.705 56.981c.29.687.938 1.156 1.681 1.217L225.362 89a16.015 16.015 0 0 1 13.733 10.643 15.999 15.999 0 0 1-4.06 16.887l-42.652 40.72a2 2 0 0 0-.56 1.92l14.335 60.087a16 16 0 0 1-6.16 16.671 16.026 16.026 0 0 1-17.77.711l-52.695-32.172a2.003 2.003 0 0 0-2.082 0L74.756 236.64a16.026 16.026 0 0 1-17.784-.696 16 16 0 0 1-6.161-16.686l14.335-60.087a2 2 0 0 0-.56-1.92l-42.637-40.736a15.999 15.999 0 0 1-4.043-16.875A16.015 16.015 0 0 1 31.623 89l56.7-4.93a2.002 2.002 0 0 0 1.665-1.216l23.705-56.981a16.011 16.011 0 0 1 8.682-8.662 16.027 16.027 0 0 1 12.268.018Z"
          fill="var(--yq-yuque-green-600)" fill-rule="nonzero"></path>
      </svg><span class="ant-menu-title-content">
        <NuxtLink :to="ROUTES.COLLECTIONS">收藏</NuxtLink>
      </span></li>

    <!-- 自定义菜单项 - 在收起状态下也使用相同的布局 -->
    <div style="margin-top: 14px" v-if="collapsed">
      <template v-if="collapsed">
        <!-- 使用a-popover替换a-dropdown -->
        <a-popover
          v-for="menuItem in menus"
          :key="menuItem.name + '-popover'"
          placement="rightTop"
          trigger="hover"
          :overlayClassName="'menu-popover'"
          :mouseEnterDelay="0.1"
          :mouseLeaveDelay="0.4"
          :getPopupContainer="getPopupContainer"
          :visible="dropdownVisible.get(menuItem.name)"
          @visibleChange="(visible) => handleVisibleChange(visible, menuItem.name)"
          :style="menuItem.hidden ? 'display:none' : ''"
          :overlayStyle="{paddingLeft: '0px'}"
          :destroyTooltipOnHide="true"
          :arrowPointAtCenter="false"
          :autoAdjustOverflow="true"
        >
          <template #content>
            <div
              class="menu-popover-container"
              @mouseenter="handlePopoverMouseEnter(menuItem.name)"
              @mouseleave="handlePopoverMouseLeave(menuItem.name)"
            >
              <div class="menu-popover-title">{{ menuItem.name }}</div>
              <a-divider style="margin: 0 0 4px 0" />
              <div class="menu-popover-list">
                <div
                  v-for="subMenuItem in getFilteredChildren(menuItem)"
                  :key="subMenuItem.routePath"
                  class="menu-popover-item"
                  :class="{ 'menu-popover-item-active': isMenuItemActive(subMenuItem.routePath) }"
                >
                  <NuxtLink :to="subMenuItem.routePath" @click="handleMenuItemClick(menuItem.name, subMenuItem.routePath, $event)">
                    {{ subMenuItem.name }}
                  </NuxtLink>
                </div>
              </div>
            </div>
          </template>

          <li
            :key="menuItem.name + '-group'"
            :style="menuItem.hidden ? 'display:none' : ''"
            class="ant-menu-item menu-trigger-item"
            style="padding-left: 10px;"
            @mouseenter="setActiveSubmenu(menuItem.name)"
            @mouseleave="() => clearActiveSubmenu(menuItem.name)"
          >
            <!-- 菜单图标 - 根据菜单项ID判断显示哪个图标 -->
             <template v-if="menuItem.id===4">
               <SlidersOutlined :style="{fontSize: '16px'}" v-if="!isMenuGroupActive(menuItem)"/>
               <SlidersFilled :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-if="isMenuGroupActive(menuItem)"/>
             </template>

            <ExperimentOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 20 && !isMenuGroupActive(menuItem)"/>
            <ExperimentFilled :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 20 && isMenuGroupActive(menuItem)"/>

            <FundOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 22 && !isMenuGroupActive(menuItem)"/>
            <FundFilled :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 22 && isMenuGroupActive(menuItem)"/>

            <ScheduleOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 37 && !isMenuGroupActive(menuItem)"/>
            <ScheduleFilled :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 37 && isMenuGroupActive(menuItem)"/>

            <SettingOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 2 && !isMenuGroupActive(menuItem)"/>
            <SettingFilled :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 2 && isMenuGroupActive(menuItem)"/>

            <ContainerOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 44 && !isMenuGroupActive(menuItem)"/>
            <ContainerOutlined :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 44 && isMenuGroupActive(menuItem)"/>

            <FolderAddOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 74 && !isMenuGroupActive(menuItem)"/>
            <FolderAddOutlined :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-else-if="menuItem.id === 74 && isMenuGroupActive(menuItem)"/>

            <template v-else>
              <AlertOutlined :style="{fontSize: '16px'}" v-if="!isMenuGroupActive(menuItem)"/>
              <AlertOutlined :style="{fontSize: '16px',color:'var(--yq-yuque-green-600)'}" v-if="isMenuGroupActive(menuItem)"/>
            </template>

            <span class="ant-menu-title-content">
              {{ menuItem.name }}
            </span>
          </li>
        </a-popover>
      </template>
    </div>
  </ul>

  <div class="index-module_scrollbarWrapper_XQaKC dashboard-sidebar-scrollbar">
    <div class="index-module_menuWrapper_Y2ft1">
      <ul
        class="ant-menu ant-menu-root ant-menu-inline ant-menu-light index-module_bookMenuWrapper_PWx1D index-module_resourceMenu_Lhgz3"
        :class="{ 'menu-collapsed': collapsed }"
        role="menu" tabindex="0" data-menu-list="true" data-testid="sidebar-sidebar-menu">
        <li class="ant-menu-submenu ant-menu-submenu-inline"
          :class="{
            'ant-menu-submenu-open': !collapsed,
            'menu-submenu-hover': collapsed
          }"
          role="none"
          v-for="menuItem in menus"
          :key="menuItem.name + '-group'"
          :style="menuItem.hidden || collapsed ? 'display:none' : ''"
          @mouseenter="setActiveSubmenu(menuItem.name)"
          @mouseleave="() => clearActiveSubmenu(menuItem.name)">

          <!-- 展开状态使用原来的实现 -->
          <div role="menuitem" class="ant-menu-submenu-title" tabindex="-1" aria-expanded="true" aria-haspopup="true"
            data-menu-id="rc-menu-uuid-77669-2-/dashboard/books"
            aria-controls="rc-menu-uuid-77669-2-/dashboard/books-popup" style="padding-left: 2px;"
            @click="handleArrowClick($event)">
            <!-- 箭头图标 -->
            <div class="index-module_switchIconWarp_dOsGE switch-arrow-wrap ant-menu-item-icon">
              <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                class="larkui-icon larkui-icon-review-arrow-down icon-svg index-module_size_wVASz"
                data-name="ReviewArrowDown"
                style="margin-right: 0px; width: 20px; min-width: 20px; height: 20px;">
                <defs>
                  <path id="3742204692a" d="M0 0h256v256H0z"></path>
                </defs>
                <g fill="none" fill-rule="evenodd">
                  <mask id="3742204692b" fill="#fff">
                    <use xlink:href="#3742204692a"></use>
                  </mask>
                  <path
                    d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z"
                    fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path>
                </g>
              </svg>
              <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                class="larkui-icon larkui-icon-review-arrow-right icon-svg index-module_size_wVASz"
                data-name="ReviewArrowRight"
                style="margin-right: 0px; width: 20px; min-width: 20px; height: 20px;">
                <path
                  d="m115.925 74.9 46.171 46.1a9.889 9.889 0 0 1 0 14l-46.17 46.1c-3.873 3.866-10.15 3.866-14.022 0a9.892 9.892 0 0 1-2.904-7V81.9c0-5.468 4.439-9.9 9.915-9.9a9.922 9.922 0 0 1 7.01 2.9Z"
                  fill="currentColor" fill-rule="nonzero"></path>
              </svg>
            </div>

            <span class="ant-menu-title-content">
              <div class="index-module_menuTitle_zpskK">
                <SlidersOutlined :style="{fontSize: '16px'}" v-if="menuItem.id === 4"/>
                <ExperimentOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 20"/>
                <FundOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 22"/>
                <ScheduleOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 37"/>
                <SettingOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 2"/>
                <ContainerOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 44"/>
                <FolderAddOutlined :style="{fontSize: '16px'}" v-else-if="menuItem.id === 74"/>
                <AlertOutlined :style="{fontSize: '16px'}" v-else/>
                {{ menuItem.name }}
              </div>
            </span>
            <i class="ant-menu-submenu-arrow"></i>
          </div>

          <!-- 展开状态下的子菜单 -->
          <ul class="ant-menu ant-menu-sub ant-menu-inline"
              v-show="!collapsed"
              data-menu-list="true">
            <div data-type="books" class="larkui-sortable">
              <li role="menuitem" tabindex="-1" v-for="subMenuItem in menuItem.children" :key="subMenuItem.routePath" data-type="books"
                data-testid="books-32589241" data-id="/32589241" data-index="0"
                class="ant-menu-item index-module_menuItem_R3vqQ"
                :class="{ 'ant-menu-item-selected': isMenuItemActive(subMenuItem.routePath) }"
                style="padding-left: 8px;" :style="subMenuItem.hidden?'display:none':''">
                <div class="index-module_itemWrapper_SxLTV">
                  <div class="index-module_bookItem_jMupe">
                    <NuxtLink :to="subMenuItem.routePath" class="index-module_link_KkvFY" data-desktoptarget="" title=""
                      draggable="false">
                      <div class="index-module_iconWrapper_1h300">
                      </div><span title="" class="sub-item-name index-module_name_4D0sT">{{ subMenuItem.name }}</span>
                    </NuxtLink>
                  </div>
                </div>
                <div class="index-module_badgeWrapper_g5Ycn"></div>
                <div></div>
              </li>
            </div>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
/* 默认状态：向下箭头显示，向右箭头隐藏 */
.larkui-icon-review-arrow-down {
  display: inline-block;
}

.larkui-icon-review-arrow-right {
  display: none;
}

/* 收起状态：向右箭头显示，向下箭头隐藏 */
.ant-menu-submenu:not(.ant-menu-submenu-open) .larkui-icon-review-arrow-down {
  display: none;
}

.ant-menu-submenu:not(.ant-menu-submenu-open) .larkui-icon-review-arrow-right {
  display: inline-block;
}

/* 菜单收起时的样式 */
.menu-collapsed .ant-menu-title-content {
  opacity: 0;
  transition: opacity 0.2s;
}

/* Popover菜单样式 */
:deep(.menu-popover) {
  min-width: 180px;
  padding: 0;
}

.menu-popover-container {
  padding: 6px;
  width: 100%;
}

.menu-popover-title {
  font-size: 15px;
  font-weight: 600;
  color: #000;
  padding: 4px 8px 4px 8px;
  text-align: left;
}

.menu-popover-list {
  margin-top: 4px;
}

.menu-popover-item {
  padding: 6px 12px;
  border-radius: 0;
  transition: all 0.15s;
  margin-bottom: 0;
  white-space: nowrap;
}

.menu-popover-item:hover {
  background-color: #e6f7ff;
}

.menu-popover-item-active {
  background-color: #e6f7ff;
  position: relative;
}

.menu-popover-item-active:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
}

.menu-popover-item-active a {
  font-weight: 500;
}

.menu-popover-item a {
  display: block;
  color: rgba(0, 0, 0, 0.85);
  text-decoration: none;
  font-size: 14px;
}

/* 箭头图标样式 */
.arrow-icon {
  margin-right: 8px;
  margin-left: auto;
}

/* 菜单图标样式 */
.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 左侧菜单选中高亮效果 */
.ant-menu-item-selected {
  position: relative;
  background-color: #e6f7ff !important; /* 设置为浅蓝色背景 */
}

.ant-menu-item-selected:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff; /* 修改为蓝色边框线 */
}

.ant-menu-item-selected .index-module_link_KkvFY {
  font-weight: 500;
}

.ant-menu-item-selected .sub-item-name {
  font-weight: 500;
}

/* 鼠标悬停效果 */
.ant-menu-item:hover {
  background-color: #e6f7ff !important; /* 设置为相同的浅蓝色背景 */
}

/* 主菜单标题悬浮效果 */
.ant-menu-submenu-title:hover {
  background-color: #e6f7ff !important; /* 设置为相同的浅蓝色背景 */
}

/* 悬浮效果 */
.menu-submenu-hover {
  background-color: #f0f0f0;
}

/* 添加弹出菜单与触发元素之间的连接桥梁，确保鼠标可以平滑移动 */
:deep(.menu-popover .ant-popover-arrow) {
  display: none;
}

:deep(.menu-popover .ant-popover-inner) {
  border-radius: 4px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}

:deep(.menu-popover .ant-popover-inner-content) {
  padding: 0;
}

/* 分割线样式调整 */
:deep(.menu-popover .ant-divider) {
  margin: 4px 0;
}

/* 确保触发项有足够的区域用于交互 */
.menu-trigger-item {
  position: relative;
  z-index: 1;
}

/* 调整弹出层位置 */
:deep(.ant-popover.menu-popover) {
  padding-left: 0 !important;
  margin-left: -4px !important;
}

:deep(.ant-popover-arrow) {
  display: none !important;
}

:deep(.ant-popover-placement-right) {
  padding-left: 0 !important;
}

:deep(.ant-popover-placement-right .ant-popover-content) {
  margin-left: 0 !important;
}

/* 确保弹出菜单与图标之间没有间隙 */
:deep(.menu-popover .ant-popover-inner) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* 确保立即关闭的样式 */
:deep(.ant-popover-hidden) {
  display: none !important;
}

:deep(.menu-popover) {
  min-width: 180px;
  padding: 0;
  transition-duration: var(--menu-transition-duration, 300ms);
}
</style>
