<!-- 任务管理操作组件 -->

<template>
    <FlexDrawer
        :visible="isModalVisible"
        @close="back"
        :title="title"
        :if="isModalVisible"
    >
        <div class="rule_bom_management">
            <div class="tabs-container">
                <a-tabs v-model:activeKey="tabName" @edit="onTabEdit" type="editable-card" hide-add >
                    <template #rightExtra>
                        <a-tooltip title="关闭全部">
                            <a-button shape="circle" v-if="tabLists.length || tabLists1.length || tabLists2.length || tabLists3.length" size="small" @click="clearTabs" style="margin-right: 10px"><template #icon><CloseOutlined /></template></a-button>
                        </a-tooltip>
                    </template>

                    <a-tab-pane :tab="subLongName('任务详情')" key="first" :closable="false">
                        <template #tab>
                            <a-tooltip :title="'任务详情'">
                                <span>{{ subLongName('任务详情') }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListDetails ref="details" :infoData="infoData" v-if="tabName === 'first'"></ListDetails>
                        </keep-alive>
                    </a-tab-pane>
                    <a-tab-pane
                            :tab="subLongName('关联规则')"
                            :closable="false"
                            key="listRule"
                            v-if="
                 datar.type == 'info' ||
                 datar.type == 'AuditOperations' ||
                 datar.type == 'adjustment' ||
                 datar.type == 'updateRuleCheck' ||
                 datar.type == 'Announcement' ||
                 datar.type == 'taskInquiry' ||
                 datar.type == 'testing' ||
                 datar.type == 'handle' ||
                 datar.type == 'AuditOperation' ||
                 datar.type == 'submission' ||
                 datar.type == 'release' ||
                 datar.type == 'taskAudit' ||
                 datar.type == 'policyInfo' ||
                 datar.type == 'pCheckInfo'
               "
                    >
                        <template #tab>
                            <a-tooltip :title="'关联规则'">
                                <span>{{ subLongName('关联规则') }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListRule
                                    @addTabs="addTabs"
                                    @addHis="addHis"
                                    :infoData="infoData"
                                    ref="listRule"
                                    v-if="tabName === 'listRule'"
                            ></ListRule>
                        </keep-alive>
                    </a-tab-pane>
                    <a-tab-pane :tab="subLongName('文件列表')" key="second" name="listFile" :closable="false">
                        <template #tab>
                            <a-tooltip :title="'文件列表'">
                                <span>{{ subLongName('文件列表') }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListFile ref="listFile" :infoData="infoData" v-if="tabName === 'second'"></ListFile>
                        </keep-alive>
                    </a-tab-pane>
                    <a-tab-pane :tab="subLongName('流程日志')" key="third" name="listLog" :closable="false">
                        <template #tab>
                            <a-tooltip :title="'流程日志'">
                                <span>{{ subLongName('流程日志') }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListLog ref="listLog" :infoData="infoData"  v-if="tabName === 'third'"></ListLog>
                        </keep-alive>
                    </a-tab-pane>

                    <a-tab-pane
                            v-for="item in tabLists"
                            :key="item.id"
                            :closable="true"
                    >
                        <template #tab>
                            <a-tooltip :title="item.name">
                                <span>{{ subLongName(item.name) }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ReleaseDetails :obj="item"></ReleaseDetails>
                        </keep-alive>
                    </a-tab-pane>
                    <a-tab-pane
                            v-for="item in tabLists2"
                            :key="item.uuid"
                            :closable="true"
                    >
                        <template #tab>
                            <a-tooltip :title="item.name">
                                <span>{{ subLongName(item.name) }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListHistoricals :obj="item"></ListHistoricals>
                        </keep-alive>
                    </a-tab-pane>
                    <a-tab-pane
                            v-for="item in tabLists3"
                            :key="item.uuid"
                            :closable="true"
                    >
                        <template #tab>
                            <a-tooltip :title="item.name">
                                <span>{{ subLongName(item.name) }}</span>
                            </a-tooltip>
                        </template>
                        <keep-alive>
                            <ListHistoryDetail :obj="item"></ListHistoryDetail>
                        </keep-alive>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>

        <!-- 次级抽屉 -->
        <FlexDrawer
            v-if="subDrawerVisible"
            :visible="subDrawerVisible"
            @close="closeSubDrawer"
            :title="currentSubDrawerTitle"
            :width="800"
            :showCloseBtn="false"
        >
            <component
                :is="currentSubDrawerComponent"
                :obj="currentSubDrawerData"
            ></component>
        </FlexDrawer>

        <template #footer v-show="showFooter">
            <!-- 政策提交 button-->
            <a-space v-if="datar.type == 'addPolicy'">
                <a-button
                        @click="policySubmit(1)"
                        v-if="datar.type !== 'policyInfo'"
                >保存</a-button>
                <a-button
                        @click="policySubmit(2)"
                        v-if="datar.type !== 'policyInfo'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 政策审核 button-->
            <a-space v-if="datar.type == 'policyAudit'">
                <a-button
                        @click="policyAuditSubmit()"
                        v-if="datar.type !== 'pCheckInfo'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 任务提交 button-->
            <a-space v-if="datar.type == 'add' || datar.type == 'update'">
                <a-button
                        @click="taskSubmit(1)"
                        v-if="datar.type !== 'info'"
                >保存</a-button>
                <a-button
                        @click="taskSubmit(2)"
                        v-if="datar.type !== 'info'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 调整规则 button-->
            <a-space v-if="datar.type == 'adjustment'">
                <a-button
                        @click="ruleSubmit()"
                        v-if="datar.type !== 'info'"
                        type="primary"
                        :loading="loading"
                >提交</a-button>
            </a-space>

            <!-- 规则测试 button-->
            <a-space v-if="datar.type == 'submission'">
                <a-button
                        @click="ruleSubmits()"
                        v-if="datar.type !== 'info'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 任务审核 button-->
            <a-space v-if="datar.type == 'updateRuleCheck'">
                <a-button
                        @click="examine()"
                        v-if="datar.type !== 'info'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 任务发布 button-->
            <a-space v-if="datar.type == 'release'">
                <a-button
                        @click="release()"
                        v-if="datar.type !== 'info'"
                        type="primary"
                >提交</a-button>
            </a-space>

            <!-- 需求审核 button-->
            <a-space v-if="datar.type == 'audit'">
                <a-button
                        @click="audit()"
                        v-if="datar.type !== 'info'"
                        type="primary"
                >提交</a-button>
            </a-space>
        </template>
    </FlexDrawer>
</template>

<script setup>
    import {updateBulletin} from "@/api/bulletin";
    import {
        adjustment,
        getDemandApplyByUuid,
        saveOrUpdate,
        chenckState,
        submission,
        updateRuleCheck,
        updatePublish,
        submitCheck,
        policySaveOrUpdate,
        submitPolicyCheck,
    } from "@/api/task";
    import ListDetails from "@/businessComponents/task/components/listDetails"; //任务详情
    import ListRule from "@/businessComponents/task/components/listRule"; // 关联规则
    import ListFile from "@/businessComponents/task/components/listFile"; // 文件列表
    import ListLog from "@/businessComponents/task/components/listLog"; // 流程日志
    import ListHistory from "@/businessComponents/task/components/listHistory"; // 历史对比
    import ListHistoricals from "@/businessComponents/task/components/listHistoricals"; // 历史版本详情
    import ListHistoryDetail from "@/businessComponents/task/components/listHistoryDetail"; // 历史版本详情
    import ReleaseDetails from "@/businessComponents/task/components/releaseDetails"; //版本信息
    import { subLongName } from "@/utils/ruleUtil"; // 引入截断函数
    const emit=defineEmits(["close"])
    const message = inject('message')
    const modal = inject('modal')

    const router = useRouter();

    const form = reactive({
        id:"",
        title:"",
        bulState:"",
        writer:"",
        content:"",
    });


    const props = defineProps({
        isModalVisible: {
            type: Boolean,
            default: false,
        },
        datar: {
            type: Object,
        },
        title: {
            type: String,
            default: '任务管理'
        }
    });
    const infoData = ref('');
    const tabName = ref("first");

    // 可显示操作按钮的类型列表
    const actionTypes = [
        'addPolicy', 'policyAudit', 'add', 'update', 'adjustment',
        'submission', 'updateRuleCheck', 'release', 'audit'
    ];

    // 计算属性：是否显示底部按钮区域
    const showFooter = computed(() => {
        return tabName.value === 'first' && actionTypes.includes(props.datar.type);
    });

    watch(()=>props.isModalVisible,(newValue,oldValue)=>{
        if(newValue === true){
            infoData.value = props.datar;
        }
    },{immediate:true,deep:true})
    watch(
        () => props.datar,
        (newValue) => {
            if (newValue) {
                infoData.value = props.datar;
            }
        },
        {
            immediate: true,
        }
    );

    const submitFun = (callback) => {
        ruleForm.value.validate().then(() => {
            updateBulletin({
                ...form
            }).then((res) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    message.error(res.data);
                }
            })
        }).catch((error) => {
            console.log(error);
            message.error(error);
        })
    };

    //返回列表
    const back = (flag = false) => {
        emit("close",flag)
    };

    const details = ref(null);
    const loading = ref(false);

    //规则调整提交
    const ruleSubmit = () => {
        details.value.ruleForm.validate().then(() => {
            ruleSubmitValidate();
        })
    }
    const ruleSubmitValidate = async () => {
        loading.value = true;
        try {
            var obj = {
                environmentIds:
                    details.value.data.ruleForm.environmentIds.toString(),
                demandUuid: details.value.data.ruleForm.uuid,
            };
            const res = await chenckState(obj)
            if (res.code == 20000) {
                const res2 = await adjustment(obj)
                if (res2.code == 20000) {
                    message.success('提交成功')
                    back(true);
                }
            }
        }finally {
            loading.value = false;
        }
    }
    //政策提交和保存
    const policySubmit = (type) =>{
        details.value.ruleForm.validate().then(() => {
            var obj = details.value.data.ruleForm;
            if (props.datar.uuid && props.datar.uuid !== "") {
            } else {
                delete obj.createdTime;
                delete obj.createdName;
                delete obj.auditType;
                delete obj.creatdeId;
                delete obj.demandUuid;
                delete obj.environmentIds;
                delete obj.logContent;
                delete obj.logContents;
                delete obj.orgName;
                delete obj.subType;
            }
            policySaveOrUpdate(obj, type).then((res) => {
                if (res.code == 20000) {
                    if (type===1){
                        message.success('保存成功')
                        back(true);
                        navigateTo('policyFilling')
                    }else if (type===2){
                        message.success('提交成功')
                        back(true);
                        navigateTo('policyFilling')
                    }
                }
            });
        });
    };
    // 政策审核提交
    const policyAuditSubmit=()=> {
        details.value.ruleForm.validate().then(() => {
            var obj = {
                logContent: details.value.data.ruleForm.logContents,
                demandUuid: details.value.data.ruleForm.uuid,
                auditType: details.value.data.ruleForm.demandUuid,
            };
            submitPolicyCheck(obj).then((res) => {
                if (res.code == 20000) {
                    message.success('审核成功')
                    back(true);
                }
            });
        });
    };
    //任务提交和保存
    const taskSubmit=(type) =>{
        details.value.ruleForm.validate().then(() => {
            var obj = details.value.data.ruleForm;
            if (props.datar.uuid && props.datar.uuid !== "") {
            } else {
                delete obj.createdTime;
                delete obj.createdName;
                delete obj.auditType;
                delete obj.creatdeId;
                delete obj.demandUuid;
                delete obj.environmentIds;
                delete obj.logContent;
                delete obj.logContents;
                delete obj.orgName;
                delete obj.subType;
            }
            saveOrUpdate(obj, type).then((res) => {
                if (res.code == 20000) {
                    if (type===1){
                        message.success('保存成功')
                        back(true);
                        navigateTo('taskFilling')
                    }else if (type===2){
                        message.success('提交成功')
                        back(true);
                        navigateTo('taskFilling')
                    }
                }
            });

        });
    };
    // 需求审核提交
    const audit = () => {
        details.value.ruleForm.validate().then(() => {
            var obj = {
                logContent: details.value.data.ruleForm.logContents,
                demandUuid: details.value.data.ruleForm.uuid,
                auditType: details.value.data.ruleForm.demandUuid,
            };
            submitCheck(obj).then((res) => {
                if (res.code == 20000) {
                    message.success('提交成功')
                    back(true);
                }
            });
        });
    };
    //规则测试提交
    const ruleSubmits=()=> {
        details.value.ruleForm.validate().then(() => {
            var obj = {
                logContent: details.value.data.ruleForm.logContent,
                subType: details.value.data.ruleForm.subType,
                demandUuid: details.value.data.ruleForm.uuid,
            };
            submission(obj).then((res) => {
                if (res.code == 20000) {
                    message.success('提交成功')
                    back(true);
                }
            });
        });
    };
    // 任务审核提交
    const examine=() =>{
        details.value.ruleForm.validate().then(() => {
            var obj = {
                logContent: details.value.data.ruleForm.logContents,
                environmentId: details.value.data.ruleForm.environmentId,
                demandUuid: details.value.data.ruleForm.uuid,
                auditType: details.value.data.ruleForm.demandUuid,
            };
            updateRuleCheck(obj).then((res) => {
                if (res.code == 20000) {
                    message.success('提交成功')
                    back(true);
                }
            });
        });
    };
    // 任务发布提交
    const release=() =>{
        details.value.ruleForm.validate().then(() => {
            var obj = {
                demandUuid: details.value.data.ruleForm.uuid,
                auditType: details.value.data.ruleForm.auditType,
                logContent: details.value.data.ruleForm.logContents,
            };
            updatePublish(obj).then((res) => {
                if (res.code == 20000) {
                    message.success('提交发布成功')
                    back(true);
                }
            });
        });
    };
    const tabLists = ref([]);
    const tabLists1 = ref([]);
    const tabLists2 = ref([]);
    const tabLists3 = ref([]);
    const nameArr = ref(["listLog", "listFile", "listRule"]);

    //次级抽屉相关
    const subDrawerVisible = ref(false);
    const currentSubDrawerTitle = ref('');
    const currentSubDrawerData = ref(null);
    const currentSubDrawerComponent = ref(null);

    //修改addTabs函数
    const addTabs = (row) => {
        currentSubDrawerData.value = {
            id: row.ruleUuid,
            ruleEdition: row.ruleEdition,
            demandUuid: row.demandUuid,
            originalEdition: row.originalEdition,
            name: row.ruleName,
        };
        currentSubDrawerTitle.value = row.ruleName;
        currentSubDrawerComponent.value = ReleaseDetails;
        subDrawerVisible.value = true;
    }

    // 修改addHis函数
    const addHis = (row) => {
        currentSubDrawerData.value = {
            ruleUuid: row.ruleUuid + "history",
            needId: row.ruleUuid,
            name: row.ruleName,
        };
        currentSubDrawerTitle.value = row.ruleName + '(历史版本)';
        currentSubDrawerComponent.value = ListHistory;
        subDrawerVisible.value = true;
    }

    // 关闭次级抽屉
    const closeSubDrawer = () => {
        subDrawerVisible.value = false;
        currentSubDrawerData.value = null;
        currentSubDrawerComponent.value = null;
    }

    // 处理标签页编辑（关闭）
    const onTabEdit = (targetKey, action) => {
        if (action === 'remove') {
            removeTab(targetKey);
        }
    }

    // 移除指定标签页
    const removeTab = (targetKey) => {
        // 查找并移除目标标签页
        let tabsArray = [
            { list: tabLists.value, keyName: 'id' },
            { list: tabLists1.value, keyName: 'ruleUuid' },
            { list: tabLists2.value, keyName: 'uuid' },
            { list: tabLists3.value, keyName: 'uuid' }
        ];

        for (let tabObj of tabsArray) {
            let index = tabObj.list.findIndex(item => item[tabObj.keyName] === targetKey);
            if (index >= 0) {
                tabObj.list.splice(index, 1);

                // 如果关闭的是当前活动页，则切换到另一个标签
                if (tabName.value === targetKey) {
                    if (tabObj.list.length) {
                        // 如果还有其他标签，选择前一个或后一个
                        const newIndex = Math.max(0, index - 1);
                        tabName.value = tabObj.list[newIndex][tabObj.keyName];
                    } else {
                        // 如果没有其他标签，返回首页
                        tabName.value = "first";
                    }
                }
                break;
            }
        }
    }

    //数组去重
    const distinct = (arr, str) => {
        for (let i = 0; i < arr.length; i++) {
            for (let j = i + 1; j < arr.length; j++) {
                if (arr[i][str] === arr[j][str]) {
                    arr.splice(j, 1);
                    j--;
                }
            }
        }
    }

    // 关闭全部
    const clearTabs = () => {
        modal.confirm({
            title: '确定关闭全部标签页吗？',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                tabLists.value = [];
                tabLists1.value = [];
                tabLists2.value = [];
                tabLists3.value = [];
                tabName.value = "first";
            }
        });
    }

    defineExpose({
        submitFun
    });

</script>

<style lang="scss" scoped>
.rule_bom_management {
    height: 100%;
    display: flex;
    flex-direction: column;

    .tabs-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;

        :deep(.ant-tabs) {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: auto
        }

        :deep(.ant-tabs-content) {
            flex: 1;
            overflow: auto;
        }

        :deep(.ant-tabs-tabpane) {
            height: 100%;
            overflow: auto;
        }
    }
}
</style>
