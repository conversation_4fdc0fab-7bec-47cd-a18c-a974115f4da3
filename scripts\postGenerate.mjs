/**
 * Nuxt应用静态生成后处理脚本 (Post-generate Processing Script)
 * -------------------------------------------------------
 * 
 * 【功能概述】
 * 此脚本用于在Nuxt应用静态生成(generate)完成后，向生成的HTML文件中注入加载动画（Loading Spinner），
 * 解决Nuxt应用初始加载时的白屏问题，提升用户体验。
 * 
 * 【解决的问题】
 * 1. Nuxt应用在客户端初始加载时通常会有一段白屏期
 * 2. 特别是在网络较慢或应用较大时，这种白屏会影响用户体验
 * 3. Nuxt官方的NuxtLoadingIndicator组件仅在页面间导航时显示
 * 4. 初始加载时没有内置的加载指示器
 * 
 * 【工作原理】
 * 1. 在静态生成完成后扫描.output/public目录下的所有HTML文件
 * 2. 向每个HTML文件的<head>标签中注入加载动画样式和脚本
 * 3. 向<body>结束前注入移除加载动画的逻辑
 * 4. 加载动画会在页面内容完全加载后自动淡出并移除
 * 
 * 【关键特性】
 * - 使用document.write确保加载动画即时显示，不依赖DOM加载
 * - 使用!important和内联样式保证样式不被覆盖
 * - 动画会在Nuxt应用内容渲染后自动淡出消失
 * - 处理各种HTML结构，确保脚本能正确注入
 * 
 * 【使用方法】
 * 1. 将此脚本添加到package.json的静态生成后钩子中：
 *    "scripts": {
 *      "generate": "nuxt generate",
 *      "postgenerate": "node scripts/postGenerate.mjs"
 *    }
 * 2. 执行npm run generate命令时会自动运行此脚本
 * 
 * 【注意事项】
 * - 此脚本仅适用于静态生成(generate)命令，不适用于常规构建(build)命令
 * - 常规构建不会生成HTML文件，因此此脚本在build后运行会报错
 * 
 * 【定制说明】
 * - 可修改immediateLoadingScript变量中的样式自定义加载动画外观
 * - 可修改nuxt-loader-text元素的文本内容更改提示文字
 * - 可调整bodyEndScript中的延迟时间控制动画消失时机
 * 
 * <AUTHOR>
 * @version 1.0.1
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 构建输出目录
const outputDir = path.resolve(__dirname, '../.output/public');

// 要添加到<head>中的样式定义
const headStyleScript = `
<style id="nuxt-loader-style">
@keyframes nuxt-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
`;

// 使用内联HTML直接插入加载动画
const staticLoaderHTML = `
<!-- 静态加载动画 -->
<div id="nuxt-loader-overlay" style="position:fixed !important;z-index:2147483647 !important;top:0 !important;left:0 !important;width:100vw !important;height:100vh !important;min-width:100vw !important;min-height:100vh !important;max-width:100vw !important;max-height:100vh !important;background:#fff !important;display:flex !important;flex-direction:column !important;align-items:center !important;justify-content:center !important;margin:0 !important;padding:0 !important;margin-top:-40px !important;transition:opacity 0.3s !important;opacity:1 !important;border:none !important;outline:none !important;">
  <div id="nuxt-loader-spinner" style="width:60px !important;height:60px !important;min-width:60px !important;min-height:60px !important;max-width:60px !important;max-height:60px !important;border-radius:50% !important;border:6px solid #f3f3f3 !important;border-top:6px solid #3498db !important;animation:nuxt-spin 1s linear infinite !important;margin:0 !important;padding:0 !important;box-sizing:border-box !important;display:block !important;"></div>
  <div id="nuxt-loader-text" style="width:auto !important;min-width:auto !important;max-width:none !important;margin:0 !important;margin-top:20px !important;padding:0 !important;font-size:18px !important;line-height:1.5 !important;letter-spacing:normal !important;color:#333 !important;font-weight:400 !important;text-align:center !important;display:block !important;font-style:normal !important;text-decoration:none !important;text-transform:none !important;white-space:normal !important;">资源加载中，请稍等...</div>
</div>
`;

// 要添加到<body>结束的脚本 - 修改后的版本
const bodyEndScript = `
<script>
window.addEventListener('load', function() {
  // 延迟检查，确保Nuxt应用已经渲染
  function checkNuxtReady() {
    var nuxtEl = document.getElementById('__nuxt');
    var loading = document.getElementById('nuxt-loader-overlay');
    
    // 检查Nuxt容器是否已有内容
    if (!loading || (nuxtEl && (nuxtEl.childElementCount > 0 || nuxtEl.innerHTML.trim() !== ''))) {
      // Nuxt应用已渲染，可以移除加载动画
      if (loading) {
        loading.style.opacity = '0';
        setTimeout(function() {
          if (loading.parentNode) {
            loading.parentNode.removeChild(loading);
            // 也可以移除样式标签
            var styleTag = document.getElementById('nuxt-loader-style');
            if (styleTag && styleTag.parentNode) {
              styleTag.parentNode.removeChild(styleTag);
            }
          }
        }, 300);
      }
    } else {
      // 继续等待Nuxt应用渲染
      setTimeout(checkNuxtReady, 50);
    }
  }
  
  // 开始检查Nuxt是否准备好
  // 先等待一小段时间确保资源加载完成
  setTimeout(checkNuxtReady, 100);
});
</script>
`;

// 递归查找所有HTML文件
function findHtmlFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findHtmlFiles(filePath, fileList);
    } else if (file.endsWith('.html')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// 处理单个HTML文件
function processHtmlFile(filePath) {
  console.log(`处理HTML文件: ${filePath}`);
  
  // 读取HTML文件
  let htmlContent = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否已经包含加载动画
  if (htmlContent.includes('nuxt-loader-overlay')) {
    console.log(`  - 文件已包含加载动画，跳过: ${filePath}`);
    return;
  }
  
  // 1. 将动画样式添加到<head>标签中
  if (htmlContent.includes('<head>')) {
    htmlContent = htmlContent.replace('<head>', '<head>' + headStyleScript);
  } else if (htmlContent.includes('<html>')) {
    // 如果找不到<head>标签但有<html>，在<html>后添加<head>
    htmlContent = htmlContent.replace('<html>', '<html><head>' + headStyleScript + '</head>');
  } else {
    // 如果连<html>都没有，在文件开头添加
    htmlContent = headStyleScript + htmlContent;
  }
  
  // 2. 将静态加载动画HTML直接添加到<body>标签开始处
  if (htmlContent.includes('<body>')) {
    htmlContent = htmlContent.replace('<body>', '<body>' + staticLoaderHTML);
  }
  
  // 3. 将结束脚本添加到</body>之前
  if (htmlContent.includes('</body>')) {
    htmlContent = htmlContent.replace('</body>', bodyEndScript + '</body>');
  }
  
  // 写回修改后的文件
  fs.writeFileSync(filePath, htmlContent, 'utf8');
  console.log(`  - 成功注入加载动画: ${filePath}`);
}

// 主函数
async function main() {
  try {
    console.log('开始执行构建后处理...');
    
    // 检查输出目录是否存在
    if (!fs.existsSync(outputDir)) {
      console.error(`错误: 找不到构建输出目录: ${outputDir}`);
      process.exit(1);
    }
    
    // 查找所有HTML文件
    console.log('查找所有HTML文件...');
    const htmlFiles = findHtmlFiles(outputDir);
    console.log(`找到 ${htmlFiles.length} 个HTML文件需要处理`);
    
    if (htmlFiles.length === 0) {
      console.log('未找到HTML文件，无需处理');
      return;
    }
    
    // 处理每个HTML文件
    console.log('开始处理HTML文件...');
    htmlFiles.forEach(processHtmlFile);
    
    console.log('构建后处理完成！');
  } catch (error) {
    console.error('构建后处理出错:', error);
    process.exit(1);
  }
}

// 执行主函数
main(); 