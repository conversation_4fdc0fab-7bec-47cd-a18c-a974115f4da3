<script setup lang="ts">
import "@/assets/css/p__newLogin__router.css"
import store from '@/store'
import defaultSettings from '@/settings'
import { getisInt, getSysIsint } from "@/api/user"
import qs from "qs"

definePageMeta({
    title: ''
})

definePageMeta({
    layout: false,
});

interface FormState {
    loginId: string;
    password: string;
}

const formRef = ref();
const loading = ref(false);
const loginError = ref(false);
const isInitData = ref(false);
const isLogin = ref(true);
const installLoading = ref(false);

const formState: FormState = reactive({
    loginId: '',
    password: '',
});

const ruleForm = reactive({
    database: '',
    isData: '',
});

const rules = {
    loginId: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
    ],
};

function handleLogin() {
    formRef.value
        .validate()
        .then(() => {
            loading.value = true;

            // 创建SM4Util对象
            const s4 = new SM4Util();
            // 创建密钥
            s4.secretKey = defaultSettings.secretKey;
            // 创建初始化偏移量
            s4.iv = defaultSettings.iv;
            // CBC模式加密
            const encryptCbc = s4.encryptCbc(formState.password);
            const loginId = formState.loginId;

            const data = {
                code: "1",
                loginId: formState.loginId,
                password: encryptCbc,
            };

            store.dispatch("user/login", { data: data, flag: "erule" })
                .then(() => {
                    setUnid(loginId);

                    // 检查是否有保存的重定向URL
                    const savedRedirect = localStorage.getItem('loginRedirectUrl');
                    if (savedRedirect) {
                        // 清除保存的URL
                        localStorage.removeItem('loginRedirectUrl');
                        console.log('登录成功，重定向到:', savedRedirect);
                        // 使用window.location直接跳转，避免客户端路由导航问题
                        window.location.href = savedRedirect;
                    } else {
                        // 默认导航到仪表盘
                        navigateTo('/dashboard');
                    }
                })
                .catch(() => {
                    // 登录失败时的处理
                })
                .finally(() => {
                    loading.value = false;
                });
        })
        .catch(error => {
            console.log('表单验证错误:', error);
            // 表单验证错误时触发晃动
            triggerShake();
        });
}

// 进一步简化和增强晃动触发函数
function triggerShake() {
    // 使用直接的DOM操作确保动画生效
    const formMain = document.querySelector('.lark-form-main');
    if (formMain) {
        // 先移除可能存在的动画类
        formMain.classList.remove('shake-animation');
        // 强制浏览器重绘
        void formMain.offsetWidth;
        // 添加动画类
        formMain.classList.add('shake-animation');
        // 动画结束后移除类
        setTimeout(() => {
            formMain.classList.remove('shake-animation');
        }, 820);
    } else {
        console.error('找不到 .lark-form-main 元素!');
    }

    // 同时维护响应式状态，以防Vue模板绑定方式仍然有效
    loginError.value = true;
    setTimeout(() => {
        loginError.value = false;
    }, 820);
}

// 添加初始化判断方法
function getInitInfo() {
    getisInt().then((res) => {
        if (res.code === 20000) {
            if (res.data === false) {
                isInitData.value = false;
                isLogin.value = true;
            } else if (res.data === true) {
                isLogin.value = false;
                isInitData.value = true;
            }
        }
    });
}

// 修改安装方法
function instData() {
    installLoading.value = true;
    let data = qs.stringify({
        dbDialect: ruleForm.database,
        isSql: ruleForm.isData,
    });
    getSysIsint(data).then((res) => {
        if (res.code === 20000) {
            ruleForm.database = '';
            ruleForm.isData = '';
            getInitInfo();
        }
    }).finally(() => {
        installLoading.value = false;
    });
}

onMounted(() => {
    getInitInfo();
    const oScript = document.createElement("script");
    oScript.type = "text/javascript";
    oScript.src = `${defaultSettings.BASE_URL}/js/base64js.min.js`;
    document.body.appendChild(oScript);

    const oScript1 = document.createElement("script");
    oScript1.type = "text/javascript";
    oScript1.src = `${defaultSettings.BASE_URL}/js/sm4encrypt.js`;
    document.body.appendChild(oScript1);
})
</script>

<template>
    <div>
        <div>
            <div class="loading" id="lark-loading">
                <div class="loading-bar"></div>
            </div>
            <div class="lark page-account pc-web lark-login">
                <div class="main-wrapper">
                    <div>
                        <div class="platform-title">eRule规则管理平台</div>
                        <div class="layout-container">
                            <!-- 登录表单 -->
                            <div v-if="isLogin" class="lark-form-main" :class="{ 'shake-animation': loginError }">
                                <div class="lark-form-content form-pro">
                                    <div class="account-login form-pro" data-aspm="c38497">
                                        <a-form ref="formRef" :model="formState" :rules="rules">
                                            <a-form-item name="loginId">
                                                <a-input autocomplete="off" v-model:value="formState.loginId"
                                                    style="height: 40px;" placeholder="请输入用户名"
                                                    @keyup.enter.native="handleLogin" />
                                            </a-form-item>
                                            <a-form-item name="password">
                                                <a-input autocomplete="off" v-model:value="formState.password"
                                                    style="height: 40px;" placeholder="请输入密码" type="password"
                                                    @keyup.enter.native="handleLogin" />
                                            </a-form-item>
                                            <a-form-item style="margin-top: 1em;">
                                                <a-button type="primary" size="large" block class="btn-login"
                                                    :loading="loading" @click="handleLogin">登录</a-button>
                                            </a-form-item>
                                        </a-form>
                                    </div>
                                </div>
                            </div>

                            <!-- 初始化安装表单 -->
                            <div v-if="isInitData" class="lark-form-main init-form">
                                <div class="lark-form-content form-pro">
                                    <div class="init-form-content">
                                        <h3 class="init-title">初始化设置</h3>
                                        <a-form>
                                            <a-form-item label="数据库">
                                                <a-select
                                                    v-model:value="ruleForm.database"
                                                    placeholder="请选择"
                                                    style="width: 100%"
                                                >
                                                    <a-select-option value="org.hibernate.dialect.MySQL5InnoDBDialect">MySQL</a-select-option>
                                                    <a-select-option value="org.hibernate.dialect.Oracle10gDialect">Oracle</a-select-option>
                                                    <a-select-option value="org.hibernate.dialect.Oracle12cDialect">Oracle12</a-select-option>
                                                    <a-select-option value="org.hibernate.dialect.PostgreSQLDialect">PostgreSQL</a-select-option>
                                                    <a-select-option value="org.hibernate.dialect.DB2Dialect">DB2</a-select-option>
                                                </a-select>
                                            </a-form-item>
                                            <a-form-item label="执行初始化数据">
                                                <a-select
                                                    v-model:value="ruleForm.isData"
                                                    placeholder="请选择"
                                                    style="width: 100%"
                                                >
                                                    <a-select-option value="1">是</a-select-option>
                                                    <a-select-option value="0">否</a-select-option>
                                                </a-select>
                                            </a-form-item>
                                            <a-form-item>
                                                <a-button type="primary" block :loading="installLoading" @click="instData">安装</a-button>
                                            </a-form-item>
                                        </a-form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer-beian">
                    <a href="https://beian.miit.gov.cn/" target="_blank">京ICP备********号-2</a>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.account-login {
    .ant-form-item {
        margin-bottom: 0;
        height: 65px;
    }
}

.lark-login {
    height: 100vh;
    background-image: url('/img/login-background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden;
    /* 防止滚动条出现 */

    .main-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 10px 0 30px 0;
        /* 减少顶部padding，增加底部padding */
    }

    .platform-title {
        color: rgb(0, 106, 212);
        font-size: 48px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 50px;
        letter-spacing: 2px;
        text-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
        width: 500px;  // 设置标题宽度
    }

    .lark-form-main {
        background-color: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 30px 50px;  // 增加左右内边距
        width: 450px;        // 增加整体宽度与标题对齐
        transition: all 0.3s ease;

        &:hover {
            backdrop-filter: blur(15px);
            background-color: rgba(255, 255, 255, 0.2);
        }
    }

    .lark-form-content {
        width: 100%;
    }

    .footer-beian {
        position: absolute;
        bottom: 20px;
        width: 100%;
        text-align: center;
        color: rgba(0, 0, 0, 0.65);
        font-size: 12px;

        a {
            color: rgba(0, 0, 0, 0.65);
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
                color: #006AD4;
            }
        }
    }
}

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-10px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(10px);
    }
}

.shake-animation {
    animation: shake 0.8s cubic-bezier(.36, .07, .19, .97) both !important;
}

.init-form {
    .init-title {
        text-align: center;
        color: #006AD4;
        font-size: 24px;
        margin-bottom: 30px;
    }

    .init-form-content {
        width: 100%;
    }

    .ant-form-item-label {
        color: #333;
    }

    .ant-select {
        width: 100%;
    }
}
</style>

