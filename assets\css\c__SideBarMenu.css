.placeholder-avatar {
    display: inline-block;
    border-radius: 50%;
    color: var(--yq-white);
    text-align: center
}

.index-module_offlineButton_XZkWh {
    cursor: not-allowed
}

.index-module_disabledStyle_MeBOz {
    opacity: .4;
    cursor: not-allowed;
    pointer-events: none
}

:root {
    --adm-radius-s: 4px;
    --adm-radius-m: 8px;
    --adm-radius-l: 12px;
    --adm-font-size-1: 9px;
    --adm-font-size-2: 10px;
    --adm-font-size-3: 11px;
    --adm-font-size-4: 12px;
    --adm-font-size-5: 13px;
    --adm-font-size-6: 14px;
    --adm-font-size-7: 15px;
    --adm-font-size-8: 16px;
    --adm-font-size-9: 17px;
    --adm-font-size-10: 18px;
    --adm-color-primary: #1677ff;
    --adm-color-success: #00b578;
    --adm-color-warning: #ff8f1f;
    --adm-color-danger: #ff3141;
    --adm-color-yellow: #ff9f18;
    --adm-color-orange: #ff6430;
    --adm-color-wathet: #e7f1ff;
    --adm-color-text: #333;
    --adm-color-text-secondary: #666;
    --adm-color-weak: #999;
    --adm-color-light: #ccc;
    --adm-color-border: #eee;
    --adm-color-background: #fff;
    --adm-color-highlight: var(--adm-color-danger);
    --adm-color-white: #fff;
    --adm-color-box: #f5f5f5;
    --adm-color-text-light-solid: var(--adm-color-white);
    --adm-color-text-dark-solid: #000;
    --adm-color-fill-content: var(--adm-color-box);
    --adm-font-size-main: var(--adm-font-size-5);
    --adm-font-family: -apple-system,blinkmacsystemfont,"Helvetica Neue",helvetica,segoe ui,arial,roboto,"PingFang SC","miui","Hiragino Sans GB","Microsoft Yahei",sans-serif;
    --adm-border-color: var(--adm-color-border)
}

html[data-prefers-color-scheme=dark] {
    --adm-color-primary: #3086ff;
    --adm-color-success: #34b368;
    --adm-color-warning: #ffa930;
    --adm-color-danger: #ff4a58;
    --adm-color-yellow: #ffa930;
    --adm-color-orange: #e65a2b;
    --adm-color-wathet: #0d2543;
    --adm-color-text: #e6e6e6;
    --adm-color-text-secondary: #b3b3b3;
    --adm-color-weak: grey;
    --adm-color-light: #4d4d4d;
    --adm-color-border: #2b2b2b;
    --adm-color-box: #0a0a0a;
    --adm-color-background: #1a1a1a;
    --adm-color-background-body: var(--adm-color-background);
    --adm-border-color: var(--adm-color-border)
}

:root {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html {
    background-color: var(--adm-color-background-body)
}

body {
    color: #333;
    color: var(--adm-color-text);
    font-size: 13px;
    font-size: var(--adm-font-size-main);
    font-family: -apple-system,blinkmacsystemfont,Helvetica Neue,helvetica,segoe ui,arial,roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif;
    font-family: var(--adm-font-family)
}

a,button {
    cursor: pointer
}

a {
    color: #1677ff;
    color: var(--adm-color-primary);
    transition: opacity .2s ease-in-out
}

a:active {
    opacity: .8
}

.adm-plain-anchor {
    color: inherit;
    transition: none
}

.adm-plain-anchor:active {
    opacity: 1
}

body.adm-overflow-hidden {
    overflow: hidden!important
}

div.adm-px-tester {
    --size: 1;
    height: 1px;
    height: calc(var(--size)/2*2px);
    width: 0;
    position: fixed;
    left: -100vw;
    top: -100vh;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none
}

.adm-dialog {
    --z-index: var(--adm-dialog-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-dialog .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-dialog-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-dialog-body>* {
    flex: none
}

.adm-dialog-body>.adm-dialog-content {
    flex: auto
}

.adm-dialog-body:not(.adm-dialog-with-image) {
    padding-top: 20px
}

.adm-dialog-image-container {
    margin-bottom: 12px;
    max-height: 40vh
}

.adm-dialog-header,.adm-dialog-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-dialog-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-dialog-content {
    padding: 0 12px 20px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-dialog-content-empty {
    padding: 0;
    height: 12px
}

.adm-dialog-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-dialog-footer .adm-dialog-action-row {
    display: flex;
    align-items: stretch;
    border-top: .5px solid var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>* {
    flex: 1
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button {
    padding: 10px;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    border-radius: 0;
    border-right: solid .5px var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button-bold {
    font-weight: 700
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button:last-child {
    border-right: none
}

.adm-dialog-image-container {
    overflow-y: auto
}

.adm-image {
    --width: var(--adm-image-width,auto);
    --height: var(--adm-image-height,auto);
    width: auto;
    width: var(--width);
    height: auto;
    height: var(--height);
    display: block;
    overflow: hidden
}

.adm-image-img {
    width: 100%;
    height: 100%
}

.adm-image-tip {
    position: relative;
    background-color: var(--adm-color-fill-content);
    height: 100%;
    min-height: 24px;
    min-width: 24px
}

.adm-image-tip>svg {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    color: var(--adm-color-weak)
}

.adm-auto-center {
    display: flex;
    justify-content: center
}

.adm-auto-center-content {
    flex: 0 1 auto
}

.adm-button {
    --color: var(--adm-color-text-light-solid);
    --text-color: var(--adm-button-text-color,var(--adm-color-text));
    --background-color: var(--adm-button-background-color,var(--adm-color-background));
    --border-radius: var(--adm-button-border-radius,4px);
    --border-width: var(--adm-button-border-width,1px);
    --border-style: var(--adm-button-border-style,solid);
    --border-color: var(--adm-button-border-color,var(--adm-color-border));
    color: var(--text-color);
    background-color: var(--background-color);
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: auto;
    padding: 7px 12px;
    margin: 0;
    font-size: var(--adm-font-size-9);
    line-height: 1.4;
    text-align: center;
    border: 1px solid var(--border-color);
    border: var(--border-width) var(--border-style) var(--border-color);
    border-radius: 4px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: opacity .15s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-button:focus {
    outline: none
}

.adm-button:before {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(calc(var(--border-width)*-1),calc(var(--border-width)*-1));
    width: 100%;
    height: 100%;
    background-color: var(--adm-color-text-dark-solid);
    border: var(--border-width) var(--border-style) var(--adm-color-text-dark-solid);
    border-radius: var(--border-radius);
    opacity: 0;
    content: " ";
    box-sizing: content-box
}

.adm-button:active:before {
    opacity: .08
}

.adm-button-default.adm-button-fill-outline {
    --background-color: transparent;
    --border-color: var(--adm-color-text)
}

.adm-button-default.adm-button-fill-none {
    --background-color: transparent;
    --border-width: 0px
}

.adm-button:not(.adm-button-default) {
    --text-color: var(--adm-color-text-light-solid);
    --background-color: var(--color);
    --border-color: var(--color)
}

.adm-button:not(.adm-button-default).adm-button-fill-outline {
    --text-color: var(--color);
    --background-color: transparent
}

.adm-button:not(.adm-button-default).adm-button-fill-none {
    --text-color: var(--color);
    --background-color: transparent;
    --border-width: 0px
}

.adm-button-primary {
    --color: var(--adm-color-primary)
}

.adm-button-success {
    --color: var(--adm-color-success)
}

.adm-button-danger {
    --color: var(--adm-color-danger)
}

.adm-button-warning {
    --color: var(--adm-color-warning)
}

.adm-button-block {
    display: block;
    width: 100%
}

.adm-button-disabled {
    cursor: not-allowed;
    opacity: .4
}

.adm-button-disabled:active:before {
    display: none
}

.adm-button.adm-button-mini {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-main)
}

.adm-button.adm-button-mini.adm-button-shape-rounded {
    padding-left: 9px;
    padding-right: 9px
}

.adm-button.adm-button-small {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-7)
}

.adm-button.adm-button-large {
    padding-top: 11px;
    padding-bottom: 11px;
    font-size: var(--adm-font-size-10)
}

.adm-button.adm-button-shape-rounded {
    --border-radius: 1000px
}

.adm-button.adm-button-shape-rectangular {
    --border-radius: 0
}

.adm-button-loading {
    vertical-align: bottom
}

.adm-button-loading-wrapper {
    display: flex;
    height: 1.4em;
    align-items: center;
    justify-content: center
}

.adm-button-loading-wrapper>.adm-loading {
    opacity: .6
}

.adm-dot-loading {
    display: inline-block
}

.adm-center-popup {
    --background-color: var(--adm-center-popup-background-color,var(--adm-color-background));
    --border-radius: var(--adm-center-popup-border-radius,8px);
    --max-width: var(--adm-center-popup-max-width,75vw);
    --min-width: var(--adm-center-popup-min-width,280px);
    --z-index: var(--adm-center-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-center-popup .adm-center-popup-mask {
    z-index: 0
}

.adm-center-popup-wrap {
    position: fixed;
    z-index: 1;
    top: 50%;
    left: 50%;
    width: auto;
    min-width: var(--min-width);
    max-width: var(--max-width);
    transform: translate(-50%,-50%)
}

.adm-center-popup-body {
    background-color: var(--background-color);
    border-radius: var(--border-radius)
}

.adm-center-popup-close {
    position: absolute;
    z-index: 100;
    right: 8px;
    top: 8px;
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    color: var(--adm-color-weak)
}

.adm-mask {
    --z-index: var(--adm-mask-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index);
    display: block
}

.adm-mask,.adm-mask-aria-button {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.adm-mask-aria-button {
    position: absolute;
    z-index: 0;
    pointer-events: none
}

.adm-mask-content {
    z-index: 1
}

.adm-modal {
    --z-index: var(--adm-modal-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-modal .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-modal-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-modal-body>* {
    flex: none
}

.adm-modal-body>.adm-modal-content {
    flex: auto
}

.adm-modal-body:not(.adm-modal-with-image) {
    padding-top: 20px
}

.adm-modal-image-container {
    margin-bottom: 12px;
    max-height: 40vh;
    overflow-y: scroll
}

.adm-modal-header,.adm-modal-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-modal-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-modal-content {
    padding: 0 12px 12px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-modal-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    padding: 8px 12px 12px
}

.adm-modal-footer-empty {
    padding: 0;
    height: 8px
}

.adm-modal-footer.adm-space {
    --gap-vertical: 20px
}

.adm-modal-footer .adm-modal-button {
    font-size: var(--adm-font-size-10);
    line-height: 25px
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary) {
    padding-top: 0;
    padding-bottom: 0
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):before {
    display: none
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):active {
    opacity: .7
}

.adm-space-item {
    flex: none
}

.adm-space {
    display: inline-flex;
    --gap: 8px;
    --gap-vertical: var(--gap);
    --gap-horizontal: var(--gap)
}

.adm-space-vertical {
    flex-direction: column
}

.adm-space-vertical>.adm-space-item {
    margin-bottom: var(--gap-vertical)
}

.adm-space-vertical>.adm-space-item:last-child {
    margin-bottom: 0
}

.adm-space-horizontal {
    flex-direction: row
}

.adm-space-horizontal>.adm-space-item {
    margin-right: var(--gap-horizontal)
}

.adm-space-horizontal>.adm-space-item:last-child {
    margin-right: 0
}

.adm-space-horizontal.adm-space-wrap {
    flex-wrap: wrap;
    margin-bottom: calc(var(--gap-vertical)*-1)
}

.adm-space-horizontal.adm-space-wrap>.adm-space-item {
    padding-bottom: var(--gap-vertical)
}

.adm-space.adm-space-block {
    display: flex
}

.adm-space-align-center {
    align-items: center
}

.adm-space-align-start {
    align-items: flex-start
}

.adm-space-align-end {
    align-items: flex-end
}

.adm-space-align-baseline {
    align-items: baseline
}

.adm-space-justify-center {
    justify-content: center
}

.adm-space-justify-start {
    justify-content: flex-start
}

.adm-space-justify-end {
    justify-content: flex-end
}

.adm-space-justify-between {
    justify-content: space-between
}

.adm-space-justify-around {
    justify-content: space-around
}

.adm-space-justify-evenly {
    justify-content: space-evenly
}

.adm-space-justify-stretch {
    justify-content: stretch
}

.adm-notice-bar {
    --background-color: var(--adm-color-weak);
    --border-color: var(--adm-color-weak);
    --text-color: var(--adm-color-text-light-solid);
    --font-size: var(--adm-font-size-7);
    --icon-font-size: var(--adm-font-size-10);
    --height: 40px;
    --adm-notice-bar-border-radius: 4px;
    --adm-notice-bar-border-width: 1px;
    height: 40px;
    height: var(--height);
    box-sizing: border-box;
    font-size: var(--font-size);
    padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: solid 1px var(--border-color);
    border: solid var(--adm-notice-bar-border-width) var(--border-color);
    border-left-width: 0;
    border-right-width: 0;
    background-color: var(--background-color)
}

.adm-notice-bar,.adm-notice-bar>span[role=img] {
    color: var(--text-color)
}

.adm-notice-bar.adm-notice-bar-alert {
    --background-color: #fff9ed;
    --border-color: #fff3e9;
    --text-color: var(--adm-color-orange)
}

.adm-notice-bar.adm-notice-bar-error {
    --background-color: var(--adm-color-danger);
    --border-color: #d9281e;
    --text-color: #fff
}

.adm-notice-bar.adm-notice-bar-info {
    --background-color: #d0e4ff;
    --border-color: #bcd8ff;
    --text-color: var(--adm-color-primary)
}

.adm-notice-bar.adm-notice-bar-success {
    --background-color: #d1fff0;
    --border-color: #a8f0d8;
    --text-color: var(--adm-color-success)
}

.adm-notice-bar .adm-notice-bar-left {
    flex-shrink: 0;
    margin-right: 8px;
    font-size: var(--icon-font-size)
}

.adm-notice-bar .adm-notice-bar-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    width: auto;
    transition-timing-function: linear;
    white-space: nowrap
}

.adm-notice-bar-wrap.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    white-space: normal
}

.adm-notice-bar .adm-notice-bar-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: 12px
}

.adm-notice-bar-close {
    width: 24px;
    height: 24px;
    margin-right: -3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--adm-font-size-10)
}

.adm-notice-bar-wrap {
    height: auto;
    align-items: flex-start;
    padding-top: 8px;
    padding-bottom: 8px;
    line-height: 22px
}

.adm-notice-bar-neutral {
    border-radius: var(--adm-notice-bar-border-radius)
}

.adm-notice-bar-rounded {
    border-radius: 1000px
}

.adm-notice-bar-bordered {
    border-left-width: var(--adm-notice-bar-border-width);
    border-right-width: var(--adm-notice-bar-border-width)
}

.adm-notice-bar-without-border {
    border-top-width: 0;
    border-bottom-width: 0
}

.adm-popup {
    --z-index: var(--adm-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-popup-body {
    position: fixed;
    background-color: var(--adm-color-background);
    z-index: calc(var(--z-index) + 10)
}

.adm-popup-body .adm-popup-close-icon {
    position: absolute;
    z-index: 100
}

.adm-popup-body-position-bottom {
    width: 100%;
    bottom: 0;
    left: 0
}

.adm-popup-body-position-bottom .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-top {
    width: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-top .adm-popup-close-icon {
    right: 8px;
    bottom: 8px
}

.adm-popup-body-position-left {
    height: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-left .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-right {
    height: 100%;
    top: 0;
    right: 0
}

.adm-popup-body-position-right .adm-popup-close-icon {
    left: 8px;
    top: 8px
}

.adm-popup-close-icon {
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    line-height: 1;
    color: var(--adm-color-weak)
}

.adm-toast-mask .adm-toast-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center
}

.adm-toast-mask .adm-toast-main {
    display: inline-block;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    max-width: 204px;
    max-height: 70%;
    overflow: auto;
    color: #fff;
    word-break: break-all;
    background-color: rgba(0,0,0,.7);
    border-radius: 8px;
    pointer-events: all;
    font-size: var(--adm-font-size-7);
    line-height: 1.5;
    box-sizing: border-box;
    text-align: left;
    text-align: initial
}

.adm-toast-mask .adm-toast-main-text {
    padding: 12px;
    min-width: 0
}

.adm-toast-mask .adm-toast-main-icon {
    padding: 35px 12px;
    min-width: 150px
}

.adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
    text-align: center;
    margin-bottom: 8px;
    font-size: 36px;
    line-height: 1
}

.adm-toast-loading {
    --size: 48px;
    margin: 0 auto 8px
}

.adm-spin-loading {
    --color: var(--adm-color-weak);
    --size: 32px;
    width: 32px;
    width: var(--size);
    height: 32px;
    height: var(--size)
}

.adm-spin-loading-svg {
    width: 100%;
    height: 100%;
    animation: adm-spin-loading-rotate .8s linear infinite
}

.adm-spin-loading-svg>.adm-spin-loading-fill {
    stroke: var(--color)
}

@keyframes adm-spin-loading-rotate {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.index-module_larkConfirm_Hedya {
    text-align: center;
    padding-top: 24px;
    background-color: var(--yq-bg-primary)
}

.index-module_larkConfirm_Hedya.index-module_dark_Bco8K {
    background-color: var(--yq-bg-secondary)
}

.index-module_larkConfirm_Hedya .index-module_title_XgoMG {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    line-height: 24px;
    font-weight: 500
}

.index-module_larkConfirm_Hedya .index-module_content_3IPt2 {
    margin-top: 8px;
    padding: 0 24px;
    font-size: 14px;
    color: var(--yq-yuque-grey-8);
    text-align: left;
    line-height: 22px;
    font-weight: 400
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF {
    margin-top: 24px;
    display: flex
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_cancelBtn_InN4Q {
    flex: 1;
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    cursor: pointer;
    font-weight: 400;
    border-top: 1px solid var(--yq-border-light);
    border-right: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_confirmBtn_O-BLZ {
    flex: 1;
    font-size: 16px;
    color: var(--yq-blue-6);
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-top: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_mobileModalContainer_XqHUd.index-module_dark_Bco8K {
    background: var(--yq-bg-secondary)
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-content] {
    padding: 0
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-footer-empty] {
    height: 0
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_qoTbx {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_qoTbx h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_qoTbx p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2tELh {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_384SJ {
    float: right
}

.DocNoteLimitModal-module_actionBtn_CUQkm {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_H0Qas {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_mnoFp {
    float: right
}

.DocNoteLimitModal-module_mobile_Kb9cX {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_limitFigure_\+Vg0M {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_rightTopPos_-olEi {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA {
    top: 38px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC {
    left: 44px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midTopPos_puaum {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_desc_YO-N4 {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_contentRight_enYYT {
    text-align: right;
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt {
    padding-bottom: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_dashline_hmVnG {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingInfo_C4MGv {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingMainTitle_po2Ep {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingSubTitle_rN0U5 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 .paymodal-module_select_YB\+z4 {
    width: 140px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingDesc_vx9fj {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_priceTag_ByB6F {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWith_dtDdE {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_totalInfo_\+pItg {
    margin: 4px 0 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayIcon_df156 {
    width: 24px;
    margin-top: -2px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayText_zlnDZ {
    padding-left: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 {
    min-height: 444px;
    text-align: center
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_qrcodeImage_PfP82 {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc1_v1ln1 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc2_D8ZQ\+ {
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_link_sWcgh {
    margin-top: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV {
    position: relative;
    min-height: 260px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .ant-spin-spinning {
    margin-top: 130px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc img {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_qrcodeCon_b5rk3 {
    width: 260px;
    height: 260px;
    margin: 0 auto;
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    overflow: hidden
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleDesc_cUN4K {
    font-size: 16px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleValue_4k4b0 {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.paymodal-module_payModalContainer_-7P4F .ant-modal-wrap {
    z-index: 9999
}

@media only screen and (max-width: 768px) {
    .alipay-payment-icon {
        width:36px!important;
        height: 36px!important;
        margin-left: -18px!important;
        margin-top: -18px!important
    }
}

.index-module_mainInput_x4D9t.ant-input-affix-wrapper {
    max-width: 300px;
    width: 172px
}

.index-module_verifyResult_pdxsH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5);
    font-weight: 400
}

.index-module_verifyResult_pdxsH.index-module_success_NhSta {
    color: var(--yq-pea-green-6)
}

.index-module_inputVerify_kl-bC {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-weight: 500
}

.index-module_editIcon_eXpln {
    margin-left: 8px
}

.index-module_promoCodeDefault_6iUh6 {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: var(--yq-text-body);
    font-weight: 400;
    text-align: right;
    cursor: pointer
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*EX5TTI_UeRIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: auto 154px;
    background-position: 0 100%
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_docCountFigure_32PxF {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*w9wrT603NQIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom
}

html[data-kumuhana=pouli] .membermodal-module_rightArea_AY\+Cs {
    background-color: var(--yq-bg-tertiary)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH {
    padding: 0 24px;
    padding-bottom: 10px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr {
    margin-bottom: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr .membermodal-module_link_GhyiF {
    margin-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH h3 {
    font-size: 14px;
    font-weight: 700
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 100px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_tips_hkMrX {
    margin: -12px 0 20px 0;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_titleTips_oWQ8t {
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWith_tqEUb {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 24px;
    margin-top: -2px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_dashline_jMarO {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px;
    line-height: 45px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj.membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r {
    margin-left: 8px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: baseline;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r .membermodal-module_discountPriceValue_a83du {
    font-size: 30px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC {
    margin-left: 20px;
    display: inline-block
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC>label {
    padding-top: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalTermsButton_jCPs1 {
    margin-left: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 16px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r {
    width: 140px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r:nth-child(3n) {
    margin-right: 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r.membermodal-module_selectedUploadCard_VMSYV {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(0,185,107,.05)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadSize_t-9U2 {
    font-size: 16px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackagePrice_YwF2\+ {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t,.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 28px 32px;
    border-radius: 8px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*tZQsTYAY-w8AAAAAAAAAAAAAARQnAQ) no-repeat;
    background-color: var(--yq-yuque-grey-2);
    background-size: auto 154px;
    background-position: 0 100%;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO {
    flex: 1;
    padding-right: 20px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO h2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO h2 {
    font-size: 20px;
    font-weight: 500;
    margin-top: 24px;
    margin-bottom: 6px;
    line-height: 1.75
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe {
    font-size: 14px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span {
    margin-right: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF {
    position: relative;
    width: 268px;
    height: 275px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*2kmNT5ZDTMkAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom;
    margin-left: 10px;
    margin-top: 80px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    position: absolute;
    left: 164px;
    font-size: 14px;
    font-weight: 500;
    transform: translateX(-50%);
    white-space: nowrap
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    top: -40px;
    background: var(--yq-black);
    border-radius: 8px;
    color: var(--yq-white);
    padding: 5px 8px;
    box-shadow: 0 8px 22px -6px rgba(38,38,38,0)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF {
    bottom: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP {
    font-size: 14px;
    line-height: 28px;
    margin-top: 12px;
    min-height: 150px;
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 {
    margin-top: 32px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7 {
    margin-bottom: 40px;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 {
    margin-right: 12px;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-white);
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF {
    flex: 1
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    font-weight: 400;
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs {
    width: 420px;
    background-color: var(--yq-white);
    padding: 24px;
    border-radius: 8px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3 {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0 {
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 80px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector {
    background: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 460px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F {
    margin-bottom: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI {
    margin-bottom: 12px;
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC {
    border: 1px solid transparent;
    background-color: var(--yq-yuque-grey-2);
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS {
    opacity: .5
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn {
    margin-top: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 {
    padding: 10px 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7 {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j {
    padding: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7 {
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh {
    display: block
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc {
    position: absolute;
    right: -1px;
    top: -13px;
    background-image: linear-gradient(to top right,#ff8487,#ff5b5d,#ff4d4f);
    color: #fff;
    border-radius: 8px 8px 8px 1px;
    height: 24px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS {
    display: flex;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4 {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA {
    margin-right: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS {
    font-size: 10px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp {
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-black);
    margin-right: 2px;
    margin-left: 7px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    text-align: right;
    line-height: 20px;
    font-weight: 500;
    margin-right: 2px;
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through;
    margin-left: 5px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu {
    text-align: left
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    margin-top: 6px;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE {
    font-size: 10px;
    display: flex;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh {
    display: none;
    width: 340px;
    margin-top: 13px;
    padding-top: 13px;
    border-top: 1px solid var(--yq-border-light);
    line-height: 25px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH {
    width: 185px;
    font-size: 12px;
    color: var(--yq-yuque-grey-8);
    display: inline-block;
    vertical-align: top
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd),.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd) {
    width: 145px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX {
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb {
    margin: 20px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go {
    font-weight: 500;
    color: var(--yq-yuque-grey-9)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 18px;
    margin-top: -2px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ {
    border-top: 1px solid var(--yq-border-light);
    margin: 10px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj {
    font-size: 20px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY {
    display: inline-block;
    margin-top: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF {
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span {
    padding-right: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw {
    height: 40px;
    font-weight: 500
}

.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 0
}

.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 400px
}

.member-modal .ant-modal-body,.membermodal-module_memberBuyModalH5Container_9ZOm4 {
    padding: 0
}

.member-modal .ant-modal-close-x {
    width: 40px;
    height: 40px;
    line-height: 40px
}

.member-modal .ant-modal-header {
    border-bottom: 0
}

.membermodal-module_selectTip_4qIX9 .membermodal-module_text_orjhF {
    margin-right: 8px
}

.index-module_placeholder_3rydn {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_placeholder_3rydn .index-module_loaded_kAsRx {
    opacity: 1
}

.index-module_placeholder_3rydn img {
    opacity: 0;
    transition: opacity .2s ease-in
}

.index-module_placeholderLoaded_0zPqS {
    background-color: transparent
}

.successmodal-module_successModalContainer_ENOHy {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoCon_EYL4y {
    padding: 0 24px 30px 24px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfo_O8N6K {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoTitle_yXIec {
    font-size: 18px;
    margin-top: 24px;
    font-weight: 500
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu {
    margin-top: 40px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu .successmodal-module_checkUploadButton_vFqCf {
    margin: 1px 0 0 12px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalApplyReceiptButton_Yj2F2 {
    margin-left: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalSubTitle_hVcSK {
    font-size: 14px;
    margin: 16px 0 16px;
    font-weight: 500;
    color: var(--yq-black)
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalItem_zxqQ2 {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 12px 0
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalBlank_tgeED {
    border-top: 1px solid var(--yq-border-light);
    margin-top: 16px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_rightChecked_8E2DG {
    margin-top: -4px;
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.processmodal-module_processModalContainer_jrXul {
    text-align: center;
    padding: 10px 0
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalImg_kmc6C {
    width: 120px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalInfo_fOy8- {
    margin-top: 12px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalOrderId_tDK\+c {
    color: var(--yq-white);
    text-align: center
}

.receipt-module_contactInfoTitle_\+WTvl {
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 10px 0
}

.receipt-module_receiptButtons_tBTuB {
    margin-top: 8px
}

.receipt-module_cancelButton_1xXKN {
    margin-left: 10px
}

.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_contactTips_rQ4lg,.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_receiptTypeTitle_u72yN {
    margin-bottom: 10px
}

.receiptmodal-module_receiptDesc_87YQ3 {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

.receiptmodal-module_receiptDesc_87YQ3 a {
    margin-left: 4px
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_SXXvX {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_SXXvX h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_SXXvX p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_7z-6U {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2ctWH {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_IlCRq {
    float: right
}

.DocNoteLimitModal-module_actionBtn_j4wTK {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_3oyZh {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_5PEmm {
    float: right
}

.DocNoteLimitModal-module_mobile_MQEQW {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_limitFigure_7z-6U {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_rightTopPos_CQ7st {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He {
    top: 38px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz {
    left: 44px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_desc_DQeXN {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.index-module_modal_ciiIL .ant-modal-content {
    overflow: hidden
}

.index-module_modal_ciiIL .ant-modal-body {
    padding: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-btns {
    display: none
}

.index-module_modalDesktop_CvNi7 {
    max-width: 100vw!important
}

.index-module_modalDesktop_CvNi7 .ant-modal-content {
    overflow: hidden
}

.index-module_modalDesktop_CvNi7 .ant-modal-body {
    padding: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-btns {
    display: none
}

.head-user-info-popover .member-badge-container {
    padding: 0
}

.member-badge-desc {
    margin-top: -5px;
    margin-bottom: 12px
}

.member-badge-upgrade-action {
    color: var(--yq-text-link)!important
}

.badge-module_memberBadgeContainer_truSH {
    padding: 16px 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 1
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_safeIcon_LvS1y {
    position: relative;
    top: -1px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_unsafeIcon_fj7tW {
    margin-left: 2px;
    position: relative;
    top: -10px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeNameCon_UOZEP {
    line-height: 24px;
    display: inline-block;
    max-width: 450px;
    margin-right: 4px;
    word-break: break-all;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO {
    position: relative;
    top: 0;
    letter-spacing: -1em;
    vertical-align: top;
    height: 100%;
    display: inline-block;
    margin-top: -1px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO:before {
    content: "\3000"
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO a {
    display: inline
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeDesc_nc5JL {
    line-height: 1;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeEnDesc_xoJbF {
    line-height: 1.5;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeDisabled_YFT6S {
    color: var(--yq-text-disable);
    cursor: not-allowed;
    margin-left: 8px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block;
    font-size: 12px;
    color: var(--yq-text-link)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr:hover {
    color: var(--yq-ant-link-hover-color)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.badge-module_memberBadgeTooltip_JvMu7 {
    max-width: 400px
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.index-module_sideBarBadge_eeNVn {
    font-weight: 400;
    font-size: 12px;
    line-height: 14px;
    max-width: 220px;
    color: var(--yq-yuque-grey-7)
}

.index-module_sideBarBadge_eeNVn .member-badge-container {
    padding: 0
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name {
    font-size: 14px;
    line-height: 22px
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name .un-safe-icon {
    margin-left: 4px;
    width: 21px!important;
    height: 21px!important;
    min-width: 21px!important;
    top: -1px;
    color: var(--yq-yuque-grey-7)
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name .safe-icon {
    width: 17px!important;
    height: 17px!important;
    min-width: 17px;
    top: 2px
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name .member-badge-sec svg,.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name .member-badge-tooltip svg {
    height: 20px!important
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name .member-badge-icon img {
    margin-top: 0
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-name a:before {
    display: none
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-desc {
    margin: -3px 0 0 0;
    line-height: 20px;
    white-space: normal
}

.index-module_sideBarBadge_eeNVn .member-badge-container .member-badge-desc a:before {
    display: none
}

.index-module_userMenu_y1kcz {
    position: relative;
    padding-top: 6px;
    border-radius: 8px
}

.index-module_userMenu_y1kcz .index-module_usermenuDivider_A8fnx {
    margin: 6px 20px!important;
    background-color: var(--yq-border-light)
}

.index-module_userMenu_y1kcz .index-module_promotionalImage_tlukd {
    margin-left: -52px;
    margin-top: 8px;
    width: 271px;
    padding: 0 12px;
    text-align: center;
    background-color: #fdf9e4;
    height: 32px;
    border: 1px solid #f6e5ac;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_userMenu_y1kcz .index-module_promotionalImage_tlukd .index-module_promotionalImageDesc_EhfDV {
    font-size: 12px;
    color: #6a5619;
    font-weight: 500
}

.index-module_userMenu_y1kcz .index-module_promotionalImage_tlukd .index-module_promotionalImageBtn_cY4Uv {
    color: #1672f3;
    font-size: 12px
}

.index-module_userMenu_y1kcz .index-module_promotionalImage_tlukd:hover {
    cursor: pointer
}

.index-module_userMenu_y1kcz .index-module_userInfoMenuItem_ojmYC {
    min-height: auto;
    padding: 6px 16px 8px;
    height: auto!important;
    padding-right: 0;
    cursor: text
}

.index-module_userMenu_y1kcz .index-module_userInfoMenuItem_ojmYC:hover {
    background-color: transparent!important
}

.index-module_userMenu_y1kcz .org-logo span {
    font-size: 14px;
    font-weight: 600;
    position: relative;
    top: -9px
}

.index-module_userMenu_y1kcz .ant-menu-submenu-title:active {
    background-color: var(--yq-white)
}

.index-module_userMenu_y1kcz .ant-menu-item {
    height: 40px;
    line-height: 40px;
    margin: 4px 6px;
    border-radius: 6px
}

.index-module_userMenu_y1kcz .ant-menu-item a .larkui-icon {
    position: relative;
    font-size: 16px;
    color: var(--yq-yuque-grey-7);
    top: 1px
}

.index-module_userMenu_y1kcz li.login-out-menu-item.ant-menu-item {
    height: 40px;
    line-height: 40px;
    margin: 0 8px 10px 8px;
    display: flex;
    justify-content: space-between;
    cursor: text;
    padding: 0
}

.index-module_userMenu_y1kcz li.login-out-menu-item.ant-menu-item:hover {
    background-color: transparent
}

.index-module_userMenu_y1kcz .ant-menu-submenu-arrow {
    color: var(--yq-yuque-grey-7)
}

.index-module_userMenu_y1kcz .ant-menu-submenu {
    margin: 0 8px;
    border-radius: 6px;
    height: 40px;
    line-height: 40px
}

.index-module_userMenu_y1kcz .ant-menu-submenu:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_userMenu_y1kcz .ant-menu-submenu svg {
    color: var(--yq-yuque-grey-7)
}

.index-module_userMenu_y1kcz .index-module_icon_TGSiA {
    position: relative;
    top: 1px
}

.index-module_loginOutWrapper_XzxDB {
    display: flex;
    align-items: center
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn {
    height: 32px;
    width: 32px;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px;
    margin-left: 8px;
    cursor: pointer
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn .index-module_translateZHIcon_2iVXj {
    margin-bottom: 0;
    font-weight: 700
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn .index-module_linkWrapper_jFhR2 {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    line-height: 100%;
    color: var(--yq-yuque-grey-7)
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn .index-module_linkWrapper_jFhR2 svg {
    font-size: 20px
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn:hover .index-module_translateZHIcon_2iVXj,.index-module_loginOutWrapper_XzxDB .index-module_itemAction_ZklAn:hover svg {
    color: var(--yq-black)
}

.index-module_isLark_yknUm .ant-menu-sub,.index-module_isLark_yknUm .ant-menu-submenu-arrow {
    display: none
}

.index-module_userInfoAvatar_TAIYp {
    display: flex
}

.index-module_userInfoName_qxlZZ {
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    line-height: 16px;
    line-height: 34px;
    margin-top: 4px
}

.index-module_userInfoName_qxlZZ .index-module_uname_Y8oq\+ {
    color: var(--yq-yuque-grey-9);
    font-weight: 600
}

.index-module_linkWrapper_jFhR2 {
    overflow: hidden
}

.index-module_userInfoWrapper_J-tX5 {
    height: 100%;
    margin-left: 10px;
    overflow: hidden
}

.index-module_userInfoWrapper_J-tX5 .index-module_userName_Jp6-6 {
    flex: 0 1 auto;
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
    color: var(--yq-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_userInfoWrapper_J-tX5 .index-module_orgName_0mWeQ {
    font-size: 12px;
    line-height: 17px;
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_larkUserInfoDesc_zXBlq {
    display: inline-block;
    margin-left: 12px
}

.index-module_larkUserInfoDesc_zXBlq .index-module_userInfoName_qxlZZ {
    margin-top: 6px
}

.index-module_addContent_9Q7HB {
    width: 36px;
    height: 36px;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 8px;
    line-height: 36px;
    text-align: center;
    padding-top: 4px
}

.index-module_addContent_9Q7HB:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_emptyOrgWapper_3iFkB {
    display: flex;
    padding: 10px 0
}

.index-module_emptyOrgWapper_3iFkB .index-module_addContent_9Q7HB svg {
    font-size: 20px
}

.index-module_emptyOrgMenuItem_Tzqje {
    margin: 8px!important;
    min-height: 89px
}

.index-module_emptyOrgMenuItem_Tzqje:hover .index-module_addContent_9Q7HB {
    border-color: var(--yq-bg-primary-hover)
}

.index-module_createOrgText_IB7Xa {
    line-height: 20px;
    margin-left: 12px;
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_createOrgDesc_kip8e {
    font-weight: 400;
    font-size: 12px;
    margin-left: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 20px;
    margin-top: 5px
}

.index-module_orgItemPic_wEklU {
    width: 36px;
    height: 36px;
    margin-top: 4px
}

.index-module_orgItemPic_wEklU img {
    height: 32px;
    width: 32px;
    border-radius: 8px;
    margin-top: -10px;
    margin-left: 1px
}

.index-module_orgItemWarp_3uqiD {
    display: flex
}

.index-module_orgItemTitle_CO2R9 {
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    margin-left: 10px;
    width: 168px;
    overflow: hidden
}

.index-module_orgMenuItem_W0TQZ {
    height: auto!important;
    width: 260px;
    margin-left: 6px!important
}

.index-module_orgMenuItem_W0TQZ .index-module_orgItemWarp_3uqiD {
    width: 230px;
    margin-bottom: 2px;
    margin-top: 2px
}

.index-module_orgMenuItem_W0TQZ .index-module_orgItemAddContentText_SV-gS,.index-module_orgMenuItem_W0TQZ:hover .index-module_orgItemAddContentText_SV-gS {
    color: var(--yq-yuque-grey-9)
}

.index-module_orgMenuItem_W0TQZ:hover .index-module_orgItemWapper_o0M2x .index-module_orgItemAddContent_0ZsVn {
    background-color: var(--yq-white)
}

.index-module_orgMenuItem_W0TQZ .ant-menu-item {
    height: 200px
}

.index-module_orgItemWapper_o0M2x {
    display: flex;
    margin: 4px 0
}

.index-module_orgItemWapper_o0M2x a {
    display: flex
}

.index-module_orgItemSelectedIcon_bcstl {
    margin-left: 10px
}

.index-module_orgItemAddContent_0ZsVn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px;
    line-height: 36px;
    text-align: center;
    margin-top: 5px;
    margin-right: 10px
}

.index-module_orgItemAddContent_0ZsVn svg {
    position: relative;
    font-weight: 600;
    font-size: 20px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgItemAddContentText_SV-gS {
    font-weight: 400;
    font-size: 14px;
    line-height: 46px
}

.index-module_currentOrgItemWarp_mMyam {
    margin-top: 10px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between
}

.index-module_orgItemInfoWarp_SD33d {
    display: flex
}

.index-module_orgBaseInfo_1G-ys {
    width: 268px;
    height: auto;
    background-color: var(--yq-yuque-grey-1);
    border-radius: 6px;
    padding: 16px;
    margin: 0 20px
}

.index-module_orgBaseInfoTitle_UKnBb {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.index-module_memberBadgeUpgradeAction_3fBmI {
    margin-left: 10px
}

.index-module_orgDesc_IMj5\+ {
    height: 14px;
    margin-top: 14px;
    padding: 0 20px;
    color: var(--yq-yuque-grey-7)
}

.index-module_orgDescPersonal_z0a-c {
    line-height: 20px;
    padding: 0 20px;
    padding-top: 8px;
    padding-bottom: 4px;
    color: var(--yq-yuque-grey-7)
}

.index-module_popupSubMenu_nVq2O .ant-menu {
    padding: 8px 0
}

.index-module_popupSubMenu_nVq2O .ant-menu-item {
    height: 52px;
    width: 250px;
    border-radius: 6px;
    margin: 0 8px;
    padding: 0 9px;
    color: var(--yq-yuque-grey-9)
}

.index-module_popupSubMenu_nVq2O .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_popupSubMenuEmpty_B\+\+1- .ant-menu {
    padding: 0
}

.index-module_loginOutWarp_gaK7d {
    width: 195px;
    border-radius: 6px;
    padding: 0 15px;
    cursor: pointer
}

.index-module_loginOutWarp_gaK7d:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_loginOutWarp_gaK7d svg {
    position: relative;
    color: var(--yq-yuque-grey-7);
    font-size: 16px
}

.index-module_switchOrgIcon_YDeGw {
    position: relative;
    top: 2px
}

.index-module_orgUserName_OdMZ3 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 20px;
    position: relative;
    margin-top: 5px;
    top: 30px;
    left: -48px
}

.index-module_persianUserAvatar_TVns8 {
    display: flex;
    line-height: 40px
}

.index-module_persianUserAvatar_TVns8 .index-module_userInfoDesc_6x2Dz {
    margin-left: 12px
}

.index-module_orgUserInfoAvatar_nsvif {
    display: flex;
    align-items: center
}

.index-module_orgUserInfoAvatar_nsvif .index-module_orgUserName_OdMZ3 {
    position: absolute;
    top: 28px;
    left: 48px
}

.index-module_orgUserInfoAvatar_nsvif span {
    max-width: 175px
}

.index-module_orgUserInfoWelfare_fSLpO span {
    max-width: 152px
}

.index-module_switchOrgSubMenu_aiEiB .ant-menu-submenu-title {
    padding-left: 14px!important
}

.index-module_switchOrgSubMenu_aiEiB .ant-menu-submenu-title span {
    margin-left: 8px
}

.index-module_switchModal_3dBTo {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
    background: var(--yq-bg-secondary);
    z-index: 1051;
    padding: 100px 32px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_orgIcons_q1VKs {
    margin-top: 11px;
    display: flex;
    align-items: flex-start;
    z-index: 10
}

.index-module_orgIcons_q1VKs svg {
    margin-left: 6px;
    height: 18px!important
}

.sidebar-is-collapse .index-module_orgUserInfoAvatar_nsvif {
    margin-bottom: -6px;
    margin-top: 7px
}

.index-module_popupThemeMenu_cneEA .ant-menu {
    padding: 8px 0
}

.index-module_popupThemeMenu_cneEA .ant-menu-item {
    height: 52px;
    width: 160px;
    border-radius: 6px;
    margin: 0 8px;
    padding: 0 9px;
    color: var(--yq-yuque-grey-9)
}

.index-module_popupThemeMenu_cneEA .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_themeMenuItem_-5sEJ {
    display: block;
    margin-left: -28px
}

.index-module_themeMenuItem_-5sEJ .index-module_themeMenuIcon_cD7bu {
    font-size: 16px
}

.index-module_themeMenuItem_-5sEJ .index-module_themeMenuItemName_E83CI {
    font-weight: 400;
    margin-left: 16px
}

.index-module_themeMenuItem_-5sEJ .index-module_themeMenuItemChecked_xCSHd {
    position: absolute;
    right: 8px
}

.index-module_headerUser_z5NUu {
    display: flex;
    align-items: center
}

.index-module_dropMenuIcon_VOcaL {
    font-size: 16px
}

.index-module_userInfoAvatar_rtges {
    display: flex;
    border-radius: 6px;
    padding: 0 4px;
    max-width: 178px;
    align-items: center;
    height: 32px;
    line-height: 25px;
    align-self: center;
    cursor: pointer
}

.index-module_userInfoAvatar_rtges:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_userInfoPopover_p\+TgW {
    width: 308px;
    border-radius: 8px
}

.index-module_userInfoPopover_p\+TgW .ant-popover .ant-popover-content {
    margin-left: 20px
}

.index-module_userInfoPopover_p\+TgW .ant-popover-inner-content {
    padding: 0;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px
}

.index-module_userInfoPopover_p\+TgW .ant-popover-content .ant-popover-inner {
    border: 0!important
}

.index-module_userInfoPopover_p\+TgW .ant-popover-arrow {
    display: none
}

.sidebar-is-collapse .sidebar-user-info {
    padding: 0;
    margin-bottom: 10px
}

.sidebar-is-collapse .sidebar-user-info .org-logo {
    margin-left: 2px
}

.sidebar-is-collapse .sidebar-user-info .sidebar-user-action {
    padding-left: 16px
}

.sidebar-is-collapse .sidebar-user-info .user-info-avatar {
    margin-left: 10px;
    margin-right: 8px;
    padding: 0 4px;
    max-width: 40px;
    overflow: hidden
}

.sidebar-is-collapse .sidebar-user-info a.org-logo span {
    display: none
}

.sidebar-is-collapse .sidebar-user-info img {
    margin-top: 3px
}

.sidebar-is-collapse .head-user-info-popover {
    padding-left: 0
}

.sidebar-user-info {
    padding-right: 12px;
    padding-left: 12px
}

.sidebar-user-info>.org-logo>span {
    font-size: 14px;
    font-weight: 600;
    max-width: 98px
}

.sidebar-user-info .org-logo {
    margin-left: 1px
}

.sidebar-user-info .user-info-avatar {
    display: flex;
    border-radius: 6px;
    padding: 2px 6px;
    cursor: pointer
}

.sidebar-user-info .user-info-avatar span {
    margin-top: -4px
}

.sidebar-user-info .user-info-avatar:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_userinfoDown_W5GQw {
    color: var(--yq-icon-caption);
    position: relative;
    top: 0;
    left: 3px
}

.index-module_larkPersionUserAvatar_B2T0B {
    display: flex;
    padding-top: 3px
}

.index-module_larkPersionUserAvatar_B2T0B .index-module_userinfoDown_W5GQw {
    margin-top: 7px
}

.sidebar-is-collapse .index-module_userName_afxDm {
    display: none
}

.sidebar-is-collapse .index-module_userInfoAvatar_rtges img {
    margin-top: 4px
}

.index-module_btnNewOrg_V6nKz {
    display: flex;
    align-items: flex-start;
    line-height: 1.35;
    padding-left: 43px!important
}

.index-module_btnNewOrg_V6nKz h6 {
    position: relative!important
}

.index-module_btnNewOrg_V6nKz .icon-svg {
    position: absolute;
    left: -28px;
    top: 1px;
    color: var(--yq-yuque-grey-9)
}

.index-module_btnNewOrg_V6nKz .index-module_orgAddText_98X0I {
    margin-left: 11px
}

.index-module_btnNewOrg_V6nKz .index-module_tag_DSBkQ {
    display: inline-flex;
    margin-left: 8px;
    padding: 0 6px;
    color: var(--yq-yuque-green-7);
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    background: var(--yq-yuque-green-1);
    border-radius: 3px
}

.index-module_wrapper_mRIh5 {
    max-width: 480px;
    margin: 32px auto
}

.index-module_card_ZtsWp .ant-card-body {
    padding: 24px
}

.index-module_card_ZtsWp .index-module_header_smdG- {
    margin-bottom: 16px
}

.index-module_card_ZtsWp .index-module_actions_APVcN .index-module_btn_r\+e7V {
    margin-right: 8px
}

.index-module_card_ZtsWp .index-module_actions_APVcN .index-module_defaultBtn_LoLNP {
    background-image: none;
    background-color: var(--yq-bg-primary)
}

.index-module_actions_APVcN,.index-module_orgWrapper_Gwl26 {
    margin-top: 24px
}

.index-module_actions_APVcN {
    display: flex
}

.index-module_actions_APVcN .index-module_btn_r\+e7V:last-child {
    margin-right: 0
}

.index-module_floatLeftBtn_idfaZ {
    cursor: pointer;
    margin-right: auto;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    height: 32px;
    padding: 4.5px 0;
    color: var(--yq-text-caption)
}

.index-module_floatLeftBtn_idfaZ:hover,.index-module_floatLeftBtn_idfaZ:hover * {
    color: var(--yq-text-body)
}

.index-module_orgWrapper_Gwl26 {
    padding: 16px;
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    display: flex;
    align-items: center
}

.index-module_orgWrapper_Gwl26 .index-module_orgLogo_sYs6j {
    margin-right: 16px
}

.index-module_orgWrapper_Gwl26 .index-module_orgInfo_Vy2Zq {
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_orgWrapper_Gwl26 .index-module_orgName_b29on {
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_placeholer_tOo7B {
    flex: 1
}

.Logo-module_wrapper_GI96t {
    display: flex;
    align-items: center
}

.Logo-module_logo_SmJ4p {
    display: block
}

.Logo-module_organization_JtGWE {
    display: flex;
    align-items: center
}

.Logo-module_organization_JtGWE .Logo-module_logo_SmJ4p {
    border-radius: 3px
}

.Logo-module_name_kvosq {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
    flex: auto;
    max-width: 157px;
    font-size: 18px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.Logo-module_yuqueName_8Mzry {
    margin-left: -32px
}

.index-module_orgVersionLabel_NC34L {
    border-width: 1px;
    border-radius: 2px;
    border-style: solid;
    padding: 0 4px;
    font-size: 10px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-blue-8);
    background-color: var(--yq-blue-1);
    border-color: var(--yq-yuque-grey-6)
}

.index-module_orgVersionLabel_NC34L.index-module_pro_jMq9U {
    color: var(--yq-yellow-9);
    background-color: var(--yq-yellow-2);
    border-color: var(--yq-yellow-3)
}

.index-module_orgVersionLabel_NC34L.index-module_pro_jMq9U>span {
    margin-left: 8px
}

.index-module_orgVersionLabel_NC34L.index-module_expired_weUIR {
    color: var(--yq-text-disable);
    background-color: var(--yq-yuque-grey-5);
    border-color: var(--yq-yuque-grey-5)
}

.index-module_orgVersionLabel_NC34L.index-module_expired_weUIR>span {
    margin-left: 8px
}

.index-module_container_ppuQD {
    position: relative;
    margin-left: 8px;
    display: flex;
    align-items: center
}

.index-module_container_ppuQD.index-module_pro_jMq9U {
    margin-left: 4px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteContainer_1caED {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V {
    position: relative;
    top: 8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -8px;
    left: -8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteH5Container_ituBE {
    position: relative;
    left: 2px
}

.btn-follow .hover-text,.btn-follow:hover .default-text {
    display: none
}

.btn-follow:hover .hover-text {
    display: inline
}

.btn-follow.ant-btn-default:focus,.btn-follow.ant-btn-default:hover {
    color: var(--yq-text-body);
    border-color: var(--yq-border-primary);
    outline: none
}

.btn-follow.ant-btn-clicked:after {
    display: none
}

.btn-plain-follow {
    color: var(--yq-text-caption)
}

.btn-plain-follow .larkui-icon {
    margin-right: 4px;
    color: var(--yq-text-caption)
}

.btn-plain-follow>a,.btn-plain-follow>a .larkui-icon {
    color: var(--yq-text-caption)
}

.btn-plain-follow>a:hover,.btn-plain-follow>a:hover .larkui-icon {
    color: var(--yq-text-body)
}

.index-module_count_pDHQn {
    margin-left: 8px;
    font-weight: 500
}

.index-module_orgCostDetail_cLnEB {
    cursor: pointer;
    font-weight: 500
}

.index-module_content_oAhH8 {
    width: 378px
}

.index-module_content_oAhH8 .ant-divider {
    margin: 12px 0
}

.index-module_row_by2z2 {
    display: flex;
    justify-content: space-between;
    padding: 6px 0
}

.index-module_row_by2z2:last-child {
    font-weight: 700
}

.index-module_row_by2z2 .index-module_right_3Ce8Y {
    display: flex
}

.index-module_tips_EUcPA {
    padding: 2px 0;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_title_J6b5y {
    padding: 8px 8px 8px 0
}

.index-module_title_J6b5y a {
    color: var(--yq-text-caption)
}

.index-module_hasDiscount_TqEsK {
    color: var(--yq-orange-7)
}

.index-module_rawPrice_2UTkU {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_detailsWrapper_rnkfa .index-module_detailTitle_-gjun {
    color: var(--yq-text-primary)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt {
    height: 72px;
    max-height: 80px;
    overflow-y: auto
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK span:nth-child(2n) {
    color: var(--yq-text-body)
}

.index-module_summary_TYNAM {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_summary_TYNAM .index-module_left_m83qX {
    display: block
}

.index-module_summary_TYNAM .index-module_left_m83qX .index-module_summaryTitle_FTeFf {
    color: var(--yq-text-primary)
}

.index-module_summary_TYNAM .index-module_left_m83qX span {
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_summary_TYNAM .index-module_right_3Ce8Y .index-module_price_5CyQB {
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_divider_Rppou {
    margin: 12px auto
}

.index-module_paymentSelector_60wvq {
    margin: 0 0 24px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_paymentSelector_60wvq h4 {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e {
    position: relative;
    padding: 16px 10px;
    height: 100%;
    border-radius: 8px;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 {
    display: flex;
    justify-content: space-between
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_icon_sjp\+e {
    margin-left: 6px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_paymentType_rBxoj {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceNumber_6EuVJ {
    color: var(--yq-text-primary);
    font-size: 12px;
    font-weight: 500
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceUnit_xqqPQ {
    color: var(--yq-text-caption);
    font-size: 10px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_oldMemberPrice_4QJhr {
    display: block;
    text-align: right;
    font-size: 10px;
    color: var(--yq-text-caption);
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_desc_5wzpx {
    position: absolute;
    bottom: 16px;
    margin-top: 6px;
    width: 160px;
    font-size: 12px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_active_HZTq5 {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-theme)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: hsla(0,0%,100%,.5);
    cursor: not-allowed
}

html[data-kumuhana=pouli] .index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    background-color: rgba(0,0,0,.5)
}

.index-module_tooltip_lMvUZ {
    font-size: 14px;
    white-space: nowrap
}

.index-module_tooltip_lMvUZ a {
    margin-left: 8px
}

.BuyModal-module_row_k9bGD {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyModal-module_row_k9bGD h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 {
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 span {
    color: var(--yq-text-caption)
}

.BuyModal-module_row_k9bGD .BuyModal-module_right_-\+dqF {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq {
    display: flex;
    align-items: center
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq svg {
    margin-right: 6px
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payBtn_JujzZ {
    margin: 12px auto;
    width: 100%
}

.BuyModal-module_promoCode_IT7ha a,.BuyModal-module_promoCode_IT7ha a:hover {
    color: var(--yq-text-primary)
}

.BuyModal-module_tips_fuHYC {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingContainer_tqVEA {
    display: flex;
    justify-content: space-between;
    padding: 32px;
    min-height: 688px;
    background-color: var(--yq-yuque-grey-2);
    background-position: 0 100%;
    background-repeat: no-repeat;
    border-radius: 8px
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*z00vQYi5mvAAAAAAAAAAAAAAARQnAQ);
    background-size: 274px 215px
}

html[data-kumuhana=pouli] .BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*4iv1S4HXxg4AAAAAAAAAAAAADvuFAQ/original)
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_enterprise_KQPTt {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*h_IRR6H2EB8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 238px 170px
}

.BuyModal-module_pricingLeft_X4ns6 {
    width: 324px;
    padding-top: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG .BuyModal-module_icon_UPf-t {
    margin-left: 8px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentDesc_7e1Uv {
    margin: 4px 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL {
    display: inline-block;
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL .BuyModal-module_icon_UPf-t {
    margin-left: 0;
    vertical-align: text-top
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe {
    margin-bottom: 40px;
    position: relative;
    padding-left: 48px;
    height: 46px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_iconWarpper_j2vhb {
    position: absolute;
    top: 50%;
    left: 0;
    width: 36px;
    height: 36px;
    transform: translateY(-50%);
    background-color: var(--yq-bg-primary);
    box-shadow: 0 2px 9px 0 rgba(0,0,0,.02);
    border-radius: 6px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_icon_UPf-t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_title_aoygt {
    margin-bottom: 4px;
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_desc_nWmKR {
    color: var(--yq-text-caption);
    font-size: 12px
}

.BuyModal-module_pricingRight_p54lJ {
    position: relative;
    padding: 24px 24px 98px 24px;
    width: 420px;
    min-height: 624px;
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .BuyModal-module_pricingRight_p54lJ {
    background: var(--yq-bg-tertiary)
}

.BuyModal-module_avatar_SRhTt {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background-color: var(--yq-bg-tertiary)
}

.BuyModal-module_termsContainer_i187T span {
    color: var(--yq-text-body)!important
}

.BuyModal-module_termsContainer_i187T .ant-checkbox+span {
    padding-right: 0
}

.BuyModal-module_statsWrapper_ZDvF1 .BuyModal-module_statsDesc_1iKOA {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 28px
}

.BuyModal-module_compact_qVAW5 h3 {
    margin-bottom: 4px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_active_SC0g\+ {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_settle_6sm-p {
    margin-left: 12px
}

.BuyModal-module_channel_aD1hq button[disabled] {
    padding: 4px 12px;
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyModal-module_channel_aD1hq button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.ant-btn.BuyModal-module_channel_aD1hq {
    padding: 4px 12px
}

.BuyModal-module_name_bL3Ia {
    max-width: 230px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BuyModal-module_memberSizeInput_xIYHO {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.BuyModal-module_memberSizeInput_xIYHO .BuyModal-module_errorMsg_ZbqFE {
    position: absolute;
    bottom: -24px;
    max-width: 200px;
    white-space: nowrap;
    color: var(--yq-function-error);
    font-weight: 400;
    font-size: 14px
}

.BuyModal-module_footer_eF3uw {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 48px);
    transform: translateX(-50%)
}

.BuyMember-module_row_9srYf {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyMember-module_row_9srYf h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og {
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og span {
    color: var(--yq-text-caption)
}

.BuyMember-module_row_9srYf .BuyMember-module_right_Oni7O {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT {
    display: flex;
    align-items: center
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT svg {
    margin-right: 6px
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payBtn_2QBqR {
    margin: 12px auto;
    width: 100%
}

.BuyMember-module_promoCode_3q8my a,.BuyMember-module_promoCode_3q8my a:hover {
    color: var(--yq-text-primary)
}

.BuyMember-module_tips_0MaeW {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyMember-module_pricingContainer_oSImw h2 {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_expiredDesc_kyKaA {
    margin-bottom: 24px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BuyMember-module_channel_oCioT.BuyMember-module_active_hyrgK {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyMember-module_channel_oCioT button[disabled] {
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyMember-module_channel_oCioT button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_right_Oni7O>:not(:first-child) {
    margin-left: 12px
}

.Pay-module_row_02i1B {
    padding-bottom: 24px
}

.Pay-module_row_02i1B:nth-child(6) {
    padding-bottom: 0
}

.Pay-module_row_02i1B .Pay-module_left_u3-Jq {
    padding-bottom: 8px
}

.Pay-module_row_02i1B .Pay-module_name_4PPzr {
    font-size: 16px;
    color: var(--yq-text-primary);
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px
}

.Pay-module_row_02i1B .Pay-module_nameRow_iKvRZ {
    display: flex
}

.Pay-module_row_02i1B .Pay-module_version_C36aU {
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-8);
    border-radius: 4px;
    font-size: 12px;
    padding: 1px 3px;
    background-color: var(--yq-blue-1)
}

.Pay-module_row_02i1B .Pay-module_version_C36aU.Pay-module_paidVersion_pBnh8 {
    color: var(--yq-yellow-7);
    border-color: var(--yq-yellow-7);
    background-color: var(--yq-yellow-1)
}

.Pay-module_cardList_OD1er {
    display: flex;
    margin-bottom: 24px
}

.Pay-module_memberSize_Snqz8 {
    overflow: hidden;
    opacity: 0;
    height: 0;
    transition: all .3s
}

.Pay-module_memberSize_Snqz8.Pay-module_show_u86Cd {
    opacity: 1;
    height: 81px
}

.Pay-module_totalInfo_YM7Fb {
    height: 45px;
    margin: 4px 0 18px 0;
    flex-wrap: wrap;
    font-weight: 600
}

.Pay-module_footer_MsuSK,.Pay-module_totalInfo_YM7Fb {
    display: flex;
    align-items: center
}

.Pay-module_footer_MsuSK .Pay-module_termsContainer_MMlAf {
    margin-left: 12px;
    display: flex
}

.Pay-module_pricingContainer_hjkUd {
    padding-bottom: 8px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_desc_a1fXI {
    margin-top: -20px;
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_dashline_y-S0k {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 24px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingInfo_NRstM {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingMainTitle_pinlM {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingSubTitle_FlFF9 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_inputNumber_Uaz26 {
    height: 32px;
    width: 200px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingDesc_SK8fY {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz {
    font-size: 28px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 45px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz.Pay-module_hasDiscount_KLNoP {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz {
    margin-left: 16px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz .Pay-module_discountPriceValue_CUsg2 {
    font-size: 28px;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWith_vFDxq {
    margin: 8px 0 20px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayIcon_SjV5X {
    width: 24px;
    margin-top: -2px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayText_r3AWK {
    padding-left: 8px
}

.Pay-module_uploadPackages_VO59r {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 32px
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL {
    width: 160px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL:nth-child(3n) {
    margin-right: 0
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL.Pay-module_selectedUploadCard_yWd2Y {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(37,184,100,.05)
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadSize_WpwBc {
    font-size: 16px;
    font-weight: 600
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackagePrice_zQPrm {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_buyMemberCon_CB8\+C {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q,.Pay-module_buyMemberCon_CB8\+C .Pay-module_upgradeMember_6duT1 {
    background-color: var(--yq-bg-secondary);
    padding: 16px;
    width: 244px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q .Pay-module_memberValue_l6w8U {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0
}

.Pay-module_tips_BIW\+T {
    margin-top: 6px;
    color: var(--yq-text-caption);
    width: 500px
}

.Pay-module_discountInfo_Uq4\+K {
    color: var(--yq-text-caption);
    padding-bottom: 8px
}

.Pay-module_promo_sD6ap {
    margin-top: -6px
}

.Pay-module_promo_sD6ap .Pay-module_left_u3-Jq {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 120px
}

.Pay-module_promo_sD6ap .Pay-module_inputNumber_Uaz26 {
    padding-top: 8px;
    padding-bottom: 40px
}

.Qrcode-module_info_XwHqZ {
    flex: auto;
    max-width: 332px
}

.Qrcode-module_infoTitle_nNa5e {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500
}

.Qrcode-module_subInfoTip_DYh05 {
    color: var(--yq-text-body);
    margin-top: 10px
}

.Qrcode-module_infoTip_wp2zv>span {
    margin-right: 28px
}

@media only screen and (max-width: 575px) {
    .Qrcode-module_infoTip_wp2zv>span {
        display:block
    }
}

.Qrcode-module_desc_T1-nf {
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi {
    padding-bottom: 8px
}

.Qrcode-module_pricingContainer_2\+hyi h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_dashline_n20jZ {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingInfo_Pe1Ai {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingMainTitle_D0fu0 {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingSubTitle_3SVt2 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P .Qrcode-module_select_2Topv {
    width: 140px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingDesc_aTeur {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_priceTag_gSsWI {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWith_Orohw {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_totalInfo_-\+rtG {
    margin: 4px 0 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayIcon_Hoi7K {
    width: 24px;
    margin-top: -2px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayText_wbuGd {
    padding-left: 8px
}

.Qrcode-module_processContainer_5BqWV {
    text-align: center;
    padding: 10px 0
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processImg_MgbCi {
    width: 120px
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processInfo_ST\+BP {
    margin-top: 12px
}

.Qrcode-module_termsContainer_Uqaqj {
    margin-bottom: 12px;
    display: flex
}

.Qrcode-module_bindingContainer_b3OnQ {
    padding: 24px 0;
    text-align: center
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingImg_sEWSW {
    width: 80px
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingInfo_qhBjF {
    color: var(--yq-text-primary);
    margin: 24px 0
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingFooter_7PjRS .Qrcode-module_bindingVerify_CgNHw {
    margin-right: 8px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi {
    position: relative
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 img {
    width: 100%
}

@media only screen and (max-width: 768px) {
    .Qrcode-module_qrcodeImageCon_yy\+Gi .alipayIconInQrCode {
        width:36px;
        height: 36px;
        margin-left: -18px;
        margin-top: -18px
    }
}

.Qrcode-module_successContainer_RvR5s {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfoCon_Yt35q {
    padding: 10px 40px 30px 40px
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfo_w7k6O {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.Qrcode-module_qrContainer_K9uEQ {
    margin-bottom: 20px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeImg_GAmpl {
    width: 72px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeInfo_8NXdM {
    font-size: 14px;
    font-weight: 400;
    padding: 24px 0 0 30px;
    color: var(--yq-text-body);
    line-height: 1.8
}

.Qrcode-module_qrcodeContainer_c79eX {
    min-height: 444px;
    text-align: center
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_qrcodeImage_UEIyZ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    width: 55%
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc1_ArZR9 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc2_E5sDl {
    margin-top: 10px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_link_ZEjlM {
    margin-top: 8px
}

.Qrcode-module_receiptTypeTitle_jIYOl {
    margin-bottom: 10px
}

.Qrcode-module_receiptTypeTitleText_8q0XQ {
    margin-bottom: 8px
}

.Qrcode-module_applyReceiptButton_Wfqmq {
    margin-left: 10px
}

.Qrcode-module_contactTips_ETe9S {
    margin-bottom: 10px
}

.Qrcode-module_buttonCon_Jic-k {
    margin-top: 8px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleDesc_OOQby {
    font-size: 16px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleValue_PBwE\+ {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.PayModal-module_noLineModal_r4djn .ant-modal-header {
    border-bottom: 0
}

.PayModal-module_warnContent_TTDov {
    margin-top: 20px;
    margin-bottom: 24px
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-body {
    padding: 0
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px
}

.ProcessingModal-module_processContainer_dOZ9t {
    text-align: center;
    padding: 10px 0
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processImg_Fl\+7o {
    width: 120px
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processInfo_VPh0u {
    margin-top: 12px
}

.paid-success-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.paid-success-modal .ant-modal-body {
    padding: 0
}

.paid-success-modal .ant-modal-body img {
    width: 100%
}

.paid-success-modal .ant-col {
    padding-top: 10px
}

.paid-success-modal .ant-col-18 {
    font-weight: 600
}

.PaidSuccessModal-module_successModalContainer_gb6Iw {
    color: var(--yq-text-body);
    border-radius: 8px;
    overflow: hidden
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoCon_aLoy0 {
    padding: 20px 24px 30px 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfo_TQeHl {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoTitle_fmm0I {
    font-size: 28px;
    margin-top: 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns {
    margin-top: 32px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_applyReceiptButton_BrptN {
    margin-left: 12px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_checkUploadButton_n0Sct {
    margin-left: 12px;
    margin-top: 1px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalApplyReceiptButton_hbmRn {
    margin-left: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalSubTitle_Hq8oy {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalItem_EBnTl {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalBlank_3ENdm {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.ReceiptModal-module_contactTips_f0fAJ,.ReceiptModal-module_receiptTypeTitle_mZi8G {
    margin-bottom: 10px
}

.ReceiptModal-module_receiptTypeTitleText_QQBhY {
    margin-bottom: 8px
}

.ReceiptModal-module_receiptDesc_fY2bw {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

@keyframes SubAccountInfoModal-module_loadingCircle_mPmQ5 {
    to {
        transform: rotate(1turn)
    }
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p:first-child {
    border-bottom: 1px dashed var(--yq-border-primary);
    padding-bottom: 16px;
    margin-bottom: 32px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldTitle_tgwmv {
    display: inline-block;
    font-weight: 600;
    min-width: 80px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p p {
    margin-bottom: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldPaymentAlert_UOLk7 {
    margin-bottom: 20px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e {
    margin-top: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e .SubAccountInfoModal-module_btn_WuNy4 {
    margin-right: 16px
}

.SubAccountInfoModal-module_loading_sNKQL {
    padding: 32px 0;
    text-align: center
}

.SubAccountInfoModal-module_loading_sNKQL .SubAccountInfoModal-module_loadingIcon_M7IJr {
    animation: SubAccountInfoModal-module_loadingCircle_mPmQ5 1s linear infinite
}

.SubAccountInfoModal-module_loading_sNKQL .larkui-icon {
    font-size: 24px
}

.SubAccountInfoModal-module_loadingDesc_QHGbP {
    margin-top: 16px
}

.index-module_buyButton_pN7y0.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_buyButton_pN7y0a.ant-btn span {
    line-height: 1
}

.index-module_upgradeButton_nJqrZ.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_upgradeButton_nJqrZa.ant-btn span {
    line-height: 1
}

.index-module_orgVersionLabel_egPu3 {
    border-width: 1px;
    border-radius: 4px;
    border-style: solid;
    padding: 0 4px;
    margin-left: 16px
}

.index-module_tip_rQx-e {
    margin-top: 2px;
    margin-bottom: 2px;
    display: flex;
    justify-content: space-between
}

.index-module_tip_rQx-e a:before {
    position: relative
}

.index-module_tip_rQx-e span span {
    margin-left: 12px
}

.index-module_tip_rQx-e .index-module_payment_pQExj {
    color: var(--yq-yuque-grey-8)
}

.index-module_tip_rQx-e .index-module_paymentLink_GbvgZ {
    color: var(--yq-blue-6);
    line-height: 60px
}

.index-module_paymentGuideWrapper_2jg39 {
    max-width: 584px;
    margin-left: auto;
    margin-right: auto;
    padding: 24px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_thumb_Lw5zW {
    display: block;
    width: 148px;
    height: 120px;
    margin: 0 auto 16px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100%
}

.index-module_paymentGuideWrapper_2jg39 .index-module_title_EykNL {
    text-align: center;
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 700
}

.index-module_paymentGuideWrapper_2jg39 .index-module_body_\+1-cr {
    margin: 12px auto 32px auto;
    max-width: 400px;
    color: var(--yq-text-body);
    text-align: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_link_zDdoa {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB {
    display: flex;
    align-items: center;
    justify-content: end
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB .index-module_btnTryout_5GVmK {
    margin-right: 8px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_btnTryout_5GVmK {
    margin-bottom: 12px;
    width: 148px;
    height: 32px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_link_zDdoa {
    width: 148px;
    text-align: center
}

.index-module_actions_LRgdL .index-module_btn_vRXZD {
    width: 160px;
    height: 32px
}

.index-module_premiumFeaturesLabel_q\+uFd {
    width: 86px;
    height: 37px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*pIrCQLn06McAAAAAAAAAAAAAARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_alignCenter_h648T {
    display: flex;
    align-items: center;
    position: relative
}

.index-module_orgExpiredTip_k7iLg {
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgExpiredTipOpt_QDhkZ,.index-module_orgExpiredTipText_gQuZw {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    width: 140px
}

.index-module_orgExpiredTipText_gQuZw {
    white-space: nowrap
}

.index-module_orgExpiredTipText_gQuZw span {
    color: var(--yq-yuque-grey-9)
}

.index-module_payflowTitle_LM3aR {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px
}

.index-module_freeWrapper_BlI2K {
    display: flex;
    justify-content: space-between
}

.index-module_payflowLink_Wt3fM {
    margin-left: 0;
    display: inline-block
}

.index-module_isExpired_UgOgs {
    border-bottom: 1px solid var(--yq-yuque-grey-4);
    padding-bottom: 10px
}

.index-module_freeTips_ybaQ- {
    display: flex;
    justify-content: space-between
}

.index-module_upgrade_qL2nR .ant-btn-link {
    padding: 0;
    height: auto
}

.index-module_wrapper_ulVDd .ant-modal-body {
    padding: 0!important
}

.index-module_wrapper_ulVDd .anticon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-information {
    display: none
}

.index-module_wrapper_ulVDd .ant-modal-confirm-content {
    width: 480px;
    margin-top: 0
}

.index-module_wrapper_ulVDd .ant-modal-confirm-btns {
    display: none
}

.index-module_wrapper_ulVDd .index-module_content_SO1HL {
    margin: 28px 0;
    padding: 0 24px
}

.index-module_wrapper_ulVDd .index-module_title_SVPzd {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_wrapper_ulVDd .index-module_desc_MlCQ9 {
    margin-top: 16px;
    margin-bottom: 32px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_wrapper_ulVDd .index-module_img_SBaEY {
    width: 480px
}

.index-module_trialButton_tH3iD {
    margin-left: 10px
}

.index-module_footer_cL9eu {
    display: flex;
    justify-content: flex-end
}

.index-module_footer_cL9eu .index-module_btn_XNqZC {
    margin-left: 10px
}

.OrgUserInfo-module_departmentInfo_3udmp {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-light)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_cardHeader_502Md {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq {
    margin: 0 24px 0 0
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: center;
    margin-right: 24px;
    line-height: 26px;
    color: var(--yq-text-body)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq .OrgUserInfo-module_icon_okrek,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz .OrgUserInfo-module_icon_okrek {
    margin-right: 8px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_fullWidth_zv0Qo {
    width: 100%;
    line-height: 32px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6:nth-child(n+2) {
    margin-top: 16px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97 {
    display: inline-block;
    margin-left: 4px;
    width: 100%;
    font-weight: 500
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97.OrgUserInfo-module_h5_M\+nrk {
    margin-left: 8px;
    font-weight: 400;
    font-size: 16px
}

.OrgUserInfo-module_divider_igZMz {
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background-color: var(--yq-border-light)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or {
    padding: 16px;
    width: 280px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 8px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 {
    position: relative;
    padding-left: 56px;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_avatar_iMqQ\+ {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_wrapper_cpsZr {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_nameWrapper_u01vS {
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_name_9rYrf {
    max-width: 100%;
    color: var(--yq-text-primary);
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_tag_dWOJO {
    margin-left: 4px;
    padding: 0 4px;
    background-color: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    color: var(--yq-text-body);
    font-size: 12px;
    height: 20px;
    line-height: 20px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_description_4Q94H {
    margin-top: 4px;
    font-size: 14px;
    line-height: 18px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_icon_okrek {
    color: var(--yq-icon-secondary)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: flex-start;
    width: 100%
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6 span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    margin-left: 8px;
    color: var(--yq-text-body);
    font-size: 14px;
    line-height: 22px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    margin-top: 12px;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6:nth-child(n+3) {
    margin-top: 12px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_icon_okrek {
    transform: translateY(4px)
}

.index-module_userCard_NOTbr {
    width: 330px
}

.index-module_userCard_NOTbr .index-module_body_20bgh {
    display: flex;
    padding: 20px 18px 20px 24px
}

.index-module_userCard_NOTbr img.index-module_avatar_OBHFJ {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    flex-shrink: 0
}

.index-module_userCard_NOTbr .index-module_userInfos_B16Pa {
    margin-left: 20px;
    width: 220px
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    word-break: break-all;
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_name_QeZm4 {
    color: var(--yq-text-primary)
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_dingtalk_kYXBj {
    height: 22px;
    vertical-align: middle;
    display: inline-block
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_badge_lsJBP {
    top: -2px;
    height: 22px
}

.index-module_userCard_NOTbr .index-module_dingding_l7pi0 {
    margin-left: 4px;
    width: 18px;
    height: 18px
}

.index-module_userCard_NOTbr .index-module_infoWithBg_JGcdP {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    color: var(--yq-text-body);
    background-color: var(--yq-bg-tertiary);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 400;
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_signature_aQ\+Wz {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-body);
    margin-top: 8px;
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_infoList_Y58li {
    margin-top: 10px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 {
    display: flex;
    margin-bottom: 8px;
    min-height: 20px;
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7:last-child {
    margin-bottom: 0
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 .index-module_icon_MGwU8 {
    margin-top: 2px;
    margin-right: 8px;
    color: var(--yq-icon-primary)
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 span {
    color: var(--yq-text-primary);
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 a {
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_footer_FNHCP {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--yq-border-light);
    padding: 12px 18px 12px 24px
}

.index-module_userCard_NOTbr .index-module_follow_1HgLb {
    display: flex
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j {
    margin-right: 24px;
    color: var(--yq-text-disable);
    margin-top: 6px
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j:last-child {
    margin-right: 0
}

.index-module_userCard_NOTbr .index-module_number_NahUE {
    margin-left: 5px;
    color: var(--yq-text-primary);
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_userLink_i0XYI {
    float: right
}

.index-module_userCard_NOTbr .index-module_skeleton_Z4M4v {
    width: 100%;
    height: 50px;
    margin-top: 16px;
    overflow: hidden
}

.index-module_userCard_NOTbr .index-module_infoDetail_dKXCO {
    word-break: break-all
}

.index-module_popover_Xyidp {
    display: inline-block
}

.index-module_overlay_A0ouW .ant-popover-inner-content {
    padding: 0
}

.larkui-member-selector-container .ant-modal-header {
    padding: 0;
    border-bottom: 0 none;
    overflow: hidden
}

.larkui-member-selector-container .ant-modal-body {
    padding: 0
}

.larkui-member-selector-container .ant-modal-close-x {
    width: 46px;
    height: 46px
}

.larkui-member-selector-container .ant-modal-close-x .larkui-icon {
    vertical-align: 0
}

.larkui-member-selector-container-select-by {
    padding-top: 0
}

.larkui-member-selector-container-select-by .ant-menu-horizontal {
    background-color: revert
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected,.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item:hover {
    color: var(--yq-text-primary);
    font-weight: 700;
    background: none
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item {
    margin: 0;
    margin-left: 32px;
    padding: 0;
    color: var(--yq-text-body);
    font-weight: 400
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item:first-child {
    margin-left: 20px
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item :hover {
    font-weight: 700
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected {
    color: var(--yq-text-primary);
    font-weight: 700;
    border-bottom-width: 2px
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected:hover {
    color: var(--yq-text-primary)
}

.larkui-member-selector-role {
    width: 80px
}

.larkui-member-selector-role-role-select {
    width: 295px
}

.larkui-member-selector-role-role-select .ant-select {
    width: 100%
}

.larkui-member-selector-role-role-tip {
    margin-right: 4px
}

.larkui-member-selector-role-role-label {
    font-weight: 500
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.larkui-member-selector-user-search,.larkui-member-selector-user-search-container {
    width: 100%
}

.larkui-member-selector-user-search-container .ant-select-selector {
    background: none!important;
    border: 0 none!important;
    box-shadow: none!important
}

.larkui-member-selector-user-search-container .ant-select-arrow {
    color: var(--yq-text-body)
}

.larkui-member-selector-user-search-container-normal {
    width: 100%
}

.larkui-member-selector-user-search-user-selector-tag {
    height: 32px;
    border-radius: 16px;
    margin-right: 8px;
    padding: 4px;
    color: var(--yq-black);
    border: none;
    background-color: var(--yq-bg-tertiary);
    margin-bottom: 8px
}

.larkui-member-selector-user-search-user-selector-tag-avatar {
    margin-right: 4px
}

.larkui-member-selector-user-search-user-selector-tag-label {
    display: inline-block;
    line-height: 20px;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar-wrapper {
    display: inline-block;
    width: 18px;
    height: 18px;
    vertical-align: middle;
    line-height: 18px;
    text-align: center;
    margin-right: 3px;
    padding-top: 2px
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar-wrapper:hover {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 50%
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar {
    color: var(--yq-text-caption)
}

.larkui-member-selector-user-search-menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.larkui-member-selector-user-search-menu-item .ant-dropdown-menu-item-disabled .icon,.larkui-member-selector-user-search-menu-item .ant-dropdown-menu-item-disabled .login {
    color: var(--yq-ant-disabled-color)
}

.larkui-member-selector-user-search-user {
    padding: 3px 10px;
    display: flex;
    align-items: center;
    height: 40px
}

.larkui-member-selector-user-search-isdep {
    height: 52px
}

.larkui-member-selector-user-search-title-wrap {
    min-width: 0
}

.larkui-member-selector-user-search-title {
    display: flex;
    align-items: center
}

.larkui-member-selector-user-search-icon {
    min-width: 32px;
    margin-right: 16px
}

.larkui-member-selector-user-search-name {
    margin-right: 8px
}

.larkui-member-selector-user-search-dep,.larkui-member-selector-user-search-login {
    color: var(--yq-text-caption);
    font-size: 12px
}

.larkui-member-selector-user-search-dep {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 295px;
    margin-top: 2px
}

.larkui-member-selector-user-search-notfound {
    padding: 50px 0;
    text-align: center;
    color: var(--yq-text-body)
}

.ellipsis-wrapper {
    width: 100%
}

.larkui-member-selector-user-search-tag-icon {
    margin-right: 2px
}

.larkui-member-selector-search {
    height: 450px;
    position: relative
}

.larkui-member-selector-search-footer {
    position: absolute;
    bottom: 29px;
    left: 24px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    row-gap: 5px;
    width: calc(100% - 48px)
}

.larkui-member-selector-search-meta {
    color: var(--yq-text-caption);
    margin-right: 16px;
    white-space: nowrap;
    flex-shrink: 0
}

.larkui-member-selector-search-action-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1
}

.larkui-member-selector-search-role {
    flex-shrink: 0;
    color: var(--yq-text-caption)
}

.larkui-member-selector-search-role .larkui-dropdown-trigger {
    color: var(--yq-text-body)
}

.larkui-member-selector-search-footer-btn {
    flex-shrink: 0
}

.larkui-member-selector-search-role-text {
    color: var(--yq-text-body)
}

.larkui-member-selector-search-content {
    padding: 20px 0 20px 8px;
    height: 100%;
    max-height: 380px;
    overflow-y: auto
}

.larkui-member-selector-search-selector {
    height: 450px
}

.larkui-member-selector-search-selector .ant-select-selection--multiple .ant-select-selection__rendered .ant-select-selection__choice {
    border-radius: 4px;
    height: 22px;
    font-size: 12px;
    background: var(--yq-bg-tertiary);
    border: 1px solid var(--yq-border-primary)
}

.larkui-member-selector-search-quick-selector-container {
    position: relative;
    border-left: 1px solid var(--yq-border-light);
    height: 450px
}

.larkui-member-selector-search-quick-selector-choose {
    padding: 18px 24px
}

.larkui-member-selector-search-quick-selector-subtitle,.larkui-member-selector-search-quick-selector-title {
    color: var(--yq-text-primary);
    font-size: 14px;
    margin-bottom: 16px;
    font-weight: 400
}

.larkui-member-selector-search-quick-selector-subtitle {
    padding: 16px 24px 0
}

.larkui-member-selector-search-quick-selector-subtitle>span {
    cursor: pointer
}

.larkui-member-selector-search-quick-selector-item {
    height: 40px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--yq-text-body)
}

.larkui-member-selector-search-quick-selector-item img {
    display: block
}

.larkui-member-selector-search-selector-icon {
    margin-right: 8px;
    vertical-align: text-bottom
}

.larkui-member-selector-search-quick-selector-icon,.larkui-member-selector-search-quick-selector-subicon {
    margin-right: 8px
}

.larkui-member-selector-search-quick-selector-main-list {
    height: 396px;
    overflow-y: auto
}

.larkui-member-selector-search-outsourcer-tip-link {
    color: var(--yq-blue-4)
}

.larkui-member-selector-search-member-type-switch-container {
    position: absolute;
    padding: 0 24px;
    bottom: 30px;
    left: 0
}

.index-module_actions_vhNlZ {
    display: block;
    padding: 6px
}

.index-module_actionItem_gm0UP {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 7px
}

.index-module_actionItem_gm0UP:hover {
    cursor: pointer;
    background: var(--yq-bg-primary-hover);
    border-radius: 8px
}

.index-module_actionItem_gm0UP .index-module_actionTitle_N9iRs {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-9)
}

.index-module_actionItem_gm0UP .index-module_actionTitleLast_KuIDA {
    color: var(--yq-red-6)
}

.index-module_actionItem_gm0UP .index-module_actionMore_8wkrV {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--yq-text-caption)
}

.index-module_actionItem_gm0UP .index-module_actionMoreIcon_5FA3\+ {
    margin-left: 2px;
    transform: rotate(-90deg)
}

.index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq {
    margin-right: 12px;
    color: var(--yq-icon-primary)
}

.index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq.index-module_actionTitleIconLast_2BO7h {
    color: var(--yq-red-6)
}

html[data-kumuhana=pouli] .index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq {
    color: var(--yq-icon-secondary)
}

html[data-kumuhana=pouli] .index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq.index-module_actionTitleIconLast_2BO7h {
    color: var(--yq-red-6)
}

.index-module_actionItem_gm0UP:last-child {
    margin-bottom: 0
}

.index-module_actionItem_gm0UP:last-child:hover {
    cursor: pointer;
    background-color: var(--yq-red-1)
}

.ShareChannels-module_shareChannels_gfpjT {
    padding: 16px 24px;
    background-color: var(--yq-bg-tertiary);
    font-weight: 700;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    display: flex;
    justify-content: flex-start;
    align-items: center
}

.ShareChannels-module_shareChannels_gfpjT .icon-svg+.icon-svg,.ShareChannels-module_shareChannels_gfpjT span+.icon-svg {
    margin-left: 16px
}

.ShareChannels-module_shareChannels_gfpjT .icon-svg {
    cursor: pointer
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 {
    position: relative;
    padding: 20px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ant-dropdown {
    padding: 0 12px 12px;
    width: 185px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04),0 2px 8px 0 rgba(0,0,0,.08),0 1px 4px -2px rgba(0,0,0,.13)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_label_tDypL {
    margin-bottom: 4px;
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_desc_u1Wyf {
    color: var(--yq-text-caption);
    font-size: 12px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_collaborator_2YM9N {
    padding-bottom: 0
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_inviteSelector_UvnYT {
    padding-top: 16px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_bookPublicSetting_skCSj {
    margin-top: -8px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_bookPublicTitle_Nvtb5 {
    margin-bottom: -8px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-size: 14px;
    font-weight: 700
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_title_FCiiD {
    text-align: left;
    display: inline-flex;
    align-items: center;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: var(--yq-yuque-grey-9);
    cursor: pointer
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_backIcon_Xa1gT {
    cursor: pointer;
    margin-top: 2px;
    margin-left: -4px;
    margin-right: 4px;
    transform: rotate(90deg)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_titleDesc_Qr9Q3 {
    margin-top: 12px;
    color: var(--yq-yuque-grey-8)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_titleDesc_Qr9Q3.ShareChannels-module_bookPublicSet_HaiHZ {
    color: var(--yq-yuque-grey-7)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_contentArea_WAxw- {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLinkWrapper_zNH1v {
    flex: 1;
    margin-right: 8px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkTitleDesc_RdIXM {
    margin-top: 12px;
    color: var(--yq-yuque-grey-9)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkTitleDesc_RdIXM span {
    font-weight: 500
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkContentArea_\+a59s {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLink_3mHuZ {
    height: 40px;
    line-height: 20px;
    padding: 12px;
    margin-right: 8px;
    color: var(--yq-yuque-grey-6);
    overflow: hidden
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLink_3mHuZ {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    color: rgba(190,192,191,.26)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_inviteCopyBtn_ju\+VP {
    color: #fff;
    font-weight: 700;
    flex: none
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigSettings_b\+fk7 {
    margin-top: 12px;
    border-radius: 8px;
    background-color: var(--yq-yuque-grey-1)
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigSettings_b\+fk7 {
    background-color: var(--yq-yuque-grey-3)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigToggler_caced {
    line-height: 20px;
    margin-top: 12px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigsTip_gtVy7 {
    color: var(--yq-yuque-grey-7);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIcon_MQJP8 {
    color: var(--yq-yuque-grey-6);
    position: relative;
    top: 3px;
    transform: rotate(0);
    transition: all .3s
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIcon_MQJP8[data-activated=true] {
    transform: rotate(-180deg);
    transition: all .3s
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIconColor_BoJlD {
    color: var(--yq-black)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBlock_dU5pI {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--yq-yuque-grey-1);
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px;
    padding: 9px 12px;
    margin-right: 12px;
    font-size: 14px;
    color: var(--yq-yuque-grey-6);
    height: 40px;
    width: 292px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBlock_dU5pI.ShareChannels-module_bookPublicSet_HaiHZ {
    width: 304px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v {
    height: 40px;
    padding: 12px;
    margin-right: 12px;
    color: var(--yq-yuque-grey-6);
    width: 292px;
    background: var(--yq-yuque-grey-1);
    border: 1px solid var(--yq-yuque-grey-4)
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    color: rgba(190,192,191,.26)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v.ShareChannels-module_bookPublicSet_HaiHZ {
    width: 304px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBtn_oJupk {
    font-weight: 700;
    width: 76px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_passwordText_gaCkx {
    color: var(--yq-yuque-grey-7);
    line-height: 22px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetBtn_aOPqB {
    display: flex;
    align-items: center;
    padding: 0;
    color: var(--yq-text-body)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetBtn_aOPqB:hover {
    color: var(--yq-text-caption)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetIcon_rn-tx {
    margin-right: 4px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L {
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L:last-child {
    margin-top: 16px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L.ShareChannels-module_notOwner_oGq\+h {
    cursor: not-allowed
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorIcon_4e0e0 {
    color: #25b864
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_disabledItem_1Ei-3 {
    color: var(--yq-yuque-grey-6)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_shareTime_cjwGm {
    display: flex;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_memberAbleIcon_xR\+WM {
    margin-left: 4px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_shareNotOwner_imImM {
    margin-top: 2px;
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_switchTitle_dS9uP {
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectOption_W3LKu {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.ShareChannels-module_collaboratorContent_0fEPi {
    border-bottom: 1px solid var(--yq-yuque-grey-3)
}

.ShareChannels-module_shareQrCodeContent_14F29 {
    width: 232px;
    margin: 24px auto 20px auto
}

.ShareChannels-module_shareQrCodeArea_crX\+x {
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px;
    margin-bottom: 24px
}

.ShareChannels-module_shareQrCodeButton_xGFZ6 {
    padding: 10px 80px
}

.styles-module_sharePanel_Sxg7e {
    width: 420px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ {
    padding: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item {
    padding-top: 24px;
    padding-bottom: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-title {
    margin-bottom: 2px;
    color: var(--yq-yuque-grey-9)
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-description {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-avatar {
    margin-right: 12px;
    margin-top: 2px;
    height: 44px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-action-split {
    display: none
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-action>li {
    padding: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_firstItem_oD4je,.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_inviteItem_NZWji {
    padding-top: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_shareDescText_Aj\+aY {
    margin-bottom: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz {
    display: flex;
    align-items: center
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz .styles-module_memberBadge_tyo4x {
    margin-left: 4px;
    font-size: 10px;
    background: linear-gradient(270deg,var(--yq-yellow-3),var(--yq-yellow-1));
    padding: 0 8px;
    color: var(--yq-yellow-9);
    border-radius: 8px;
    height: 18px;
    line-height: 18px;
    cursor: default
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz .styles-module_memberAbleIcon_Qqdnq {
    height: 22px!important;
    margin-left: 4px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY {
    padding: 9px;
    color: #fff;
    border-radius: 8px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY.styles-module_appointIcon_4sk\+S {
    background-color: #4b73b3
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY.styles-module_publicIcon_zEz8l {
    background-color: #23ad73
}

.styles-module_sharePanel_Sxg7e .styles-module_inviteIconLi_8F87a {
    margin-left: 16px
}

.styles-module_sharePanel_Sxg7e .styles-module_dot_Z8pRl .ant-badge-dot {
    background: var(--yq-blue-5)
}

.styles-module_sharePanel_Sxg7e .styles-module_inviteIcon_\+3x-U {
    padding: 6px;
    color: var(--yq-yuque-grey-9);
    background: var(--yq-bg-pre-secondary);
    border-radius: 50%;
    border: .5px solid var(--yq-yuque-grey-3);
    cursor: pointer
}

html[data-kumuhana=pouli] .styles-module_sharePanel_Sxg7e .styles-module_inviteIcon_\+3x-U {
    background: none;
    border-color: var(--yq-sheetborder)
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkSet_k942E {
    width: 380px;
    padding: 0 12px 4px 12px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkSet_k942E+.styles-module_description_j9YFu {
    margin-top: 16px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkShow_bMh8i {
    margin-right: 12px;
    height: 32px;
    line-height: 20px;
    padding: 12px;
    color: var(--yq-text-disable);
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    overflow: hidden;
    width: 272px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkShow_bMh8i.styles-module_qrCode_suE8x {
    width: 228px;
    margin-right: 8px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareButton_L93gj {
    padding: 5px 11px;
    font-weight: 500;
    color: var(--yq-yuque-grey-9);
    border-color: var(--yq-border-primary);
    border-radius: 6px;
    font-size: 14px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareButton_L93gj.styles-module_sharePublic_495Qd {
    padding: 5px 15px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareQrCode_xBxWb {
    padding: 7px 6px;
    margin-left: 8px;
    color: var(--yq-icon-primary);
    border-color: var(--yq-border-primary);
    border-radius: 6px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareQrCodeIcon_uR90f {
    color: var(--yq-icon-primary)
}

.styles-module_sharePanel_Sxg7e .styles-module_moreConfigs_qgWYG {
    margin-top: 16px;
    padding-top: 16px;
    background-color: var(--yq-yuque-grey-1);
    border-radius: 8px
}

html[data-kumuhana=pouli] .styles-module_sharePanel_Sxg7e .styles-module_moreConfigs_qgWYG {
    background-color: var(--yq-yuque-grey-3)
}

.styles-module_sharePanel_Sxg7e .styles-module_loadingIcon_pQzuM {
    margin: 0 7px
}

.styles-module_popover_9iY7y .ant-popover-inner {
    border-radius: 6px!important
}

.styles-module_popover_9iY7y .ant-popover-inner-content {
    padding: 0!important
}

.larkui-member-selector-invite-reset-icon {
    margin-right: 5px
}

.larkui-member-selector-batch-batchTip .tip {
    margin-bottom: 35px;
    color: var(--yq-text-primary);
    text-align: center;
    font-size: 16px;
    line-height: 24px
}

.ant-table.ant-table-middle {
    font-size: 14px
}

.ant-table.ant-table-middle .ant-table-footer,.ant-table.ant-table-middle .ant-table-tbody>tr>td,.ant-table.ant-table-middle .ant-table-thead>tr>th,.ant-table.ant-table-middle .ant-table-title,.ant-table.ant-table-middle tfoot>tr>td,.ant-table.ant-table-middle tfoot>tr>th {
    padding: 12px 8px
}

.ant-table.ant-table-middle .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-middle .ant-table-expanded-row-fixed {
    margin: -12px -8px
}

.ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -12px -8px -12px 25px
}

.ant-table.ant-table-small {
    font-size: 14px
}

.ant-table.ant-table-small .ant-table-footer,.ant-table.ant-table-small .ant-table-tbody>tr>td,.ant-table.ant-table-small .ant-table-thead>tr>th,.ant-table.ant-table-small .ant-table-title,.ant-table.ant-table-small tfoot>tr>td,.ant-table.ant-table-small tfoot>tr>th {
    padding: 8px 8px
}

.ant-table.ant-table-small .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-small .ant-table-expanded-row-fixed {
    margin: -8px -8px
}

.ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -8px -8px -8px 25px
}

.ant-table-small .ant-table-thead>tr>th {
    background-color: var(--yq-ant-table-header-bg)
}

.ant-table-small .ant-table-selection-column {
    width: 46px;
    min-width: 46px
}

.ant-table.ant-table-bordered>.ant-table-container,.ant-table.ant-table-bordered>.ant-table-title {
    border: 1px solid var(--yq-ant-border-color-split);
    border-bottom: 0
}

.ant-table.ant-table-bordered>.ant-table-container {
    border-right: 0
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr:not(:last-child)>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th:before {
    background-color: transparent!important
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>.ant-table-cell-fix-right-first:after {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -16px -17px
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed:after {
    position: absolute;
    top: 0;
    right: 1px;
    bottom: 0;
    border-right: 1px solid var(--yq-ant-border-color-split);
    content: ""
}

.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-expanded-row>td,.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-placeholder>td {
    border-right: 0
}

.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -12px -9px
}

.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -8px -9px
}

.ant-table.ant-table-bordered>.ant-table-footer {
    border: 1px solid var(--yq-ant-border-color-split);
    border-top: 0
}

.ant-table-cell .ant-table-container:first-child {
    border-top: 0
}

.ant-table-cell-scrollbar {
    box-shadow: 0 1px 0 1px var(--yq-ant-table-header-bg)
}

.ant-table-wrapper {
    clear: both;
    max-width: 100%
}

.ant-table-wrapper:before {
    display: table;
    content: ""
}

.ant-table-wrapper:after {
    display: table;
    clear: both;
    content: ""
}

.ant-table {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    font-size: 14px;
    background: var(--yq-ant-component-background);
    border-radius: 6px
}

.ant-table table {
    width: 100%;
    text-align: left;
    border-radius: 6px 6px 0 0;
    border-collapse: separate;
    border-spacing: 0
}

.ant-table-tbody>tr>td,.ant-table-thead>tr>th,.ant-table tfoot>tr>td,.ant-table tfoot>tr>th {
    position: relative;
    padding: 16px 16px;
    word-wrap: break-word
}

.ant-table-cell-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first {
    overflow: visible
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last .ant-table-cell-content,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first .ant-table-cell-content {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis
}

.ant-table-title {
    padding: 16px 16px
}

.ant-table-footer {
    padding: 16px 16px;
    color: var(--yq-ant-heading-color);
    background: var(--yq-ant-background-color-light)
}

.ant-table-thead>tr>th {
    position: relative;
    color: var(--yq-ant-heading-color);
    font-weight: 500;
    text-align: left;
    background: var(--yq-ant-table-header-bg);
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s ease
}

.ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: var(--yq-ant-table-header-cell-split-color);
    transform: translateY(-50%);
    transition: background-color .3s;
    content: ""
}

.ant-table-thead>tr:not(:last-child)>th[colspan] {
    border-bottom: 0
}

.ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table {
    margin: -16px -16px -16px 33px
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td {
    border-bottom: 0
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child {
    border-radius: 0
}

.ant-table-tbody>tr.ant-table-row:hover>td {
    background: var(--yq-ant-table-row-hover-bg)
}

.ant-table-tbody>tr.ant-table-row-selected>td {
    background: var(--yq-yuque-green-100);
    border-color: rgba(0,0,0,.03)
}

.ant-table-tbody>tr.ant-table-row-selected:hover>td {
    background: var(--yq-yuque-grey-300)
}

.ant-table-summary {
    background: var(--yq-ant-component-background)
}

div.ant-table-summary {
    box-shadow: 0 -1px 0 var(--yq-ant-border-color-split)
}

.ant-table-summary>tr>td,.ant-table-summary>tr>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-pagination.ant-pagination {
    margin: 16px 0
}

.ant-table-pagination {
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px
}

.ant-table-pagination>* {
    flex: none
}

.ant-table-pagination-left {
    justify-content: flex-start
}

.ant-table-pagination-center {
    justify-content: center
}

.ant-table-pagination-right {
    justify-content: flex-end
}

.ant-table-thead th.ant-table-column-has-sorters {
    cursor: pointer;
    transition: all .3s
}

.ant-table-thead th.ant-table-column-has-sorters:hover {
    background: var(--yq-ant-table-header-sort-active-bg)
}

.ant-table-thead th.ant-table-column-has-sorters:hover:before {
    background-color: transparent!important
}

.ant-table-thead th.ant-table-column-sort {
    background: var(--yq-ant-table-header-sort-bg)
}

.ant-table-thead th.ant-table-column-sort:before {
    background-color: transparent!important
}

td.ant-table-column-sort {
    background: var(--yq-ant-table-body-sort-bg)
}

.ant-table-column-sorters {
    display: flex;
    flex: auto;
    align-items: center;
    justify-content: space-between
}

.ant-table-column-sorters:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: ""
}

.ant-table-column-sorter {
    color: #bfbfbf;
    font-size: 0;
    transition: color .3s
}

.ant-table-column-sorter-inner {
    display: inline-flex;
    flex-direction: column;
    align-items: center
}

.ant-table-column-sorter-down,.ant-table-column-sorter-up {
    font-size: 11px
}

.ant-table-column-sorter-down.active,.ant-table-column-sorter-up.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-column-sorter-up+.ant-table-column-sorter-down {
    margin-top: -.3em
}

.ant-table-column-sorters:hover .ant-table-column-sorter {
    color: #a5a5a5
}

.ant-table-filter-column {
    display: flex;
    justify-content: space-between
}

.ant-table-filter-trigger {
    position: relative;
    display: flex;
    align-items: center;
    margin: -4px -8px -4px 4px;
    padding: 0 4px;
    color: #bfbfbf;
    font-size: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all .3s
}

.ant-table-filter-trigger:hover {
    color: var(--yq-ant-text-color-secondary);
    background: var(--yq-ant-table-header-filter-active-bg)
}

.ant-table-filter-trigger.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-filter-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    min-width: 120px;
    background-color: var(--yq-ant-table-filter-dropdown-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-table-filter-dropdown .ant-dropdown-menu {
    max-height: 264px;
    overflow-x: hidden;
    border: 0;
    box-shadow: none
}

.ant-table-filter-dropdown-submenu>ul {
    max-height: calc(100vh - 130px);
    overflow-x: hidden;
    overflow-y: auto
}

.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-left: 8px
}

.ant-table-filter-dropdown-btns {
    display: flex;
    justify-content: space-between;
    padding: 7px 8px 7px 3px;
    overflow: hidden;
    background-color: var(--yq-ant-table-filter-btns-bg);
    border-top: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-selection-col {
    width: 32px
}

.ant-table-bordered .ant-table-selection-col {
    width: 50px
}

table tr td.ant-table-selection-column,table tr th.ant-table-selection-column {
    padding-right: 8px;
    padding-left: 8px;
    text-align: center
}

table tr td.ant-table-selection-column .ant-radio-wrapper,table tr th.ant-table-selection-column .ant-radio-wrapper {
    margin-right: 0
}

table tr th.ant-table-selection-column:after {
    background-color: transparent!important
}

.ant-table-selection {
    position: relative;
    display: inline-flex;
    flex-direction: column
}

.ant-table-selection-extra {
    position: absolute;
    top: 0;
    z-index: 1;
    cursor: pointer;
    transition: all .3s;
    margin-left: 100%;
    padding-left: 4px
}

.ant-table-selection-extra .anticon {
    color: #bfbfbf;
    font-size: 10px
}

.ant-table-selection-extra .anticon:hover {
    color: #a5a5a5
}

.ant-table-expand-icon-col {
    width: 48px
}

.ant-table-row-expand-icon-cell {
    text-align: center
}

.ant-table-row-indent {
    float: left;
    height: 1px
}

.ant-table-row-expand-icon {
    color: var(--yq-ant-link-color);
    -webkit-text-decoration: none;
    text-decoration: none;
    cursor: pointer;
    transition: color .3s;
    position: relative;
    display: inline-flex;
    float: left;
    box-sizing: border-box;
    width: 17px;
    height: 17px;
    padding: 0;
    color: inherit;
    line-height: 17px;
    background: var(--yq-ant-table-expand-icon-bg);
    border: 1px solid var(--yq-ant-border-color-split);
    border-radius: 6px;
    outline: none;
    transform: scale(.94117647);
    transition: all .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-table-row-expand-icon:active {
    color: var(--yq-ant-link-active-color)
}

.ant-table-row-expand-icon:active,.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    border-color: currentColor
}

.ant-table-row-expand-icon:after,.ant-table-row-expand-icon:before {
    position: absolute;
    background: currentColor;
    transition: transform .3s ease-out;
    content: ""
}

.ant-table-row-expand-icon:before {
    top: 7px;
    right: 3px;
    left: 3px;
    height: 1px
}

.ant-table-row-expand-icon:after {
    top: 3px;
    bottom: 3px;
    left: 7px;
    width: 1px;
    transform: rotate(90deg)
}

.ant-table-row-expand-icon-collapsed:before {
    transform: rotate(-180deg)
}

.ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

.ant-table-row-expand-icon-spaced {
    background: transparent;
    border: 0;
    visibility: hidden
}

.ant-table-row-expand-icon-spaced:after,.ant-table-row-expand-icon-spaced:before {
    display: none;
    content: none
}

.ant-table-row-indent+.ant-table-row-expand-icon {
    margin-top: 2.5005px;
    margin-right: 8px
}

tr.ant-table-expanded-row:hover>td,tr.ant-table-expanded-row>td {
    background: var(--yq-ant-table-expanded-row-bg)
}

tr.ant-table-expanded-row .ant-descriptions-view {
    display: flex
}

tr.ant-table-expanded-row .ant-descriptions-view table {
    flex: auto;
    width: auto
}

.ant-table .ant-table-expanded-row-fixed {
    position: relative;
    margin: -16px -16px;
    padding: 16px 16px
}

.ant-table-tbody>tr.ant-table-placeholder {
    text-align: center
}

.ant-table-empty .ant-table-tbody>tr.ant-table-placeholder {
    color: var(--yq-ant-disabled-color)
}

.ant-table-tbody>tr.ant-table-placeholder:hover>td {
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left,.ant-table-cell-fix-right {
    position: sticky!important;
    z-index: 2;
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left-first:after,.ant-table-cell-fix-left-last:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: -1px;
    width: 30px;
    transform: translateX(100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table-cell-fix-right-first:after,.ant-table-cell-fix-right-last:after {
    position: absolute;
    top: 0;
    bottom: -1px;
    left: 0;
    width: 30px;
    transform: translateX(-100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:after,.ant-table .ant-table-container:before {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    width: 30px;
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:before {
    left: 0
}

.ant-table .ant-table-container:after {
    right: 0
}

.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container {
    position: relative
}

.ant-table-ping-left .ant-table-cell-fix-left-first:after,.ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container:before {
    box-shadow: inset 10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-ping-left .ant-table-cell-fix-left-last:before {
    background-color: transparent!important
}

.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container {
    position: relative
}

.ant-table-ping-right .ant-table-cell-fix-right-first:after,.ant-table-ping-right .ant-table-cell-fix-right-last:after,.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container:after {
    box-shadow: inset -10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-sticky-holder,.ant-table-sticky-scroll {
    position: sticky;
    z-index: 3
}

.ant-table-sticky-scroll {
    bottom: 0;
    display: flex;
    align-items: center;
    background: var(--yq-ant-border-color-split);
    border-top: 1px solid var(--yq-ant-border-color-split);
    opacity: .6
}

.ant-table-sticky-scroll:hover {
    transform-origin: center bottom
}

.ant-table-sticky-scroll-bar {
    height: 8px;
    background-color: rgba(0,0,0,.35);
    border-radius: 4px
}

.ant-table-sticky-scroll-bar-active,.ant-table-sticky-scroll-bar:hover {
    background-color: rgba(0,0,0,.8)
}

@media (-ms-high-contrast:none) {
    .ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-right .ant-table-cell-fix-right-first:after {
        box-shadow: none!important
    }
}

.ant-table-title {
    border-radius: 6px 6px 0 0
}

.ant-table-title+.ant-table-container {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ant-table-title+.ant-table-container table>thead>tr:first-child th:first-child,.ant-table-title+.ant-table-container table>thead>tr:first-child th:last-child {
    border-radius: 0
}

.ant-table-container {
    border-top-right-radius: 6px
}

.ant-table-container,.ant-table-container table>thead>tr:first-child th:first-child {
    border-top-left-radius: 6px
}

.ant-table-container table>thead>tr:first-child th:last-child {
    border-top-right-radius: 6px
}

.ant-table-footer {
    border-radius: 0 0 6px 6px
}

.ant-table-rtl,.ant-table-wrapper-rtl {
    direction: rtl
}

.ant-table-wrapper-rtl .ant-table table {
    text-align: right
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th {
    text-align: right
}

.ant-table-tbody>tr .ant-table-wrapper:only-child .ant-table.ant-table-rtl {
    margin: -16px 33px -16px -16px
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-left {
    justify-content: flex-end
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-right {
    justify-content: flex-start
}

.ant-table-wrapper-rtl .ant-table-column-sorter {
    margin-right: 8px;
    margin-left: 0
}

.ant-table-wrapper-rtl .ant-table-filter-column-title {
    padding: 16px 16px 16px 2.3em
}

.ant-table-rtl .ant-table-thead tr th.ant-table-column-has-sorters .ant-table-filter-column-title {
    padding: 0 0 0 2.3em
}

.ant-table-wrapper-rtl .ant-table-filter-trigger-container {
    right: auto;
    left: 0
}

.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-right: 8px;
    padding-left: 0
}

.ant-table-wrapper-rtl .ant-table-selection {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon,.ant-table-wrapper-rtl .ant-table-row-indent {
    float: right
}

.ant-table-wrapper-rtl .ant-table-row-indent+.ant-table-row-expand-icon {
    margin-right: 0;
    margin-left: 8px
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon:after {
    transform: rotate(-90deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:before {
    transform: rotate(180deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

.larkui-member-selector-batch-batchBox {
    padding: 24px
}

.larkui-member-selector-batch-batchBox p {
    margin-bottom: 18px;
    color: rgba(0,0,0,.85);
    font-weight: 600;
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox .ant-table-wrapper {
    overflow-y: scroll;
    max-height: 300px;
    width: 600px
}

.larkui-member-selector-batch-batchBox ::-webkit-scrollbar {
    display: none
}

.larkui-member-selector-batch-batchBox .desc {
    margin-bottom: 28px;
    color: rgba(0,0,0,.85);
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox .info {
    color: var(--yq-text-caption);
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox svg {
    display: block;
    margin: 0 auto
}

.larkui-member-selector-batch-batchBox .ant-upload {
    padding: 24px;
    border-radius: 8px;
    background-color: #f9fafa
}

.larkui-member-selector-batch-batchBox .ant-upload-hint {
    margin-top: 12px
}

.larkui-member-selector-batch-batchBox .footer {
    margin-top: 24px;
    text-align: right
}

.larkui-member-selector-batch-batchBox .footer .ant-btn {
    margin-right: 8px
}

.larkui-member-selector-batch-batchBox .footer .ant-btn:last-child {
    margin-right: 0
}

.larkui-member-selector-batch-batchBox .downloadBtn {
    margin-left: 20px
}

.larkui-member-selector-batch-batchBox .iconBtn {
    padding-right: 12px;
    padding-left: 12px
}

.larkui-member-selector-batch-batchBox .iconBtn>span:last-child {
    margin-left: 0
}

.larkui-member-selector-batch-batchBox .bodyContainer {
    padding: 16px 24px;
    min-height: 290px
}

.larkui-member-selector-batch-batchBox .uploading {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center
}

.larkui-member-selector-batch-batchBox .uploading>p {
    width: 100%;
    text-align: center
}

.larkui-member-selector-batch-batchBox .infoIcon {
    margin-bottom: 16px;
    font-size: 40px
}

.larkui-member-selector-batch-batchBox .infoIcon.success {
    color: var(--yq-ant-success-color)
}

.larkui-member-selector-batch-batchBox .infoIcon.warning {
    color: var(--yq-function-warning)
}

.larkui-member-selector-batch-batchBox .infoIcon.failed {
    color: var(--yq-function-error)
}

.larkui-member-selector-list .ant-list .ant-list-item {
    padding: 0;
    border-bottom: 0
}

.larkui-member-selector-list .ant-breadcrumb .ant-breadcrumb-link {
    font-weight: 400;
    cursor: pointer;
    color: var(--yq-text-primary)
}

.larkui-member-selector-list .ant-breadcrumb>span:last-child .ant-breadcrumb-link {
    font-weight: 500;
    color: var(--yq-black)
}

.larkui-member-selector-list-breadcrumb {
    margin-bottom: 16px;
    padding: 0 24px
}

.larkui-member-selector-list-breadcrumb .ant-breadcrumb {
    line-height: 2
}

.larkui-member-selector-list-item {
    padding-left: 24px;
    height: 42px;
    line-height: 42px;
    text-align: left;
    cursor: pointer;
    border-radius: 4px;
    color: var(--yq-text-primary);
    position: relative;
    padding-right: 70px;
    overflow: hidden;
    display: flex;
    align-items: center
}

.larkui-member-selector-list-item:hover {
    background: var(--yq-bg-secondary)
}

.larkui-member-selector-list-item-larkicon {
    color: var(--yq-text-caption);
    margin-right: 4px;
    vertical-align: middle
}

.larkui-member-selector-list-item-icon {
    color: var(--yq-text-caption)
}

.larkui-member-selector-list-item-name {
    color: var(--yq-text-primary);
    margin-left: 8px
}

.larkui-member-selector-list-item-action {
    position: absolute;
    right: 24px;
    top: 0;
    color: var(--yq-text-body);
    font-weight: 400
}

.larkui-member-selector-list-item-action .icon {
    display: inline-block;
    line-height: 42px;
    cursor: pointer;
    margin-left: 12px;
    transition: .2s ease-in-out
}

.larkui-member-selector-list-role-filter {
    position: absolute;
    top: 16px;
    right: 24px
}

.larkui-member-selector-list-select-dep {
    padding: 0 24px;
    margin-bottom: 18px;
    margin-top: 18px
}

.larkui-member-selector-list-select-dep-name {
    padding-left: inherit
}

.larkui-member-selector-list-select-all {
    padding: 0 24px;
    margin-bottom: 12px
}

.larkui-member-selector-list-user {
    padding: 3px 10px
}

.larkui-member-selector-list-icon {
    margin-right: 12px
}

.larkui-member-selector-list-name {
    margin-right: 16px
}

.larkui-member-selector-list-login {
    color: var(--yq-text-caption);
    font-size: 12px
}

.larkui-member-selector-list-title {
    align-items: baseline
}

.larkui-member-selector-list-member-type-switch-container {
    display: flex;
    align-items: center;
    color: var(--yq-text-body);
    font-size: 14px;
    padding: 15px 24px;
    border-top: solid 1px var(--yq-border-primary)
}

.larkui-member-selector-list-member-type-switch-container span {
    margin-left: 8px
}

.larkui-member-selector-list-member-switch {
    overflow: hidden
}

.larkui-member-selector-list-member-switch-inner {
    height: 345px;
    overflow: auto;
    scrollbar-width: none
}

.larkui-member-selector-list-member-switch-inner::-webkit-scrollbar {
    display: none
}

.larkui-member-selector-list-empty-view {
    text-align: center;
    color: var(--yq-yuque-grey-7)
}

.larkui-member-selector-list-empty-view img {
    padding-top: 48px;
    width: 80px;
    margin-bottom: 20px
}

.larkui-member-selector-import {
    padding: 24px
}

.larkui-member-selector-import-desc {
    margin-bottom: 16px
}

.DingTalkGroupSelector-module_groups_GrxWm .ant-list-item {
    padding: 8px 24px;
    height: 42px;
    line-height: 42px;
    text-align: left;
    cursor: pointer;
    border-radius: 4px;
    color: var(--yq-text-primary);
    position: relative;
    padding-right: 70px;
    overflow: hidden;
    display: flex;
    align-items: center;
    border-bottom: none
}

.DingTalkGroupSelector-module_groups_GrxWm .ant-list-item:hover {
    background: var(--yq-bg-secondary)
}

.DingTalkGroupSelector-module_checkbox_ryHx- {
    display: flex;
    align-items: center
}

.DingTalkGroupSelector-module_checkbox_ryHx- .ant-checkbox {
    position: relative;
    top: 0
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr {
    margin-left: 8px;
    display: flex;
    align-items: center
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr .DingTalkGroupSelector-module_avatar_7hoxt {
    display: inline-block;
    margin-right: 12px
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr .DingTalkGroupSelector-module_name_xw1v1 {
    display: inline-block;
    max-width: 210px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.larkui-member-selector-list-select-all .ant-checkbox-wrapper span:last-child {
    padding-left: 15px
}

.larkui-member-selector-list-user {
    display: flex;
    justify-content: center;
    align-items: center
}

.ant-modal-content .larkui-member-selector-list .ant-list {
    background-color: revert
}

.larkui-member-selector-list .ant-list .ant-list-item {
    padding: 8px 0!important
}

.larkui-member-selector-list .ant-list .ant-list-item:hover {
    background-color: var(--yq-bg-secondary)
}

.larkui-member-selector-list-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px!important;
    max-width: 120px
}

.larkui-member-selector-list-item {
    padding-right: 0!important;
    height: auto!important;
    line-height: normal!important
}

.larkui-member-selector-list .ant-checkbox-wrapper {
    align-items: center;
    display: flex
}

.larkui-member-selector-list .ant-checkbox-wrapper .ant-checkbox {
    top: auto
}

.larkui-member-selector-list-title {
    display: flex
}

.larkui-member-selector-list-login {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100px
}

.larkui-member-selector-list-item-action {
    top: 5px!important
}

.OrgSearchNotFound-module_container_MlZtJ {
    width: 320px;
    height: 200px;
    text-align: center
}

.OrgSearchNotFound-module_icon_oJoZx {
    margin-top: 65px
}

.OrgSearchNotFound-module_content_CgmVs {
    font-size: 14px;
    margin-top: 15px;
    color: var(--yq-text-caption)
}

.OrgSearchNotFound-module_goToContacts_mFsnK {
    display: inline-flex;
    align-items: center;
    color: var(--yq-yuque-grey-7)
}

.index-module_modal_pcaUs .ant-modal-content {
    overflow: hidden
}

.index-module_modal_pcaUs .ant-modal-body {
    padding: 0
}

.index-module_content_QzTkp {
    padding: 32px 24px 24px 24px;
    overflow: hidden
}

.index-module_content_QzTkp h1 {
    font-size: 18px;
    color: var(--yq-text-primary)
}

.index-module_content_QzTkp p {
    margin: 16px auto 32px auto;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_content_QzTkp .index-module_boldText_fg3cF {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_footer_RFKYC {
    display: flex;
    margin-top: 24px
}

.index-module_actionBtn_3gX8b {
    float: right
}

.index-module_actionBtnWrapper_NTfX9 {
    display: flex;
    align-items: center
}

.index-module_actionBtnWrapper_NTfX9 .index-module_errorTip_Z14UW {
    margin: 0 0 0 16px;
    color: var(--yq-function-error)
}

.OrgPreview-module_container_Z1Y1L {
    margin: 5px 20px 20px 20px;
    background-color: var(--yq-yuque-grey-1);
    border-radius: 6px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_titleLink_hfEOU {
    color: var(--yq-text-primary)
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_titleLink_hfEOU:hover {
    color: var(--yq-text-body)
}

.OrgPreview-module_container_Z1Y1L a {
    color: var(--yq-yuque-grey-8)
}

.OrgPreview-module_container_Z1Y1L a:hover {
    color: var(--yq-yuque-grey-9)
}

.OrgPreview-module_container_Z1Y1L .ant-card-body {
    border: 0;
    padding: 10px 17px
}

.OrgPreview-module_container_Z1Y1L .ant-card .ant-card-body {
    background: var(--yq-bg-secondary);
    border-radius: 6px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_header_4Q4Yw {
    border-style: dashed;
    border-color: var(--yq-yuque-grey-5);
    border-width: 0;
    border-bottom-width: 1px;
    padding-bottom: 16px;
    margin-bottom: 8px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_titleRow_p1nDH {
    display: flex;
    margin-bottom: 8px;
    flex-wrap: wrap;
    line-height: 2
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_titleRow_p1nDH .OrgPreview-module_title_pASRb {
    max-width: 132px;
    color: var(--yq-text-primary);
    font-weight: 700;
    font-size: 14px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 2px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_titleRow_p1nDH .OrgPreview-module_version_BKzn6 {
    margin-right: 4px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_usage_i2Dfu .OrgPreview-module_totalUsage_AYod6 {
    margin-right: 10px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_usage_i2Dfu .OrgPreview-module_trafficExchange_JmDGN {
    display: inline-block
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_expireTip_47wKl {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_members_2XXzV {
    margin-top: 8px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_members_2XXzV .OrgPreview-module_membersTip_248Wp {
    margin-right: 10px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_members_2XXzV a {
    position: relative;
    top: 6px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_membersCont_bHrcp {
    display: flex;
    justify-content: space-between;
    width: 132px;
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 22px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_membersCont_bHrcp span {
    display: inline-block
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_membersCont_bHrcp b {
    color: var(--yq-black);
    font-weight: 500
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_usageBarItem_ipU84 {
    width: 132px;
    height: 2px;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 2px;
    margin-top: -5px;
    margin-bottom: 12px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_usageBar_RimBB {
    height: 2px;
    border-radius: 2px;
    background-image: linear-gradient(90deg,var(--yq-yuque-grey-7),var(--yq-yuque-grey-7))
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerList_0f-ev {
    position: relative;
    flex-wrap: wrap;
    z-index: 1;
    margin-left: 12px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerList_0f-ev span {
    margin-right: 4px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerList_0f-ev .img {
    border: 1px solid var(--yq-border-primary)
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerWrapper_chW91 .OrgPreview-module_orgManage_nouoD {
    margin-top: 16px;
    padding-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerWrapper_chW91 .OrgPreview-module_orgManage_nouoD:hover {
    color: var(--yq-yuque-green-7)!important
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_ownerWrapper_chW91 .OrgPreview-module_orgManage_nouoD .OrgPreview-module_settingIcon_RPpMH {
    margin-right: 8px
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_wrapper_pl9jP {
    display: flex
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_moreOwner_nKdeY {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin: 0;
    position: absolute;
    top: 3px;
    border-radius: 50%
}

.OrgPreview-module_container_Z1Y1L .OrgPreview-module_moreOwnerNum_VzpfU {
    width: 18px;
    height: 18px;
    display: block;
    position: absolute;
    z-index: 998;
    font-size: 12px;
    text-align: center;
    border-radius: 50%;
    background: var(--yq-white);
    color: var(--yq-yuque-grey-8);
    line-height: 9px;
    cursor: pointer;
    border: 1px solid var(--yq-yuque-grey-4)
}

.OrgPreview-module_ownerTitle_4jGqP {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 24px
}

.OrgPreview-module_allAwnerWarp_0jZVW span {
    margin: 0 3px
}

.OrgPreview-module_allAwnerWarp_0jZVW p {
    font-size: 14px;
    color: var(--yq-yuque-grey-8);
    margin-bottom: 4px
}

.index-module_ellipsis-text_yJMGK {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_userMenu_mAS13 {
    padding-top: 4px;
    display: flex;
    flex-direction: column
}

.index-module_userMenu_mAS13 .index-module_menuItem_of\+Lq {
    padding: 0 16px;
    flex: 0 0 auto;
    height: auto
}

.index-module_userMenu_mAS13 .index-module_menuDivider_vBChm {
    margin: 6px 16px!important;
    background-color: var(--yq-border-light)
}

.index-module_userMenu_mAS13 .index-module_newOrgItem_5fzW9,.index-module_userMenu_mAS13 .index-module_orgListItem_5450y,.index-module_userMenu_mAS13 .index-module_userInfoMenuItem_B3mlE {
    padding: 0
}

.index-module_userMenu_mAS13 .index-module_newOrgItem_5fzW9:hover,.index-module_userMenu_mAS13 .index-module_orgListItem_5450y:hover,.index-module_userMenu_mAS13 .index-module_userInfoMenuItem_B3mlE:hover {
    background-color: transparent!important
}

.index-module_userMenu_mAS13 .index-module_orgPreviewMenuItem_-JvgX {
    padding: 0
}

.index-module_userMenu_mAS13 .index-module_orgPreviewMenuItem_-JvgX:hover {
    background-color: transparent!important
}

.index-module_userMenu_mAS13 .index-module_orgPreviewMenuItem_-JvgX .index-module_orgPreviewDivider_GpVcy {
    margin: 16px 0 0 0
}

.index-module_userMenu_mAS13 .index-module_orgListItem_5450y {
    flex-shrink: 1
}

.index-module_userMenu_mAS13 .index-module_orgListItem_5450y::-webkit-scrollbar {
    visibility: hidden;
    width: 6px;
    height: 6px;
    background-color: transparent
}

.index-module_userMenu_mAS13 .index-module_orgListItem_5450y::-webkit-scrollbar-track {
    border-radius: 6px;
    background-color: var(--yq-bg-secondary)
}

.index-module_userMenu_mAS13 .index-module_orgListItem_5450y::-webkit-scrollbar-thumb {
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 6px
}

.index-module_userMenu_mAS13 .index-module_orgListItem_5450y::-webkit-scrollbar-thumb:hover {
    background-color: var(--yq-yuque-grey-5)
}

.index-module_userMenu_mAS13 .index-module_userInfoMenuItem_B3mlE .index-module_userItemWrapper_LtScn {
    margin-bottom: 8px
}

.index-module_userMenu_mAS13 .index-module_orgPreviewContainer_8oTEL {
    margin: 0
}

.index-module_userInfoAvatar_D5E2q {
    display: flex
}

.index-module_orgUserInfoAvatar_dN5mv {
    display: flex;
    margin-top: 14px;
    margin-bottom: 14px
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU {
    display: flex;
    flex-direction: column;
    flex: 0 1 auto;
    margin-left: 12px;
    width: 100%;
    overflow: hidden
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU .index-module_orgTitle_myAbM {
    flex: 1;
    overflow: hidden;
    display: flex
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU .index-module_orgTitle_myAbM .index-module_orgName_B-8Tu {
    flex: 0 1 auto;
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
    color: var(--yq-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU .index-module_orgTitle_myAbM .index-module_orgIcons_CIrl3 {
    display: flex;
    align-items: center;
    flex: 0 0 auto
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU .index-module_orgTitle_myAbM .index-module_orgIcons_CIrl3 svg {
    margin-left: 6px;
    height: 18px!important
}

.index-module_orgUserInfoAvatar_dN5mv .index-module_orgInfoWrapper_0V0CU .index-module_orgDesc_5bO3- {
    font-size: 12px;
    line-height: 20px;
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_orgListWarp_OUdwf {
    padding: 4px 0
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w {
    display: flex
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemPic_aoZ-Z {
    width: 32px;
    height: 40px;
    display: flex;
    align-items: center
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemPic_aoZ-Z img {
    height: 32px;
    width: 32px
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemDesc_RKM3z {
    margin-left: 14px;
    width: 168px;
    overflow: hidden;
    flex: 1;
    display: flex;
    justify-content: center;
    flex-direction: column
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemDesc_RKM3z .index-module_name_jMswI {
    display: block;
    line-height: 20px;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemDesc_RKM3z .index-module_desc_bOxEB {
    font-size: 10px;
    line-height: 14px;
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.index-module_orgListWarp_OUdwf .index-module_orgItemWarp_zbj4w .index-module_orgItemSelectedIcon_zyD7Q {
    margin-left: 10px;
    flex-shrink: 0;
    flex-basis: 20px
}

.index-module_disableMenuItemWrapper_dz9np {
    cursor: not-allowed
}

.index-module_menuItemWrapper_NVGN2 {
    padding: 6px 8px;
    border-radius: 6px
}

.index-module_menuItemWrapper_NVGN2:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_newOrgWrapper_VCVNL {
    display: flex;
    align-items: center;
    margin-top: 4px
}

.index-module_newOrgWrapper_VCVNL .index-module_text_1Itv9 {
    margin-left: 14px;
    font-size: 14px;
    line-height: 20px;
    color: var(--yq-text-primary)
}

.index-module_newOrgWrapper_VCVNL .index-module_addContent_6rrez {
    height: 32px;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 8px;
    padding: 0 7px;
    background-color: var(--yq-white);
    line-height: 32px;
    text-align: center;
    cursor: pointer
}

.index-module_newOrgWrapper_VCVNL .index-module_addContent_6rrez:hover {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_newOrgWrapper_VCVNL .index-module_addContent_6rrez svg {
    margin-top: 7px;
    color: var(--yq-yuque-grey-9)
}

.index-module_userInfoDesc_iq30m {
    margin-left: 12px
}

.index-module_disableLink_1Pgkx {
    margin-left: 4px;
    color: var(--yq-text-link)
}

.index-module_menuGroup_F07Eb h4 {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 12px;
    line-height: 17px;
    color: var(--yq-text-caption)
}

.index-module_menuGroup_F07Eb:nth-of-type(2) h4 {
    margin-top: 20px
}

.index-module_organizationItems_f1bLt {
    min-height: 52px;
    overflow: auto
}

.index-module_organizationItems_f1bLt::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
    scrollbar-width: thin;
    scrollbar-color: transparent
}

html[data-kumuhana=pouli] .index-module_organizationItems_f1bLt::-webkit-scrollbar {
    background-color: transparent
}

.index-module_organizationItems_f1bLt:hover::-webkit-scrollbar-thumb {
    background: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_personal_0ZY2c .index-module_organizationItems_f1bLt {
    max-height: calc(100vh - 250px)
}

.index-module_organization_LhMB7 .index-module_organizationItems_f1bLt {
    max-height: calc(100vh - 500px)
}

.index-module_userLogo_gmHJP {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center
}

.index-module_yuqueLogo_uRxkO {
    min-width: 105px
}

.index-module_userLogoCollapsed_39Pi7 {
    width: 34px;
    overflow: hidden;
    display: block;
    margin-left: 1px;
    margin-top: 4px
}

.index-module_userLogoWrapper_P2JVc {
    cursor: pointer;
    flex: 1 1 auto;
    overflow: hidden
}

.index-module_userNotCollapseLogWrapper_FfZva {
    padding-right: 24px;
    position: relative;
    flex-grow: 0;
    overflow: hidden
}

.index-module_userNotCollapseLogWrapper_FfZva .index-module_arrowDownWrapper_bR5Pp {
    cursor: pointer;
    right: 4px
}

.index-module_arrowDownWrapper_bR5Pp {
    position: absolute;
    right: 16px;
    top: 1px;
    display: flex;
    width: 56px;
    height: 100%;
    align-items: center;
    background-color: transparent;
    flex-direction: row-reverse
}

.index-module_arrowDownWrapper_bR5Pp .index-module_arrowDown_cHuVb {
    width: 18px;
    height: 18px;
    border-radius: 4px;
    color: var(--yq-icon-caption);
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_arrowDownWrapper_bR5Pp:hover .index-module_arrowDown_cHuVb {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_orgArrowDownWrapper_Xfv8X {
    width: calc(100% - 36px);
    min-width: 56px
}

.index-module_logo_tHEfk {
    display: block
}

.index-module_switchOrgPopover_RPqHg {
    width: 312px;
    border-radius: 8px;
    z-index: 1012
}

.index-module_switchOrgPopover_RPqHg .ant-popover-inner-content {
    padding: 0 20px 12px 20px
}

a.org-logo>span {
    font-size: 14px;
    margin-left: 6px
}

.index-module_switchModal_zuGUP {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
    background: var(--yq-bg-secondary);
    z-index: 1051;
    padding: 100px 32px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_syncError_rcxGl {
    position: absolute;
    left: 4px;
    top: 4px
}

.index-module_syncErrorWrapper_GaB-J {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--yq-white);
    position: absolute;
    top: 18px;
    right: 7px
}

.index-module_popMenu_aOUBY {
    border-radius: 12px 12px 0 0
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j {
    background-color: var(--yq-bg-tertiary);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    color: var(--yq-text-primary);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_item_-0ClC {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_cancel_i36-H {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 8px
}

.NotificationItem-module_content_XeuM0 {
    margin-left: 12px;
    font-size: 14px;
    word-wrap: break-word;
    color: var(--yq-text-body);
    overflow: hidden;
    width: 100%
}

.NotificationItem-module_content_XeuM0>time {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.NotificationItem-module_content_XeuM0>p {
    margin-bottom: 0;
    max-width: 534px
}

.NotificationItem-module_content_XeuM0>p .ant-btn {
    position: absolute;
    right: 16px;
    top: 20px
}

.NotificationItem-module_mobile_77e-m {
    color: var(--yq-text-caption)
}

.NotificationItem-module_item_T8nNI {
    display: flex;
    align-items: top;
    background-color: var(--yq-bg-primary);
    transition: background-color .3s ease;
    border-top: 1px solid var(--yq-border-light);
    word-break: break-word;
    position: relative;
    padding: 16px 12px
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m {
    padding: 12px;
    display: flex
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m .NotificationItem-module_content_XeuM0 {
    margin-left: 13px;
    font-size: 15px;
    line-height: 21px
}

.NotificationItem-module_item_T8nNI.NotificationItem-module_mobile_77e-m .NotificationItem-module_content_XeuM0>time {
    font-size: 13px;
    line-height: 18px;
    color: var(--yq-text-caption)
}

.NotificationItem-module_item_T8nNI:hover {
    background-color: var(--yq-bg-secondary)
}

.notification-list .ant-list-item:first-child .NotificationItem-module_item_T8nNI {
    border-top: none
}

.NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0,a:visited .NotificationItem-module_content_XeuM0 {
    color: var(--yq-text-caption)
}

.NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0 .NotificationItem-module_actor_n5\+y-,.NotificationItem-module_itemRead_hJQML .NotificationItem-module_content_XeuM0 .NotificationItem-module_subject_DpAt3,a:visited .NotificationItem-module_content_XeuM0 .NotificationItem-module_actor_n5\+y-,a:visited .NotificationItem-module_content_XeuM0 .NotificationItem-module_subject_DpAt3 {
    color: var(--yq-text-body)
}

.NotificationItem-module_itemDeleted_d2M2z .NotificationItem-module_content_XeuM0 {
    color: var(--yq-text-caption)
}

.NotificationItem-module_itemSystem_5zHlV {
    overflow: hidden
}

.NotificationItem-module_gallary_Q6-bs {
    margin-top: 8px;
    display: flex
}

.NotificationItem-module_gallary_Q6-bs .NotificationItem-module_artboard_ln6q3 {
    margin-right: 8px;
    width: 144px;
    height: 104px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px
}

.NotificationItem-module_gallary_Q6-bs .NotificationItem-module_artboard_ln6q3>img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.NotificationItem-module_asset_Yvf7u {
    display: inline-block;
    border: 1px solid var(--yq-border-primary);
    padding: 10px 16px;
    border-radius: 4px
}

.NotificationItem-module_asset_Yvf7u .NotificationItem-module_title_9tpK8 {
    color: var(--yq-text-primary)
}

.NotificationItem-module_asset_Yvf7u .larkicon-svg-asset {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px
}

.NotificationItem-module_resource_IE846 {
    margin-top: 8px;
    display: flex;
    align-items: center
}

.NotificationItem-module_resource_IE846>svg {
    margin-right: 8px
}

.NotificationItem-module_listAvatar_i95DX {
    width: 32px;
    min-width: 32px;
    height: 32px;
    border-radius: 16px
}

.NotificationItem-module_transferGroupList_oc7BE {
    display: flex
}

.NotificationItem-module_transferGroupList_oc7BE .NotificationItem-module_group_nPupO {
    margin-right: 16px;
    color: var(--yq-text-primary)
}

.NotificationItem-module_transferGroupList_oc7BE .NotificationItem-module_groupName_8YrGV {
    margin-left: 4px
}

.context-module_actor_n76K5 {
    color: var(--yq-text-primary);
    font-weight: 600
}

.context-module_actor_n76K5:hover {
    color: var(--yq-text-body)
}

.context-module_subject_0TOZC {
    color: var(--yq-text-primary);
    font-weight: 400
}

.context-module_subject_0TOZC:hover {
    color: var(--yq-text-body)
}

.context-module_actions_b-TCi {
    margin-left: 16px;
    float: right;
    background: none;
    margin-top: 5px
}

.context-module_upgrade_odBAm {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 8px
}

.m-notifications .context-module_actions_b-TCi {
    display: none
}

.ant-popover-message-title {
    max-width: 200px
}

.notifications-header-button {
    margin-left: 10px;
    line-height: 50px
}

.notifications-header-button-disable {
    color: var(--yq-text-caption);
    cursor: default
}

.notification-list .blankslate {
    padding: 32px 16px;
    text-align: center;
    color: var(--yq-text-disable)
}

.notification-list .blankslate-icon {
    margin: 0 auto 10px;
    width: 63px;
    height: 60px;
    background: var(--yq-white) url(https://gw.alipayobjects.com/zos/rmsportal/jWbuyWBKeJIBgCAmjFzS.svg) no-repeat 50%;
    background-size: 100%
}

.notification-list.ant-list.larkui-list .ant-list-item {
    padding: 0;
    border: none
}

.notification-list.ant-list.larkui-list .ant-list-item:last-child {
    border-bottom: none
}

.system-list time {
    display: inline-block;
    margin-top: 8px
}

.card-notifications .ant-card-head .ant-card-extra {
    padding: 0
}

.card-notifications .ant-card-head-title .num,.card-notifications .ant-card-head-title .text {
    display: inline-block;
    vertical-align: middle
}

.card-notifications .ant-card-head-title .text {
    color: var(--yq-text-caption)
}

.card-notifications .ant-card-head-title .text:hover {
    color: var(--yq-text-body)
}

.card-notifications .ant-card-head-title .text.active {
    color: var(--yq-text-primary)
}

.card-notifications .ant-card-head-title .readed {
    margin-left: 24px
}

.card-notifications .ant-card-head-title .num {
    border-radius: 10px;
    margin-left: 8px;
    padding: 2px 8px;
    background-color: var(--yq-bg-tertiary);
    font-size: 12px;
    line-height: 1.2;
    font-weight: 400;
    color: var(--yq-text-body);
    min-width: 24px;
    text-align: center
}

.mobile {
    border-top: none
}

.mobile,.mobile .ant-card-head-title {
    color: var(--yq-text-body);
    font-size: 13px;
    line-height: 18px
}

.mobile .ant-card-head-title {
    padding: 16px 0 4px 0;
    font-weight: 400
}

.mobile .ant-card-head {
    border-bottom: none;
    min-height: auto
}

.mobile .ant-card .ant-card-head-title {
    font-size: 14px;
    line-height: 32px;
    color: var(--yq-text-body)
}

.Notifications-module_header_STB3- {
    background-color: var(--yq-bg-primary);
    padding: 8px 12px;
    color: var(--yq-text-body);
    font-size: 13px;
    line-height: 18px;
    display: flex;
    justify-content: space-between
}

.Notifications-module_header_STB3- .Notifications-module_num_Gsr4A {
    margin-right: 4px
}

.Notifications-module_header_STB3- .Notifications-module_action_JP\+m1 {
    line-height: 21px;
    color: var(--yq-text-primary);
    font-size: 15px;
    min-height: 21px
}

.Notifications-module_list_qCvTE,.Notifications-module_list_qCvTE .Notifications-module_ant-list_Z9sHe {
    background-color: var(--yq-bg-primary)
}

.Notifications-module_popMenu_kxa\+3 .Notifications-module_tips_PBC5H {
    color: var(--yq-text-caption)
}

.Notifications-module_emptyWrapper_OnwXY {
    height: 250px;
    margin-top: 100px
}

.Notifications-module_emptyWrapper_OnwXY p {
    line-height: 20px
}

.Notifications-module_extraLinkWrapper_CV4OS {
    margin-right: 44px
}

.Notifications-module_extraLinkWrapper_CV4OS a {
    color: var(--yq-yuque-grey-8)
}

.Notifications-module_extraLinkWrapper_CV4OS a:hover {
    color: var(--yq-yuque-grey-9)
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM {
    cursor: not-allowed;
    color: var(--yq-text-disable)
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM:hover {
    color: var(--yq-text-disable)
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_disabledButton_5uDDM .Notifications-module_iconActionRead_55WNa {
    opacity: .4
}

.Notifications-module_extraLinkWrapper_CV4OS svg {
    margin-right: 4px;
    position: relative
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_iconActionDelete_PVcOx {
    margin-right: 2px
}

.Notifications-module_extraLinkWrapper_CV4OS .Notifications-module_iconActionRead_55WNa {
    margin-right: -1px
}

.Notifications-module_setting_qVm6t {
    margin-left: 12px;
    color: var(--yq-text-body)
}

.Notifications-module_setting_qVm6t .Notifications-module_settingIcon_dLIc4 {
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    top: -2px
}

.Notifications-module_mButton_Ai9DS {
    font-size: 14px
}

.Notifications-module_mHeaderReaded_eUdT1,.Notifications-module_mHeaderUnread_SsQUk {
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    background: var(--yq-bg-tertiary);
    color: var(--yq-text-body);
    padding: 4px 10px;
    font-size: 14px
}

.Notifications-module_mHeaderReaded_eUdT1 .text,.Notifications-module_mHeaderUnread_SsQUk .text {
    color: var(--yq-text-body)
}

.Notifications-module_mHeaderUnread_SsQUk {
    margin-right: 6px
}

.Notifications-module_mHeaderUnread_SsQUk .num {
    color: var(--yq-text-body);
    display: inline-block;
    margin-left: 4px
}

.Notifications-module_mHeaderActive_nyNrW {
    color: var(--yq-text-link);
    background: var(--yq-bg-tertiary)
}

.Notifications-module_mHeaderActive_nyNrW .text {
    color: var(--yq-text-link)
}

.Notifications-module_webappChannel_ZA4Sr {
    padding-top: 15px
}

.Notifications-module_selectType_sfCA8 {
    color: var(--yq-yuque-grey-8);
    cursor: pointer
}

.Notifications-module_selectType_sfCA8.Notifications-module_active_Azaid {
    font-weight: 600;
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.Notifications-module_leftSelect_j4iaj {
    margin-right: 19px
}

.card-notifications .ant-card-head {
    padding: 0;
    border: 0
}

.card-notifications .ant-card-body .ant-list {
    max-height: 558px;
    height: 558px;
    padding-right: 18px;
    border-radius: 0
}

.card-notifications .ant-card-body .ant-list .ant-list-items {
    width: 724px
}

.lark-nav {
    border: 1px solid var(--yq-border-light)
}

.lark-nav.ant-menu-vertical>.ant-menu-item {
    margin-left: 0;
    padding-left: 0;
    border-bottom: 1px solid var(--yq-border-light);
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-900);
    margin-bottom: 0
}

.lark-nav.ant-menu-vertical>.ant-menu-item:last-child {
    border-bottom: 0
}

.lark-nav.ant-menu-vertical>.ant-menu-item>a {
    display: block;
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-900);
    padding-left: 24px;
    border-left: 2px solid transparent
}

.lark-nav.ant-menu-vertical>.ant-menu-item>a:hover {
    color: var(--yq-ant-text-color-secondary)
}

.lark-nav.ant-menu-vertical>.ant-menu-item>a.active {
    border-left-color: var(--yq-yuque-green-600);
    font-weight: 500;
    color: var(--yq-ant-heading-color)
}

.lark-nav.ant-menu-vertical>.ant-menu-item>a.active:hover {
    color: var(--yq-ant-heading-color)
}

.lark-nav.ant-menu-vertical.ant-menu>.ant-menu-item-active,.lark-nav.ant-menu-vertical.ant-menu>.ant-menu-item-selected {
    background: transparent
}

.lark-nav.ant-menu-vertical>.ant-menu-item-selected>a {
    border-left-color: var(--yq-yuque-green-600);
    font-weight: 500;
    color: var(--yq-ant-heading-color)
}

.lark-nav.ant-menu-vertical>.ant-menu-item-selected>a:hover {
    color: var(--yq-ant-heading-color)
}

.lark-nav.ant-menu-horizontal {
    border: 0;
    margin-left: -10px;
    margin-right: -10px
}

.lark-nav.ant-menu-horizontal>.ant-menu-item {
    border-bottom: 0;
    padding: 0;
    margin: 0 10px
}

.lark-nav.ant-menu-horizontal>.ant-menu-item.ant-menu-item-active,.lark-nav.ant-menu-horizontal>.ant-menu-item:hover {
    border: 0
}

.lark-nav.ant-menu-horizontal>.ant-menu-item>a {
    display: block;
    padding: 0 16px;
    border: none;
    line-height: 48px;
    color: var(--yq-yuque-grey-900)
}

.lark-nav.ant-menu-horizontal>.ant-menu-item>a:hover {
    color: var(--yq-ant-text-color-secondary)
}

.lark-nav.ant-menu-horizontal>.ant-menu-item>a.active {
    font-weight: 500;
    border-bottom-color: var(--yq-yuque-grey-9);
    color: var(--yq-ant-heading-color)
}

.lark-nav.ant-menu-horizontal>.ant-menu-item-selected>a {
    border-bottom-color: var(--yq-yuque-grey-9)
}

@media only screen and (max-width: 575px) {
    .lark-nav.ant-menu-horizontal>.ant-menu-item {
        margin:0
    }
}

.Nav-module_nav_iFpRT {
    float: right;
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 10px;
    line-height: 21px
}

.Nav-module_dot_w2N16 {
    display: inline-block;
    vertical-align: middle;
    width: 5px;
    height: 5px;
    background-color: var(--yq-yuque-green-6);
    border-radius: 100%;
    box-shadow: 0 0 0 1px var(--yq-white)
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav:before {
    border-bottom: 0
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-content-left {
    border-radius: 10px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-content-holder>.ant-tabs-content>.ant-tabs-tabpane {
    padding-left: 0
}

.Nav-module_tabsWrapper_KmVeN .ant-card-head {
    padding-left: 26px;
    padding-right: 4px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-tab {
    padding: 0 8px;
    height: 32px;
    border-radius: 6px;
    min-width: 132px;
    margin-right: 12px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-tab.ant-tabs-tab-active,.Nav-module_tabsWrapper_KmVeN .ant-tabs-tab:hover {
    background-color: var(--yq-bg-primary-hover)
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-ink-bar {
    display: none
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav-list {
    margin-top: 64px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav-wrap .ant-tabs-nav-list .ant-tabs-tab+.ant-tabs-tab {
    margin: 8px 12px 0 0;
    padding: 8px 12px
}

.Nav-module_tabsWrapper_KmVeN .ant-tabs-nav {
    height: 610px
}

.Nav-module_notificationsWrapper_Uflnw {
    min-height: 400px
}

.Nav-module_tabTitle_d9DlL {
    display: flex;
    justify-content: space-between;
    width: 108px
}

.Nav-module_tabTitle_d9DlL span {
    display: block;
    cursor: pointer
}

.Nav-module_setting_M8Bx8 {
    margin-left: 12px;
    margin-bottom: 14px;
    position: absolute;
    bottom: 4px;
    color: var(--yq-yuque-grey-8)
}

.Nav-module_setting_M8Bx8 svg {
    font-size: 16px
}

.Nav-module_setting_M8Bx8:hover {
    color: var(--yq-yuque-grey-9)
}

.index-module_wrapper_pEEnH .ant-modal-body {
    padding: 0 0 0 12px
}

.index-module_wrapper_pEEnH .ant-modal-body .ant-tabs-content {
    max-height: 610px;
    overflow-y: auto
}

.index-module_wrapper_pEEnH .ant-modal-header {
    position: absolute
}

.index-module_wrapper_pEEnH .ant-modal-close-x {
    line-height: 51px
}

.index-module_notifications_hZhLx {
    cursor: pointer;
    margin-right: 3px;
    text-align: center;
    padding: 5px;
    height: 28px;
    width: 28px;
    border-radius: 6px;
    padding-left: 11px;
    padding-top: 10px;
    display: block
}

.index-module_notifications_hZhLx .index-module_bell_KxNIQ {
    display: inline-block;
    position: relative;
    right: 6px;
    color: var(--yq-yuque-grey-9);
    font-size: 16px;
    top: -3px
}

.index-module_notifications_hZhLx .index-module_countBell_-bMDE .ant-scroll-number {
    background: var(--yq-blue-6);
    height: 14px;
    line-height: 14px;
    padding: 0 3px
}

.index-module_notifications_hZhLx:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_notifications_hZhLx:hover a {
    color: var(--yq-text-primary)
}

.index-module_notifications_hZhLx.index-module_showCountBell_0JA2J {
    left: 0
}

.index-module_notifications_hZhLx .ant-badge {
    display: block
}

.index-module_notifications_hZhLx .ant-badge-dot {
    background: var(--yq-red-4);
    top: 0;
    right: 4px
}

.index-module_notifications_hZhLx .larkicon-notification {
    font-size: 16px
}

.index-module_notifications_hZhLx .ant-scroll-number {
    background: var(--yq-blue-6);
    color: var(--yq-white)
}

.popover-notifications.ant-popover .ant-menu .notifications-menu-item {
    height: 40px;
    line-height: 40px;
    margin-bottom: 0;
    padding-left: 10px;
    padding-right: 10px;
    position: relative
}

.sidebar-notifications {
    height: 40px;
    line-height: 40px;
    margin-bottom: 0;
    position: relative
}

.index-module_notificationMenuBell_tUL61 .ant-badge {
    position: absolute;
    right: 30px;
    top: 50%;
    margin-top: -3px
}

.index-module_notificationMenuBell_tUL61 .ant-badge-dot {
    background: var(--yq-theme);
    top: 0;
    right: 4px
}

.index-module_menu_rIOsP {
    width: 250px;
    max-height: 410px;
    overflow-y: auto
}

.index-module_menuName_Qh\+-p {
    display: inline-block;
    margin-left: 6px;
    vertical-align: middle;
    max-width: 136px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_menuUnread_zwOuN {
    float: right;
    background-color: var(--yq-bg-primary-hover);
    font-size: 12px;
    color: var(--yq-text-primary);
    border-radius: 11px;
    height: 22px;
    line-height: 14px;
    padding: 4px 8px;
    font-weight: 600;
    margin-top: 10px;
    transition: background-color 1s
}

.notifications-menu-item.ant-menu-item-active .index-module_menuUnread_zwOuN {
    background-color: var(--yq-bg-primary)
}

.index-module_sidebarNotifications_tBF-x {
    margin-left: 4px;
    margin-top: -8px;
    text-align: center;
    padding: 5px;
    height: 32px;
    width: 32px;
    border-radius: 6px;
    padding-left: 13px;
    padding-top: 9px;
    display: block
}

.index-module_sidebarNotifications_tBF-x .index-module_bell_KxNIQ {
    font-size: 18px;
    top: -2px;
    color: var(--yq-yuque-grey-9)
}

.index-module_sidebarNotifications_tBF-x:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_sidebarNotifications_tBF-x:hover a {
    color: var(--yq-text-primary)
}

.index-module_sidebarNotifications_tBF-x.index-module_showCountBell_0JA2J {
    left: 0
}

.styles-module_iconAction_3ZFFk {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    z-index: 1;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer
}

.styles-module_iconAction_3ZFFk:hover {
    background-color: var(--yq-bg-primary-hover)
}

.styles-module_iconAction_3ZFFk:active,.styles-module_iconAction_3ZFFk:focus {
    outline: none
}

.index-module_searchModalWrapper_vtDAq .ant-modal-content {
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-modal-close {
    display: none
}

.index-module_searchModalWrapper_vtDAq .search-dropdown ul {
    padding-top: 0;
    box-shadow: none;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-top: 1px solid var(--yq-yuque-grey-3)
}

.index-module_searchModalWrapper_vtDAq .search-dropdown:before {
    display: none
}

.index-module_searchModalWrapper_vtDAq .ant-modal-body {
    padding: 0;
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-input-affix-wrapper {
    border: 0!important;
    box-shadow: none!important
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 {
    padding: 15px;
    font-size: 16px;
    line-height: 24px;
    box-shadow: none
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 input {
    color: var(--yq-text-primary);
    margin-right: 32px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 svg {
    font-size: 16px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 .ant-input {
    padding-left: 2px!important
}

.index-module_searchInput_MvuM1+div {
    position: relative!important
}

.larkui-feature-guide>div {
    width: 284px!important
}

.larkui-feature-guide>div>div {
    z-index: 999;
    position: absolute
}

.larkui-feature-guide-sign {
    position: relative;
    width: 12px;
    height: 12px;
    line-height: 12px;
    cursor: pointer
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-outer {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 12px;
    height: 12px;
    border-radius: 12px;
    background: rgba(47,142,244,.2);
    animation: fadein 1s ease-in-out 1s infinite alternate
}

.larkui-feature-guide-sign .larkui-feature-guide-sign-spirit-inner {
    display: inline-block;
    position: absolute;
    top: 3px;
    left: 3px;
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: var(--yq-blue-5)
}

.larkui-feature-guide-mini .larkui-feature-guide-sign {
    display: inline-block;
    vertical-align: -.125em;
    margin-left: 8px
}

.larkui-feature-guide-lite {
    position: absolute;
    display: block;
    height: 12px;
    width: 12px;
    bottom: 0;
    left: 6px
}

.larkui-feature-guide-lite .larkui-feature-guide-sign {
    display: block;
    margin-left: 0
}

.larkui-feature-guide-lite-content {
    cursor: text
}

.larkui-feature-guide-lite-close {
    margin-left: 8px;
    cursor: pointer
}

.larkui-feature-guide-lite-close .icon-svg {
    vertical-align: middle;
    margin-bottom: 3px
}

.larkui-feature-guide-lite-close-multi-line {
    position: absolute;
    right: 12px;
    top: 18px
}

.larkui-feature-guide-lite-multi-line-button {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background: var(--yq-bg-secondary);
    color: var(--yq-function-info);
    min-width: 70px;
    height: 24px;
    margin: 8px 0
}

.larkui-feature-guide-lite-multi-line-button:hover {
    background: var(--yq-bg-tertiary)
}

.larkui-feature-guide-lite-multi-line-button:focus {
    outline: none
}

.larkui-feature-guide-content {
    max-width: 228px
}

.ant-tooltip.larkui-feature-guide-lite-tip {
    max-width: 360px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content,.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    background: var(--yq-blue-5)
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-inner {
    padding: 8px 16px;
    font-size: 12px;
    line-height: 22px
}

.ant-tooltip.larkui-feature-guide-lite-tip .ant-tooltip-arrow-content {
    width: 8.484px;
    height: 8.484px
}

.ant-tooltip-placement-bottomLeft.larkui-feature-guide-lite-tip .ant-tooltip-arrow {
    left: 15px
}

.ant-tooltip-placement-right {
    padding-left: 4px;
    margin-top: 8px
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.styles-module_selector_Z3\+Wx {
    padding: 24px;
    position: relative
}

.styles-module_title_Misjv {
    font-size: 16px;
    line-height: 24px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageAction_9Iy5y {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz {
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz .larkui-icon-close {
    color: var(--yq-text-body);
    top: 0;
    font-size: 14px;
    cursor: pointer
}

.message-module_memberMessageContainer_7bp54 .message-module_showMemberIntro_spXu1 {
    margin-left: 4px;
    margin-right: -8px
}

.message-module_orgPayBtn_DLLTR {
    margin-left: 20px
}

.index-module_wrap_iKZPE {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body)
}

.index-module_wrap_iKZPE:hover .index-module_close_mZbMN {
    display: flex
}

.index-module_icon_gxtpV {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: none
}

.index-module_close_mZbMN {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_closeIcon_kM1zW {
    font-size: 16px
}

.doc-draft-tip {
    font-weight: 400
}

.doc-draft-tip-content .update-info {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.doc-draft-tip-content .update-info a {
    color: var(--yq-text-body)
}

.ant-tag.doc-template-tag {
    margin: 0 0 0 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.doc-title {
    font-size: 14px;
    line-height: 21px;
    text-overflow: ellipsis;
    color: var(--yq-text-body);
    font-family: Chinese Quote,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji
}

.doc-title-draft {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 4px;
    font-weight: 400
}

.doc-icon {
    margin-right: 4px
}

.doc-access-scope {
    margin-left: 8px
}

.doc-belong,.doc-belong a {
    color: var(--yq-text-caption)
}

.doc-belong a {
    margin: 0 4px
}

.doc-belong a:first-child {
    margin-left: 0
}

.doc-contributors,.doc-contributors span a {
    color: var(--yq-text-caption)
}

.index-module_articleTitle_VJTLJ {
    word-break: break-word
}

.index-module_popover_nfMC3 {
    display: inline
}

.index-module_belongMenu_2QmLB {
    outline: none;
    cursor: pointer
}

.index-module_belongMenu_2QmLB .larkui-icon {
    display: inline-block;
    font-size: 12px;
    margin-left: 4px
}

.index-module_belongText_TkCAl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu {
    min-width: 188px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item {
    display: flex;
    align-items: center
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .larkui-icon-check-outlined {
    margin-right: 6px;
    visibility: hidden;
    font-size: 16px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 280px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>.larkui-icon,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>span {
    display: inline-block;
    vertical-align: middle
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item:hover {
    background-color: var(--yq-bg-tertiary);
    font-weight: 400
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active .larkui-icon-check-outlined {
    visibility: visible
}

@media only screen and (max-width: 575px) {
    .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
        max-width:140px
    }
}

.index-module_privacy_QkaFB {
    display: inline-block;
    padding: 3px 5px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    color: var(--yq-text-body);
    line-height: 14px;
    font-size: 10px;
    font-weight: 400;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
    margin: 0 6px 0
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    justify-content: center
}

.GlobalDocCreate-module_menuItemContainer_\+DgHG,.index-module_spinArea_HqKpu {
    display: flex;
    align-items: center
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu {
    padding: 8px 0;
    min-width: 110px
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu .ant-menu-item {
    height: 32px;
    line-height: 32px
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-popover-inner-content {
    padding: 0
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-popover-inner-content {
    padding: 8px
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-menu-item {
    border-radius: 8px
}

.GlobalDocCreate-module_iconContainer_eX6jK {
    margin-right: 12px
}

.GlobalDocCreate-module_iconBeta_hDUuJ {
    background-color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 3px;
    color: var(--yq-white);
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    text-align: center;
    border-radius: 8px;
    padding: 0 5px;
    position: absolute;
    top: 6px;
    left: 50%
}

.GlobalDocCreate-module_menuList_s1r7J {
    display: flex;
    justify-content: space-between
}

.GlobalDocCreate-module_menuListItem_hC68r {
    text-align: center;
    cursor: pointer
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    background: none!important
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE>svg {
    margin-right: 8px
}

.GlobalDocCreate-module_desc_6gQhM {
    color: var(--yq-text-caption);
    margin-left: 16px
}

.HeadNewButton-module_header-new-btn_VH3hy {
    position: relative;
    white-space: nowrap
}

.HeadNewButton-module_header-new-btn_VH3hy.larkui-popover-trigger {
    display: flex;
    align-items: center
}

.HeadNewButton-module_header-new-btn_VH3hy.larkui-popover-trigger:hover {
    color: var(--yq-theme)
}

.HeadNewButton-module_header-new-btn_VH3hy .larkicon-header-new {
    display: block;
    text-align: left;
    font-size: 20px
}

.HeadNewButton-module_header-new-btn_VH3hy .larkicon.larkicon-add {
    font-size: 16px
}

.HeadNewButton-module_header-add-btn_nJ9ub {
    display: inline-block;
    vertical-align: top;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    margin-top: -7px;
    color: var(--yq-theme)
}

.HeadNewButton-module_headNewPopover_DoiNN.larkui-popover-menu .ant-menu .ant-menu-item a {
    display: flex;
    align-items: center
}

.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-inner .ant-popover-inner-content {
    padding: 3px 0
}

.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-content .ant-popover-inner-content .ant-menu-item {
    height: 36px;
    line-height: 36px;
    border-radius: 6px
}

.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-content .ant-menu {
    padding: 2px 8px
}

.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-content .ant-menu .ant-menu-item {
    height: 40px;
    line-height: 40px
}

.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-content .ant-menu .ant-menu-item>a,.HeadNewButton-module_headNewPopover_DoiNN .ant-popover-content .ant-menu .ant-menu-item>span {
    display: block;
    padding: 0
}

.HeadNewButton-module_headNewPopover_DoiNN .HeadNewButton-module_headNewMenu_TXIWb .ant-menu-item-selected {
    background: none!important
}

.HeadNewButton-module_headNewPopover_DoiNN .HeadNewButton-module_headNewMenu_TXIWb .ant-menu-item.ant-menu-item-only-child {
    height: 36px;
    line-height: 36px;
    margin: 2px 0
}

.HeadNewButton-module_headNewPopover_DoiNN .HeadNewButton-module_headNewMenu_TXIWb .ant-menu-item {
    min-width: 148px
}

.HeadNewButton-module_menuItemContainer_VLntH {
    display: flex;
    align-items: center
}

.HeadNewButton-module_menuItem_aztNg {
    padding: 0!important
}

.HeadNewButton-module_featureGuide_YV\+Pu {
    display: flex
}

.HeadNewButton-module_iconContainer_HmX2B {
    margin-right: 12px;
    width: 20px;
    min-width: 20px;
    height: 20px;
    margin-top: 8px;
    margin-left: 8px
}

.HeadNewButton-module_iconContainer_HmX2B.HeadNewButton-module_icon-ai-active_1oM3g {
    color: var(--yq-theme)
}

.HeadNewButton-module_iconBeta_pQkiL {
    color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 9px
}

.HeadNewButton-module_addContent_KXrJV {
    width: 32px;
    height: 32px;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 8px;
    background-color: var(--yq-white);
    line-height: 32px;
    text-align: center;
    cursor: pointer;
    margin-bottom: 0
}

.HeadNewButton-module_addContent_KXrJV:hover {
    background-color: var(--yq-yuque-grey-2)
}

.HeadNewButton-module_addContent_KXrJV svg {
    margin-top: 7px;
    color: var(--yq-text-body)
}

.HeadNewButton-module_iconWarp_o9rNt {
    height: 34px;
    width: 36px;
    border-radius: 8px
}

.HeadNewButton-module_desc_0seY9 {
    color: var(--yq-text-caption);
    margin-left: 16px
}

.index-module_iconSearchWrapper_uf7UV {
    align-self: center
}

.index-module_inputSearchWrapper_Kly-7 {
    display: flex;
    padding: 0 12px;
    margin: 16px 0;
    width: 100%
}

.index-module_inputSearchWrapper_Kly-7 .index-module_searchContainer_7EKIH {
    flex: 1;
    margin-right: 12px
}

.index-module_inputSearchWrapper_Kly-7 .index-module_search_GTiOp {
    width: 100%;
    background: var(--yq-bg-primary-hover);
    height: 32px;
    cursor: pointer;
    border: 0;
    opacity: 1;
    border-radius: 8px;
    color: var(--yq-text-disable)
}

html[data-kumuhana=pouli] .index-module_inputSearchWrapper_Kly-7 .index-module_search_GTiOp {
    border: 1px solid var(--yq-border-primary)
}

.index-module_inputSearchWrapper_Kly-7 .index-module_search_GTiOp input {
    color: var(--yq-text-disable);
    opacity: 1;
    pointer-events: none
}

.index-module_inputSearchWrapper_Kly-7 .index-module_search_GTiOp svg {
    font-size: 18px
}

.index-module_collapsedWrapper_y9pOA {
    display: block;
    margin-bottom: 56px;
    padding: 0 9px
}

.index-module_collapsedWrapper_y9pOA .index-module_addContent_cdPQx {
    top: -29px;
    left: 5px
}

.index-module_collapsedWrapper_y9pOA .index-module_searchContainer_7EKIH {
    position: relative;
    top: 78px;
    left: 5px
}

.index-module_desktopCollapedWrapper_S52NF {
    margin-bottom: 18px
}

.index-module_desktopCollapedWrapper_S52NF .index-module_searchContainer_7EKIH {
    top: 42px
}

.index-module_iconWrapper_8XX6t {
    text-align: center;
    padding: 5px 5px 5px 6px;
    height: 28px;
    width: 28px;
    border-radius: 6px;
    display: block;
    cursor: pointer
}

.index-module_iconWrapper_8XX6t:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_iconWrapper_8XX6t:hover a {
    color: var(--yq-text-primary)
}

.index-module_iconWrapper_8XX6t svg {
    font-size: 18px
}

.sidebar-is-collapse.is-desktop-app .index-module_wrapper_H3CfZ {
    margin-bottom: 50px
}

.sidebar-is-collapse.is-desktop-app .index-module_wrapper_H3CfZ .index-module_iconWrapper_8XX6t {
    top: 40px
}

.sidebar-is-collapse.index-module_is-desktop-app_iAshn .index-module_wrapper_H3CfZ {
    margin-bottom: 50px
}

.sidebar-is-collapse.index-module_is-desktop-app_iAshn .index-module_wrapper_H3CfZ .index-module_iconWrapper_8XX6t {
    top: 40px
}

.sidebar-is-collapse .index-module_wrapper_H3CfZ {
    display: flex;
    padding: 0;
    margin-bottom: 84px;
    margin-top: 20px
}

.sidebar-is-collapse .index-module_wrapper_H3CfZ .index-module_iconWrapper_8XX6t {
    position: relative;
    top: 74px;
    left: 14px;
    width: 32px;
    height: 32px;
    padding: 8px
}

.index-module_addContent_cdPQx {
    position: relative
}

.index-module_userInfo_XLKh6 {
    display: flex;
    justify-content: space-between;
    line-height: 32px;
    height: 32px;
    padding: 0 12px
}

.index-module_userInfo_XLKh6 .index-module_orgLogo_N1dxn {
    min-width: 105px;
    display: flex
}

.index-module_userInfo_XLKh6 .index-module_userAction_\+C\+L4 {
    display: flex;
    align-items: center
}

.index-module_userInfo_XLKh6 .index-module_userAction_\+C\+L4 .index-module_userAvatar_ERAUQ {
    align-self: center;
    height: 32px
}

.index-module_collapsedUserInfo_9WwIp {
    display: block;
    position: relative
}

.index-module_collapsedUserInfo_9WwIp .index-module_orgLogo_N1dxn {
    margin-left: 3px;
    margin-top: 1px;
    min-width: 32px;
    width: 34px;
    overflow: hidden
}

.index-module_collapsedUserInfo_9WwIp .index-module_userAction_\+C\+L4 {
    display: block;
    position: relative;
    top: 64px;
    height: 32px;
    width: 32px;
    margin-bottom: 12px;
    left: -2px
}

.index-module_sidebarAction_yE6Ka {
    display: flex;
    justify-content: space-between
}

.index-module_badge_NHQXd {
    display: inline-block;
    width: 100%
}

.index-module_badge_NHQXd sup {
    right: 4px;
    margin-top: 7px;
    background-color: var(--yq-blue-5)
}

.index-module_collapseBadge_-NU9u sup {
    right: 24px;
    margin-top: -2px
}

.MemberAbleIcon-module_lightIcon_wz4WU {
    height: 16px;
    padding: 0 5px;
    line-height: 16px;
    background: rgba(0,0,0,.04);
    border-radius: 10px;
    text-align: center;
    display: inline-block;
    margin-left: 4px;
    color: #b4b7b5;
    font-size: 12px
}

.DesktopMoreLink-module_iconCheck_db8tc {
    position: absolute;
    bottom: 11px;
    left: 2px
}

.DesktopMoreLink-module_btn_odE04:before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: transparent;
    content: ""
}

.DesktopMoreLink-module_showUpgradeWindow_hOH8q {
    float: right
}

.MoreLink-module_moreLink_3qeJT {
    width: 240px;
    padding: 8px 8px 4px 8px
}

.MoreLink-module_moreLink_3qeJT .ant-menu.ant-menu-vertical .ant-menu-item {
    height: 38px;
    line-height: 38px;
    padding: 0 18px;
    border-radius: 6px;
    margin-bottom: 4px
}

.MoreLink-module_moreLink_3qeJT .ant-menu.ant-menu-vertical {
    border: 0
}

.MoreLink-module_moreLink_3qeJT .MoreLink-module_defaultLink_8Mvnq,.MoreLink-module_moreLink_3qeJT .MoreLink-module_moreLinkText_96v0p {
    margin-top: 8px
}

.MoreLink-module_moreLink_3qeJT .MoreLink-module_moreLinkText_96v0p li.ant-menu-item:hover a a,.MoreLink-module_moreLink_3qeJT .MoreLink-module_moreLinkText_96v0p li.ant-menu-item a {
    color: var(--yq-yuque-grey-8)
}

.MoreLink-module_moreText_reLWV {
    height: 40px;
    line-height: 40px;
    font-weight: 600;
    font-size: 14px;
    color: var(--yq-black);
    padding: 0 18px
}

.MoreLink-module_textLink_e43Al {
    color: var(--yq-yuque-grey-8);
    line-height: 20px;
    padding: 20px 24px
}

.MoreLink-module_textLink_e43Al li {
    height: 32px;
    line-height: 32px
}

.MoreLink-module_iconLink_Xl3Un {
    padding-bottom: 10px;
    border-bottom: 1px solid var(--yq-border-light)
}

.MoreLink-module_iconLink_Xl3Un li {
    display: flex;
    padding: 10px 14px;
    cursor: pointer;
    border-radius: 6px
}

.MoreLink-module_iconLink_Xl3Un li a {
    display: flex
}

.MoreLink-module_iconLink_Xl3Un li:hover {
    background-color: var(--yq-bg-primary-hover)
}

.MoreLink-module_iconLink_Xl3Un li svg {
    font-size: 16px;
    position: relative;
    top: 2px;
    color: var(--yq-yuque-grey-9)
}

.MoreLink-module_defaultLink_8Mvnq li:hover {
    background-color: var(--yq-bg-primary-hover)
}

.MoreLink-module_iconWarp_Ktp-I {
    height: 32px;
    width: 32px;
    border-radius: 6px;
    text-align: center;
    line-height: 32px;
    margin-right: 16px;
    flex: 0 0 auto
}

.MoreLink-module_iconTitle_QSL4A {
    font-weight: 500;
    font-size: 12px;
    color: var(--yq-yuque-grey-9);
    margin-top: -4px;
    height: 18px;
    line-height: 18px
}

.MoreLink-module_iconContent_u4edp {
    font-weight: 400;
    font-size: 12px;
    color: var(--yq-yuque-grey-8);
    line-height: 20px
}

.MoreLink-module_iconWarpText_iL2XP {
    max-width: 166px
}

.DesktopUpgradeTips-module_desktopUpgradeTips_IwNAm {
    float: right
}

.DesktopUpgradeTips-module_desktopUpgradeTips_IwNAm .ant-tag {
    margin-right: 0
}

.ant-menu.ant-menu-inline-collapsed .ant-menu-item.larkui-popover-trigger .DesktopUpgradeTips-module_menuItemBadge_yH9uS {
    line-height: 26px
}

.ant-menu.ant-menu-inline-collapsed .ant-menu-item.larkui-popover-trigger .DesktopUpgradeTips-module_menuItemBadge_yH9uS .ant-badge-dot {
    top: 4px
}

.index-module_navLinkPopover_SeFzl span.ant-popover-inner-content {
    padding: 0
}

.index-module_navLinkPopover_SeFzl .ant-popover-arrow {
    display: none
}

.index-module_navLinkPopover_SeFzl .ant-popover-inner-content {
    padding: 0
}

.index-module_navLinkPopover_SeFzl .ant-menu-root {
    background-color: var(--yq-bg-primary)!important
}

html[data-kumuhana=pouli] .index-module_navLinkPopover_SeFzl .ant-menu-root {
    background-color: var(--yq-bg-secondary)!important
}

.index-module_defaultMenu_qjIBj .index-module_menuItem_S3OA1 {
    display: flex;
    padding-right: 4px!important;
    padding-left: 10px!important;
    margin: 4px 0!important;
    align-items: center
}

.index-module_defaultMenu_qjIBj .index-module_menuItem_S3OA1 .index-module_span_nCoEo {
    flex: 1 1 auto;
    margin-left: 0;
    transition: none
}

.index-module_defaultMenu_qjIBj .index-module_menuItem_S3OA1 .ant-menu-title-content {
    display: flex!important;
    align-items: center;
    margin: 0!important;
    padding: 0!important
}

.index-module_defaultMenu_qjIBj .index-module_menuItem_S3OA1 .index-module_span_nCoEo,.index-module_isCollapsedMenu_1\+yk- .index-module_menuItem_S3OA1 .index-module_span_nCoEo {
    transition: none
}

.index-module_modal_agbF3 {
    width: 100px
}

.index-module_modal_agbF3 .ant-modal-body {
    padding: 0
}

.index-module_content_QXb3M {
    padding: 0 24px 24px 24px
}

.index-module_content_QXb3M h1 {
    margin: 32px auto 16px auto;
    font-size: 18px;
    color: var(--yq-text-primary)
}

.index-module_content_QXb3M p {
    margin-bottom: 24px;
    color: var(--yq-text-body)
}

.index-module_content_QXb3M .index-module_card_tMAa1 {
    margin-bottom: 37px;
    position: relative;
    padding: 16px 12px 16px 66px;
    height: 84px;
    background: rgba(245,203,97,.05);
    border: .5px solid var(--yq-yellow-3);
    border-radius: 12px
}

.index-module_content_QXb3M .index-module_card_tMAa1 .index-module_icon_FBwt0 {
    position: absolute;
    top: 18px;
    left: 12px
}

.index-module_content_QXb3M .index-module_card_tMAa1 .index-module_title_UUXXw {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 700;
    color: var(--yq-yellow-7)
}

.index-module_content_QXb3M .index-module_card_tMAa1 .index-module_desc_5PHTr {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_content_QXb3M .index-module_card_tMAa1 span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block
}

.index-module_content_QXb3M .index-module_enterpriseCard_oKRIy {
    background: rgba(252,245,230,.25);
    border: .5px solid var(--yq-brown-300)
}

.index-module_content_QXb3M .index-module_enterpriseCard_oKRIy .index-module_title_UUXXw {
    color: var(--yq-yuque-grey-800)
}

.index-module_content_QXb3M .index-module_footer_NovhG {
    display: flex;
    justify-content: space-between
}

.index-module_content_QXb3M .index-module_footer_NovhG .ant-btn-link {
    padding: 0
}

.index-module_orgTipWrapper_SGN9N {
    display: flex;
    justify-content: space-around;
    flex-direction: row;
    align-items: center;
    width: 100%
}

.index-module_orgTipWrapper_SGN9N .index-module_title_jt6hu {
    color: var(--yq-text-primary);
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1
}

.index-module_orgTipWrapper_SGN9N .index-module_title_jt6hu svg {
    vertical-align: -4px;
    margin-right: 8px
}

.index-module_orgTipWrapper_SGN9N .index-module_willExpired_Z1VSY {
    font-weight: 500;
    color: var(--yq-yuque-green-600)
}

.index-module_orgTipWrapper_SGN9N .index-module_willExpiredWarning_OOONc {
    color: var(--yq-red-500)
}

.index-module_orgTipWrapper_SGN9N .index-module_action_Lellm .index-module_button1_qSmHA,.index-module_orgTipWrapper_SGN9N .index-module_action_Lellm .index-module_button_R\+\+me {
    font-size: 12px;
    cursor: pointer
}

.index-module_orgTipWrapper_SGN9N .index-module_action_Lellm .index-module_button1_qSmHA {
    margin-left: 6px
}

.index-module_orgTipWrapper_SGN9N .index-module_oneButtonActions_R1nOm {
    width: auto;
    cursor: pointer
}

.index-module_welfare_dWfvI {
    position: relative;
    width: 100%;
    display: flex!important;
    align-items: center;
    justify-content: flex-start;
    line-height: 1.5;
    padding: 10px 0;
    flex-wrap: wrap;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_welfare_dWfvI .index-module_text_k40Dh {
    margin-left: 0;
    font-size: 12px;
    color: var(--yq-text-body)
}

.index-module_welfare_dWfvI a {
    font-size: 12px;
    margin-top: 5px;
    color: var(--yq-text-link)!important
}

.index-module_welfare_dWfvI a:hover {
    color: var(--yq-text-link)
}

.index-module_img_1Pbk3 {
    margin-right: 8px;
    vertical-align: -5px
}

@keyframes RegisterTrialModal-module_showIcon_8EOMg {
    0% {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
    }

    to {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original)
    }
}

.RegisterTrialModal-module_modal_hmMaz .ant-modal-body {
    padding: 0
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--yq-icon-colorbg);
    cursor: pointer
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d svg {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 10px;
    fill: #fff;
    transform: translate(-50%,-50%);
    color: #fff
}

.RegisterTrialModal-module_wrapper_v2OGS {
    padding-bottom: 28px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(0) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:first-child .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(2) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(3) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.6s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(4) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(5) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(6) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    width: 188px;
    height: 323px;
    left: 38px;
    top: 0;
    padding-left: 40px;
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_tip_\+NtIs {
    width: 45px;
    height: 21px;
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-text-primary);
    position: absolute;
    right: 0;
    top: 0;
    background: var(--yq-bg-primary-hover);
    border-radius: 0 4px 0 12px;
    text-align: center
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT {
    left: 242px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(0);
    padding-left: 10px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_img_bAwkI {
    width: 480px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_highLight_AL6MB {
    color: var(--yq-theme)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: #fdf9e4;
    text-align: center;
    line-height: 32px;
    font-size: 20px;
    color: #e4495b;
    font-weight: 500;
    margin: 0 9px
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    background: var(--yq-yellow-100)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC .RegisterTrialModal-module_img_bAwkI {
    width: 22px;
    height: 22px;
    margin-right: 12px;
    margin-top: -2px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE {
    height: 323px;
    position: relative;
    top: 0
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    width: 107px;
    height: 55px;
    position: absolute;
    left: 87px;
    top: 170px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1;
    position: absolute;
    left: 32px;
    top: 44px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 12px;
    width: 128px;
    height: 154px;
    padding-left: 28px;
    padding-top: 16px;
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K {
    margin-top: 18px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 8px;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 20px;
    list-style: disc
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    position: absolute;
    left: 168px;
    right: 32px;
    top: 0;
    background-image: linear-gradient(125deg,#fdf9e4,#fff 57%,#fdf9e4);
    border: .5px solid #f5da80;
    border-radius: 12px;
    height: 323px;
    padding: 16px 20px 0;
    transition: all 1s ease
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    background: rgba(245,210,97,.05);
    border: .5px solid #776118
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK span {
    line-height: 24px;
    height: 24px;
    display: inline-block
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_yuan_4C\+Ad {
    font-size: 16px;
    color: #c99b03;
    font-weight: 500;
    margin-left: 6px;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_price_f4gnN {
    font-size: 24px;
    color: #c99b03;
    font-weight: 500;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_time_QNDxs {
    font-size: 14px;
    color: #c99b03;
    margin-left: 5px;
    font-weight: 400
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K {
    margin-top: 20px;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-body);
    line-height: 20px;
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 16px 16px;
    display: inline-block;
    margin-right: 12px;
    width: 16px;
    height: 16px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_bold_rwjpD {
    color: var(--yq-text-primary);
    font-weight: 600
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_bg_xLr\+V {
    width: 69px;
    height: 154px;
    position: absolute;
    right: 0;
    top: 99px
}

.ExpiringTip-module_wrapper_3ljfM {
    display: flex;
    font-size: 12px;
    justify-content: space-between;
    align-items: center;
    color: var(--yq-yuque-grey-8)
}

.ExpiringTip-module_wrapper_3ljfM a {
    color: var(--yq-text-link)!important;
    margin-left: 8px
}

.ExpiringTip-module_wrapper_3ljfM.ExpiringTip-module_inTooltip_be15J {
    font-size: 14px;
    color: #fff
}

.ExpiringTip-module_wrapper_3ljfM .ExpiringTip-module_title_vKFxY {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ExpiringTip-module_tip_d8vvy {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    width: 100%;
    position: relative
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_tipClose_E-trW {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    cursor: pointer
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_title_vKFxY {
    color: var(--yq-text-primary);
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    width: 100%
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_button_Zfmrx {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    width: 76px;
    height: 30px;
    border: 1px solid var(--yq-yellow-300);
    background: var(--yq-white);
    margin-right: 10px;
    border-radius: 30px;
    font-size: 12px;
    text-align: center;
    line-height: 30px;
    color: var(--yq-yellow-600);
    font-weight: 500;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_button_Zfmrx svg {
    margin-right: 5px;
    margin-left: -2px
}

html[data-kumuhana=pouli] .ExpiringTip-module_tip_d8vvy .ExpiringTip-module_button_Zfmrx {
    background: #292929;
    border: 1px solid #776118
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_btnBuy_K4L2v {
    border: 1px solid var(--yq-orange-500);
    color: var(--yq-orange-500)
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_trialWrap_DactR {
    display: flex;
    margin-top: 12px
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_trialWrap_DactR .ExpiringTip-module_btn1_Kg4kg {
    width: 96px;
    height: 28px;
    border: 1px solid var(--yq-yellow-300);
    background: var(--yq-white);
    border-radius: 28px;
    font-size: 12px;
    text-align: center;
    line-height: 26px;
    color: var(--yq-yellow-600);
    margin-right: 6px
}

html[data-kumuhana=pouli] .ExpiringTip-module_tip_d8vvy .ExpiringTip-module_trialWrap_DactR .ExpiringTip-module_btn1_Kg4kg {
    background: #292929;
    border: 1px solid #776118;
    color: #d2ad38
}

.ExpiringTip-module_tip_d8vvy .ExpiringTip-module_trialWrap_DactR .ExpiringTip-module_btn2_qYo\+5 {
    width: 96px;
    height: 28px;
    border: 1px solid var(--yq-blue-200);
    background: var(--yq-white);
    border-radius: 28px;
    font-size: 12px;
    text-align: center;
    line-height: 26px;
    color: var(--yq-blue-400)
}

html[data-kumuhana=pouli] .ExpiringTip-module_tip_d8vvy .ExpiringTip-module_trialWrap_DactR .ExpiringTip-module_btn2_qYo\+5 {
    background: #292929;
    border: 1px solid #245294;
    color: #6897d9
}

.ExpiringTip-module_tipOther_DyO3Y {
    flex-direction: column;
    align-items: flex-start
}

.ExpiringTip-module_equityLink_kBRKY {
    font-size: 12px
}

.ExpiringTip-module_equityTitle_oevKT {
    color: var(--yq-text-primary);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1
}

.ExpiringTip-module_rightsReminder_dop2B {
    display: flex;
    justify-content: space-between;
    font-size: 12px
}

.ExpiringTip-module_rightsReminder_dop2B svg {
    font-size: 18px;
    vertical-align: -5px;
    margin-right: 10px
}

.ExpiringTip-module_img_BXz\+n {
    margin-right: 10px;
    vertical-align: -6px
}

.index-module_cloudAdvertise_3TWa4 {
    width: 100%;
    max-width: 250px;
    padding: 10px;
    border-radius: 8px;
    background-color: var(--yq-yellow-50);
    border: 1px solid var(--yq-yellow-200);
    height: auto
}

.index-module_cloudAdvertise_3TWa4.index-module_moreAdvertise_DoHQt {
    margin-bottom: 10px
}

.index-module_tip_gtfbs {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    position: relative
}

.index-module_tip_gtfbs .index-module_tipClose_Mj9nF {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    cursor: pointer
}

.index-module_tip_gtfbs .index-module_title_Q0HgZ {
    font-size: 14px;
    color: var(--yq-yellow-800);
    height: 20px;
    line-height: 20px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    width: 100%;
    margin-bottom: 6px
}

.index-module_tip_gtfbs .index-module_text_QfnH6 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: var(--yq-yellow-800)
}

.index-module_tip_gtfbs .index-module_button_V6Xo9 {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    padding: 8px 13px;
    line-height: 12px;
    border: 1px solid var(--yq-yellow-300);
    background: var(--yq-bg-pre-foreground);
    margin-right: 10px;
    border-radius: 30px;
    font-size: 12px;
    text-align: center;
    color: var(--yq-yellow-600);
    font-weight: 500;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_advertise_BtL6t {
    width: 100%;
    max-width: 250px;
    padding: 10px;
    background: var(--yq-white);
    border-radius: 8px;
    border: 1px solid var(--yq-yuque-grey-4)
}

.index-module_navLinkMemberPay_i52aF {
    background-color: var(--yq-yellow-50);
    border: 1px solid var(--yq-yellow-200);
    height: auto
}

.index-module_navLinkMembership_R57Iu,.index-module_navLinkOrgPay_z\+6hK {
    background: var(--yq-white);
    height: auto;
    border-radius: 8px;
    border: 1px solid var(--yq-yuque-grey-4)
}

.index-module_collapseWrapper_UsCJc {
    display: flex;
    flex-direction: column;
    align-items: center
}

.index-module_defaultWrapper_HbVoM {
    margin-top: 12px
}

.index-module_collapsedAdvertise_Fa3f7 {
    width: 40px;
    height: 40px;
    background-color: var(--yq-white);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px;
    border: 1px solid var(--yq-yuque-grey-4);
    margin-top: 10px;
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08);
    cursor: pointer
}

.book-link,.book-name,.lark-book-title {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-book-title .book-icon {
    margin-right: 8px
}

.book-name {
    display: flex;
    align-items: center
}

.book-name .book-name-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.book-name .book-name-scope {
    position: relative
}

.book-name .book-name-scope a {
    pointer-events: none
}

.book-name .book-name-scope .icon-svg {
    margin-left: 5px
}

.book-name .book-name-orglabel {
    margin-left: 12px
}

.book-name .icon-svg {
    display: block
}

.book-name-split {
    margin: 0 4px
}

.permission__public__tip {
    margin-top: 12px;
    line-height: 22px
}

.permission__public__tip .highlight {
    color: var(--yq-function-error)
}

.belong-icon {
    font-size: 12px;
    margin-left: 4px
}

.toggle-publicity {
    color: var(--yq-text-link)!important;
    margin-left: 12px
}

.index-module_content_v7lvp,.index-module_form_5k71F .larkui-form-item {
    margin-bottom: 16px
}

.index-module_form_5k71F .larkui-form-item:last-child {
    margin-bottom: 0
}

.index-module_tip_yNrai {
    line-height: 40px;
    color: var(--yq-text-primary)
}

.index-module_revertLink_csROb {
    margin-left: 4px
}

.BaseSettings-module_baseSettingsMenu_dc9ae .ant-menu-item {
    display: flex;
    align-items: center
}

.BaseSettings-module_baseSettingsMenu_dc9ae .ant-menu-item-icon+span {
    margin-left: 8px
}

.BaseSettings-module_moreActionIcon_lbrJG {
    color: var(--yq-icon-secondary)
}

.BaseSettings-module_moreActionDelete_BUd4F {
    color: var(--yq-red-6)
}

.PopoverContent-module_moreActionIcon_3y75a {
    color: var(--yq-icon-secondary)
}

.index-module_resourceMenu_Lhgz3 {
    margin-top: 14px
}

.index-module_resourceMenu_Lhgz3 .index-module_switchIconWarp_dOsGE {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 26px;
    height: 26px;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    margin: 0;
    color: var(--yq-yuque-grey-7)
}

.index-module_resourceMenu_Lhgz3 .index-module_switchIconWarp_dOsGE svg {
    margin: 0
}

.index-module_resourceMenu_Lhgz3 .index-module_switchIconWarp_dOsGE:hover {
    background: var(--yq-yuque-grey-5)
}

.index-module_resourceMenu_Lhgz3 .index-module_switchIconWarp_dOsGE:hover svg {
    color: var(--yq-yuque-grey-9)
}

.index-module_resourceMenu_Lhgz3 .index-module_notDataTips_VSvSw {
    height: 22px;
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-yuque-grey-6);
    line-height: 22px;
    margin: 10px auto 18px;
    text-align: center
}

.index-module_resourceMenu_Lhgz3 .index-module_menuTitle_zpskK {
    display: flex;
    align-items: center
}

.index-module_resourceMenu_Lhgz3 .index-module_menuTitle_zpskK .index-module_span_7Zrjv {
    flex: 1 1 auto;
    color: var(--yq-yuque-grey-9)
}

.index-module_resourceMenu_Lhgz3 .index-module_menuTitle_zpskK svg {
    color: var(--yq-yuque-grey-7);
    margin-right: 6px
}

.index-module_resourceMenu_Lhgz3 .index-module_subMenuTitle_dfp7Q {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_resourceMenu_Lhgz3 .index-module_subMenuTitle_dfp7Q svg {
    margin-right: 6px;
    color: var(--yq-yuque-grey-7)
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ {
    display: flex;
    justify-content: flex-start;
    position: relative;
    width: 100%;
    padding-right: 4px;
    border-radius: 6px
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ .index-module_itemAction_KAYbp {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    color: var(--yq-yuque-grey-7);
    position: relative;
    width: 20px;
    height: 24px;
    border-radius: 6px;
    visibility: hidden;
    margin: 0!important;
    padding: 0!important;
    font-size: 16px
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ .index-module_itemAction_KAYbp:hover {
    background: var(--yq-yuque-grey-5);
    color: var(--yq-yuque-grey-9)
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ .index-module_itemWrapper_SxLTV {
    flex: 1 1 auto!important;
    overflow: hidden
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ .index-module_badgeWrapper_g5Ycn {
    position: absolute;
    right: 13px;
    top: -2px;
    z-index: 3
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ:hover {
    background-color: var(--yq-bg-primary-hover);
    border-radius: 6px
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ:hover .index-module_badgeWrapper_g5Ycn {
    display: none
}

.index-module_resourceMenu_Lhgz3 .index-module_menuItem_R3vqQ:hover .index-module_itemAction_KAYbp {
    visibility: visible
}

.index-module_resourceMenu_Lhgz3 .ant-menu-submenu-title {
    padding-right: 4px;
    padding-left: 4px!important
}

.index-module_resourceMenu_Lhgz3 .ant-menu-submenu-title>.ant-menu-title-content {
    margin-left: 6px
}

.index-module_resourceMenu_Lhgz3 .ant-menu-submenu-title>.ant-menu-submenu-arrow {
    display: none
}

.index-module_resourceMenu_Lhgz3 .placeholder {
    border: 0!important
}

.index-module_resourceMenu_Lhgz3 .placeholder:hover {
    background-color: var(--yq-yuque-grey-1)!important;
    border-radius: 0!important
}

.index-module_resourceMenu_Lhgz3 .placeholder:after {
    margin-top: -3px;
    top: 27px!important
}

.index-module_resourceMenu_Lhgz3 .placeholder:after,.index-module_resourceMenu_Lhgz3 .placeholder:before {
    border-left: 1.5px solid var(--yq-blue-6)!important;
    border-right: 1.5px solid var(--yq-blue-6)!important;
    transform: none!important;
    opacity: 1!important;
    width: 233px;
    content: "";
    display: inline-block
}

.index-module_resourceMenu_Lhgz3 .placeholder:before {
    height: 2px;
    left: -10px!important;
    background: var(--yq-blue-6);
    position: absolute;
    margin-left: 10px;
    top: 27px
}

.index-module_resourceMenu_Lhgz3 .ant-menu-item:after {
    display: none
}

.index-module_displayResourceMenu_pOPME {
    display: none!important
}

.index-module_subMenuGroup_gVc1X {
    position: relative;
    z-index: 2
}

.index-module_subMenu_4EJsi {
    margin-top: 18px
}

.index-module_menuItemSelected_Owein {
    background-color: var(--yq-bg-primary-hover);
    border-radius: 6px
}

.index-module_popoverMoreAction_qArsg {
    padding: 5px 0;
    z-index: 1000
}

.index-module_popoverMoreAction_qArsg .ant-menu-item {
    padding: 6px 10px!important
}

.index-module_popoverMoreAction_qArsg .ant-menu-item:hover {
    background: var(--yq-yuque-grey-4)
}

.index-module_popoverMoreAction_qArsg .ant-menu-item svg {
    margin-right: 5px
}

.index-module_popoverMoreAction_qArsg .ant-popover-inner {
    overflow: hidden
}

.index-module_popoverMoreAction_qArsg .ant-popover-inner-content {
    padding: 0
}

.index-module_isCollapsedResourceMenu_RRN9Y {
    width: 214px
}

.index-module_isCollapsedResourceMenu_RRN9Y .index-module_menuItem_R3vqQ {
    display: flex;
    justify-content: flex-start;
    position: relative;
    width: 100%;
    padding-right: 16px
}

.index-module_isCollapsedResourceMenu_RRN9Y .index-module_menuItem_R3vqQ .index-module_badgeWrapper_g5Ycn,.index-module_isCollapsedResourceMenu_RRN9Y .index-module_menuItem_R3vqQ .index-module_itemAction_KAYbp {
    display: none
}

.index-module_isCollapsedResourceMenu_RRN9Y .ant-menu {
    padding: 8px 0
}

.index-module_isCollapsedResourceMenu_RRN9Y .ant-menu-item-divider {
    margin: 8px 0
}

.index-module_isCollapsedResourceMenu_RRN9Y .ant-menu .ant-menu-item {
    margin: 2px 8px;
    width: calc(100% - 16px)
}

.index-module_isCollapsedResourceMenu_RRN9Y .collapse-menu-title {
    border-radius: 6px
}

.index-module_isCollapsedResourceMenu_RRN9Y .collapse-menu-title:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_isNavigationMenu_8r-mW .index-module_menuItem_R3vqQ .index-module_badgeWrapper_g5Ycn,.index-module_isNavigationMenu_8r-mW .index-module_menuItem_R3vqQ .index-module_itemAction_KAYbp {
    opacity: 0
}

.index-module_skeletonWrapper_SwQU1 {
    padding: 0 32px
}

.index-module_isNewPinItem_9ac\+5 {
    animation: index-module_changeBg_i8YpF 2.5s ease-out forwards;
    border-radius: 6px
}

@keyframes index-module_changeBg_i8YpF {
    0% {
        background-color: var(--yq-blue-50)
    }

    66% {
        background-color: var(--yq-blue-50)
    }
}

.index-module_bookItem_jMupe {
    display: flex;
    width: 100%;
    flex: 1!important;
    flex: 1 1 auto!important;
    overflow: hidden
}

.index-module_bookItem_jMupe .index-module_iconWrapper_1h300 {
    width: 18px;
    justify-content: center;
    align-items: center;
    display: flex
}

.index-module_bookItem_jMupe .index-module_link_KkvFY {
    width: 100%;
    display: flex;
    align-items: center
}

.index-module_bookItem_jMupe .index-module_link_KkvFY .index-module_name_4D0sT {
    display: inline-block;
    margin-left: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    color: var(--yq-text-body)
}

.index-module_bookItem_jMupe .name-lock {
    color: var(--yq-yuque-grey-7);
    margin-left: 4px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.GroupItem-module_wrapper_52cD0 {
    height: 100%;
    display: flex;
    align-items: center
}

.GroupItem-module_wrapper_52cD0>a {
    display: flex;
    align-items: center
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_content_gCPoO {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_name_7fan9 {
    display: flex;
    align-items: center;
    min-width: 0;
    margin-left: 8px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 22px
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_desc_YfUoO {
    margin-left: 8px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_desc_YfUoO,.GroupItem-module_wrapper_52cD0 .GroupItem-module_nameText_x0iyn {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_displayAccessScopeText_WEtv3 {
    margin-right: 4px
}

.GroupItem-module_wrapper_52cD0 .GroupItem-module_name_7fan9 .GroupItem-module_privacyIcon_dfC1P {
    margin-left: 4px;
    margin-right: 4px;
    top: 0;
    left: 2px;
    color: var(--yq-yuque-grey-7)
}

.GroupItem-module_wrapper_52cD0 .ant-tooltip {
    position: fixed
}

.GroupItem-module_wrapper_52cD0 .name-lock {
    margin-left: 4px;
    color: var(--yq-icon-caption)
}

.GroupItem-module_wrapper_52cD0 .larkui-feature-guide-lite {
    position: static;
    margin-left: 4px;
    z-index: 99
}

.PopoverContent-module_menuItem_bsDWg {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-900)
}

.PopoverContent-module_menuItem_bsDWg svg {
    top: 0!important;
    margin-right: 0
}

.PopoverContent-module_divider_IjtdV {
    width: 100%;
    height: 1px;
    background-color: var(--yq-yuque-grey-2);
    margin: 8px 0
}

.index-module_groupMenuItem_3R1Ir a {
    width: 100%;
    height: 100%
}

.index-module_groupMenuItem_3R1Ir .group-avatar {
    flex: 0 0 auto
}

.index-module_groupMenuItem_3R1Ir .group-avatar>.img {
    border-radius: 4px!important
}

.index-module_groupBadge_wMlu9 {
    border-radius: 3px;
    width: 6px;
    height: 6px;
    background-color: var(--yq-blue-5)
}

.index-module_sidebarMenuWrapper_iw4HJ {
    height: 100%;
    display: flex;
    flex-direction: column
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 {
    border: 0;
    padding: 0 10px;
    width: 100%!important;
    background-color: var(--yq-bg-secondary)
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-inline-collapsed>.ant-menu-item {
    overflow: hidden
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-sub.ant-menu-inline {
    background: transparent
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .icon {
    display: inline-block;
    margin-right: 10px
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .icon-active {
    display: none;
    margin-right: 10px
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-sub.ant-menu-inline>.ant-menu-item,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu>.ant-menu-submenu-title {
    line-height: 32px;
    height: 32px;
    border-radius: 6px;
    width: 100%;
    margin: 4px 0
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item:hover,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-sub.ant-menu-inline>.ant-menu-item:hover,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu>.ant-menu-submenu-title:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item:after,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-sub.ant-menu-inline>.ant-menu-item:after,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu>.ant-menu-submenu-title:after {
    opacity: 0
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item:not(:last-child) {
    margin: 2px 0
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-root {
    border: none;
    width: 100%;
    background-color: var(--yq-bg-secondary)
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item-selected .icon,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-selected>.ant-menu-submenu-title .icon {
    display: none
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item-selected .icon-active,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-selected>.ant-menu-submenu-title .icon-active {
    display: inline-block
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-selected>.ant-menu-submenu-title {
    background-color: var(--yq-bg-primary-hover);
    border-radius: 6px
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-item-icon,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-sub,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-title,.index-module_sidebarMenuWrapper_iw4HJ .index-module_menuWrapper_Y2ft1 .ant-menu-title-content {
    transition: none
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_wikiMenuWrapper_04pIq {
    margin: 0
}

.index-module_sidebarMenuWrapper_iw4HJ .index-module_wikiMenuWrapper_04pIq .ant-menu-item {
    margin: 0 2px
}

.index-module_navigationWrapper_pWOtk {
    max-height: 80vh
}

.index-module_navigationWrapper_pWOtk .index-module_menuWrapper_Y2ft1,.index-module_navigationWrapper_pWOtk .index-module_menuWrapper_Y2ft1 .ant-menu-root {
    background-color: var(--yq-bg-primary)
}

.index-module_collapseSideBarWarp_sGPBl {
    display: flex;
    flex-direction: column;
    height: 100%
}

.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 {
    padding: 0 13.5px;
    margin-bottom: 2px
}

.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-item:not(:last-child) {
    margin: 0 0 6px
}

.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-item,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-sub,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-title {
    transition: none!important
}

.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-item,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-title {
    padding-top: 1px!important
}

.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-selected>.ant-menu-submenu-title .icon,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-selected>.ant-menu-submenu-title .icon-active,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-title .icon,.index-module_collapseSideBarWarp_sGPBl .index-module_menuWrapper_Y2ft1 .ant-menu-submenu-title .icon-active {
    position: relative;
    left: 4px
}

.index-module_collapseSideBarWarp_sGPBl .index-module_otherMenuWrapper_ay541 {
    margin: 4px 0 7px
}

.index-module_collapseSideBarWarp_sGPBl .index-module_bookMenuWrapper_PWx1D,.index-module_collapseSideBarWarp_sGPBl .index-module_groupMenuWrapper_jYSGk {
    margin-top: 0
}

.index-module_personalSubMenuWrapper_fQkuG {
    flex: 0 0 auto
}

.index-module_collapsedPersonalSubMenu_9zyG7 {
    margin-bottom: 4px
}

.index-module_scrollbarWrapper_XQaKC {
    flex: 1 1 auto;
    overflow: auto;
    overflow: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    scrollbar-color: var(--yq-yuque-grey-4);
    scrollbar-width: thin
}

.index-module_scrollbarWrapper_XQaKC::-webkit-scrollbar {
    visibility: hidden;
    width: 8px;
    height: 8px
}

.index-module_scrollbarWrapper_XQaKC::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: var(--yq-bg-secondary)
}

.index-module_scrollbarWrapper_XQaKC::-webkit-scrollbar-thumb {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 8px
}

.index-module_scrollbarWrapper_XQaKC::-webkit-scrollbar-thumb:hover {
    background-color: var(--yq-yuque-grey-5)
}

.index-module_scrollbarWrapper_XQaKC:hover {
    overflow-y: auto
}

.index-module_bottomBorder_Kefbk {
    border-top: 1px solid var(--yq-border-primary)
}

.index-module_resourceMenuWrapper_dt38A {
    height: 100%;
    overflow-y: scroll
}

.index-module_otherMenuWrapper_ay541 {
    flex: 0 0 auto;
    margin-top: 9px;
    margin-bottom: 7px
}

.index-module_bottomAdvertiseWrapper_5sVxv {
    flex: 0 0 auto;
    padding: 0 11px
}

.index-module_menuDriver_lzenT {
    height: 1px;
    background-color: var(--yq-border-primary);
    margin: 0 20px 10px
}

.index-module_userAvatarWrapper_wzam- {
    display: flex;
    justify-content: center
}

.index-module_userAvatarWrapper_wzam- .user-info-avatar {
    width: 32px;
    display: flex;
    justify-content: center
}

.index-module_sidebarMenuHidden_IfNao {
    width: 0;
    transition: width .2s cubic-bezier(.1,0,0,1) 0s;
    overflow: hidden
}

.index-module_syncError_Iax\+d {
    margin: 0 10px 6px
}

.index-module_syncError_Iax\+d svg {
    margin-right: 6px
}

.KnowledgePieGuide-module_wrapper_McOcd {
    max-width: 218px;
    height: 76px;
    position: relative;
    border-radius: 8px;
    background-color: var(--yq-yuque-green-600);
    color: var(--yq-white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px
}

.KnowledgePieGuide-module_wrapper_McOcd .KnowledgePieGuide-module_close_Hta-f {
    position: absolute;
    cursor: pointer;
    top: 8px;
    right: 8px
}

.KnowledgePieGuide-module_breathBorder_JnuNa {
    max-width: 259px;
    height: 55px;
    position: absolute;
    background-color: rgba(0,185,107,.1);
    border: 2px solid var(--yq-yuque-green-600);
    animation: KnowledgePieGuide-module_breathe_hzZ03 2s ease-in-out infinite;
    border-radius: 8px;
    left: 224px;
    top: -20px
}

@keyframes KnowledgePieGuide-module_breathe_hzZ03 {
    0% {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }

    50% {
        box-shadow: 0 0 0 2px var(--yq-yuque-green-600)
    }

    to {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }
}

.index-module_container_YifTz {
    white-space: break-spaces;
    z-index: 1020
}

.index-module_container_YifTz .ant-tooltip-inner {
    background-color: var(--yq-yuque-grey-2);
    padding: 0
}

.index-module_container_YifTz .ant-tooltip-arrow-content {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_container_YifTz p {
    font-size: 14px;
    font-weight: 700
}

.index-module_container_YifTz h4,.index-module_container_YifTz p {
    color: var(--yq-white);
    line-height: 22px
}

.index-module_titleContainer_E19Ts {
    position: relative
}

.index-module_content_u6ksX {
    background-color: var(--yq-blue-5);
    padding: 4px 10px;
    padding-right: 30px;
    border-radius: 6px;
    min-width: 250px;
    margin-top: 6px;
    margin-left: -2px
}

.index-module_close_xIF4s {
    position: absolute!important;
    top: 2px;
    right: -2px;
    color: var(--yq-white)
}

.index-module_close_xIF4s :hover {
    color: var(--yq-white)
}
