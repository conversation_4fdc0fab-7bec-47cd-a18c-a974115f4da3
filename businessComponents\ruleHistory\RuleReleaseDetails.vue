<!-- 规则发布历史详情 -->
<template>
    <div class="rule-release-details">
        <a-tabs v-model:activeKey="editableTabsValue" hide-add type="editable-card" @edit="onEdit">
            <a-tab-pane
                    v-for="item in editableTabs"
                    :key="item.name"
                    :tab="item.title"
                    :closable="item.closable"
            >
                <component :is="item.content" :uuid="uuid" @parentmehods="baseLinedetail" />
            </a-tab-pane>
            <template #tabBarExtraContent>
                <TabRight v-model:editableTabsValue="editableTabsValue" v-model:editableTabs="editableTabs" :showClose="editableTabs.length > 1"/>
            </template>
        </a-tabs>
    </div>

</template>
<script setup lang="ts">
    import { ref, onMounted } from 'vue';
    import { Tabs } from 'ant-design-vue';
    import { useStore } from 'vuex';
    import { defineAsyncComponent } from 'vue';  // 延迟加载组件
    definePageMeta({
        title: '规则发布历史详情',
        path:'/rulePublishHistory/ruleReleaseDetails',
        name:'ruleReleaseDetails'
    })

    const ATabs = Tabs;
    const ATabPane = Tabs.TabPane;

    const props = defineProps({
        uuid: String
    })
    const ruleHistory = defineAsyncComponent(() =>
        import('@/businessComponents/ruleHistory/RuleHistory.vue')
    );
    const ruleHistoryRight = defineAsyncComponent(() =>
        import('@/businessComponents/baseLineManage/RuleHistoryRight.vue')
    );
    const store = useStore();
    const BaseDataUid = ref('');
    const arrList = ref(true);
    const editableTabsValue = ref('designView');
    const editableTabs = ref([
        {
            title: '规则列表',
            name: 'designView',
            content: ruleHistory,
            closable: false
        }
    ]);

    const tMouse = (obj) => {
        obj.setAttribute('title', obj.innerText);
    };

    const baseLinedetail = (v) => {
        const name = `ruleHistoryRight_${v.uuid}`;
       !editableTabs.value.some((i) => {
            return i.name === name;
       }) && editableTabs.value.push({
            title: v.ruleName,
            name: name,
            content: ruleHistoryRight,
            closable: true,
       });
       editableTabsValue.value = name
    }

    const onEdit = (targetKey: string | MouseEvent, action: string) => {
        if("remove" == action){
            remove(targetKey as string);
        }
    }

    const remove = (targetKey: string) => {
        let lastIndex = 0;
        editableTabs.value.forEach((item , i) => {
            if(item.name === targetKey){
                lastIndex = i - 1;
            }
        });
        editableTabs.value = editableTabs.value.filter(editableTab => editableTab.name !== targetKey);
        if (editableTabsValue.value === targetKey) {
            // 如果还有其他标签页，且不只是designView
            if (editableTabs.value.length > 1) {
                if (lastIndex >= 0) {
                    editableTabsValue.value = editableTabs.value[lastIndex].name;
                } else {
                    editableTabsValue.value = editableTabs.value[0].name;
                }
            } else {
                // 如果没有其他标签页或只剩下designView，返回到designView
                editableTabsValue.value = 'designView';
            }
        }
    }

    // 关闭全部
    const changeTab = (val) => {
        if(val === 'close'){
            editableTabs.value = editableTabs.value.filter((item) => {return item.name === 'designView'});
        }
        editableTabsValue.value = 'designView'
    }

    onMounted(() => {
        window.tMouse = tMouse;
    });
</script>

<style lang="scss" scoped>
    .rule-release-details{
        padding: 0 10px;
    }
</style>
