<!--规则模型新增更新参数列表组件-->
<template>
    <a-layout class="params-layout">
        <a-layout-content>
            <a-form v-for="(item, index) in paramsList" :key="index" class="params_form">
                <div style="margin-left: 10px;margin-top: 15px;margin-bottom: 15px">
                    <div style="margin-bottom: 10px;font-size: 15px;">
                        <b>参数&nbsp;[{{item.argLabel}}]
                            <span v-if="item.status==0">已创建</span>
                            <span v-else-if="item.status==2" class="primary">新建</span>
                            <span v-else-if="item.status==3" class="danger">冲突</span>
                        </b>
                    </div>
                    <div class="form_flex_div">
                        <a-form-item label="参数名称" class="form_flex_div_item">
                            <span>{{item.argName}}</span>
                        </a-form-item>
                        <a-form-item label="中文标签" class="form_flex_div_item">
                            <span>{{item.argLabel}}</span>
                        </a-form-item>
                    </div>
                    <div class="form_flex_div">
                        <a-form-item label="参数方向" class="form_flex_div_item">
                            <span>{{ruleArgDirect[item.direct]}}(
                                <span v-if="ruleArgDirect[item.direct]=='in'">输入参数</span>
                                <span v-else-if="ruleArgDirect[item.direct]=='out'">输出参数</span>
                                <span v-else-if="ruleArgDirect[item.direct]=='inout'">输入输出参数</span>
                            )</span>
                        </a-form-item>
                        <a-form-item label="参数类型" class="form_flex_div_item">
                            <span>{{item.argType}}</span>
                        </a-form-item>
                    </div>
                </div>
            </a-form>
        </a-layout-content>
    </a-layout>
</template>

<script setup lang="ts">
interface ParamItem {
    argLabel: string;
    status: number;
    argName: string;
    direct: string;
    argType: string;
}

interface Props {
    paramsList: ParamItem[];
    ruleArgDirect: Record<string, string>;
}

defineProps<Props>();
</script>

<style lang="scss" scoped>
.params-layout {
    min-height: 500px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;

    .ant-layout-content {
        padding: 0 20px;
        height: 100%;
    }
}
.form_flex_div_item {
    margin-bottom: 5px;
}
</style>
