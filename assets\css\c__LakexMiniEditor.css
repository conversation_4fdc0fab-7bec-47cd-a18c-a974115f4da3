.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.TemplateTreeItem-module_item_rGFbi {
    height: 38px
}

.TemplateTreeItem-module_title_15bPv {
    height: 100%;
    display: flex;
    align-items: center
}

.TemplateTreeItem-module_docIcon_494bD {
    margin-right: 12px
}

.ant-tree-treenode-selected .TemplateTreeItem-module_title_15bPv {
    font-weight: 700
}

.TemplateTreeItem-module_name_kpLbs {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateTreeItem-module_menuItem_FWIgC {
    display: flex;
    align-items: center
}

.TemplateTreeItem-module_more_ikuvF {
    white-space: nowrap;
    width: 24px;
    height: 24px;
    display: none;
    justify-content: center;
    align-items: center;
    border-radius: 4px
}

.TemplateTreeItem-module_more_ikuvF:hover {
    background-color: var(--yq-yuque-grey-5)
}

.ant-popover-open.TemplateTreeItem-module_more_ikuvF,.TemplateTreeItem-module_item_rGFbi:hover .TemplateTreeItem-module_more_ikuvF {
    display: flex
}

.ant-popover-open.TemplateTreeItem-module_more_ikuvF {
    background-color: var(--yq-yuque-grey-5)
}

.TemplateTreeItem-module_actionIcon_haD5C {
    display: inline-block!important;
    margin: 0 8px 0 0!important;
    padding: 0!important
}

.TemplateList-module_list_7Gkfr {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start
}

.TemplateList-module_item_SpAc7 {
    width: 215px;
    height: 67px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 8px;
    cursor: pointer;
    transform: translateY(0);
    transition: transform .25s cubic-bezier(.645,.045,.355,1);
    will-change: transform,opacity,margin-top;
    border: 1px solid var(--yq-border-light);
    border-radius: 4px;
    background-color: var(--yq-bg-primary);
    display: flex;
    align-items: center
}

.TemplateList-module_item_SpAc7:hover {
    transform: translateY(-6px);
    box-shadow: 0 4px 5px -5px rgba(0,0,0,.1),0 6px 7px 0 rgba(0,0,0,.06)
}

.TemplateList-module_tip_UaJ8v {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateList-module_name_ywSQW {
    color: var(--yq-text-body);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateList-module_desc_hb3sC {
    font-size: 12px;
    color: var(--yq-text-disable);
    line-height: 17px;
    margin-top: 4px
}

.TemplateList-module_docIconWrapper_Gvaw8 {
    margin-right: 16px;
    line-height: 40px;
    width: 51px;
    height: 51px;
    border-radius: 4px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 0 6px 2px rgba(0,0,0,.05);
    display: flex;
    justify-content: center;
    align-items: center
}

.TemplateList-module_moreImg_12JlL {
    margin-right: 16px;
    width: 80px;
    height: 67px;
    position: relative
}

.TemplateList-module_moreImg_12JlL img {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%
}

.RecentDocs-module_tip_n1Nmq {
    font-size: 15px;
    color: var(--yq-yuque-grey-5)
}

.RecentDocs-module_docItem_GCIP4 {
    width: 194px;
    padding: 10px 12px;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-light);
    border-radius: 4px;
    height: 42px;
    margin-top: 16px;
    cursor: pointer;
    transition: transform .25s cubic-bezier(.645,.045,.355,1),display .25s ease-in-out;
    display: flex;
    align-items: center
}

.RecentDocs-module_docItem_GCIP4:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 5px -5px rgba(0,0,0,.1),0 6px 7px 0 rgba(0,0,0,.06)
}

.RecentDocs-module_title_O3oxy {
    flex: 1;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.RecentDocs-module_icon_e5am\+ {
    margin-right: 8px
}

.EditorBase-module_init_eZbjJ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 3px 3px;
    height: 222px;
    padding: 16px;
    font-size: 14px
}

.EditorBase-module_init_eZbjJ pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 8px 0;
    font-size: 12px
}

.EditorBase-module_editor_zrggr {
    position: relative;
    height: 100%;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px
}

.ne-ui-fullscreen.EditorBase-module_editor_zrggr {
    border: none
}

.index-module_lakeCreateGuide_VutDm {
    position: absolute;
    top: 90px;
    padding: 0 20px;
    z-index: 1
}
