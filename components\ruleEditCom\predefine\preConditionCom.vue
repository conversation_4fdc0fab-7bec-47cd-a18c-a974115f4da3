<template>
  <div class="designView" style="font-weight: 500; position: relative; z-index: 5;">
    <div class="ruleBody" style="padding-left: 0; position: relative; z-index: 5;">
      <ConditionList
        :hasTitle="true"
        pos="r"
        :conditionData="conditionData"
        :validList="validList"
        :locked="locked"
        @conditionChange="onConditionChange"
        @addRule="addRule"
        @addUp="addUp"
        @addChildCondition="addChildCondition"
        @addTailItem="addTailItem"
        @decreaseRule="decreaseRule"
        @logicBtnClick="onLogicBtnClick"
        :replaceItem="replaceItem"
        :preIndex="preIndex"
        :predefineLine="predefineLine"
        :predefineCon="predefineCon"
      />
    </div>
    <ReplaceItem
      :replaceIShow="replaceIShow"
      :replaceIErrInfo="replaceIErrInfo"
      @replaceItemHide="replaceItemHide"
      @replaceItemSure="replaceItemSure"
    />
  </div>
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";
import ConditionList from "@/components/ruleEditCom/condition/conditionList.vue";
import ReplaceItem from "@/components/ruleEditCom/ruleItemList/operationMenu/replaceItem.vue";
import { mxReplace } from "@/components/ruleEditCom/utils/ruleReplace";

const { ConditionGenerate } = util;

const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};

const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
};

const idRef = {
  current: 1000,
};

const props = defineProps({
  conditions: {
    type: Object,
    default: () => ({}),
  },
  validList: {
    type: Array,
    default: () => [],
  },
  locked: Boolean,
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
});

const emit = defineEmits(['onchange', 'replaceItemHide', 'replaceItemSure']);

const ruleUuid = inject('ruleUuid', '');
const replaceIShow = ref(false);
const replaceIErrInfo = ref([]);

const replaceItem = ref(null);
const conditionData = ref({});

conditionData.value = util.getConditionTree(
  props.conditions.conditionExpression
    ? props.conditions.conditionExpression
    : initRuleData.conditionExpression,
  props.conditions.conditions
    ? props.conditions.conditions
    : initRuleData.conditions,
  ruleUuid
);

// 监听 props.conditions 的变化
watch(
  () => props.conditions,
  (newConditions) => {
    conditionData.value = util.getConditionTree(
      newConditions.conditionExpression
        ? newConditions.conditionExpression
        : initRuleData.conditionExpression,
      newConditions.conditions
        ? newConditions.conditions
        : initRuleData.conditions,
      ruleUuid
    );
  },
  { deep: true, immediate: true }
);

const isPreCondition = () => {
  return true;
};

const getCurrentData = () => {
  const currentData = null;
  return { conditionData: conditionData.value, currentData };
};

const onConditionChange = (pos, newContents) => {
  const [, conditionId] = pos.split("_");
  const { targetNode } = util.findTargetNodeInfoById(conditionData.value, conditionId);
  targetNode.ruleCondition.contents = new ConditionGenerate(newContents);
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const addRule = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 1);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData),
    },
  });
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const addUp = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  let arr = conditionData.value.children;
  upArrIndentLayer(arr);
  function upArrIndentLayer(data) {
    for (let i in data) {
      data[i].indent && (data[i].indent += 1);
      if (data[i].ruleCondition && data[i].ruleCondition.layer) {
        data[i].ruleCondition.layer += 1;
        data[i].ruleCondition.showLayer += 1;
      }
      if (data[i].children) {
        upArrIndentLayer(data[i].children);
      }
    }
  }
  const resArr = [
    {
      indent: 1,
      ruleCondition: {
        layer: layer,
        showLayer: layer,
        conditionId: idRef.current++,
        contents: new ConditionGenerate(initConditionData),
      },
    },
    {
      indent: 1,
      logicalSymbol: "and",
      fold: false,
      children: arr,
    },
  ];
  arr = resArr;
  conditionData.value.children = arr;
};

const addChildCondition = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 2);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    fold: false,
    children: [
      {
        indent: targetNode.indent + 1,
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData),
        },
      },
      {
        indent: targetNode.indent + 1,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 2,
          showLayer: layer + 2,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData),
        },
      },
    ],
  });
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const addTailItem = (pos, conditionId) => {
  const { parentNode, targetNode } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  const lastCondition = util.getLastNode(parentNode);
  const { layer } = lastCondition;
  util.updateLayers(layer, conditionData.value, 1);

  arr.push({
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData),
    },
  });
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const decreaseRule = ({ pos, conditionId, layer }) => {
  const { parentNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  if (arr.length === 1) {
    emit("onchange", {});
    return;
  }
  if (arr.length > 2) {
    arr.splice(arrIndex, 1);
  } else if (arr.length === 2) {
    arr.splice(arrIndex, 1);
    const _logicalSymbol = parentNode.logicalSymbol;
    // 如果数组中只有两组数据，删除后，子节点逻辑组合，将转化为一个普通的子节点；
    if (parentNode !== conditionData.value || !!arr[0].children) {
      if (arr[0].ruleCondition) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
        delete parentNode.children;
      } else if (arr[0].children) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
      }
      parentNode.logicalSymbol = _logicalSymbol;
      util.updateIndent(parentNode, -1);
    }
  }
  if (arr && arrIndex / 1 === 0) {
    delete arr[0].logicalSymbol;
  }
  util.updateLayers(layer, conditionData.value, -1);
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const onSwitcherChange = (pos, conditionId, layer) => {
  util.updateNodeFold(conditionData.value, conditionId, layer);
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const onLogicBtnClick = (pos, val) => {
  const [, targetIndent, targetLayer] = pos.split("_");
  const logicNode = util.findLogicNode(
    conditionData.value,
    targetIndent,
    targetLayer
  );
  logicNode.logicalSymbol = val || null;
  emit(
    "onchange",
    util.getConditionDataToBE(
      conditionData.value,
      {
        conditions: [],
        conditionExpression: "",
      },
      ruleUuid
    )
  );
};

const findConditionNodeByPosition = (pos) => {
  const [index, conditionId, path] = pos.split("_"); // eslint-disable-line
  const pathArr = path.split(".");
  const { conditionData: currentConditionData, currentData } = getCurrentData();
  const { targetNode } = util.findTargetNodeInfoById(
    currentConditionData,
    conditionId
  );
  const { node, key } = util.findDataByPath(
    targetNode.ruleCondition.contents.list,
    pathArr
  );
  return { targetNode, ruleItem: node, ruleItemIndex: key, currentData };
};

const replaceItemHide = () => {
  replaceIShow.value = false;
  replaceIErrInfo.value = [];
};

const replaceItemSure = (newItem) => {
  if (replaceItem.value) {
    // 使用 mxReplace 中的方法进行替换
    const result = mxReplace(replaceItem.value, newItem);
    if (result.success) {
      // 替换成功，更新 conditionData
      conditionData.value = result.newConditionData;
      emit(
        "onchange",
        util.getConditionDataToBE(
          conditionData.value,
          {
            conditions: [],
            conditionExpression: "",
          },
          ruleUuid
        )
      );
      replaceItemHide();
    } else {
      // 替换失败，显示错误信息
      replaceIErrInfo.value = Array.isArray(result.error) ? result.error : [result.error];
    }
  }
};
</script>

<style scoped>
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}
</style>
