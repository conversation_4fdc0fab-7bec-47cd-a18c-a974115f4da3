<template>
  <div class="ruleCreate" style="">
    <div v-if="!isGetData">
      <span style="margin-left:15px">loading...</span><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div>
    </div>
    <rule-editor
      v-if="isGetData"
      theme="oneDark_Light"
      :operatorTypeList="operatorTypeList"
      :dataEntryList="dataEntryList"
      :fieldsDataList="fieldsDataList"
      :baseMethodListMap="baseMethodListMap"
      :methodsDataList="methodsDataList"
      :validateResult="validateResult"
      :predefineList="predefineList"
      :initModelData="initModelData"
      :injectModelData="injectModelData"
      :ruleDrls="self_ruleDrl"
      @save="save"
      @validate="validate"
      @changePredefineList="changePredefineList"
      :locked="false"
      :uuid="uuid"
      :demandUuid="demandUuid"
      mode="create"
      ref="rule_editor_com"
    />
  </div>
</template>

<script lang="jsx">
import { mapMutations } from "vuex";
import RuleEditor from "@/components/ruleEditCom/RuleEditor.vue";
import * as util from "@/components/ruleEditCom/utils/util";
import { ruleVerify, ruleSave } from "@/api/rule_editor";
import globalEventEmitter from '~/utils/eventBus';
import { REFRESH_RULE_LIST } from '@/consts/globalEventConsts';
import ErrorMessageFormatter from '@/components/common/ErrorMessageFormatter.vue';

export default {
  name: "RuleCreate",
  inject: {
    ruleUuid: { default: '' },
    message: { default: () => {} },
  },
  components: {
    "rule-editor": RuleEditor
  },
  props: {
    modelData: {
      type: Object,
      default: () => {}
    },
    uuid: String,
    demandUuid: String
  },
  data() {
    return {
      operatorTypeList: null,
      fieldsDataList: [],
      dataEntryList: [],
      baseMethodListMap: {},
      methodsDataList: [],
      validateResult: {},
      initModelData: {},
      injectModelData: {},
      predefineList: [],
      self_ruleDrl: "",
      listMap: {
        dictMap: {},
        initModelData: {},
        order: 1
      }
    };
  },
  watch: {
    modelData: {
      handler: function(val, oldVal) {
        if (Object.keys(val).length > 0) {
          this.getModalInit(val);
          this.initModelData = val;
          this.injectModelData = val;
        }
      },
      deep: true
    }
  },
  computed: {
    isGetData() {
      return Object.keys(this.initModelData).length > 0;
    }
  },
  methods: {
    changePredefineList(val) {
      this.predefineList = val;
      const list = [];
      const includesList = [
        "String",
        "Boolean",
        "Date",
        "Double",
        "Short",
        "Integer",
        "Long",
        "Float",
        "BigDecimal"
      ];
      const numberList = [
        "Double",
        "Short",
        // "Integer",
        // "Long",
        // "Float",
        "BigDecimal"
      ];
      val &&
        val.map(item => {
          const modelData = {
            valueType: numberList.includes(item.variableType)
              ? "Double"
              : item.variableType,
            name: item.id,
            viewName: item.preViewName,
            status: "01",
            display: true
          };
          if (item.operatorType === "equals") {
            if (includesList.includes(item.variableType)) {
              modelData.valueType = item.variableType;
            } else {
              const name = this.getEqualsVariableName(
                item.variableType,
                item.selectVariableValue.conditions[0].variable
              );
              modelData.valueType = name;
              modelData.fieldsList = this.initModelData.complexModels.find(
                items => items.valueType === name
              ).fieldsList;
              modelData.methodsList = this.initModelData.complexModels.find(
                items => items.valueType === name
              ).methodsList;
            }
          } else {
            modelData.valueType = item.variableType;
            const findItem = this.initModelData.complexModels.find(
              items => items.valueType === item.variableType
            );
            modelData.fieldsList = findItem ? findItem.fieldsList : [];
            modelData.methodsList = findItem ? findItem.methodsList : [];
            // modelData.methodsList = item.variableType;
          }

          list.push(modelData);
        });
      const newList = [...this.initModelData.complexModels, ...list];
      this.injectModelData = { ...this.initModelData, complexModels: newList };
      this.getModalInit(this.injectModelData, this.listMap.order + 1);
    },
    getEqualsVariableName(sourceName, data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        name.push(obj.valueType ? obj.valueType : obj.name);
        if (obj.next) {
          deepFn(obj.next);
        }
      }
      if (name.includes(sourceName) && name[name.length - 1].includes(".")) {
        return name[name.length - 1];
      }

      return sourceName;
    },
    getVariableName(data) {
      let name = [];
      deepFn(data);
      function deepFn(obj) {
        if (!obj.next) {
          name.push(obj.valueType ? obj.valueType : obj.name);
        } else {
          deepFn(obj.next);
        }
      }
      return name[0];
    },
    getFieldsType(data) {
      const { baseModelList, datasetGroupList, modelList } = this;
      const baseMethodListMap = util.getBaseMethodMapping(baseModelList);
      const _datasetGroupList = util.getDatasetGroupList(
        datasetGroupList,
        baseMethodListMap
      );
      const { fieldsData, methodsData } = util.getModelList(modelList);
      this.dataEntryList = _datasetGroupList;
      this.fieldsDataList = fieldsData;
      this.methodsDataList = methodsData;
      this.baseMethodListMap = baseMethodListMap;
    },
    getPredefinedRule(list) {
      const predefines = [];
      list &&
        list.map(item => {
          const obj = {
            name: item.id,
            viewName: item.preViewName,
            variableType: item.variableType,
            variableTypeName: item.variableTypeName,
            operatorType: item.operatorType,
            variable: {
              type: item.inputOrSelect,
              value:
                item.inputOrSelect === "Select"
                  ? item.selectVariableValue
                  : item.inputvariableValue
            },
            conditionsRule: item.hasConditions ? item.conditions : {}
          };
          predefines.push(obj);
        });

      return predefines;
    },
    save(ruleData) {
      const { ruleName, ...others } = ruleData;
      const predefines = this.getPredefinedRule(this.predefineList);
      // const param = { ruleName, ruleContent: others, ruleType: "01" };
      const param = {
        ruleUuid: this.uuid,
        demandUuid: this.demandUuid,
        ruleContent: { predefines, ...others }
      };
      // 如果
      param.ruleContent.conditions = this.setExpressionValueType(
        param.ruleContent.conditions
      );
      // 预定义
      for (let i in param.ruleContent.predefines) {
        for (let j in param.ruleContent.predefines[i].conditionsRule
          .conditions) {
          param.ruleContent.predefines[i].conditionsRule.conditions =
            this.setExpressionValueType(
              param.ruleContent.predefines[i].conditionsRule.conditions
            );
        }
      }
      // 那么
      param.ruleContent.actions = this.setExpressionValueType(
        param.ruleContent.actions
      );
      // 否则
      param.ruleContent.elseActions = this.setExpressionValueType(
        param.ruleContent.elseActions
      );
      ruleSave(param)
        .then(res => {
          if (res.data.ruleValidates && res.data.ruleValidates[0]) {
            const result = res.data.ruleValidates[0];
            this.self_ruleDrl = res.data.ruleDrl;
            if (result.validate) {
              message.success("保存成功");
              //更新上级列表状态
              globalEventEmitter.emit(REFRESH_RULE_LIST);
            } else {
              this.validateResult = result;
              message.error({
                dangerouslyUseHTMLString: true,
                content: '<p style="color:#52c41a;margin-bottom:10px">保存成功</p>规则校验失败，请重新编写'
              });
            }
          }
        })
        .catch(error => {
          //message.error(error);
        });
    },
    setExpressionValueType(data) {
      for (let i in data) {
        if (data[i].variable) {
          this.setExpressionParamsValType(
            data[i],
            data[i].variable,
            "variable"
          );
        }
        if (
          (data[i].comparator && data[i].comparator.operatorParams) ||
          data[i].actionParams
        ) {
          let loopData =
            data[i].actionParams ||
            (data[i].comparator && data[i].comparator.operatorParams);
          for (let j in loopData) {
            this.setExpressionParamsValType(loopData[j], loopData[j]);
          }
        }
      }
      return data;
    },
    setExpressionParamsValType(data, obj, tag) {
      const aParItemValType = [];
      if (obj.variableType === "expression") {
        for (let j in obj.expressionParams) {
          if (obj.expressionParams[j].next) {
            getLastNextValType(obj.expressionParams[j].next);
            function getLastNextValType(data) {
              if (data.next) {
                getLastNextValType(data.next);
              } else {
                aParItemValType.push(data.valueType);
              }
            }
          } else {
            aParItemValType.push(obj.expressionParams[j].valueType);
          }
        }
        let isBigDecimal = aParItemValType.find(
          (item) => item === "BigDecimal"
        );
        let isDouble = aParItemValType.find((item) => item === "Double");
        let isFloat = aParItemValType.find((item) => item === "Float");
        let isLong = aParItemValType.find((item) => item === "Long");
        let isInteger = aParItemValType.find((item) => item === "Integer");
        let isShort = aParItemValType.find((item) => item === "Short");
        if (isBigDecimal) {
          tag === "variable" && (data.leftValueType = "BigDecimal");
          obj.valueType = "BigDecimal";
        } else if (isDouble) {
          tag === "variable" && (data.leftValueType = "Double");
          obj.valueType = "Double";
        } else if (isFloat) {
          tag === "variable" && (data.leftValueType = "Float");
          obj.valueType = "Float";
        } else if (isLong) {
          tag === "variable" && (data.leftValueType = "Long");
          obj.valueType = "Long";
        } else if (isInteger) {
          tag === "variable" && (data.leftValueType = "Integer");
          obj.valueType = "Integer";
        } else if (isShort) {
          tag === "variable" && (data.leftValueType = "Short");
          obj.valueType = "Short";
        }
      } else if (obj.next) {
        // 方法
        let _this = this;
        getLastValType(obj.next);
        function getLastValType(data) {
          if (data.next) {
            getLastValType(data.next);
          } else {
            if (data.variableType === "method") {
              const aParamsType = data.paramsList;
              for (let k in data.methodParams) {
                if (data.methodParams[k].variableType === "expression") {
                  data.methodParams[k].valueType = aParamsType[k].valueType;
                }
                if (data.methodParams[k].expressionParams) {
                  for (let l in data.methodParams[k].expressionParams) {
                    _this.setExpressionParamsValType(
                      data.methodParams[k].expressionParams[l],
                      data.methodParams[k].expressionParams[l]
                    );
                  }
                }
              }
            }
          }
        }
      }
    },
    validate(ruleData) {
      const { ruleName, ...others } = ruleData;
      const predefines = this.getPredefinedRule(this.predefineList);
      // const param = { ruleName, ruleContent: others, ruleType: "01" };
      const param = {
        ruleUuid: this.uuid,
        ruleContent: { predefines, ...others }
      };
      // 如果
      param.ruleContent.conditions = this.setExpressionValueType(
        param.ruleContent.conditions
      );
      // 预定义
      for (let i in param.ruleContent.predefines) {
        for (let j in param.ruleContent.predefines[i].conditionsRule
          .conditions) {
          param.ruleContent.predefines[i].conditionsRule.conditions =
            this.setExpressionValueType(
              param.ruleContent.predefines[i].conditionsRule.conditions
            );
        }
      }
      // 那么
      param.ruleContent.actions = this.setExpressionValueType(
        param.ruleContent.actions
      );
      // 否则
      param.ruleContent.elseActions = this.setExpressionValueType(
        param.ruleContent.elseActions
      );
      ruleVerify(param)
        .then(res => {
          if (res.data.ruleValidates && res.data.ruleValidates[0]) {
            const result = res.data.ruleValidates[0];
            this.validateResult = result;
            this.self_ruleDrl = res.data.ruleDrl;

            if (result.validate) {
              message.success("检验成功");
            } else {
              message.error({
                content: () => <ErrorMessageFormatter errorMessage={result.msg} />
              });
            }
          }
        })
        .catch(error => {
          //message.error(error);
        });
    },
    getModalInit(data, order) {
      // this.$forceUpdate();
      const modelData = data;
      const {
        complexModels,
        dicts,
        modelDomains,
        simpleModels,
        sysModels,
        sysOperators
      } = modelData;

      const operatorTypeList = util.getoperatorTypeList(sysOperators);
      const baseMethodListMap = util.getBaseMethodMapping(sysModels);
      const _datasetGroupList = util.getDatasetGroupList(
        simpleModels,
        baseMethodListMap
      );
      const { fieldsDataList, methodsDataList } = util.getModelList(
        complexModels
      );
      const dictMap = {};
      dicts.forEach(item => {
        const { dictName, values } = item;
        dictMap[dictName] = values.map(item2 => {
          return {
            value: item2.key,
            viewName: item2.value
          };
        });
      });
      modelDomains.map(item => {
        const { name, domainAttributes } = item;
        dictMap[name] = domainAttributes.map(item2 => {
          return {
            value: item2.value,
            viewName: item2.viewName
          };
        });
      });
      const firstFieldItemList = util.getFirstFieldItemList(
        fieldsDataList,
        baseMethodListMap,
        fieldsDataList
      );
      let tempObj={
        dataEntryList: _datasetGroupList,
        fieldsDataList,
        methodsDataList,
        baseMethodListMap,
        operatorTypeList,
        firstFieldItemList
      }
      this.setInitModelData({list:tempObj,ruleUuid:this.ruleUuid})
      this.loading = false;
      this.listMap.dictMap = dictMap;
      this.listMap.initModelData = tempObj;
      if (order) {
        this.listMap.order = order;
      }
      this.dataEntryList = _datasetGroupList;
      this.fieldsDataList = fieldsDataList;
      this.methodsDataList = methodsDataList;
      this.baseMethodListMap = baseMethodListMap;
      this.operatorTypeList = operatorTypeList;
      this.setListMap({list:this.listMap,ruleUuid:this.ruleUuid})
    },
    ...mapMutations(["setListMap","setInitModelData"]),
  },
  mounted() {
    const val = this.modelData;
    if (Object.keys(val).length > 0) {
      this.getModalInit(val);
      this.initModelData = val;
    }
  },
};
</script>
<style lang="scss" scoped>
.ruleCreate {
  height: calc(100vh - 170px);
}
</style>
