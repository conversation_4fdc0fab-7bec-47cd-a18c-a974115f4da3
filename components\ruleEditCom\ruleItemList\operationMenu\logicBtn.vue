<template>
  <a-button
    v-if="text === 'unOr'"
    @click="setText"
    :style="buttonStyle"
    :disabled="disabled"
    :class="'logicBtn ' + colorClassName"
  >
    或非
  </a-button>
  <a-button
    v-else-if="text === 'or'"
    @click="setText"
    :style="buttonStyle"
    :disabled="disabled"
    :class="'logicBtn ' + colorClassName"
  >
    或
  </a-button>
  <a-button
    v-else-if="text === 'unAnd'"
    @click="setText"
    :style="buttonStyle"
    :disabled="disabled"
    :class="'logicBtn ' + colorClassName"
  >
    且非
  </a-button>
  <a-button
    v-else
    @click="setText"
    :style="buttonStyle"
    :disabled="disabled"
    :class="'logicBtn ' + colorClassName"
  >
    <span class="el-icon-star-ons">且</span>
  </a-button>
</template>

<script setup>

const props = defineProps({
  text: {
    type: String,
    default: "and"
  },
  pos: {
    type: String,
    default: "erule"
  },
  locked: Boolean
});

const emit = defineEmits(['onClick']);

const attrs = useAttrs();

const colorClassName = computed(() => {
  return props.text === "and" || props.text === "unAnd"
    ? "logicBtnAnd"
    : "logicBtnOr";
});

const disabled = computed(() => {
  return !!props.locked;
});

const buttonStyle = computed(() => {
  const { style } = attrs;
  return style;
});

const setText = () => {
  const textMap = ["and", "or", "unAnd", "unOr"];
  let valueIndex = textMap.findIndex(item => item === props.text);
  if (valueIndex === 3) {
    valueIndex = -1;
  }
  emit("onClick", props.pos, textMap[valueIndex + 1]);
};
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
