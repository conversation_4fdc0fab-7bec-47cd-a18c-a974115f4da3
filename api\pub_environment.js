import request from '@/utils/request'
//发布环境
//列表/查询
export function pubList(params) {
    return request({
      url: 'erule/manage/environment/list',
      method: 'get',
      params, 
    })
  }
//新增/修改提交
export function pubadd(data) {
  return request({
    url: 'erule/manage/environment/saveOrUpdate',
    method: 'post',
    data, 
  })
}
  //获取修改文本框的值
  export function pubupdate(params) {
    return request({
      url: 'erule/manage/environment/getEnvironmentById',
      method: 'get',
      params, 
    })
  }
  //停用/启用用
 
  export function pubstart(data) {
    return request({
      url: 'erule/manage/environment/changeUse',
      method: 'post',
      data,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
    })
  }
 //删除
 export function pubDel(data) {
  return request({
    url: 'erule/manage/environment/delete',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}

//获取发布环境
export function getPublishEnvir() {
  return request({
    url: 'erule/manage/environment/getEnvironmentListByLoginUser',
    method: 'get',
  })
}

// 根据规则库uuid获得所有的发布环境列表
export function pubEnvUUid(params) {
  return request({
    url: 'erule/manage/environment/getEnvironmentListByEngUuid',
    method: 'get',
    params
  })
}
