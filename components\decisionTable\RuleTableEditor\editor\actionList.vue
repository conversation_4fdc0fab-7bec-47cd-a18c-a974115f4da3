<template>
  <div>
    <div class="actionCom">
      <!-- 遍历 actionData 数组，渲染 ActionComTable 组件 -->
      <ActionComTable
        v-for="(item, index) in actionData"
        :key="'action' + index"
        :actionData="actionData[index]"
        :pos="pos + '_' + index"
        :actionValid="actionValidList[index]"
        :locked="locked"
        :isTrack="isTrack"
        :isTable="isTable"
        :noRuleCellUnit="noRuleCellUnit"
        @addActionItem="handleAddActionItem"
        @deleteActionItem="handleDeleteActionItem"
        @onChange="handleActionComChange"
        ref="action_com"
      />
    </div>
  </div>
</template>

<script setup>
import ActionComTable from "./action.vue";

// 定义 props
const props = defineProps({
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: <PERSON>olean,
    default: false,
  },
  actionData: {
    type: Array,
    default: () => [],
  },
  validList: {
    type: Array,
    default: () => [],
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 解构 props
const { pos, locked, isTrack, actionData, validList: actionValidList, isTable } = toRefs(props);

// 定义 emit
const emit = defineEmits(['addActionItem', 'deleteActionItem', 'onActionComChange']);

// 处理添加动作项事件
const handleAddActionItem = (...args) => {
  emit("addActionItem", "action", ...args);
};

// 处理删除动作项事件
const handleDeleteActionItem = (...args) => {
  emit("deleteActionItem", "action", ...args);
};

// 处理动作项变化事件
const handleActionComChange = (...args) => {
  emit("onActionComChange", "action", ...args);
};
</script>
