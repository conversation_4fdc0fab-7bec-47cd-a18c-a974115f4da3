<!-- 大头针实心图标 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-nopin-outlined CommonUsedList-module_moreActionIcon_pAqlB ant-menu-item-icon">
        <g fill="currentColor" fill-rule="nonzero">
            <path
                d="M61.243 76.55 76.985 92.29a76.459 76.459 0 0 0-25.597 12.625l-.701.534 94.924 94.854.42-.55a76.575 76.575 0 0 0 12.718-25.698l15.736 15.736a96.605 96.605 0 0 1-13.663 23.53l-.484.61a18.71 18.71 0 0 1-14.61 6.995 18.701 18.701 0 0 1-13.257-5.48l-96.924-96.852c-7.898-7.898-7.179-20.927 1.526-27.88a96.32 96.32 0 0 1 24.17-14.166Zm99.572 65.631 20.327 20.327a97.1 97.1 0 0 1-4.811 22.342l-16.271-16.272a77.588 77.588 0 0 0 1.108-23.21l-.074-.692-.28-2.495ZM156.002 25c4.931 0 9.698 1.922 13.26 5.483l51.289 51.294c7.274 7.33 7.274 19.192-.027 26.438l-39.132 39.133.028.468c.157 2.77.194 5.539.115 8.298l-18.596-18.597 42.533-42.536-49.471-49.473-42.52 42.551-18.544-18.543a102.46 102.46 0 0 1 7.669.076l1.001.053 39.136-39.162a18.698 18.698 0 0 1 12.824-5.478l.435-.005ZM88.541 69.906l20.332 20.333-2.62-.3c-2.709-.312-5.55-.462-8.55-.462-5.145 0-10.248.508-15.238 1.507l-16.28-16.278a97.145 97.145 0 0 1 22.356-4.8Z">
            </path>
            <path
                d="M96.414 154.586c-3.905-3.906-10.237-3.906-14.142 0L21.93 214.929c-3.905 3.905-3.905 10.237 0 14.142 3.905 3.905 10.237 3.905 14.142 0l60.343-60.343c3.906-3.905 3.906-10.237 0-14.142ZM46.845 28.212l187 187a9.6 9.6 0 0 1-13.576 13.576l-187-187a9.6 9.6 0 0 1 13.576-13.576Z">
            </path>
        </g>
    </svg>
</template>
