@font-face {
  font-family: "iconfontbtn"; /* Project id  */
  src: url('iconfontbtn.ttf?t=1640589300578') format('truetype');
}

.iconfontbtn {
  font-family: "iconfontbtn" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-add:before {
  content: "\e628";
  color:rgba(0,0,0,0.4)
}

.icon-menu:before {
  content: "\e502";
  color:rgba(0,0,0,0.4)
}

.el-icon-delete{
  color:rgba(0,0,0,0.4)
}
