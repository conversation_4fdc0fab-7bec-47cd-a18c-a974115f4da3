<!-- 复制机构树页 -->
<script setup lang="ts">
import { getcopyline, copyTree } from '@/api/post_permissions';
import qs from 'qs';
definePageMeta({
    title: '复制机构树'
})


const router = useRouter();
const route = useRoute();
const message = inject('message')
interface Form {
    copyBusinLine: string;
}

const form = ref<Form>({
    copyBusinLine: '',
});

const spantext = ref(false);
const itemLine = ref(true);
const copybtn = ref(true);
const codeVal = ref('');
const dataval = ref('');
const fromlinUuid = ref('');
const copyLine = ref([]);

const props = defineProps({
    copyUuid: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['success', 'close']);

onMounted(() => {
    fromlinUuid.value = props.copyUuid;
    getline();
});

const getline = () => {
    getcopyline().then((res) => {
        if (res.data == null) {
            itemLine.value = false;
            copybtn.value = false;
            spantext.value = true;
        } else {
            copyLine.value = res.data;
        }
    });
};

const test = (myVal: any, event: any) => {
    dataval.value = myVal.uuid; // 选中的数据
    codeVal.value = myVal.code;
};

const getCopytree = () => {
    const data = qs.stringify({
        fromBusinessUuid: fromlinUuid.value, // 外面条线uuid
        toBusinessUuid: dataval.value, // 里面选择目标条线uuid
    });
    copyTree(data).then((res) => {
        message.success(res.data)
        emit('success');
        emit('close');
    });
};

const resetForm = () => {
    emit('close');
};

// 暴露方法给父组件
defineExpose({
    onSubmit: getCopytree
});
</script>

<template>
    <a-form  :model="form" label-width="80px" style="margin-top: 25px">
        <span style="color: red; margin: 30px 85px; display: block" v-show="spantext">没有要选择的条线!</span>
        <div v-show="itemLine">
            <a-form-item label="业务条线" class="bold-label">
                <a-radio-group v-model:value="form.copyBusinLine" name="type">
                    <a-radio @change="test(item, $event)" v-for="item in copyLine" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-radio>
                </a-radio-group>
            </a-form-item>
        </div>
    </a-form>
</template>

<style scoped>
.bold-label :deep(.ant-form-item-label > label) {
    font-weight: bold;
}
</style>
