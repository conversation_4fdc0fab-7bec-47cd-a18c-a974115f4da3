 <!-- 复制图标 -->

<script setup lang="ts">
interface Props {
  size?: number | string;
  color?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  color: '#cdcdcd'
})

// 计算样式
const iconStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  minWidth: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color
}))
</script>

<template>
  <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" :style="iconStyle">
    <path d="M63.872 895.68h128.256v128h768V126.656h-127.232V0.32H257.152L63.872 193.664v702.016zM256.128 192.704H153.152l102.976-102.976v102.976z m641.216 768.448H256.128V383.68h190.976V190.656h450.304v770.816-0.32h-0.064zM382.272 320.704H279.104L382.272 215.68v105.024z m-255.168 510.976V256.704h192V63.68h448l1.024 65.024H383.104L191.232 321.664v510.016H127.104z" :fill="props.color" p-id="4822"></path>
  </svg>
</template>
