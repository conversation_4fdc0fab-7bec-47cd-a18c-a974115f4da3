<!-- 岗位权限设置页 -->

<script setup lang="ts">
import {
    getAllBusinessByUserId,
    listOrgTree,
    getRoleByBusinessUuid,
    getRoleIdsByUserAndOrgAndBusiness,
    setOrgAction,
} from "@/api/userManagement";
import qs from "qs";
import {useRouter} from "vue-router";
definePageMeta({
    title: '岗位权限设置'
})

const props = defineProps({
    userId: {
        type: String,
        required: true
    },
    loginId: {
        type: String,
        required: true
    }
});

const message = inject('message');
const router = useRouter();
const ruleForm = reactive({
    business: '',
    businessId: '',
});
const rules = {
    business: [
        { required: true, message: '请选择业务条线', trigger: 'change' },
    ],
};
const radioErrorMsg = ref('');
const businessList = ref([]);
const tableData = ref([]);
const treeHidden = ref(true);
const dialogFormVisible = ref(false);
const form = reactive({
    roleList: [],
    roleSelectList: [],
});
const orgId = ref('');
const orgAndRoleIdsArr = ref([]);
const isContainOrg = ref(false);

onMounted(() => {
    getAllBusinessByUserId({
        userId: props.userId,
    }).then((res) => {
        businessList.value = res.data;
    });
});

const submitForm = () => {
    let orgAndRoleIdsStr = '';
    if (orgAndRoleIdsArr.value && orgAndRoleIdsArr.value.length) {
        orgAndRoleIdsStr = orgAndRoleIdsArr.value.toString();
        orgAndRoleIdsArr.value = [];
    } else {
        orgAndRoleIdsStr = orgId.value;
    }
    setOrgAction(
        qs.stringify({
            businessLineId: ruleForm.businessId,
            id: props.userId,
            loginId: props.loginId,
            orgAndRoleIds: orgAndRoleIdsStr,
            isContainOrg: isContainOrg.value ? 1 : 0,
        })
    ).then((res) => {
        orgAndRoleIdsStr = '';
        message.success(res.data);
        dialogFormVisible.value = false;
        listOrgTree({
            loginId: props.loginId,
            businessLineId: ruleForm.businessId,
        }).then((res) => {
            treeHidden.value = false;
            tableData.value = res.data;
        });
    });
};

const checkRadioGet = (e: any) => {
    radioErrorMsg.value = '';
    ruleForm.businessId = e.target.value;
    treeHidden.value = true;
    listOrgTree({
        loginId: props.loginId,
        businessLineId: e.target.value,
    }).then((res) => {
        treeHidden.value = false;
        tableData.value = res.data;
    });
};
interface CustomRowProps {
    onClick?: (event: MouseEvent) => void;
}
const customRow = (record: any): CustomRowProps => {
    return {
        onClick: (event: MouseEvent) => {
            treeClick(record);
        },
    }
}
const treeClick = (record: any) => {
    orgId.value = record.id;
    getRoleByBusinessUuid({
        businessUuid: ruleForm.businessId,
    }).then((resList) => {
        getRoleIdsByUserAndOrgAndBusiness({
            loginId: props.loginId,
            orgCode: record.orgId,
            businessUuid: ruleForm.businessId,
        }).then((res) => {
            form.roleList = [];
            form.roleSelectList = [];
            let resD = res.data;
            if (resD && resD.length) {
                resD = resD.split(',');
                resList.data.forEach((item: any) => {
                    if (resD.includes(item.id.toString())) {
                        form.roleSelectList.push(item.id);
                        item.checked = true;
                    }
                    form.roleList.push(item);
                });
            } else {
                form.roleList = resList.data;
            }
            dialogFormVisible.value = true;
        });
    });
};

const checkboxGet = (name: any) => {
    orgAndRoleIdsArr.value = [];
    name.forEach((item: any, index: number) => {
        orgAndRoleIdsArr.value[index] = `${orgId.value}_${item}`;
    });
};

const resetForm = () => {
    emit('close');
};

const allChecked = () => {
    form.roleSelectList = form.roleList.map((item: any) => item.id);
    orgAndRoleIdsArr.value = [];
    form.roleSelectList.forEach((item: any, index: number) => {
        orgAndRoleIdsArr.value[index] = `${orgId.value}_${item}`;
    });
};

const checkReset = () => {
    form.roleSelectList = [];
};

const emit = defineEmits(['close']);
</script>

<template>
    <!-- 搜索区域 -->
    <a-form
            :model="ruleForm"
            :rules="rules"
            label-width="100px"
            class="demo-ruleForm"
            style="margin-top: 25px"
    >
        <a-form-item label="业务条线" prop="business" :help="radioErrorMsg">
            <a-radio-group v-model:value="ruleForm.business" @change="checkRadioGet">
                <a-radio v-for="item in businessList" :key="item.uuid" :value="item.uuid">{{ item.name }}</a-radio>
            </a-radio-group>
        </a-form-item>
    </a-form>
    <!-- 表格区域 -->
    <a-table
            v-if="tableData && tableData.length"
            :hidden="treeHidden"
            :data-source="tableData"
            style="width: 100%; margin-bottom: 20px;"
            row-key="id"
            :customRow="customRow"
            :bordered="true"
            :defaultExpandAllRows="true"
            :pagination="false"

    >
        <a-table-column key="orgName" title="机构名称" data-index="orgName" :ellipsis="true"></a-table-column>
        <a-table-column key="orgId" title="机构代码" data-index="orgId" :ellipsis="true"></a-table-column>
        <a-table-column key="level" title="等级" data-index="level" :ellipsis="true"></a-table-column>
        <a-table-column key="roleNames" title="关联角色" data-index="roleNames" :ellipsis="true"></a-table-column>
    </a-table>
<a-modal v-if="dialogFormVisible" title="设置角色" v-model:visible="dialogFormVisible" :footer="null">
    <a-form :model="form">
        <a-form-item>
            <a-checkbox-group v-model:value="form.roleSelectList" @change="checkboxGet">
                <a-checkbox v-for="item in form.roleList" :key="item.id" :value="item.id">{{ item.name }}</a-checkbox>
            </a-checkbox-group>
        </a-form-item>
    </a-form>
    <div style="margin-bottom: 20px">
        <a-checkbox v-model:checked="isContainOrg">分配到该条所有子机构</a-checkbox>
    </div>
    <div>
        <a-button type="primary" size="small" @click="allChecked" style="margin-right: 8px;">全选</a-button>
        <a-button type="primary" size="small" @click="checkReset" style="margin-right: 8px;">重置</a-button>
        <a-button type="primary" size="small" @click="dialogFormVisible = false" style="margin-right: 8px;">取消</a-button>
        <a-button type="primary" size="small" @click="submitForm">确定</a-button>
    </div>
</a-modal>
</template>
<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 14px;
    padding-right: 8px;
}

.ant-tree {
    position: relative;
    cursor: default;
    background: #FFF;
    color: #606266;
    right: 22px;
}
</style>
