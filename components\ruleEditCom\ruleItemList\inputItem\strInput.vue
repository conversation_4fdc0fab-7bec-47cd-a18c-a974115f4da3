<template>
  <a-input
    v-model:value.lazy="innerValue"
    :disabled="disabled"
    :style="width"
    @blur="handleChange"
    @keydown="handleKeyDown"
    :title="titleStr"
    :data-val="innerValue"
  />
</template>

<script setup>
import { calculateWidth } from "@/components/ruleEditCom/utils/inputItmeUtil";

const props = defineProps({
  disabled: Boolean,
  value: String,
  inputtype: {
    type: String,
    default: "",
  },
  titleStr: String,
});

const emit = defineEmits(['onChange']);

// 确保 innerValue 初始化为 props.value
const innerValue = ref(props.value);

// 监听 props.value 的变化并更新 innerValue
watch(
  () => props.value,
  (newVal) => {
    innerValue.value = newVal;
  },
  { immediate: true }
);

// 计算宽度
const width = computed(() => {
  return { 
    width: calculateWidth(props.value),
    color: props.inputtype === 'inputObj' ? '#757531' : '#008000' 
  };
});

// 处理输入框失去焦点事件
const handleChange = () => {
  emit("onChange", innerValue.value);
};

// 处理键盘按下事件
const handleKeyDown = (e) => {
  e && e.stopPropagation && e.stopPropagation();
};
</script>
