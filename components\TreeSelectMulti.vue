<!-- 通用树形多选组件 -->
<template>
    <a-tree-select
        v-model:value="selectedValue"
        :tree-data="treeData"
        :field-names="fieldNames"
        :show-search="showSearch"
        :tree-checkable="treeCheckable"
        :multiple="multiple"
        :tree-default-expand-all="treeDefaultExpandAll"
        :filter-tree-node="customFilterTreeNode"
        :placeholder="placeholder"
        :allow-clear="allowClear"
        :dropdown-style="dropdownStyle"
        :disabled="disabled"
        :tree-node-filter-prop="treeNodeFilterProp"
        :tree-node-label-prop="treeNodeLabelProp"
        :max-tag-count="maxTagCount"
        :size="size"
        @change="onChange"
        @search="onSearch"
        @select="onSelect"
        @treeExpand="onTreeExpand"
    >
        <!-- 自定义节点标题渲染 -->
        <template v-if="$slots.title" #title="nodeData">
            <slot name="title" :node-data="nodeData"></slot>
        </template>

        <!-- 自定义下拉框渲染 -->
        <template v-if="$slots.dropdown" #dropdown="dropdownProps">
            <slot name="dropdown" v-bind="dropdownProps"></slot>
        </template>
    </a-tree-select>
</template>

<script setup>

const props = defineProps({
    modelValue: {
        type: [Array, String, Number],
        default: () => []
    },
    treeData: {
        type: Array,
        default: () => []
    },
    // 自定义字段名映射
    fieldNames: {
        type: Object,
        default: () => ({
            children: 'children',
            label: 'title',
            value: 'key',
            key: 'key'
        })
    },
    // 是否显示搜索框
    showSearch: {
        type: Boolean,
        default: true
    },
    // 是否支持多选
    multiple: {
        type: Boolean,
        default: false
    },
    // 是否展示复选框
    treeCheckable: {
        type: Boolean,
        default: true
    },
    // 是否默认展开所有树节点
    treeDefaultExpandAll: {
        type: Boolean,
        default: false
    },
    // 占位符
    placeholder: {
        type: String,
        default: '请选择'
    },
    // 是否支持清除
    allowClear: {
        type: Boolean,
        default: true
    },
    // 下拉菜单样式
    dropdownStyle: {
        type: Object,
        default: () => ({ maxHeight: '300px', overflow: 'auto' })
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 搜索过滤属性
    treeNodeFilterProp: {
        type: String,
        default: ''
    },
    // 标签展示属性
    treeNodeLabelProp: {
        type: String,
        default: ''
    },
    // 最多显示多少个标签
    maxTagCount: {
        type: Number,
        default: undefined
    },
    // 尺寸
    size: {
        type: String,
        default: 'default'
    },
    // 自定义过滤方法
    customFilter: {
        type: Function,
        default: null
    },
    // 加载数据的方法
    loadData: {
        type: Function,
        default: null
    }
});

const emit = defineEmits([
    'update:modelValue',
    'change',
    'search',
    'select',
    'tree-expand',
    'data-loaded'
]);

// 组件内部的选中值
const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => {
        emit('update:modelValue', value);
    }
});

// 自定义过滤树节点方法
const customFilterTreeNode = (inputValue, treeNode) => {
    // 如果提供了自定义过滤方法，则使用它
    if (props.customFilter) {
        return props.customFilter(inputValue, treeNode);
    }

    // 默认过滤方法
    const filterProp = props.treeNodeFilterProp || props.fieldNames.label || 'title';
    const nodeValue = treeNode[filterProp];

    if (!nodeValue) return false;

    return nodeValue.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0;
};

// 事件处理方法
const onChange = (value, labelList, extra) => {
    emit('change', value, labelList, extra);
};

const onSearch = (value) => {
    emit('search', value);
};

const onSelect = (value, node, extra) => {
    emit('select', value, node, extra);
};

const onTreeExpand = (expandedKeys) => {
    emit('tree-expand', expandedKeys);
};

// 加载数据
const loadTreeData = async () => {
    if (props.loadData) {
        try {
            const data = await props.loadData();
            emit('data-loaded', data);
        } catch (error) {
            console.error('复选框数据加载失败:', error);
        }
    }
};

// 监听 treeData 的变化，如果为空且有 loadData 方法，则加载数据
watch(
    () => props.treeData,
    (newVal) => {
        if (!newVal || newVal.length === 0) {
            loadTreeData();
        }
    },
    { immediate: true }
);

onMounted(() => {
    if ((!props.treeData || props.treeData.length === 0) && props.loadData) {
        loadTreeData();
    }
});

// 对外暴露方法
defineExpose({
    loadTreeData,
    // 这里可以暴露更多方法，如：获取选中节点的详细信息等
});
</script>

<style lang="scss" scoped>
.ant-tree-select {
    width: 100%;
}

// 通用样式修复
:deep(.ant-select-tree-switcher-icon) {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

:deep(.ant-select-selection-overflow) {
    flex-wrap: wrap;
}
</style>
