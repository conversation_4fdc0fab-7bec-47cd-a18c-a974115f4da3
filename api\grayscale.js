import request from '@/utils/request'

// API路径
const API_PATH = '/erule/demand2/grayscaleRelease';

// 获取灰度设置列表
export function getGrayscaleList(params) {
    const url = `${API_PATH}/list`;
    return request({
        url,
        method: 'get',
        params
    });
}

// 新增灰度设置
export function addGrayscale(data) {
    return request({
        url: `${API_PATH}/add`,
        method: 'post',
        data
    });
}

// 更新灰度设置
export function updateGrayscale(data) {
    return request({
        url: `${API_PATH}/update`,
        method: 'post',
        data
    });
}

// 删除灰度设置
export function deleteGrayscale(data) {
    return request({
        url: `${API_PATH}/delete`,
        method: 'post',
        data: data
    });
}
