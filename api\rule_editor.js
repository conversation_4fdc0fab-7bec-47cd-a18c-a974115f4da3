import request from "@/utils/request";
//获取编辑器模型init数据
export function ruleModalInit(engUuid, params) {
  return request({
    // url: 'erule/rule/init?engUuid=8a2d8e9f73d7ebb90173d8eace6604dc',
    url: `erule/rule/init?engUuid=${engUuid}`,
    method: "get",
    params
  });
}
//获取编辑器模型init数据(模板使用)
export function userRuleModalInit(engUuid, params) {
  return request({
    url: "erule/rule/init?engUuid=8a2d8e9f73d7ebb90173d8eace6604dc",
    // url: `erule/rule/init?engUuid=${engUuid}`,
    method: "get",
    params
  });
}

//规则详情接口
export function ruleDetail(uuid, type, params) {
  return request({
    url: `erule/rule/detail?ruleUuid=${uuid}&ruleType=${type}`,
    method: "post",
    data: params
  });
}

//规则保存接口
export function ruleSave(params) {
  return request({
    url: "erule/rule/save",
    method: "post",
    data: params
  });
}

//规则检验接口
export function ruleVerify(params) {
  return request({
    url: "erule/rule/verify",
    method: "post",
    data: params
  });
}

export function parseExcel(params) {
  return request({
    url: "erule/rule/parseExcel",
    method: "post",
    data: params
  });
}

export function generateExcel(params) {
  return request({
    url: "erule/rule/generateExcel",
    method: "post",
    data: params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  });
}
//规则流_规则树列表
export function flowTreeList(uuid, params) {
  return request({
    url: `erule/rule/flowTreeList?ruleUuid=${uuid}`,
    method: "get",
    params
  });
}

// 获取规则文本模板标签列表
export function getRuleTextTags(params) {
  return request({
    url: '/erule/demand2/ruleText/findByEngId',
    method: 'get',
    params
  })
}

// 获取业务字段信息
export function findByEngUuid(params) {
  return request({
    url: '/businessField/findByEngUuid',
    method: 'get',
    params
  })
}

//drl接口
export function getRuleDrl(uuid, params) {
  return request({
    url: `erule/rule/ruleDrl?ruleUuid=${uuid}`,
    method: "post",
    data: params
  });
}