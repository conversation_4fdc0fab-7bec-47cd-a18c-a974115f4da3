.AIDocModal-module_aiDocModal_0okhy {
    height: 642px;
    padding: 24px;
    padding-top: 0;
    border-radius: 6px;
    background-image: linear-gradient(180deg,rgba(63,239,165,.08) 0,transparent 82px)
}

.AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_agentList_8yOxG {
    height: 440px;
    overflow: auto;
    display: grid;
    grid-template-rows: repeat(4,98px);
    grid-template-columns: repeat(4,calc(25% - 18px));
    margin-right: -24px;
    grid-gap: 16px;
    gap: 16px;
    scrollbar-color: var(--yq-yuque-grey-600) transparent;
    scrollbar-width: thin
}

.AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_agentList_8yOxG .AIDocModal-module_createAgentInfo_nwXXI {
    width: calc(100% - 97px)
}

@media only screen and (max-width: 1162px) {
    .AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_agentList_8yOxG {
        grid-template-columns:repeat(3,calc(33.33333% - 18.66667px))
    }
}

@media only screen and (max-width: 884px) {
    .AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_agentList_8yOxG {
        grid-template-columns:repeat(2,calc(50% - 20px))
    }
}

@media only screen and (max-width: 475px) {
    .AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_agentList_8yOxG {
        grid-template-columns:1fr
    }
}

.AIDocModal-module_aiDocModal_0okhy .AIDocModal-module_category_ZMvg1 {
    font-size: 14px;
    margin-top: 16px;
    margin-bottom: 8px;
    color: var(--yq-text-caption);
    display: inline-block
}

.AIDocModal-module_aiDocModal_0okhy .ant-input-affix-wrapper-focused,.AIDocModal-module_aiDocModal_0okhy .ant-input-affix-wrapper:focus,.AIDocModal-module_aiDocModal_0okhy .ant-input:focus {
    border-color: var(--yq-theme);
    box-shadow: none
}

.AIDocModal-module_aiDocModal_0okhy .ant-input[disabled] {
    color: var(--yq-ant-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    opacity: .5
}

.AIDocModal-module_loadingAgent_XBTSR {
    width: 262px;
    height: 98px;
    margin-right: 16px;
    margin-bottom: 16px
}

.AIDocModal-module_loadingAgent_XBTSR .ant-skeleton-content .ant-skeleton-paragraph,.AIDocModal-module_loadingAgent_XBTSR .ant-skeleton-content .ant-skeleton-title+.ant-skeleton-paragraph {
    margin-top: 18px
}

.AgentItem-module_agentItem_93YZ5 {
    background-color: var(--yq-bg-secondary);
    height: 98px;
    border-radius: 8px;
    display: flex;
    padding: 16px 12px;
    flex-shrink: 0;
    cursor: pointer;
    border: solid 1px var(--yq-yuque-grey-200)
}

.AgentItem-module_agentItem_93YZ5:hover {
    border: solid 1px var(--yq-yuque-green-600)
}

.AgentItem-module_agentItem_93YZ5 img {
    width: 42px;
    height: 42px;
    border-radius: 5px;
    -o-object-fit: contain;
    object-fit: contain
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    width: calc(100% - 54px)
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_head_30NJb {
    display: flex;
    align-items: center;
    font-weight: 700
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_head_30NJb .AgentItem-module_name_9xqEO {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_head_30NJb .AgentItem-module_tag_ECryM {
    margin-left: 6px;
    font-weight: 400;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 30px;
    border-radius: 4px;
    font-size: 11px
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_head_30NJb .AgentItem-module_tag_ECryM.AgentItem-module_official_ngDhM {
    background-color: var(--yq-pea-green-100);
    color: var(--yq-pea-green-900)
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_head_30NJb .AgentItem-module_tag_ECryM.AgentItem-module_self_5iuQC {
    background-color: var(--yq-yuque-grey-300);
    color: var(--yq-yuque-grey-800)
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_info_Al6Oc .AgentItem-module_desc_9OoDn {
    color: var(--yq-yuque-grey-700);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-top: 6px;
    font-size: 12px
}

.AgentItem-module_agentItem_93YZ5 .AgentItem-module_arrow_m-hUN {
    width: 20px;
    margin-left: 19px;
    align-self: center;
    color: var(--yq-yuque-grey-800)
}

.Header-module_header_JGdBP {
    display: flex;
    height: 69px;
    align-items: center;
    justify-content: space-between
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf {
    display: flex;
    align-items: center;
    cursor: pointer
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_restVal_8PnUX {
    color: var(--yq-pea-green-900);
    font-size: 12px;
    margin-right: 12px
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_restVal_8PnUX.Header-module_empty_lXAM1 {
    color: var(--yq-text-caption)
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_title_9Nbta {
    font-weight: 700;
    font-size: 16px;
    margin: 0 8px 0 6px
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_close_E\+Z9o {
    margin-left: 12px
}

.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_close_E\+Z9o,.Header-module_header_JGdBP .Header-module_inline_p-JWf .Header-module_question_nH0IH {
    cursor: pointer;
    color: var(--yq-text-caption)
}

.Input-module_spin_q7URb {
    margin-right: 8px
}

.Input-module_spin_q7URb .larkui-spin-indicator {
    width: 14px;
    height: 14px
}

.Input-module_sendBtn_PdKgZ {
    width: 78px;
    height: 36px;
    border-radius: 6px;
    background-color: var(--yq-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-yuque-grey-800);
    margin-right: 5px
}

.Input-module_sendBtn_PdKgZ .Input-module_enterIcon_oX-HG {
    border: solid 1px var(--yq-yuque-grey-500);
    border-radius: 4px;
    margin-right: 8px
}

.Input-module_sendBtn_PdKgZ:hover {
    background-color: var(--yq-yuque-grey-200)
}

.Input-module_input_PYqnt {
    height: 54px
}

.Input-module_input_PYqnt.ant-input-affix-wrapper-disabled,.Input-module_input_PYqnt.ant-input-affix-wrapper-disabled .ant-input-disabled {
    background-color: var(--yq-white)
}

.ResultPage-module_skeleton_1J4a3 {
    height: 400px
}

.ResultPage-module_chat_iGNKg {
    min-height: 550px;
    display: flex;
    flex-direction: column;
    justify-content: space-between
}

.ResultPage-module_chat_iGNKg .ResultPage-module_intro_oI-os {
    display: flex;
    width: 100%
}

.ResultPage-module_chat_iGNKg .ResultPage-module_intro_oI-os img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    -o-object-fit: contain;
    object-fit: contain;
    margin-right: 20px
}

.ResultPage-module_chat_iGNKg .ResultPage-module_intro_oI-os .ResultPage-module_info_QBrPT {
    width: calc(100% - 100px)
}

.ResultPage-module_chat_iGNKg .ResultPage-module_intro_oI-os .ResultPage-module_info_QBrPT .ResultPage-module_greet_mt-rB {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.ResultPage-module_chat_iGNKg .ResultPage-module_intro_oI-os .ResultPage-module_info_QBrPT .ResultPage-module_desc_zrMYF {
    font-size: 16px;
    color: var(--yq-yuque-grey-800)
}

.ResultPage-module_chat_iGNKg .ResultPage-module_questionList_rtIct {
    border: solid 1px var(--yq-yuque-grey-200);
    border-radius: 12px;
    padding: 16px;
    margin-top: 24px
}

.ResultPage-module_chat_iGNKg .ResultPage-module_questionList_rtIct .ResultPage-module_tip_3wlHk {
    font-size: 16px;
    color: var(--yq-sea-blue-900);
    font-weight: 700
}

.ResultPage-module_chat_iGNKg .ResultPage-module_questionList_rtIct .ResultPage-module_questionItem_42jmf {
    width: 100%;
    font-size: 14px;
    border-radius: 8px;
    background-color: var(--yq-yuque-grey-100);
    height: 42px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    margin-top: 16px
}

.ResultPage-module_chat_iGNKg .ResultPage-module_questionList_rtIct .ResultPage-module_questionItem_42jmf:hover {
    background-color: var(--yq-yuque-grey-200)
}

.ResultPage-module_chat_iGNKg .ResultPage-module_questionList_rtIct .ResultPage-module_questionItem_42jmf>span {
    margin-right: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ResultPage-module_chat_iGNKg .ResultPage-module_blank_L-eXs {
    flex: 1
}

.ResultPage-module_chat_iGNKg .ResultPage-module_answer_Uf4CM {
    height: 481px;
    overflow: auto;
    width: 100%;
    scrollbar-color: var(--yq-yuque-grey-600) transparent;
    scrollbar-width: thin
}

.ResultPage-module_chat_iGNKg .ResultPage-module_input_MKW8k {
    margin-top: 16px
}

.ResultPage-module_spin_1GdW3 {
    margin-right: 8px
}

.ResultPage-module_spin_1GdW3 .larkui-spin-indicator {
    width: 14px;
    height: 14px
}

.ResultPage-module_completion_XCmV0 {
    position: relative;
    height: 549px;
    overflow: hidden;
    padding-bottom: 0;
    padding-right: 24px;
    margin-right: -24px
}

.ResultPage-module_completion_XCmV0 .ResultPage-module_scrollable_zbuXi {
    overflow: auto;
    max-height: 512px;
    margin-bottom: 48px;
    padding-right: 16px;
    margin-right: -24px;
    scrollbar-color: var(--yq-yuque-grey-600) transparent;
    scrollbar-width: thin
}

.ResultPage-module_completion_XCmV0 .ResultPage-module_footer_Q\+X\+7 {
    width: 100%;
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    background-color: var(--yq-white);
    padding-right: 24px
}

.ResultPage-module_completion_XCmV0 .ResultPage-module_answer_Uf4CM {
    height: 481px;
    margin-top: 24px;
    margin-bottom: 16px
}

.styles-module_selector_Z3\+Wx {
    padding: 24px;
    position: relative
}

.styles-module_title_Misjv {
    font-size: 16px;
    line-height: 24px
}

.TypeWriter-module_typeWriter_gMY9z {
    border: solid 1px var(--yq-yuque-grey-400);
    border-radius: 8px;
    padding: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 2px -2px var(--yq-yuque-grey-300),0 2px 6px 0 var(--yq-yuque-grey-200),0 4px 8px 1px var(--yq-yuque-grey-100)
}

.TypeWriter-module_typeWriter_gMY9z.TypeWriter-module_active_coCvj {
    border-color: var(--yq-yuque-green-400)
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_content_mG50R {
    overflow: auto;
    flex: 1;
    margin-right: -16px;
    padding-right: 16px;
    scrollbar-color: var(--yq-yuque-grey-600) transparent;
    scrollbar-width: thin
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footerDivider_Fszy5 {
    height: 1px;
    background-color: var(--yq-yuque-grey-400);
    margin-right: 12px;
    margin-top: 4px
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT>span {
    color: var(--yq-yuque-grey-600)
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT .TypeWriter-module_actions_MKDDR {
    display: flex;
    align-items: center
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT .TypeWriter-module_actions_MKDDR button {
    margin-right: 12px
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT .TypeWriter-module_actions_MKDDR .TypeWriter-module_iconBtn_a7ovs {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT .TypeWriter-module_actions_MKDDR .TypeWriter-module_iconBtn_a7ovs.TypeWriter-module_active_coCvj {
    color: var(--yq-yuque-green-600)
}

.TypeWriter-module_typeWriter_gMY9z .TypeWriter-module_footer_XufwT .TypeWriter-module_actions_MKDDR .ant-divider.ant-divider-vertical {
    margin-left: 0;
    margin-right: 12px;
    height: 26px
}
