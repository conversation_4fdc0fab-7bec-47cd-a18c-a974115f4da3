import request from '@/utils/request'

// 获取规则库列表
export function list(params) {
  const url = 'erule/manage/engineering/list';

  const executeRequest = () => {
    return request({
      url,
      method: 'get',
      params
    });
  };

  // 应用防重复调用装饰器
  return preventDuplicateRequest(executeRequest, url)(params);
}

// 根据uuid获得规则库信息
export function getEngineeringByUuid(params) {
  return request({
    url: 'erule/manage/engineering/getEngineeringByUuid',
    method: 'get',
    params,
  })
}
// 返回
export function demandRuleRelList(params) {
  return request({
    url: '/erule/demand2/edit/ruleRel/demandRuleRelList',
    method: 'get',
    params,
  })
}

// 根据参数查询数据字典项信息
export function getDicValueByTypeCode(params) {
  return request({
    url: 'sys/dictionaryValue/getDicValueByTypeCode',
    method: 'get',
    params,
  })
}

// 通过模型类型和业务条线获取规则模型
export function ruleBomModelListJson(params) {
  return request({
    url: 'erule/manage/ruleBomModel/ruleBomModelListJson',
    method: 'get',
    params,
  })
}

// 新增或修改规则库信息
export function saveOrUpdate(params) {
  return request({
    url: 'erule/manage/engineering/saveOrUpdate',
    method: 'post',
    data: params
  })
}

// 删除规则库(逻辑删除)
export function ruleBaseDelete(params) {
  return request({
    url: 'erule/manage/engineering/delete',
    method: 'post',
    data: params
  })
}

// 通过uuid获取文件夹信息
export function getRuleFolderByUuid(params) {
  return request({
    url: 'erule/manage/folder/getRuleFolderByUuid',
    method: 'get',
    params,
  })
}

// 规则库下的文件树
export function engineeringTreeList(params) {
  return request({
    url: 'erule/manage/folder/engineeringTreeList',
    method: 'get',
    params,
  })
}
// 规则库下的文件树（所有）
export function engineeringTreeListAll(params) {
  return request({
    url: 'erule/manage/folder/engineeringTreeListAll',
    method: 'get',
    params,
  })
}

// 添加一个请求缓存和锁机制
const pendingRequests = {};

// 用于生成请求的唯一key
function getRequestKey(url, params) {
  return url + JSON.stringify(params);
}

// 防重复调用装饰器
function preventDuplicateRequest(apiFunc, url) {
  return function(params) {
    // 生成请求唯一标识
    const requestKey = getRequestKey(url, params);

    // 如果相同请求正在进行中，则返回缓存的Promise
    if (pendingRequests[requestKey]) {
      return pendingRequests[requestKey];
    }

    // 发起新请求并缓存Promise
    const requestPromise = apiFunc(params).finally(() => {
      // 请求完成后删除缓存
      setTimeout(() => {
        delete pendingRequests[requestKey];
      }, 300); // 设置短暂的冷却时间，防止高频重复请求
    });

    // 存储请求Promise
    pendingRequests[requestKey] = requestPromise;

    return requestPromise;
  };
}

// 修改getRuleList实现，使用防重复调用装饰器
export function getRuleList(params) {
  const url = 'erule/manage/rule/list';

  const executeRequest = () => {
    return request({
      url,
      method: 'get',
      params
    });
  };

  // 应用防重复调用装饰器
  return preventDuplicateRequest(executeRequest, url)(params);
}

// 获取新增或修改文件夹的信息
export function getSaveOrUpdateRuleFolderInfo(params) {
  return request({
    url: 'erule/manage/folder/getSaveOrUpdateRuleFolderInfo',
    method: 'get',
    params,
  })
}

// 新增或修改文件夹信息
export function folderSsaveOrUpdate(params) {
  return request({
    url: 'erule/manage/folder/saveOrUpdate',
    method: 'post',
    data: params
  })
}

// 新增或修改规则并保存
export function ruleSaveOrUpdate(params,businessLine,demandUuid,templateUuid) {
  return request({
    url: `erule/manage/rule/saveOrUpdate?businessLine=${businessLine}&demandUuid=${demandUuid}&templateUuid=${templateUuid}`,
    method: 'post',
    data: params
  })
}

// 通过uuid获取规则信息
export function getRuleByUuid(params) {
  return request({
    url: 'erule/manage/rule/getRuleByUuid',
    method: 'get',
    params,
  })
}

// 检测该规则的加锁状态
export function isLock(params) {
  return request({
    url: `erule/manage/rule/isLock`,
    method: 'post',
    data: params
  })
}

// 删除规则
export function ruleDelete(params) {
  return request({
    url: 'erule/manage/rule/delete',
    method: 'post',
    data: params
  })
}

// 规则加锁
export function toLock(params) {
  return request({
    url: 'erule/manage/ruleLock/toLock',
    method: 'post',
    data: params
  })
}

// 规则解锁
export function unLock(params) {
  return request({
    url: 'erule/manage/ruleLock/unLock',
    method: 'post',
    data: params
  })
}

// 规则提交
export function submitRule(params) {
  return request({
    url: 'erule/manage/rule/submitRule',
    method: 'post',
    data: params
  })
}

// 获取某一业务条线下的所有规则库文件夹列表
export function treeList(params) {
  return request({
    url: 'erule/manage/folder/treeList',
    method: 'get',
    params,
  })
}

// 获取某一业务条线下的模版规则库文件夹列表
export function templateTreeList(params) {
  return request({
    url: 'erule/manage/folder/templateTreeList',
    method: 'get',
    params,
  })
}

export function getRuleFolderChild(params) {
  return request({
    url: 'erule/manage/folder/getRuleFolderChild',
    method: 'get',
    params,
  })
}

// 复制保存
export function copySave(params,newEngUuid,demandUuid,businessLine) {
  return request({
    url: `erule/manage/rule/copySave?newEngUuid=${newEngUuid}&demandUuid=${demandUuid}&businessLine=${businessLine}`,
    method: 'post',
    data: params
  })
}

// 提交选择的规则列表复制到对应的规则包下
export function copyRuleListToFolder(params) {
  return request({
    url: `erule/manage/rule/copyRuleListToFolder`,
    method: 'post',
    data: params
  })
}


// 通过uuids获取规则列表
export function getRuleListByUuids(params) {
  return request({
    url: 'erule/manage/rule/getRuleListByUuids',
    method: 'get',
    params,
  })
}

// 修改重名的规则并复制
export function copySaveMore(params) {
  return request({
    url: `erule/manage/rule/copySaveMore`,
    method: 'post',
    data: params
  })
}


// 显示规则历史表的查询列表
export function getRuleHistoryList(params) {
  return request({
    url: 'erule/manage/rulelHis/list',
    method: 'get',
    params,
  })
}

// 规则集_规则历史详情
export function read2(params) {
  return request({
    url: 'erule/manage/rulelHis/read2',
    method: 'get',
    params,
  })
}


// 规则的历史审核信息
export function ruleCheckList(params) {
  return request({
    url: 'erule/manage/ruleCheck/ruleCheckList',
    method: 'get',
    params,
  })
}

// 从历史表中的数据回滚到rule表中
export function rollBack(params) {
  return request({
    url: `erule/manage/rulelHis/rollBack`,
    method: 'post',
    data: params
  })
}

// 历史规则版本比对
export function compareRule(params) {
  return request({
    url: 'erule/manage/rulelHis/compareRule',
    method: 'get',
    params,
  })
}

// 审核历史规则详情
export function getRuleCheckLogByUuid(params) {
  return request({
    url: 'erule/manage/ruleCheck/getRuleCheckLogByUuid',
    method: 'get',
    params,
  })
}
// 通过uuids获取规则列表
export function getRuleHisListByUuids(params) {
  return request({
    url: 'erule/manage/rulelHis/getRuleHisListByUuids',
    method: 'get',
    params,
  })
}
// 删除文件夹及其子文件夹
export function ruleBaseFolderDelete(params) {
  return request({
    url: `erule/manage/folder/delete`,
    method: 'post',
    data: params
  })
}

// 复制规则库文件
export function ruleBaseFolderCopySave(params,newEngUuid,oldUuid) {
  return request({
    url: `erule/manage/folder/copySave?newEngUuid=${newEngUuid}&oldUuid=${oldUuid}`,
    method: 'post',
    data: params
  })
}

// 导入规则库文件
export function importEng(params) {
  return request({
    url: `erule/manage/engineering/importEng`,
    method: 'post',
    data: params
  })
}
// 规则库导出
export function ruleBaseExport(params) {
  return request({
    url: 'erule/manage/engineering/export',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}

// 规则库模型关系列表
export function getRuleBaseModList(params) {
  return request({
    url: 'erule/manage/ruleModel/list',
    method: 'get',
    params,
  })
}

// 获取规则模型参数接口列表
export function getRuleModArgList(params) {
  return request({
    url: 'erule/manage/ruleArg/list',
    method: 'get',
    params,
  })
}

// 通过uuid获取编辑规则时的详细信息
export function getRuleDetailByUuid(params) {
  return request({
    url: 'erule/manage/rule/getRuleDetailByUuid',
    method: 'get',
    params,
  })
}

// 编辑保存
export function editSave(params) {
  return request({
    url: `erule/manage/rule/editSave`,
    method: 'post',
    data: params
  })
}

// 撤回
export function ruleCall(params) {
  return request({
    url: `erule/manage/rule/ruleCall`,
    method: 'get',
    params
  })
}

// 规则对比
export function contrastRuleByUuid(params) {
  return request({
    url: `erule/manage/rule/contrastRuleByUuid`,
    method: 'get',
    params
  })
}

// 获取登录用户所有的业务条线
export function getAllBusinessByLoginUser() {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get'
  })
}
