import policyImg from '@/components/ruleFlow/img/policy.png';
import convergenceImg from '@/components/ruleFlow/img/convergence.png';
import taskImg from '@/components/ruleFlow/img/task.png';
import liucheng2Img from '@/components/ruleFlow/img/liucheng2.png';
import startImg from '@/components/ruleFlow/img/start.png';
import endImg from '@/components/ruleFlow/img/end.png';

const customElements = [
  'bpmn:StartEvent',
  'bpmn:InclusiveGateway',
  'bpmn:ParallelGateway',
  'bpmn:ExclusiveGateway',
  'bpmn:BusinessRuleTask',
  'bpmn:EndEvent',
  'bpmn:CallActivity'
]; // 自定义元素的类型

const customConfig = { // 自定义元素的配置
  'bpmn:InclusiveGateway': {
    'url': policyImg,
    'attr': { x: 0, y: 0, width: 50, height: 50 }
  },
  'bpmn:ExclusiveGateway': {
    'url': convergenceImg,
    'attr': { x: 0, y: 0, width: 50, height: 50 }
  },
  'bpmn:ParallelGateway': {
    'url': '',
    'attr': { x: 0, y: 0, width: 50, height: 50 }
  },
  'bpmn:BusinessRuleTask': {
    'url': taskImg,
    'attr': { x: 0, y: 0, width: 40, height: 40 }
  },
  'bpmn:CallActivity': {
    'url': liucheng2Img,
    'attr': { x: 0, y: 0, width: 40, height: 40 }
  },
  'bpmn:StartEvent': {
    'id': 'tssssss',
    'url': startImg,
    'attr': { x: 0, y: 0, width: 40, height: 40 }
  },
  'bpmn:EndEvent': {
    'url': endImg,
    'attr': { x: 0, y: 0, width: 36, height: 36 }
  }
};

export { customElements, customConfig };
