<template>
  <div style="display: inline-block">
    <a-dropdown>
      <a class="ant-dropdown-link" @click.prevent>
        <ToolOutlined />
      </a>
      <template #overlay>
        <a-menu @click="onMenuChange">
          <a-menu-item key="addVar" :disabled="index !== paramLength - 1">
            <span>增加</span>
          </a-menu-item>
          <a-menu-item key="addVars" :disabled="index !== paramLength - 1">
            <span>批量文本增加</span>
          </a-menu-item>
          <a-menu-item
            key="checkAddVars"
            :disabled="!enumDictName || index !== paramLength - 1"
          >
            <span>批量勾选增加</span>
          </a-menu-item>
          <a-menu-item key="deleteVar" :disabled="paramLength === 1">
            <span>删除</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>

// 定义 props
const props = defineProps({
  paramLength: Number,
  enumDictName: String,
  index: Number,
});

// 定义 emit 事件
const emit = defineEmits(['onChange']);

// 方法
const onMenuChange = (obj) => {
  obj.domEvent.stopPropagation();
  emit("onChange", obj.key, props.index);
};
</script>

<style scoped>
.ant-dropdown-link {
  cursor: pointer;
}
</style>
