<template>
  <span>
    <input-item
      v-if="variableType === 'constant' || enumDictName"
      :key="pos + 'in'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :enumDictName="enumDictName"
      @onChange="onChange"
      ref="ii_ref"
    />
    <prop-select-com
      v-else-if="variableType === 'dataEntry'"
      flag="dataEntry"
      :key="pos + 'da'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      :preIndex="preIndex"
      @onChange="onChange"
      :dataForLoad="fieldsDataList"
      @calcPropData="calcPropData"
      :nameList="nameList"
      :next="next"
      :nextOptions="nextOptions"
    />
    <prop-select-com
      v-else-if="variableType === 'field'"
      flag="field"
      :key="pos + 'fi'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :dataForLoad="fieldsDataList"
      @onChange="onChange"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      :preIndex="preIndex"
      @calcPropData="calcPropData"
      :nameList="nameList"
      :next="next"
      :nextOptions="nextOptions"
    />
    <method-com
      v-else-if="variableType === 'method'"
      :key="pos + 'me'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :dataForLoad="fieldsDataList"
      :methodData="getData"
      @onChange="onChange"
    />
    <expression-com
      v-else-if="variableType === 'expression'"
      :key="pos + 'ex'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      @onChange="onChange"
    />
    <span v-else :key="pos + 'er'">渲染错误</span>
  </span>
</template>

<script setup>
import store from "@/store";
import InputItem from "@/components/ruleEditCom/ruleItemList/inputItem/inputItem.vue";
import PropSelectCom from "@/components/ruleEditCom/ruleItemList/propSelect/propSelectCom.vue";
import MethodCom from "@/components/ruleEditCom/ruleItemList/method/MethodCom";
import ExpressionCom from "@/components/ruleEditCom/ruleItemList/expression/expressionCom.vue";
import { getLabelArrByValues } from "@/components/ruleEditCom/utils/displayUtil";
import * as util from "@/components/ruleEditCom/utils/util";
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";
import { ref, computed, watch, inject } from "vue";

// 注入 ruleUuid
const ruleUuid = inject("ruleUuid", "");

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  variableData: {
    type: Object,
    default: () => ({}),
  },
  propOptions: {
    type: Array,
    default: () => [],
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  predefineLine: {
    type: String,
    default: "",
  },
  predefineCon: {
    type: String,
    default: "",
  },
  preIndex: {
    type: Number,
    default: 0,
  },
});

// 定义组件的 emits
const emit = defineEmits(["onChange", "onOperatorParamChange"]);

// 定义响应式数据
const defaultOptions = ref([]);

// 计算属性，根据 variableData 返回 enumDictName
const enumDictName = computed(() => {
  return props.variableData.enumDictName;
});

// 计算属性，根据 variableData 返回 variableType
const variableType = computed(() => {
  return props.variableData.variableType;
});

// 计算属性，根据 variableData 返回 valueType
const valueType = computed(() => {
  return props.variableData.valueType;
});

// 计算属性，根据 propOptions 和 variableType 返回 getData
const getData = computed(() => {
  let data = null;
  const { propOptions } = props;
  if (variableType.value === "dataEntry" || variableType.value === "field") {
    data = propOptions.length > 0 ? propOptions : defaultOptions.value;
  } else if (variableType.value === "method") {
    data = propOptions.length > 0 ? propOptions : methodsDataList.value;
  }
  return data;
});

// 计算属性，根据 store 返回 fieldsDataList
const fieldsDataList = computed(() => {
  const {
    initModelData: { fieldsDataList },
  } = store.getters.listMap[ruleUuid];
  return dataFilter(fieldsDataList) || [];
});

// 计算属性，根据 store 返回 firstFieldItemList
const firstFieldItemList = computed(() => {
  const {
    initModelData: { firstFieldItemList },
  } = store.getters.listMap[ruleUuid];
  return dataFilter(firstFieldItemList) || [];
});

// 计算属性，根据 store 返回 methodsDataList
const methodsDataList = computed(() => {
  const {
    initModelData: { methodsDataList },
  } = store.getters.listMap[ruleUuid];
  return dataFilter(methodsDataList) || [];
});

// 计算属性，根据 variableData 返回 value
const value = computed(() => {
  return props.variableData.value;
});

// 计算属性，根据 getData 和 value 返回 nameList
const nameList = computed(() => {
  let ml =
    getData.value && value.value && value.value.length > 0
      ? getLabelArrByValues(getData.value, value.value)
      : [];
  return ml;
});

// 计算属性，根据 variableData 返回 next
const next = computed(() => {
  return props.variableData.next;
});

// 计算属性，根据 valueType 返回 nextOptions
const nextOptions = computed(() => {
  return next.value ? getNextOptions(valueType.value) : [];
});

// 处理 calcPropData 事件
const calcPropData = (
  e,
  pos,
  preIndex,
  predefineLine,
  predefineCon,
  variableData,
  fn
) => {
  let filterOData = filterOptionData(getData.value, variableData);
  if (preIndex && predefineLine) {
    filterOData = filterOData.filter((item) => {
      let temp_str = item.value && item.value.split(".erule.predefine");
      if (predefineCon === "predefineCon") {
        if (temp_str[1] <= preIndex) {
          return item;
        } else {
          return item.value.indexOf(`predefine`) === -1;
        }
      } else {
        if (temp_str[1] < preIndex) {
          return item;
        } else {
          return item.value.indexOf(`predefine`) === -1;
        }
      }
    });
  }
  globalEventEmitter.emit(SHOW_ACASCADER, { e, pos, filterOData, fn });
};

// 过滤选项数据
const filterOptionData = (argdata, variableData) => {
  const { __context = {} } = variableData;
  const { owner = {}, isParam, isRootVar } = __context;
  const { actionType } = owner;
  const withoutMethod = (data) => {
    return data.map((item) => {
      const { children } = item;
      if (children && children.length > 0) {
        const childrenArray = withoutMethod(children);
        return { ...item, children: childrenArray };
      }
      return item;
    });
  };

  if (actionType === "setValue" && isParam === "actionSetLeft") {
    let _argdata = withoutMethod(argdata);
    let posStr = "_setValueCom_";
    if (props.pos.indexOf(posStr) !== -1) {
      let itemIndex = props.pos.split(posStr)[1].split("_");
      if (itemIndex && itemIndex.length === 1) {
        return filterSetInvoke(_argdata, false);
      }
    }
    return _argdata;
  }

  if (actionType === "invokeMethod" && isRootVar) {
    let _argdata = withoutMethod(argdata);
    let posStr = "_actionMethod_";
    if (props.pos.indexOf(posStr) !== -1) {
      let itemIndex = props.pos.split(posStr)[1].split("_");
      if (itemIndex && itemIndex.length === 1) {
        return filterSetInvoke(_argdata, true);
      }
    }
    return _argdata;
  }

  return argdata;
};

// 过滤设置调用
const filterSetInvoke = (data, isMethod) => {
  for (let i = data.length - 1; i >= 0; i--) {
    if (data[i].children && data[i].children.length) {
      for (let j = data[i].children.length - 1; j >= 0; j--) {
        if (
          (isMethod && !data[i].children[j].isMethodItem) ||
          (!isMethod && data[i].children[j].isMethodItem)
        ) {
          data[i].children.splice(j, 1);
        }
      }
    }
    if (!data[i].children || data[i].children.length <= 0) {
      data.splice(i, 1);
    }
  }
  return data;
};

// 获取下一个选项
const getNextOptions = (valueType) => {
  const childModelOptions = getChildModelOptions(valueType);
  const baseMethodList = getBaseMethodList(valueType);
  const nextOptions = [];
  childModelOptions.forEach((item) => {
    item.isLeaf = false;
    nextOptions.push(item);
  });
  baseMethodList.forEach((item) => {
    item.isLeaf = true;
    nextOptions.push(item);
  });
  return nextOptions;
};

// 获取基础方法列表
const getBaseMethodList = (valueType) => {
  const { owner = {}, isParam } = props.variableData.__context;
  const { actionType } = owner;

  // 对于action中的值统一不再返回基础数据类型方法；
  if (actionType && isParam === "actionSetLeft") {
    return [];
  } else {
    const baseMethodListMap =
      store.getters.listMap[ruleUuid].initModelData.baseMethodListMap;

    const _baseMethodList = baseMethodListMap[valueType]
      ? baseMethodListMap[valueType]
      : valueType.includes("List<")
      ? baseMethodListMap["List"]
      : [];
    return _baseMethodList;
  }
};

// 获取子模型选项
const getChildModelOptions = (valueType) => {
  let childModelOptions;
  getData.value.forEach((item) => {
    if (
      item.valueType === valueType &&
      item.children &&
      item.children.length > 0
    ) {
      childModelOptions = filterChildOptions(item.children);
    }
  });
  return childModelOptions || [];
};

// 过滤子选项
const filterChildOptions = (options = []) => {
  const { owner = {}, isParam } = props.variableData.__context;
  const { actionType = "" } = owner;
  return options.filter((item) => {
    if (!item.isMethodItem || item.isMethodItem) {
      return true;
    } else if (item.position === "02" && isParam !== "actionSetLeft") {
      return true;
    } else if (
      item.position === "01" &&
      actionType &&
      isParam !== "actionSetLeft"
    ) {
      return true;
    }
    return false;
  });
};

// 更新缓存
const updateCache = () => {
  const {
    initModelData: { dataEntryList },
  } = store.getters.listMap[ruleUuid];
  defaultOptions.value = [...dataEntryList, ...firstFieldItemList.value];
};

// 数据过滤
const dataFilter = (fieldsDataList) => {
  if (valueType.value === "Object" || valueType.value.includes(".")) {
    return fieldsDataList;
  }
  const filterArray = filterFn(fieldsDataList);
  return filterArray;
};

// 处理 onChange 事件
const onChange = (pos, _newVariableData, finalValueType) => {
  emit("onChange", pos, _newVariableData, finalValueType);
};

// 过滤数字类型
const filterNumbFn = (type, itemType) => {
  const numList = ["Short", "Integer", "Float", "Double", "BigDecimal", "Long"];
  if (numList.includes(type)) {
    return numList.includes(itemType);
  }
  return false;
};

// 过滤函数
const filterFn = (list) => {
  let res = [];
  list.forEach((item) => {
    if (item.valueType.includes(".") && item.children) {
      const childList = filterFn(item.children);
      if (childList.length > 0) {
        res.push({
          ...item,
          children: childList,
        });
      }
    } else {
      if (
        item.valueType === valueType.value ||
        filterNumbFn(valueType.value, item.valueType)
      ) {
        res.push(item);
      }
    }
  });
  return res;
};

// 监听 store 中的 listMap.order 变化
watch(
  () => store.getters.listMap[ruleUuid].order,
  () => {
    updateCache();
  },
  { deep: true }
);
onMounted(() => {
  updateCache();
});
</script>
