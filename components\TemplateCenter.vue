<template>
  <div class="styles-module_templateCenter_nWehM" ref="templateCenterRef">
    <div class="styles-module_previewer_WyUa2">
      <div
        class="DocPreviewer-module_previewer_hqGVo styles-module_docPreviewer_7A0pQ"
        data-template-id="17024332"
      >
        <div class="DocPreviewer-module_templatePreviewer_ZItob">
          <div data-lark-doc-embed="true">
            <div class="TemplateDocViewer-module_view_35DTA">
              <div class="TemplateDocViewer-module_content_jE3Dj">
                <div class="TemplateDocViewer-module_title_lmsM-">
                  <h1
                    id="article-title"
                    class="index-module_articleTitle_VJTLJ doc-article-title"
                    style="font-size: 16px;line-height: unset !important;"
                  >
                    {{ selectedTemplate?.templateName }}
                  </h1>
                </div>
                <div
                  class="yuque-doc-content"
                  data-df="lake"
                  style="position: relative"
                >
                  <div>
                    <div
                      class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed"
                      data-viewer-mode="normal"
                      id="ud032"
                    >
                      <!-- 规则内容 -->
                      <RuleEdit
                        v-if="type"
                        :type="type"
                        :jsonContentObject="jsonContentObject"
                        :engUuid="eng_uuid"
                        :demandUuid="demandUuid"
                        :isTemplate="true"
                      />
                    </div>
                    <div style="height: 0px; overflow: hidden">​</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="styles-module_operations_fzS4X">
      <div class="styles-module_header_lmrkY">
        <div class="styles-module_title_yvutm">
          <svg
            width="1em"
            height="1em"
            viewBox="0 0 256 256"
            xmlns="http://www.w3.org/2000/svg"
            class="larkui-icon larkui-icon-template-center icon-svg index-module_size_wVASz"
            data-name="TemplateCenter"
            style="width: 22px; min-width: 22px; height: 22px"
          >
            <g fill="none" fill-rule="evenodd">
              <path d="M0-2h256v256H0z"></path>
              <path
                d="M85.347 68.498c4.184 0 8.268.43 12.21 1.25l-10.487 42.1-.115.482c-2.668 11.56 1.745 23.148 10.454 30.14l.196.155-25.044 44.135C45.708 180.9 25.6 156.958 25.6 128.315c0-33.036 26.75-59.817 59.747-59.817Z"
                fill="#3384F5"
              ></path>
              <path
                d="m138.617 27.454.308.074c.003 0 .006.001.006.01l75.654 18.886c8.708 2.183 14.009 11.004 11.848 19.718l-18.87 75.777c-2.137 8.614-10.78 13.897-19.394 11.925l-.307-.073c-.003-.001-.006-.002-.007-.011l-2.925-.73-23.394-41.224a26.698 26.698 0 0 0-10.042-10.042c-12.824-7.278-29.12-2.781-36.397 10.042l-10.586 18.654c-3.805-3.903-5.565-9.626-4.157-15.303l18.87-75.777c2.137-8.614 10.78-13.897 19.393-11.926Z"
                fill="#ECB604"
              ></path>
              <path
                d="m149.63 118.563 51.308 90.407c3.546 6.249 1.355 14.189-4.893 17.735a13.01 13.01 0 0 1-6.421 1.695H87.01c-7.185 0-13.01-5.824-13.01-13.01 0-2.25.585-4.462 1.696-6.42l51.307-90.407c3.546-6.249 11.486-8.44 17.735-4.894a13.01 13.01 0 0 1 4.893 4.894Z"
                fill="#E4495B"
              ></path>
            </g></svg
          >模板中心
        </div>
      </div>
      <div class="styles-module_body_N8IY-">
        <button
          data-aspm-click="c100045.d207094"
          type="button"
          class="ant-btn ant-btn-primary ant-btn-block"
          @click="handleUseTemplate"
        >
          <span>使用此模板</span>
        </button>
        <div>
          <a-tabs class="styles-module_tabs_eCZbD" size="small">
            <a-tab-pane key="personal" tab="我的">
              <a-spin :spinning="loading">
                <a-tree
                  class="TemplateTree-module_templateTree_EaM86"
                  :tree-data="myTreeData"
                  :block-node="true"
                  :show-icon="false"
                  :selectable="true"
                  :selected-keys="[selectedTemplateId]"
                  :height="myTreeHeight"
                  @select="handleSelect"
                >
                  <template #title="{ title, key }">
                    <div class="TemplateTreeItem-module_item_rGFbi">
                      <div class="TemplateTreeItem-module_title_15bPv">
                        <IconRuleTypeOrdinaryRule
                          class="TemplateTreeItem-module_docIcon_494bD"
                        />
                        <a-tooltip :title="title" placement="bottomLeft">
                          <span
                            class="TemplateTreeItem-module_name_kpLbs"
                          >{{ title }}</span>
                        </a-tooltip>
                        <span>
                          <a-popover
                            placement="bottomRight"
                            trigger="click"
                            overlayClassName="larkui-popover larkui-popover-menu"
                          >
                            <template #content>
                              <a-menu>
                                <a-menu-item
                                  key="edit"
                                  class="TemplateTreeItem-module_menuItem_FWIgC"
                                  @click="openTemplateEditor(key)"
                                >
                                  <IconEdit />编辑
                                </a-menu-item>
                                <a-menu-item
                                  key="delete"
                                  class="TemplateTreeItem-module_menuItem_FWIgC"
                                  @click="handleDeleteTemplate(key, title)"
                                >
                                  <IconDelete />删除
                                </a-menu-item>
                                <a-menu-item
                                  key="setCommonTemplate"
                                  class="TemplateTreeItem-module_menuItem_FWIgC"
                                  @click="handleSetCommonTemplate(key)"
                                >
                                  设为公共模板
                                </a-menu-item>
                              </a-menu>
                            </template>
                            <span class="TemplateTreeItem-module_more_ikuvF">
                              <IconMore />
                            </span>
                          </a-popover>
                        </span>
                      </div>
                    </div>
                  </template>
                </a-tree>
              </a-spin>

              <div
                class="TemplateTree-module_creation_SNQOH"
                style="height: 38px"
              >
                <a-popover
                  overlayClassName="HeadNewButton-module_headNewPopover_DoiNN larkui-popover larkui-popover-menu"
                >
                  <template #content>
                    <a-menu class="HeadNewButton-module_headNewMenu_TXIWb">
                      <a-menu-item
                        v-for="ruleType in ruleTypes"
                        :key="ruleType.code"
                        @click="
                          showChooseRuleBaseModalOnDashboard(
                            ruleType.name,
                            ruleType.code
                          )
                        "
                      >
                        <a class="HeadNewButton-module_menuItemContainer_VLntH">
                          <div class="HeadNewButton-module_iconWarp_o9rNt">
                            <component
                              :is="ruleType.icon"
                              :size="18"
                              class="HeadNewButton-module_iconContainer_HmX2B"
                            />
                          </div>
                          <span>{{ ruleType.name }}</span>
                        </a>
                      </a-menu-item>
                    </a-menu>
                  </template>

                  <!-- <button
                    type="button"
                    class="ant-btn ant-btn-dashed ant-btn-block"
                  >
                    <svg
                      width="1em"
                      height="1em"
                      viewBox="0 0 16 16"
                      xmlns="http://www.w3.org/2000/svg"
                      class="larkui-icon larkui-icon-plus icon-svg index-module_size_wVASz"
                      data-name="Plus"
                      style="
                        margin-right: 8px;
                        vertical-align: middle;
                        width: 16px;
                        min-width: 16px;
                        height: 16px;
                      "
                    >
                      <path
                        d="M8.018 2.91c.305 0 .556.223.6.516l.007.09-.001 3.96h3.862a.606.606 0 0 1 .09 1.206l-.09.007H8.624v3.803a.606.606 0 0 1-1.205.089l-.007-.09V8.69H3.513a.606.606 0 0 1-.09-1.207l.09-.006h3.897v-3.96c0-.335.272-.607.607-.607Z"
                        fill="currentColor"
                        fill-rule="evenodd"
                      ></path>
                    </svg>
                    <span style="vertical-align: middle">新建模板</span>
                  </button> -->
                </a-popover>
              </div>
            </a-tab-pane>
            <a-tab-pane key="shared" tab="共享">
              <a-tree
                class="TemplateTree-module_templateTree_EaM86"
                :tree-data="sharedTreeData"
                :block-node="true"
                :show-icon="false"
                :selectable="true"
                :selected-keys="[selectedTemplateId]"
                :height="sharedTreeHeight"
                @select="handleSelect"
              >
                <template #title="{ title, key }">
                  <div class="TemplateTreeItem-module_item_rGFbi">
                    <div class="TemplateTreeItem-module_title_15bPv">
                      <IconRule class="TemplateTreeItem-module_docIcon_494bD" />
                      <a-tooltip :title="title" placement="bottomLeft">
                        <span
                          class="TemplateTreeItem-module_name_kpLbs"
                        >{{ title }}</span>
                      </a-tooltip>
                      <span>
                        <a-popover
                          placement="bottomRight"
                          trigger="click"
                          overlayClassName="larkui-popover larkui-popover-menu"
                        >
                          <template #content>
                            <a-menu>
                              <a-menu-item
                                key="edit"
                                class="TemplateTreeItem-module_menuItem_FWIgC"
                                @click="openTemplateEditor(key)"
                              >
                                <IconEdit />编辑
                              </a-menu-item>
                              <a-menu-item
                                key="delete"
                                class="TemplateTreeItem-module_menuItem_FWIgC"
                                @click="handleDeleteTemplate(key, title)"
                              >
                                <IconDelete />删除
                              </a-menu-item>
                              <a-menu-item
                                key="setCommonTemplate"
                                class="TemplateTreeItem-module_menuItem_FWIgC"
                                @click="handleSetCommonTemplate(key)"
                              >
                                取消公共模板
                              </a-menu-item>
                            </a-menu>
                          </template>
                          <span class="TemplateTreeItem-module_more_ikuvF">
                            <IconMore />
                          </span>
                        </a-popover>
                      </span>
                    </div>
                  </div>
                </template>
              </a-tree>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { saveRuleTemplate, getTemplateList, deleteTemplate, setOrCancelCommonTemplate, getTemplateDetail } from '@/api/dashboardApi'

interface Props {
  showChooseRuleBaseModal?: (title: string, onSelect?: (ruleBase: { uuid: string, name: string }) => void) => void
  modal?: any
  message?: any
  eng_uuid?: string,
  onUpdateRuleContent?: (content: Record<string, any>) => void
}

const props = defineProps<Props>()

const templateCenterRef = ref<HTMLElement | null>(null)
// 分别为两个标签页的树设置高度
const myTreeHeight = ref(0)
const sharedTreeHeight = ref(0)

// 计算单个树节点的高度（包括padding等）
const TREE_NODE_HEIGHT = 32 // 每个节点的高度

// 计算树形组件的高度
const updateTreeHeight = () => {
  if (templateCenterRef.value) {
    // 获取对话框的总高度
    const containerHeight = templateCenterRef.value.clientHeight

    // 计算"我的"标签页可用高度
    const myAvailableHeight = containerHeight - 231
    // 计算"我的"标签页树节点总高度
    const myTotalNodesHeight = myTreeData.value.length * TREE_NODE_HEIGHT
    // 只有当内容超出时才设置高度
    myTreeHeight.value = myTotalNodesHeight > myAvailableHeight ? myAvailableHeight : 0

    // 计算"共享"标签页可用高度
    const sharedAvailableHeight = containerHeight - 193
    // 计算"共享"标签页树节点总高度
    const sharedTotalNodesHeight = sharedTreeData.value.length * TREE_NODE_HEIGHT
    // 只有当内容超出时才设置高度
    sharedTreeHeight.value = sharedTotalNodesHeight > sharedAvailableHeight ? sharedAvailableHeight : 0
  }
}
// 规则uuid
const jsonContentObject = ref<Record<string, any>>({});
// 规则demandUuid
const demandUuid = ref("");
// 规则类型
const type = ref("");

// 监听窗口大小变化
onMounted(() => {
  updateTreeHeight()
  window.addEventListener('resize', updateTreeHeight)
  fetchTemplateList()
})

onUnmounted(() => {
  window.removeEventListener('resize', updateTreeHeight)
})

// 我的模板数据
const myTreeData = ref<any[]>([])
// 共享模板数据
const sharedTreeData = ref<any[]>([])
// loading状态
const loading = ref(false)

const selectedTemplateId = ref('')
const selectedTemplate = ref<any>(null)

// 获取模版列表
const fetchTemplateList = async () => {
  loading.value = true
  try {
    // 获取我的模版
    const myTemplates = await getTemplateList({ engUuid: props.eng_uuid, ifCommonTemplate: 0 })
    myTreeData.value = myTemplates.data ? myTemplates.data.map((item: any) => ({
      key: item.uuid,
      title: item.templateName,
      children: []
    })) : []

    // 获取共享模版
    const sharedTemplates = await getTemplateList({ engUuid: props.eng_uuid, ifCommonTemplate: 1 })
    sharedTreeData.value = sharedTemplates.data ? sharedTemplates.data.map((item: any) => ({
      key: item.uuid,
      title: item.templateName,
      children: []
    })) : []

    // 默认选中第一个模板
    if (myTreeData.value.length > 0) {
      selectedTemplateId.value = myTreeData.value[0].key
      await fetchTemplateDetail(selectedTemplateId.value)
    }
  } finally {
    loading.value = false
  }
}

// 获取模板详情
const fetchTemplateDetail = async (uuid: string) => {
  type.value= ''
  try {
    const res = await getTemplateDetail({ uuid })
    selectedTemplate.value = res.data
    type.value = res.data.ruleType;
    jsonContentObject.value = res.data.jsonContent ? JSON.parse(res.data.jsonContent) : {};
  } catch (error) {
    props.message?.error('获取模板详情失败')
  }
}

// 处理选择模板
const handleSelect = async (selectedKeys: (string | number)[], _info: any) => {
  if (selectedKeys.length > 0) {
    const selectedKey = selectedKeys[0].toString()
    selectedTemplateId.value = selectedKey
    await fetchTemplateDetail(selectedKey)
  }
}

// 监听树数据变化，重新计算高度
watch([myTreeData, sharedTreeData], () => {
  updateTreeHeight()
})

// 处理使用模板按钮点击
const handleUseTemplate = () => {
  if (!selectedTemplate.value) {
    props.message?.error('请先选择一个模板')
    return
  }
  
  if (!jsonContentObject.value) {
    props.message?.error('模板内容为空')
    return
  }

  // 检查类型是否匹配
  if (selectedTemplate.value.ruleType !== type.value) {
    props.message?.error('模板类型与当前规则类型不匹配')
    return
  }

  props.onUpdateRuleContent?.(jsonContentObject.value)
  props.modal?.destroy?.()
}

/**
 * 显示选择规则库对话框
 * @param type 新建的内容类型
 */
function showChooseRuleBaseModalOnDashboard(type: string, ruleTypeCode: string) {
  props.showChooseRuleBaseModal?.('新建' + type + '模版', (ruleBase: { uuid: string, name: string }) => {
    saveRuleTemplate({
      templateName: '未命名' + type + '模版',
      ruleType: ruleTypeCode,
      engUuid: ruleBase.uuid,
    }).then((res) => {
      const templateId = res.data

      if (templateId) {
        fetchTemplateList()
        openTemplateEditor(templateId)
      }
    })
  })
}

const { ruleTypes } = useRuleTypes()

// 打开模板编辑器
const openTemplateEditor = (templateId: string) => {
  window.open(`/templates/${templateId}/edit`)
}

// 处理删除模板
const handleDeleteTemplate = async (uuid: string, title: string) => {
  // props.modal?.confirm({
  // title: '确认删除',
  // content: `确定要删除模板"${title}"吗？`,
  // okText: '确定',
  // cancelText: '取消',
  // zIndex: 1001,
  // async onOk() {
  try {
    await deleteTemplate({ uuid })
    props.message?.success('删除成功')
    fetchTemplateList()
  } catch (error) {
    props.message?.error('删除失败')
  }
  // }
  // })
}

// 处理设置公共模板
const handleSetCommonTemplate = async (uuid: string) => {
  try {
    await setOrCancelCommonTemplate({ uuid })
    props.message?.success('操作成功')
    fetchTemplateList()
  } catch (error) {
    props.message?.error('操作失败')
  }
}
</script>
