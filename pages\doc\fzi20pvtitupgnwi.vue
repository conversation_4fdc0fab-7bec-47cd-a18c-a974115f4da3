<template>
  <div class="ReaderLayout-module_wrapper_l-8F+" style="padding-left: 276px;"><div id="headerOverlayContainer" class="ViewerHeader-module_headerOverlayContainer_30mty"></div><div class="BookReader-module_wrapper_s6Jdt BookReader-module_docTypographyClassic_tUB5r" data-testid="content"><div class="BookReader-module_content_BGKYX" id="main"><div class="BookReader-module_docContainer_mQ3Tk"><div class="DocReader-module_wrapper_t3Z8X" data-doc-layout="fixed" data-doc-sidebar="false" data-doc-toc="true"><div class=""><div id="doc-reader-content" class="DocReader-module_content_AcIMy "><div class="DocReader-module_header_xAOtU"><div><div class="DocReader-module_title_fXOQi"><h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title">规则流</h1></div></div></div><div><article id="content" class="article-content" tabindex="0" style="outline-style: none;"><div class="ne-doc-major-viewer"><div class="yuque-doc-content" data-df="lake" style="position: relative;"><div><div class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed" data-viewer-mode="normal" id="ue335"><div class="ne-viewer-header"><button type="button" class="ne-ui-exit-max-view-btn" style="background-image:url(https://gw.alipayobjects.com/zos/bmw-prod/09ca6e30-fd03-49ff-b2fb-15a2fbd8042a.svg)">返回文档</button></div><div class="ne-viewer-body"><ne-h4 id="dMiUF" data-lake-id="dMiUF"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h4" data-name="card-h4"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h4"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="ub484d2cb" style="color: rgb(51, 51, 51);">规则流，它整个的结构类似于工作流，用来对已有的决策集、决策表、决策树或其它子规则流的执行顺序进行编排，以清晰直观的实现一个大的复杂的业务规则。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h4><ne-p id="uda5b6df4" data-lake-id="uda5b6df4"><ne-text id="u1c65b391">erule规则引擎中的决策流可以实现对已有的决策集、决策表、决策树或其它子规则流进行编排执行；编排过程中可以根据条件选择分支执行。erule中提供了一个基于网页的流程设计器，通过简单拖曳就可以快速实现对已有的决策集、决策表、决策树或其它子规则流执行顺序的编排。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="f91a24973c452f95538178f458391043" data-lake-id="f91a24973c452f95538178f458391043"><ne-text id="uc43e9b0f">规则流程 是对规则库中规则在执行时进行排序执行配置。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="e1760fde814a39f5ff339eafe2644e41" data-lake-id="e1760fde814a39f5ff339eafe2644e41"><ne-card data-card-name="image" data-card-type="inline" id="ub851c3ae" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1119.2" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743392491464-85ebe882-3ac1-4500-b66e-55380bd261fc.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="4016b2e2ff2099ab9c6ca82fbedbbafd" data-lake-id="4016b2e2ff2099ab9c6ca82fbedbbafd"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="06acc1dd28b84669e0f5153af50c2a44" data-lake-id="06acc1dd28b84669e0f5153af50c2a44"><ne-text id="u7c6d9748">①新增规则流，需要选择好规则流类型。注：一个规则库只能存在一条主流程。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="3c3f77eb581498d4a6f6bfcc06852990" data-lake-id="3c3f77eb581498d4a6f6bfcc06852990"><ne-card data-card-name="image" data-card-type="inline" id="u791e9bec" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="639.2" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743392604016-ff380bea-b55c-4728-aebd-2c56ca3d5c83.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="0fe9f5c03767ab5cfcbd2309170547af" data-lake-id="0fe9f5c03767ab5cfcbd2309170547af"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="f62fbec9527f93b28ed90b64fcfa3342" data-lake-id="f62fbec9527f93b28ed90b64fcfa3342" ne-alignment="left"><ne-text id="u4e01457e">② 规则流控件介绍</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="a3cb407e4df3dc336a0cefdc259dbfba" data-lake-id="a3cb407e4df3dc336a0cefdc259dbfba"><ne-card data-card-name="image" data-card-type="inline" id="ud02ad2be" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="353.6" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743392814961-db08e8ca-7f88-4321-b34f-b37e9734a367.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="b146bb80f4476725e49c088dc97cca46" data-lake-id="b146bb80f4476725e49c088dc97cca46"><ne-text id="u85d19214" style="color: rgb(0, 0, 0);" ne-fontsize="16">激活连接线：用于连接两个控件使用，建立控件间的连接通道。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ebe0a76256d5ec29c1dcfdde937df77c" data-lake-id="ebe0a76256d5ec29c1dcfdde937df77c"><ne-text id="u59604b14" style="color: rgb(0, 0, 0);" ne-fontsize="16">规则节点：存放规则流节点下的规则集。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="1df5202904135255a9badfd6c9839868" data-lake-id="1df5202904135255a9badfd6c9839868"><ne-text id="u721acd75" style="color: rgb(0, 0, 0);" ne-fontsize="16">决策分支：条件分支控件，编辑决策分支条件实现不同条件指向不同规则的作用。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="37dd16162c59799adb4470a643df69c1" data-lake-id="37dd16162c59799adb4470a643df69c1"><ne-text id="ud1cbb61a" style="color: rgb(0, 0, 0);" ne-fontsize="16">汇聚分支：多个分支汇聚点。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="1f00a39eee88e4e8104ff53b6ebeac63" data-lake-id="1f00a39eee88e4e8104ff53b6ebeac63"><ne-text id="u3a15b402" style="color: rgb(0, 0, 0);" ne-fontsize="16">开始节点：规则流开始端（</ne-text><ne-text id="u83765d79" style="color: rgb(255, 0, 0);" ne-fontsize="16">唯一性</ne-text><ne-text id="uc7648ea4" style="color: rgb(0, 0, 0);" ne-fontsize="16">）。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="a46788397ddc3c66e931d0ab8984d615" data-lake-id="a46788397ddc3c66e931d0ab8984d615"><ne-text id="u802c9eb2" style="color: rgb(0, 0, 0);" ne-fontsize="16">结束节点：规则流结束端。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="c399d7ae24c289bc447720544d1fc58f" data-lake-id="c399d7ae24c289bc447720544d1fc58f"><ne-text id="uf2b320fa" ne-fontsize="16">子流节点：规则主流的分支，其画法与主流一样，可以存在多个。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="6ed196a069b794e3cc8e53b1f6d0d989" data-lake-id="6ed196a069b794e3cc8e53b1f6d0d989"><ne-text id="u032224f7">③根据规则库里的要求编写规则流执行顺序</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>1</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="fede3d3a5cde724bce2acdc5e9f9d5ef" data-lake-id="fede3d3a5cde724bce2acdc5e9f9d5ef"><ne-text id="u52b33896">节点间链接通过 </ne-text><ne-card data-card-name="image" data-card-type="inline" id="fk1Vn" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(243, 243, 243); width: 94px; height: 0px; padding-bottom: 54.2553%;"><img width="94" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><ne-text id="u0200953f"> 连接线连接的，</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>2</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="05d2751fa67e4a0378511d0e8072313a" data-lake-id="05d2751fa67e4a0378511d0e8072313a"><ne-text id="u3b11924e">如果节点是需要指定条件，允许执行的需要使用决策的</ne-text><ne-card data-card-name="image" data-card-type="inline" id="rwjVb" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(241, 241, 241); width: 110px; height: 0px; padding-bottom: 60.9091%;"><img width="110" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><ne-text id="u3ed6979d">，那么区分分支节点后面的连接线就使用</ne-text><ne-card data-card-name="image" data-card-type="inline" id="lqqzI" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(239, 239, 239); width: 99px; height: 0px; padding-bottom: 67.6768%;"><img width="99" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><ne-text id="uf6e9ded6">。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="480383e600fa19dcdea9fbe3dbd4309c" data-lake-id="480383e600fa19dcdea9fbe3dbd4309c"><ne-card data-card-name="image" data-card-type="inline" id="LhvPt" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(249, 248, 247); width: 535px; height: 0px; padding-bottom: 74.9533%;"><img width="535" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>3</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="u2d665a47" data-lake-id="u2d665a47"><ne-text id="uae784315">决策分支 的条件根据要求自行配置。双击决策分支划分的线</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="be426787a24413e79b9931258665388f" data-lake-id="be426787a24413e79b9931258665388f"><ne-card data-card-name="image" data-card-type="inline" id="nV43m" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(254, 253, 252); width: 1021px; height: 0px; padding-bottom: 44.9559%;"><img width="1021" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="1c5bdf772e645e2ee56271fee0be81ac" data-lake-id="1c5bdf772e645e2ee56271fee0be81ac"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>4</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="u3988f578" data-lake-id="u3988f578"><ne-text id="u6b4629ad">节点的规则引用</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="e4c2837975d959b5677322b93fdeadaf" data-lake-id="e4c2837975d959b5677322b93fdeadaf"><ne-card data-card-name="image" data-card-type="inline" id="e5AVE" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(251, 249, 249); width: 1304px; height: 0px; padding-bottom: 39.4172%;"><img width="1304" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>5</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="uab43326d" data-lake-id="uab43326d"><ne-text id="u023feb7c">双点击画好的规则节点，将规则库的规则选择到节点中</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="8dae8b085edaa3ec3d930328c24c99f2" data-lake-id="8dae8b085edaa3ec3d930328c24c99f2"><ne-card data-card-name="image" data-card-type="inline" id="KjJmT" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(254, 252, 251); width: 1499px; height: 0px; padding-bottom: 42.6284%;"><img width="1499" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>6</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="u1558a93a" data-lake-id="u1558a93a"><ne-text id="u98fe16f6">规则子流的画法与规则主流画法相同。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="ua6a40d44" data-lake-id="ua6a40d44"><ne-card data-card-name="image" data-card-type="inline" id="ue6503821" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(251, 250, 250); width: 1127.2px; height: 0px; padding-bottom: 36.0185%;"><img width="1127.2" class="ne-image ne-image-preview" alt="image.png" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ue8ae5cb8" data-lake-id="ue8ae5cb8"><ne-card data-card-name="image" data-card-type="inline" id="u662f6143" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(250, 249, 248); width: 705px; height: 0px; padding-bottom: 44.539%;"><img width="705" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="061a2069459191eb4213cb02ebc6330d" data-lake-id="061a2069459191eb4213cb02ebc6330d"><ne-text id="ub913801b">引用子规则流</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u2765f4af" data-lake-id="u2765f4af"><ne-card data-card-name="image" data-card-type="inline" id="u473451c0" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(207, 206, 206); width: 1711px; height: 0px; padding-bottom: 51.7826%;"><img width="1711" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="9e910888aa32392ddaca3073e2582d85" data-lake-id="9e910888aa32392ddaca3073e2582d85"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="a28d7ba046b4a817a61241dff0580e8f" data-lake-id="a28d7ba046b4a817a61241dff0580e8f"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p></div><div class="ne-inner-overlay-container"><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay"><div class="count-tips-module_tipsContainer___5agc"></div></ne-overlay-tmp></div></div><div style="height: 0px; overflow: hidden;">​</div></div></div></div></article></div></div><div ne-viewer-toc-pin="true"></div><div></div></div><div></div></div></div></div></div></div>
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>