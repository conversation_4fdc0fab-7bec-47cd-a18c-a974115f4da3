import request from '@/utils/request'
// 规则发布历史列表
export function getRule_history(params) {
  return request({
    url: 'erule/manage/ruleMuster/list',
    method: 'get',
    params,
  })
}
//查询规则集发布版本对应的规则列表
export function getRule_histDetails(params) {
    return request({
      url: 'erule/manage/ruleMuster/ruleList',
      method: 'get',
      params,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
    })
  }
  // 获取规则列表头部展示信息
  export function getRule_Head(params) {
    return request({
      url: 'erule/manage/ruleMuster/getRuleMusterByUuid',
      method: 'get',
      params,
    })
  }

  export function getRuleDetail(params) {
    return request({
      url: 'erule/manage/rulelHis/read2',
      method: 'get',
      params,
    })
  }

  // 导出规则
 export function getExportRule(params) {
  return request({
    url: '/erule/manage/ruleMuster/exportData',
    method: 'get',
    params,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}

// 统计规则
export function statisticsRule(params) {
  return request({
    url: 'erule/manage/ruleMuster/statisticsRule',
    method: 'get',
    params,
  })
}

// 批量导出
export function musterBatchExportData(data) {
  return request({
    url: '/erule/manage/ruleMuster/ruleMusterBatchExport',
    method: 'post',
    data,
    responseType: "arraybuffer",
    headers: {
      response: true,
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    }
  })
}

