<template>
  <span class="operator">
    <span v-if="locked || isTrack" class="lockedText">
      <span
        v-if="titleArr.length > 0 && (dataSource.operatorParams && dataSource.operatorParams.length >= 0) || (dataSource.value && dataSource.value.length > 0)"
        class="txtItem"
        >{{ txt }}</span
      >
      <span class="txtItem">请选择操作符</span>
    </span>
    <a-cascader
      v-else
      :value="dataSource.value"
      :options="options"
      @change="onSelectChange"
      :autoFocus="true"
      :allowClear="false"
      :showSearch="showSearchConfig"
      expandTrigger="hover"
      @click.stop
    >
      <span
        v-if="titleArr.length > 0 && (dataSource.operatorParams && dataSource.operatorParams.length >= 0) || (dataSource.value && dataSource.value.length > 0)"
        class="txtItem" style="color:#4108F6"
        >{{ txt }}</span
      >
      <span class="txtItem" @click.stop>请选择操作符</span>
    </a-cascader>
  </span>
</template>

<script setup>
import RuleOperatorNode from "./RuleOperatorNode";
import { findLabel } from "@/components/ruleEditCom/utils/displayUtil";

// 定义组件的 props
const props = defineProps({
  type: {
    type: String,
    default: ""
  },
  operatorData: {
    type: Object
  },
  dataSource: {
    type: Object
  },
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  },
  onChange: {
    type: Function
  },
  isTable: {
    type: Boolean,
    default: false
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false
  }
});

// 定义组件的 emits
const emit = defineEmits(['onChange']);

// 计算属性，根据 operatorData 和 type 返回 options
const options = computed(() => {
  if (props.operatorData) {
    let result = [];
    if (props.operatorData[props.type]) {
      result = props.operatorData[props.type];
    } else if (props.type.includes(".") && props.type.includes("List<")) {
      // 当字段是列表类型时
      result = props.operatorData["List"];
      
      // 如果是列表类型，需要过滤掉数组相关的操作符
      if (result && result.length > 0) {
        result = result.filter(item => {
          // 过滤掉含有"数组"关键字的操作符
          return !item.label.includes('<数组>') && !item.paramTypes?.includes('Array');
        });
      }
    } else {
      result = props.operatorData["Object"];
    }
    return result;
  }
  return [];
});

// 计算属性，返回 showSearchConfig
const showSearchConfig = computed(() => {
  return {
    filter: filter,
    matchInputWidth: false // 搜索结果列表是否与input同宽；
  };
});

// 计算属性，根据 options 和 dataSource 返回 txt
const txt = computed(() => {
  return findLabel(options.value, props.dataSource.value)
    .replace(/\(#[0-9]+,/g, "")
    .replace(/\)/g, "");
});

// 计算属性，根据 txt 返回 titleArr
const titleArr = computed(() => {
  const reg1 = /#.+?>/g;
  return txt.value ? txt.value.split(reg1) : [];
});


// 处理操作符变化的方法
const onOperatorChange = (newOperatorData) => {
  const _newOperatorData = props.dataSource._derive(newOperatorData);
  emit("onChange", props.pos, _newOperatorData);
};

// 处理选择变化的方法
const onSelectChange = (valueArr, selectedOptions) => {
  const { label, paramTypes, paramQuantity } = selectedOptions[0];
  const num = label ? label.split("(").length - 1 : 0;
  const paramTypeArr = paramTypes ? paramTypes.split(",") : ["object"];
  const { enumDictName } = props.dataSource;
  const newOperatorData = {
    enumDictName: enumDictName || null,
    value: valueArr,
    operatorParams: [],
    paramQuantity
  };
  for (let i = 0; i < num; i++) {
    const newVairable = {
      variableType: "constant",
      enumDictName: enumDictName || null,
      value: ""
    };
    newVairable.valueType = paramTypeArr[i];
    newOperatorData.operatorParams.push(newVairable);
  }
  onOperatorChange(newOperatorData);
};

// 处理子组件变化的方法
const onChildChange = (childPos, variableData, conditionValueType) => {
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const {
    operatorParams: operatorParamsList = [],
    value,
    enumDictName = null,
    paramQuantity
  } = props.dataSource;

  const newOperatorParamsList = operatorParamsList.map(
    (paramItem, paramIndex) => {
      if (index === paramIndex) {
        return variableData;
      }
      return paramItem;
    }
  );
  const newOperatorData = {
    value,
    paramQuantity,
    enumDictName,
    operatorParams: newOperatorParamsList
  };
  onOperatorChange(newOperatorData);
};

// 处理操作符参数变化的方法
const onOperatorParamChange = (key) => {
  const {
    operatorParams: operatorParamsList = [],
    value,
    enumDictName = null,
    paramQuantity
  } = props.dataSource;
  const firstParam = operatorParamsList[0];
  const { valueType } = firstParam;
  if (key === "addVar") {
    const newVairable = {
      variableType: enumDictName ? "constant" : "field",
      enumDictName,
      valueType,
      value: []
    };
    const newOperatorData = {
      value,
      paramQuantity,
      enumDictName,
      operatorParams: [...operatorParamsList, newVairable]
    };
    onOperatorChange(newOperatorData);
  } else if (key === "deleteVar") {
    const newOperatorData = {
      value,
      paramQuantity,
      enumDictName,
      operatorParams: operatorParamsList.slice(0, -1)
    };
    onOperatorChange(newOperatorData);
  }
};

// 过滤方法
const filter = (inputValue, path) => {
  return path.some(option => {
    const opetionLabel = option.label.toLowerCase();
    const _inputValue = inputValue.toLowerCase();
    return opetionLabel.indexOf(_inputValue) > -1;
  });
};
</script>
