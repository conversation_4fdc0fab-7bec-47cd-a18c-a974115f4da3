<!-- 定时时间选择组件 -->
<!-- 该组件用于定时发布，定时任务等情况-->
<template>
    <div style="display: flex; align-items: center;">
        <a-date-picker
                v-model:value="localPubDate"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择日期"
                :disabled-date="disabledDate"
                @change="handleDateChange"
                style="flex: 2; margin-right: 10px;"
        />
        <a-time-picker
                v-model:value="localPubTime"
                :minute-step="30"
                format="HH:mm"
                value-format="HH:mm"
                :disabled-time="disabledDateTime"
                placeholder="选择时间"
                style="flex: 1;"
                popupClassName="baseline-time-dis-style"
                :disabled="!isDateSelected"
                @openChange="handleTimePickerOpen"
        />
    </div>
</template>

<script setup lang="ts">
    import dayjs, { Dayjs } from 'dayjs';

    // 定义 Props
    const props = defineProps<{
        pubDate: string;
        pubTime: string;
        disabledDate?: (current: Dayjs) => boolean;
        disabledDateTime?: (selectedDate: Dayjs, selectedTime: Dayjs) => { disabledHours?: () => number[]; disabledMinutes?: () => number[]; disabledSeconds?: () => number[] };
    }>();

    // 定义 Emits
    const emit = defineEmits<{
        (e: 'update:pubDate', value: string): void;
        (e: 'update:pubTime', value: string): void;
        (e: 'change', date: string, time: string): void;
    }>();

    // 定义本地响应式变量
    const localPubDate = ref<string>(props.pubDate);
    const localPubTime = ref<string>(props.pubTime);
    const isDateSelected = ref<boolean>(false);
    // 监听 Props 变化
    watch(
        () => props.pubDate,
        (newValue) => {
            localPubDate.value = newValue;
            isDateSelected.value = !!newValue;
        }
    );

    watch(
        () => props.pubTime,
        (newValue) => {
            localPubTime.value = newValue;
        }
    );

    // 监听本地变量变化并更新 Props
    watch(
        localPubDate,
        (newValue) => {
            emit('update:pubDate', newValue);
            emit('change', newValue, localPubTime.value);
            isDateSelected.value = !!newValue;
        }
    );

    watch(
        localPubTime,
        (newValue) => {
            emit('update:pubTime', newValue);
            emit('change', localPubDate.value, newValue);
        }
    );
    const disabledDate = (current: Dayjs) => {
        return current && current.isBefore(dayjs().startOf('day'));
    };
    const disabledDateTime = () => {
        const currentDate = new Date();
        if(new Date(localPubDate.value).toDateString() === currentDate.toDateString()){
            const currentHour = dayjs().hour();
            const currentMinute = dayjs().minute();
            const nextHour = (currentHour);

            const disabledHours = () => {
                const hours = [];
                for (let i = 0; i < nextHour; i++) {
                    hours.push(i);
                }
                if(currentMinute >= 30){
                    hours.push(nextHour)
                }
                return hours;
            };

            const disabledMinutes = (selectedHour: number) => {
                if (selectedHour === currentHour) {
                    const minutes = [];
                    for (let i = 0; i <= currentMinute; i++) {
                        minutes.push(i);
                    }
                    return minutes;
                }
                return [];
            };

            return {
                disabledHours,
                disabledMinutes,
            };
        }else{
            const disabledHours = () => {
                return [];
            }
            const disabledMinutes = () => {
                return [];
            }
            return {
                disabledHours,
                disabledMinutes,
            };
        }

    };
    // 处理日期变化
    const handleDateChange = (date: Dayjs | null) => {
        if (date) {
            localPubDate.value = date.format('YYYY-MM-DD');
        } else {
            localPubDate.value = '';
            localPubTime.value = '';
        }
        isDateSelected.value = !!localPubDate.value;
    };

    // 获取第一个可用时间
    const getFirstAvailableTime = () => {
        const currentDate = new Date();
        if(new Date(localPubDate.value).toDateString() === currentDate.toDateString()){
            const currentHour = dayjs().hour();
            const currentMinute = dayjs().minute();
            let firstAvailableHour = currentHour;
            let firstAvailableMinute = '00';
            
            if(currentMinute >= 30) {
                firstAvailableHour = currentHour + 1;
                firstAvailableMinute = '00';
            } else {
                firstAvailableMinute = '30';
            }
            
            return `${String(firstAvailableHour).padStart(2, '0')}:${firstAvailableMinute}`;
        }
        return '00:00';
    };

    // 处理时间选择器打开事件
    const handleTimePickerOpen = (open: boolean) => {
        if (open && !localPubTime.value && localPubDate.value) {
            localPubTime.value = getFirstAvailableTime();
        }
    };
</script>

<style lang="scss">
    .baseline-time-dis-style{
        .ant-picker-now-btn{
            display: none;
        }
    }
</style>
