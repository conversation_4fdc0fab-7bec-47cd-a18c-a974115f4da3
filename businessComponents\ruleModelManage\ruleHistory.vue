<template>
    <FlexDrawer
        :visible="controlVisible"
        title="历史"
        @close="closeModal"
        :width="1000"
        :destroyOnClose="true"
    >
        <div class="table-container">
            <a-table
                :columns="columns"
                :data-source="tableDate"
                :pagination="pagination"
                row-key="uuid"
                :loading="pagination.isLoading"
                @change="handlePageChange"
                :scroll="{ x: 1200 }"
            >
                <template v-slot:bodyCell="{column,record,index}">
                    <template v-if="column.dataIndex == 'action'">
                        <a-button type="link" size="small" @click="handleExport(record)">导出</a-button>
                    </template>
                </template>
            </a-table>
        </div>
    </FlexDrawer>
</template>
<script setup lang="ts">
    import {
        hisList,
        ruleBomHisExport
    } from "@/api/rule_model";
    const message = inject('message')

    //子父页面参数传递
    const emit = defineEmits(['closeModal'])
    const props = defineProps({
        controlVisible: {
            type: Boolean,
            default: false,
        },
        i: {
            type: String,
            default: '',
        }
    })

    watch(()=>props.controlVisible,(newValue,oldValue)=>{
        if(newValue == true){
            fetchRules();
        }
    },{immediate:true,deep:true})
    const closeModal=()=>{
        emit('closeModal')
    }



    //页脚参数
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });


    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        fetchRules();
    };
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`
        },
        {
            title: '模型名称',
            dataIndex: 'modelName',
            key: 'modelName',
        },
        {
            title: '模型类型',
            dataIndex: 'bomModelType',
            key: 'bomModelType',
        },
        {
            title: '业务条线',
            dataIndex: 'businessLine',
            key: 'businessLine',
        },
        {
            title: 'jar包路径',
            dataIndex: 'jarPath',
            key: 'jarPath',
        },
        {
            title: 'jar包名称',
            dataIndex: 'jarName',
            key: 'jarName',
        },
        {
            title: 'jar包版本',
            dataIndex: 'jarEdition',
            key: 'jarEdition',
        },
        {
            title: '创建日期',
            dataIndex: 'createdTimeStr',
            key: 'createdTimeStr',
        },
        {
            title: '操作',
            key: 'action',
            dataIndex: 'action',
        },
    ];

    //定义参数
    const tableDate = ref<RuleHistory[]>([]);
    interface RuleHistory {
        modelName: string;
        bomModelType: string;
        businessLine: string;
        jarPath: string;
        jarName: string;
        jarEdition: string;
        createdTimeStr: string;
    }

    const fetchRules = async () => {
        pagination.value.isLoading = true;
        try {
            const res = await hisList({
                ruleBomModelUuid: props.i
            });
            tableDate.value = res.data.data;
            pagination.value.total = res.data.totalCount;
            pagination.value.isLoading = false;
        } catch (error) {
            pagination.value.isLoading = false;
            message.error('获取历史失败');
        }
    };

    //导出jar包
    const handleExport = (record: RuleHistory) => {
        ruleBomHisExport({ uuid: record.uuid }).then((res) => {
            let headers = res.headers;
            let blob = new Blob([res.data], {
                type: headers["content-type"]
            });
            let link = document.createElement("a");
            let url = window.URL.createObjectURL(blob);
            let fileName = headers["content-disposition"].split(';')[1].split('=')[1];
            link.href=url
            link.download = fileName;
            link.style.display = "none";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            closeModal();
        });
    }
    //组件挂载后
    onMounted(() => {
        fetchRules();
    });
</script>

<style lang="scss" scoped>
    .drawer-content {
        padding: 24px;

        :deep(.ant-table-wrapper) {
            background: #fff;
        }
    }
    
    .table-container {
        width: 100%;
        overflow-x: auto;
        
        :deep(.ant-table-cell) {
            white-space: nowrap;
            padding: 8px 16px;
        }
        
        :deep(.ant-table-thead > tr > th) {
            background-color: #f5f5f5;
            font-weight: 500;
        }
    }
</style>
