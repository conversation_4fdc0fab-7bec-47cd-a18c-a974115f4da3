/**定义tab页多条后更多显示样式*/
.ant-tabs-dropdown-menu .ant-tabs-dropdown-menu-item-remove {
  background-color: white;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.ant-tabs-dropdown-menu .ant-tabs-dropdown-menu-item:hover .ant-tabs-dropdown-menu-item-remove {
  background-color: #f5f5f5;
}
:root {
  --yq-yuque-green-500: rgb(92, 163, 242);
  --yq-yuque-green-600: rgb(0, 106, 212);
  --yq-yuque-green-700: rgb(0, 85, 170);
}

/* 标签页样式覆盖 - 蓝色主题 */
.ant-tabs-tab:hover {
  color: rgb(92, 163, 242) !important; /* 浅蓝色 */
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: rgb(0, 106, 212) !important; /* 深蓝色 */
  // font-weight: bold !important; /* 加粗效果 */
  text-shadow: unset !important;
}

/* 表格表头加粗样式 - 全局应用到所有表格 */
.ant-table-thead > tr > th {
  font-weight: bold !important;
  color: #000 !important;
}

/* 全局链接样式 - 适用于所有表格中的链接和规则名称链接 */
.nameButton,
.rule-name-link,
.rule-item,
.ant-table a,
.nuxt-link-style,
a[type="link"] {
  color: #000 !important;
  transition: color 0.3s;
  text-decoration: none !important;
  
  &:hover {
    color: var(--yq-yuque-green-600) !important;
    text-decoration: none !important;
  }
}

