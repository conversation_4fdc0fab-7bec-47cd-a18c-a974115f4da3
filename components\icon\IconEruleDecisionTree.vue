<!-- 决策树图标组件 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 16,
    class: ''
})
</script>

<template>
    <svg :width="props.size" :height="props.size" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'icon-svg',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path d="M901.632 414.72c-22.528 0-43.52 7.168-60.928 18.944l-238.08-236.032c12.8-18.432 19.968-40.448 19.968-64.512 0-62.464-50.688-113.152-113.152-113.152S396.288 70.656 396.288 133.12c0 24.064 7.68 46.08 19.968 64.512L178.176 434.176c-17.408-12.288-38.4-18.944-60.928-18.944C57.856 414.72 10.24 462.848 10.24 521.728s47.616 107.008 107.008 107.008c58.88 0 107.008-47.616 107.008-107.008 0-22.528-6.656-43.008-18.432-60.416L443.904 225.28c13.824 9.728 29.696 16.384 46.592 19.456v169.472c-50.176 8.704-88.576 52.736-88.576 105.472 0 29.696 11.776 56.32 31.232 75.776L264.704 839.68c-11.264-4.608-23.04-7.168-35.84-7.168-51.2 0-93.184 41.472-93.184 93.184S177.664 1018.88 229.376 1018.88c51.2 0 93.184-41.472 93.184-93.184 0-24.576-9.728-47.104-25.6-64l168.96-244.736c13.312 6.144 28.16 9.216 43.52 9.216s30.72-3.072 44.032-9.216l169.472 244.736c-15.872 16.896-25.088 38.912-25.088 64 0 51.2 41.472 93.184 93.184 93.184 51.2 0 93.184-41.472 93.184-93.184s-41.472-93.184-93.184-93.184c-12.8 0-24.576 2.56-35.84 7.168l-169.472-244.736c19.456-19.456 31.232-46.08 31.232-75.264 0-52.224-37.376-95.744-86.528-104.96V244.736c16.384-3.072 31.744-9.728 45.056-18.944l238.08 236.032c-11.776 17.408-18.432 37.888-18.432 60.416 0 58.88 47.616 107.008 107.008 107.008 58.88 0 107.008-47.616 107.008-107.008-0.512-59.392-48.64-107.52-107.52-107.52z" fill="#2c2c2c"></path>
    </svg>
</template> 