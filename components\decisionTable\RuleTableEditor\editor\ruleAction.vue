<template>
  <div class="designView" style="font-weight: 500">
    <div class="ruleBody" style="padding-left: 0">
      <ActionListTable
        pos="r"
        :actionData="actionData"
        :locked="locked"
        @onActionComChange="onActionChange"
        :isTable="isTable"
        :noRuleCellUnit="true"
      />
    </div>
  </div>
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";
import ActionListTable from "./actionList.vue";
import { cloneDeep } from "lodash";

// 导入 ActionGenerate 从 util 中
const { ActionGenerate } = util;

// 定义 props
const props = defineProps({
  actions: {
    type: Array,
    default: () => []
  },
  locked: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit 事件
const emit = defineEmits(['onchange']);

// 定义响应式数据
const actionData = ref(cloneDeep(props.actions));

// 监听 actions 的变化
watch(
  () => props.actions,
  (newVal) => {
    actionData.value = cloneDeep(newVal);
  },
  { deep: true }
);

// 方法
const onActionChange = (flag = "action", pos, data) => {
  const copyData = cloneDeep(actionData.value);
  const [, actionIndex] = pos.split("_");
  if (flag === "action") {
    copyData[actionIndex] = new ActionGenerate(data);
  }
  actionData.value = [...copyData];
  emit("onchange", [...copyData]);
};
</script>
