
<template>
  <div class="BookCatalog-module_sideCatalog_wpCdP">
    <div class="lark-virtual-tree" style="position: relative; height: 100%; width: 259px; overflow: auto; will-change: transform; direction: ltr;">
      <div style="height: auto; width: 100%;">
        <div class="BookCatalog-module_sideCatalog_wpCdP"><div class="lark-virtual-tree" style="position: relative; height: 100%; width: 276px; overflow: auto; will-change: transform; direction: ltr;"><div style="height: auto; width: 100%;"><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="PikBK86fBnr0FaX4" style="position: absolute; left: 0px; top: 0px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="PikBK86fBnr0FaX4" data-rbd-drag-handle-context-id="0" draggable="false"><div class="catalogTreeItem-module_content_fLFbS" style="padding-left: 0px;"><div style="width: 28px; display: inline-block;"></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="「分组」- erule3.0帮助文档"><span class="catalogTreeItem-module_title_snpKw">erule3.0帮助文档</span></div></div></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="ceikxcwmM8K0SURN" style="position: absolute; left: 0px; top: 36px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="ceikxcwmM8K0SURN" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 24px;" to="/doc/xyu3hsey0hrb4kxs" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/xyu3hsey0hrb4kxs' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="产品简介"><span class="catalogTreeItem-module_title_snpKw">产品简介</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="pDePesfSWDsZ0FIi" style="position: absolute; left: 0px; top: 72px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="pDePesfSWDsZ0FIi" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 24px;" to="/doc/bu90nmg1blcffdmq" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/bu90nmg1blcffdmq' }"><div class="catalogTreeItem-module_collapseContent_09v0m catalogTreeItem-module_hasChildren_TrI8X"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" style="width: 16px; min-width: 16px; height: 16px;" class="larkui-icon larkui-icon-mini-dropdown icon-svg catalogTreeItem-module_collapseIcon_1ZFIu index-module_size_wVASz" data-name="MiniDropdown"><path d="M180.58 108.184c3.84 3.839 3.905 10.023.196 13.941l-.195.2-38.184 38.185c-7.73 7.729-20.21 7.81-28.039.241l-.245-.241-38.184-38.184c-3.905-3.905-3.905-10.237 0-14.142 3.839-3.84 10.023-3.904 13.941-.195l.201.195 38.184 38.184 38.184-38.184c3.905-3.905 10.236-3.905 14.142 0Z" fill="currentColor" fill-rule="nonzero"></path></svg></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="规则编写"><span class="catalogTreeItem-module_title_snpKw">规则编写</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="VW5JAGpD3jo53Mnd" style="position: absolute; left: 0px; top: 108px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="VW5JAGpD3jo53Mnd" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/bd2byuegarv3qmz2" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/bd2byuegarv3qmz2' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="普通规则"><span class="catalogTreeItem-module_title_snpKw">普通规则</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="oY7Xt10u8bQHSbNz" style="position: absolute; left: 0px; top: 144px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="oY7Xt10u8bQHSbNz" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/gxlr925vsh2o1uvk" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/gxlr925vsh2o1uvk' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="决策表"><span class="catalogTreeItem-module_title_snpKw">决策表</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="SfBb1M4oPGsL8t0R" style="position: absolute; left: 0px; top: 180px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="SfBb1M4oPGsL8t0R" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/fzi20pvtitupgnwi" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/fzi20pvtitupgnwi' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="规则流"><span class="catalogTreeItem-module_title_snpKw">规则流</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="0CWcmkGr96eSXbdu" style="position: absolute; left: 0px; top: 216px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="0CWcmkGr96eSXbdu" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/fi3hlcdygx2tduql" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/fi3hlcdygx2tduql' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="决策树"><span class="catalogTreeItem-module_title_snpKw">决策树</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="JdNBgR9wqKmkEihe" style="position: absolute; left: 0px; top: 252px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="JdNBgR9wqKmkEihe" data-rbd-drag-handle-context-id="0" draggable="false"><div class="catalogTreeItem-module_content_fLFbS" style="padding-left: 24px;"><div class="catalogTreeItem-module_collapseContent_09v0m catalogTreeItem-module_hasChildren_TrI8X"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" style="width: 16px; min-width: 16px; height: 16px;" class="larkui-icon larkui-icon-mini-dropdown icon-svg catalogTreeItem-module_collapseIcon_1ZFIu index-module_size_wVASz" data-name="MiniDropdown"><path d="M180.58 108.184c3.84 3.839 3.905 10.023.196 13.941l-.195.2-38.184 38.185c-7.73 7.729-20.21 7.81-28.039.241l-.245-.241-38.184-38.184c-3.905-3.905-3.905-10.237 0-14.142 3.839-3.84 10.023-3.904 13.941-.195l.201.195 38.184 38.184 38.184-38.184c3.905-3.905 10.236-3.905 14.142 0Z" fill="currentColor" fill-rule="nonzero"></path></svg></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="「分组」- 规则管理"><span class="catalogTreeItem-module_title_snpKw">规则管理</span></div></div></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="SAaCwUzqTQBU9Mfm" style="position: absolute; left: 0px; top: 288px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="SAaCwUzqTQBU9Mfm" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink aria-current="page" class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/mfi34xtc8vp5m700" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/mfi34xtc8vp5m700' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="规则审核"><span class="catalogTreeItem-module_title_snpKw">规则审核</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="uvbZiSHhl0ET7jfx" style="position: absolute; left: 0px; top: 324px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="uvbZiSHhl0ET7jfx" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/sg715n5xz5x9nmat" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/sg715n5xz5x9nmat' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="基线管理"><span class="catalogTreeItem-module_title_snpKw">基线管理</span></div></NuxtLink></div><div class="catalogTreeItem-module_CatalogItem_xkX7p" data-rbd-draggable-context-id="0" data-rbd-draggable-id="ZHLC1nBbDCdHduAg" style="position: absolute; left: 0px; top: 360px; height: 36px; width: 100%; cursor: pointer;" tabindex="0" role="button" aria-describedby="rbd-hidden-text-0-hidden-text-0" data-rbd-drag-handle-draggable-id="ZHLC1nBbDCdHduAg" data-rbd-drag-handle-context-id="0" draggable="false"><NuxtLink class="catalogTreeItem-module_content_fLFbS" style="padding-left: 48px;" to="/doc/eh215qdgewc79loc" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/eh215qdgewc79loc' }"><div class="catalogTreeItem-module_collapseContent_09v0m"><div class="catalogTreeItem-module_collapseIconWrapper_XcS8B"></div></div><div class="catalogTreeItem-module_titleWrapper_sFyDt" title="规则发布历史"><span class="catalogTreeItem-module_title_snpKw">规则发布历史</span></div></NuxtLink></div></div></div></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const currentPath = computed(() => route.path)

onMounted(() => {
  const collapseButtons = document.querySelectorAll('.catalogTreeItem-module_hasChildren_TrI8X')

  collapseButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation()
      e.preventDefault()

      const iconWrapper = button.querySelector('.catalogTreeItem-module_collapseIconWrapper_XcS8B')
      iconWrapper.classList.toggle('catalogTreeItem-module_collapsed_u8yPq')

      const menuItem = button.closest('.catalogTreeItem-module_CatalogItem_xkX7p')
      const currentPadding = parseInt(menuItem.querySelector('.catalogTreeItem-module_content_fLFbS').style.paddingLeft) || 0

      let nextItem = menuItem.nextElementSibling
      let offset = 0
      let itemsToUpdate = []

      // 首先收集需要隐藏的项目和它们的高度
      while (nextItem) {
        const nextPadding = parseInt(nextItem.querySelector('.catalogTreeItem-module_content_fLFbS').style.paddingLeft) || 0
        if (nextPadding <= currentPadding) break

        itemsToUpdate.push(nextItem)
        offset += 36 // 每个项的高度
        nextItem = nextItem.nextElementSibling
      }

      // 获取所有后续项目
      const allItems = Array.from(document.querySelectorAll('.catalogTreeItem-module_CatalogItem_xkX7p'))
      const startIndex = allItems.indexOf(menuItem)
      const subsequentItems = allItems.slice(startIndex + itemsToUpdate.length + 1)

      if (iconWrapper.classList.contains('catalogTreeItem-module_collapsed_u8yPq')) {
        // 收起状态
        itemsToUpdate.forEach(item => {
          item.style.display = 'none'
        })

        // 更新后续项目的位置
        subsequentItems.forEach(item => {
          const currentTop = parseInt(item.style.top)
          item.style.top = `${currentTop - offset}px`
        })
      } else {
        // 展开状态
        itemsToUpdate.forEach(item => {
          item.style.display = ''
        })

        // 恢复后续项目的位置
        subsequentItems.forEach(item => {
          const currentTop = parseInt(item.style.top)
          item.style.top = `${currentTop + offset}px`
        })
      }

      // 更新容器高度
      const container = document.querySelector('.lark-virtual-tree > div')
      if (container) {
        const visibleItems = document.querySelectorAll('.catalogTreeItem-module_CatalogItem_xkX7p:not([style*="display: none"])')
        container.style.height = `${visibleItems.length * 36}px`
      }
    }, { capture: true })
  })
})
</script>

<style scoped>
.BookCatalog-module_sideCatalog_wpCdP {
  height: calc(100vh - 165px);
}

.catalogTreeItem-module_collapseIconWrapper_XcS8B {
  transition: transform 0.2s ease;
}

.catalogTreeItem-module_collapsed_u8yPq {
  transform: rotate(-90deg);
}

.catalogTreeItem-module_CatalogItem_xkX7p {
  transition: top 0.2s ease;
}
</style>