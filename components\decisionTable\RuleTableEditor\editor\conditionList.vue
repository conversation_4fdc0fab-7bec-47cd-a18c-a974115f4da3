<template>
  <div>
    <p v-if="!hasTitle" class="structuralWord">
      <span>如果：</span>
    </p>
    <div
      class="conditionCom tableCon"
      :style="{
        // minWidth: '10000px',
        marginLeft: '20px',
        width: '400px',
        height: '56px'
      }"
    >
      <div ref="refNode">
        <ConditionComTable
          v-for="item in conditionList"
          :key="item.ruleCondition.conditionId"
          :style="item.style"
          :pos="pos + '_' + item.ruleCondition.conditionId"
          :fold="item.fold"
          :indexPath="item.indexPath"
          :indentLength="indentLength"
          :dataSource="item"
          :conditionValid="conditionValidList[item.ruleCondition.layer - 1]"
          :noRuleCellUnit="noRuleCellUnit"
          :conditionData="conditionData"
          :isTrack="isTrack"
          :locked="locked"
          :validList="validList"
          @conditionChange="conditionChange"
          @addRule="addRule"
          @addChildCondition="addChildCondition"
          @addTailItem="addTailItem"
          @decreaseRule="decreaseRule"
          @switcherChange="switcherChange"
          @logicBtnClick="logicBtnClick"
          :isTable="isTable"
        />
        <LogicBtn
          v-for="logicBtn in logicBtns"
          :key="logicBtn.pos"
          :style="logicBtn.style"
          :pos="pos + '_' + logicBtn.blockIndent + '_' + logicBtn.blockLayer"
          :text="logicBtn.logicalSymbol"
          :locked="locked"
          :isTrack="isTrack"
          @click="onLogicBtnClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import ConditionComTable from "./condition.vue";
import Raphael from "raphael";
import * as util from "@/components/ruleEditCom/utils/util";
import { LogicBtn } from "@/components/ruleEditCom/utils/";

// 定义 props
const props = defineProps({
  pos: String,
  conditionData: {
    type: Object
  },
  validList: {
    type: Array,
    default: () => []
  },
  locked: Boolean,
  isTrack: Boolean,
  hasTitle: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义 data
const refNode = ref(null);
const baseT = ref(0);
const baseL = ref(0);
const layerHeight = ref(30);
const indentLength = ref(30);
const Paper = ref(null);
const conditionList = ref([]);
const logicBtns = ref([]);

// 计算属性
const conditionValidList = computed(() => props.validList);

// 初始化方法，在组件挂载后执行
const init = async () => {
  await nextTick();
  if (refNode.value) {
    Paper.value = new Raphael(refNode.value); // 初始化 Raphael 实例
    const { conditionData } = props;
    conditionData && renderConditions(conditionData, false); // 渲染条件数据
    renderLogicBtns(conditionData, props.locked, props.isTrack); // 渲染逻辑按钮
  }
};

const renderConditions = (conditionData, needRenderNode, conditionValidList = []) => {
  clear();
  conditionList.value = getConditionList(conditionData, { needRenderNode }, conditionValidList);
};

const decreaseRule = ({ pos, conditionId, layer }) => {
  emit("decreaseRule", { pos, conditionId, layer });
};

const getConditionList = (conditionData, options, conditionValidList = []) => {
  const {
    needRenderNode,
    startLayer = 1,
    startIndent = 0,
    indexPath = "",
    parentFold = false
  } = options;
  const { ruleCondition, children = [], fold, indent } = conditionData;
  const blockIndent = indent;
  drawLine(blockIndent, 1, startLayer, startIndent, conditionData);

  if (ruleCondition) {
    if (needRenderNode) {
      const style = `position: absolute;top:${baseT.value + (ruleCondition.showLayer - 1) * layerHeight.value - 6}px;left: ${baseL.value + blockIndent * indentLength.value}px;`;
      const { layer, conditionId } = ruleCondition;
      return [
        {
          ruleCondition,
          style,
          fold,
          indexPath,
          indentLength: indentLength.value,
          dataSource: conditionData,
          conditionValid: conditionValidList[layer - 1],
          noRuleCellUnit: props.noRuleCellUnit,
          conditionData: props.conditionData,
          isTrack: props.isTrack,
          locked: props.locked,
          validList: props.validList,
          pos: props.pos + "_" + conditionId
        }
      ];
    }
  } else if (fold) {
    const _options = {
      needRenderNode: true,
      startLayer: 1,
      startIndent: 0,
      indexPath: indexPath + "|" + 0,
      parentFold: fold
    };
    return getConditionList(children[0], _options, conditionValidList);
  } else {
    return children.map((item, i) => {
      const _options = {
        needRenderNode: true,
        startLayer: 1,
        startIndent: 0,
        indexPath: indexPath + "|" + i,
        parentFold: fold
      };
      return getConditionList(item, _options, conditionValidList);
    }).flat();
  }
};

const drawLine = (indent, layer, startLayer, startIndent, conditionData) => {
  if (!Paper.value) return; // 确保 Paper 对象不为 null
  const { executeRes } = conditionData;
  let lineColor = "#DCDFE6";
  if (executeRes) {
    const { executeStatus } = executeRes;
    lineColor =
      executeStatus === true
        ? "green"
        : executeStatus === false
        ? "red"
        : "#989898";
  }

  const _ox = startIndent * indentLength.value + baseL.value;
  const _oy = (startLayer - 1) * layerHeight.value + baseT.value + layerHeight.value * 0.3;
  const _mx1 = 0;
  const _my1 = (layer - startLayer) * layerHeight.value;
  const _mx2 = (indent - startIndent) * indentLength.value;
  const _my2 = 0;

  const line = Paper.value.path(
    `M ${_ox} ${_oy} l ${_mx1} ${_my1} l ${_mx2} ${_my2}`
  );
  line.attr({
    "arrow-end": "diamond",
    "stroke-width": 1,
    stroke: lineColor
  });
};

const clear = () => {
  if (Paper.value) {
    Paper.value.clear();
  }
};

const resetSize = () => {
  if (Paper.value) {
    const newHeight = layerHeight.value * util.countDisplayedRules(props.conditionData) + 10;
    const newWidth = 10000;
    Paper.value.setSize(newWidth, newHeight);
    document.getElementsByClassName("conditionCom")[0].style.width = newWidth + "px";
  }
};

const renderLogicBtns = (conditionData, locked, isTrack) => {
  const arr = util.getLogicData(conditionData);
  logicBtns.value = arr.map(item => {
    const { logicalSymbol, blockIndent, blockLayer, blockShowLayer } = item;
    const style = `position: absolute;z-index: 1;top:${baseT.value + (blockShowLayer - 1) * layerHeight.value - 16}px;left: ${baseL.value + (blockIndent - 1) * indentLength.value}px;font-size: 10px`;
    return {
      logicalSymbol,
      blockIndent,
      blockLayer,
      blockShowLayer,
      style,
      pos: props.pos + "_" + blockIndent + "_" + blockLayer,
      locked,
      isTrack
    };
  });
};

// 定义 emit
const emit = defineEmits(['conditionChange', 'addRule', 'addChildCondition', 'addTailItem', 'decreaseRule', 'switcherChange', 'logicBtnClick']);

// 条件变化时触发的方法
const conditionChange = (pos, newContents) => {
  emit('conditionChange', pos, newContents); // 触发条件变化事件
};

// 添加规则的方法
const addRule = (pos, conditionId, layer) => {
  emit('addRule', pos, conditionId, layer);
};

// 添加子条件的方法
const addChildCondition = (pos, conditionId, layer) => {
  emit('addChildCondition', pos, conditionId, layer);
};

// 添加尾部项的方法
const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};

const switcherChange = (pos, conditionId, layer) => {
  emit("switcherChange", pos, conditionId, layer);
};

const logicBtnClick = (pos, val) => {
  emit("logicBtnClick", pos, val);
};



// 监听 conditionData 的变化，重新渲染条件和逻辑按钮
watch(
  () => props.conditionData,
  (newConditionData) => {
    if (newConditionData) {
      renderConditions(newConditionData, true);
      renderLogicBtns(newConditionData, props.locked, props.isTrack);
    }
  },
  { immediate: true, deep: true }
);

// 生命周期钩子
onMounted(() => {
  init();
  // 添加延迟执行，确保在DOM完全渲染后校正位置
  setTimeout(() => {
    if (Paper.value) {
      resetSize();
      if (props.conditionData) {
        renderConditions(props.conditionData, true);
        renderLogicBtns(props.conditionData, props.locked, props.isTrack);
      }
    }
  }, 100);
});
</script>

<style lang="scss" scoped>
.tableCon > div {
  height: 56px !important;
}
.tableCon > div > svg {
  height: 56px !important;
  width: 10000px;
}
</style>
