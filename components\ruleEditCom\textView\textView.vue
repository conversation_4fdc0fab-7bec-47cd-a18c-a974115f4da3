<template>
  <div class="rule-text-container">
    <div v-if="loading" class="loading-message">
      <p>正在加载规则信息...</p>
    </div>
    <div v-else-if="error" class="error-message">
      <p>加载规则信息失败: {{ error }}</p>
    </div>
    <div v-else>
      <RuleDetailContent 
        :ruleInfoData="ruleInfoData" 
        :ruleSize="false" 
        :viewStatus="viewStatus"
        :validateResult="validateResult"
        ref="ruleDetailRef" 
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, defineExpose } from 'vue';
import { getRuleByUuid } from '@/api/rule_base';
import RuleDetailContent from '@/components/ruleDetailCom/RuleDetailContent.vue';

const props = defineProps({
  uuid: {
    type: String,
    required: true
  },
  dateNow: {
    type: String,
    required: true
  },
  viewStatus: {
    type: String,
    required: true
  },
  validateResult: {
    type: Object,
    default: () => ({})
  }
});

const ruleInfoData = ref(null);
const loading = ref(true);
const error = ref(null);
const ruleDetailRef = ref(null);

// 获取最新的文本编辑器内容
const getLatestTextContent = () => {
  if (ruleDetailRef.value && ruleDetailRef.value.getEditorContent) {
    return ruleDetailRef.value.getEditorContent();
  }
  return ruleInfoData.value?.textDtl || '';
};

// 清除缓存
const clearLocalContent = () => {
  if (ruleDetailRef.value && ruleDetailRef.value.clearLocalContent) {
    ruleDetailRef.value.clearLocalContent();
  }
};

// 监听uuid变化
watch(() => props.uuid, (newVal) => {
  if (newVal) {
    loading.value = true;
    error.value = null;
    getRuleInfo(newVal);
  }
});
watch(() => props.dateNow, (newVal) => {
  if (newVal && props.viewStatus === 'ruleTextView') {
    loading.value = true;
    error.value = null;
    getRuleInfo(props.uuid);
  }
});


// 获取规则信息
function getRuleInfo(uuid) {
  loading.value = true;
  error.value = null;
  getRuleByUuid({
    uuids: uuid,
  }).then((res) => {
    if (res.data) {
      ruleInfoData.value = res.data;
    } else {
      error.value = '获取规则信息为空';
    }
  }).catch(err => {
    console.error('获取规则信息失败：', err);
    error.value = err.message || '获取规则信息失败';
  }).finally(() => {
    loading.value = false;
  });
}

// 暴露方法给父组件
defineExpose({
  getLatestTextContent,
  clearLocalContent
});
</script>

<style lang="scss" scoped>
.loading-message,
.error-message {
  text-align: center;
  margin-top: 50px;
  font-size: 16px;
}

.error-message {
  color: #f5222d;
}
</style>
