<!--规则路径公共组件-->
<script setup lang="ts">

const props = defineProps({
  path: {
    type: String,
    required: true
  },
  ruleName: {
    type: String
  },
  pathLength: {
      type: Number,
      default: 22
  },
  showCopyButton: {
    type: Boolean,
    default: false
  },
  copyTitle: {
    type: String,
    default: '复制规则路径'
  },
  copySuccessMessage: {
    type: String,
    default: '规则路径已复制到剪贴板'
  },
  useDotForFirstLevel: {
    type: Boolean,
    default: false
  }
})
/**
 * 格式化工具函数集合
 */

/**
 * 格式化规则路径，将点号替换为指定分隔符
 * @param packageName 包名
 * @param ruleName 规则名
 * @param flag 复制全路径开关
 * @param separator 分隔符，默认为 ' > '
 * @returns 格式化后的规则路径
 */
const formatRulePath = (packageName: string, ruleName: string,flag: boolean, separator: string = ' / '): string => {
    if (!packageName && !ruleName) return '';
    const fullPath = `${packageName || ''}.${ruleName || ''}`;
    return formatFullPath(fullPath, flag, separator);
};

/**
 * 格式化规则路径（单一参数版本）
 * @param fullPath 完整路径字符串
 * @param separator 分隔符，默认为 ' > '
 * @returns 格式化后的规则路径
 */
const formatFullPath = (fullPath: string, flag: boolean, separator: string = ' / '): string => {
    if (!fullPath) return '';

    if (props.useDotForFirstLevel) {
        const parts = fullPath.split('.');
        if (flag && parts.length > 1) {
            const remainingParts = parts.slice(1).join(separator);
            return `. / ${remainingParts}`;
        }
    }

    return fullPath.replace(/\./g, separator);
};

// 使用计算属性代替ref，确保props变化时自动重新计算
const fullRulePath = computed(() => {
    return props.ruleName
        ? formatRulePath(props.path, props.ruleName,true)
        : formatFullPath(props.path,true);
});

// 使用计算属性代替ref，确保props变化时自动重新计算(复制时提供全路径)
const copyFullRulePath = computed(() => {
    return props.ruleName
        ? formatRulePath(props.path, props.ruleName,false)
        : formatFullPath(props.path,false);
});

// 添加鼠标悬浮状态变量
const isHovering = ref(false);

// 鼠标悬浮事件处理函数
const handleMouseEnter = () => {
  isHovering.value = true;
};

const handleMouseLeave = () => {
  isHovering.value = false;
};

</script>

<template>
  <div class="rule-path-container"
       @mouseenter="handleMouseEnter"
       @mouseleave="handleMouseLeave">
    <a-tooltip :title="fullRulePath" v-if="pathLength>0&&fullRulePath.length>pathLength">
        {{ fullRulePath.substring(0,pathLength)+'...' }}
    </a-tooltip>
    <span v-else>{{fullRulePath}}</span>
    <div class="copy-button">
      <CopyButton
              v-if="showCopyButton && isHovering"
              :content="copyFullRulePath"
              :title="copyTitle"
              :successMessage="copySuccessMessage"
      />
    </div>

  </div>
</template>

<style scoped>
.rule-path-container {
  position: relative;
  display: inline-flex;
  align-items: center;
}
.copy-button{
  height:30px;
  margin-left: 5px;
}
</style>
