<!-- 默认开始页 -->
<script setup lang="ts">
    import type { ColumnType } from 'ant-design-vue/es/table'
    import { getRecentList, deleteRecent, collectRule, CollectObjectType,getCollectPageList,collectOrCancelCommonUse } from '@/api/dashboardApi'
    import { getDicValueByTypeCode } from '@/api/rule_base'
    import dayjs from 'dayjs'
    import relativeTime from 'dayjs/plugin/relativeTime'
    import { getUnid } from '@/utils/auth'
    import { openRulePage } from '@/utils/ruleUtil'

    dayjs.extend(relativeTime)

    definePageMeta({
        title: '工作台',
    })

    const message = inject<any>('message')
    const modal = inject<any>('modal')
    const showChooseRuleBaseModal = inject<(title: string, onSelect?: (ruleBase: { uuid: string, name: string }) => void) => void>('showChooseRuleBaseModal')

    // 是否显示新建内容菜单浮层
    const isAddContentPopoverShow = ref(false)

    const segmentedOptions = reactive(['编辑过', '浏览过'])
    const segmentedValue = ref(segmentedOptions[0])

    interface RuleType {
        code: string
        name: string
    }

    const ruleTypeOptions = ref<RuleType[]>([])
    const selectedType = ref('所有')

    // 获取规则类型字典值
    const getDicValue = (type: string, fn: (data: any) => void) => {
        getDicValueByTypeCode({ typeCode: type }).then((res) => {
            fn(res.data)
        })
    }

    function handleSelectType(ruleType: string) {
        selectedType.value = ruleType
        fetchTableData()
    }

    const belongs = ['所有']
    const selectedBelong = ref('所有')
    function handleSelectBelong(belong: string) {
        selectedBelong.value = belong
    }

    const creators = ['所有', '我的']
    const selectedCreator = ref('所有')

    const ruleBaseTitleRefs = ref(new Map());
    const ruleBaseTitleOverflow = reactive(new Map());

    function handleSelectCreator(creator: string) {
        selectedCreator.value = creator
        fetchTableData()
    }

    /**
     * 显示选择规则库对话框
     * @param type 新建的内容类型
     */
    function showChooseRuleBaseModalOnDashboard(type: string) {
        isAddContentPopoverShow.value = false

        if (type === 'AI文档') {
            // AI文档场景：选择规则库后打开AI助手
            showChooseRuleBaseModal?.('选择规则库', (ruleBase: { uuid: string, name: string }) => {
                modal.confirm({
                    footer: null,
                    icon: null,
                    closable: true,
                    centered: true,
                    width: '76vw',
                    class: 'styles-module_modal_8nT+9 ai-doc-modal',
                    content: h(AIWriter, {
                        ruleBaseId: ruleBase.uuid,
                        ruleBaseName: ruleBase.name,
                        onShowErrorMsg: message.error
                    }),
                })
            })
        } else {
            // 新建规则
            showChooseRuleBaseModal?.('新建' + type)
        }
    }

    /**
     * 显示模板中心
     */
    function showTemplateCenter() {
        modal.confirm({
            footer: null,
            icon: null,
            closable: true,
            centered: true,
            width: 1140,
            class: 'styles-module_modal_8nT+9',
            content: h(TemplateCenter, {
                showChooseRuleBaseModal,
                modal,
                message
            }),
        });
    }

    /**
     * 显示AI帮你写
     */
    function showAIWriter() {
        isAddContentPopoverShow.value = false

        showChooseRuleBaseModal?.('选择规则库', (ruleBase: { uuid: string, name: string }) => {
            modal.confirm({
                footer: null,
                icon: null,
                closable: true,
                centered: true,
                width: '76vw',
                class: 'styles-module_modal_8nT+9 ai-doc-modal',
                content: h(AIWriter, {
                    ruleBaseId: ruleBase.uuid,
                    ruleBaseName: ruleBase.name,
                    loginId: getUnid(),
                    onShowErrorMsg: message.error
                }),
            })
        })
    }

    interface TableRecord {
        ruleName: string
        packageNameAll: string
        createdTime: string
        ruleUuid: string
        uuid: string
        ifCollect: boolean
        engUuid: string
        folderUuid: string
    }

    // 表格数据
    const tableData = ref<TableRecord[]>([])
    const loading = ref(false)
    const pagination = reactive({
        current: 1,
        pageSize: 10,
        total: 0,
    })

    // 获取表格数据
    const tableContainerRef = ref<HTMLElement | null>(null)
    const fetchTableData = async (loadMore = false) => {
        loading.value = true
        try {
            const res = await getRecentList({
                action: segmentedValue.value === '编辑过' ? 'edit' : 'view',
                ruleType: selectedType.value === '所有' ? undefined : ruleTypeOptions.value.find(item => item.name === selectedType.value)?.code,
                loginId: selectedCreator.value === '我的' ? getUnid() : undefined,
                page: loadMore ? pagination.current : 1,
                several: pagination.pageSize,
            })
            tableData.value = loadMore ? [...tableData.value, ...res.data.data] : res.data.data
            pagination.total = res.data.totalCount
        } catch (error) {
            console.error('获取数据失败:', error)
        } finally {
            loading.value = false
        }
    }

    // 监听筛选条件变化
    watch(segmentedValue, () => {
        pagination.current = 1
        tableData.value = [];
        fetchTableData()
    })

    // 格式化时间
    const formatTime = (time: string) => {
        const date = dayjs(time)
        if (date.isSame(dayjs(), 'day')) {
            return '今天 ' + date.format('HH:mm')
        } else if (date.isSame(dayjs().subtract(1, 'day'), 'day')) {
            return '昨天 ' + date.format('HH:mm')
        } else {
            return date.format('MM-DD HH:mm')
        }
    }

    // 表格列配置
    const columns: ColumnType<TableRecord>[] = [
        {
            title: '规则名称',
            dataIndex: 'ruleName',
            key: 'ruleName',
            slots: { customRender: 'ruleName' },
            width:350,
            ellipsis: true,
            align: 'left',
        },
        {
            title: '规则包',
            dataIndex: 'packageNameAll',
            key: 'packageNameAll',
            align: 'left',
            slots: { customRender: 'packageNameAll' }
        },
        {
            title: '创建时间',
            dataIndex: 'createdTime',
            key: 'createdTime',
            width:300,
            slots: { customRender: 'createdTime' }
        },
        {
            title: '操作',
            key: 'action',
            width: 60,
            slots: { customRender: 'action' }
        }
    ]

    // 处理收藏
    const handleCollect = async (record: any) => {
        try {
            await collectRule({
                uuid: record.ruleUuid,
                type: CollectObjectType.RULE
            })
            record.ifCollect = !record.ifCollect
            message.success(record.ifCollect ? '收藏成功' : '取消收藏成功')
        } catch (error) {
            message.error(record.ifCollect ? '收藏失败' : '取消收藏失败')
            console.error('收藏操作失败:', error)
        }
    }

    // 处理移除记录
    const handleRemove = async (record: any) => {
        try {
            await deleteRecent({ uuid: record.uuid })
            message.success('移除成功')
            fetchTableData()
        } catch (error) {
            message.error('移除失败')
            console.error('移除记录失败:', error)
        }
    }

    //常用规则库列表
    const alwaysUsedList = ref([]);

    onMounted(() => {
        fetchTableData()
        // 获取规则类型选项
        getDicValue('type_rule', (arr) => {
            ruleTypeOptions.value = [{ code: '所有', name: '所有' }, ...arr]
        })
        //获取常用规则库
        fetchCollectPageList().then(() => {
            checkRuleBaseTitleOverflow();
        });

        // 添加滚动监听
        nextTick(() => {
            if (tableContainerRef.value) {
                tableContainerRef.value.$el.addEventListener('scroll', handleTableScroll)
            }
            
            // 在DOM更新后检查标题是否溢出
            setTimeout(() => {
                checkTitleOverflow();
            }, 100);
        })
    })

    // 组件卸载时移除滚动监听
    onUnmounted(() => {
        if (tableContainerRef.value) {
            tableContainerRef.value.$el.removeEventListener('scroll', handleTableScroll)
        }
    })

    const customScrollClass = ref('');
    function changeCustomScrollClass() {
        //设置常用规则高度
        if(showAlwaysUsedFlag.value === false){
            customScrollClass.value = 'custom-scroll-container';
        }else{
            if(alwaysUsedList.value && alwaysUsedList.value.length>3){
                customScrollClass.value = 'custom-scroll-short-container';

            }else if(alwaysUsedList.value === null || alwaysUsedList.value.length<=3){
                customScrollClass.value = 'custom-scroll-short-container2';
            }
        }
    }
    const fetchCollectPageList = async () => {
        try{
            const res = await getCollectPageList(CollectObjectType.RULE_BASE)
            alwaysUsedList.value = res.data;
            changeCustomScrollClass();
        }catch (error) {
            console.error('获取常用规则库数据失败:', error)
        }

    }
    const { ruleTypes } = useRuleTypes()

    //常用规则库收起/展开开关
    const showAlwaysUsedFlag = ref(true);
    const changeShowAlwaysUsedFlag = () => {
        showAlwaysUsedFlag.value = !showAlwaysUsedFlag.value;
        changeCustomScrollClass();
    }

    //取消常用方法
    const setAlwaysUsed = async (uuid) => {
        try {
            const res = await collectOrCancelCommonUse({
                uuid: uuid as string,
                type: CollectObjectType.RULE_BASE // 使用枚举类型代替硬编码的"1"
            })
            message.success(res.data);
            fetchCollectPageList();
        } catch (error) {
            message.error( '取消常用失败')
            console.error('取消常用操作失败:', error)

        }
    }

    // 监听滚动事件，实现滚动加载
    const handleTableScroll = () => {

        if (!tableContainerRef.value) return
        const container = tableContainerRef.value
        const scrollTop = container.$el.scrollTop
        const scrollHeight = container.$el.scrollHeight
        const clientHeight = container.$el.clientHeight
        // 当滚动到距离底部100px时，加载更多数据
        if (Math.floor(scrollHeight - scrollTop) === clientHeight && !loading.value && tableData.value.length < pagination.total) {
            pagination.current++
            fetchTableData(true)
        }
    }

    // 处理规则链接点击事件
    const handleRuleClick = async (record: any) => {
        return await openRulePage(record)
    }

    // 简化实现，直接在模板中使用条件判断
    function isEllipsisActive(element: HTMLElement): boolean {
        return element.offsetWidth < element.scrollWidth;
    }

    // 创建一个方法来处理规则名称的tooltip显示
    const ruleTitleRefs = ref(new Map());

    // 检查文本是否溢出并设置相应的title属性
    const checkTitleOverflow = () => {
        ruleTitleRefs.value.forEach((element, uuid) => {
            if (element && element.offsetWidth < element.scrollWidth) {
                element.setAttribute('data-overflow', 'true');
            } else if (element) {
                element.setAttribute('data-overflow', 'false');
            }
        });
    };

    // 当表格数据更新时，重新检查标题是否溢出
    watch(tableData, () => {
        nextTick(() => {
            setTimeout(() => {
                checkTitleOverflow();
            }, 100);
        });
    });

    const checkRuleBaseTitleOverflow = () => {
        nextTick(() => { 
            setTimeout(() => { 
                let parentOffsetWidth = 0; 
                ruleBaseTitleRefs.value.forEach((element, uuid) => {
                    if (element && element.parentElement) { 
                        parentOffsetWidth = element.parentElement.offsetWidth;
                        const isOverflowing = element.scrollWidth > parentOffsetWidth + 1;
                        ruleBaseTitleOverflow.set(uuid, isOverflowing);
                    } else {
                        if (ruleBaseTitleOverflow.has(uuid)) {
                            ruleBaseTitleOverflow.delete(uuid);
                        }
                    }
                });
            }, 100); 
        });
    };

    watch(alwaysUsedList, () => {
        ruleBaseTitleRefs.value.clear();
        ruleBaseTitleOverflow.clear();
        checkRuleBaseTitleOverflow();
    });

</script>

<template>
    <div class="DashboardLayout-module_wrapper_bRvE0" style="height: 80vh">
        <div class="ant-row" style="row-gap: 0px;">
            <div class="ant-col DashboardLayout-module_main_6rn+d">
                <div class="Dashboard-module_wrapper_GPBeB" data-testid="dashboard-index">
                    <div class="Dashboard-module_wrapper_tsXCe" data-testid="dashboard:index">
                        <div class="Dashboard-module_mainContent_KdOzP" style="min-height: 80vh">
                            <!--<div class="Dashboard-module_dashboardTitle_bDeua DashboardTitle-module_titleWarp_CQylY">开始
                            </div>-->

                            <!-- 注释掉新建规则库菜单项 -->
                            <!--<div class="QuickStart-module_wrapper_IVNNy">
                                <div v-for="item in ruleTypes" :key="item.code"
                                    class="QuickStart-module_itemWrapper_iYEAl QuickStart-module_dropDown_CgHt8"
                                    @click="showChooseRuleBaseModalOnDashboard(item.name)">
                                    <div class="QuickStart-module_icon_1-NoR">
                                        <component :is="item.icon" :size="50" />
                                    </div>
                                    <div data-testid="dashboard:quick_start:create_doc"
                                        class="QuickStart-module_content_fWfHm">
                                        <p class="QuickStart-module_title_otFG1">新建{{ item.name }}</p>
                                        <p class="QuickStart-module_desc_O+CH4">{{ item.desc }}</p>
                                    </div>
                                </div>

                                 <div><span><span class="larkui-popover-trigger" @click="showAddRepoModal">
                                  <div class="QuickStart-module_itemWrapper_iYEAl" data-aspm-click="c324040.d353827"
                                    data-testid="dashboard:quick_start:create_book">
                                    <div class="QuickStart-module_icon_1-NoR"><svg width="1em" height="1em" viewBox="0 0 256 256"
                                        xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-newbook-default">
                                        <g fill="none" fill-rule="evenodd">
                                          <path
                                            d="M135.095 36.265a58.24 58.24 0 0 0-3.647 12.801H86.952v144.537h95.981v-76.615a59.321 59.321 0 0 0 12.801 0v79.006c0 .407-.02.812-.06 1.214l-.037.327.033.2c.031.221.051.447.06.676l.003.23v14.692c0 15.317-12.416 27.734-27.733 27.734H58.013c-13.483 0-24.413-10.93-24.413-24.414 0-.184.002-.369.006-.553l.008-.222-.01-.194-.004-.231V64c0-15.317 12.417-27.733 27.733-27.733l73.762-.002Zm47.838 167.41-124.92.002A12.976 12.976 0 0 0 46.4 210.86v3.8l.01.075c.03.29.04.588.03.892l-.032.626-.008.4c0 6.414 5.2 11.614 11.614 11.614H168c8.247 0 14.933-6.686 14.933-14.934v-9.657ZM76.878 49.066l-15.545.002C53.086 49.067 46.4 55.753 46.4 64v132.739a22.944 22.944 0 0 1 11.614-3.136h18.865V49.066Z"
                                            fill="#585A5A" fill-rule="nonzero"></path>
                                          <path
                                            d="M189.333 11.733c25.92 0 46.934 21.013 46.934 46.934 0 25.92-21.013 46.933-46.934 46.933-25.92 0-46.933-21.013-46.933-46.933 0-25.92 21.013-46.934 46.933-46.934Zm.88 18.774a5.632 5.632 0 0 0-5.632 5.632v16.895h-16.896a5.632 5.632 0 0 0 0 11.265h16.895l.001 16.896a5.632 5.632 0 0 0 11.264 0V64.299h16.896a5.632 5.632 0 1 0 0-11.264l-16.897-.001.001-16.895a5.632 5.632 0 0 0-5.632-5.632Z"
                                            fill="#00B96B"></path>
                                        </g>
                                      </svg></div>
                                    <div>
                                      <p class="QuickStart-module_title_otFG1">新建规则库</p>
                                      <p class="QuickStart-module_desc_O+CH4">使用规则库整理规则</p>
                                    </div>
                                  </div>
                                </span></span></div>

                                <div><span>
                    <div class="QuickStart-module_itemWrapper_iYEAl" data-aspm-click="c324040.d353829"
                         data-testid="dashboard:quick_start:template" @click="showTemplateCenter">
                      <div class="QuickStart-module_icon_1-NoR"><svg width="1em" height="1em" viewBox="0 0 256 256"
                                                                     xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-template-center">
                          <g fill="none" fill-rule="evenodd">
                            <path d="M0-2h256v256H0z"></path>
                            <path
                                    d="M85.347 68.498c4.184 0 8.268.43 12.21 1.25l-10.487 42.1-.115.482c-2.668 11.56 1.745 23.148 10.454 30.14l.196.155-25.044 44.135C45.708 180.9 25.6 156.958 25.6 128.315c0-33.036 26.75-59.817 59.747-59.817Z"
                                    fill="#3384F5"></path>
                            <path
                                    d="m138.617 27.454.308.074c.003 0 .006.001.006.01l75.654 18.886c8.708 2.183 14.009 11.004 11.848 19.718l-18.87 75.777c-2.137 8.614-10.78 13.897-19.394 11.925l-.307-.073c-.003-.001-.006-.002-.007-.011l-2.925-.73-23.394-41.224a26.698 26.698 0 0 0-10.042-10.042c-12.824-7.278-29.12-2.781-36.397 10.042l-10.586 18.654c-3.805-3.903-5.565-9.626-4.157-15.303l18.87-75.777c2.137-8.614 10.78-13.897 19.393-11.926Z"
                                    fill="#ECB604"></path>
                            <path
                                    d="m149.63 118.563 51.308 90.407c3.546 6.249 1.355 14.189-4.893 17.735a13.01 13.01 0 0 1-6.421 1.695H87.01c-7.185 0-13.01-5.824-13.01-13.01 0-2.25.585-4.462 1.696-6.42l51.307-90.407c3.546-6.249 11.486-8.44 17.735-4.894a13.01 13.01 0 0 1 4.893 4.894Z"
                                    fill="#E4495B"></path>
                          </g>
                        </svg></div>
                      <div>
                        <p class="QuickStart-module_title_otFG1">模板中心</p>
                        <p class="QuickStart-module_desc_O+CH4">从模板中获取灵感</p>
                      </div>
                    </div>
                  </span></div>
                                <div @click="showAIWriter"><span>
                    <div class="QuickStart-module_itemWrapper_iYEAl" data-testid="dashboard:quick_start:ai_write"
                         data-feature-guide-container="knowledge_pie_ai_doc_create">
                      <div class="QuickStart-module_icon_1-NoR QuickStart-module_aiIcon_tqDrH"><svg width="1em"
                                                                                                    height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                                                                                                    class="larkui-icon larkui-icon-icon-ai-active icon-svg index-module_size_wVASz"
                                                                                                    data-name="IconAiActive" style="width: 20px; min-width: 20px; height: 20px;">
                          <path
                                  d="M128 17c61.304 0 111 49.696 111 111s-49.696 111-111 111H51c-18.778 0-34-15.222-34-34v-77C17 66.696 66.696 17 128 17ZM99.805 75.308c-6.371-1.826-13.016 1.86-14.84 8.23l-24.578 85.79c-1.521 5.309 1.55 10.846 6.859 12.367l.27.073c5.226 1.343 10.602-1.713 12.097-6.932l3.468-12.11h26.836l3.47 12.11c1.52 5.309 7.058 8.38 12.367 6.859 5.31-1.521 8.38-7.058 6.86-12.368L108.035 83.54a12 12 0 0 0-8.231-8.231ZM173 75h-20c-5.523 0-10 4.477-10 10s4.477 10 10 10h.5v66.5c0 .168.004.334.012.5H153c-5.523 0-10 4.477-10 10s4.477 10 10 10h20c5.523 0 10-4.477 10-10 0-5.36-4.216-9.734-9.513-9.988.009-.17.013-.34.013-.512V94.988c5.29-.26 9.5-4.633 9.5-9.988 0-5.523-4.477-10-10-10Zm-76.5 40.892 7.687 26.834H88.811l7.689-26.834Z"
                                  fill="currentColor" fill-rule="nonzero"></path>
                        </svg></div>
                      <div>
                        <p class="QuickStart-module_title_otFG1">AI 帮你写</p>
                        <p class="QuickStart-module_desc_O+CH4">AI 助手帮你一键生成规则</p>
                      </div>
                    </div>
                  </span></div>
                            </div>-->

                            <!--<div class="Dashboard-module_dashboardTitle_bDeua DashboardTitle-module_titleWarp_CQylY">规则库
                            </div>-->
                            <div class="Books-module_commonList_Q928s" data-testid="Books:Dashboard:Group">
                                <div class="CommonUsedList-module_wrapper_pY+tO">
                                    <div class="CommonUsedList-module_listWrapper_ruoLv">
                                        <div class="CommonUsedList-module_listHeadWrapper_3jI+Q">
                                            <div class="CommonUsedList-module_listHead_bY-Be always-used-title-class" style="margin-left: 3px"><b>常用规则库</b></div>
                                            <div
                                                    class="CommonUsedList-module_listHead_bY-Be CommonUsedList-module_listHeadArrow_4Ubwk toggle-button-align" v-if="showAlwaysUsedFlag" @click="changeShowAlwaysUsedFlag">
                                                收起<svg width="16" height="16" viewBox="0 0 256 256"
                                                       xmlns="http://www.w3.org/2000/svg"
                                                       class="larkui-icon larkui-icon-arrow-up" style="top:1px">
                                                <path
                                                        d="M57.297 162.139c3.834 3.834 10.007 3.904 13.927.209l.215-.21 48.99-48.99c3.834-3.833 10.007-3.903 13.927-.208l.215.209 48.99 48.99c3.905 3.905 10.237 3.905 14.142 0 3.834-3.834 3.904-10.008.21-13.927l-.21-.215-48.99-48.99c-11.598-11.599-30.331-11.715-42.073-.348l-.353.348-48.99 48.99c-3.905 3.905-3.905 10.237 0 14.142Z"
                                                        fill="currentColor" fill-rule="nonzero"></path>
                                            </svg></div>
                                            <div
                                                    class="CommonUsedList-module_listHead_bY-Be CommonUsedList-module_listHeadArrow_4Ubwk toggle-button-align" v-else @click="changeShowAlwaysUsedFlag">
                                                展开<svg style="top:1px" width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-arrow-down"><path d="M57.297 102.865c3.834-3.834 10.007-3.904 13.927-.21l.215.21 48.99 48.99c3.834 3.834 10.007 3.904 13.927.209l.215-.21 48.99-48.99c3.905-3.904 10.237-3.904 14.142 0 3.834 3.835 3.904 10.008.21 13.928l-.21.215-48.99 48.99c-11.598 11.599-30.331 11.714-42.073.348l-.353-.348-48.99-48.99c-3.905-3.905-3.905-10.237 0-14.142Z" fill="currentColor" fill-rule="nonzero"></path></svg>
                                            </div>
                                        </div>
                                        <div class="larkui-sortable CommonUsedList-module_list_qQh50" :class="alwaysUsedList && alwaysUsedList.length>6?'always-used-class':''" v-show="showAlwaysUsedFlag">
                                            <div class="Custom-module_empty_nHM5b" style="margin-top: 0px;margin-bottom: 10px" v-show="alwaysUsedList === null"><div>暂无常用规则库，您可以在<NuxtLink data-desktoptarget="rowser" class="Custom-module_moreLinkBtn_75bDE always-used-empty-msg" href="/ruleBaseManage">规则库管理</NuxtLink>中进入到某个规则库后，将其设为常用规则库</div></div>
                                            <div class="CommonUsedList-module_listItemWrapper_t0U5t" draggable="true"
                                                 data-aspm-param="index=0^type=Book" data-index="0" v-for="item in alwaysUsedList">
                                                <div class="CommonUsedList-module_listItem_TpTGH" style="cursor: pointer !important; transition: all 0.3s;">
                                                    <NuxtLink :to="`/ruleBase-${item.commonUuid}`" target="_self"
                                                              id="linklist-5966667130"
                                                              class="CommonUsedList-module_title_ShF00" 
                                                              draggable="false"><span
                                                                    class="CommonUsedList-module_icon_3XYzm"><IconRuleBase :size="24"/></span>
                                                        <div class="CommonUsedList-module_contentWrapper_H6el2">
                                                            <div class="CommonUsedList-module_textWrapper_11SEv">
                                                                <a-tooltip :title="ruleBaseTitleOverflow.get(item.commonUuid) ? item.name : undefined">
                                                                    <span :ref="el => { if (el) ruleBaseTitleRefs.set(item.commonUuid, el); else ruleBaseTitleRefs.delete(item.commonUuid); }" class="CommonUsedList-module_text_rH9Cb">{{item.name}}</span>
                                                                </a-tooltip>
                                                                <span class="CommonUsedList-module_privacyIcon_t5itV">
                                                                    <span class="name-lock larkui-tooltip"></span>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </NuxtLink>
                                                    <div @click="setAlwaysUsed(item.commonUuid)"
                                                         class="CommonUsedList-module_actionsIcon_5GzwZ CommonUsedList-module_btnPin_O4HEi larkui-popover-trigger">
                                                        <a-tooltip title="移出常用">
                                                            <IconPin/>
                                                        </a-tooltip>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="index-module_wrapper_p9MmH index-module_pc_6TxeL" data-aspm="c66161">
                                <div class="ant-tabs ant-tabs-top" value="doc">
                                    <div role="tablist" class="ant-tabs-nav">
                                        <div class="ant-tabs-nav-operations ant-tabs-nav-operations-hidden"><button
                                                type="button" class="ant-tabs-nav-more" tabindex="-1" aria-hidden="true"
                                                aria-haspopup="listbox" aria-controls="rc-tabs-0-more-popup"
                                                id="rc-tabs-0-more" aria-expanded="false"
                                                style="visibility: hidden; order: 1;"><span role="img"
                                                                                            aria-label="ellipsis" class="larkui-icon larkui-icon-ellipsis"><svg
                                                viewBox="64 64 896 896" focusable="false" data-icon="ellipsis"
                                                width="1em" height="1em" fill="currentColor" aria-hidden="true">
                                                        <path
                                                                d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z">
                                                        </path>
                                                    </svg></span></button></div>
                                    </div>
                                    <div class="ant-tabs-content-holder">
                                        <div class="ant-tabs-content ant-tabs-content-top">
                                            <div role="tabpanel" tabindex="0" aria-hidden="false"
                                                 class="ant-tabs-tabpane ant-tabs-tabpane-active"
                                                 id="rc-tabs-0-panel-doc" aria-labelledby="rc-tabs-0-tab-doc">
                                                <div class="index-module_segmentedWrapper_S2aPH">
                                                    <div class="always-used-title-class"><b>常用规则</b></div>
                                                    <div class="index-module_filterWrapper_JHQXC">
                                                        <div style="margin-right: 10px;margin-top: 5px">
                                                            <a-segmented v-model:value="segmentedValue"
                                                                         :options="segmentedOptions" />
                                                        </div>
                                                        <div data-testid="dashboard:filter:type"
                                                             :class="['index-module_filterItem_LTMKV', selectedType !== '所有' && 'index-module_activeFilterItem_hYmnA']">
                                                            <a-dropdown :trigger="['click']"
                                                                        overlay-class-name="index-module_filterMenuOverlay_IZPSV larkui-dropdown"
                                                                        placement="bottomRight">
                                                                <span>
                                                                    {{ selectedType === '所有' ? '类型' : selectedType }}
                                                                    <DownOutlined />
                                                                </span>
                                                                <template #overlay>
                                                                    <a-menu>
                                                                        <a-menu-item v-for="item in ruleTypeOptions"
                                                                                     :key="item.code"
                                                                                     @click="handleSelectType(item.name)">
                                                                            <IconCheckOutlined
                                                                                    v-if="item.name === selectedType" />
                                                                            {{ item.name }}
                                                                        </a-menu-item>
                                                                    </a-menu>
                                                                </template>
                                                            </a-dropdown>
                                                        </div>
                                                        <!--注释掉所属/创建者选择框-->
                                                        <!--<div data-testid="dashboard:filter:belong"
                                                             :class="['index-module_filterItem_LTMKV', selectedBelong !== '所有' && 'index-module_activeFilterItem_hYmnA']">
                                                            <a-dropdown :trigger="['click']"
                                                                        overlay-class-name="index-module_filterMenuOverlay_IZPSV larkui-dropdown"
                                                                        placement="bottomRight">
                                                                <span>
                                                                    {{ selectedBelong === '所有' ? '所属' : selectedBelong
                                                                    }}
                                                                    <DownOutlined />
                                                                </span>
                                                                <template #overlay>
                                                                    <a-menu>
                                                                        <a-menu-item v-for="item in belongs"
                                                                                     @click="handleSelectBelong(item)">
                                                                            <IconCheckOutlined
                                                                                    v-if="item === selectedBelong" />
                                                                            {{ item }}
                                                                        </a-menu-item>
                                                                    </a-menu>
                                                                </template>
                                                            </a-dropdown>
                                                        </div>
                                                        <div data-testid="dashboard:filter:creator"
                                                             :class="['index-module_filterItem_LTMKV', selectedCreator !== '所有' && 'index-module_activeFilterItem_hYmnA']">
                                                            <a-dropdown :trigger="['click']"
                                                                        overlay-class-name="index-module_filterMenuOverlay_IZPSV larkui-dropdown"
                                                                        placement="bottomRight">
                                                                <span>
                                                                    {{ selectedCreator === '所有' ? '创建者' :
                                                                        selectedCreator }}
                                                                    <DownOutlined />
                                                                </span>
                                                                <template #overlay>
                                                                    <a-menu>
                                                                        <a-menu-item v-for="item in creators"
                                                                                     @click="handleSelectCreator(item)">
                                                                            <IconCheckOutlined
                                                                                    v-if="item === selectedCreator" />
                                                                            {{ item }}
                                                                        </a-menu-item>
                                                                    </a-menu>
                                                                </template>
                                                            </a-dropdown>
                                                        </div>-->
                                                    </div>
                                                </div>
                                                <div
                                                        class="ant-table-wrapper larkui-table index-module_recentTable_S6TKN" style="margin-left: 10px" ref="basePoint">
                                                    <!--判断常用规则库高度-->
                                                    <a-table :columns="columns" :data-source="tableData" ref="tableContainerRef" :class="customScrollClass"
                                                             :loading="loading" :pagination="false">
                                                        <template #ruleName="{ record }: { record: TableRecord }">
                                                            <div class="index-module_nameCol_wzb6W">
                                                                <div v-for="item in ruleTypes">
                                                                    <component :is="item.icon" :size="24" v-if="item.code === record.ruleType"
                                                                               class="HeadNewButton-module_iconContainer_HmX2B" style="margin-top:3px"/>
                                                                </div>
                                                                <div class="index-module_titleWrapper_HsFm5">
                                                                    <NuxtLink
                                                                            class="index-module_title_3+pWJ"
                                                                            target="_blank"
                                                                            @click="handleRuleClick(record)">
                                                                        <div class="rule-title-wrapper">
                                                                            <div class="index-module_title_3+pWJ rule-title">
                                                                                {{ record.ruleName }}
                                                                            </div>
                                                                            <a-tooltip :title="record.ruleName" v-if="record.ruleName.length > 20">
                                                                                <div class="invisible-trigger"></div>
                                                                            </a-tooltip>
                                                                        </div>
                                                                    </NuxtLink>
                                                                </div>
                                                            </div>
                                                        </template>
                                                        <template #packageNameAll="{ record }: { record: TableRecord }">
                                                            <span style="color: #8c8c8c;">
                                                                 <RulePath
                                                                         :path="record.packageNameAll"
                                                                         showCopyButton
                                                                 />
                                                            </span>
                                                        </template>
                                                        <template #createdTime="{ text }: { text: string }">
                                                            <span class="index-module_timeCol_0H8AB" :title="text" style="color: #8c8c8c;">
                                                                {{ formatTime(text) }}
                                                            </span>
                                                        </template>
                                                        <template #action="{ record }: { record: TableRecord }">
                                                            <a-dropdown :trigger="['click']" placement="bottomRight">
                                                                <span
                                                                        class="index-module_more_sDgGm larkui-popover-trigger"
                                                                        data-testid="more-actions:Popover:more-actions"
                                                                        style="line-height: 16px; padding: 4px;">
                                                                    <IconMoreHorizontal />
                                                                </span>
                                                                <template #overlay>
                                                                    <a-menu>
                                                                        <a-menu-item @click="handleCollect(record)">
                                                                            <template #icon>
                                                                                <IconStar v-if="record.ifCollect"/>
                                                                                <IconStarOutlined v-else/>
                                                                            </template>
                                                                            {{ record.ifCollect ? '取消收藏' : '收藏' }}
                                                                        </a-menu-item>
                                                                        <a-menu-item @click="handleRemove(record)">
                                                                            <template #icon>
                                                                                <IconSwipOutlined />
                                                                            </template>
                                                                            移除记录
                                                                        </a-menu-item>
                                                                    </a-menu>
                                                                </template>
                                                            </a-dropdown>
                                                        </template>
                                                    </a-table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
    .QuickStart-module_itemWrapper_iYEAl {
        height: 100px;
    }

    .QuickStart-module_content_fWfHm {
        height: 80px;
    }

    .QuickStart-module_itemWrapper_iYEAl.QuickStart-module_dropDown_CgHt8 .QuickStart-module_desc_O\+CH4 {
        overflow: unset;
        text-overflow: unset;
        white-space: unset;
    }
    .custom-scroll-container{
        height: calc(100vh - 230px); /* or any desired height */
        overflow-y: auto; /* enable vertical scrolling */

    }
    .custom-scroll-short-container2{
        height: calc(100vh - 310px); /* or any desired height */
        overflow-y: auto; /* enable vertical scrolling */

    }

    .custom-scroll-short-container{
        height: calc(100vh - 400px); /* or any desired height */
        overflow-y: auto; /* enable vertical scrolling */

    }
    .custom-scroll-container::-webkit-scrollbar {
        width: 0; /* 对于垂直滚动条 */
        height: 0; /* 对于水平滚动条 */
    }
    .custom-scroll-short-container::-webkit-scrollbar {
        width: 0; /* 对于垂直滚动条 */
        height: 0; /* 对于水平滚动条 */
    }
    .custom-scroll-short-container2::-webkit-scrollbar {
        width: 0; /* 对于垂直滚动条 */
        height: 0; /* 对于水平滚动条 */
    }
    .always-used-class{
        height: 22vh; /* or any desired height */
        overflow-y: auto; /* enable vertical scrolling */
    }
    .always-used-title-class{
        float: left;
        font-size:16px;
        margin-right: 10px;
        display: grid;
        place-items: center;
        height: 40px;
        font-weight:500;
        margin-left: 20px;
        cursor: pointer;
        transition: color 0.3s;
    }
    .always-used-title-class:hover {
        color: var(--yq-primary-color) !important;
    }
    .always-used-empty-msg{
        color:var(--yq-text-primary);
        padding-left: 7px;
        padding-right: 7px;
        margin-left: 5px;
        margin-right: 5px;
        font-size:14px;
        font-weight: 400
    }
    .toggle-button-align {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 22px;
        gap: 4px;
    }
    .CommonUsedList-module_listHeadArrow_4Ubwk {
        cursor: pointer;
        transition: color 0.3s;
    }
    .CommonUsedList-module_listHeadArrow_4Ubwk:hover {
        color: var(--yq-primary-color) !important;
    }
    .CommonUsedList-module_title_ShF00 {
        cursor: pointer;
        transition: color 0.3s;
    }
    .CommonUsedList-module_title_ShF00:hover {
        color: var(--yq-primary-color) !important;
    }
    .CommonUsedList-module_text_rH9Cb {
        transition: color 0.3s;
    }
    .CommonUsedList-module_title_ShF00:hover .CommonUsedList-module_text_rH9Cb {
        color: var(--yq-primary-color) !important;
    }
    .CommonUsedList-module_listItem_TpTGH {
        cursor: pointer !important;
        transition: all 0.3s;
    }
    .CommonUsedList-module_listItem_TpTGH:hover {
        background-color: rgba(230, 247, 255, 1) !important;
    }
    .index-module_title_3\+pWJ {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300px;
    }
    
    .rule-title-wrapper {
        position: relative;
    }
    
    .invisible-trigger {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
</style>
