# 语雀文档提取工具

## 功能介绍

这是一个用于从语雀知识库中提取文档并转换为erule3.0项目内置文档格式的浏览器脚本工具。该工具可以：

1. 提取指定菜单结构（如"erule3.0帮助文档"）
2. 生成符合项目规范的Vue组件菜单
3. 提取文档内容并转换为Vue组件页面
4. 下载文档中的所有图片资源
5. 自动处理文档间的链接关系

## 工作原理

### 核心流程

1. **菜单提取**：
   - 自动展开语雀文档的菜单树
   - 识别并提取目标菜单结构
   - 生成Vue组件格式的菜单代码

2. **文档内容提取**：
   - 模拟浏览器访问每个文档页面
   - 通过滚动页面确保所有内容（包括懒加载内容）都被加载
   - 提取文档DOM结构并转换为Vue组件

3. **图片处理**：
   - 识别文档中的所有图片元素
   - 下载图片资源并保存为本地文件
   - 更新图片路径为项目规范的路径（`/img/doc/`）

4. **文件导出**：
   - 生成菜单组件文件（`DocMenu.vue`）
   - 为每个文档生成对应的Vue组件页面
   - 导出所有图片资源

### 技术细节

- 使用DOM操作和事件模拟来处理语雀的动态加载内容
- 采用滚动加载策略确保捕获所有懒加载内容
- 特殊处理语雀的图片懒加载机制，确保所有图片都能被正确提取
- 使用浏览器的下载API导出生成的文件

## 使用方法

### 安装脚本

1. 安装Tampermonkey浏览器扩展（Chrome、Firefox、Edge等均支持）
2. 创建新脚本，复制`genDoc.js`的内容并保存
3. 确保脚本已启用

### 浏览器弹出窗口权限设置

**为什么需要弹出窗口权限？**

本工具使用新窗口方式访问每个文档页面以提取内容。这是因为：
1. 需要完整加载每个文档的DOM结构和资源
2. 避免干扰当前页面的状态和导航
3. 允许并行处理多个文档，提高效率
4. 能够模拟真实的页面滚动和交互，确保懒加载内容被正确加载

**如何设置弹出窗口权限：**

* **Chrome浏览器**：
  1. 点击地址栏右侧的"已阻止弹出窗口"图标 (🚫)
  2. 选择"始终允许来自 [语雀域名] 的弹出窗口和重定向"
  3. 点击"完成"

* **Firefox浏览器**：
  1. 当弹出窗口被阻止时，会在地址栏显示一个通知
  2. 点击"选项"按钮
  3. 选择"允许来自 [语雀域名] 的弹出窗口"

* **Edge浏览器**：
  1. 点击地址栏右侧的"弹出窗口已阻止"通知
  2. 选择"始终允许来自此网站的弹出窗口"

也可以在浏览器设置中永久允许特定网站的弹出窗口：

* **Chrome/Edge**：设置 > 隐私和安全 > 网站设置 > 弹出窗口和重定向 > 添加允许的网站
* **Firefox**：设置 > 隐私与安全 > 权限 > 弹出窗口 > 例外

如果在使用过程中看到"无法打开新窗口，请允许弹出窗口"的错误提示，请按照上述步骤设置权限后重试。

### 提取文档

1. 访问语雀知识库中包含目标文档的页面（如`https://teratech.yuque.com/org-wiki-teratech-ebpxyp/*`）
2. 页面右下角会出现"提取erule3.0帮助文档"按钮
3. 点击按钮开始提取过程
4. 等待提取完成，期间可以通过右下角的提示查看进度
5. 文件会自动下载到浏览器的默认下载目录

### 文件整理

提取完成后，您将获得以下文件：

- `DocMenu.vue`：菜单组件文件
- 多个文档页面组件（如`xxx.vue`）
- 多个图片文件（各种格式）

可以使用提供的`yuque_update.sh`脚本将这些文件移动到项目的正确位置：

```bash
./yuque_update.sh
```

该脚本会将：
- `DocMenu.vue`移动到`businessComponents/doc/`目录
- 其他Vue文件移动到`pages/doc/`目录
- 图片文件移动到`public/img/doc/`目录

## 配置选项

脚本中的`CONFIG`对象包含多种可配置选项：

```javascript
const CONFIG = {
    // 按钮配置
    BUTTON_TEXT: '提取erule3.0帮助文档',
    LOADING_TEXT: '正在提取...',
    
    // 目标配置
    TARGET: {
        MENU_NAME: "erule3.0帮助文档"
    },
    
    // 操作延迟配置
    MENU_EXPAND_DELAY: 2000,
    TOOLTIP_DELAY: 500,
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000,
    DEBOUNCE_DELAY: 1000,
    
    // DOM选择器配置
    SELECTORS: {
        // 各种DOM选择器...
    },
    
    // 文本匹配配置
    TEXT: {
        TOOLTIP_EXPANDED: '全部折叠'
    },
    DELAY: 2000,                   // 通用延迟时间
    DELAY_AFTER_LOAD: 2000,        // 页面加载后的延迟时间
    DELAY_BETWEEN_REQUESTS: 1000,  // 请求之间的延迟时间 
    TIMEOUT: 30000                 // 加载超时时间
};
```

主要配置项说明：
- `BUTTON_TEXT`：提取按钮的文本
- `TARGET.MENU_NAME`：要提取的目标菜单名称
- 各种延迟时间和重试次数设置
- DOM选择器配置（如需适应语雀界面变化可修改）

## 常见问题

### 图片未完全下载

如果发现某些图片未被下载，可能是因为：

1. 图片使用了特殊的懒加载机制
2. 页面滚动过程中未触发图片加载
3. 图片来自非语雀域名（如第三方图床）

解决方法：
- 增加脚本中的等待时间（`CONFIG.DELAY`等参数）
- 修改`browseDocument`函数中的滚动策略
- 检查并调整图片域名过滤条件

### 菜单结构不完整

如果提取的菜单结构不完整，可能是因为：

1. 目标菜单名称配置不正确
2. 菜单展开过程失败
3. 虚拟滚动未加载所有菜单项

解决方法：
- 确认`CONFIG.TARGET.MENU_NAME`设置正确
- 增加`CONFIG.MENU_EXPAND_DELAY`的值
- 调整`scrollMenuToLoadAll`函数中的滚动参数

### 弹出窗口被阻止

如果看到"无法打开新窗口，请允许弹出窗口"的错误提示，请确保：

1. 已按照上述说明设置浏览器的弹出窗口权限
2. 没有其他浏览器扩展阻止弹出窗口
3. 浏览器的弹出窗口阻止功能未全局启用

解决方法：
- 检查浏览器地址栏是否显示弹出窗口被阻止的通知，并允许弹出窗口
- 在浏览器设置中将语雀域名添加到弹出窗口白名单
- 暂时禁用可能干扰的浏览器扩展

### 特殊DOM元素处理

脚本已针对语雀文档中的特殊DOM元素进行了处理：

1. 懒加载图片（`.ne-image-box-loading`和`.ne-image-hide`类）
2. 未加载的图片包装器（`.ne-image-wrap:not(.ne-image-loaded)`）
3. 评论面板（`.index-module_commentPanel_rHJ\\+M`）

如果发现新的特殊元素未被正确处理，可以修改相应的处理函数。

## 注意事项

- 该脚本仅用于提取您有权访问的文档
- 请遵守语雀的使用条款和版权规定
- 大量文档提取可能需要较长时间，请耐心等待
- 建议在提取前保存您的工作，避免浏览器意外关闭导致进度丢失
- **必须允许浏览器弹出窗口**，否则脚本无法正常工作

## 脚本文件说明

- `genDoc.js`：主脚本文件，用于从语雀提取文档
- `yuque_update.sh`：辅助脚本，用于将提取的文件移动到项目正确位置

## 项目集成

提取的文档将以Vue组件的形式集成到erule3.0项目中：

1. 菜单组件（`DocMenu.vue`）将被放置在`businessComponents/doc/`目录下
2. 文档页面组件将被放置在`pages/doc/`目录下，可通过路由`/doc/xxx`访问
3. 图片资源将被放置在`public/img/doc/`目录下，可通过`/img/doc/xxx`路径访问

这种集成方式确保了文档可以作为项目的一部分，而不需要依赖外部文档系统。 

## 关于单个文件下载的说明

本工具采用单个文件逐一下载的方式，而非打包为ZIP文件统一下载，主要基于以下考虑：

### 技术原因

1. **浏览器限制**：浏览器对生成大型ZIP文件有内存限制，特别是当文档数量多、图片资源大时，可能导致内存溢出或浏览器崩溃。

2. **处理可靠性**：单个文件下载方式可以确保每个文件都被正确处理和保存，即使中途出现网络问题或其他错误，已下载的文件也不会丢失。

3. **进度可视化**：用户可以直观地看到每个文件的下载进度，了解整个提取过程的完成情况。

4. **错误隔离**：如果某个文件处理失败，不会影响其他文件的下载，便于定位和解决问题。

### 实践经验

在开发过程中，我们尝试过ZIP打包下载的方式，但遇到了以下问题：

1. 当文档和图片数量较多时，ZIP文件生成过程会占用大量内存，导致浏览器性能下降甚至崩溃。

2. 打包过程中如果出现错误，所有工作都会前功尽弃，用户需要重新开始整个提取过程。

3. 某些浏览器对大型ZIP文件的下载支持不佳，可能导致下载失败或文件损坏。

4. 无法提供详细的进度反馈，用户体验较差。

虽然单个文件下载方式会产生多个下载通知，但结合`yuque_update.sh`脚本的使用，文件整理过程仍然简单高效。这种方式在实际使用中证明更加可靠和用户友好。 