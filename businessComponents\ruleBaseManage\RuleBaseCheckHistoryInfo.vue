<!-- 审核历史信息详情 -->
<template>
    <div class="rule-base-check-history-info">
        <RuleInfoDisplay :data="tableData" :config="checkHistoryConfig" :showRuleContent="false" />
    </div>
</template>

<script setup lang="ts">
import { getRuleCheckLogByUuid } from "@/api/rule_base";
import { ref } from 'vue';

const props = defineProps({
    dataId: {
        type: String,
        default: () => '',
    }
});

const tableData = ref({});

// 审核历史信息配置
const checkHistoryConfig = [
    { label: '规则名称', field: 'ruleName', span: 12 },
    { label: '审核状态', field: 'checkStatusName', span: 12 },
    { label: '审核人', field: 'checkId', span: 12 },
    { label: '审核时间', field: 'checkTimeStr', span: 12 },
    { label: '提交人', field: 'createdId', span: 12 },
    { label: '创建时间', field: 'createdTimeStr', span: 12 }
];

onMounted(() => {
    getRuleCheckLogByUuid({
        uuid: props.dataId,
    }).then((res) => {
        tableData.value = res.data;
    });
});
</script>

<style lang="scss" scoped>
.rule-base-check-history-info {
    padding: 20px;
}
</style>
