<!-- 任务发布页 -->
<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import { userOrgList, taskDetails } from "@/api/task";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo.vue";
    import useTableConfig from '@/composables/useTableConfig';
    import { nextTick } from 'vue';

    definePageMeta({
        title: '任务发布'
    })
    const router = useRouter();
    const message = inject('message') as any;

    const tableDate = ref<any[]>([]);

    // 添加 loading 变量
    const loading = ref(false);

    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);
    const task_state_options = ref([
        { value: "10", label: "任务待发布" },
        { value: "12", label: "任务暂停发布" },
        { value: "20", label: "任务发布中" },
        { value: "21", label: "任务待立即发布" },
        { value: "22", label: "发布异常" },
        { value: "14", label: "结束" },
    ]);

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    // 获取列表数据
    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await taskDetails({
                appNo: params.appNo,
                businessLine: params.businessLine,
                createdName: params.createdName,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                createdTimeStr: params.createdTimeStr,
                applyOrgId: params.applyOrgId,
                orgId: params.orgId,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取数据失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLine_option.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLine_option.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };

    const getCreatedName = (value: string) => {

        // 手动触发搜索配置中归属机构选择器的值更新
        const applyOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'applyOrgId');
        if (applyOrgField && applyOrgField.compConfig) {
            // 先清空选项
            applyOrgField.compConfig.options = [];
        }

        const orgIdField = searchConfig.advancedSearchFields.find(field => field.field === 'orgId');
        if (orgIdField && orgIdField.compConfig) {
            // 先清空选项
            orgIdField.compConfig.options = [];
        }

        userOrgList({
            roleName: "DEMAND_ROLE_AUDIT",
            businessCode: value,
        }).then((res: any) => {
            model_type_options.value = res.data;

            // 更新搜索配置中的归属机构选项
            if (applyOrgField && applyOrgField.compConfig && model_type_options.value) {
                // 使用新数组替换原有options，保证响应式更新
                const newOptions = model_type_options.value.map(item => ({
                    name: item.orgName,
                    value: item.id
                }));
                // 使用nextTick确保DOM更新后再填充数据
                nextTick(() => {
                    applyOrgField.compConfig.options = [...newOptions];
                });
            }

            // 更新搜索配置中的审核机构选项
            if (orgIdField && orgIdField.compConfig && model_type_options.value) {
                // 使用新数组替换原有options，保证响应式更新
                const newOptions = model_type_options.value.map(item => ({
                    name: item.orgName,
                    value: item.id
                }));
                // 使用nextTick确保DOM更新后再填充数据
                nextTick(() => {
                    orgIdField.compConfig.options = [...newOptions];
                });
            }
        });
    };

    //显示详情对话框
    const datar = ref<Record<string, any>>({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false);

    const showModal = (type: string, record: Record<string, any> = {}) => {
        modalType.value = type;
        if (type === 'Announcement') {
            datar.value = {
                type: "Announcement",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        if (type === 'release') {
            datar.value = {
                type: "release",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                roleName: record.ruleName,
                businessCode: record.businessLine,
            };
        }
        isModalVisible.value = true;
    };

    const close = (flag: boolean) => {
        if(flag){//如果是数据提交，则刷新表单
            fetchTaskList();
        }
        isModalVisible.value = false;
    }

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input' as const
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input' as const
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select' as const,
                compConfig: {
                    options: [],
                    onChange: getCreatedName,
                    clearFields: ['applyOrgId']
                }
            },
            {
                label: '归属机构',
                field: 'applyOrgId',
                compType: 'select' as const,
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker' as const,
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker' as const,
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input' as const
            },
            {
                label: '审核机构',
                field: 'orgId',
                compType: 'select' as const,
                compConfig: {
                    options: model_type_options.value.map(item => ({
                        name: item?.orgName || '',
                        value: item?.id || ''
                    }))
                }
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select' as const,
                compConfig: {
                    options: task_state_options.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 事件配置
    const eventConfig = {
        // 搜索事件 - 不再直接使用handleSearch
        searchEvent: () => {},
        // 添加事件（任务发布页面不需要添加功能，但必须提供这个字段）
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 日期字段特殊处理
            dateFields: {
                startField: 'fristTime',
                endField: 'endTime',
            },
            // 查询方法
            queryMethod: fetchTaskList
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: Record<string, any>) => {
        if (record.state != 14) {
            return [
                {
                    key: 'release',
                    label: '发布',
                    onClick: () => showModal('release', record)
                }
            ];
        }
        return [];
    };

    // 组件引用
    const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];

    // 数据加载完成处理函数
    const handleDataLoaded = (response: any) => {
        if (response && response.data) {
            tableDate.value = response.data;
        }
    };

    onMounted(() => {
        getOption();
    });
</script>

<template>
    <ListPage
        ref="listLayout"
        title="任务发布"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="false"
        @dataLoaded="handleDataLoaded"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('Announcement',record)">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state as keyof typeof TASK_STATE] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'任务发布'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
