<!-- 规则详情通用组件 -->
<template>
    <div id="doc-reader-content" class="DocReader-module_content_AcIMy">
        <div class="DocReader-module_header_xAOQU">
            <div>
                <div class="DocReader-module_title_fXOQi">
                    <h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title" style="margin-left: 30px">
                        {{ ruleInfoData.ruleName }}
                    </h1>
                </div>
            </div>
        </div>
        <div>
            <article id="content" class="article-content" tabindex="0" style="outline-style: none;">
                <div class="ne-doc-major-viewer">
                    <div class="yuque-doc-content" data-df="lake" style="position: relative;">
                        <div>
                            <RuleInfoDisplay
                                :data="ruleInfoData"
                                :config="ruleDisplayConfig"
                            />
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</template>

<script setup>
import { recordAccess } from '@/api/dashboardApi';


const props = defineProps({
    ruleInfoData: Object,
});

const route = useRoute();

onMounted(() => {
    // 异步记录访问历史 首页进入不添加
    if(route.query.backFlag && route.query.backFlag !== 'dashboard'){
        recordAccess({ruleUuid: props.ruleInfoData.uuid}).catch(console.error)
    }
});

const ruleDisplayConfig = [
    { label: '规则名称', field: 'ruleName' },
    { label: '规则路径', field: 'packageNameAll', },
    { label: '规则状态', field: 'status', format: 'status' },
    { label: '加锁人', field: 'lockId' },
    { label: '修改时间', field: 'lastModifiedTimeStr' },
    { label: '修改人', field: 'modifiedId' },
    { label: '创建时间', field: 'createdTimeStr' },
    { label: '创建人', field: 'createdId' },
    { label: '规则编号', field: 'ruleNumber' },
    { label: '规则描述', field: 'descs', tooltip: true, format: 'truncate' }
];
</script>

