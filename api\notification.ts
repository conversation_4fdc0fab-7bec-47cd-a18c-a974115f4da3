// 通知相关 API 封装
import request from '@/utils/request'

const BASE_URL = '/api/notification'

/**
 * 发送邮件通知
 * @param to 收件人
 * @param subject 主题
 * @param content 内容
 * @param isHtml 是否为 HTML 格式，默认 false
 */
export function sendEmail(params: {
  to: string
  subject: string
  content: string
  isHtml?: boolean
}) {
  return request({
    url: BASE_URL + '/email',
    method: 'post',
    params
  })
}

// /**
//  * 发送钉钉通知
//  * @param to 接收人
//  * @param subject 主题
//  * @param content 内容
//  * @param isMarkdown 是否为 Markdown 格式，默认 false
//  */
// export function sendDingTalk(params: {
//   to: string
//   subject: string
//   content: string
//   isMarkdown?: boolean
// }) {
//   return request({
//     url: BASE_URL + '/dingtalk',
//     method: 'post',
//     params
//   })
// }

// /**
//  * 发送企业微信通知
//  * @param to 接收人
//  * @param subject 主题
//  * @param content 内容
//  * @param isMarkdown 是否为 Markdown 格式，默认 false
//  */
// export function sendWeChat(params: {
//   to: string
//   subject: string
//   content: string
//   isMarkdown?: boolean
// }) {
//   return request({
//     url: BASE_URL + '/wechat',
//     method: 'post',
//     params
//   })
// }
