<!-- 分配按钮页 -->

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { getInfobtn, getrolebtn, getInfosub, showbtn } from "@/api/role";
import qs from "qs";
definePageMeta({
    title: '分配按钮'
})

const props = defineProps({
    roleId: {
        type: String,
        required: true
    }
});

const router = useRouter();
const message = inject('message')
const tableData = ref<any[]>([]);
const selectedRowKeys = ref<string[]>([]);
const menu_btn = ref<boolean>(false);
const rolebtn = ref<any[]>([]);
const checkedBtns = ref<any[]>([]);
const rowBtnNameArr = ref<string[]>([]);
const rowBtnIdArr = ref<string[]>([]);
const selectIdArr = ref<string[]>([]);
const activeRowID = ref<string>("");
const rowlist = ref<any>({});

onMounted(() => {
    getlist();
});

const allchecked = () => {
    checkedBtns.value = rolebtn.value;
};
const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    showSizeChanger:true,
    isLoading :false,
    showQuickJumper:true,
    showTotal: (total) => `共 ${total} 条数据`,
});
const getlist = () => {
    getInfobtn({
        roleId: props.roleId,
    }).then((res) => {
        if (res.data && res.data.length) {
            pagination.value.total = res.data.totalCount;
            tableData.value = res.data.map((item) => {
                if (item.buttonNames && item.buttonNames.length) {
                    item.buttonNames = item.buttonNames.toString();
                }
                item.isClick = false;
                return item;
            });
        }
    });
};
interface CustomRowProps {
    onClick?: (event: MouseEvent) => void;
}
const customRow = (record: any): CustomRowProps => {
    return {
        onClick: (event: MouseEvent) => {
            rowClick(record);
        },
    }
}
const rowClick = (record: any) => {
    rowlist.value = record;
    activeRowID.value = record.id;
    selectedRowKeys.value = [record.id];
    let pars = {
        menuId: record.id, // 弹框菜单id
    };
    // 根据菜单id查询按钮列表信息
    getrolebtn(pars).then((res) => {
        if (res.data && res.data.length) {
            menu_btn.value = true;
            rolebtn.value = [];
            res.data.forEach((item) => {
                rolebtn.value.push({
                    id: item.id,
                    name: item.name,
                    menuPath: item.menuPath,
                    rowId: `${record.id}`,
                    rowBtnId: `${record.id}_${item.id}`,
                });
            });
            rolebtn.value.sort((a, b) => a.name.localeCompare(b.name));

            // 根据当前行的 buttonNames 设置选中状态
            checkedBtns.value = [];
            if (record.buttonNames) {
                const selectedNames = record.buttonNames.split(',');
                rolebtn.value.forEach(btn => {
                    if (selectedNames.includes(btn.name)) {
                        checkedBtns.value.push(btn);
                    }
                });
            }
        } else {
            rolebtn.value = [];
            message.error("没有可分配的按钮");
        }
    });
};

const setBtnStatus = (resD: string[]) => {
    checkReset();
    resD.forEach((id) => {
        rolebtn.value.forEach((item) => {
            if (id.toString() === item.id.toString()) {
                checkedBtns.value.push(item);
            }
        });
    });
};

const btnInfo = () => {
    if (rowlist.value.isClick === false) {
        rowlist.value.isClick = true;
    } else {
        rowlist.value.isClick = false;
    }
    rowBtnNameArr.value = [];
    rowBtnIdArr.value = [];
    if (checkedBtns.value && checkedBtns.value.length) {
        const _current_row_checked = checkedBtns.value.filter((item) => {
            return item.rowId.toString() === rowlist.value.id.toString();
        });
        _current_row_checked.forEach((item) => {
            rowBtnNameArr.value.push(item.name);
            rowBtnIdArr.value.push(item.rowBtnId);
            selectIdArr.value.push(item.id);
        });
        menu_btn.value = false;
        rowlist.value.buttonNames = rowBtnNameArr.value.toString();
        rowlist.value.menuAndButtonIds = rowBtnIdArr.value.toString();
    } else {
        rowlist.value.buttonNames = "";
        rowlist.value.menuAndButtonIds = "";
        menu_btn.value = false;
    }
};

const checkReset = () => {
    selectIdArr.value = [];
    checkedBtns.value = [];
};

const submitBtn = () => {
    let menuBId: string[] = [];
    tableData.value.forEach((item) => {
        if (item.menuAndButtonIds) {
            menuBId.push(item.menuAndButtonIds);
        }
    });
    getInfosub(
        qs.stringify({
            menuButtonIdStr: menuBId.toString(), // 选中的id
            roleId: props.roleId,
        })
    ).then((res) => {
        message.success(res.data);
        emit('close');
    });
};

const dyhandleClose = () => {
    menu_btn.value = false;
};

const resetForm = () => {
    emit('close');
};

const onSelect = (selectedKeys: string[]) => {
    selectedRowKeys.value = selectedKeys;
};

const emit = defineEmits(['close']);

// 暴露给父组件的方法
defineExpose({
    submitBtn
});
</script>

<template>
    <!-- 表格区域 -->
    <a-table :data-source="tableData" style="width: 100%" :pagination="pagination" @row-click="rowClick" :customRow="customRow">
        <a-table-column key="name" data-index="name" title="菜单名称" :ellipsis="true"></a-table-column>
        <a-table-column key="buttonNames" data-index="buttonNames" title="关联按钮" :ellipsis="true"></a-table-column>
    </a-table>
    <a-modal
        title="设置按钮"
        v-model:visible="menu_btn"
        :before-close="dyhandleClose"
        :footer="null"
    >

    <a-checkbox-group v-model:value="checkedBtns">
        <div v-if="rolebtn[0].menuPath === 'erule/manage/engineering/query.do'">
            <div>
                <b>规则库权限</b><br/>
                <span v-for="item in rolebtn">
                <a-checkbox  v-if="item.name.indexOf('p') !== -1"  :key="item.id" :value="item">{{ item.name }}</a-checkbox>
            </span>
            </div>
            <div>
                <b>规则历史权限</b><br/>
                <span v-for="item in rolebtn">
                <a-checkbox  v-if="item.name.indexOf('c') !== -1"  :key="item.id" :value="item">{{ item.name }}</a-checkbox>
            </span>
            </div>
            <div>
                <b>规则权限</b><br/>
                <span v-for="item in rolebtn">
                <a-checkbox  v-if="item.name.indexOf('_') === -1"  :key="item.id" :value="item">{{ item.name }}</a-checkbox>
            </span>
            </div>
        </div>
        <div v-else>
            <a-checkbox v-for="item in rolebtn" :key="item.id" :value="item">{{ item.name }}</a-checkbox>
        </div>


    </a-checkbox-group>
    <div>
        <a-button type="primary" size="small" @click="menu_btn = false" style="margin-right: 8px;">取消</a-button>
        <a-button type="primary" size="small" @click="btnInfo" style="margin-right: 8px">确定</a-button>
        <a-button type="primary" size="small" @click="allchecked" style="margin-right: 8px">全选</a-button>
        <a-button type="primary" size="small" @click="checkReset">重置</a-button>
    </div>
    </a-modal>
</template>
