<!-- 规则通过率报表 -->
<script setup lang="ts">
    definePageMeta({
        title: '规则通过率报表'
    })

    const url = 'http://************:8083/report/view/autoDecision/query.do'
    const src = ref('');
    const unId = getUnid();
    onMounted(() => {
        if (src.value.indexOf('?') !==-1) {
            src.value = `${url}&id=${unId}`;
        } else {
            src.value = `${url}?id=${unId}`;
        }
    });

    const getIframeInfo = computed(() => {
        return window.innerHeight - 50 + 'px';
    });
</script>
<template>
    <a-card>
        <iframe
                ref="ifHeight"
                :src="src"
                frameborder="0"
                width="100%"
                :key="url"
                :style="{ height: getIframeInfo }"
        ></iframe>
    </a-card>
</template>


