import request from '@/utils/request'
//字典类型列表
export function dictionList(params) {
    return request({
      url: 'sys/dictionaryType/list',
      method: 'get',
      params, 
    })
  }
// 字典类型新增/修改
export function dictionsaveOrUpdate(data) {
  return request({
    url: 'sys/dictionaryType/saveOrUpdate',
    method: 'post',
    data, 
  })
}

//字典类型删除
export function dictionDel(data) {
    return request({
      url: 'sys/dictionaryType/delete',
      method: 'post',
      data,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
    })
  }

  // 字典类型/父字典类型下拉框
  export function dictionTypeSel() {
    return request({
      url: 'sys/dictionaryType/getAllDicType',
      method: 'get', 
    })
  }
  
// 获取字典类型更新文本框的值
export function getDictionType(params) {
  return request({
    url: 'sys/dictionaryType/getDicTypeByUuid',
    method: 'get', 
    params
  })
}