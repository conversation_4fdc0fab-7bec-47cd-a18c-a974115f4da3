<!-- 规则库/规则包内容组件 -->

<script setup>
import { createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import globalEventEmitter from '@/utils/eventBus';
import RuleAdd from "@/businessComponents/ruleBaseManage/RuleAdd";
import RuleUpdate from "@/businessComponents/ruleBaseManage/RuleUpdate";
import RuleCopy from "@/businessComponents/ruleBaseManage/RuleCopy";
import RuleModifyCopy from "@/businessComponents/ruleBaseManage/RuleModifyCopy";
import RuleHistory from "@/businessComponents/ruleBaseManage/RuleHistory";
import { batchExportRules } from '@/api/rule';
import { handleDownload } from '@/components/utils/exportUtil';
import {
    REFRESH_RULE_LIST,
    RULE_NAVIGATE_ACTION,
    RULE_NAVIGATION_COMPLETE,
    RULE_SUBMIT_ACTION,
    RULE_WITHDRAW_ACTION,
    RULE_UPDATE_ACTION,
    RULE_DELETE_ACTION,
    RULE_LOCK_ACTION,
    RULE_UNLOCK_ACTION,
    RULE_HISTORY_ACTION,
    RULE_UPDATED
} from '@/consts/globalEventConsts';
import {
    isLock,
    ruleDelete,
    toLock,
    unLock,
    submitRule,
    copyRuleListToFolder,
    getDicValueByTypeCode,
    getRuleList,
    treeList,
    getRuleByUuid,
    getRuleHistoryList,
    ruleCall,
    getEngineeringByUuid,
    list,
    ruleSaveOrUpdate
} from "@/api/rule_base";
import qs from 'qs'
import useTableConfig from '@/composables/useTableConfig';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { TAB_TYPE, TAB_PREFIX, TAB_TITLE_FORMAT, TAB_KEY_FORMAT } from "@/consts/ruleTabConsts";
//权限判断
import { checkPermi } from "@/directive/permission/permission";
import TableSkeleton from '@/components/TableSkeleton.vue';
import ExportConfigModal from '@/components/ExportConfigModal.vue';

const message = inject('message')
const modal = inject('modal')
//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);

const selectKeys = ref([]);//刷新列表后清空
const checkedUuids = ref([])
const form = reactive({
    name: undefined,
    rule_type: undefined,
    rule_state: undefined,
    rule_desc: undefined,
    textdtl: undefined,
    validState: undefined,
    createdId:undefined,
    ruleNumber:undefined,
});
const data = ref([])
const loading = ref(false);
const isRequestPending = ref(false); // 添加请求锁，防止多次重复请求
const ruleBaseId = ref('')
const rulePackageId = ref('')
const demandUuid = ref('')
const businessUuid = ref('')
const route = useRoute();
//返回页脚
const backPage = ref(1);
//tab选中主键
const tabName = ref('designView');
//工作空间判断逻辑，若为工作空间不可进行新增等操作
const selectFlag = ref(false);
//规则包名称
const rulePackageName = ref('');
const layoutKey = ref(Date.now()) // 添加一个随机key变量
const isDebouncing = ref(false); // 添加防抖标记
const sort=ref('')

const reset = () => {
    // 重置所有表单字段
    form.name = undefined;
    form.rule_type = undefined;
    form.rule_state = undefined;
    form.validState = undefined;
    form.rule_desc = undefined;
    form.textdtl = undefined;
    form.ruleNumber = undefined;
    form.createdId = undefined;

    // 重置分页到第一页
    if (pagination.value) {
        pagination.value.page = 1;
    }

    // 立即执行搜索，不管是否有请求正在进行
    searchRuleList(1, pagination.value?.limit || parseInt((scrollY.value - 30) / 40));
    return false; // 已经刷新，不需要外部再刷新
};

const rule_type_options = ref([]);
const rule_state_options = ref([]);
const validState_options = ref([]);
//使用tab显示数据
const ruleTabLists = ref([]);
const detailTabLists = ref([]);
//关闭全部按钮显示隐藏
const closeBtnFlag = ref(false);
const getDicValue = (type, fn) => {
    getDicValueByTypeCode({ typeCode: type }).then((res) => {
        fn && fn(res.data);
    });
};

// 初始化字典值
getDicValue('ruleValidStatus', (arr) => {
    validState_options.value = arr;
});
getDicValue('type_rule', (arr) => {
    rule_type_options.value = arr;
});
getDicValue('status_rule', (arr) => {
    rule_state_options.value = arr;
});

// 表格列配置 - 不包含序号列和操作列
const columnsWithoutIndexAction = [
    {
        title: '名称',
        dataIndex: 'ruleName',
        dataFlag: 'ruleName',
        sorter: true,
        key: 'ruleName',
        width: 200,
        fixed: 'left',
        align: 'left',
    },
    {
        title: '规则路径',
        dataIndex: 'packageNameAll',
        dataFlag: 'packageNameAll',
        key: 'rulePath',
        width: 280,
        align: 'left',
    },
    {
        title: '状态',
        dataIndex: 'status',
        dataFlag: 'status',
        key: 'status',
        align: 'center',
        width: 80,
    },
    {
        title: '是否有效',
        dataIndex: 'validStatus',
        dataFlag: 'validStatus',
        key: 'validStatus',
        width: 90,
        align: 'center',
    },
    {
        title: '类型',
        dataIndex: 'type',
        dataFlag: 'type',
        key: 'type',
        width: 100,
        align: 'center',
    },
    {
        title: '优先级',
        dataIndex: 'salience',
        dataFlag: 'salience',
        key: 'salience',
        width: 80,
        align: 'center',
    },
    {
        title: '更新时间',
        dataIndex: 'lastModifiedTimeStr',
        dataFlag: 'lastModifiedTime',
        exportKey: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        width: 180,
        align: 'left',
        sorter: {
            compare: (a, b) => Date.parse(a.lastModifiedTimeStr) - Date.parse(b.lastModifiedTimeStr),
        }
    },
    {
        title: '锁定用户',
        dataIndex: 'lockId',
        dataFlag: 'lockId',
        key: 'lockId',
        width: 100,
        align: 'center',
        checked: false
    },
    {
        title: '描述',
        dataIndex: 'descs',
        dataFlag: 'descs',
        key: 'descs',
        ellipsis: true,
        align: 'left',
        width: 200,
        checked: false
    },
    {
        title: '创建人',
        dataIndex: 'createdId',
        dataFlag: 'createdId',
        key: 'createdId',
        width: 100,
        align: 'center',
        checked: false
    },
    {
        title: '规则编号',
        dataIndex: 'ruleNumber',
        dataFlag: 'ruleNumber',
        key: 'ruleNumber',
        width: 100,
        align: 'center',
        checked: false
    },
    {
        title: '生效时间',
        dataIndex: 'effectDate',
        dataFlag: 'effectDate',
        key: 'effectDate',
        width: 100,
        align: 'center',
        checked: false
    },
    {
        title: '失效时间',
        dataIndex: 'expiredDate',
        dataFlag: 'expiredDate',
        key: 'expiredDate',
        width: 100,
        align: 'center',
        checked: false
    },
];

// 声明列显示和序号计算函数
const columns = ref([]);

// 声明tableColumnsConfig变量
let tableColumnsConfig;

// 初始化pagination并提供默认值
const pagination = ref({});

// 初始化表格列
function initColumns() {
    // 使用useTableConfig创建表格列配置
    tableColumnsConfig = useTableConfig(
        columnsWithoutIndexAction,
        handleColumnChange,
    );
    // 更新当前显示的列
    columns.value = tableColumnsConfig.columns;

    // 合并tableColumnsConfig中的pagination配置到我们的pagination
    if (tableColumnsConfig.pagination && tableColumnsConfig.pagination.value) {
        // 只合并值，保持响应式引用
        Object.assign(pagination.value, tableColumnsConfig.pagination.value);
    }
}

// 组件挂载后执行
onMounted(() => {
    initColumns();

    // 由路由监听器来统一管理数据加载

    globalEventEmitter.on(REFRESH_RULE_LIST, refreshRuleList);

    // 处理从URL打开特定规则标签页的情况
    const handleUrlParams = () => {
        // 检查URL是否有activeTab和ruleUuid参数
        if (route.query.activeTab && route.query.ruleUuid) {
            const activeTab = route.query.activeTab;
            const ruleUuid = route.query.ruleUuid;
            const tabType = route.query.tabType || 'edit'; // 默认为edit

            // 根据标签类型打开相应标签
            if (tabType === TAB_TYPE.EDIT) {
                // 检查当前是否已经打开了该标签
                const existingTabIndex = ruleTabLists.value.findIndex(tab => tab.uuid === ruleUuid);

                if (existingTabIndex === -1) {
                    // 如果标签不存在，则打开新标签页
                    addRuleTabs(ruleUuid);
                } else {
                    // 如果标签已存在，则激活该标签
                    tabName.value = TAB_KEY_FORMAT.EDIT(ruleUuid);
                }
            } else if (tabType === TAB_TYPE.DETAIL) {
                // 检查详情标签是否已打开
                const existingDetailIndex = detailTabLists.value.findIndex(tab => tab.uuid === ruleUuid);

                if (existingDetailIndex === -1) {
                    // 如果不存在，获取规则信息并打开详情标签
                    getRuleByUuid({
                        uuids: ruleUuid,
                    }).then(res => {
                        addDetailTabs(res.data);
                    });
                } else {
                    // 如果标签已存在，则激活该标签
                    tabName.value = TAB_KEY_FORMAT.DETAIL(ruleUuid);
                }
            }
        }
    };

    // 延迟执行URL参数处理，确保规则列表已加载
    setTimeout(() => {
        handleUrlParams();
    }, 500);

    // 监听提交规则事件 - 确保只绑定一次，使用防抖处理
    const debouncedHandleSubmitEvent = (data) => {
        if (isDebouncing.value) {
            console.log('[DEBUG] 事件被防抖机制阻止');
            return;
        }
        isDebouncing.value = true;
        setTimeout(() => {
            isDebouncing.value = false;
        }, 1000);

        if (!data || !data.record || !data.record.uuid) {
            console.warn('[DEBUG] 事件数据无效');
            return;
        }
        handleSubmitRule(data.record, data.tabKey);
    };

    // 先移除所有可能存在的监听器
    globalEventEmitter.off(RULE_SUBMIT_ACTION);
    // 添加新的监听器
    globalEventEmitter.on(RULE_SUBMIT_ACTION, debouncedHandleSubmitEvent);

    // 监听规则撤回事件
    globalEventEmitter.on(RULE_WITHDRAW_ACTION, (data) => {
        if (data && data.record) {
            handleRuleCall(data.record);
        }
    });

    // 监听规则更新属性事件
    globalEventEmitter.on(RULE_UPDATE_ACTION, (data) => {
        if (data && data.record) {
            showModal('update', data.record);
        }
    });

    // 监听规则删除事件
    globalEventEmitter.on(RULE_DELETE_ACTION, (data) => {
        if (data && data.record) {
            handleDelete(data.record, data.tabKey);
        }
    });

    // 监听规则锁定事件
    globalEventEmitter.on(RULE_LOCK_ACTION, (data) => {
        if (data && data.record) {
            handleLock(data.record);
        };
    });

    // 监听规则解锁事件
    globalEventEmitter.on(RULE_UNLOCK_ACTION, (data) => {
        if (data && data.record) {
            handleUnLock(data.record);
        };
    });

    // 监听历史版本事件
    globalEventEmitter.on(RULE_HISTORY_ACTION, (data) => {
        if (data && data.record) {
            handleHistory(data.record);
        }
    });

    // 监听规则导航事件（上一个/下一个）
    globalEventEmitter.on(RULE_NAVIGATE_ACTION, (data) => {
        if (data && data.uuid && data.direction) {
            handleRuleNavigation(data.uuid, data.direction);
        }
    });

    // 将实例方法绑定到DOM元素，便于外部组件访问
    const componentElement = document.getElementById('rule-base-content-component');
    if (componentElement) {
        // 将方法直接绑定到DOM元素，使其可被外部访问
        componentElement.__ruleBaseInstance = {
            checkRulePosition
        };
    }

    // 更新layoutKey强制重新渲染YunXiaoListPageLayout组件
    layoutKey.value = Date.now()
})

// 监听 rule_type_options 的变化，如果包含数据，
// 且路由参数中包含 openCreateDialog=true，则显示新建规则对话框
watch(rule_type_options, (newVal) => {
    if (newVal.length) {
        // 如果路由参数中包含 openCreateDialog=true，则显示新建规则对话框
        if (route.query.openCreateDialog === 'true') {
            showModal('add')

            const ruleType = route.query.ruleType

            // 如果路由参数中包含 ruleType，则设置 ruleType
            if (ruleType) {
                // 延迟执行，以确保 ruleAddComponent 已经挂载
                nextTick(() => {
                    ruleAddComponent.value.form.ruleType = ruleType
                    //新建规则为规则流时，优先级变为规则流类型
                    if (ruleType === '5') {
                        ruleAddComponent.value.ruleTypeChange(ruleType);
                    }
                })
            }
        }
    }
})

// 组件卸载时移除事件监听
onUnmounted(() => {
    globalEventEmitter.off(REFRESH_RULE_LIST, refreshRuleList);
    globalEventEmitter.off(RULE_WITHDRAW_ACTION);
    globalEventEmitter.off(RULE_UPDATE_ACTION);
    globalEventEmitter.off(RULE_DELETE_ACTION);
    globalEventEmitter.off(RULE_LOCK_ACTION);
    globalEventEmitter.off(RULE_UNLOCK_ACTION);
    globalEventEmitter.off(RULE_HISTORY_ACTION);
    globalEventEmitter.off(RULE_NAVIGATE_ACTION);
    globalEventEmitter.off(RULE_SUBMIT_ACTION, debouncedHandleSubmitEvent);

    // 清除当前规则库相关的localStorage数据
    const ruleBasePrefix = ruleBaseId.value ? `${ruleBaseId.value}_` : '';
    localStorage.removeItem(`${ruleBasePrefix}ruleTabLists`);
    localStorage.removeItem(`${ruleBasePrefix}detailTabLists`);
})
const engUuid = ref('');

// 定义一个加载标志，用于跟踪数据加载状态
const isLoading = ref(false);

// 简化的searchRuleList函数，不使用setTimeout和防抖
async function searchRuleList(page = 1, pageSize = parseInt((scrollY.value - 30) / 40)) {
    // 如果已经在加载数据，则不要重复请求
    if (isLoading.value) {
        return;
    }

    isLoading.value = true;
    loading.value = true;

    //清空列表多选选中数据
    selectKeys.value = [];
    checkedUuids.value = [];

    let folderUuid = '';
    let engUuidData = '';

    //规则浏览/规则调整工作空间附值
    if (ruleBaseId.value.indexOf('demandUuid') !== -1 && !route.query.ruleBaseId && !rulePackageId.value) {
        try {
            let res = await list({
                businessLine: ruleBaseId.value.split('demandUuid-')[1].split('businessUuid-')[1],
                page: 1,
                several: 10000,
            });
            let engUuid1 = [];
            res.data.data.forEach((item) => {
                engUuid1.push(item.uuid.split(","));
            });
            engUuidData = engUuid1.toString();
            selectFlag.value = false;
        } catch (error) {
            console.error('获取列表失败', error);
            message.error('获取规则列表失败');
            isLoading.value = false;
            loading.value = false;
            return;
        }
    } else if (ruleBaseId.value === 'all' || ruleBaseId.value.indexOf('demandUuid') !== -1) {
        selectFlag.value = true;
        engUuidData = route.query.ruleBaseId;
    } else {
        selectFlag.value = true;
        engUuidData = ruleBaseId.value;
    }

    if (rulePackageId.value === 'all') {
        folderUuid = '';
    } else {
        folderUuid = rulePackageId.value;
    }

    if (pageSize <= 0) {
        isLoading.value = false;
        loading.value = false;
        return;
    }

    try {
        const res = await getRuleList({
            engUuid: engUuidData,
            folderUuid: folderUuid,
            page: page,
            several: pageSize,
            ruleName: form.name,
            type: form.rule_type,
            status: form.rule_state,
            validStatus: form.validState,
            descs: form.rule_desc,
            ruleNumber:form.ruleNumber,
            createdId:form.createdId,
            textDtl: form.textdtl,
            sortColumn: flag,
            sortOrder: sort.value
        });

        data.value = res.data.data;
        pagination.value.total = res.data.totalCount;
        pagination.value.page = page;
        pagination.value.limit = pageSize;
        engUuid.value = engUuidData;
    } catch (error) {
        console.error('获取规则列表失败', error);
        message.error('获取规则列表失败');
    } finally {
        isLoading.value = false;
        loading.value = false;
    }
}

// 简化的refreshRuleList函数
function refreshRuleList() {
    // 如果已经在加载数据，不要重复请求
    if (isLoading.value) {
        return;
    }
    // 刷新时使用当前页和每页数量
    if (pagination.value && pagination.value.page && pagination.value.limit) {
        searchRuleList(pagination.value.page, pagination.value.limit);
    } else {
        searchRuleList();
    }
}

// 优化路由监听，通过单一的路由监听器来管理数据加载
watch(
    () => ({
        ruleBaseId: route.params.ruleBaseId || route.query.ruleBaseId,
        rulePackageId: route.params.rulePackageId || route.query.rulePackageId,
        backPage: route.query.backPage,
        rulePackageName: route.query.rulePackageName,
        ruleAddUuid: route.query.ruleAddUuid
    }),
    (newRouteParams, oldRouteParams) => {
        // 从路由参数中获取ruleBaseId和rulePackageId
        const routeRuleBaseId = route.params.ruleBaseId || route.query.ruleBaseId;
        const routeRulePackageId = route.params.rulePackageId || route.query.rulePackageId;

        // 如果有规则库ID，则更新相关状态
        if (routeRuleBaseId) {
            // 检查是否包含demandUuid信息
            if (typeof routeRuleBaseId === 'string' && routeRuleBaseId.indexOf('demandUuid') !== -1) {
                try {
                    demandUuid.value = routeRuleBaseId.split('demandUuid-')[1].split('-businessUuid-')[0];
                    businessUuid.value = routeRuleBaseId.split('demandUuid-')[1].split('businessUuid-')[1];
                } catch (e) {
                    console.error('解析demandUuid失败', e);
                }
            } else {
                // 直接从query参数获取
                demandUuid.value = route.query.demandUuid || '';
                businessUuid.value = route.query.businessUuid || '';
            }

            // 更新ruleBaseId
            ruleBaseId.value = routeRuleBaseId;
        }

        // 更新rulePackageId
        if (routeRulePackageId) {
            rulePackageId.value = routeRulePackageId;
        }else {
            rulePackageId.value = '';
        }

        // 处理其他路由参数
        if (route.query.backPage) {
            backPage.value = route.query.backPage;
        }

        if (route.query.rulePackageName) {
            rulePackageName.value = route.query.rulePackageName;
        }

        // 监听左侧树是否新增规则，是则调用添加tab方法
        if (route.query.ruleAddUuid) {
            addRuleTabs(route.query.ruleAddUuid);
        }

        // 处理需要重新加载列表的情况
        const needReload = () => {
            // 初始加载时oldRouteParams可能为空，此时应该加载数据
            const isInitialLoad = !oldRouteParams || Object.keys(oldRouteParams).length === 0;

            // 判断规则库ID或规则包ID是否发生变化
            const ruleBaseIdChanged = newRouteParams.ruleBaseId !== oldRouteParams?.ruleBaseId;
            const rulePackageIdChanged = newRouteParams.rulePackageId !== oldRouteParams?.rulePackageId;

            // 检查当前是否有加载进行中
            const isCurrentlyLoading = isLoading.value;

            if (isInitialLoad) {
                return true;
            }

            if (ruleBaseIdChanged || rulePackageIdChanged) {
                return !isCurrentlyLoading;
            }

            return false;
        };

        // 只有在真正需要时才加载数据
        if (needReload()) {

            // 处理标签页状态
            ruleTabLists.value = [];
            detailTabLists.value = [];

            // 使用规则库ID作为前缀
            const ruleBasePrefix = ruleBaseId.value ? `${ruleBaseId.value}_` : '';

            // 使用带前缀的key来获取localStorage中的数据
            if (localStorage.getItem(`${ruleBasePrefix}ruleTabLists`))
                objToArr(ruleTabLists.value, qs.parse(localStorage.getItem(`${ruleBasePrefix}ruleTabLists`)));

            if (localStorage.getItem(`${ruleBasePrefix}detailTabLists`))
                objToArr(detailTabLists.value, qs.parse(localStorage.getItem(`${ruleBasePrefix}detailTabLists`)));

            if (localStorage.getItem(`${ruleBasePrefix}ruleTabLists`) || localStorage.getItem(`${ruleBasePrefix}detailTabLists`))
                closeBtnFlag.value = true;

            tabName.value = "designView";

            // 加载数据 - 只调用一次searchRuleList
            searchRuleList();
        }

        // 更新layoutKey强制重新渲染YunXiaoListPageLayout组件
        layoutKey.value = Date.now();
    },
    {
        immediate: true,
        deep: true
    }
);

// 需要排序某个字段
var flag = '';
function handleTableChange(paginations, filters, sorter) {
    if (!tableColumnsConfig || !tableColumnsConfig.pagination || !tableColumnsConfig.pagination.value) {
        return; // 确保pagination已初始化
    }
    if (sorter === undefined || sorter.columnKey === undefined) {
        flag = 'lastModifiedTime';
        sort.value = 'desc';
    } else {
        flag = sorter.columnKey;
        if (sorter.columnKey === 'lastModifiedTimeStr') {
            flag = 'lastModifiedTime'
        } else {
            flag = sorter.columnKey;
        }
        if (sort.value === 'asc') {
            sort.value = 'desc'
        } else {
            sort.value = 'asc'
        }
    }
    searchRuleList(pagination.value.page, pagination.value.limit);
}
const handlePageChange = (cur, pageSize) => {
    if (!tableColumnsConfig || !tableColumnsConfig.pagination || !tableColumnsConfig.pagination.value) {
        return; // 确保pagination已初始化
    }

    pagination.value.page = cur;
    pagination.value.limit = pageSize;
    searchRuleList(pagination.value.page, pagination.value.limit);
};
const isModalVisible = ref(false); // 对话框显示状态
const modalType = ref(''); // 对话框类型：'add' 或 'update'

const ruleId = ref('');

// 规则新增组件
const ruleAddComponent = ref()

// 规则更新组件
const ruleUpdateComponent = ref()

// 规则复制组件
const ruleCopyComponent = ref()

// 规则名修改并复制组件
const ruleModifyCopyComponent = ref()
// 历史查看组件
const ruleHistoryComponent = ref()
// 检查选中记录条数
const checkSelectedRow = (single) => {
    if (checkedUuids.value.length) {
        if (single) {
            if (checkedUuids.value.length > 1) {
                message.error("只能选择一条记录");
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    } else {
        message.error("请选择要操作的记录");
        return false;
    }
}
const showModal = (type, record = {}) => {
    modalType.value = type;
    if (type === 'update') {
        isLock(
            qs.stringify({
                uuids: record.uuid,
                demandUuid: demandUuid.value,
            })
        ).then((res) => {
            if (res.code === 20000) {
                ruleId.value = record.uuid;
                if (ruleUpdateComponent.value) {
                    ruleUpdateComponent.value.getRuleInfo(ruleId.value)
                }
                isModalVisible.value = true;
            } else {
                message.error(res.data);
            }
        });

    } else if (type === 'copy') {
        if (record.uuid) {
            // 如果有 record，直接使用 record 的值
            let _uuids = [record.uuid];
            getRuleByUuid({
                uuids: _uuids.toString(),
                demandUuid: demandUuid.value
            }).then((res) => {
                getEngineeringByUuid({
                    uuid: engUuid.value,
                }).then((res1) => {
                    copyOperPage(res1.data.businessLine, res.data, record);
                })
            })
        } else if (checkSelectedRow(false)) {
            let _uuids = [];
            checkedUuids.value.filter((i) => {
                _uuids.push(i.uuid);
            });
            var flag = true;
            if (checkedUuids.value.length > 1) {
                // 多选，判断是否是同一个规则库
                checkedUuids.value.filter((i) => {
                    if (i.engUuid != engUuid.value) {
                        flag = false;
                    }
                });
            }
            if (flag) {
                getRuleByUuid({
                    uuids: _uuids.toString(),
                    demandUuid: demandUuid.value
                }).then((res) => {
                    getEngineeringByUuid({
                        uuid: engUuid.value,
                    }).then((res1) => {
                        copyOperPage(res1.data.businessLine, res.data);
                    })
                })
            } else {
                message.warning('批量复制请选择同一规则库');
            }
        }
    } else if (type === 'ruleHistory') {
        isFullModalVisible.value = true;
        titleText.value = '查看历史'
    } else {
        isModalVisible.value = true;
    }
};
const dialogFormVisible = ref(false);
const setTreeList = (params) => {
    let startIndex = 0;
    _setAttirbute(params);
    function _setAttirbute(item) {
        item.filter((v) => {
            v.key = startIndex;
            startIndex += 1;
            if (v.children && v.children.length) {
                _setAttirbute(v.children);
            }
        });
    }
    return params;
};
const datar = ref(null);
function copyOperPage(copyBusinessLine, data, record = null) {
    if (record) {
        // 如果是单个记录的情况
        ruleId.value = record.uuid;
        datar.value = data;
        datar.value.businessLine = copyBusinessLine;
        if (ruleCopyComponent.value) {
            ruleCopyComponent.value.getRuleInfo(ruleId.value)
        }
        isModalVisible.value = true;
    } else if (checkedUuids.value.length === 1) {
        // 如果是单个选中记录的情况
        ruleId.value = checkedUuids.value[0].uuid;
        datar.value = data;
        datar.value.businessLine = copyBusinessLine;
        if (ruleCopyComponent.value) {
            ruleCopyComponent.value.getRuleInfo(ruleId.value)
        }
        isModalVisible.value = true;
    } else {
        // 如果是多个记录的情况
        treeList({
            businessLine: copyBusinessLine
        }).then((res) => {
            function formatData(data1) {
                data1.forEach(res => {
                    res.title = res.folderName;
                    res.value = res.key;
                    if (res.children) {
                        formatData(res.children)
                    }
                })
            }
            let data = setTreeList(res.data);
            formatData(data);
            ruleTreeOptions.value = data;
            const packageNameArr = checkedUuids.value[0].packageNameAll.split(".");
            selectFolderName.value = packageNameArr[packageNameArr.length - 1];


            // 初始化 clickTreeData
            if (!checkedUuids.value[0].folderUuid) {
                //folderUuid为空时取engUuid，处理根节点选择情况
                clickTreeData.value = { 'engUuid': checkedUuids.value[0].engUuid };
            } else {
                function _setDefauktKey(arr, fn) {
                    arr.filter((v) => {
                        fn(v);
                        if (v.children && v.children.length) {
                            _setDefauktKey(v.children, fn);
                        }
                    });
                }
                _setDefauktKey(res.data, (v) => {
                    v.uuid === checkedUuids.value[0].folderUuid && (clickTreeData.value = v);
                })
            }
            dialogFormVisible.value = true;
        });
    }
}
const clickTreeData = ref(null)
const ruleTreeOptions = ref([])
const selectFolderName = ref('')
function copyDetermine() {
    let _uuids = [];
    checkedUuids.value.filter((i) => {
        _uuids.push(i.uuid);
    });
    /*if (clickTreeData.value.uuid) {*/
    //规则包id赋值，防止空参判断
    let clickTreeDataUuid = clickTreeData.value.uuid ? clickTreeData.value.uuid : '';
    dialogFormVisible.value = false
    showGlobalLoading('批量复制中，请耐心等待');
    copyRuleListToFolder(
        qs.stringify({
            folderUuid: clickTreeDataUuid,
            newEngUuid: clickTreeData.value.engUuid,
            uuids: _uuids.toString(),
        })
    ).then((res) => {
        if (res.code === 20000) {
            if (res.data === "复制成功！") {
                hideGlobalLoading()
                cancelCopy();
                message.success(res.data)
                searchRuleList()
            } else {
                hideGlobalLoading()
                modal.confirm({
                    title: () => "提示",
                    content: () => "部分规则名称已存在，点击确定进行修改",
                    okText: () => '确定',
                    okType: 'warning',
                    cancelText: () => '取消',
                    onOk() {
                        hideGlobalLoading()
                        const _parmData = {
                            folderUuid: clickTreeDataUuid,
                            newEngUuid: clickTreeData.value.engUuid,
                            uuids: res.data,
                        };
                        datar.value = _parmData;
                        showModal('ruleModifyCopy')
                    },
                    onCancel() {
                        hideGlobalLoading()
                        cancelCopy();
                    }
                })
            }
        } else {
            hideGlobalLoading()
            message.error(res.data);
        }
    }).catch(()=>{
        hideGlobalLoading();
    });
    /*} else {
        message.error("请选择规则包");
    }*/
}
const handleCancel = () => {
    isModalVisible.value = false;
};
const router = useRouter();
// 处理modal ok 事件
const handleSubmit = () => {
    let submitFun;
    switch (modalType.value) {
        case 'add':
            submitFun = ruleAddComponent.value?.handleSubmit;
            break;
        case 'update':
            submitFun = ruleUpdateComponent.value?.handleSubmit;
            break;
        case 'copy':
            isModalVisible.value = false;
            submitFun = ruleCopyComponent.value?.handleSubmit(()=>{
                searchRuleList(pagination.value.page, pagination.value.limit)
            });
            break;
        case 'ruleModifyCopy':
            isModalVisible.value = false;
            submitFun = ruleModifyCopyComponent.value?.handleSubmit;
            break;
    }
    submitFun && submitFun((res) => {
        searchRuleList(pagination.value?.page || 1, pagination.value?.limit || parseInt((scrollY.value - 30) / 40))
        isModalVisible.value = false;
        if (modalType.value === 'add') {
            addRuleTabs(res)
            // 将滚动条滚动到底部
            nextTick(() => {
                let dataDom = document.querySelector('.ant-table-body');
                dataDom && (dataDom.scrollTop = dataDom.scrollHeight);
            });
        } else if (modalType.value === 'ruleModifyCopy') {
            dialogFormVisible.value = false;
        }

        // 如果是更新操作，更新标签页标题
        if (modalType.value === 'update' && ruleId.value) {
            // 获取最新的规则信息
            getRuleByUuid({
                uuids: ruleId.value,
            }).then(response => {
                if (response && response.data) {
                    const updatedRule = response.data;

                    // 更新编辑标签页的标题
                    const updatedTabIndex = ruleTabLists.value.findIndex(tab => tab.uuid === ruleId.value);
                    if (updatedTabIndex !== -1) {
                        // 更新标签页标题
                        ruleTabLists.value[updatedTabIndex].ruleTitle = TAB_TITLE_FORMAT.EDIT(updatedRule.ruleName);
                        ruleTabLists.value[updatedTabIndex].ruleName = updatedRule.ruleName;

                        // 发送事件通知ruleTabEdit组件更新规则信息
                        globalEventEmitter.emit(RULE_UPDATED, {
                            uuid: ruleId.value,
                            ruleName: updatedRule.ruleName,
                            // 可以根据需要添加更多需要更新的信息
                        });
                    }
                }
            }).catch(() => {
                message.error('获取更新后的规则信息失败');
            });
        }
    })
};

/**
 * 删除规则
 * @param record
 */
function handleDelete(record, tabKey) {
    let uuids = '';
    let ruleNames = '';
    //判断数据是否存在
    let delFlag = false;
    if (record.uuid) {
        delFlag = true;
        uuids = record.uuid;
        ruleNames = record.ruleName;
    } else {
        if (checkSelectedRow(false)) {
            delFlag = true;
            let _uuids = [];
            let _ruleNames = [];
            checkedUuids.value.filter((i) => {
                _uuids.push(i.uuid);
                _ruleNames.push(i.ruleName + '\n')
            });
            uuids = _uuids.toString();
            ruleNames = _ruleNames.toString();
        }
    }

    if (delFlag) {
        modal.confirm({
            title: '确定要删除【' + ruleNames + '】吗？',
            icon: createVNode(ExclamationCircleOutlined),
            onOk() {
                showGlobalLoading('删除中，请耐心等待');
                ruleDelete(
                    qs.stringify({
                        uuids: uuids,
                        demandUuid: demandUuid.value,
                    })
                ).then((res) => {
                    if (res.code === 20000) {
                        hideGlobalLoading();
                        message.success('删除成功')
                        searchRuleList(pagination.value.page, pagination.value.limit)
                        // 如果有标签页标识，则关闭对应的标签页
                        if (tabKey) {
                            onEdit(tabKey);
                        }
                    } else {
                        hideGlobalLoading();
                        message.error(res.data);
                    }
                }).catch(()=>{
                    hideGlobalLoading();
                });
            },
            onCancel() { }
        })
    }
}
// 提交数据前状态判断
function checkSubmitStatus(record, type) {
    let uuids = '';
    //状态判断
    let status = '';
    if (record.uuid) {
        uuids = record.uuid;
        if (record.status === '已删除') {
            status = record.status;
        }
        //锁定/解锁数据不可再次提交
        if ((record.lockId && type === '已锁定') || (!record.lockId && type === '已解锁')) {
            status = type;
        }
    } else {
        if (checkSelectedRow(false)) {
            let _uuids = [];
            checkedUuids.value.filter((i) => {
                _uuids.push(i.uuid);
                if (i.status === '已删除') {
                    status = i.status;
                }
                //锁定/解锁数据不可再次提交
                if ((i.lockId && type === '已锁定') || (!i.lockId && type === '已解锁')) {
                    status = type;
                }
            });
            uuids = _uuids.toString();
        }
    }
    return [uuids, status];
}
//锁定
function handleLock(record) {
    let [uuids, status] = checkSubmitStatus(record, '已锁定');
    if (status) {
        message.error('状态为' + status + '规则不可锁定');
        return;
    }
    if (uuids) {
        showGlobalLoading('锁定中，请耐心等待');
        toLock(
            qs.stringify({
                uuids: uuids,
            })
        ).then((res) => {
            if (res.code === 20000) {
                hideGlobalLoading();
                message.success(res.data)
                searchRuleList(pagination.value.page, pagination.value.limit)
            } else {
                hideGlobalLoading();
                message.error(res.data);
            }
        }).catch(()=>{
            hideGlobalLoading();
        });
    }
};
//解锁
function handleUnLock(record) {
    let [uuids, status] = checkSubmitStatus(record, '已解锁');
    if (status) {
        message.error('状态为' + status + '规则不可解锁');
        return;
    }
    if (uuids) {
        showGlobalLoading('解锁中，请耐心等待');
        unLock(
            qs.stringify({
                uuids: uuids,
            })
        ).then((res) => {
            if (res.code === 20000) {
                hideGlobalLoading();
                message.success(res.data)
                searchRuleList(pagination.value.page, pagination.value.limit)
            } else {
                hideGlobalLoading();
                message.error(res.data);
            }
        }).catch(()=>{
            hideGlobalLoading();
        });
    }

};
const ruleHistoryDrawerVisible = ref();
const closeDrawer = () => {
    ruleHistoryDrawerVisible.value = false;
}
//历史
function handleHistory(record) {
    showGlobalLoading('加载中，请耐心等待');
    getRuleHistoryList({
        ruleUuid: record.uuid,
        page: 1,
        several: 10000,
    }).then((res) => {
        if (res.code == 20000) {
            ruleId.value = record.uuid;
            datar.value = res.data;
            hideGlobalLoading();
            ruleHistoryDrawerVisible.value = true;
        } else {
            hideGlobalLoading();
            message.error(res.data);
        }
    }).catch(()=>{
        hideGlobalLoading();
    });
}
// 添加防抖标记
const isSubmitting = ref(false);

// 提交
function handleSubmitRule(record, tabKey) {
    // 防抖处理：如果正在提交中，直接返回
    if (isSubmitting.value) {
        message.warning('请勿频繁提交，请稍后再试');
        return;
    }

    let [uuids, status] = checkSubmitStatus(record, '');
    if (status) {
        message.error('状态为' + status + '规则不可提交');
        return;
    }
    if (uuids) {
        // 设置提交中状态
        isSubmitting.value = true;
        showGlobalLoading('提交中，请耐心等待');
        let pars = qs.stringify({
            uuids: uuids,
        })
        if (ruleBaseId.value.indexOf('demandUuid') !== -1) {
            pars = qs.stringify({
                uuids: uuids,
                demandUuid: demandUuid.value
            })
        }
        submitRule(pars).then((res) => {
            if (res.code === 20000) {
                hideGlobalLoading();
                if (res.data == '规则置为提交待审核成功！') {
                    message.success("规则置为提交待审核成功后，不允许操作，关闭页面！")
                    if (tabKey) {
                        onEdit(tabKey);
                    }
                } else {
                    message.success(res.data)
                }
                searchRuleList(pagination.value.page, pagination.value.limit)
            } else {
                hideGlobalLoading();
                message.error(res.data);
            }
        }).catch(()=>{
            hideGlobalLoading();
        }).finally(() => {
            // 1秒后重置提交状态
            setTimeout(() => {
                isSubmitting.value = false;
            }, 1000);
        });
    }
};
//撤回
function handleRuleCall(record) {
    let [uuids, status] = checkSubmitStatus(record, '');
    if (status) {
        message.error('状态为' + status + '规则不可撤回');
        return;
    }
    if (uuids) {
        showGlobalLoading('撤回中，请耐心等待');
        ruleCall({
            uuids: uuids
        }).then((res) => {
            if (res.code === 20000) {
                hideGlobalLoading();
                message.success(res.data)
                searchRuleList(pagination.value.page, pagination.value.limit)
            } else {
                hideGlobalLoading();
                message.error(res.data);
            }
        }).catch(()=>{
            hideGlobalLoading();
        });
    }

};

const rowSelection = {
    selectedRowKeys: selectKeys,
    onChange: (selectedRowKeys, selectedRows) => {
        selectKeys.value = selectedRowKeys;
        checkedUuids.value = [];
        selectedRows.forEach((item) => {
            checkedUuids.value.push({ 'uuid': item.uuid, 'engUuid': item.engUuid, 'ruleName': item.ruleName, 'status': item.status, 'lockId': item.lockId, 'packageNameAll': item.packageNameAll, 'folderUuid': item.folderUuid })
        });
    }
};
const defaultKey = ref([])
const rules = ref({
    ruleTrees: [{ required: true, message: "不能为空", trigger: "blur" }],
})
const copyForm = reactive({
    ruleTrees: undefined
})
const ruleNodeClick = (value, node) => {
    selectFolderName.value = node.folderName;
    clickTreeData.value = node;
    copyForm.ruleTrees = node.folderName;
}
const cancelCopy = () => {
    dialogFormVisible.value = false;
    selectFolderName.value = '';
    clickTreeData.value = null;
    copyForm.ruleTrees = undefined;
}
//返回任务
const toTask = () => {
    navigateTo('/ruleAdjustment?backPage=' + backPage.value)
}
//全屏切换
const isFullscreen = ref(false)
const isFullModalVisible = ref(false)
const titleText = ref('')
const onFullscreenToggle = () => {
    isFullscreen.value = !isFullscreen.value
}
const handleFullCancel = () => {
    isFullModalVisible.value = false;
}





//数组去重
const distinct = (arr) => {
    for (let i = 0; i < arr.length; i++) {
        for (let j = i + 1; j < arr.length; j++) {
            if (arr[i].uuid === arr[j].uuid) {
                arr.splice(j, 1);
                j--;
            }
        }
    }
}
const goRule = (type, uuid, engUuid, folderUuid, packageNameAll, ruleName, ifCollect, status) => {
    const oType = rule_type_options.value.find(item => item.name === type)
    const oState = rule_state_options.value.find(item => item.name === status)
    ruleTabLists.value.push({
        type: oType?.code || '',
        uuid,
        engUuid,
        demandUuid: demandUuid.value,
        folderUuid,
        packageName: packageNameAll,
        ruleTitle: TAB_TITLE_FORMAT.EDIT(ruleName),
        ruleName,
        ifCollect,
        ruleId: uuid,
        status: oState?.code || ''
    })
    distinct(ruleTabLists.value);
    tabName.value = TAB_KEY_FORMAT.EDIT(uuid);
    closeBtnFlag.value = true;
    setTabList();
}
const openRule = (record) => {
    const { lockStatus, type, uuid, engUuid, folderUuid, packageNameAll, ruleName, ifCollect, status } = record
        isLock(
            qs.stringify({
                uuids: uuid,
                demandUuid: demandUuid.value,
            })
        ).then((res) => {
            if (res.code === 20000) {
                goRule(type, uuid, engUuid, folderUuid, packageNameAll, ruleName, ifCollect, status)
            } else {
                message.error(res.data);
            }
        })
}
const addRuleTabs = (uuid) => {
    //判断当前规则是否可编辑
    isLock(
        qs.stringify({
            uuids: uuid,
            demandUuid: demandUuid.value,
        })
    ).then((res) => {
        if (res.code === 20000) {
            getRuleByUuid({
                uuids: uuid,
            }).then((res) => {
                ruleTabLists.value.push({
                    type: res.data.type,
                    uuid: res.data.uuid,
                    engUuid: res.data.engUuid,
                    demandUuid: demandUuid.value,
                    folderUuid: res.data.folderUuid,
                    packageName: res.data.packageNameAll,
                    ruleTitle: TAB_TITLE_FORMAT.EDIT(res.data.ruleName),
                    ruleName: res.data.ruleName,
                    ifCollect: res.data.ifCollect,
                    ruleId: uuid
                })
                distinct(ruleTabLists.value);
                tabName.value = TAB_KEY_FORMAT.EDIT(res.data.uuid);
                closeBtnFlag.value = true;
                setTabList();
            });
        } else {
            message.error(res.data);
        }
    })
}
// 获取规则信息
function addDetailTabs(record) {
    getRuleByUuid({
        uuids: record.uuid,
    }).then(res => {
        detailTabLists.value.push(res.data);
        distinct(detailTabLists.value);
        tabName.value = TAB_KEY_FORMAT.DETAIL(res.data.uuid);
    })
    closeBtnFlag.value = true;
    setTabList();
}

// 关闭全部
const clearTabs = () => {
    ruleTabLists.value = [];
    detailTabLists.value = [];
    tabName.value = "designView";
    closeBtnFlag.value = false;
    setTabList();
}
const onEdit = (targetKey) => {
    // 判断是否是designView，如果是则不做处理
    if (targetKey === 'designView') {
        return;
    }

    // 调用remove函数处理标签页移除，确保同时清理URL参数
    remove(targetKey);

    // 查找一个可选的标签页来激活
    // 首先尝试显示规则列表
    if (tabName.value === targetKey) {
        let newActiveKey = 'designView';

        // 如果还有其他标签页，激活最后一个标签页
        if (ruleTabLists.value.length > 0) {
            const lastRuleTab = ruleTabLists.value[ruleTabLists.value.length - 1];
            newActiveKey = TAB_KEY_FORMAT.EDIT(lastRuleTab.uuid);
        } else if (detailTabLists.value.length > 0) {
            const lastDetailTab = detailTabLists.value[detailTabLists.value.length - 1];
            newActiveKey = TAB_KEY_FORMAT.DETAIL(lastDetailTab.uuid);
        }

        tabName.value = newActiveKey;
    }
};
const remove = (targetKey) => {
    // 提取当前标签页的UUID
    let uuid = '';
    let tabType = '';

    // 判断是编辑还是详情标签
    if (targetKey.startsWith(TAB_PREFIX.EDIT)) {
        uuid = targetKey.replace(TAB_PREFIX.EDIT, '');
        tabType = TAB_TYPE.EDIT;
    } else if (targetKey.startsWith(TAB_PREFIX.DETAIL)) {
        uuid = targetKey.replace(TAB_PREFIX.DETAIL, '');
        tabType = TAB_TYPE.DETAIL;
    }

    // 从列表中移除标签页
    ruleTabLists.value = ruleTabLists.value.filter(pane => TAB_KEY_FORMAT.EDIT(pane.uuid) !== targetKey);
    detailTabLists.value = detailTabLists.value.filter(pane => TAB_KEY_FORMAT.DETAIL(pane.uuid) !== targetKey);

    // 检查是否需要清理URL参数
    const currentUrlUuid = route.query.ruleUuid;
    const currentTabType = route.query.tabType;

    // 如果URL中的参数与当前关闭的标签页相关，则清理参数
    if (currentUrlUuid === uuid && (!currentTabType || currentTabType === tabType)) {
        // 创建一个新的query对象，不包含与标签页相关的参数
        const newQuery = { ...route.query };
        delete newQuery.ruleUuid;
        delete newQuery.activeTab;
        delete newQuery.tabType;

        // 更新路由，但不触发新的导航
        router.replace({ query: newQuery });
    }

    // 如果没有标签页了，重置标志位
    if (ruleTabLists.value.length === 0 && detailTabLists.value.length === 0) {
        closeBtnFlag.value = false;
    }

    // 恢复代码行：重置标签页为规则列表，并更新标签列表
    tabName.value = 'designView';
    setTabList();
};
const setTabList = () => {
    // 从 route 中获取规则库 id 作为前缀
    const ruleBasePrefix = ruleBaseId.value ? `${ruleBaseId.value}_` : '';
    localStorage.setItem(`${ruleBasePrefix}ruleTabLists`, qs.stringify(ruleTabLists.value))
    localStorage.setItem(`${ruleBasePrefix}detailTabLists`, qs.stringify(detailTabLists.value))
}

const searchFlag = ref(true)

const handleSearchFlag = (val) => {
    searchFlag.value = val
}

const filterColumn = ref(columns)
const changeTableList = (val) => {
    filterColumn.value = val
}

// 定义云效列表页所需配置
const searchConfig = computed(() => ({
    simpleSearchField: {
        label: '规则名称',
        field: 'name'
    },
    advancedSearchFields: [
        {
            label: '规则类型',
            field: 'rule_type',
            compType: 'select',
            compConfig: {
                options: rule_type_options.value.map(item => ({
                    name: item.name,
                    value: item.code
                }))
            }
        },
        {
            label: '规则状态',
            field: 'rule_state',
            compType: 'select',
            compConfig: {
                options: rule_state_options.value.map(item => ({
                    name: item.name,
                    value: item.code
                }))
            }
        },
        {
            label: '有效状态',
            field: 'validState',
            compType: 'select',
            compConfig: {
                options: validState_options.value.map(item => ({
                    name: item.name,
                    value: item.code
                }))
            }
        },
        {
            label: '描述',
            field: 'rule_desc',
            compType: 'input'
        },
        {
            label: '创建人',
            field: 'createdId',
            compType: 'input'
        },
        {
            label: '规则编号',
            field: 'ruleNumber',
            compType: 'input'
        },
        {
            label: '规则变量',
            field: 'textDtl',
            compType: 'input'
        }
    ]
}));

// 事件配置
const eventConfig = reactive({
    searchEvent: (formValue) => {

        // 将表单值同步到原有表单中
        form.name = formValue.name;
        form.rule_type = formValue.rule_type;
        form.rule_state = formValue.rule_state;
        form.validState = formValue.validState;
        form.rule_desc = formValue.rule_desc;
        form.textdtl = formValue.textdtl;
        form.createdId = formValue.createdId;
        form.ruleNumber = formValue.ruleNumber;
        searchRuleList(pagination.value.page, pagination.value.limit);
    },
    addPermission: RULE_PERMISSION.RULE.ADD,
    addNewEvent: () => {
        showModal('add');
    }
});

// 批量操作按钮配置
const batchActionsConfig = computed(() => {
    const actions = [];

    if (!demandUuid.value) {
        actions.push({
            key: 'lock',
            label: '锁定',
            permission: RULE_PERMISSION.RULE.LOCK,
            handler: handleLock
        });

        actions.push({
            key: 'unlock',
            label: '解锁',
            permission: RULE_PERMISSION.RULE.UNLOCK,
            handler: handleUnLock
        });

        actions.push({
            key: 'recall',
            label: '撤回',
            permission: RULE_PERMISSION.RULE.WITHDRAW,
            handler: handleRuleCall
        });
    }

    // 只有当selectFlag为true时（非工作空间模式），才显示复制按钮
    if (selectFlag.value) {
        actions.push({
            key: 'copy',
            label: '复制',
            permission: RULE_PERMISSION.RULE.COPY,
            handler: () => showModal('copy')
        });
    }

    actions.push({
        key: 'delete',
        label: '删除',
        permission: RULE_PERMISSION.RULE.DELETE,
        handler: handleDelete
    });

    actions.push({
        key: 'submit',
        label: '提交',
        permission: RULE_PERMISSION.RULE.SUBMIT,
        handler: handleSubmitRule
    });

    // 添加导出功能
    actions.push({
        key: 'exportSelected',
        label: '导出所选',
        handler: handleExportSelected,
        permission: RULE_PERMISSION.RULE.SUBMIT // 使用现有权限
    });

    actions.push({
        key: 'exportAll',
        label: '导出全部',
        handler: handleExportAll,
        permission: RULE_PERMISSION.RULE.SUBMIT // 使用现有权限
    });

    return actions;
});

// 获取操作菜单项
const getActionMenuItems = (record) => {
    const menuItems = [
        {
            key: 'submit',
            label: '提交',
            permission: RULE_PERMISSION.RULE.SUBMIT,
            onClick: () => handleSubmitRule(record)
        }
    ];
    // 只有当selectFlag为true时（非工作空间模式），才显示更新菜单项
    if (selectFlag.value) {
        menuItems.push({
            key: 'update',
            label: '更新',
            permission: RULE_PERMISSION.RULE.EDIT,
            onClick: () => showModal('update', record)
        });
        menuItems.push({
            key: 'copy',
            label: '复制',
            permission: RULE_PERMISSION.RULE.COPY,
            onClick: () => showModal('copy', record)
        });
    }
    menuItems.push(
        {
            key: 'detail',
            label: '详情',
            permission: RULE_PERMISSION.RULE.DETAIL,
            onClick: () => addDetailTabs(record)
        },
        {
            key: 'delete',
            label: '删除',
            permission: RULE_PERMISSION.RULE.DELETE,
            onClick: () => handleDelete(record)
        },
        {
            key: 'lock',
            label: '锁定',
            permission: RULE_PERMISSION.RULE.LOCK,
            onClick: () => handleLock(record)
        },
        {
            key: 'unLock',
            label: '解锁',
            permission: RULE_PERMISSION.RULE.UNLOCK,
            onClick: () => handleUnLock(record)
        },
        {
            key: 'history',
            label: '历史',
            onClick: () => handleHistory(record)
        },
        {
            key: 'call',
            label: '撤回',
            permission: RULE_PERMISSION.RULE.WITHDRAW,
            onClick: () => handleRuleCall(record)
        }
    );

    return menuItems;
};

// 是否显示侧边栏
const props = defineProps({
    ruleSidebarRef: {
        type: Object,
        default: null
    }
});


const { ruleTypes } = useRuleTypes()

// 处理列显示/隐藏事件
function handleColumnChange(visibleColumns) {
    // 使用tableColumnsConfig提供的updateVisibleColumns方法
    columns.value = tableColumnsConfig.updateVisibleColumns(visibleColumns);
}

// 暴露方法和属性给父组件
defineExpose({
    rulePackageName,
    checkRulePosition
})

//object转array
function objToArr(arr, obj) {
    Object.keys(obj).forEach(key => {
        arr.push(obj[key]);
    })
}

/**
 * 处理规则导航（上一个/下一个）
 * @param currentUuid 当前规则uuid
 * @param direction 导航方向 'prev'或'next'
 */
function handleRuleNavigation(currentUuid, direction) {
    if (!data.value || data.value.length === 0) {
        message.info('当前列表中没有可导航的规则');
        return;
    }

    // 在当前数据中找到当前规则的索引
    const currentIndex = data.value.findIndex(item => item.uuid === currentUuid);

    if (currentIndex === -1) {
        message.info('无法确定当前规则在列表中的位置');
        return;
    }

    // 检查是否是第一个或最后一个
    const isFirst = currentIndex === 0;
    const isLast = currentIndex === data.value.length - 1;

    // 如果是第一个且要向前导航，或者是最后一个且要向后导航，则不执行任何操作
    if ((isFirst && direction === 'prev') || (isLast && direction === 'next')) {
        return;
    }

    let targetIndex;
    if (direction === 'prev') {
        // 上一个
        targetIndex = currentIndex - 1;
    } else if (direction === 'next') {
        // 下一个
        targetIndex = currentIndex + 1;
    } else {
        return;
    }

    // 获取目标规则
    const targetRule = data.value[targetIndex];

    // 检查已打开的标签页中是否存在目标规则
    const existingRuleTabIndex = ruleTabLists.value.findIndex(tab => tab.uuid === targetRule.uuid);

    if (existingRuleTabIndex !== -1) {
        // 如果标签页已存在，直接激活它
        tabName.value = TAB_KEY_FORMAT.EDIT(targetRule.uuid);
    } else {
        // 如果标签页不存在，打开新标签页
        addRuleTabs(targetRule.uuid);
    }

    // 发送导航完成事件，包含新规则的位置信息
    const newPosition = {
        isFirst: targetIndex === 0,
        isLast: targetIndex === data.value.length - 1,
        ruleUuid: targetRule.uuid
    };

    globalEventEmitter.emit(RULE_NAVIGATION_COMPLETE, newPosition);
}

/**
 * 检查规则在当前列表中的位置
 * @param ruleUuid 规则UUID
 * @returns {Object} 位置信息对象，包含isFirst和isLast属性
 */
function checkRulePosition(ruleUuid) {
    if (!data.value || data.value.length === 0 || !ruleUuid) {
        return { isFirst: true, isLast: true }; // 空列表情况下，视为同时是第一个和最后一个
    }

    const index = data.value.findIndex(item => item.uuid === ruleUuid);
    if (index === -1) {
        return { isFirst: false, isLast: false }; // 找不到规则时，返回默认值
    }

    return {
        isFirst: index === 0,
        isLast: index === data.value.length - 1,
        index: index
    };
}

/**
 * 复制当前标签页的链接到剪贴板
 */
function copyCurrentTabLink() {
    // 创建一个新的URL对象，基于当前页面的URL
    const url = new URL(window.location.href);

    // 清理URL中可能存在的旧参数
    url.searchParams.delete('activeTab');
    url.searchParams.delete('ruleUuid');
    url.searchParams.delete('tabType');

    // 从当前活动的标签页key中提取信息
    const activeKey = tabName.value;
    let uuid = '';
    let tabType = '';

    // 确定标签类型和uuid
    if (activeKey.startsWith(TAB_PREFIX.EDIT)) {
        tabType = TAB_TYPE.EDIT;
        uuid = activeKey.replace(TAB_PREFIX.EDIT, '');
    } else if (activeKey.startsWith(TAB_PREFIX.DETAIL)) {
        tabType = TAB_TYPE.DETAIL;
        uuid = activeKey.replace(TAB_PREFIX.DETAIL, '');
    } else {
        // 如果是设计视图，则不添加参数
        message.info('当前页面无法生成分享链接');
        return;
    }

    // 设置URL参数
    url.searchParams.set('activeTab', tabType);
    url.searchParams.set('ruleUuid', uuid);
    url.searchParams.set('tabType', tabType);

    // 添加路由相关参数确保能够正确打开规则
    if (route.query.ruleBaseId) {
        url.searchParams.set('ruleBaseId', route.query.ruleBaseId);
    }

    if (route.query.rulePackageId) {
        url.searchParams.set('rulePackageId', route.query.rulePackageId);
    }

    // 添加时间戳参数，防止浏览器缓存造成问题
    url.searchParams.set('_t', Date.now().toString());

    // 生成最终URL
    const finalUrl = url.toString();

    // 复制链接到剪贴板
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 对于支持Clipboard API的现代浏览器
            navigator.clipboard.writeText(finalUrl).then(() => {
                message.success('链接已复制到剪贴板');
            }).catch(() => {
                message.error('复制失败');
            });
        } else {
            // 对于不支持Clipboard API的浏览器，使用传统方法
            const textarea = document.createElement('textarea');
            textarea.value = finalUrl;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textarea);

            if (successful) {
                message.success('链接已复制到剪贴板');
            } else {
                message.error('复制失败');
            }
        }
    } catch (err) {
        message.error('复制失败');
        console.error('复制失败:', err);
    }
}
const updateValidStatus = (record) => {
    // 切换validStatus的值，0表示有效，1表示无效
    const newValidStatus = record.validStatus == 0 ? '1' : '0';

    // 先获取完整的规则信息，再更新
    getRuleByUuid({
        uuids: record.uuid,
    }).then(ruleInfo => {
        if (!ruleInfo || !ruleInfo.data) {
            message.error('获取规则信息失败');
            return;
        }

        // 只更新validStatus字段
        const ruleData = ruleInfo.data;
        ruleData.validStatus = newValidStatus;

        // 调用API更新规则，使用和RuleUpdate相同的参数格式
        ruleSaveOrUpdate(ruleData, record.engUuid, demandUuid.value).then((res) => {
            if (res.code === 20000 && res.data === '修改成功！') {
                message.success('状态已更新');
                // 更新本地数据，确保UI和数据一致
                record.validStatus = newValidStatus;
                //实时更新修改时间
                getRuleByUuid({
                    uuids: record.uuid,
                }).then(res => {
                    record.lastModifiedTimeStr = res.data.lastModifiedTimeStr;
                })
            } else {
                message.error(res.data || '更新失败');
            }
        }).catch((error) => {
            message.error('更新失败：' + (error.message || '未知错误'));
        });
    });
};

// 导出相关功能已经整合到上面的batchActionsConfig computed方法中

// 导出相关状态和变量

const exportModalVisible = ref(false);
const exportType = ref<'selected' | 'all'>('selected');
const exportFields = ref([]);
const isExporting = ref(false);

// 生成导出字段列表
const generateExportFields = () => {
  // 从columnsWithoutIndexAction获取所有可能的字段（不仅是可见的）
  const systemFields = columnsWithoutIndexAction.map(col => {
    // 直接使用列定义中的字段名，不进行任何映射
    const key = col.dataFlag || col.key;
    const label = col.title;

    // 查找当前列是否在显示的列中
    const isVisible = columns.value.some(visibleCol =>
      (visibleCol.key === col.key) || (visibleCol.dataIndex === col.dataFlag) || (visibleCol.dataIndex === col.dataIndex)
    );

    return {
      key,
      label,
      checked: isVisible, // 根据列是否可见设置初始勾选状态
      type: 'system'
    };
  }).filter(field =>
    // 过滤掉没有key的字段
    field.key &&
    // 过滤掉没有label的字段
    field.label &&
    // 过滤掉action字段(操作列)
    field.key !== 'action'
  );

  // 可以在这里添加自定义字段
  const customFields = [];

  return [...systemFields, ...customFields];
};

// 导出所选
const handleExportSelected = () => {
    if (selectKeys.value.length === 0) {
        message.warning('请先选择要导出的规则');
        return;
    }

    // 动态生成当前列表中的字段
    exportFields.value = generateExportFields();
    exportType.value = 'selected';
    exportModalVisible.value = true;
};

// 导出全部
const handleExportAll = () => {
    // 动态生成当前列表中的字段
    exportFields.value = generateExportFields();
    exportType.value = 'all';
    exportModalVisible.value = true;
};

// 执行导出
const handleExport = async (exportParams) => {
    if (isExporting.value) return;

    let loadingMessage;
    try {
        isExporting.value = true;
        loadingMessage = message.loading('正在导出...', 0);

        // 按照新规范构建导出参数
        const params = {
            exportModule: exportParams.exportModule || 'ruleList',
            exportType: exportParams.exportType,
            fields: exportParams.fields,
            pattern: exportParams.pattern || 'Excel',
            // 模块特定参数
            engUuid: ruleBaseId.value,
            folderUuid: rulePackageId.value || undefined,
            // 将所有筛选参数放入filters对象，并进行参数名映射
            filters: {
                ruleName: form.name,                // 规则名称
                type: form.rule_type,              // 规则类型
                status: form.rule_state,           // 规则状态
                validStatus: form.validState,      // 有效状态
                descs: form.rule_desc,             // 描述
                textDtl: form.textdtl,             // 内容
                sortColumn: flag,                  // 排序字段
                sortOrder: sort.value              // 排序方向
            }
        };

        // 如果是导出选中记录，添加选中ID
        if (exportParams.exportType === 'selected' && exportParams.selectedIds) {
            params.selectedIds = exportParams.selectedIds;
        }

        const res = await batchExportRules(params);
        const success = handleDownload(res);

        if (success) {
            message.success('导出成功');
            // 清除选择
            if (params.exportType === 'selected') {
                selectKeys.value = [];
                checkedUuids.value = [];
            }
        } else {
            message.error('导出失败');
        }

    } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
    } finally {
        // 确保无论成功还是失败都关闭loading
        if (loadingMessage) {
            loadingMessage();
        }
        isExporting.value = false;
    }
};
// 添加一个计算属性来决定layoutKey
const computedLayoutKey = computed(() => {
  // 检查URL是否包含特定路径
  const currentUrl = window.location.href;
  const isRuleBaseAll = currentUrl.includes('ruleBase-all');
  const isRuleBaseDemand = currentUrl.includes('ruleBase-demandUuid');

  // 只有当满足条件时才返回动态的layoutKey，否则返回静态值
  return (isRuleBaseAll || isRuleBaseDemand) ? layoutKey.value : 'static-key';
});

// 在RuleBaseContent.vue中
const handleSubmitEvent = (data) => {
  if (!data || !data.record || !data.record.uuid) {
    console.warn('[DEBUG] 事件数据无效');
    return;
  }

  // 使用防抖处理事件响应
  if (isDebouncing.value) {
    return;
  }

  isDebouncing.value = true;
  setTimeout(() => {
    isDebouncing.value = false;
  }, 1000);

  // 处理提交逻辑
  handleSubmitRule(data.record, data.tabKey);
};

// 组件卸载时清理
onUnmounted(() => {
  globalEventEmitter.off(RULE_SUBMIT_ACTION, handleSubmitEvent);
});
</script>

<template>
    <div class="rule-base-content">
        <a-tabs class="ant-tabs-nav rule-base-tabs" id="rule-base-content-component"
            style="margin-left: 5px; margin-top: 5px" v-model:activeKey="tabName" hide-add @edit="onEdit"
            type="editable-card">
            <a-tab-pane tab="规则列表" key="designView" :closable="false">
                <YunXiaoListPageLayout :key="computedLayoutKey" title="规则列表" :searchConfig="searchConfig"
                    :eventConfig="eventConfig" :batchActions="batchActionsConfig" :reloadFun="reset"
                    :showAddButton="selectFlag" :showSidebarButton="true" :ruleSidebarRef="props.ruleSidebarRef">
                    <!-- 添加返回任务按钮 -->
                    <template #rightButtons>
                        <a-button style="margin-right: 10px" v-if="demandUuid" type="primary" @click="toTask">返回任务</a-button>
                    </template>
                    <template #table="{ formValue, scrollY }">
                        <div class="table-container">
                            <div class="table-content">
                                <TableSkeleton
                                    v-if="loading"
                                    :columns="columns"
                                    :limit="pagination.limit"
                                    :scrollY="scrollY - 5"
                                />
                                <a-table
                                    v-else
                                    :columns="columns"
                                    :data-source="data"
                                    rowKey="ruleName"
                                    :pagination="false"
                                    :loading="false"
                                    row-key="uuid"
                                    @change="handleTableChange"
                                    :scroll="data?.length ? { x: '100vw', y: scrollY - 50 } : {}"
                                    :rowSelection="rowSelection"
                                    size="small"
                                >
                                    <template #bodyCell="{ column, record, index }">
                                        <template v-if="column.key === 'index'">
                                            {{ (pagination.page - 1) * pagination.limit + index + 1 }}
                                        </template>
                                        <template v-else-if="column.key === 'status'">
                                            <span v-if="record.status == '已创建'">已创建</span>
                                            <span v-else-if="record.status == '已删除'">已删除</span>
                                            <span v-else-if="record.status == '编辑中'">编辑中</span>
                                            <span v-else-if="record.status == '提交待审核'">提交待审核</span>
                                            <span v-else-if="record.status == '审核中'">审核中</span>
                                            <span v-else-if="record.status == '提交审核退回'">提交审核退回</span>
                                            <span v-else-if="record.status == '删除待审核'">删除待审核</span>
                                            <span v-else-if="record.status == '删除审核退回'">删除审核退回</span>
                                            <span v-else>{{ record.status }}</span>
                                        </template>
                                        <template v-if="column.key === 'validStatus'">
                                            <div
                                                style="height: 32px; display: flex; justify-content: center; align-items: center;">
                                                <a-switch :checked="record.validStatus == 0"
                                                    :checked-children="record.validStatus == 0 ? '有效' : '无效'"
                                                    @click="updateValidStatus(record)" size="default"
                                                    :disabled="!checkPermi([RULE_PERMISSION.RULE.EDIT])"/>
                                            </div>
                                        </template>
                                        <template v-if="column.key === 'type'">
                                            <a-tag v-if="record.type === '决策表'" color="green">决策表</a-tag>
                                            <a-tag v-else-if="record.type === '普通规则'" color="blue">普通规则</a-tag>
                                            <a-tag v-else-if="record.type === '规则流' && record.salience === '1'"
                                                color="purple">规则流</a-tag>
                                            <a-tag v-else-if="record.type === '规则流' && record.salience === '0'"
                                                color="geekblue">子规则流</a-tag>
                                            <a-tag v-else-if="record.type === '决策树'" color="cyan">决策树</a-tag>
                                            <a-tag v-else>{{ record.type }}</a-tag>
                                        </template>
                                        <template v-if="column.key === 'ruleName'">
                                            <template v-if="checkPermi([RULE_PERMISSION.RULE.EDIT])">
                                                <a-tooltip :title="record.ruleName">
                                                    <a type="link" @click="openRule(record)" size="small"
                                                        class="rule-item" :data-uuid="record.uuid">
                                                        <!-- 使用组件替换img标签 -->
                                                        <IconRuleTypeDecisionTable v-if="record.type === '决策表'" :size="16"
                                                            style="margin-right: 5px;" />
                                                        <IconRuleTypeOrdinaryRule v-else-if="record.type === '普通规则'"
                                                            :size="16" style="margin-right: 5px;" />
                                                        <IconRuleTypeRuleFlow v-else-if="record.type === '规则流'" :size="16"
                                                            style="margin-right: 5px;" />
                                                        <IconRuleTypeDecisionTree v-else-if="record.type === '决策树'"
                                                            :size="16" style="margin-right: 5px;" />
                                                        {{ record.ruleName }}
                                                    </a>
                                                </a-tooltip>
                                            </template>
                                            <template v-else>
                                                <span>
                                                    <!-- 使用组件替换img标签 -->
                                                    <IconRuleTypeDecisionTable v-if="record.type === '决策表'" :size="16"
                                                        style="margin-right: 5px;" />
                                                    <IconRuleTypeOrdinaryRule v-else-if="record.type === '普通规则'" :size="16"
                                                        style="margin-right: 5px;" />
                                                    <IconRuleTypeRuleFlow v-else-if="record.type === '规则流'" :size="16"
                                                        style="margin-right: 5px;" />
                                                    <IconRuleTypeDecisionTree v-else-if="record.type === '决策树'" :size="16"
                                                        style="margin-right: 5px;" />
                                                    {{ record.ruleName }}
                                                </span>
                                            </template>
                                        </template>
                                        <template v-if="column.key === 'rulePath'">
                                            <RulePath :path="record.packageNameAll" :ruleName="record.ruleName"
                                                :pathLength="30" showCopyButton :useDotForFirstLevel="true" />
                                        </template>
                                        <template v-if="column.key === 'descs'">
                                            <a-tooltip placement="topLeft" :title="record.descs">
                                                {{ record.descs }}
                                            </a-tooltip>
                                        </template>
                                        <template v-if="column.key === 'action'">
                                            <MoreActionMenu :menuItems="getActionMenuItems(record)" />
                                        </template>
                                    </template>
                                </a-table>
                            </div>
                            <div class="pagination-container">
                                <Pagination :paginations="pagination" @change="handlePageChange" :scrollY="scrollY - 30" mode="ruleManage" />
                            </div>
                        </div>
                    </template>
                </YunXiaoListPageLayout>
            </a-tab-pane>
            <a-tab-pane v-for="item in ruleTabLists" :tab="item.ruleTitle" :key="TAB_KEY_FORMAT.EDIT(item.uuid)"
                :closable="true" :name="item.uuid">
                <!-- 规则内容 -->
                <RuleTabEdit v-if="item.type" :obj="item" />
            </a-tab-pane>
            <a-tab-pane v-for="item in detailTabLists" :tab="TAB_TITLE_FORMAT.DETAIL(item.ruleName)"
                :key="TAB_KEY_FORMAT.DETAIL(item.uuid)" :closable="true" :name="item.uuid">
                <RuleDetail :ruleInfoData="item"></RuleDetail>
            </a-tab-pane>
            <template #tabBarExtraContent>
                <TabRight v-model:editableTabsValue="tabName" v-model:editableTabs="ruleTabLists"
                    v-model:detailTabLists="detailTabLists"
                    :showClose="ruleTabLists.length > 0 || detailTabLists.length > 0" :ruleBaseId="ruleBaseId" />
            </template>
        </a-tabs>
    </div>
    <!-- 新增和更新对话框 -->
    <a-modal :width="modalType === 'ruleHistory' ? '100%' : 600"
        :wrapClassName="modalType === 'ruleHistory' ? 'full-modal' : ''" :visible="isModalVisible" :title="modalType === 'add'
            ? '新增规则'
            : modalType === 'copy'
                ? '复制规则'
                : modalType === 'ruleModifyCopy'
                    ? '规则名修改并复制'
                    : '更新规则'
            " @ok="handleSubmit" @cancel="handleCancel" :okText="modalType === 'ruleHistory' ? '确定' : '保存'"
        v-if="isModalVisible">
        <div style="max-height: 60vh; overflow-y: auto">
            <RuleAdd ref="ruleAddComponent" v-if="modalType === 'add'" :ruleBaseId="ruleBaseId"
                :rulePackageId="rulePackageId" />
            <RuleUpdate ref="ruleUpdateComponent" v-if="modalType === 'update'" :ruleId="ruleId"
                :ruleBaseId="ruleBaseId" :rulePackageId="rulePackageId" />
            <RuleCopy ref="ruleCopyComponent" v-if="modalType === 'copy'" :ruleId="ruleId" :ruleBaseId="ruleBaseId"
                :rulePackageId="rulePackageId" :datar="datar" />
            <RuleModifyCopy ref="ruleModifyCopyComponent" v-if="modalType === 'ruleModifyCopy'" :datar="datar" />
        </div>
    </a-modal>
    <!-- 规则历史 -->
    <FlexDrawer :visible="ruleHistoryDrawerVisible" @close="closeDrawer" title="规则历史" :width="1200">
        <RuleHistory ref="ruleHistoryComponent" v-if="ruleHistoryDrawerVisible" :ruleId="ruleId" :datar="datar" />
    </FlexDrawer>
    <!-- 详情对话框 -->
    <FullModel :isFullscreen="isFullscreen" :onFullscreenToggle="onFullscreenToggle" :titleText="titleText"
        :handleCancel="handleFullCancel" :isModalVisible="isFullModalVisible">
        <template #default>
            <div :style="{
                maxHeight: isFullscreen ? '75vh' : '60vh',
                overflowY: 'auto',
            }">
                <RuleHistory ref="ruleHistoryComponent" v-if="modalType === 'ruleHistory'" :ruleId="ruleId"
                    :datar="datar" />
            </div>
        </template>
    </FullModel>
    <!-- 批量复制规则 -->
    <a-modal v-if="dialogFormVisible" :visible="dialogFormVisible" title="复制到" @ok="copyDetermine" @cancel="cancelCopy">
        <a-form :model="copyForm" :rules="rules">
            <a-form-item label="目标规则包" name="ruleTrees">
                <a-tree-select v-model:value="selectFolderName" style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" :tree-data="ruleTreeOptions" show-search
                    tree-node-filter-prop="title" placeholder="请输入查询内容" @select="ruleNodeClick">
                </a-tree-select>
            </a-form-item>
        </a-form>
    </a-modal>
    <!-- 批量导出配置对话框 -->
    <ExportConfigModal
        v-model:visible="exportModalVisible"
        :fields="exportFields"
        :export-type="exportType"
        :selected-ids="selectKeys"
        :total-count="pagination.total"
        :export-module="'ruleList'"
        :module-params="{ engUuid: ruleBaseId, folderUuid: rulePackageId }"
        :search-params="form"
        @cancel="exportModalVisible = false"
        @export="handleExport"
    />
</template>

<style lang="scss" scoped>
.rule-base-content {
    :deep(.ant-form-inline) {
        .ant-form-item {
            margin-bottom: 10px;
        }
    }

    .ant-tabs-editable-card {
        &> :deep(.ant-tabs-nav) {
            margin: 0 0 0 0;

            .ant-tabs-extra-content {
                margin-right: 16px;
            }
        }
    }

    :deep(.ant-form-item-explain-error) {
        color: #ff4d4f !important;
    }

    .action {
        display: flex;
        align-items: center;
        justify-content: space-around;
    }

    // 添加表格容器样式
    .table-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 180px);
        position: relative;

        .table-content {
            flex: 1;
            overflow: auto;
        }

        .pagination-container {
            position: sticky;
            bottom: 0;
            background: #fff;
            padding: 8px 0;
            border-top: 1px solid #f0f0f0;
            z-index: 10;
        }
    }
}

.full-modal {
    .ant-modal {
        max-width: 100%;
        top: 0;
        padding-bottom: 0;
        margin: 0;
    }

    .ant-modal-content {
        display: flex;
        flex-direction: column;
        height: calc(100vh);
    }

    .ant-modal-body {
        flex: 1;
    }
}
</style>

