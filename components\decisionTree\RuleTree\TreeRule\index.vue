<script lang="jsx">
import { ruleModalInit } from "@/api/rule_editor";
import RuleUpdateTree from "./ruleUpdate.vue";

export default {
  name: "TreeRule",
  components: {
    RuleUpdateTree: RuleUpdateTree,
  },
  props: {
    ruleInfo: {
      type: Object,
      default: () => {},
    },
    conditions: {
      type: Array,
      default: () => [],
    },
    predefines: {
      type: Array,
      default: () => [],
    },
    activeShapeId: {
      type: String,
      default: () => "",
    },
    ruleDrl: {
      type: String,
      default: () => "",
    },
    viewStatus:{
      type: String,
      default: ''
    },
    activeShow:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      modelInit: {},
      ruleContent: {},
      uuid: "",
      demandUuid: "",
    };
  },
  watch: {
    ruleInfo: {
      handler(newValue, oldValue) {
        const {
          engUuid, uuid, demandUuid
        } = newValue;
        this.$data.uuid = uuid;
        this.$data.demandUuid = demandUuid;
        this.getData(engUuid, uuid);
      },
      deep: true,
      immediate: true,
    },
    activeShapeId: {
      handler(newValue, oldValue) {
        let hasObj = this.conditions.some((i) => {
          return i.nodeId === newValue;
        });
        if (hasObj) {
          this.conditions.map((item, i) => {
            if (item.nodeId === newValue) {
              this.$data.ruleContent = this.conditions[i];
            }
          });
        } else {
          this.$data.ruleContent = {};
        }
        // this.$forceUpdate();
      },
      deep: true,
      immediate: true,
    },
  },
  provide() {
    return {
      providEngUuid: this.ruleInfo.engUuid,
      ruleUuid: this.ruleInfo.uuid
    };
  },
  methods: {
    getData(engUuid) {
      ruleModalInit(engUuid)
        .then((res) => {
          this.modelInit = res.data;
        })
        .catch((error) => {
          return Promise.reject(error);
        });
    },
    save(param) {
      this.$emit("setConditions", param);
    },
  },
  render() {
    let { ruleContent = {}, uuid, modelInit, ruleDrl } = this;
    if (Object.keys(ruleContent).length > 0) {
      if (ruleContent["nodeRule"]) {
        // conditionExpression初始值
        if (
          ruleContent["nodeRule"].conditionExpression == "null" ||
          !ruleContent["nodeRule"].conditionExpression ||
          ruleContent["nodeRule"].conditionExpression == "undefined"
        ) {
          ruleContent["nodeRule"].conditionExpression = "#";
        }
        // conditions初始值
        if (
          ruleContent["nodeRule"].conditions &&
          ruleContent["nodeRule"].conditions.length === 0
        ) {
          ruleContent["nodeRule"].conditions = [
            {
              variable: {
                valueType: "String",
                variableType: "dataEntry",
                viewName: "",
              },
              leftValueType: "String",
            },
          ];
        }
      }
    } else {
      ruleContent.nudeId = this.activeShapeId;
      ruleContent.nodeRule = {
        conditionExpression: "#",
        ruleAttributes: [],
        elseActions: [],
        conditions: [
          {
            variable: {
              valueType: "String",
              variableType: "dataEntry",
              viewName: "",
            },
            leftValueType: "String",
          },
        ],
        predefines: [],
        actions: [],
      };
    }
    return (
      <div>
        <RuleUpdateTree
          ref="ruleUpdate"
          modelData={modelInit}
          ruleContent={ruleContent}
          uuid={uuid}
          ruleDrl={ruleDrl}
          demandUuid={this.demandUuid}
          viewStatus={this.viewStatus}
          activeShow={this.activeShow}
          predefines={this.predefines}
          onSave={this.save}
        />
      </div>
    );
  },
};
</script>
