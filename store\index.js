import { createStore } from 'vuex'

import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import ruleEdit from './modules/ruleEdit'
import generalRule from './modules/generalRule'
import bpmn from './modules/bpmn'

const store = createStore({
  modules: {
    app,
    settings,
    user,
    ruleEdit,
    generalRule,
    bpmn
  }
})

export default store
