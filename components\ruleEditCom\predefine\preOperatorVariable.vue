<template>
  <ConditionListPre
    pos="r"
    :conditionData="conditionData"
    :locked="locked"
    :variableType="variableType"
    :predefineLine="predefineLine"
    :preIndex="preIndex"
    :predefineCon="predefineCon"
    @conditionChange="onConditionChange"
  />
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";
import ConditionListPre from "./ruleItem/conditionList.vue";

const { ConditionGenerate } = util;

const props = defineProps({
  selectVariableValue: {
    type: Object,
    default: () => ({})
  },
  locked: Boolean,
  variableType: String,
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
});

const emit = defineEmits(['onchange']);

const ruleUuid = inject('ruleUuid', '');

const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry"
  },
  leftValueType: "String"
};

const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#"
};

const conditionData = computed(() => {
  const res = props.selectVariableValue.conditionExpression
    ? util.getConditionTree(
        props.selectVariableValue.conditionExpression,
        props.selectVariableValue.conditions,
        ruleUuid
      )
    : util.getConditionTree(
        initRuleData.conditionExpression,
        initRuleData.conditions,
        ruleUuid
      );
  return res;
});

const onConditionChange = (pos, newContents) => {
  const [, conditionId] = pos.split("_");
  const { targetNode } = util.findTargetNodeInfoById(conditionData.value, conditionId);
  targetNode.ruleCondition.contents = new ConditionGenerate(newContents);
  emit("onchange", util.getConditionDataToBE(conditionData.value, {
    conditions: [],
    conditionExpression: "",
  }, ruleUuid));
};
</script>
