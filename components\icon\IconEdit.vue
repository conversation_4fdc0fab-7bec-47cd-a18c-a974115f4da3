<!-- 编辑图标 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-pencil-underscore icon-svg TemplateTreeItem-module_actionIcon_haD5C index-module_size_wVASz"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <g fill="none" fill-rule="evenodd">
            <path d="M0 0h16v16H0z"></path>
            <path
                d="M13.74 3.35a1 1 0 0 1 0 1.414l-6.48 6.479a1 1 0 0 1-.572.284l-1.636.22a1 1 0 0 1-1.125-1.124l.221-1.636a1 1 0 0 1 .284-.573l6.48-6.479a1 1 0 0 1 1.413 0L13.74 3.35Zm-1.061.706-1.06-1.06-6.243 6.241-.166 1.227 1.226-.165 6.243-6.243ZM2.375 13h11.25a.625.625 0 1 1 0 1.25H2.375a.625.625 0 1 1 0-1.25Z"
                fill="currentColor" fill-rule="nonzero"></path>
        </g>
    </svg>
</template>