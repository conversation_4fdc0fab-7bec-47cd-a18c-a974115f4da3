<!-- 大头针空心图标 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg :width="`${size}px`" :height="`${size}px`" viewBox="0 0 16 16"
         xmlns="http://www.w3.org/2000/svg"
         class="larkui-icon larkui-icon-pin-outlined ReaderLayout-module_actionItem_CbOzz index-module_size_wVASz TemplateTreeItem-module_actionIcon_haD5C"
         :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <g fill="none" fill-rule="evenodd">
            <path d="M16 0H0v16h16z"></path>
            <path d="m14.028 5.176-3.204-3.204a.544.544 0 0 0-.773 0L7.4 4.625a5.424 5.424 0 0 0-4.004 1.157.547.547 0 0 0-.045.813l6.058 6.053a.544.544 0 0 0 .813-.044 5.437 5.437 0 0 0 1.155-4.006l2.651-2.651a.545.545 0 0 0 0-.77Z"
                  stroke="currentColor"
                  stroke-width="1.25"></path>
            <path stroke="currentColor"
                  stroke-width="1.25"
                  stroke-linecap="round"
                  d="m2.5 13.5 3.771-3.771"></path>
        </g>
    </svg>
</template>
