<template>
  <span class="lockedText" v-if="locked || isTrack">
    <span class="calculateSign txtItem">{{ value }}</span>
  </span>
  <a-dropdown
    v-else
    :trigger="['click']"
    :open="visible"
    @openChange="handleVisibleChange"
  >
    <span v-if="value" class="calculateSign txtItem">{{ value }}</span>
    <span v-else class="calculateSign txtItem">
      <DownOutlined />
    </span>
    <template #overlay>
      <a-menu @click="onSelectChange">
        <template v-for="item in options" :key="item.value">
          <a-menu-item
            v-if="item.value === 'delete'"
            :disabled="isLastOne"
            :key="item.value"
          >
            <span>{{ item.label }}</span>
          </a-menu-item>
          <a-sub-menu
            v-else
            :title="item.label"
            :key="item.value"
            @titleClick="onSubmenuClick"
          >
            <a-menu-item
              v-for="item2 in secendLevelOptions"
              :key="item2.value"
              :disabled="value === item2.value"
            >
              <span>{{ item2.label }}</span>
            </a-menu-item>
          </a-sub-menu>
        </template>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup>
import { ref, computed } from 'vue';
import { DownOutlined } from '@ant-design/icons-vue';

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  realValueType: {
    type: String,
    default: "default",
  },
  value: {
    type: String,
    default: "",
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  isLastOne: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits(['onChange']);

// 定义响应式数据
const visible = ref(false);
const secendLevelOptions = ref([
  { value: "val", label: "值" },
  { value: "(", label: "(" },
]);

// 计算属性，根据 realValueType 返回不同的选项
const options = computed(() => {
  const numberList = [
    "Double",
    "Short",
    "Integer",
    "Long",
    "Float",
    "BigDecimal",
  ];
  if (numberList.includes(props.realValueType)) {
    return [
      { value: "+", label: "+" },
      { value: "-", label: "-" },
      { value: "*", label: "*" },
      { value: "/", label: "/" },
      { value: "%", label: "%" },
      { value: "delete", label: "删除" },
    ];
  }
  if (props.realValueType === "String") {
    return [
      { value: "+", label: "+" },
      { value: "delete", label: "删除" },
    ];
  }
  return [];
});

// 处理下拉菜单显示状态变化的方法
const handleVisibleChange = (flag) => {
  visible.value = flag;
};

// 处理选项选择变化的方法
const onSelectChange = (obj) => {
  let paramType, sign;
  if (obj.keyPath.length === 2) {
    sign = obj.keyPath[0];
    paramType = obj.keyPath[1];
  } else {
    sign = obj.keyPath[0];
  }
  change(sign, paramType);
  handleVisibleChange(false);
};

// 处理子菜单点击的方法
const onSubmenuClick = (obj) => {
  change(obj.key);
  handleVisibleChange(false);
};

// 发送变化事件的方法
const change = (sign, paramType) => {
  emit("onChange", props.pos, sign, paramType);
};
</script>
