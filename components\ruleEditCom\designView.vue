<template>
  <div class="designView">
    <div class="ruleBody">
      <!-- 条件列表组件 -->
      <ConditionList
        v-bind="conditionProps"
        @addRule="addRule"
        @addUp="addUp"
        @addChildCondition="addChildCondition"
        @addTailItem="addTailItem"
        @replaceItem="replaceItem"
        @decreaseRule="decreaseRule"
        @logicBtnClick="onLogicBtnClick"
        @conditionChange="onConditionChange"
        @conditionInit="conditionInit"
        ref="condition_list_com"
      />
      <!-- 动作列表组件 -->
      <ActionList
        v-bind="actionProps"
        @addActionItem="addActionItem"
        @replaceActionItem="replaceActionItem"
        @deleteActionItem="deleteActionItem"
        @actionComChange="onActionComChange"
        ref="action_list_com"
      />
      <!-- 否则动作列表组件 -->
      <ElseActionList
        v-bind="elseActionProps"
        @addActionItem="addActionItem"
        @replaceActionItem="replaceActionItem"
        @deleteActionItem="deleteActionItem"
        @actionComChange="onActionComChange"
        ref="else_action_list_com"
      />
    </div>
  </div>
</template>

<script setup>
import ConditionList from "@/components/ruleEditCom/condition/conditionList.vue";
import ActionList from "@/components/ruleEditCom/action/actionList.vue";
import ElseActionList from "@/components/ruleEditCom/action/elseActionList.vue";

// 定义 props
const props = defineProps({
  ruleData: {
    type: Object,
    default: () => ({}),
  },
  locked: Boolean,
  isTrack: Boolean,
  validateResult: {
    type: Object,
    default: () => ({}),
  },
  toAnalysis: {
    type: Function,
  },
});
const emit = defineEmits([
  "addRule",
  "addUp",
  "addChildCondition",
  "addTailItem",
  "replaceItem",
  "decreaseRule",
  "addActionItem",
  "replaceActionItem",
  "deleteActionItem",
  "switcherChange",
  "logicBtnClick",
  "conditionChange",
  "actionComChange",
  "conditionInit",
]);
let conditionProps = ref({});
let actionProps = ref({});
let elseActionProps = ref({});

const setProps = (newValue) => {
  // 解构 props
  const { ruleData = {}, locked, isTrack, validateResult = {} } = toRefs(props);

  // 解构 ruleData 和 validateResult
  const {
    conditionData,
    actionData,
    elseActionData = [],
  } = ruleData.value;
  const {
    conditionValids: conditionValidList = [],
    actionValids: actionValidList = [],
    elseActionValids: elseActionValidList = [],
  } = validateResult.value;

  // 定义条件列表的属性
  conditionProps.value = {
    pos: "r",
    conditionData,
    validList: conditionValidList,
    locked: locked.value,
    isTrack: isTrack.value,
  };

  // 定义动作列表的属性
  actionProps.value = {
    pos: "r",
    actionData,
    validList: actionValidList,
    locked: locked.value,
    isTrack: isTrack.value,
  };

  // 定义否则动作列表的属性
  elseActionProps.value = {
    pos: "r",
    elseActionData,
    validList: elseActionValidList,
    locked: locked.value,
    isTrack: isTrack.value,
  };
};

// 监听 ruleData 变化，当 ruleData 发生变化时获取数据
watch(
  () => props.ruleData,
  (newValue) => {
    setProps(newValue); // 调用 getData 方法获取数据
  },
  { deep: true, immediate: true } // 深度监听
);

// 监听 validateResult 变化，当 validateResult 发生变化时获取数据
watch(
  () => props.validateResult,
  (newValue) => {
    setProps(newValue); // 调用 getData 方法获取数据
  },
  { deep: true, immediate: true } // 深度监听
);

const onConditionChange = (pos, newContents) => {
  emit("conditionChange", pos, newContents);
};
const addRule = (pos, conditionId, layer) => {
  emit("addRule", pos, conditionId, layer);
};
const addUp = (pos, conditionId, layer) => {
  emit("addUp", pos, conditionId, layer);
};
const addChildCondition = (pos, conditionId, layer) => {
  emit("addChildCondition", pos, conditionId, layer);
};
const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};
const replaceItem = (pos, conditionId, layer) => {
  emit("replaceItem", pos, conditionId, layer);
};
const decreaseRule = (pos, conditionId, layer) => {
  emit("decreaseRule", pos, conditionId, layer);
};
const addActionItem = (flag, pos) => {
  emit("addActionItem", flag, pos);
};
const replaceActionItem = (flag, pos) => {
  emit("replaceActionItem", flag, pos);
};
const deleteActionItem = (flag, pos) => {
  emit("deleteActionItem", flag, pos);
};
const onLogicBtnClick = (pos, logicalSymbol) => {
  emit("logicBtnClick", pos, logicalSymbol);
};
const onActionComChange = (flag, pos, newActionData, oldRowData) => {
  emit("actionComChange", flag, pos, newActionData, oldRowData);
};
const conditionInit = () => {
  emit("conditionInit");
};

// 定义组件引用
const condition_list_com = ref(null);
const action_list_com = ref(null);
const else_action_list_com = ref(null);
</script>
