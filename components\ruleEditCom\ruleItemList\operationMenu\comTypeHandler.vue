<template>
  <a-dropdown v-if="!locked" :trigger="['click']">
    <span class="comTypeHandler" @click.stop> <DownOutlined /> </span>
    <template #overlay>
      <a-menu @click="onMenuChange">
        <a-menu-item
          key="constant"
          :disabled="
            currentType === 'constant' || disabledItems.includes('constant')
          "
        >
          <span>手动输入</span>
        </a-menu-item>
        <a-menu-item
          key="propSelect"
          :disabled="
            currentType === 'field' ||
            currentType === 'dataEntry' ||
            disabledItems.includes('propSelect')
          "
        >
          <span>属性选择</span>
        </a-menu-item>
        <a-menu-item
          key="expression"
          :disabled="
            currentType === 'expression' ||
            disabledItems.includes('expression') ||
            hiddenExpress
          "
        >
          <span>括号修饰</span>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup>

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  changeComType: {
    type: Function
  },
  currentType: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  hiddenExpress: {
    type: Boolean,
    default: false
  },
  disabledItems: {
    type: Array,
    default: () => []
  }
});

// 定义组件的 emits
const emit = defineEmits(['changeComType']);

// 处理菜单变化的方法
const onMenuChange = (obj) => {
  emit("changeComType", props.pos, obj.key, "selectChange");
};
</script>
