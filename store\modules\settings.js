import defaultSettings from '@/settings'

const {
  showSettings,
  fixedHeader,
  sidebarLogo
} = defaultSettings

const state = {
  showSettings: showSettings,
  fixedHeader: fixedHeader,
  sidebarLogo: sidebarLogo,
  dataId: "",
  businLine:"",
  demuid:"",
  taskId:""
}

const mutations = {
  CHANGE_SETTING: (state, {
    key,
    value
  }) => {
    // eslint-disable-next-line no-prototype-builtins
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  SET_ID(state, cid) {
    state.dataId = cid;
  },
  Base_ID(state, bId) {
    state.baseDataId = bId;
  },
  set_business(state,rid) {
    state.businLine = rid;
  },
  demand(state,uid){
    state.demuid = uid
  },
  task_business(state,businLineID){
    state.taskId = businLineID
  }


}

const actions = {
  changeSetting({
    commit
  }, data) {
    commit('CHANGE_SETTING', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
