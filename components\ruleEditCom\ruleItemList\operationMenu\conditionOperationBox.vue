<template>
  <a-dropdown :trigger="['click']">
    <span
      class="iconContainer iconfontbtn icon-menu"
      :style="bgImg"
      @mouseenter="onMouseEnter"
      @click="stopClick"
    />
    <template #overlay>
      <a-menu @click="onMenuChange">
        <a-menu-item key="delete">
          <span>删除</span>
        </a-menu-item>
        <a-menu-item key="addUp" v-if="layer === 1 && showAddUp">
          <span>添加上级条件</span>
        </a-menu-item>
        <a-menu-item key="addChildren">
          <span>添加联合条件</span>
        </a-menu-item>
        <a-menu-item key="addTailItem">
          <span>加至队尾</span>
        </a-menu-item>
        <a-menu-item key="replaceItem" v-if="showAddUp">
          <span>替换</span>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup>

// 定义组件的 props
const props = defineProps({
  paramLength: {
    type: Number,
    default: 0
  },
  layer: {
    type: Number,
    default: 0
  },
  showAddUp: {
    type: Boolean,
    default: true
  }
});

// 定义组件的 emits
const emit = defineEmits([
  'onChange'
]);

// 计算属性，用于获取背景样式
const bgImg = computed(() => ({
  color: '#409EFF',
  'line-height': '16px'
}));


// 处理菜单变化事件
const onMenuChange = (obj) => {
  emit('onChange', obj.key);
};

// 处理鼠标进入事件
const onMouseEnter = (e) => {
  e.stopPropagation();
  e.preventDefault();
};

// 停止点击事件冒泡
const stopClick = (e) => {
  e.stopPropagation();
};
</script>
