export const mxContrast = {
  data: () => {
    return {
      sliderValue: 100,
      sliderValue2: 100,
      tableScale: 1,
      tableScale2: 1,
      tableFullscreen: false,
      tip: "",
      tipTop: "0px",
      tipLeft: "0px",
      showTip: false,
      tabHeight: "",
      switchVal: true,
      switchVal2: true,
      hidden: '',
      tableStr: '',
      tableUuid: ''
    }
  },
  methods: {
    fullScreen(val, rout) {
      val && (this.hidden = val)
      if (!rout) {
        let newWin = window.open(window.location.href, val ? val : "newWin");
        newWin.onload = () => {
          setTimeout(() => {
            this.newWinStyle(newWin);
          }, 0);
        };
      } else {
        let strTxt = '/BRMS/erule-web/'
        let hrefA = window.location.href.split(strTxt)
        if (hrefA.length) {
          window.open(`${hrefA[0]}${strTxt}rule_contrast?uuid=${this.tableUuid}`, `conWin_${this.tableUuid}`);
          // conWin.onload = () => {
          //   setTimeout(() => {
          //     const scriptDom = document.createElement("script");
          //     scriptDom.innerHTML = `let tableStr=${this.tableStr}`
          //     conWin.document.getElementsByTagName('head')[0].appendChild(scriptDom);
          //   }, 0);
          // };
        }
      }
    },
    newWinStyle(win) {
      const styleDom = document.createElement("style");
      styleDom.innerHTML =
        `#app .el-form { margin: 1px !important; padding: 2px !important } .el-tabs { margin: 0 !important; padding: 0 !important; } #app .hideSidebar .main-container, #app .main-container { margin: 0 !important; } .el-form-item__label, .btn-item, .el-row, .el-tabs__header, .sidebar-container, .navbar,.baseline_left,.baseline_right,.el-row.fullrow.${this.hidden},.btn { display: none !important } .el-row.fullrow { display: block !important } .fakeContainer.fixed-table { height: calc(100vh - 50px) !important; }.el-form-item__content,.basd,#app .el-form,.audit_info{margin:0 !important;padding: 0 !important} .baselin_compare_detail,.el-form-item{
          margin: 0 !important;
        }`;
      win.document.body.appendChild(styleDom);
    },
    sliderInput(val = 100, className) {
      this[className] = val / 100
    },
    tipLeave() {
      this.showTip = false;
      this.tip = "";
    },
    tipHover() {
      this.showTip = true;
    },
    switchChange(strN, classN) {
      let tableObj = document.querySelectorAll(`.${classN} .datatable td`);
      for (let i = 0; i < tableObj.length; i++) {
        if (!this[strN]) {
          tableObj[i].style.whiteSpace = "pre-wrap";
          tableObj[i].style.wordWrap = "break-word";
        } else {
          tableObj[i].style.whiteSpace = "nowrap";
        }
      }
    },
  }
};
