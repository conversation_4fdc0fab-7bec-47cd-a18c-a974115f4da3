<!-- 通用操作下拉菜单组件 -->
<script setup>
// 定义组件属性
const props = defineProps({
  // 菜单项配置数组
  menuItems: {
    type: Array,
    required: true,
    // [{
    //   key: 'update',         // 操作唯一标识
    //   label: '更新',         // 显示文字
    //   permission: '',        // 权限标识
    //   disabled: false,       // 是否禁用
    //   hide: false,           // 是否隐藏
    //   onClick: Function      // 点击处理函数
    // }]
  },
  // 下拉菜单位置
  placement: {
    type: String,
    default: 'bottomRight'
  },
  // 触发方式
  trigger: {
    type: Array,
    default: () => ['click']
  },
  // 提示文字
  tooltipTitle: {
    type: String,
    default: '更多操作'
  },
  // 提示位置
  tooltipPlacement: {
    type: String,
    default: 'top'
  },
  // 图标大小
  iconSize: {
    type: Number,
    default: 20
  },
  // 是否启用图标悬停效果
  enableIconHover: {
    type: Boolean,
    default: true
  },
  // 下拉菜单最小宽度
  minWidth: {
    type: Number,
    default: 120
  }
});

// 菜单项点击处理函数
const handleMenuItemClick = (item) => {
  // 如果菜单项有onClick函数，则调用它
  if (item.onClick && typeof item.onClick === 'function') {
    item.onClick(item.key);
  }
};
//权限判断
import { checkPermi } from "@/directive/permission/permission";
</script>

<template>
  <template v-if="menuItems.length === 1">
    <a-button type="link" class="action-button" @click="handleMenuItemClick(menuItems[0])">
      {{ menuItems[0].label }}
    </a-button>
  </template>
  <template v-else>
    <a-tooltip :title="tooltipTitle" :placement="tooltipPlacement">
      <a-dropdown :trigger="trigger" :placement="placement" overlayClassName="action-dropdown-overlay">
        <template #overlay>
          <a-menu class="action-dropdown-menu" :style="{ minWidth: `${minWidth}px` }">
            <template v-for="item in menuItems">
              <a-menu-item
                v-if="!item.hide && (item.permission ? checkPermi([item.permission]):true)"
                :key="item.key"
                :disabled="item.disabled"
                @click="handleMenuItemClick(item)"
                class="action-menu-item"
              >
                {{ item.label }}
              </a-menu-item>
            </template>
          </a-menu>
        </template>
        <a-button type="text" class="action-button">
          <IconMoreHorizontal :size="iconSize" :enableHover="enableIconHover" />
        </a-button>
      </a-dropdown>
    </a-tooltip>
  </template>
</template>

<style lang="scss" scoped>
.action-button {
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.3s;
}
</style>

<style lang="scss">
  .action-dropdown-overlay {
  .ant-dropdown-menu {
    padding: 6px !important;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .ant-dropdown-menu-item {
    border-radius: 2px;
    transition: background-color 0.3s;
    font-size: 14px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important;
    color: #332828 !important;
    line-height: 1.5715 !important;
    font-weight: 400 !important;

  &:hover {
     background-color: #f5f5f5;
   }

  &[disabled] {
     color: rgba(0, 0, 0, 0.25) !important;
     cursor: not-allowed;
   }
  }
  }
</style>
