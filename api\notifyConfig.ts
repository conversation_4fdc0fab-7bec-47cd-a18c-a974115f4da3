import request from '@/utils/request'
/**
 * @description 获取通知配置列表
 * @param params 查询参数
 */
export function getNotifyConfigList(params) {
  return request({
    url: '/erule/demand2/notify-config/list',
    method: 'get',
    params
  })
}

/**
 * @description 查询通知配置
 * @param params 查询参数
 */
export function searchNotifyConfig(params) {
  return request({
    url: '/erule/demand2/notify-config/search',
    method: 'get',
    params
  })
}

/**
 * @description 根据ID获取通知配置
 * @param id 配置ID
 */
export function getNotifyConfigById(params) {
  return request({
    url: `/erule/demand2/notify-config/findById`,
    method: 'get',
    params
  })
}


/**
 * @description 保存或更新通知配置
 * @param data 通知配置数据
 */
export function saveOrUpdateNotifyConfig(data) {
  return request({
    url: '/erule/demand2/notify-config/save',
    method: 'post',
    data
  })
}

/**
 * @description 删除通知配置
 * @param id 配置ID
 */
export function deleteNotifyConfig(id) {
  return request({
    url: `/erule/demand2/notify-config/${id}`,
    method: 'delete'
  })
}
/**
 * @description 获取全部
 * @param id 配置ID
 */
export function allNotifyConfig() {
  return request({
    url: `/erule/demand2/notify-config/all`,
    method: 'get'
  })
}
