import { assign } from 'lodash'
import {
  append as svgAppend,
  attr as svgAttr,
  create as svgCreate
} from 'tiny-svg'

export default {
  'create.start-StartEvent': createAction(
    'StartNode',
    '开始节点',
    'bpmn:StartEvent',
    'event',
    'custom-icon-start-event-none',
    '开始节点',
    // require('@/components/bpmn/img/start.png')
    // createStratEvent,
  ),
  'create.end-event': createAction(
    'EndNode',
    '结束节点',
    'bpmn:EndEvent',
    'event',
    'custom-icon-end-event-none',
    '结束节点'
  ),
   'create.event': createAction(
    'RuleNode',
    '规则节点',
    'bpmn:DataStoreReference', // etl.json 定义
    'event',
    'bpmn-icon-data-store',
    '规则节点',
  ),
  'create.task': createAction(
    'DecisionNode',
    '决策分支',
    'bpmn:Task',
    'event',
    'bpmn-icon-intermediate-event-none',
    '决策分支',
    drawTask
  ),
   'create.Intermediate': createAction(
      'JoinNode',
       '汇聚节点',
       'bpmn:Task',
      'event',
      'bpmn-icon-print',
       '汇聚节点'
  ),
 'create.call-activity': createAction(
    'CallActivityNode',
    '子规则流',
    'bpmn:CallActivity',
    'event',
    'bpmn-icon-subflow-event-none',
    '子规则流'
  ),
  'create.data-object': createAction(
    '',
    '注释',
    'bpmn:DataObjectReference',
    'data-object',
    'bpmn-icon-data-object',
    '注释'
  )
}

function createAction (
  id,
  name1,
  type,
  group,
  className,
  title,
  imageUrl, 
  drawShape,
  translate,
  options
) {
  var shortType = type.replace(/^bpmn:/, '')
  	var str="";

  function createListener (event, autoActivate, elementFactory, create) {
    var shape = elementFactory.createShape(assign({ type: type }, options))

    if (options) {
      shape.businessObject.di.isExpanded = options.isExpanded
    }

    // TODO: 自定义元模型 需要 实现 createText
    // 拼接节点
    var obj;
   
     if(shape.businessObject.id =="StartNode" || shape.businessObject.id == "EndNode"){  //开始节点和结束节点
       obj = {
        base:{
          id:"",
          name:"",
          remark:""

        }
      }
    }else if(shape.businessObject.id =="TaskNode"){
      obj={
        base:{
          id:"",
          name:"",
          priority:"",
          skipauth:"",
          url:"",
          remark:""
        }
      }
      
    }else if(shape.businessObject.id =="AutoNode"){
      obj={
        base:{
          id:"",
          name:"",
          adaptertype:"",
          skipauth:"",
          rejectauth:"",
          remark:""
        }
      }

    }else if(shape.businessObject.id =="WaitNode"){
      obj={
        base:{
          id:"",
          name:"",
          skipauth:"",
          rejectauth:"",
          remark:""
        }
      }

    }else if(shape.businessObject.id =="TimerNode"){
      obj={
        base:{
          id:"",
          name:"",
          delayday:"",
          delayhour:"",
          delayminute:"",
          delaysecond:"",
          cycletype:"",
          cyclecount:"",
          intervalday:"",
          intervalhour:"",
          intervalminute:"",
          intervalsecond:"",
          continueday:"",
          continuehour:"",
          continueminute:"",
          continuesecond:"",
          adaptertype:"",
          adaptername:"",
          skipauth:"",
          rejectauth:""
        }
      }

    }else if(shape.businessObject.id =="ForkNode"||shape.businessObject.id =="DecisionNode"){ //决策分支
      obj = {
        base:{
          id:"",
          name:"",
          skipauth:"",
          rejectauth:"",
          passType:"",
          remark:""
        }
      }
    }else if(shape.businessObject.id =="JoinNode" ||shape.businessObject.id =="SJoinNode"){
      obj = {
        base:{
          id:"",
          name:"",
          skipauth:"",
          rejectauth:"",
          skipauth:"",
          rejectauth:"",
          remark:"",
          rulename:""
        }
      }
    }else if(shape.businessObject.id =="SubFlowNode"){
      obj = {
        base:{
          id:"",
          name:"",
          remark:""
        
        }
      }
    }
    else if(shape.businessObject.id =="RuleNode"){  //规则节点
      obj = {
        base:{
          id:"",
          name:"",
          adaptertype:"",
          adaptertype:"",
          skipauth:"",
          rejectauth:"",
          remark:"",
          rulename:""
        }
      }
 
    }else if(shape.businessObject.id =="ConditionNode"){
      obj = {
        base:{
          id:"",
          name:"",
          changeflag:"",
          bflag:"",
          skipauth:"",
          rulecontent:"",
          remark:"",
        }
      }
    }else if(shape.businessObject.id =="ActionNode"){
      obj = {
        base:{
          id:"",
          name:"",
          rulecontent:"",
          remark:""
        }
      }
 
    }
  
   
    shape.businessObject.name = name1

    create.start(event, shape)
      getRandomstr(5)
    shape.businessObject.id = id+"_"+str   //拼接节点id和随机数
    return obj;
   
  }
  
    // 生成0到9,a-z,0-35位取5位的随机数
  function getRandomstr(num){
      var arr=[0,1,2,3,4,5,6,7,8,9,'a','b','c','d','e','f','g','h','i','j','k','l','m','n',
				'o','p','q','r','s','t','u','v','w','x','y','z'];
      for(var i=0;i<num;i++){
       	 str+= arr[getRandom(0,35)]
      }
  }

  function getRandom(min,max){
    return Math.floor(Math.random()*(max-min))+min

  }

 
  return {
    id,
    name1,
    type,
    group: group,
    className: className,
    title: title || translate('Create {type}', { type: shortType }),
    imageUrl,
    drawShape,
    action: {
      dragstart: createListener,
      click: createListener
    }
  }
}
// 自定义事件方法
function drawCustomTask (parentNode, element, textRenderer, entries) {
  const width = 130
  const height = 60
  const borderRadius = 20
  const strokeColor = '#4483ec'
  const fillColor = !element.businessObject.suitable && '#a2c5fd'

  element.width = width
  element.height = height
  const rect = drawRect(
    parentNode,
    width,
    height,
    borderRadius,
    strokeColor,
    fillColor,
  )
  const text = textRenderer.createText(element.businessObject.name='规则节点',{
    // type:type,
    // businessObject:businessObject,
    box: element,
    align: 'center-middle',
    padding: 5,
    size: {
      width: 100
    }
  })
  svgAppend(parentNode, text)
  return rect
}

function drawTask (parentNode, element, textRenderer, entries) {
  const width = 100
  const height = 80
  const borderRadius = 20
  const strokeColor = element.businessObject.suitable
  const fillColor = '#fff'

  element.width = width
  element.height = height
  const rect = drawRect(
    parentNode,
    width,
    height,
    borderRadius,
    strokeColor,
    fillColor
  )
  const text = textRenderer.createText(element.businessObject.name || '', {
    box: element,
    align: 'center-middle',
    padding: 5,
    size: {
      width: 100
    }
  })
  svgAppend(parentNode, text)
  return rect
}

// helpers //////////

// copied from https://github.com/bpmn-io/bpmn-js/blob/master/lib/draw/BpmnRenderer.js
function drawRect (
  parentNode,
  width,
  height,
  borderRadius,
  strokeColor,
  fillColor
) {
  const rect = svgCreate('rect')

  svgAttr(rect, {
    width: width,
    height: height,
    rx: borderRadius,
    ry: borderRadius,
    stroke: strokeColor || '#000',
    strokeWidth: 2,
    fill: fillColor
  })

  svgAppend(parentNode, rect)

  return rect
}

// copied from https://github.com/bpmn-io/diagram-js/blob/master/lib/core/GraphicsFactory.js
// function prependTo (newNode, parentNode, siblingNode) {
//   parentNode.insertBefore(newNode, siblingNode || parentNode.firstChild)
// }
