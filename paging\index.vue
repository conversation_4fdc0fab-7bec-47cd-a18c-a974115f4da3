<template>
    <div class="pagination-container">
      <a-pagination
        size="small"
        :total="total"
        :defaultPageSize="10"
        :current="currentPage"
        :pageSize="pageSize"
        :showSizeChanger="sizeChanger"
        :showQuickJumper="true"
        :pageSizeOptions="['10', '20', '50', '100', '200', '500', '1000']"
        :showTotal="(total) => `共${total}条`"
        @change="currentChange"
        @showSizeChange="sizeChange"
      />
    </div>
  </template>
  
  <script setup>
  import { defineProps, defineEmits, computed } from 'vue';
  
  const props = defineProps({
    total: {
      required: true,
      type: Number,
      default: 0,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 20,
    },
    sizeChanger: {
      type: Boolean,
      default: true,
    },
  });
  
  const emit = defineEmits(['update:page', 'update:limit', 'pagination']);
  
  const currentPage = computed({
    get() {
      return props.page;
    },
    set(val) {
      emit('update:page', val);
    },
  });
  
  const pageSize = computed({
    get() {
      return props.limit;
    },
    set(val) {
      emit('update:limit', val);
    },
  });
  
  const sizeChange = (current, pageSize) => {
    emit('update:limit', pageSize);
    currentChange(1);
  };
  
  const currentChange = (val) => {
    emit('update:page', val);
    emit('pagination', { page: val, limit: pageSize.value });
  };
  </script>
  
  <style scoped lang="scss">
  .pagination-container {
    ::v-deep {
      & {
        padding: 32px 16px;
      }
      .ant-select-selection {
        border-style: solid !important;
      }
      .ant-pagination.mini .ant-pagination-item {
        line-height: 23px !important;
        margin: 0 4px !important;
        color: #606266;
      }
      .ant-pagination-item a {
        background-color: #f4f4f5;
        color: #606266;
        min-width: 30px;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 700;
      }
      .ant-pagination-item-active a {
        background-color: #409eff !important;
        color: #fff !important;
      }
      .ant-pagination.mini .ant-pagination-prev,
      .ant-pagination.mini .ant-pagination-next {
        margin: 0 5px;
        background-color: #f4f4f5;
        min-width: 30px;
        border-radius: 2px;
        font-weight: 700;
        font-size: 12px !important;
      }
      .ant-pagination.mini .ant-pagination-next .ant-pagination-item-link {
        color: #606266 !important;
      }
      .ant-pagination-disabled a,
      .ant-pagination-disabled .ant-pagination-item-link {
        color: #c0c4cc !important;
      }
      .ant-pagination.mini .ant-pagination-total-text {
        margin-right: 10px;
        font-weight: 400;
        color: #606266;
        font-size: 12px;
        min-width: 35.5px;
        height: 28px;
        line-height: 28px;
      }
      .ant-table-pagination.ant-pagination {
        text-align: center !important;
        float: unset !important;
        // margin-bottom: 0 !important;
      }
      .ant-pagination.mini .ant-pagination-options-quick-jumper,
      .ant-pagination-options-size-changer.ant-select {
        margin: 0 5px 0 10px;
        font-weight: 400;
        color: #606266;
        font-size: 12px;
        min-width: 35.5px;
      }
      .ant-pagination.mini .ant-pagination-options-quick-jumper {
        margin-right: 0;
      }
      .ant-select-dropdown-menu-item {
        font-size: 12px;
        color: #606266;
        font-weight: 400;
      }
      .ant-select-dropdown-menu-item-selected {
        background: #f5f7fa !important;
      }
      .ant-select-dropdown-menu-item-active:not(
          .ant-select-dropdown-menu-item-disabled
        ) {
        background: #f5f7fa !important;
        color: #409eff;
        font-weight: 700;
      }
    }
  }
  </style>