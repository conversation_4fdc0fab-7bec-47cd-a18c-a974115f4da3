<!-- 新增机构条线页 -->

<script setup lang="ts">

    import { useRouter } from 'vue-router';
    import { getAllBusinessByLoginUser, getSaveInfo } from '@/api/post_permissions';
    definePageMeta({
        title: '新增机构条线'
    })
    const emit = defineEmits(['success', 'close']);

    const router = useRouter();
    const message = inject('message')
    const form = reactive({
        business: '' as string,
    });

    const menusList = ref([]);
    const checked = ref(false);
    const bin = ref(false);
    const dataval = ref({} as any);

    const postsel = () => {
        getAllBusinessByLoginUser().then((res) => {
            menusList.value = res.data;
        });
    };

    const test = (myVal: any, n: boolean) => {
        bin.value = n; // 状态
        dataval.value = myVal; // 选中的数据
    };

    const submitForm = () => {
        const uuid = dataval.value.uuid;
        if (bin.value) {
            getSaveInfo({
                businessLineId: uuid,
                level: '1',
                orgId: '00000000',
                orgName: '总公司',
            }).then((res) => {
                if (res.data === '失败，该条线已经创建过！') {
                    message.info(res.data);
                } else {
                    message.success(res.data);
                    emit('success');
                    emit('close');
                }
            });
        }
    };

    const resetForm = () => {
        emit('close');
    };

    // 暴露方法给父组件
    defineExpose({
        onSubmit: submitForm
    });

    onMounted(() => {
        postsel();
    });
</script>

<template>
    <!-- 搜索区域 -->
    <a-form  :model="form" label-width="80px" style="margin-top: 25px">
        <a-form-item label="业务条线" class="bold-label">
            <a-radio-group v-model:value="form.business" name="type">
                <a-radio
                        v-for="item in menusList"
                        :key="item.code"
                        :value="item.code"
                        @change="test(item, $event)"
                >
                    {{ item.name }}
                </a-radio>
            </a-radio-group>
        </a-form-item>
    </a-form>
</template>

<style scoped>
.bold-label :deep(.ant-form-item-label > label) {
    font-weight: bold;
}
</style>
