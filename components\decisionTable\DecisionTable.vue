<template>
  <div>
    <DecisionTableRule
      :modelData="modelInit"
      :ruleContent="ruleContent"
      :uuid="uuid"
      :ruleDrl="ruleDrl"
      :demandUuid="demandUuid"
      :ruleName="ruleInfo.ruleName"
      @handleImport="handleImport"
      @setCascaderClose="setCascaderClose"
    />
  </div>
</template>

<script setup>
import { ruleModalInit, ruleDetail } from "@/api/rule_editor";
import DecisionTableRule from "@/components/decisionTable/RuleTableEditor/decisionTableRule.vue";

// 定义props
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
});

// 定义data
const modelInit = ref({});
const ruleContent = ref({});
const uuid = ref("");
const ruleDrl = ref("");
const demandUuid = ref("");
const emit = defineEmits(["setCascaderClose"]);
// 定义watch
watch(
  () => props.ruleInfo,
  (newValue) => {
    const { engUuid, uuid: newUuid, type, jsonContentObject } = newValue;
    getData(engUuid, newUuid, type, jsonContentObject);
  },
  { deep: true }
);

// 定义methods
const getData = async (engUuid, _uuid, type, jsonContentObject) => {
  try {
    if (jsonContentObject === undefined) {
      // 正常获取已有规则数据
      const [initResult, detailResult] = await Promise.all([
        ruleModalInit(engUuid),
        ruleDetail(_uuid,type),
      ]);
      modelInit.value = initResult.data;
      
      // 安全地获取 ruleContent，处理新建规则的情况
      const ruleContentData = detailResult.data.ruleContent || {};
      const definition = ruleContentData.definition || {};
      const ruleCon = definition.conditions;
      
      let cellUnitConParams = [];
      if (ruleCon && ruleCon.length > 0) {
        for (let i = 0; i < ruleCon.length; i++) {
          const { variable, comparator } = ruleCon[i];
          if (variable && variable.variableType === "cellUnitCon") {
            const { operatorParams } = comparator || {};
            cellUnitConParams = variable.cellUnitConParams;
            ruleCon[i].comparator = variable.cellUnitConParams[0].comparator;
            ruleCon[i].variable = variable.cellUnitConParams[0].variable;
            ruleCon[i].leftValueType = variable.cellUnitConParams[0].leftValueType;
            ruleCon[i].conditionExpression = variable.cellUnitConExpression;
            ruleCon[i].cellUnit = true;
            ruleCon[i].conditions = cellUnitConParams;
            if (operatorParams && operatorParams[0] && operatorParams[0].value) {
              ruleCon[i].allCellUnit = operatorParams[0].value;
            }
          }
        }
      }
      
      ruleContent.value = ruleContentData;
      ruleDrl.value = detailResult.data.ruleDrl || "";
    } else {
      // 新建规则，使用传入的 jsonContentObject
      const initResult = await ruleModalInit(engUuid);
      modelInit.value = initResult.data;
      
      // 如果传入了 jsonContentObject，使用它
      // 如果是新建规则，确保 ruleContent 有正确的结构
      ruleContent.value = jsonContentObject || {
        definition: {
          conditions: [],
          actions: []
        },
        rows: []
      };
      ruleDrl.value = "";
    }
    
    uuid.value = _uuid;
  } catch (error) {
    console.error("获取决策表数据失败:", error);
    // 设置默认值，防止组件渲染失败
    ruleContent.value = {
      definition: {
        conditions: [],
        actions: []
      },
      rows: []
    };
    ruleDrl.value = "";
    uuid.value = _uuid;
  }
};

const handleImport = () => {
  const { engUuid, uuid, type, jsonContentObject } = props.ruleInfo;
  getData(engUuid, uuid, type, jsonContentObject);
};

// 组件的生命周期钩子
onMounted(() => {
  demandUuid.value = props.ruleInfo.demandUuid;
  const { engUuid, uuid, type, jsonContentObject } = props.ruleInfo;
  getData(engUuid, uuid, type, jsonContentObject);
});

// provide
provide('ruleUuid', props.ruleInfo.uuid);

const setCascaderClose = () => {
  emit("setCascaderClose");
};
</script>

<style scoped>
/* 添加样式 */
</style>