<!--角色新增页-->

<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import { roleAdd, roleBusin } from "@/api/role";

    const router = useRouter();
    const message = inject('message')
    interface RuleForm {
        name: string;
        describe: string;
        business: string;
        roletype: string;
        lever: string;
        flag: string;
    }

    const ruleForm = reactive<RuleForm>({
        name: '',
        describe: '',
        business: '',
        roletype: '',
        lever: '',
        flag: '',
    });

    const rules = {
        name: [
            { required: true, message: '名称不能为空', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        describe: [
            { required: true, message: '描述不能为空', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        business: [
            { required: true, message: '业务条线不能为空', trigger: 'change' }
        ],
        roletype: [
            { required: true, message: '类型不能为空', trigger: 'change' }
        ],
    };

    const menusList = ref([]);
    const strmenu = ref('');
    const options = [
        { value: '1', label: '规则管理' },
        { value: '2', label: '规则审核' },
        { value: '3', label: '系统管理' },
    ];

    const ruleFormRef = ref();

    const submitForm = (callback) => {
        ruleFormRef.value.validate().then(() => {
            const data = {
                name: ruleForm.name,
                description: ruleForm.describe,
                businessLineId: strmenu.value,
                type: ruleForm.roletype,
                flag: ruleForm.flag,
                lever: ruleForm.lever,
            };
            roleAdd(data).then((res) => {
                if (res.data === "失败，已经存在该名称的角色！") {
                    message.error(res.data);
                } else {
                    message.success('新增成功');
                    if (typeof callback === 'function') {
                        callback();
                    }
                }
            }).catch((err: any) => {
                console.log(err);
            });
        }).catch((error: any) => {
            console.log('error submit!!', error);
        });
    };

    const selectGet = (vid: string) => {
        const obj = menusList.value.find((item) => item.uuid === vid);
        if (obj) {
            strmenu.value = obj.uuid;
        }
    };

    onMounted(() => {
        roleBusin().then((res) => {
            menusList.value = res.data;
        });
    });
    defineExpose({
        submitForm,
    });
</script>
<template>
    <div id="role_add">
        <a-form :model="ruleForm" :rules="rules" ref="ruleFormRef"            label-align="right" :label-col="{ span: 4 }"
                :wrapper-col="{ span: 18 }">
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="描述" name="describe">
                <a-input autocomplete="off" v-model:value="ruleForm.describe" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="业务条线" name="business">
                <a-select v-model:value="ruleForm.business" placeholder="请选择" @change="selectGet" style="width:100%">
                    <a-select-option
                            v-for="item in menusList"
                            :key="item.uuid"
                            :value="item.uuid"
                    >
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="类型" name="roletype">
                <a-select v-model:value="ruleForm.roletype" placeholder="请选择" style="width:100%">
                    <a-select-option
                            v-for="item in options"
                            :key="item.value"
                            :value="item.value"
                    >
                        {{ item.label }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="等级" name="lever">
                <a-input autocomplete="off" v-model:value="ruleForm.lever" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="标记" name="flag">
                <a-input autocomplete="off" v-model:value="ruleForm.flag" placeholder="请输入"></a-input>
            </a-form-item>
        </a-form>
    </div>
</template>
