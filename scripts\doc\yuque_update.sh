#!/bin/bash

# 语雀文档更新脚本 (yuque_update.sh)
# 
# 功能说明：
# 此脚本用于将从语雀下载的文档组件和图片文件移动到项目相应目录中
# 实现文档的自动化更新和集成
#
# 使用方法：
# 1. 从语雀下载文档后，文件会保存在 ~/tmp 目录
# 2. 运行此脚本将文件移动到项目对应位置
# 3. 文件将按以下规则分类：
#    - DocMenu.vue 移动到 businessComponents/doc/
#    - 其他 Vue 文件移动到 pages/doc/
#    - 图片文件移动到 public/img/doc/

# 定义源目录和目标目录
SOURCE_DIR=~/tmp
PROJECT_DIR=~/erule-web-3.0

# 定义目标子目录
MENU_DIR=$PROJECT_DIR/businessComponents/doc
PAGES_DIR=$PROJECT_DIR/pages/doc
IMAGES_DIR=$PROJECT_DIR/public/img/doc

# 创建目标目录（如果不存在）
mkdir -p $MENU_DIR
mkdir -p $PAGES_DIR
mkdir -p $IMAGES_DIR

echo "开始从语雀下载的文档更新到项目中..."

# 先清空目标目录
echo "正在清空目标目录..."
# 清空文档页面目录和图片目录，保留菜单组件目录
rm -rf "$PAGES_DIR"/*
rm -rf "$IMAGES_DIR"/*

echo "✅ 目标目录清空完成"

# 移动 DocMenu.vue 到 businessComponents/doc 目录
if [ -f "$SOURCE_DIR/DocMenu.vue" ]; then
    mv "$SOURCE_DIR/DocMenu.vue" "$MENU_DIR/"
    echo "✅ 已更新菜单组件 DocMenu.vue 到 $MENU_DIR"
else
    echo "❌ 未找到菜单组件 DocMenu.vue"
fi

# 移动其他 Vue 文件到 pages/doc 目录
VUE_COUNT=0
for file in "$SOURCE_DIR"/*.vue; do
    if [ -f "$file" ] && [ "$(basename "$file")" != "DocMenu.vue" ]; then
        mv "$file" "$PAGES_DIR/"
        VUE_COUNT=$((VUE_COUNT + 1))
    fi
done

if [ $VUE_COUNT -gt 0 ]; then
    echo "✅ 已更新 $VUE_COUNT 个文档组件到 $PAGES_DIR"
else
    echo "❌ 未找到文档组件"
fi

# 移动图片文件到 public/img/doc 目录
IMG_COUNT=0
for file in "$SOURCE_DIR"/*.{jpg,jpeg,png,gif,webp,svg}; do
    if [ -f "$file" ]; then
        mv "$file" "$IMAGES_DIR/"
        IMG_COUNT=$((IMG_COUNT + 1))
    fi
done

if [ $IMG_COUNT -gt 0 ]; then
    echo "✅ 已更新 $IMG_COUNT 张图片到 $IMAGES_DIR"
else
    echo "❌ 未找到图片文件"
fi

echo "语雀文档更新完成！"
echo "文件已更新到以下目录："
echo "- 菜单组件: $MENU_DIR"
echo "- 文档组件: $PAGES_DIR"
echo "- 图片文件: $IMAGES_DIR"

echo "脚本执行完毕！" 