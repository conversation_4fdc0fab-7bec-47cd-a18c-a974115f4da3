<!-- 规则流图标组件 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 16,
    class: ''
})
</script>

<template>
    <svg :width="props.size" :height="props.size" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'icon-svg',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path d="M793.6 128a38.4 38.4 0 0 1 38.4 38.4v117.056a38.4 38.4 0 0 1-38.4 38.4H422.4a38.4 38.4 0 0 1-38.4-38.4V249.6H227.648a38.4 38.4 0 0 0-38.4 38.4V448a38.4 38.4 0 0 0 38.4 38.4l156.288-0.064v-32.192a38.4 38.4 0 0 1 38.4-38.4h179.2a38.4 38.4 0 0 1 38.4 38.4v32.192l163.328 0.064a89.6 89.6 0 0 1 89.6 89.6v160.768a89.6 89.6 0 0 1-89.6 89.6L640 826.24v30.08a38.4 38.4 0 0 1-38.4 38.4H230.4a38.4 38.4 0 0 1-38.4-38.4V742.4a38.4 38.4 0 0 1 38.4-38.4h371.2a38.4 38.4 0 0 1 38.4 38.4v32.704h163.2a38.4 38.4 0 0 0 38.4-38.4V576a38.4 38.4 0 0 0-38.4-38.4L640 537.536v33.6a38.4 38.4 0 0 1-38.4 38.4H422.4a38.4 38.4 0 0 1-38.4-38.4V537.6L227.648 537.6a89.6 89.6 0 0 1-89.6-89.6V288a89.6 89.6 0 0 1 89.6-89.6H384v-32a38.4 38.4 0 0 1 38.4-38.4h371.2zM576 768H256v62.784h320V768z m0-288.256H448v65.792h128V479.744zM768 192H448v65.792h320V192z" fill="#2c2c2c"></path>
    </svg>
</template> 