import axios from 'axios'
import defaultSettings from '@/settings'

const request = axios.create({
  baseURL: defaultSettings.AI_API,
//   timeout: 10000,
//   headers: {
    // 'Content-Type': 'application/json;charset=UTF-8',
//   },
})

// 提取关键词
export function extractKeywords(data) {
    return request({
      url: 'api/v1/rules/keywords/extract',
      method: 'post',
      data, 
    })
  }

/**
 * 转换规则
 * @param {Object} data - 转换参数
 * @param {string} data.rule_text - 规则文本，例如："如果投保单的业务员代码为空，那么设置投保单的业务员代码等于'001'"
 * @param {string} data.eng_uuid - 引擎UUID
 * @returns {Promise<{ converted_rule: string, metadata: Object }>} 返回转换结果
 */
export function convertRule(data) {
  return request({
    url: 'api/v1/rules/convert',
    method: 'post',
    data,
  })
}
// 转换规则流式输出接口
export function streamConverRule(data) {
  // 返回原始的fetch Promise，这样调用者可以处理流数据
  return fetch(`${defaultSettings.AI_API}api/v1/rules/streamConverRule`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
}

// 包装流式响应的处理逻辑，支持回调处理每个数据块
export async function handleStreamResponse(response, {
  onContent = (content) => {},
  onError = (error) => {},
  onComplete = () => {}
}) {
  try {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';
    
    // 循环读取流数据
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        onComplete();
        break;
      }
      
      // 解码二进制数据
      const text = decoder.decode(value, { stream: true });
      buffer += text;
      
      // 处理接收到的行
      const lines = buffer.split('\n');
      // 保留最后一行（可能不完整）
      buffer = lines.pop() || '';
      // 处理每一行
      for (const line of lines) {
        const trimmedLine = line;
        onContent(trimmedLine.trim());
      }
    }
  } catch (error) {
    onError(error);
  }
}

/**
 * 创建WebSocket连接
 * @param {Object} callbacks - 回调函数集合
 * @param {Function} callbacks.onOpen - 连接建立时的回调
 * @param {Function} callbacks.onMessage - 收到消息时的回调
 * @param {Function} callbacks.onClose - 连接关闭时的回调
 * @param {Function} callbacks.onError - 发生错误时的回调
 * @returns {WebSocket} WebSocket实例
 */
export function createChatWebSocket(callbacks = {}) {
  const { onOpen, onMessage, onClose, onError } = callbacks
  
  // 根据当前协议动态选择WebSocket协议
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  
  let wsUrl;
  
  // 使用配置文件中的环境变量判断
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // 开发环境直接使用硬编码地址
    wsUrl = `${protocol}//*************:8088/api/v1/ws/chat`;
  } else {
    // 生产环境使用相对路径，通过代理访问
    const wsPath = 'api/v1/ws/chat';
    // wsUrl = `${protocol}//${window.location.host}/${wsPath}`;
    wsUrl = `${protocol}//*************:8088/api/v1/ws/chat`;
  }
  
  const ws = new WebSocket(wsUrl);
  
  // 设置事件处理器
  ws.onopen = () => {
    onOpen && onOpen()
  }
  
  ws.onmessage = (event) => {
    try {
      const response = JSON.parse(event.data)
      onMessage && onMessage(response)
    } catch (error) {
      console.error('解析WebSocket消息出错:', error)
      onError && onError(error)
    }
  }
  
  ws.onclose = (event) => {
    onClose && onClose(event)
  }
  
  ws.onerror = (error) => {
    console.error('WebSocket错误:', error)
    onError && onError(error)
  }
  
  return ws
}

/**
 * 通过WebSocket发送聊天消息
 * @param {WebSocket} ws - WebSocket实例
 * @param {Object} data - 发送的数据
 * @param {string} data.message - 消息内容
 * @param {string} data.engUuid - 引擎UUID或使用data.engUuid
 * @param {string} [data.ruleId] - 规则ID (可选)
 * @param {string} [data.loginId] - 登录ID (可选)
 * @returns {boolean} 是否发送成功
 */
export function sendChatMessage(ws, data) {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    console.error('WebSocket未连接')
    return false
  }
  
  try {
    // 适配参数名称
    const adaptedData = { ...data };
    
    ws.send(JSON.stringify(adaptedData))
    return true
  } catch (error) {
    console.error('发送WebSocket消息出错:', error)
    return false
  }
}