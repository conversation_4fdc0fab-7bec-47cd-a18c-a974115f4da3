<template>
  <span :style="style" class="condition">
    <span class="LeftValueCom">
      <VariableCom
        :key="pos + '_va'"
        :pos="pos"
        :dataSource="variable"
        :signValue="''"
        :locked="locked"
        :isTrack="isTrack"
        :isLastOne="true"
        :filterType="variableType"
        :predefineLine="predefineLine"
        :preIndex="preIndex"
        @onChange="onVariableComChange"
        @onCalSignChange="changeCalculateSign"
      />
    </span>
    <!-- 渲染验证图标 -->
    <template v-if="validate === true">
      <a-tooltip :title="msg">
        <span
          class="iconfont icon-tongguo"
          style="color: green; margin-left: 20px; font-size: 16px;"
        ></span>
      </a-tooltip>
    </template>
    <template v-else-if="validate === false">
      <a-tooltip :title="msg">
        <span
          class="iconfont icon-renzhengbutongguotishi"
          style="color: red; margin-left: 20px; font-size: 16px;"
        ></span>
      </a-tooltip>
    </template>
  </span>
</template>

<script setup>
import { cloneDeep } from "lodash";
import * as util from "@/components/ruleEditCom/utils/util";

// 定义 props
const props = defineProps({
  dataSource: {
    type: Object,
    default: () => ({}),
  },
  indentLength: Number,
  fold: Boolean,
  pos: String,
  change: Function,
  locked: Boolean,
  isTrack: Boolean,
  variableType: String,
  conditionValid: {
    type: Object,
    default: () => ({}),
  },
  predefineLine: String,
  preIndex: Number,
  style: {
    type: Object,
    default: () => ({}),
  },
});

// 定义 emit
const emit = defineEmits(['conditionChange']);

// 定义响应式数据
const oldVari = ref(null);
const variable = ref(null);
const validate = ref(null);
const msg = ref(null);

const init=()=>{
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable: initialVariable, conditionValid: initialConditionValid={} } = contents;
  if (oldVari.value === null) {
    oldVari.value = cloneDeep(initialVariable);
  }
  variable.value = initialVariable;
  validate.value = initialConditionValid.validate;
  msg.value = initialConditionValid.msg;
}

// 监听 dataSource 的变化
watch(
  () => props,
  (newVal) => {
    if (newVal) {
      init(false);
    }
  },
  { deep: true, immediate: true }
);

// 生命周期钩子
onMounted(() => {
  init()
});

// 方法
const onVariableComChange = (pos, newVariableData, conditionValueType) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator } = contents;
  const isBool = conditionValueType === "Boolean";
  const { dictName } = newVariableData;
  const domain = util.getRealDomain(newVariableData);

  if (isBool) {
    if (newVariableData.next) {
      newVariableData.next.domain = "boolean";
    }
  }

  const newContents = {
    variable: newVariableData,
    conditionValueType: conditionValueType,
  };

  let comparatorData = null;

  if (isBool) {
    comparatorData = {
      operatorName: "",
      enumDictName: "boolean",
    };
  } else if (dictName || domain) {
    comparatorData = {
      operatorName: "",
      enumDictName: dictName || domain || null,
    };
  } else {
    comparatorData = {
      operatorName: "",
    };
  }

  let aOldValue = null;
  let aNewValue = null;
  let comparaRes = null;

  if (oldVari.value) {
    getLastOV(oldVari.value);
    getLastNV(newVariableData);
    comparaRes = getComparaRes(aOldValue, aNewValue);
  }

  function getLastNV(data) {
    if (data.next) {
      getLastNV(data.next);
    } else {
      aNewValue = data.value;
    }
  }

  function getLastOV(data) {
    if (data.next) {
      getLastOV(data.next);
    } else {
      aOldValue = data.value;
    }
  }

  if (comparator && comparator.value && comparaRes) {
    newContents.comparator = comparator;
  } else {
    oldVari.value = cloneDeep(newVariableData);
    newContents.comparator = comparatorData;
  }

  emit("conditionChange", pos, newContents);
};

const getComparaRes = (oVal, nVal) => {
  let bRes = false;
  const aO = oVal;
  const aN = nVal;
  const aR = [];

  if (aO && aN) {
    if (aO.length !== aN.length) {
      bRes = false;
    } else {
      for (let i = 0; i < aO.length; i++) {
        if (aO[i] === aN[i]) {
          aR.push(true);
        } else {
          aR.push(false);
        }
      }
      if (aR.includes(false)) {
        bRes = false;
      } else {
        bRes = true;
      }
    }
  }
  return bRes;
};

const changeCalculateSign = (pos, sign, newParamItem) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator, conditionValueType } = contents;

  const targetOption = variable;
  const valueType = targetOption._getRealValueType();

  if (sign === "delete" || !newParamItem) {
    return;
  }

  const newVariable = {
    valueType: valueType,
    variableType: "expression",
    expressionTreeData: {
      type: "expression",
      symbols: [sign],
      params: [
        {
          type: "variable",
          data: {
            ...targetOption,
          },
        },
        newParamItem,
      ],
    },
  };

  const newComparator = comparator || { operatorName: "" };
  const _newVariable = targetOption._derive(newVariable, {
    owner: targetOption._getOwner(),
    isRootVar: true,
  });
  const _newComparator = targetOption._derive(newComparator, {
    owner: targetOption._getOwner(),
  });

  const newContents = {
    variable: _newVariable,
    conditionValueType: conditionValueType,
    comparator: _newComparator,
  };

  emit("conditionChange", pos, newContents);
};
</script>
