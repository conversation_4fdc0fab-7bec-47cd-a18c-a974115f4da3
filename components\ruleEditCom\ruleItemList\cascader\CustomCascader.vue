<!--
  @description 自定义级联选择器组件
  
  @features
  - 支持多级数据的级联选择
  - 支持模糊搜索，可通过空格分隔多个关键词
  - 支持键盘上下键选择和回车确认
  - 支持按一级、二级、三级分类筛选搜索结果
  - 支持虚拟滚动和分批加载
  - 支持运算符节点的自动添加
  
  @props
  - options: Array    级联选择器的选项数据
  - acTop: String     级联选择器的垂直位置
  - acLeft: String    级联选择器的水平位置
  - open: Boolean     控制级联选择器是否显示
  - ruleUuid: String  规则的唯一标识
  
  @events
  - onSelectChange(path: Array, nodes: Array) 选择变更时触发
    - path: 选中项的值路径
    - nodes: 选中项的节点路径
  
  @example
  <CustomCascader
    :options="cascaderOptions"
    :acTop="'100px'"
    :acLeft="'200px'"
    :open="isOpen"
    :ruleUuid="currentRuleUuid"
    @onSelectChange="handleSelectChange"
  />
-->

<template>
  <div class="custom-cascader-container" :style="{
    top: computedTop,
    left: computedLeft,
    '--computed-top': computedTop
  }" :class="{ 'popup-upward': isPopupUpward }" ref="cascaderContainer" v-if="open" tabindex="0">
    <!-- 输入框部分 -->
    <div class="custom-cascader-input" @click.stop="toggleDropdown" style="width: 100%;">
      <input ref="searchInput" type="text" :placeholder="getPlaceholder" v-model="searchValue"
        @input="handleSearchInput" @click.stop @focus="handleFocus" @keydown="handleKeyDown" 
        @blur="handleBlur" style="width: 100%;" />
      <span class="selected-value" v-if="displayLabel && !showDropdown && !isSearchMode">{{ displayLabel }}</span>
      <!-- 没有输入文本时显示搜索图标 -->
      <span class="search-icon" v-if="(showDropdown || (!selectedOptions.length && !isSearchMode)) && !searchValue"
        @click.stop>
        <IconSearch />
      </span>
      <!-- 有输入文本时显示清除文本图标 -->
      <span class="search-icon" v-if="searchValue" @click.stop="clearSearchText">
        <IconClean />
      </span>
    </div>

    <!-- 下拉菜单部分 -->
    <div class="custom-cascader-dropdown" v-if="showDropdown" ref="dropdown">
      <!-- 搜索结果 -->
      <div class="search-results" v-if="isSearchMode && filteredOptions.length">
        <!-- 一级节点过滤器 -->
        <div class="root-node-filters" v-if="matchedRootNodes.length > 1">
          <div class="filter-tags">
            <span v-for="node in matchedRootNodes" :key="node.value"
              :class="['filter-tag', { active: activeRootFilter === node.value }]" @click.stop="filterByRootNode(node)">
              {{ node.label }} ({{ getRootNodeCount(node) }})
            </span>
            <span class="filter-tag" :class="{ active: activeRootFilter === null }" @click.stop="resetRootFilter">
              全部 ({{ allMatchedOptions.length }})
            </span>
          </div>

          <!-- 二级节点过滤器 -->
          <div class="second-level-tags" v-if="showSecondLevelTags && matchedSecondLevelNodes.length">
            <div class="filter-tags second-level">
              <span v-for="node in matchedSecondLevelNodes" :key="node.value"
                :class="['filter-tag', { active: activeSecondLevelFilter === node.value }]"
                @click.stop="filterBySecondLevelNode(node)">
                {{ node.label }} ({{ getSecondLevelNodeCount(node) }})
              </span>
            </div>

            <!-- 三级节点过滤器 -->
            <div class="third-level-tags" v-if="showThirdLevelTags && matchedThirdLevelNodes.length">
              <div class="filter-tags third-level">
                <span v-for="node in matchedThirdLevelNodes" :key="node.value"
                  :class="['filter-tag', { active: activeThirdLevelFilter === node.value }]"
                  @click.stop="filterByThirdLevelNode(node)">
                  {{ node.label }} ({{ getThirdLevelNodeCount(node) }})
                </span>
              </div>
            </div>
          </div>
        </div>
        <!-- 结果数量提示 -->
        <div class="result-info" v-if="searchLimitExceeded">
          <div class="result-limit-warning">
            只显示前 100 条最匹配的结果，请输入更精确的关键词缩小范围
          </div>
        </div>
        <!-- 搜索结果列表 -->
        <ul class="search-list">
          <li v-for="(option, index) in displayedOptions" :key="index"
            @click="selectSearchOption(option.path, option.nodes)" class="search-item"
            :class="{ 'search-item-active': activeIndex === index }" :title="option.displayPath.join(' / ')">
            <span v-for="(pathItem, pIndex) in option.displayPath" :key="pIndex">
              <span v-html="highlightText(pathItem)"></span>
              <span v-if="pIndex < option.displayPath.length - 1" class="path-separator"> / </span>
            </span>
          </li>
          <!-- 加载更多按钮 -->
          <li v-if="displayedOptions.length < filteredOptions.length" class="load-more-item"
            @click.stop="loadMoreResults">
            点击加载更多结果 (已显示 {{ displayedOptions.length }}/{{ filteredOptions.length }})
          </li>
        </ul>
      </div>
      <!-- 正在搜索提示 -->
      <div class="loading-result" v-else-if="isSearching">
        <span>正在搜索...</span>
      </div>
      <!-- 最小字符提示 -->
      <div class="min-chars-hint" v-else-if="searchValue.length > 0 && searchValue.length < minSearchChars">
        <span>请输入至少 {{ minSearchChars }} 个字符进行搜索</span>
      </div>
      <!-- 空搜索结果提示 -->
      <div class="empty-result" v-else-if="isSearchMode && debouncedSearchValue.value && hasNoData">
        <span>无匹配结果</span>
      </div>
      <!-- 正在扁平化数据提示 -->
      <div class="loading-result" v-else-if="isSearchMode && debouncedSearchValue.value && isFlatteningOptions">
        <span>正在处理数据...</span>
      </div>
      <!-- 常规级联菜单 -->
      <div class="menus-container" v-else>
        <div class="menu-column" v-for="(level, levelIndex) in activeLevels" :key="levelIndex">
          <ul class="menu-list">
            <li v-for="option in level" :key="option.value" @click="selectOption(option, levelIndex)"
              @mouseenter="handleOptionHover(option, levelIndex)" :class="{
                'menu-item': true,
                'menu-item-active': isActiveOption(option, levelIndex),
                'menu-item-selected': isSelectedOption(option, levelIndex)
              }" :title="option.label">
              <span class="option-label">{{ option.label }}</span>
              <span class="expand-icon" v-if="option.children && option.children.length">
                <IconRightArrow />
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import store from "@/store";
import { debounce } from 'lodash-es';
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";

// 定义组件的 props
const props = defineProps({
  options: Array, // 级联选择器的选项数据
  acTop: String, // 级联选择器的 top 位置
  acLeft: String, // 级联选择器的 left 位置
  open: Boolean, // 控制级联选择器是否显示
  ruleUuid: String, // 规则的 uuid
});

// 定义组件的 emits
const emit = defineEmits(["onSelectChange"]);

// 内部状态
const searchInput = ref(null);
const cascaderContainer = ref(null);
const dropdown = ref(null);
const showDropdown = ref(false);
const searchValue = ref('');
const debouncedSearchValue = ref(''); // 新增：用于防抖后的搜索值
const isSearchMode = ref(false);
const isSearching = ref(false); // 新增：标记是否正在搜索中
const searchLimitExceeded = ref(false); // 新增：标记是否超出搜索限制
const minSearchChars = 2; // 新增：最小搜索字符数
const selectedOptions = ref([]);
const selectedValues = ref([]);
const activeLevels = ref([]);
const activeOptions = ref([]);
const activeIndex = ref(-1); // 跟踪当前键盘选中的搜索项索引

// 新增：智能定位相关状态
const isPopupUpward = ref(false); // 是否向上弹出
const computedTop = ref(''); // 计算后的top位置
const computedLeft = ref(''); // 计算后的left位置
// 移除智能水平滚动相关状态
// const initialScrollLeft = ref(0); // 初始滚动位置

// 使用记忆化的扁平选项缓存
const flattenedOptionsCache = ref(null);
// 添加扁平化处理状态
const isFlatteningOptions = ref(false);
// 添加无数据状态
const hasNoData = ref(false);

// 获取运算符映射
const operatorMap = computed(() => {
  try {
    const operatorTypeList = store.getters.listMap[props.ruleUuid]?.initModelData?.operatorTypeList || {};
    return operatorTypeList;
  } catch (error) {
    console.error('Error getting operatorMap:', error);
    return {};
  }
});

// 新增：智能定位功能（禁用水平滚动）
const calculateOptimalPosition = () => {
  if (!props.acTop) {
    computedTop.value = '0px';
    isPopupUpward.value = false;
    return;
  }

  const topValue = parseInt(props.acTop.replace('px', '')) || 0;
  const viewportHeight = window.innerHeight;
  const scrollTop = window.scrollY || document.documentElement.scrollTop;
  
  // 始终按照最大高度340px进行计算，因为即使初始渲染小于340px，
  // 当展开子项时也会撑开到340px
  // 340px = 下拉菜单290px + 输入框32px + 间距，已包含所有必要高度
  const estimatedDropdownHeight = 340;
  
  // 计算垂直方向的可用空间
  const absoluteTop = topValue - scrollTop;
  const availableBottomSpace = viewportHeight - absoluteTop;
  const availableTopSpace = absoluteTop;
  
  // 判断垂直方向：如果底部空间不足，且上方有足够空间，则向上弹出
  if (availableBottomSpace < estimatedDropdownHeight && availableTopSpace > estimatedDropdownHeight) {
    isPopupUpward.value = true;
    computedTop.value = `${topValue - 18}px`;
  } else {
    isPopupUpward.value = false;
    computedTop.value = props.acTop;
  }
  
  // 直接设置左侧位置，不进行水平滚动处理
  computedLeft.value = props.acLeft;
  
  // 移除智能水平滚动逻辑
  // const leftValue = parseInt(props.acLeft.replace('px', '')) || 0;
  // const ruleBodyElement = document.querySelector('.ruleBody');
  // const scrollLeft = ruleBodyElement ? ruleBodyElement.scrollLeft : (window.scrollX || document.documentElement.scrollLeft);
  // const viewportWidth = ruleBodyElement ? ruleBodyElement.clientWidth : window.innerWidth;
  // const estimatedDropdownWidth = 540; // 3列 × 180px
  // initialScrollLeft.value = scrollLeft;
  // const absoluteLeft = leftValue - scrollLeft;
  // const rightBoundary = absoluteLeft + estimatedDropdownWidth;
  // if (rightBoundary > viewportWidth) {
  //   // 水平滚动逻辑已禁用
  // }
};

// 监听位置变化，重新计算最优位置
watch(() => [props.acTop, props.acLeft, props.open], () => {
  if (props.open) {
    nextTick(() => {
      calculateOptimalPosition();
    });
  }
}, { immediate: true });

// 监听下拉菜单显示状态，重新计算位置（获得实际高度后）
watch(() => showDropdown.value, (isVisible) => {
  if (isVisible && props.open) {
    // 延迟一点时间确保DOM完全渲染
    setTimeout(() => {
      calculateOptimalPosition();
    }, 50);
    // 再次延迟检查滚动，确保DOM完全渲染
    setTimeout(() => {
      calculateOptimalPosition();
    }, 200);
  }
});

// 移除智能水平滚动位置更新函数
// const updatePositionOnScroll = () => {
//   if (!props.open) return;
//   
//   const ruleBodyElement = document.querySelector('.ruleBody');
//   if (ruleBodyElement) {
//     const currentScrollLeft = ruleBodyElement.scrollLeft;
//     const scrollDelta = currentScrollLeft - initialScrollLeft.value;
//     
//     // 更新计算后的left位置，减去滚动距离以保持相对静止
//     const originalLeft = parseInt(props.acLeft.replace('px', '')) || 0;
//     computedLeft.value = `${originalLeft - scrollDelta}px`;
//   }
// };

// 监听窗口大小变化，重新计算位置（移除水平滚动相关监听）
onMounted(() => {
  window.addEventListener('resize', calculateOptimalPosition);
  window.addEventListener('scroll', calculateOptimalPosition);
  
  // 移除.ruleBody的滚动事件监听
  // const ruleBodyElement = document.querySelector('.ruleBody');
  // if (ruleBodyElement) {
  //   ruleBodyElement.addEventListener('scroll', updatePositionOnScroll);
  // }
});

onUnmounted(() => {
  window.removeEventListener('resize', calculateOptimalPosition);
  window.removeEventListener('scroll', calculateOptimalPosition);
  
  // 移除.ruleBody的滚动事件监听相关代码
  // const ruleBodyElement = document.querySelector('.ruleBody');
  // if (ruleBodyElement) {
  //   ruleBodyElement.removeEventListener('scroll', updatePositionOnScroll);
  // }
});

// 初始化时设置第一级菜单
watch(() => props.options, (newOptions) => {
  if (newOptions && newOptions.length) {
    activeLevels.value = [newOptions];
  }
}, { immediate: true });

// 监听open属性变化
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    showDropdown.value = true;
    // 始终重置为初始状态，不再根据选中值初始化
    activeLevels.value = [props.options];
    activeOptions.value = [];
    searchValue.value = '';
    debouncedSearchValue.value = ''; // 同时重置防抖搜索值
    isSearchMode.value = false;
    searchLimitExceeded.value = false; // 重置超出限制标记
    activeIndex.value = -1; // 重置键盘选中索引
    isFlatteningOptions.value = false; // 重置扁平化状态
    hasNoData.value = false; // 重置无数据状态

    // 重置过滤状态
    activeRootFilter.value = null;
    showSecondLevelTags.value = false;
    selectedRootNode.value = null;
    activeSecondLevelFilter.value = null;
    showThirdLevelTags.value = false;
    selectedSecondNode.value = null;
    activeThirdLevelFilter.value = null;

    // 计算最优位置
    nextTick(() => {
      calculateOptimalPosition();
      
      // 移除智能水平滚动监听器相关代码
      // const ruleBodyElement = document.querySelector('.ruleBody');
      // if (ruleBodyElement) {
      //   ruleBodyElement.removeEventListener('scroll', updatePositionOnScroll);
      //   ruleBodyElement.addEventListener('scroll', updatePositionOnScroll);
      // }
      
      if (searchInput.value) {
        searchInput.value.focus();
      }
    });
  } else {
    showDropdown.value = false;
    searchValue.value = '';
    debouncedSearchValue.value = ''; // 同时重置防抖搜索值
    isSearchMode.value = false;
    searchLimitExceeded.value = false; // 重置超出限制标记
    isFlatteningOptions.value = false; // 重置扁平化状态
    hasNoData.value = false; // 重置无数据状态

    // 重置过滤状态
    activeRootFilter.value = null;
    showSecondLevelTags.value = false;
    selectedRootNode.value = null;
    activeSecondLevelFilter.value = null;
    showThirdLevelTags.value = false;
    selectedSecondNode.value = null;
    activeThirdLevelFilter.value = null;
    
    // 移除智能水平滚动监听器相关代码
    // const ruleBodyElement = document.querySelector('.ruleBody');
    // if (ruleBodyElement) {
    //   ruleBodyElement.removeEventListener('scroll', updatePositionOnScroll);
    // }
  }
});

// 切换下拉菜单显示状态
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;

  // 如果关闭下拉框，重置搜索和过滤状态
  if (!showDropdown.value) {
    searchValue.value = '';
    debouncedSearchValue.value = '';
    isSearchMode.value = false;
    searchLimitExceeded.value = false;

    // 重置过滤状态
    activeRootFilter.value = null;
    showSecondLevelTags.value = false;
    selectedRootNode.value = null;
    activeSecondLevelFilter.value = null;
    showThirdLevelTags.value = false;
    selectedSecondNode.value = null;
    activeThirdLevelFilter.value = null;
  }
};

// 处理搜索输入，使用防抖优化
const handleSearchInput = debounce(() => {
  // 删除首尾空格并计算有效文本
  const trimmedValue = searchValue.value.trim();
  
  // 检查是否全是空格或输入字符太少
  if (trimmedValue.length < minSearchChars) {
    debouncedSearchValue.value = '';
    isSearchMode.value = false;
    searchLimitExceeded.value = false;
    return;
  }
  
  // 如果上一次搜索值去掉空格与当前值去掉空格相同，则不触发新搜索
  // 这样多个关键词之间输入空格不会触发新的搜索
  if (debouncedSearchValue.value.trim() === trimmedValue) {
    return;
  }

  debouncedSearchValue.value = searchValue.value; // 更新防抖后的搜索值
  isSearchMode.value = true;
  // 重置键盘选中索引
  activeIndex.value = -1;

  // 标记为搜索中状态
  isSearching.value = true;
  searchLimitExceeded.value = false;
  
  // 如果缓存不存在，手动触发扁平化数据构建
  if (!flattenedOptionsCache.value) {
    // flattenOptions会自动设置isFlatteningOptions状态
    flattenOptions(props.options);
  }
}, 300);

// 处理输入框获得焦点
const handleFocus = () => {
  showDropdown.value = true;
};

// 计算placeholder的显示内容
const getPlaceholder = computed(() => {
  // 当已有选择值且没有处于搜索模式且下拉框没显示时，不显示placeholder
  if (displayLabel.value && !isSearchMode.value && !showDropdown.value) {
    return '';
  }
  return '多个关键词用空格分隔';
});

// 选择搜索结果中的选项
const selectSearchOption = (path, nodes) => {
  selectedValues.value = path;
  selectedOptions.value = nodes;
  searchValue.value = '';
  debouncedSearchValue.value = ''; // 同时重置防抖搜索值
  isSearchMode.value = false;
  initActiveLevelsFromSelection();
  showDropdown.value = false;
  emit("onSelectChange", path, nodes);
};

// 选择常规级联菜单中的选项
const selectOption = (option, levelIndex) => {
  try {
    // 更新当前级别的激活选项
    activeOptions.value[levelIndex] = option;

    // 移除后续级别的激活选项和菜单
    activeOptions.value = activeOptions.value.slice(0, levelIndex + 1);
    activeLevels.value = activeLevels.value.slice(0, levelIndex + 1);

    // 构建路径和节点列表（无论是否为叶子节点）
    const path = [];
    const nodes = [];

    for (let i = 0; i <= levelIndex; i++) {
      const activeOption = activeOptions.value[i];
      path.push(activeOption.value);
      nodes.push(activeOption);
    }

    // 更新选中的值和选项
    selectedValues.value = path;
    selectedOptions.value = nodes;

    // 触发选择变更事件
    emit("onSelectChange", path, nodes);

    // 如果有子选项，显示下一级菜单
    if (option.children && option.children.length) {
      activeLevels.value.push(option.children);
    } else {
      // 只有叶子节点才关闭下拉菜单
      showDropdown.value = false;
    }
  } catch (error) {
    console.error('Error in selectOption:', error);
  }
};

// 根据已选值初始化激活的菜单层级
const initActiveLevelsFromSelection = () => {
  try {
    if (!selectedValues.value.length) return;

    const levels = [props.options];
    const activeOpts = [];

    let currentOptions = props.options;

    for (let i = 0; i < selectedValues.value.length; i++) {
      const value = selectedValues.value[i];
      const foundOption = currentOptions.find(opt => opt.value === value);

      if (foundOption) {
        activeOpts.push(foundOption);

        if (foundOption.children && foundOption.children.length) {
          levels.push(foundOption.children);
          currentOptions = foundOption.children;
        }
      } else {
        break;
      }
    }

    activeLevels.value = levels;
    activeOptions.value = activeOpts;
  } catch (error) {
    console.error('Error in initActiveLevelsFromSelection:', error);
  }
};

// 判断选项是否为激活状态
const isActiveOption = (option, levelIndex) => {
  return activeOptions.value[levelIndex] && activeOptions.value[levelIndex].value === option.value;
};

// 判断选项是否为已选状态
const isSelectedOption = (option, levelIndex) => {
  return selectedValues.value[levelIndex] === option.value;
};

// 生成用于显示的选中路径标签
const displayLabel = computed(() => {
  if (!selectedOptions.value.length) return '';
  return selectedOptions.value.map(opt => opt.label).join(' / ');
});

// 执行搜索并返回匹配的选项，使用异步处理优化
const flattenOptions = (options, path = [], nodes = [], result = [], isRootCall = true) => {
  // 返回Promise用于异步处理
  return new Promise((resolve) => {
    try {
      // 如果已经有缓存且传入的是根选项，则直接返回缓存
      if (flattenedOptionsCache.value && options === props.options && !path.length) {
        resolve(flattenedOptionsCache.value);
        return;
      }
      
      // 设置状态为正在处理
      if (isRootCall) {
        isFlatteningOptions.value = true;
        hasNoData.value = false;
      }
      
      // 异步处理选项数据
      setTimeout(() => {
        // 预先创建所有必要数据，避免在setTimeout内部多次创建临时数组
        const optionsBatch = [];
        
        options.forEach(option => {
          const currentPath = [...path, option.value];
          const currentNodes = [...nodes, option];
          const displayPath = [...nodes.map(n => n.label), option.label];
          
          // 添加原始选项
          result.push({
            path: currentPath,
            nodes: currentNodes,
            displayPath,
            // 预先计算小写的displayPath用于搜索
            lowercaseDisplayPath: displayPath.map(text => String(text).toLowerCase())
          });
          
          // 如果当前节点不是运算符节点，则添加运算符
          if (!option.isOperator) {
            // 获取当前选项的类型，如果没有则使用'unknown'
            const optionType = option.valueType || option.paramType || 'unknown';
            
            // 检查是否有与该类型匹配的运算符
            if (operatorMap.value) {
              let operators = null;
              
              // 先尝试直接匹配类型
              if (operatorMap.value[optionType]) {
                operators = operatorMap.value[optionType];
              }
              // 如果匹配不到，再尝试特殊类型的匹配
              else if (!operators && optionType.includes('.') && optionType.includes('List<')) {
                // 对于List类型的对象，使用List类型的运算符
                operators = operatorMap.value['List'];
                
                // 如果是列表类型，需要过滤掉数组相关的操作符
                if (operators && operators.length > 0) {
                  operators = operators.filter(item => {
                    // 过滤掉含有"数组"关键字的操作符
                    return !item.label.includes('<数组>') && !item.paramTypes?.includes('Array');
                  });
                }
              }
              else if (!operators && optionType.includes('.')) {
                // 对于普通对象类型，使用Object类型的运算符
                operators = operatorMap.value['Object'];
              }
              
              // 为每个运算符创建新的选项
              if (Array.isArray(operators)) {
                operators.forEach(op => {
                  if (typeof op === 'object' && op !== null) {
                    const operatorPath = [...currentPath, op.value];
                    
                    // 直接使用operatorMap中的原始数据，只添加isOperator标识
                    const operatorNode = {
                      ...op,
                      isOperator: true
                    };
                    
                    const operatorNodes = [...currentNodes, operatorNode];
                    const operatorDisplayPath = [...displayPath, op.label];
                    
                    result.push({
                      path: operatorPath,
                      nodes: operatorNodes,
                      displayPath: operatorDisplayPath,
                      lowercaseDisplayPath: operatorDisplayPath.map(text => String(text).toLowerCase()),
                      isOperator: true
                    });
                  }
                });
              }
            }
          }
          
          // 收集所有需要递归处理的子选项
          if (option.children && option.children.length) {
            optionsBatch.push({
              options: option.children,
              path: currentPath,
              nodes: currentNodes
            });
          }
        });
        
        // 异步处理子选项
        if (optionsBatch.length > 0) {
          // 使用Promise.all处理所有子选项
          const promises = optionsBatch.map(batch => 
            flattenOptions(batch.options, batch.path, batch.nodes, result, false)
          );
          
          Promise.all(promises).then(() => {
            // 如果是根调用，则缓存结果并更新状态
            if (isRootCall) {
              flattenedOptionsCache.value = result;
              isFlatteningOptions.value = false;
              hasNoData.value = result.length === 0;
            }
            resolve(result);
          });
        } else {
          // 没有子选项，直接完成
          if (isRootCall) {
            flattenedOptionsCache.value = result;
            isFlatteningOptions.value = false;
            hasNoData.value = result.length === 0;
          }
          resolve(result);
        }
      }, 0); // 使用setTimeout实现异步处理
    } catch (error) {
      console.error('Error in flattenOptions:', error);
      if (isRootCall) {
        isFlatteningOptions.value = false;
        hasNoData.value = true;
      }
      resolve([]);
    }
  });
};

// 监听options变化时清除缓存，使用返回值比较而非深度监听，并添加防抖
watch(() => {
  // 只返回结构信息，避免监听内部变化
  if (!props.options) return null;

  // 返回一个唯一标识符，仅在选项结构变化时才会改变
  return props.options.map(opt => {
    if (opt && typeof opt === 'object') {
      return opt.value || opt.id || JSON.stringify(Object.keys(opt));
    }
    return String(opt);
  }).join('|');
}, (newVal, oldVal) => {
  // 如果值相同或为初始化（oldVal为undefined），不执行操作
  if (newVal === oldVal || oldVal === undefined) return;

  // 设置一个短暂的延迟，避免频繁触发，只清空缓存，不主动重建
  setTimeout(() => {
    flattenedOptionsCache.value = null;
  }, 50);
});

// 监听ruleUuid变化或operatorMap变化时清除缓存
watchEffect(() => {
  // 监听ruleUuid变化
  const currentRuleUuid = props.ruleUuid;
  const currentOperatorMap = operatorMap.value;

  // 当ruleUuid或operatorMap变化时，只清除缓存，不主动重建
  if (currentRuleUuid || currentOperatorMap) {
    flattenedOptionsCache.value = null;
  }
});

// 添加一级节点过滤功能
const activeRootFilter = ref(null);

// 添加二级标签功能
const activeSecondLevelFilter = ref(null);
const showSecondLevelTags = ref(false);
const selectedRootNode = ref(null);

// 添加三级标签功能
const activeThirdLevelFilter = ref(null);
const showThirdLevelTags = ref(false);
const selectedSecondNode = ref(null);

// 根据一级节点过滤搜索结果
const filterByRootNode = (node) => {
  if (activeRootFilter.value === node.value) {
    // 如果点击的是已选中的节点，则切换二级标签的显示状态
    showSecondLevelTags.value = !showSecondLevelTags.value;
    if (!showSecondLevelTags.value) {
      // 如果关闭二级标签，同时关闭三级标签
      showThirdLevelTags.value = false;
      selectedSecondNode.value = null;
      activeThirdLevelFilter.value = null;
    }
  } else {
    // 选择新的一级节点
    activeRootFilter.value = node.value;
    selectedRootNode.value = node;
    showSecondLevelTags.value = true; // 显示二级标签
    activeSecondLevelFilter.value = null; // 重置二级过滤
    showThirdLevelTags.value = false; // 隐藏三级标签
    selectedSecondNode.value = null; // 重置选中的二级节点
    activeThirdLevelFilter.value = null; // 重置三级过滤
  }
};

// 重置一级节点过滤时也重置二级和三级节点过滤
const resetRootFilter = () => {
  activeRootFilter.value = null;
  showSecondLevelTags.value = false;
  selectedRootNode.value = null;
  activeSecondLevelFilter.value = null;
  showThirdLevelTags.value = false;
  selectedSecondNode.value = null;
  activeThirdLevelFilter.value = null;
};

// 根据二级节点过滤搜索结果
const filterBySecondLevelNode = (node) => {
  if (activeSecondLevelFilter.value === node.value) {
    // 如果点击的是已选中的节点，则切换三级标签的显示状态
    showThirdLevelTags.value = !showThirdLevelTags.value;
    if (!showThirdLevelTags.value) {
      // 如果关闭三级标签，同时重置三级过滤
      activeThirdLevelFilter.value = null;
    }
  } else {
    activeSecondLevelFilter.value = node.value;
    selectedSecondNode.value = node;
    showThirdLevelTags.value = true; // 显示三级标签
    activeThirdLevelFilter.value = null; // 重置三级过滤
  }
};

// 根据三级节点过滤搜索结果
const filterByThirdLevelNode = (node) => {
  if (activeThirdLevelFilter.value === node.value) {
    activeThirdLevelFilter.value = null; // 再次点击相同标签则取消过滤
  } else {
    activeThirdLevelFilter.value = node.value;
  }
};

// 添加性能优化的搜索方法
const performSearch = (options, keywords) => {
  // 搜索结果数量限制
  const MAX_SEARCH_RESULTS = 100;
  let matchCount = 0;
  let limitExceeded = false;

  // 使用更高效的方式累积结果
  const result = [];

  // 优先精确匹配
  const exactMatches = [];
  const partialMatches = [];

  // 遍历所有选项
  for (let i = 0; i < options.length; i++) {
    const option = options[i];

    // 计算匹配分数
    const matchScore = getSearchMatchScore(option, keywords);

    // 根据分数决定是精确匹配还是部分匹配
    if (matchScore > 50) {
      exactMatches.push(option);
    } else if (matchScore > 0) {
      partialMatches.push(option);
    }

    // 检查是否超出数量限制
    matchCount = exactMatches.length + partialMatches.length;
    if (matchCount >= MAX_SEARCH_RESULTS) {
      limitExceeded = true;
      break;
    }
  }

  // 添加精确匹配结果
  result.push(...exactMatches);

  // 如果还有空间，添加部分匹配结果
  const remainingSlots = MAX_SEARCH_RESULTS - exactMatches.length;
  if (remainingSlots > 0 && partialMatches.length > 0) {
    result.push(...partialMatches.slice(0, remainingSlots));
  }

  return {
    results: result,
    limitExceeded: limitExceeded
  };
};

// 计算搜索匹配分数 - 更高效的版本
const getSearchMatchScore = (option, keywords) => {
  let score = 0;

  // 最后一级标签的匹配权重更高
  const lastIndex = option.lowercaseDisplayPath.length - 1;
  const lastLevelLabel = option.lowercaseDisplayPath[lastIndex];

  // 路径中的每一级都检查，确保所有关键词都匹配
  for (let k = 0; k < keywords.length; k++) {
    const keyword = keywords[k];
    let keywordMatched = false;

    // 先检查最后一级（权重最高）
    if (lastLevelLabel === keyword) {
      score += 100;
      keywordMatched = true;
    } else if (lastLevelLabel.startsWith(keyword)) {
      score += 50;
      keywordMatched = true;
    } else if (lastLevelLabel.includes(keyword)) {
      score += 20;
      keywordMatched = true;
    }

    // 如果在最后一级没找到，检查其他级别
    if (!keywordMatched) {
      for (let i = 0; i < lastIndex; i++) {
        if (option.lowercaseDisplayPath[i].includes(keyword)) {
          score += 10;
          keywordMatched = true;
          break; // 每个关键词在每个路径级别只计分一次
        }
      }
    }

    // 如果有关键词完全没匹配到，整体分数为0，确保多关键词AND搜索
    if (!keywordMatched) {
      return 0;
    }
  }

  return score;
};

// 修改 allMatchedOptions 计算属性，以使用防抖后的搜索值并支持异步加载
const allMatchedOptions = ref([]);

// 当搜索值变化时，触发异步搜索
watch(() => debouncedSearchValue.value, async (searchValue) => {
  if (!searchValue) {
    allMatchedOptions.value = [];
    isSearching.value = false;
    hasNoData.value = false;
    return;
  }
  
  // 处理搜索值，去除多余空格，并检查有效字符长度
  const trimmedValue = searchValue.trim();
  if (trimmedValue.length < minSearchChars) {
    allMatchedOptions.value = [];
    isSearching.value = false;
    hasNoData.value = false;
    return;
  }
  
  // 设置搜索状态
  isSearching.value = true;
  hasNoData.value = false;
  searchLimitExceeded.value = false;
  
  try {
    // 触发扁平化处理，如果缓存不存在则会生成
    const allOptions = await flattenOptions(props.options);
    
    // 如果在处理过程中搜索值已变化，则放弃当前处理结果
    if (searchValue !== debouncedSearchValue.value) {
      return;
    }
    
    // 提取关键词，过滤掉空字符串
    const keywords = trimmedValue.toLowerCase().split(/\s+/).filter(Boolean);
    if (keywords.length === 0) {
      allMatchedOptions.value = [];
      isSearching.value = false;
      hasNoData.value = true;
      return;
    }
    
    // 先进行完整过滤，但限制处理的数量
    const MAX_SEARCH_RESULTS = 100; // 搜索结果数量限制
    
    // 使用异步处理搜索，避免阻塞UI线程
    setTimeout(() => {
      // 使用every和some组合，确保每个关键词都能匹配到路径中的某项
      const matched = [];
      let processedCount = 0;
      
      for (let i = 0; i < allOptions.length; i++) {
        const option = allOptions[i];
        
        // 检查是否所有关键词都匹配
        const isMatched = keywords.every(keyword => {
          return option.lowercaseDisplayPath.some(label => label.includes(keyword));
        });
        
        if (isMatched) {
          matched.push(option);
          processedCount++;
          
          // 如果匹配结果超出限制，标记并退出循环
          if (processedCount >= MAX_SEARCH_RESULTS) {
            searchLimitExceeded.value = true;
            break;
          }
        }
      }
      
      // 标记是否超出限制
      searchLimitExceeded.value = processedCount >= MAX_SEARCH_RESULTS;
      
      // 对结果进行排序，更相关的结果排在前面
      matched.sort((a, b) => {
        // 计算匹配度分数
        const scoreA = getMatchScore(a, keywords);
        const scoreB = getMatchScore(b, keywords);
        return scoreB - scoreA; // 高分在前
      });
      
      // 更新结果和状态
      allMatchedOptions.value = matched;
      isSearching.value = false;
      hasNoData.value = matched.length === 0;
    }, 0);
  } catch (error) {
    console.error('Error in search:', error);
    isSearching.value = false;
    hasNoData.value = true;
    allMatchedOptions.value = [];
  }
}, { immediate: true });

// 获取所选一级节点下的二级节点（去重）
const matchedSecondLevelNodes = computed(() => {
  if (!selectedRootNode.value) return [];

  // 获取所选一级节点下的所有二级节点
  const secondLevelMap = new Map();
  allMatchedOptions.value
    .filter(option => option.nodes[0].value === selectedRootNode.value.value && option.nodes.length > 1)
    .forEach(option => {
      const secondNode = option.nodes[1];
      if (!secondLevelMap.has(secondNode.value)) {
        secondLevelMap.set(secondNode.value, secondNode);
      }
    });
  return Array.from(secondLevelMap.values());
});

// 获取所选二级节点下的三级节点（去重）
const matchedThirdLevelNodes = computed(() => {
  if (!selectedSecondNode.value || !selectedRootNode.value) return [];

  // 获取所选二级节点下的所有三级节点
  const thirdLevelMap = new Map();
  allMatchedOptions.value
    .filter(option =>
      option.nodes[0].value === selectedRootNode.value.value &&
      option.nodes.length > 2 &&
      option.nodes[1].value === selectedSecondNode.value.value
    )
    .forEach(option => {
      const thirdNode = option.nodes[2];
      if (!thirdLevelMap.has(thirdNode.value)) {
        thirdLevelMap.set(thirdNode.value, thirdNode);
      }
    });
  return Array.from(thirdLevelMap.values());
});

// 获取每个二级节点下的匹配结果数量
const getSecondLevelNodeCount = (secondNode) => {
  return allMatchedOptions.value.filter(option =>
    option.nodes[0].value === selectedRootNode.value.value &&
    option.nodes.length > 1 &&
    option.nodes[1].value === secondNode.value
  ).length;
};

// 获取每个三级节点下的匹配结果数量
const getThirdLevelNodeCount = (thirdNode) => {
  return allMatchedOptions.value.filter(option =>
    option.nodes[0].value === selectedRootNode.value.value &&
    option.nodes.length > 2 &&
    option.nodes[1].value === selectedSecondNode.value.value &&
    option.nodes[2].value === thirdNode.value
  ).length;
};

// 修改 filteredOptions 计算属性，以支持三级节点过滤
const filteredOptions = computed(() => {
  // 如果正在搜索或者扁平化处理中，返回空数组
  if (isSearching.value || isFlatteningOptions.value) {
    return [];
  }
  
  if (activeRootFilter.value === null) {
    return allMatchedOptions.value;
  }

  let filtered = allMatchedOptions.value.filter(option => {
    return option.nodes[0].value === activeRootFilter.value;
  });

  // 如果有二级过滤条件，则进一步过滤
  if (activeSecondLevelFilter.value) {
    filtered = filtered.filter(option => {
      return option.nodes.length > 1 && option.nodes[1].value === activeSecondLevelFilter.value;
    });

    // 如果有三级过滤条件，则继续过滤
    if (activeThirdLevelFilter.value) {
      filtered = filtered.filter(option => {
        return option.nodes.length > 2 && option.nodes[2].value === activeThirdLevelFilter.value;
      });
    }
  }

  return filtered;
});

// 提取匹配的一级节点（去重）
const matchedRootNodes = computed(() => {
  if (!allMatchedOptions.value || !allMatchedOptions.value.length) return [];

  const rootNodeMap = new Map();
  allMatchedOptions.value.forEach(option => {
    const rootNode = option.nodes[0];
    if (!rootNodeMap.has(rootNode.value)) {
      rootNodeMap.set(rootNode.value, rootNode);
    }
  });
  return Array.from(rootNodeMap.values());
});

// 计算匹配分数，用于排序
const getMatchScore = (option, keywords) => {
  let score = 0;

  // 最后一级标签的匹配权重更高
  const lastLevelLabel = option.displayPath[option.displayPath.length - 1].toLowerCase();

  keywords.forEach(keyword => {
    // 完全匹配得分高
    if (lastLevelLabel === keyword) {
      score += 100;
    }
    // 作为开头匹配得分也高
    else if (lastLevelLabel.startsWith(keyword)) {
      score += 50;
    }
    // 包含关键词得基础分
    else if (lastLevelLabel.includes(keyword)) {
      score += 20;
    }

    // 路径中其他部分的匹配
    for (let i = 0; i < option.lowercaseDisplayPath.length - 1; i++) {
      if (option.lowercaseDisplayPath[i].includes(keyword)) {
        score += 10;
      }
    }
  });

  return score;
};

// 处理鼠标悬停在选项上的事件
const handleOptionHover = (option, levelIndex) => {
  try {
    // 更新当前级别的激活选项
    activeOptions.value[levelIndex] = option;

    // 移除后续级别的激活选项和菜单
    activeOptions.value = activeOptions.value.slice(0, levelIndex + 1);
    activeLevels.value = activeLevels.value.slice(0, levelIndex + 1);

    // 如果有子选项，显示下一级菜单
    if (option.children && option.children.length) {
      activeLevels.value.push(option.children);
    }
  } catch (error) {
    console.error('Error in handleOptionHover:', error);
  }
};

// 添加高亮文本的方法
const highlightText = (text) => {
  if (!debouncedSearchValue.value) return text;

  // 将搜索文本按空格拆分成多个关键词
  const keywords = debouncedSearchValue.value.toLowerCase().split(/\s+/).filter(Boolean);

  let result = text;
  // 依次高亮每个关键词
  keywords.forEach(keyword => {
    if (!keyword) return;
    const regex = new RegExp(`(${keyword})`, 'gi');
    result = result.replace(regex, '<span class="highlight-text">$1</span>');
  });

  return result;
};

// 添加清除搜索文本的方法
const clearSearchText = (e) => {
  e.stopPropagation();
  searchValue.value = '';
  debouncedSearchValue.value = '';
  isSearchMode.value = false;
  searchLimitExceeded.value = false;
  // 聚焦回输入框，提升用户体验
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.focus();
    }
  });
};

// 添加虚拟滚动和"加载更多"功能
const displayedOptions = ref([]);
const initialLoadSize = 50; // 初始加载数量
const incrementLoadSize = 100; // 每次增加的显示数量

// 监听过滤结果变化，更新显示选项
watch(() => filteredOptions.value, (newOptions) => {
  if (newOptions && newOptions.length) {
    // 初始只显示前50个结果
    displayedOptions.value = newOptions.slice(0, initialLoadSize);
    // 重置键盘选中索引
    activeIndex.value = -1;
  } else {
    displayedOptions.value = [];
  }
}, { immediate: true });

// 加载更多结果
const loadMoreResults = (event) => {
  // 阻止事件冒泡，防止关闭下拉框
  if (event) {
    event.stopPropagation();
  }

  const currentSize = displayedOptions.value.length;
  const nextBatch = filteredOptions.value.slice(
    currentSize,
    currentSize + incrementLoadSize
  );
  displayedOptions.value = [...displayedOptions.value, ...nextBatch];
};

// 获取每个根节点下的匹配结果数量
const getRootNodeCount = (rootNode) => {
  return allMatchedOptions.value.filter(option =>
    option.nodes[0].value === rootNode.value
  ).length;
};

// 处理键盘事件
const handleKeyDown = (event) => {
  if (!isSearchMode.value || !displayedOptions.value.length) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      if (activeIndex.value < displayedOptions.value.length - 1) {
        activeIndex.value++;
      } else {
        activeIndex.value = 0; // 循环到第一项
      }
      scrollToActiveOption();
      break;
    case 'ArrowUp':
      event.preventDefault();
      if (activeIndex.value > 0) {
        activeIndex.value--;
      } else {
        activeIndex.value = displayedOptions.value.length - 1; // 循环到最后一项
      }
      scrollToActiveOption();
      break;
    case 'Enter':
      event.preventDefault();
      if (activeIndex.value >= 0 && activeIndex.value < displayedOptions.value.length) {
        const option = displayedOptions.value[activeIndex.value];
        selectSearchOption(option.path, option.nodes);
      }
      break;
    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      break;
  }
};

// 滚动到当前选中项
const scrollToActiveOption = () => {
  nextTick(() => {
    const dropdown = document.querySelector('.search-results');
    const activeItem = document.querySelector('.search-item-active');
    if (dropdown && activeItem) {
      const dropdownRect = dropdown.getBoundingClientRect();
      const activeItemRect = activeItem.getBoundingClientRect();

      // 检查选中项是否在可视区域内
      if (activeItemRect.top < dropdownRect.top) {
        // 选中项在可视区域上方
        dropdown.scrollTop -= (dropdownRect.top - activeItemRect.top);
      } else if (activeItemRect.bottom > dropdownRect.bottom) {
        // 选中项在可视区域下方
        dropdown.scrollTop += (activeItemRect.bottom - dropdownRect.bottom);
      }
    }
  });
};

// 添加页面点击事件的监听和处理
onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
  // 只聚焦输入框
  if (props.open && searchInput.value) {
    searchInput.value.focus();
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// 处理点击组件外部的事件
const handleOutsideClick = (event) => {
  if (
    cascaderContainer.value &&
    !cascaderContainer.value.contains(event.target) &&
    showDropdown.value
  ) {
    // 关闭下拉框并重置所有状态
    showDropdown.value = false;
    searchValue.value = '';
    debouncedSearchValue.value = '';
    isSearchMode.value = false;
    searchLimitExceeded.value = false;

    // 重置过滤状态
    activeRootFilter.value = null;
    showSecondLevelTags.value = false;
    selectedRootNode.value = null;
    activeSecondLevelFilter.value = null;
    showThirdLevelTags.value = false;
    selectedSecondNode.value = null;
    activeThirdLevelFilter.value = null;

    // 关闭整个级联选择器
    if (props.open) {
      emit("onSelectChange", [], []);
    }
  }
};

// 创建一个全局变量，用于标记是否正在打开新的级联选择器
const isOpeningNewCascader = ref(false);

// 在showACascader事件发生前，添加标记
const handleSHOW_ACASCADER = (params) => {
  // 标记为正在打开新的级联选择器
  isOpeningNewCascader.value = true;
};

onBeforeMount(() => {
  globalEventEmitter.on(SHOW_ACASCADER, handleSHOW_ACASCADER);
});

// 处理失去焦点事件
const handleBlur = (e) => {
  // 检查失去焦点是因为点击了什么元素
  const relatedTarget = e.relatedTarget;
  
  // 辅助函数：关闭级联选择器
  const closeCascader = () => {
    setTimeout(() => {
      if (props.open) {
        emit("onSelectChange", [], []);
      }
    }, 100);
  };

  // 如果点击的是输入框本身或其子元素，不关闭级联选择器
  if (relatedTarget && (
    relatedTarget === searchInput.value ||
    searchInput.value?.contains(relatedTarget) ||
    cascaderContainer.value?.contains(relatedTarget)
  )) {
    return;
  }

  if (relatedTarget) {
    // 优先检查是否点击的是下拉箭头或SVG元素
    const isArrowElement =
      relatedTarget.classList && relatedTarget.classList.contains('anticon-down') ||
      relatedTarget.closest && relatedTarget.closest('.anticon-down') ||
      (relatedTarget.tagName &&
        (relatedTarget.tagName.toLowerCase() === 'path' ||
          relatedTarget.tagName.toLowerCase() === 'svg'));

    // 如果是下拉箭头，直接关闭级联选择器，不设置isOpeningNewCascader标记
    if (isArrowElement) {
      closeCascader();
      return;
    }

    // 检查是否点击的是属性选择相关元素
    const isPropertySelectElement =
      relatedTarget.classList && relatedTarget.classList.contains('comTypeHandler') ||
      relatedTarget.closest && relatedTarget.closest('.comTypeHandler');

    // 检查是否点击的是需要触发新级联选择器的元素
    const isPointerElement =
      (relatedTarget.classList && relatedTarget.classList.contains('pointer') &&
        !relatedTarget.parentElement.classList.contains('ant-dropdown-menu-item')) ||
      (relatedTarget.closest && relatedTarget.closest('.pointer') &&
        !relatedTarget.closest('.pointer').parentElement.classList.contains('ant-dropdown-menu-item'));

    const isMethodElement =
      relatedTarget.classList && relatedTarget.classList.contains('methodCom') ||
      relatedTarget.closest && relatedTarget.closest('.methodCom');

    const isActionTypeElement =
      relatedTarget.classList && relatedTarget.classList.contains('actionTypeItem') ||
      relatedTarget.closest && relatedTarget.closest('.actionTypeItem');

    // 检查是否点击的是下拉菜单相关元素
    const isDropdownMenu =
      (relatedTarget.classList && relatedTarget.classList.contains('ant-dropdown')) ||
      (relatedTarget.closest && relatedTarget.closest('.ant-dropdown')) ||
      (relatedTarget.classList && relatedTarget.classList.contains('ant-dropdown-menu')) ||
      (relatedTarget.closest && relatedTarget.closest('.ant-dropdown-menu')) ||
      (relatedTarget.classList && relatedTarget.classList.contains('ant-dropdown-menu-item')) ||
      (relatedTarget.closest && relatedTarget.closest('.ant-dropdown-menu-item')) ||
      // 检查是否在pointer内部，但具有特定结构表明它是下拉菜单
      (relatedTarget.closest && relatedTarget.closest('.pointer') && (
        relatedTarget.closest('.pointer').querySelector('.ant-dropdown') ||
        relatedTarget.closest('.pointer').querySelector('.ant-dropdown-menu') ||
        relatedTarget.closest('.pointer').querySelector('.ant-dropdown-menu-item')
      )) ||
      // 检查更多类型的下拉菜单
      (relatedTarget.classList && relatedTarget.classList.contains('ant-select-dropdown')) ||
      (relatedTarget.closest && relatedTarget.closest('.ant-select-dropdown')) ||
      (relatedTarget.classList && relatedTarget.classList.contains('ant-cascader-menus')) ||
      (relatedTarget.closest && relatedTarget.closest('.ant-cascader-menus'));

    // 计算是否应该触发新的级联选择器
    const shouldTriggerNewCascader = isPointerElement || isMethodElement || isActionTypeElement;

    // 处理各种情况
    if (isPropertySelectElement) {
      // 属性选择元素：设置标记并关闭级联选择器
      isOpeningNewCascader.value = true;
      closeCascader();
      return;
    }
    else if (shouldTriggerNewCascader) {
      // 触发新级联选择器的元素：仅设置标记
      isOpeningNewCascader.value = false;
      closeCascader();
      return;
    }
    else if (isDropdownMenu) {
      // 下拉菜单元素：直接关闭级联选择器
      closeCascader();
      return;
    }

    // 其他情况：关闭级联选择器
    closeCascader();
  } else if (!isOpeningNewCascader.value) {
    closeCascader();
    return;
  }
};

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
  globalEventEmitter.off(SHOW_ACASCADER, handleSHOW_ACASCADER);
});

</script>

<style lang="scss">
.custom-cascader-container {
  position: fixed;
  background: #fff;
  z-index: 999999999 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  max-width: 60vw;
  overflow-x: auto;
  outline: none;
  /* 移除默认的focus轮廓 */

  // 向上弹出时的样式调整
  &.popup-upward {
    // 使用bottom定位，让容器从底部开始向上扩展
    bottom: calc(100vh - var(--computed-top)) !important;
    top: auto !important;
    // 设置最大高度，防止超出视窗顶部
    max-height: calc(var(--computed-top) - 20px) !important;
    
    // 调整整体布局方向：输入框在下方，下拉菜单在上方
    display: flex;
    flex-direction: column-reverse;

    .custom-cascader-dropdown {
      // 向上弹出时，下拉菜单在上方，所以上方需要圆角
      border-radius: 6px 6px 0 0 !important;
      box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15) !important; // 阴影向上
      border-bottom: 1px solid #d9d9d9; // 与输入框的分隔边框

      // 使用flex布局反转内容显示顺序，避免文字倒置问题
      .search-results {
        display: flex !important;
        flex-direction: column-reverse !important; // 反转搜索结果的显示顺序
        
        .search-list {
          display: flex !important;
          flex-direction: column-reverse !important; // 反转搜索项的顺序
        }
      }

      // 反转级联菜单的显示顺序
      .menus-container {
        align-items: flex-end !important; // 向上弹出时底部对齐
        
        .menu-column {
          display: flex !important;
          flex-direction: column-reverse !important; // 反转菜单列内容
          justify-content: flex-end !important; // 内容贴底部
          
          .menu-list {
            display: flex !important;
            flex-direction: column-reverse !important; // 反转菜单项顺序
            list-style: none !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            overflow: auto;

            .menu-item {
              display: block !important; // 确保每个菜单项都正确显示
              order: initial !important; // 确保菜单项顺序由flex-direction控制
            }
          }
        }
      }

      // 反转加载状态和空状态的显示
      .loading-result,
      .empty-result,
      .min-chars-hint {
        order: 1 !important;
      }
    }

    .custom-cascader-input {
      // 向上弹出时，输入框在下方，所以下方需要圆角
      border-radius: 0 0 6px 6px;
      border-top: none; // 移除顶部边框避免与下拉菜单重复
      
      &:hover {
        border-color: #40a9ff;
        border-top: 1px solid #40a9ff; // hover时恢复顶部边框
      }
    }
  }

  .custom-cascader-input {
    position: relative;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background-color: #fff;
    display: flex;
    align-items: center;
    transition: border-color 0.3s;

    &:hover {
      border-color: #40a9ff;
    }

    .search-icon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.25);
      margin-right: 8px;
      cursor: pointer;

      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }

    input {
      flex: 1;
      border: none;
      outline: none;
      height: 100%;
      padding: 0 11px;
      font-size: 12px;
      background: transparent;
      z-index: 1;

      &::placeholder {
        color: #bfbfbf;
      }
    }

    .selected-value {
      position: absolute;
      left: 11px;
      right: 28px;
      top: 6px;
      color: rgba(0, 0, 0, 0.65);
      white-space: nowrap;
      pointer-events: none;
    }

    .arrow,
    .clear {
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(0, 0, 0, 0.25);
      cursor: pointer;
      margin-right: 8px;

      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .arrow-active {
      transform: rotate(180deg);
    }
  }

  .custom-cascader-dropdown {
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .loading-result,
    .empty-result,
    .min-chars-hint {
      padding: 12px 16px;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      min-height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .result-info {
      padding: 8px 12px;
      background-color: #fffbe6;
      border-bottom: 1px solid #ffe58f;

      .result-limit-warning {
        color: #faad14;
        font-size: 12px;
        text-align: center;
      }
    }

    .search-results {
      min-height: 180px;
      max-height: 290px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 7px;
        height: 7px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-button {
        display: none;
      }

      .root-node-filters {
        padding: 6px 12px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #f9f9f9;

        .filter-title {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          margin-right: 8px;
        }

        .filter-tags {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          .filter-tag {
            padding: 2px 8px;
            font-size: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin-right: 8px;
            margin-top: 4px;
            margin-bottom: 4px;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.65);
            transition: all 0.3s;

            &:hover {
              color: #40a9ff;
              border-color: #40a9ff;
            }

            &.active {
              background-color: #e6f7ff;
              color: #1890ff;
              border-color: #1890ff;
            }
          }

          &.second-level {
            margin-top: 8px;
            padding-top: 6px;
            border-top: 1px dashed #e8e8e8;

            .filter-tag {
              font-size: 12px;
              padding: 1px 6px;
              background-color: #f0f7ff;

              &.active {
                background-color: #d9edff;
              }
            }
          }

          &.third-level {
            margin-top: 6px;
            padding-top: 5px;
            border-top: 1px dotted #e8e8e8;

            .filter-tag {
              font-size: 12px;
              padding: 0px 5px;
              background-color: #eef5ff;

              &.active {
                background-color: #c7e3ff;
              }
            }
          }
        }
      }

      .result-count {
        padding: 12px 16px;
        color: rgba(0, 0, 0, 0.25);
        text-align: center;
      }

      .search-list {
        list-style: none;
        margin: 0;
        padding: 0;

        .search-item {
          padding: 5px 10px;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;

          &:hover {
            background-color: #f5f5f5;
          }

          &.search-item-active {
            background-color: #e6f7ff;
          }

          .path-separator {
            color: rgba(0, 0, 0, 0.45);
            margin: 0 4px;
          }

          .highlight-text {
            color: #1890ff;
            font-weight: 500;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }

        .load-more-item {
          padding: 5px 10px;
          cursor: pointer;
          text-align: center;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.25);

          &:hover {
            background-color: #f5f5f5;
          }
        }
      }
    }

    .menus-container {
      display: flex;
      align-items: flex-start;

      .menu-column {
        min-width: 120px;
        max-width: 300px;
        min-height: 180px;
        max-height: 290px;
        overflow: auto;
        border-right: 1px solid #f0f0f0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 默认向下弹出时内容从顶部开始

        &::-webkit-scrollbar {
          width: 7px;
          height: 7px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        &::-webkit-scrollbar-button {
          display: none;
        }

        &:last-child {
          border-right: none;
        }

        .menu-list {
          list-style: none;
          margin: 0;
          padding: 0;
          flex: 1; // 占满父容器空间
          display: flex;
          flex-direction: column;
          overflow: auto;

          .menu-item {
            padding: 5px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s;
            white-space: nowrap;
            font-size: 12px;
            flex-shrink: 0; // 防止项目被压缩

            &:hover {
              background-color: #f5f5f5;
            }

            &-active {
              background-color: #e6f7ff;
              font-weight: 500;
              color: #1890ff;
            }

            &-selected {
              font-weight: 500;
            }

            .option-label {
              flex: 1;
            }

            .expand-icon {
              margin-left: 8px;
              color: rgba(0, 0, 0, 0.45);
            }
          }
        }
      }
    }
  }
}
</style>
