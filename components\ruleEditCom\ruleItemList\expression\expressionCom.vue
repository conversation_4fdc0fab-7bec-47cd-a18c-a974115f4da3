<template>
  <span class="expressionCom">
    <!-- 遍历 params 数组，根据 item.type 渲染不同的组件 -->
    <template v-if="params && params.length > 0">
      <template v-for="(item, index) in params" :key="pos + '_' + index + 'wrapper'">
        <!-- 渲染 VariableCom 组件 -->
        <span v-if="item.type === 'variable'">
          <VariableCom
            :key="pos + '_' + index"
            :pos="pos + '_' + index"
            :locked="locked"
            :isTrack="isTrack"
            :isLastOne="index === params.length - 1"
            :dataSource="item.data"
            @onChange="onVariableComChange"
            @onCalSignChange="onChildSignChange"
            :signValue="symbols[index]"
            :titleStr="titleStr"
            :refListData="refListData"
            :isTable="isTable"
            :noRuleCellUnit="noRuleCellUnit"
          />
        </span>
        <!-- 渲染 ChildrenExpression 组件 -->
        <span v-if="item.type === 'expression'">
          <ChildrenExpression
            :key="pos + '_' + index"
            :pos="pos + '_' + index"
            :locked="locked"
            :isTrack="isTrack"
            :isLastOne="index === params.length - 1"
            :valueType="valueType"
            :expressionTreeData="item"
            @onChange="onChildChange"
            @onCalSignChange="onChildSignChange"
            :signValue="symbols[index]"
          />
        </span>
      </template>
    </template>
  </span>
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";
import ChildrenExpression from "./childrenExpression.vue";
import VariableCom from "@/components/ruleEditCom/variable/variableCom.vue";

// 定义 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule"
  },
  variableData: {
    type: Object,
    default: () => ({})
  },
  locked: Boolean,
  isTrack: Boolean,
  titleStr: String,
  refListData: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit
const emit = defineEmits(['onChange', 'onCalSignChange']);

// 计算属性
const expressionTreeData = computed(() => props.variableData.expressionTreeData);
const valueType = computed(() => props.variableData.valueType);
const symbols = computed(() => expressionTreeData.value.symbols);
const params = computed(() => expressionTreeData.value.params);


// 方法
const onChildChange = (childPos, newValueObj, finalValueType) => {
  const { type, data } = newValueObj;
  const newComData = util.cloneRuleData(props.variableData);
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const newValueType = finalValueType || util.getRealValueType(newComData);

  if (type && type === "expression") {
    newComData.expressionTreeData.params[index] = newValueObj;
  } else {
    newComData.expressionTreeData.params[index] = {
      data: data,
      type: "variable"
    };
  }
  emit("onChange", props.pos, newComData, newValueType);
};

const onVariableComChange = (childPos, newValueObj, finalValueType) => {
  const { variableType } = newValueObj;
  if (variableType === "expression") {
    const newChildExpressionObj = newValueObj.expressionTreeData.params[0];
    onChildChange(childPos, newChildExpressionObj, finalValueType);
  } else {
    const newChildObj = {
      type: "variable",
      data: newValueObj
    };
    onChildChange(childPos, newChildObj, finalValueType);
  }
};

const onChildSignChange = (childPos, sign, newParamItem) => {
  const newComData = util.cloneRuleData(props.variableData);
  const valueType = util.getRealValueType(newComData);
  const { symbols: new_symbols, params: new_params } = newComData.expressionTreeData;
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const isLastItem = index === new_params.length - 1;

  if (sign !== "delete") {
    if (!newParamItem && isLastItem) {
      return;
    }
    new_symbols[index] = sign;
    newParamItem && new_params.splice(index + 1, 0, newParamItem);
    !isLastItem && (new_symbols.splice(index + 1, 0, new_symbols[index+1] ? new_symbols[index+1] : sign));
  } else {
    if (!newParamItem && isLastItem) {
      return;
    }
    new_symbols.splice(index, 1);
    new_params.splice(index + 1, 1);
  }
  emit("onChange", props.pos, newComData, valueType);
};
</script>
