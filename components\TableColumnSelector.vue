<!-- 表格列选择器组件 -->
<!-- 用于控制表格列的显示隐藏 -->
<template>
    <a-tooltip v-if="columns.length > 0">
        <a-popover placement="bottomRight" trigger="click">
            <template #content>
                <a-checkbox-group style="display: grid;" v-model:value="checkedColumns" @change="changeTableList">
                    <a-checkbox v-for="item in columns" :key="item.key" :value="item.title" style="margin: 5px 0px 5px 0px">{{ item.title }}</a-checkbox>
                </a-checkbox-group>
            </template>
            <a-button type="text" size="small" class="column-selector-btn" @mouseenter="isHovered = true" @mouseleave="isHovered = false">
                <template #icon><AppstoreOutlined :style="{ color: isHovered ? 'var(--yq-yuque-green-600)' : undefined }" /></template>
            </a-button>
        </a-popover>
        <template #title><span>表头设置</span></template>
    </a-tooltip>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { AppstoreOutlined } from '@ant-design/icons-vue';

const props = defineProps({
    columns: {
        type: Array,
        default: () => []
    },
    checkedColumns: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['changeTableList']);
const checkedColumns = ref([]);
const isHovered = ref(false);

const changeTableList = (val) => {
    const filterValue = props.columns.filter(item => {
        if (val.includes(item.title)){
            // 当暴露值为true时，循环遍历的值会赋给filterValue
            return true
        }
        return false
    })
    emit('changeTableList', filterValue);
}

onMounted(() => {
    if(!props.checkedColumns || props.checkedColumns.length === 0){
        if(props.columns){
            let filterValue = [];
            props.columns.forEach((item) => {
                //如果列表的checked不为false，则添加进默认列表
                if(item.checked !== false){
                    checkedColumns.value.push(item.title);
                    filterValue.push(item)
                }
            })
            emit('changeTableList', filterValue);
        }
    } else {
        checkedColumns.value = props.checkedColumns;
    }
})
</script>

<style scoped>
.column-selector-btn {
    padding: 0;
}
</style>
