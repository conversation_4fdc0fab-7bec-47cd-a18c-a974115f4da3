<template>
    <ListPage
        ref="listLayout"
        title="灰度设置"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumns"
        :queryMethod="getList"
        :actionMenuGetter="getActionMenuItems"
        :pagination="pagination"
        :customRenderColumns="customRenderColumns"
    >
      <template #type="{ record }">
        <a-tag :color="record.type === '1' ? 'blue' : 'gray'">
          {{ record.type === '1' ? '正式环境' : '灰度环境' }}
        </a-tag>
      </template>
      <template #environmentId="{ record }">
        <span>{{ publishEnvir.find(item => item.id === record.environmentId)?.environmentName || '-' }}</span>
      </template>
    </ListPage>
    <!-- 新增/编辑对话框 -->
    <a-modal
        v-model:visible="isModalVisible"
        :title="modalType === 'add' ? '新增灰度设置' : '编辑灰度设置'"
        @ok="handleSubmit"
        @cancel="isModalVisible = false"
        :confirmLoading="submitLoading"
    >
        <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
        >
            <a-form-item label="发布环境" name="environmentId" required>
                <a-select 
                    v-model:value="form.environmentId" 
                    placeholder="请选择发布环境"
                    :disabled="modalType === 'update'"
                    show-search
                    :filter-option="(input, option) => 
                        (option?.children ?? '').toLowerCase().includes(input.toLowerCase())"
                >
                    <a-select-option v-for="item in publishEnvir" :key="item.id" :value="item.id">
                        {{ item.environmentName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="唯一识别码" name="code">
                <a-input v-model:value="form.code" placeholder="请输入唯一识别码" :disabled="modalType === 'update'" />
            </a-form-item>
          <a-form-item label="发布方式" prop="type">
            <a-select v-model:value="form.type" placeholder="请选择">
              <a-select-option value="1">正式发布</a-select-option>
              <a-select-option value="2">灰度发布</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, inject } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { getPublishEnvir } from "@/api/pub_environment";
import { getGrayscaleList, addGrayscale, updateGrayscale, deleteGrayscale } from "@/api/grayscale";

const modal = inject('modal');

const message = inject('message') as any;

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

const customRenderColumns = ['type', 'environmentId'];

import qs from "qs";

// API响应接口
interface ApiResponse<T = any> {
    data: T;
    total?: number;
    code: number;
    message: string;
}

// 发布环境接口定义
interface PublishEnvironment {
    id: string;
    environmentName: string;
}

// 搜索字段选项接口
interface SearchOption {
    name: string;
    value: string | number;
}

// 搜索字段接口
interface SearchField {
    label: string;
    field: string;
    compType: 'select' | 'input';
    compConfig: {
        options?: SearchOption[];
        defaultValue: string;
        required?: boolean;
    };
}

// 搜索配置接口
interface SearchConfig {
    simpleSearchField: {
        label: string;
        field: string;
    };
    advancedSearchFields: SearchField[];
}

interface FormData {
    environmentId: string;
    code: string;
    type: string;
}

// 定义页面元数据
definePageMeta({
    title: '灰度设置',
    path: '/grayscaleSetting',
    name: 'grayscaleSetting'
});

// 发布环境数据
const publishEnvir = ref<PublishEnvironment[]>([]);

// 表格列定义
const tableColumns = [
    {
        title: '发布环境',
        dataIndex: 'environmentId',
        key: 'environmentId',
        align: 'center',
        width: 150,
    },
    {
        title: '唯一识别码',
        dataIndex: 'code',
        key: 'code',
        align: 'left',
        width: 200,
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        width: 150,
    }
];

// 表单数据
const form = ref<FormData>({
    environmentId: '',
    code: '',
    type: ''
});

// 分页配置
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
});

// 加载状态
const loading = ref<boolean>(false);


// 对话框相关
const isModalVisible = ref(false);
const modalType = ref(''); // 'add' 或 'update'
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单验证规则
const rules = {
    environmentId: [{ required: true, message: '请选择发布环境', trigger: 'change' }],
    code: [{ required: true, message: '请输入唯一识别码', trigger: 'blur', type: 'string' }],
    type: [{ required: true, message: '请输入类型', trigger: 'blur', type: 'string' }]
} as Record<string, Rule[]>;

// 获取发布环境
const getEnvInfo = async () => {
    try {
        // 获取发布环境
        getPublishEnvir().then((res) => {
            if (res?.data) {
                publishEnvir.value = res.data;
            }
            const publishEnvirField = searchConfig.advancedSearchFields.find(field => field.field === 'environmentId');
            if (publishEnvirField && publishEnvirField.compConfig) {
                publishEnvirField.compConfig.options = res.data.map(item => ({
                    name: item.environmentName,
                    value: item.id
                }));
            }
        });
    } catch (err) {
        console.error('获取发布环境失败:', err);
        message.error('获取发布环境失败');
    }
};

// 获取列表数据
const getList = async (params: Record<string, any> = {}) => {
    loading.value = true;
    try {
        console.log(params);
        const res = await getGrayscaleList({
            ...params
        });
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        console.error('获取列表失败:', error);
        message.error('获取列表失败');
    } finally {
        loading.value = false;
    }
};

// 显示新增/编辑对话框
const showModal = (type: string, record: Partial<FormData> = {}) => {
    modalType.value = type;
    if (type === 'update') {
        form.value = { ...form.value, ...record };
    } else {
        form.value = {
            environmentId: form.value.environmentId,
            code: '',
            type: ''
        };
    }
    isModalVisible.value = true;
};

// 处理表单提交
const handleSubmit = async () => {
    try {
        await formRef.value?.validate();
        submitLoading.value = true;

        const saveParams = {
            ...form.value,
            environmentId: form.value.environmentId
        };

        if (modalType.value === 'add') {
            await addGrayscale(saveParams);
        } else {
            await updateGrayscale(saveParams);
        }

        message.success(`${modalType.value === 'add' ? '新增' : '更新'}成功`);
        isModalVisible.value = false;
        // 使用布局组件提供的query方法，自动处理分页重置
        if (listLayout.value) {
            listLayout.value.refresh();
        }
    } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败');
    } finally {
        submitLoading.value = false;
    }
};

// 删除记录
const handleDelete = async (record: any) => {
    modal.confirm({
        title: () => "删除灰度设置",
        content: () => "是否删除该灰度设置?",
        okText: () => '确定',
        okType: 'danger',
        cancelText: () => '取消',
        onOk() {
            deleteGrayscale(
                qs.stringify({
                    uuid: record.uuid
                })
            ).then(() => {
                message.success('删除成功');
                // 使用布局组件提供的query方法，自动处理分页重置
                if (listLayout.value) {
                    listLayout.value.refresh();
                }
            }).catch((error) => {
                console.error('删除失败:', error);
                message.error('删除失败');
            });
        },
        onCancel() {
        },
    });
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 批量操作事件
    batchEvent: [],
    // 新建事件
    addNewEvent: () => showModal('add'),
    // 表单处理器配置
    formHandler: {
        queryMethod: getList
    }
};

// 搜索配置
const searchConfig = reactive<SearchConfig>({
    // 简单搜索字段
    simpleSearchField: {
        label: '唯一识别码',
        field: 'code'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '发布环境',
            field: 'environmentId',
            compType: 'select',
            compConfig: {
                options: publishEnvir.value.map(item => ({
                    name: item.environmentName,
                    value: item.id
                })),
            }
        },
        {
            label: '唯一识别码',
            field: 'code',
            compType: 'input',
            compConfig: {
                defaultValue: ''
            }
        },
        {
            label: '类型',
            field: 'type',
            compType: 'select',
            compConfig: {
                options: [
                    { name: '正式环境', value: '1' },
                    { name: '灰度环境', value: '2' }
                ],
            }
        }
    ]
});

// 获取操作菜单项
const getActionMenuItems = (record: any) => {
    return [
        {
            key: 'update',
            label: '编辑',
            onClick: () => showModal('update', record)
        },
        {
            key: 'delete',
            label: '删除',
            onClick: () => handleDelete(record)
        }
    ];
};

// 页面加载时获取数据
onMounted(() => {
    getEnvInfo();
    //getList();
});
</script>

<style lang="scss" scoped>
.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}
</style>
