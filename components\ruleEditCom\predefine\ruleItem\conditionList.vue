<template>
  <span class="conditionCom" :style="{ minWidth: '10000px' }">
    <span ref="refNode">
      <!-- 渲染条件列表 -->
      <template v-if="conditionData">
        <ConditionCom
          v-for="(item, index) in conditionList"
          :key="index"
          v-bind="item.props"
          @addRule="addRule"
          @addUp="addUp"
          @addChildCondition="addChildCondition"
          @addTailItem="addTailItem"
          @replaceItem="replaceItem"
          @decreaseRule="decreaseRule"
          @conditionChange="onConditionChange"
          @conditionInit="conditionInit"
        />
      </template>
    </span>
  </span>
</template>

<script setup>
import ConditionCom from "./condition.vue";
import * as util from "@/components/ruleEditCom/utils/util";

// 定义 props
const props = defineProps({
  pos: String,
  variableType: String,
  conditionData: {
    type: Object,
  },
  validList: {
    type: Array,
    default: () => [],
  },
  locked: Boolean,
  isTrack: Boolean,
  predefineLine: String,
  preIndex: Number,
});

// 定义响应式数据
const baseT = ref(0);
const baseL = ref(0);
const layerHeight = ref(30);
const indentLength = ref(30);

// 定义引用
const refNode = ref(null);

// 定义组件的 emits
const emit = defineEmits([
  'conditionChange',
  'decreaseRule',
  'addUp',
  'conditionInit',
  'logicBtnClick',
  'addRule',
  'replaceItem'
]);

// 计算属性
const conditionList = computed(() => {
  if (props.conditionData) {
    return renderConditions(props.conditionData, false, props.validList);
  }
  return [];
});

// 生命周期钩子
onMounted(() => {
  init();
});

onUpdated(() => {
  resetSize();
});

// 方法
const init = () => {
  const { conditionData } = props;
  if (conditionData) {
    renderConditions(conditionData, false, props.validList);
  }
};

const renderConditions = (conditionData, needRenderNode, conditionValidList) => {
  resetSize();
  return getConditionList(conditionData, { needRenderNode }, conditionValidList);
};

const getConditionList = (conditionData, options, conditionValidList = []) => {
  const {
    needRenderNode,
    startLayer = 1,
    startIndent = 0,
    indexPath = "",
    parentFold,
  } = options;
  const { ruleCondition, children, fold, indent } = conditionData;
  const blockIndent = indent;
  const blockLayer = util.getFirstRule(conditionData).showLayer;

  if (ruleCondition) {
    if (needRenderNode) {
      const { layer, conditionId } = ruleCondition;
      const conditionProps = {
        props: {
          conditionData: props.conditionData,
          isTrack: props.isTrack,
          locked: props.locked,
          validList: props.validList,
          pos: props.pos + "_" + conditionId,
          fold: parentFold,
          indexPath: indexPath, // path记录数据在每一层数组中的位置，以此判断是否显示折叠三角、逻辑符号的位置
          indentLength: indentLength.value,
          dataSource: conditionData,
          variableType: props.variableType,
          conditionValid: conditionValidList[layer - 1],
          predefineLine: props.predefineLine,
          preIndex: props.preIndex,
        },
      };

      return [conditionProps];
    }
  } else {
    return children.map((item, i) => {
      const _options = {
        needRenderNode: true,
        startLayer: blockLayer,
        startIndent: blockIndent,
        indexPath: indexPath + "|" + i,
        parentFold: fold,
      };
      return getConditionList(item, _options, conditionValidList);
    }).flat();
  }
};


const resetSize = () => {
  if (refNode.value) {
    const { conditionData } = props;
    const newHeight = layerHeight.value * util.countDisplayedRules(conditionData) + 10;
    const newWidth = 10000;

    // 设置 ConditionCom 的宽度
    document.getElementsByClassName("conditionCom")[0].style.width = newWidth + "px";
  }
};

// 条件变化时触发的方法
const onConditionChange = (pos, newContents) => {
  emit('conditionChange', pos, newContents); // 触发条件变化事件
};

// 添加规则的方法
const addRule = (pos, conditionId, layer) => {
  emit('addRule', pos, conditionId, layer);
};

// 添加子条件的方法
const addChildCondition = (pos, conditionId, layer) => {
  emit('addChildCondition', pos, conditionId, layer);
};

// 添加尾部项的方法
const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};

// 替换项的方法
const replaceItem = (pos, conditionId, layer) => {
  emit("replaceItem", pos, conditionId, layer);
};

// 初始化条件的方法
const conditionInit = () => {
  emit('conditionInit'); // 触发初始化条件事件
};
// 删除规则的方法
const decreaseRule = ({ pos, conditionId, layer }) => {
  emit("decreaseRule", { pos, conditionId, layer }); // 触发删除规则事件
};

// 添加规则的方法
const addUp = ({ pos, conditionId, layer }) => {
  emit("addUp", pos, conditionId, layer); // 触发添加规则事件
};
</script>
