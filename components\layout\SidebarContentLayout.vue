<!--侧边栏布局组件-->
<template>
    <a-layout class="sidebar-content-layout">
        <!-- 中间侧边栏 -->
        <a-layout-sider
            :width="route.query.backFlag !== 'dashboard'?currentWidth:0" :style="{
            background: '#fff',
            position: 'fixed',
            height: 'calc(100vh - 48px)',
            zIndex: 9,
            borderRight: '1px solid #f0f0f0'
        }" class="secondary-sidebar">
            <!-- 侧边栏内容 -->
            <div class="sidebar-content">
                <slot name="sidebar-content" :width="currentWidth"></slot>
            </div>

            <!-- 拖拽调整宽度的句柄 -->
            <div
                class="resize-handle"
                @mousedown="startResize"
            >
                <!-- 右侧文字提示 -->
                <div class="resize-tooltip">拖拽侧边栏</div>
            </div>
        </a-layout-sider>

        <!-- 内容区域 -->
        <a-layout-content class="main-content" :style="{
            marginLeft: `${route.query.backFlag !== 'dashboard'?currentWidth:0}px`
        }">
            <slot></slot>
        </a-layout-content>
    </a-layout>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';

// 侧边栏配置
const props = defineProps({
    sidebarWidth: {
        type: Number,
        default: 260
    }
});
const route = useRoute()
// 定义事件
const emit = defineEmits(['update:width']);

// 默认侧边栏宽度
const DEFAULT_WIDTH = 260;
// 当前侧边栏宽度
const currentWidth = ref(props.sidebarWidth);
// 最小宽度和最大宽度
const MIN_WIDTH = 5;
const MAX_WIDTH = 480;
// 是否正在拖拽调整大小
const isResizing = ref(false);
// 拖拽前的宽度
const beforeDragWidth = ref(DEFAULT_WIDTH);

// 监听宽度变化并触发事件
watch(currentWidth, (newWidth) => {
    emit('update:width', newWidth);
});

// 开始拖拽
const startResize = (e: MouseEvent) => {
    isResizing.value = true;
    // 记录拖拽前的宽度
    beforeDragWidth.value = currentWidth.value;
    // 记录初始鼠标位置和侧边栏宽度
    const startX = e.clientX;
    const startWidth = currentWidth.value;

    // 添加事件监听器
    const handleMouseMove = (e: MouseEvent) => {
        if (!isResizing.value) return;

        // 计算宽度变化
        const delta = e.clientX - startX;
        let newWidth = startWidth + delta;

        // 限制宽度范围
        newWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, newWidth));
        currentWidth.value = newWidth;

        // 防止选中文本
        e.preventDefault();
    };

    // 鼠标释放处理
    const handleMouseUp = () => {
        isResizing.value = false;

        // 移除事件监听器
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // 保存宽度到本地存储
        try {
            localStorage.setItem('sidebarWidth', currentWidth.value.toString());
        } catch (error) {
            console.error('Failed to save sidebar width:', error);
        }
    };

    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
};

// 重置到默认宽度
const resetToDefaultWidth = () => {
    currentWidth.value = DEFAULT_WIDTH;
    try {
        localStorage.setItem('sidebarWidth', DEFAULT_WIDTH.toString());
    } catch (error) {
        console.error('Failed to save default sidebar width:', error);
    }
};

// 从本地存储读取之前保存的宽度
onMounted(() => {
    try {
        const savedWidth = localStorage.getItem('sidebarWidth');
        if (savedWidth) {
            const width = parseInt(savedWidth);
            if (!isNaN(width) && width >= MIN_WIDTH && width <= MAX_WIDTH) {
                currentWidth.value = width;
            }
        }

    } catch (error) {
        console.error('Failed to get saved sidebar width:', error);
    }
});

// 导出当前宽度和重置方法，供子组件使用
defineExpose({
    currentWidth,
    resetToDefaultWidth,
    DEFAULT_WIDTH
});
</script>

<style lang="scss" scoped>
.sidebar-content-layout {
    height: 100%;
    background: #fff;
}

.sidebar-header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-content {
    height: 100%;
}

.main-content {
    padding: 0;
    background: #fff;
    min-height: calc(100vh - 48px);
    transition: margin-left 0.1s ease;
}

/* 拖拽句柄样式 */
.resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px; /* 调整宽度 */
    height: 100%;
    cursor: col-resize;
    background-color: transparent;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #b7b9bb;

        .resize-tooltip {
            opacity: 1;
            transform: translateX(100%) translateY(-50%) scale(1);
        }
    }

    &:active {
        background-color: #b7b9bb;
    }

    /* 右侧提示文字样式 */
    .resize-tooltip {
        position: absolute;
        top: 20%;
        right: 0;
        color: #f0f0f0;
        background-color: #333;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        transform: translateX(100%) translateY(-50%) scale(0.9);
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        z-index: 10;
        pointer-events: none;
    }
}

/* 拖拽时禁止选中文本 */
:deep(.secondary-sidebar) {
    user-select: none;
}
</style>
