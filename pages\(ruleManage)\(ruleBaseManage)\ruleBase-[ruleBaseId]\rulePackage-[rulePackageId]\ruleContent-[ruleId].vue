<!-- 规则内容编辑 -->

<script setup lang="ts">
import {
  getRuleByUuid,
} from "@/api/rule_base";
// @ts-ignore - 忽略类型检查
import RuleTopBar from "@/businessComponents/ruleBaseManage/RuleTopBar";
import { recordAccess, collectRule, CollectObjectType } from '@/api/dashboardApi'
import { definePageMeta } from '#imports';

definePageMeta({
  layout: "default",
  title: ' '
});
const router = useRouter();
const route = useRoute();

// 规则标题
const ruleTitle = ref("");
// 规则包名称
const packageName = ref("");

// 规则uuid
const uuid = ref("");
// 规则engUuid
const engUuid = ref("");
// 规则demandUuid
const demandUuid = ref("");
// 规则类型
const type = ref("");
// 是否收藏
const ifCollect = ref(false)

const ruleInfo = ref();

// 获取规则信息
function getRuleInfo(ruleId: string) {
  getRuleByUuid({
    uuids: ruleId,
  }).then((res) => {
    ruleTitle.value = res.data.ruleName;
    packageName.value = res.data.packageNameAll;
    type.value = res.data.type;
    uuid.value = res.data.uuid;
    engUuid.value = res.data.engUuid;
    demandUuid.value = res.data.demandUuid;
    ifCollect.value = res.data.ifCollect;
    ruleInfo.value = res.data;
    // 异步记录访问历史
    recordAccess({ ruleUuid: ruleId }).catch(console.error)
  });
}

// 获取路由和标题更新方法
const updateRouteTitle = inject('updateRouteTitle') as (path: string, title: string) => void;

// 监听规则标题和包名变化，更新页面标题
watch([ruleTitle, packageName], ([newRuleTitle, newPackageName]) => {
  if (newRuleTitle && newPackageName) {
    updateRouteTitle(route.path, newRuleTitle);
  }
}, { immediate: true })
function getData() {
  const ruleId = route.params.ruleId as string;
  getRuleInfo(ruleId);
}

onActivated(() => {
  getData();
});

</script>

<template>
  <!-- 使用提取的顶部栏组件，不再需要传递事件 -->
  <RuleTopBar
    ref="ruleTopBar"
    :rule-title="ruleTitle"
    :package-name="packageName"
    :type="type"
    :uuid="uuid"
    :eng-uuid="engUuid"
    :demand-uuid="demandUuid"
    :if-collect="ifCollect"
    :rule-info="ruleInfo"
    :symbol="false"
  />

  <!-- 下部栏：规则内容 -->
  <div class="rule-content">
    <RuleEdit v-if="type" :type="type" :uuid="uuid" :engUuid="engUuid" :demandUuid="demandUuid"
              :packageName="packageName" :ruleName="ruleTitle" />
  </div>
</template>
<style lang="scss" scoped>
  .rule-content {
    padding: 15px;
  }
</style>
