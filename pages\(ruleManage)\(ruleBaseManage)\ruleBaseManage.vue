<!-- 规则库管理页 -->

<script setup lang="ts">
import qs from "qs";
import { list, ruleBaseDelete, ruleBaseExport, getAllBusinessByLoginUser } from "@/api/rule_base";
import RuleBaseEdit from "@/businessComponents/ruleBaseManage/RuleBaseEdit";
import useTableConfig from '@/composables/useTableConfig';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";

definePageMeta({
    title: '规则库管理'
})

const message = inject<any>('message')
const modal = inject<any>('modal')

const router = useRouter();

// 规则库新增组件
const ruleBaseAddComponent = ref()
// 规则库更新组件
const ruleBaseUpdateComponent = ref()
const isSaving = ref(false);

// 处理modal ok 事件
function handleModalOk() {
    isSaving.value = true;
    let submitFun
    switch (modalType.value) {
        case 'add':
            submitFun = ruleBaseAddComponent.value.submitFun;
            break;
        case 'update':
            submitFun = ruleBaseUpdateComponent.value.submitFun;
            break;
    }
    submitFun && submitFun((success) => {
        if (success) {
            listLayout.value?.refresh();
            isModalVisible.value = false;
        }
        isSaving.value = false;
    });
}

const businessLineOptions = ref<Array<{name: string, code: string}>>([]);

// 表格列配置 - 不包含序号列和操作列
const tableColumns = [
    {
        title: '规则库名称',
        align:'left',
        dataIndex: 'chineseName',
        key: 'chineseName',
        fixed: "left",
        width:250
    },
    {
        title: '规则库标识',
        align:'left',
        dataIndex: 'engName',
        key: 'engName',
        width:180,
        checked:false
    },
    {
        title: '业务条线',
        align: 'left',
        dataIndex: 'businessLineName',
        key: 'businessLineName',
        width: 180,
        checked:false
    },
    {
        title: '审核级别',
        align:'center',
        dataIndex: 'checkLevel',
        width:80,
        key: 'checkLevel',
    },
    {
        title: '修改时间',
        align:'center',
        dataIndex: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        width:180,
    },
    {
        title: '修改人',
        align:'center',
        dataIndex: 'modifiedId',
        key: 'modifiedId',
        width:100,
    },
];

const fetchRules = async (params: Record<string, any> = {}) => {
    try {
        const res = await list({
            chineseName: params.name,
            businessLine: params.businessLine,
            startDate: params.fristTime,
            endDate: params.endTime,
            page: params.page || 1,
            several: params.pageSize || 10,
        });
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取规则库失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

const info = (record: any) => {
    const url = `/ruleBase-${record.uuid}?backPage=${listLayout.value?.getPagination().current}`;
    window.open(url, '_blank', 'noopener,noreferrer');
};

const onDelete = (record: any) => {
    modal.confirm({
        title: '确定删除吗？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
            // 添加加载状态
            showGlobalLoading('删除中，请耐心等待');
            ruleBaseDelete(
                qs.stringify({
                    uuid: record.uuid,
                })
            ).then(() => {
                hideGlobalLoading(); // 关闭loading消息
                message.success("删除成功");
                listLayout.value?.refresh();
            }).catch((error) => {
                hideGlobalLoading(); // 关闭loading消息
                message.error("删除失败: " + (error.message || '未知错误'));
            });
        }
    });
};

const exportFun = (record: any) => {
    modal.confirm({
        title: '确定导出名称为'+record.chineseName+'的规则库么？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
            showGlobalLoading('导出中，请耐心等待');
            ruleBaseExport({uuid: record.uuid}).then((res) => {
                const headers = res.headers;
                const blob = new Blob([res.data], {type: headers["content-type"]});
                const link = document.createElement("a");
                const url = window.URL.createObjectURL(blob);
                const fileName = headers["content-disposition"].split(";")[1].split("=")[1];
                link.href = url;
                link.download = decodeURI(fileName);
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                hideGlobalLoading();
            }).catch(()=>{
                hideGlobalLoading();
            });
        }
    })
};

const route = useRoute();
onMounted(() => {
    getAllBusinessByLoginUser().then((res) => {
        businessLineOptions.value = res.data;
        // 更新搜索配置中的业务条线选项
        const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
        if (businessLineField && businessLineField.compConfig) {
            businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
                name: item.name,
                value: item.code
            }));
        }
    });
});

const isModalVisible = ref(false);
const modalType = ref('');
const ruleBaseUuid = ref('');

const showModal = (type: string, record: any = {}) => {
    modalType.value = type;
    if (type === 'update') {
        ruleBaseUuid.value = record.uuid;
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    isModalVisible.value = false;
    isSaving.value = false;
};

// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '规则库名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '规则库名称',
            field: 'name',
            compType: 'input',
            defaultValue: ''
        },
        {
            label: '业务条线',
            field: 'businessLine',
            compType: 'select',
            compConfig: {
                options: businessLineOptions.value.map(item => ({
                    name: item.name,
                    value: item.code
                }))
            },
            defaultValue: null
        },
        {
            label: '开始时间',
            field: 'fristTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false
            }
        },
        {
            label: '结束时间',
            field: 'endTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false
            }
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 新建事件权限
    addPermission: RULE_PERMISSION.RULE_BASE.ADD,
    // 新建事件
    addNewEvent: () => showModal('add'),
    // 表单处理器配置
    formHandler: {
        // 日期字段特殊处理
        dateFields: {
            startField: 'fristTime',
            endField: 'endTime',
        },
        // 查询方法
        queryMethod: fetchRules
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: any) => {
    return [
        {
            key: 'update',
            label: '更新',
            permission: RULE_PERMISSION.RULE_BASE.UPDATE,
            onClick: () => showModal('update', record)
        },
        {
            key: 'delete',
            label: '删除',
            permission: RULE_PERMISSION.RULE_BASE.DELETE,
            onClick: () => onDelete(record)
        },
        {
            key: 'export',
            label: '导出',
            permission: RULE_PERMISSION.RULE_BASE.EXPORT,
            onClick: () => exportFun(record)
        }
    ];
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

// 数据加载完成处理函数
const handleDataLoaded = (response: any) => {
    if (response && response.data) {
        // 可以在这里处理数据加载完成后的逻辑
    }
};

//
const customRenderColumns = ['chineseName'];
</script>

<template>
    <ListPage
            ref="listLayout"
            title="规则库管理"
            :searchConfig="searchConfig"
            :eventConfig="eventConfig"
            :showAddButton="true"
            :tableColumns="tableColumns"
            :queryMethod="fetchRules"
            rowKey="uuid"
            :actionMenuGetter="getActionMenuItems"
            @dataLoaded="handleDataLoaded"
            :customRenderColumns="customRenderColumns"
    >
        <template #chineseName="{ record }">
            <a @click="info(record)" class="nameButton"
                    v-if="checkPermi([RULE_PERMISSION.RULE_BASE.DETAIL])">
                {{ record.chineseName }}
            </a>
            <a class="nameButton" v-else>
                {{ record.chineseName }}
            </a>
        </template>
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal v-if="isModalVisible" :visible="isModalVisible" :title="modalType === 'add' ? '新增规则库' : '更新规则库'" @ok="handleModalOk"
        @cancel="handleCancel" okText="保存" :okButtonProps="{ disabled: isSaving }" class="rule-base-modal" :width="600">
        <!-- 新增模式 -->
        <RuleBaseEdit ref="ruleBaseAddComponent" mode="add" v-if="modalType === 'add'"/>
        <!-- 更新模式 -->
        <RuleBaseEdit ref="ruleBaseUpdateComponent" mode="update" :uuid="ruleBaseUuid" v-if="modalType === 'update'"/>
    </a-modal>
</template>

<style lang="scss" scoped>
.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.rule-base-modal {
    :deep(.ant-modal-body) {
        padding: 20px 24px;
        overflow: visible;
    }

    :deep(.ant-modal-footer) {
        border-top: 1px solid #f0f0f0;
        padding: 10px 24px;
        margin-top: 0;
    }
}
</style>
