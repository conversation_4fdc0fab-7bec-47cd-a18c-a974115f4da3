<!-- 通用抽屉组件 -->
<template>
  <div class="drawer-wrapper" ref="wrapperRef">
    <!-- 抽屉组件 -->
    <a-drawer
            :visible="visible"
            @close="handleClose"
            :placement="placement"
            :width="width"
            :closable="false"
            :destroyOnClose="destroyOnClose"
            class="common-drawer"
            :getContainer="() => drawerContainer"
            :mask="!keepWidth"
            :maskClosable="!keepWidth"
            :class="{'keep-width': keepWidth}"
            :rootClassName="drawerId"
            :push="false"
    >
      <template #title>
        <!-- 独立的关闭按钮 -->
        <div v-if="visible && showCloseBtn" class="close-button" @click="handleClose" ref="closeBtn" :style="{ visibility: closeBtnVisible ? 'visible' : 'hidden' }">
          <RightOutlined style="color:blue"/>
        </div>
        <div class="drawer-title-container">
          <div class="drawer-title">
            <span>{{ title }}</span>
          </div>
          <!-- 子抽屉关闭按钮 -->
          <div v-if="!showCloseBtn" class="sub-drawer-close">
            <a-button shape="circle" size="small" @click="handleClose"><template #icon><CloseOutlined /></template></a-button>
          </div>
        </div>
      </template>

      <div class="drawer-content">
        <!-- 内容区域 -->
        <slot></slot>
      </div>

      <!-- 只有当footer slot有内容时才显示底部区域 -->
      <template #footer v-if="hasFooterContent">
        <div class="drawer-footer">
          <!-- 底部按钮区域 -->
          <slot name="footer"></slot>
        </div>
      </template>
    </a-drawer>


  </div>
</template>

<script setup>
  import { LeftOutlined, RightOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { ref, onMounted, nextTick, watch, useSlots, computed, onBeforeUnmount } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '抽屉标题'
    },
    width: {
      type: [Number, String],
      default: 1000
    },
    placement: {
      type: String,
      default: 'right',
      validator: (value) => ['left', 'right', 'top', 'bottom'].includes(value)
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    //showCloseBtn为false时隐藏关闭按钮
    showCloseBtn:{
      type: Boolean,
      default: true
    },
    // 子抽屉打开时父抽屉保持宽度不变
    keepWidth: {
      type: Boolean,
      default: false
    }
  });

  // 生成一个唯一的ID用于标识当前抽屉
  const drawerId = `drawer-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  const slots = useSlots();
  // 检查是否有footer内容
  const hasFooterContent = computed(() => !!slots.footer);

  const emit = defineEmits(['close']);
  const closeBtn = ref(null);
  const wrapperRef = ref(null);
  const drawerContainer = ref(document.body);
  // 添加一个状态来控制关闭按钮的可见性
  const closeBtnVisible = ref(false);

  const handleClose = () => {
    emit('close');
  };

  // 监听抽屉可见性变化，调整按钮位置
  watch(() => props.visible, (newVal) => {
    if (newVal) {
      // 先设置为不可见
      closeBtnVisible.value = false;
      nextTick(() => {
        positionCloseButton();
      });
    }
  }, { immediate: true });

  // 定位关闭按钮
  const positionCloseButton = () => {
    nextTick(() => {
      // 等待DOM更新后再获取元素
      setTimeout(() => {
        if (!closeBtn.value) return;

        // 在document中查找当前抽屉的元素，通过唯一ID确保找到正确的抽屉
        const drawerEl = document.querySelector(`.${drawerId} .ant-drawer-content`);
        const headerEl = document.querySelector(`.${drawerId} .ant-drawer-header`);

        if (!drawerEl || !headerEl) return;

        const drawerRect = drawerEl.getBoundingClientRect();
        const headerRect = headerEl.getBoundingClientRect();

        // 按钮定位在标题下方横线与抽屉左边框交界处
        closeBtn.value.style.top = `${headerRect.bottom}px`;
        closeBtn.value.style.left = `${drawerRect.left}px`;

        // 定位完成后再显示按钮
        closeBtnVisible.value = true;
      }, 200); // 增加延迟时间，确保DOM更新完成
    });
  };

  // 窗口大小变化时重新计算位置
  onMounted(() => {
    window.addEventListener('resize', positionCloseButton);
  });

  // 组件卸载前移除事件监听器
  onBeforeUnmount(() => {
    window.removeEventListener('resize', positionCloseButton);
  });
</script>

<style lang="scss" scoped>
  .drawer-wrapper {
    position: relative;
  }

  // 独立的关闭按钮
  .close-button {
    position: fixed;
    width: 23px;
    height: 23px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    z-index: 99999; // 最高层级
    transition: all 0.3s;
    transform: translate(-50%, -50%); // 让按钮中心位于边框交界处

    &:hover {
      background-color: #f5f5f5;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }

    .anticon {
      font-size: 14px;
      color: #666;
    }
  }

  .common-drawer {
    :deep(.ant-drawer-content-wrapper) {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    :deep(.ant-drawer-header) {
      padding: 20px 30px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fff;
    }

    :deep(.ant-drawer-body) {
      padding: 0;
      height: calc(100% - 55px);
      overflow: hidden;
      background-color: #fafafa;
    }

    :deep(.ant-drawer-footer) {
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
      background-color: #fff;
    }

    // 添加保持宽度样式
    &.keep-width {
      :deep(.ant-drawer-content-wrapper) {
        position: fixed !important;
        transform: none !important;
      }
    }
  }

  .drawer-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .drawer-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 16px;
    margin-left: 6px;
    font-weight: 500;
    color: #333;
  }

  .sub-drawer-close {
    margin-right: 10px;
  }

  .drawer-content {
    height: 100%;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
