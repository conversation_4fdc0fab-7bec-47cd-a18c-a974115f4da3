<!-- 通用弹出框全屏组件 -->
<!-- 该组件用于弹出框内容过多需要全屏展示时使用（个别固定高度的表单可通过isFullscreen参数判断调整样式） -->
<template>
    <a-modal
        :open="props.isModalVisible"
        v-if="props.isModalVisible"
        @cancel="props.handleCancel"
        :closable="false"
        @ok="props.handleOk"
        :wrapClassName="props.isFullscreen ? 'full-modal' : 'half-modal'"
        :footer="null"
    >
        <template #title>
            <div style="display: flex; justify-content: space-between">
                <span>{{props.titleText}}</span>
                <span v-if="showFullBtn">
                    <span style="margin-right: 8px" class="close-btn">
                        <fullscreen-exit-outlined v-if="props.isFullscreen" @click="props.onFullscreenToggle"/>
                        <fullscreen-outlined v-else @click="props.onFullscreenToggle"/>
                    </span>
                    <span class="close-btn">
                        <close-outlined @click="props.handleCancel"/>
                    </span>
                </span>
                <span v-else>
                   <FullscreenExitOutlined  @click="props.handleCancel"/>
                </span>
            </div>
        </template>
        <template #default>
        <slot name="default"></slot>
        </template>
        <template #footer v-if="showFooter">
        <slot name="footer"></slot>
        </template>
    </a-modal>

</template>
<script setup lang="ts">
import type { PropType } from 'vue'
import { FullscreenExitOutlined, FullscreenOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
const props = defineProps({
    isFullscreen: {
        type: Boolean,
        default: false
    },
    titleText: {
        type: String,
        default: ''
    },
    handleCancel: {
        type: Function as PropType<(e: MouseEvent) => void>,
        required: true
    },
    onFullscreenToggle: {
        type: Function as PropType<(e: MouseEvent) => void>,
        required: true
    },
    isModalVisible: {
        type: Boolean,
        default: false
    },
    handleOk: {
        type: Function as PropType<(e: MouseEvent) => void>,
        default: () => {}
    },
    showFullBtn:{
        type: Boolean,
        default: true
    },
    showFooter:{
        type: Boolean,
        default: true
    }
})
</script>
<style lang="scss">
.full-modal {
    .ant-modal {
        max-width: 100%;
        width: 100vw !important;
        top: 0;
        padding-bottom: 0;
        margin: 0;
    }

    .ant-modal-content {
        height: 100vh;
        display: flex;
        flex-direction: column;
        border-radius: 0;
    }

    .ant-modal-body {
        flex: 1;
        overflow: auto;
        padding: 8px 16px;
        display: flex;
        flex-direction: column;
    }
    .ant-modal-header{
        padding: 8px 24px;
    }
}
.half-modal {
    .ant-modal {
        width: 80vw !important;
    }

    .ant-modal-content {
        display: flex;
        flex-direction: column;
        max-height: calc(100vh - 100px);
    }

    .ant-modal-body {
        flex: 1;
        overflow: auto;
        padding: 16px;
        display: flex;
        flex-direction: column;
        max-height: calc(90vh - 170px);
    }
}

.close-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    .anticon {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.45);
        transition: color 0.3s;
    }

    &:hover .anticon {
        color: rgba(0, 0, 0, 0.85);
    }
}
</style>
