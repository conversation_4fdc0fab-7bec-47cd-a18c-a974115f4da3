import { type Ref } from "vue";

// 自定义 hook 用于窗口调整和元素位置计算
export default function useResize(elementRef: Ref<HTMLElement | null>) {
    // 通过传入的元素和窗口高度来计算滚动距离
    const basePoint = ref<HTMLElement | null>(null);
    const elementToTopHeight = ref(0);
    const scrollY = ref(0);
    const windowHeight = ref(0);

    const updateDimensions = () => {
        if (elementRef.value) {
            elementToTopHeight.value = elementRef.value.getBoundingClientRect().top;
            windowHeight.value = window.innerHeight;
            scrollY.value = windowHeight.value - elementToTopHeight.value - 100;
        }
    };

    // 窗口大小变化时更新尺寸
    const onResize = () => {
        updateDimensions();
    };

    // 初始化时获取尺寸
    onMounted(() => {
        basePoint.value = elementRef.value;
        updateDimensions();
        window.addEventListener("resize", onResize);
    });

    // 销毁时清理事件监听
    onBeforeUnmount(() => {
        window.removeEventListener("resize", onResize);
    });

    return {
        elementToTopHeight,
        scrollY,
        windowHeight,
        onResize
    };
}
