<template>
  <div id="replace-item">
    <!-- 使用 Ant Design Vue 的 Drawer 组件 -->
    <a-drawer
      title="替换规则"
      :open="replaceShow"
      placement="right"
      width="30%"
      @close="replaceClose"
      class="diy_drawer"
    >
      <a-form>
        <!-- 使用 Ant Design Vue 的 Input 组件 -->
        <a-textarea
          v-model:value="replaceVar"
          :rows="10"
          placeholder="请输入一行新的规则内容"
        />
        <!-- 使用 Ant Design Vue 的 Alert 组件 -->
        <a-alert
          :message="'错误值：' + replaceIErrInfo.join('，')"
          type="error"
          show-icon
          v-show="elAlert"
          style="margin-top: 10px"
        />
        <div style="margin-top: 30px; text-align: right;">
          <!-- 使用 Ant Design Vue 的 Button 组件 -->
          <a-button @click="replaceShow = false" style="margin-right: 15px;">取 消</a-button>
          <a-button type="primary" @click="replaceSave">确定</a-button>
        </div>
      </a-form>
    </a-drawer>
  </div>
</template>

<script setup>
const message = inject("message");
// 定义组件的 props
const props = defineProps({
  replaceIShow: {
    type: Boolean,
    default: false,
  },
  replaceIErrInfo: {
    type: Array,
    default: () => [],
  },
});

// 定义组件的 emits
const emit = defineEmits([
  'replaceItemHide',
  'replaceItemSure',
]);

// 定义响应式数据
const replaceVar = ref("");
const elAlert = ref(false);
const replaceShow = ref(false);

// 监听 replaceIShow 的变化
watch(
  () => props.replaceIShow,
  (val) => {
    replaceShow.value = val;
  }
);

// 监听 replaceIErrInfo 的变化
watch(
  () => props.replaceIErrInfo,
  (val) => {
    if (val && val.length) {
      elAlert.value = true;
    } else {
      replaceShow.value = false;
    }
  }
);

// 关闭 Drawer 的方法
const replaceClose = () => {
  replaceVar.value = "";
  elAlert.value = false;
  emit("replaceItemHide");
};

// 保存替换内容的方法
const replaceSave = () => {
  let replaceStr = replaceVar.value.trim();
  let replaceLast = replaceStr.slice(replaceStr.length - 1);
  if (replaceStr.length) {
    if (replaceLast === ";") {
      replaceStr = replaceStr.slice(0, replaceStr.length - 1);
    }
    elAlert.value = false;
    emit("replaceItemSure", replaceStr.trim());
    replaceClose();
  } else {
    message.error("请输入一行新的规则内容");
  }
};
</script>

<style scoped lang="scss">
#replace-item ::v-deep {
  .ant-drawer {
    overflow: auto !important;
    header {
      span {
        font-size: 18px;
        color: #303133;
      }
    }
    .ant-drawer-header {
      text-align: left;
    }
    .ant-form {
      padding: 0 20px 20px;
    }
  }
}
</style>
