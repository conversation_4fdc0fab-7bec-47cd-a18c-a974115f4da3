<!-- 分配规则库页 -->

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { getAllBusinessByLoginUser, getEnginesByUserId, setEngAction, getPersonalEngIdsById } from '@/api/userManagement';
import qs from 'qs';
definePageMeta({
    title: '分配规则库'
})

const props = defineProps({
    userId: {
        type: String,
        required: true
    }
});

const message = inject('message');
const router = useRouter();

const checkAll = ref(false);
const checkedRuleBases = ref<any[]>([]);
const ruleBases = ref<any[]>([]);
const ruleBaseOptions = ref<any[]>([]);
const isIndeterminate = ref(true);
const formHidden = ref(true);
const noData = ref(true);
const noCheckedOptions = ref<any[]>([]);
const checked = ref<any[]>([]);
const newArr = ref<any[]>([]);
const isboouer = ref('');

onMounted(() => {
    ruleBaseInit();
});

const checkAllChange = (e: any) => {
    checkedRuleBases.value = e.target.checked ? ruleBaseOptions.value : [];
    isIndeterminate.value = false;
};

const checkedRuleBasesChange = (value: any[]) => {
    checked.value = value;
    const checkedCount = value.length;
    checkAll.value = checkedCount === ruleBases.value.length;
    isIndeterminate.value = checkedCount > 0 && checkedCount < ruleBases.value.length;
};

const submitForm = () => {
    let engineeringId: any[] = [];
    let resultEngineeringId: any[] = [];
    let resultEngineeringArr: any[] = [];
    let Audit: any[] = [];

    if (ruleBaseOptions.value.length > checked.value.length && checked.value.length >= 1 && !checkAll.value) {
        if (ruleBaseOptions.value && ruleBaseOptions.value.length) {
            Object.assign(resultEngineeringArr, ruleBaseOptions.value);
            for (let i in ruleBaseOptions.value) {
                let isCheacked = false;
                checked.value.filter((item: any) => {
                    if (ruleBaseOptions.value[i].uuid.toString() === item.uuid.toString()) {
                        isCheacked = true;
                    }
                });
                if (!isCheacked) {
                    Audit.push(ruleBaseOptions.value[i].uuid.toString());
                }
            }
            resultEngineeringId = Audit;
        }
    } else if (!checkAll.value) {
        engineeringId = ruleBaseOptions.value;
        engineeringId.filter((item: any) => {
            resultEngineeringId.push(item.uuid);
            return true;
        });
    }

    setEngAction(
        qs.stringify({
            userId: props.userId,
            user_engineeringIds: resultEngineeringId.toString(),
        })
    ).then((res: any) => {
        message.success(res.data);
        ruleBaseInit();
    });
};

const ruleBaseInit = () => {
    if (props.userId) {
        getEnginesByUserId({
            id: props.userId,
        }).then((resBase: any) => {
            if (resBase.data && resBase.data.length) {
                ruleBaseOptions.value = [];
                resBase.data.filter((item: any) => {
                    ruleBaseOptions.value.push({
                        name: item.chineseName,
                        uuid: item.uuid,
                    });
                });
                Object.assign(noCheckedOptions.value, ruleBaseOptions.value);

                getPersonalEngIdsById({
                    id: props.userId,
                }).then((res: any) => {
                    let resD = res.data;
                    if (resD && resD.length) {
                        resD = resD.split(',');
                        for (let i in resD) {
                            noCheckedOptions.value.filter((item: any, index: number) => {
                                if (resD[i].toString() === item.uuid.toString()) {
                                    noCheckedOptions.value.splice(index, 1);
                                }
                                return true;
                            });
                        }
                        checkedRuleBases.value = noCheckedOptions.value;
                        checkedRuleBasesChange(checkedRuleBases.value);
                    } else {
                        checkedRuleBases.value = ruleBaseOptions.value;
                        checkedRuleBasesChange(checkedRuleBases.value);
                    }
                });
                ruleBases.value = ruleBaseOptions.value;
                noData.value = true;
                formHidden.value = false;
            } else {
                noData.value = false;
            }
        });
    }
};

const back = () => {
    emit('close');
};

const resetForm = () => {
    checkAll.value =false;
    checkedRuleBases.value = '';
};

const emit = defineEmits(['close']);

// 暴露给父组件的方法
defineExpose({
    submitForm,
    resetForm
});

</script>

<template>
    <!-- 搜索区域 -->
    <a-form>
        <a-form-item label="设置权限" v-if="!formHidden">
            <a-checkbox :indeterminate="isIndeterminate" v-model:checked="checkAll" @change="checkAllChange">全选</a-checkbox>
            <div style="margin: 15px 0;"></div>
            <a-checkbox-group v-model:value="checkedRuleBases" @change="checkedRuleBasesChange">
                <a-checkbox v-for="item in ruleBases" :key="item.uuid" :value="item">{{ item.name }}</a-checkbox>
            </a-checkbox-group>
        </a-form-item>
    </a-form>
</template>
