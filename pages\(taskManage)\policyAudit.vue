<!-- 政策审核页 -->
<script setup lang="ts">
    import { useRouter, useRoute } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import { policyCheckList, userOrgList } from "@/api/task";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    definePageMeta({
        title: '政策审核'
    })
    const router = useRouter();
    const route = useRoute();
    const tableDate = ref<any[]>([]);
    const form = ref({
        appNo: "",
        businessLine: "",
        createdName: "",
        policyOrgId: "",
        demandName: "",
        endDate: "",
        page: "",
        several: "",
        startDate: "",
        auditType: "",
        demandUuid: "",
        logContent: "",
        endTime: "",
        fristTime: "",
        state: "",
    });
    const columns = reactive([{
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        align:'center',
        ellipsis: true,
        width: 50,
        fixed: 'left',
        customRender: ({ index }) => `${(paginations.value.page - 1) * paginations.value.limit + index + 1}`,
    },...tableColumns]);
    const paginations = ref({
        loading: true,
        total: 1, //总数
        limit: 10, //一页显示都是条
        page: 1, //当前页
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条数据`,
    });

    // 添加 loading 变量
    const loading = ref(false);

    const currentPage = ref(1); //当前页码
    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);
    const task_state_options = ref([
        { value: "25", label: "政策待审核" },
        { value: "26", label: "政策审核中" },
        { value: "28", label: "政策审核下发" },
    ]);

    const list = () => {
        loading.value = true; // 查询开始时设置 loading 为 true
        policyCheckList({
            appNo: form.value.appNo,
            businessLine: form.value.businessLine,
            createdName: form.value.createdName,
            endDate: form.value.endTime,
            page: paginations.value.page,
            several: paginations.value.limit,
            policyOrgId: form.value.policyOrgId,
            demandName: form.value.demandName,
            startDate: form.value.fristTime,
            state: form.value.state,
        }).then((res: any) => {
            tableDate.value = res.data.data;
            paginations.value.total = res.data.totalCount;
            loading.value = false; // 查询结束时设置 loading 为 false
        });
    };

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLine_option.value = res.data;
        });
    };

    const getCreatedName = (value: string) => {
        form.value.applyOrgId = "";
        let name = "";
        businessLine_option.value.map((i) => {
            if (i.code === value) {
                name = i.name;
            }
        });
        userOrgList({
            roleName: "DEMAND_POLICY_ROLE_CHECK",
            businessCode: value,
        }).then((res: any) => {
            model_type_options.value = res.data;
        });
    };

    const reset = () => {
        form.value.appNo = "";
        form.value.businessLine = "";
        form.value.endTime = "";
        form.value.fristTime = "";
        form.value.demandName = "";
        form.value.policyOrgId = "";
        form.value.createdName = "";
        form.value.state = "";
        list();
    };

    const startTimeChange = (val: string) => {
        form.value.fristTime = `${val} 00:00:00`;
    };

    const endTimeChange = (val: string) => {
        form.value.endTime = `${val} 23:59:59`;
    };

    const queryList = () => {
        paginations.value.page = 1;
        currentPage.value = 1;
        list();
    };

    const pagin = (cur,pageSize) => {
        paginations.value.page = cur;
        paginations.value.limit = pageSize;
        list();
    };

    onMounted(() => {
        //list();
        getOption();
    });
    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'pCheckInfo') {
            datar.value = {
                type: "pCheckInfo",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        if (type === 'policyAudit') {
            datar.value = {
                type: "policyAudit",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                file: record.file,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                createdId: record.createdId,
                createdName: record.createdName,
            };
        }
        isModalVisible.value = true;
    };
    const close = (flag) => {
        if(flag){//如果是数据提交，则刷新表单
            list();
        }
        isModalVisible.value = false;
    }
</script>
<template>
    <ListPageLayout  :reloadFun="reset" :columns="columns">

        <template #title>
            <!-- 页面标题 -->
            政策审核
        </template>

        <template #search>
            <!-- 搜索区域 -->
           <a-form v-modal:model="form" layout="inline" @keyup.enter="queryList">
                <a-form-item label="任务编码">
                    <a-input
                            v-model:value="form.appNo"
                            placeholder="任务编码"
                            allow-clear="true" autocomplete="off"
                    ></a-input>
                </a-form-item>
                <a-form-item label="任务名称">
                    <a-input
                            v-model:value="form.demandName"
                            placeholder="任务名称"
                            allow-clear="true" autocomplete="off"
                    ></a-input>
                </a-form-item>
                <a-form-item label="业务条线">
                    <a-select
                            @change="getCreatedName"
                            v-model:value="form.businessLine"
                            placeholder="请选择"
                            style="width: 200px"
                            :filterOption="filterOption" showSearch
                    >
                        <a-select-option
                                v-for="item in businessLine_option"
                                :key="item.demandUuid"
                                :value="item.code"
                                :name="item.name"
                        >
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="当前审核机构">
                    <a-select
                            v-model:value="form.policyOrgId"
                            placeholder="请选择"
                            filterable
                            style="width: 200px"
                    >
                        <a-select-option
                                v-for="item in model_type_options"
                                :key="item.id"
                                :value="item.id"
                        >
                            {{ item.orgName }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="申请时间" name="fristTime">
                    <a-date-picker v-model:value="form.fristTime" valueFormat="YYYY-MM-DD 00:00:00" format="YYYY-MM-DD"/>
                </a-form-item>
                <a-form-item label="至" name="endTime">
                    <a-date-picker v-model:value="form.endTime" valueFormat="YYYY-MM-DD 23:59:59" format="YYYY-MM-DD"/>
                </a-form-item>
                <a-form-item label="申请人">
                    <a-input
                            v-model:value="form.createdName"
                            placeholder="申请人"
                            allow-clear="true" autocomplete="off"
                    ></a-input>
                </a-form-item>
                <a-form-item label="任务状态">
                    <a-select
                            v-model:value="form.state"
                            placeholder="请选择"
                            style="width: 150px"
                    >
                        <a-select-option
                                v-for="item in task_state_options"
                                :key="item.value"
                                :value="item.value"
                        >
                            {{ item.label }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item class="buttonItem">
                    <a-button
                            type="primary"
                            @click="queryList"
                            style="margin-right: 8px"
                    >
                        查询
                    </a-button>
                    <a-button  @click="reset">
                        重置
                    </a-button>
                </a-form-item>

            </a-form>
        </template>

        <template #table="slotProps">
            <!-- 表格区域 -->
            <a-table :scroll="{ x: 'max-content', y: slotProps.scrollY }" :columns="slotProps.filterColumn" :data-source="tableDate" :pagination="false" row-key="uuid" :loading="loading" size="small">
                <template v-slot:bodyCell="{column,record,index}">
                    <template v-if="column.dataIndex === 'demandName'">
                        <a-button type="link" size="small" @click="showModal('pCheckInfo',record)">{{record.demandName}}</a-button>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <span>{{ TASK_STATE[record.state] }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'action'">
                        <a-button type="link" @click="showModal('policyAudit',record)" size="small">
                            审核
                        </a-button>
                    </template>
                </template>
            </a-table>
            <Pagination
                    :paginations="paginations"
                    @change="pagin"
                    :scrollY="slotProps.scrollY"
            />
        </template>
        <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" v-if="isModalVisible"/>
    </ListPageLayout>
</template>
