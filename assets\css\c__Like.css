.CornerBubble-module_cornerBubble_2kGnu {
    position: fixed;
    width: 42px;
    height: 42px;
    display: flex;
    right: 24px;
    bottom: 86px;
    justify-content: space-around;
    align-items: center;
    border-radius: 50%;
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04);
    color: var(--yq-text-body);
    cursor: pointer;
    z-index: 997;
    background: var(--yq-bg-secondary)
}

.CornerBubble-module_cornerBubble_2kGnu:hover {
    background: var(--yq-yuque-grey-4)
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_cornerCount_O3YTX {
    position: absolute;
    right: -6px;
    top: -6px;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 12px;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-primary)
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_cornerCountLarge_3x8Br {
    width: auto;
    padding: 0 6px;
    border-radius: 10px
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_cornerCountHover_x2J7h:hover {
    background-color: var(--yq-bg-primary-hover)
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_cornerIcon_N8Ibc {
    width: 42px;
    height: 42px
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_btnWord_-QoZm {
    color: var(--yq-text-body);
    font-size: 14px;
    font-weight: 500
}

.CornerBubble-module_cornerBubble_2kGnu .CornerBubble-module_badge_SQuZN {
    position: absolute;
    right: -20px;
    top: -10px;
    justify-content: center;
    align-items: center
}

html[data-kumuhana=pouli] .like-module_simplifyLike_GZF9s .like-module_text_mdOB6,html[data-kumuhana=pouli] .like-module_simplifyLike_GZF9s>svg {
    color: var(--yq-text-body)
}

@keyframes like-module_like_ywZuk {
    to {
        background-position: -2160px 0;
        overflow: hidden
    }
}

@keyframes like-module_shake_0jtRf {
    25% {
        transform: rotate(6deg)
    }

    75% {
        transform: rotate(-6deg)
    }

    to {
        transform: rotate(0)
    }
}

.like-module_larkLike_zeNuZ {
    text-align: center
}

.like-module_larkLikeIcon_5ZWgd {
    position: absolute;
    left: -18px;
    bottom: -2px;
    z-index: 1;
    display: inline-block;
    margin: -1px 0;
    width: 80px;
    height: 80px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*eDsBTKcm1IcAAAAAAAAAAAAAARQnAQ) no-repeat 0 0;
    background-size: auto 80px
}

html[data-kumuhana=pouli] .like-module_larkLikeIcon_5ZWgd {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*M-nVTIujAMsAAAAAAAAAAAAAARQnAQ) no-repeat 25px 39px;
    background-size: 37%
}

.like-module_larkLikeBtnActive_5GFT6 {
    overflow: visible
}

.like-module_larkLikeBtnActive_5GFT6 .like-module_larkLikeIcon_5ZWgd {
    animation-name: like-module_like_ywZuk;
    animation-duration: .9s;
    animation-iteration-count: 1;
    animation-timing-function: steps(27);
    animation-fill-mode: forwards
}

html[data-kumuhana=pouli] .like-module_larkLikeBtnActive_5GFT6 .like-module_larkLikeIcon_5ZWgd {
    animation-name: none;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*2pR5RZPHQx8AAAAAAAAAAAAAARQnAQ) no-repeat 25px 32px;
    background-size: 35%
}

.like-module_larkLikeBtn_uHsxq {
    position: relative;
    margin: 10px auto;
    width: 40px;
    height: 50px;
    text-align: center;
    cursor: pointer;
    overflow: hidden
}

.like-module_larkLikeBtnNoAnimate_ugzKL .like-module_larkLikeIcon_5ZWgd {
    overflow: hidden;
    animation-duration: 0s
}

.like-module_larkLikeBtnShake_0G6vd .like-module_larkLikeIcon_5ZWgd {
    animation-name: like-module_shake_0jtRf;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-out;
    transform-origin: 25px 67px
}

.like-module_larkLikeCount_eLa4m {
    font-size: 14px;
    color: var(--yq-text-caption)
}

.like-module_larkLikeCount_eLa4m>span {
    display: inline-block;
    min-width: 14px;
    text-align: center
}

.like-module_larkLikeDocTip_\+Obkt {
    text-align: center;
    padding-bottom: 62px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.like-module_userListWrapper_Y79Yc {
    position: relative;
    max-height: 336px;
    overflow-y: auto;
    padding-bottom: 40px
}

.like-module_userListWrapper_Y79Yc .larkui-spin {
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    color: var(--yq-text-caption)
}

.like-module_userList_SKTTT {
    margin-top: 22px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-left: -8px
}

.like-module_userList_SKTTT>li {
    margin-left: 8px;
    margin-bottom: 8px
}

.like-module_userList_SKTTT .like-module_showLikeList_TUchs {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    line-height: 32px;
    border: 1px solid var(--yq-border-primary);
    cursor: pointer;
    font-size: 18px
}

.like-module_readerUserList_hfxMS {
    justify-content: center
}

.like-module_modal_kOEJe .ant-modal-body {
    padding: 48px 22px 40px
}

.like-module_sum_fSlWD {
    display: flex;
    align-items: flex-end;
    justify-content: center
}

.like-module_sumTip_VbrM1 {
    margin-top: 8px;
    font-size: 12px;
    color: var(--yq-text-body);
    text-align: center
}

.like-module_sumIcon_jnfi2 {
    margin-left: 8px;
    margin-right: -23px
}

.like-module_sumCount_AljHR {
    margin-right: 4px;
    font-size: 40px;
    font-weight: 500;
    line-height: 1;
    color: var(--yq-text-primary)
}

.like-module_simplifyLike_GZF9s {
    position: relative;
    top: -3px;
    cursor: pointer
}

.like-module_simplifyLike_GZF9s.like-module_isMini_LHe3B {
    display: flex;
    align-items: center
}

.like-module_simplifyLike_GZF9s.like-module_isMini_LHe3B>svg:hover {
    color: var(--yq-yellow-6)
}

.like-module_simplifyLike_GZF9s.like-module_isMini_LHe3B .like-module_text_mdOB6 {
    position: relative;
    top: 3px;
    margin-left: 4px
}

.like-module_modalContentIcon_K\+o1v {
    width: 30px;
    position: relative;
    left: 50%;
    margin-left: -15px;
    margin-bottom: 12px
}

.like-module_modalContentCount_6ku9X {
    font-size: 14px;
    color: var(--yq-text-caption);
    text-align: center
}

.like-module_likePopover_yBbcz .ant-popover-arrow {
    display: none
}

.like-module_likePopover_yBbcz .ant-popover-inner-content {
    padding: 0
}

.like-module_popoverUserContainer_FwwqR {
    max-height: calc(100vh - 160px);
    overflow-y: auto
}

.like-module_popoverUserList_ZjVm8 {
    padding: 10px 0
}

.like-module_popoverUserList_ZjVm8 .like-module_popoverUserItem_Hf89G {
    display: flex;
    width: 160px;
    overflow: hidden;
    padding: 6px 16px
}

.like-module_popoverUserList_ZjVm8 .like-module_popoverUserItem_Hf89G:hover {
    background-color: var(--yq-bg-tertiary)
}

.like-module_popoverUserList_ZjVm8 .like-module_popoverUserItem_Hf89G .like-module_popoverUserItemAvatar_Xvk9i {
    margin-right: 8px
}

.like-module_popoverUserList_ZjVm8 .like-module_popoverUserItem_Hf89G .like-module_popoverUserItemName_aIDSI {
    font-size: 14px;
    color: var(--yq-text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteContainer_1caED {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V {
    position: relative;
    top: 8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -8px;
    left: -8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteH5Container_ituBE {
    position: relative;
    left: 2px
}

.index-module_mainInput_x4D9t.ant-input-affix-wrapper {
    max-width: 300px;
    width: 172px
}

.index-module_verifyResult_pdxsH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5);
    font-weight: 400
}

.index-module_verifyResult_pdxsH.index-module_success_NhSta {
    color: var(--yq-pea-green-6)
}

.index-module_inputVerify_kl-bC {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-weight: 500
}

.index-module_editIcon_eXpln {
    margin-left: 8px
}

.index-module_promoCodeDefault_6iUh6 {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: var(--yq-text-body);
    font-weight: 400;
    text-align: right;
    cursor: pointer
}

.index-module_orgCostDetail_cLnEB {
    cursor: pointer;
    font-weight: 500
}

.index-module_content_oAhH8 {
    width: 378px
}

.index-module_content_oAhH8 .ant-divider {
    margin: 12px 0
}

.index-module_row_by2z2 {
    display: flex;
    justify-content: space-between;
    padding: 6px 0
}

.index-module_row_by2z2:last-child {
    font-weight: 700
}

.index-module_row_by2z2 .index-module_right_3Ce8Y {
    display: flex
}

.index-module_tips_EUcPA {
    padding: 2px 0;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_title_J6b5y {
    padding: 8px 8px 8px 0
}

.index-module_title_J6b5y a {
    color: var(--yq-text-caption)
}

.index-module_hasDiscount_TqEsK {
    color: var(--yq-orange-7)
}

.index-module_rawPrice_2UTkU {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_detailsWrapper_rnkfa .index-module_detailTitle_-gjun {
    color: var(--yq-text-primary)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt {
    height: 72px;
    max-height: 80px;
    overflow-y: auto
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK span:nth-child(2n) {
    color: var(--yq-text-body)
}

.index-module_summary_TYNAM {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_summary_TYNAM .index-module_left_m83qX {
    display: block
}

.index-module_summary_TYNAM .index-module_left_m83qX .index-module_summaryTitle_FTeFf {
    color: var(--yq-text-primary)
}

.index-module_summary_TYNAM .index-module_left_m83qX span {
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_summary_TYNAM .index-module_right_3Ce8Y .index-module_price_5CyQB {
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_divider_Rppou {
    margin: 12px auto
}

.index-module_paymentSelector_60wvq {
    margin: 0 0 24px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_paymentSelector_60wvq h4 {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e {
    position: relative;
    padding: 16px 10px;
    height: 100%;
    border-radius: 8px;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 {
    display: flex;
    justify-content: space-between
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_icon_sjp\+e {
    margin-left: 6px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_paymentType_rBxoj {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceNumber_6EuVJ {
    color: var(--yq-text-primary);
    font-size: 12px;
    font-weight: 500
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceUnit_xqqPQ {
    color: var(--yq-text-caption);
    font-size: 10px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_oldMemberPrice_4QJhr {
    display: block;
    text-align: right;
    font-size: 10px;
    color: var(--yq-text-caption);
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_desc_5wzpx {
    position: absolute;
    bottom: 16px;
    margin-top: 6px;
    width: 160px;
    font-size: 12px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_active_HZTq5 {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-theme)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: hsla(0,0%,100%,.5);
    cursor: not-allowed
}

html[data-kumuhana=pouli] .index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    background-color: rgba(0,0,0,.5)
}

.index-module_tooltip_lMvUZ {
    font-size: 14px;
    white-space: nowrap
}

.index-module_tooltip_lMvUZ a {
    margin-left: 8px
}

.BuyModal-module_row_k9bGD {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyModal-module_row_k9bGD h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 {
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 span {
    color: var(--yq-text-caption)
}

.BuyModal-module_row_k9bGD .BuyModal-module_right_-\+dqF {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq {
    display: flex;
    align-items: center
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq svg {
    margin-right: 6px
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payBtn_JujzZ {
    margin: 12px auto;
    width: 100%
}

.BuyModal-module_promoCode_IT7ha a,.BuyModal-module_promoCode_IT7ha a:hover {
    color: var(--yq-text-primary)
}

.BuyModal-module_tips_fuHYC {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingContainer_tqVEA {
    display: flex;
    justify-content: space-between;
    padding: 32px;
    min-height: 688px;
    background-color: var(--yq-yuque-grey-2);
    background-position: 0 100%;
    background-repeat: no-repeat;
    border-radius: 8px
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*z00vQYi5mvAAAAAAAAAAAAAAARQnAQ);
    background-size: 274px 215px
}

html[data-kumuhana=pouli] .BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*4iv1S4HXxg4AAAAAAAAAAAAADvuFAQ/original)
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_enterprise_KQPTt {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*h_IRR6H2EB8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 238px 170px
}

.BuyModal-module_pricingLeft_X4ns6 {
    width: 324px;
    padding-top: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG .BuyModal-module_icon_UPf-t {
    margin-left: 8px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentDesc_7e1Uv {
    margin: 4px 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL {
    display: inline-block;
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL .BuyModal-module_icon_UPf-t {
    margin-left: 0;
    vertical-align: text-top
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe {
    margin-bottom: 40px;
    position: relative;
    padding-left: 48px;
    height: 46px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_iconWarpper_j2vhb {
    position: absolute;
    top: 50%;
    left: 0;
    width: 36px;
    height: 36px;
    transform: translateY(-50%);
    background-color: var(--yq-bg-primary);
    box-shadow: 0 2px 9px 0 rgba(0,0,0,.02);
    border-radius: 6px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_icon_UPf-t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_title_aoygt {
    margin-bottom: 4px;
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_desc_nWmKR {
    color: var(--yq-text-caption);
    font-size: 12px
}

.BuyModal-module_pricingRight_p54lJ {
    position: relative;
    padding: 24px 24px 98px 24px;
    width: 420px;
    min-height: 624px;
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .BuyModal-module_pricingRight_p54lJ {
    background: var(--yq-bg-tertiary)
}

.BuyModal-module_avatar_SRhTt {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background-color: var(--yq-bg-tertiary)
}

.BuyModal-module_termsContainer_i187T span {
    color: var(--yq-text-body)!important
}

.BuyModal-module_termsContainer_i187T .ant-checkbox+span {
    padding-right: 0
}

.BuyModal-module_statsWrapper_ZDvF1 .BuyModal-module_statsDesc_1iKOA {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 28px
}

.BuyModal-module_compact_qVAW5 h3 {
    margin-bottom: 4px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_active_SC0g\+ {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_settle_6sm-p {
    margin-left: 12px
}

.BuyModal-module_channel_aD1hq button[disabled] {
    padding: 4px 12px;
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyModal-module_channel_aD1hq button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.ant-btn.BuyModal-module_channel_aD1hq {
    padding: 4px 12px
}

.BuyModal-module_name_bL3Ia {
    max-width: 230px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BuyModal-module_memberSizeInput_xIYHO {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.BuyModal-module_memberSizeInput_xIYHO .BuyModal-module_errorMsg_ZbqFE {
    position: absolute;
    bottom: -24px;
    max-width: 200px;
    white-space: nowrap;
    color: var(--yq-function-error);
    font-weight: 400;
    font-size: 14px
}

.BuyModal-module_footer_eF3uw {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 48px);
    transform: translateX(-50%)
}

.BuyMember-module_row_9srYf {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyMember-module_row_9srYf h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og {
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og span {
    color: var(--yq-text-caption)
}

.BuyMember-module_row_9srYf .BuyMember-module_right_Oni7O {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT {
    display: flex;
    align-items: center
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT svg {
    margin-right: 6px
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payBtn_2QBqR {
    margin: 12px auto;
    width: 100%
}

.BuyMember-module_promoCode_3q8my a,.BuyMember-module_promoCode_3q8my a:hover {
    color: var(--yq-text-primary)
}

.BuyMember-module_tips_0MaeW {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyMember-module_pricingContainer_oSImw h2 {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_expiredDesc_kyKaA {
    margin-bottom: 24px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BuyMember-module_channel_oCioT.BuyMember-module_active_hyrgK {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyMember-module_channel_oCioT button[disabled] {
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyMember-module_channel_oCioT button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_right_Oni7O>:not(:first-child) {
    margin-left: 12px
}

.Pay-module_row_02i1B {
    padding-bottom: 24px
}

.Pay-module_row_02i1B:nth-child(6) {
    padding-bottom: 0
}

.Pay-module_row_02i1B .Pay-module_left_u3-Jq {
    padding-bottom: 8px
}

.Pay-module_row_02i1B .Pay-module_name_4PPzr {
    font-size: 16px;
    color: var(--yq-text-primary);
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px
}

.Pay-module_row_02i1B .Pay-module_nameRow_iKvRZ {
    display: flex
}

.Pay-module_row_02i1B .Pay-module_version_C36aU {
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-8);
    border-radius: 4px;
    font-size: 12px;
    padding: 1px 3px;
    background-color: var(--yq-blue-1)
}

.Pay-module_row_02i1B .Pay-module_version_C36aU.Pay-module_paidVersion_pBnh8 {
    color: var(--yq-yellow-7);
    border-color: var(--yq-yellow-7);
    background-color: var(--yq-yellow-1)
}

.Pay-module_cardList_OD1er {
    display: flex;
    margin-bottom: 24px
}

.Pay-module_memberSize_Snqz8 {
    overflow: hidden;
    opacity: 0;
    height: 0;
    transition: all .3s
}

.Pay-module_memberSize_Snqz8.Pay-module_show_u86Cd {
    opacity: 1;
    height: 81px
}

.Pay-module_totalInfo_YM7Fb {
    height: 45px;
    margin: 4px 0 18px 0;
    flex-wrap: wrap;
    font-weight: 600
}

.Pay-module_footer_MsuSK,.Pay-module_totalInfo_YM7Fb {
    display: flex;
    align-items: center
}

.Pay-module_footer_MsuSK .Pay-module_termsContainer_MMlAf {
    margin-left: 12px;
    display: flex
}

.Pay-module_pricingContainer_hjkUd {
    padding-bottom: 8px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_desc_a1fXI {
    margin-top: -20px;
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_dashline_y-S0k {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 24px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingInfo_NRstM {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingMainTitle_pinlM {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingSubTitle_FlFF9 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_inputNumber_Uaz26 {
    height: 32px;
    width: 200px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingDesc_SK8fY {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz {
    font-size: 28px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 45px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz.Pay-module_hasDiscount_KLNoP {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz {
    margin-left: 16px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz .Pay-module_discountPriceValue_CUsg2 {
    font-size: 28px;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWith_vFDxq {
    margin: 8px 0 20px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayIcon_SjV5X {
    width: 24px;
    margin-top: -2px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayText_r3AWK {
    padding-left: 8px
}

.Pay-module_uploadPackages_VO59r {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 32px
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL {
    width: 160px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL:nth-child(3n) {
    margin-right: 0
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL.Pay-module_selectedUploadCard_yWd2Y {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(37,184,100,.05)
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadSize_WpwBc {
    font-size: 16px;
    font-weight: 600
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackagePrice_zQPrm {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_buyMemberCon_CB8\+C {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q,.Pay-module_buyMemberCon_CB8\+C .Pay-module_upgradeMember_6duT1 {
    background-color: var(--yq-bg-secondary);
    padding: 16px;
    width: 244px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q .Pay-module_memberValue_l6w8U {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0
}

.Pay-module_tips_BIW\+T {
    margin-top: 6px;
    color: var(--yq-text-caption);
    width: 500px
}

.Pay-module_discountInfo_Uq4\+K {
    color: var(--yq-text-caption);
    padding-bottom: 8px
}

.Pay-module_promo_sD6ap {
    margin-top: -6px
}

.Pay-module_promo_sD6ap .Pay-module_left_u3-Jq {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 120px
}

.Pay-module_promo_sD6ap .Pay-module_inputNumber_Uaz26 {
    padding-top: 8px;
    padding-bottom: 40px
}

.Qrcode-module_info_XwHqZ {
    flex: auto;
    max-width: 332px
}

.Qrcode-module_infoTitle_nNa5e {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500
}

.Qrcode-module_subInfoTip_DYh05 {
    color: var(--yq-text-body);
    margin-top: 10px
}

.Qrcode-module_infoTip_wp2zv>span {
    margin-right: 28px
}

@media only screen and (max-width: 575px) {
    .Qrcode-module_infoTip_wp2zv>span {
        display:block
    }
}

.Qrcode-module_desc_T1-nf {
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi {
    padding-bottom: 8px
}

.Qrcode-module_pricingContainer_2\+hyi h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_dashline_n20jZ {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingInfo_Pe1Ai {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingMainTitle_D0fu0 {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingSubTitle_3SVt2 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P .Qrcode-module_select_2Topv {
    width: 140px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingDesc_aTeur {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_priceTag_gSsWI {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWith_Orohw {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_totalInfo_-\+rtG {
    margin: 4px 0 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayIcon_Hoi7K {
    width: 24px;
    margin-top: -2px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayText_wbuGd {
    padding-left: 8px
}

.Qrcode-module_processContainer_5BqWV {
    text-align: center;
    padding: 10px 0
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processImg_MgbCi {
    width: 120px
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processInfo_ST\+BP {
    margin-top: 12px
}

.Qrcode-module_termsContainer_Uqaqj {
    margin-bottom: 12px;
    display: flex
}

.Qrcode-module_bindingContainer_b3OnQ {
    padding: 24px 0;
    text-align: center
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingImg_sEWSW {
    width: 80px
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingInfo_qhBjF {
    color: var(--yq-text-primary);
    margin: 24px 0
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingFooter_7PjRS .Qrcode-module_bindingVerify_CgNHw {
    margin-right: 8px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi {
    position: relative
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 img {
    width: 100%
}

@media only screen and (max-width: 768px) {
    .Qrcode-module_qrcodeImageCon_yy\+Gi .alipayIconInQrCode {
        width:36px;
        height: 36px;
        margin-left: -18px;
        margin-top: -18px
    }
}

.Qrcode-module_successContainer_RvR5s {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfoCon_Yt35q {
    padding: 10px 40px 30px 40px
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfo_w7k6O {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.Qrcode-module_qrContainer_K9uEQ {
    margin-bottom: 20px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeImg_GAmpl {
    width: 72px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeInfo_8NXdM {
    font-size: 14px;
    font-weight: 400;
    padding: 24px 0 0 30px;
    color: var(--yq-text-body);
    line-height: 1.8
}

.Qrcode-module_qrcodeContainer_c79eX {
    min-height: 444px;
    text-align: center
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_qrcodeImage_UEIyZ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    width: 55%
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc1_ArZR9 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc2_E5sDl {
    margin-top: 10px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_link_ZEjlM {
    margin-top: 8px
}

.Qrcode-module_receiptTypeTitle_jIYOl {
    margin-bottom: 10px
}

.Qrcode-module_receiptTypeTitleText_8q0XQ {
    margin-bottom: 8px
}

.Qrcode-module_applyReceiptButton_Wfqmq {
    margin-left: 10px
}

.Qrcode-module_contactTips_ETe9S {
    margin-bottom: 10px
}

.Qrcode-module_buttonCon_Jic-k {
    margin-top: 8px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleDesc_OOQby {
    font-size: 16px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleValue_PBwE\+ {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.PayModal-module_noLineModal_r4djn .ant-modal-header {
    border-bottom: 0
}

.PayModal-module_warnContent_TTDov {
    margin-top: 20px;
    margin-bottom: 24px
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-body {
    padding: 0
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px
}

.ProcessingModal-module_processContainer_dOZ9t {
    text-align: center;
    padding: 10px 0
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processImg_Fl\+7o {
    width: 120px
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processInfo_VPh0u {
    margin-top: 12px
}

.paid-success-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.paid-success-modal .ant-modal-body {
    padding: 0
}

.paid-success-modal .ant-modal-body img {
    width: 100%
}

.paid-success-modal .ant-col {
    padding-top: 10px
}

.paid-success-modal .ant-col-18 {
    font-weight: 600
}

.PaidSuccessModal-module_successModalContainer_gb6Iw {
    color: var(--yq-text-body);
    border-radius: 8px;
    overflow: hidden
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoCon_aLoy0 {
    padding: 20px 24px 30px 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfo_TQeHl {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoTitle_fmm0I {
    font-size: 28px;
    margin-top: 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns {
    margin-top: 32px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_applyReceiptButton_BrptN {
    margin-left: 12px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_checkUploadButton_n0Sct {
    margin-left: 12px;
    margin-top: 1px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalApplyReceiptButton_hbmRn {
    margin-left: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalSubTitle_Hq8oy {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalItem_EBnTl {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalBlank_3ENdm {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.receipt-module_contactInfoTitle_\+WTvl {
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 10px 0
}

.receipt-module_receiptButtons_tBTuB {
    margin-top: 8px
}

.receipt-module_cancelButton_1xXKN {
    margin-left: 10px
}

.ReceiptModal-module_contactTips_f0fAJ,.ReceiptModal-module_receiptTypeTitle_mZi8G {
    margin-bottom: 10px
}

.ReceiptModal-module_receiptTypeTitleText_QQBhY {
    margin-bottom: 8px
}

.ReceiptModal-module_receiptDesc_fY2bw {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

@keyframes SubAccountInfoModal-module_loadingCircle_mPmQ5 {
    to {
        transform: rotate(1turn)
    }
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p:first-child {
    border-bottom: 1px dashed var(--yq-border-primary);
    padding-bottom: 16px;
    margin-bottom: 32px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldTitle_tgwmv {
    display: inline-block;
    font-weight: 600;
    min-width: 80px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p p {
    margin-bottom: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldPaymentAlert_UOLk7 {
    margin-bottom: 20px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e {
    margin-top: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e .SubAccountInfoModal-module_btn_WuNy4 {
    margin-right: 16px
}

.SubAccountInfoModal-module_loading_sNKQL {
    padding: 32px 0;
    text-align: center
}

.SubAccountInfoModal-module_loading_sNKQL .SubAccountInfoModal-module_loadingIcon_M7IJr {
    animation: SubAccountInfoModal-module_loadingCircle_mPmQ5 1s linear infinite
}

.SubAccountInfoModal-module_loading_sNKQL .larkui-icon {
    font-size: 24px
}

.SubAccountInfoModal-module_loadingDesc_QHGbP {
    margin-top: 16px
}

.index-module_buyButton_pN7y0.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_buyButton_pN7y0a.ant-btn span {
    line-height: 1
}

.index-module_upgradeButton_nJqrZ.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_upgradeButton_nJqrZa.ant-btn span {
    line-height: 1
}

.index-module_orgVersionLabel_egPu3 {
    border-width: 1px;
    border-radius: 4px;
    border-style: solid;
    padding: 0 4px;
    margin-left: 16px
}

.index-module_tip_rQx-e {
    margin-top: 2px;
    margin-bottom: 2px;
    display: flex;
    justify-content: space-between
}

.index-module_tip_rQx-e a:before {
    position: relative
}

.index-module_tip_rQx-e span span {
    margin-left: 12px
}

.index-module_tip_rQx-e .index-module_payment_pQExj {
    color: var(--yq-yuque-grey-8)
}

.index-module_tip_rQx-e .index-module_paymentLink_GbvgZ {
    color: var(--yq-blue-6);
    line-height: 60px
}

.index-module_paymentGuideWrapper_2jg39 {
    max-width: 584px;
    margin-left: auto;
    margin-right: auto;
    padding: 24px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_thumb_Lw5zW {
    display: block;
    width: 148px;
    height: 120px;
    margin: 0 auto 16px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100%
}

.index-module_paymentGuideWrapper_2jg39 .index-module_title_EykNL {
    text-align: center;
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 700
}

.index-module_paymentGuideWrapper_2jg39 .index-module_body_\+1-cr {
    margin: 12px auto 32px auto;
    max-width: 400px;
    color: var(--yq-text-body);
    text-align: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_link_zDdoa {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB {
    display: flex;
    align-items: center;
    justify-content: end
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB .index-module_btnTryout_5GVmK {
    margin-right: 8px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_btnTryout_5GVmK {
    margin-bottom: 12px;
    width: 148px;
    height: 32px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_link_zDdoa {
    width: 148px;
    text-align: center
}

.index-module_actions_LRgdL .index-module_btn_vRXZD {
    width: 160px;
    height: 32px
}

.index-module_premiumFeaturesLabel_q\+uFd {
    width: 86px;
    height: 37px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*pIrCQLn06McAAAAAAAAAAAAAARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_alignCenter_h648T {
    display: flex;
    align-items: center;
    position: relative
}

.index-module_orgExpiredTip_k7iLg {
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgExpiredTipOpt_QDhkZ,.index-module_orgExpiredTipText_gQuZw {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    width: 140px
}

.index-module_orgExpiredTipText_gQuZw {
    white-space: nowrap
}

.index-module_orgExpiredTipText_gQuZw span {
    color: var(--yq-yuque-grey-9)
}

.index-module_payflowTitle_LM3aR {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px
}

.index-module_freeWrapper_BlI2K {
    display: flex;
    justify-content: space-between
}

.index-module_payflowLink_Wt3fM {
    margin-left: 0;
    display: inline-block
}

.index-module_isExpired_UgOgs {
    border-bottom: 1px solid var(--yq-yuque-grey-4);
    padding-bottom: 10px
}

.index-module_freeTips_ybaQ- {
    display: flex;
    justify-content: space-between
}

.index-module_btnNewOrg_V6nKz {
    display: flex;
    align-items: flex-start;
    line-height: 1.35;
    padding-left: 43px!important
}

.index-module_btnNewOrg_V6nKz h6 {
    position: relative!important
}

.index-module_btnNewOrg_V6nKz .icon-svg {
    position: absolute;
    left: -28px;
    top: 1px;
    color: var(--yq-yuque-grey-9)
}

.index-module_btnNewOrg_V6nKz .index-module_orgAddText_98X0I {
    margin-left: 11px
}

.index-module_btnNewOrg_V6nKz .index-module_tag_DSBkQ {
    display: inline-flex;
    margin-left: 8px;
    padding: 0 6px;
    color: var(--yq-yuque-green-7);
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    background: var(--yq-yuque-green-1);
    border-radius: 3px
}

.index-module_upgrade_qL2nR .ant-btn-link {
    padding: 0;
    height: auto
}

.index-module_wrapper_ulVDd .ant-modal-body {
    padding: 0!important
}

.index-module_wrapper_ulVDd .anticon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-information {
    display: none
}

.index-module_wrapper_ulVDd .ant-modal-confirm-content {
    width: 480px;
    margin-top: 0
}

.index-module_wrapper_ulVDd .ant-modal-confirm-btns {
    display: none
}

.index-module_wrapper_ulVDd .index-module_content_SO1HL {
    margin: 28px 0;
    padding: 0 24px
}

.index-module_wrapper_ulVDd .index-module_title_SVPzd {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_wrapper_ulVDd .index-module_desc_MlCQ9 {
    margin-top: 16px;
    margin-bottom: 32px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_wrapper_ulVDd .index-module_img_SBaEY {
    width: 480px
}

.index-module_trialButton_tH3iD {
    margin-left: 10px
}

.index-module_footer_cL9eu {
    display: flex;
    justify-content: flex-end
}

.index-module_footer_cL9eu .index-module_btn_XNqZC {
    margin-left: 10px
}
