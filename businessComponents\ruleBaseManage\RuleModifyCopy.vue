<!-- 规则名修改并复制组件 -->

<template>
    <div id="rule_attribute">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-width="120px">
        <div v-for="(item,index) in formData" :key="index">
            <a-form-item label="原规则名">
                <label>{{item.ruleName}}</label>
            </a-form-item>
            <a-form-item label="修改为" name="ruleName">
                <a-input autocomplete="off" v-model:value="form.ruleName[index]" placeholder="规则名称"></a-input>
            </a-form-item>
        </div>
        </a-form>
    </div>
</template>

<script setup>
    import { copySaveMore, getRuleListByUuids } from "@/api/rule_base";
    import { showGlobalLoading, hideGlobalLoading } from '@/utils/loading';
    import qs from 'qs'

    const message = inject('message')

    const props = defineProps({
        datar: {
            type: Object,
        },
    })

    const form = reactive({
        ruleName: [],
    });

    const rules = {
        ruleName: [{ required: true, message: "不能为空", trigger: "blur" }],
    };
    const ruleForm = ref(null);
    const formData = ref([]);
    // todo 下面这两个ref感觉不需要，待进一步确认
    const BaseId = ref('');
    const demandUuid = ref('');

    const emit = defineEmits(['removeTab', 'addTab']);

    const handleSubmit = (callback) => {
        ruleForm.value.validate()
            .then(() => {
                showGlobalLoading('修改并复制中，请耐心等待');
                copySaveMore(qs.stringify({
                    copyList: props.datar.uuids,
                    folderUuid: props.datar.folderUuid,
                    newNames: form.ruleName.toString(),
                    newEngUuid:props.datar.newEngUuid
                })).then((res) => {
                    if (res.code === 20000) {
                        hideGlobalLoading();
                        message.success(res.data);
                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        hideGlobalLoading();
                        message.error(res.data);
                    }
                }).catch(()=>{
                    hideGlobalLoading();
                });
            })
            .catch((error) => {
                console.log(error);
            });
    };
    onMounted(async () => {
        demandUuid.value = '';
        BaseId.value = props.ruleBaseId;
        getRuleListByUuids({
            uuids: props.datar.uuids,
        }).then((res) => {
            formData.value = res.data;
            res.data &&
            res.data.length &&
            res.data.filter((i) => {
                form.ruleName.push(i.ruleName);
            });
        });
    });

    defineExpose({
        handleSubmit
    });
</script>

<style lang="scss" scoped>
    #rule_attribute {
        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-upload-dragger),
        :deep(.ant-upload-list),
        :deep(.ant-input-textarea) {
            width: 400px;
        }

        :deep(.ant-input-textarea) {
            height: 400px;
        }

        :deep(.ant-upload-dragger) {
            height: 100px;
        }

        :deep(.ant-upload-dragger .anticon-upload) {
            margin: 0;
            line-height: 50px;
            font-size: 50px;
        }

        :deep(.jarUpload.hidden .ant-upload) {
            display: none;
        }

        :deep(.ant-tabs) {
            width: 70%;
            min-height: 100px;
            margin: 30px 30px 30px 120px;
        }

        :deep(.ant-input-textarea),
        :deep(textarea) {
            height: 150px !important;
        }

        :deep(.ant-form) {
            padding-top: 30px;
        }

        :deep(.ant-form-item-explain-error) {
            color: #ff4d4f !important;
        }
    }
</style>
