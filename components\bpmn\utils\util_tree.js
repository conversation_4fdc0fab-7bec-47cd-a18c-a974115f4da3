// util_tree.js
import conditionIcon from '@/components/ruleFlow/img/condition.png';
import actionIcon from '@/components/ruleFlow/img/action.png';

const customElements = ['bpmn:ExclusiveGateway', 'bpmn:Task']; // 自定义元素的类型
const customConfig = { // 自定义元素的配置
    'bpmn:ExclusiveGateway': {
        'url': conditionIcon,
        'attr': { x: 0, y: 0, width: 36, height: 36 }
    },
    'bpmn:Task': {
        'url': actionIcon,
        'attr': { x: 0, y: 0, width: 36, height: 36 }
    }
};

export { customElements, customConfig };
