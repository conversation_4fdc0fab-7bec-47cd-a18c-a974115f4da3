<template>
  <div>
    <!-- 使用 TableValidate 组件，并传递 props -->
    <TableValidate
      :props_columns="columns"
      :props_data="data"
      :validate="validate"
    />
  </div>
</template>

<script setup>
import TableValidate from "./TableValidate.vue";
import * as tableUtil from "../util";

// 注入 ruleUuid
const ruleUuid = inject('ruleUuid', '');

// 定义 props
const props = defineProps({
  ruleContent: {
    type: Object,
    default: () => ({})
  },
  validateResult: {
    type: Array,
    default: () => []
  }
});

// 定义响应式数据
const colConditionKey = ref(1001);
const colActionKey = ref(2001);
const rowKey = ref(10001);
const colMap = ref([]);
const newCondition = ref(1);
const newAction = ref(1);
const _newCol = ref([]);

const columns = ref([]);
const data = ref([]);
const validate = ref([]);

// 初始化数据
columns.value = colConverter();
data.value = rowConverter();
validate.value = getValidateResult(props.validateResult);

// 监听 validateResult 的变化
watch(
  () => props.validateResult,
  (val) => {
    validate.value = getValidateResult(val);
  },
  { deep: true }
);

// 获取验证结果
function getValidateResult(validateResult = []) {
  const newRes = [];
  validateResult.map(item => {
    newRes.push([...item.conditionValids, ...item.actionValids]);
  });
  return newRes;
}

// 转换列
function colConverter() {
  const { ruleContent = {} } = props;
  const { definition = {} } = ruleContent;
  let columns;
  if (Object.keys(definition).length > 0) {
    columns = [
      ...prospConditions(ruleContent),
      ...prospActions(ruleContent)
    ];
  } else {
    columns = [...initConditions(), ...initActions()];
  }
  _newCol.value = columns;
  return columns;
}

// 处理条件列
function prospConditions() {
  const { ruleContent } = props;
  const data = tableUtil.getPropsTbCondition(
    colConditionKey.value,
    colMap.value,
    ruleContent,
    ruleUuid
  );
  const { colConditionKey: newCol, colMap: newMap, initCondition } = data;
  colConditionKey.value = newCol;
  colMap.value = newMap;
  return initCondition;
}

// 处理动作列
function prospActions() {
  const { ruleContent, ruleType = "" } = props;
  let data = null;
  if (ruleType === "ruleCard") {
    data = tableUtil.getPropsCardAction(
      colActionKey.value,
      colMap.value,
      ruleContent
    );
  } else {
    data = tableUtil.getPropsTbAction(
      colActionKey.value,
      colMap.value,
      ruleContent,
      ruleUuid
    );
  }
  const { colActionKey: newCol, colMap: newMap, initAction } = data;
  colActionKey.value = newCol;
  colMap.value = newMap;
  return initAction;
}

// 初始化条件列
function initConditions() {
  const data = tableUtil.getInitCondition(
    colConditionKey.value,
    newCondition.value,
    colMap.value,
    ruleUuid
  );
  const {
    colConditionKey: newCol,
    newCondition: newCon,
    colMap: newMap,
    initCondition
  } = data;
  colConditionKey.value = newCol;
  newCondition.value = newCon;
  colMap.value = newMap;
  return initCondition;
}

// 初始化动作列
function initActions() {
  const data = tableUtil.getInitActions(
    colActionKey.value,
    newAction.value,
    colMap.value,
    ruleUuid
  );
  const {
    colActionKey: newCol,
    newAction: newAct,
    colMap: newColMap,
    initAction
  } = data;
  colActionKey.value = newCol;
  newAction.value = newAct;
  colMap.value = newColMap;
  return initAction;
}

// 转换行数据
function rowConverter() {
  const { ruleContent = {} } = props;
  const { definition = {} } = ruleContent;
  let data;
  if (Object.keys(definition).length > 0) {
    data = propsDatas(_newCol.value);
  } else {
    data = initDatas();
  }
  return data;
}

// 处理 props 数据
function propsDatas(columns) {
  const {
    ruleContent: { rows }
  } = props;
  const data = tableUtil.getPropsTbData(
    columns,
    rowKey.value,
    colMap.value,
    rows
  );
  const { rowKey: newKey, colMap: newMap, initData } = data;
  rowKey.value = newKey;
  colMap.value = newMap;
  return initData;
}

// 初始化数据
function initDatas() {
  const initData = [{}];
  colMap.value.map(item => {
    initData[0][item] = "";
  });
  initData[0].key = "rule" + rowKey.value;
  rowKey.value += 1;
  return initData;
}
</script>
