<!-- 复制图标样式 -->

<script setup lang="ts">
    interface Props {
        size?: number
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 16
    })
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-pencil-underscore icon-svg TemplateTreeItem-module_actionIcon_haD5C index-module_size_wVASz" :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }"><g fill="currentColor" fill-rule="nonzero"><path d="M198 28h-80c-16.569 0-30 13.431-30 30v80c0 16.569 13.431 30 30 30h80c16.569 0 30-13.431 30-30V58c0-16.569-13.431-30-30-30Zm-80 20h80c5.523 0 10 4.477 10 10v80c0 5.523-4.477 10-10 10h-80c-5.523 0-10-4.477-10-10V58c0-5.523 4.477-10 10-10Z"></path><path d="M97.6 88v20H58c-5.43 0-9.848 4.327-9.996 9.72L48 118v80c0 5.43 4.327 9.848 9.72 9.996L58 208h80c5.43 0 9.848-4.327 9.996-9.72L148 198v-40.705h20V198c0 16.403-13.164 29.731-29.504 29.996L138 228H58c-16.403 0-29.731-13.164-29.996-29.504L28 198v-80c0-16.403 13.164-29.731 29.504-29.996L58 88h39.6Z"></path></g></svg>
</template>
