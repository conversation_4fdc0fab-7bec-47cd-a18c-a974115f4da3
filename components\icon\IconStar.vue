<!-- 实心星标，用于表示收藏 -->

<script setup lang="ts">
interface Props {
  size?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
})

// 计算样式
const iconStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  minWidth: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size
}))
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-star icon-svg index-module_icon_hhi0u index-module_iconMarked_vNmDS index-module_size_wVASz"
        style="color: var(--yq-yellow-6);"
        :style="iconStyle">
        <path
            d="M134.643 17.23a16.011 16.011 0 0 1 8.649 8.643l23.705 56.981c.29.687.938 1.156 1.681 1.217L225.362 89a16.015 16.015 0 0 1 13.733 10.643 15.999 15.999 0 0 1-4.06 16.887l-42.652 40.72a2 2 0 0 0-.56 1.92l14.335 60.087a16 16 0 0 1-6.16 16.671 16.026 16.026 0 0 1-17.77.711l-52.695-32.172a2.003 2.003 0 0 0-2.082 0L74.756 236.64a16.026 16.026 0 0 1-17.784-.696 16 16 0 0 1-6.161-16.686l14.335-60.087a2 2 0 0 0-.56-1.92l-42.637-40.736a15.999 15.999 0 0 1-4.043-16.875A16.015 16.015 0 0 1 31.623 89l56.7-4.93a2.002 2.002 0 0 0 1.665-1.216l23.705-56.981a16.011 16.011 0 0 1 8.682-8.662 16.027 16.027 0 0 1 12.268.018Z"
            fill="currentColor" fill-rule="nonzero"></path>
    </svg>
</template>