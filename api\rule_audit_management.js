import request from '@/utils/request'
//规则审核管理
//列表/查询
export function rule_auditList(params) {
    return request({
      url: 'erule/manage/ruleCheck/list',
      method: 'get',
      params, 
    })
  }
//规则类型下拉列表
export function ruleType(params) {
    return request({
      url: 'sys/dictionaryValue/getDicValueByTypeCode',
      method: 'get',
      params, 
    })
  }
//获取审批信息
export function getRuleinfo(params) {
    return request({
      url: 'erule/manage/ruleCheck/getRuleInfo',
      method: 'get',
      params, 
    })
  }
// 审核退回下拉列表
export function auditBack(data) {
    return request({
      url: 'erule/manage/ruleCheck/back',
      method: 'post',
      data, 
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
    })
  }

  // 审核通过下拉列表
export function auditPass(data) {
    return request({
      url: 'erule/manage/ruleCheck/pass',
      method: 'post',
      data, 
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
    })
  }
//历史版本信息
export function getHistoryInfo(params) {
    return request({
      url: 'erule/manage/ruleCheck/getRuleHisInfo',
      method: 'get',
      params, 
    })
  }
