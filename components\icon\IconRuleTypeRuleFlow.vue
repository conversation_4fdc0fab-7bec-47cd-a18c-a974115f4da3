<!-- 规则流图标 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 18,
    class: ''
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'larkui-icon',
            'larkui-icon-doc-type-flow',
            'icon-svg',
            'index-module_size_wVASz',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <g fill="none" fill-rule="evenodd">
            <path
                d="M4.75 1.267h10.5a2 2 0 0 1 2 2v13.5a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-13.5a2 2 0 0 1 2-2Z"
                stroke="#7D6CAB" stroke-width="0.976" fill="#FFF" stroke-linecap="round"
                stroke-linejoin="round"></path>
            <path
                d="M11.6 4.5H7.1a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h4.5a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2Zm-4.5 1h4.5a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H7.1a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1Z"
                fill="#AD9DDA" fill-rule="nonzero"></path>
            <path d="M12 8.75a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5Z" fill="#9177D9"
                fill-rule="nonzero"></path>
        </g>
    </svg>
</template> 