import {
  login,
  logout,
  getAvailableMenuTree
} from '@/api/user'
import {
  getToken,
  setToken,
  removeToken
} from '@/utils/auth'
import qs from 'qs'
// 基础路径
const baseUrl = process.env.BASE_URL;

// 后台数据转化成前端路由
function filterAsyncRouter(route) {
  let accessedRouters = []
  // 名称对应关系
  const nameObj = {
    '系统管理': 'material',
    '规则管理': 'work_queue',
    '任务管理': 'taskManagement',
    '测试管理': 'testManagement',
    '规则报表': 'ruleReport',
  }
  // icon options
  const icon = {
    material: 'el-icon-setting',
    work_queue: 'tree',
    taskManagement: 'el-icon-s-management',
    testManagement: 'el-icon-s-marketing',
    ruleReport: 'el-icon-s-claim',
  }

  return accessedRouters
}

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
  }
}
const getters = {
  token: () => state.token,
  name: () => state.name,
  avatar: () => state.avatar,
  routers: () => state.routers,
  addRouters: () => state.addRouters,
};
const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROUTERS: (state, routers) => {
    state.addRouters = routers;
  }
}

const actions = {
  // user login
  login({
    commit
  }, {
    data,
    flag,
    code
  }) {
    var datas = qs.stringify(data)
    return new Promise((resolve, reject) => {
      login(datas, flag, code).then((response) => {
        const token = 'admin-token'
        commit('SET_TOKEN', token)
        setToken(token)
        resolve(response)

      }).catch(error => {
        reject(error)
      })
    })
  },
  // get user info  登录成功之后获取可用菜单树
  getAvailableMenuTree({
    commit
  }) {
    return new Promise((resolve, reject) => {
      getAvailableMenuTree().then(response => {
        let _menuTree = filterAsyncRouter(response.data)
        let _work_queue = _menuTree.find(i => {
          return i.path === '/work_queue'
        })
        _menuTree.push({
          path: '*',
          redirect: `${baseUrl}404`,
          hidden: true
        })
        commit('SET_NAME', 'admin')
        commit('SET_ROUTERS', _menuTree);
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },
  // user logout
  logout({
    commit
  }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        removeToken()
        removeButtonRole()
        removeUnid()
        commit('RESET_STATE')
        sessionStorage.clear()
        localStorage.clear()
        resolve()

      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({
    commit
  }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
