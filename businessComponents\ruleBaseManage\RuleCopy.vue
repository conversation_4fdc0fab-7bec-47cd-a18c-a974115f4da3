<!-- 规则复制 -->

<template>
    <div id="rule_attribute">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="规则名称" name="ruleName">
                <a-input autocomplete="off" v-model:value="form.ruleName" placeholder="规则名称" />
            </a-form-item>
            <a-form-item label="有效状态" name="validStatus">
                <a-select v-model:value="form.validStatus" placeholder="请选择">
                    <a-select-option v-for="item in validStateOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则类型">
                <a-select v-model:value="form.type" placeholder="请选择" disabled>
                    <a-select-option v-for="item in ruleTypeOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则优先级">
                <a-select v-model:value="form.salience" placeholder="请选择">
                    <a-select-option v-for="item in priorityOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则包" name="ruleTree">
                <a-tree-select
                        v-model:value="form.ruleTree"
                        :tree-data="ruleTreeOptions"
                        placeholder="请输入查询内容"
                        show-search
                        tree-node-filter-prop="title"
                        @select="ruleNodeClick"
                >
                </a-tree-select>
            </a-form-item>
            <a-form-item label="规则编号" name="ruleNumber">
                <a-input autocomplete="off" v-model:value="form.ruleNumber" placeholder="规则编号" />
            </a-form-item>
            <a-form-item label="规则描述" name="descs">
                <a-textarea v-model:value="form.descs" :auto-size="{ minRows: 2, maxRows: 8 }" :maxLength="500"/>
            </a-form-item>
            <a-form-item label="生效时间：" name="effectDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="effectDate"
                        v-model:pubTime="effectTime"
                        @change="(date, time) => handleEffectDateChange(date, time)"
                    />
                </div>
            </a-form-item>
            <a-form-item label="失效时间：" name="expiredDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="expiredDate"
                        v-model:pubTime="expiredTime"
                        @change="(date, time) => handleExpiredDateChange(date, time)"
                    />
                </div>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import {
    copySave,
    getDicValueByTypeCode,
    treeList,
    getRuleByUuid
} from "@/api/rule_base";
import { showGlobalLoading, hideGlobalLoading } from '@/utils/loading';
const message = inject('message')

const props = defineProps({
    ruleBaseId: {
        type: String,
        default: () => '',
    },
    rulePackageId: {
        type: String,
        default: () => '',
    },
    ruleId: {
        type: String,
        default: () => '',
    },
    datar: {
        type: Object,
    },
})

const form = reactive({
    ruleName: '',
    validStatus: '',
    type: '',
    ruleNumber: '',
    salience: '',
    descs: '',
    ruleTree:''
});

// 添加时间相关变量
const effectDate = ref('');
const effectTime = ref('');
const expiredDate = ref('');
const expiredTime = ref('');

// 处理ISO时间格式转换为日期和时间
const parseISODateTime = (isoString) => {
    if (!isoString) return { date: '', time: '' };
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return {
        date: `${year}-${month}-${day}`,
        time: `${hours}:${minutes}`
    };
};

// 添加时间变化处理函数
const handleEffectDateChange = (date, time) => {
    effectDate.value = date;
    effectTime.value = time;
};

const handleExpiredDateChange = (date, time) => {
    expiredDate.value = date;
    expiredTime.value = time;
};

const rules = {
    ruleName: [
        { required: true, message: '不能为空', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\d\(\)\（\）、\-\%]+$/, message: '规则名称只能包括中文、数字、字母、下划线、中英文小括号和、-% 符号' },
    ],
    validState: [{ required: true, message: '不能为空', trigger: 'blur' }],
    ruleCode: [{ pattern: /^[0-9a-zA-Z]{2,50}$/, message: '规则编号长度必须在2-50之间,且只能包含字母和数字' }],
    ruleTree: [{ required: true, message: "不能为空", trigger: "blur" }],
};
const clickTreeData = ref(null)
const validStateOptions = ref([]);
const ruleTypeOptions = ref([]);
const priorityOptions = ref([]);
const ruleTreeOptions = ref([]);
const ruleForm = ref(null);

const ruleBaseId = ref('');
const demandUuid = ref('');
const emit = defineEmits(['removeTab', 'addTab']);

const getDicValue = (type, fn) => {
    getDicValueByTypeCode({ typeCode: type }).then((res) => {
        fn(res.data);
    });
};

const handleSubmit = (callback) => {
    ruleForm.value.validate()
        .then(() => {
            if (ruleBaseId.value) {
                showGlobalLoading('复制中，请耐心等待');

                // 验证时间格式
                if (effectDate.value && !effectTime.value) {
                    hideGlobalLoading();
                    message.error('请选择完整的生效时间');
                    return Promise.reject('生效时间不完整');
                }
                if (expiredDate.value && !expiredTime.value) {
                    hideGlobalLoading();
                    message.error('请选择完整的失效时间');
                    return Promise.reject('失效时间不完整');
                }

                // 验证时间先后顺序
                const effectDateTime = new Date(`${effectDate.value} ${effectTime.value}:00`);
                const expiredDateTime = new Date(`${expiredDate.value} ${expiredTime.value}:00`);
                if(effectDate.value && expiredDate.value){
                    if (effectDateTime >= expiredDateTime) {
                        message.error('失效时间必须晚于生效时间');
                        return Promise.reject('时间顺序错误');
                    }
                }
                // 格式化日期为后端期望的格式
                const formatDate = (date, time) => {
                    if (!date || !time) return null;
                    const d = new Date(`${date} ${time}:00`);
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    const hours = String(d.getHours()).padStart(2, '0');
                    const minutes = String(d.getMinutes()).padStart(2, '0');
                    const seconds = String(d.getSeconds()).padStart(2, '0');
                    const milliseconds = String(d.getMilliseconds()).padStart(3, '0');
                    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+0800`;
                };

                copySave({
                    ...props.datar,
                    ruleName: form.ruleName,
                    validStatus: form.validStatus,
                    type: form.type,
                    salience: form.salience,
                    descs: form.descs,
                    folderUuid: clickTreeData.value.uuid ? clickTreeData.value.uuid : '',
                    engUuid: ruleBaseId.value,
                    effectDate: formatDate(effectDate.value, effectTime.value),
                    expiredDate: formatDate(expiredDate.value, expiredTime.value),
                },
                clickTreeData.value.engUuid,
                demandUuid.value,
                props.datar.businessLine).then((res) => {
                    if (res.code === 20000) {
                        hideGlobalLoading();
                        message.success(res.data);
                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        hideGlobalLoading();
                        message.error(res.data);
                    }
                }).catch((error) => {
                    hideGlobalLoading();
                    message.error("复制失败: " + (error.message || '未知错误'));
                });
               /* } else {
                    message.error("请选择规则包");
                }*/
            }
        })
        .catch((error) => {
            console.log(error);
        });
};
const setTreeList=(params)=> {
    let startIndex = 0;
    _setAttirbute(params);
    function _setAttirbute(item) {
        item.filter((v) => {
            v.key = startIndex;
            startIndex += 1;
            if (v.children && v.children.length) {
                _setAttirbute(v.children);
            }
        });
    }
    return params;
};
const ruleInfoData = ref({});
async function getRuleInfo(ruleId) {
    const ruleInfo = await getRuleByUuid({
        uuids: ruleId,
    });

    const ruleData = ruleInfo.data;

    if (!ruleData) return;

    ruleInfoData.value = ruleData;
    form.ruleName = ruleData.ruleName;
    form.validStatus = ruleData.validStatus;
    form.type = ruleData.type;
    form.salience = ruleData.salience;
    form.descs = ruleData.descs;
    form.ruleNumber = ruleData.ruleNumber;

    // 处理生效时间回显
    if (ruleData.effectDate) {
        const effectDateTime = parseISODateTime(ruleData.effectDate);
        effectDate.value = effectDateTime.date;
        effectTime.value = effectDateTime.time;
    }

    // 处理失效时间回显
    if (ruleData.expiredDate) {
        const expiredDateTime = parseISODateTime(ruleData.expiredDate);
        expiredDate.value = expiredDateTime.date;
        expiredTime.value = expiredDateTime.time;
    }

    // tree select
    treeList({
        businessLine: props.datar.businessLine
    }).then((res) => {
        function formatData(data1){
            data1.forEach(res => {
                res.title = res.folderName;
                res.value = res.key;
                if (res.children) {
                    formatData(res.children)
                }
            })
        }
        let data = setTreeList(res.data);
        formatData(data);
        ruleTreeOptions.value = data;
        // 设置clickTreeData
        function _setDefauktKey(arr, fn) {
            arr.filter((v) => {
                fn(v);
                if (v.children && v.children.length) {
                    _setDefauktKey(v.children, fn);
                }
            });
        }
        if(!props.datar.folderUuid){
            //folderUuid为空时取engUuid，处理根节点选择情况
            clickTreeData.value = {'engUuid':props.datar.engUuid};
        }else{
            _setDefauktKey( res.data, (v) => {
                v.uuid === props.datar.folderUuid && (clickTreeData.value = v);
            })
        }
        console.log(clickTreeData.value);
        const packageNameArr = props.datar.packageNameAll.split(".");
        form.ruleTree = packageNameArr[packageNameArr.length - 1];
    });
}
const ruleNodeClick = (value,node) => {
    form.ruleTree = node.folderName;
    clickTreeData.value = node;
}
useRuleBaseId(props,ruleBaseId,demandUuid)
onMounted(async () => {

    getDicValue('ruleValidStatus', (arr) => {
        validStateOptions.value = arr;
    });
    getDicValue('type_rule', (arr) => {
        ruleTypeOptions.value = arr;
    });
    getDicValue('salience_rule', (arr) => {
        priorityOptions.value = arr;
    });

    getRuleInfo(props.ruleId)
});

defineExpose({
    handleSubmit
});
</script>

<style lang="scss" scoped>
#rule_attribute {
    :deep(.ant-input),
    :deep(.ant-select),
    :deep(.ant-upload-dragger),
    :deep(.ant-upload-list),
    :deep(.ant-input-textarea) {
        width: 100%;
        max-width: 400px;
    }

    :deep(.ant-input-textarea) {
        height: 120px !important;
        resize: none;
    }

    :deep(.ant-upload-dragger) {
        height: 100px;
    }

    :deep(.ant-upload-dragger .anticon-upload) {
        margin: 0;
        line-height: 50px;
        font-size: 50px;
    }

    :deep(.jarUpload.hidden .ant-upload) {
        display: none;
    }

    :deep(.ant-tabs) {
        width: 70%;
        min-height: 100px;
        margin: 15px 15px 15px 60px;
    }

    :deep(.ant-tabs-card) {
        box-shadow: unset;
    }

    :deep(.params_form) {
        border-bottom: 1px solid #dcdfe6;

        h4 {
            font-weight: unset;

            span {
                margin-left: 100px;
            }

            span.primary {
                color: #409eff;
            }

            span.danger {
                color: #f56c6c;
            }
        }

        label {
            color: #99a9bf;
        }

        span {
            color: #606266;
        }
    }

    :deep(.params_form:last-child) {
        border-bottom: unset;
    }

    :deep(.form_flex_div),
    :deep(.form_flex_div .ant-form-item) {
        display: flex;
        flex-direction: row;
        flex: 1;

        .ant-form-item {
            overflow: auto;
        }
    }

    :deep(.ant-form) {
        padding: 20px;
    }

    :deep(.ant-form-item) {
        margin-bottom: 16px;
    }

    :deep(.ant-form-item:last-child) {
        margin-bottom: 0;
    }

    :deep(.ant-form-item-explain-error) {
        color: #ff4d4f !important;
    }
}
</style>
