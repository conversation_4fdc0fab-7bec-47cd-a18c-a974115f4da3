export default class CustomContextPad {
  constructor(config, contextPad, create, elementFactory, injector, translate, modeling, bpmnFactory) {
    this.create = create;
    this.elementFactory = elementFactory;
    this.translate = translate;
    this.modeling = modeling;
    this.bpmnFactory = bpmnFactory;

    if (config.autoPlace !== false) {
      this.autoPlace = injector.get('autoPlace', false);
    }

    contextPad.registerProvider(this); // // 定义这是一个contextPad
  }

  getContextPadEntries(element) {
    const {
      autoPlace,
      create,
      elementFactory,
      translate,
      modeling,
      bpmnFactory
    } = this;

    function appendElement(type, name, gatewayDirection) {
      return function (event, element) {
        let businessObject = null
        if (gatewayDirection) {
          businessObject = bpmnFactory.create(type, {
            name,
            gatewayDirection
          });
        } else {
          businessObject = bpmnFactory.create(type, {
            name
          });
        }
        const shape = elementFactory.createShape({
          type,
          businessObject
        });
        if (autoPlace) {
          autoPlace.append(element, shape);
        } else {
          create.start(event, shape, element);
        }
      }
    }

    return {
      'append.end-event': {
      // 'append.exclusive-gateway': {
        group: 'model',
        className: 'entry iconfontree icon-condition',
        title: translate('追加条件节点'),
        action: {
          click: appendElement('bpmn:ExclusiveGateway', '条件节点'),
        }
      },
      'append.task': {
        group: 'model',
        className: 'entry iconfontree icon-action',
        title: translate('追加动作节点'),
        action: {
          click: appendElement('bpmn:Task', '动作节点')
        }
      },
      'append.delete': {
        group: 'model',
        className: 'entry bpmn-icon-trash dir',
        title: translate('删除'),
        action: {
          click: () => {
            modeling.removeElements([element])
          }
        }
      },
    }
  }
}

CustomContextPad.$inject = [
  'config',
  'contextPad',
  'create',
  'elementFactory',
  'injector',
  'translate',
  'modeling',
  'bpmnFactory'
];
