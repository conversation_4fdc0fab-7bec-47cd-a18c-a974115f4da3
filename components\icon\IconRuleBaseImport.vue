<!-- 规则库导入图标样式 -->
<script setup lang="ts">
    interface Props {
        size?: number
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 16
    })
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-import icon-svg ReaderLayout-module_actionItem_CbOzz index-module_size_wVASz TemplateTreeItem-module_actionIcon_haD5C" data-name="Import" style="width: 16px; min-width: 16px; height: 16px;"><path d="M198 28c16.403 0 29.731 13.146 29.996 29.463l.004.496v20.194c0 5.515-4.477 9.986-10 9.986-5.43 0-9.848-4.32-9.996-9.706l-.004-.28V57.96c0-5.422-4.327-9.835-9.72-9.983l-.28-.004H58c-5.43 0-9.848 4.321-9.996 9.707l-.004.28v139.807c0 5.421 4.327 9.834 9.72 9.982l.28.004h140c5.43 0 9.848-4.321 9.996-9.707l.004-.28v-20.15c0-5.515 4.477-9.986 10-9.986 5.43 0 9.848 4.32 9.996 9.707l.004.28v20.15c0 16.38-13.164 29.69-29.504 29.954l-.496.004H58c-16.403 0-29.731-13.146-29.996-29.463l-.004-.495V57.959c0-16.38 13.164-29.69 29.504-29.955L58 28h140Zm-79.03 49.72c3.92-3.695 10.093-3.625 13.927.209 3.905 3.905 3.905 10.237 0 14.142l-26.124 26.123L209.388 118l.28.003c5.394.138 9.729 4.549 9.74 9.978.01 5.523-4.459 10.008-9.982 10.019l-102.485.194 26.333 26.333.21.215c3.694 3.92 3.625 10.093-.21 13.927-3.905 3.905-10.236 3.905-14.142 0l-29.345-29.346-.348-.353c-11.367-11.741-11.25-30.474.348-42.073l28.968-28.968Z" fill="currentColor" fill-rule="nonzero"></path></svg>
</template>

