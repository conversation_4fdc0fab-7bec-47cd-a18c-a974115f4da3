.index-module_bookTips_cZ25F {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-left: 2px;
    margin-top: 2px;
    margin-bottom: 2px
}

.index-module_groupTips_znta0 {
    margin-top: 10px;
    margin-left: 16px
}

.index-module_commentTips_eS4uK {
    margin-top: 8px;
    margin-left: 2px;
    font-size: 14px;
    display: inline-block
}

.Creator-module_createContainer_E9WwG {
    width: 100%
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 {
    width: 288px;
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 2px;
    background-color: var(--yq-bg-primary);
    color: var(--yq-text-body);
    padding: 16px;
    margin: 0 auto
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 .Creator-module_editorContainer_N1BzF {
    position: relative
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 .Creator-module_editorContainer_N1BzF .lake-content-editor-core.lake-engine {
    overflow: auto;
    padding-bottom: 8px;
    height: 92px;
    min-height: 0
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 .Creator-module_actions_CFe4H {
    display: flex;
    margin-top: 8px
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 .Creator-module_actions_CFe4H .Creator-module_actionItem_NsFQU {
    flex: none;
    margin-right: 16px;
    cursor: pointer;
    height: 32px
}

.Creator-module_createContainer_E9WwG .Creator-module_createBox_YJGH6 .Creator-module_actions_CFe4H .Creator-module_actionItem_NsFQU .Creator-module_cancel_G\+\+6L {
    display: inline-block;
    color: var(--yq-text-body);
    text-align: center;
    vertical-align: middle
}

.post {
    background: var(--yq-bg-primary);
    padding: 24px 32px 32px 32px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    position: relative;
    margin-bottom: 16px
}

.post .post-origin {
    font-size: 12px;
    line-height: 18px;
    text-align: right
}

.post .post-origin a {
    margin-left: 6px;
    color: var(--yq-text-caption)
}

.post .post-origin a:hover {
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.post .post-meta {
    padding-right: 48px;
    display: flex;
    position: relative
}

.post .post-meta .thread-user-name {
    display: none
}

.post .post-meta .thread-user {
    margin-right: 16px;
    margin-top: 6px
}

.post .post-meta .post-meta-time {
    font-size: 12px;
    line-height: 20px;
    display: block
}

.post .post-meta .post-meta-time a {
    color: var(--yq-text-caption)
}

.post .post-meta .post-meta-time a:hover {
    -webkit-text-decoration: underline;
    text-decoration: underline
}

.post .post-meta .post-meta-user-name {
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 22px;
    margin-right: 8px
}

.post .post-meta .post-meta-user-name .icon-svg {
    vertical-align: top;
    margin-top: 3px;
    margin-left: 8px
}

.post .post-meta .post-origin {
    flex: 1;
    padding-top: 12px;
    text-align: right
}

.post .post-meta .post-actions {
    position: absolute;
    right: 0;
    top: 10px
}

.post .post-content {
    margin-top: 24px;
    margin-bottom: 16px
}

.post-pinned:before {
    content: " ";
    position: absolute;
    width: 0;
    height: 0;
    left: 4px;
    top: 4px;
    border-color: var(--yq-yuque-green-3) transparent transparent var(--yq-yuque-green-3);
    border-style: solid;
    border-width: 5px
}

.ant-list.post-list {
    background: none;
    margin: 24px 0
}

.ant-list.post-list .ant-list-item {
    padding: 0;
    border-bottom: none
}

.ant-list.post-list .list-load-more {
    padding-left: 0;
    padding-right: 0
}

.post-interaction {
    position: relative;
    min-height: 28px
}

.post-interaction .post-interaction-actions {
    display: flex;
    height: 28px;
    margin-left: -4px
}

.post-interaction .post-interaction-actions .icon-svg {
    vertical-align: top
}

.post-interaction .post-interaction-action {
    cursor: pointer;
    padding: 4px;
    line-height: 20px;
    height: 28px
}

.post-interaction .post-interaction-action:hover {
    background: var(--yq-bg-tertiary);
    border-radius: 4px
}

.post-interaction .post-interaction-reaction {
    margin-right: 12px
}

.post-interaction .post-interaction-action-total {
    margin-left: 8px
}

.post-interaction .post-interaction-reaction-summary {
    margin-right: 16px;
    margin-bottom: 8px
}

.post-interaction .post-interaction-reaction-summary ul {
    display: flex;
    flex-wrap: wrap
}

.post-interaction .post-interaction-reaction-summary li {
    flex: none;
    margin-bottom: 8px
}

.post-interaction .post-reaction-detail.post-reaction-detail-type {
    padding: 3px 12px;
    height: 28px;
    line-height: 20px;
    margin-right: 8px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    cursor: pointer
}

.post-interaction .post-reaction-detail.post-reaction-detail-active {
    border-color: var(--yq-border-primary);
    background: var(--yq-bg-tertiary)
}

.post-interaction .post-reaction-detail .post-interaction-num {
    margin-left: 4px
}

.post-interaction .post-reaction-detail .post-interaction-icon {
    vertical-align: top
}

.post-interaction .post-reaction-detail-popover .ant-popover-content {
    max-width: 240px
}

.post-interaction .post-reaction-detail-popover .ant-popover-inner-content {
    padding: 12px 16px
}

.post-interaction .post-reaction-users {
    min-height: 32px
}

.post-interaction .post-reaction-users li {
    display: inline-block;
    margin-right: 8px
}

.post-interaction .post-reaction-users a {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.post-reaction {
    display: inline-block
}

.post-reaction .post-reaction-entry {
    cursor: pointer
}

.post-reaction .post-reaction-entry-mini {
    cursor: pointer;
    width: 28px;
    height: 28px;
    display: inline-block;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    padding: 3px
}

.post-reaction .post-reaction-items {
    display: flex
}

.post-reaction .post-reaction-items li {
    display: inline-block;
    margin-right: 4px;
    padding: 2px;
    border: 1px solid var(--yq-white)
}

.post-reaction .post-reaction-items li:last-of-type {
    margin-right: 0
}

.post-reaction .post-reaction-items li:hover {
    background: var(--yq-bg-tertiary)
}

.post-reaction .post-reaction-items li img {
    width: 24px;
    height: 24px
}

.post-reaction .post-reaction-items li.post-reaction-active {
    border-radius: 2px;
    border-color: var(--yq-border-primary);
    background: var(--yq-bg-tertiary)
}

.posts .ant-list {
    background: none
}

.posts .comment-list-item-inner {
    padding: 0
}

.posts .comment-list-item {
    margin-bottom: 24px;
    padding: 24px 32px 32px 32px;
    background: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px
}

.posts .comment-list-item .comment-header {
    margin-bottom: 0;
    position: relative
}

.posts .comment-list-item .comment-header-right {
    display: flex;
    position: absolute;
    right: 0;
    top: 0;
    height: 32px
}

.posts .comment-list-item .comment-header-right .post-mood {
    text-align: center;
    height: 32px;
    flex: none
}

.posts .comment-list-item .comment-header-right .thread-ui-more-action {
    padding: 4px 0
}

.posts .comment-list-item .avatar {
    margin-right: 8px
}

.posts .comment-list-item .commenter {
    line-height: 24px;
    padding: 4px 0
}

.posts .comment-list-item .commenter .time {
    color: var(--yq-text-caption)
}

.posts .comment-list-item .commenter .name {
    color: var(--yq-text-primary)
}

.posts .comment-list-item .commenter .name,.posts .comment-list-item .commenter .time {
    font-size: 14px;
    line-height: 24px
}

.posts .comment-list-item .content {
    border-radius: 4px;
    margin-left: 0;
    margin-top: 24px;
    position: relative;
    padding-left: 0
}

.posts .comment-list-item .actions {
    display: none
}

.posts .comment-list-item .content-detail {
    position: relative;
    padding: 0
}

.posts .comment-list-item .content-detail-reply {
    background: var(--yq-bg-tertiary)
}

.posts .comment-list-item .content-detail-reply .parent-link {
    top: 6px;
    color: var(--yq-text-caption)
}

.post-mood {
    display: inline-block;
    border-radius: 20px;
    background-color: var(--yq-bg-secondary);
    padding-right: 12px;
    vertical-align: top
}

.posts.posts-classic .comment-list-item-focus {
    border-color: var(--yq-yuque-grey-6)
}

.posts.posts-classic .comment-list-item-incoming-wrap {
    border-color: var(--yq-yellow-4)
}

.posts.posts-classic .comment-list-item-current,.posts.posts-classic .comment-list-item-incoming {
    background: none
}

.posts.posts-classic .comment-list-item .comment-header {
    display: flex
}

.posts.posts-classic .comment-list-item .comment-header-right .thread-ui-more-action {
    margin-top: 6px
}

.posts.posts-classic .comment-list-item .avatar {
    padding-top: 6px;
    margin-right: 16px
}

.posts.posts-classic .comment-list-item .commenter {
    padding: 0;
    line-height: 22px
}

.posts.posts-classic .comment-list-item .commenter .name {
    line-height: 22px
}

.posts.posts-classic .comment-list-item .commenter .time {
    display: block;
    font-size: 12px;
    line-height: 20px;
    height: 20px
}

.posts.posts-classic .comment-list-item .content-detail {
    margin-bottom: 16px
}

@media only screen and (max-width: 576px) {
    .posts .comment-list-item-inner {
        padding:0
    }

    .posts .comment-list-item {
        border-radius: 0;
        border-left: 0;
        border-right: 0
    }

    .posts .comment-list-item .comment-header {
        padding-left: 24px
    }

    .posts .comment-list-item .avatar {
        width: 28px;
        height: 28px;
        margin-right: 8px
    }

    .posts .comment-list-item .avatar>img {
        width: 28px!important;
        min-width: 28px!important;
        height: 28px!important
    }

    .posts .comment-list-item .content {
        margin-top: 8px;
        margin-left: 0;
        padding: 20px 24px;
        border: none;
        border-radius: 0;
        border-top: 1px solid var(--yq-border-primary);
        border-bottom: 1px solid var(--yq-border-primary)
    }

    .posts .comment-list-item .content-mood {
        width: 72px;
        height: 52px;
        right: 24px;
        top: -26px
    }

    .posts .comment-list-item .content-detail-reply .parent-link {
        display: block
    }

    .posts.posts-classic .comment-list-item {
        padding: 20px 24px 24px
    }

    .posts.posts-classic .comment-list-item .comment-header {
        padding-left: 0
    }

    .posts.posts-classic .comment-list-item .content {
        border: none;
        margin-top: 20px;
        margin-left: 0;
        padding: 0
    }

    .post {
        padding: 20px 24px;
        margin-left: 0;
        margin-top: 0;
        border: none;
        border-top: 1px solid var(--yq-border-primary);
        border-bottom: 1px solid var(--yq-border-primary);
        border-radius: 0
    }

    .post-interaction .post-interaction-reaction-summary {
        max-width: 184px;
        margin-bottom: 0
    }

    .post-interaction .post-interaction-actions {
        position: absolute;
        top: 0;
        right: 0
    }

    .post-interaction .post-interaction-reaction {
        margin-right: 8px
    }

    .post-interaction .post-reaction-detail .post-reaction-detail-type {
        padding: 3px 4px
    }
}

.EditView-module_editorViewContainer_tCK3o {
    margin-top: 16px
}

.EditView-module_editorViewContainer_tCK3o .lake-content-editor-core.lake-engine {
    overflow: auto;
    padding-bottom: 8px;
    height: 92px;
    min-height: 0
}

.EditView-module_editorViewContainer_tCK3o .EditView-module_actions_TAmbV {
    display: flex;
    margin-top: 8px
}

.EditView-module_editorViewContainer_tCK3o .EditView-module_actions_TAmbV .EditView-module_actionItem_wxvcy {
    flex: none;
    margin-right: 16px;
    cursor: pointer;
    height: 32px;
    line-height: 32px
}

.EditView-module_editorViewContainer_tCK3o .EditView-module_actions_TAmbV .EditView-module_actionItem_wxvcy .EditView-module_cancel_i\+xST {
    display: inline-block;
    color: var(--yq-text-body);
    text-align: center;
    vertical-align: middle
}

.CommentGroupItem-module_groupItemContainer_hcFs5 {
    padding: 8px 16px 8px 48px;
    position: relative
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_avatar_sZcW2 {
    position: absolute;
    left: 16px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw {
    line-height: 22px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_name_9c70h {
    display: inline-flex;
    align-items: center
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_name_9c70h span {
    color: var(--yq-text-body);
    max-width: 100px;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
    overflow: hidden
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_name_9c70h .CommentGroupItem-module_arrow_u0PYm {
    margin: 0 8px;
    color: var(--yq-text-disable)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_time_KbowO {
    color: var(--yq-text-caption);
    vertical-align: middle;
    font-size: 12px;
    cursor: pointer
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_content_KAu25 {
    margin-top: 8px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 {
    margin-top: 8px;
    margin-left: -4px;
    color: var(--yq-text-caption)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 .CommentGroupItem-module_moreAction_l2lcj {
    display: inline-flex;
    visibility: hidden
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 .CommentGroupItem-module_actionItem_C4Epa {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    border-radius: 3px;
    cursor: pointer
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 .CommentGroupItem-module_actionItem_C4Epa.CommentGroupItem-module_withoutMarginRight_Z0YBO {
    margin-right: 0
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 .CommentGroupItem-module_actionItem_C4Epa span {
    display: inline-block;
    text-align: center;
    line-height: 22px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_actions_eyLP8 .CommentGroupItem-module_actionItem_C4Epa:hover {
    background-color: var(--yq-bg-tertiary)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction {
    margin-right: 8px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items {
    width: auto;
    display: flex
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items li {
    display: flex;
    padding: 6px;
    margin-right: 4px;
    border-radius: 3px;
    border: 0
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items li:hover {
    cursor: pointer
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items li.post-reaction-active {
    background-color: var(--yq-bg-tertiary)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items li:last-of-type {
    margin-right: 0
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .post-reaction-items li img {
    width: 20px;
    height: 20px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-reaction .ant-popover-inner-content {
    padding: 6px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction {
    min-height: 24px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction .post-interaction-reaction-summary {
    margin-right: 0;
    margin-bottom: 0;
    max-width: none!important
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction .post-interaction-reaction-summary .post-reaction-detail-type {
    display: flex;
    align-items: center;
    height: 24px;
    padding: 4px 8px;
    margin-right: 4px;
    border: 0;
    background-color: var(--yq-bg-tertiary)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction .post-interaction-reaction-summary .post-reaction-detail-type.post-reaction-detail-active {
    border: 0;
    background-color: var(--yq-yuque-green-1)
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction .post-interaction-reaction-summary li {
    margin-bottom: 4px
}

.CommentGroupItem-module_groupItemContainer_hcFs5 .CommentGroupItem-module_main_LL-Xw .post-interaction .post-interaction-reaction-summary .post-interaction-icon {
    width: 16px;
    height: 16px
}

.CommentGroupItem-module_groupItemContainer_hcFs5:hover .CommentGroupItem-module_main_LL-Xw .CommentGroupItem-module_moreAction_l2lcj {
    visibility: visible
}

.CommentGroupItem-module_replyContainer_R7zLP {
    margin-top: 8px;
    background: var(--yq-bg-secondary);
    border-radius: 2px;
    cursor: pointer;
    padding: 4px 8px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.CommentGroupItem-module_replyContainer_R7zLP .CommentGroupItem-module_editContainer_qHYQX {
    padding-top: 4px
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK {
    padding: 0 16px;
    margin-top: 16px
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_editorContainer_Tvxsn {
    position: relative
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_editorContainer_Tvxsn .lake-content-editor-core.lake-engine {
    overflow: auto;
    padding-bottom: 8px;
    height: 92px;
    min-height: 0
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv {
    display: flex;
    margin-top: 8px
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv .ReplyCreator-module_actionItem_UHAql {
    flex: none;
    margin-right: 16px;
    cursor: pointer;
    height: 32px
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv .ReplyCreator-module_actionItem_UHAql .ReplyCreator-module_cancel_ARRnU {
    display: inline-block;
    color: var(--yq-text-body);
    text-align: center;
    vertical-align: middle
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv .ReplyCreator-module_replyingInfo_RH86T {
    flex: auto;
    overflow: hidden;
    text-align: right;
    height: 32px
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv .ReplyCreator-module_replyingInfo_RH86T .ReplyCreator-module_wrapper_uNAur {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--yq-bg-secondary);
    font-size: 12px;
    height: 100%;
    line-height: 28px;
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%
}

.ReplyCreator-module_replyCreatorContainer_NQ4sK .ReplyCreator-module_actions_pnqDv .ReplyCreator-module_replyingInfo_RH86T .ReplyCreator-module_wrapper_uNAur .ReplyCreator-module_name_Pay8V {
    color: var(--yq-text-caption)
}

.CommentGroup-module_groupContainer_KczOF {
    width: 100%;
    visibility: hidden
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw {
    width: 288px;
    border-radius: 8px;
    background-color: var(--yq-bg-primary);
    padding: 16px 0;
    border: 1px solid var(--yq-border-light);
    margin: 0 auto
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 0 16px;
    margin-bottom: 10px
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_title_ptcf0 {
    flex: auto;
    color: var(--yq-text-body);
    line-height: 22px
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd {
    display: flex;
    height: 30px;
    border-radius: 5px;
    border: 1px solid var(--yq-border-primary)
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    flex: 1
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP.CommentGroup-module_close_oU6Vu {
    border-left: 1px solid var(--yq-border-primary)
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP .CommentGroup-module_action_wOOTd {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--yq-black);
    border-radius: 3px;
    cursor: pointer
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP .CommentGroup-module_action_wOOTd:hover {
    background-color: var(--yq-bg-tertiary)
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP .CommentGroup-module_action_wOOTd.CommentGroup-module_disable_Il\+lS {
    color: var(--yq-text-disable)
}

.CommentGroup-module_groupContainer_KczOF .CommentGroup-module_groupBox_Rwdmw .CommentGroup-module_header_fIsOp .CommentGroup-module_actionsBtnGroup_r9Otd .CommentGroup-module_actionBtn_eImZP .CommentGroup-module_action_wOOTd.CommentGroup-module_disable_Il\+lS:hover {
    background-color: inherit
}

.CommentGroup-module_groupContainer_KczOF.CommentGroup-module_active_htoNq .CommentGroup-module_groupBox_Rwdmw {
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04)
}

.CommentGroup-module_groupContainer_KczOF.CommentGroup-module_visible_\+GH4- {
    visibility: visible
}

.SideCommentItem-module_selectionInfo_EkyqK {
    display: inline-block
}

.SideCommentItem-module_selectionInfo_EkyqK .SideCommentItem-module_text_8tA08 {
    display: inline-block;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.SideCommentItem-module_selectionInfo_EkyqK .SideCommentItem-module_action_xPcKj {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: var(--yq-text-link);
    margin-left: 16px
}

.SideCommentList-module_commentListWrapper_fE\+ZY {
    position: relative
}

.index-module_commentPanel_rHJ\+M {
    position: fixed;
    top: 0;
    right: 0;
    width: 305px;
    min-height: 100%;
    background-color: var(--yq-bg-primary);
    border-left: 1px solid var(--yq-border-light);
    z-index: 415;
    transform: translateX(100%);
    transition: transform .3s ease 0s;
    visibility: hidden;
    box-sizing: content-box
}

.index-module_commentPanel_rHJ\+M .index-module_toolbar_WR5Oc {
    position: -webkit-sticky;
    position: sticky;
    top: 52px;
    width: 305px;
    height: 52px;
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: var(--yq-bg-primary);
    z-index: 2
}

.index-module_commentPanel_rHJ\+M .index-module_toolbar_WR5Oc .index-module_toolbarContent_wiz0k {
    flex: 1;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_commentPanel_rHJ\+M .index-module_toolbar_WR5Oc .index-module_toolbarContent_wiz0k .index-module_text_Mbvbt {
    font-weight: 700;
    font-size: 14px
}

.index-module_commentPanel_rHJ\+M .index-module_closeBtn_wG-wO {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    border-radius: 3px;
    cursor: pointer
}

.index-module_commentPanel_rHJ\+M .index-module_closeBtn_wG-wO:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_commentPanel_rHJ\+M .index-module_closeBtn_wG-wO .index-module_closeIcon_BZ\+U- {
    width: 16px;
    height: 16px;
    color: var(--yq-yuque-grey-7)
}

.index-module_commentPanel_rHJ\+M.index-module_visible_Rew5O {
    position: absolute;
    transform: translateX(0);
    visibility: visible
}

.index-module_commentPanel_rHJ\+M.index-module_hidden_QeAYC {
    position: absolute;
    transform: translateX(100%);
    visibility: hidden
}

@media only print {
    .index-module_commentPanel_rHJ\+M {
        display: none
    }
}
