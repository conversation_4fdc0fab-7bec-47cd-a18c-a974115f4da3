const NODE_ENV = process.env.NODE_ENV
let _targetUrl = '',
  _uaaUrl = '',
  _uaaUrl_1 = ''

switch (NODE_ENV) {
  case 'development':
    _targetUrl = 'http://10.30.0.2:8081'
    _uaaUrl = 'https://10.30.8.7:9443/uaa-cas-server/portalsso/singleSignOnForPortal.do'
    _uaaUrl_1 = 'http://10.30.8.8:9892/uaa-cas-server/portalsso/singleSignOnForPortal.do'
    break
  case 'testing':
    _targetUrl = 'http://10.30.0.2:8081'
    _uaaUrl = 'https://10.30.8.7:9443/uaa-cas-server/portalsso/singleSignOnForPortal.do'
    _uaaUrl_1 = 'http://10.30.8.8:9892/uaa-cas-server/portalsso/singleSignOnForPortal.do'
    break
  case 'production':
    _targetUrl = 'http://10.20.46.29:29211'
    _uaaUrl = 'https://uaa.cas5.clic:9311/uaa-cas-server/portalsso/singleSignOnForPortal.do'
    _uaaUrl_1 = 'http://cas.clic/uaa-cas-server/portalsso/singleSignOnForPortal.do'
}

export default {
  SITE_NAME: 'eRule规则管理引擎',
  VUE_APP_BASE_API: '/BRMS/erule-service/',
  AI_API: '/BRMS/erule-ai/',
  BASE_URL: '/BRMS/erule-web',

  UAA_URL: _uaaUrl,
  UAA_URL_1: _uaaUrl_1,

  TARGET_URL: _targetUrl,

  secretKey:"他ijhy123ssda*%",
  iv:"议gred12345678u",

}
