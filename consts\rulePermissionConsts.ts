export const RULE_PERMISSION = {
  // 工程相关权限
  RULE_BASE: {
    ADD: 'erule/manage/engineering/query.do|p_新增',
    UPDATE: 'erule/manage/engineering/query.do|p_更新',
    DELETE: 'erule/manage/engineering/query.do|p_删除',
    DETAIL: 'erule/manage/engineering/query.do|p_详情',
    EXPORT: 'erule/manage/engineering/query.do|p_导出',
    IMPORT: 'erule/manage/engineering/query.do|p_导入',
  },

  RULE_HISTORY: {
    DETAIL: 'erule/manage/engineering/query.do|c_详情',
    ROLLBACK: 'erule/manage/engineering/query.do|c_回滚',
    BASELINE: 'erule/manage/engineering/query.do|c_基线',
  },

  RULE: {
    ADD: 'erule/manage/engineering/query.do|新增',
    PROPERTY: 'erule/manage/engineering/query.do|属性',
    EDIT: 'erule/manage/engineering/query.do|编辑',
    DELETE: 'erule/manage/engineering/query.do|删除',
    LOCK: 'erule/manage/engineering/query.do|锁定',
    COPY: 'erule/manage/engineering/query.do|复制',
    UNLOCK: 'erule/manage/engineering/query.do|解锁',
    SUBMIT: 'erule/manage/engineering/query.do|提交',
    DETAIL: 'erule/manage/engineering/query.do|详情',
    WITHDRAW: 'erule/manage/engineering/query.do|撤回',
  },

  // 规则参数相关权限
  RULE_ARG: {
    ADD: 'erule/manage/ruleArg/query.do|p_新增',
    UPDATE: 'erule/manage/ruleArg/query.do|p_更新',
    DELETE: 'erule/manage/ruleArg/query.do|p_删除',
  },

  // 规则模型相关权限
  RULE_MODEL: {
    ADD: 'erule/manage/ruleModel/query.do|p_新增',
    UPDATE: 'erule/manage/ruleModel/query.do|p_更新',
    DELETE: 'erule/manage/ruleModel/query.do|p_删除',
  },

  // 规则检查相关权限
  RULE_CHECK: {
    AUDIT: 'erule/manage/ruleCheck/query.do|p_审核',
  },

  // 快照相关权限
  SNAPSHOOT: {
    PUBLISH: 'erule/manage/snapshoot/query.do|p_发布',
    DELETE: 'erule/manage/snapshoot/query.do|p_删除',
    ADD: 'erule/manage/snapshoot/query.do|p_新增',
    COPY: 'erule/manage/snapshoot/query.do|p_复制',
    DETAIL: 'erule/manage/snapshoot/query.do|p_详情',
    RULE_DETAIL: 'erule/manage/snapshoot/query.do|详情',
    COMPARE_VERSION: 'erule/manage/snapshoot/query.do|p_版本对比',
  },

  // BOM模型相关权限
  BOM_MODEL: {
    ADD: 'erule/manage/bomModel/query.do|新增',
    UPDATE: 'erule/manage/bomModel/query.do|更新',
  },

  // 规则集合相关权限
  RULE_MUSTER: {
    EXPORT_HTML: 'erule/manage/ruleMuster/query.do|p_导出_html',
  },

  // 用户管理相关权限
  SYS_USER: {
    ADD: 'sys/user/query.do|p_新增',
    UPDATE: 'sys/user/query.do|p_修改',
    DELETE: 'sys/user/query.do|p_删除',
    COPY: 'sys/user/query.do|p_复制',
    ORG: 'sys/user/query.do|p_岗位权限设置',
    RULE_BASE: 'sys/user/query.do|p_分配规则库',
  },

  // 角色管理相关权限
  SYS_ROLE: {
    ADD: 'sys/role/query.do|p_新增',
    UPDATE: 'sys/role/query.do|p_修改',
    DELETE: 'sys/role/query.do|p_删除',
    ASSIGN: 'sys/role/query.do|p_分配菜单',
    BUTTON: 'sys/role/query.do|p_分配按钮',
    RULE_BASE:'sys/role/query.do|p_分配规则库'
  },
} as const;

// 权限类型定义
export type RulePermissionType = typeof RULE_PERMISSION;
export type RulePermissionKeys = keyof typeof RULE_PERMISSION;
export type RuleBasePermissionKeys = keyof typeof RULE_PERMISSION.RULE_BASE;
export type RuleHistoryPermissionKeys = keyof typeof RULE_PERMISSION.RULE_HISTORY;
export type RuleKeys = keyof typeof RULE_PERMISSION.RULE;
export type RuleArgPermissionKeys = keyof typeof RULE_PERMISSION.RULE_ARG;
export type RuleModelPermissionKeys = keyof typeof RULE_PERMISSION.RULE_MODEL;
export type RuleCheckPermissionKeys = keyof typeof RULE_PERMISSION.RULE_CHECK;
export type SnapshootPermissionKeys = keyof typeof RULE_PERMISSION.SNAPSHOOT;
export type BomModelPermissionKeys = keyof typeof RULE_PERMISSION.BOM_MODEL;
export type RuleMusterPermissionKeys = keyof typeof RULE_PERMISSION.RULE_MUSTER;
export type SysUserPermissionKeys = keyof typeof RULE_PERMISSION.SYS_USER;
export type SysRolePermissionKeys = keyof typeof RULE_PERMISSION.SYS_ROLE;
