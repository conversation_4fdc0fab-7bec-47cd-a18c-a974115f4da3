<template>
  <span class="childExpressionCom">
    <!-- 渲染 ComTypeHandler 组件 -->
    <ComTypeHandler
      :pos="pos"
      :currentType="currentType"
      :disabledItems="disabledItems"
      :locked="locked"
      @changeComType="changeComType"
    />
    <!-- 左括号 -->
    <span class="brackets txtItem">(</span>
    <!-- 遍历 params 数组，根据 item.type 渲染不同的组件 -->
    <template v-if="params && params.length > 0">
      <template v-for="(item, index) in params" :key="pos + '_' + index + 'wrapper'">
        <!-- 渲染 VariableCom 组件 -->
        <span v-if="item.type === 'variable'">
          <VariableCom
            :key="pos + '_' + index"
            :pos="pos + '_' + index"
            :locked="locked"
            :isTrack="isTrack"
            :isLastOne="index === params.length - 1"
            :dataSource="item.data"
            :signValue="symbols[index]"
            :isTable="isTable"
            @onChange="onVariableComChange"
            @onCalSignChange="onChildSignChange"
          />
        </span>
        <!-- 渲染 ChildrenExpression 组件 -->
        <span v-if="item.type === 'expression'">
          <ChildrenExpression
            :key="pos + '_' + index"
            :pos="pos + '_' + index"
            :locked="locked"
            :isTrack="isTrack"
            :isLastOne="index === params.length - 1"
            :expressionTreeData="item"
            :signValue="symbols[index]"
            :isTable="isTable"
            @onChange="onChildChange"
            @onCalSignChange="onChildSignChange"
          />
        </span>
      </template>
    </template>
    <!-- 右括号 -->
    <span class="brackets txtItem">)</span>
    <!-- 渲染 CalculateSign 组件 -->
    <CalculateSign
      :pos="pos"
      :value="signValue"
      :locked="locked"
      :isTrack="isTrack"
      :isLastOne="isLastOne"
      @onChange="changeCalculateSign"
    />
  </span>
</template>

<script setup>
import { ComTypeHandler, CalculateSign } from "@/components/ruleEditCom/utils/index";
import * as util from "@/components/ruleEditCom/utils/util";
import VariableCom from "@/components/ruleEditCom/variable/variableCom.vue";
import ChildrenExpression from "./childrenExpression.vue";

// 定义 props
const props = defineProps({
  pos: String,
  expressionTreeData: {
    type: Object,
    default: () => ({}),
  },
  signValue: String,
  locked: Boolean,
  isTrack: Boolean,
  valueType: String,
  isLastOne: Boolean,
  isTable: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit
const emit = defineEmits(['onChange', 'onCalSignChange']);

// 计算属性
const symbols = computed(() => props.expressionTreeData.symbols);
const params = computed(() => props.expressionTreeData.params);

// 常量
const currentType = 'expression';
const disabledItems = [];

// 方法
const onChildChange = (childPos, newValueObj, finalValueType) => {
  const { expressionTreeData, pos } = props;
  const { type } = newValueObj;
  const newComData = util.cloneRuleData(expressionTreeData);
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const newValueType = finalValueType || util.getRealValueType(newComData);

  if (type && type === "expression") {
    newComData.params[index] = newValueObj;
  } else {
    newComData.params[index] = {
      data: newValueObj,
      type: "variable",
    };
  }
  emit("onChange", pos, newComData, newValueType);
};

const onVariableComChange = (childPos, newValueObj, finalValueType) => {
  const { variableType } = newValueObj;
  if (variableType === "expression") {
    const newChildExpressionObj = newValueObj.expressionTreeData.params[0];
    onChildChange(childPos, newChildExpressionObj, finalValueType);
  } else {
    onChildChange(childPos, newValueObj, finalValueType);
  }
};

const onChildSignChange = (childPos, sign, newParam) => {
  const { expressionTreeData, pos, valueType } = props;
  const newComData = util.cloneRuleData(expressionTreeData);
  const { symbols: new_symbols, params: new_params } = newComData;
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const isLastItem = index === new_params.length - 1;

  if (sign !== "delete") {
    if (!newParam && isLastItem) {
      return;
    }
    new_symbols[index] = sign;
    newParam && new_params.splice(index + 1, 0, newParam);
    !isLastItem && (new_symbols.splice(index + 1, 0, new_symbols[index+1] ? new_symbols[index+1] : sign));
  } else {
    if (!newParam && isLastItem) {
      return;
    }
    new_symbols.splice(index, 1);
    new_params.splice(index + 1, 1);
  }
  emit("onChange", pos, newComData, valueType);
};

const changeCalculateSign = (pos, sign, paramType) => {
  const { valueType } = props;
  const newParamItem = util.getExpressionItem(paramType, valueType);
  emit("onCalSignChange", pos, sign, newParamItem);
};

const changeComType = (pos, type) => {
  const { valueType } = props;
  const newVariableData = {};
  const newComData = {};
  newVariableData.valueType = valueType;
  if (type === "constant") {
    newVariableData.variableType = "constant";
    newVariableData.value = "";
  } else if (type === "propSelect") {
    newVariableData.variableType = "field";
    newVariableData.value = [];
  }
  newComData.type = "variable";
  newComData.data = newVariableData;
  emit("onChange", pos, newComData, valueType);
};

const randomRgbColor = () => {
  var r = Math.floor(Math.random() * 256);
  var g = Math.floor(Math.random() * 256);
  var b = Math.floor(Math.random() * 256);
  return `rgb(${r},${g},${b},0.7)`;
};
</script>
