<!-- 非列表数据显示，布局组件 -->

<template>
    <div class="DashboardLayout-module_wrapper_bRvE0">
        <div class="ant-row" style="row-gap: 0px;">
            <div class="ant-col DashboardLayout-module_main_6rn+d DashboardLayout-module_superSidebarInvisible_vap-Q">
                <div class="Books-module_container_w2bB6">

                    <div class="Books-module_header_VoxHN">
                        <div class="Books-module_bookTitle_aNqu0 DashboardTitle-module_titleWarp_CQylY">
                            <slot name="title" />
                        </div>
                    </div>

                    <div class="Books-module_commonList_Q928s search-table-search">
                            <slot name="search"/>
                    </div>

                    <div class="Books-module_layout_prAqE">
                        <slot name="table"/>
                    </div>
                    <slot />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
    :deep(.search-table-search) {
        .ant-form-inline .ant-form-item {
            margin-bottom: 10px;
        }
    }
    .Books-module_container_w2bB6 {
        padding: 20px 36px 10px 36px;
    }
</style>
