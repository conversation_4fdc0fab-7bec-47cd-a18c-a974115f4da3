<!-- 规则更新 -->

<template>
    <div id="rule_attribute">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="规则名称" name="ruleName">
                <a-input autocomplete="off" v-model:value="form.ruleName" placeholder="规则名称"  />
            </a-form-item>
            <a-form-item label="有效状态" name="validStatus">
                <a-select v-model:value="form.validStatus" placeholder="请选择">
                    <a-select-option v-for="item in validStateOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则类型">
                <a-select v-model:value="form.type" placeholder="请选择" disabled>
                    <a-select-option v-for="item in ruleTypeOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则优先级" v-show="salienceShow">
                <a-select v-model:value="form.salience" placeholder="请选择">
                    <a-select-option v-for="item in priorityOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则流类型" v-show="flowTypeShow" name="ruleFlowType">
                <a-select v-model:value="form.ruleFlowType" placeholder="请选择" disabled>
                    <a-select-option v-for="item in ruleFlowTypeOptions" :key="item.code" :value="item.code"  @change="ruleFlowTypeChange">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则编号" name="ruleNumber">
                <a-input autocomplete="off" v-model:value="form.ruleNumber" placeholder="规则编号" />
            </a-form-item>
            <a-form-item label="规则描述" name="descs">
                <a-textarea v-model:value="form.descs" :auto-size="{ minRows: 2, maxRows: 4 }" :maxLength="500"
                     />
            </a-form-item>
            <a-form-item label="生效时间：" name="effectDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="effectDate"
                        v-model:pubTime="effectTime"
                        @change="(date, time) => handleEffectDateChange(date, time)"
                    />
                </div>
            </a-form-item>
            <a-form-item label="失效时间：" name="expiredDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="expiredDate"
                        v-model:pubTime="expiredTime"
                        @change="(date, time) => handleExpiredDateChange(date, time)"
                    />
                </div>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { getRuleByUuid, ruleSaveOrUpdate, getDicValueByTypeCode } from '@/api/rule_base';
import { showGlobalLoading, hideGlobalLoading } from '@/utils/loading';
const message = inject('message')

const props = defineProps({
    ruleBaseId: {
        type: String,
        default: () => '',
    },
    rulePackageId: {
        type: String,
        default: () => '',
    },
    ruleId: {
        type: String,
        default: () => '',
    },
})
const ruleBaseId = ref('');
const demandUuid = ref('');
const form = reactive({
    ruleName: '',
    validStatus: '',
    type: '',
    ruleNumber: '',
    salience: '',
    descs: '',
    ruleFlowType:''
});

const effectDate = ref('');
const effectTime = ref('');
const expiredDate = ref('');
const expiredTime = ref('');

// 处理ISO时间格式转换为日期和时间
const parseISODateTime = (isoString) => {
    if (!isoString) return { date: '', time: '' };
    const date = new Date(isoString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return {
        date: `${year}-${month}-${day}`,
        time: `${hours}:${minutes}`
    };
};
const flowTypeShow = ref(false);
const salienceShow = ref(true);
const rules = {
    ruleName: [
        { required: true, message: '不能为空', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\d\(\)\（\）、\-\%]+$/, message: '规则名称只能包括中文、数字、字母、下划线、中英文小括号和、-% 符号' },
    ],
    validState: [{ required: true, message: '不能为空', trigger: 'blur' }],
    ruleCode: [{ pattern: /^[0-9a-zA-Z]{2,50}$/, message: '规则编号长度必须在2-50之间,且只能包含字母和数字' }],
};

const validStateOptions = ref([]);
const ruleTypeOptions = ref([]);
const priorityOptions = ref([]);
const ruleFlowTypeOptions = ref([]);
const ruleForm = ref(null);


const emit = defineEmits(['removeTab', 'addTab']);

const getDicValue = (type, fn) => {
    getDicValueByTypeCode({ typeCode: type }).then((res) => {
        fn(res.data);
    });
};

const handleSubmit = (callback) => {
    ruleForm.value.validate()
        .then(() => {
            if (ruleBaseId.value) {
                showGlobalLoading('修改中，请耐心等待');
                // 验证时间格式
                if (effectDate.value && !effectTime.value) {
                    message.error('请选择完整的生效时间');
                    return Promise.reject('生效时间不完整');
                }
                if (expiredDate.value && !expiredTime.value) {
                    message.error('请选择完整的失效时间');
                    return Promise.reject('失效时间不完整');
                }

                // 验证时间先后顺序
                const effectDateTime = new Date(`${effectDate.value} ${effectTime.value}:00`);
                const expiredDateTime = new Date(`${expiredDate.value} ${expiredTime.value}:00`);
                if(effectDate.value && expiredDate.value){
                    if (effectDateTime >= expiredDateTime) {
                        message.error('失效时间必须晚于生效时间');
                        return Promise.reject('时间顺序错误');
                    }
                }
                // 格式化日期为后端期望的格式
                const formatDate = (date, time) => {
                    if (!date || !time) return null;
                    const d = new Date(`${date} ${time}:00`);
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    const hours = String(d.getHours()).padStart(2, '0');
                    const minutes = String(d.getMinutes()).padStart(2, '0');
                    const seconds = String(d.getSeconds()).padStart(2, '0');
                    const milliseconds = String(d.getMilliseconds()).padStart(3, '0');
                    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+0800`;
                };

                ruleSaveOrUpdate({
                    ...ruleInfoData.value,
                    ...form,
                    effectDate: formatDate(effectDate.value, effectTime.value),
                    expiredDate: formatDate(expiredDate.value, expiredTime.value),
                }, ruleBaseId.value, demandUuid.value).then((res) => {
                    if (res.code === 20000 && res.data === '修改成功！') {
                        hideGlobalLoading();
                        message.success(res.data);
                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        hideGlobalLoading();
                        message.error(res.data);
                    }
                }).catch(()=>{
                    hideGlobalLoading();
                });
            }
        })
        .catch((error) => {
            console.log(error);
        });
};

const ruleInfoData = ref({});
async function getRuleInfo(ruleId) {
    const ruleInfo = await getRuleByUuid({
        uuids: ruleId,
    });

    const data = ruleInfo.data;

    if (!data) return;

    ruleInfoData.value = data;

    form.ruleName = data.ruleName;
    form.validStatus = data.validStatus;
    form.type = data.type;
    form.salience = data.salience;
    form.descs = data.descs;
    form.ruleNumber = data.ruleNumber;

    // 处理生效时间回显
    if (data.effectDate) {
        const effectDateTime = parseISODateTime(data.effectDate);
        effectDate.value = effectDateTime.date;
        effectTime.value = effectDateTime.time;
    }

    // 处理失效时间回显
    if (data.expiredDate) {
        const expiredDateTime = parseISODateTime(data.expiredDate);
        expiredDate.value = expiredDateTime.date;
        expiredTime.value = expiredDateTime.time;
    }

    ruleFlowInit();
}
useRuleBaseId(props,ruleBaseId,demandUuid)
onMounted(async () => {


    getDicValue('ruleValidStatus', (arr) => {
        validStateOptions.value = arr;
    });
    getDicValue('type_rule', (arr) => {
        ruleTypeOptions.value = arr;
    });
    getDicValue('salience_rule', (arr) => {
        priorityOptions.value = arr;
    });
    // 规则流类型
    getDicValue("ruleFlowType", (arr) => {
        ruleFlowTypeOptions.value = arr;
    });

    getRuleInfo(props.ruleId)

});
const ruleFlowInit = () => {
    if(form.type === "5"){
        flowTypeShow.value = true;
        salienceShow.value = false;
        if(form.salience === "1"){
            form.ruleFlowType = "1";
        } else {
            form.ruleFlowType = "0";
        }
    } else {
        flowTypeShow.value = false;
        salienceShow.value = true;
    }
}
defineExpose({
    handleSubmit,
    getRuleInfo
});

// 添加时间变化处理函数
const handleEffectDateChange = (date, time) => {
    effectDate.value = date;
    effectTime.value = time;
};

const handleExpiredDateChange = (date, time) => {
    expiredDate.value = date;
    expiredTime.value = time;
};
</script>

<style lang="scss" scoped>
#rule_attribute {
    :deep(.ant-input),
    :deep(.ant-select),
    :deep(.ant-upload-dragger),
    :deep(.ant-upload-list),
    :deep(.ant-input-textarea) {
        width: 400px;
    }

    :deep(.ant-input-textarea) {
        height: 400px;
    }

    :deep(.ant-upload-dragger) {
        height: 100px;
    }

    :deep(.ant-upload-dragger .anticon-upload) {
        margin: 0;
        line-height: 50px;
        font-size: 50px;
    }

    :deep(.jarUpload.hidden .ant-upload) {
        display: none;
    }

    :deep(.ant-tabs) {
        width: 70%;
        min-height: 100px;
        margin: 30px 30px 30px 120px;
    }

    :deep(.ant-input-textarea),
    :deep(textarea) {
        height: 150px !important;
    }

    :deep(.ant-form) {
        padding-top: 30px;
    }

    :deep(.ant-form-item-explain-error) {
        color: #ff4d4f !important;
    }
}
</style>
