//任务状态
export const TASK_STATE = {
    1: '任务填报中',
    2: '需求待审核',
    3: '需求审核中',
    4: '需求审核退回',
    5: '规则待编写',
    6: '规则编写中',
    7: '规则待测试',
    8: '规则测试中',
    9: '规则测试验证错误，退回',
    10: '任务待发布',
    11: '任务发布失败，生产退回',
    12: '任务暂停发布',
    13: '任务待审核',
    14: '结束',
    15: '规则编写完成',
    16: '发布测试环境失败',
    17: '发布测试环境等待',
    18: '任务审核发布退回',
    19: '任务审核中',
    20: '任务发布中',
    21: '任务待立即发布',
    22: '任务发布异常',
    23: '任务发布退回',
    24: '政策填报中',
    25: '政策待审核',
    26: '政策审核中',
    27: '政策审核退回',
    28: '政策审核下发',
}
//table通用样式
export const tableColumns = [
    {
        title: '任务名称',
        dataIndex: 'demandName',
        key: 'demandName',
        width:250,
        align:'left',
        fixed: 'left',
        ellipsis: true,
    },
    {
        title: '申请人',
        dataIndex: 'createdName',
        key: 'createdName',
        width:200,
        align:'left',
    },
    {
        title: '申请时间',
        dataIndex: 'createdTimeStr',
        key: 'createdTimeStr',
        align:'center',
        width:180,
    },
    {
        title: '状态',
        dataIndex: 'state',
        key: 'state',
        width:200,
        align:'center',
    },
    {
        title: '任务编码',
        dataIndex: 'appNo',
        key: 'appNo',
        width:200,
        align:'left',
    },

    {
        title: '归属机构',
        dataIndex: 'applyOrgName',
        key: 'applyOrgName',
        align:'center',
        width:100,
    },
    {
        title: '审核机构',
        dataIndex: 'orgName',
        key: 'orgName',
        align:'center',
        width:100,
    },
    {
        title: '业务条线',
        dataIndex: 'businessLineName',
        key: 'businessLineName',
        width:150,
        align:'center',
        checked: false
    },

    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        align:'center',
        width:150,
        fixed:'right',
    },
];
