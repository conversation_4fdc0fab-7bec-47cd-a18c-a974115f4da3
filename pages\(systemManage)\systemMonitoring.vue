<script setup lang="ts">
import { Client } from '@stomp/stompjs';
import { getServer } from "@/api/server";
import { SettingOutlined, AndroidOutlined, WindowsOutlined, LaptopOutlined, MonitorOutlined, CoffeeOutlined } from '@ant-design/icons-vue';
import { allNotifyConfig, saveOrUpdateNotifyConfig, getNotifyConfigById } from '@/api/notifyConfig';
import { getLatestServerMonitorConfig, updateServerMonitorConfig } from '@/api/serverMonitorConfig';
import { getUsersWithNotification } from '@/api/userManagement';  // 导入用户列表接口
import NotifyUserSelect from '@/components/NotifyUserSelect.vue';

definePageMeta({
    title: '系统监控'
})
const message = inject('message')
const server = ref([]);
const notifyConfigList = ref([]); // 通知配置列表
const selectedUserIds = ref([]); // 选中的用户ID数组

// 配置弹窗状态
const configVisible = ref(false);

// 打开配置弹窗
const openConfigModal = async () => {
    // 获取最新配置
    await getServerMonitorConfig();
    await getNotifyConfigs();
    // 显示弹窗
    configVisible.value = true;
};

// 显示配置
const displayConfig = ref({
    cpu: true,
    memory: true,
    serverInfo: true,
    jvmInfo: true,
    diskStatus: true,
    selectedNotifyIds: [],  // 选中的通知配置ID数组
    selectedUserIds: [],    // 选中的用户ID数组
    // 新增阈值和任务间隔配置
    cpuThreshold: '',
    memoryThreshold: '',
    jvmThreshold: '',
    diskThreshold: '',
    taskIntervalTime: ''
});

// 获取通知配置列表
const getNotifyConfigs = async () => {
    try {
        const res = await getUsersWithNotification();
        if (res.code === 20000) {
            notifyConfigList.value = res.data;
            // 根据enableAlertNotification设置选中状态
            const selectedIds = res.data
                .filter(item => item.enableAlertNotification === '1')
                .map(item => item.id);
            displayConfig.value.selectedUserIds = selectedIds;
        }
    } catch (err) {
        console.error('获取通知配置失败:', err);
    }
};

// 更新通知配置
const updateNotifyConfigs = async () => {
    const loadingMessage = message.loading('正在保存通知配置...', 0);
    try {
        for (const item of notifyConfigList.value) {
            const notifyRes = await getNotifyConfigById({id: item.id});
            if (notifyRes.data == null){
                const isSelected = displayConfig.value.selectedUserIds.includes(item.id);
                await saveOrUpdateNotifyConfig({
                    userId:item.id,
                    enableAlertNotification: isSelected ? '1' : '0'
                });
            }else {
                const isSelected = displayConfig.value.selectedUserIds.includes(item.id);
                await saveOrUpdateNotifyConfig({
                    ...notifyRes.data,
                    enableAlertNotification: isSelected ? '1' : '0'
                });
            }
        }
        loadingMessage();
    } catch (err) {
        loadingMessage();
        console.error('更新通知配置失败:', err);
        message.error('更新通知配置失败');
    }
};

// 获取服务器监控配置
const getServerMonitorConfig = async () => {
    try {
        const res = await getLatestServerMonitorConfig();
        if (res.code === 20000) {
            const {
                cpu,
                memory,
                server,
                jvm,
                disk,
                cpuThreshold,
                memoryThreshold,
                jvmThreshold,
                diskThreshold,
                taskIntervalTime,
                id
            } = res.data;

            // 更新显示配置
            displayConfig.value = {
                ...displayConfig.value,
                cpu: cpu === '1',
                memory: memory === '1',
                serverInfo: server === '1',
                jvmInfo: jvm === '1',
                diskStatus: disk === '1',
                // 更新阈值设置
                cpuThreshold: cpuThreshold || '',
                memoryThreshold: memoryThreshold || '',
                jvmThreshold: jvmThreshold || '',
                diskThreshold: diskThreshold || '',
                taskIntervalTime: taskIntervalTime || ''
            };
        }
    } catch (err) {
        console.error('获取服务器监控配置失败:', err);
        message.error('获取服务器监控配置失败');
    }
};
// 保存配置
const saveConfig = async () => {
    try {
        const res = await updateServerMonitorConfig({
            id: 1,
            cpu: displayConfig.value.cpu ? '1' : '0',
            memory: displayConfig.value.memory ? '1' : '0',
            server: displayConfig.value.serverInfo ? '1' : '0',
            jvm: displayConfig.value.jvmInfo ? '1' : '0',
            disk: displayConfig.value.diskStatus ? '1' : '0',
            cpuThreshold: displayConfig.value.cpuThreshold,
            memoryThreshold: displayConfig.value.memoryThreshold,
            jvmThreshold: displayConfig.value.jvmThreshold,
            diskThreshold: displayConfig.value.diskThreshold,
            taskIntervalTime: displayConfig.value.taskIntervalTime,
        });

        if (res.code === 20000) {
            const displayModuleConfig = {
                cpu: displayConfig.value.cpu,
                memory: displayConfig.value.memory,
                serverInfo: displayConfig.value.serverInfo,
                jvmInfo: displayConfig.value.jvmInfo,
                diskStatus: displayConfig.value.diskStatus,
                selectedUserIds: displayConfig.value.selectedUserIds
            };
            localStorage.setItem('monitorDisplayConfig', JSON.stringify(displayModuleConfig));
            await updateNotifyConfigs();
            configVisible.value = false;
            message.success('配置保存成功');
            await getServerMonitorConfig();
        } else {
            message.error('配置保存失败');
        }
    } catch (error) {
        message.error('配置保存失败：' + error.message);
    }
};

// 加载配置
const loadConfig = () => {
    const savedConfig = localStorage.getItem('monitorDisplayConfig');
    if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        // 更新显示模块的配置
        displayConfig.value = {
            ...displayConfig.value,
            cpu: parsedConfig.cpu,
            memory: parsedConfig.memory,
            serverInfo: parsedConfig.serverInfo,
            jvmInfo: parsedConfig.jvmInfo,
            diskStatus: parsedConfig.diskStatus,
            selectedUserIds: parsedConfig.selectedUserIds || []
        };
    }
};

// STOMP客户端配置
const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
const brokerURL = `${protocol}//${window.location.hostname}:18081/BRMS/erule-service/websocket`;

const client = new Client({
    brokerURL: brokerURL,
    onConnect: () => {
        console.log('STOMP连接成功');
        // 订阅服务器信息主题
        client.subscribe('/topic/server-info', (message) => {
            try {
                server.value = JSON.parse(message.body);
            } catch (error) {
                console.error('解析服务器信息失败:', error);
            }
        });
    },
    onDisconnect: () => {
        console.log('STOMP连接断开');
    },
    onStompError: (frame) => {
        console.error('STOMP错误:', frame);
    },
    reconnectDelay: 5000,      // 重连延迟5秒
    heartbeatIncoming: 4000,   // 接收心跳4秒
    heartbeatOutgoing: 4000    // 发送心跳4秒
});

// 组件挂载时
onMounted(async () => {
    // 加载显示配置
    loadConfig();
    // 初始化数据
    try {
        const response = await getServer();
        server.value = response;
    } catch (error) {
        console.error('获取初始服务器信息失败:', error);
    }

    // 激活STOMP连接
    client.activate();
});

// 组件卸载时
onUnmounted(() => {
    // 断开STOMP连接
    if (client.connected) {
        client.deactivate();
    }
});

// 根据百分比返回不同的颜色
const getProgressColor = (percent) => {
    if (percent < 60) {
        return '#52c41a'; // 绿色
    } else if (percent < 80) {
        return '#faad14'; // 黄色
    } else {
        return '#f5222d'; // 红色
    }
}
</script>
<template>
    <div class="app-container">
        <!-- 添加顶部配置按钮 -->
        <div class="top-config">
            <a-button type="primary" @click="openConfigModal">
                <template #icon><SettingOutlined /></template>
                系统监控配置
            </a-button>
        </div>
        <a-row :gutter="[16, 16]">
            <!-- CPU信息卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="displayConfig.cpu">
                <a-card hoverable class="monitor-card">
                    <div slot="title" class="title-style">
                        <AndroidOutlined class="card-icon" />
                        <span class="card-title">CPU</span>
                    </div>
                    <div slot="description">
                        <table cellspacing="0" class="monitor-table">
                            <thead>
                                <tr>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">属性</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">值</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">核心数</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">{{ server.cpu.cpuNum }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">用户使用率</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">
                                            <div class="progress-container">
                                                <a-progress :stroke-color="getProgressColor(server.cpu.used)" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">系统使用率</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">
                                            <div class="progress-container">
                                                <a-progress :percent="server.cpu.sys"
                                                    :stroke-color="getProgressColor(server.cpu.sys)" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">当前空闲率</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">
                                            <div class="progress-container">
                                                <a-progress :percent="server.cpu.free" stroke-color="#52c41a" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </a-card>
            </a-col>

            <!-- 内存信息卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" v-if="displayConfig.memory">
                <a-card hoverable class="monitor-card">
                    <div slot="title" class="title-style">
                        <WindowsOutlined class="card-icon" />
                        <span class="card-title">内存</span>
                    </div>
                    <div slot="description">
                        <table cellspacing="0" class="monitor-table">
                            <thead>
                                <tr>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">属性</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">内存</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">JVM</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">总内存</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.total }}G</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.total }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">已用内存</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.used }}G</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.used }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">剩余内存</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.free }}G</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.free }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">使用率</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">
                                            <div class="progress-container">
                                                <a-progress :percent="server.mem.usage"
                                                    :stroke-color="getProgressColor(server.mem.usage)" />
                                            </div>
                                        </div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">
                                            <div class="progress-container">
                                                <a-progress :percent="server.jvm.usage"
                                                    :stroke-color="getProgressColor(server.jvm.usage)" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </a-card>
            </a-col>

            <!-- 磁盘状态卡片 -->
            <a-col :span="24" v-if="displayConfig.diskStatus">
                <a-card hoverable class="monitor-card">
                    <div slot="title" class="title-style">
                        <LaptopOutlined class="card-icon" />
                        <span class="card-title">磁盘状态</span>
                    </div>
                    <div slot="description">
                        <table cellspacing="0" class="monitor-table">
                            <thead>
                                <tr>
                                    <th class="a-table__cell a-table__cell is-leaf">
                                        <div class="cell">盘符路径</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">文件系统</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">盘符类型</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">总大小</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">可用大小</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">已用大小</div>
                                    </th>
                                    <th class="a-table__cell is-leaf">
                                        <div class="cell">已用百分比</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody v-if="server.sysFiles">
                                <tr v-for="(sysFile, index) in server.sysFiles" :key="index">
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.dirName }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.sysTypeName }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.typeName }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.total }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.free }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.used }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">
                                            <div class="progress-container">
                                                <a-progress :percent="sysFile.usage"
                                                    :stroke-color="getProgressColor(sysFile.usage)" />
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </a-card>
            </a-col>

            <!-- 服务器信息卡片 -->
            <a-col :span="24" v-if="displayConfig.serverInfo">
                <a-card hoverable class="monitor-card">
                    <div slot="title" class="title-style">
                        <MonitorOutlined class="card-icon" />
                        <span class="card-title">服务器信息</span>
                    </div>
                    <div slot="description">
                        <table cellspacing="0" class="monitor-table">
                            <tbody>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">服务器名称</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.computerName }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">操作系统</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.osName }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">服务器IP</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.computerIp }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">系统架构</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.osArch }}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </a-card>
            </a-col>

            <!-- Java虚拟机信息卡片 -->
            <a-col :span="24" v-if="displayConfig.jvmInfo">
                <a-card hoverable class="monitor-card">
                    <div slot="title" class="title-style">
                        <CoffeeOutlined class="card-icon" />
                        <span class="card-title">Java虚拟机信息</span>
                    </div>
                    <div slot="description">
                        <table cellspacing="0" class="monitor-table java-table">
                            <tbody>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">Java名称</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.name }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">Java版本</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.version }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">启动时间</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.startTime }}</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell">运行时长</div>
                                    </td>
                                    <td class="a-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.runTime }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="a-table__cell is-leaf">
                                        <div class="cell">安装路径</div>
                                    </td>
                                    <td colspan="3" class="a-table__cell is-leaf">
                                        <div class="cell path-cell" v-if="server.jvm">{{ server.jvm.home }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="a-table__cell is-leaf">
                                        <div class="cell">项目路径</div>
                                    </td>
                                    <td colspan="3" class="a-table__cell is-leaf">
                                        <div class="cell path-cell" v-if="server.sys">{{ server.sys.userDir }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="a-table__cell is-leaf">
                                        <div class="cell">运行参数</div>
                                    </td>
                                    <td colspan="3" class="a-table__cell is-leaf">
                                        <div class="cell path-cell" v-if="server.jvm">{{ server.jvm.inputArgs }}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </a-card>
            </a-col>
        </a-row>
        <!-- 配置弹窗 -->
        <a-modal v-model:visible="configVisible" title="监控显示配置" @ok="saveConfig" okText="保存" cancelText="取消"
                 width="800px" :maskClosable="false" class="monitor-config-modal">
            <div class="config-form">
                <div class="form-item">
                    <div class="label">显示模块</div>
                    <div class="content">
                        <a-checkbox v-model:checked="displayConfig.cpu">CPU信息</a-checkbox>
                        <a-checkbox v-model:checked="displayConfig.memory">内存信息</a-checkbox>
                        <a-checkbox v-model:checked="displayConfig.diskStatus">磁盘状态</a-checkbox>
                        <a-checkbox v-model:checked="displayConfig.serverInfo">服务器信息</a-checkbox>
                        <a-checkbox v-model:checked="displayConfig.jvmInfo">Java虚拟机信息</a-checkbox>
                    </div>
                </div>

                <a-divider style="margin: 16px 0" />

                <div class="form-item">
                    <div class="label">阈值设置</div>
                    <div class="content">
                        <div class="threshold-item" style="margin-top: 5px">
                            <span class="threshold-label" style="margin-right: 5px">CPU阈值(%)</span>
                            <a-input v-model:value="displayConfig.cpuThreshold" placeholder="请输入CPU阈值"
                                     style="width: 150px" size="small" />
                        </div>
                        <div class="threshold-item" style="margin-top: 5px">
                            <span class="threshold-label" style="margin-right: 5px">内存阈值(%)</span>
                            <a-input v-model:value="displayConfig.memoryThreshold" placeholder="请输入内存阈值"
                                     style="width: 150px" size="small" />
                        </div>
                        <div class="threshold-item" style="margin-top: 5px">
                            <span class="threshold-label" style="margin-right: 5px">JVM阈值(%)</span>
                            <a-input v-model:value="displayConfig.jvmThreshold" placeholder="请输入JVM阈值"
                                     style="width: 150px" size="small" />
                        </div>
                        <div class="threshold-item" style="margin-top: 5px">
                            <span class="threshold-label" style="margin-right: 5px">磁盘阈值(%)</span>
                            <a-input v-model:value="displayConfig.diskThreshold" placeholder="请输入磁盘阈值"
                                     style="width: 150px" size="small" />
                        </div>
                        <div class="threshold-item" style="margin-top: 5px">
                            <span class="threshold-label" style="margin-right: 5px">任务间隔(小时)</span>
                            <a-input v-model:value="displayConfig.taskIntervalTime" placeholder="请输入任务间隔"
                                     style="width: 150px" size="small" />
                        </div>
                    </div>
                </div>

                <a-divider style="margin: 16px 0" />

                <div class="form-item">
                    <div class="label">预警邮件通知用户</div>
                    <div class="content">
                        <NotifyUserSelect
                            v-model="displayConfig.selectedUserIds"
                            :notifyConfigList="notifyConfigList"
                            :autoSelect="true"
                        />
                    </div>
                </div>
            </div>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped="scoped">
.app-container {
    padding: 15px;
    background-color: #f0f2f5;
    min-height: 100vh;

    .top-config {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-end;
    }

    .title-style {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-icon {
        font-size: 22px;
        margin-right: 10px;
        color: #1890ff;
    }

    .card-title {
        font-size: 18px;
        font-weight: 600;
    }

    .monitor-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        height: 100%;
        transition: all 0.3s;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
    }

    .monitor-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
            font-size: 14px;
        }

        th {
            background-color: #fafafa;
            font-weight: 600;
            text-align: left;
        }

        .a-table__cell {
            padding: 16px;
            transition: background-color 0.3s;

            &:first-child {
                font-weight: 500;
                width: 120px;
                background-color: #fafafa;
            }
        }

        .is-leaf {
            border-bottom: 1px solid #f0f0f0;
        }

        tr:hover .a-table__cell {
            background-color: #e6f7ff;
        }
    }

    .java-table {
        table-layout: fixed;

        .path-cell {
            word-break: break-all;
            font-family: "Courier New", monospace;
            font-size: 12px;
            background-color: #f9f9f9;
            padding: 6px;
            border-radius: 4px;
        }
    }

    .progress-container {
        display: flex;
        align-items: center;

        .ant-progress {
            flex: 1;
            margin-right: 10px;
        }
    }

    .text-danger {
        color: #f5222d;
        font-weight: bold;
    }

    :deep(.monitor-config-modal) {
        .ant-modal-content {
            border-radius: 8px;

            .ant-modal-body {
                padding: 24px;

                .config-form {
                    .form-item {
                        display: flex;
                        align-items: flex-start;
                        margin-bottom: 16px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .label {
                            width: 70px;
                            line-height: 32px;
                            flex-shrink: 0;
                            color: #000000d9;
                            font-size: 14px;
                        }

                        .content {
                            flex: 1;
                            padding-top: 6px;

                            .ant-checkbox-wrapper {
                                margin-right: 24px;
                                margin-bottom: 8px;

                                &:last-child {
                                    margin-right: 0;
                                }

                                .ant-checkbox {
                                    top: 0;
                                }

                                span {
                                    font-size: 14px;
                                }
                            }
                        }
                    }
                }

                .ant-divider {
                    margin: 16px 0;
                }
            }
        }
    }
}
</style>
