<!-- components/Pagination.vue -->
<template>
    <div class="pagination">
        <a-pagination
                v-show="paginations.total > 0"
                :current="paginations.page"
                :total="paginations.total"
                v-model:pageSize="paginations.limit"
                :showSizeChanger="paginations.showSizeChanger"
                @change="handleChange"
                :showQuickJumper="true"
                :showTotal="paginations.showTotal"
                :disabled="loading"
                :page-size-options="pageSizeOptions"
        >
          <!--增加全部分页展示-->
          <template #buildOptionText="props">
            <span v-if="props.value !== '10000'">{{ props.value }}条/页</span>
            <span v-else>全部</span>
          </template>
        </a-pagination>
    </div>
</template>

<script setup>
    import { defineProps, defineEmits, watch, ref, onMounted } from 'vue';
    const pageSizeOptions = ref(['10', '20', '50', '100', '10000']);
    const props = defineProps({
        paginations: {
            type: Object,
            required: true
        },
        scrollY:Number,
        loading: {
            type: Boolean,
            default: false,
        },
        pageNum:{
            type:Number,
            default:1
        },
        // 新增模式参数，用于区分规则管理和规则库管理
        mode: {
            type: String,
            default: 'ruleBase' // 默认为规则库管理模式
        }
    });

    onMounted(()=>{
        //根据行高计算显示多少条数据
        setTimeout(()=>{
          // 根据不同模式计算不同的分页大小
          if (props.mode === 'ruleManage') {
            // 规则管理使用更小的行高计算
            emit('change', props.pageNum, parseInt(props.scrollY/45)<1?1:parseInt(props.scrollY/50));
          } else {
            // 规则库管理使用原有计算逻辑
            emit('change', props.pageNum, parseInt(props.scrollY/40)<1?1:parseInt(props.scrollY/40));
          }
        })
    })
    const emit = defineEmits(['change']);

    const handleChange = (page,pageSize) => {
        // 处理"全部"选项（值为10000）
        if (pageSize === 10000) {
            // 如果选择了"全部"，将pageSize设置为实际的总记录数
            emit('change', page, props.paginations.total);
        } else {
            emit('change', page, pageSize);
        }
    };
</script>

<style scoped>
    /* 分页容器样式 */
    .pagination {
        display: flex;
        justify-content: flex-end;
        width: 100%;
    }

    :deep(.ant-select-dropdown) {
        top: auto !important;
        bottom: 100% !important;
    }
</style>
