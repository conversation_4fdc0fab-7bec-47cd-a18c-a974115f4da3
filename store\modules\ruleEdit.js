
const state = {

  // 当前编辑规则id
  currentRuleId: '',
  // setListMap
  listMap: {},
  preListMap: {},
  editData:{},
  listMapOrder: null,
  textRule: {},
  // 缓存标签数据（按engId存储）
  ruleTextTags: {},
  // 缓存业务字段数据（按engUuid存储）
  businessFields: {}
}

const mutations = {

  // 设置当前编辑的规则
  setCurrentEdit: (state, val) => {
    state.editData = val
  },
  // 设置当前编辑的规则id
  setCurrentRuleId: (state, val) => {
    state.currentRuleId = val
  },
  // 设置当前编辑的listMapOrder
  setListMapOrder: (state, val) => {
    state.listMapOrder = val
  },
  // 设置listMap
  setListMap: (state, val) => {
    state.listMap[val.ruleUuid] = val['list']
  },
  // 设置initModelData
  setInitModelData: (state, val) => {
    if(val.ruleUuid){
      if(!state.listMap[val.ruleUuid]){
        state.listMap[val.ruleUuid] = {}
      }
      state.listMap[val.ruleUuid].initModelData = val['list']
    }
  },
  // 设置fieldsDataList
  setFieldsDataList: (state, val) => {
    if(val.ruleUuid){
      if(!state.listMap[val.ruleUuid]){
        state.listMap[val.ruleUuid] = {}
      }
      state.listMap[val.ruleUuid].initModelData.fieldsDataList = val['list']
    }
  },
  // 设置preListMap
  setPreListMap: (state, val) => {
    state.preListMap[val.ruleUuid] = val['list']
  },
  // 设置preInitModelData
  setPreInitModelData: (state, val) => {
    if(val.ruleUuid){
      if(!state.listMap[val.ruleUuid]){
        state.listMap[val.ruleUuid] = {}
      }
      state.preListMap[val.ruleUuid].initModelData = val['list']
    }
  },
  // 设置当前编辑的listMapOrder
  setTextRule: (state, val) => {
    state.textRule[val.ruleUuid] = val['text']
  },
  // 设置标签数据缓存
  setRuleTextTags: (state, val) => {
    state.ruleTextTags[val.engId] = val['tags']
  },
  // 设置业务字段数据缓存
  setBusinessFields: (state, val) => {
    state.businessFields[val.engUuid] = val['fields']
  },
}

const getters  = {
  currentRuleId: (state) => state.currentRuleId,
  listMap: (state) => state.listMap,
  preListMap: (state) => state.preListMap,
  editData: (state) => state.editData,
  listMapOrder: (state) => state.listMapOrder,
  textRule: (state) => state.textRule,
  ruleTextTags: (state) => state.ruleTextTags,
  businessFields: (state) => state.businessFields,
};

export default {
  namespaced: false,
  state,
  mutations,
  getters
}
