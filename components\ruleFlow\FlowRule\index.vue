<script setup>
import { ruleModalInit } from "@/api/rule_editor";
import RuleUpdate from "./ruleUpdate.vue";

// 定义 props
const props = defineProps({
  ruleInfo: {
    type: Object,
    default: () => ({}),
  },
  conditions: {
    type: Array,
    default: () => [],
  },
  activeShapeId: {
    type: String,
    default: () => "",
  },
});

// 定义 emits
const emit = defineEmits(["setConditions"]);

// 定义响应式数据
const modelInit = ref({});
const ruleContent = ref({});
const uuid = ref("");
const demandUuid = ref("");
const ruleUpdate = ref(null);

// 获取数据的方法
const getData = (engUuid) => {
  ruleModalInit(engUuid)
    .then((res) => {
      modelInit.value = res.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

// 监听 ruleInfo 的变化
watch(
  () => props.ruleInfo,
  (newValue) => {
    const { engUuid, uuid: newUuid, demandUuid: newDemandUuid } = newValue;
    uuid.value = newUuid;
    demandUuid.value = newDemandUuid;
    getData(engUuid, newUuid);
  },
  { deep: true }
);

// 监听 activeShapeId 的变化
watch(
  () => props.activeShapeId,
  (newValue) => {
    try {
      const hasObj = props.conditions.some((i) => i.nodeId === newValue);
      if (hasObj) {
        props.conditions.forEach((item) => {
          if (item.nodeId === newValue) {
            ruleContent.value = item;
          }
        });
      } else {
        // 确保 ruleContent 是一个对象
        if (Object.keys(ruleContent.value).length === 0) {
          ruleContent.value = {
            nudeId: newValue,
            nodeRule: {
              conditionExpression: "#",
              ruleAttributes: [],
              elseActions: [],
              conditions: [
                {
                  variable: {
                    valueType: "String",
                    variableType: "dataEntry",
                    viewName: "",
                  },
                  leftValueType: "String",
                },
              ],
              predefines: [],
              actions: [],
            },
          };
        } else {
          // 如果 ruleContent 已经有值，确保 nodeRule 存在
          if (!ruleContent.value.nodeRule) {
            ruleContent.value.nodeRule = {
              conditionExpression: "#",
              ruleAttributes: [],
              elseActions: [],
              conditions: [
                {
                  variable: {
                    valueType: "String",
                    variableType: "dataEntry",
                    viewName: "",
                  },
                  leftValueType: "String",
                },
              ],
              predefines: [],
              actions: [],
            };
          } else {
            // 处理 conditionExpression
            if (
              ruleContent.value.nodeRule.conditionExpression === "null" ||
              !ruleContent.value.nodeRule.conditionExpression ||
              ruleContent.value.nodeRule.conditionExpression === "undefined"
            ) {
              ruleContent.value.nodeRule.conditionExpression = "#";
            }
            // 处理 conditions
            if (
              ruleContent.value.nodeRule.conditions &&
              ruleContent.value.nodeRule.conditions.length === 0
            ) {
              ruleContent.value.nodeRule.conditions = [
                {
                  variable: {
                    valueType: "String",
                    variableType: "dataEntry",
                    viewName: "",
                  },
                  leftValueType: "String",
                },
              ];
            }
          }
        }
      }
    } catch (error) {
      console.error("Error in activeShapeId watcher:", error);
    }
  },
  { deep: true, immediate: true }
);

// 保存条件的方法
const save = (param) => {
  emit("setConditions", param);
};

// 组件挂载时初始化数据
onMounted(() => {
  if (props.ruleInfo.engUuid) {
    getData(props.ruleInfo.engUuid);
  }
});
</script>

<template>
  <div>
    <!-- 使用 RuleUpdate 组件 -->
    <RuleUpdate
      ref="ruleUpdate"
      :modelData="modelInit"
      :ruleContent="ruleContent"
      :uuid="uuid"
      :ruleDrl="''"
      :demandUuid="demandUuid"
      @save="save"
    />
  </div>
</template>

<style scoped>
/* 添加样式 */
</style>
