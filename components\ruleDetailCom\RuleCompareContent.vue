<!--规则详情比对规则展示通用组件-->
<template>
  <div class="rule-compare-content">
    <div class="ruletable">
      <a-form :model="ruleCompareData">
        <a-row class="row-bg fullrow fulltab">
          <a-col :span="24">
            <a-form-item style="font-weight: 700" label="规则内容：">
              <span v-if="ruleCompareData?.ruleHisVO1?.edition && ruleCompareData?.ruleHisVO2?.edition">
                (版本：{{ ruleCompareData?.ruleHisVO1?.edition || '' }}对比{{ ruleCompareData?.ruleHisVO2?.edition || '' }})
              </span>
            </a-form-item>
            <div
              class="slider-alert-div"
              v-show="showDecisionTableTools"
            >
              <div class="slider-div-left">
                <a-alert
                  message="点击对应单元格可查看完整数据"
                  type="warning"
                  :closable="false"
                  showIcon
                />
                <a-switch
                  style="margin-left: 20px;margin-right: 10px"
                  v-model:checked="switchVal"
                  @change="switchChange('switchVal', 'fulltab')"
                />
                <span style="color: gray">{{checkedValue}}</span>
              </div>
              <div class="slider-div">
                <div class="slider-block">
                  <a-slider
                    v-model:value="sliderValue"
                    @change="sliderInput"
                    :min="0"
                    :max="100"
                    :step="1"
                    :tooltip-visible="false"
                  />
                </div>
                <a-tooltip title="全屏">
                  <FullscreenOutlined style="color:green;margin-left:10px" @click="fullScreen()" v-if="showFullScreen"/>
                </a-tooltip>
              </div>
            </div>
            <div
              class="fakeContainer fixed-table"
              :style="'height:' + tabHeight + 'px'"
            >
              <div v-if="ruleHisVO1IsNormalRule">
                <table
                  class="datatable rule"
                  id="datatable_tc"
                  style="width: 100%"
                >
                  <tr>
                    <td>
                      <span v-html="ruleCompareData?.ruleHisVO1?.textDtl || ''"></span>
                    </td>
                  </tr>
                </table>
              </div>
              <div v-if="ruleHisVO1IsDecisionTable">
                <table
                  class="datatable"
                  id="datatable_tc"
                  :style="{
                    transform: 'scale(' + tableScale + ')',
                    transformOrigin: 'left top',
                  }"
                ></table>
              </div>
            </div>
            <div
              class="fakeContainer2 fixed-table"
              :style="'height:' + tabHeight + 'px;display: none'"
            >
              <div v-if="ruleHisVO2IsNormalRule">
                <table class="datatable2" id="datatable2_tc">
                  <tr>
                    <td>
                      <span v-html="ruleCompareData?.ruleHisVO2?.textDtl || ''"></span>
                    </td>
                  </tr>
                </table>
              </div>
              <div v-if="ruleHisVO2IsDecisionTable">
                <table
                  class="datatable2"
                  id="datatable2_tc"
                  :style="{
                    transform: 'scale(' + tableScale + ')',
                    transformOrigin: 'left top',
                  }"
                ></table>
              </div>
            </div>
            <div
              id="tip-div"
              v-html="tip"
              :style="{ top: tipTop, left: tipLeft }"
              v-show="showTip"
              @mouseenter="tipHover"
              @mouseleave="tipLeave"
            ></div>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>

  <!-- 使用FullModel组件实现全屏展示 -->
  <FullModel
    :isModalVisible="isModalVisible"
    :isFullscreen="isFullscreen"
    :titleText="titleText"
    :handleCancel="handleModalCancel"
    :onFullscreenToggle="onFullscreenToggle"
    :showFullBtn="false"
    :showFooter="false"
  >
    <template #default>
      <div class="fulltab" :style="{maxHeight: isFullscreen ? '75vh' : '60vh'}">
        <RuleCompareContent :rule-compare-data="ruleCompareData" :showFullScreen="false"/>
      </div>
    </template>
  </FullModel>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, computed } from 'vue';
import { differ, beautyTable } from '@/api/table_diff';
import $ from 'jquery';
import FullModel from '@/components/FullModel.vue';

interface RuleHistoryVO {
  edition?: string;
  textDtl?: string;
  type?: string;
  uuid?: string;
  tableContent?: string;
  tableContentHis?: string;
  validStatus?: string;
  ruleUuid?: string;
}

const props = defineProps({
    ruleCompareData: {
        type: Object,
        default: () => ({
            ruleHisVO1: {
                edition: '',
                textDtl: '',
                type: '',
                uuid: '',
                tableContent: '',
                tableContentHis: '',
                validStatus: '',
                ruleUuid: ''
            },
            ruleHisVO2: {
                edition: '',
                textDtl: '',
                type: '',
                uuid: '',
                tableContent: '',
                tableContentHis: '',
                validStatus: '',
                ruleUuid: ''
            }
        })
    },
    //全屏展示显示/隐藏
    showFullScreen: {
        type: Boolean,
        default: true
    },
    tabHeight:{
        type: Number,
        default: 300
    }
});

// 判断规则类型的辅助函数
const isNormalRule = (type?: string) => type === '普通规则' || type === '1';
const isDecisionTable = (type?: string) => type === '决策表' || type === '2';
const isValidRuleType = (type?: string) => isNormalRule(type) || isDecisionTable(type);

// 计算属性，用于简化模板中的条件判断
const showDecisionTableTools = computed(() => {
  return isDecisionTable(props.ruleCompareData?.ruleHisVO1?.type) ||
         isDecisionTable(props.ruleCompareData?.ruleHisVO2?.type);
});

const ruleHisVO1IsNormalRule = computed(() => {
  return isNormalRule(props.ruleCompareData?.ruleHisVO1?.type);
});

const ruleHisVO1IsDecisionTable = computed(() => {
  return isDecisionTable(props.ruleCompareData?.ruleHisVO1?.type);
});

const ruleHisVO2IsNormalRule = computed(() => {
  return isNormalRule(props.ruleCompareData?.ruleHisVO2?.type);
});

const ruleHisVO2IsDecisionTable = computed(() => {
  return isDecisionTable(props.ruleCompareData?.ruleHisVO2?.type);
});

// 决策表样式相关变量
const switchVal = ref(false);
const sliderValue = ref(100);
const tabHeight = ref(props.showFullScreen?props.tabHeight:800); // 默认高度
const minHeight = ref(200); // 最小高度
const maxHeight = ref(800); // 最大高度
const tableScale = ref(1);
const tip = ref('');
const tipTop = ref('');
const tipLeft = ref('');
const showTip = ref(false);
const checkedValue = ref('长文本隐藏');
const curDataId = ref("datatable_tc");
const hisDataId = ref("datatable2_tc");

// 全屏弹出框相关状态
const isModalVisible = ref(false);
const isFullscreen = ref(true); // 默认全屏模式
const fullTableRef = ref<HTMLElement | null>(null);
const titleText = ref('规则比对全屏展示');

//动态tableId
const tableKeyNew = ref('');
const tableKeyOld = ref('');

// 计算表格实际内容高度
const calculateTableHeight = () => {
  nextTick(() => {
    const table = document.getElementById(curDataId.value);
    if (table) {
      const tableHeight = table.offsetHeight;
      // 根据表格内容高度计算容器高度，但不小于最小高度，不大于最大高度
      const calculatedHeight = Math.max(minHeight.value, Math.min(tableHeight + 50, maxHeight.value));
      tabHeight.value = calculatedHeight;
    }
  });
};

// 监听规则比对数据变化
watch(() => props.ruleCompareData, (newVal) => {
  if (newVal?.ruleHisVO1) {
    // 添加规则类型同步逻辑：如果一个有值另一个为空，则将空值替换为非空值
    if (newVal.ruleHisVO1.type && newVal?.ruleHisVO2 && !newVal.ruleHisVO2.type) {
      newVal.ruleHisVO2.type = newVal.ruleHisVO1.type;
    } else if (newVal?.ruleHisVO2 && newVal.ruleHisVO2.type && !newVal.ruleHisVO1.type) {
      newVal.ruleHisVO1.type = newVal.ruleHisVO2.type;
    }

    setTimeout(() => {
      tableKeyNew.value = Math.random().toString(16).slice(2);
      tableKeyOld.value = Math.random().toString(16).slice(2);
      const ruleHisVO1 = newVal.ruleHisVO1;
      const ruleHisVO2 = newVal.ruleHisVO2 || {}; // 确保即使没有历史版本也不会报错

      // 规则比对
      const timestamp1 = Date.now();
      if (isValidRuleType(ruleHisVO1?.type)) {
        const ruleUuid = ruleHisVO1?.ruleUuid || '';
        const oCusDataId = curDataId.value + "_" + tableKeyNew.value;
        const oHisDataId = hisDataId.value + "_" + tableKeyOld.value;
        $("#" + curDataId.value).attr("id", oCusDataId);
        $("#" + hisDataId.value).attr("id", oHisDataId);
        const curNewId = "datatable_" + (ruleHisVO1?.uuid || '') + "_" + timestamp1;
        const hisNewId = "datatable2_" + (ruleHisVO2?.uuid || '') + "_" + timestamp1;
        $("#" + oCusDataId).attr("id", curNewId);
        $("#" + oHisDataId).attr("id", hisNewId);

        // 判断是否为决策表
        if (isDecisionTable(ruleHisVO1?.type)) {
          // 优先使用tableContent，如果不存在则使用tableContentHis
          const currentTableContent = ruleHisVO1?.tableContent || ruleHisVO1?.tableContentHis || '';
          const historyTableContent = ruleHisVO2?.tableContent || ruleHisVO2?.tableContentHis || '';

          console.log('当前决策表内容类型:', typeof currentTableContent);
          console.log('是否有决策表内容:', !!currentTableContent);

          // 检查决策表内容的结构
          if (currentTableContent && typeof currentTableContent === 'string') {
            // 如果表格内容中缺少<table>标签，则添加完整的表格结构
            let processedContent = currentTableContent;
            if (!currentTableContent.trim().toLowerCase().startsWith('<table')) {
              processedContent = `<table>${currentTableContent}</table>`;
            }

            // 直接显示决策表内容
            $("#" + curNewId).html(processedContent);
          } else {
            console.error('决策表内容格式不正确:', currentTableContent);
            $("#" + curNewId).html('<tr><td>决策表数据格式不正确</td></tr>');
          }

          if (historyTableContent && typeof historyTableContent === 'string') {
            let processedHistoryContent = historyTableContent;
            if (!historyTableContent.trim().toLowerCase().startsWith('<table')) {
              processedHistoryContent = `<table>${historyTableContent}</table>`;
            }
            $("#" + hisNewId).html(processedHistoryContent);
          }

          // 直接对当前决策表内容进行美化
          if (currentTableContent) {
            try {
              // 确保表格内容包含了完整的表结构，包括表头
              setTimeout(() => {
                console.log('表格DOM元素数量:', $("#" + curNewId + " tr").length);

                // 检查表格的前三行是否存在，这些应该是表头
                const hasFirstRow = $("#" + curNewId + " tr").eq(0).length > 0;
                const hasSecondRow = $("#" + curNewId + " tr").eq(1).length > 0;
                const hasThirdRow = $("#" + curNewId + " tr").eq(2).length > 0;

                console.log('表头行检查:', { hasFirstRow, hasSecondRow, hasThirdRow });

                // 防止beautyTable函数因为缺少表头行而出错
                if (hasFirstRow && hasSecondRow && hasThirdRow) {
                  beautyTable(
                    curNewId,
                    (e: MouseEvent, tipText: string) => {
                      if (tipText) {
                        tipLeft.value = `${e.clientX - 2}px`;
                        tipTop.value = `${e.clientY - 2}px`;
                        tip.value = tipText;
                        showTip.value = true;
                      }
                    },
                    "audit"
                  );

                  // 修复后检查一下表格结构
                  console.log('美化后表格行数:', $("#" + curNewId + " tr").length);
                  console.log('美化后表格宽度:', $("#" + curNewId + " tr").eq(0).children().length);

                  // 检查表头是否渲染正确
                  const headerCells = $("#" + curNewId + " th").length;
                  console.log('表头单元格数:', headerCells);
                } else {
                  console.error('表格缺少必要的表头行，尝试添加默认表头行');

                  // 获取表格数据行的第一行，用于确定列数
                  const firstDataRow = $("#" + curNewId + " tr").eq(0);
                  const columnCount = firstDataRow && firstDataRow.length > 0 ?
                                     firstDataRow.children().length : 3;

                  // 创建默认表头
                  let headerHtml = '<tr>';
                  if (columnCount > 0) {
                    headerHtml += '<td style="border:none;background-color:#fff;">决策表</td>';
                  }
                  headerHtml += '</tr>';

                  // 添加两行表头，保证beautyTable函数能正常工作
                  let secondRowHtml = '<tr>';
                  let thirdRowHtml = '<tr>';

                  // 为每一列添加表头单元格
                  for (let i = 0; i < columnCount; i++) {
                    secondRowHtml += `<td>${i === 0 ? '序号' : '条件' + i}</td>`;
                    thirdRowHtml += `<td>${i === 0 ? '' : '条件' + i}</td>`;
                  }
                  secondRowHtml += '</tr>';
                  thirdRowHtml += '</tr>';

                  // 将现有表格内容提取出来
                  const existingRows = [];
                  $("#" + curNewId + " tr").each(function(index) {
                    existingRows.push($(this).prop('outerHTML'));
                  });

                  // 重建表格，添加表头和原有内容
                  let newTableHtml = `<table>${headerHtml}${secondRowHtml}${thirdRowHtml}${existingRows.join('')}</table>`;

                  // 应用新的表格HTML
                  $("#" + curNewId).html(newTableHtml);

                  // 再次尝试应用beautyTable
                  try {
                    beautyTable(
                      curNewId,
                      (e: MouseEvent, tipText: string) => {
                        if (tipText) {
                          tipLeft.value = `${e.clientX - 2}px`;
                          tipTop.value = `${e.clientY - 2}px`;
                          tip.value = tipText;
                          showTip.value = true;
                        }
                      },
                      "audit"
                    );
                    console.log('修复表头后应用beautyTable成功');
                  } catch (error) {
                    console.error('修复表头后应用beautyTable失败:', error);
                    // 如果仍然失败，确保至少基本格式是正确的
                    $("#" + curNewId + " td").css({
                      'border': '1px solid #ccc',
                      'padding': '4px 10px'
                    });
                  }
                }

                // 确保表格能够正确显示
                calculateTableHeight();
              }, 100);
            } catch (error) {
              console.error('美化当前决策表出错:', error);
            }
          }
        }

        const uuid1 = tableKeyNew.value || '';
        const uuid2 = tableKeyOld.value || '';
        sessionStorage.removeItem(`tableStr_${uuid1}${uuid2}`);

        // 只有当有两个版本时才进行比较
        if (ruleHisVO2 && Object.keys(ruleHisVO2).length > 0) {
          // 比较两个表格的差异
          try {
            const compar = differ(curNewId, hisNewId, "");
            sessionStorage.setItem(
              `tableStr_${uuid1}${uuid2}`,
              compar
            );

            // 美化表格
            beautyTable(
              curNewId,
              (e: MouseEvent, tipText: string) => {
                if (tipText) {
                  tipLeft.value = `${e.clientX - 2}px`;
                  tipTop.value = `${e.clientY - 2}px`;
                  tip.value = tipText;
                  showTip.value = true;
                }
              },
              "audit"
            );

            // 计算表格高度
            calculateTableHeight();
          } catch (error) {
            console.error('Error in differ or beautyTable:', error);
          }
        }

        $("#" + curNewId).attr("id", oCusDataId);
        $("#" + hisNewId).attr("id", oHisDataId);
      }
    });
  }
}, { deep: true, immediate: true });

// 添加组件挂载逻辑，确保在组件挂载后初始化决策表
onMounted(() => {
  nextTick(() => {
    if (props.ruleCompareData?.ruleHisVO1 &&
        isDecisionTable(props.ruleCompareData.ruleHisVO1.type)) {
      const tableContent = props.ruleCompareData.ruleHisVO1.tableContent ||
                          props.ruleCompareData.ruleHisVO1.tableContentHis;

      if (tableContent) {
        console.log('组件挂载时尝试渲染决策表');
        setTimeout(() => {
          try {
            // 检查表格的前三行是否存在
            const hasFirstRow = $("#" + curDataId.value + " tr").eq(0).length > 0;
            const hasSecondRow = $("#" + curDataId.value + " tr").eq(1).length > 0;
            const hasThirdRow = $("#" + curDataId.value + " tr").eq(2).length > 0;

            if (hasFirstRow && hasSecondRow && hasThirdRow) {
              beautyTable(
                curDataId.value,
                (e: MouseEvent, tipText: string) => {
                  if (tipText) {
                    tipLeft.value = `${e.clientX - 2}px`;
                    tipTop.value = `${e.clientY - 2}px`;
                    tip.value = tipText;
                    showTip.value = true;
                  }
                },
                "audit"
              );
            } else {
              console.error('组件挂载时表格缺少表头行，尝试处理表格内容');

              // 处理表格内容，确保包含必要的表头
              let processedContent = tableContent;
              if (!tableContent.trim().toLowerCase().startsWith('<table')) {
                processedContent = `<table>${tableContent}</table>`;
              }

              // 尝试将处理后的内容添加到表格中
              $("#" + curDataId.value).html(processedContent);

              // 获取列数并创建默认表头，与watch中的逻辑类似
              const firstDataRow = $("#" + curDataId.value + " tr").eq(0);
              const columnCount = firstDataRow && firstDataRow.length > 0 ?
                                 firstDataRow.children().length : 3;

              // 创建和添加默认表头，确保beautyTable能正常工作
              let headerHtml = '<tr><td style="border:none;background-color:#fff;">决策表</td></tr>';
              let secondRowHtml = '<tr>';
              let thirdRowHtml = '<tr>';

              for (let i = 0; i < columnCount; i++) {
                secondRowHtml += `<td>${i === 0 ? '序号' : '条件' + i}</td>`;
                thirdRowHtml += `<td>${i === 0 ? '' : '条件' + i}</td>`;
              }
              secondRowHtml += '</tr>';
              thirdRowHtml += '</tr>';

              // 将现有内容保存并重建表格
              const existingRows = [];
              $("#" + curDataId.value + " tr").each(function(index) {
                existingRows.push($(this).prop('outerHTML'));
              });

              $("#" + curDataId.value).html(`<table>${headerHtml}${secondRowHtml}${thirdRowHtml}${existingRows.join('')}</table>`);

              // 再次尝试应用beautyTable
              beautyTable(
                curDataId.value,
                (e: MouseEvent, tipText: string) => {
                  if (tipText) {
                    tipLeft.value = `${e.clientX - 2}px`;
                    tipTop.value = `${e.clientY - 2}px`;
                    tip.value = tipText;
                    showTip.value = true;
                  }
                },
                "audit"
              );
            }
          } catch (error) {
            console.error('挂载时渲染决策表出错:', error);
            // 确保即使出错也能提供基本显示
            $("#" + curDataId.value + " td").css({
              'border': '1px solid #ccc',
              'padding': '4px 10px'
            });
          }
        }, 300);
      }
    }
  });
});

// 切换长文本显示/隐藏
const switchChange = (strN: string, classN: string) => {
  setTimeout(() => {
    const tableObj = document.querySelectorAll(`.${classN} .datatable td`);
    for (let i = 0; i < tableObj.length; i++) {
      const element = tableObj[i] as HTMLElement;
      if (switchVal.value) {
        element.style.whiteSpace = "pre-wrap";
        element.style.wordWrap = "break-word";
        checkedValue.value = '长文本显示';
      } else {
        element.style.whiteSpace = "nowrap";
        checkedValue.value = '长文本隐藏';
      }
    }
  });
};

// 调整表格缩放比例
const sliderInput = (val: number | [number, number]) => {
  // 处理可能是数组的情况
  const value = Array.isArray(val) ? val[0] : val;
  tableScale.value = value / 100;
};

// 提示框相关方法
const tipLeave = () => {
  showTip.value = false;
  tip.value = "";
};

const tipHover = () => {
  showTip.value = true;
};

// 全屏显示函数 - 使用FullModel
const fullScreen = () => {
  titleText.value = '规则比对全屏展示';
  isModalVisible.value = true;
};

// 关闭弹出框
const handleModalCancel = () => {
  isModalVisible.value = false;
};

// 切换全屏/半屏模式
const onFullscreenToggle = () => {
  isFullscreen.value = !isFullscreen.value;
};
</script>

<style lang="scss" scoped>
.rule-compare-content {
  :deep(.ruletable){
    .ant-form-item {
      margin-bottom: 10px;
    }
    .datatable tr:first-child td,
    .datatable2 tr:first-child td {
      text-align: left !important;
    }
    .datatable.rule tr:first-child td,
    .datatable2.rule tr:first-child td {
      text-align: left !important;
      white-space: normal !important;
      word-wrap: break-word;
      word-break: break-all;
    }
    .fakeContainer {
      margin: 0 !important;
      margin-top: 20px;
      width: 100% !important;
      min-height: 200px; /* 设置最小高度 */
      max-height: calc(100vh - 200px)!important; /* 设置最大高度 */
      overflow: auto;
      /*border: 1px solid #c7d8ee;*/
      border-radius: 4px;
    }
    .datatable th{
      word-wrap: break-word;
      word-break: normal;
      border: 1px solid #ccc;
      background: #f2f2f2;
      color: #333;
      font-weight: bold;
      height: 30px;
      line-height: 24px;
      padding-left: 5px;
      padding-right: 5px;
      text-align: center;
      white-space: nowrap;
    }
    .datatable td{
      border: 1px solid #ccc;
      text-align: left;
      padding-top: 4px;
      padding-bottom: 4px;
      padding-left: 10px;
      padding-right: 10px;
      white-space: nowrap;
    }
    .datatable tr:hover,
    .datatable tr.altrow{
      background-color: #f2f2f2;
    }

    table {
      width: 100%;
    }
    th,
    td {
      max-width: 90px !important;
      text-overflow: ellipsis;
      overflow-x: hidden;
      text-align: center;
    }
    #tip-div {
      position: fixed;
      top: 0;
      left: 0;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #000;
      padding: 10px;
      z-index: 2000;
      font-size: 14px;
      line-height: 1.2;
      min-width: 10px;
      word-wrap: break-word;
      overflow: auto;
      max-height: 300px;
      max-width: 600px;
    }

    .fixed-table {
      overflow: auto;
      height: 100%;
      width: 100%;
    }

    .fixed-table td,
    .fixed-table th {
      border: 1px solid #c7d8ee;
      width: 150px;
      min-width: 150px;
    }

    .fixed-table th {
      position: sticky;
      top: 0;
    }
    .fixed-table th.th2 {
      top: 30px;
    }
    .fixed-table th.th3 {
      top: 60px;
    }
    .fixed-table th.xh {
      left: 0; /* 首列永远固定在左侧 */
      top: 0; /* 首行永远固定在头部  */
      z-index: 2;
      background-color: #fff;
      min-width: 40px;/* 序号列宽度缩小 */
    }
    .fixed-table td:first-child{
      position: sticky;
      left: 0; /* 首列永远固定在左侧 */
      min-width: 40px;/* 序号列宽度缩小 */
      background-color: white;
    }

    .fixed-table th.xh {
      z-index: 2; /*表头的首列要在上面*/
      background-color: #f2f2f2;
    }

    .fixed-table th > div {
      width: 100%;
      white-space: normal;
      word-wrap: break-word;
      word-break: break-all;
    }
    .slider-div-left {
      display: flex;
      align-items: center;
      .a-alert {
        margin-right: 20px;
      }
    }
    .slider-alert-div {
      display: flex;
      justify-content: space-between;
      .a-alert {
        width: auto;
      }
    }
    .slider-div {
      width: 170px;
      display: flex;
      float: right;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      .slider-block {
        width: 140px;
      }
    }
    .fakeContainer {
      margin: 0 !important;
      margin-top: 20px;
      width: 100% !important;
      overflow: auto;
    }

    .fullrow {
      background: #fff;
    }
  }
}

/* 全屏内容样式 */
:deep(.full-content) {
  height: 100%;
  overflow: auto;
  padding: 10px;
}

:deep(.compare-title) {
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}
</style>
