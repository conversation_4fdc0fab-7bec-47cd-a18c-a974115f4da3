<template>
  <span class="setValueCom">
    <!-- 左侧变量组件 -->
    <div class="setValueComLeft">
      <VariableCom
        :pos="pos + '_setValueCom_0'"
        :locked="locked"
        :isTrack="isTrack"
        :dataSource="leftOption"
        :hideFrontBtn="true"
        :hideEndBtn="true"
        :signValue="''"
        @onChange="onLeftValueChange"
      />
    </div>
    <!-- 等号操作符 -->
    <span class="operator eq">=</span>
    <!-- 右侧变量组件 -->
    <div class="setValueComRight">
      <VariableCom
        :pos="pos + '_setValueCom_1'"
        :locked="locked"
        :isTrack="isTrack"
        :dataSource="rightOption"
        :hideFrontBtn="false"
        :hideEndBtn="false"
        :signValue="''"
        :enumDictName="rightOption && rightOption.enumDictName"
        @onChange="onRightValueChange"
        @onCalSignChange="onCalSignChange"
      />
    </div>
  </span>
</template>

<script setup>
import VariableCom from "@/components/ruleEditCom/variable/variableCom.vue";
import * as util from "@/components/ruleEditCom/utils/util";

// 定义组件的 props
const props = defineProps({
  actionData: {
    type: Array,
    default: () => []
  },
  pos: {
    type: String
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTrack: {
    type: Boolean,
    default: false
  }
});

// 定义组件的 emits
const emit = defineEmits([
  'onChange'
]);

// 定义响应式数据
const leftOption = ref(props.actionData[0] || {});
const rightOption = ref(props.actionData[1] || {});

// 监听 actionData 的变化
watch(
  () => props.actionData,
  (newActionData) => {
    leftOption.value = newActionData[0] || {};
    rightOption.value = newActionData[1] || {};
  },
  { deep: true, immediate: true }
);


// 处理变化事件
const onChange = (pos, newActionItem) => {
  emit('onChange', pos, newActionItem);
};

// 获取最终值
const getFinalValue = (variable = {}) => {
  const { value = [], next } = variable;
  if (next) {
    return getFinalValue(next);
  }
  return value[value.length - 1];
};

// 处理左侧值变化
const onLeftValueChange = (pos, newVariable, conditionValueType) => {
  const { dictName } = newVariable;
  const domain = util.getRealDomain(newVariable);
  const _initRightVariable = {
    value: "",
    valueType: conditionValueType,
    variableType: "constant",
    enumDictName: domain || dictName || null
  };
  onChange(pos, [newVariable, _initRightVariable]);
};

// 处理右侧值变化
const onRightValueChange = (pos, variable) => {
  const { actionData } = props;
  const newVariable = variable;
  const [leftOption] = actionData;
  onChange(pos, [leftOption, newVariable]);
};

// 处理计算符号变化
const onCalSignChange = (childPos, sign, newParam) => {
  const { actionData, pos } = props;
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const targetOption = actionData[index];
  const valueType = util.getRealValueType(targetOption);

  if (sign === "delete" || !newParam) {
    return;
  }

  const _newVariable = {
    valueType,
    variableType: "expression",
    expressionTreeData: {
      type: "expression",
      symbols: [sign],
      params: [{ type: "variable", data: targetOption }, newParam]
    }
  };

  if (index === 0) {
    onLeftValueChange(pos, _newVariable, valueType);
  } else {
    onRightValueChange(pos, _newVariable);
  }
};
</script>

<style scoped>
.operator.eq {
  margin: 0 10px;
}
</style>
