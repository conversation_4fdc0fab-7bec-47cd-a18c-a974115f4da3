<template>
    <div id="version_info_release_detail" class="version-info-container">
        <a-tabs v-model:activeKey="activeTab" @change="tabsClick">
            <a-tab-pane tab="当前版本信息" key="nowVersion">
                <div>
                    <RuleInfoDisplay
                            :data="ruleForm1"
                            :config="currentVersionConfig"
                            :showRuleContent="false"
                    />
                    <!-- 添加规则内容对比部分 -->
                    <a-row type="flex" class="row-bg fullrow fulltab" style="margin-left: 5px">
                        <a-col class="col-detail" :span="24">
                            <RuleCompareContent
                                    :rule-compare-data="{
                                                ruleHisVO1: ruleForm1,
                                                ruleHisVO2: ruleForm2
                                            }"
                            />
                        </a-col>
                    </a-row>
                </div>

            </a-tab-pane>

            <a-tab-pane tab="历史版本信息" key="hisVersion">
                <div>
                    <RuleInfoDisplay
                        :data="ruleForm2"
                        :config="historyVersionConfig"
                        :showRuleContent="true"
                    />
                </div>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script setup>
    import { rules } from "@/api/task";
    import { differ, beautyTable } from "@/api/table_diff";
    import $ from 'jquery';

const ruleForm1 = ref({});
const ruleForm2 = ref({});
const salienceColor = ref(false);
const ruleNameColor = ref(false);
const curValidColor = ref(false);
const hisValidColor = ref(false);
    const tip = ref('');
    const tipTop = ref('');
    const tipLeft = ref('');
    const showTip = ref(false);
    const activeTab = ref('nowVersion');

// 当前版本配置
const currentVersionConfig = [
    { label: '规则名称', field: 'ruleName', class: computed(() => ruleNameColor.value ? 'compareColor' : '') },
    { label: '规则路径', field: 'packageNameAll', format: (value, data) => data.packageNameAll + data.ruleName },
    { label: '规则版本', field: 'curEdition' },
    { label: '有效状态', field: 'validStatus', class: computed(() => curValidColor.value ? 'compareColor' : '') },
    { label: '创建时间', field: 'createdTimeStr' },
    { label: '创建人', field: 'createdId' },
    { label: '修改时间', field: 'lastModifiedTimeStr' },
    { label: '修改人', field: 'modifiedId' },
    { label: '优先级', field: 'salience', class: computed(() => salienceColor.value ? 'compareColor' : ''), span: 24 },
    { label: '规则描述', field: 'descs', span: 24 },
    { label: '规则流信息', field: 'ruleFlowInfo', class: 'regulation', span: 24 }
];

// 历史版本配置
const historyVersionConfig = [
    { label: '规则名称', field: 'ruleName' },
    { label: '规则路径', field: 'packageNameAll', format: (value, data) => data.packageNameAll + data.ruleName },
    { label: '规则版本', field: 'edition' },
    { label: '有效状态', field: 'validStatus', class: computed(() => hisValidColor.value ? 'compareColor' : '') },
    { label: '创建时间', field: 'createdTimeStr' },
    { label: '创建人', field: 'createdId' },
    { label: '修改时间', field: 'lastModifiedTimeStr' },
    { label: '修改人', field: 'modifiedId' },
    { label: '规则描述', field: 'descs', span: 24 },
    { label: '规则流信息', field: 'ruleFlowInfo', class: 'regulation', span: 24 }
];

    // 组件挂载时执行的逻辑
    onMounted(() => {
        getInfo();
    });

    const props = defineProps({
        obj: {
            type: Object,
            default: () => {
                return {};
            },
        },
    });

    const tabsClick = (value) => {
        if(value==='hisVersion'){
            ruleForm2.value.tableContent = ruleForm2.value.tableContentHis
            sessionStorage.removeItem(`tableStr_${ruleForm2.uuid}`);
            sessionStorage.setItem(`tableStr_${ruleForm2.value.uuid}`, ruleForm2.value.tableContent);
        }else{
        }
    };

    // 监听 ruleForm1 变化
    watch(
        () => ruleForm1,
        () => {
            if (
                ruleForm1.value.uuid != undefined &&
                (ruleForm1.value.type == "1" || ruleForm1.value.type == "2")
            ) {
                setTimeout(() => {
                    //console.log(ruleForm1.value);
                    //console.log(ruleForm2.value)
                    var curId = "releaseDeatils" + ruleForm1.value.uuid;
                    var hisId = "releaseDeatils" + "datatable2";
                    if (ruleForm2.value.uuid != null) {
                        hisId = "releaseDeatils" + ruleForm2.value.uuid;
                    }
                    $("#datatable").attr("id", curId);
                    $("#datatable2").attr("id", hisId);
                    if (ruleForm1.value.type == "2") {
                        $("#" + curId).html(ruleForm1.value.tableContent);
                        $("#" + hisId).html(ruleForm2.value.tableContentHis);
                    }
                    sessionStorage.removeItem(`tableStr_${ruleForm1.value.uuid}`);

                    //this.tableUuid = ruleForm1.value.uuid;
                    let compar = differ(curId, hisId);
                    sessionStorage.setItem(`tableStr_${ruleForm1.value.uuid}`, compar);
                    beautyTable(
                        curId,
                        (e, tip1) => {
                            if (tip1) {
                                tipLeft.value = `${e.clientX - 2}px`;
                                tipTop.value = `${e.clientY - 2}px`;
                                tip.value = tip1;
                                showTip.value = true;
                            }
                        },
                        "audit"
                    );
                    beautyTable(hisId,
                        (e, tip1) => {
                            if (tip1) {
                                tipLeft.value = `${e.clientX - 2}px`;
                                tipTop.value = `${e.clientY - 2}px`;
                                tip.value = tip1;
                                showTip.value = true;
                            }
                        },
                        "audit");
                    var ruleName = ruleForm1.value.ruleName;
                    var ruleNameHis = ruleForm2.value.ruleName;
                    if (
                        ruleNameHis != "" &&
                        ruleNameHis != null &&
                        ruleNameHis != ruleName
                    ) {
                        ruleNameColor.value = true;
                    }
                    var salience = ruleForm1.value.salience;
                    var salienceHis = ruleForm2.value.salience;
                    if (
                        salienceHis != "" &&
                        salienceHis != null &&
                        salienceHis != salience
                    ) {
                        salienceColor.value = true;
                    }
                    var valid = ruleForm1.value.validStatus;
                    var validHis = ruleForm2.value.validStatus;
                    if (valid == "无效") {
                        curValidColor.value = true;
                    }
                    if (validHis == "无效") {
                        hisValidColor.value = true;
                    }
                    if (validHis != null && validHis != "" && validHis != valid) {
                        curValidColor.value = true;
                    }
                });
            }
        },
        { deep: true }
    );

    const getInfo = () => {
        rules({
            demandUuid: props.obj.demandUuid,
            originalEdition: props.obj.originalEdition,
            ruleEdition: props.obj.ruleEdition,
            ruleUuid: props.obj.id,
        }).then((res) => {
            if (res.code == 20000) {
                res.data.ruleVO ? (ruleForm1.value = res.data.ruleVO) : null;
                res.data.ruleHisVO ? (ruleForm2.value = res.data.ruleHisVO) : null;
                res.data.ruleFlowInfo
                    ? (ruleForm1.value.ruleFlowInfo = res.data.ruleFlowInfo,ruleForm2.value.ruleFlowInfo = res.data.ruleFlowInfo)
                    : null;
                res.data.curEdition ? (ruleForm1.value.curEdition = res.data.curEdition) : null;
                if (ruleForm1.value != null) {
                    ruleForm1.value.curRulePath =
                        ruleForm1.value.packageNameAll + ruleForm1.value.ruleName;
                }
                if (ruleForm2.value != null) {
                    ruleForm1.value.hisRulePath =
                        (ruleForm2.value.packageNameAll == null
                            ? ""
                            : ruleForm2.value.packageNameAll) +
                        (ruleForm2.value.ruleName == null ? "" : ruleForm2.value.ruleName);
                }

            }
        });
    }


</script>

<style lang="scss" scoped>
.version-info-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;

    :deep(.ant-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    :deep(.ant-tabs-content) {
        flex: 1;
        overflow: auto;
    }

    :deep(.ant-tabs-tabpane) {
        height: 100%;
        overflow: auto;
    }

    :deep(.ant-tabs-content-holder) {
        overflow: auto;
        flex: 1;
    }
}
</style>
