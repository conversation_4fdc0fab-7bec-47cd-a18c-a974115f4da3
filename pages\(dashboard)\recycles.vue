<!-- 回收站 -->

<script setup lang="tsx">
import { getRecyclesList, restoreRecycle, completelyDeleteRecycle } from '@/api/dashboardApi'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { createVNode, h } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { recordAccess } from '@/api/dashboardApi'
import { getRuleByUuid } from "@/api/rule_base";
import type { TableProps } from 'ant-design-vue';
import Pagination from '@/components/Pagination.vue';
dayjs.extend(relativeTime)

definePageMeta({
    title: '回收站'
})

const message = inject('message') as any
const modal = inject('modal') as any

// 格式化时间为相对时间
const formatRelativeTime = (time: string) => {
    return dayjs(time).fromNow()
}

interface RecycleItem {
    uuid: string;
    ruleName: string;
    packageNameAll: string;
    createdTime: string;
    ruleUuid?: string; // Optional property for rule UUID if different from the record UUID
}

// 表格列定义
const columns = [
    {
        title: '名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        align: 'left',
        ellipsis: true,
        width: 180,
        customRender: ({ text, record }: { text: string, record: RecycleItem }) => {
            return {
                children: h(
                    'a',
                    {
                        onClick: () => handleDetail(record)
                    },
                    text
                )
            }
        }
    },
    {
        title: '归属',
        dataIndex: 'packageNameAll',
        key: 'packageNameAll',
        width: 250,
        align: 'left',
        ellipsis: true,
    },
    {
        title: '删除时间',
        dataIndex: 'createdTime',
        key: 'createdTime',
        width: 100,
        align: 'left',
        ellipsis: true,
    },
    {
        title: '操作',
        key: 'action',
        width: 120,
        align: 'center'
    }
]

const dataSource = ref<RecycleItem[]>([])
const loading = ref(false)

// 分页配置
const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    showSizeChanger: true,
    isLoading: false,
    showTotal: (total: number) => `共 ${total} 条数据`,
});

// 获取回收站列表数据
const fetchRecyclesList = async () => {
    loading.value = true
    pagination.value.isLoading = true
    try {
        // 使用分页参数调用更新后的API
        const res = await getRecyclesList({
            page: pagination.value.page,
            several: pagination.value.limit
            // 如果API支持其他查询参数（如ruleName），可以在这里添加
        })
        dataSource.value = res.data.data || []
        // 获取总条数
        pagination.value.total = res.data.totalCount || (res.data.data ? res.data.data.length : 0)
    } catch (error) {
        console.error('获取回收站列表失败:', error)
    } finally {
        loading.value = false
        pagination.value.isLoading = false
    }
}

// 处理分页变化
const handlePageChange = (cur: number, pageSize : number) => {
    pagination.value.page = cur
    pagination.value.limit = pageSize
    fetchRecyclesList()
}

onMounted(() => {
    fetchRecyclesList();
})

// 处理还原
const handleRestore = async (record: RecycleItem) => {
    try {
        await restoreRecycle({ uuid: record.uuid })
        message.success('还原成功')
        // 从列表中移除该条记录
        dataSource.value = dataSource.value.filter(item => item.uuid !== record.uuid)
        // 刷新当前页数据
        fetchRecyclesList()
    } catch (error) {
        message.error('还原失败')
        console.error('还原失败:', error)
    }
}
// 全局多选表单校验
const checkSelectedRow = (single) => {
    if (checkedUuids.value.length) {
        if (single) {
            if (checkedUuids.value.length > 1) {
                message.error("只能选择一条记录");
                return false;
            } else {

                return true;
            }
        } else {
            return true;
        }
    } else {
        message.error("请选择要操作的记录");
        return false;
    }
}
// 处理彻底删除
const handleDelete = (record) => {
    let _uuids = [];
    let _ruleName = [];
    let deleteFlag = false;
    if(record){
        _uuids.push(record.uuid)
        _ruleName.push(record.ruleName);
        deleteFlag = true;
    }else {
        if(checkSelectedRow(false)){
            deleteFlag = true;
            checkedUuids.value.filter((i) => {
                _uuids.push(i.uuid);
                _ruleName.push(i.ruleName);
            });
        }
    }

    if(deleteFlag){
        modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: `确定要彻底删除【${_ruleName.toString().length>50?_ruleName.toString().substring(0,50)+'......':_ruleName.toString()}】吗？此操作不可恢复！`,
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            async onOk() {
                try {
                    await completelyDeleteRecycle({ uuid: _uuids.toString() })
                    message.success('删除成功')
                    // 从列表中移除该条记录
                    fetchRecyclesList();
                } catch (error) {
                    message.error('删除失败')
                    console.error('删除失败:', error)
                }
            }
        })
    }
}


const checkedUuids = ref([]);
const rowSelection: TableProps['rowSelection'] = {
    onChange: (selectedRowKeys: string[], selectedRows: any) => {
        checkedUuids.value = [];
        selectedRows.forEach((item) => {
            checkedUuids.value.push({ 'uuid': item.uuid,'ruleName':item.ruleName })
        });
    }
};
interface RuleInfo {
    uuid: string
    ruleName: string
    packageNameAll: string
    status: string
    lockId: string
    lastModifiedTimeStr: string
    modifiedId: string
    createdTimeStr: string
    createdId: string
    ruleNumber: string
    descs: string
    textDtl: string
    ifCollect: boolean
}

const ruleInfoData = ref<RuleInfo>({} as RuleInfo)
//查看规则详情
const handleDetail = (record: RecycleItem) => {
    // 确保 record 包含 ruleUuid，如果没有，使用 uuid
    const ruleUuid = record.ruleUuid || record.uuid;
    
    getRuleByUuid({
        uuids: ruleUuid,
    }).then((res: any) => {
        ruleInfoData.value = res.data
        isFullModalVisible.value = true;
        // 异步记录访问历史
        recordAccess({ ruleUuid }).catch(console.error)
    });
}
//全屏切换
const isFullscreen = ref(false)
const isFullModalVisible = ref(false)
const titleText = ref('规则详情')
const onFullscreenToggle = () => {
    isFullscreen.value = !isFullscreen.value
}
const handleFullCancel = () => {
    isFullModalVisible.value = false;
}

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);

</script>

<template>
    <div class="Recycles-module_wrapper_jPSDM Recycles-module_mytable_1fj45" style="margin-bottom: 0">
        <a-card :bordered="false">
            <template #title>
                <div style="display: flex; justify-content: space-between; align-items: center">
                    <span>回收站</span>
                    <a-button @click="handleDelete('')" type="primary">批量彻底删除</a-button>
                </div>
            </template>
            <div style="margin-top: 15px;" ref="basePoint">
                <a-table
                        :columns="columns"
                        :data-source="dataSource"
                        :loading="loading"
                        :pagination="false"
                        row-key="uuid"
                        :rowSelection="rowSelection"
                        :scroll="{ y: scrollY-50 }"
                        size="small"
                >
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'packageNameAll'">
                         <span style="color: #8c8c8c;">
                             <RulePath
                                     :path="record.packageNameAll"
                             />
                        </span>
                        </template>
                        <template v-if="column.key === 'createdTime'">
                         <span class="ant-table-cell" :title="formatRelativeTime(record.createdTime)" style="color: #8c8c8c;">
                            {{ formatRelativeTime(record.createdTime) }}
                         </span>
                        </template>
                        <template v-if="column.key === 'action'">
                            <div class="table-actions">
                                <a-button type="link" @click="handleRestore(record as RecycleItem)">还原</a-button>
                                <a-button type="link" @click="handleDelete(record as RecycleItem)">彻底删除</a-button>
                            </div>
                        </template>
                    </template>
                </a-table>

            </div>
            <!-- 添加分页组件 -->
            <div style="margin-top: 50px;">
                <Pagination
                        :paginations="pagination"
                        @change="handlePageChange"
                        :loading="loading"
                        :scrollY="scrollY-130"
                />
            </div>
        </a-card>
        <!-- 详情对话框 -->
        <FullModel :isFullscreen="isFullscreen" :onFullscreenToggle="onFullscreenToggle" :titleText="titleText" :handleCancel="handleFullCancel" :isModalVisible="isFullModalVisible">
            <template #default>
                <div :style="{maxHeight: isFullscreen?'90vh':'60vh'}">
                    <!-- 规则内容 -->
                    <RuleDetail :ruleInfoData="ruleInfoData"></RuleDetail>
                </div>
            </template>
        </FullModel>
    </div>
</template>
