/* eslint-disable no-use-before-define */
import store from '@/store'
import {getRuleByUuid} from '@/api/rule_base'
// 更新某一节点及其子节点的indent值；
export const updateIndent = (parentNode, num) => {
  // const { ruleCondition, children } = parentNode
  const {
    children
  } = parentNode;
  parentNode.indent = parentNode.indent + num;
  if (children) {
    children.forEach(item => {
      updateIndent(item, num);
    });
  }
};

// 更新某一节点及其子节点的showLayer值；
export const updateShowLayers = (targetLayer, data, num = 1, aChildConditionId, dataSource) => {
  const {
    ruleCondition,
    children
  } = data;
  if (ruleCondition) {

    if (ruleCondition.layer > targetLayer) {

      let hasId = aChildConditionId.filter(item => {
        return ruleCondition.conditionId === item
      })

      if (num < 0) {
        if (hasId.length > 0) {
          ruleCondition.showLayer = ruleCondition.showLayer + num;
        } else {
          const ruleCon = findTargetNodeInfoById(dataSource, targetLayer).ruleCondition;
          const ruleConBefore = findTargetNodeInfoById(dataSource, ruleCondition.conditionId - 1).ruleCondition;
          // if (ruleCon.showLayer < targetLayer) {
          //   ruleCondition.showLayer = ruleCon.showLayer - num;
          // } else {
          if (aChildConditionId &&aChildConditionId.length>0 && aChildConditionId[aChildConditionId.length-1] == Number(ruleCondition.layer-1)) {
            ruleCondition.showLayer = ruleCon.showLayer + 1;
          } else {
            ruleCondition.showLayer = ruleConBefore.showLayer + 1;

          }
          // }
        }
      } else {
        if (hasId.length > 0) {
          ruleCondition.showLayer = ruleCondition.showLayer + num;
        } else {
          const ruleCon = findTargetNodeInfoById(dataSource, ruleCondition.conditionId - 1).ruleCondition;
          if (ruleCon.showLayer < targetLayer) {
            ruleCondition.showLayer = ruleCon.showLayer - num;
          } else {
            ruleCondition.showLayer = ruleCon.showLayer + 1;
          }
        }
      }
    }
  } else {
    children &&
      children.forEach(item => {
        updateShowLayers(targetLayer, item, num, aChildConditionId, dataSource);
      });
  }
};

// 更新某一节点及其子节点的layer值；
export const updateLayers = (targetLayer, data, num = 1) => {
  const {
    ruleCondition,
    children
  } = data;
  if (ruleCondition) {
    if (ruleCondition.layer > targetLayer) {
      ruleCondition.layer = ruleCondition.layer + num;
      ruleCondition.showLayer = ruleCondition.showLayer + num;
    }
  } else {
    children &&
      children.forEach(item => {
        updateLayers(targetLayer, item, num);
      });
  }
};

// 根据conditionId寻找节点相关信息
export const findTargetNodeInfoById = (
  data,
  conditionId,
  arrIndex,
  parentNode,
  grandparentNode,
  parentArrIndex
) => {
  const {
    ruleCondition,
    children
  } = data;
  if (ruleCondition && ruleCondition.conditionId + "" === conditionId + "") {
    return {
      targetNode: data,
      parentNode,
      arrIndex,
      ruleCondition,
      grandparentNode,
      parentArrIndex,
      layer: ruleCondition.layer
    };
  }
  if(children){
    parentArrIndex=arrIndex
  }
  let obj;
  for (const i in children) {
    obj = findTargetNodeInfoById(children[i], conditionId, Number(i), data,parentNode,parentArrIndex);
    if (obj) {
      return obj;
    }
  }
};
// 寻找子节点包含的conditionId
export const findTargetNodeChildId = (
  data,
  res
) => {
  const {
    ruleCondition,
    children
  } = data;
  if (ruleCondition && ruleCondition.conditionId) {
    res.push(ruleCondition.conditionId);
  }
  for (const i in children) {
    findTargetNodeChildId(children[i], res);
  }
  return res
};


// 根据path返回数据结构
export function findDataByPath(data, pathArr) {
  const len = pathArr.length;
  const item = pathArr.splice(0, 1)[0];
  if (len === 1) {
    return {
      node: data[item],
      key: item
    };
  }
  return findDataByPath(data[item], pathArr);
}

// 更新可折叠节点的折叠信息
export const updateNodeFold = (dataSource, conditionId, layer) => {
  const {
    parentNode
  } = findTargetNodeInfoById(dataSource, conditionId);
  const len = countRulesNum(parentNode);
  let aChildConditionId = findTargetNodeChildId(parentNode, [])
  if (len / 1 === 1) {
    return;
  }
  parentNode.fold = !parentNode.fold;
  const num = parentNode.fold ? 1 - len : len - 1;
  updateShowLayers(layer, dataSource, num, aChildConditionId, dataSource);
};

// 寻找逻辑符号所在的节点
export const findLogicNode = (data, tartetIndent, targetLayer) => {
  // const { ruleCondition, children, logicalSymbol, indent } = data
  const {
    children,
    logicalSymbol,
    indent
  } = data;
  if (indent === Number(tartetIndent) && logicalSymbol) {
    const {
      layer
    } = getFirstRule(data);
    if (layer === Number(targetLayer)) {
      return data;
    }
  } else if (children) {
    let obj;
    for (const i in children) {
      obj = findLogicNode(children[i], tartetIndent, targetLayer);
      if (obj) return obj;
    }
  }
};

// 找到并返回某节点下的第一条规则
export const getFirstRule = data => {
  if (!data) return;
  const {
    ruleCondition,
    children = []
  } = data;
  if (ruleCondition) {
    return ruleCondition;
  }
  return getFirstRule(children[0]);
};
// 找到并返回某节点下的最后一条规则
export const getLastNode = data => {
  const {
    ruleCondition,
    children
  } = data;
  if (!children) {
    return ruleCondition;
  }
  const len = children.length;
  return getLastNode(children[len - 1]);
};

// 计算某节点下可见节点（未折叠）的数量
export const countDisplayedRules = (data, obj = {
  num: 0
}) => {
  if (data) {
    const {
      children,
      ruleCondition,
      fold
    } = data;
    if (ruleCondition || fold) {
      obj.num++;
    } else if(children) {
      children.map(item => {
        countRules(item, obj);
      });
    }
  }
  return obj.num;
};

export const countRules = (data, obj) => {
  const {
    children,
    ruleCondition
  } = data;
  if (ruleCondition) {
    obj.num++;
  } else if(children) {
    children.map(item => {
      countRules(item, obj);
    });
  }
};

// 计算某节点下的所有规则的数目
export const countRulesNum = data => {
  const obj = {
    num: 0
  };
  countRules(data, obj);
  return obj.num;
};

// 获取某个节点下的所有可见的逻辑运算符的相关信息
export const getLogicData = (conditionData, arr = []) => {
  // const { ruleCondition, children, fold, logicalSymbol } = data
  const {
    children,
    fold,
    logicalSymbol
  } = conditionData;
  const blockIndent = conditionData?.indent;
  const blockLayer = getFirstRule(conditionData)?.layer;
  const blockShowLayer = getFirstRule(conditionData)?.showLayer;
  if (logicalSymbol) {
    arr.push({
      logicalSymbol,
      blockIndent,
      blockShowLayer,
      blockLayer
    });
  }
  if (children) {
    if (fold) {
      // 如果折叠，只渲染数组中的第一个元素
      getLogicData(children[0], arr);
    } else if(children) {
      children.map(item => {
        getLogicData(item, arr);
      });
    }
  }
  return arr;
};

// 寻找某数据节点的上一级对象
const findUpperLevelData = (targetObj, parentObj) => {
  const {
    children
  } = parentObj;
  let o;
  for (let i = 0, len = children.length; i < len; i++) {
    if (children[i] === targetObj) {
      o = parentObj;
    }
    if (o) {
      return o;
    }
  }
  for (let i = 0, len = children.length; i < len; i++) {
    if (children[i].children) {
      o = findUpperLevelData(targetObj, children[i]);
    }
    if (o) {
      return o;
    }
  }
};

// 将 dataEntry 数据转化为前端需要的数据
function translateDataEntry(name, dataEntryList = [], values = []) {
  // dataEntry 类型，后台只保存最里层的value(name)值,但前端界面需要完整的 values
  const copyDataEntryList = dataEntryList || [];
  for (const item of copyDataEntryList) {
    const {
      value,
      children
    } = item;
    values.push(value);
    if (value === name) {
      return values;
    }
    if (children && children.length > 0) {
      const oldLength = values.length;
      const newValus = translateDataEntry(name, children, values);
      const newLength = newValus.length;
      if (oldLength < newLength) {
        return newValus;
      }
    }
    values.pop();
  }
  return values;
}

// // 将 variable 数据转化为前端需要的数据
function translateVar(variable, newVariable = {},ruleUuid,listString,isTable) {
  const {
    valueType,
    variableType,
    value,
    name,
    methodName,
    methodParams,
    next,
    expressionValue,
    expressionParams,
    enumDictName,
    paramsList,
    singleParams,
    display = true
  } = variable;
  newVariable.variableType = variableType;
  newVariable.valueType = valueType;
  newVariable.display = display;
  if (variableType === "constant"||listString) {
    if(listString && !isTable){
      if(singleParams){
        newVariable.singleParams=singleParams
      }else{
        if(enumDictName){
          newVariable.singleParams=[{
            valueType: 'String',
            variableType,
            value,
            enumDictName
          }]
        }else{
          newVariable.singleParams=[{
            valueType: 'String',
            variableType,
            value
          }]
        }
      }
      newVariable.valueType = 'List<String>';
      newVariable.variableType = 'method';
      if (enumDictName) {
        newVariable.enumDictName = enumDictName;
      }
    }else{
      newVariable.value = value;
      if (enumDictName) {
        newVariable.enumDictName = enumDictName;
      }
    }
  } else if (variableType === "dataEntry") {

    const dataEntryList = store.getters.listMap[ruleUuid].initModelData.dataEntryList;
    newVariable.value = translateDataEntry(name, dataEntryList);
    if (next) {
      newVariable.next = translateVar(next,{},ruleUuid,null,isTable);
    }
  } else if (variableType === "field") {
    newVariable.value = [name];
    if (next) {
      newVariable.next = translateVar(next,{},ruleUuid,null,isTable);
    }
  } else if (variableType === "method") {
    newVariable.value = [methodName];
    newVariable.paramsList = paramsList;
    if (methodParams&&methodParams.length>0) {
      // 字符串包含字符串列表
      methodParams.map((paramsItem,paramsI)=>{
        if(paramsItem.valueType==='List<String>'){
          methodParams[paramsI]=translateVar(paramsItem,{},ruleUuid,true,isTable)
        }else{
          methodParams[paramsI]=translateVar(paramsItem,{},ruleUuid,null,isTable)
        }
      })
      newVariable.methodParams=methodParams
      // if(valueType==='List<String>'){

      //   newVariable.methodParams = methodParams.map(item => {
      //     return translateVar(item,{},ruleUuid,true,isTable);
      //   });
      // }else{
      //   newVariable.methodParams = methodParams.map(item => {
      //     return translateVar(item,{},ruleUuid,null,isTable);
      //   });
      // }
    }
    if (next) {
      newVariable.next = translateVar(next,{},ruleUuid,null,isTable);
    }
  } else if (variableType === "expression") {
    const params = expressionParams.map(item => {
      return translateVar(item,{},ruleUuid,null,isTable);
    });
    newVariable.expressionTreeData = getExpressionTree(expressionValue, params);
  }
  return newVariable;
}

// 将操作符转换为前端需要的数据
function translateOperator(comparator, newComparator = {},ruleUuid) {
  const {
    operatorParams,
    operatorName,
    enumDictName,
    paramQuantity
  } = comparator;
  newComparator.value = [operatorName];
  newComparator.paramQuantity = paramQuantity;
  if (operatorParams) {
    newComparator.operatorParams = operatorParams.map(item => {
      return translateVar(item,{},ruleUuid);
    });
  }

  if (enumDictName) {
    newComparator.enumDictName = enumDictName;
  }

  return newComparator;
}

/**
 * __context.owener: 指向 condition 对象，或 action 对象；
 * __context.isParam: 该变量是否是参数变量，可取值：methodParam | expressionParam | operatorPram | actionSetLeft | actionSetRight | actionFuncParam
 * __context.isRootVar: 判断该变量是否是独立的变量，如：左值变量、参数变量
 * __context.prever: 与 next 相对，指向上一层对象；
 */
export class ContextGenerate {
  constructor(data, options = {}) {
    const {
      valueType,
      variableType,
      value,
      methodParams,
      next,
      expressionTreeData,
      enumDictName,
      dictName,
      domain,
      operatorParams,
      operatorName,
      paramQuantity,
      paramsList,
      display,
      singleParams
    } = data;
    this.__context = {
      ...options
    };
    this.valueType = valueType;
    this.display = display;
    if (variableType) {
      this.variableType = variableType;
    }
    if (value || value === 0) {
      this.value = value;
    }
    if (operatorName) {
      this.operatorName = operatorName;
    }
    if (paramQuantity !== null && paramQuantity !== undefined) {
      this.paramQuantity = paramQuantity;
    }
    if (enumDictName) {
      this.enumDictName = enumDictName;
    }
    if (dictName) {
      this.dictName = dictName;
    }
    if (domain) {
      this.domain = domain;
    }
    if (paramsList) {
      this.paramsList = paramsList;
    }
    if (singleParams) {
      this.singleParams = singleParams;
    }
    if (next) {
      this.next = new ContextGenerate(next, {
        owner: this._getOwner(),
        isRootVar: false,
        prever: this
      });
    }
    if (methodParams) {
      this.methodParams = methodParams.map(item => {
        return new ContextGenerate(item, {
          owner: this._getOwner(),
          isRootVar: true,
          isParam: "methodParam"
        });
      });
    }
    if (expressionTreeData) {
      this.expressionTreeData = this._deriveExpressionTreeData(
        expressionTreeData
      );
    }
    if (operatorParams) {
      this.operatorParams = operatorParams.map(item => {
        return new ContextGenerate(item, {
          owner: this._getOwner(),
          isParam: "operatorParam",
          isRootVar: true
        });
      });
    }
  }

  _getOwner = () => {
    return this.__context.owner;
  };

  _getStartVar = () => {
    if (!this.prever) {
      return this;
    }
    return this.prever._getStartVar();
  };

  _getEndVar = () => {
    return getEndVar(this);
  };

  // 当变量结构中存在 next 结构时候， 最终的 valueType 由最内层的 next 决定；
  _getRealValueType = () => {
    return getRealValueType(this);
  };

  _getExpressionStartVar = () => {
    return getExpressionStartVar(this);
  };

  // 当变量结构中存在 next 结构时候， 最终的 domain 由最内层的 next 决定；
  _getRealDomain = () => {
    return getRealDomain(this);
  };

  _updateContext = options => {
    const {
      __context
    } = this;
    const newContext = {
      ...__context,
      ...options
    };
    this.__context = newContext;
  };

  _deriveExpressionTreeData = (expressTreeData, options) => {
    const {
      params,
      symbols,
      type,
      data
    } = expressTreeData;
    const _options = {
      owner: this._getOwner(),
      isRootVar: true,
      isParam: "expressionParam"
    };
    const newExpression = {
      type,
      __context: options || _options
    };
    if (data) {
      newExpression.data = this._derive(data, _options);
    }
    if (symbols) {
      newExpression.symbols = [...symbols];
    }
    if (params && params.length > 0) {
      newExpression.params = params.map(item => {
        return this._deriveExpressionTreeData(item, _options);
      });
    }
    return newExpression;
  };

  _derive = (varData, options) => {
    const _option = options || {
      owner: this._getOwner(),
      isRootVar: this.__context.isRootVar,
      isParam: this.__context.isParam,
      prever: this.__context.prever
    };
    return new ContextGenerate(varData, _option);
  };
}

export function getEndVar(variableData) {
  if (!variableData.next) {
    return variableData;
  }
  return getEndVar(variableData.next);
}
export function getRealDomain(variableData) {
  const endVar = getEndVar(variableData);
  return endVar.domain;
}
export function getExpressionStartVar(variableData) {
  const {
    expressionTreeData
  } = variableData;
  const {
    params
  } = expressionTreeData;
  const getFirstParam = _params => {
    const {
      params: childParams
    } = _params[0];
    if (childParams && childParams.length > 0) {
      return getFirstParam(childParams);
    }
    return _params[0].data;
  };
  return getFirstParam(params);
}
export function getRealValueType(variableData) {
  const {
    variableType
  } = variableData;
  if (variableType !== "expression") {
    const endVar = getEndVar(variableData);
    return endVar.valueType;
  }
  const firstParam = getExpressionStartVar(variableData);
  const endVar = getEndVar(firstParam);
  return endVar.valueType;
}

export class ConditionGenerate {
  constructor(data) {
    const {
      conditionValueType,
      variable,
      comparator
    } = data;
    this.conditionValueType = conditionValueType;
    this.variable = new ContextGenerate(variable, {
      owner: this,
      isRoot: true
    });
    if (comparator) {
      this.comparator = new ContextGenerate(comparator, {
        owner: this
      });
    }
  }
}

export class ActionGenerate {
  constructor(data) {
    const {
      actionType,
      actionParams,
      actionId,
      titleComplete
    } = data;
    this.actionId = actionId;
    this.actionType = actionType;
    this.titleComplete = titleComplete;
    if (actionParams) {
      this.actionParams = actionParams.map((item2, index) => {
        const contextObj = {
          owner: this,
          isRootVar: true
        };
        if (actionType === "setValue" && index === 0) {
          contextObj.isParam = "actionSetLeft";
        }
        if (actionType === "setValue" && index === 1) {
          contextObj.isParam = "actionSetRight";
        }
        if (actionType === "invokeMethod" && index === 1) {
          contextObj.isParam = "actionFuncParam";
        }
        return new ContextGenerate(item2, contextObj);
      });
    }
  }
}

// 将后台返回 conditions 数组转化为前端数据；
export const gatherConditionData = (param,ruleUuid,isTable) => {
  let {
    targetObj,
    parentObj,
    expressionIndex,
    expression,
    conditionList,
    extraAttr,
    originObj,
    rowNameList
  } = param;
  let i = expressionIndex;
  const char = expression.charAt(i);
  const {
    children
  } = parentObj;
  i++;
  if (char === "#") {
    const conditionData = conditionList.splice(0, 1)[0];
    const {
      leftValueType,
      variable,
      comparator
    } = conditionData;
    const _conditionData = {
      conditionValueType: leftValueType,
      variable: translateVar(variable,{},ruleUuid,null,isTable)
    };
    if (comparator) {
      _conditionData.comparator = translateOperator(comparator,{},ruleUuid);
    }
    // const ruleContent = translateConditionData(conditionData);
    const ruleContent = new ConditionGenerate(_conditionData);
    if (targetObj.ruleCondition) {
      children.push({
        indent: parentObj.indent + 1,
        ruleCondition: {
          layer: extraAttr.layer,
          showLayer: extraAttr.layer,
          conditionId: extraAttr.conditionId,
          contents: ruleContent
        },
        rowNameList
      });
    } else {
      targetObj.indent = parentObj.indent + 1;
      targetObj.ruleCondition = {
        layer: extraAttr.layer,
        showLayer: extraAttr.layer,
        conditionId: extraAttr.conditionId,
        contents: ruleContent
      };
      targetObj.rowNameList=rowNameList
    }
    extraAttr.layer++;
    extraAttr.conditionId++;
    gatherConditionData({
      targetObj,
      parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === "A") {
    const _obj = {
      logicalSymbol: "and"
    };
    children.push(_obj);
    gatherConditionData({
      targetObj: _obj,
      parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === "B") {
    const _obj = {
      logicalSymbol: "unAnd"
    };
    children.push(_obj);
    gatherConditionData({
      targetObj: _obj,
      parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === "O") {
    const _obj = {
      logicalSymbol: "or"
    };
    children.push(_obj);
    gatherConditionData({
      targetObj: _obj,
      parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === "P") {
    const _obj = {
      logicalSymbol: "unOr"
    };
    children.push(_obj);
    gatherConditionData({
      targetObj: _obj,
      parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === "(") {
    if (!targetObj.children) {
      targetObj.children = [{}];
      targetObj.indent = parentObj.indent + 1;
      targetObj.fold = false;
    }
    gatherConditionData({
      targetObj: targetObj.children[0],
      parentObj: targetObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
  if (char === ")") {
    const _parentObj = findUpperLevelData(targetObj, originObj);
    const __parentObj = findUpperLevelData(parentObj, originObj);
    gatherConditionData({
      targetObj: _parentObj,
      parentObj: __parentObj,
      expressionIndex: i,
      expression,
      conditionList,
      extraAttr,
      originObj,
      rowNameList
    },ruleUuid,isTable);
  }
};

export function generateContextConditionList(_data,id) {
  if (Array.isArray(_data)) {
    return _data.map(condition => generateContextConditionList(condition,id));
  }
  const {
    children,
    ruleCondition
  } = _data;
  // let ruleCondition= null
  if (children && children.length > 0) {
    _data.children = generateContextConditionList(children,id);
    // if (id - 1 < children.length) {
      // ruleCondition = _data.children[id - 1].ruleCondition
    // }
  }
  if (ruleCondition) {
    const {
      contents
    } = ruleCondition;
    if(ruleCondition.conditionId===id){
      ruleCondition.contents = new ConditionGenerate(contents);
    }
    // ruleCondition.contents = new ConditionGenerate(contents);
  }

  return _data;
}

export function generateContextRuleData(data,key, id) {
  const {
    ruleData
  } = data;
  const {
    actionData = [], conditionData = [], elseActionData = []
  } = ruleData;
  if(key==='conditionData'){
    ruleData.conditionData = generateContextConditionList(conditionData,id);
  }else if(key==='actionData'){
    // debugger
    id < ruleData.actionData.length && ruleData.actionData.splice(id,1,new ActionGenerate(ruleData.actionData[id]));
    // ruleData.actionData = actionData.map((action,index) => {
    //     return new ActionGenerate(action);
    // });
  }else{
    id < ruleData.elseActionData.length && ruleData.elseActionData.splice(id,1,new ActionGenerate(ruleData.elseActionData[id]));
    // ruleData.elseActionData = elseActionData.map(elseAction => {
    //   return new ActionGenerate(elseAction);
    // });
  }
  return data;
}

// 生成属性结构；
export function getConditionTree(conditionExpression, conditions,ruleUuid,isTable) {
  const strIndex = 0;
  const extraAttr = {
    layer: 1,
    conditionId: 1
  };
  const conditionData = {
    children: [{}],
    indent: 0,
    fold: false
  };
  const conditionList = cloneRuleData(conditions);
  const param = {
    targetObj: conditionData.children[0],
    parentObj: conditionData,
    expressionIndex: strIndex,
    expression: conditionExpression,
    // conditionList:conditions,
    conditionList,
    extraAttr,
    originObj: conditionData,
    rowNameList: conditions.nameList
  };
  gatherConditionData(param,ruleUuid,isTable);
  return conditionData;
}

// 将后台返回 action 数组转化为前端数据；
export const getActionData = (actions = [],ruleUuid) => {
  return actions.map(item => {
    const {
      actionParams
    } = item;
    if (actionParams && actionParams.length > 0) {
      item.actionParams = actionParams.map(actionParam =>
        translateVar(actionParam,{},ruleUuid)
      );
    }
    return new ActionGenerate(item);
  });
};

// 在 数据结构 的上下文关系中生成一个 owner 属性，最终指向 condition对象，或 action 对象；
export function createContextOwner(targetItem, originItem) {
  let _owner;
  if (originItem.__context && originItem.__context.owner) {
    _owner = originItem.__context.owner;
  } else {
    _owner = originItem;
  }
  if (!targetItem.__context) {
    targetItem.__context = {};
  }
  targetItem.__context.owner = _owner;
}

// 在 数据结构 的上下文关系中生成一个 prever 属性，指向上一层变量，与 next 属性相反；
export function createContextPrever(targetItem, originItem) {
  if (!targetItem.__context) {
    targetItem.__context = {};
  }
  targetItem.__context.prever = originItem;
}

// 在 数据结构 的上下文关系中生成一个 isParam 属性，指明该参数的位置：'methodParam' | 'expressParam' | 'operatorParam' | 'actionSetLeft' | 'actionSetRight'
export function createContextIsParam(targetItem, prop) {
  if (!targetItem.__context) {
    targetItem.__context = {};
  }
  targetItem.__context.isParam = prop;
}

// 在 数据结构 的上下文关系中生成一个 isRootVar 属性，指明该参数是否是一个根变量（包括左值变量、参数变量）
export function createContextIsRootVar(targetItem, prop = true) {
  if (!targetItem.__context) {
    targetItem.__context = {};
  }
  targetItem.__context.isRootVar = prop;
}

// 将后台返回 ruleAttributes 数组转化为前端数据；
export const getRuleAttribute = (ruleAttributes = []) => {
  const newRuleAttribute = {};
  ruleAttributes.forEach(item => {
    newRuleAttribute[item.name] = item.dict;
  });
  return newRuleAttribute;
};

// 数据处理：modelData
export const getoperatorTypeList = data => {
  const operatorTypeList = {};
  data.map(item => {
    const type = item.operatorType;
    const arr = item.operatorList.map(item2 => {
      return {
        label: item2.operatorDescription,
        value: item2.operatorName,
        paramTypes: item2.paramTypes,
        paramQuantity: item2.paramQuantity
      };
    });
    operatorTypeList[type] = arr;
  });
  return operatorTypeList;
};

// 数据处理：dataSet
function getDatasetList(datasetList, baseMethodListMap, newArr = []) {
  datasetList.forEach(item => {
    const obj = {};
    const {
      viewName,
      name,
      dataType,
      dictName
    } = item;
    obj.label = viewName || "空";
    obj.value = name;
    obj.dictName = dictName;
    if (dataType) {
      obj.valueType = dataType;
      obj.isDatasetItem = true;
      if (baseMethodListMap[dataType]) {
        obj.children = baseMethodListMap[dataType];
      } else if (dataType.includes("List<")) {
        obj.children = baseMethodListMap["List"];
      }
    }
    newArr.push(obj);
  });
  return newArr;
}

// 数据处理：dataSet
export const getDatasetGroupList = (
  datasetGroupList = [],
  baseMethodListMap = {},
  newArr = []
) => {
  datasetGroupList.forEach((item, index) => {
    const obj = {};
    const {
      datasets: datasetList,
      packages,
      name
    } = item;
    // const { datasetGroupList, datasetList, groupId, groupName } = item;
    obj.label = name || "空";
    obj.value = name + index;
    obj.isDatasetItem = true;
    obj.children = [];
    if (packages && packages.length > 0) {
      obj.freezed = true;
      getDatasetGroupList(packages, baseMethodListMap, obj.children);
    }
    if (datasetList && datasetList.length > 0) {
      obj.freezed = true;
      getDatasetList(datasetList, baseMethodListMap, obj.children);
    }
    newArr.push(obj);
  });
  return newArr;
};

// 数据处理： baseModelList
export const getBaseMethodMapping = (baseModelList, baseMethodListMap = {}) => {
  baseModelList.forEach(item => {
    const {
      valueType,
      methodsList = []
    } = item;
    baseMethodListMap[valueType] = getMethodsList(methodsList);
  });
  return baseMethodListMap;
};

// 数据处理：modelList
export const getModelList = (
  modelList = [],
  fieldsDataList = [],
  methodsDataList = []
) => {
  modelList.forEach(item => {
    const {
      viewName,
      name,
      valueType,
      fieldsList,
      methodsList,
      status,
      display
    } = item;
    const fieldObj = {
      status,
      value: name,
      valueType,
      isFieldItem: true,
      label: viewName || "空",
      display
    };
    const methodObj = {
      value: name,
      valueType,
      isMethodItem: true,
      label: viewName || "空"
    };
    const newFieldsList = getFieldsList(fieldsList) || [];
    const newMethodList = getMethodsList(methodsList) || [];
    methodObj.children = [...newMethodList];
    fieldObj.children = [...newFieldsList, ...newMethodList]; // 将方法组件的数据合并进来
    fieldsDataList.push(fieldObj);
    methodsDataList.push(methodObj);
  });
  return {
    fieldsDataList,
    methodsDataList
  };
};

// 处理model数据中的 field 类型数据
function getFieldsList(fieldsList = []) {
  const newFieldList = fieldsList.map(item => {
    const obj = {};
    const {
      name,
      valueType,
      viewName,
      domain,
      display
    } = item;
    obj.label = viewName || "空";
    obj.value = name;
    obj.domain = domain;
    obj.valueType = valueType;
    obj.display = display;
    obj.isFieldItem = true;
    return obj;
  });
  return newFieldList;
}

// 处理model数据中的 method 类型数据
function getMethodsList(methodsList = []) {
  const newMethodList = methodsList.map(item => {
    const obj = {};
    obj.label = item.viewName;
    obj.value = item.name;
    obj.valueType = item.valueType;
    // obj.valueType = item.valuetype;
    obj.position = item.position;
    obj.isMethodItem = true;
    obj.paramsList = item.paramsTypeList;
    obj.domain = item.enumDictName || null;
    return obj;
  });
  return newMethodList;
}

// 将前端数据转换为后台数据
export const getBackEndData = (data,ruleUuid) => {
  const {
    conditionData,
    actionData,
    elseActionData,
    ruleAttributes,
    ruleName
  } = data;
  const newData = {};
  const newConditions = getConditionDataToBE(conditionData,
    {
      conditions: [],
      conditionExpression: "",
    },
    ruleUuid);
  newData.ruleName = ruleName;
  newData.conditions = newConditions.conditions;
  newData.conditionExpression = newConditions.conditionExpression.slice(1, -1);
  newData.ruleAttributes = getRuleAttributeToBE(ruleAttributes);
  newData.actions = getActionDataToBE(actionData,ruleUuid);
  newData.elseActions = getActionDataToBE(elseActionData,ruleUuid);
  if(newData.conditionExpression===''){
    newData.conditionExpression='null'
  }
  return newData;
};

function getRuleAttributeToBE(ruleAttributes = {}) {
  const newRuleAttributes = [];
  Object.keys(ruleAttributes).map(item => {
    const obj = {};
    obj.name = item;
    obj.dict = ruleAttributes[item];
    newRuleAttributes.push(obj);
  });
  return newRuleAttributes;
}

// 将 action 数据转换为后台数据
export function getActionDataToBE(actionData,ruleUuid) {
  if (!Array.isArray(actionData)) {
    return null;
  }
  return actionData.map(item => {
    return {
      actionType: item.actionType,
      actionParams: item.actionParams.map(item2 => tlBackVar(item2,ruleUuid)),
      titleComplete: item.titleComplete,
    };
  });
}

// 将树形结构的 condtion 数据转换为扁平的后台数据
export function getConditionDataToBE(
  conditionData,
  obj = {
    conditions: [],
    conditionExpression: ""
  },
  ruleUuid
) {
  const {
    children,
    ruleCondition,
    logicalSymbol
  } = conditionData;
  const {
    conditions
  } = obj;
  if (logicalSymbol) {
    const logicalMap = {
      and: "A",
      or: "O",
      unAnd: "B",
      unOr: "P"
    };
    const str = logicalMap[logicalSymbol];
    obj.conditionExpression = obj.conditionExpression + str;
  }
  if (ruleCondition) {
    const newCondition = tlBackCondition(ruleCondition,{},ruleUuid);
    conditions.push(newCondition);
    obj.conditionExpression = obj.conditionExpression + "#";
  } else if(children) {
    children.map((item, i) => {
      if (i === 0) {
        obj.conditionExpression = obj.conditionExpression + "(";
      }
      getConditionDataToBE(item, obj,ruleUuid);
    });
    obj.conditionExpression && (obj.conditionExpression = obj.conditionExpression + ")");
  }
  return obj;
}

// 将condition数据转化为后台需要的数据
function tlBackCondition(ruleCondition, newCondition = {},ruleUuid) {
  const {
    variable,
    comparator,
    conditionValueType
  } = ruleCondition.contents;
  newCondition.variable = tlBackVar(variable,ruleUuid);
  newCondition.leftValueType = conditionValueType;
  if (comparator) {
    newCondition.comparator = tlBackOperator(comparator, conditionValueType,{},ruleUuid);
  }
  return newCondition;
}

// 将variable转化为后台需要的数据
function tlBackVar(varData,ruleUuid) {
  let newVariable;
  const {
    variableType,
    singleParams
  } = varData;
  if (variableType === "constant") {
    newVariable = tlBackConstantItem(varData);
  } else if (variableType === "dataEntry") {
    newVariable = tlBackDataEntryItem(varData,{},ruleUuid);
  } else if (variableType === "field") {
    newVariable = tlBackFieldItem(varData,{},undefined,ruleUuid);
  } else if (variableType === "method") {
    if(singleParams){
      newVariable = tlBackSingleParamsItem(varData,{});
    }else{
      newVariable = tlBackMethodItem(varData,[],{},ruleUuid);
    }
  } else if (variableType === "expression") {
    newVariable = tlExpressionItem(varData,{},ruleUuid);
  }
  return newVariable;
}

function tlBackConstantItem(varData, newVariable = {}) {
  const {
    value,
    valueType,
    variableType,
    enumDictName
  } = varData;
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  newVariable.value = value;
  if (enumDictName) {
    newVariable.enumDictName = enumDictName;
  }
  return newVariable;
}

function tlBackSingleParamsItem(varData, newVariable = {}) {
  const {
    valueType,
    variableType,
    enumDictName,
    singleParams
  } = varData;
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  newVariable.singleParams = singleParams;
  if (enumDictName) {
    newVariable.enumDictName = enumDictName;
  }
  return newVariable;
}

function tlBackDataEntryItem(varData, newVariable = {},ruleUuid) {
  const baseMethodListMap = store.getters.listMap[ruleUuid].initModelData.baseMethodListMap;
  const dataEntryList = store.getters.listMap[ruleUuid].initModelData.dataEntryList;
  const {
    value,
    valueType,
    variableType,
    next
  } = varData;
  const newValuePath = value ? value.join(";") : "";
  const viewName = findLabelByValuePath(dataEntryList, newValuePath,ruleUuid);
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  newVariable.name = value ? value[value.length - 1] : null;
  newVariable.viewName = viewName;
  if (next) {
    const _baseMethodList = baseMethodListMap[valueType] ?
      baseMethodListMap[valueType] :
      valueType.includes("List<") ?
      baseMethodListMap["List"] : [];
    newVariable.next = tlBackMethodItem(next, _baseMethodList, {},ruleUuid);
  }
  return newVariable;
}
function getVariableData(option, nextVariableData, methodTxt) {
  const firstFieldItemList =
    this.listMap[this.ruleUuid].initModelData.firstFieldItemList || [];
  const {
    value,
    valueType,
    paramsList,
    isDatasetItem,
    isMethodItem,
    dictName,
    label,
    // domain,
    display,
  } = option;
  let {
    domain
  } = option;
  const dictMap =
    this.listMap[this.ruleUuid].dictMap;
  if (valueType === "Boolean") {
    domain = "boolean";
  }
  const newValueObj = {};

  if (isMethodItem) {
    let aValue = []
    let aValueIndex = []
    let _aValueIndex = []
    if (methodTxt) {
      const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
      let aFilterOperaItem = label.match(regs);
      let aFilterMethodTxt = methodTxt.match(regs)
      let filterComparatorTxt = methodTxt
      if (aFilterOperaItem) {
        let filterItem = label
        if (aFilterOperaItem.length === aFilterMethodTxt.length) {
          aFilterOperaItem = label.match(regs);
        } else {
          // 取金额113和金额245中较大的数
          // 取金额1和金额2中较大的数
          const tempItemNum = aFilterMethodTxt.map((item) => {
            if (isNaN(item)) {
              return false
            } else {
              return true
            }
          })
          // number
          if (!tempItemNum.find(item => {
              return item === false
            })) {
            const nanRes = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|true|false|【[^】]+】(的【[^】]+】)*/g;
            const numRes = /\d+\.\d+|\d+/g;
            aFilterOperaItem = label.match(nanRes);
            let aPosition = []
            for (let i = 0; i < aFilterOperaItem.length; i++) {
              let itemI = label.indexOf(aFilterOperaItem[i])
              let beforeItem = label.slice(0, itemI)
              let afterItem = label.slice(itemI + aFilterOperaItem[i].length, itemI + aFilterOperaItem[i].length + 1)
              let afterItemNum = afterItem.match(numRes) && afterItem.match(numRes)[0]
              let beforeItemNum = beforeItem.match(numRes) && beforeItem.match(numRes)[0]
              if (afterItemNum && !isNaN(afterItemNum) && beforeItemNum && !isNaN(beforeItemNum)) {
                aPosition[i] = {
                  position: 'm',
                  length: [beforeItemNum.length, afterItemNum.length]
                }
              } else if (afterItemNum && !isNaN(afterItemNum)) {
                aPosition[i] = {
                  position: 'a',
                  length: [afterItemNum.length]
                }
              } else if (beforeItemNum && !isNaN(beforeItemNum)) {
                aPosition[i] = {
                  position: 'b',
                  length: [beforeItemNum.length]
                }
              }
            }
            for (let i = 0; i < aFilterMethodTxt.length; i++) {
              const {
                position,
                length
              } = aPosition[i]
              if (position === 'b') {
                aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(length[0])
              } else if (position === 'a') {
                aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(0, aFilterMethodTxt[i].length - length[0])
              } else if (position === 'm') {
                aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(length[0]) + aFilterMethodTxt[i].slice(0, aFilterMethodTxt[i].length - length[1])
              }
            }
          }
        }
        if (aFilterOperaItem) {
          let tempT = filterItem
          for (let j = 0; j < aFilterOperaItem.length; j++) {
            // 去掉item过滤后的txt
            tempT = tempT.replace(
              aFilterOperaItem[j],
              ""
            );
          }
          filterComparatorTxt = tempT
        }
        // if (aFilterMethodTxt) {
        //   for (let j = 0; j < aFilterMethodTxt.length; j++) {
        //     // 去掉item过滤后的txt
        //     filterComparatorTxt = filterComparatorTxt.replace(
        //       aFilterMethodTxt[j],
        //       ""
        //     );
        //   }
        // }
        for (let j = 0; j < aFilterOperaItem.length; j++) {
          if (aFilterOperaItem[j].indexOf('#') !== -1) {
            _aValueIndex.push(j)
          }
          // 去掉item
          filterItem = filterItem.replace(aFilterOperaItem[j], "");
          if (filterComparatorTxt === filterItem) {
            aValueIndex = _aValueIndex
          }
        }
      }
      // get value
      if(aFilterMethodTxt){
        if (aFilterMethodTxt.length === 1) {
          if (aFilterMethodTxt[0].indexOf("(") === 0) {
            let valueTxt = aFilterMethodTxt[0].slice(
              1,
              aFilterMethodTxt[0].length - 1
            );
            aValue = valueTxt.split(",");
          } else {
            aValue = aFilterMethodTxt;
          }
        } else {
          aValue = aFilterMethodTxt;
        }
      }
      for (let j = 0; j < aValue.length; j++) {
        if (aValue[j].indexOf("(") === 0) {
          aValue[j] = aValue[j].slice(1);
        }
        if (aValue[j][aValue[j].length - 1] === ")") {
          aValue[j] = aValue[j].slice(0, aValue[j].length - 1);
        }
        if (
          (aValue[j].indexOf("'") === 0 &&
            aValue[j][aValue[j].length - 1] === "'") ||
          (aValue[j].indexOf('"') === 0 &&
            aValue[j][aValue[j].length - 1] === '"')
        ) {
          aValue[j] = aValue[j].slice(1, aValue[j].length - 1);
        }
      }
    }

    newValueObj.value = [value];
    newValueObj.valueType = valueType;
    newValueObj.domain = domain;
    newValueObj.variableType = "method";
    if (nextVariableData) {
      newValueObj.next = nextVariableData;
    }
    if (paramsList && paramsList.length > 0) {
      const fregex = /\【.*?\】/g;
      newValueObj.paramsList = paramsList;
      newValueObj.methodParams = paramsList.map((item, index) => {
        let tempV = null
        let tempaV = []
        let resObj = {}
        if (aValue && aValueIndex) {
          tempV = aValue[aValueIndex[index]]
        }
        if (tempV) {
          tempaV = tempV.match(fregex)
        }
        if (tempaV && tempaV.length > 0) {
          // 变量
          this.replaceComVariable = []
          resObj = this.getComVari(firstFieldItemList, tempaV)
        } else {
          // 常量
          const {
            domain: item_domain,
            valueType: item_valueType
          } = item;
          resObj = {
            value: aValue[aValueIndex[index]] || "",
            valueType: item_valueType,
            variableType: "constant",
          };
          if (item_domain) {
            // domain
            resObj.enumDictName = item_domain;
            resObj.value = this.findValueForViewName(dictMap[item_domain], aValue[aValueIndex[index]]) || aValue[aValueIndex[index]] || ""
          }
        }
        return resObj;
      });
    }
  } else if (isDatasetItem) {
    // dataEntry 类型，需要将value进行合并，除非下一层（next）是method类型；
    if (nextVariableData && nextVariableData.variableType === "dataEntry") {
      newValueObj.value = nextVariableData.value;
      newValueObj.value.unshift(value);
      newValueObj.valueType = nextVariableData.valueType;
      newValueObj.dictName = nextVariableData.dictName;
      newValueObj.variableType = "dataEntry";
      if (nextVariableData.next) {
        newValueObj.next = nextVariableData.next;
      }
    } else {
      newValueObj.value = [value];
      newValueObj.valueType = valueType;
      newValueObj.variableType = "dataEntry";
      newValueObj.dictName = dictName;
      if (nextVariableData) {
        newValueObj.next = nextVariableData;
      }
    }
  } else {
    newValueObj.value = [value];
    newValueObj.valueType = valueType;
    newValueObj.domain = domain;
    newValueObj.variableType = "field";
    newValueObj.display = display;
    if (nextVariableData) {
      newValueObj.next = nextVariableData;
    }
  }
  return newValueObj;
}
function tlBackFieldItem(varData, newVariable = {}, type = undefined,ruleUuid) {
  const baseMethodListMap = store.getters.listMap[ruleUuid].initModelData.baseMethodListMap;
  const fieldsDataList = store.getters.listMap[ruleUuid].initModelData.fieldsDataList;
  const {
    value,
    valueType,
    variableType,
    next,
    valuePath,
    parentInstanceName
  } = varData;
  const newValuePath = valuePath ? valuePath + ";" + value[0] : value[0];
  const newValuePathArr = newValuePath ? newValuePath.split(";") : [];
  // 获取path对应的 field 路径
  const optionFieldList = type ?
    getFieldOptionByPath(newValuePathArr, fieldsDataList, [], type) :
    getFieldOptionByPath(newValuePathArr, fieldsDataList);
  const lastOptionField = optionFieldList[optionFieldList.length - 1];
  const viewName = lastOptionField ? lastOptionField.label : "";

  let instanceName;
  if (valueType && valueType.indexOf(".") > 0) {
    instanceName = getInstanceName(valueType); // instanceName 如果valueType不是类名
  } else if (type) {
    instanceName = getInstanceName(type);
  } else {
    instanceName = parentInstanceName;
  }
  if (next) {
    // valuePath 保证能够找到正确的 viewName
    next.valuePath = newValuePath;
    if (valueType && valueType.indexOf(".") > 0) {
      next.parentInstanceName = instanceName;
    }

    if (next.variableType === "field") {
      newVariable.next = tlBackFieldItem(next,{},undefined,ruleUuid);
    } else if (next.variableType === "method") {
      // field 的最后一层有可能是 method
      // 获取基础类型方法
      const nextOptionList = lastOptionField.children || [lastOptionField];
      const _baseMethodList = baseMethodListMap[valueType] ?
        baseMethodListMap[valueType] :
        valueType.includes("List<") ?
        baseMethodListMap["List"] : [];
      const extraOptionList = [...nextOptionList, ..._baseMethodList];
      newVariable.next = tlBackMethodItem(next, extraOptionList,{},ruleUuid);
    }
  }
  newVariable.viewName = viewName;
  newVariable.instanceName = instanceName;
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  newVariable.name = value[0];
  return newVariable;
}

function getFieldOptionByPath(
  pathArr = [],
  fieldsDataList = [],
  optionList = [],
  type = undefined
) {
  let dataSource = fieldsDataList;
  for (let i = 0; i < pathArr.length; i++) {
    const path = pathArr[i];
    let option;
    if (type) {
      const outOption = dataSource.find(item => item.valueType === type);
      option = outOption.children.find(item => item.value === path);
      optionList.push(option);
      return optionList;
    } else {
      option = dataSource.find(item => item.value === path);
    }

    // 理论上 option 必定存在，但如果模型数据发生变化，则为 undefined ， 此时停止搜索
    if (option) {
      const {
        value,
        valueType,
        children
      } = option;
      // fieldsDataList 中数据包括两层，外层的数据的value属性为后台的类名，包括很多的 ".", 根据这个特性，来判断下一个数据是在内层找还是在外层找
      if (value.indexOf(".") > -1 && children) {
        dataSource = children;
      } else {
        const target = fieldsDataList.find(
          item => item.valueType === valueType
        );
        dataSource = target ? target.children : [];
      }

      optionList.push(option);
    } else {
      optionList.push({});
    }
  }
  return optionList;
}

function getMethodReturnName(valueType,ruleUuid) {
  const fieldsDataList = store.getters.listMap[ruleUuid].initModelData.fieldsDataList;
  const findItem = fieldsDataList.find(item => item.valueType === valueType);
  return findItem.label || "";
}

function tlBackMethodItem(varData, methodsDataList, newVariable = {},ruleUuid) {
  const baseMethodListMap = store.getters.listMap[ruleUuid].initModelData.baseMethodListMap;
  const
  objMethodsDataList = store.getters.listMap[ruleUuid].initModelData.
  methodsDataList;
  if(varData.singleParams) return
  const {
    value,
    valueType,
    variableType,
    next,
    methodParams = [],
    parentInstanceName,
    paramsList
  } = varData;
  const viewName = findLabelByValuePath(methodsDataList, value[0],ruleUuid);
  let instanceName;
  if (valueType.indexOf(".") > 0) {
    instanceName = getInstanceName(valueType); // instanceName 有问题。。。。。如果valueType不是类名，就从上一层取值；？？？？？？？
  } else {
    instanceName = parentInstanceName;
  }
  newVariable.methodViewName = viewName;
  newVariable.instanceName = instanceName;
  newVariable.methodName = value[0];
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  newVariable.paramsList = paramsList;
  if (methodParams.length > 0) {
    newVariable.methodParams = methodParams.map(item => tlBackVar(item,ruleUuid));
  }

  if (next) {
    if (next.variableType === "field") {
      newVariable.next = tlBackFieldItem(next, {}, valueType,ruleUuid);
      newVariable.viewName = getMethodReturnName(valueType,ruleUuid);
    } else {
      let _baseMethodList = baseMethodListMap[valueType] ?
        baseMethodListMap[valueType] :
        valueType.includes("List<") ?
        baseMethodListMap["List"] : [];
      if(_baseMethodList.length===0){
        let objML=objMethodsDataList.find(item=>{
          return item.valueType===valueType
        })
        if(objML&&objML.children){
          _baseMethodList=objML.children
        }
      }
      newVariable.next = tlBackMethodItem(next, _baseMethodList, {},ruleUuid);
    }
  }
  return newVariable;
}

function tlExpressionItem(varData, newVariable = {},ruleUuid) {
  const {
    valueType,
    variableType,
    expressionTreeData
  } = varData;
  const {
    newExpression,
    newConditions
  } = flattenExpressionTreeData(
    expressionTreeData,ruleUuid
  );
  newVariable.expressionValue = newExpression.slice(1, -1); // 去除最外层的括号
  newVariable.expressionParams = newConditions;
  // newVariable.valueType = 'test';
  newVariable.valueType = valueType;
  newVariable.variableType = variableType;
  return newVariable;
}

// 将树形结构的expression数据扁平化
function flattenExpressionTreeData(expressionTreeData,ruleUuid) {
  const {
    params,
    symbols,
    type,
    data
  } = expressionTreeData;
  let newConditions = [];
  let newExpression = "";
  if (type === "expression") {
    newExpression = newExpression + "(";
    if (params && params.length > 0) {
      for (let i = 0; i < params.length; i++) {
        const {
          newExpression: childExpression,
          newConditions: childConditions
        } = flattenExpressionTreeData(params[i],ruleUuid);
        newConditions = newConditions.concat(childConditions);
        newExpression = newExpression + childExpression;
        if (symbols[i]) {
          newExpression = newExpression + symbols[i];
        }
      }
    }
    newExpression = newExpression + ")";
  } else if (type === "variable") {
    newExpression = "#";
    newConditions = [tlBackVar(data,ruleUuid)];
  }
  return {
    newExpression,
    newConditions
  };
}

// 将 operator 转换为后台数据
function tlBackOperator(comparator, valueType, newComparator = {},ruleUuid) {
  const operatorTypeList = store.getters.listMap[ruleUuid].initModelData.operatorTypeList;
  // const operatorList = operatorTypeList[valueType] ?
  //   operatorTypeList[valueType] :
  //   valueType.includes(".") && valueType.includes("List<") ?
  //   operatorTypeList.List :
  //   operatorTypeList.Object;
  let operatorList=[]
  if(valueType){
    operatorList = operatorTypeList[valueType] ?
    operatorTypeList[valueType] :
    valueType.includes(".") && valueType.includes("List<") ?
    operatorTypeList.List :
    operatorTypeList.Object;
  }
  const {
    operatorParams = [],
      value = [],
      enumDictName,
      paramQuantity
  } = comparator;
  newComparator.operatorName = value[0];
  newComparator.paramQuantity = paramQuantity;
  const label = findLabelByValuePath(operatorList, value.join(";"),ruleUuid) || "";
  newComparator.operatorViewName = label;

  if (operatorParams && operatorParams.length > 0) {
    newComparator.operatorParams = tlBackOperatorParam(operatorParams,[],ruleUuid);
  }
  if (enumDictName) {
    newComparator.enumDictName = enumDictName;
  }
  return newComparator;
}

// 将 operatorParam 转换为后台数据
function tlBackOperatorParam(operatorParams, newOperatorParams = [],ruleUuid) {
  operatorParams.forEach(item => {
    const newParam = tlBackVar(item,ruleUuid);
    newOperatorParams.push(newParam);
  });
  return newOperatorParams;
}

// 计算获取instanceName
export const getInstanceName = className => {
  if (className) {
    const arr = className.split(".");
    const str = arr[arr.length - 1];
    return str.charAt(0).toLowerCase() + str.slice(1);
  }
  return "";
};

// 通过 valuePath 获取 viewName, 适用于树形数据（即不包括Field类型的）
function findLabelByValuePath(treeData, valuePath = "",ruleUuid,loopFirst) {
  const valuePathArr = valuePath.split(";");
  const targetValue = valuePathArr[valuePathArr.length - 1];
  const len = valuePathArr.length;
  if (targetValue === "") {
    return "";
  }
  for (let ti=0;ti<treeData.length;ti++) {
    const obj = treeData[ti];
    const {
      value,
      label,
      children,
      valueType
    } = obj;
    if (value === targetValue) {
      // valuePathArr 可能是多个，只返回当前层级，也就是最后一个；
      if (len === 1) {
        return label || "";
      }
    } else if(children) {
      const realLabel = findLabelByValuePath(children, targetValue,ruleUuid,loopFirst);
      if (realLabel) {
        return realLabel;
      }
    }else if(ti === treeData.length-1 && !loopFirst) {
      let firstFieldItemList = store.getters.listMap[ruleUuid].initModelData.firstFieldItemList
      let trD = null
      findItem(firstFieldItemList)
      if(trD){
        return findLabelByValuePath([trD],targetValue,ruleUuid,true)
      }
      function findItem(data) {
        for (let i = 0; i < data.length; i++) {
          if (valueType === data[i].valueType && value === data[i].value && label === data[i].label) {
            trD=data[i]
            return
          }else if(data[i].children){
            findItem(data[i].children)
          }
        }
      }
    }
  }
}

// 拼接 method 的 viewName
export function getMethodViewName(nameList = []) {
  let viewName = "";
  for (let i = 0, len = nameList.length; i < len; i++) {
    if (i === len - 1) {
      viewName = viewName + "-" + nameList[i];
    } else if (i === 0) {
      viewName = viewName + nameList[i];
    } else {
      viewName = viewName + "【的】" + nameList[i];
    }
  }
  return viewName;
}

// 将 expression 表达式转化为树形结构，例如 expressionStr = '#*(#+(#-#))+(#-#)', conditions = [1, 2, 3, 4, 5, 6]；
export function getExpressionTree(expressionStr = "", conditions = []) {
  const expTree = {
    type: "expression",
    params: [],
    symbols: []
  };
  let childConditions = [];
  let childExpStr = "";
  let bracketsCount = 0;
  for (const item of expressionStr) {
    if (item === "#") {
      const conditionItem = conditions.splice(0, 1)[0];
      if (bracketsCount === 0) {
        expTree.params.push({
          type: "variable",
          data: conditionItem
        });
      } else {
        childConditions.push(conditionItem);
        childExpStr = childExpStr + "#";
      }
    } else if (item === "(") {
      if (bracketsCount > 0) {
        childExpStr = childExpStr + "(";
      }
      bracketsCount++;
    } else if (item === ")") {
      bracketsCount--;
      if (bracketsCount > 0) {
        childExpStr = childExpStr + ")";
      } else if (bracketsCount === 0 && childConditions.length > 0) {
        const childTree = getExpressionTree(childExpStr, childConditions);
        expTree.params.push(childTree);
        childExpStr = "";
        childConditions = [];
      }
    } else {
      if (bracketsCount === 0) {
        expTree.symbols.push(item);
      } else {
        childExpStr = childExpStr + item;
      }
    }
  }
  return expTree;
}

// 获取 expression 的默认子项
export function getExpressionItem(type, valueType) {
  let expressionItem;
  const _newData = {
    value: [],
    valueType,
    variableType: "field"
  };

  if (type === "(") {
    expressionItem = {
      type: "expression",
      params: [{
        type: "variable",
        data: _newData
      }],
      symbols: []
    };
  } else if (type === "val") {
    expressionItem = {
      type: "variable",
      data: _newData
    };
  }
  return expressionItem;
}

const getBaseMethodList = (valueType, baseMethodListMap) => {
  const _baseMethodList = baseMethodListMap[valueType] ?
    baseMethodListMap[valueType] :
    valueType.includes("List<") ?
    baseMethodListMap["List"] : [];
  // return cloneRuleData(_baseMethodList);
  return _baseMethodList;
};

const filterChildOptions = (options = []) => {
  // const { owner = {}, isParam } = this.variableData.__context;
  // const { actionType = "" } = owner;
  return options.filter(item => {
    // if (item.isMethodItem && actionType === "invokeMethod") {
    //   return true;
    // }
    if (!item.isMethodItem || item.isMethodItem) {
      return true;
      // } else if (item.position === "02" && isParam !== "actionSetLeft") {
      //   return true;
      // } else if (
      //   item.position === "01" &&
      //   actionType &&
      //   isParam !== "actionSetLeft"
      // ) {
      //   return true;
    }
    return false;
  });
};

const getChildModelOptions = (valueType, dataForLoad = []) => {
  let childModelOptions;
  const targetItem = dataForLoad.find(item => {
    return (
      item.valueType === valueType && item.children && item.children.length > 0
    );
  });
  if (targetItem) {
    childModelOptions = filterChildOptions(targetItem.children);
  }
  // return cloneRuleData(childModelOptions) || [];
  return childModelOptions || [];
};

const loadData = (targetOption, baseMethodListMap, baseDataList) => {
  // targetOption.loading = true;
  const {
    valueType = ""
  } = targetOption;
  // load options lazily
  const baseMethodList = getBaseMethodList(valueType, baseMethodListMap);
  let childrenOptions = [];
  // valueType 如果是 string 等基本类型，表明处于最后一层，没有children
  if (valueType.indexOf(".") > 0) {
    childrenOptions = getChildModelOptions(valueType, baseDataList);
  }
  const returnOption = {};
  if (baseMethodList.length > 0 || childrenOptions.length > 0) {
    returnOption.children = [];
    returnOption.isLeaf = false;
    childrenOptions.forEach(item => {
      item.isLeaf = false;
      returnOption.children.push(item);
    });
    // 默认基础方法为最后一环，不再动态向后加载；
    baseMethodList.forEach(item => {
      item.isLeaf = true;
      returnOption.children.push(item);
    });
  } else {
    returnOption.isLeaf = true;
  }
  return returnOption;
};

function structData(fieldsDataList = [], baseMethodListMap, baseDataList) {
  if (Array.isArray(fieldsDataList.children)) {
    structData(fieldsDataList.children, baseMethodListMap, baseDataList);
  } else {
    fieldsDataList.forEach(item => {
      if (item.isLeaf === false) {
        const temp = loadData(item, baseMethodListMap, baseDataList);
        item = Object.assign(item, temp);
        if (Array.isArray(item.children)) {
          if (item.isLeaf === false) {
            structData(item, baseMethodListMap, baseDataList);
          }
        }
      }
    });
  }
}

export function getFirstFieldItemList(
  fieldsDataList = [],
  baseMethodListMap,
  baseDataList
) {
  const firstFieldItemList = [];
  for (let i = 0; i < fieldsDataList.length; i++) {
    if (String(fieldsDataList[i].status) !== "02") {
      firstFieldItemList.push({
        isFieldItem: fieldsDataList[i].isFieldItem,
        display: fieldsDataList[i].display,
        label: fieldsDataList[i].label,
        status: fieldsDataList[i].status,
        value: fieldsDataList[i].value,
        valueType: fieldsDataList[i].valueType,
        isLeaf: false
      });
    }
  }
  structData(firstFieldItemList, baseMethodListMap, baseDataList);
  return firstFieldItemList;
}

function addDisplayAttrToVariable(variable, fieldsDataList) {
  const {
    variableType,
    valueType,
    next,
    expressionParams,
    name,
    methodParams
  } = variable;
  const addDisplayAttr = (_fieldsDataList, target_name, target_valueType) => {
    _fieldsDataList.forEach(fieldsData => {
      const {
        valueType: _valueType,
        value,
        children
      } = fieldsData;
      if (value === target_name && target_valueType === _valueType) {
        variable.display = fieldsData.display;
      } else if (children && children.length > 0) {
        addDisplayAttr(children, target_name, target_valueType);
      }
    });
  };

  if (variableType === "field") {
    addDisplayAttr(fieldsDataList, name, valueType);
    if (next) {
      addDisplayAttrToVariable(next, fieldsDataList);
    }
  } else if (variableType === "expression") {
    expressionParams.forEach(param =>
      addDisplayAttrToVariable(param, fieldsDataList)
    );
  } else if (
    variableType === "method" &&
    methodParams &&
    methodParams.length > 0
  ) {
    methodParams.forEach(param =>
      addDisplayAttrToVariable(param, fieldsDataList)
    );
  }
}

export function addDisplayAttrToRuleData(ruleData, fieldsDataList = []) {
  const {
    actions,
    conditions,
    elseActions
  } = ruleData;
  conditions.map(condition => {
    const {
      variable,
      comparator = {}
    } = condition;
    const {
      operatorParams
    } = comparator;
    addDisplayAttrToVariable(variable, fieldsDataList);
    if (operatorParams && operatorParams.length > 0) {
      operatorParams.forEach(operatorParam =>
        addDisplayAttrToVariable(operatorParam, fieldsDataList)
      );
    }
  });
  actions.map(action => {
    const {
      actionParams
    } = action;
    actionParams.map(actionParam =>
      addDisplayAttrToVariable(actionParam, fieldsDataList)
    );
  });
  elseActions.map(action => {
    const {
      actionParams
    } = action;
    actionParams.map(actionParam =>
      addDisplayAttrToVariable(actionParam, fieldsDataList)
    );
  });
}

export function cloneRuleData(data) {
  if (!data) {
    return data;
  }

  const isSelfAttr = attrName => attrName.indexOf("_") === 0;

  if (Array.isArray(data)) {
    return data.map(item => {
      return cloneRuleData(item);
    });
  }
  if (typeof data === "object") {
    const obj = {};
    for (const key in data) {
      if (!isSelfAttr(key)) {
        obj[key] = cloneRuleData(data[key]);
      }
    }
    return obj;
  }
  return data;
}

export function cacheModelData(modelData) {
  const {
    complexModels,
    dicts = [],
    modelDomains = [],
    simpleModels,
    tccrSysModels,
    tccrSysOperators,
    latestMetadataVersion
  } = modelData;
  if (latestMetadataVersion || latestMetadataVersion === 0) {
    const operatorTypeList = getoperatorTypeList(tccrSysOperators);
    const baseMethodListMap = getBaseMethodMapping(tccrSysModels);
    const _datasetGroupList = getDatasetGroupList(
      simpleModels,
      baseMethodListMap
    );
    const {
      fieldsDataList,
      methodsDataList
    } = getModelList(complexModels);
    const dictMap = {};
    dicts.map(item => {
      const {
        dictName,
        values
      } = item;
      dictMap[dictName] = values.map(item2 => {
        return {
          value: item2.key,
          viewName: item2.value
        };
      });
    });
    modelDomains.map(item => {
      const {
        name,
        domainAttributes
      } = item;
      dictMap[name] = domainAttributes.map(item2 => {
        return {
          value: item2.value,
          viewName: item2.viewName
        };
      });
    });

    const firstFieldItemList = getFirstFieldItemList(fieldsDataList);
    let tempObj={
      dataEntryList: _datasetGroupList,
      fieldsDataList,
      methodsDataList,
      baseMethodListMap,
      operatorTypeList,
      firstFieldItemList
    }
    store.commit('setInitModelData', tempObj)
    return tempObj;
  }
  return {};
}

//名称超长返回截取
export function subLongName (name,len = 10) {
  return name.length>len?name.substring(0,len)+'...':name;
}

/**
 * 新窗口打开规则编辑页面
 * @param {Object} record - 包含规则信息的记录对象
 * @param {string} record.ruleUuid - 规则UUID
 * @param {string} record.engUuid - 引擎UUID
 * @param {string} record.folderUuid - 文件夹UUID
 * @param {string} backFlag - 返回标志，默认为dashboard
 * @returns {Promise<boolean>} - 成功返回true，失败返回false
 */
export const openRulePage = async (record, backFlag = 'dashboard') => {
  try {
    // 兼容不同数据结构，如果没有ruleUuid字段，可能使用uuid字段
    const ruleUuid = record.ruleUuid || record.uuid;

    if (!ruleUuid) {
      console.error('规则UUID不存在');
      return false;
    }

    // 先检查规则状态
    const res = await getRuleByUuid({ uuids: ruleUuid });
    // 如果返回"规则待审核,不允许操作!"，则阻止导航
    if (res.code == 50006) {
      return false;
    } else if (res.code == 20000) {
      const url = `/ruleBase-${record.engUuid}/rulePackage-${record.folderUuid || 'all'}/ruleContent-${ruleUuid}?backFlag=${backFlag}`;
      window.open(url);
      return true;
    }
    return false;
  } catch (error) {
    console.error('打开规则页面失败:', error);
    return false;
  }
};
