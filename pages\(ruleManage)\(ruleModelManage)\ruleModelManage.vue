<!-- 规则模型管理 -->

<script setup lang="ts">

import { list, getDicValueByTypeCode, ruleBomExport } from "@/api/rule_model";
import { getAllBusinessByLoginUser } from "@/api/rule_base";
import RuleHistory from "@/businessComponents/ruleModelManage/ruleHistory.vue";
import useTableConfig from '@/composables/useTableConfig';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import RuleModelAdd from "@/businessComponents/ruleModelManage/ruleModelAdd.vue";
import RuleModelUpdate from "@/businessComponents/ruleModelManage/ruleModelUpdate.vue";

definePageMeta({
    title: '规则模型管理',
    path:'/ruleModelManage'
})

const message = inject<any>('message')
interface Rule {
    modelName: string;
    businessLine: string;
    bomModelType: string;
    desc: string;
    lastModifiedTimeStr: string;
    modifiedId: string;
    uuid: string;
}

//字典值
const model_type_options = reactive<Record<string, any>>({});
const businessLineOptions = ref<Array<{name: string, code: string}>>([]);

// 定义不包含序号列和操作列的表格列
const tableColumns = [
    {
        title: '模型名称',
        align:'left',
        dataIndex: 'modelName',
        key: 'modelName',
        width:180
    },
    {
        title: '业务条线',
        align:'left',
        dataIndex: 'businessLine',
        key: 'businessLine',
        width:150,
    },
    {
        title: '模型类型',
        align:'center',
        dataIndex: 'bomModelType',
        width:100,
        key: 'bomModelType',
    },
    {
        title: '修改人',
        align:'center',
        dataIndex: 'modifiedId',
        key: 'modifiedId',
        width:100,
    },
    {
        title: '修改时间',
        align:'center',
        dataIndex: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        width:200,
    },
    {
        title: '模型描述',
        align:'left',
        dataIndex: 'desc',
        key: 'desc',
        width:200,
    },
];

const rules = ref<Rule[]>([]);

const fetchRules = async (params: Record<string, any> = {}) => {
    // 构建完整的查询参数
    const queryParams = {
        ...params,
        modelName: params.name,
        businessLine: params.businessLine,
        bomModelType: params.model_type,
        startDate: params.fristTime,
        endDate: params.endTime,
        page: params.page || 1,
        several: params.pageSize || 10
    };

    try {
        const res = await list(queryParams);
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取规则模型失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

// 新增抽屉相关状态
const addDrawerVisible = ref(false);
const updateDrawerVisible = ref(false);
const currentUuid = ref('');

const handleAdd = ()=> {
    addDrawerVisible.value = true;
}

const handleAddClose = () => {
    addDrawerVisible.value = false;
    // 使用布局组件提供的query方法，自动处理分页重置
    if (listLayout.value) {
        listLayout.value.refresh();
    }
}

const handleEdit = (record: Rule) => {
    currentUuid.value = record.uuid;
    updateDrawerVisible.value = true;
}

const handleUpdateClose = () => {
    updateDrawerVisible.value = false;
    // 使用布局组件提供的query方法，自动处理分页重置
    if (listLayout.value) {
        listLayout.value.refresh();
    }
}

const handleExport = (record: Rule) => {
    ruleBomExport({uuid: record.uuid}).then((res) => {
        let headers = res.headers;
        let blob = new Blob([res.data], {
            type: headers["content-type"]
        });
        let link = document.createElement("a");
        let url = window.URL.createObjectURL(blob);
        let fileName = headers["content-disposition"].split(';')[1].split('=')[1];
        link.href = url
        link.download = fileName;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    });
}
const handleHistory = (record: Rule) => {
    // 这里可以跳转到编辑页面或者弹出编辑对话框
    //message.info(`历史${record.modelName}`);
    hisVisible.value = true;
    i.value = record.uuid;

};

//隐藏flag
const hisVisible = ref<boolean>(false)
const i = ref<string>('')
//组件挂载后
onMounted(() => {
    selectDictOption();
    //fetchRules();
});
// 模型类型
const selectDictOption = async () => {
    const res = await getDicValueByTypeCode({
        typeCode: "bomModelType",
    })
    Object.assign(model_type_options, res.data);
    const res2 = await getAllBusinessByLoginUser();
    businessLineOptions.value = res2.data;

    // 更新搜索配置中的业务条线选项
    const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
    if (businessLineField && businessLineField.compConfig) {
        businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
            name: item.name,
            value: item.code
        }));
    }

    // 更新模型类型选项
    const modelTypeField = searchConfig.advancedSearchFields.find(field => field.field === 'model_type');
    if (modelTypeField && modelTypeField.compConfig) {
        modelTypeField.compConfig.options = Object.values(model_type_options).map((item: any) => ({
            name: item.name,
            value: item.code
        }));
    }
}
// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '模型名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '模型名称',
            field: 'name',
            compType: 'input',
            defaultValue:''
        },
        {
            label: '业务条线',
            field: 'businessLine',
            compType: 'select',
            compConfig: {
                options: businessLineOptions.value.map(item => ({
                    name: item.name,
                    value: item.code
                }))
            },
            defaultValue:null
        },
        {
            label: '模型类型',
            field: 'model_type',
            compType: 'select',
            compConfig: {
                options: Object.values(model_type_options).map(item => ({
                    name: item.name,
                    value: item.code
                }))
            },
            defaultValue:null
        },
        {
            label: '开始时间',
            field: 'fristTime',
            compType: 'datePicker'
        },
        {
            label: '结束时间',
            field: 'endTime',
            compType: 'datePicker'
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件 - 不再直接使用handleSearch
    searchEvent: () => {},
    //新建事件权限
    addPermission: RULE_PERMISSION.BOM_MODEL.ADD,
    // 新建事件
    addNewEvent: handleAdd,
    // 新增：表单处理器配置
    formHandler: {
        // 日期字段特殊处理
        dateFields: {
            startField: 'fristTime',
            endField: 'endTime',
        },
        // 查询方法
        queryMethod: fetchRules
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: Rule) => {
    return [
        {
            key: 'update',
            label: '更新',
            permission: RULE_PERMISSION.BOM_MODEL.UPDATE,
            onClick: () => handleEdit(record)
        },
        {
            key: 'export',
            label: '导出',
            onClick: () => handleExport(record)
        },
        {
            key: 'history',
            label: '历史',
            onClick: () => handleHistory(record)
        }
    ];
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);
// 数据加载完成处理函数
const handleDataLoaded = (response: any) => {
    if (response && response.data) {
        rules.value = response.data;
    }
};
</script>

<template>
    <ListPage
            ref="listLayout"
            title="规则模型管理"
            :searchConfig="searchConfig"
            :eventConfig="eventConfig"
            :showAddButton="true"
            :tableColumns="tableColumns"
            :queryMethod="fetchRules"
            rowKey="uuid"
            :actionMenuGetter="getActionMenuItems"
            @dataLoaded="handleDataLoaded"
    >
    </ListPage>
    <RuleHistory :controlVisible="hisVisible" :i="i" @closeModal="hisVisible=false"></RuleHistory>

    <FlexDrawer
            :visible="addDrawerVisible"
            @close="handleAddClose"
            title="新增规则模型"
            :width="1000"
            v-if="addDrawerVisible"
    >
        <RuleModelAdd @close="handleAddClose" />
    </FlexDrawer>

    <FlexDrawer
            :visible="updateDrawerVisible"
            @close="handleUpdateClose"
            title="更新规则模型"
            :width="1000"
            v-if="updateDrawerVisible"
    >
        <RuleModelUpdate :uuid="currentUuid" @close="handleUpdateClose"/>
    </FlexDrawer>
</template>

<style lang="scss" scoped>
.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}
</style>
