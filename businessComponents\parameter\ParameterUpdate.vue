<!-- 参数修改 -->

<template>
    <div id="data_edit">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="参数名称" name="paramName">
                <a-input autocomplete="off" v-model:value="form.paramName" placeholder="请输入标题"></a-input>
            </a-form-item>
            <a-form-item label="参数值" name="paramValue">
                <a-input autocomplete="off" v-model:value="form.paramValue" placeholder="请输入作者"></a-input>
            </a-form-item>
            <a-form-item label="说明" name="discription">
                <a-textarea :auto-size="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="500"
                            v-model:value="form.discription"></a-textarea>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
    import {parameterSub} from '@/api/parameter'

    const message = inject('message')

    const router = useRouter();

    const form = reactive({
        paramName: '',
        paramValue:'',
        discription:''
    });


    const rules = {
        paramName: [
            { required: true, message: '参数名称不能为空', trigger: 'change' }
        ],
        paramValue: [
            { required: true, message: '参数值不能为空', trigger: 'change' }
        ],
    };


    const props = defineProps({
        datar: {
            type: Object,
        },
    });

    watch(
        () => props.datar,
        (newValue) => {
            if (newValue) {
                let newForm = {
                    paramName: '',
                    paramValue:'',
                    discription:''
                };
                Object.assign(form, newForm);
                Object.assign(form, props.datar);
            }
        },
        {
            immediate: true,
        }
    );


    const submitFun = (callback) => {
        ruleForm.value.validate().then(() => {
            parameterSub({
                ...form
            }).then((res) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    message.error(res.data);
                }
            })
        }).catch((error) => {
            console.log(error);
        })
    };


    const ruleForm = ref(null);

    defineExpose({
        submitFun
    });
</script>

<style lang="scss" scoped>
    #data_edit ::v-deep {

        .ant-input,
        .ant-select,
        .ant-upload-dragger,
        .ant-upload-list,
        .ant-textarea {
            width: 400px;
        }

        .ant-textarea {
            height: 400px;
        }

        .ant-upload-dragger {
            height: 100px;
        }

        .ant-upload-dragger .anticon-upload {
            margin: 0;
            line-height: 50px;
            font-size: 50px;
        }

        .jarUpload.hidden .ant-upload {
            display: none;
        }

        .ant-tabs {
            width: 70%;
            min-height: 100px;
            margin: 30px 30px 30px 120px;
        }

        .ant-textarea,
        textarea {
            height: 150px !important;
        }

        .ant-tabs-card {
            box-shadow: unset;
        }

        .params_form {
            border-bottom: 1px solid #dcdfe6;

            h4 {
                font-weight: unset;

                span {
                    margin-left: 100px;
                }

                span.primary {
                    color: #409eff;
                }

                span.danger {
                    color: #f56c6c;
                }
            }

            label {
                color: #99a9bf;
            }

            span {
                color: #606266;
            }
        }

        .params_form:last-child {
            border-bottom: unset;
        }

        .form_flex_div,
        .form_flex_div .ant-form-item {
            display: flex;
            flex-direction: row;
            flex: 1;

            .ant-form-item {
                overflow: auto;
            }
        }

        .ant-tree {
            font-size: 14px;
        }

        .ant-main {
            padding: 0 0 10px 0;
        }
    }
</style>
