<!--规则详情规则展示通用组件-->
<template>
    <div class="rule-detail-content">
        <div class="ruletable">
            <div class="slider-alert-div" v-show="ruleInfoData.type == '2' || ruleInfoData.type == '决策表'">
                <div class="slider-div-left">
                    <a-alert message="点击对应单元格可查看完整数据" type="warning" :closable="false" show-icon />
                    <a-switch style="margin-left: 20px;margin-right: 10px" v-model:checked="switchVal"
                        @change="switchChange('switchVal', 'fulltab')" />
                    <span style="color: gray">{{ checkedValue }}</span>
                </div>
                <div class="slider-div">
                    <div class="slider-block">
                        <a-slider v-model:value="sliderValue" @change="sliderInput" :min="0" :max="100" :step="1"
                            :tooltip-visible="false" />
                    </div>
                    <div class="scale-buttons">
                        <a-button-group size="small">
                            <a-button @click="adjustScale(-10)">-</a-button>
                            <a-button>{{ sliderValue }}%</a-button>
                            <a-button @click="adjustScale(10)">+</a-button>
                        </a-button-group>
                    </div>
                    <a-tooltip title="全屏">
                        <FullscreenOutlined style="color:green;margin-left:10px" @click="fullScreen()"
                            v-if="showFullScreen" />
                    </a-tooltip>
                </div>
            </div>
            <div v-if="ruleInfoData.type == '1' || ruleInfoData.type == '普通规则'" class="normal-rule-container">
                <!--普通规则详情-->
                <table v-if="props.ruleInfoFlag" class="datatable rule" style="width: 100%">
                    <tr v-show="ruleInfoData.textDtl !== null && ruleInfoData.textDtl !== ''">
                        <td
                                :style="props.ruleSize ? 'background-color: #f6f5ec' : 'background-color: #f6f5ec;font-size:14px;height:380px;vertical-align: top;'">
                            <div style="margin: 10px" v-html="ruleInfoData.textDtl"></div>
                        </td>
                    </tr>
                    <tr v-show="ruleInfoData.textDtl === null || ruleInfoData.textDtl === ''">
                        <td
                                :style="props.ruleSize ? 'background-color: #f6f5ec;' : 'background-color: #f6f5ec;height:380px;'">
                            <div
                                    style="display: flex; justify-content: center; align-items: center; flex-direction: column; margin-top: 10px;">
                                <IconSearchEmpty />
                                <div
                                        style="font-size: var(--font-size-body-2,14px);margin-bottom: 30px;color:var(--color-text1-2,#6e6e6e)">
                                    <span>暂无规则文本</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                <!--文本编辑器-->
                <TextEditor
                        v-if="!props.ruleInfoFlag"
                        v-model="ruleInfoData.textDtl"
                        :readOnly="false"
                        :uuid="ruleInfoData.uuid"
                        :viewStatus="viewStatus"
                        :validateResult="validateResult"
                        ref="textEditorRef"
                />
            </div>
            <div v-show="ruleInfoData.type == '2' || ruleInfoData.type == '决策表'" class="fakeContainer2 fixed-table" id="fakeContainer"
                :style="props.ruleSize ? 'height:' + tabHeight + 'px' : 'height:450px'">

                <div id="tip-div" v-html="tip" :style="{ top: tipTop, left: tipLeft }" v-show="showTip"
                    @mouseenter="tipHover" @mouseleave="tipLeave"></div>
                <div v-if="ruleInfoData.type == '2' || ruleInfoData.type == '决策表'">
                    <div class="fakeContainer fixed-table"
                        :style="props.ruleSize ? 'height:' + tabHeight + 'px' : 'height:450px'">
                        <table v-show="ruleInfoData.textDtl !== null && ruleInfoData.textDtl !== ''" class="datatable"
                            id="datatable" :style="{
                                transform: 'scale(' + tableScale + ')',
                                transformOrigin: 'left top',

                            }">
                        </table>
                        <div v-show="ruleInfoData.textDtl === null || ruleInfoData.textDtl === ''"
                            style="display: flex; justify-content: center; align-items: center; flex-direction: column; margin-top: 10px;">
                            <IconSearchEmpty />
                            <div
                                style="font-size: var(--font-size-body-2,14px);margin-bottom: 30px;color:var(--color-text1-2,#6e6e6e)">
                                <span>暂无规则文本</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用FullModel组件实现全屏展示 -->
    <FullModel :isModalVisible="isModalVisible" :isFullscreen="isFullscreen" :titleText="titleText"
        :handleCancel="handleModalCancel" :onFullscreenToggle="onFullscreenToggle" :showFullBtn="false"
        :showFooter="false">
        <template #default>
            <div class="fulltab" :style="{ maxHeight: isFullscreen ? '75vh' : '60vh' }">
                <RuleDetailContent :ruleInfoData="ruleInfoData" :showFullScreen="false" />
            </div>
        </template>
    </FullModel>

</template>

<script setup lang="ts">
import { beautyTable } from '@/api/table_diff';
import TextEditor from '@/components/textEditor/index.vue';
import { ref, watch, nextTick, defineExpose } from 'vue';

const props = defineProps({
    ruleInfoData: {
        type: Object
    },
    //全屏展示显示/隐藏
    showFullScreen: {
        type: Boolean,
        default: true
    },
    ruleSize: {
        type: Boolean,
        default: true
    },
    maxHeight: {
        type: Number,
        default: 600
    },
    viewStatus: {
        type: String,
        required: true
    },
    validateResult: {
        type: Object,
        default: () => ({})
    },
    ruleInfoFlag:{
        type:Boolean,
        default: false
    }
});

// 文本编辑器引用
const textEditorRef = ref<any>(null);

// 获取编辑器内容
const getEditorContent = () => {
    if (textEditorRef.value && typeof textEditorRef.value.getLatestContent === 'function') {
        return textEditorRef.value.getLatestContent();
    }
    return props.ruleInfoData?.textDtl || '';
};

// 清除缓存
const clearLocalContent = () => {
    if (textEditorRef.value && typeof textEditorRef.value.clearLocalContent === 'function') {
        textEditorRef.value.clearLocalContent();
  }
};

// 暴露给父组件的方法
defineExpose({
    getEditorContent,
    clearLocalContent
});

const validColor = ref(false);
const switchVal = ref(false);
const sliderValue = ref(100);
const tip = ref('');
const tipTop = ref('');
const tipLeft = ref('');
const showTip = ref(false);
const tableScale = ref(1);
const checkedValue = ref('长文本隐藏');
const tabHeight = ref(200); // 默认高度
const minHeight = 200; // 最小高度
const maxHeight = props.maxHeight; // 最大高度

// 全屏弹出框相关状态
const isModalVisible = ref(false);
const isFullscreen = ref(true); // 默认全屏模式
const titleText = ref('规则详情全屏展示');
//动态tableId
const tableKey = ref('');

// 计算表格实际内容高度
const calculateTableHeight = () => {
    setTimeout(() => {
        // 对于决策表类型保持原来的逻辑
        if (props.ruleInfoData.type === '2' || props.ruleInfoData.type === '决策表') {
            let table = document.getElementById('datatable');
            if (!table) {
                table = document.getElementById(tableKey.value);
            }
            if (table) {
                const tableHeight = table.offsetHeight;
                // 根据表格内容高度计算容器高度，但不小于最小高度，不大于最大高度
                const calculatedHeight = Math.max(minHeight, Math.min(tableHeight + 50, maxHeight));
                tabHeight.value = calculatedHeight;
            }
        } else {
            // 对于普通规则类型，使用固定高度或者根据props设置
            tabHeight.value = props.ruleSize ? 450 : maxHeight;
        }
    });
};

const ruleInfoChange = () => {
    nextTick(() => {
        tableKey.value = Math.random().toString(16).slice(2);
        const newId = tableKey.value;
        const fakeContainerId = `fakeContainer_${newId}`;
        document.getElementById('datatable')?.setAttribute('id', newId);
        document.getElementById('fakeContainer')?.setAttribute('id', fakeContainerId);
        if (props.ruleInfoData.type == '决策表') {
            sessionStorage.removeItem(`tableStr_${newId}`);
            sessionStorage.setItem(`tableStr_${newId}`, props.ruleInfoData.tableContentHis);
            const elm = document.getElementById(newId);

            if (elm) {
                elm.innerHTML = props.ruleInfoData.tableContentHis;
            }
        }
        if (props.ruleInfoData.type == '2') {
            sessionStorage.removeItem(`tableStr_${newId}`);
            sessionStorage.setItem(`tableStr_${newId}`, props.ruleInfoData.tableContent);
            const elm = document.getElementById(newId);
            if (elm) {
                elm.innerHTML = props.ruleInfoData.tableContent;
            }
        }

        beautyTable(
            newId,
            (e: MouseEvent, tipText: string) => {
                if (tipText) {
                    tipLeft.value = `${e.clientX - 2}px`;
                    tipTop.value = `${e.clientY - 2}px`;
                    tip.value = tipText;
                    showTip.value = true;
                }
            },
            'audit'
        );
        if (props.ruleInfoData.validStatus === '无效') {
            validColor.value = true;
        }
    });
};

// 使用 watch 监听 ruleInfoData 的变化
watch(() => props.ruleInfoData, (newVal) => {
    if (newVal.type === '2') {
        ruleInfoChange();
    }
    if (newVal.type === '决策表') {
        ruleInfoChange();
    }
    // 计算表格高度
    calculateTableHeight();
}, { immediate: true });

const switchChange = (strN: string, classN: string) => {
    setTimeout(() => {
        let tableObj = document.querySelectorAll(`.${classN} .datatable td`);
        for (let i = 0; i < tableObj.length; i++) {
            const element = tableObj[i] as HTMLElement;
            if (switchVal.value) {
                element.style.whiteSpace = 'pre-wrap';
                element.style.wordWrap = 'break-word';
                checkedValue.value = '长文本显示';
            } else {
                element.style.whiteSpace = 'nowrap';
                checkedValue.value = '长文本隐藏';
            }
        }
    });
};

const sliderInput = (val: number | [number, number]) => {
    if (typeof val === 'number') {
        tableScale.value = val / 100;
    } else {
        // 如果是数组形式，取第一个值
        tableScale.value = val[0] / 100;
    }
};

const tipLeave = () => {
    showTip.value = false;
    tip.value = '';
};

const tipHover = () => {
    showTip.value = true;
};

// 全屏显示函数 - 使用弹出框
const fullScreen = () => {
    isModalVisible.value = true;
};

// 切换全屏/半屏模式
const onFullscreenToggle = () => {
    isFullscreen.value = !isFullscreen.value;
};
// 关闭弹出框
const handleModalCancel = () => {
    isModalVisible.value = false;
};

const setScale = (scale: number) => {
    sliderValue.value = scale;
    tableScale.value = scale / 100;
};

const adjustScale = (delta: number) => {
    const newScale = Math.min(Math.max(sliderValue.value + delta, 0), 100);
    setScale(newScale);
};
</script>

<style lang="scss" scoped>
.rule-detail-content {
    :deep(.ruletable) {

        .datatable tr:first-child td,
        .datatable2 tr:first-child td {
            text-align: left !important;
        }

        .datatable.rule tr:first-child td,
        .datatable2.rule tr:first-child td {
            text-align: left !important;
            white-space: normal !important;
            word-wrap: break-word;
            word-break: break-all;
        }

        .fakeContainer {
            margin: 0 !important;
            margin-top: 20px;
            width: 100% !important;
            min-height: 200px;
            /* 设置最小高度 */
            overflow: auto;
        }

        .datatable th {
            word-wrap: break-word;
            word-break: normal;
            border: 1px solid #ccc;
            /* 行名称边框 */
            background: #f2f2f2;
            /*url(../img/201407061920.gif) repeat-x;*/
            /* 行名称背景色 */
            color: #333;
            /* 行名称颜色 */
            font-weight: bold;
            height: 30px;
            line-height: 24px;
            padding-left: 5px;
            padding-right: 5px;
            text-align: center;
            white-space: nowrap;
        }

        .datatable td {
            border: 1px solid #ccc;
            /* 单元格边框 */
            text-align: left;
            padding-top: 4px;
            padding-bottom: 4px;
            padding-left: 10px;
            padding-right: 10px;
            white-space: nowrap;
        }

        .datatable tr:hover,
        .datatable tr.altrow {
            background-color: #f2f2f2;
            /* 动态变色 */
        }

        table {
            width: 100%;
        }

        th,
        td {
            max-width: 90px !important;
            text-overflow: ellipsis;
            overflow-x: hidden;
            text-align: center;
        }

        #tip-div {
            position: fixed;
            top: 0;
            left: 0;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #000;
            padding: 10px;
            z-index: 2000;
            font-size: 14px;
            line-height: 1.2;
            min-width: 10px;
            word-wrap: break-word;
            overflow: auto;
            max-height: 300px;
            max-width: 600px;
        }

        // table {
        //   border-collapse: collapse;
        // }
        .fixed-table {
            overflow: auto;
            height: 100%;
            /* 设置固定高度 */
            width: 100%;
            // position: absolute
        }

        .fixed-table td,
        .fixed-table th {
            /* 设置td,th宽度高度 */
            border: 1px solid #c7d8ee;
            width: 150px;
            min-width: 150px;
            // height: 30px;
            // padding: 5px;
        }

        .fixed-table th {
            position: sticky;
            top: 0;
            /* 首行永远固定在头部  */
        }

        .fixed-table th.th2 {
            top: 30px;
        }

        .fixed-table th.th3 {
            top: 60px;
        }

        .fixed-table th.xh {
            left: 0;
            /* 首列永远固定在左侧 */
            top: 0;
            /* 首行永远固定在头部  */
            z-index: 2;
            background-color: #fff;
            min-width: 40px;
            /* 序号列宽度缩小 */
        }

        .fixed-table td:first-child {
            position: sticky;
            left: 0;
            /* 首列永远固定在左侧 */
            min-width: 40px;
            /* 序号列宽度缩小 */
            background-color: white;
        }

        .fixed-table th.xh {
            z-index: 2;
            /*表头的首列要在上面*/
            background-color: #f2f2f2;
        }

        .fixed-table th>div {
            width: 100%;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
        }

        .slider-div-left {
            display: flex;
            align-items: center;

            .el-alert {
                margin-right: 20px;
            }
        }

        .slider-alert-div {
            display: flex;
            justify-content: space-between;

            .el-alert {
                width: auto;
            }
        }

        .slider-div {
            width: 300px;
            display: flex;
            float: right;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;

            .slider-block {
                width: 100px;
                margin-right: 10px;

                .el-slider__runway.show-input {
                    width: 50px !important;
                    top: -2px;
                }
            }

            .scale-buttons {
                margin-right: 10px;

                :deep(.ant-btn) {
                    min-width: 32px;
                    padding: 0 8px;

                    &:nth-child(2) {
                        pointer-events: none;
                        background-color: #f0f0f0;
                        min-width: 50px;
                    }
                }
            }
        }

        .fakeContainer {
            margin: 0 !important;
            margin-top: 20px;
            width: 100% !important;
            min-height: 200px;
            /* 设置最小高度 */
            overflow: auto;
        }

        .datatable tr:first-child td,
        .datatable2 tr:first-child td {
            text-align: left !important;
        }

        .datatable.rule tr:first-child td,
        .datatable2.rule tr:first-child td {
            text-align: left !important;
            white-space: normal !important;
            word-wrap: break-word;
            word-break: break-all;
        }

        .normal-rule-container {
            height: 100%;
            padding: 0;
            background-color: #fff;
        }
    }
}
</style>
