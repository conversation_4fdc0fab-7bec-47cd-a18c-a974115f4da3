<template>
    <div id="doc-reader-content" class="DocReader-module_content_AcIMy">
        <div>
            <article id="content" class="article-content" tabindex="0" style="outline-style: none;">
                <div class="ne-doc-major-viewer">
                    <div class="yuque-doc-content" data-df="lake" style="position: relative;">
                        <div v-if="loading">
                            <TableSkeleton :columns="skeletonColumns" :limit="5" :scrollY="300" />
                        </div>
                        <div v-else>
                            <RuleInfoDisplay
                                :data="ruleInfo"
                                :config="ruleDisplayConfig"
                                :hisFlag="true"
                            />
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { getRuleDetail } from "@/api/rule_release_history";
import store from '@/store';
import TableSkeleton from '@/components/TableSkeleton.vue';

const props = defineProps({
    linkOptions: {
        type: Object,
        required: true
    }
});

const router = useRouter();
const ruleInfo = ref([]);
const datailsUid = ref("");
const baseDataUid = ref("");
const BaseDataUid = ref('');
const loading = ref(true);

// 骨架屏表格列配置
const skeletonColumns = [
    { title: '规则名称', dataIndex: 'ruleName', key: 'name', width: 180 },
    { title: '规则类型', dataIndex: 'type', key: 'type', width: 120 },
    { title: '规则版本', dataIndex: 'edition', key: 'edition', width: 100 },
    { title: '规则状态', dataIndex: 'status', key: 'status', width: 120 },
    { title: '修改时间', dataIndex: 'lastModifiedTimeStr', key: 'time', width: 150 },
];

const ruleDisplayConfig = [
    { label: '规则名称', field: 'ruleName' },
    { label: '任务编号', field: 'appNo' },
    { label: '规则类型', field: 'type' },
    { label: '规则版本', field: 'edition' },
    { label: '规则状态', field: 'status' },
    { label: '有效状态', field: 'validStatus', class: 'valState' },
    { label: '修改时间', field: 'lastModifiedTimeStr' },
    { label: '修改人', field: 'modifiedId' },
    { label: '创建时间', field: 'createdTimeStr' },
    { label: '创建人', field: 'createdId' },
    { label: '规则优先级', field: 'salience' },
    { label: '规则包', field: 'packageNameAll' },
    { label: '规则描述', field: 'descs', span: 24 }
];

const getRuleInfo = () => {
    loading.value = true;
    let params = {
        uuid: baseDataUid.value.uuid
    }
    //规则集_规则历史详情
    getRuleDetail(params).then((res) => {
        ruleInfo.value = res.data;
        loading.value = false;
    }).catch(() => {
        loading.value = false;
    });
}

onMounted(() => {
    datailsUid.value = store.state.settings.dataId;
    baseDataUid.value = store.state.settings.baseDataId;
    getRuleInfo();
})
</script>

<style lang="scss" scoped>
.rule_historyRight {
    :deep(.ant-form-item) {
        .ant-form-item-label > label {
            font-weight: 600;
            font-size: 14px;
            margin-left: 20px;
       }
    }
}
</style>
