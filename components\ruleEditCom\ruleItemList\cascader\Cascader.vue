<template>
  <!-- 根据 open 属性控制是否显示级联选择器 -->
  <div
    class="global-cascader-container"
    :style="{
      top: acTop, // 设置级联选择器的 top 位置
      left: acLeft, // 设置级联选择器的 left 位置
    }"
    ref="cascaderContainer"
    v-if="open"
  >
    <ACascader
      ref="cascader"
      :options="options"
      @change="onSelectChange"
      :changeOnSelect="true"
      :showSearch="showSearchConfig"
      expandTrigger="hover"
      :open="open"
      :getPopupContainer="() => $refs.cascaderContainer"    
    >
    </ACascader>
  </div>
</template>

<script setup>

// 定义组件的 props
const props = defineProps({
  options: Array, // 级联选择器的选项数据
  acTop: String, // 级联选择器的 top 位置
  acLeft: String, // 级联选择器的 left 位置
  open: Boolean, // 控制级联选择器是否显示
});

// 定义组件的 emits
const emit = defineEmits(["onSelectChange"]); // 定义 onSelectChange 事件

// 处理级联选择器选项改变的方法
const onSelectChange = (value, selectedOptions) => {
  // 如果有值，则触发 onSelectChange 事件并将值和选中的选项传递出去
  value && emit("onSelectChange", value, selectedOptions);
};

// 计算属性，返回搜索配置对象
const showSearchConfig = computed(() => {
  return {
    // 自定义搜索过滤函数
    filter: (inputValue, path) => {
      return path.some((option) => {
        if (typeof option.label !== "string") {
          return false;
        }
        const optionLabel = option.label.toLowerCase(); // 将选项标签转换为小写
        const _inputValue = inputValue.toLowerCase(); // 将输入值转换为小写
        return optionLabel.indexOf(_inputValue) > -1; // 检查选项标签是否包含输入值
      });
    },
    matchInputWidth: true, // 搜索框宽度是否与输入框匹配
    limit: Infinity // 显示所有搜索结果,不限制结果条数
  };
});
</script>

<style lang="scss">
.global-cascader-container {
  position: fixed;
  background: #fff;
  .ant-select-dropdown {
    position: relative !important;
  }
  .ant-select-single .ant-select-selector {
    min-width: 114px !important;
    width: auto;
  }
  .ant-cascader-menus ol,
  .ant-cascader-menus ul {
    max-width: 300px !important;
    overflow-x: auto !important;
    flex-shrink: 0; 
    .ant-cascader-menu-item {
      overflow: unset !important;
      display: flex;
      justify-content: start;
      flex-wrap: nowrap;
      align-items: center;
      padding-right: 24px;  
    }
  }
  .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,
  .ant-cascader-menu-item-loading-icon {
    position: unset !important;
    margin-top: 2px !important;
  }
}
</style>
