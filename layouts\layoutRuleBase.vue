<!-- 规则库页面模版 -->

<script setup>
import {
    engineeringTreeList, engineeringTreeListAll, importEng, getSaveOrUpdateRuleFolderInfo,
    folderSsaveOrUpdate,
    ruleBaseFolderDelete,
    getEngineeringByUuid,
    list,
    treeList,
    ruleBaseFolderCopySave
} from '@/api/rule_base'
import { ruleModalInit } from "@/api/rule_editor";
import qs from 'qs'
import RuleAdd from "@/businessComponents/ruleBaseManage/RuleAdd";
import { collectOrCancelCommonUse, CollectObjectType } from '@/api/dashboardApi'
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";

// 给html元素添加特定类，以使规则编辑及详情页面上的可滚动区域在顶部栏之下
document?.querySelector('html')?.classList.add('layout-read-write')
const isLoading = ref(false);
const route = useRoute()
const ruleBaseId = ref('')
//返回页脚
const backPage = ref(1);

watch(
    () => route.params.ruleBaseId,
    (value, oldValue) => {
        if (typeof value === 'string' && value !== oldValue) {
            ruleBaseId.value = value
            if (route.query.backPage) {
                backPage.value = route.query.backPage
            }
            getRulePackageTree(value)
        }
    },
    {
        immediate: true,
    }
)
onUnmounted(() => {
    localStorage.setItem('ruleTabLists', '');
    localStorage.setItem('detailTabLists', '');
    isLoading.value = false;
})
// 规则包结构树
const rulePackageTree = ref([])
const modelTree = ref([])
const dictTree = ref([])
// 搜索前结构树
const rulePackageTreeAll = ref([])
const modelTreeAll = ref([])
const dictTreeAll = ref([])


// 规则库名称
const ruleBaseName = ref('')
//规则库id
const ruleBaseAlwaysUsedId = ref('')
const ruleBaseData = ref({})

const expandedKeys = ref(['0']);

//设置常用判断开关
const ifAlwaysUsed = ref(false);
// 获取规则包结构树
function getRulePackageTree(uuid = ruleBaseId.value) {
    let parmas = {
        uuid,
        state: 0,
    };
    if (uuid === 'all') {//规则浏览
        /*uuid =
            "8a2d8e9f7e974458017ebf8485360000,8a2d8e9f83c15c020183c4dea5ab0116,8a2d8e9f7fb15561017fb4cc027300d6,8a2d8e9f809cf2c00180b1b53c1e09d5,8a2d8e9f809cf2c00180b27502200adb,8a2d8e9f7fb15561017fb581bf0100d9,8a2d8e9f8466a2c001849d44eb370799,8a2d8e9f83f01c59018409663d1c00eb,8a2d8e9f83888c180183ba5dbe830067,8a2d8e9f83888c180183ba5c854a0054,8a2d8e9f783a8c660179e4851e710bd8,8a2d8e9f7f7305b9017f7324fcb10036,8a2d8e9f7c6d75cc017c6f684228000b,8a2d8e9f7c6d75cc017c6f6c78f20018,8a2d8e9f7c6d75cc017c6f6e66eb0023,8a2d8e9f7c6d75cc017c6f6678c80000,8a2d8e9f8441a9bb018450c354c6000f,8a2d8e9f87bd1a550187c6607d58002d,8a2d8e9f8441a9bb018450991f480008,8a2d8e9f87bd1a550187c665fd080034,8a2d8e9f885141dd0188c392e6c927d8,";
        */let parmas = {
            uuid,
            state: 0,
        };
        isLoading.value = true;
        engineeringTreeListAll(parmas).then((res) => {
            const treeData = res.data;
            let key = 0;
            // key赋值
            function formatData(data1, treeEngUuid) {
                data1.forEach(res => {
                    res.key = key.toString();
                    key++;
                    if (treeEngUuid && res.iconCls === "ico_rule_project") {
                        res.treeEngUuid = res.engUuid;
                    } else if (treeEngUuid && res.iconCls === null) {
                        res.treeEngUuid = treeEngUuid;
                    } else {
                        res.treeEngUuid = res.engUuid;
                    }
                    if (!res.uuid) {
                        res.uuid = 'all';
                    }
                    let id = res.engUuid;
                    if (res.children) {
                        formatData(res.children, id)
                    }
                })
            }
            formatData(treeData, '');
            rulePackageTree.value = treeData;
            rulePackageTreeAll.value = treeData;
            ruleBaseName.value = '工作空间'
            treeChildClick.value = '0';
            isLoading.value = false;
        })
    } else {
        let key = 0;
        isLoading.value = true;
        // key赋值
        function formatData(data1, treeEngUuid) {
            if (data1 !== null) {
                data1.forEach(res => {
                    res.key = key.toString();
                    key++;
                    if (treeEngUuid && res.iconCls === "ico_rule_project") {
                        res.treeEngUuid = res.engUuid;
                    } else if (treeEngUuid && res.iconCls === null) {
                        res.treeEngUuid = treeEngUuid;
                    } else {
                        res.treeEngUuid = res.engUuid;
                    }
                    if (!res.uuid) {
                        res.uuid = 'all';
                    }
                    let id = res.engUuid;
                    if (res.children) {
                        formatData(res.children, id)
                    }

                })
            }

        }
        if (uuid.indexOf('demandUuid') !== -1) {
            list({
                businessLine: uuid.split('demandUuid-')[1].split('businessUuid-')[1],
                page: 1,
                several: 10000,
            }).then((res) => {
                let engUuid1 = [];
                res.data.data.forEach((item) => {
                    engUuid1.push(item.uuid.split(","));
                });
                parmas = {
                    uuid: engUuid1.toString(),
                    state: "0",
                    page: 1,
                    several: 10000,
                }
                engineeringTreeList(parmas).then((res) => {
                    isLoading.value = false;
                    const treeData = res.data;
                    formatData(treeData, '');
                    rulePackageTree.value = treeData;
                    rulePackageTreeAll.value = treeData;
                    ruleBaseName.value = treeData[0].children[0].folderName;
                    treeChildClick.value = '0';
                })
            });

        } else {
            engineeringTreeList(parmas).then((res) => {
                let key = 0;
                isLoading.value = false;
                const treeData = res?.data?.[0]?.children?.[0];
                formatData(treeData.children, '');
                ruleBaseData.value = treeData
                ruleBaseName.value = treeData?.folderName
                ruleBaseAlwaysUsedId.value = treeData?.engUuid
                ifAlwaysUsed.value = treeData?.ifSetCommonUsed
                rulePackageTree.value = treeData?.children ?? []
                rulePackageTreeAll.value = treeData?.children ?? []

            })

            ruleModalInit(ruleBaseId.value).then((res) => {
                const data = res.data || {};
                const { complexModels, modelDomains } = data || [];
                if (complexModels && complexModels.length > 0) {
                    let res = [];
                    for (let i = 0; i < complexModels.length; i++) {
                        let tempJ = {
                            children: [],
                            name: complexModels[i].name + new Date().getTime() + i,
                            viewName: complexModels[i].viewName,
                        };
                        const { fieldsList, methodsList } = complexModels[i];
                        const fieldMethods = fieldsList.concat(methodsList);
                        for (let j = 0; j < fieldMethods.length; j++) {
                            let tempCJ = {
                                name: fieldMethods[j].name + new Date().getTime() + i + j,
                                viewName: fieldMethods[j].viewName,
                            };
                            tempJ.children.push(tempCJ);
                        }
                        res.push(tempJ);
                    }
                    modelTree.value = res;
                    modelTreeAll.value = res;
                }
                if (modelDomains && modelDomains.length > 0) {
                    let res = [];
                    for (let i = 0; i < modelDomains.length; i++) {
                        let tempJ = {
                            children: [],
                            name: modelDomains[i].name + new Date().getTime() + i,
                            viewName: modelDomains[i].viewName,
                        };
                        const { domainAttributes } = modelDomains[i];
                        for (let j = 0; j < domainAttributes.length; j++) {
                            let tempCJ = {
                                name: domainAttributes[j].name + new Date().getTime() + i + j,
                                viewName: domainAttributes[j].viewName,
                            };
                            tempJ.children.push(tempCJ);
                        }
                        res.push(tempJ);
                    }

                    dictTree.value = res;
                    dictTreeAll.value = res;
                }
            });
        }

    }

}
const activeName = ref('first');
const tabClick = (key) => {
    //切换tab时重置查询输入内容
    let val = { target: { value: '' } }
    onSearchChange(val);
}
const glossary = ref('model');
const searchValue = ref('');
const onSearchChange = (e) => {
    expandedKeys.value = ['0'];
    searchValue.value = e.target.value;
    rulePackageTree.value = searchTree(rulePackageTreeAll.value, searchValue.value, true)
    modelTree.value = searchTree(modelTreeAll.value, searchValue.value, true)
    dictTree.value = searchTree(dictTreeAll.value, searchValue.value, true)
    /* if(activeName.value === 'first'){//规则包

     }else{//词汇表

     }*/

}
function searchTree(tree, keyword, includeChildren = false) {
    const newTree = []
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        let title = node.viewName ? node.viewName : node.folderName;
        if (title && title.includes(keyword)) {
            // 如果当前节点符合条件，则将其复制到新的树形结构中，并根据 includeChildren 参数决定是否将其所有子节点也复制到新的树形结构中
            newTree.push({ ...node, children: includeChildren ? searchTree(node.children || [], '', true) : [] })
        } else if (node.children) {
            // 如果当前节点不符合条件且存在子节点，则递归遍历子节点，以继续搜索
            const result = searchTree(node.children, keyword, true)
            if (result.length > 0) {
                //如果搜索条件匹配字段，展开节点
                if (!expandedKeys.value.includes(node.key)) {
                    expandedKeys.value.push(node.key)
                }
                // 如果子节点中存在符合条件的节点，则将其复制到新的树形结构中
                newTree.push({ ...node, children: result })
            }
        }
    }
    return newTree
}

//导入规则包
const uploadHidden = ref(false);
const dialogUploadVisible = ref(false);
const importFun = () => {
    dialogUploadVisible.value = true;
    uploadHidden.value = false;
}





//文件处理
const upForm = ref(null)
const uploadFile = async (params) => {
    const file = params.file,
        aExtension = ["erule"],
        resFil = aExtension.filter((item) => {
            return file.name.indexOf(item) !== -1;
        });

    if (resFil.length === 0) {
        message.error("只能上传erule文件！");
        return;
    }
    const upForm1 = new FormData();
    upForm1.append("engUuid", ruleBaseId.value);
    upForm1.append("importFile", file);
    upForm1.append("fileName", file.name);
    //刷新文件上传状态，延时执行才会显示效果
    setTimeout(() => {
        params.onSuccess({ 'code': 200 }, file)
    })

    upForm.value = upForm1;
    // }
}
//确认框加载样式
const dialogUploadLoading = ref(false);
const dialogUploadSubmit = async () => {
    dialogUploadLoading.value = true;
    try {
        let res = await importEng(upForm.value);
        if (res.code === 20000) {
            message.success(res.data)
            //批量导入整个规则模型后需要延时刷新页面使数据全部更新
            setTimeout(() => {
                location.reload();
            }, 2000);

        } else {
            message.error(res.data);
        }
    } finally {
        dialogUploadVisible.value = false;
        dialogUploadLoading.value = false;

    }

}



//编辑左侧菜单栏规则包
const data = ref({
    form: {
        ruleBaseName: "",
        superiorRuleBase: "",
        ruleBaseDesc: "",
        extraProperty: "",
    },
    dialogFormVisible: false,
    dialogFormTitle: "",
    jData: {},
    copyDomShow: false,
    defaultKey: [],
    defaultKey2: [],
    clickTreeData2: null,
    ruleTreeOptions: [],
    rules: {
        ruleBaseName: [
            { required: true, message: "不能为空", trigger: "blur" },
            {
                pattern: /^[a-zA-Z_\u4e00-\u9fa5][\u4e00-\u9fa5a-zA-Z0-9_\d]+$/,
                message:
                    "规则包名称只能包括中文、数字、字母和下划线,并且不能以数字开头！",
            },
        ],
        ruleTree: [{ required: true, message: "不能为空", trigger: "blur" }],
    },
    submitLoading: false
})
const dropdownClick = (command, node) => {
    buttonShowClick.value = '';
    if (!node.data) {
        node.data = node;
    }
    const dialogFormTitle = {
        create: "新建规则包",
        update: "修改规则包",
        copy: "复制规则包",
    };
    if (command === "delete") {
        ruleBaseFolderDelete(
            qs.stringify({
                uuid: node.data.uuid,
            })
        ).then((res) => {
            if (res.code === 20000) {
                message.success('删除成功')
                getRulePackageTree();
            } else {
                message.error(res.data);
            }
        });
    } else {
        data.value.copyDomShow = false;
        data.value.dialogFormTitle = dialogFormTitle[command];
        data.value.jData.engUuid = node.data.engUuid;
        if (command === "create") {
            data.value.jData.uuid = "";
            data.value.jData.parentUuid = node.data.uuid;
        } else if (command === "update") {
            data.value.jData.uuid = node.data.uuid;
            data.value.jData.parentUuid = node.data.parent;
        } else if (command === "copy") {
            data.value.copyDomShow = true;
            data.value.jData.uuid = node.data.uuid;
        }
        getSaveOrUpdateRuleFolderInfo(data.value.jData).then((res) => {
            let sJ = 1;
            data.value.form.ruleBaseName = "";
            data.value.form.superiorRuleBase = "";
            data.value.form.ruleBaseDesc = "";
            data.value.jData.status = res.data.status ? res.data.status : "";
            data.value.jData.createdId = res.data.createdId ? res.data.createdId : "";
            data.value.jData.createdTimeStr = res.data.createdTimeStr
                ? res.data.createdTimeStr
                : "";
            data.value.jData.createdTime = res.data.createdTime
                ? res.data.createdTime
                : "";
            if (command === "update") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                data.value.form.ruleBaseName = res.data.folderName;
                data.value.form.ruleBaseDesc = res.data.descs;
                data.value.form.extraProperty = res.data.extraProperty;
                data.value.dialogFormVisible = true;
            } else if (command === "copy") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                // data.value.selectFolderName = res.data.parentFolderName;
                data.value.form.ruleBaseName = res.data.folderName;
                data.value.form.ruleBaseDesc = res.data.descs;
                const _parent = res.data.parent;
                // tree select
                getEngineeringByUuid({
                    uuid: res.data.engUuid,
                }).then((res1) => {
                    copyFolderOper(res1.data.businessLine, _parent, sJ);
                });
            } else if (command === "create") {
                data.value.form.superiorRuleBase = res.data.parentFolderName;
                data.value.dialogFormVisible = true;
            }
        });
    }
}
const setTreeList = (params) => {
    let startIndex = 0;
    _setAttirbute(params);
    function _setAttirbute(item) {
        item.filter((v) => {
            v.key = startIndex;
            startIndex += 1;
            if (v.children && v.children.length) {
                _setAttirbute(v.children);
            }
        });
    }
    return params;
};
const copyFolderOper = (businessLine, _parent, sJ) => {
    treeList({
        businessLine: businessLine,
    }).then((res) => {
        function formatData(data1) {
            data1.forEach(res => {
                res.title = res.folderName;
                res.value = res.key;
                if (res.children) {
                    formatData(res.children)
                }
            })
        }
        let data1 = setTreeList(res.data);
        formatData(data1);
        data.value.ruleTreeOptions = data1;
        data.value.dialogFormVisible = true;
    });
}
const ruleNodeClick = (value, node) => {
    data.value.form.superiorRuleBase = node.folderName;
    data.value.clickTreeData2 = node;
}
const dialoForm = ref(null);
const saveOrUpdate = (formName) => {
    dialoForm.value.validate().then((valid) => {
        if (valid) {
            data.value.submitLoading = true;
            if (data.value.copyDomShow) {
                if (data.value.form.superiorRuleBase) {
                    if (data.value.clickTreeData2) {
                        ruleBaseFolderCopySave(
                            {
                                descs: data.value.form.ruleBaseDesc,
                                engUuid: data.value.clickTreeData2.engUuid,
                                folderName: data.value.form.ruleBaseName,
                                parent: data.value.clickTreeData2.uuid === 'all' ? '' : data.value.clickTreeData2.uuid,
                                parentFolderName: data.value.form.superiorRuleBase,
                                uuid: "",
                                status: data.value.jData.status,
                                createdId: data.value.jData.createdId,
                                createdTime: data.value.jData.createdTime,
                                createdTimeStr: data.value.jData.createdTimeStr,
                            },
                            data.value.clickTreeData2.engUuid,
                            data.value.jData.uuid
                        ).then((res) => {
                            data.value.submitLoading = false;
                            if (res.code === 20000) {
                                message.success(res.data);
                                data.value.dialogFormVisible = false;
                                data.value.copyDomShow = false;
                                getRulePackageTree();
                            } else {
                                message.error(res.data);
                            }
                        }).catch((res) => {
                            data.value.submitLoading = false;
                        });
                    } else {
                        message.error("请选择其他规则包");
                        data.value.submitLoading = false;
                    }
                } else {
                    message.error("请选择规则包");
                    data.value.submitLoading = false;
                }
            } else {
                folderSsaveOrUpdate({
                    descs: data.value.form.ruleBaseDesc,
                    engUuid: data.value.jData.engUuid,
                    folderName: data.value.form.ruleBaseName,
                    parent: data.value.jData.parentUuid === 'all' ? '' : data.value.jData.parentUuid,
                    parentFolderName: data.value.form.superiorRuleBase,
                    uuid: data.value.jData.uuid,
                    status: data.value.jData.status,
                    createdId: data.value.jData.createdId,
                    createdTime: data.value.jData.createdTime,
                    createdTimeStr: data.value.jData.createdTimeStr,
                    extraProperty: data.value.form.extraProperty,
                }).then((res) => {
                    if (res.code === 20000) {
                        message.success(res.data);
                        data.value.dialogFormVisible = false;

                        getRulePackageTree();
                    } else {
                        message.error(res.data);
                    }
                    data.value.submitLoading = false;
                }).catch((res) => {
                    data.value.submitLoading = false;
                });

            }
        }
    });
}

//点击按钮时不隐藏
const buttonShowClick = ref('');
const openChangeStyle = (open, node) => {
    if (open) {
        buttonShowClick.value = node.key;
    } else {
        buttonShowClick.value = '';
    }
}

//选中字体加粗
const treeChildClick = ref('');
//生成唯一键，用于刷新查询到的table及返回规则列表
const clickKey = ref('');
const clickTreeChildStyle = (node) => {
    if (node.key) {
        treeChildClick.value = node.key;
    }
    clickKey.value = Math.random().toString(16).slice(2);
}

//新增规则
const isModalVisible = ref(false); // 对话框显示状态
// 规则新增组件
const ruleAddComponent = ref();

const { ruleTypes } = useRuleTypes()

//新增规则用传递id
const ruleBaseAddId = ref('');
const rulePackageAddId = ref('');
// 缓存node值，如果添加规则后增加左侧树选中样式
const treeChildAddClick = ref({});
const openAddModal = (type, node) => {
    treeChildAddClick.value = {};
    isModalVisible.value = true;
    ruleBaseAddId.value = node.treeEngUuid;
    rulePackageAddId.value = node.uuid;
    // 延迟执行，以确保 ruleAddComponent 已经挂载
    nextTick(() => {
        ruleAddComponent.value.form.ruleType = type
        //新建规则为规则流时，优先级变为规则流类型
        if (type === '5') {
            ruleAddComponent.value.ruleTypeChange(type);
        }
    })
    //打开对话框后隐藏下拉菜单
    buttonShowClick.value = '';
    treeChildAddClick.value = node;
}

// 处理modal ok 事件
function handleModalOk() {
    let submitFun = ruleAddComponent.value.handleSubmit;
    submitFun && submitFun((res) => {
        isModalVisible.value = false;
        treeChildClick.value = treeChildAddClick.value.key;
        navigateTo(generateLink(treeChildAddClick.value) + '&ruleAddUuid=' + res);
        treeChildAddClick.value = ''
    });
}

//设为/取消常用方法
const setAlwaysUsed = async () => {
    try {
        const res = await collectOrCancelCommonUse({
            uuid: ruleBaseAlwaysUsedId.value,
            type: CollectObjectType.RULE_BASE // 使用枚举类型代替硬编码的"1"
        })
        ifAlwaysUsed.value = !ifAlwaysUsed.value
        message.success(res.data)
    } catch (error) {
        message.error(ifAlwaysUsed.value ? '设为常用失败' : '取消常用失败')
        console.error('设为常用操作失败:', error)
    }
}


// 方法来生成 NuxtLink 的地址
const generateLink = (node) => {
    if (ruleBaseId.value === 'all' && node.folderName === '工作空间') {
        //工作空间链接
        return '/ruleBase-all'
    } else if (ruleBaseId.value.indexOf('demandUuid') !== -1 && node.folderName === '工作空间') {
        //规则调整首页链接
        return `/ruleBase-${ruleBaseId.value}`
    } else if (ruleBaseId.value === 'all' && node.folderName !== '工作空间') {
        //规则浏览规则包链接
        return `/ruleBase-all/rulePackage-${node.uuid}?ruleBaseId=${node.treeEngUuid}&clickKey=${clickKey.value}`
    } else if (ruleBaseId.value.indexOf('demandUuid') !== -1 && node.folderName !== '工作空间') {
        //规则调整规则包链接
        return `/ruleBase-${ruleBaseId.value}/rulePackage-${node.uuid}?ruleBaseId=${node.treeEngUuid}&backPage=${backPage.value}&clickKey=${clickKey.value}`
    } else if (ruleBaseId.value !== 'all' && ruleBaseId.value.indexOf('demandUuid') === -1 && node.folderName !== '工作空间') {
        //正常链接
        return `/ruleBase-${ruleBaseId.value}/rulePackage-${node.uuid}?clickKey=${clickKey.value}`
    }
    return '#'
}
</script>

<template>
    <div id="App">
        <div class="lark">
            <div class="main-wrapper" style="width: calc(0px + 100vw); margin-left: 0px; margin-right: 0px;">
                <div id="main-right-content" class="WebPureLayout-module_rightMainContent_88QOi main-right-content">
                    <div class="WebPureLayout-module_rightMainContentChildren_hiyzf">
                        <div class="lark" data-testid="ReaderLayout:reader-main">
                            <div class="main-wrapper">
                                <slot />
                                <div id="aside"
                                    class="ReaderLayout-module_asideWrapper_+Fdu8 ReaderLayout-module_onlyTocAside_ljsfR"
                                    data-testid="ReaderLayout:aside" data-lark-popup-container="true">
                                    <div class="ReaderLayout-module_aside_WX6k7"
                                        style="will-change: width; transition: width 200ms cubic-bezier(0.1, 0, 0, 1); width: 259px;">
                                        <div class="ReaderLayout-module_crumb_wF-5C"
                                            v-if="ruleBaseId === 'all' || ruleBaseId.indexOf('demandUuid') !== -1">
                                            <NuxtLink href="/dashboard" class="ReaderLayout-module_yuqueLogo_KdpAI">
                                                <IconErule />
                                            </NuxtLink><svg width="14" height="14" viewBox="0 0 256 256"
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="larkui-icon larkui-icon-arrow-left3 ReaderLayout-module_miniDropdown_7uya+">
                                                <path
                                                    d="M154.995 58.157c3.834 3.834 3.904 10.007.21 13.927l-.21.215-48.99 48.99c-3.834 3.834-3.904 10.007-.21 13.927l.21.215 48.99 48.99c3.905 3.905 3.905 10.237 0 14.142-3.834 3.834-10.008 3.904-13.927.21l-.215-.21-48.99-48.99c-11.599-11.599-11.715-30.331-.348-42.073l.348-.353 48.99-48.99c3.905-3.905 10.237-3.905 14.142 0Z"
                                                    fill="currentColor" fill-rule="nonzero"></path>
                                            </svg>
                                            <NuxtLink href="/dashboard" v-if="ruleBaseId === 'all'"
                                                class="ReaderLayout-module_crumbText_l2jpO">返回首页</NuxtLink>
                                            <NuxtLink v-if="ruleBaseId.indexOf('demandUuid') !== -1"
                                                :to="`/ruleAdjustment?backPage=${backPage}`"
                                                class="ReaderLayout-module_crumbText_l2jpO">返回任务</NuxtLink>
                                        </div>
                                        <div class="ReaderLayout-module_asideHead_dVgz0" id="asideHead"
                                            v-if="ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1">
                                            <div class="ReaderLayout-module_bookInfo_FivMr">
                                                <div class="ReaderLayout-module_crumb_wF-5C">
                                                    <NuxtLink href="/dashboard"
                                                        class="ReaderLayout-module_yuqueLogo_KdpAI">
                                                        <IconErule />
                                                    </NuxtLink><svg width="14" height="14" viewBox="0 0 256 256"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="larkui-icon larkui-icon-arrow-left3 ReaderLayout-module_miniDropdown_7uya+">
                                                        <path
                                                            d="M154.995 58.157c3.834 3.834 3.904 10.007.21 13.927l-.21.215-48.99 48.99c-3.834 3.834-3.904 10.007-.21 13.927l.21.215 48.99 48.99c3.905 3.905 3.905 10.237 0 14.142-3.834 3.834-10.008 3.904-13.927.21l-.215-.21-48.99-48.99c-11.599-11.599-11.715-30.331-.348-42.073l.348-.353 48.99-48.99c3.905-3.905 10.237-3.905 14.142 0Z"
                                                            fill="currentColor" fill-rule="nonzero"></path>
                                                    </svg>
                                                    <NuxtLink :to="`/ruleBaseManage?backPage=${backPage}`"
                                                        v-if="ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1"
                                                        class="ReaderLayout-module_crumbText_l2jpO">规则库管理</NuxtLink>
                                                </div>
                                            </div>
                                            <div class="ReaderLayout-module_bookInfo_FivMr">
                                                <div class="ReaderLayout-module_bookName_7LuCC">
                                                    <div tabindex="0" data-testid="popover-bookicon"
                                                        class="ReaderLayout-module_icon_HU4bU"><svg width="1em"
                                                            height="1em" viewBox="0 0 20 20"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            class="larkui-icon larkui-icon-book-type-default icon-svg index-module_size_wVASz"
                                                            data-name="BookTypeDefault">
                                                            <g fill="none" fill-rule="evenodd">
                                                                <path
                                                                    d="M4.75 1.267h10.5a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-14a2 2 0 0 1 2-2Z"
                                                                    fill="#C4DCFF"></path>
                                                                <path
                                                                    d="M4.75 1.267h2.215v18H5.75a3 3 0 0 1-3-3v-13a2 2 0 0 1 2-2Z"
                                                                    fill="#679FF4"></path>
                                                                <path stroke="#397ABD" d="M7.25 1.1v17.667"></path>
                                                                <path stroke="#397ABD" stroke-linecap="round"
                                                                    stroke-linejoin="round" d="M10.85 5.394h3.4"></path>
                                                                <path
                                                                    d="M4.25 1.267h11.5a1.5 1.5 0 0 1 1.5 1.5v14.5a1.5 1.5 0 0 1-1.5 1.5H4.25a1.5 1.5 0 0 1-1.5-1.5v-14.5a1.5 1.5 0 0 1 1.5-1.5Z"
                                                                    stroke="#397ABD"></path>
                                                            </g>
                                                        </svg></div>
                                                    <div class="ReaderLayout-module_bookName_7LuCC"
                                                        style="width: calc(22vh)">
                                                        <span class="ReaderLayout-module_text_3S3RN"
                                                            :title="ruleBaseName">{{ ruleBaseName }}</span>
                                                    </div>

                                                    <span
                                                        class="index-module_more_sDgGm ReaderLayout-module_bookOverviewMoreActions_fKcZR larkui-popover-trigger"
                                                        data-testid="more-actions:Popover:more-actions"
                                                        @click="setAlwaysUsed(ruleBaseName)"
                                                        style="line-height: 16px; padding: 4px;margin-right: 30px;">
                                                        <a-tooltip :title="ifAlwaysUsed ? '取消常用' : '设为常用'">
                                                            <span class="BookList-module_btnPin_WZXvK larkui-tooltip">
                                                                <IconPin v-if="ifAlwaysUsed" />
                                                                <IconPinOutlined v-else />
                                                            </span>
                                                        </a-tooltip>
                                                    </span>

                                                    <span
                                                        class="index-module_more_sDgGm ReaderLayout-module_bookOverviewMoreActions_fKcZR larkui-popover-trigger"
                                                        data-testid="more-actions:Popover:more-actions"
                                                        @click="dropdownClick('create', ruleBaseData)"
                                                        style="line-height: 16px; padding: 4px;">
                                                        <a-tooltip title="新建规则包">
                                                            <span class="BookList-module_btnPin_WZXvK larkui-tooltip">
                                                                <PlusOutlined />

                                                            </span>
                                                        </a-tooltip>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ReaderLayout-module_searchNav_KneVs">
                                        </div>
                                        <NuxtLink class="ReaderLayout-module_nav_k8NJg" style="color: inherit;"
                                            :to="`/ruleBase-${ruleBaseId}?clickKey=${clickKey}`"
                                            @click="clickTreeChildStyle('')"
                                            v-if="ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1"><svg
                                                width="1em" height="1em" viewBox="0 0 256 256"
                                                xmlns="http://www.w3.org/2000/svg"
                                                class="larkui-icon larkui-icon-knowledge-base-home ReaderLayout-module_navIcon_eoaa4">
                                                <path
                                                    d="m139.26 31.243.36.232 76.656 50.543a21.11 21.11 0 0 1 9.486 17.222l.004.401v96.129c0 17.616-14.138 31.93-31.687 32.215l-.532.004h-34.698c-.1 0-.198-.001-.297-.004-.183.01-.367.015-.552.015-5.43 0-9.848-4.327-9.996-9.72L148 218v-47.755c0-11.2-8.973-20.245-20-20.245-10.913 0-19.813 8.858-19.997 19.896l-.003.349V218c0 5.523-4.477 10-10 10-.185 0-.37-.005-.552-.015h-.017l-.28.004H62.453c-17.616 0-31.93-14.138-32.215-31.686l-.004-.533V99.64a21.11 21.11 0 0 1 9.157-17.4l.333-.223 76.656-50.543a21.11 21.11 0 0 1 22.88-.232ZM127.516 48.1l-.127.072-76.656 50.543a1.11 1.11 0 0 0-.49.788l-.009.138v96.129c0 6.646 5.306 12.053 11.913 12.215l.306.004H88L88 170.245C88 148.036 105.89 130 128 130c21.889 0 39.642 17.677 39.995 39.58l.005.665v37.744h25.547c6.646 0 12.053-5.306 12.215-11.913l.004-.306V99.64a1.11 1.11 0 0 0-.388-.842l-.11-.084-76.657-50.543a1.11 1.11 0 0 0-1.095-.072Z"
                                                    fill="currentColor" fill-rule="nonzero"></path>
                                            </svg><span>
                                                规则库首页
                                            </span></NuxtLink>
                                        <nav class="ReaderLayout-module_asideNav_-Kxml" id="navBox"
                                            style="height:100%;will-change: width; transition: width 200ms cubic-bezier(0.1, 0, 0, 1); width: 259px; max-width: 480px; min-width: 259px;">
                                            <a-spin :spinning="isLoading">
                                                <div class="BookCatalog-module_asiderTab_0aJLs">
                                                    <div class="BookCatalog-module_tabBar_BKeYX">
                                                        <a-tabs v-model:activeKey="activeName" @change="tabClick">
                                                            <a-tab-pane tab="规则包" key="first">
                                                            </a-tab-pane>
                                                            <!--<a-tab-pane tab="词汇表" key="second"
                                                                v-if="ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1">
                                                            </a-tab-pane>-->
                                                        </a-tabs>
                                                        <a-button @click="importFun" style="margin-top: 10px"
                                                            v-hasPermi="[RULE_PERMISSION.RULE_BASE.IMPORT]">导入</a-button>

                                                    </div>
                                                    <div class="ReaderLayout-module_searchBar_F9DSq">
                                                        <a-input autocomplete="off" v-model:value="searchValue"
                                                            @change="onSearchChange"
                                                            style="width: 95%; margin-left: 8px;margin-bottom: 5px"
                                                            placeholder="请输入搜索内容" />
                                                    </div>
                                                    <div class="ant-tabs-content-holder">
                                                        <div class="ant-tabs-content ant-tabs-content-top">
                                                            <div role="tabpanel" tabindex="0" aria-hidden="false"
                                                                class="ant-tabs-tabpane ant-tabs-tabpane-active"
                                                                id="rc-tabs-0-panel-Catalog"
                                                                aria-labelledby="rc-tabs-0-tab-Catalog">
                                                                <div style="display: initial;">
                                                                    <div class="BookCatalog-module_sideCatalog_wpCdP">
                                                                        <div
                                                                            style="overflow: visible; height: 0px; width: 0px;">
                                                                            <div class="lark-virtual-tree">
                                                                                <div :style="ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1 ? `height: calc(100vh - 38vh);` : `height: calc(100vh - 25vh);`
                                                                                    + `overflow-y: auto;
                                                                                        border: 1px solid #d9d9d9;`"
                                                                                    v-if="activeName === 'first'">
                                                                                    <a-tree class="rule-tree-class"
                                                                                        v-if="rulePackageTree.length > 0"
                                                                                        :treeData="rulePackageTree"
                                                                                        :loading="isLoading"
                                                                                        v-model:expandedKeys="expandedKeys"
                                                                                        blockNode>
                                                                                        <template #title="node">
                                                                                            <div :class="treeChildClick === node.key ? 'selected-tree-style' : ''"
                                                                                                class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style rule-tree-hover-style">
                                                                                                <div :style="(ruleBaseId !== 'all' && ruleBaseId.indexOf('demandUuid') === -1 && node.folderName !== '工作空间') ? 'width: calc(100% - 9px)' : 'width: calc(100% - 7px)'"
                                                                                                    class="catalogTreeItem-module_content_fLFbS custom-tree-node">
                                                                                                    <a-tooltip
                                                                                                        :title="node.folderName">
                                                                                                        <NuxtLink
                                                                                                            class="catalogTreeItem-module_title_snpKw nuxt-link-style"
                                                                                                            :to="generateLink(node)"
                                                                                                            @click="clickTreeChildStyle(node)">
                                                                                                            {{
                                                                                                            node.folderName
                                                                                                            }}
                                                                                                        </NuxtLink>
                                                                                                    </a-tooltip>
                                                                                                    <a-dropdown
                                                                                                        :trigger="['click']"
                                                                                                        @openChange="openChangeStyle($event, node)"
                                                                                                        v-if="node.folderName !== '工作空间'">
                                                                                                        <div style="float: right;margin-right: 10px"
                                                                                                            :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                                                            class="catalogTreeItem-module_btnItem_HrYZm">
                                                                                                            <span
                                                                                                                class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                                                <IconMore />
                                                                                                            </span>

                                                                                                        </div>
                                                                                                        <template
                                                                                                            #overlay>
                                                                                                            <a-menu>
                                                                                                                <a-menu-item
                                                                                                                    key="1"
                                                                                                                    @click="dropdownClick('create', node)">
                                                                                                                    <IconAdd />
                                                                                                                    新建
                                                                                                                </a-menu-item>
                                                                                                                <div
                                                                                                                    v-show="node.iconCls !== 'ico_rule_project'">
                                                                                                                    <a-menu-item
                                                                                                                        key="2"
                                                                                                                        @click="dropdownClick('update', node)">
                                                                                                                        <IconEdit />
                                                                                                                        编辑
                                                                                                                    </a-menu-item>
                                                                                                                    <a-menu-item
                                                                                                                        key="3"
                                                                                                                        @click="dropdownClick('copy', node)">
                                                                                                                        <IconPencilUnderscore />
                                                                                                                        复制
                                                                                                                    </a-menu-item>
                                                                                                                    <a-menu-item
                                                                                                                        key="4"
                                                                                                                        @click="dropdownClick('delete', node)">
                                                                                                                        <IconDelete />
                                                                                                                        删除
                                                                                                                    </a-menu-item>
                                                                                                                </div>
                                                                                                            </a-menu>
                                                                                                        </template>

                                                                                                    </a-dropdown>
                                                                                                    <a-dropdown
                                                                                                        :trigger="['click']"
                                                                                                        @openChange="openChangeStyle($event, node)"
                                                                                                        v-if="node.folderName !== '工作空间'">
                                                                                                        <div style="float: right;margin-right: 8px"
                                                                                                            :class="buttonShowClick === node.key ? 'show-more-button' : 'hide-more-button'"
                                                                                                            class="catalogTreeItem-module_btnItem_HrYZm">
                                                                                                            <span
                                                                                                                class="catalogTreeItem-module_btnGap_uDkKN ant-popover-open larkui-popover-trigger">
                                                                                                                <PlusOutlined />
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <template
                                                                                                            #overlay>
                                                                                                            <a-menu>
                                                                                                                <a-menu-item
                                                                                                                    v-for="ruleType in ruleTypes"
                                                                                                                    :key="ruleType.code"
                                                                                                                    @click="openAddModal(ruleType.code, node)">
                                                                                                                    <a
                                                                                                                        class="HeadNewButton-module_menuItemContainer_VLntH">
                                                                                                                        <div
                                                                                                                            class="HeadNewButton-module_iconWarp_o9rNt">
                                                                                                                            <component
                                                                                                                                :is="ruleType.icon"
                                                                                                                                :size="18"
                                                                                                                                class="HeadNewButton-module_iconContainer_HmX2B" />
                                                                                                                        </div>
                                                                                                                        <span
                                                                                                                            style="color:black">{{
                                                                                                                            ruleType.name
                                                                                                                            }}</span>
                                                                                                                    </a>
                                                                                                                </a-menu-item>
                                                                                                            </a-menu>
                                                                                                        </template>
                                                                                                    </a-dropdown>
                                                                                                </div>


                                                                                            </div>

                                                                                        </template>
                                                                                        <template
                                                                                            #switcherIcon="{ switcherCls }">
                                                                                            <div :class="switcherCls"
                                                                                                class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                                                <div
                                                                                                    class="catalogTreeItem-module_collapseIconWrapper_XcS8B">
                                                                                                    <IconMiniDropdown />
                                                                                                </div>
                                                                                            </div>
                                                                                        </template>
                                                                                    </a-tree>
                                                                                </div>

                                                                                <a-tabs type="card"
                                                                                    v-model:activeKey="glossary"
                                                                                    @change="tabClick"
                                                                                    v-if="activeName === 'second'">
                                                                                    <a-tab-pane tab="模型" key="model">
                                                                                        <div class="lark-virtual-tree">
                                                                                            <div class="tree-y-class">
                                                                                                <a-tree
                                                                                                    class="rule-tree-class"
                                                                                                    :treeData="modelTree"
                                                                                                    :loading="isLoading"
                                                                                                    blockNode>
                                                                                                    <template
                                                                                                        #title="{ viewName }">
                                                                                                        <div
                                                                                                            class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style">
                                                                                                            <div
                                                                                                                class="catalogTreeItem-module_content_fLFbS custom-tree-node2">
                                                                                                                <a-tooltip
                                                                                                                    :title="viewName">
                                                                                                                    <span
                                                                                                                        class="catalogTreeItem-module_title_snpKw nuxt-link-style ">
                                                                                                                        {{
                                                                                                                            viewName
                                                                                                                        }}
                                                                                                                    </span>
                                                                                                                </a-tooltip>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </template>
                                                                                                    <template
                                                                                                        #switcherIcon="{ switcherCls }">
                                                                                                        <div :class="switcherCls"
                                                                                                            class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                                                            <div
                                                                                                                class="catalogTreeItem-module_collapseIconWrapper_XcS8B">
                                                                                                                <IconMiniDropdown />
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </template>
                                                                                                </a-tree>

                                                                                            </div>
                                                                                        </div>
                                                                                    </a-tab-pane>
                                                                                    <a-tab-pane tab="数据字典"
                                                                                        key="dataDict">
                                                                                        <div class="lark-virtual-tree">
                                                                                            <div class="tree-y-class">
                                                                                                <a-tree
                                                                                                    class="rule-tree-class"
                                                                                                    :treeData="dictTree"
                                                                                                    :loading="isLoading"
                                                                                                    blockNode>
                                                                                                    <template
                                                                                                        #title="{ viewName }">
                                                                                                        <div
                                                                                                            class="catalogTreeItem-module_CatalogItem_xkX7p custom-tree-style">
                                                                                                            <div
                                                                                                                class="catalogTreeItem-module_content_fLFbS custom-tree-node2">
                                                                                                                <a-tooltip
                                                                                                                    :title="viewName">
                                                                                                                    <span
                                                                                                                        class="catalogTreeItem-module_title_snpKw nuxt-link-style">
                                                                                                                        {{
                                                                                                                            viewName
                                                                                                                        }}
                                                                                                                    </span>
                                                                                                                </a-tooltip>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </template>
                                                                                                    <template
                                                                                                        #switcherIcon="{ switcherCls }">
                                                                                                        <div :class="switcherCls"
                                                                                                            class="catalogTreeItem-module_hasChildren_TrI8X">
                                                                                                            <div
                                                                                                                class="catalogTreeItem-module_collapseIconWrapper_XcS8B">
                                                                                                                <IconMiniDropdown />
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </template>
                                                                                                </a-tree>
                                                                                            </div>
                                                                                        </div>
                                                                                    </a-tab-pane>
                                                                                </a-tabs>



                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div role="tabpanel" tabindex="-1" aria-hidden="true"
                                                                class="ant-tabs-tabpane" id="rc-tabs-0-panel-All_Docs"
                                                                aria-labelledby="rc-tabs-0-tab-All_Docs"
                                                                style="display: none;"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a-spin>

                                        </nav>
                                    </div>
                                    <div class="ReaderLayout-module_dragbar_NUlrA"></div>
                                    <!--                                                                            <div class="ReaderLayout-module_pinWrapper_8A7Ri">-->
                                    <!--                                                                                <span-->
                                    <!--                                                                                    class="ReaderLayout-module_pinIconWrapper_WglAw larkui-tooltip"><span-->
                                    <!--                                                                                    class="ReaderLayout-module_arrowRight_xXebO"></span></span>-->
                                    <!--                                                                            </div>-->
                                </div>
                                <div id="sidePanel" class="sidePanel-module_panel_Vr-DC" style="right: 0px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <a-modal v-if="dialogUploadVisible" :visible="dialogUploadVisible" title="导入到当前规则库中" @ok="dialogUploadSubmit"
        :confirmLoading="dialogUploadLoading" @cancel="dialogUploadVisible = false">
        <a-form class="dialogForm">
            <a-form-item name="choiceFile">
                <FileUpload :accept="'.erule'" @uploadFile="uploadFile" :uploadHidden="uploadHidden" />
            </a-form-item>
        </a-form>
    </a-modal>
    <a-modal v-if="data.dialogFormVisible" :title="data.dialogFormTitle" :visible="data.dialogFormVisible"
        @ok="saveOrUpdate" @cancel="data.dialogFormVisible = false" :confirmLoading="data.submitLoading">
        <a-form :model="data.form" :rules="data.rules" ref="dialoForm" label-align="right" :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18 }">
            <a-form-item label="规则包名称" name="ruleBaseName">
                <a-input autocomplete="off" v-model:value="data.form.ruleBaseName"></a-input>
            </a-form-item>
            <a-form-item label="上级规则包" v-show="data.copyDomShow">
                <a-tree-select v-model:value="data.form.superiorRuleBase" show-search tree-node-filter-prop="title"
                    :tree-data="data.ruleTreeOptions" placeholder="请输入查询内容" @select="ruleNodeClick">
                </a-tree-select>
            </a-form-item>
            <a-form-item label="上级规则包" v-show="!data.copyDomShow">
                <span>{{ data.form.superiorRuleBase || '无' }}</span>
            </a-form-item>
            <a-form-item label="规则包描述">
                <a-input autocomplete="off" v-model:value="data.form.ruleBaseDesc"></a-input>
            </a-form-item>
        </a-form>
    </a-modal>
    <a-modal v-if="isModalVisible" title="新增规则" :visible="isModalVisible" @ok="handleModalOk"
        @cancel="isModalVisible = false" :width="600">
        <div style="max-height: 60vh; overflow-y: auto;">
            <RuleAdd ref="ruleAddComponent" :ruleBaseId="ruleBaseAddId" :rulePackageId="rulePackageAddId" />
        </div>
    </a-modal>
</template>
<style lang="scss" scoped>
.custom-tree-node {
    text-align: left;

    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: auto;
    padding: 4px 26px 4px 2px;
    border-radius: 4px;
    display: flex;
}

.custom-tree-node2 {
    width: 200px;
    white-space: nowrap;
    /* 确保文本不换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 使用省略号表示超出的文本 */
}

.nuxt-link-style {
    color: var(--yq-text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    /* 使用省略号表示超出的文本 */
}

.tree-y-class {
    height: calc(100vh - 38vh);
    overflow-y: auto;
    border: 1px solid #d9d9d9;
}

.lark-virtual-tree {
    position: relative;
    width: 300px;
    overflow: auto;
}

.catalogTreeItem-module_CatalogItem_xkX7p {
    padding: 0 8px 0 0
}

.catalogTreeItem-module_hasChildren_TrI8X {
    line-height: 26px;
    width: 24px;
    margin: 6px 2px 5px 6px;
}

.catalogTreeItem-module_collapseIconWrapper_XcS8B {}

.catalogTreeItem-module_hasChildren_TrI8X :hover {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 4px;
}

.catalogTreeItem-module_btnItem_HrYZm {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 14px;
    color: var(--yq-text-primary);
    transition: background .35s ease-in-out;
    border-radius: 6px;
}

.catalogTreeItem-module_btnItem_HrYZm:hover {
    background-color: var(--yq-yuque-grey-4)
}

.rule-tree-hover-style :hover .catalogTreeItem-module_btnItem_HrYZm {
    display: flex;
}

.catalogTreeItem-module_btnItem_HrYZm {
    display: none;
}

/**选中字体加粗*/
:deep(.rule-tree-class) {
    .selected-tree-style {
        font-weight: 700;
        background-color: var(--yq-bg-primary-hover);
    }

    .ant-tree-switcher {
        width: 30px;
    }

    /**内容过多时隐藏*/
    .ant-tree-node-content-wrapper {
        padding: 0;
        width: 90%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .show-more-button {
        display: flex;
    }

    .hide-more-button {
        display: none;
    }

    /* 覆盖选中节点的样式 */
    .ant-tree-node-selected {
        background: transparent !important;
        color: inherit !important;
    }
}
</style>
<style lang="scss">
.upload-file-eruleFile-style .ant-upload .ant-upload-btn {
    display: none;
}

.upload-file-eruleFile-styleShow .ant-upload .ant-upload-btn {
    display: block;
}

.form_flex_div_item {
    margin-bottom: 5px;
}

.upload-file-eruleFile-style .ant-upload .ant-upload-btn {
    display: none;
}

.upload-file-eruleFile-styleShow .ant-upload .ant-upload-btn {
    display: block;
}

.form_flex_div_item {
    margin-bottom: 5px;
}

/* 隐藏拖动条的可拖动图标 */
.ReaderLayout-module_dragbar_NUlrA {
    cursor: default !important;
}

.ReaderLayout-module_dragbar_NUlrA:hover {
    cursor: default !important;
    background-color: transparent !important;
}
</style>
