<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
    modelValue: string[];
    notifyConfigList: any[];
    placeholder?: string;
    autoSelect?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择通知用户',
    autoSelect: true
});

const emit = defineEmits(['update:modelValue']);

const selectedValue = ref<string[]>(props.modelValue);

watch(() => props.modelValue, (newVal) => {
    selectedValue.value = newVal;
});

watch(selectedValue, (newVal) => {
    emit('update:modelValue', newVal);
});
</script>

<template>
    <a-select
        v-model:value="selectedValue"
        mode="multiple"
        :placeholder="placeholder"
        style="width: 100%"
        show-search
        optionFilterProp="label"
    >
        <a-select-option
            v-for="item in notifyConfigList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
        >
            {{ item.name }}
        </a-select-option>
    </a-select>
</template> 