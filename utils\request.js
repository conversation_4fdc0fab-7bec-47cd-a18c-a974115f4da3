/**
 * @file 数据请求方法
 */

import axios from 'axios'
import store from '@/store'
import defaultSettings from '@/settings'
import globalEventEmitter from '@/utils/eventBus'
import { SHOW_MESSAGE,SHOW_MODAL} from '@/consts/globalEventConsts';
import { debounce } from 'lodash'

let requestFlag = null
let parseExcelUrl=false
// let loadingInstance = null

// create an axios instance
const service = axios.create({
  baseURL: defaultSettings.VUE_APP_BASE_API,
  // withCredentials: true, // send cookies when cross-domain requests
  // maxBodyLength: 1000000,
  timeout: 480000 // request timeout
})
// request interceptor
service.interceptors.request.use(
  config => {

    // loadingInstance = Loading.service({
    // lock: true,
    // background: 'rgba(0, 0, 0, 0.7)'
    // })

    // do something before request is sent
    if (config.headers && config.headers.response) {
      requestFlag = true
    } else {
      requestFlag = false
    }
    if(config.url.indexOf('erule/rule/parseExcel')>-1){
      parseExcelUrl = true
    }else{
      parseExcelUrl = false
    }
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 使用防抖包装错误提示
const showErrorMessageDebounced = debounce((type, content, duration) => {
  globalEventEmitter.emit(SHOW_MESSAGE, {
    type,
    content,
    duration
  })
}, 300)

// 使用防抖包装session过期提示
const showSessionModalebounced = debounce(() => {
  globalEventEmitter.emit(SHOW_MODAL, {
    type: 'confirm',
    config: {
      content: 'session已过期，请重新登录',
      title: '提示',
      okText: '确定',
      cancelButtonProps: null,
      okCancel: false,
      onOk: () => {
        logout()
      }
    }
  })
}, 300)
// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    // loadingInstance.close();
    // if the custom code is not 20000, it is judged as an error.
    if (res && res.code && res.code !== 20000 && res.code !== 200 && res.code !== 50002 && (response.data && response.data.toString() !== '[object ArrayBuffer]')) {

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      // 50008:非法令牌50012:其他客户端登录50014:令牌过期
      // || res.code === 50012 || res.code === 50014
      if (res && res.code && res.code === 50010) {
        showSessionModalebounced();
      }
      // else if (res.code == 60000) {
      //   navigateTo("/login")
      // }
      else if( parseExcelUrl && requestFlag ) {
        return response
      } else {
        //登录失败后返回登录页面
        if(res.data === '用户未登录，请刷新重试'){
          showSessionModalebounced();
        }else{
          globalEventEmitter.emit(SHOW_MESSAGE, {
            type: 'error',
            content: res.data || 'Error',
            duration: 3
          })
        }
      }
      return Promise.reject('error')
    } else {
      if (requestFlag) {
        return response
      } else {
        return res
      }
    }
  },
  error => {
    var eMessage = error.message;
    var eCode = '';
    if (error?.response?.data?.data) {
      eMessage = error.response.data.data;
      eCode = error.response.data.code;
    }
    if (eCode && eCode === 50010) {
      showSessionModalebounced();
    }else{
      // 显示错误信息
      showErrorMessageDebounced('error', eMessage, 3)
    }


    // loadingInstance.close();
    return Promise.reject(error)
  }
)

export default service
