<template>
  <div class="ReaderLayout-module_wrapper_l-8F+" style="padding-left: 276px;"><div id="headerOverlayContainer" class="ViewerHeader-module_headerOverlayContainer_30mty"></div><div class="BookReader-module_wrapper_s6Jdt BookReader-module_docTypographyClassic_tUB5r" data-testid="content"><div class="BookReader-module_content_BGKYX" id="main"><div class="BookReader-module_docContainer_mQ3Tk"><div class="DocReader-module_wrapper_t3Z8X" data-doc-layout="fixed" data-doc-sidebar="false" data-doc-toc="true"><div class=""><div id="doc-reader-content" class="DocReader-module_content_AcIMy "><div class="DocReader-module_header_xAOtU"><div><div class="DocReader-module_title_fXOQi"><h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title">产品简介</h1></div></div></div><div><article id="content" class="article-content" tabindex="0" style="outline-style: none;"><div class="ne-doc-major-viewer"><div class="yuque-doc-content" data-df="lake" style="position: relative;"><div><div class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed" data-viewer-mode="normal" id="u86b3"><div class="ne-viewer-header"><button type="button" class="ne-ui-exit-max-view-btn" style="background-image:url(https://gw.alipayobjects.com/zos/bmw-prod/09ca6e30-fd03-49ff-b2fb-15a2fbd8042a.svg)">返回文档</button></div><div class="ne-viewer-body"><ne-p id="u40df89f6" data-lake-id="u40df89f6" ne-alignment="justify"><ne-text id="uc45be1e4">eRule规则管理平台系统V3.0是由北京天瑞兴隆科技发展有限公司自主研发的一款业务规则管理软件系统，在V1.0版本、V1.9版本、V2.0版本基础上进行了全面的技术升级，使得该产品的易用性、可靠性、兼容性、安全性都得到提升。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="uea7c91bf" data-lake-id="uea7c91bf" ne-alignment="justify"><ne-text id="u1b441d3d">业务规则管理用户通过浏览器访问eRuleV3.0规则管理服务组件，进行日常业务规则管理和维护，将维护好的规则库热部署到规则执行服务组件，实现业务规则的实时运行，响应外部系统的调用请求。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h3 id="95423b35" data-lake-id="95423b35"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h3" data-name="card-h3"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h3"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u9a1351e1" ne-bold="true">强大的功能</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h3><ne-p id="ube189a3f" data-lake-id="ube189a3f"><ne-text id="u3c48ef2c">在eRule 当中，提供规则集、决策表、决策树、规则流等几种类型的业务规则设计工具，从各个角度满足复杂业务规则设计的需要。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u800b4d86" data-lake-id="u800b4d86"><ne-text id="uf3d6f397">如果我们的业务给出的是零散的逻辑规则，那么可以使用规则集来实现；如果给出的是表格形式的业务规则，那么可以直接使用对应的决策表来实现；最后还可以通过规则流对一系列复杂的规则个体进行编排，将这个规则流作为实际业务规则调用入口，从而实现任意复杂的业务规则。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="uef043a00" data-lake-id="uef043a00"><ne-text id="u328a0263">规则文件的部署，可以直接实现热部署，系统不重启即可实现所有与规则相关的业务需求变更。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h3 id="f2770ee3" data-lake-id="f2770ee3"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h3" data-name="card-h3"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h3"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u5f15b77c">简单的使用方式</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h3><ne-p id="u2a056015" data-lake-id="u2a056015"><ne-text id="u62d3e4a3">eRule中提供的所有的规则设计器及打包测试工具，全部基于浏览器实现，所有的规则设计器皆为可视化、图形化设计器，通过鼠标点击即可实现复杂的业务规则定义，eRule 中规则的多条件组合也是以图形方式展现，这样即使没有任何编程经验的普通业务人员，也可以轻松上手，完成复杂业务规则的定义。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ucb12cfce" data-lake-id="ucb12cfce"><ne-text id="u8eedc658">因为所有的业务规则设计器都是基于网页的，且规则的定义都是通过鼠标点击的方式完成，所以对于一个普通的使用者来说，两到三天即可完全掌握eRule 中各种设计器的使用，结合业务需要定义出想要的业务规则。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h3 id="f725aa59" data-lake-id="f725aa59"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h3" data-name="card-h3"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h3"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u53169190">优秀的性能</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h3><ne-p id="ua24239b2" data-lake-id="ua24239b2"><ne-text id="u7b2f4580">eRule后台采用纯Java实现，运行时借鉴Rete了算法的优势，再结合中式规则引擎的特点，独创了一套自己的规则模式匹配算法，这套算法可以从根本上保证规则运行的效率，实现大量复杂业务规则计算时的毫秒级响应。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h3 id="ae15a99c" data-lake-id="ae15a99c"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h3" data-name="card-h3"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h3"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u43b0378f">完善的版本控制机制</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h3><ne-p id="u02bdb61b" data-lake-id="u02bdb61b"><ne-text id="u67fbd98e">在eRule 当中，无论是单个规则文件、或是用户调用的规则包，都提供了完善的版本控制机制。对于规则文件来说只要有需要，可以回退到任何一个历史版本；对于给用户调用的规则包，可以在不同的历史版本之间灵活切换。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h3 id="4c4b4f89" data-lake-id="4c4b4f89"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h3" data-name="card-h3"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h3"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><ne-text id="u9f6e3dd1">产品优势</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h3><ne-p id="uce475a26" data-lake-id="uce475a26"><ne-text id="u18195b2d" style="color: rgb(51, 51, 51);">灵活和完整性：erule</ne-text><ne-text id="u8b70521b" style="color: rgb(18, 17, 39);">支持普通规则、决策表、决策树、决策流等录入方式，涵盖从简单条件语句到复杂业务逻辑的全面应用场景。 同时支持文本编辑和点选式两种规则编辑模式，满足不同特征的客户需求。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u77442685" data-lake-id="u77442685"><ne-text id="ue50b71b0" style="color: rgb(51, 51, 51);">高度迁移：</ne-text><ne-text id="u7800312b" style="color: rgb(18, 17, 39);">高度实现IBM ODM产品（原ILOG）的一键迁移和对Drools产品的兼容。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u8b8b9c33" data-lake-id="u8b8b9c33"><ne-text id="uc6f33609" style="color: rgb(51, 51, 51);">可视化规则：</ne-text><ne-text id="ud10c3e7e" style="color: rgb(18, 17, 39);">基于业务语言的规则可视化不只是看见，更在于易于理解。业务人员可以根据业务术语、用语灵活地制定业务规则。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="u04ecb1d3" data-lake-id="u04ecb1d3"><ne-text id="ue29fb113" style="color: rgb(51, 51, 51);">性能和稳定性：e</ne-text><ne-text id="u7524493a" style="color: rgb(18, 17, 39);">Rule支持高并发、高可用、高效率要求的业务场景。经过严谨的压力测试，erule可支持的吞吐量远高于同类其他产品。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h2 id="plg5S" data-lake-id="plg5S"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h2" data-name="card-h2"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h2"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h2></div><div class="ne-inner-overlay-container"><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay"><div class="count-tips-module_tipsContainer___5agc"></div></ne-overlay-tmp></div></div><div style="height: 0px; overflow: hidden;">​</div></div></div></div></article></div></div><div ne-viewer-toc-pin="true"></div><div></div></div><div></div></div></div></div></div></div>
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>