<!-- 规则管理新增页 -->

<script setup lang="ts">
import {
    getAllBusinessByLoginUser,
    getDicValueByTypeCode,
    checkJarName,
    ruleBomModelConvertor,
    treeList,
    domainTreeList,
    submitTree,
    getRuleBomArgList,
    updateJarFile
} from "@/api/rule_model";
import ModelParamsList from "@/businessComponents/ruleModelManage/ModelParamsList.vue";
const message = inject('message')
//字典值
const businessLineOptions = reactive<Record<string, any>>({});
const modelTypeOptions = reactive<Record<string, any>>({});

//组件挂载后
onMounted(() => {
    // 业务条线
    getAllBusinessByLoginUser().then((res) => {
        Object.assign(businessLineOptions, res.data);
    });
    //模型类型
    getDicValue("bomModelType", (arr) => {
        Object.assign(modelTypeOptions, arr);
    });
});

const getDicValue = (type, fn) =>{
    getDicValueByTypeCode({
        typeCode: type,
    }).then((res) => {
        fn && fn(res.data);
    });
}
const form = ref({
    businessLine: [],
        modelName: "",
        modelType: [],
        desc: "",
})
//表单校验
const rules= {
    modelName: [{ required: true, message: "不能为空", trigger: "blur" }],
        businessLine: [
        { required: true, message: "不能为空", trigger: "blur" },
    ],
        modelType: [{ required: true, message: "不能为空", trigger: "blur" }],
        choiceFile: [
        {
            required: true,
            validator: (rule, value, callback) => {
                if (data.value.uploadHidden) {
                    callback();
                } else {
                    callback(new Error("不能为空"));
                }
            },
            trigger: "blur",
        },
    ],
    desc: [
        {
            required: true,
            validator: (rule, value, callback) => {
                if(value.length>60){
                    callback(new Error("长度在 1 到 60 个字符"));
                    return;
                }
                if (value) {
                    callback();
                }else {
                    callback(new Error("不能为空"));
                }
            },
            trigger: "blur",
        },
    ],
};

//其他参数
const data = ref({
    uploadHidden: false,
    checkedDomainKeysIsChange:false,
    checkedModelKeysIsChange:false,
    ruleArgDirect: {},
    paramsList: [],
    modelAttTreedata: [],
    modelAttTreeChecked: [],
    modelAttTreeHalfChecked:[],
    ModelAttexpandAll: false,
    domainTreedata: [],
    domainTreeChecked: [],
    domainTreeHalfChecked:[],
    domainexpandAll: false,
    jar: "",
    uuid: "",
    isLoading:false,
    expandedKeys:[0],
    defaultProps: {
        children: "children",
        label: (data, node) => {
            return data;
        },
    },

})
// 基础路径
const baseUrl = process.env.BASE_URL;
const ruleForm = ref(null);

const onCheckModel=(checkedKeys, e)=> {
    data.value.modelAttTreeChecked = checkedKeys;//选择部分
    data.value.checkedModelKeysIsChange = true;
    data.value.modelAttTreeHalfChecked =e.halfCheckedKeys;//半选部分

}
const onCheckDomain=(checkedKeys, e)=> {
    data.value.domainTreeChecked = checkedKeys;//选择部分
    data.value.checkedDomainKeysIsChange = true;
    data.value.domainTreeHalfChecked =e.halfCheckedKeys;//半选部分

}
const submitFun=(tree, formName,callback)=> {
    const treeData = tree === 'domainTree'?data.value.domainTreedata:data.value.modelAttTreedata;
    const treeCheckedData = tree === 'domainTree'?data.value.domainTreeChecked:data.value.modelAttTreeChecked;
    const treeHalfCheckedData = tree === 'domainTree'?data.value.domainTreeHalfChecked:data.value.modelAttTreeHalfChecked;

    // 检查是否有必要的数据
    if (!data.value.jar) {
        message.error("请先上传jar文件");
        return;
    }

    if (!treeData || treeData.length === 0) {
        message.error("模型数据尚未加载完成，请稍候再试");
        return;
    }

    ruleForm.value.validate().then((valid) => {
        if (valid) {
            //修改bom字段
            let allMap = [];

            for(let i = 0;i<treeData[0].children.length;i++){
                let jarClass = treeData[0].children[i];
                if(formData.value.changeList.includes(jarClass.id)){
                    allMap.push(jarClass)
                }
            }
            //修改jar包
            updateJarFile({
                'jarPath': data.value.jar,
                'allMap': allMap,
            }).then((res) => {
                let halfCheckedArr = [];
                //获取半选中父节点
                function formatData(data1) {
                    data1.forEach(res => {
                        /* if (res.id === 'root') {
                             treeCheckedData.push(res.key)
                         }*/
                        if (res.children) {
                            let checked = res.children.find(item => treeCheckedData.indexOf(item.key) !== -1)
                            if (checked && halfCheckedArr.indexOf(res.key) === -1) {//如果存在子节点选中情况，父节点跟着选中
                                halfCheckedArr.push(res.key)
                            }
                            formatData(res.children)
                        }
                    })
                }
                data.value.isLoading = true;
                formatData(treeData);
                const checkedIdArr = treeCheckedData.concat(halfCheckedArr);
                let allNodeData = [];
                let allNodeData2 = [];
                let unCheckedNodes = [];
                let checkedNodes = [];
                if (checkedIdArr && checkedIdArr.length) {
                    const allNode = treeData;

                    function formatDataTree(data1) {
                        data1.forEach(res => {
                            if (checkedIdArr.indexOf(res.key) !== -1 && res.id !== 'root') {
                                checkedNodes.push(res);
                            } else if (res.id !== 'root') {
                                unCheckedNodes.push(res);
                            }
                            if (res.children) {
                                formatDataTree(res.children)
                            }
                        })
                    }

                    formatDataTree(treeData);
                    if (tree === 'domainTree') {
                        for (let i = 0; i < formData.value.changeDelList.length; i++) {
                            unCheckedNodes.push(formData.value.changeDelList[i])
                        }
                        formData.value.changeDelList = [];
                    }
                    //checkedNodes.shift();
                    let ruleBomModelParamMapQO = {};
                    let checkedMap = {};
                    let unCheckedMap = {};
                    let j = {};
                    checkedNodes.filter((item) => {
                        let tag = JSON.parse(item.attributes).tag;
                        if (tag === "ClassMethod") {
                            if (j[[item.id]]) {
                                j[[item.id]].nodeList.push({
                                    attributes: item.attributes,
                                    label: JSON.parse(item.attributes).label,
                                });
                            } else {
                                j[[item.id]] = {
                                    nodeList: [
                                        {
                                            attributes: item.attributes,
                                            label: JSON.parse(item.attributes).label,
                                        },
                                    ],
                                };
                            }
                        } else {
                            j[[item.id]] = {
                                attributes: item.attributes,
                                label: JSON.parse(item.attributes).label,
                            };
                        }
                    });
                    checkedMap = Object.assign(checkedMap, j);
                    // return
                    ruleBomModelParamMapQO.checkedMap = checkedMap;
                    let m = {};
                    unCheckedNodes.filter((item) => {
                        let tag = JSON.parse(item.attributes).tag;
                        if (tag === "ClassMethod") {
                            if (m[[item.id]]) {
                                m[[item.id]].nodeList.push({
                                    attributes: item.attributes,
                                    label: JSON.parse(item.attributes).label,
                                });
                            } else {
                                m[[item.id]] = {
                                    nodeList: [
                                        {
                                            attributes: item.attributes,
                                            label: JSON.parse(item.attributes).label,
                                        },
                                    ],
                                };
                            }
                        } else {
                            m[[item.id]] = {
                                attributes: item.attributes,
                                label: JSON.parse(item.attributes).label,
                            };
                        }
                        unCheckedMap = Object.assign(unCheckedMap, m);
                    });
                    ruleBomModelParamMapQO.unCheckedMap = unCheckedMap;
                    submitTree({
                        jarPath: data.value.jar,
                        modelName: form.value.modelName,
                        bomModelType: form.value.modelType,
                        businessLine: form.value.businessLine.toString(),
                        desc: form.value.desc,
                        checkedMap: checkedMap,
                        unCheckedMap: unCheckedMap,
                        uuid: data.value.uuid,
                    }).then((res) => {
                        if (res.data.jsonMsg.type === "2") {
                            message.success("上传成功")
                            data.value.uuid = res.data.uuid;
                            // 参数列表
                            getParams();
                            data.value.isLoading = false;
                            back();
                        } else {
                            message.error("错误！");
                        }
                    });
                } else {
                    message.error("请勾选属性再提交！");
                    data.value.isLoading = false;
                }
            })
        } else {
            //console.log("error submit!!");
            return false;
        }
    }).catch((error) => {
        message.error("请填写基本信息");
        console.log(error);
    });
};
const back=()=>{
    navigateTo("/ruleModelManage");
}




// 参数列表
const getParams=()=>{
    getRuleBomArgList({
        uuid: data.value.uuid,
    }).then((res) => {
        // 参数方向
        paramDirection(res.data.ruleArgDirectList);
        // 参数列表
        data.value.paramsList = res.data.ruleBomArgList;
    });
}
    // 参数方向
const paramDirection=(arr)=>{
    let j = {};
    arr.filter((i) => {
        j = {
            [i.code]: i.name,
        };
        data.value.ruleArgDirect = Object.assign(data.value.ruleArgDirect, j);
        return true;
    });
};

//文件处理
//定义页面ref参数
const upload = ref(null);
const setTreeList=(params)=> {
    let startIndex = 0;
    _setAttirbute(params);
    function _setAttirbute(item) {
        item.filter((v) => {
            v.indexId = startIndex;
            startIndex += 1;
            if (v.children && v.children.length) {
                _setAttirbute(v.children);
            }
        });
    }
    return params;
};
const uploadFile=async(params)=> {
    const file = params.file,
        name = file.name.substring(0, file.name.indexOf("."));
    try {
        // 检查jar包是否存在
        await checkJarName({
            jarName: name,
            uuid: "",
        });

        const form1 = new FormData();
        form1.append("uuid", "");
        form1.append("bomModel", file);
        form1.append("bomModelName", file.name);
        function formatData(data1){
            data1.forEach(res => {
                res.key = res.indexId;
                res.title = res.text;
                if (res.children) {
                    formatData(res.children)
                }
            })
        }
        // 解析jar包内容获取信息
        const res = await ruleBomModelConvertor(form1);
        // 参数方向
        paramDirection(res.data.ruleArgDirectList);
        // 参数列表
        data.value.paramsList = res.data.ruleBomArgList;
        data.value.jar = res.data.jarPath;

        // 模型属性
        const treeRes = await treeList({
            jarPath: res.data.jarPath,
            modelName: form.value.modelName,
            uuid: "",
        });
        let modelAttTreedata = setTreeList(treeRes.data);
        formatData(modelAttTreedata);
        data.value.modelAttTreedata = modelAttTreedata;

        // 字典数据
        const domainRes = await domainTreeList({
            jarPath: res.data.jarPath,
            modelName: form.value.modelName,
            uuid: "",
        });
        let treeData = setTreeList(domainRes.data);
        formatData(treeData);
        data.value.domainTreedata = treeData;

        // 设置上传成功标志
        data.value.uploadHidden = true;
        params.onSuccess({'code':200}, file);
    } catch (error) {
        //message.error("文件上传失败：" + (error.message || "未知错误"));
        params.onError(error);
    }
};
// 模型属性checked
const currentModelAttTreeChecked=(data, currentChecked)=> {
};
// 字典数据checked
const currentdomainTreeChecked=()=> {};



//编辑bom所需参数
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined,EditOutlined,PlusOutlined,DeleteOutlined } from '@ant-design/icons-vue';
definePageMeta({
    title: '新增模型',
    path:'/ruleModelManage/ruleModelAdd'
})

const formData = ref({
    editformRule: {
        name: [
            { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        value: [
            { required: true, message: "值不能为空", trigger: "blur" }
        ]
    },
    editform: {
        id:'',
        indexId:0,
        name:'',
        value:'',
        checked:false,
        parent:{},
        key:0,
        title:''
    },
    openEditform:false,
    changeList:[],
    changeDelList:[]//记录删除的list
})

const cancelback = () =>{
    formData.value.editform = {
        id:'',
        indexId:0,
        name:'',
        value:'',
        checked:false,
        parent:[],
        key:0,
        title:''

    };
    formData.value.openEditform = false;
}

function selectParentData (key,data1){
    for (let i = 0; i < data1.length; i++) {
        let res = data1[i];
        if(res.id === 'root'){
            selectParentData(key, res.children)
        }
        if (res.children) {
            parent = res.children.find(item => item.key === key)
            if (parent) {
                formData.value.editform.parent = res;
                break;
            }
        }
    }
}
// 点击编辑按钮修改
const editCateName=(editData)=> {

    formData.value.editform.name = JSON.parse(editData.attributes).label;
    formData.value.editform.value = JSON.parse(editData.attributes).value;
    formData.value.editform.indexId = editData.indexId;

    selectParentData(editData.key,data.value.domainTreedata);
    formData.value.editform.id = editData.id;//预设id值
    formData.value.editform.checked = editData.checked;
    formData.value.openEditform = true;
}
const appendTreeNode = (data) => {

    let indexId = 0;
    let id = '';
    for(let i =0;i<data.children.length;i++){
        if(indexId<data.children[i].indexId){
            indexId = data.children[i].indexId;
        }
        id = data.children[i].id;
    }
    indexId += 1;
    formData.value.editform.id = id;//预设id值
    formData.value.editform.indexId = indexId;
    formData.value.editform.parent = data
    formData.value.openEditform = true;
}
const modal = inject('modal');
const removeTreeNode = (editData) => {
    modal.confirm({
        title: () => "删除字典",
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () => "是否删除名称为 " + JSON.parse(editData.attributes).label + " 的字典项?",
        okText: () => '确定',
        okType: 'danger',
        cancelText: () => '取消',
        onOk() {
            formData.value.changeDelList.push(editData);
            selectParentData(editData.key,data.value.domainTreedata);
            let parent = formData.value.editform.parent;
            const children = parent.children || parent;
            const index = children.findIndex(d => d.id === editData.id);
            children.splice(index, 1);
            formData.value.changeList.push(parent.id);
        },
        onCancel() {
        },
    })
}
const submitFormEdit = () => {
    const parent = formData.value.editform.parent;
    const children = parent.children || parent;
    const newChild = {
        "id": formData.value.editform.id.split('_')[0]+'_'+formData.value.editform.value,
        "text": formData.value.editform.name+'(码值：'+formData.value.editform.value+')',
        "state": "open",
        "checked": formData.value.editform.checked,
        "attributes": "{\"label\":\""+formData.value.editform.name+"\",\"tag\":\"DomainAttribute\",\"value\":\""+formData.value.editform.value+"\"}",
        "children": null,
        "iconCls": "ico-baiyuanquan",
        "indexId":formData.value.editform.indexId,
        "key":formData.value.editform.indexId+'-1',
        "title": formData.value.editform.name+'(码值：'+formData.value.editform.value+')',
    };
    const index = children.findIndex(d => d.indexId === newChild.indexId);
    for(let i = 0;i<children.length;i++){
        if(index !== -1){
            if(i !== index && (JSON.parse(children[i].attributes).label === formData.value.editform.name || JSON.parse(children[i].attributes).value === formData.value.editform.value)){
                message.error("名称或值在该类中已存在，请修改后重试！");
                return;
            }
        }else if(JSON.parse(children[i].attributes).label === formData.value.editform.name || JSON.parse(children[i].attributes).value === formData.value.editform.value){
            message.error("名称或值在该类中已存在，请修改后重试！");
            return;
        }
    }

    //编辑状态移除旧字段
    if(index !== -1){
        let changeIndex = formData.value.changeDelList.findIndex(d => d.id === newChild.id);
        if(changeIndex !== -1){//如果id相同则取消移除
            formData.value.changeDelList.splice(changeIndex,1);
        }
        formData.value.changeDelList.push(children[index]);
        children.splice(index, 1);
    }
    //添加新字段
    function formatData(key,data1) {
        for (let i = 0; i < data1.length; i++) {
            let res = data1[i];
            if(res.id === 'root'){
                formatData(key, res.children)
            }
            if(res.key === key){
                res.children.push(newChild)
            }
        }
    }
    formatData(formData.value.editform.parent.key,data.value.domainTreedata);
    formData.value.changeList.push(parent.id);
    if(newChild.checked){
        data.value.domainTreeChecked.push(newChild.key)
    }
    cancelback();
}

// 添加当前激活的标签页
const activeTab = ref('0')
</script>

<template>
    <a-spin :spinning="data.isLoading">
        <a-tabs v-model:activeKey="activeTab">
            <template #rightExtra>
                <a-button
                        @click="submitFun('modelAttTree','ruleForm')"
                        :loading="data.isLoading"
                        type="primary"
                        v-if="activeTab === '2'"
                >
                    提交模型属性
                </a-button>
                <a-button
                        @click="submitFun('domainTree','ruleForm')"
                        type="primary"
                        :loading="data.isLoading"
                        v-if="activeTab === '3'"
                >
                    提交数据字典
                </a-button>
                <a-button
                        @click="submitFun('modelAttTree','ruleForm')"
                        type="primary"
                        :loading="data.isLoading"
                        v-if="activeTab === '0'"
                >
                    提交基本信息
                </a-button>
            </template>
            <!-- 基本信息标签页 -->
            <a-tab-pane tab="基本信息" key="0">
                <a-form :model="form" :rules="rules" ref="ruleForm" laba-width="100px" class="demo-ruleForm" style="width: 600px">
                    <a-form-item label="模型名称" name="modelName">
                        <a-input autocomplete="off" v-model:value="form.modelName" placeholder="模型名称"></a-input>
                    </a-form-item>
                    <a-form-item label="业务条线" name="businessLine">
                        <a-select v-model:value="form.businessLine" placeholder="请选择" :filterOption="filterOption" showSearch>
                            <a-select-option
                                v-for="item in businessLineOptions"
                                :key="item.uuid"
                                :value="item.code"
                                :name="item.name"
                            >{{item.name}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="模型类型" name="modelType">
                        <a-select v-model:value="form.modelType" placeholder="请选择">
                            <a-select-option
                                v-for="item in modelTypeOptions"
                                :key="item.uuid"
                                :value="item.code"
                            >{{item.name}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="选择文件" name="choiceFile">
                        <FileUpload :accept="'.jar'" @uploadFile="uploadFile" :uploadHidden="data.uploadHidden"/>
                    </a-form-item>
                    <a-form-item label="模型描述" name="desc">
                        <a-textarea v-model:value="form.desc"/>
                    </a-form-item>
                </a-form>
            </a-tab-pane>

            <!-- 参数列表标签页 -->
            <a-tab-pane tab="参数列表" key="1">
                <ModelParamsList
                    :paramsList="data.paramsList"
                    :ruleArgDirect="data.ruleArgDirect"
                />
            </a-tab-pane>

            <!-- 模型属性标签页 -->
            <a-tab-pane key="2" tab="模型属性">
                <div class="tree-container">
                    <a-tree
                        :tree-data="data.modelAttTreedata"
                        :checkable="true"
                        node-key="indexId"
                        v-model:checkedKeys="data.modelAttTreeChecked"
                        :defaultExpandedKeys="data.expandedKeys"
                        :props="data.defaultProps"
                        @check="onCheckModel"
                    >
                    </a-tree>
                </div>
            </a-tab-pane>

            <!-- 数据字典标签页 -->
            <a-tab-pane key="3" tab="数据字典">
                <div class="tree-container">
                    <a-tree
                            :tree-data="data.domainTreedata"
                            :checkable="true"
                            node-key="indexId"
                            v-model:checkedKeys="data.domainTreeChecked"
                            :defaultExpandedKeys="data.expandedKeys"
                            :props="data.defaultProps"
                            @check="onCheckDomain"
                            blockNode
                    >
                        <template #title="editData">
                            <a-dropdown :trigger="['contextmenu']">
                                <div class="node-actions">
                                    {{ editData.title }}
                                </div>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item v-if="!editData.children || editData.children.length===0" @click="editCateName(editData)">
                                            <EditOutlined />编辑
                                        </a-menu-item>
                                        <a-menu-item v-if="editData.id !== 'root' && editData.children" @click="appendTreeNode(editData)">
                                            <PlusOutlined />添加
                                        </a-menu-item>
                                        <a-menu-item v-if="!editData.children || editData.children.length===0" @click="removeTreeNode(editData)">
                                            <DeleteOutlined />删除
                                        </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </template>
                    </a-tree>
                </div>
            </a-tab-pane>
        </a-tabs>
    </a-spin>

    <!-- 原有的模态框 -->
    <a-modal :open="formData.openEditform" title="编辑数据字典" @ok="submitFormEdit" @cancel="cancelback" width="500px">
        <a-form ref="editform" :model="formData.editform" :rules="formData.editformRule" laba-width="80px">
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="formData.editform.name"  placeholder="请输入名称" />
            </a-form-item>
            <a-form-item label="值" name="value">
                <a-input autocomplete="off" v-model:value="formData.editform.value"  placeholder="请输入值" />
            </a-form-item>
        </a-form>

    </a-modal>
</template>

<style lang="scss" scoped>
.tree-container {
    height: 500px;
    overflow-y: auto;
    background-color: #F0F2F5;

    :deep(.ant-tree) {
        background-color: #F0F2F5;
        margin-top:15px;
        margin-left:15px;
        margin-bottom:15px;
        :deep(.ant-tree-node-content-wrapper) {
            &:hover {
                background-color: #E6E8EB;
            }

            &.ant-tree-node-selected {
                background-color: #E6E8EB;
            }
        }

        :deep(.ant-tree-checkbox-checked) {
            .ant-tree-checkbox-inner {
                background-color: #1890ff;
                border-color: #1890ff;
            }
        }

        :deep(.ant-tree-treenode) {
            padding: 4px 0;

            &:hover {
                background-color: #E6E8EB;
            }
        }
    }
}

// 新增的基本信息表单样式
.demo-ruleForm {
    padding: 20px;
    background-color: #fff;
}

:deep(.ant-tabs-content) {
    min-height: 400px; // 确保内容区域有足够高度
}

:deep(.ant-tabs-tabpane) {
    padding: 16px;
}


.bottom-actions {
    margin-top: 16px;
    text-align: right;
}

:deep(.ant-tabs-nav-wrap) {
    flex: 1;
}

:deep(.ant-tabs-extra-content) {
    padding-left: 16px;
}
.node-actions {
    display: inline-block;
    width:100%;
    .button {
        padding: 0 4px;
        &:hover {
            background-color: transparent;
        }
    }
}

</style>
