<!-- 分配规则库页 -->

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { getRulebase, getRule, roleRuleStates } from "@/api/role";
import qs from "qs";
definePageMeta({
    title: '分配规则库'
})

const props = defineProps({
    role: {
        type: String,
        required: true
    }
});

const router = useRouter();
const message = inject('message')
const checkedRuleBases = ref<string[]>([]);
const businId = ref();
const businArr = ref<any[]>([]);
const arr = ref<any[]>([]);
const checkVal = ref<any[]>([]);
const dataval = ref<any[]>([]);
const arrRole = ref<string>('');

const getbaseInfo = async () => {
    const pars = {
        businessUuid: businId.value.businessLineId == null ? '' : businId.value.businessLineId,
    };
    const res = await getRulebase(pars);
    businArr.value = res.data;
    if (businArr.value) {
        businArr.value.forEach((item, index) => {
            arr.value[index] = {
                name: item.chineseName,
                uuid: item.uuid,
                checked: false,
            };
        });
    }
    await getRuleStat();
};

const getRuleStat = async () => {
    const pars = {
        id: props.role.id,
    };
    const res = await roleRuleStates(pars);
    checkedRuleBases.value = res.data.split(",");
};

const test = (v: string[]) => {
    checkVal.value = v;
    dataval.value = v;
    if (dataval.value.length !== 0) {
        dataval.value = dataval.value.filter(item => item !== '');
    }
    dataval.value = Array.from(new Set(dataval.value));
    arrRole.value = dataval.value.join(",");
};

const allChecked = () => {
    arr.value.forEach(item => {
        checkedRuleBases.value.push(item.uuid);
    });
    checkedRuleBases.value = Array.from(new Set(checkedRuleBases.value));
    arrRole.value = checkedRuleBases.value.join(",");
};

const checkReset = () => {
    checkedRuleBases.value = [];
};

const submit = async () => {
    const data = qs.stringify({
        engIdsStr: arrRole.value, //选中的
        id: props.role.id, //角色列id
    });
    const res = await getRule(data);
    if (res.data === "操作失败，请联系管理员！") {
        message.error(res.data);
    } else {
        message.success(res.data);
        emit('close');
    }
};

const resetForm = () => {
    emit('close');
};

const emit = defineEmits(['close']);

onMounted(() => {
    businId.value = props.role;
    console.log(businId.value)
    getbaseInfo();
});

// 暴露方法给父组件
defineExpose({
    allChecked,
    checkReset,
    resetForm,
    submit
});
</script>

<template>
    <!-- 搜索区域 -->
    <a-form style="margin-top: 25px">
        <a-form-item>
            <a-checkbox-group v-model:value="checkedRuleBases" @change="test">
                <a-checkbox v-for="item in arr" :key="item.uuid" :value="item.uuid">{{ item.name }}</a-checkbox>
            </a-checkbox-group>
        </a-form-item>
    </a-form>
</template>
