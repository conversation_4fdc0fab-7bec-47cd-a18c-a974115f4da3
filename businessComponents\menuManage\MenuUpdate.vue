<!--菜单编辑页-->
<template>
    <div id="menu_update">
        <a-form :rules="rules" :model="ruleForm" ref="ruleFormRef" label-width="100px" class="demo-ruleForm" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="地址" name="routePath">
                <a-input autocomplete="off" v-model:value="ruleForm.routePath" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="上级菜单" name="parentId">
                <a-select v-model:value="ruleForm.parentId" placeholder="请选择" style="width:100%" @change="selectGet">
                    <a-select-option v-for="item in menusList" :key="item.id" :value="item.id">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="ruleForm.description" :autosize="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="100" style="width:100%;height: 30%" placeholder="请输入"></a-textarea>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
    import { useRouter, useRoute } from 'vue-router';
    import qs from 'qs';
    import { menuUpdate, ParentMenu, menuById } from '@/api/menuList';

    const router = useRouter();
    const route = useRoute();
    const message = inject('message')
    const props = defineProps({
        menuId: {
            type: String,
            required: true,
        },
        menuParentId: {
            type: String,
            required: true,
        },
        menuType: {
            type: String,
            required: true,
        },
        menuName: {
            type: String,
            required: true,
        },
    });
    const ruleForm = reactive({
        name: '',
        routePath: '',
        description: '',
        url: '',
        state: '',
        ordernum: "",
        parentId: "",
        type: "",
    });

    const strId = ref('');
    const menusList = ref([]);

    const rules = {
        name: [
            { required: true, message: '名称不能为空', trigger: 'change' }
        ],
        parentId: [
            { required: true, message: '请选择', trigger: 'change' }
        ],
        description: [
            {
                min: 1,
                max: 100,
                message: "长度在 1 到 100 个字符",
                trigger: "blur",
            },
        ]
    };

    onMounted(() => {
        strId.value = props.menuId;
        ruleForm.parentId = props.menuParentId;
        ruleForm.type = props.menuType;
        ruleForm.name = props.menuName
        updalist();
        sonmenu();
    });
    const  path = ref();
    const sonmenu = () => {
        const pars = {
            id: strId.value
        };
        menuById(pars).then((res) => {
            console.log(res)
            ruleForm.name = res.data.name
            ruleForm.routePath = res.data.routePath
            ruleForm.description = res.data.description
            ruleForm.url = res.data.url
            ruleForm.state = res.data.state
            ruleForm.ordernum = res.data.ordernum
            ruleForm.parentId = res.data.parentId
            ruleForm.type = res.data.type
            path.value = res.data.path
        });
    };
    const ruleFormRef = ref()
    const submitForm = (callback) => {
        ruleFormRef.value.validate().then((valid: boolean) => {
            if (valid) {
                const data = {
                    description: ruleForm.description,
                    id: strId.value,
                    name: ruleForm.name,
                    ordernum: ruleForm.ordernum,
                    url: ruleForm.url,
                    parentId: ruleForm.parentId,
                    state: ruleForm.state,
                    type: ruleForm.type,
                    routePath: ruleForm.routePath,
                    path:path.value
                };
                menuUpdate(data).then((res) => {
                    message.success(res.data);
                    
                    // 成功后简单地回到菜单管理页面，让页面自然刷新
                    if (typeof callback === 'function') {
                        callback();
                        // 短暂延迟后返回，让用户看到成功消息
                        setTimeout(() => {
                            router.go(0); // 刷新当前页面
                        }, 800);
                    }
                }).catch((err: any) => {
                    console.log(err);
                });
            } else {
                console.log('error submit!!');
                return false;
            }
        });
    };

    const updalist = () => {
        ParentMenu().then(res => {
            menusList.value = res.data;
        });
    };

    const selectGet = (vid: string) => {
        const obj = menusList.value.find((item) => item.id === vid);
        if (obj) {
            ruleForm.parentId = obj.id;
        }
    };
    defineExpose({
        submitForm,
    });
</script>

