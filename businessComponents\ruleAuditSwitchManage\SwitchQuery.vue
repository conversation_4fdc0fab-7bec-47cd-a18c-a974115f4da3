<!-- 批量审核规则页 -->
<template>

    <a-modal v-if="controlVisible" :visible="controlVisible" title="待审核规则" @ok="closeModal" @cancel="closeModal" width="800">
        <b>规则库名称：{{props.i.keyname}}</b>
        <a-button type="primary" @click="pass" style="margin-left: 8px">
            批量审核
        </a-button>
        <a-table :columns="columns" :data-source="tableDate" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading"
                 @change="handlePageChange">

        </a-table>
    </a-modal>
</template>
<script setup lang="ts">
    import { ruleList, on_pass, offstate } from "@/api/on_off";
    import qs from "qs";
    //子父页面参数传递
    const emit = defineEmits(['event'])
    const props = defineProps({
        controlVisible: {
            type: Boolean,
            default: false,
        },
        i: {
            type: Object
        }
    })
    const message = inject('message')
    const modal = inject('modal')
    watch(()=>props.controlVisible,(newValue,oldValue)=>{
        if(newValue == true){
            fetchRules();
        }
    },{immediate:true,deep:true})
    const closeModal=()=>{
        emit('closeModal')
    }



    //页脚参数
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });


    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        fetchRules();
    };
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`
        },
        {
            title: '规则库名称',
            dataIndex: 'ruleName',
            key: 'ruleName',
        },
        {
            title: '规则类型',
            dataIndex: 'type',
            key: 'type',
        },
        {
            title: '规则优先级',
            dataIndex: 'salience',
            key: 'salience',
        },
        {
            title: '修改时间',
            dataIndex: 'lastModifiedTimeStr',
            key: 'lastModifiedTimeStr',
        },
        {
            title: '规则状态',
            dataIndex: 'status',
            key: 'status',
        },
        {
            title: '提交时间',
            dataIndex: 'createdTimeStr',
            key: 'createdTimeStr',
        },
        {
            title: '提交人',
            dataIndex: 'createdId',
            key: 'createdId',
        }
    ];

    //定义参数
    const tableDate = ref<TableDate[]>([]);
    interface TableDate {
        ruleName: string;
        type: string;
        salience: string;
        lastModifiedTimeStr: string;
        status: string;
        createdTimeStr: string;
        createdId: string;
    }

    const fetchRules = async () => {
        pagination.value.isLoading = true;
        try {
            let pars = {
                engUuid: props.i.key,
                page: pagination.value.current,
                several: pagination.value.pageSize,
            };
            ruleList(pars).then((res) => {
                tableDate.value = res.data.data;
            });
            pagination.value.isLoading = false;
        } catch (error) {
            pagination.value.isLoading = false;
            message.error('获取历史失败');
        }
    };

    //组件挂载后
    onMounted(() => {
        fetchRules();
    });
    //批量审核
    const pass = () => {
        let data = qs.stringify({
            engUuid: props.i.key,
        });
        on_pass(data).then((res) => {
            modal.confirm({
                title: () => "提示",
                content: () => "您确定要批量审核吗?",
                okText: () => '确定',
                okType: 'warning',
                cancelText: () => '取消',
                onOk() {
                    modal.confirm({
                        title: () => "提示",
                        content: () => "待审核规则数据已全部处理，您是否要继续关闭规则库的审核控制开关?",
                        okText: () => '确定',
                        okType: 'warning',
                        cancelText: () => '取消',
                        onOk() {
                            let data = qs.stringify({
                                upStatus: props.i.keystatus ? "1" : "0",
                                uuid: props.i.key,
                            });
                            offstate(data).then((res) => {
                                props.i.keyname = ''
                                fetchRules();
                                closeModal();
                            });
                        },
                        onCancel() {
                        },
                    })
                },
                onCancel() {
                },
            })
        });
    }
</script>

<style lang="scss" scoped>
    /*固定列高*/
    :deep(.ant-modal-body) {
        height: 100px !important;
    }
</style>
