<!-- 规则信息显示公共组件 -->
<template>
    <div class="rule-info-display">
        <a-collapse class="base-info-collapse" v-model:activeKey="collapseActiveKey" @change="handleCollapseChange">
            <a-collapse-panel key="1" header="规则信息详情">
                <a-row v-for="(row, index) in displayData" :key="index" type="flex" class="row-bg">
                    <template v-for="item in row" :key="item.label">
                        <a-col :span="item.span || 12" class="col-border">
                            <label>{{ item.label }}：</label>
                            <template v-if="item.field === 'packageNameAll'">
                                <RulePath :path="formatValue(props.data[item.field], item.format)" />
                            </template>
                            <template v-else>
                                <template v-if="item.tooltip && props.data[item.field]">
                                    <a-tooltip :title="props.data[item.field]">
                                        <span :class="item.class">
                                            {{ formatValue(props.data[item.field], item.format) }}
                                        </span>
                                    </a-tooltip>
                                </template>
                                <span v-else :class="item.class">
                                    {{ formatValue(props.data[item.field], item.format) }}
                                </span>
                            </template>
                        </a-col>
                    </template>
                </a-row>
            </a-collapse-panel>
        </a-collapse>

        <a-row v-if="showRuleContent" type="flex" class="row-bg fullrow fulltab" style="margin-top: 5px;">
            <a-col class="col-detail" :span="24">
                <label style="font-weight: 700">规则内容：</label>
                <!-- 非历史模式下，根据类型显示不同组件 -->
                <template v-if="!hisFlag">
                    <RuleDetailContent v-if="props.data.type != 4 && props.data.type != 5" :ruleInfoFlag=true :ruleInfoData="props.data" :maxHeight="450"/>
                    <RuleFlowDetail v-if="props.data.type == 5" :ruleInfo="props.data" />
                    <DecisionTreeDetail v-if="props.data.type == 4" :ruleInfo="props.data" />
                </template>

                <!-- 历史模式下，根据类型显示不同组件 -->
                <template v-else>
                    <RuleFlowDetail v-if="props.data.type == 5 || props.data.type == '规则流'" :ruleInfo="props.data" :hisFlag="true" :historyId="historyId" />
                    <DecisionTreeDetail v-if="props.data.type == 4 || props.data.type == '决策树'" :ruleInfo="props.data" :hisFlag="true" :historyId="historyId"/>
                    <RuleDetailContent v-if="props.data.type != 4 && props.data.type != 5 && props.data.type != '规则流' && props.data.type != '决策树'" :ruleInfoData="props.data" :ruleInfoFlag=true :maxHeight="450"/>
                </template>
            </a-col>
        </a-row>
    </div>
</template>

<script setup>
    import { RULE_STATUS } from '@/consts/ruleStatusConsts';
    import {getRuleinfo} from "@/api/rule_audit_management";
    import RuleFlowDetail from "@/businessComponents/ruleAudit/RuleFlowDetail.vue";
    import DecisionTreeDetail from "@/businessComponents/ruleAudit/DecisionTreeDetail.vue";
    const props = defineProps({
        data: {
            type: Object,
            required: true
        },
        config: {
            type: Array,
            required: true
        },
        showRuleContent: {
            type: Boolean,
            default: true
        },
        showDetailFlag: {
            type: Boolean,
            default: false
        },
        hisFlag: {
            type: Boolean,
            default: false
        },
        historyId: {
            type: String,
            default: ''
        }
    });

    const emit = defineEmits(['update:showDetailFlag']);
    console.log(props.data.type)
    const collapseActiveKey = ref(props.showDetailFlag ? ['1'] : []);

    watch(() => props.showDetailFlag, (newVal) => {
        collapseActiveKey.value = newVal ? ['1'] : [];
    });

    const handleCollapseChange = (keys) => {
        collapseActiveKey.value = keys;
        emit('update:showDetailFlag', keys.length > 0);
    };

    const displayData = computed(() => {
        const rows = [];
        let currentRow = [];

        props.config.forEach((item, index) => {
            currentRow.push(item);
            if (currentRow.length === 2 || item.span === 24) {
                rows.push([...currentRow]);
                currentRow = [];
            }
        });

        if (currentRow.length > 0) {
            rows.push(currentRow);
        }

        return rows;
    });

    const formatValue = (value, format) => {
        if (!value) return '';
        if (format === 'status') return RULE_STATUS[value];
        if (format === 'truncate') {
            return value.length > 16 ? value.substring(0, 16) + '...' : value;
        }
        return value;
    };
</script>

<style lang="scss" scoped>
    .rule-info-display {
        .base-info-collapse {
            margin: 0;

            :deep(.ant-collapse-header) {
                display: flex;
                align-items: center;
                padding: 8px 16px;
                font-weight: 500;
            }

            :deep(.ant-collapse-content-box) {
                padding: 16px;
            }
        }

        .row-bg {
            margin-top: 3px;
        }

        .col-border {
            display: flex;

            label {
                margin: 5px;
                color: #303133;
                font-weight: 700;
                width: 90px;
                text-align: left;
                flex-shrink: 0;
            }

            span {
                color: #606266;
                margin: 5px 5px 5px 0;
                display: inline-block;
                word-break: break-word;
            }
        }

        .col-detail {
            color: #303133;
        }
        .compareColor {
            color: red !important;
        }
        .regulation {
            color: red !important;
        }
    }
</style>
