// 校验流程
export default function checkFlow(_this) {
  const elementRegistry = _this.bpmnModeler.get("elementRegistry");

  const definitionList = elementRegistry.filter(
    (item) =>

    item.type === "bpmn:ExclusiveGateway"
  );
  const taskList = elementRegistry.filter(
    (item) => item.type === "bpmn:BusinessRuleTask"
  );
  const convergenceList = elementRegistry.filter(
    (item) =>
    (item.type === "bpmn:ParallelGateway" ||
      item.type === "bpmn:ExclusiveGateway") &&
    item.businessObject.gatewayDirection === "Converging"
  );

  _this.flowErrorMsg = [];

  // 条件
  definitionList.map((i) => {
    const iFlag = i.businessObject.name || i.id;

    if (i.incoming.length > 1) {
      _this.flowErrorMsg.push(
        `条件节点：<strong>【${iFlag}】</strong>至多有一条接入路由边！`
      );
    }
    if (i.outgoing.length < 1) {
      _this.flowErrorMsg.push(
        `条件节点：<strong>【${iFlag}】</strong>至少有一条接出路由边！`
      );
    }
  });
  // 汇聚
  convergenceList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    if (i.incoming.length < 2) {
      _this.flowErrorMsg.push(
        `汇聚分支节点：<strong>【${iFlag}】</strong>至少存在两条接入路由边！`
      );
    }
    if (i.outgoing.length === 0) {
      _this.flowErrorMsg.push(
        `汇聚分支节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
      );
    }
  });
  // 规则
  taskList.map((i) => {
    const iFlag = i.businessObject.name || i.id;
    if (i.incoming.length === 0) {
      _this.flowErrorMsg.push(
        `规则节点：<strong>【${iFlag}】</strong>必须存在一条接入路由边！`
      );
    }
    if (i.incoming.length > 1) {
      _this.flowErrorMsg.push(
        `规则节点：<strong>【${iFlag}】</strong>只能存在一条接入路由边！`
      );
    }
    if (i.outgoing.length === 0) {
      _this.flowErrorMsg.push(
        `规则节点：<strong>【${iFlag}】</strong>必须存在一条接出路由边！`
      );
    }
    if (i.outgoing.length > 1) {
      _this.flowErrorMsg.push(
        `规则节点：<strong>【${iFlag}】</strong>只能存在一条接出路由边！`
      );
    }
  });

  if (_this.flowErrorMsg.length !== 0) {
    // _this.errorDrawer = true;
    _this.$notify.closeAll()
    _this.showError()
  }
}
