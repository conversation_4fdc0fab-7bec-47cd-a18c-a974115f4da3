<template>
  <div class="designView">
    <div class="ruleBody">
      <!-- 使用 ConditionList 组件，并传递相应的 props 和事件 -->
      <ConditionList
        v-bind="conditionProps"
        @addRule="addRule"
        @addUp="addUp"
        @addChildCondition="addChildCondition"
        @addTailItem="addTailItem"
        @decreaseRule="decreaseRule"
        @logicBtnClick="onLogicBtnClick"
        @conditionChange="onConditionChange"
        @conditionInit="conditionInit"
        @replaceItem="replaceItem"
      />
    </div>
  </div>
</template>

<script setup>
import ConditionList from "@/components/ruleEditCom/condition/conditionList.vue";

// 定义 props
const props = defineProps({
  ruleData: {
    type: Object,
    default: () => ({}),
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  validateResult: {
    type: Object,
    default: () => ({}),
  },
  toAnalysis: {
    type: Function,
    default: () => () => {},
  },
});

const emit = defineEmits([
  "addRule",
  "addUp",
  "addChildCondition",
  "addTailItem",
  "replaceItem",
  "decreaseRule",
  "switcherChange",
  "logicBtnClick",
  "conditionChange",
  "conditionInit",
]);

// 定义条件列表的属性
const conditionProps = ref({});

// 定义 setProps 方法
const setProps = (newValue) => {
  // 解构 props
  const { ruleData = {}, locked, isTrack, validateResult = {} } = toRefs(props);
  // 解构 ruleData 和 validateResult
  const { conditionData } = ruleData.value;
  const { conditionValids: conditionValidList = [] } = validateResult.value;

  // 定义条件列表的属性
  conditionProps.value = {
    pos: "r",
    conditionData,
    validList: conditionValidList,
    locked: locked.value,
    isTrack: isTrack.value,
  };
};

// 监听 ruleData 变化，当 ruleData 发生变化时获取数据
watch(
  () => props,
  (newValue) => {
    setProps(newValue); // 调用 setProps 方法获取数据
  },
  { deep: true, immediate: true } // 深度监听
);

// 定义方法
const addRule = (pos, conditionId, layer) => {
  emit("addRule", pos, conditionId, layer);
};

const addUp = (pos, conditionId, layer) => {
  emit("addUp", pos, conditionId, layer);
};

const addChildCondition = (pos, conditionId, layer) => {
  emit("addChildCondition", pos, conditionId, layer);
};

const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};

const decreaseRule = (pos, conditionId, layer) => {
  emit("decreaseRule", pos, conditionId, layer);
};

const onLogicBtnClick = (pos, logicalSymbol) => {
  emit("onLogicBtnClick", pos, logicalSymbol);
};

const onConditionChange = (pos, newContents) => {
  emit("conditionChange", pos, newContents);
};

const conditionInit = () => {
  emit("conditionInit");
};

const replaceItem = (pos, conditionId, layer) => {
  emit("replaceItem", pos, conditionId, layer);
};
</script>

