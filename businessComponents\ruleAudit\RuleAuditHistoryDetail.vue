<!-- 规则审核历史详情页 -->

<script setup lang="ts">
import { getPassInfo, getBuckInfo, getRuleInfo } from "@/api/audit_history";
import { differ, beautyTable } from "@/api/table_diff";
import { ref } from "vue";
import RuleCompareContent from "@/components/ruleDetailCom/RuleCompareContent.vue";
import RuleFlowDetail from "@/businessComponents/ruleAudit/RuleFlowDetail.vue";
import DecisionTreeDetail from "@/businessComponents/ruleAudit/DecisionTreeDetail.vue";
interface RuleInfo {
    beforeTextDtl?: string;
    tableContentHis?: {
        textDtl?: string;
    };
    tableContentHisFail?: {
        textDtl?: string;
    };
    beforeTableContentHis?: {
        textDtl?: string;
    };
    beforeTableContentHisFail?: {
        textDtl?: string;
    };
    [key: string]: any;
}

interface AuditVal {
    checkStatus?: string;
    [key: string]: any;
}

const props = defineProps({
    uuid: String,
    checkStatus: String
})
const router = useRouter();
const data = ref({
    activeName: "first",
    getInfoVal: "",
    getInfoCheck: "",
    arrAuditVal: [] as AuditVal[],
    arrRuleInfo: {} as RuleInfo,
    cause: false,
    cause1: true,
    arrAuditVal1: [] as AuditVal[],
    ruleStates: "",
    validColor: false,
})
const tip = ref('');
const tipTop = ref('');
const tipLeft = ref('');
const showTip = ref(false);
const switchVal = ref(false);
const sliderValue = ref(100);
const tabHeight = ref(600);
const tableScale = ref(1);
const hisUuid = ref<string>("");

const handleTextDtl = () => {
    if (!data.value.arrRuleInfo) {
        return;
    }
    // 处理比对详情所需参数
    data.value.arrRuleInfo.ruleHisVO1 = {}, data.value.arrRuleInfo.ruleHisVO2 = {};
    data.value.arrRuleInfo.ruleHisVO1.type = data.value.arrRuleInfo.type;
    data.value.arrRuleInfo.ruleHisVO2.type = data.value.arrRuleInfo.type;
    if (data.value.arrRuleInfo.type === '普通规则') {
        if (data.value.arrRuleInfo.textDtl) {
            data.value.arrRuleInfo.ruleHisVO1.textDtl = data.value.arrRuleInfo.textDtl;
        }
        if (data.value.arrRuleInfo.beforeTextDtl) {
            data.value.arrRuleInfo.ruleHisVO2.textDtl = data.value.arrRuleInfo.beforeTextDtl;
        }
    } else if (data.value.arrRuleInfo.type === '决策表') {
        if (data.value.arrRuleInfo.tableContentHis) {
            data.value.arrRuleInfo.ruleHisVO1.tableContentHis = data.value.arrRuleInfo.tableContentHis;
        }
        if (data.value.arrRuleInfo.tableContentHisFail) {
            data.value.arrRuleInfo.ruleHisVO1.tableContentHis = data.value.arrRuleInfo.tableContentHisFail;
        }
        if (data.value.arrRuleInfo.beforeTableContentHis) {
            data.value.arrRuleInfo.ruleHisVO2.tableContentHis = data.value.arrRuleInfo.beforeTableContentHis;
        }
        if (data.value.arrRuleInfo.beforeTableContentHisFail) {
            data.value.arrRuleInfo.ruleHisVO2.tableContentHis = data.value.arrRuleInfo.beforeTableContentHisFail;
        }
    }
}
onMounted(() => {
    data.value.getInfoVal = props.uuid as string;
    data.value.getInfoCheck = props.checkStatus as string;
    getstatesInfo().then(() => {
        handleTextDtl();
    });
})
const getstatesInfo = () => {
    let pars = {
        uuid: data.value.getInfoVal,
    };

    return new Promise((resolve, reject) => {
        if (data.value.getInfoCheck == "提交审核通过" || data.value.getInfoCheck == "删除审核通过") {
            getBuckInfo(pars).then((res) => {
                data.value.arrRuleInfo = res.data;
                resolve();
            }).catch(err => {
                reject(err);
            });
        } else if (data.value.getInfoCheck == "提交审核退回" || data.value.getInfoCheck == "删除审核未通过") {
            getPassInfo(pars).then((res) => {
                data.value.cause = true;
                data.value.cause1 = false;
                data.value.arrRuleInfo = res.data;
                data.value.arrAuditVal = res.data;
                resolve();
            }).catch(err => {
                reject(err);
            });
        }
        getRuleInfo(pars).then((res) => {
            data.value.arrAuditVal1 = res.data;
            hisUuid.value = res.data.ruleHisUuid
            if (data.value.arrAuditVal1) {
                switch (data.value.arrAuditVal1.checkStatus) {
                    case "4":
                        data.value.arrAuditVal1.checkStatus = "删除审核通过";
                        break;
                    case "0":
                        data.value.arrAuditVal1.checkStatus = "提交审核通过";
                        break;
                    case "1":
                        data.value.arrAuditVal1.checkStatus = "提交审核退回";
                        break;
                    default:
                        data.value.arrAuditVal1.checkStatus = "删除审核未通过";
                }
            }
            data.value.cause = false;
            data.value.cause1 = true;
        });
    });
}
const ruleForm = () => {
    navigateTo("/ruleAuditHistory")
}

// 规则信息配置
const ruleDisplayConfig = [
    { label: '规则名称', field: 'ruleName' },
    { label: '规则库', field: 'engUuid' },
    { label: '规则版本', field: 'edition' },
    { label: '规则类型', field: 'type' },
    { label: '规则状态', field: 'status' },
    { label: '有效状态', field: 'validStatus', class: 'valState' },
    { label: '修改时间', field: 'lastModifiedTimeStr' },
    { label: '修改人', field: 'modifiedId' },
    { label: '创建时间', field: 'createdTimeStr' },
    { label: '创建人', field: 'createdId' },
    { label: '规则优先级', field: 'salience' },
    { label: '规则包', field: 'packageNameAll' },
    { label: '规则描述', field: 'descs', span: 24 }
];

// 审核信息配置（通过/未通过）
const auditDisplayConfig1 = [
    { label: '审核状态', field: 'checkStatus', span: 24 },
    { label: '审核意见', field: 'checkComment', span: 24 },
    { label: '审核时间', field: 'checkTimeStr', span: 24 },
    { label: '审核人', field: 'checkId', span: 24 },
    { label: '提交时间', field: 'createdTimeStr', span: 24 },
    { label: '提交人', field: 'createdId', span: 24 }
];

// 审核信息配置（退回）
const auditDisplayConfig2 = [
    { label: '审核状态', field: 'status', span: 24 },
    { label: '退回原因', field: 'failRemark', span: 24 },
    { label: '审核时间', field: 'checkTimeStr', span: 24 },
    { label: '审核人', field: 'createdId', span: 24 },
    { label: '提交时间', field: 'createdTimeStr', span: 24 },
    { label: '提交人', field: 'createdId', span: 24 }
];
</script>

<template>
    <!-- 表格区域 -->
    <div class="audit_info" style="margin: 25px 15px 0">

        <a-tabs type="card" v-model="data.activeName">
            <a-tab-pane tab="规则信息" key="first">
                <RuleInfoDisplay :data="data.arrRuleInfo" :config="ruleDisplayConfig" :showRuleContent="false" />
                <template v-if="data.arrRuleInfo.type =='普通规则' || data.arrRuleInfo.type =='决策表' ">
                    <!-- 添加规则内容对比部分 -->
                    <a-row type="flex" class="row-bg fullrow fulltab" style="margin-left: 5px">
                        <a-col class="col-detail" :span="24">
                            <RuleCompareContent :rule-compare-data="{
                            ruleHisVO1: data.arrRuleInfo.ruleHisVO1,
                            ruleHisVO2: data.arrRuleInfo.ruleHisVO2
                        }"/>
                        </a-col>
                    </a-row>
                </template>
                <template v-else>
                    <RuleFlowDetail v-if="data.arrRuleInfo.type == '规则流'"  :hisFlag="true" :historyId="hisUuid"/>
                    <DecisionTreeDetail v-if="data.arrRuleInfo.type == '决策树'" :hisFlag="true" :historyId="hisUuid"/>
                </template>
            </a-tab-pane>

            <!-- 审核信息状态不一样,返回的数据和字段也不一样，所以要写2个结构 -->
            <a-tab-pane tab="审核信息" key="second">
                <RuleInfoDisplay v-show="data.cause1" :data="data.arrAuditVal1" :config="auditDisplayConfig1"
                    :showRuleContent="false" :showDetailFlag="true" />
                <RuleInfoDisplay v-show="data.cause" :data="data.arrAuditVal" :config="auditDisplayConfig2"
                    :showRuleContent="false" :showDetailFlag="true" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<style lang="scss" scoped>
.datatable tr:first-child td,
.datatable2 tr:first-child td {
    text-align: left !important;
}

.datatable.rule tr:first-child td,
.datatable2.rule tr:first-child td {
    text-align: left !important;
    white-space: normal !important;
    word-wrap: break-word;
    word-break: break-all;
}

.fakeContainer {
    margin: 20px;
    padding: 0px;
    border: none;
    width: 95%;
    // overflow: hidden;
    overflow: scroll;
}

.datatable th {
    word-wrap: break-word;
    word-break: normal;
    border: 1px solid #ccc;
    /* 行名称边框 */
    background: #f2f2f2;
    /*url(../img/201407061920.gif) repeat-x;*/
    /* 行名称背景色 */
    color: #333;
    /* 行名称颜色 */
    font-weight: bold;
    height: 30px;
    line-height: 24px;
    padding-left: 5px;
    padding-right: 5px;
    text-align: center;
    white-space: nowrap;
}

.datatable td {
    border: 1px solid #ccc;
    /* 单元格边框 */
    text-align: left;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 10px;
    padding-right: 10px;
    white-space: nowrap;
}

.datatable tr:hover,
.datatable tr.altrow {
    background-color: #f2f2f2;
    /* 动态变色 */
}

.audit_info {
    .valState {
        color: red;
    }

    .a-form-item {
        margin-bottom: 0px;
    }

    .a-row {
        margin-top: 0px;
    }

    .fakeContainer,
    .fakeContainer2 {
        height: 600px;
        overflow-y: auto;
        /* 启用垂直滚动条 */
    }
}

table {
    width: 100%;
}

th,
td {
    max-width: 90px !important;
    text-overflow: ellipsis;
    overflow-x: hidden;
    text-align: center;
}

#tip-div {
    position: fixed;
    top: 0;
    left: 0;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #000;
    padding: 10px;
    z-index: 2000;
    font-size: 14px;
    line-height: 1.2;
    min-width: 10px;
    word-wrap: break-word;
    overflow: auto;
    max-height: 300px;
    max-width: 600px;
}

// table {
//   border-collapse: collapse;
// }
.fixed-table {
    overflow: auto;
    height: 100%;
    /* 设置固定高度 */
    width: 100%;
    // position: absolute
}

.fixed-table td,
.fixed-table th {
    /* 设置td,th宽度高度 */
    border: 1px solid #c7d8ee;
    width: 150px;
    min-width: 150px;
    // height: 30px;
    // padding: 5px;
}

.fixed-table th {
    position: sticky;
    top: 0;
    /* 首行永远固定在头部  */
}

.fixed-table th.th2 {
    top: 30px;
}

.fixed-table th.th3 {
    top: 60px;
}

.fixed-table td:first-child,
.fixed-table th.xh {
    position: sticky;
    left: 0;
    /* 首列永远固定在左侧 */
    z-index: 1;
    background-color: #fff;
}

.fixed-table th.xh {
    z-index: 2;
    /*表头的首列要在上面*/
    background-color: #f2f2f2;
}

.fixed-table th>div {
    width: 100%;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
}

.slider-div-left {
    display: flex;
    align-items: center;

    .el-alert {
        margin-right: 20px;
    }
}

.slider-alert-div {
    display: flex;
    justify-content: space-between;

    .el-alert {
        width: auto;
    }
}

.slider-div {
    width: 170px;
    display: flex;
    float: right;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;

    .slider-block {
        width: 140px;

        .el-slider__runway.show-input {
            width: 50px !important;
            top: -2px;
        }
    }
}

.fakeContainer {
    margin: 0 !important;
    margin-top: 20px;
    width: 100% !important;
    overflow: auto;
}

.datatable tr:first-child td,
.datatable2 tr:first-child td {
    text-align: left !important;
}

.datatable.rule tr:first-child td,
.datatable2.rule tr:first-child td {
    text-align: left !important;
    white-space: normal !important;
    word-wrap: break-word;
    word-break: break-all;
}

.col-border {
    display: flex;
    height: 40px;

    label {
        margin: 5px;
        color: #303133;
        font-weight: 700;
        width: 80px;
        /* 固定宽度 */
        text-align: left;
        flex-shrink: 0;
        /* 防止label被压缩 */
    }

    span {
        color: #606266;
        margin: 5px 5px 5px 0;
        /* 上右下左 */
        display: inline-block;
        word-break: break-word;
        /* 允许在任意字符间换行 */
    }
}

.col-detaill {
    margin-left: 5px;
    color: #303133;
}
</style>
