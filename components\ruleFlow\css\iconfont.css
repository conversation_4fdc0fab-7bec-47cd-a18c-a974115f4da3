@font-face {
  font-family: "iconfont";
  /* Project id  */
  src: url('iconfont.ttf?t=1636359470512') format('truetype');
}
@font-face {
  font-family: "ruleiconfont"; /* Project id  */
  src: url('ruleiconfont.ttf?t=1637806028854') format('truetype');
}
@font-face {
  font-family: "iconfontree"; /* Project id  */
  src: url('iconfontree.ttf?t=1652180869097') format('truetype');
}
@font-face {
  font-family: "iconfontflow"; /* Project id  */
  src: url('iconfontflow.ttf?t=165218086909') format('truetype');
}
.iconfontree {
  font-family: "iconfontree" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfontflow {
  font-family: "iconfontflow" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.ruleiconfont {
  font-family: "ruleiconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  position: relative;
  left:-4px;
}

.icon-kongjian {
  background-image: url("../img/kongjian.png");
  background-size: 20px 20px;
  width:20px;
  height:20px;
  top: 4px;
}

.icon-database {
  background-image: url("../img/ku.png");
  background-size: 22px 22px;
  width:22px;
  height:22px;
  top: 5px;
}


.icon-wenjianjia {
  background-image: url("../img/wenjianjia.png");
  background-size: 16px 16px;
  width:16px;
  height:16px;
  top: 3px;
}
.icon-guize {
  background-image: url("../img/guize.png");
  background-size: 16px 16px;
  width:16px;
  height:16px;
  top: 3px;
}
.icon-biaoge {
  background-image: url("../img/biaoge.png");
  background-size: 16px 16px;
  width:16px;
  height:16px;
  top: 3px;
}
.icon-tree {
  background-image: url("../img/tree.png");
  background-size: 16px 16px;
  width:16px;
  height:16px;
  top: 3px;
}
.icon-flow {
  background-image: url("../img/liucheng.png");
  background-size: 16px 16px;
  width:16px;
  height:16px;
  top: 3px;
}
.icon-import {
  background-image: url("../img/import.png");
  background-size: 10px 10px;
  width:10px;
  height:10px;
  display: inline-block;
}
.icon-export {
  background-image: url("../img/export.png");
  background-size: 10px 10px;
  width:10px;
  height:10px;
  display: inline-block;
}
.icon-import-bpmn {
  background-image: url("../img/import-bpmn.png");
  background-size: 10px 10px;
  width:10px;
  height:10px;
  display: inline-block;
}
.icon-export-bpmn {
  background-image: url("../img/export-bpmn.png");
  background-size: 10px 10px;
  width:10px;
  height:10px;
  display: inline-block;
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

}

.icon-start:before,
.icon-policy:before,
.icon-convergence:before,
.icon-end:before {
  font-style: normal;
  font-weight: normal;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
}

.icon-start:before {
  content: "\e6e1";
}

.icon-policy:before {
  content: "\e63d";

}

.icon-task:before {
  content: "\e659";
}

.icon-convergence:before {
  content: "\e6ac";
}

.icon-end:before {
  content: "\e6e0";
}

.icon-condition:before {
  content: "\10208";
}

.icon-action:before {
  content: "\10207";
}

.icon-notes:before {
  content: "\e6ac";
}
.icon-subflow:before {
  content: "\e654";
}
