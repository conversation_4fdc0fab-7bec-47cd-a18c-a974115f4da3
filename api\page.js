import request from '@/utils/request'

// 统计用户数量、规则库数量、规则数量、模型数量
export function getTotalStatistics() {
  return request({
    url: '/sys/page/totalStatistics',
    method: 'get'
  })
}

// 统计普通规则、决策表、规则流
export function getRuleTypeStatistics() {
  return request({
    url: '/sys/page/ruleTypeStatistics',
    method: 'get'
  })
}

// 统计当天新增、修改、删除、审核规则
export function getRuleStatusDayStatistics() {
  return request({
    url: '/sys/page/ruleStatusDayStatistics',
    method: 'get'
  })
}

// 统计任务总量、当天新增、完成任务
export function getDemandApplyStatistics() {
  return request({
    url: '/sys/page/demandApplyStatistics',
    method: 'get'
  })
}