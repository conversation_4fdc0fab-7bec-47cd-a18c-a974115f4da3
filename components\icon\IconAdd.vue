<!-- 新增图标样式 -->

<script setup lang="ts">
    interface Props {
        size?: number
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 16
    })
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-add icon-svg ReaderLayout-module_actionItem_CbOzz index-module_size_wVASz TemplateTreeItem-module_actionIcon_haD5C" data-name="Add" style="width: 16px; min-width: 16px; height: 16px;"><path d="M128 28c5.523 0 10 4.477 10 10v80h80c5.523 0 10 4.477 10 10s-4.477 10-10 10h-80v80c0 5.523-4.477 10-10 10s-10-4.477-10-10v-80H38c-5.523 0-10-4.477-10-10s4.477-10 10-10h80V38c0-5.523 4.477-10 10-10Z" fill="currentColor" fill-rule="evenodd"></path></svg>
</template>
