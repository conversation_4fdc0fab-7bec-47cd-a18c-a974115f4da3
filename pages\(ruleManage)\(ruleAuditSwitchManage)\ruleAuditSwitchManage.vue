<!-- 规则审核开关管理 -->

<script setup lang="ts">
import { offList, offstate } from "@/api/on_off";
import { getAllBusinessByLoginUser } from "@/api/rule_base";
import SwitchQuery from "@/businessComponents/ruleAuditSwitchManage/SwitchQuery.vue";
import useTableConfig from '@/composables/useTableConfig';
import qs from "qs";

definePageMeta({
    title: '规则审核开关管理',
    path: '/ruleAuditSwitchManage'
})

const modal = inject<any>('modal');
const message = inject<any>('message');

// 定义不包含序号列和操作列的表格列
const tableColumns = [
    {
        title: '规则库名称',
        dataIndex: 'chineseName',
        key: 'chineseName',
        align: 'left',
    },
    {
        title: '修改时间',
        dataIndex: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        align: 'center',
        width: 180,
    },
    {
        title: '修改人',
        dataIndex: 'modifiedId',
        key: 'modifiedId',
        align: 'center',
        width: 120,
    },{
        title: '规则审核开关',
        key: 'checkControlStatus',
        align: 'center',
        dataIndex: 'checkControlStatus',
        width: 150,
        fixed: 'right',
        ellipsis: true,
    }
];

const fetchRules = async (params: Record<string, any> = {}) => {
    try {
        let pars = {
            checkControlStatus: params.offstate,
            chineseName: params.name,
            businessLine: params.businessLine,
            page: params.page || 1,
            several: params.pageSize || 10,
        };

        const res: any = await offList(pars);
        if (res.code === 20000) {
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        }
        return {
            data: [],
            totalCount: 0
        };
    } catch (error) {
        message.error('获取规则审核开关失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

//字典值
const businessLineOptions = ref<Array<{name: string, code: string}>>([]);
const switchStatusOptions = ref([
    {
        name: "开",
        value: "0",
    },
    {
        name: "关",
        value: "1",
    },
]);

// 获取业务条线
const getBusinessLines = async () => {
    try {
        const res = await getAllBusinessByLoginUser();
        businessLineOptions.value = res.data;

        // 更新搜索配置中的业务条线选项
        const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
        if (businessLineField && businessLineField.compConfig) {
            businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
                name: item.name,
                value: item.code
            }));
        }
    } catch (error) {
        message.error('获取业务条线失败');
    }
}

//组件挂载后
onMounted(() => {
    getBusinessLines();
});

// 开关切换处理
const handleSwitchChange = (record: any) => {
    const { checkControlStatus, uuid, chineseName } = record;
    let data = qs.stringify({
        upStatus: checkControlStatus === '0'?'1':'0', //开关状态 1为开 0为关
        uuid: uuid,
    });
    let checkStr = (checkControlStatus==='0'?'停用':'启用');
    if (checkControlStatus === '1') {
        (modal as any).confirm({
            title: () => "提示",
            content: () => "您确定要"+checkStr+"审核控制开关吗?",
            okText: () => '确定',
            okType: 'warning',
            cancelText: () => '取消',
            onOk() {
                offstate(data).then((res: any) => {
                    message.success(checkStr+'成功');
                    listLayout.value.refresh();
                });
            },
            onCancel() {
            },
        });
    } else {
        (modal as any).confirm({
            title: () => "提示",
            content: () => "您确定要关闭审核控制开关吗?",
            okText: () => '确定',
            okType: 'warning',
            cancelText: () => '取消',
            onOk() {
                offstate(data).then((res: any) => {
                    if (res.code === 50002) {
                        (modal as any).confirm({
                            title: () => "提示",
                            content: () => "当前存在待审核规则记录，请先予以处理！",
                            okText: () => '确定',
                            okType: 'warning',
                            cancelText: () => '取消',
                            onOk() {
                                queryInfo.value.key = uuid;
                                queryInfo.value.keyname = chineseName;
                                queryInfo.value.keystatus = checkControlStatus;
                                showSwitchQuery.value = true;
                            },
                            onCancel() {
                                message.info('已取消');
                                listLayout.value.refresh();
                            }
                        });
                    } else {
                        listLayout.value.refresh();
                    }
                });
            },
            onCancel() {
                listLayout.value.refresh();
            },
        });
    }
};

// 查询控制
const showSwitchQuery = ref<boolean>(false);
const queryInfo = ref({
    key: '',
    keyname: '',
    keystatus: '',
});
const handleCloseQuery = () => {
    showSwitchQuery.value = false;
    listLayout.value.refresh();
};

// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '规则库名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '规则库名称',
            field: 'name',
            compType: 'input' as const
        },
        {
            label: '业务条线',
            field: 'businessLine',
            compType: 'select' as const,
            compConfig: {
                options: [] as {name: string, value: string}[]
            }
        },
        {
            label: '开关状态',
            field: 'offstate',
            compType: 'select' as const,
            compConfig: {
                options: switchStatusOptions.value.map(item => ({
                    name: item.name,
                    value: item.value
                }))
            }
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加空的新建事件以满足接口要求
    addNewEvent: () => {},
    // 不显示新建按钮的权限
    addPermission: '',
    // 新增：表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchRules
    }
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);
</script>

<template>
    <ListPage
        ref="listLayout"
        title="规则审核开关管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="false"
        :tableColumns="tableColumns"
        :queryMethod="fetchRules"
        rowKey="uuid"
        :customRenderColumns="['checkControlStatus']"
        :showActionColumn="false"
    >
        <template #checkControlStatus="{ record }">
            <div style="height: 32px; display: flex; justify-content: center; align-items: center;">
                <a-switch
                    :checked="record.checkControlStatus === '0'"
                    @click="handleSwitchChange(record)"
                    size="default"
                />
            </div>
        </template>
    </ListPage>
    <SwitchQuery
            :controlVisible="showSwitchQuery"
            :i="queryInfo"
            @closeModal="handleCloseQuery"
    />
</template>

