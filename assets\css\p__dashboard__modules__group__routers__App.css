.index-module_noWrap_glNxF {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%
}

.index-module_center_zSt9A {
    text-align: center
}

.index-module_inherit_TQ-oW {
    text-align: inherit
}

.index-module_justify_BBNTE {
    text-align: justify
}

.index-module_left_melIP {
    text-align: left
}

.index-module_right_BcDRZ {
    text-align: right
}

.index-module_common-link_LkRIl {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_common-link_LkRIl.active,.index-module_common-link_LkRIl:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-link_LkRIl:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_common-tab_EdL1P {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-menu_OiYt2 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_common-menu_OiYt2.active,.index-module_common-menu_OiYt2:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_common-icon_60unB {
    display: inline-flex;
    align-items: center
}

.index-module_common-flex_YpvU1 {
    display: flex
}

.index-module_common-iconButton_9fUSh {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_common-iconButton_9fUSh svg {
    z-index: 1
}

.index-module_common-iconButton_9fUSh:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_common-iconButton_9fUSh:hover {
    color: var(--yq-text-primary)
}

.index-module_common-iconButton_9fUSh:hover:after {
    visibility: visible
}

.index-module_common-menu-item_wENsl {
    display: flex;
    align-items: center
}

.index-module_headline1_gNn3d {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 32px;
    line-height: 40px
}

.index-module_headline1-link_hpiWt {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline1-link_hpiWt.active,.index-module_headline1-link_hpiWt:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-link_hpiWt:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline1-tab_2tnKG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-menu_kn1nZ {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline1-menu_kn1nZ.active,.index-module_headline1-menu_kn1nZ:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline1-icon_Ugsfz {
    display: inline-flex;
    align-items: center
}

.index-module_headline1-flex_XKxAU {
    display: flex
}

.index-module_headline1-iconButton_JxTPN {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline1-iconButton_JxTPN svg {
    z-index: 1
}

.index-module_headline1-iconButton_JxTPN:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline1-iconButton_JxTPN:hover {
    color: var(--yq-text-primary)
}

.index-module_headline1-iconButton_JxTPN:hover:after {
    visibility: visible
}

.index-module_headline1-menu-item_6KtqL {
    display: flex;
    align-items: center
}

.index-module_headline1-secondary_oSaxw {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline2_xyzBy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 24px;
    line-height: 32px
}

.index-module_headline2-link_bkLCB {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline2-link_bkLCB.active,.index-module_headline2-link_bkLCB:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-link_bkLCB:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline2-tab_n1Akb {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-menu_QL2po {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline2-menu_QL2po.active,.index-module_headline2-menu_QL2po:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline2-icon_Ge-vk {
    display: inline-flex;
    align-items: center
}

.index-module_headline2-flex_KZxEZ {
    display: flex
}

.index-module_headline2-iconButton_uBIj0 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline2-iconButton_uBIj0 svg {
    z-index: 1
}

.index-module_headline2-iconButton_uBIj0:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline2-iconButton_uBIj0:hover {
    color: var(--yq-text-primary)
}

.index-module_headline2-iconButton_uBIj0:hover:after {
    visibility: visible
}

.index-module_headline2-menu-item_1iIzo {
    display: flex;
    align-items: center
}

.index-module_headline2-secondary_t1vqD {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline3_6OwWy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 18px;
    line-height: 26px
}

.index-module_headline3-link_wcUG4 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline3-link_wcUG4.active,.index-module_headline3-link_wcUG4:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-link_wcUG4:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline3-tab_GRz8p {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-menu_pdEQ6 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline3-menu_pdEQ6.active,.index-module_headline3-menu_pdEQ6:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline3-icon_maylx {
    display: inline-flex;
    align-items: center
}

.index-module_headline3-flex_EdA3C {
    display: flex
}

.index-module_headline3-iconButton_ljBJY {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline3-iconButton_ljBJY svg {
    z-index: 1
}

.index-module_headline3-iconButton_ljBJY:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline3-iconButton_ljBJY:hover {
    color: var(--yq-text-primary)
}

.index-module_headline3-iconButton_ljBJY:hover:after {
    visibility: visible
}

.index-module_headline3-menu-item_FneW1 {
    display: flex;
    align-items: center
}

.index-module_headline3-secondary_gdmSF {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline4_cCWAs {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 16px;
    line-height: 24px
}

.index-module_headline4-link_pksrg {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline4-link_pksrg.active,.index-module_headline4-link_pksrg:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-link_pksrg:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline4-tab_AZhw7 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-menu_Zh\+Gf {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline4-menu_Zh\+Gf.active,.index-module_headline4-menu_Zh\+Gf:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline4-icon_I8A2c {
    display: inline-flex;
    align-items: center
}

.index-module_headline4-flex_-WCmH {
    display: flex
}

.index-module_headline4-iconButton_unM1q {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline4-iconButton_unM1q svg {
    z-index: 1
}

.index-module_headline4-iconButton_unM1q:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline4-iconButton_unM1q:hover {
    color: var(--yq-text-primary)
}

.index-module_headline4-iconButton_unM1q:hover:after {
    visibility: visible
}

.index-module_headline4-menu-item_HZvZs {
    display: flex;
    align-items: center
}

.index-module_headline4-secondary_mwc2q {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_title_AMQeB {
    font-weight: 400;
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 22px
}

.index-module_title-link_LhWVV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_title-link_LhWVV.active,.index-module_title-link_LhWVV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-link_LhWVV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_title-tab_PEcOM {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-menu_yRd7C {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_title-menu_yRd7C.active,.index-module_title-menu_yRd7C:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_title-icon_jD1r3 {
    display: inline-flex;
    align-items: center
}

.index-module_title-flex_6ud\+f {
    display: flex
}

.index-module_title-iconButton_eKfGH {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_title-iconButton_eKfGH svg {
    z-index: 1
}

.index-module_title-iconButton_eKfGH:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_title-iconButton_eKfGH:hover {
    color: var(--yq-text-primary)
}

.index-module_title-iconButton_eKfGH:hover:after {
    visibility: visible
}

.index-module_title-menu-item_\+2INX {
    display: flex;
    align-items: center
}

.index-module_title-secondary_\+7kF8 {
    color: var(--yq-text-body)
}

.index-module_title-strong_rB2kd {
    font-weight: 500
}

.index-module_body_0t8B1 {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_body-link_1Ezeh {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-link_1Ezeh.active,.index-module_body-link_1Ezeh:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-link_1Ezeh:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-tab_ZArAR {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-menu_rkEZj {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-menu_rkEZj.active,.index-module_body-menu_rkEZj:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-icon_dtRXT {
    display: inline-flex;
    align-items: center
}

.index-module_body-flex_xUh97 {
    display: flex
}

.index-module_body-iconButton_zEAAb {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-iconButton_zEAAb svg {
    z-index: 1
}

.index-module_body-iconButton_zEAAb:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-iconButton_zEAAb:hover {
    color: var(--yq-text-primary)
}

.index-module_body-iconButton_zEAAb:hover:after {
    visibility: visible
}

.index-module_body-menu-item_530BT {
    display: flex;
    align-items: center
}

.index-module_body-secondary_jzKzN {
    color: var(--yq-text-caption)
}

.index-module_body-strong_B8Xyf {
    font-weight: 500
}

.index-module_body-small_M0nB9 {
    font-weight: 400;
    color: var(--yq-text-body);
    font-size: 12px;
    line-height: 20px
}

.index-module_body-small-link_dpTlG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-small-link_dpTlG.active,.index-module_body-small-link_dpTlG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-link_dpTlG:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-small-tab_HAI-k {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-menu_\+sZ91 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-small-menu_\+sZ91.active,.index-module_body-small-menu_\+sZ91:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-small-icon_9yvQH {
    display: inline-flex;
    align-items: center
}

.index-module_body-small-flex_TzPYW {
    display: flex
}

.index-module_body-small-iconButton_Ry5bn {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-small-iconButton_Ry5bn svg {
    z-index: 1
}

.index-module_body-small-iconButton_Ry5bn:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-small-iconButton_Ry5bn:hover {
    color: var(--yq-text-primary)
}

.index-module_body-small-iconButton_Ry5bn:hover:after {
    visibility: visible
}

.index-module_body-small-menu-item_5h34h {
    display: flex;
    align-items: center
}

.index-module_body-small-secondary_dJo56 {
    color: var(--yq-text-caption)
}

.index-module_disable_bsdL7 {
    font-weight: 400;
    color: var(--yq-text-disable);
    font-size: 14px;
    line-height: 22px
}

.index-module_disable-link_fsP5Q {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-link_fsP5Q.active,.index-module_disable-link_fsP5Q:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-link_fsP5Q:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-tab_1L8n1 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-menu_h0UA9 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-menu_h0UA9.active,.index-module_disable-menu_h0UA9:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-icon_szLm2 {
    display: inline-flex;
    align-items: center
}

.index-module_disable-flex_EDI9g {
    display: flex
}

.index-module_disable-iconButton_41jru {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-iconButton_41jru svg {
    z-index: 1
}

.index-module_disable-iconButton_41jru:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-iconButton_41jru:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-iconButton_41jru:hover:after {
    visibility: visible
}

.index-module_disable-menu-item_tolga {
    display: flex;
    align-items: center
}

.index-module_caption_bWso- {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 14px;
    line-height: 22px
}

.index-module_caption-link_iUcKX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-link_iUcKX.active,.index-module_caption-link_iUcKX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-link_iUcKX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-tab_Pahmo {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-menu_FYIeq {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-menu_FYIeq.active,.index-module_caption-menu_FYIeq:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-icon_Y3NyS {
    display: inline-flex;
    align-items: center
}

.index-module_caption-flex_FI2Np {
    display: flex
}

.index-module_caption-iconButton_hWPqs {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-iconButton_hWPqs svg {
    z-index: 1
}

.index-module_caption-iconButton_hWPqs:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-iconButton_hWPqs:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-iconButton_hWPqs:hover:after {
    visibility: visible
}

.index-module_caption-menu-item_1kLnU {
    display: flex;
    align-items: center
}

.index-module_caption-secondary_SFdcH {
    color: var(--yq-text-disable)
}

.index-module_caption-small_B13mr {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 12px;
    line-height: 20px
}

.index-module_caption-small-link_8T\+\+a {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-small-link_8T\+\+a.active,.index-module_caption-small-link_8T\+\+a:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-link_8T\+\+a:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-small-tab_ibUZP {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-menu_G8H-J {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-small-menu_G8H-J.active,.index-module_caption-small-menu_G8H-J:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-small-icon_PtjdH {
    display: inline-flex;
    align-items: center
}

.index-module_caption-small-flex_\+EFJk {
    display: flex
}

.index-module_caption-small-iconButton_33CTf {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-small-iconButton_33CTf svg {
    z-index: 1
}

.index-module_caption-small-iconButton_33CTf:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-small-iconButton_33CTf:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-small-iconButton_33CTf:hover:after {
    visibility: visible
}

.index-module_caption-small-menu-item_cqmp0 {
    display: flex;
    align-items: center
}

.index-module_caption-small-secondary_H2H1r {
    color: var(--yq-text-disable)
}

.index-module_primary-button-default_Vw1-y {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white)
}

.index-module_primary-button-default-secondary_R\+QUT {
    color: var(--yq-text-caption)
}

.index-module_primary-button-default-strong_Htk5A {
    font-weight: 500
}

.index-module_primary-button-default-link_oQhLa {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-default-link_oQhLa.active,.index-module_primary-button-default-link_oQhLa:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-link_oQhLa:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-default-tab_rf8zH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-menu_JESyh {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-default-menu_JESyh.active,.index-module_primary-button-default-menu_JESyh:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-default-icon_E0TCd {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-default-flex_pNGzV {
    display: flex
}

.index-module_primary-button-default-iconButton_bguHS {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-default-iconButton_bguHS svg {
    z-index: 1
}

.index-module_primary-button-default-iconButton_bguHS:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-default-iconButton_bguHS:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-iconButton_bguHS:hover:after {
    visibility: visible
}

.index-module_primary-button-default-menu-item_EY5rA {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong_tCPsX {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white);
    font-weight: 500
}

.index-module_primary-button-strong-secondary_rR6X1 {
    color: var(--yq-text-caption)
}

.index-module_primary-button-strong-strong_KuhtF {
    font-weight: 500
}

.index-module_primary-button-strong-link_FFk3N {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-strong-link_FFk3N.active,.index-module_primary-button-strong-link_FFk3N:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-link_FFk3N:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-strong-tab_iu42A {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-menu_MUMyr {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-strong-menu_MUMyr.active,.index-module_primary-button-strong-menu_MUMyr:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-strong-icon_-xloa {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-strong-flex_uWzkw {
    display: flex
}

.index-module_primary-button-strong-iconButton_f6r63 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-strong-iconButton_f6r63 svg {
    z-index: 1
}

.index-module_primary-button-strong-iconButton_f6r63:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-strong-iconButton_f6r63:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-iconButton_f6r63:hover:after {
    visibility: visible
}

.index-module_primary-button-strong-menu-item_RzDwe {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong-ghost_LfbnS {
    color: var(--yq-yuque-green-600)
}

.index-module_secondary-button-default_nN4ts {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-link_P4JJV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_secondary-button-default-link_P4JJV.active,.index-module_secondary-button-default-link_P4JJV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-link_P4JJV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_secondary-button-default-tab_sTKaj {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-menu_CnLQt {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_secondary-button-default-menu_CnLQt.active,.index-module_secondary-button-default-menu_CnLQt:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-icon_EBrIo {
    display: inline-flex;
    align-items: center
}

.index-module_secondary-button-default-flex_myAK8 {
    display: flex
}

.index-module_secondary-button-default-iconButton_3l8nj {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_secondary-button-default-iconButton_3l8nj svg {
    z-index: 1
}

.index-module_secondary-button-default-iconButton_3l8nj:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_secondary-button-default-iconButton_3l8nj:hover {
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-iconButton_3l8nj:hover:after {
    visibility: visible
}

.index-module_secondary-button-default-menu-item_OZFHO {
    display: flex;
    align-items: center
}

.index-module_secondary-button-default-secondary_iH73C {
    color: var(--yq-text-caption)
}

.index-module_secondary-button-default-strong_U1PcZ {
    font-weight: 500
}

.index-module_disable-button-default_do2Ky {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable)
}

.index-module_disable-button-default-link_Cc4nX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-button-default-link_Cc4nX.active,.index-module_disable-button-default-link_Cc4nX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-link_Cc4nX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-button-default-tab_uzzYS {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-menu_ki5Hs {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-button-default-menu_ki5Hs.active,.index-module_disable-button-default-menu_ki5Hs:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-button-default-icon_\+eAaQ {
    display: inline-flex;
    align-items: center
}

.index-module_disable-button-default-flex_pSS0P {
    display: flex
}

.index-module_disable-button-default-iconButton_Ns-ZT {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-button-default-iconButton_Ns-ZT svg {
    z-index: 1
}

.index-module_disable-button-default-iconButton_Ns-ZT:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover:after {
    visibility: visible
}

.index-module_disable-button-default-menu-item_H7X58 {
    display: flex;
    align-items: center
}

.index-module_disable-button-default-secondary_sJwUn {
    color: var(--yq-text-caption)
}

.index-module_disable-button-default-strong_ApPSw {
    font-weight: 500
}

.index-module_link_4lMzA {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_link-link_MSu6v {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_link-link_MSu6v.active,.index-module_link-link_MSu6v:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-link_MSu6v:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_link-tab_UaSsE {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-menu_X9-jl {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_link-menu_X9-jl.active,.index-module_link-menu_X9-jl:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_link-icon_GlYMK {
    display: inline-flex;
    align-items: center
}

.index-module_link-flex_y87zq {
    display: flex
}

.index-module_link-iconButton_hFUyW {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_link-iconButton_hFUyW svg {
    z-index: 1
}

.index-module_link-iconButton_hFUyW:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_link-iconButton_hFUyW:hover {
    color: var(--yq-text-primary)
}

.index-module_link-iconButton_hFUyW:hover:after {
    visibility: visible
}

.index-module_link-menu-item_QrtKv {
    display: flex;
    align-items: center
}

.index-module_link-secondary_cYp8F {
    color: var(--yq-text-caption)
}

.index-module_link-strong_6cupy {
    font-weight: 500
}

.index-module_link_4lMzA:hover {
    color: var(--yq-blue-6)
}

.index-module_linkBtn_viBrQ {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable);
    padding: 4.5px;
    display: flex;
    align-items: center;
    border-radius: 6px
}

.index-module_linkBtn-link_fRNiK {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_linkBtn-link_fRNiK.active,.index-module_linkBtn-link_fRNiK:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-link_fRNiK:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_linkBtn-tab_xLPRH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-menu_K8vUm {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_linkBtn-menu_K8vUm.active,.index-module_linkBtn-menu_K8vUm:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_linkBtn-icon_bNwRp {
    display: inline-flex;
    align-items: center
}

.index-module_linkBtn-flex_Xds\+s {
    display: flex
}

.index-module_linkBtn-iconButton_nWnGl {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_linkBtn-iconButton_nWnGl svg {
    z-index: 1
}

.index-module_linkBtn-iconButton_nWnGl:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_linkBtn-iconButton_nWnGl:hover {
    color: var(--yq-text-primary)
}

.index-module_linkBtn-iconButton_nWnGl:hover:after {
    visibility: visible
}

.index-module_linkBtn-menu-item_rDBK\+ {
    display: flex;
    align-items: center
}

.index-module_linkBtn-secondary_pxTMi {
    color: var(--yq-text-caption)
}

.index-module_linkBtn-strong_5PlMk {
    font-weight: 500
}

.index-module_linkBtn_viBrQ:hover {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-yuque-grey-9)
}

html[data-kumuhana=pouli] .index-module_linkBtn_viBrQ:hover {
    color: var(--yq-yuque-grey-9)!important
}

.index-module_yuque-green-1_t\+Yes {
    color: var(--yq-yuque-green-1)
}

.index-module_yuque-green-2_PSwCn {
    color: var(--yq-yuque-green-2)
}

.index-module_yuque-green-3_990Pj {
    color: var(--yq-yuque-green-3)
}

.index-module_yuque-green-4_LGQYT {
    color: var(--yq-yuque-green-4)
}

.index-module_yuque-green-5_Wdkpl {
    color: var(--yq-yuque-green-5)
}

.index-module_yuque-green-6_sp\+C1 {
    color: var(--yq-yuque-green-6)
}

.index-module_yuque-green-7_Yz0nb {
    color: var(--yq-yuque-green-7)
}

.index-module_yuque-green-8_TdQoj {
    color: var(--yq-yuque-green-8)
}

.index-module_yuque-green-9_78uX8 {
    color: var(--yq-yuque-green-9)
}

.index-module_yuque-grey-1_MFlzw {
    color: var(--yq-yuque-grey-1)
}

.index-module_yuque-grey-2_1MPla {
    color: var(--yq-yuque-grey-2)
}

.index-module_yuque-grey-3_Xwd3D {
    color: var(--yq-yuque-grey-3)
}

.index-module_yuque-grey-4_Iaalm {
    color: var(--yq-yuque-grey-4)
}

.index-module_yuque-grey-5_dTMGn {
    color: var(--yq-yuque-grey-5)
}

.index-module_yuque-grey-6_9P9mX {
    color: var(--yq-yuque-grey-6)
}

.index-module_yuque-grey-7_fo9a2 {
    color: var(--yq-yuque-grey-7)
}

.index-module_yuque-grey-8_5ZItk {
    color: var(--yq-yuque-grey-8)
}

.index-module_yuque-grey-9_2KrCK {
    color: var(--yq-yuque-grey-9)
}

.index-module_blue-1_9NXmy {
    color: var(--yq-blue-1)
}

.index-module_blue-2_71Xfa {
    color: var(--yq-blue-2)
}

.index-module_blue-3_QzBMA {
    color: var(--yq-blue-3)
}

.index-module_blue-4_eIuv6 {
    color: var(--yq-blue-4)
}

.index-module_blue-5_--rJ2 {
    color: var(--yq-blue-5)
}

.index-module_blue-6_EM\+ye {
    color: var(--yq-blue-6)
}

.index-module_blue-7_dcd5v {
    color: var(--yq-blue-7)
}

.index-module_blue-8_i6IXc {
    color: var(--yq-blue-8)
}

.index-module_blue-9_8WLz4 {
    color: var(--yq-blue-9)
}

.index-module_organge-1_QF-zP {
    color: var(--yq-orange-1)
}

.index-module_organge-2_zYIHH {
    color: var(--yq-orange-2)
}

.index-module_organge-3_EaUTx {
    color: var(--yq-orange-3)
}

.index-module_organge-4_KEzWt {
    color: var(--yq-orange-4)
}

.index-module_organge-5_uWCqi {
    color: var(--yq-orange-5)
}

.index-module_organge-6_SWaCL {
    color: var(--yq-orange-6)
}

.index-module_organge-7_UCnnq {
    color: var(--yq-orange-7)
}

.index-module_organge-8_yxbUr {
    color: var(--yq-orange-8)
}

.index-module_organge-9_Wqbx6 {
    color: var(--yq-orange-9)
}

.index-module_yellow-1_9BdDH {
    color: var(--yq-yellow-1)
}

.index-module_yellow-2_mmpuD {
    color: var(--yq-yellow-2)
}

.index-module_yellow-3_1MgEh {
    color: var(--yq-yellow-3)
}

.index-module_yellow-4_twlST {
    color: var(--yq-yellow-4)
}

.index-module_yellow-5_Z7Kqp {
    color: var(--yq-yellow-5)
}

.index-module_yellow-6_EZhZz {
    color: var(--yq-yellow-6)
}

.index-module_yellow-7_lCkCE {
    color: var(--yq-yellow-7)
}

.index-module_yellow-8_ZQ7hX {
    color: var(--yq-yellow-8)
}

.index-module_yellow-9_NAs4Y {
    color: var(--yq-yellow-9)
}

.index-module_red-1_Z-G\+I {
    color: var(--yq-red-1)
}

.index-module_red-2_ec26p {
    color: var(--yq-red-2)
}

.index-module_red-3_CitaX {
    color: var(--yq-red-3)
}

.index-module_red-4_Rf4VB {
    color: var(--yq-red-4)
}

.index-module_red-5_8DEQO {
    color: var(--yq-red-5)
}

.index-module_red-6_e40Pl {
    color: var(--yq-red-6)
}

.index-module_red-7_ND3LM {
    color: var(--yq-red-7)
}

.index-module_red-8_S4EAE {
    color: var(--yq-red-8)
}

.index-module_red-9_s0fc1 {
    color: var(--yq-red-9)
}

.index-module_magenta-1_BVOfc {
    color: var(--yq-magenta-1)
}

.index-module_magenta-2_LeAiv {
    color: var(--yq-magenta-2)
}

.index-module_magenta-3_anmFf {
    color: var(--yq-magenta-3)
}

.index-module_magenta-4_iyuji {
    color: var(--yq-magenta-4)
}

.index-module_magenta-5_XFO8v {
    color: var(--yq-magenta-5)
}

.index-module_magenta-6_83nvI {
    color: var(--yq-magenta-6)
}

.index-module_magenta-7_EylhI {
    color: var(--yq-magenta-7)
}

.index-module_magenta-8_H7UoN {
    color: var(--yq-magenta-8)
}

.index-module_magenta-9_BZu8L {
    color: var(--yq-magenta-9)
}

.index-module_purple-1_4Mt5K {
    color: var(--yq-purple-1)
}

.index-module_purple-2_ZU8FB {
    color: var(--yq-purple-2)
}

.index-module_purple-3_XZk6m {
    color: var(--yq-purple-3)
}

.index-module_purple-4_RkQAl {
    color: var(--yq-purple-4)
}

.index-module_purple-5_UwSof {
    color: var(--yq-purple-5)
}

.index-module_purple-6_VE5X4 {
    color: var(--yq-purple-6)
}

.index-module_purple-7_4qxQ3 {
    color: var(--yq-purple-7)
}

.index-module_purple-8_34VRx {
    color: var(--yq-purple-8)
}

.index-module_purple-9_S5yo0 {
    color: var(--yq-purple-9)
}

.index-module_cyan-1_XcHSS {
    color: var(--yq-cyan-1)
}

.index-module_cyan-2_8Z0Qc {
    color: var(--yq-cyan-2)
}

.index-module_cyan-3_8EpV- {
    color: var(--yq-cyan-3)
}

.index-module_cyan-4_BTV0V {
    color: var(--yq-cyan-4)
}

.index-module_cyan-5_OOfUa {
    color: var(--yq-cyan-5)
}

.index-module_cyan-6_8F8Ne {
    color: var(--yq-cyan-6)
}

.index-module_cyan-7_tQGo7 {
    color: var(--yq-cyan-7)
}

.index-module_cyan-8_nBcrC {
    color: var(--yq-cyan-8)
}

.index-module_cyan-9_pp0HZ {
    color: var(--yq-cyan-9)
}

.index-module_pea-green-1_YZ7VF {
    color: var(--yq-pea-green-1)
}

.index-module_pea-green-2_cB-wj {
    color: var(--yq-pea-green-2)
}

.index-module_pea-green-3_0aW\+j {
    color: var(--yq-pea-green-3)
}

.index-module_pea-green-4_V3TMD {
    color: var(--yq-pea-green-4)
}

.index-module_pea-green-5_UQ6\+a {
    color: var(--yq-pea-green-5)
}

.index-module_pea-green-6_VBEAH {
    color: var(--yq-pea-green-6)
}

.index-module_pea-green-7_fz-pg {
    color: var(--yq-pea-green-7)
}

.index-module_pea-green-8_vhbae {
    color: var(--yq-pea-green-8)
}

.index-module_pea-green-9_iLSdb {
    color: var(--yq-pea-green-9)
}

.index-module_white_-Ikm2 {
    color: var(--yq-white)
}

.index-module_yuque-color-text-disable_H8og1 {
    color: var(--yq-text-disable)
}

.index-module_flexFix_gj50B {
    width: 0;
    flex: 1
}

.module-error {
    padding: 80px 0 120px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center
}

.module-error .error-tip {
    text-align: center;
    line-height: 2;
    color: var(--yq-text-caption)
}

.module-error .error-tip>h3 {
    font-size: 32px;
    font-weight: 400;
    line-height: 2.2;
    color: var(--yq-text-body)
}

.module-error .image {
    max-width: 400px
}

@media screen and (max-width: 768px) {
    .module-error .image {
        max-width:170px
    }
}

.index-module_size_wVASz {
    width: 24px;
    height: 24px
}

.note-status-module_dev_g73YZ {
    background-color: var(--yq-yuque-grey-6);
    padding: 0 4px;
    position: absolute;
    right: 0;
    top: 0;
    opacity: .5;
    pointer-events: none
}

.note-status-module_publicInfo_8wLHo {
    background: var(--yq-bg-secondary);
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-5);
    padding: 2px 4px;
    border-radius: 4px;
    margin-left: 8px;
    font-size: 12px;
    cursor: pointer
}

.note-status-module_text_ZLRRs {
    font-size: 12px;
    padding-left: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    color: var(--yq-text-body);
    background-color: var(--yq-bg-secondary);
    border-color: var(--yq-border-primary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border: 1px solid var(--yq-blue-1);
    color: var(--yq-blue-9)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    color: var(--yq-icon-secondary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-icon-primary)
}

.TagList-module_wrapper_wAu1a {
    padding: 12px 0 0 16px;
    position: relative;
    font-size: 0
}

.TagList-module_wrapper_wAu1a.TagList-module_hideMore_2Kx5n {
    height: 50px;
    overflow: hidden
}

.TagList-module_wrapper_wAu1a.TagList-module_expand_J-EtW {
    height: auto;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 0 0 4px 4px
}

.TagList-module_wrapper_wAu1a.TagList-module_expandForMini_G9K8m {
    height: 40px!important;
    width: auto;
    display: flex
}

.TagList-module_list_g-vtQ {
    margin-bottom: 0
}

.TagList-module_list_g-vtQ .TagList-module_line_pgNjs {
    margin-right: 8px;
    width: 1px;
    height: 16px;
    background-color: var(--yq-yuque-grey-4);
    display: inline-block;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 8px
}

.TagList-module_list_g-vtQ .TagList-module_addTag_P9XTi {
    display: inline-block;
    height: 30px;
    margin-bottom: 8px;
    vertical-align: middle
}

.TagList-module_list_g-vtQ .TagList-module_rightSide_65\+zS {
    float: right;
    display: flex
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    vertical-align: middle;
    display: inline-flex;
    max-width: 100%;
    border-radius: 15px;
    margin-bottom: 8px;
    height: 30px;
    color: var(--yq-text-body);
    padding: 0 8px;
    background-color: var(--yq-bg-primary);
    margin-right: 8px;
    border: 1px solid var(--yq-border-primary);
    justify-content: center;
    align-items: center
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg {
    margin-right: 4px
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg path {
    fill: currentColor
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8:hover {
    color: var(--yq-text-body)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border-color: var(--yq-blue-1);
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_canSelect_XVnT1 {
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ {
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    height: 30px;
    line-height: 30px
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ.TagList-module_highLight_Fx\+g3 {
    color: var(--yq-red-6)
}

.TagList-module_list_g-vtQ .TagList-module_count_JG6aO {
    margin-left: 4px;
    font-size: 14px;
    height: 30px;
    line-height: 28px
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    width: 18px;
    position: relative;
    display: inline-block;
    left: 8px;
    margin-right: 4px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 svg {
    height: 12px;
    width: 12px
}

.TagList-module_listForMini_n1Fjy {
    display: flex;
    width: 415px;
    overflow-x: auto;
    overflow-y: hidden
}

.TagList-module_expandBtn_kA52P {
    padding: 0 12px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.TagList-module_expandBtn_kA52P.TagList-module_expanded_tVFvP .TagList-module_icon_zLTbc {
    transform: rotate(-90deg)
}

.TagList-module_expandBtn_kA52P .TagList-module_icon_zLTbc {
    transform: rotate(90deg);
    font-size: 12px;
    transition: transform .3s ease-in
}

html[data-kumuhana=pouli] .index-module_toolBarBtn_kG8Cd,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_viewBlank_ecM7l,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J .ant-card-body {
    background: var(--yq-bg-secondary)
}

html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_tagPinned_--NI2 {
    color: var(--yq-theme)
}

.index-module_toolBarBtn_kG8Cd {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_descMoreIcon_wlBa3 {
    color: var(--yq-blue-4)
}

.index-module_wrapper_8ethE {
    min-height: calc(100vh - 178px)
}

.index-module_wrapper_8ethE .ne-editor-wrap-content {
    max-height: none!important
}

.index-module_wrapper_8ethE .ne-card-video [data-testid=controls] [data-testid=time] {
    display: none
}

.index-module_wrapper_8ethE .view-more-mask .yuque-doc-content:after {
    content: "";
    display: block;
    position: absolute;
    bottom: 7px;
    z-index: 1;
    height: 26px;
    width: 100%;
    background-image: linear-gradient(180deg,hsla(0,0%,100%,0),var(--yq-white))
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m {
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    align-items: center;
    margin-bottom: 16px
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m:hover {
    cursor: pointer;
    background: transparent!important
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowUp_lRGKc {
    color: var(--yq-yuque-grey-7);
    margin-left: 8px;
    transform: rotate(0deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowDown_3-EN3 {
    transform: rotate(-90deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj {
    margin-top: 16px
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj:hover {
    cursor: auto
}

.index-module_wrapper_8ethE .index-module_hidden_M8z9j {
    display: none
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 0 8px 0 0;
    top: 0;
    right: 0;
    overflow: hidden
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT .index-module_tagPinned_--NI2 {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--yq-yuque-green-2)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 {
    padding: 0 64px;
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItem_dVp5J {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItemExpand_PjrCj {
    padding-bottom: 64px
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .note-list-toolbar,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_leftToolBar_JMyy5,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_noteItem_dVp5J {
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_loading_uVMCS {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    text-align: center;
    padding-top: 80px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_mask_-nOYf {
    position: absolute;
    left: 0;
    top: 0;
    background-color: hsla(0,0%,100%,.8);
    height: 100%;
    width: 100%
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list {
    margin-top: 4px;
    padding: 0;
    background: transparent
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list li {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .ant-card-body .yuque-doc-content {
    min-height: 30px;
    padding-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewBlank_ecM7l,.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewMore_gsQbm {
    visibility: hidden
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J {
    padding: 20px 12px 8px;
    position: relative;
    box-shadow: 0 0 1px -1px rgba(0,0,0,.08),0 1px 2px 0 rgba(0,0,0,.04),0 2px 4px 1px rgba(0,0,0,.02);
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--yq-bg-primary);
    transition: box-shadow .2s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_searchMode_jG3fm {
    padding-left: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL {
    font-size: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    min-height: 24px;
    justify-content: space-between;
    position: relative;
    line-height: 24px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-checked:after,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-inner {
    border-radius: 50%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_radio_2HGaM {
    margin-right: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm {
    border: none;
    min-height: 40px;
    position: relative;
    transition: box-shadow .5s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .ant-card-body {
    padding: 8px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI {
    max-height: none!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content>div {
    max-height: none!important;
    overflow: auto!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content:after {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ {
    max-height: none;
    overflow: visible;
    background-color: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor {
    min-height: 30px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-editor-wrap-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-engine {
    min-height: 30px!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-editor-body {
    background-color: var(--yq-bg-secondary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-engine {
    min-height: 100vh!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor-wrapper {
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-tag-add {
    margin-bottom: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_hasTag_wtzsZ {
    max-height: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewBlank_ecM7l {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 16px;
    z-index: 9;
    background: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 8px 0 0 12px;
    cursor: pointer;
    z-index: 9;
    font-size: 12px;
    color: var(--yq-text-body)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm svg {
    position: relative;
    top: 2px;
    right: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMoreShow_pN6H7 {
    display: block
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view div[data-card-type=block] .lake-card-toolbar,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view span[data-card-type=inline] .lake-card-toolbar {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp {
    margin: 0 64px 12px;
    padding: 20px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_date_095hy {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 {
    display: flex;
    flex-wrap: wrap
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU {
    width: 80px;
    height: 80px;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU img {
    width: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU {
    margin-bottom: 8px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU a {
    margin-top: 8px;
    color: var(--yq-text-primary);
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_title_6RJyp {
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_desc_WTwbK {
    color: var(--yq-text-caption);
    font-size: 14px;
    margin-top: 8px;
    white-space: nowrap;
    word-break: break-all;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b {
    color: var(--yq-text-body);
    font-size: 12px;
    margin-top: 14px;
    display: flex;
    align-items: center;
    min-height: 18px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b img {
    width: 12px;
    height: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b .index-module_text_bro5m {
    margin-left: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_left_s-MXM {
    width: calc(100% - 140px);
    padding: 11px 18px 11px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX {
    width: 116px;
    height: 76px;
    border-radius: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX img {
    height: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU {
    margin-bottom: 4px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 10px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM {
    display: flex;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_name_fKT1c {
    color: var(--yq-text-primary);
    font-size: 14px;
    margin-left: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_size_xQNXJ {
    color: var(--yq-text-disable);
    padding-left: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_right_RJVuX a {
    color: #3f4b63
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    text-align: center;
    padding-top: calc(50vh - 200px);
    overflow: hidden;
    font-size: 14px;
    color: var(--yq-text-caption);
    margin-top: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k img {
    margin-bottom: 8px;
    width: 120px
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W {
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W,.index-module_wrapper_8ethE .index-module_theEndTips_tq\+NY {
    text-align: center;
    padding: 16px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_8ethE .index-module_blank_2HJdh {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 16px 0 16px;
    color: var(--yq-text-caption);
    text-align: center
}

.index-module_wrapper_8ethE.index-module_desktop_oahLD {
    min-height: calc(100vh - 96px)
}

.note-public-modal .ant-modal-body {
    padding: 0!important
}

.index-module_leftToolBar_JMyy5,.index-module_toolbarStyle_cNHWw {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out
}

.index-module_leftToolBar_JMyy5 {
    left: 0
}

.index-module_leftToolBar_JMyy5.index-module_batchProcess_BqhA4 {
    opacity: 1
}

.index-module_leftToolBar_JMyy5 .index-module_checkBox_mz\+g\+ {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    margin-left: 30px
}

.index-module_rightToolBar_e1gvN {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out;
    width: 64px;
    right: 0
}

.index-module_rightToolBar_e1gvN.index-module_batchProcess_BqhA4 {
    display: none
}

.index-module_rightToolBar_e1gvN .note-list-toolbar {
    opacity: 1
}

.index-module_rightToolBarCollapse_cPTfb {
    color: var(--yq-text-body);
    top: calc(100% - 70px)
}

.index-module_rightToolBarCollapse_cPTfb,.index-module_rightToolBarSave_vhDcz {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_rightToolBarSave_vhDcz {
    color: var(--yq-yuque-green-6);
    top: 0
}

.index-module_tagContainer_nVqVt {
    width: 1px;
    height: 1px;
    position: absolute;
    right: 0;
    top: 80px
}

.index-module_tagContainer_nVqVt .note-tag-popover .ant-popover-inner {
    margin-top: -170px
}

.search-module_wrapper_S445p {
    display: flex;
    align-items: center;
    position: relative
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl {
    width: 64px;
    opacity: 0
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_active_eYHT\+ {
    transition: width .15s ease;
    width: 200px;
    opacity: 1
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper {
    transition: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper-focused {
    border-color: none;
    box-shadow: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_isLiteMode_C4hZN {
    width: 100%
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h {
    font-size: 14px;
    text-align: right;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: -1px;
    padding: 6px 0 6px 4px;
    opacity: 0;
    pointer-events: none;
    min-width: 76px
}

.search-module_wrapper_S445p .search-module_searchMiniShow_xFcgm {
    transition: opacity .3s ease-in;
    opacity: 1;
    pointer-events: auto
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h .larkui-icon-help-search {
    color: var(--yq-icon-primary)!important;
    font-size: 16px;
    transform: translateY(2px)
}

.sidebar-toolbar-module_wrapper_oE4lW {
    padding: 0;
    cursor: pointer;
    transition: opacity .5s ease-in-out
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--yq-text-link)
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c svg {
    margin-right: 4px
}

.index-module_wrapper_kkqhF {
    overflow-x: hidden
}

.index-module_wrapper_kkqhF .note-list {
    height: calc(100vh - 320px);
    overflow-y: scroll;
    min-height: 0
}

.index-module_wrapper_kkqhF .note-list-item {
    padding: 0;
    margin-bottom: 12px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-yuque-grey-1);
    border-radius: 4px
}

.index-module_wrapper_kkqhF .note-item-normal {
    background: var(--yq-bg-secondary)
}

.index-module_wrapper_kkqhF .note-item {
    box-shadow: none!important
}

.index-module_wrapper_kkqhF .note-item,.index-module_wrapper_kkqhF .view-blank {
    background: var(--yq-bg-secondary)!important
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 {
    height: 77px;
    overflow-y: hidden;
    margin-top: 8px;
    display: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4.index-module_show_oAR0z {
    display: block
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch {
    height: auto
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transform: rotate(-180deg)
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD {
    cursor: pointer;
    margin-top: 2px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transition: transform .3s ease-in;
    position: relative;
    left: 5px;
    top: 3px
}

.index-module_wrapper_kkqhF .index-module_notesList_xu6a8 {
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B {
    padding: 0;
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B .note-tag-list {
    background-color: transparent!important;
    padding: 0
}

.material-library-view {
    width: 100%
}

.material-library-view .ant-tabs {
    height: 100%
}

.material-library-view .ant-tabs .ant-tabs-tab {
    padding-top: 0
}

.material-library-view .ant-tabs .ant-tabs-ink-bar {
    background: var(--yq-yuque-grey-9)
}

.material-library-view .ant-tabs .ant-tabs-content-holder {
    display: flex;
    flex: 1
}

.index-module_ellipsis-text_P9SIT {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_lP3f7 {
    width: 100%;
    min-height: 22px
}

.index-module_container_lP3f7 .ne-doc-minor-editor {
    border-color: transparent
}

.index-module_container_lP3f7 #lark-mini-editor {
    border-color: transparent!important
}

.index-module_container_lP3f7 .ne-doc-micro-editor .ne-simple-ui.ne-editor {
    background: none
}

.index-module_border_\+KKxe {
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px
}

.index-module_placeholder_BB\+lM {
    border-radius: 4px;
    min-height: 22px;
    width: 100%
}

.placeholder-avatar {
    display: inline-block;
    border-radius: 50%;
    color: var(--yq-white);
    text-align: center
}

.index-module_circle_xLGTZ {
    display: inline;
    color: var(--yq-text-caption)
}

.index-module_timeline_wHf9r {
    color: var(--yq-text-body);
    padding-bottom: 12px
}

.index-module_timeline_wHf9r .ant-timeline-item-head-custom {
    display: flex;
    background: none
}

.index-module_timeline_wHf9r .ant-timeline-item-tail {
    left: 4.5px;
    border-left-width: 1px
}

.index-module_commentActions_nAB4L {
    visibility: visible;
    margin-top: 4px;
    margin-left: -2px
}

.index-module_commentContainer_cxuod {
    margin-top: 0;
    visibility: visible;
    padding-bottom: 16px
}

.index-module_commentContainer_cxuod .ne-doc-minor-editor .ne-simple-ui .ne-engine {
    min-height: 80px
}

.index-module_commentContainer_cxuod:hover .index-module_extraAction_cYXjg {
    visibility: visible
}

.index-module_commentContainer_cxuod:last-child {
    padding-bottom: 0
}

.index-module_actionBtn_cdqST svg {
    color: var(--yq-text-caption)
}

.index-module_extraAction_cYXjg {
    visibility: hidden
}

.index-module_extraAction_cYXjg svg {
    color: var(--yq-text-caption)
}

.index-module_replayFormItem_fuuqg {
    margin-top: 4px;
    margin-bottom: 12px
}

.index-module_replayFormItemActions_A\+dOY {
    margin-bottom: 12px
}

.index-module_viewOnReplay_8eEVR {
    margin-top: 8px
}

.index-module_deleteComment_hPr64 {
    background-color: var(--yq-bg-tertiary);
    color: var(--yq-text-caption);
    padding: 3px 4px;
    border-radius: 4px
}

.index-module_normalIcon_FcfCs {
    width: 7px;
    height: 7px;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 50%
}

.index-module_normalIconSmall_sByCP {
    width: 2px;
    height: 2px
}

.index-module_archive_Z\+4gh {
    color: var(--yq-blue-5)
}

.index-module_userName_vmnnU {
    max-width: 200px
}

.index-module_actionContent_IM2YA {
    word-break: break-all
}

.index-module_actionContent_IM2YA h6,.index-module_actionContent_IM2YA p {
    display: inline-flex;
    word-break: break-all;
    margin-right: 6px!important;
    white-space: break-spaces;
    vertical-align: bottom
}

.index-module_actionContent_IM2YA h6 {
    color: var(--yq-yuque-grey-9)
}

.index-module_actionContent_IM2YA div {
    display: inline-flex;
    word-break: break-all
}

.index-module_actionContent_IM2YA .index-module_actionUserName_N7cZ1 {
    color: var(--yq-yuque-grey-9)
}

.index-module_comment_dSrZu {
    margin-bottom: 28px;
    padding-bottom: 0;
    margin-top: 18px
}

.index-module_comment_dSrZu .ant-timeline-item-tail {
    height: calc(100% + 46px);
    top: -18px
}

.index-module_changeRelationshipName_QF6eT {
    color: var(--yq-yuque-grey-9);
    display: flex;
    align-items: center
}

.index-module_packetActivity_7bxUF {
    display: flex;
    align-items: center
}

.ColorTag-module_ellipsis-text_EDTve {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ColorTag-module_grid1_7n-EF {
    background-color: var(--yq-blue-1);
    color: var(--yq-blue-9)
}

.ColorTag-module_grid2_UAsf4 {
    background-color: var(--yq-cyan-1);
    color: var(--yq-cyan-9)
}

.ColorTag-module_grid3_451bH {
    background-color: var(--yq-yuque-green-1);
    color: var(--yq-yuque-green-9)
}

.ColorTag-module_grid4_PZJh9 {
    background-color: var(--yq-pea-green-1);
    color: var(--yq-pea-green-9)
}

.ColorTag-module_grid5_H57V1 {
    background-color: var(--yq-yellow-1);
    color: var(--yq-yellow-9)
}

.ColorTag-module_grid6_kzO19 {
    background-color: var(--yq-brown-1);
    color: var(--yq-brown-9)
}

.ColorTag-module_grid7_vv\+oe {
    background-color: var(--yq-red-1);
    color: var(--yq-red-9)
}

.ColorTag-module_grid8_AslUw {
    background-color: var(--yq-sea-blue-1);
    color: var(--yq-sea-blue-9)
}

.ColorTag-module_grid9_0Zj1g {
    background-color: var(--yq-purple-1);
    color: var(--yq-purple-9)
}

.ColorTag-module_grid10_n7Ls1 {
    background-color: var(--yq-yuque-grey-200);
    color: var(--yq-yuque-grey-900)
}

.ColorTag-module_grid11_XUEgG {
    background-color: var(--yq-blue-3);
    color: var(--yq-blue-9)
}

.ColorTag-module_grid12_nIseE {
    background-color: var(--yq-cyan-3);
    color: var(--yq-cyan-9)
}

.ColorTag-module_grid13_T8moL {
    background-color: var(--yq-yuque-green-3);
    color: var(--yq-yuque-green-9)
}

.ColorTag-module_grid14_gj5CE {
    background-color: var(--yq-pea-green-3);
    color: var(--yq-pea-green-9)
}

.ColorTag-module_grid15_OQ0UD {
    background-color: var(--yq-yellow-3);
    color: var(--yq-yellow-9)
}

.ColorTag-module_grid16_imtvU {
    background-color: var(--yq-brown-3);
    color: var(--yq-brown-9)
}

.ColorTag-module_grid17_tqPni {
    background-color: var(--yq-red-3);
    color: var(--yq-red-9)
}

.ColorTag-module_grid18_vw\+nU {
    background-color: var(--yq-sea-blue-3);
    color: var(--yq-sea-blue-9)
}

.ColorTag-module_grid19_to7QI {
    background-color: var(--yq-purple-3);
    color: var(--yq-purple-9)
}

.ColorTag-module_grid20_9jDeN {
    background-color: var(--yq-yuque-grey-600);
    color: var(--yq-yuque-grey-900)
}

.ColorTag-module_grid1Border_K5WIv {
    border: 1px solid var(--yq-blue-2)
}

.ColorTag-module_grid2Border_NYpgT {
    border: 1px solid var(--yq-cyan-2)
}

.ColorTag-module_grid3Border_4zEH3 {
    border: 1px solid var(--yq-yuque-green-2)
}

.ColorTag-module_grid4Border_buGgL {
    border: 1px solid var(--yq-pea-green-2)
}

.ColorTag-module_grid5Border_7thMK {
    border: 1px solid var(--yq-yellow-2)
}

.ColorTag-module_grid6Border_rEWTu {
    border: 1px solid var(--yq-brown-2)
}

.ColorTag-module_grid7Border_LbChI {
    border: 1px solid var(--yq-red-2)
}

.ColorTag-module_grid8Border_IwAfA {
    border: 1px solid var(--yq-sea-blue-2)
}

.ColorTag-module_grid9Border_mn2Cl {
    border: 1px solid var(--yq-purple-2)
}

.ColorTag-module_grid10Border_T36NN {
    border: 1px solid var(--yq-yuque-grey-100)
}

.ColorTag-module_grid11Border_8ECdt {
    border: 1px solid var(--yq-blue-4)
}

.ColorTag-module_grid12Border_xhdXl {
    border: 1px solid var(--yq-cyan-4)
}

.ColorTag-module_grid13Border_2siKX {
    border: 1px solid var(--yq-yuque-green-4)
}

.ColorTag-module_grid14Border_Xm5v8 {
    border: 1px solid var(--yq-pea-green-4)
}

.ColorTag-module_grid15Border_4YQPb {
    border: 1px solid var(--yq-yellow-4)
}

.ColorTag-module_grid16Border_ojrsV {
    border: 1px solid var(--yq-brown-4)
}

.ColorTag-module_grid17Border_VW4ES {
    border: 1px solid var(--yq-red-4)
}

.ColorTag-module_grid18Border_4FwGi {
    border: 1px solid var(--yq-sea-blue-4)
}

.ColorTag-module_grid19Border_StHF9 {
    border: 1px solid var(--yq-purple-4)
}

.ColorTag-module_grid20Border_II7nI {
    border: 1px solid var(--yq-yuque-grey-400)
}

.ColorTag-module_gridPacket1_EC3ur {
    background-color: var(--yq-blue-6);
    color: var(--yq-blue-6)
}

.ColorTag-module_gridPacket2_fFb1\+ {
    background-color: var(--yq-cyan-6);
    color: var(--yq-cyan-6)
}

.ColorTag-module_gridPacket3_aUeV9 {
    background-color: var(--yq-yuque-green-6);
    color: var(--yq-yuque-green-6)
}

.ColorTag-module_gridPacket4_hhDfK {
    background-color: var(--yq-pea-green-6);
    color: var(--yq-pea-green-6)
}

.ColorTag-module_gridPacket5_xwvIs {
    background-color: var(--yq-yellow-6);
    color: var(--yq-yellow-6)
}

.ColorTag-module_gridPacket6_2cwe6 {
    background-color: var(--yq-orange-6);
    color: var(--yq-orange-6)
}

.ColorTag-module_gridPacket7_qHO9o {
    background-color: var(--yq-red-6);
    color: var(--yq-red-6)
}

.ColorTag-module_gridPacket8_6Ilz0 {
    background-color: var(--yq-magenta-6);
    color: var(--yq-magenta-6)
}

.ColorTag-module_gridPacket9_qDTiy {
    background-color: var(--yq-purple-6);
    color: var(--yq-purple-6)
}

.ColorTag-module_gridPacket10_VOBTF {
    background-color: var(--yq-yuque-grey-6);
    color: var(--yq-yuque-grey-6)
}

.ColorTag-module_gridPacket1Border_hnJDZ {
    border: 1px solid var(--yq-blue-6)
}

.ColorTag-module_gridPacket2Border_bACf1 {
    border: 1px solid var(--yq-cyan-6)
}

.ColorTag-module_gridPacket3Border_Cw6Xh {
    border: 1px solid var(--yq-yuque-green-6)
}

.ColorTag-module_gridPacket4Border_ozG6U {
    border: 1px solid var(--yq-pea-green-6)
}

.ColorTag-module_gridPacket5Border_SNB5R {
    border: 1px solid var(--yq-yellow-6)
}

.ColorTag-module_gridPacket6Border_35cJt {
    border: 1px solid var(--yq-orange-6)
}

.ColorTag-module_gridPacket7Border_lC\+M7 {
    border: 1px solid var(--yq-red-6)
}

.ColorTag-module_gridPacket8Border_g44Kf {
    border: 1px solid var(--yq-magenta-6)
}

.ColorTag-module_gridPacket9Border_ppYX\+ {
    border: 1px solid var(--yq-purple-6)
}

.ColorTag-module_gridPacket10Border_zR6\+m {
    border: 1px solid var(--yq-yuque-grey-6)
}

.ColorTag-module_container_6iTXE {
    border-radius: 32px;
    padding: 2px 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.ColorTag-module_value_GA0Yo {
    line-height: 18px;
    font-size: 12px
}

.ColorTag-module_onClose_Cc88Z {
    margin-left: 2px;
    transform: translateX(3px);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption)
}

.ColorTag-module_grid1OnClose_qCQrP:hover,.ColorTag-module_grid2OnClose_zAkG-:hover,.ColorTag-module_grid3OnClose_l6EoZ:hover,.ColorTag-module_grid4OnClose_4C56\+:hover,.ColorTag-module_grid5OnClose_GPOGe:hover,.ColorTag-module_grid6OnClose_AZxg0:hover,.ColorTag-module_grid7OnClose_3ECcL:hover,.ColorTag-module_grid8OnClose_RQud9:hover,.ColorTag-module_grid9OnClose_6X724:hover,.ColorTag-module_grid10OnClose_KuJax:hover {
    background-color: var(--yq-yuque-grey-4)!important
}

.ColorTag-module_grid11OnClose_n98Ee:hover,.ColorTag-module_grid12OnClose_-77CI:hover,.ColorTag-module_grid13OnClose_0wWXb:hover,.ColorTag-module_grid14OnClose_PxLHv:hover,.ColorTag-module_grid15OnClose_h6vI0:hover,.ColorTag-module_grid16OnClose_QsvBd:hover,.ColorTag-module_grid17OnClose_U7Fdi:hover,.ColorTag-module_grid18OnClose_UVVRv:hover,.ColorTag-module_grid19OnClose_dYV\+a:hover,.ColorTag-module_grid20OnClose_zvJgf:hover {
    background-color: var(--yq-yuque-grey-5)!important
}

.ColorTag-module_noWrap_hWkEc {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_selected_fpJQX {
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--yq-text-body)
}

.index-module_selectList_EDcJk .ant-select-item-option-active {
    background-color: var(--yq-bg-tertiary)!important
}

.index-module_container_CsuqT {
    flex: 1
}

.index-module_container_CsuqT .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    box-shadow: none!important;
    border-color: var(--yq-border-primary-active)
}

.index-module_container_CsuqT .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: transparent;
    background: none!important
}

.index-module_container_CsuqT .ant-select {
    width: 100%
}

.index-module_container_CsuqT .ant-select:hover .ant-select-selector {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_container_CsuqT .ant-select-arrow {
    opacity: 0
}

.index-module_Null_2Lrls {
    color: var(--yq-yuque-grey-5)
}

.index-module_ellipsis-text_-Uf6\+ {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_sRPfv {
    display: inline
}

.index-module_iconContainer_StiON {
    display: inline-flex;
    transform: translateY(2px)
}

.ColorBlock-module_grid1_c04We {
    background-color: var(--yq-blue-1);
    color: var(--yq-blue-9)
}

.ColorBlock-module_grid2_lCYbg {
    background-color: var(--yq-cyan-1);
    color: var(--yq-cyan-9)
}

.ColorBlock-module_grid3_7X68c {
    background-color: var(--yq-yuque-green-1);
    color: var(--yq-yuque-green-9)
}

.ColorBlock-module_grid4_v6ijm {
    background-color: var(--yq-pea-green-1);
    color: var(--yq-pea-green-9)
}

.ColorBlock-module_grid5_BZgF8 {
    background-color: var(--yq-yellow-1);
    color: var(--yq-yellow-9)
}

.ColorBlock-module_grid6_qi5r5 {
    background-color: var(--yq-brown-1);
    color: var(--yq-brown-9)
}

.ColorBlock-module_grid7_RsmL7 {
    background-color: var(--yq-red-1);
    color: var(--yq-red-9)
}

.ColorBlock-module_grid8_oU7Tt {
    background-color: var(--yq-sea-blue-1);
    color: var(--yq-sea-blue-9)
}

.ColorBlock-module_grid9_HIOo5 {
    background-color: var(--yq-purple-1);
    color: var(--yq-purple-9)
}

.ColorBlock-module_grid10_Kyntf {
    background-color: var(--yq-yuque-grey-200);
    color: var(--yq-yuque-grey-900)
}

.ColorBlock-module_grid11_byVEk {
    background-color: var(--yq-blue-3);
    color: var(--yq-blue-9)
}

.ColorBlock-module_grid12_3H2ur {
    background-color: var(--yq-cyan-3);
    color: var(--yq-cyan-9)
}

.ColorBlock-module_grid13_mb9DE {
    background-color: var(--yq-yuque-green-3);
    color: var(--yq-yuque-green-9)
}

.ColorBlock-module_grid14_YVGsF {
    background-color: var(--yq-pea-green-3);
    color: var(--yq-pea-green-9)
}

.ColorBlock-module_grid15_Nwr\+j {
    background-color: var(--yq-yellow-3);
    color: var(--yq-yellow-9)
}

.ColorBlock-module_grid16_-xN3O {
    background-color: var(--yq-brown-3);
    color: var(--yq-brown-9)
}

.ColorBlock-module_grid17_FsRfw {
    background-color: var(--yq-red-3);
    color: var(--yq-red-9)
}

.ColorBlock-module_grid18_vDvkI {
    background-color: var(--yq-sea-blue-3);
    color: var(--yq-sea-blue-9)
}

.ColorBlock-module_grid19_G9Ym3 {
    background-color: var(--yq-purple-3);
    color: var(--yq-purple-9)
}

.ColorBlock-module_grid20_HaCX- {
    background-color: var(--yq-yuque-grey-600);
    color: var(--yq-yuque-grey-900)
}

.ColorBlock-module_grid1Border_PkbaJ {
    border: 1px solid var(--yq-blue-2)
}

.ColorBlock-module_grid2Border_ZABXq {
    border: 1px solid var(--yq-cyan-2)
}

.ColorBlock-module_grid3Border_TvDfj {
    border: 1px solid var(--yq-yuque-green-2)
}

.ColorBlock-module_grid4Border_Qn9Uq {
    border: 1px solid var(--yq-pea-green-2)
}

.ColorBlock-module_grid5Border_upXUF {
    border: 1px solid var(--yq-yellow-2)
}

.ColorBlock-module_grid6Border_kIuSP {
    border: 1px solid var(--yq-brown-2)
}

.ColorBlock-module_grid7Border_WM4D0 {
    border: 1px solid var(--yq-red-2)
}

.ColorBlock-module_grid8Border_XLXU2 {
    border: 1px solid var(--yq-sea-blue-2)
}

.ColorBlock-module_grid9Border_4V\+TO {
    border: 1px solid var(--yq-purple-2)
}

.ColorBlock-module_grid10Border_XC4Q0 {
    border: 1px solid var(--yq-yuque-grey-100)
}

.ColorBlock-module_grid11Border_u9yu9 {
    border: 1px solid var(--yq-blue-4)
}

.ColorBlock-module_grid12Border_zCkpk {
    border: 1px solid var(--yq-cyan-4)
}

.ColorBlock-module_grid13Border_ulA2Y {
    border: 1px solid var(--yq-yuque-green-4)
}

.ColorBlock-module_grid14Border_yPHg3 {
    border: 1px solid var(--yq-pea-green-4)
}

.ColorBlock-module_grid15Border_8V4Z4 {
    border: 1px solid var(--yq-yellow-4)
}

.ColorBlock-module_grid16Border_Gviff {
    border: 1px solid var(--yq-brown-4)
}

.ColorBlock-module_grid17Border_7o2S6 {
    border: 1px solid var(--yq-red-4)
}

.ColorBlock-module_grid18Border_tzwNG {
    border: 1px solid var(--yq-sea-blue-4)
}

.ColorBlock-module_grid19Border_sbDx0 {
    border: 1px solid var(--yq-purple-4)
}

.ColorBlock-module_grid20Border_MZdAh {
    border: 1px solid var(--yq-yuque-grey-400)
}

.ColorBlock-module_gridPacket1_hHgx0 {
    background-color: var(--yq-blue-6);
    color: var(--yq-blue-6)
}

.ColorBlock-module_gridPacket2_KVnTC {
    background-color: var(--yq-cyan-6);
    color: var(--yq-cyan-6)
}

.ColorBlock-module_gridPacket3_SbICl {
    background-color: var(--yq-yuque-green-6);
    color: var(--yq-yuque-green-6)
}

.ColorBlock-module_gridPacket4_\+5a5K {
    background-color: var(--yq-pea-green-6);
    color: var(--yq-pea-green-6)
}

.ColorBlock-module_gridPacket5_Dl0DQ {
    background-color: var(--yq-yellow-6);
    color: var(--yq-yellow-6)
}

.ColorBlock-module_gridPacket6_jrvcB {
    background-color: var(--yq-orange-6);
    color: var(--yq-orange-6)
}

.ColorBlock-module_gridPacket7_wDUIs {
    background-color: var(--yq-red-6);
    color: var(--yq-red-6)
}

.ColorBlock-module_gridPacket8_hWM\+- {
    background-color: var(--yq-magenta-6);
    color: var(--yq-magenta-6)
}

.ColorBlock-module_gridPacket9_NldIT {
    background-color: var(--yq-purple-6);
    color: var(--yq-purple-6)
}

.ColorBlock-module_gridPacket10_Gfm9I {
    background-color: var(--yq-yuque-grey-6);
    color: var(--yq-yuque-grey-6)
}

.ColorBlock-module_gridPacket1Border_sLWtj {
    border: 1px solid var(--yq-blue-6)
}

.ColorBlock-module_gridPacket2Border_HP9lP {
    border: 1px solid var(--yq-cyan-6)
}

.ColorBlock-module_gridPacket3Border_mMfdu {
    border: 1px solid var(--yq-yuque-green-6)
}

.ColorBlock-module_gridPacket4Border_d85ja {
    border: 1px solid var(--yq-pea-green-6)
}

.ColorBlock-module_gridPacket5Border_P1Ug9 {
    border: 1px solid var(--yq-yellow-6)
}

.ColorBlock-module_gridPacket6Border_6w34v {
    border: 1px solid var(--yq-orange-6)
}

.ColorBlock-module_gridPacket7Border_LZy7w {
    border: 1px solid var(--yq-red-6)
}

.ColorBlock-module_gridPacket8Border_joTF8 {
    border: 1px solid var(--yq-magenta-6)
}

.ColorBlock-module_gridPacket9Border_ZIWEH {
    border: 1px solid var(--yq-purple-6)
}

.ColorBlock-module_gridPacket10Border_j65a1 {
    border: 1px solid var(--yq-yuque-grey-6)
}

.ColorBlock-module_container_ZPNPa {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative
}

.ColorBlock-module_container_ZPNPa:hover:after {
    opacity: 1
}

.ColorBlock-module_container_ZPNPa:after {
    content: "";
    position: absolute;
    left: -3px;
    top: -3px;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    opacity: 0;
    border-radius: 6px;
    border: 1px solid var(--yq-cardborder-hover)
}

.ColorBlock-module_checkedIcon_vZJZh {
    color: var(--yq-white)
}

.index-module_grid1_lea78 {
    background-color: var(--yq-blue-1);
    color: var(--yq-blue-9)
}

.index-module_grid2_rNd7q {
    background-color: var(--yq-cyan-1);
    color: var(--yq-cyan-9)
}

.index-module_grid3_t7mDg {
    background-color: var(--yq-yuque-green-1);
    color: var(--yq-yuque-green-9)
}

.index-module_grid4_2X7Js {
    background-color: var(--yq-pea-green-1);
    color: var(--yq-pea-green-9)
}

.index-module_grid5_hyVKM {
    background-color: var(--yq-yellow-1);
    color: var(--yq-yellow-9)
}

.index-module_grid6_rno1Q {
    background-color: var(--yq-brown-1);
    color: var(--yq-brown-9)
}

.index-module_grid7_4Dw01 {
    background-color: var(--yq-red-1);
    color: var(--yq-red-9)
}

.index-module_grid8_MPqjk {
    background-color: var(--yq-sea-blue-1);
    color: var(--yq-sea-blue-9)
}

.index-module_grid9_TaFBB {
    background-color: var(--yq-purple-1);
    color: var(--yq-purple-9)
}

.index-module_grid10_c64t5 {
    background-color: var(--yq-yuque-grey-200);
    color: var(--yq-yuque-grey-900)
}

.index-module_grid11_FlnOf {
    background-color: var(--yq-blue-3);
    color: var(--yq-blue-9)
}

.index-module_grid12_Uqt19 {
    background-color: var(--yq-cyan-3);
    color: var(--yq-cyan-9)
}

.index-module_grid13_B6t6g {
    background-color: var(--yq-yuque-green-3);
    color: var(--yq-yuque-green-9)
}

.index-module_grid14_lfi\+W {
    background-color: var(--yq-pea-green-3);
    color: var(--yq-pea-green-9)
}

.index-module_grid15_PMtTr {
    background-color: var(--yq-yellow-3);
    color: var(--yq-yellow-9)
}

.index-module_grid16_5hqtD {
    background-color: var(--yq-brown-3);
    color: var(--yq-brown-9)
}

.index-module_grid17_HrxvZ {
    background-color: var(--yq-red-3);
    color: var(--yq-red-9)
}

.index-module_grid18_KeVnp {
    background-color: var(--yq-sea-blue-3);
    color: var(--yq-sea-blue-9)
}

.index-module_grid19_NZkx- {
    background-color: var(--yq-purple-3);
    color: var(--yq-purple-9)
}

.index-module_grid20_bjR3j {
    background-color: var(--yq-yuque-grey-600);
    color: var(--yq-yuque-grey-900)
}

.index-module_grid1Border_K2A2S {
    border: 1px solid var(--yq-blue-2)
}

.index-module_grid2Border_KlO4Z {
    border: 1px solid var(--yq-cyan-2)
}

.index-module_grid3Border_NahOv {
    border: 1px solid var(--yq-yuque-green-2)
}

.index-module_grid4Border_Y3xxs {
    border: 1px solid var(--yq-pea-green-2)
}

.index-module_grid5Border_yxrSC {
    border: 1px solid var(--yq-yellow-2)
}

.index-module_grid6Border_42Fo- {
    border: 1px solid var(--yq-brown-2)
}

.index-module_grid7Border_elQTZ {
    border: 1px solid var(--yq-red-2)
}

.index-module_grid8Border_aDHOR {
    border: 1px solid var(--yq-sea-blue-2)
}

.index-module_grid9Border_4AtT6 {
    border: 1px solid var(--yq-purple-2)
}

.index-module_grid10Border_p0JLO {
    border: 1px solid var(--yq-yuque-grey-100)
}

.index-module_grid11Border_gaZS8 {
    border: 1px solid var(--yq-blue-4)
}

.index-module_grid12Border_lfFdj {
    border: 1px solid var(--yq-cyan-4)
}

.index-module_grid13Border_bfdXS {
    border: 1px solid var(--yq-yuque-green-4)
}

.index-module_grid14Border_AS9Ru {
    border: 1px solid var(--yq-pea-green-4)
}

.index-module_grid15Border_stN7x {
    border: 1px solid var(--yq-yellow-4)
}

.index-module_grid16Border_FNjsd {
    border: 1px solid var(--yq-brown-4)
}

.index-module_grid17Border_VI2UJ {
    border: 1px solid var(--yq-red-4)
}

.index-module_grid18Border_2KAoR {
    border: 1px solid var(--yq-sea-blue-4)
}

.index-module_grid19Border_4SxHk {
    border: 1px solid var(--yq-purple-4)
}

.index-module_grid20Border_QlByP {
    border: 1px solid var(--yq-yuque-grey-400)
}

.index-module_gridPacket1_-ecNn {
    background-color: var(--yq-blue-6);
    color: var(--yq-blue-6)
}

.index-module_gridPacket2_aJFH9 {
    background-color: var(--yq-cyan-6);
    color: var(--yq-cyan-6)
}

.index-module_gridPacket3_fro7o {
    background-color: var(--yq-yuque-green-6);
    color: var(--yq-yuque-green-6)
}

.index-module_gridPacket4_Z49u5 {
    background-color: var(--yq-pea-green-6);
    color: var(--yq-pea-green-6)
}

.index-module_gridPacket5_Oyw94 {
    background-color: var(--yq-yellow-6);
    color: var(--yq-yellow-6)
}

.index-module_gridPacket6_GCnwF {
    background-color: var(--yq-orange-6);
    color: var(--yq-orange-6)
}

.index-module_gridPacket7_tXaFC {
    background-color: var(--yq-red-6);
    color: var(--yq-red-6)
}

.index-module_gridPacket8_I2BUO {
    background-color: var(--yq-magenta-6);
    color: var(--yq-magenta-6)
}

.index-module_gridPacket9_iM673 {
    background-color: var(--yq-purple-6);
    color: var(--yq-purple-6)
}

.index-module_gridPacket10_dn2wO {
    background-color: var(--yq-yuque-grey-6);
    color: var(--yq-yuque-grey-6)
}

.index-module_gridPacket1Border_rIIMd {
    border: 1px solid var(--yq-blue-6)
}

.index-module_gridPacket2Border_XfzI9 {
    border: 1px solid var(--yq-cyan-6)
}

.index-module_gridPacket3Border_JnHeZ {
    border: 1px solid var(--yq-yuque-green-6)
}

.index-module_gridPacket4Border_mRI9U {
    border: 1px solid var(--yq-pea-green-6)
}

.index-module_gridPacket5Border_Zn7i0 {
    border: 1px solid var(--yq-yellow-6)
}

.index-module_gridPacket6Border_FRa6r {
    border: 1px solid var(--yq-orange-6)
}

.index-module_gridPacket7Border_xOnU- {
    border: 1px solid var(--yq-red-6)
}

.index-module_gridPacket8Border_ys7xD {
    border: 1px solid var(--yq-magenta-6)
}

.index-module_gridPacket9Border_stJ6- {
    border: 1px solid var(--yq-purple-6)
}

.index-module_gridPacket10Border_UK8\+2 {
    border: 1px solid var(--yq-yuque-grey-6)
}

.index-module_cell_WrNrn {
    display: flex
}

.index-module_colorGrid_jhO7T {
    display: grid;
    grid-template-columns: repeat(5,1fr);
    grid-gap: 8px;
    gap: 8px;
    grid-auto-rows: minmax(24px,auto);
    margin: 4px 0
}

.index-module_popoverContainer_Bnr1M {
    padding: 4px;
    cursor: pointer;
    background-color: transparent!important;
    display: flex;
    align-items: center;
    border-radius: 4px;
    border: 1px solid transparent
}

.index-module_popoverContainer_Bnr1M:hover {
    border: 1px solid var(--yq-yuque-green-6)!important
}

.index-module_borderContainer_g8Q1R {
    border: 1px solid var(--yq-yuque-grey-5)
}

.index-module_borderContainer_g8Q1R:hover {
    border: 1px solid var(--yq-yuque-green-6)!important
}

.index-module_noBgColor_k\+cG\+ {
    background-color: transparent!important;
    display: inline-flex!important;
    align-items: center;
    justify-content: center
}

.index-module_container_bP9wV {
    height: 100%;
    flex: 1
}

.index-module_userInfo_yk3wr {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    position: relative
}

.index-module_userInfo_yk3wr .ant-input-affix-wrapper-focused {
    border-color: var(--yq-yuque-green-5)!important
}

.index-module_userInfo_yk3wr .larkui-input {
    position: absolute;
    z-index: 2;
    border-color: transparent;
    background: transparent;
    width: 100%
}

.index-module_userInfo_yk3wr .larkui-input:hover {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_userInfo_yk3wr input {
    background: transparent
}

.index-module_userInfoOnlyAvatar_d7S6E {
    width: 24px
}

.index-module_userInfoOnlyAvatar_d7S6E img {
    margin-left: 2px
}

.index-module_userInfoOnlyAvatar_d7S6E .larkui-input {
    padding: 0;
    cursor: pointer
}

.index-module_userInfoOnlyAvatar_d7S6E .ant-input-affix-wrapper .ant-input-prefix,.index-module_userInfoOnlyAvatar_d7S6E .ant-input-affix-wrapper .ant-input-suffix,.index-module_userInfoOnlyAvatar_d7S6E .ant-input-affix-wrapper>input.ant-input {
    cursor: pointer
}

.index-module_userInfoOnlyAvatarExpand_FbWOP {
    width: 95px
}

.index-module_userInfoOnlyAvatarExpand_FbWOP img {
    margin-left: 11px
}

.index-module_userInfoOnlyAvatarExpand_FbWOP .larkui-input {
    padding: 4px 11px
}

.index-module_avatar_y3R5A {
    margin-left: 12px
}

.index-module_loadingPlaceholder_lV33B {
    min-width: 290px;
    min-height: 50px
}

.index-module_value_f2Swu {
    height: 32px
}

.index-module_clearIcon_86YPU {
    color: var(--yq-text-disable)
}

.index-module_empty_An6D- {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding-bottom: 32px;
    padding-top: 32px
}

.index-module_emptyImg_txqYL {
    width: 110px;
    height: 74;
    margin-bottom: 12px
}

.index-module_assigneeIds_ClfM1 {
    width: 289px
}

.index-module_transitionName_A13Iu {
    animation-duration: 0
}

.index-module_hidden_1yhPl {
    display: none
}

.index-module_placeholder_3rydn {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_placeholder_3rydn .index-module_loaded_kAsRx {
    opacity: 1
}

.index-module_placeholder_3rydn img {
    opacity: 0;
    transition: opacity .2s ease-in
}

.index-module_placeholderLoaded_0zPqS {
    background-color: transparent
}

.Users-module_avatar_IsuKU {
    margin-left: 12px
}

.Users-module_userList_Sl8Jc {
    min-height: 50px;
    max-height: 300px;
    overflow: auto
}

.Users-module_loadingPlaceholder_jmT3q {
    min-width: 229px;
    min-height: 50px
}

.Users-module_userInfoItem_RWVcv {
    display: flex;
    align-items: center;
    min-width: 220px;
    cursor: pointer;
    padding: 7px 24px
}

.Users-module_userInfoItem_RWVcv:hover,.Users-module_userInfoItemActive_wjRel {
    background-color: var(--yq-bg-tertiary)
}

.Users-module_userInfoItemDetail_C94oq {
    margin-left: 16px;
    max-width: 200px
}

.Users-module_userInfoItemDetail_C94oq .Users-module_userName_1yUVm {
    color: var(--yq-yuque-grey-8)
}

.Users-module_value_QHf6F {
    height: 32px
}

.Users-module_clearIcon_ioWfG {
    color: var(--yq-text-disable)
}

.Users-module_empty_C0\+YZ {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center
}

.Users-module_userName_1yUVm {
    max-width: 200px
}

.index-module_container_0Y8sq {
    display: flex;
    align-items: center;
    width: 100%
}

.index-module_container_0Y8sq:hover .index-module_editBtn_-yCKZ {
    visibility: visible
}

.index-module_title_wXn-v {
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer
}

.index-module_editBtn_-yCKZ {
    visibility: hidden;
    color: var(--yq-text-body);
    cursor: pointer;
    margin-left: 16px
}

.index-module_editBtn_-yCKZ svg {
    transform: translateY(4px)
}

.index-module_childMenu_T2JYi {
    padding: 23px 29px 0 24px;
    width: 291px;
    border: none!important
}

.index-module_childMenu_T2JYi .index-module_childMenuTitle_TYgrM {
    font-weight: 500;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 24px
}

.index-module_childMenu_T2JYi .index-module_childContent_kvTx8 {
    max-height: 315px;
    overflow-y: auto
}

.index-module_childMenu_T2JYi .index-module_childNameContainer_BUQDv {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    cursor: pointer
}

.index-module_childMenu_T2JYi .index-module_childNameContainer_BUQDv .index-module_name_MP7Hw {
    margin-left: 12px;
    max-width: 210px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-yuque-grey-9)
}

.index-module_childProgressContainer_5uDNJ {
    border-radius: 17px;
    background-color: var(--yq-yuque-grey-1);
    margin-left: 8px;
    padding-right: 6px
}

.index-module_parentContainer_bSdFa {
    display: flex;
    align-items: center
}

.index-module_parentContainer_bSdFa .index-module_parentName_fI28z {
    color: var(--yq-yuque-grey-6);
    max-width: 140px
}

.index-module_issueProgressContainer_AFLmj {
    color: var(--yq-yuque-grey-7);
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-size: 12px
}

.index-module_issueProgressContainer_AFLmj .index-module_issueProgress_QkF\+S {
    margin-left: 4px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-4)
}

.index-module_ellipsis-text_3P0vR {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_searchInput_\+lqfL {
    width: calc(100% - 40px);
    margin: 16px 0 16px 20px
}

.index-module_attrDropDownList_tG\+C4 {
    max-width: 280px;
    max-height: 325px;
    overflow: auto
}

.index-module_attrDropDownListTagContainer_Px\+ZS {
    max-width: 100%;
    min-width: 168px;
    max-height: 400px;
    overflow: auto
}

.index-module_attrDropDownListItem_5QGvY {
    cursor: pointer;
    padding: 8px 20px 8px 20px;
    color: var(--yq-text-body);
    overflow: hidden
}

.index-module_attrDropDownListItem_5QGvY:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_attrDropDownListItem_5QGvY:hover .publication-checkbox {
    border-color: var(--yq-yuque-green-5)
}

.index-module_attrDropDownListItem_5QGvY .publication-checkbox-checked {
    color: var(--yq-yuque-green-5);
    border-color: var(--yq-yuque-green-5);
    background-color: var(--yq-yuque-green-5)
}

.index-module_attrDropDownListItem_5QGvY .publication-checkbox-checked:after {
    border-top: 1px var(--yq-yuque-green-5) solid;
    border-bottom: 1px var(--yq-yuque-green-5) solid;
    border-right: 1px var(--yq-yuque-green-5) solid;
    border-left: 1px var(--yq-yuque-green-5) solid
}

.index-module_attrDropDownListItemPriority_D6RIz {
    padding: 6px 13px;
    min-width: 110px
}

.index-module_attrDropDownListItemPriority_D6RIz:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_attrDropDownListItemPrioritynull_z578l {
    color: var(--yq-yuque-grey-5)
}

.index-module_attrDropDownListItemTag_DFmf2 {
    padding: 7px 20px 7px 20px;
    width: 213px
}

.index-module_selectFilterDropDown_0zW27 .slide-down-leave {
    animation-name: none!important;
    animation-duration: 0;
    height: 0!important
}

.index-module_filterBtnGroup_r5DNd {
    cursor: pointer;
    display: flex;
    align-items: center;
    outline: none;
    border-radius: 4px;
    border: 1px solid var(--yq-border-primary);
    background-color: transparent;
    padding: 0;
    height: 28px
}

.index-module_filterBtnGroup_r5DNd .larkui-icon {
    color: var(--yq-text-caption)
}

.index-module_closeAttr_GMBtq {
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0 4px 4px 0;
    height: 100%
}

.index-module_closeAttr_GMBtq:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_dropdownAttrValue_AYqDH {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    border: 4px 0 0 4px;
    height: 100%;
    padding-left: 6px
}

.index-module_dropdownAttrValueInline_28tqD {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 4px
}

.index-module_dropdownAttrValueInline_28tqD:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_hidden_qwaih {
    display: none
}

.index-module_menu_D-Qdi {
    min-width: 109px
}

.index-module_menu_D-Qdi .ant-dropdown-menu-item {
    display: flex;
    align-items: center
}

.index-module_menu_D-Qdi .dropdown-menu-item:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_dropdownOverlay_N8hIA {
    max-width: 300px
}

.index-module_itemNameAndLogin_yOkbi {
    width: 164px
}

.index-module_itemIcons_SnapZ h6 svg,.index-module_statusIcon_XOGVQ {
    display: block
}

.index-module_itemIconsassigneeIds_Aak9B,.index-module_itemIconscreatorIds_KmegG,.index-module_itemIconsstate_wR69r,.index-module_itemIconstagIds_M334i {
    display: flex
}

.index-module_operator_MwQ1s {
    padding: 0 4px;
    height: 100%;
    display: flex;
    align-items: center
}

.index-module_operator_MwQ1s:hover {
    background: var(--yq-bg-tertiary)
}

.index-module_datePickerContainer_Y4fI\+ {
    padding: 5px 12px
}

.index-module_datePickerContainer_Y4fI\+ .ant-picker,.index-module_datePickerContainer_Y4fI\+ .container {
    border-color: var(--yq-yuque-grey-5)!important
}

.index-module_container_R\+qAi {
    border-color: transparent
}

.index-module_container_R\+qAi .ant-picker-focused {
    border-color: var(--yq-yuque-green-5)!important
}

.index-module_container_R\+qAi .ant-picker {
    background: transparent;
    border-color: transparent;
    width: 100%
}

.index-module_container_R\+qAi .ant-picker:hover {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_container_R\+qAi .ant-picker-suffix {
    display: none
}

.index-module_container_R\+qAi .ant-picker-input input {
    color: var(--yq-text-body)
}

.index-module_extra_DAWaE {
    padding: 0 6px;
    justify-content: space-between;
    width: calc(100% + 16px);
    transform: translateX(-8px)
}

.ant-picker-content {
    margin: 0 auto!important
}

.ant-picker-date-panel {
    width: 100%!important
}

.ant-picker {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    padding: 4px 11px 4px;
    position: relative;
    display: inline-flex;
    align-items: center;
    background: var(--yq-ant-picker-bg);
    border: 1px solid var(--yq-ant-border-color-base);
    border-radius: 6px;
    transition: border .3s,box-shadow .3s
}

.ant-picker-focused,.ant-picker:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-picker-focused {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color)
}

.ant-picker.ant-picker-disabled {
    background: var(--yq-ant-input-disabled-bg);
    border-color: var(--yq-ant-border-color-base);
    cursor: not-allowed
}

.ant-picker.ant-picker-disabled .ant-picker-suffix {
    color: var(--yq-ant-disabled-color)
}

.ant-picker.ant-picker-borderless {
    background-color: transparent!important;
    border-color: transparent!important;
    box-shadow: none!important
}

.ant-picker-input {
    position: relative;
    display: inline-flex;
    align-items: center;
    width: 100%
}

.ant-picker-input>input {
    position: relative;
    display: inline-block;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: var(--yq-ant-input-color);
    font-size: 14px;
    line-height: 1.5715;
    background-color: var(--yq-ant-input-bg);
    background-image: none;
    border: 1px solid var(--yq-ant-input-border-color);
    border-radius: 6px;
    transition: all .3s;
    flex: auto;
    min-width: 1px;
    height: auto;
    padding: 0;
    background: transparent;
    border: 0
}

.ant-picker-input>input::-moz-placeholder {
    opacity: 1
}

.ant-picker-input>input:-ms-input-placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-picker-input>input::placeholder {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-picker-input>input:-moz-placeholder-shown {
    text-overflow: ellipsis
}

.ant-picker-input>input:-ms-input-placeholder {
    text-overflow: ellipsis
}

.ant-picker-input>input:placeholder-shown {
    text-overflow: ellipsis
}

.ant-picker-input>input:hover {
    border-color: var(--yq-ant-input-hover-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input-focused,.ant-picker-input>input:focus {
    border-color: #0d0c0c;
    border-right-width: 1px!important;
    outline: 0;
    box-shadow: 0 0 0 2px var(--yq-ant-outline-color)
}

.ant-picker-input>input-disabled {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-picker-input>input-disabled:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input[disabled] {
    color: var(--yq-ant-input-disabled-color);
    background-color: var(--yq-ant-input-disabled-bg);
    cursor: not-allowed;
    opacity: 1
}

.ant-picker-input>input[disabled]:hover {
    border-color: var(--yq-ant-input-border-color);
    border-right-width: 1px!important
}

.ant-picker-input>input-borderless,.ant-picker-input>input-borderless-disabled,.ant-picker-input>input-borderless-focused,.ant-picker-input>input-borderless:focus,.ant-picker-input>input-borderless:hover,.ant-picker-input>input-borderless[disabled] {
    background-color: transparent;
    border: none;
    box-shadow: none
}

textarea.ant-picker-input>input {
    max-width: 100%;
    height: auto;
    min-height: 32px;
    line-height: 1.5715;
    vertical-align: bottom;
    transition: all .3s,height 0s
}

.ant-picker-input>input-lg {
    padding: 6.5px 11px;
    font-size: 16px
}

.ant-picker-input>input-sm {
    padding: 0 7px
}

.ant-picker-input>input:focus {
    box-shadow: none
}

.ant-picker-input>input[disabled] {
    background: transparent
}

.ant-picker-input:hover .ant-picker-clear {
    opacity: 1
}

.ant-picker-input-placeholder>input {
    color: var(--yq-ant-input-placeholder-color)
}

.ant-picker-large {
    padding: 6.5px 11px 6.5px
}

.ant-picker-large .ant-picker-input>input {
    font-size: 16px
}

.ant-picker-small {
    padding: 0 7px 0
}

.ant-picker-suffix {
    align-self: center;
    margin-left: 4px;
    color: var(--yq-ant-disabled-color);
    line-height: 1;
    pointer-events: none
}

.ant-picker-suffix>* {
    vertical-align: top
}

.ant-picker-clear {
    position: absolute;
    top: 50%;
    right: 0;
    color: var(--yq-ant-disabled-color);
    line-height: 1;
    background: var(--yq-ant-component-background);
    transform: translateY(-50%);
    cursor: pointer;
    opacity: 0;
    transition: opacity .3s,color .3s
}

.ant-picker-clear>* {
    vertical-align: top
}

.ant-picker-clear:hover {
    color: var(--yq-ant-text-color-secondary)
}

.ant-picker-separator {
    position: relative;
    display: inline-block;
    width: 1em;
    height: 16px;
    color: var(--yq-ant-disabled-color);
    font-size: 16px;
    vertical-align: top;
    cursor: default
}

.ant-picker-focused .ant-picker-separator {
    color: var(--yq-ant-text-color-secondary)
}

.ant-picker-disabled .ant-picker-range-separator .ant-picker-separator {
    cursor: not-allowed
}

.ant-picker-range {
    position: relative;
    display: inline-flex
}

.ant-picker-range .ant-picker-clear {
    right: 11px
}

.ant-picker-range:hover .ant-picker-clear {
    opacity: 1
}

.ant-picker-range .ant-picker-active-bar {
    bottom: -1px;
    height: 2px;
    margin-left: 11px;
    background: var(--yq-yuque-green-600);
    opacity: 0;
    transition: all .3s ease-out;
    pointer-events: none
}

.ant-picker-range.ant-picker-focused .ant-picker-active-bar {
    opacity: 1
}

.ant-picker-range-separator {
    align-items: center;
    padding: 0 8px;
    line-height: 1
}

.ant-picker-range.ant-picker-small .ant-picker-clear {
    right: 7px
}

.ant-picker-range.ant-picker-small .ant-picker-active-bar {
    margin-left: 7px
}

.ant-picker-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: absolute;
    z-index: 1050
}

.ant-picker-dropdown-hidden {
    display: none
}

.ant-picker-dropdown-placement-bottomLeft .ant-picker-range-arrow {
    top: 1.66666667px;
    display: block;
    transform: rotate(-45deg)
}

.ant-picker-dropdown-placement-topLeft .ant-picker-range-arrow {
    bottom: 1.66666667px;
    display: block;
    transform: rotate(135deg)
}

.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-topRight,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-topRight {
    animation-name: antSlideDownIn
}

.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-appear.slide-up-appear-active.ant-picker-dropdown-placement-bottomRight,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-enter.slide-up-enter-active.ant-picker-dropdown-placement-bottomRight {
    animation-name: antSlideUpIn
}

.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-topLeft,.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-topRight {
    animation-name: antSlideDownOut
}

.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-bottomLeft,.ant-picker-dropdown.slide-up-leave.slide-up-leave-active.ant-picker-dropdown-placement-bottomRight {
    animation-name: antSlideUpOut
}

.ant-picker-dropdown-range {
    padding: 6.66666667px 0
}

.ant-picker-dropdown-range-hidden {
    display: none
}

.ant-picker-dropdown .ant-picker-panel>.ant-picker-time-panel {
    padding-top: 4px
}

.ant-picker-ranges {
    margin-bottom: 0;
    padding: 4px 12px;
    overflow: hidden;
    line-height: 34px;
    text-align: left;
    list-style: none
}

.ant-picker-ranges>li {
    display: inline-block
}

.ant-picker-ranges .ant-picker-preset>.ant-tag-blue {
    color: var(--yq-yuque-green-600);
    background: var(--yq-yuque-green-100);
    border-color: var(--yq-yuque-green-300);
    cursor: pointer
}

.ant-picker-ranges .ant-picker-ok {
    float: right;
    margin-left: 8px
}

.ant-picker-range-wrapper {
    display: flex
}

.ant-picker-range-arrow {
    position: absolute;
    z-index: 1;
    display: none;
    width: 10px;
    height: 10px;
    margin-left: 16.5px;
    box-shadow: 2px -2px 6px rgba(0,0,0,.06);
    transition: left .3s ease-out
}

.ant-picker-range-arrow:after {
    position: absolute;
    top: 1px;
    right: 1px;
    width: 10px;
    height: 10px;
    border: 5px solid var(--yq-ant-border-color-split);
    border-color: var(--yq-ant-calendar-bg) var(--yq-ant-calendar-bg) transparent transparent;
    content: ""
}

.ant-picker-panel-container {
    overflow: hidden;
    vertical-align: top;
    background: var(--yq-ant-calendar-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base);
    transition: margin .3s
}

.ant-picker-panel-container .ant-picker-panels {
    display: inline-flex;
    flex-wrap: nowrap;
    direction: ltr
}

.ant-picker-panel-container .ant-picker-panel {
    vertical-align: top;
    background: transparent;
    border-width: 0 0 1px 0;
    border-radius: 0
}

.ant-picker-panel-container .ant-picker-panel-focused {
    border-color: var(--yq-ant-border-color-split)
}

.ant-picker-panel {
    display: inline-flex;
    flex-direction: column;
    text-align: center;
    background: var(--yq-ant-calendar-bg);
    border: 1px solid var(--yq-ant-picker-border-color);
    border-radius: 6px;
    outline: none
}

.ant-picker-panel-focused {
    border-color: var(--yq-yuque-green-600)
}

.ant-picker-date-panel,.ant-picker-decade-panel,.ant-picker-month-panel,.ant-picker-quarter-panel,.ant-picker-time-panel,.ant-picker-week-panel,.ant-picker-year-panel {
    display: flex;
    flex-direction: column;
    width: 280px
}

.ant-picker-header {
    display: flex;
    padding: 0 8px;
    color: var(--yq-ant-heading-color);
    border-bottom: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-header>* {
    flex: none
}

.ant-picker-header button {
    padding: 0;
    color: var(--yq-ant-disabled-color);
    line-height: 40px;
    background: transparent;
    border: 0;
    cursor: pointer;
    transition: color .3s
}

.ant-picker-header>button {
    min-width: 1.6em;
    font-size: 14px
}

.ant-picker-header>button:hover {
    color: var(--yq-yuque-grey-900)
}

.ant-picker-header-view {
    flex: auto;
    font-weight: 500;
    line-height: 40px
}

.ant-picker-header-view button {
    color: inherit;
    font-weight: inherit
}

.ant-picker-header-view button:not(:first-child) {
    margin-left: 8px
}

.ant-picker-header-view button:hover {
    color: var(--yq-yuque-green-600)
}

.ant-picker-next-icon,.ant-picker-prev-icon,.ant-picker-super-next-icon,.ant-picker-super-prev-icon {
    position: relative;
    display: inline-block;
    width: 7px;
    height: 7px
}

.ant-picker-next-icon:before,.ant-picker-prev-icon:before,.ant-picker-super-next-icon:before,.ant-picker-super-prev-icon:before {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 7px;
    height: 7px;
    border: 0 solid currentColor;
    border-width: 1.5px 0 0 1.5px;
    content: ""
}

.ant-picker-super-next-icon:after,.ant-picker-super-prev-icon:after {
    position: absolute;
    top: 4px;
    left: 4px;
    display: inline-block;
    width: 7px;
    height: 7px;
    border: 0 solid currentColor;
    border-width: 1.5px 0 0 1.5px;
    content: ""
}

.ant-picker-prev-icon,.ant-picker-super-prev-icon {
    transform: rotate(-45deg)
}

.ant-picker-next-icon,.ant-picker-super-next-icon {
    transform: rotate(135deg)
}

.ant-picker-content {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse
}

.ant-picker-content td,.ant-picker-content th {
    position: relative;
    min-width: 24px;
    font-weight: 400
}

.ant-picker-content th {
    height: 30px;
    color: var(--yq-yuque-grey-900);
    line-height: 30px
}

.ant-picker-cell {
    padding: 3px 0;
    color: var(--yq-ant-disabled-color);
    cursor: pointer
}

.ant-picker-cell-in-view {
    color: var(--yq-yuque-grey-900)
}

.ant-picker-cell-disabled {
    cursor: not-allowed
}

.ant-picker-cell:before {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    z-index: 1;
    height: 24px;
    transform: translateY(-50%);
    content: ""
}

.ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,.ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner:before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    border: 1px solid var(--yq-yuque-green-600);
    border-radius: 6px;
    content: ""
}

.ant-picker-cell-in-view.ant-picker-cell-in-range {
    position: relative
}

.ant-picker-cell-in-view.ant-picker-cell-in-range:before {
    background: var(--yq-yuque-green-100)
}

.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
    color: var(--yq-ant-text-color-inverse);
    background: var(--yq-yuque-green-600)
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):before,.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):before {
    background: var(--yq-yuque-green-100)
}

.ant-picker-cell-in-view.ant-picker-cell-range-start:before {
    left: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:before {
    right: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range):after {
    position: absolute;
    top: 50%;
    z-index: 0;
    height: 24px;
    border-top: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-bottom: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    transform: translateY(-50%);
    content: ""
}

.ant-picker-cell-range-hover-end:after,.ant-picker-cell-range-hover-start:after,.ant-picker-cell-range-hover:after {
    right: 0;
    left: 2px
}

.ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end:before,.ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover:before,.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start:before,.ant-picker-panel>:not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end:before,.ant-picker-panel>:not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start:before {
    background: var(--yq-ant-picker-basic-cell-hover-with-range-color)
}

.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {
    border-radius: 6px 0 0 6px
}

.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {
    border-radius: 0 6px 6px 0
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after,.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: -1;
    background: var(--yq-ant-picker-basic-cell-hover-with-range-color);
    content: ""
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    right: -6px;
    left: 0
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after {
    right: 0;
    left: -6px
}

.ant-picker-cell-range-hover.ant-picker-cell-range-start:after {
    right: 50%
}

.ant-picker-cell-range-hover.ant-picker-cell-range-end:after {
    left: 50%
}

.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:after,.ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child:after {
    left: 6px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range:after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range):after,.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child:after,tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child:after {
    right: 6px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.ant-picker-cell-disabled {
    pointer-events: none
}

.ant-picker-cell-disabled .ant-picker-cell-inner {
    color: var(--yq-ant-disabled-color);
    background: transparent
}

.ant-picker-cell-disabled:before {
    background: var(--yq-ant-picker-basic-cell-disabled-bg)
}

.ant-picker-cell-disabled.ant-picker-cell-today .ant-picker-cell-inner:before {
    border-color: var(--yq-ant-disabled-color)
}

.ant-picker-decade-panel .ant-picker-content,.ant-picker-month-panel .ant-picker-content,.ant-picker-quarter-panel .ant-picker-content,.ant-picker-year-panel .ant-picker-content {
    height: 264px
}

.ant-picker-decade-panel .ant-picker-cell-inner,.ant-picker-month-panel .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-inner {
    padding: 0 8px
}

.ant-picker-decade-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-month-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-disabled .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-disabled .ant-picker-cell-inner {
    background: var(--yq-ant-picker-basic-cell-disabled-bg)
}

.ant-picker-quarter-panel .ant-picker-content {
    height: 56px
}

.ant-picker-footer {
    width: -moz-min-content;
    width: min-content;
    min-width: 100%;
    line-height: 38px;
    text-align: center;
    border-bottom: 1px solid transparent
}

.ant-picker-panel .ant-picker-footer {
    border-top: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-footer-extra {
    padding: 0 12px;
    line-height: 38px;
    text-align: left
}

.ant-picker-footer-extra:not(:last-child) {
    border-bottom: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-now {
    text-align: left
}

.ant-picker-today-btn {
    color: var(--yq-ant-link-color)
}

.ant-picker-today-btn:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-picker-today-btn:active {
    color: var(--yq-ant-link-active-color)
}

.ant-picker-today-btn.ant-picker-today-btn-disabled {
    color: var(--yq-ant-disabled-color);
    cursor: not-allowed
}

.ant-picker-decade-panel .ant-picker-cell-inner {
    padding: 0 4px
}

.ant-picker-decade-panel .ant-picker-cell:before {
    display: none
}

.ant-picker-month-panel .ant-picker-body,.ant-picker-quarter-panel .ant-picker-body,.ant-picker-year-panel .ant-picker-body {
    padding: 0 8px
}

.ant-picker-month-panel .ant-picker-cell-inner,.ant-picker-quarter-panel .ant-picker-cell-inner,.ant-picker-year-panel .ant-picker-cell-inner {
    width: 60px
}

.ant-picker-month-panel .ant-picker-cell-range-hover-start:after,.ant-picker-quarter-panel .ant-picker-cell-range-hover-start:after,.ant-picker-year-panel .ant-picker-cell-range-hover-start:after {
    left: 14px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px 0 0 6px
}

.ant-picker-month-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-start:after,.ant-picker-quarter-panel .ant-picker-cell-range-hover-end:after,.ant-picker-year-panel .ant-picker-cell-range-hover-end:after {
    right: 14px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 0 6px 6px 0
}

.ant-picker-panel-rtl .ant-picker-month-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-quarter-panel .ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl .ant-picker-year-panel .ant-picker-cell-range-hover-end:after {
    left: 14px;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px 0 0 6px
}

.ant-picker-week-panel .ant-picker-body {
    padding: 8px 12px
}

.ant-picker-week-panel .ant-picker-cell-selected .ant-picker-cell-inner,.ant-picker-week-panel .ant-picker-cell .ant-picker-cell-inner,.ant-picker-week-panel .ant-picker-cell:hover .ant-picker-cell-inner {
    background: transparent!important
}

.ant-picker-week-panel-row td {
    transition: background .3s
}

.ant-picker-week-panel-row:hover td {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-week-panel-row-selected:hover td,.ant-picker-week-panel-row-selected td {
    background: var(--yq-yuque-green-600)
}

.ant-picker-week-panel-row-selected:hover td.ant-picker-cell-week,.ant-picker-week-panel-row-selected td.ant-picker-cell-week {
    color: var(--yq-ant-text-color-inverse)
}

.ant-picker-week-panel-row-selected:hover td.ant-picker-cell-today .ant-picker-cell-inner:before,.ant-picker-week-panel-row-selected td.ant-picker-cell-today .ant-picker-cell-inner:before {
    border-color: var(--yq-ant-text-color-inverse)
}

.ant-picker-week-panel-row-selected:hover td .ant-picker-cell-inner,.ant-picker-week-panel-row-selected td .ant-picker-cell-inner {
    color: var(--yq-ant-text-color-inverse)
}

.ant-picker-date-panel .ant-picker-body {
    padding: 8px 12px
}

.ant-picker-date-panel .ant-picker-content {
    width: 252px
}

.ant-picker-date-panel .ant-picker-content th {
    width: 36px
}

.ant-picker-datetime-panel {
    display: flex
}

.ant-picker-datetime-panel .ant-picker-time-panel {
    border-left: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-datetime-panel .ant-picker-date-panel,.ant-picker-datetime-panel .ant-picker-time-panel {
    transition: opacity .3s
}

.ant-picker-datetime-panel-active .ant-picker-date-panel,.ant-picker-datetime-panel-active .ant-picker-time-panel {
    opacity: .3
}

.ant-picker-datetime-panel-active .ant-picker-date-panel-active,.ant-picker-datetime-panel-active .ant-picker-time-panel-active {
    opacity: 1
}

.ant-picker-time-panel {
    width: auto;
    min-width: auto
}

.ant-picker-time-panel .ant-picker-content {
    display: flex;
    flex: auto;
    height: 224px
}

.ant-picker-time-panel-column {
    flex: 1 0 auto;
    width: 56px;
    margin: 0;
    padding: 0;
    overflow-y: hidden;
    text-align: left;
    list-style: none;
    transition: background .3s
}

.ant-picker-time-panel-column:after {
    display: block;
    height: 196px;
    content: ""
}

.ant-picker-datetime-panel .ant-picker-time-panel-column:after {
    height: 198px
}

.ant-picker-time-panel-column:not(:first-child) {
    border-left: 1px solid var(--yq-ant-picker-border-color)
}

.ant-picker-time-panel-column-active {
    background: var(--yq-ant-item-active-bg)
}

.ant-picker-time-panel-column:hover {
    overflow-y: auto
}

.ant-picker-time-panel-column>li {
    margin: 0;
    padding: 0
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
    display: block;
    width: 100%;
    height: 28px;
    margin: 0;
    padding: 0 0 0 14px;
    color: var(--yq-yuque-grey-900);
    line-height: 28px;
    border-radius: 0;
    cursor: pointer;
    transition: background .3s
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner:hover {
    background: var(--yq-ant-item-hover-bg)
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background: var(--yq-ant-item-active-bg)
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell-disabled .ant-picker-time-panel-cell-inner {
    color: var(--yq-ant-disabled-color);
    background: transparent;
    cursor: not-allowed
}

:root .ant-picker-range-wrapper .ant-picker-month-panel .ant-picker-cell,:root .ant-picker-range-wrapper .ant-picker-year-panel .ant-picker-cell,_:-ms-fullscreen .ant-picker-range-wrapper .ant-picker-month-panel .ant-picker-cell,_:-ms-fullscreen .ant-picker-range-wrapper .ant-picker-year-panel .ant-picker-cell {
    padding: 21px 0
}

.ant-picker-rtl {
    direction: rtl
}

.ant-picker-rtl .ant-picker-suffix {
    margin-right: 4px;
    margin-left: 0
}

.ant-picker-rtl .ant-picker-clear {
    right: auto;
    left: 0
}

.ant-picker-rtl .ant-picker-separator {
    transform: rotate(180deg)
}

.ant-picker-panel-rtl .ant-picker-header-view button:not(:first-child) {
    margin-right: 8px;
    margin-left: 0
}

.ant-picker-rtl.ant-picker-range .ant-picker-clear {
    right: auto;
    left: 11px
}

.ant-picker-rtl.ant-picker-range .ant-picker-active-bar {
    margin-right: 11px;
    margin-left: 0
}

.ant-picker-rtl.ant-picker-range.ant-picker-small .ant-picker-active-bar {
    margin-right: 7px
}

.ant-picker-dropdown-rtl .ant-picker-ranges {
    text-align: right
}

.ant-picker-dropdown-rtl .ant-picker-ranges .ant-picker-ok {
    float: left;
    margin-right: 8px;
    margin-left: 0
}

.ant-picker-panel-rtl {
    direction: rtl
}

.ant-picker-panel-rtl .ant-picker-prev-icon,.ant-picker-panel-rtl .ant-picker-super-prev-icon {
    transform: rotate(135deg)
}

.ant-picker-panel-rtl .ant-picker-next-icon,.ant-picker-panel-rtl .ant-picker-super-next-icon {
    transform: rotate(-45deg)
}

.ant-picker-cell .ant-picker-cell-inner {
    position: relative;
    z-index: 2;
    display: inline-block;
    min-width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 6px;
    transition: background .3s,border .3s
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start:before {
    right: 50%;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-end:before {
    right: 0;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-end:before {
    right: 50%;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner:after {
    right: 0;
    left: -6px
}

.ant-picker-panel-rtl .ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner:after {
    right: -6px;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-range-hover.ant-picker-cell-range-start:after {
    right: 0;
    left: 50%
}

.ant-picker-panel-rtl .ant-picker-cell-range-hover.ant-picker-cell-range-end:after {
    right: 50%;
    left: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(.ant-picker-cell-range-end) .ant-picker-cell-inner {
    border-radius: 0 6px 6px 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(.ant-picker-cell-range-start) .ant-picker-cell-inner {
    border-radius: 6px 0 0 6px
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):first-child:after {
    right: 6px;
    left: 0;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-left: none;
    border-top-left-radius: 0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 0
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range:after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-selected):last-child:after {
    right: 0;
    left: 6px;
    border-right: none;
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-top-left-radius: 6px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 6px
}

.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-start.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-end.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover):after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-end:first-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child:after,.ant-picker-panel-rtl tr>.ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-start:last-child:after {
    right: 6px;
    left: 6px;
    border-right: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-left: 1px dashed var(--yq-ant-picker-date-hover-range-border-color);
    border-radius: 6px
}

.ant-picker-dropdown-rtl .ant-picker-footer-extra {
    direction: rtl;
    text-align: right
}

.ant-picker-panel-rtl .ant-picker-time-panel {
    direction: ltr
}

.TimeAttrs-module_container_56WnK {
    display: flex;
    align-items: center;
    outline: none;
    border-color: transparent;
    background-color: var(--yq-bg-tertiary);
    padding: 0 7.5px;
    height: 28px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--yq-text-body);
    white-space: nowrap;
    position: relative
}

.TimeAttrs-module_container_56WnK p {
    color: var(--yq-text-caption)
}

.TimeAttrs-module_container_56WnK:hover p {
    color: var(--yq-text-body)
}

.TimeAttrs-module_container_56WnK .TimeAttrs-module_dateText_oYq0Z {
    color: var(--yq-text-body)!important
}

.TimeAttrs-module_container_56WnK .TimeAttrs-module_datePickerContainer_L40PX {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0
}

.TimeAttrs-module_container_56WnK .TimeAttrs-module_datePickerContainer_L40PX .ant-picker-input input {
    cursor: pointer
}

.Footer-module_container_MtpiX {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.index-module_container_sFSfl {
    border-radius: 8px
}

.index-module_container_sFSfl .ant-modal-body {
    padding: 12.5px 20px 12.5px 20px
}

.index-module_addPacketBtnIcon_QGqet {
    display: inline-flex;
    align-items: center;
    margin-right: 6px;
    margin-top: 2px
}

.index-module_addPacketBtn_DbAAc {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500
}

.index-module_addPacketBtnText_MIobv {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-yuque-grey-7)
}

.index-module_header_fmg-a {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_body_bL7DT {
    width: 100%
}

.index-module_packetNameContainer_Ky5SE {
    margin-top: 22px;
    margin-bottom: 10px;
    display: flex;
    align-items: center
}

.index-module_breadcrumb_PbVL0 {
    display: flex;
    align-items: center
}

.index-module_title_7bsJ7 {
    margin-left: 12px;
    margin-bottom: 0!important;
    width: 100%;
    line-height: 22px!important;
    min-height: 22px;
    padding: 0
}

.index-module_title_7bsJ7::-moz-placeholder {
    color: var(--yq-yuque-grey-7)!important;
    font-size: 16px!important
}

.index-module_title_7bsJ7::placeholder {
    color: var(--yq-yuque-grey-7)!important;
    font-size: 16px!important
}

.index-module_desc_PZg-c {
    margin-top: 8px;
    width: 100%;
    min-height: 24px;
    padding: 0
}

.index-module_desc_PZg-c .ne-ui-toolbar {
    display: none
}

.index-module_desc_PZg-c .ne-doc-micro-editor {
    border-width: 0
}

.index-module_desc_PZg-c .ne-ui-enter-fullscreen {
    display: none
}

.index-module_desc_PZg-c .ne-engine {
    padding: 0!important;
    min-height: 24px!important
}

.index-module_desc_PZg-c .ne-b-filler:before {
    color: var(--yq-text-disable)
}

.index-module_selected_pUsd\+ {
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--yq-text-body)
}

.index-module_selectList_sy\+kQ .ant-select-item-option-active {
    background-color: var(--yq-bg-tertiary)!important;
    border-radius: 4px
}

.index-module_container_x1NSu {
    flex: 1
}

.index-module_container_x1NSu .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    box-shadow: none!important;
    border-color: var(--yq-border-primary-active)
}

.index-module_container_x1NSu .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: transparent;
    background: none!important
}

.index-module_container_x1NSu .ant-select {
    width: 100%
}

.index-module_container_x1NSu .ant-select:hover .ant-select-selector {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_container_x1NSu .ant-select-arrow {
    opacity: 0
}

.index-module_menuContainer_stINy .index-module_addNewPacket_yS--4 {
    border-top: 1px solid var(--yq-yuque-grey-2);
    width: 100%;
    padding: 8px 12px;
    padding-bottom: 4px;
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-7);
    cursor: pointer
}

.index-module_menuContainer_stINy .index-module_addNewPacket_yS--4 .index-module_addBtnIcon_mpjdP {
    display: inline-flex;
    align-items: center;
    margin-right: 6px
}

.index-module_packetName_Kff7S {
    max-width: 85%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin-left: 8px
}

.index-module_content_szRs4 {
    padding-bottom: 4px
}

.index-module_emptyContent_38r0B {
    color: var(--yq-yuque-grey-6);
    text-align: center;
    padding-top: 24px;
    padding-bottom: 24px
}

.index-module_emptyContent_38r0B img {
    width: 112px;
    margin-bottom: 16px
}

.Search-module_container_i5O\+o {
    margin-right: 8px;
    width: 190px
}

.Search-module_clear_2msFz {
    color: var(--yq-text-disable)
}

.PacketIdAttr-module_ellipsis-text_d-ApH {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.PacketIdAttr-module_menuContainer_d33tH {
    width: 246px;
    padding-top: 7px;
    background: var(--yq-white)
}

.PacketIdAttr-module_menuContainer_d33tH h6 {
    max-width: 180px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_menuTitle_GneJQ {
    color: var(--yq-yuque-grey-9);
    font-weight: 500;
    margin-bottom: 17px;
    padding-top: 20px;
    padding-left: 24px
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_search_q6nIz {
    padding-left: 16px;
    padding-right: 16px;
    margin-bottom: 16px
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_search_q6nIz .larkui-input {
    width: 100%
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_content_w6r\+v {
    max-height: 190px;
    padding-bottom: 8px;
    overflow-y: auto
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_content_w6r\+v .PacketIdAttr-module_packetItem_q2xjj {
    padding-left: 16px;
    padding-right: 16px;
    height: 32px;
    line-height: 32px;
    cursor: pointer
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_content_w6r\+v .PacketIdAttr-module_packetItem_q2xjj:hover,.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_content_w6r\+v .PacketIdAttr-module_packetItemChecked_xwYb5 {
    background-color: var(--yq-yuque-grey-2)
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_addNewPacket_qPPHp {
    border-top: 1px solid var(--yq-yuque-grey-2);
    padding-top: 2px
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_emptyContent_\+m2rZ {
    color: var(--yq-yuque-grey-6);
    text-align: center;
    padding-top: 24px;
    padding-bottom: 24px
}

.PacketIdAttr-module_menuContainer_d33tH .PacketIdAttr-module_emptyContent_\+m2rZ img {
    width: 112px;
    margin-bottom: 16px
}

.ColorPicker-module_grid1_IQkvq {
    background-color: var(--yq-blue-1);
    color: var(--yq-blue-9)
}

.ColorPicker-module_grid2_MFqlv {
    background-color: var(--yq-cyan-1);
    color: var(--yq-cyan-9)
}

.ColorPicker-module_grid3_K-oBK {
    background-color: var(--yq-yuque-green-1);
    color: var(--yq-yuque-green-9)
}

.ColorPicker-module_grid4_bkPOC {
    background-color: var(--yq-pea-green-1);
    color: var(--yq-pea-green-9)
}

.ColorPicker-module_grid5_ldr18 {
    background-color: var(--yq-yellow-1);
    color: var(--yq-yellow-9)
}

.ColorPicker-module_grid6_bCQb4 {
    background-color: var(--yq-brown-1);
    color: var(--yq-brown-9)
}

.ColorPicker-module_grid7_jxwlU {
    background-color: var(--yq-red-1);
    color: var(--yq-red-9)
}

.ColorPicker-module_grid8_mtYDT {
    background-color: var(--yq-sea-blue-1);
    color: var(--yq-sea-blue-9)
}

.ColorPicker-module_grid9_-UBnD {
    background-color: var(--yq-purple-1);
    color: var(--yq-purple-9)
}

.ColorPicker-module_grid10_QFLvJ {
    background-color: var(--yq-yuque-grey-200);
    color: var(--yq-yuque-grey-900)
}

.ColorPicker-module_grid11_wEKBO {
    background-color: var(--yq-blue-3);
    color: var(--yq-blue-9)
}

.ColorPicker-module_grid12_37TzY {
    background-color: var(--yq-cyan-3);
    color: var(--yq-cyan-9)
}

.ColorPicker-module_grid13_Rkst0 {
    background-color: var(--yq-yuque-green-3);
    color: var(--yq-yuque-green-9)
}

.ColorPicker-module_grid14_GHvHU {
    background-color: var(--yq-pea-green-3);
    color: var(--yq-pea-green-9)
}

.ColorPicker-module_grid15_mPtDT {
    background-color: var(--yq-yellow-3);
    color: var(--yq-yellow-9)
}

.ColorPicker-module_grid16_Tu2bM {
    background-color: var(--yq-brown-3);
    color: var(--yq-brown-9)
}

.ColorPicker-module_grid17_AbgvT {
    background-color: var(--yq-red-3);
    color: var(--yq-red-9)
}

.ColorPicker-module_grid18_5396l {
    background-color: var(--yq-sea-blue-3);
    color: var(--yq-sea-blue-9)
}

.ColorPicker-module_grid19_Zlo0Q {
    background-color: var(--yq-purple-3);
    color: var(--yq-purple-9)
}

.ColorPicker-module_grid20_hBZuZ {
    background-color: var(--yq-yuque-grey-600);
    color: var(--yq-yuque-grey-900)
}

.ColorPicker-module_grid1Border_EjeMJ {
    border: 1px solid var(--yq-blue-2)
}

.ColorPicker-module_grid2Border_iz\+HJ {
    border: 1px solid var(--yq-cyan-2)
}

.ColorPicker-module_grid3Border_eZDMX {
    border: 1px solid var(--yq-yuque-green-2)
}

.ColorPicker-module_grid4Border_zyFbC {
    border: 1px solid var(--yq-pea-green-2)
}

.ColorPicker-module_grid5Border_zlP1x {
    border: 1px solid var(--yq-yellow-2)
}

.ColorPicker-module_grid6Border_5PKoc {
    border: 1px solid var(--yq-brown-2)
}

.ColorPicker-module_grid7Border_esaw0 {
    border: 1px solid var(--yq-red-2)
}

.ColorPicker-module_grid8Border_cDuGx {
    border: 1px solid var(--yq-sea-blue-2)
}

.ColorPicker-module_grid9Border_5gXP0 {
    border: 1px solid var(--yq-purple-2)
}

.ColorPicker-module_grid10Border_VeFQu {
    border: 1px solid var(--yq-yuque-grey-100)
}

.ColorPicker-module_grid11Border_Y\+3Qo {
    border: 1px solid var(--yq-blue-4)
}

.ColorPicker-module_grid12Border_6trMn {
    border: 1px solid var(--yq-cyan-4)
}

.ColorPicker-module_grid13Border_MXLv1 {
    border: 1px solid var(--yq-yuque-green-4)
}

.ColorPicker-module_grid14Border_Gjsc9 {
    border: 1px solid var(--yq-pea-green-4)
}

.ColorPicker-module_grid15Border_nq9HZ {
    border: 1px solid var(--yq-yellow-4)
}

.ColorPicker-module_grid16Border_ZLD5l {
    border: 1px solid var(--yq-brown-4)
}

.ColorPicker-module_grid17Border_HrqVc {
    border: 1px solid var(--yq-red-4)
}

.ColorPicker-module_grid18Border_ks6YW {
    border: 1px solid var(--yq-sea-blue-4)
}

.ColorPicker-module_grid19Border_xT\+7f {
    border: 1px solid var(--yq-purple-4)
}

.ColorPicker-module_grid20Border_yuHfd {
    border: 1px solid var(--yq-yuque-grey-400)
}

.ColorPicker-module_gridPacket1_EMhps {
    background-color: var(--yq-blue-6);
    color: var(--yq-blue-6)
}

.ColorPicker-module_gridPacket2_\+Rvch {
    background-color: var(--yq-cyan-6);
    color: var(--yq-cyan-6)
}

.ColorPicker-module_gridPacket3_57YHY {
    background-color: var(--yq-yuque-green-6);
    color: var(--yq-yuque-green-6)
}

.ColorPicker-module_gridPacket4_uzp06 {
    background-color: var(--yq-pea-green-6);
    color: var(--yq-pea-green-6)
}

.ColorPicker-module_gridPacket5_cwS3v {
    background-color: var(--yq-yellow-6);
    color: var(--yq-yellow-6)
}

.ColorPicker-module_gridPacket6_pwiRt {
    background-color: var(--yq-orange-6);
    color: var(--yq-orange-6)
}

.ColorPicker-module_gridPacket7_FBcb\+ {
    background-color: var(--yq-red-6);
    color: var(--yq-red-6)
}

.ColorPicker-module_gridPacket8_-2r2e {
    background-color: var(--yq-magenta-6);
    color: var(--yq-magenta-6)
}

.ColorPicker-module_gridPacket9_5sQMS {
    background-color: var(--yq-purple-6);
    color: var(--yq-purple-6)
}

.ColorPicker-module_gridPacket10_c7RnD {
    background-color: var(--yq-yuque-grey-6);
    color: var(--yq-yuque-grey-6)
}

.ColorPicker-module_gridPacket1Border_eBE26 {
    border: 1px solid var(--yq-blue-6)
}

.ColorPicker-module_gridPacket2Border_tb3ic {
    border: 1px solid var(--yq-cyan-6)
}

.ColorPicker-module_gridPacket3Border_-gjlQ {
    border: 1px solid var(--yq-yuque-green-6)
}

.ColorPicker-module_gridPacket4Border_KjWLZ {
    border: 1px solid var(--yq-pea-green-6)
}

.ColorPicker-module_gridPacket5Border_AKiR8 {
    border: 1px solid var(--yq-yellow-6)
}

.ColorPicker-module_gridPacket6Border_P3gNP {
    border: 1px solid var(--yq-orange-6)
}

.ColorPicker-module_gridPacket7Border_Ng4iH {
    border: 1px solid var(--yq-red-6)
}

.ColorPicker-module_gridPacket8Border_RWAX7 {
    border: 1px solid var(--yq-magenta-6)
}

.ColorPicker-module_gridPacket9Border_fSOzK {
    border: 1px solid var(--yq-purple-6)
}

.ColorPicker-module_gridPacket10Border_NI3yX {
    border: 1px solid var(--yq-yuque-grey-6)
}

.ColorPicker-module_cell_5PxGr {
    display: flex
}

.ColorPicker-module_row_5R-vP {
    width: 18px;
    height: 18px;
    border-radius: 4px
}

.ColorPicker-module_colorGrid_PWYn1 {
    display: grid;
    grid-template-columns: repeat(5,1fr);
    grid-gap: 8px;
    gap: 8px;
    grid-auto-rows: minmax(24px,auto);
    margin: 4px 0
}

.ColorPicker-module_popoverContainer_UNW22 {
    padding: 0
}

.ColorFormItem-module_container_Ikmxi {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 20px
}

.ColorFormItem-module_container_Ikmxi .ant-input {
    margin-left: 16px;
    margin-right: 8px;
    width: auto;
    flex: 1
}

.ColorFormItem-module_colorGrid_mtdtz {
    display: grid;
    grid-template-columns: repeat(5,1fr);
    grid-gap: 8px;
    gap: 8px;
    grid-auto-rows: minmax(18px,auto)
}

.ColorFormItem-module_move_Ypigq {
    margin-left: 2px;
    cursor: move;
    color: var(--yq-text-caption)
}

.ColorFormItem-module_del_Is0LQ {
    color: var(--yq-text-caption)
}

.ColorFormItem-module_formItem_6mgPt {
    line-height: 1;
    margin-bottom: 0!important;
    height: 32px
}

.ColorFormItem-module_formItem_6mgPt .ant-form-item-explain-error {
    display: none!important;
    height: 0;
    overflow: hidden
}

.ColorFormItem-module_formItem_6mgPt .ant-form-item-explain-error:after,.ColorFormItem-module_formItem_6mgPt .ant-form-item-explain-error:before {
    display: none
}

.ColorModalForm-module_container_gU4G\+ .ant-modal-body {
    padding: 20px 0 0 0
}

.ColorModalForm-module_container_gU4G\+ .ant-modal-footer {
    border-color: transparent;
    padding: 20px
}

.ColorModalForm-module_sortable_jTpzO {
    max-height: 360px;
    overflow: auto
}

.ColorModalForm-module_colorGrid_b0xWH {
    display: grid;
    grid-template-columns: repeat(5,1fr);
    grid-gap: 8px;
    gap: 8px;
    grid-auto-rows: minmax(18px,auto)
}

.ColorModalForm-module_add_seTfv {
    color: var(--yq-text-caption);
    margin-left: 38px;
    margin-top: 4px
}

.NotFound-module_container_LzUtS {
    color: var(--yq-text-body);
    padding: 8px 16px;
    box-sizing: border-box;
    background-color: var(--yq-bg-tertiary);
    cursor: pointer
}

.NotFound-module_addBtnCtx_UtQ8R {
    display: flex;
    align-items: center
}

.NotFound-module_title_xR84e {
    word-break: keep-all
}

.NotFound-module_tag_TIqZh {
    flex: 1
}

.Empty-module_container_DvdHf {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 210px;
    min-width: 198px
}

.Empty-module_container_DvdHf img {
    width: 111px
}

.Option-module_container_KOnGd {
    display: flex;
    cursor: pointer;
    position: relative
}

.Option-module_container_KOnGd:hover {
    background-color: var(--yq-bg-tertiary)
}

.Option-module_container_KOnGd:hover .Option-module_tagEditBtnVisible_AFeUA {
    display: block
}

.Option-module_form_i2QNF {
    display: flex;
    width: 100%;
    align-items: center
}

.Option-module_checkboxPlaceHolder_hXz1w {
    width: 16px;
    height: 100%;
    display: flex;
    align-items: center
}

.Option-module_checkboxPlaceHolder_hXz1w svg {
    color: var(--yq-text-body)
}

.Option-module_checkboxPlaceHolder_hXz1w .larkui-icon {
    margin-left: 0!important
}

.Option-module_tagContent_i19J4 {
    display: flex;
    flex: 1
}

.Option-module_popoverContainer_nhRGd .ant-popover-content {
    min-width: 102px!important
}

.Option-module_popoverContainer_nhRGd .ant-popover-inner-content {
    padding: 0
}

.Option-module_tagEditBtn_2wnfR {
    position: absolute;
    right: 0;
    top: -1px;
    display: none
}

.Option-module_menuItem_zEOla {
    display: flex;
    align-items: center;
    min-width: 70px
}

.Option-module_menuItem_zEOla svg {
    color: var(--yq-text-primary)
}

.Option-module_formItem_aRg9\+ {
    line-height: 1;
    margin-bottom: 0;
    height: 32px;
    flex: 1;
    margin-right: 7px
}

.Option-module_formItem_aRg9\+ .ant-form-item-explain-error {
    display: none!important;
    height: 0;
    overflow: hidden
}

.Option-module_formItem_aRg9\+ .ant-form-item-explain-error:after,.Option-module_formItem_aRg9\+ .ant-form-item-explain-error:before {
    display: none
}

.Options-module_ellipsis-text_6ShYz {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Options-module_container_kOGJq {
    width: 264px;
    padding: 0;
    padding-top: 7px
}

.Options-module_menuList_T9BvT {
    display: flex;
    flex-direction: column;
    max-height: 225px;
    overflow: auto
}

.Options-module_menuItem_t-JSg {
    padding: 7px 12px 7px 16px
}

.Options-module_menuItem_t-JSg:last-child {
    margin-bottom: 9px
}

.Options-module_menuItem_t-JSg:hover {
    background-color: var(--yq-bg-tertiary)
}

.Options-module_menuItem_t-JSg:hover .publication-checkbox {
    border-color: var(--yq-yuque-green-4)
}

.Options-module_menuItem_t-JSg .publication-checkbox-checked {
    color: var(--yq-yuque-green-5);
    border-color: var(--yq-yuque-green-5);
    background-color: var(--yq-yuque-green-5)
}

.Options-module_menuItem_t-JSg .publication-checkbox-checked:after {
    border-top: 1px var(--yq-yuque-green-5) solid;
    border-bottom: 1px var(--yq-yuque-green-5) solid;
    border-right: 1px var(--yq-yuque-green-5) solid;
    border-left: 1px var(--yq-yuque-green-5) solid
}

.index-module_container_QolP8 {
    position: relative;
    border: 1px solid transparent;
    border-radius: 4px
}

.index-module_container_QolP8:hover {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_container_QolP8 .ant-space-item {
    max-width: 290px
}

.index-module_container_QolP8 .larkui-input {
    border-color: transparent;
    background: transparent;
    box-shadow: none!important;
    padding: 0
}

.index-module_container_QolP8 .larkui-input:focus,.index-module_container_QolP8 .larkui-input:hover,html[data-kumuhana=pouli] .index-module_container_QolP8 .larkui-input:focus,html[data-kumuhana=pouli] .index-module_container_QolP8 .larkui-input:hover {
    border-color: transparent
}

.index-module_inputContainer_K5\+\+V {
    min-width: 160px;
    padding: 2px 9px;
    min-height: 29px
}

.index-module_inputContainer_K5\+\+V .ant-space-horizontal {
    margin-bottom: 0!important
}

.index-module_tagManagerBtn_MJGyb {
    color: var(--yq-blue-7);
    border-top: 1px solid var(--yq-border-primary);
    display: flex;
    align-items: center;
    padding: 8px 0 8px 16px;
    cursor: pointer
}

.index-module_tagManagerBtn_MJGyb:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_tagManagerBtn_MJGyb p {
    color: var(--yq-blue-7)
}

.index-module_overlayClassName_jxK2p {
    max-width: 264px;
    min-width: 160px!important
}

.index-module_input_7EzrO {
    display: none
}

.index-module_inputVisible_UFExs {
    width: auto;
    max-width: 150px!important;
    display: inline
}

.index-module_tagPlaceholder_mOhvM {
    position: absolute;
    left: 12px;
    top: 5px
}

.index-module_focus_x0Lht {
    border-color: var(--yq-yuque-green-5)!important;
    box-shadow: 0 0 0 2px rgba(0,148,86,.2)
}

.index-module_transition_R6vX7 {
    display: none
}

.index-module_inputInDropdown_PlF33 {
    width: calc(100% - 32px);
    margin-left: 16px;
    margin-bottom: 16px
}

.Attrs-module_container_G9vkO {
    display: flex;
    align-items: center;
    outline: none;
    border-color: transparent;
    background-color: var(--yq-bg-tertiary);
    padding: 0 7.5px;
    height: 28px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--yq-text-body);
    white-space: nowrap
}

.Attrs-module_container_G9vkO p {
    color: var(--yq-text-caption)!important
}

.Attrs-module_container_G9vkO:hover p {
    color: var(--yq-text-body)!important
}

.Attrs-module_containerNoTitle_vIBWd {
    width: 24px;
    height: 24px;
    line-height: 20px;
    border-radius: 6px;
    background-color: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    outline: none;
    color: var(--yq-text-body);
    border-color: transparent;
    padding-left: 2px
}

.Attrs-module_containerNoTitle_vIBWd:hover {
    background-color: var(--yq-yuque-grey-4)
}

.Attrs-module_priority_680fV {
    color: var(--yq-text-body)
}

.Attrs-module_packetInfoIcon_\+jt4- {
    margin-top: 6px
}

.Attrs-module_startOrDueTimeItem_2-sz1 {
    display: flex;
    align-items: center
}

.Attrs-module_startOrDueTimeItem_2-sz1 .Attrs-module_dateText_PpVXb {
    margin-left: 4px
}

.Attrs-module_ownerAvatar_PkBau {
    display: flex;
    align-items: center
}

.index-module_ellipsis-text_D4H7i {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_issueContent_5BQIq {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    line-height: 38px;
    border-radius: 4px;
    padding-right: 4px;
    padding-left: 4px;
    cursor: pointer
}

.index-module_issueContent_5BQIq:hover {
    background: var(--yq-yuque-grey-2)
}

.index-module_issueContent_5BQIq:hover .index-module_deleteChildIcon_O3rPq {
    visibility: visible
}

.index-module_issueContent_5BQIq .index-module_issueLeft_VL\+3d,.index-module_issueContent_5BQIq .index-module_issueRight_NwTGA {
    display: flex;
    align-items: center
}

.index-module_issueContent_5BQIq .index-module_issueLeft_VL\+3d {
    width: calc(100% - 135px)
}

.index-module_issueContent_5BQIq .index-module_stateContainer_k9NVD {
    margin-right: 8px
}

.index-module_issueContent_5BQIq .index-module_editableContainer_advaw {
    width: calc(100% - 80px)
}

.index-module_issueContent_5BQIq .index-module_editableContainer_advaw h6 {
    color: var(--yq-yuque-grey-9)
}

.index-module_issueContent_5BQIq .index-module_deleteChildIcon_O3rPq {
    margin-left: 20px;
    padding-top: 2px;
    visibility: hidden;
    color: var(--yq-yuque-grey-6)
}

.index-module_issueContent_5BQIq .index-module_userSearchContainer_gGe7W {
    margin-left: 20px
}

.index-module_addChildrenContainer_LxCCM {
    border-radius: 8px;
    box-shadow: var(--yq-ant-shadow-2);
    padding: 24px!important
}

.index-module_addChildrenContainerHidden_4HoPg {
    display: none
}

.index-module_body_\+R8I\+ {
    width: 100%
}

.index-module_footer_rlAEx {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_title_hM8UL {
    width: 100%;
    font-size: 16px;
    line-height: 22px!important;
    min-height: 22px;
    padding: 0
}

.index-module_title_hM8UL::-moz-placeholder {
    color: var(--yq-yuque-grey-6)!important;
    font-size: 16px!important
}

.index-module_title_hM8UL::placeholder {
    color: var(--yq-yuque-grey-6)!important;
    font-size: 16px!important
}

.index-module_desc_N4dIA {
    margin-top: 8px;
    width: 100%;
    min-height: 24px;
    padding: 0
}

.index-module_desc_N4dIA .ne-ui-toolbar {
    display: none
}

.index-module_desc_N4dIA .ne-doc-micro-editor {
    border-width: 0
}

.index-module_desc_N4dIA .ne-ui-enter-fullscreen {
    display: none
}

.index-module_desc_N4dIA .ne-engine {
    padding: 0!important;
    min-height: 24px!important
}

.index-module_desc_N4dIA .ne-b-filler:before {
    color: var(--yq-text-disable)
}

.Content-module_ellipsis-text_RrnL\+ {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Content-module_container_rE-SA {
    background: transparent;
    padding-right: 12px;
    max-width: 1664px;
    padding-left: 36px;
    padding-top: 16px
}

.Content-module_body_v1971 {
    display: flex;
    padding-right: 24px
}

.Content-module_bodyLeft_clxYv {
    flex: 1;
    padding-right: 24px;
    box-sizing: border-box;
    max-width: calc(100% - 276px)
}

.Content-module_bodyRight_-bqnK {
    width: 276px;
    padding-left: 24px;
    min-height: calc(100vh - 267px);
    border-left: 1px var(--yq-border-light) solid
}

@media only screen and (max-width: 991px) {
    .Content-module_bodyRight_-bqnK {
        display:none
    }

    .Content-module_bodyLeft_clxYv {
        max-width: 100%
    }
}

.index-module_container_swscX {
    display: flex;
    align-items: center;
    max-width: 154px
}

.IssueOptions-module_ellipsis-text_tZ-Nb {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.IssueOptions-module_formItem_xOPHn {
    display: flex;
    align-items: flex-start;
    min-height: 38px;
    margin-bottom: 4px
}

.IssueOptions-module_formItem_xOPHn label {
    width: 80px;
    height: 30px;
    display: flex;
    padding-top: 1px;
    align-items: center
}

.IssueOptions-module_formItem_xOPHn .ant-dropdown-trigger>.ant-space {
    flex-direction: row;
    max-width: 166px;
    margin-bottom: 0!important
}

.IssueOptions-module_formItem_xOPHn .tags-input-container {
    max-width: 166px
}

.IssueOptions-module_formItem_xOPHn .IssueOptions-module_formItemInput_5kx8K {
    flex: 1
}

.IssueOptions-module_creator_E0tGU {
    padding-left: 12px;
    display: flex;
    height: 30px;
    align-items: center;
    transform: translateY(2px)
}

.IssueOptions-module_goToPacketDetail_New8t {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 28px;
    border-radius: 4px;
    color: var(--yq-yuque-grey-8);
    margin-top: 2px;
    visibility: hidden
}

.IssueOptions-module_goToPacketDetail_New8t:hover {
    color: var(--yq-yuque-grey-8);
    background-color: var(--yq-yuque-grey-2)
}

.IssueOptions-module_packetContainer_U7p5g {
    padding-top: 12px;
    border-top: 1px solid var(--yq-yuque-grey-3)
}

.IssueOptions-module_packetContainer_U7p5g:hover .IssueOptions-module_goToPacketDetail_New8t {
    visibility: visible
}

.IssueOptions-module_packetContainer_U7p5g .IssueOptions-module_packetInput_Pcveh {
    width: 145px
}

.index-module_container_TBsSK {
    display: inline;
    align-items: center;
    height: 100%;
    width: 100%
}

.index-module_selected_7U7mX {
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--yq-text-body)
}

.index-module_selectList_4L-tR {
    width: 100%
}

.index-module_selectList_4L-tR .ant-select-item-option-active {
    background-color: var(--yq-bg-tertiary)!important
}

.index-module_container_TBsSK {
    flex: 1
}

.index-module_container_TBsSK .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    box-shadow: none!important;
    border-color: var(--yq-border-primary-active)
}

.index-module_container_TBsSK .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: transparent;
    background: none!important
}

.index-module_container_TBsSK .ant-select {
    width: 100%
}

.index-module_container_TBsSK .ant-select:hover .ant-select-selector {
    border-color: var(--yq-yuque-grey-5)
}

.index-module_container_TBsSK .ant-select-arrow {
    opacity: 0
}

.index-module_ellipsis-text_EX0dB {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_ar6Ho {
    width: 100%
}

.index-module_header_FnJBX {
    display: flex;
    justify-content: space-between;
    align-items: flex-start
}

.index-module_headerTitle_jfz7Z {
    flex: 1;
    display: flex;
    align-items: flex-start;
    width: 100%;
    word-break: break-all
}

.index-module_headerActions_Lu8Yq {
    display: flex;
    justify-content: flex-end
}

.index-module_body_qTqJF {
    padding-bottom: 66px;
    border-bottom: 1px var(--yq-border-light) solid
}

.index-module_footer_RCF86 .ant-avatar-group {
    display: block
}

.index-module_footerHeader_B\+Icb {
    padding: 25px 0 16px 0;
    display: flex;
    justify-content: space-between
}

.index-module_footerHeader_B\+Icb .ant-avatar-string {
    transform: scale(.761905) translate(-50%,-5%)!important
}

.index-module_childrenIssue_l-LGa {
    padding-bottom: 32px;
    border-bottom: 1px solid var(--yq-yuque-grey-2)
}

.index-module_childrenIssueHeader_4i1If {
    padding: 25px 0 16px 0;
    display: flex;
    justify-content: space-between
}

.index-module_childrenIssueHeader_4i1If .index-module_childTitle_GUTLC {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-7);
    font-size: 12px
}

.index-module_childrenIssueHeader_4i1If .index-module_childProgressContainer_1Glu5 {
    margin-left: 8px
}

.index-module_back_TwUpB {
    background: transparent;
    outline: none;
    border-color: transparent;
    display: flex;
    height: 24px;
    align-items: center;
    margin-left: -8px;
    cursor: pointer;
    border-radius: 4px
}

.index-module_back_TwUpB:hover {
    background-color: var(--yq-bg-secondary)
}

.index-module_back_TwUpB svg {
    stroke: var(--yq-yuque-grey-700)
}

.index-module_back_TwUpB svg path {
    fill: var(--yq-yuque-grey-700)!important
}

.index-module_statusIcon_p0xw3 {
    display: inline-flex;
    transform: translateY(-2px)
}

.index-module_bigSizeIconBtn_erm0X {
    font-size: 16px
}

@media only screen and (max-width: 991px) {
    .index-module_back_TwUpB {
        display:none
    }
}

.index-module_addChildBtn_NMBpz {
    margin-right: 4px
}

.Subscribers-module_ellipsis-text_ju7-9 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Subscribers-module_more_J\+uZT {
    margin-left: -6px
}

.Subscribers-module_more_J\+uZT .larkui-icon {
    font-size: 18px!important
}

.Subscribers-module_subscribers_L2RPB {
    display: flex;
    flex-direction: column;
    max-width: 400px;
    overflow: auto
}

.Subscribers-module_subscriber_-1O9N {
    width: 100%;
    display: flex;
    padding: 10px 0 0;
    justify-content: space-between;
    align-items: center
}

.Subscribers-module_hidden_8mzfc {
    display: none
}

.Subscribers-module_userName_\+PSzY {
    max-width: 200px
}

.Subscribers-module_subBtnSmall_mquJ5 {
    box-shadow: none;
    text-shadow: none;
    border-radius: 6px;
    height: 24px;
    padding: 0 7px;
    font-size: 14px;
    border: 1px solid var(--yq-border-primary);
    cursor: pointer
}

.Subscribers-module_subBtnSmall_mquJ5:hover {
    color: var(--yq-yuque-green-600);
    border-color: var(--yq-border-primary-active)
}

.Search-module_ellipsis-text_PmB6U {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Search-module_container_\+wxz0 {
    width: 556px
}

.Search-module_clear_pUv0I {
    color: var(--yq-text-disable)
}

.index-module_ellipsis-text_GMUQW {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_linkChildrenContainer_d8St7 .ant-modal-body {
    padding: 24px 14px 0
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_header_5y7MP {
    font-weight: 500;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 17px;
    padding-left: 10px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_search_a4yJI {
    padding-left: 10px;
    margin-bottom: 17px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContentPadding_EFdUn {
    height: 8px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_title_TCY0j {
    color: var(--yq-yuque-grey-6);
    margin-bottom: 12px;
    padding-left: 10px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_empty_RwVQM {
    padding-bottom: 33px;
    text-align: center;
    color: var(--yq-yuque-grey-7)
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_empty_RwVQM img {
    width: 112px;
    margin-bottom: 16px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_recentlyIssuesContainer_vjl4C {
    max-height: 205px;
    overflow-y: auto
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContent_lmG5Z {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-left: 10px;
    padding-right: 10px;
    height: 30px;
    line-height: 30px;
    border-radius: 6px;
    cursor: pointer
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContent_lmG5Z .index-module_issueLeft_JgPLp {
    display: flex;
    align-items: center
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContent_lmG5Z .index-module_issueLeft_JgPLp .index-module_stateIcon_x6sQ5 {
    padding-top: 3px
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContent_lmG5Z .index-module_issueLeft_JgPLp .index-module_name_pJ4Vi {
    margin-left: 12px;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    max-width: 490px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContentDisabled_5Au0z {
    cursor: not-allowed;
    opacity: .6
}

.index-module_linkChildrenContainer_d8St7 .index-module_body_CvpQr .index-module_resultContent_dp3xw .index-module_issueContentDisabled_5Au0z:hover {
    background: var(--yq-yuque-grey-2)
}

.index-module_btnNewOrg_V6nKz {
    display: flex;
    align-items: flex-start;
    line-height: 1.35;
    padding-left: 43px!important
}

.index-module_btnNewOrg_V6nKz h6 {
    position: relative!important
}

.index-module_btnNewOrg_V6nKz .icon-svg {
    position: absolute;
    left: -28px;
    top: 1px;
    color: var(--yq-yuque-grey-9)
}

.index-module_btnNewOrg_V6nKz .index-module_orgAddText_98X0I {
    margin-left: 11px
}

.index-module_btnNewOrg_V6nKz .index-module_tag_DSBkQ {
    display: inline-flex;
    margin-left: 8px;
    padding: 0 6px;
    color: var(--yq-yuque-green-7);
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    background: var(--yq-yuque-green-1);
    border-radius: 3px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.index-module_ellipsis-text_\+8fXC {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_issueHeaderContainer_Q39IO {
    width: 100%
}

.index-module_issueHeaderContainer_Q39IO .group-avatar {
    display: inline-flex!important
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB {
    padding: 15px 0 15px 36px;
    line-height: 28px;
    border-bottom: 1px solid var(--yq-yuque-grey-2);
    color: var(--yq-yuque-grey-8);
    display: flex;
    align-items: center
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_groupName_THAnD {
    color: var(--yq-yuque-grey-8);
    display: inline-flex;
    align-items: center;
    padding: 3px 6px;
    border-radius: 6px
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_groupName_THAnD .index-module_name_9i0QW {
    margin-left: 8px;
    max-width: 520px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_groupName_THAnD:hover {
    background: var(--yq-yuque-grey-2)
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_division_Hicxr {
    color: var(--yq-yuque-grey-6);
    margin-left: 4px;
    margin-right: 4px
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_backToIssue_nlyEU {
    color: var(--yq-yuque-grey-8);
    cursor: pointer;
    padding: 3px 6px;
    border-radius: 6px
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_backToIssue_nlyEU:hover {
    background: var(--yq-yuque-grey-2)
}

.index-module_issueHeaderContainer_Q39IO .index-module_groupInfo_nL3mB .index-module_headerIssueName_RYzO8 {
    padding-left: 6px;
    color: var(--yq-yuque-grey-9);
    font-weight: 500;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh {
    padding: 16px 0 0 36px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k {
    height: 32px;
    line-height: 32px;
    display: inline-flex;
    align-items: center;
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_parentLink_\+GLdl {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-8);
    max-width: 140px;
    padding-right: 8px;
    padding-left: 8px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_parentLink_\+GLdl .index-module_parentName_fv0Q\+ {
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-yuque-grey-8)
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_parentStateIcon_bfGSv {
    margin-right: 4px;
    padding-bottom: 2px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_vertical_7wBuU {
    height: 17px;
    margin: 0;
    margin-top: 2px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_childIssueLength_Uhp80 {
    padding-left: 8px;
    padding-right: 8px;
    display: flex;
    align-items: center;
    height: 30px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_childIssueLength_Uhp80 .index-module_lengthText_REsiB {
    margin-left: 4px;
    margin-right: 4px
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_childIssueLength_Uhp80 .index-module_arrowUp_k-mHx {
    transform: rotateX(180deg)
}

.index-module_issueHeaderContainer_Q39IO .index-module_parentContainer_L7ihh .index-module_parentContent_SWs2k .index-module_childIssueLength_Uhp80:hover {
    background: var(--yq-yuque-grey-2);
    border-radius: 0 6px 6px 0
}

.index-module_childIssueContainer_4MNgh {
    display: flex!important;
    align-items: center;
    height: 38px;
    min-width: 108px;
    max-width: 360px;
    justify-content: space-between
}

.index-module_childIssueContainer_4MNgh .index-module_childIssueNameWrapper_HcXX- {
    display: flex;
    align-items: center
}

.index-module_childIssueContainer_4MNgh .index-module_childIssueName_rsH5f {
    margin-left: 4px;
    margin-right: 24px;
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_childIssueMenu_-1uop {
    border-radius: 4px!important
}

.index-module_container_v07xy {
    background-color: var(--yq-bg-primary)
}

.index-module_container_v07xy .index-module_searchContainer_hKgbQ {
    display: flex;
    align-items: center
}

.index-module_container_v07xy .index-module_searchContainer_hKgbQ .ant-input-affix-wrapper-focused,.index-module_container_v07xy .index-module_searchContainer_hKgbQ .ant-input-affix-wrapper:focus {
    box-shadow: none!important;
    border-color: var(--yq-border-primary-active)
}

.index-module_toolbar_wBVzb {
    height: 48px;
    padding-left: 36px;
    margin-bottom: -8px
}

.index-module_filterBar_hGPzo,.index-module_toolbar_wBVzb {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 25px
}

.index-module_filterBar_hGPzo {
    padding-top: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--yq-yuque-grey-2)
}

.index-module_filterBar_hGPzo .index-module_issueCount_4Mkxf {
    color: var(--yq-yuque-grey-7)
}

.index-module_content_orwNS {
    align-items: center;
    padding-left: 36px;
    padding-right: 24px;
    display: flex;
    flex-wrap: wrap;
    gap: 3px
}

.index-module_content_orwNS .index-module_clearFilter_jDshI {
    display: flex;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer
}

.index-module_content_orwNS .index-module_clearFilter_jDshI:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_content_orwNS button {
    margin-right: 5px!important
}

.index-module_btnIcon_8rbLf {
    font-size: 15px;
    transform: translateX(-4px)
}

.index-module_clear_psbSr {
    color: var(--yq-text-link)!important;
    margin-left: -2px;
    padding: 5px 8px
}

.index-module_emptyFiltersContent_35mej {
    padding-left: 36px
}

.index-module_midBtn_LTVGL {
    height: 28px;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-left: 6px;
    padding-right: 6px;
    display: flex;
    align-items: center;
    color: var(--yq-text-body);
    border-radius: 4px
}

.index-module_midBtn_LTVGL .index-module_midBtnText_eM4hp {
    margin-left: 5px!important
}

.index-module_midBtn_LTVGL .larkui-icon-filter {
    transform: translateY(1px)
}

.index-module_closeAll_3\+1n7 {
    margin-left: 16px
}

.index-module_addBtn_nDc8o {
    display: flex;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-left: -4px
}

.index-module_addBtn_nDc8o:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_addBtn_nDc8o svg {
    transform: translateX(0)
}

.CreateOpt-module_ellipsis-text_L3PoG {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.CreateOpt-module_container_dJ5hH,.Footer-module_container_m4Htj {
    display: flex;
    align-items: center
}

.Footer-module_container_m4Htj {
    justify-content: flex-end
}

.index-module_container_qSRFg {
    border-radius: 8px
}

.index-module_container_qSRFg .ant-modal-body {
    padding: 12.5px 20px 12.5px 20px
}

.index-module_addIssueBtn_XwJd4 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500
}

.index-module_addIssueBtn_XwJd4 .index-module_addIssueBtnIcon_A5qVE {
    display: inline-flex;
    align-items: center;
    margin-right: 6px
}

.index-module_header_M9DX- {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_body_e-rsy {
    width: 100%
}

.index-module_breadcrumb_HtYZR {
    display: flex;
    align-items: center
}

.index-module_btnIcon_GTVcA {
    color: var(--yq-text-primary);
    font-size: 16px
}

.index-module_title_rhgUm {
    margin-top: 22px;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    min-height: 22px;
    padding: 0
}

.index-module_title_rhgUm::-moz-placeholder {
    font-size: 16px!important;
    color: var(--yq-text-disable)!important
}

.index-module_title_rhgUm::placeholder {
    font-size: 16px!important;
    color: var(--yq-text-disable)!important
}

.index-module_desc_63AP- {
    margin-top: 8px;
    width: 100%;
    min-height: 24px;
    padding: 0
}

.index-module_desc_63AP- .ne-ui-toolbar {
    display: none
}

.index-module_desc_63AP- .ne-doc-micro-editor {
    border-width: 0
}

.index-module_desc_63AP- .ne-ui-enter-fullscreen {
    display: none
}

.index-module_desc_63AP- .ne-engine {
    padding: 0!important;
    min-height: 24px!important
}

.index-module_desc_63AP- .ne-b-filler:before {
    color: var(--yq-text-disable)
}

.Header-module_headerContainer_Xs2fN {
    width: 100%;
    height: 60px;
    padding-left: 36px;
    border-bottom: 1px solid var(--yq-yuque-grey-3);
    display: flex;
    justify-content: space-between;
    align-items: center
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb {
    display: flex;
    align-items: center;
    cursor: pointer
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_packetName_HkUPC {
    display: flex;
    align-items: center;
    height: 32px;
    line-height: 32px;
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 4px
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_packetName_HkUPC:hover {
    background-color: var(--yq-yuque-grey-2)
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_back_3Hj0Z {
    height: 32px;
    width: 32px;
    background: transparent;
    outline: none;
    border-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_back_3Hj0Z:hover {
    background-color: var(--yq-bg-secondary)
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_back_3Hj0Z svg {
    stroke: var(--yq-yuque-grey-800)
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_back_3Hj0Z svg path {
    fill: var(--yq-yuque-grey-800)!important
}

.Header-module_headerContainer_Xs2fN .Header-module_leftContent_3EZqb .Header-module_name_CpwrC {
    margin-left: 8px;
    max-width: 210px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--yq-yuque-grey-9)
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh {
    padding-right: 25px;
    display: flex;
    align-items: center;
    justify-content: center
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_addIssueBtn_C-szl {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_addIssueBtn_C-szl .Header-module_addIssueBtnIcon_C5UgK {
    display: inline-flex;
    align-items: center;
    margin-right: 6px
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_create_uVT2m {
    height: 32px;
    width: 64px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 6px;
    display: flex;
    padding: 0 12px;
    justify-content: space-between;
    line-height: 32px
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_createIcon_nGz\+L {
    width: 16px;
    height: 16px;
    margin-top: 7px
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_overlay_5aJSo .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-secondary)
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_overlay_5aJSo .ant-dropdown-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_overlay_5aJSo .Header-module_icon_3iQ97 {
    margin-right: 8px;
    color: var(--yq-text-caption)
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_divider_uvKUx {
    margin-left: 16px;
    margin-right: 10px;
    width: 1px;
    height: 16px;
    background-color: var(--yq-yuque-grey-4)
}

.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_detailBtn_Vti\+f {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px
}

.Header-module_headerContainer_Xs2fN .Header-module_activeDetail_LQb6E,.Header-module_headerContainer_Xs2fN .Header-module_rightContent_ImNJh .Header-module_detailBtn_Vti\+f:hover {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_ellipsis-text_uE36D {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_Xq-rh,.index-module_listContainer_66Z4j {
    position: relative;
    width: 100%;
    max-width: 1200px;
    min-width: 949px
}

.index-module_avatar_35CdA {
    position: absolute;
    left: 4px;
    top: 20px
}

.index-module_itemContainer_Fe5tc {
    position: relative;
    margin-left: 48px;
    width: 100%
}

.index-module_issueContainer_XAysk {
    align-items: center
}

.index-module_itemHeader_r7wAF {
    display: flex;
    align-items: center;
    gap: 6px
}

.index-module_headerContainer_Ig91a {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_cover_qgv7P {
    max-width: 220px;
    border-radius: 4px;
    border: 1px solid var(--yq-border-primary)
}

.index-module_docContainer_QQazt {
    display: flex;
    flex-direction: column
}

.index-module_docContent_05rd\+ {
    display: flex
}

.index-module_docLeft_-RZ8\+ {
    flex: 1
}

.index-module_docRight_uMfni {
    width: 162px;
    border-radius: 8px;
    height: 106px;
    overflow: hidden
}

.index-module_docCover_GAdUI {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.index-module_docTitle_co0hG {
    display: flex;
    align-items: center;
    flex: 1
}

.index-module_docDesc_d45Ff {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    display: -webkit-box
}

.index-module_docDescText_ehjco {
    flex: 1;
    -webkit-line-clamp: 2;
    white-space: break-spaces;
    -webkit-box-orient: vertical;
    word-break: break-all
}

.index-module_threadRightSmall_jF6Da {
    height: 50px!important;
    transform: translateY(-10px)
}

.index-module_threadRightSmall_jF6Da .index-module_threadCollaborators_t-ghc {
    transform: translateY(-26px)
}

.index-module_threadCollaborators_t-ghc {
    width: 162px;
    height: 106px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.index-module_threadCollaboratorsIcon_DTzOp {
    opacity: .5
}

.index-module_alertEmptyContainer_Sr7\+r {
    cursor: pointer
}

.index-module_alertEmptyContainer_Sr7\+r:hover {
    background-color: var(--yq-bg-secondary)
}

.index-module_alertEmpty_lDspG {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed var(--yq-yuque-grey-5)
}

.index-module_alertContainer_3BelF {
    min-height: 58px;
    border-radius: 8px;
    margin-bottom: 32px;
    padding-top: 0;
    width: 100%;
    position: relative
}

.index-module_alertContainer_3BelF .ne-doc-minor-editor .ne-simple-ui .ne-engine {
    min-height: 58px
}

.index-module_alertContainer_3BelF .ne-simple-ui.ne-editor {
    margin-top: 5px;
    flex-direction: column
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW {
    min-height: 280px
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW .index-module_lakeEditor_5cigT {
    position: relative;
    min-height: 280px;
    margin-bottom: 8px
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW .ne-doc-minor-editor .ne-simple-ui .ne-engine {
    min-height: 280px
}

.index-module_alertContainer_3BelF .index-module_docletHistoryViewerBtn_kqCNK {
    position: absolute;
    bottom: 5px;
    right: 0;
    padding: 0
}

.index-module_alertEditAble_tXlfM:after {
    content: "";
    background-color: var(--yq-bg-secondary);
    width: calc(100% + 16px);
    height: 100%;
    border-radius: 8px;
    left: -8px;
    top: 0;
    visibility: hidden;
    position: absolute
}

.index-module_alertEditAble_tXlfM:hover .index-module_alertActions_-zAYM,.index-module_alertEditAble_tXlfM:hover:after {
    visibility: visible
}

.index-module_lakeEditor_5cigT {
    position: relative;
    min-height: 58px;
    margin-bottom: 8px
}

.index-module_lakeCreateGuide_IMfDM {
    position: absolute;
    top: 50px;
    padding: 0 20px
}

.index-module_showEditorBtn_MJMEj {
    cursor: pointer;
    border-radius: 6px;
    border: 1px dashed var(--yq-yuque-grey-6);
    height: 44px;
    width: 100%;
    font-style: 14px;
    color: var(--yq-yuque-grey-700);
    line-height: 22px;
    padding: 11px;
    text-align: center
}

.index-module_showEditorBtn_MJMEj .larkui-icon-add {
    margin-right: 8px
}

.index-module_lakeViewer_ajTsr {
    position: relative;
    border-radius: 8px
}

.index-module_hidden_7DOP3 {
    display: none
}

.index-module_alertActions_-zAYM {
    visibility: hidden;
    z-index: 2;
    height: 24px;
    position: absolute;
    top: -29px;
    right: -8px;
    border-radius: 12px;
    background-color: var(--yq-white);
    padding: 0 12px;
    display: flex;
    justify-content: space-between;
    border-top: 1px var(--yq-border-light) solid;
    border-bottom: 1px var(--yq-border-light) solid;
    border-right: 1px var(--yq-border-light) solid;
    border-left: 1px var(--yq-border-light) solid
}

.index-module_alertActions_-zAYM svg {
    fill: var(--yq-yuque-grey-700)
}

.index-module_alertActions_-zAYM button {
    line-height: 1;
    color: var(--yq-yuque-grey-700)
}

.index-module_alertPlaceholder_dgtKD {
    height: 58px;
    border-color: transparent
}

.index-module_alertHidden_rUwfY {
    visibility: hidden
}

.index-module_editorHidden_ka-XF {
    position: absolute;
    visibility: hidden
}

.index-module_emptyImg_Vaj3r {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.index-module_emptyImg_Vaj3r.index-module_showMargin_QM4JP {
    margin: 100px auto auto auto
}

.index-module_emptyImg_Vaj3r img {
    width: 130px
}

.index-module_titleSkeleton_H4gMM {
    width: 200px
}

.index-module_boardSkeleton_E5cho {
    height: 58px
}

.index-module_boardSkeleton_E5cho .ant-skeleton-title {
    height: 58px!important;
    width: 100%!important
}

.index-module_issueContent_cG6V0 {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_placeholder_tr85j {
    flex: 1
}

.index-module_issueFields_Bvp63 {
    display: flex;
    gap: 8px
}

.index-module_issueFieldsItem_Wi\+r\+ {
    border: 1px solid var(--yq-yuque-grey-2);
    padding: 1px 6px;
    border-radius: 4px;
    display: flex;
    white-space: pre
}

.index-module_issueNameContent_rg92j {
    flex: 1;
    width: 0;
    max-width: 616px;
    min-width: 0;
    display: flex;
    margin-right: 32px;
    align-items: center;
    overflow: hidden
}

.index-module_issueNameContent_rg92j span {
    flex: 1
}

.index-module_listItem_GBepM {
    padding-top: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--yq-yuque-grey-2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_listItem_GBepM:last-child,.index-module_noBorder_la5s1 {
    border-bottom-width: 0
}

.index-module_count30D_BSOrD {
    position: relative
}

.index-module_count30DList_EcML9 {
    display: flex;
    gap: 16px
}

.index-module_count30DCard_2vBi8 {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-primary);
    position: relative;
    overflow: hidden
}

.index-module_count30DImg_gKp0F {
    position: absolute;
    z-index: 0;
    right: 16px;
    bottom: 16px;
    width: 20%;
    max-width: 104px
}

.index-module_count30DIconContainer_weVXI {
    display: flex;
    align-items: center
}

.index-module_count30DIcon_qvpVH {
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_count30DtitleIcon_o5J4I {
    transform: translateY(2px)
}

.index-module_docsContent_SAECI {
    display: flex;
    justify-content: space-between
}

.index-module_docsTitle_T4R0q {
    display: block;
    width: 400px
}

.index-module_docsDocAction_wrXea {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-7)!important
}

.index-module_titleIcon_ZSDhk {
    transform: translateY(2px)
}

.index-module_popover_cIXI4 {
    max-width: 330px;
    min-width: 280px;
    display: flex
}

.index-module_membersBorder_WAeEb {
    transform: translate(-.5px,13.5px);
    position: absolute;
    bottom: 0;
    width: calc(100% + 1px);
    left: 0
}

.index-module_membersList_PdWUB {
    display: flex;
    width: 100%;
    gap: 32px;
    position: relative;
    z-index: 1
}

.index-module_membersUser_PNWbC,.index-module_membersUserAvatar_Y\+fmq {
    position: relative
}

.index-module_membersImg1_XxUqA,.index-module_membersUserName_bMXEE {
    position: absolute;
    left: 50%;
    transform: translateX(-50%)
}

.index-module_membersImg1_XxUqA {
    bottom: 0;
    height: 100%;
    z-index: 0
}

html[data-kumuhana=pouli] .index-module_membersImg1_XxUqA {
    opacity: .1
}

.index-module_membersImg2_OrF0P {
    width: 62px;
    position: absolute;
    top: 6px;
    right: 8px;
    z-index: 0
}

.index-module_system_40Zuc {
    width: 100%;
    display: flex;
    gap: 17px
}

.index-module_system_40Zuc a:hover {
    color: var(--yq-text-link)
}

.index-module_system_40Zuc .index-module_count30DCard_2vBi8 {
    flex: 1;
    width: 0
}

.index-module_width100_Uc8N0 {
    width: 100%
}

.index-module_divider_enpVz {
    border-color: var(--yq-yuque-grey-2);
    color: var(--yq-yuque-grey-7)!important
}

.index-module_hotIcon_0r\+fa path {
    fill: var(--yq-yuque-grey-7)
}

.index-module_boardIcon_\+v94I,.index-module_sheetIcon_9ij0U,.index-module_tableIcon_8f30d,.index-module_threadIcon_7xy9b {
    transform: translateY(2px)
}

.index-module_gridTitle_TMQ0E {
    font-weight: 600
}

.index-module_filterContainer_VJ\+Ox {
    min-width: 103px
}

.index-module_filterIsActive_5IZFW {
    background-color: var(--yq-yuque-grey-3)
}

.index-module_filterItem_bqxF2 {
    display: flex;
    justify-content: center;
    position: relative
}

.index-module_filterItemIcon_TOBA8 {
    position: absolute;
    top: 7px;
    left: 13px
}

.index-module_descriptionMoreThanTwoLine_EaDyE {
    transform: translateY(-20px)
}

@media only screen and (max-width: 768px) {
    .index-module_container_Xq-rh {
        padding:12px 12px 0 12px;
        max-width: 100vw;
        min-width: 100vw
    }

    .index-module_listContainer_66Z4j {
        max-width: calc(100vw - 24px);
        min-width: calc(100vw - 24px)
    }

    .index-module_issueNameContent_rg92j {
        width: 100%
    }

    .index-module_issueFields_Bvp63 {
        width: 100%;
        overflow: hidden;
        margin-top: 8px
    }

    .index-module_threadCollaborators_t-ghc {
        width: 83px;
        height: 55px
    }

    .index-module_docRight_uMfni {
        width: 83px;
        height: 55px;
        margin-right: 24px
    }

    .index-module_docLeft_-RZ8\+ {
        flex: 1
    }

    .index-module_docDesc_d45Ff {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        width: calc(100% - 24px)
    }

    .index-module_gridTitle_TMQ0E {
        font-size: 14px;
        font-weight: 400;
        color: var(--yq-text-caption)
    }
}

.index-module_backTop_D0Kau {
    position: relative!important;
    right: inherit!important;
    bottom: inherit!important;
    width: inherit!important;
    height: inherit!important
}

@media screen and (max-width: 768px) {
    .index-module_system_40Zuc {
        flex-direction:column;
        width: calc(100vw - 60px)
    }

    .index-module_system_40Zuc .index-module_count30DCard_2vBi8 {
        width: calc(100% - 48px);
        flex: auto
    }

    .index-module_count30DImg_gKp0F {
        display: none
    }

    .index-module_itemContainer_Fe5tc {
        overflow: hidden
    }
}

.index-module_container_IcAF- {
    margin-top: -22px
}

.index-module_container_IcAF- .ant-table-column-sorters {
    height: 32px
}

.index-module_container_IcAF- .ant-table-tbody>tr.ant-table-row-selected>td {
    background-color: var(--yq-white)
}

.index-module_container_IcAF- .ant-table-small .ant-table-selection-column {
    width: 30px;
    min-width: 30px
}

.index-module_container_IcAF- .ant-table-pagination.ant-pagination {
    margin: 16px 16px 16px 0
}

.index-module_container_IcAF- .ant-table-content::-webkit-scrollbar {
    display: block;
    height: 4px
}

.index-module_container_IcAF- .ant-table-content::-webkit-scrollbar-thumb {
    display: block;
    background: transparent;
    border-radius: 2px
}

.index-module_container_IcAF- .ant-table-content:hover::-webkit-scrollbar-thumb {
    background: var(--yq-bg-primary-hover)
}

.index-module_container_IcAF- .ant-affix .issue-filter-content {
    border-top-color: transparent
}

.index-module_container_IcAF- .ant-table-placeholder td:first-child {
    padding-left: 0!important
}

.index-module_container_IcAF- .ant-table-placeholder td {
    border-bottom-width: 0!important
}

.index-module_container_IcAF- .ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-border-light);
    height: 40px
}

.index-module_container_IcAF- .ant-table-tbody>tr>td:first-child {
    padding-left: 26px;
    padding-right: 0
}

.index-module_container_IcAF- .ant-table-thead td,.index-module_container_IcAF- .ant-table-thead th.ant-table-column-has-sorters:hover:first-child {
    background-color: var(--yq-bg-primary)
}

.index-module_container_IcAF- .ant-table-tbody>tr.ant-table-row:hover>td {
    background-color: var(--yq-bg-secondary)
}

.index-module_container_IcAF- .ant-table-thead>tr>th {
    background: var(--yq-bg-primary);
    padding-left: 20px!important;
    font-size: 14px;
    border-top: 0;
    border-bottom: 1px solid var(--yq-border-light)
}

.index-module_container_IcAF- .ant-table-thead>tr>th:first-child {
    padding-left: 36px!important
}

.index-module_container_IcAF- .ant-table-thead>tr>th:nth-child(2) {
    padding-left: 7px!important
}

.index-module_container_IcAF- .ant-table-measure-row {
    padding-top: 0;
    padding-bottom: 0
}

.index-module_packetTable_rYNf- {
    margin-top: 8px
}

.index-module_packetTable_rYNf- .ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-border-light);
    height: 40px
}

.index-module_packetTable_rYNf- .ant-table-tbody>tr>td:first-child {
    padding-left: 36px
}

.index-module_packetTable_rYNf- .ant-table-thead:hover .ant-table-selection-column {
    visibility: hidden!important
}

.index-module_packetTable_rYNf- .ant-table-thead>tr>th {
    background: var(--yq-bg-primary);
    padding-left: 20px!important;
    font-size: 14px;
    border-top: 0;
    border-bottom: 1px solid var(--yq-border-light)
}

.index-module_packetTable_rYNf- .ant-table-thead>tr>th:first-child {
    padding-left: 40px!important
}

.index-module_packetTable_rYNf- .ant-table-thead>tr>th:nth-child(2) {
    padding-left: 20px!important
}

.index-module_userInfo_YRIqJ {
    display: inline-flex;
    align-items: center
}

.index-module_tag_rpmGP {
    border-radius: 32px
}

.index-module_priority_0E6rF {
    display: inline-flex;
    align-items: center
}

.index-module_emptyText_VSGVF {
    margin-bottom: 20px
}

.index-module_addBtn_PKTI6 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-top: 15px
}

.index-module_addBtn_PKTI6 .index-module_addBtnIcon_zk7mJ {
    display: inline-flex;
    align-items: center;
    margin-right: 6px
}

.index-module_batchOperationWrapper_50gLI {
    display: flex;
    align-items: center
}

.index-module_batchOperationBtn_hv6WL {
    height: 26px
}

.index-module_selectTableTitle_sNfK2 {
    height: 38px;
    line-height: 38px;
    padding: 0!important;
    color: var(--yq-yuque-grey-8)!important;
    font-weight: 400!important
}

.index-module_tipModal_yE5Ly .ant-modal-body {
    padding: 32px 20px 15px 20px
}

.index-module_tipModal_yE5Ly .index-module_tipModalTitle_MO03j {
    display: flex;
    align-items: center
}

.index-module_tipModal_yE5Ly .index-module_tipModalTitle_MO03j svg {
    margin-right: 12px
}

.index-module_tipModal_yE5Ly .index-module_tipModalContent_n4Fnt {
    color: var(--yq-yuque-grey-8);
    padding-left: 32px
}

.index-module_iTableContainer_x3Dvw {
    position: relative
}

.index-module_iTableContainer_x3Dvw .index-module_batchOperationContent_vR\+kz {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 67px;
    z-index: 5;
    width: calc(100% - 67px);
    padding-right: 5px;
    background: var(--yq-white);
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-8);
    background-color: var(--yq-yuque-grey-1)
}

.index-module_selectIssueTable_CYDUi .ant-table-thead>tr>th {
    background-color: var(--yq-yuque-grey-1)!important
}

.ant-table.ant-table-middle {
    font-size: 14px
}

.ant-table.ant-table-middle .ant-table-footer,.ant-table.ant-table-middle .ant-table-tbody>tr>td,.ant-table.ant-table-middle .ant-table-thead>tr>th,.ant-table.ant-table-middle .ant-table-title,.ant-table.ant-table-middle tfoot>tr>td,.ant-table.ant-table-middle tfoot>tr>th {
    padding: 12px 8px
}

.ant-table.ant-table-middle .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-middle .ant-table-expanded-row-fixed {
    margin: -12px -8px
}

.ant-table.ant-table-middle .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -12px -8px -12px 25px
}

.ant-table.ant-table-small {
    font-size: 14px
}

.ant-table.ant-table-small .ant-table-footer,.ant-table.ant-table-small .ant-table-tbody>tr>td,.ant-table.ant-table-small .ant-table-thead>tr>th,.ant-table.ant-table-small .ant-table-title,.ant-table.ant-table-small tfoot>tr>td,.ant-table.ant-table-small tfoot>tr>th {
    padding: 8px 8px
}

.ant-table.ant-table-small .ant-table-filter-trigger {
    margin-right: -4px
}

.ant-table.ant-table-small .ant-table-expanded-row-fixed {
    margin: -8px -8px
}

.ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: -8px -8px -8px 25px
}

.ant-table-small .ant-table-thead>tr>th {
    background-color: var(--yq-ant-table-header-bg)
}

.ant-table-small .ant-table-selection-column {
    width: 46px;
    min-width: 46px
}

.ant-table.ant-table-bordered>.ant-table-container,.ant-table.ant-table-bordered>.ant-table-title {
    border: 1px solid var(--yq-ant-border-color-split);
    border-bottom: 0
}

.ant-table.ant-table-bordered>.ant-table-container {
    border-right: 0
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr:not(:last-child)>th,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr:not(:last-child)>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th:before,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th:before {
    background-color: transparent!important
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>.ant-table-cell-fix-right-first:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>.ant-table-cell-fix-right-first:after {
    border-right: 1px solid var(--yq-ant-border-color-split)
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -16px -17px
}

.ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed:after,.ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td>.ant-table-expanded-row-fixed:after {
    position: absolute;
    top: 0;
    right: 1px;
    bottom: 0;
    border-right: 1px solid var(--yq-ant-border-color-split);
    content: ""
}

.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-expanded-row>td,.ant-table.ant-table-bordered.ant-table-scroll-horizontal>.ant-table-container>.ant-table-body>table>tbody>tr.ant-table-placeholder>td {
    border-right: 0
}

.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-middle>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -12px -9px
}

.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-body>table>tbody>tr>td>.ant-table-expanded-row-fixed,.ant-table.ant-table-bordered.ant-table-small>.ant-table-container>.ant-table-content>table>tbody>tr>td>.ant-table-expanded-row-fixed {
    margin: -8px -9px
}

.ant-table.ant-table-bordered>.ant-table-footer {
    border: 1px solid var(--yq-ant-border-color-split);
    border-top: 0
}

.ant-table-cell .ant-table-container:first-child {
    border-top: 0
}

.ant-table-cell-scrollbar {
    box-shadow: 0 1px 0 1px var(--yq-ant-table-header-bg)
}

.ant-table-wrapper {
    clear: both;
    max-width: 100%
}

.ant-table-wrapper:before {
    display: table;
    content: ""
}

.ant-table-wrapper:after {
    display: table;
    clear: both;
    content: ""
}

.ant-table {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    position: relative;
    font-size: 14px;
    background: var(--yq-ant-component-background);
    border-radius: 6px
}

.ant-table table {
    width: 100%;
    text-align: left;
    border-radius: 6px 6px 0 0;
    border-collapse: separate;
    border-spacing: 0
}

.ant-table-tbody>tr>td,.ant-table-thead>tr>th,.ant-table tfoot>tr>td,.ant-table tfoot>tr>th {
    position: relative;
    padding: 16px 16px;
    word-wrap: break-word
}

.ant-table-cell-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first {
    overflow: visible
}

.ant-table-cell-ellipsis.ant-table-cell-fix-left-last .ant-table-cell-content,.ant-table-cell-ellipsis.ant-table-cell-fix-right-first .ant-table-cell-content {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis
}

.ant-table-title {
    padding: 16px 16px
}

.ant-table-footer {
    padding: 16px 16px;
    color: var(--yq-ant-heading-color);
    background: var(--yq-ant-background-color-light)
}

.ant-table-thead>tr>th {
    position: relative;
    color: var(--yq-ant-heading-color);
    font-weight: 500;
    text-align: left;
    background: var(--yq-ant-table-header-bg);
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s ease
}

.ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: var(--yq-ant-table-header-cell-split-color);
    transform: translateY(-50%);
    transition: background-color .3s;
    content: ""
}

.ant-table-thead>tr:not(:last-child)>th[colspan] {
    border-bottom: 0
}

.ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-ant-border-color-split);
    transition: background .3s
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table {
    margin: -16px -16px -16px 33px
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td {
    border-bottom: 0
}

.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-expanded-row-fixed>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:first-child,.ant-table-tbody>tr>td>.ant-table-wrapper:only-child .ant-table-tbody>tr:last-child>td:last-child {
    border-radius: 0
}

.ant-table-tbody>tr.ant-table-row:hover>td {
    background: var(--yq-ant-table-row-hover-bg)
}

.ant-table-tbody>tr.ant-table-row-selected>td {
    background: var(--yq-yuque-green-100);
    border-color: rgba(0,0,0,.03)
}

.ant-table-tbody>tr.ant-table-row-selected:hover>td {
    background: var(--yq-yuque-grey-300)
}

.ant-table-summary {
    background: var(--yq-ant-component-background)
}

div.ant-table-summary {
    box-shadow: 0 -1px 0 var(--yq-ant-border-color-split)
}

.ant-table-summary>tr>td,.ant-table-summary>tr>th {
    border-bottom: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-pagination.ant-pagination {
    margin: 16px 0
}

.ant-table-pagination {
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px
}

.ant-table-pagination>* {
    flex: none
}

.ant-table-pagination-left {
    justify-content: flex-start
}

.ant-table-pagination-center {
    justify-content: center
}

.ant-table-pagination-right {
    justify-content: flex-end
}

.ant-table-thead th.ant-table-column-has-sorters {
    cursor: pointer;
    transition: all .3s
}

.ant-table-thead th.ant-table-column-has-sorters:hover {
    background: var(--yq-ant-table-header-sort-active-bg)
}

.ant-table-thead th.ant-table-column-has-sorters:hover:before {
    background-color: transparent!important
}

.ant-table-thead th.ant-table-column-sort {
    background: var(--yq-ant-table-header-sort-bg)
}

.ant-table-thead th.ant-table-column-sort:before {
    background-color: transparent!important
}

td.ant-table-column-sort {
    background: var(--yq-ant-table-body-sort-bg)
}

.ant-table-column-sorters {
    display: flex;
    flex: auto;
    align-items: center;
    justify-content: space-between
}

.ant-table-column-sorters:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: ""
}

.ant-table-column-sorter {
    color: #bfbfbf;
    font-size: 0;
    transition: color .3s
}

.ant-table-column-sorter-inner {
    display: inline-flex;
    flex-direction: column;
    align-items: center
}

.ant-table-column-sorter-down,.ant-table-column-sorter-up {
    font-size: 11px
}

.ant-table-column-sorter-down.active,.ant-table-column-sorter-up.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-column-sorter-up+.ant-table-column-sorter-down {
    margin-top: -.3em
}

.ant-table-column-sorters:hover .ant-table-column-sorter {
    color: #a5a5a5
}

.ant-table-filter-column {
    display: flex;
    justify-content: space-between
}

.ant-table-filter-trigger {
    position: relative;
    display: flex;
    align-items: center;
    margin: -4px -8px -4px 4px;
    padding: 0 4px;
    color: #bfbfbf;
    font-size: 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all .3s
}

.ant-table-filter-trigger:hover {
    color: var(--yq-ant-text-color-secondary);
    background: var(--yq-ant-table-header-filter-active-bg)
}

.ant-table-filter-trigger.active {
    color: var(--yq-yuque-green-600)
}

.ant-table-filter-dropdown {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: var(--yq-yuque-grey-900);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum","tnum";
    min-width: 120px;
    background-color: var(--yq-ant-table-filter-dropdown-bg);
    border-radius: 6px;
    box-shadow: var(--yq-ant-box-shadow-base)
}

.ant-table-filter-dropdown .ant-dropdown-menu {
    max-height: 264px;
    overflow-x: hidden;
    border: 0;
    box-shadow: none
}

.ant-table-filter-dropdown-submenu>ul {
    max-height: calc(100vh - 130px);
    overflow-x: hidden;
    overflow-y: auto
}

.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-left: 8px
}

.ant-table-filter-dropdown-btns {
    display: flex;
    justify-content: space-between;
    padding: 7px 8px 7px 3px;
    overflow: hidden;
    background-color: var(--yq-ant-table-filter-btns-bg);
    border-top: 1px solid var(--yq-ant-border-color-split)
}

.ant-table-selection-col {
    width: 32px
}

.ant-table-bordered .ant-table-selection-col {
    width: 50px
}

table tr td.ant-table-selection-column,table tr th.ant-table-selection-column {
    padding-right: 8px;
    padding-left: 8px;
    text-align: center
}

table tr td.ant-table-selection-column .ant-radio-wrapper,table tr th.ant-table-selection-column .ant-radio-wrapper {
    margin-right: 0
}

table tr th.ant-table-selection-column:after {
    background-color: transparent!important
}

.ant-table-selection {
    position: relative;
    display: inline-flex;
    flex-direction: column
}

.ant-table-selection-extra {
    position: absolute;
    top: 0;
    z-index: 1;
    cursor: pointer;
    transition: all .3s;
    margin-left: 100%;
    padding-left: 4px
}

.ant-table-selection-extra .anticon {
    color: #bfbfbf;
    font-size: 10px
}

.ant-table-selection-extra .anticon:hover {
    color: #a5a5a5
}

.ant-table-expand-icon-col {
    width: 48px
}

.ant-table-row-expand-icon-cell {
    text-align: center
}

.ant-table-row-indent {
    float: left;
    height: 1px
}

.ant-table-row-expand-icon {
    color: var(--yq-ant-link-color);
    -webkit-text-decoration: none;
    text-decoration: none;
    cursor: pointer;
    transition: color .3s;
    position: relative;
    display: inline-flex;
    float: left;
    box-sizing: border-box;
    width: 17px;
    height: 17px;
    padding: 0;
    color: inherit;
    line-height: 17px;
    background: var(--yq-ant-table-expand-icon-bg);
    border: 1px solid var(--yq-ant-border-color-split);
    border-radius: 6px;
    outline: none;
    transform: scale(.94117647);
    transition: all .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-table-row-expand-icon:active {
    color: var(--yq-ant-link-active-color)
}

.ant-table-row-expand-icon:active,.ant-table-row-expand-icon:focus,.ant-table-row-expand-icon:hover {
    border-color: currentColor
}

.ant-table-row-expand-icon:after,.ant-table-row-expand-icon:before {
    position: absolute;
    background: currentColor;
    transition: transform .3s ease-out;
    content: ""
}

.ant-table-row-expand-icon:before {
    top: 7px;
    right: 3px;
    left: 3px;
    height: 1px
}

.ant-table-row-expand-icon:after {
    top: 3px;
    bottom: 3px;
    left: 7px;
    width: 1px;
    transform: rotate(90deg)
}

.ant-table-row-expand-icon-collapsed:before {
    transform: rotate(-180deg)
}

.ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

.ant-table-row-expand-icon-spaced {
    background: transparent;
    border: 0;
    visibility: hidden
}

.ant-table-row-expand-icon-spaced:after,.ant-table-row-expand-icon-spaced:before {
    display: none;
    content: none
}

.ant-table-row-indent+.ant-table-row-expand-icon {
    margin-top: 2.5005px;
    margin-right: 8px
}

tr.ant-table-expanded-row:hover>td,tr.ant-table-expanded-row>td {
    background: var(--yq-ant-table-expanded-row-bg)
}

tr.ant-table-expanded-row .ant-descriptions-view {
    display: flex
}

tr.ant-table-expanded-row .ant-descriptions-view table {
    flex: auto;
    width: auto
}

.ant-table .ant-table-expanded-row-fixed {
    position: relative;
    margin: -16px -16px;
    padding: 16px 16px
}

.ant-table-tbody>tr.ant-table-placeholder {
    text-align: center
}

.ant-table-empty .ant-table-tbody>tr.ant-table-placeholder {
    color: var(--yq-ant-disabled-color)
}

.ant-table-tbody>tr.ant-table-placeholder:hover>td {
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left,.ant-table-cell-fix-right {
    position: sticky!important;
    z-index: 2;
    background: var(--yq-ant-component-background)
}

.ant-table-cell-fix-left-first:after,.ant-table-cell-fix-left-last:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: -1px;
    width: 30px;
    transform: translateX(100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table-cell-fix-right-first:after,.ant-table-cell-fix-right-last:after {
    position: absolute;
    top: 0;
    bottom: -1px;
    left: 0;
    width: 30px;
    transform: translateX(-100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:after,.ant-table .ant-table-container:before {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    width: 30px;
    transition: box-shadow .3s;
    content: "";
    pointer-events: none
}

.ant-table .ant-table-container:before {
    left: 0
}

.ant-table .ant-table-container:after {
    right: 0
}

.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container {
    position: relative
}

.ant-table-ping-left .ant-table-cell-fix-left-first:after,.ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-left:not(.ant-table-has-fix-left) .ant-table-container:before {
    box-shadow: inset 10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-ping-left .ant-table-cell-fix-left-last:before {
    background-color: transparent!important
}

.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container {
    position: relative
}

.ant-table-ping-right .ant-table-cell-fix-right-first:after,.ant-table-ping-right .ant-table-cell-fix-right-last:after,.ant-table-ping-right:not(.ant-table-has-fix-right) .ant-table-container:after {
    box-shadow: inset -10px 0 8px -8px var(--yq-ant-shadow-color)
}

.ant-table-sticky-holder,.ant-table-sticky-scroll {
    position: sticky;
    z-index: 3
}

.ant-table-sticky-scroll {
    bottom: 0;
    display: flex;
    align-items: center;
    background: var(--yq-ant-border-color-split);
    border-top: 1px solid var(--yq-ant-border-color-split);
    opacity: .6
}

.ant-table-sticky-scroll:hover {
    transform-origin: center bottom
}

.ant-table-sticky-scroll-bar {
    height: 8px;
    background-color: rgba(0,0,0,.35);
    border-radius: 4px
}

.ant-table-sticky-scroll-bar-active,.ant-table-sticky-scroll-bar:hover {
    background-color: rgba(0,0,0,.8)
}

@media (-ms-high-contrast:none) {
    .ant-table-ping-left .ant-table-cell-fix-left-last:after,.ant-table-ping-right .ant-table-cell-fix-right-first:after {
        box-shadow: none!important
    }
}

.ant-table-title {
    border-radius: 6px 6px 0 0
}

.ant-table-title+.ant-table-container {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.ant-table-title+.ant-table-container table>thead>tr:first-child th:first-child,.ant-table-title+.ant-table-container table>thead>tr:first-child th:last-child {
    border-radius: 0
}

.ant-table-container {
    border-top-right-radius: 6px
}

.ant-table-container,.ant-table-container table>thead>tr:first-child th:first-child {
    border-top-left-radius: 6px
}

.ant-table-container table>thead>tr:first-child th:last-child {
    border-top-right-radius: 6px
}

.ant-table-footer {
    border-radius: 0 0 6px 6px
}

.ant-table-rtl,.ant-table-wrapper-rtl {
    direction: rtl
}

.ant-table-wrapper-rtl .ant-table table {
    text-align: right
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th[colspan]:not([colspan="1"]) {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-thead>tr>th {
    text-align: right
}

.ant-table-tbody>tr .ant-table-wrapper:only-child .ant-table.ant-table-rtl {
    margin: -16px 33px -16px -16px
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-left {
    justify-content: flex-end
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-right {
    justify-content: flex-start
}

.ant-table-wrapper-rtl .ant-table-column-sorter {
    margin-right: 8px;
    margin-left: 0
}

.ant-table-wrapper-rtl .ant-table-filter-column-title {
    padding: 16px 16px 16px 2.3em
}

.ant-table-rtl .ant-table-thead tr th.ant-table-column-has-sorters .ant-table-filter-column-title {
    padding: 0 0 0 2.3em
}

.ant-table-wrapper-rtl .ant-table-filter-trigger-container {
    right: auto;
    left: 0
}

.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-menu-submenu-rtl.ant-table-filter-dropdown .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown-submenu .ant-checkbox-wrapper+span,.ant-dropdown-rtl .ant-table-filter-dropdown .ant-checkbox-wrapper+span {
    padding-right: 8px;
    padding-left: 0
}

.ant-table-wrapper-rtl .ant-table-selection {
    text-align: center
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon,.ant-table-wrapper-rtl .ant-table-row-indent {
    float: right
}

.ant-table-wrapper-rtl .ant-table-row-indent+.ant-table-row-expand-icon {
    margin-right: 0;
    margin-left: 8px
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon:after {
    transform: rotate(-90deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:before {
    transform: rotate(180deg)
}

.ant-table-wrapper-rtl .ant-table-row-expand-icon-collapsed:after {
    transform: rotate(0deg)
}

.PacketOptions-module_ellipsis-text_BPbo- {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.PacketOptions-module_formItem_R1wl8 {
    display: flex;
    align-items: flex-start;
    min-height: 38px;
    margin-bottom: 4px
}

.PacketOptions-module_formItem_R1wl8 label {
    width: 85px;
    height: 30px;
    display: flex;
    padding-top: 1px;
    align-items: center
}

.PacketOptions-module_formItem_R1wl8 .ant-dropdown-trigger>.ant-space {
    flex-direction: row;
    max-width: 166px;
    margin-bottom: 0!important
}

.PacketOptions-module_formItem_R1wl8 .tags-input-container {
    max-width: 166px
}

.PacketOptions-module_formItem_R1wl8 .PacketOptions-module_formItemInput_3xMil {
    flex: 1
}

.PacketOptions-module_creator_oYvLe {
    padding-left: 12px;
    display: flex;
    height: 30px;
    align-items: center;
    transform: translateY(2px)
}

.index-module_packetProgressContainer_YKzMO {
    color: var(--yq-yuque-grey-8);
    white-space: nowrap;
    display: flex;
    align-items: center;
    font-size: 14px
}

.index-module_packetProgressContainer_YKzMO .index-module_packetProgress_fdxDH {
    margin-right: 4px;
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-4)
}

.index-module_pl12_L0zOF {
    padding-left: 12px
}

.index-module_progressText_ijhm7 {
    padding-left: 26px
}

.index-module_progressText_ijhm7 .index-module_text1_HBdQT {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--yq-yuque-grey-9)
}

.index-module_progressText_ijhm7 .index-module_emptyProgressText_bUTx2 {
    font-weight: 500;
    font-size: 16px;
    color: var(--yq-yuque-grey-6);
    margin-bottom: 4px
}

.index-module_progressText_ijhm7 .index-module_text2_uJEtT {
    color: var(--yq-yuque-grey-7)
}

.index-module_emptyProgressText_bUTx2 {
    font-size: 14px;
    color: var(--yq-yuque-grey-6)
}

.Progress-module_ellipsis-text_3RM6c {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Progress-module_progress_bmtb8 {
    border-top: 1px solid var(--yq-yuque-grey-2);
    padding-top: 24px
}

.Progress-module_progress_bmtb8 .Progress-module_title_i4f37 {
    margin-bottom: 22px;
    color: var(--yq-yuque-grey-7)
}

.Progress-module_progress_bmtb8 .Progress-module_content_3Qtd6 {
    display: flex
}

.Progress-module_progress_bmtb8 .Progress-module_content_3Qtd6 .Progress-module_dueTime_doyVa .Progress-module_text1_WRw9e {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--yq-yuque-grey-9)
}

.Progress-module_progress_bmtb8 .Progress-module_content_3Qtd6 .Progress-module_dueTime_doyVa .Progress-module_emptyDueTime_bh7ck {
    color: var(--yq-yuque-grey-6);
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 4px
}

.Progress-module_progress_bmtb8 .Progress-module_content_3Qtd6 .Progress-module_dueTime_doyVa .Progress-module_text2_gqQJ1 {
    color: var(--yq-yuque-grey-7)
}

.Progress-module_progress_bmtb8 .Progress-module_content_3Qtd6 .Progress-module_pl40_026QR {
    padding-left: 40px
}

.index-module_container_0a\+Z- {
    flex: 1
}

.index-module_container_0a\+Z- .ant-input {
    width: 100%;
    border-color: transparent;
    background: none!important;
    text-overflow: ellipsis;
    font-size: 16px;
    font-weight: 500
}

.index-module_container_0a\+Z- .ant-input:hover {
    border-color: var(--yq-yuque-grey-5)!important
}

.Title-module_ellipsis-text_FrYQk {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.Title-module_titleContainer_7NUVh {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 12px
}

.Title-module_titleContainer_7NUVh .Title-module_titleLeft_qyOkI {
    display: flex
}

.Title-module_deletePacketModal_0H5Q1 .ant-modal-body {
    padding: 32px 20px 15px 20px
}

.Title-module_deletePacketModal_0H5Q1 .Title-module_deletePacketTitle_NtIwz {
    display: flex;
    align-items: center
}

.Title-module_deletePacketModal_0H5Q1 .Title-module_deletePacketTitle_NtIwz svg {
    margin-right: 12px
}

.Title-module_deletePacketModal_0H5Q1 .Title-module_deletePacketContent_SKcKX {
    color: var(--yq-yuque-grey-8);
    padding-left: 32px
}

.index-module_ellipsis-text_InTsq {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_V9b2n {
    margin-top: -22px
}

.index-module_container_V9b2n .ant-table-column-sorters {
    height: 32px
}

.index-module_container_V9b2n .ant-table-tbody>tr.ant-table-row-selected>td {
    background-color: var(--yq-white)
}

.index-module_container_V9b2n .ant-table-small .ant-table-selection-column {
    width: 30px;
    min-width: 30px
}

.index-module_container_V9b2n .ant-table-pagination.ant-pagination {
    margin: 16px 16px 16px 0
}

.index-module_container_V9b2n .ant-table-content::-webkit-scrollbar {
    display: block;
    height: 4px
}

.index-module_container_V9b2n .ant-table-content::-webkit-scrollbar-thumb {
    display: block;
    background: transparent;
    border-radius: 2px
}

.index-module_container_V9b2n .ant-table-content:hover::-webkit-scrollbar-thumb {
    background: var(--yq-bg-primary-hover)
}

.index-module_container_V9b2n .ant-affix .issue-filter-content {
    border-top-color: transparent
}

.index-module_container_V9b2n .ant-table-placeholder td:first-child {
    padding-left: 0!important
}

.index-module_container_V9b2n .ant-table-placeholder td {
    border-bottom-width: 0!important
}

.index-module_container_V9b2n .ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-border-light);
    height: 40px
}

.index-module_container_V9b2n .ant-table-tbody>tr>td:first-child {
    padding-left: 26px;
    padding-right: 0
}

.index-module_container_V9b2n .ant-table-thead td,.index-module_container_V9b2n .ant-table-thead th.ant-table-column-has-sorters:hover:first-child {
    background-color: var(--yq-bg-primary)
}

.index-module_container_V9b2n .ant-table-tbody>tr.ant-table-row:hover>td {
    background-color: var(--yq-bg-secondary)
}

.index-module_container_V9b2n .ant-table-thead>tr>th {
    background: var(--yq-bg-primary);
    padding-left: 20px!important;
    font-size: 14px;
    border-top: 0;
    border-bottom: 1px solid var(--yq-border-light)
}

.index-module_container_V9b2n .ant-table-thead>tr>th:first-child {
    padding-left: 36px!important
}

.index-module_container_V9b2n .ant-table-thead>tr>th:nth-child(2) {
    padding-left: 7px!important
}

.index-module_container_V9b2n .ant-table-measure-row {
    padding-top: 0;
    padding-bottom: 0
}

.index-module_packetTable_oEcPl {
    margin-top: 8px
}

.index-module_packetTable_oEcPl .ant-table-tbody>tr>td {
    border-bottom: 1px solid var(--yq-border-light);
    height: 40px
}

.index-module_packetTable_oEcPl .ant-table-tbody>tr>td:first-child {
    padding-left: 36px
}

.index-module_packetTable_oEcPl .ant-table-thead:hover .ant-table-selection-column {
    visibility: hidden!important
}

.index-module_packetTable_oEcPl .ant-table-thead>tr>th {
    background: var(--yq-bg-primary);
    padding-left: 20px!important;
    font-size: 14px;
    border-top: 0;
    border-bottom: 1px solid var(--yq-border-light)
}

.index-module_packetTable_oEcPl .ant-table-thead>tr>th:first-child {
    padding-left: 40px!important
}

.index-module_packetTable_oEcPl .ant-table-thead>tr>th:nth-child(2) {
    padding-left: 20px!important
}

.index-module_userInfo_b3IgY {
    display: inline-flex;
    align-items: center
}

.index-module_tag_qeEIe {
    border-radius: 32px
}

.index-module_priority_kkVaN {
    display: inline-flex;
    align-items: center
}

.index-module_emptyText_OvK\+K {
    margin-bottom: 20px
}

.index-module_addBtn_Z-m7h {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-top: 15px
}

.index-module_addBtn_Z-m7h .index-module_addBtnIcon_mQCR6 {
    display: inline-flex;
    align-items: center;
    margin-right: 6px
}

.index-module_batchOperationWrapper_XiID4 {
    display: flex;
    align-items: center
}

.index-module_batchOperationBtn_QNfLh {
    height: 26px
}

.index-module_selectTableTitle_zUQ1S {
    height: 38px;
    line-height: 38px;
    padding: 0!important;
    color: var(--yq-yuque-grey-8)!important;
    font-weight: 400!important
}

.index-module_tipModal_GwOJF .ant-modal-body {
    padding: 32px 20px 15px 20px
}

.index-module_tipModal_GwOJF .index-module_tipModalTitle_HJYr7 {
    display: flex;
    align-items: center
}

.index-module_tipModal_GwOJF .index-module_tipModalTitle_HJYr7 svg {
    margin-right: 12px
}

.index-module_tipModal_GwOJF .index-module_tipModalContent_2fbkL {
    color: var(--yq-yuque-grey-8);
    padding-left: 32px
}

.index-module_iTableContainer_oJ9AX {
    position: relative
}

.index-module_iTableContainer_oJ9AX .index-module_batchOperationContent_BE-TX {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 67px;
    z-index: 5;
    width: calc(100% - 67px);
    padding-right: 5px;
    background: var(--yq-white);
    height: 48px;
    line-height: 48px;
    color: var(--yq-yuque-grey-8);
    background-color: var(--yq-yuque-grey-1)
}

.index-module_selectIssueTable_9CsY\+ .ant-table-thead>tr>th {
    background-color: var(--yq-yuque-grey-1)!important
}

.index-module_packetDetail_Scnc2 {
    height: 100vh;
    position: relative;
    margin-top: 22px
}

.index-module_packetDetail_Scnc2 .index-module_container_V9b2n {
    max-width: 100%;
    height: 100%;
    overflow: auto
}

.index-module_packetDetailDrawer_HFbIK {
    position: absolute;
    top: 53px;
    height: calc(100% - 53px);
    overflow-y: auto
}

.index-module_packetDetailDrawer_HFbIK .ant-drawer-body {
    padding: 24px!important
}

.index-module_packetDetailDrawer_HFbIK .ne-ui-random-tip {
    display: none
}

.index-module_packetDetailDrawer_HFbIK .index-module_packetOptions_ZbjIP {
    padding-bottom: 10px;
    padding-top: 24px;
    margin-top: 24px;
    border-top: 1px solid var(--yq-yuque-grey-2)
}

.index-module_lakexTextViewer_TJMAm {
    padding: 2px 7px;
    border: 1px solid transparent;
    border-radius: 4px;
    min-height: 32px
}

.index-module_lakexTextViewer_TJMAm:hover {
    border: 1px solid var(--yq-yuque-grey-5)
}

.index-module_contentEditBtn_mMgM7 {
    margin-top: 12px
}

.Status-module_container_2mUiF,.Status-module_content_F9woD {
    display: flex;
    align-items: center;
    height: 100%
}

.Status-module_content_F9woD {
    padding: 5px 12px;
    border-radius: 6px;
    margin-right: 8px;
    cursor: pointer;
    color: var(--yq-yuque-grey-8);
    justify-content: center
}

.Status-module_content_F9woD svg {
    margin-right: 8px
}

.Status-module_content_F9woD:hover,.Status-module_contentActive_6bS3K {
    background: var(--yq-yuque-grey-2);
    font-weight: 500;
    color: var(--yq-yuque-grey-9)
}

.index-module_withIconContainer_3LfVp {
    display: flex;
    align-items: center
}

.index-module_withIconContainer_3LfVp .index-module_iconContainer_9mFOI {
    margin-right: 4px
}

.index-module_container_yXW-g {
    display: flex;
    align-items: center;
    width: 100%
}

.index-module_container_yXW-g:hover .index-module_editBtn_JIQas {
    visibility: visible
}

.index-module_title_zbG3t {
    width: 544px;
    display: flex;
    align-items: center;
    cursor: pointer
}

.index-module_editBtn_JIQas {
    visibility: hidden;
    color: var(--yq-text-body);
    cursor: pointer;
    margin-left: 16px
}

.Footer-module_container_Oa9Cy {
    display: flex;
    align-items: center;
    margin-top: 24px
}

.Footer-module_cancel_pNKys {
    background-color: transparent!important;
    background-image: none!important
}

.index-module_ellipsis-text_8IFLv {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_back_mmooW {
    background: transparent;
    outline: none;
    border-color: transparent;
    display: flex;
    height: 24px;
    align-items: center;
    margin-left: -8px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 24px
}

.index-module_back_mmooW:hover {
    background-color: var(--yq-bg-secondary)
}

.index-module_back_mmooW svg {
    stroke: var(--yq-yuque-grey-700)
}

.index-module_back_mmooW svg path {
    fill: var(--yq-yuque-grey-700)!important
}

.UpgradeToOrg-module_modal_zFmAx .ant-modal-body {
    padding: 0
}

.UpgradeToOrg-module_modal_zFmAx .ant-modal-content {
    overflow: hidden
}

.UpgradeToOrg-module_modal_zFmAx .ant-modal-footer {
    border-top: none;
    padding: 0 32px 24px;
    text-align: left
}

.UpgradeToOrg-module_thumb_M4J1F {
    max-width: 100%
}

.UpgradeToOrg-module_modalContent_6bc4o {
    padding: 24px 32px
}

.UpgradeToOrg-module_modalContent_6bc4o>h3 {
    margin-bottom: 16px;
    font-size: 24px
}

.index-module_container_S7jHS {
    border-bottom: 1px solid var(--yq-border-primary);
    width: 100%;
    padding: 0 36px;
    background-color: var(--yq-white)
}

.index-module_container_S7jHS .group-avatar {
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_content_i6b0G {
    display: flex;
    width: 100%;
    height: 60px;
    align-items: center
}

.index-module_content_i6b0G .ant-menu-overflow {
    flex: 1
}

.index-module_nav_yi9Gm {
    display: flex;
    flex: 1;
    margin-left: 36px;
    align-items: center
}

.index-module_name_etvQ0 {
    display: flex;
    align-items: center
}

.index-module_name_etvQ0,.index-module_name_etvQ0:hover {
    color: var(--yq-yuque-grey-9)
}

.index-module_operate_7y-ta {
    display: flex;
    align-items: center
}

.index-module_groupScope_X-LD6 {
    display: flex!important;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.index-module_sortable_DP-tB {
    display: flex;
    position: relative
}

.index-module_sortable_DP-tB .placeholder {
    width: 1px;
    height: 100%
}

.index-module_dragable_lKrH\+ {
    margin-right: 8px;
    padding: 0 25px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid var(--yq-border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: var(--yq-white)
}

.index-module_dragable_lKrH\+ .index-module_draggableBtn_yV4wO {
    position: absolute;
    left: 1px;
    top: 4px
}

.index-module_dragable_lKrH\+:hover {
    background-color: var(--yq-yuque-grey-4)
}

.index-module_dragable_lKrH\+:hover .index-module_draggableBtn_yV4wO {
    display: block
}

.index-module_dragableNavigation_bcaCU {
    padding: 5px 25px;
    font-size: 14px;
    color: var(--yq-yuque-grey-8)
}

.index-module_navModal_nKB\+h .ant-modal-footer {
    padding-top: 24px;
    border-top-width: 0
}

.index-module_navModal_nKB\+h .index-module_navModalClose_gAHdK {
    position: absolute;
    top: 16px;
    right: 16px
}

.index-module_isDragging_RND0u {
    opacity: .4
}

.index-module_isDraggingPlaceholder_9k55J {
    display: block
}

.index-module_isDraggingOver_IhLAw:hover {
    background-color: var(--yq-white)
}

.index-module_isDraggingOver_IhLAw:hover .index-module_draggableBtn_yV4wO {
    display: none
}

.index-module_placeholder_0WwmW {
    position: absolute;
    top: -2px
}

.index-module_flex_uRjcp {
    flex: 1
}

.index-module_divider_bHfvG {
    margin: 8px 0
}

.index-module_dropdownMenu_jXEnE {
    min-width: 115px
}

.index-module_dropdownMenuItem_dNF0q {
    display: flex;
    align-items: center
}

.index-module_navItem_bW96\+ {
    margin-right: 32px
}

.index-module_badge_WKe0B .ant-badge-dot {
    top: 2px!important;
    background: var(--yq-blue-5)
}

.index-module_nameWrapperPopover_wogR9 {
    display: flex;
    position: relative;
    width: 324px;
    padding: 8px 0
}

.index-module_nameWrapperPopoverContent_dTqmv {
    width: calc(100% - 111px);
    padding-top: 4px
}

.index-module_nameWrapperPopoverMember_dqOTV {
    display: flex;
    align-items: center
}

.index-module_nameWrapperPopover_wogR9 .group-avatar {
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_nameWrapperPopover_wogR9 .index-module_settingCard_Kxczh {
    color: var(--yq-yuque-grey-7);
    position: absolute;
    top: 4px;
    right: 4px
}

.index-module_groupAvatar_vI32V .group-avatar,.index-module_groupAvatar_vI32V .group-avatar:hover {
    color: var(--yq-yuque-grey-9)!important
}

@media only screen and (max-width: 768px) {
    .index-module_content_i6b0G {
        height:100px;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-end;
        padding-bottom: 10px
    }

    .index-module_name_etvQ0 {
        width: 100%
    }

    .index-module_nav_yi9Gm {
        flex: 0;
        margin-left: 0;
        margin-top: 20px;
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: row
    }
}

.index-module_groupName_DppYx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: var(--yq-text-body)
}

.index-module_groupName_DppYx:hover {
    color: var(--yq-text-primary)
}

.index-module_groupName_DppYx>.index-module_groupNameText_XYhrt {
    margin-right: 4px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_groupName_DppYx>.icon-svg {
    color: var(--yq-text-caption)
}

.index-module_groupName_DppYx .index-module_groupNameScope_qP0XF {
    position: relative;
    top: 3px;
    color: var(--yq-text-caption)
}

.index-module_card_MJe8k {
    padding-top: 8px;
    padding-bottom: 8px;
    max-width: 290px;
    min-width: 240px
}

.index-module_cardBody_C-l0H {
    display: flex
}

.index-module_cardAvatar_S1GOD {
    margin-right: 8px
}

.index-module_cardInfo_1BnUz {
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz,.index-module_cardInfo_1BnUz>h6 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_cardInfo_1BnUz>h6 {
    margin-top: 2px;
    font-size: 16px
}

.index-module_cardInfo_1BnUz>h6>a {
    color: var(--yq-text-primary)
}

.index-module_cardInfo_1BnUz>h6>a:hover {
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz>p {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption)
}

.index-module_cardFooter_iT7Xq {
    margin-top: 16px;
    border-top: 1px solid var(--yq-border-primary);
    padding-top: 12px;
    display: flex;
    justify-content: flex-end
}

.index-module_cardFooter_iT7Xq>a {
    color: var(--yq-text-body)
}

.index-module_cardFooter_iT7Xq>a:hover {
    color: var(--yq-text-caption)
}

:root {
    --adm-radius-s: 4px;
    --adm-radius-m: 8px;
    --adm-radius-l: 12px;
    --adm-font-size-1: 9px;
    --adm-font-size-2: 10px;
    --adm-font-size-3: 11px;
    --adm-font-size-4: 12px;
    --adm-font-size-5: 13px;
    --adm-font-size-6: 14px;
    --adm-font-size-7: 15px;
    --adm-font-size-8: 16px;
    --adm-font-size-9: 17px;
    --adm-font-size-10: 18px;
    --adm-color-primary: #1677ff;
    --adm-color-success: #00b578;
    --adm-color-warning: #ff8f1f;
    --adm-color-danger: #ff3141;
    --adm-color-yellow: #ff9f18;
    --adm-color-orange: #ff6430;
    --adm-color-wathet: #e7f1ff;
    --adm-color-text: #333;
    --adm-color-text-secondary: #666;
    --adm-color-weak: #999;
    --adm-color-light: #ccc;
    --adm-color-border: #eee;
    --adm-color-background: #fff;
    --adm-color-highlight: var(--adm-color-danger);
    --adm-color-white: #fff;
    --adm-color-box: #f5f5f5;
    --adm-color-text-light-solid: var(--adm-color-white);
    --adm-color-text-dark-solid: #000;
    --adm-color-fill-content: var(--adm-color-box);
    --adm-font-size-main: var(--adm-font-size-5);
    --adm-font-family: -apple-system,blinkmacsystemfont,"Helvetica Neue",helvetica,segoe ui,arial,roboto,"PingFang SC","miui","Hiragino Sans GB","Microsoft Yahei",sans-serif;
    --adm-border-color: var(--adm-color-border)
}

html[data-prefers-color-scheme=dark] {
    --adm-color-primary: #3086ff;
    --adm-color-success: #34b368;
    --adm-color-warning: #ffa930;
    --adm-color-danger: #ff4a58;
    --adm-color-yellow: #ffa930;
    --adm-color-orange: #e65a2b;
    --adm-color-wathet: #0d2543;
    --adm-color-text: #e6e6e6;
    --adm-color-text-secondary: #b3b3b3;
    --adm-color-weak: grey;
    --adm-color-light: #4d4d4d;
    --adm-color-border: #2b2b2b;
    --adm-color-box: #0a0a0a;
    --adm-color-background: #1a1a1a;
    --adm-color-background-body: var(--adm-color-background);
    --adm-border-color: var(--adm-color-border)
}

:root {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html {
    background-color: var(--adm-color-background-body)
}

body {
    color: #333;
    color: var(--adm-color-text);
    font-size: 13px;
    font-size: var(--adm-font-size-main);
    font-family: -apple-system,blinkmacsystemfont,Helvetica Neue,helvetica,segoe ui,arial,roboto,PingFang SC,miui,Hiragino Sans GB,Microsoft Yahei,sans-serif;
    font-family: var(--adm-font-family)
}

a,button {
    cursor: pointer
}

a {
    color: #1677ff;
    color: var(--adm-color-primary);
    transition: opacity .2s ease-in-out
}

a:active {
    opacity: .8
}

.adm-plain-anchor {
    color: inherit;
    transition: none
}

.adm-plain-anchor:active {
    opacity: 1
}

body.adm-overflow-hidden {
    overflow: hidden!important
}

div.adm-px-tester {
    --size: 1;
    height: 1px;
    height: calc(var(--size)/2*2px);
    width: 0;
    position: fixed;
    left: -100vw;
    top: -100vh;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none
}

.adm-dialog {
    --z-index: var(--adm-dialog-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-dialog .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-dialog-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-dialog-body>* {
    flex: none
}

.adm-dialog-body>.adm-dialog-content {
    flex: auto
}

.adm-dialog-body:not(.adm-dialog-with-image) {
    padding-top: 20px
}

.adm-dialog-image-container {
    margin-bottom: 12px;
    max-height: 40vh
}

.adm-dialog-header,.adm-dialog-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-dialog-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-dialog-content {
    padding: 0 12px 20px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-dialog-content-empty {
    padding: 0;
    height: 12px
}

.adm-dialog-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-dialog-footer .adm-dialog-action-row {
    display: flex;
    align-items: stretch;
    border-top: .5px solid var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>* {
    flex: 1
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button {
    padding: 10px;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    border-radius: 0;
    border-right: solid .5px var(--adm-color-border)
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button-bold {
    font-weight: 700
}

.adm-dialog-footer .adm-dialog-action-row>.adm-dialog-button:last-child {
    border-right: none
}

.adm-dialog-image-container {
    overflow-y: auto
}

.adm-image {
    --width: var(--adm-image-width,auto);
    --height: var(--adm-image-height,auto);
    width: auto;
    width: var(--width);
    height: auto;
    height: var(--height);
    display: block;
    overflow: hidden
}

.adm-image-img {
    width: 100%;
    height: 100%
}

.adm-image-tip {
    position: relative;
    background-color: var(--adm-color-fill-content);
    height: 100%;
    min-height: 24px;
    min-width: 24px
}

.adm-image-tip>svg {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    color: var(--adm-color-weak)
}

.adm-auto-center {
    display: flex;
    justify-content: center
}

.adm-auto-center-content {
    flex: 0 1 auto
}

.adm-button {
    --color: var(--adm-color-text-light-solid);
    --text-color: var(--adm-button-text-color,var(--adm-color-text));
    --background-color: var(--adm-button-background-color,var(--adm-color-background));
    --border-radius: var(--adm-button-border-radius,4px);
    --border-width: var(--adm-button-border-width,1px);
    --border-style: var(--adm-button-border-style,solid);
    --border-color: var(--adm-button-border-color,var(--adm-color-border));
    color: var(--text-color);
    background-color: var(--background-color);
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: auto;
    padding: 7px 12px;
    margin: 0;
    font-size: var(--adm-font-size-9);
    line-height: 1.4;
    text-align: center;
    border: 1px solid var(--border-color);
    border: var(--border-width) var(--border-style) var(--border-color);
    border-radius: 4px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: opacity .15s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.adm-button:focus {
    outline: none
}

.adm-button:before {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(calc(var(--border-width)*-1),calc(var(--border-width)*-1));
    width: 100%;
    height: 100%;
    background-color: var(--adm-color-text-dark-solid);
    border: var(--border-width) var(--border-style) var(--adm-color-text-dark-solid);
    border-radius: var(--border-radius);
    opacity: 0;
    content: " ";
    box-sizing: content-box
}

.adm-button:active:before {
    opacity: .08
}

.adm-button-default.adm-button-fill-outline {
    --background-color: transparent;
    --border-color: var(--adm-color-text)
}

.adm-button-default.adm-button-fill-none {
    --background-color: transparent;
    --border-width: 0px
}

.adm-button:not(.adm-button-default) {
    --text-color: var(--adm-color-text-light-solid);
    --background-color: var(--color);
    --border-color: var(--color)
}

.adm-button:not(.adm-button-default).adm-button-fill-outline {
    --text-color: var(--color);
    --background-color: transparent
}

.adm-button:not(.adm-button-default).adm-button-fill-none {
    --text-color: var(--color);
    --background-color: transparent;
    --border-width: 0px
}

.adm-button-primary {
    --color: var(--adm-color-primary)
}

.adm-button-success {
    --color: var(--adm-color-success)
}

.adm-button-danger {
    --color: var(--adm-color-danger)
}

.adm-button-warning {
    --color: var(--adm-color-warning)
}

.adm-button-block {
    display: block;
    width: 100%
}

.adm-button-disabled {
    cursor: not-allowed;
    opacity: .4
}

.adm-button-disabled:active:before {
    display: none
}

.adm-button.adm-button-mini {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-main)
}

.adm-button.adm-button-mini.adm-button-shape-rounded {
    padding-left: 9px;
    padding-right: 9px
}

.adm-button.adm-button-small {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: var(--adm-font-size-7)
}

.adm-button.adm-button-large {
    padding-top: 11px;
    padding-bottom: 11px;
    font-size: var(--adm-font-size-10)
}

.adm-button.adm-button-shape-rounded {
    --border-radius: 1000px
}

.adm-button.adm-button-shape-rectangular {
    --border-radius: 0
}

.adm-button-loading {
    vertical-align: bottom
}

.adm-button-loading-wrapper {
    display: flex;
    height: 1.4em;
    align-items: center;
    justify-content: center
}

.adm-button-loading-wrapper>.adm-loading {
    opacity: .6
}

.adm-dot-loading {
    display: inline-block
}

.adm-center-popup {
    --background-color: var(--adm-center-popup-background-color,var(--adm-color-background));
    --border-radius: var(--adm-center-popup-border-radius,8px);
    --max-width: var(--adm-center-popup-max-width,75vw);
    --min-width: var(--adm-center-popup-min-width,280px);
    --z-index: var(--adm-center-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-center-popup .adm-center-popup-mask {
    z-index: 0
}

.adm-center-popup-wrap {
    position: fixed;
    z-index: 1;
    top: 50%;
    left: 50%;
    width: auto;
    min-width: var(--min-width);
    max-width: var(--max-width);
    transform: translate(-50%,-50%)
}

.adm-center-popup-body {
    background-color: var(--background-color);
    border-radius: var(--border-radius)
}

.adm-center-popup-close {
    position: absolute;
    z-index: 100;
    right: 8px;
    top: 8px;
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    color: var(--adm-color-weak)
}

.adm-mask {
    --z-index: var(--adm-mask-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index);
    display: block
}

.adm-mask,.adm-mask-aria-button {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.adm-mask-aria-button {
    position: absolute;
    z-index: 0;
    pointer-events: none
}

.adm-mask-content {
    z-index: 1
}

.adm-modal {
    --z-index: var(--adm-modal-z-index,1000);
    ---z-index: var(--z-index)
}

.adm-modal .adm-center-popup {
    --z-index: var(---z-index)
}

.adm-modal-body {
    width: 100%;
    max-height: 70vh;
    font-size: var(--adm-font-size-6);
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.adm-modal-body>* {
    flex: none
}

.adm-modal-body>.adm-modal-content {
    flex: auto
}

.adm-modal-body:not(.adm-modal-with-image) {
    padding-top: 20px
}

.adm-modal-image-container {
    margin-bottom: 12px;
    max-height: 40vh;
    overflow-y: scroll
}

.adm-modal-header,.adm-modal-title {
    margin-bottom: 8px;
    padding: 0 12px
}

.adm-modal-title {
    font-weight: 700;
    font-size: var(--adm-font-size-10);
    line-height: 25px;
    text-align: center
}

.adm-modal-content {
    padding: 0 12px 12px;
    max-height: 70vh;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: var(--adm-font-size-7);
    line-height: 1.4;
    color: var(--adm-color-text)
}

.adm-modal-footer {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    padding: 8px 12px 12px
}

.adm-modal-footer-empty {
    padding: 0;
    height: 8px
}

.adm-modal-footer.adm-space {
    --gap-vertical: 20px
}

.adm-modal-footer .adm-modal-button {
    font-size: var(--adm-font-size-10);
    line-height: 25px
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary) {
    padding-top: 0;
    padding-bottom: 0
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):before {
    display: none
}

.adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary):active {
    opacity: .7
}

.adm-space-item {
    flex: none
}

.adm-space {
    display: inline-flex;
    --gap: 8px;
    --gap-vertical: var(--gap);
    --gap-horizontal: var(--gap)
}

.adm-space-vertical {
    flex-direction: column
}

.adm-space-vertical>.adm-space-item {
    margin-bottom: var(--gap-vertical)
}

.adm-space-vertical>.adm-space-item:last-child {
    margin-bottom: 0
}

.adm-space-horizontal {
    flex-direction: row
}

.adm-space-horizontal>.adm-space-item {
    margin-right: var(--gap-horizontal)
}

.adm-space-horizontal>.adm-space-item:last-child {
    margin-right: 0
}

.adm-space-horizontal.adm-space-wrap {
    flex-wrap: wrap;
    margin-bottom: calc(var(--gap-vertical)*-1)
}

.adm-space-horizontal.adm-space-wrap>.adm-space-item {
    padding-bottom: var(--gap-vertical)
}

.adm-space.adm-space-block {
    display: flex
}

.adm-space-align-center {
    align-items: center
}

.adm-space-align-start {
    align-items: flex-start
}

.adm-space-align-end {
    align-items: flex-end
}

.adm-space-align-baseline {
    align-items: baseline
}

.adm-space-justify-center {
    justify-content: center
}

.adm-space-justify-start {
    justify-content: flex-start
}

.adm-space-justify-end {
    justify-content: flex-end
}

.adm-space-justify-between {
    justify-content: space-between
}

.adm-space-justify-around {
    justify-content: space-around
}

.adm-space-justify-evenly {
    justify-content: space-evenly
}

.adm-space-justify-stretch {
    justify-content: stretch
}

.adm-notice-bar {
    --background-color: var(--adm-color-weak);
    --border-color: var(--adm-color-weak);
    --text-color: var(--adm-color-text-light-solid);
    --font-size: var(--adm-font-size-7);
    --icon-font-size: var(--adm-font-size-10);
    --height: 40px;
    --adm-notice-bar-border-radius: 4px;
    --adm-notice-bar-border-width: 1px;
    height: 40px;
    height: var(--height);
    box-sizing: border-box;
    font-size: var(--font-size);
    padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: solid 1px var(--border-color);
    border: solid var(--adm-notice-bar-border-width) var(--border-color);
    border-left-width: 0;
    border-right-width: 0;
    background-color: var(--background-color)
}

.adm-notice-bar,.adm-notice-bar>span[role=img] {
    color: var(--text-color)
}

.adm-notice-bar.adm-notice-bar-alert {
    --background-color: #fff9ed;
    --border-color: #fff3e9;
    --text-color: var(--adm-color-orange)
}

.adm-notice-bar.adm-notice-bar-error {
    --background-color: var(--adm-color-danger);
    --border-color: #d9281e;
    --text-color: #fff
}

.adm-notice-bar.adm-notice-bar-info {
    --background-color: #d0e4ff;
    --border-color: #bcd8ff;
    --text-color: var(--adm-color-primary)
}

.adm-notice-bar.adm-notice-bar-success {
    --background-color: #d1fff0;
    --border-color: #a8f0d8;
    --text-color: var(--adm-color-success)
}

.adm-notice-bar .adm-notice-bar-left {
    flex-shrink: 0;
    margin-right: 8px;
    font-size: var(--icon-font-size)
}

.adm-notice-bar .adm-notice-bar-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    width: auto;
    transition-timing-function: linear;
    white-space: nowrap
}

.adm-notice-bar-wrap.adm-notice-bar .adm-notice-bar-content .adm-notice-bar-content-inner {
    white-space: normal
}

.adm-notice-bar .adm-notice-bar-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: 12px
}

.adm-notice-bar-close {
    width: 24px;
    height: 24px;
    margin-right: -3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--adm-font-size-10)
}

.adm-notice-bar-wrap {
    height: auto;
    align-items: flex-start;
    padding-top: 8px;
    padding-bottom: 8px;
    line-height: 22px
}

.adm-notice-bar-neutral {
    border-radius: var(--adm-notice-bar-border-radius)
}

.adm-notice-bar-rounded {
    border-radius: 1000px
}

.adm-notice-bar-bordered {
    border-left-width: var(--adm-notice-bar-border-width);
    border-right-width: var(--adm-notice-bar-border-width)
}

.adm-notice-bar-without-border {
    border-top-width: 0;
    border-bottom-width: 0
}

.adm-popup {
    --z-index: var(--adm-popup-z-index,1000);
    position: fixed;
    z-index: 1000;
    z-index: var(--z-index)
}

.adm-popup-body {
    position: fixed;
    background-color: var(--adm-color-background);
    z-index: calc(var(--z-index) + 10)
}

.adm-popup-body .adm-popup-close-icon {
    position: absolute;
    z-index: 100
}

.adm-popup-body-position-bottom {
    width: 100%;
    bottom: 0;
    left: 0
}

.adm-popup-body-position-bottom .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-top {
    width: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-top .adm-popup-close-icon {
    right: 8px;
    bottom: 8px
}

.adm-popup-body-position-left {
    height: 100%;
    top: 0;
    left: 0
}

.adm-popup-body-position-left .adm-popup-close-icon {
    right: 8px;
    top: 8px
}

.adm-popup-body-position-right {
    height: 100%;
    top: 0;
    right: 0
}

.adm-popup-body-position-right .adm-popup-close-icon {
    left: 8px;
    top: 8px
}

.adm-popup-close-icon {
    cursor: pointer;
    padding: 4px;
    font-size: 18px;
    line-height: 1;
    color: var(--adm-color-weak)
}

.adm-toast-mask .adm-toast-wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center
}

.adm-toast-mask .adm-toast-main {
    display: inline-block;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    max-width: 204px;
    max-height: 70%;
    overflow: auto;
    color: #fff;
    word-break: break-all;
    background-color: rgba(0,0,0,.7);
    border-radius: 8px;
    pointer-events: all;
    font-size: var(--adm-font-size-7);
    line-height: 1.5;
    box-sizing: border-box;
    text-align: left;
    text-align: initial
}

.adm-toast-mask .adm-toast-main-text {
    padding: 12px;
    min-width: 0
}

.adm-toast-mask .adm-toast-main-icon {
    padding: 35px 12px;
    min-width: 150px
}

.adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
    text-align: center;
    margin-bottom: 8px;
    font-size: 36px;
    line-height: 1
}

.adm-toast-loading {
    --size: 48px;
    margin: 0 auto 8px
}

.adm-spin-loading {
    --color: var(--adm-color-weak);
    --size: 32px;
    width: 32px;
    width: var(--size);
    height: 32px;
    height: var(--size)
}

.adm-spin-loading-svg {
    width: 100%;
    height: 100%;
    animation: adm-spin-loading-rotate .8s linear infinite
}

.adm-spin-loading-svg>.adm-spin-loading-fill {
    stroke: var(--color)
}

@keyframes adm-spin-loading-rotate {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.index-module_larkConfirm_Hedya {
    text-align: center;
    padding-top: 24px;
    background-color: var(--yq-bg-primary)
}

.index-module_larkConfirm_Hedya.index-module_dark_Bco8K {
    background-color: var(--yq-bg-secondary)
}

.index-module_larkConfirm_Hedya .index-module_title_XgoMG {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    line-height: 24px;
    font-weight: 500
}

.index-module_larkConfirm_Hedya .index-module_content_3IPt2 {
    margin-top: 8px;
    padding: 0 24px;
    font-size: 14px;
    color: var(--yq-yuque-grey-8);
    text-align: left;
    line-height: 22px;
    font-weight: 400
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF {
    margin-top: 24px;
    display: flex
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_cancelBtn_InN4Q {
    flex: 1;
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    text-align: center;
    cursor: pointer;
    font-weight: 400;
    border-top: 1px solid var(--yq-border-light);
    border-right: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_larkConfirm_Hedya .index-module_button_DDdVF .index-module_confirmBtn_O-BLZ {
    flex: 1;
    font-size: 16px;
    color: var(--yq-blue-6);
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-top: 1px solid var(--yq-border-light);
    padding: 10px 0
}

.index-module_mobileModalContainer_XqHUd.index-module_dark_Bco8K {
    background: var(--yq-bg-secondary)
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-content] {
    padding: 0
}

.index-module_mobileModalContainer_XqHUd [class~=adm-modal-footer-empty] {
    height: 0
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_xdBMN .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_qoTbx {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_qoTbx h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_qoTbx p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum,.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_leftTopPos_G-UCA {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_rightTopPos_-olEi {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midTopPos_puaum:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_\+Vg0M .DocNoteLimitModal-module_midBottomPos_C1MRC {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2tELh {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_384SJ {
    float: right
}

.DocNoteLimitModal-module_actionBtn_CUQkm {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_H0Qas {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_mnoFp {
    float: right
}

.DocNoteLimitModal-module_mobile_Kb9cX {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_limitFigure_\+Vg0M {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC,.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_rightTopPos_-olEi {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_leftTopPos_G-UCA {
    top: 38px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midBottomPos_C1MRC {
    left: 44px
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_midTopPos_puaum {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_Kb9cX .DocNoteLimitModal-module_desc_YO-N4 {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_contentRight_enYYT {
    text-align: right;
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt {
    padding-bottom: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_dashline_hmVnG {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingInfo_C4MGv {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingMainTitle_po2Ep {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingSubTitle_rN0U5 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_inputNumber_QjQU4 .paymodal-module_select_YB\+z4 {
    width: 140px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_pricingDesc_vx9fj {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_priceTag_ByB6F {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWith_dtDdE {
    margin: 8px 0 20px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_totalInfo_\+pItg {
    margin: 4px 0 8px 0
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayIcon_df156 {
    width: 24px;
    margin-top: -2px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_pricingContainer_IE8kt .paymodal-module_payWithAlipayText_zlnDZ {
    padding-left: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 {
    min-height: 444px;
    text-align: center
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_qrcodeImage_PfP82 {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc1_v1ln1 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_desc2_D8ZQ\+ {
    margin-top: 10px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeContainer_aNOA5 .paymodal-module_link_sWcgh {
    margin-top: 8px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV {
    position: relative;
    min-height: 260px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .ant-spin-spinning {
    margin-top: 130px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_alipayIconInQrCode_aiQkc img {
    width: 100%
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeImageCon_wmICV .paymodal-module_qrcodeCon_b5rk3 {
    width: 260px;
    height: 260px;
    margin: 0 auto;
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    overflow: hidden
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleDesc_cUN4K {
    font-size: 16px
}

.paymodal-module_payModalContainer_-7P4F .paymodal-module_qrcodeTitleCon_UKj\+0 .paymodal-module_qrcodeTitleValue_4k4b0 {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.paymodal-module_payModalContainer_-7P4F .ant-modal-wrap {
    z-index: 9999
}

@media only screen and (max-width: 768px) {
    .alipay-payment-icon {
        width:36px!important;
        height: 36px!important;
        margin-left: -18px!important;
        margin-top: -18px!important
    }
}

.index-module_mainInput_x4D9t.ant-input-affix-wrapper {
    max-width: 300px;
    width: 172px
}

.index-module_verifyResult_pdxsH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5);
    font-weight: 400
}

.index-module_verifyResult_pdxsH.index-module_success_NhSta {
    color: var(--yq-pea-green-6)
}

.index-module_inputVerify_kl-bC {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-weight: 500
}

.index-module_editIcon_eXpln {
    margin-left: 8px
}

.index-module_promoCodeDefault_6iUh6 {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: var(--yq-text-body);
    font-weight: 400;
    text-align: right;
    cursor: pointer
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*EX5TTI_UeRIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: auto 154px;
    background-position: 0 100%
}

html[data-kumuhana=pouli] .membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_docCountFigure_32PxF {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*w9wrT603NQIAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom
}

html[data-kumuhana=pouli] .membermodal-module_rightArea_AY\+Cs {
    background-color: var(--yq-bg-tertiary)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH {
    padding: 0 24px;
    padding-bottom: 10px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr {
    margin-bottom: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_desc_JofNr .membermodal-module_link_GhyiF {
    margin-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH h3 {
    font-size: 14px;
    font-weight: 700
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 100px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_tips_hkMrX {
    margin: -12px 0 20px 0;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_titleTips_oWQ8t {
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWith_tqEUb {
    margin: 8px 0 20px 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 24px;
    margin-top: -2px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_dashline_jMarO {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px;
    line-height: 45px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_priceTag_q1DEj.membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r {
    margin-left: 8px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: baseline;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_discountPrice_NqF1r .membermodal-module_discountPriceValue_a83du {
    font-size: 30px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC {
    margin-left: 20px;
    display: inline-block
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalAgreeButton_NdbXC>label {
    padding-top: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_memberModalTermsButton_jCPs1 {
    margin-left: 6px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 16px
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r {
    width: 140px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r:nth-child(3n) {
    margin-right: 0
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackageCard_1RD0r.membermodal-module_selectedUploadCard_VMSYV {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(0,185,107,.05)
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadSize_t-9U2 {
    font-size: 16px;
    font-weight: 600
}

.membermodal-module_memberBuyUploadModalContainer_-bTXH .membermodal-module_uploadPackages_GOTDj .membermodal-module_uploadPackagePrice_YwF2\+ {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t,.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 28px 32px;
    border-radius: 8px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*tZQsTYAY-w8AAAAAAAAAAAAAARQnAQ) no-repeat;
    background-color: var(--yq-yuque-grey-2);
    background-size: auto 154px;
    background-position: 0 100%;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO {
    flex: 1;
    padding-right: 20px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO h2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO h2 {
    font-size: 20px;
    font-weight: 500;
    margin-top: 24px;
    margin-bottom: 6px;
    line-height: 1.75
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe {
    font-size: 14px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_viewMore_WkMOe span {
    margin-right: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF {
    position: relative;
    width: 268px;
    height: 275px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*2kmNT5ZDTMkAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 268px auto;
    background-position: 0 bottom;
    margin-left: 10px;
    margin-top: 80px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    position: absolute;
    left: 164px;
    font-size: 14px;
    font-weight: 500;
    transform: translateX(-50%);
    white-space: nowrap
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_total_\+M0ck {
    top: -40px;
    background: var(--yq-black);
    border-radius: 8px;
    color: var(--yq-white);
    padding: 5px 8px;
    box-shadow: 0 8px 22px -6px rgba(38,38,38,0)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_docCountFigure_32PxF .membermodal-module_text_orjhF {
    bottom: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_descList_LiQqP {
    font-size: 14px;
    line-height: 28px;
    margin-top: 12px;
    min-height: 150px;
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 {
    margin-top: 32px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItem_dOcB7 {
    margin-bottom: 40px;
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 {
    margin-right: 12px;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemIconWrapper_tZMu0 .membermodal-module_rightItemIcon_4gCmi {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-white);
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF {
    flex: 1
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemTitle_oryDc {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    font-weight: 400;
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_leftArea_7ORzO .membermodal-module_rightsList_ljdl2 .membermodal-module_rightItemText_Y4zbF .membermodal-module_rightItemCont_nP1\+J {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs {
    width: 420px;
    background-color: var(--yq-white);
    padding: 24px;
    border-radius: 8px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H h3 {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H .membermodal-module_tips_hkMrX {
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowLabel_0Ae0H {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payDetail_jiCmc .membermodal-module_row_5uTr\+ .membermodal-module_rowCon_rkuw0 {
    color: var(--yq-text-caption)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .membermodal-module_select_u4Ura {
    width: 80px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_inputNumber_iepWR .ant-select-selector {
    background: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 460px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt .membermodal-module_title_y0sTR {
    font-size: 16px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 4px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_package_H834F {
    margin-bottom: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageDesc_bvGFI {
    margin-bottom: 12px;
    color: var(--yq-text-body)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC {
    border: 1px solid transparent;
    background-color: var(--yq-yuque-grey-2);
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
    position: relative
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_selected_TQf6j {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_disabled_WEWJJ {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_canNotSelect_JGR1f .membermodal-module_header_Uw4oS {
    opacity: .5
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_withDiscount_TPrbn {
    margin-top: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 {
    padding: 10px 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4 .membermodal-module_right_pFVL7 {
    display: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j {
    padding: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_right_pFVL7 {
    display: flex
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC.membermodal-module_showDetailRight_0rol4.membermodal-module_selected_TQf6j .membermodal-module_detailRights_Al4vh {
    display: block
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_discountBadge_KJ2Kc {
    position: absolute;
    right: -1px;
    top: -13px;
    background-image: linear-gradient(to top right,#ff8487,#ff5b5d,#ff4d4f);
    color: #fff;
    border-radius: 8px 8px 8px 1px;
    height: 24px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 24px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS {
    display: flex;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS h4 {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_headerIcon_ylijA {
    margin-right: 6px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_priceArea_w0YHS {
    font-size: 10px;
    color: var(--yq-yuque-grey-7)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_price_6JXHp {
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-black);
    margin-right: 2px;
    margin-left: 7px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_originalPrice_qBFT8 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    text-align: right;
    line-height: 20px;
    font-weight: 500;
    margin-right: 2px;
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_header_Uw4oS .membermodal-module_hasDiscount_08fuh {
    -webkit-text-decoration: line-through;
    text-decoration: line-through;
    margin-left: 5px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_body_hZbtu {
    text-align: left
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    margin-top: 6px;
    justify-content: space-between
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_right_pFVL7 .membermodal-module_showMore_4dDVE {
    font-size: 10px;
    display: flex;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRights_Al4vh {
    display: none;
    width: 340px;
    margin-top: 13px;
    padding-top: 13px;
    border-top: 1px solid var(--yq-border-light);
    line-height: 25px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH {
    width: 185px;
    font-size: 12px;
    color: var(--yq-yuque-grey-8);
    display: inline-block;
    vertical-align: top
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd),.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_detailRight_L0lYH:nth-child(odd) {
    width: 145px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_packageList_NMR0b .membermodal-module_packageItem_OyxZC .membermodal-module_rightChecked_7I7eX {
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb {
    margin: 20px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWith_tqEUb .membermodal-module_verifyBtn_Jb2go {
    font-weight: 500;
    color: var(--yq-yuque-grey-9)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayIcon_o9Nmb {
    width: 18px;
    margin-top: -2px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_payWithAlipayText_PWm4b {
    padding-left: 8px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_line_-4HSJ {
    border-top: 1px solid var(--yq-border-light);
    margin: 10px 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_totalInfo_lKVo\+ .membermodal-module_priceTag_q1DEj {
    font-size: 20px;
    font-weight: 500
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY {
    display: inline-block;
    margin-top: 16px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY .membermodal-module_text_orjhF {
    color: var(--yq-yuque-grey-8)
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_agreeButton_vl5tY span {
    padding-right: 0
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_memberModalButtonCon_6vKEz {
    margin-top: 20px
}

.membermodal-module_memberBuyModalContainer_XeZ7t .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw,.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_confirmPayBtn_rnhOw {
    height: 40px;
    font-weight: 500
}

.membermodal-module_memberUpgradeModalContainer_pOAjj {
    padding: 0
}

.membermodal-module_memberUpgradeModalContainer_pOAjj .membermodal-module_rightArea_AY\+Cs .membermodal-module_rightAreaInfo_FLyOt {
    min-height: 400px
}

.member-modal .ant-modal-body,.membermodal-module_memberBuyModalH5Container_9ZOm4 {
    padding: 0
}

.member-modal .ant-modal-close-x {
    width: 40px;
    height: 40px;
    line-height: 40px
}

.member-modal .ant-modal-header {
    border-bottom: 0
}

.membermodal-module_selectTip_4qIX9 .membermodal-module_text_orjhF {
    margin-right: 8px
}

.successmodal-module_successModalContainer_ENOHy {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoCon_EYL4y {
    padding: 0 24px 30px 24px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfo_O8N6K {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalInfoTitle_yXIec {
    font-size: 18px;
    margin-top: 24px;
    font-weight: 500
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu {
    margin-top: 40px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalButtonCon_uM8cu .successmodal-module_checkUploadButton_vFqCf {
    margin: 1px 0 0 12px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalApplyReceiptButton_Yj2F2 {
    margin-left: 10px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalSubTitle_hVcSK {
    font-size: 14px;
    margin: 16px 0 16px;
    font-weight: 500;
    color: var(--yq-black)
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalItem_zxqQ2 {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 12px 0
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalBlank_tgeED {
    border-top: 1px solid var(--yq-border-light);
    margin-top: 16px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_successModalWechatQRCon_\+CtHf img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.successmodal-module_successModalContainer_ENOHy .successmodal-module_rightChecked_8E2DG {
    margin-top: -4px;
    margin-right: 5px;
    vertical-align: middle;
    color: var(--yq-yuque-green-6)
}

.processmodal-module_processModalContainer_jrXul {
    text-align: center;
    padding: 10px 0
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalImg_kmc6C {
    width: 120px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalInfo_fOy8- {
    margin-top: 12px
}

.processmodal-module_processModalContainer_jrXul .processmodal-module_processModalOrderId_tDK\+c {
    color: var(--yq-white);
    text-align: center
}

.receipt-module_contactInfoTitle_\+WTvl {
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 10px 0
}

.receipt-module_receiptButtons_tBTuB {
    margin-top: 8px
}

.receipt-module_cancelButton_1xXKN {
    margin-left: 10px
}

.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_contactTips_rQ4lg,.receiptmodal-module_receiptContainer_E57\+C .receiptmodal-module_receiptTypeTitle_u72yN {
    margin-bottom: 10px
}

.receiptmodal-module_receiptDesc_87YQ3 {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

.receiptmodal-module_receiptDesc_87YQ3 a {
    margin-left: 4px
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitModal-module_modal_9Rtx7 .ant-modal-body {
    padding: 0
}

.DocNoteLimitModal-module_content_SXXvX {
    padding: 142px 24px 24px 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*VWKCRrV2n_cAAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: 480px 120px;
    overflow: hidden
}

.DocNoteLimitModal-module_content_SXXvX h1 {
    font-size: 18px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitModal-module_content_SXXvX p {
    margin-top: 16px;
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitModal-module_limitFigure_7z-6U {
    height: 126px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*Cq4iSrVHmH4AAAAAAAAAAAAAARQnAQ) var(--yq-yuque-grey-1) no-repeat;
    position: relative;
    background-size: 400px 86px;
    background-position: 16px 22px;
    border-radius: 8px;
    margin-top: 20px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM,.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    position: absolute;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_leftTopPos_9X2He {
    left: 16px;
    top: 30px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_rightTopPos_CQ7st {
    bottom: 70px;
    right: 16px;
    max-width: 200px
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 120px;
    top: 20px;
    background: var(--yq-black);
    color: var(--yq-white);
    border-radius: 4px;
    padding: 4px 10px 4px 8px;
    font-weight: 500;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midTopPos_b35jM:after {
    content: "";
    bottom: -10px;
    position: absolute;
    width: 0;
    border: 5px solid transparent;
    border-top-color: var(--yq-black);
    left: 50%;
    transform: translateX(-50%)
}

.DocNoteLimitModal-module_limitFigure_7z-6U .DocNoteLimitModal-module_midBottomPos_VQGhz {
    bottom: 20px;
    left: 80px
}

.DocNoteLimitModal-module_footer_2ctWH {
    margin-top: 24px
}

.DocNoteLimitModal-module_footerAction_IlCRq {
    float: right
}

.DocNoteLimitModal-module_actionBtn_j4wTK {
    margin-right: 8px;
    min-width: 88px
}

.DocNoteLimitModal-module_footerOrgOwnerAction_3oyZh {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.DocNoteLimitModal-module_footerOrgMemberAction_5PEmm {
    float: right
}

.DocNoteLimitModal-module_mobile_MQEQW {
    padding: 0 24px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_limitFigure_7z-6U {
    background-size: 203px 86px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz,.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_rightTopPos_CQ7st {
    font-size: 12px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_leftTopPos_9X2He {
    top: 38px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midBottomPos_VQGhz {
    left: 44px
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_midTopPos_b35jM {
    left: 68px;
    font-size: 12px;
    height: 20px;
    width: 41px;
    top: 29px;
    line-height: 20px;
    padding: 0
}

.DocNoteLimitModal-module_mobile_MQEQW .DocNoteLimitModal-module_desc_DQeXN {
    margin-top: 16px;
    font-size: 14px;
    color: var(--yq-yuque-grey-800);
    line-height: 22px;
    text-align: left
}

.index-module_modal_ciiIL .ant-modal-content {
    overflow: hidden
}

.index-module_modal_ciiIL .ant-modal-body {
    padding: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modal_ciiIL .ant-modal-confirm-btns {
    display: none
}

.index-module_modalDesktop_CvNi7 {
    max-width: 100vw!important
}

.index-module_modalDesktop_CvNi7 .ant-modal-content {
    overflow: hidden
}

.index-module_modalDesktop_CvNi7 .ant-modal-body {
    padding: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modalDesktop_CvNi7 .ant-modal-confirm-btns {
    display: none
}

.head-user-info-popover .member-badge-container {
    padding: 0
}

.member-badge-desc {
    margin-top: -5px;
    margin-bottom: 12px
}

.member-badge-upgrade-action {
    color: var(--yq-text-link)!important
}

.badge-module_memberBadgeContainer_truSH {
    padding: 16px 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH {
    display: flex;
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 1
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_safeIcon_LvS1y {
    position: relative;
    top: -1px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeName_Rb9bH .badge-module_unsafeIcon_fj7tW {
    margin-left: 2px;
    position: relative;
    top: -10px;
    cursor: pointer
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeNameCon_UOZEP {
    line-height: 24px;
    display: inline-block;
    max-width: 450px;
    margin-right: 4px;
    word-break: break-all;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO {
    position: relative;
    top: 0;
    letter-spacing: -1em;
    vertical-align: top;
    height: 100%;
    display: inline-block;
    margin-top: -1px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO:before {
    content: "\3000"
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeIcon_cNWkO a {
    display: inline
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeDesc_nc5JL {
    line-height: 1;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeEnDesc_xoJbF {
    line-height: 1.5;
    margin-top: 4px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeDisabled_YFT6S {
    color: var(--yq-text-disable);
    cursor: not-allowed;
    margin-left: 8px
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block;
    font-size: 12px;
    color: var(--yq-text-link)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberBadgeUpgradeAction_Rs3Pr:hover {
    color: var(--yq-ant-link-hover-color)
}

.badge-module_memberBadgeContainer_truSH .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.badge-module_memberBadgeTooltip_JvMu7 {
    max-width: 400px
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberBadgeUpgradeAction_Rs3Pr {
    display: inline-block
}

.badge-module_memberBadgeTooltip_JvMu7 .badge-module_memberDesc_iad4N {
    padding-right: 8px
}

.OrgCertification-module_wrap_mcHiA {
    display: flex;
    align-items: center
}

.OrgCertification-module_wrap_mcHiA .larkui-tooltip {
    display: flex;
    align-items: center;
    justify-content: center
}

.OrgCertification-module_wrap_mcHiA .larkui-popover-trigger {
    display: flex;
    align-items: center
}

.OrgCertification-module_btn_veUok {
    padding: 0;
    font-size: 14px
}

.OrgCertification-module_icon_mgkVd {
    margin-right: 6px;
    margin-left: 2px;
    cursor: pointer
}

.OrgCertification-module_text_JDkK6 {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 14px
}

.OrgCertification-module_wrap_mcHiA .OrgCertification-module_main_umJwo {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.OrgCertification-module_wrap_mcHiA .OrgCertification-module_main_umJwo a {
    margin: 0;
    line-height: 30px
}

.OrgCertification-module_disabled_DMrOf {
    filter: grayscale(1)
}

.index-module_orgCostDetail_cLnEB {
    cursor: pointer;
    font-weight: 500
}

.index-module_content_oAhH8 {
    width: 378px
}

.index-module_content_oAhH8 .ant-divider {
    margin: 12px 0
}

.index-module_row_by2z2 {
    display: flex;
    justify-content: space-between;
    padding: 6px 0
}

.index-module_row_by2z2:last-child {
    font-weight: 700
}

.index-module_row_by2z2 .index-module_right_3Ce8Y {
    display: flex
}

.index-module_tips_EUcPA {
    padding: 2px 0;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_title_J6b5y {
    padding: 8px 8px 8px 0
}

.index-module_title_J6b5y a {
    color: var(--yq-text-caption)
}

.index-module_hasDiscount_TqEsK {
    color: var(--yq-orange-7)
}

.index-module_rawPrice_2UTkU {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_detailsWrapper_rnkfa .index-module_detailTitle_-gjun {
    color: var(--yq-text-primary)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt {
    height: 72px;
    max-height: 80px;
    overflow-y: auto
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK span:nth-child(2n) {
    color: var(--yq-text-body)
}

.index-module_summary_TYNAM {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_summary_TYNAM .index-module_left_m83qX {
    display: block
}

.index-module_summary_TYNAM .index-module_left_m83qX .index-module_summaryTitle_FTeFf {
    color: var(--yq-text-primary)
}

.index-module_summary_TYNAM .index-module_left_m83qX span {
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_summary_TYNAM .index-module_right_3Ce8Y .index-module_price_5CyQB {
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_divider_Rppou {
    margin: 12px auto
}

.index-module_paymentSelector_60wvq {
    margin: 0 0 24px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_paymentSelector_60wvq h4 {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e {
    position: relative;
    padding: 16px 10px;
    height: 100%;
    border-radius: 8px;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 {
    display: flex;
    justify-content: space-between
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_icon_sjp\+e {
    margin-left: 6px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_paymentType_rBxoj {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceNumber_6EuVJ {
    color: var(--yq-text-primary);
    font-size: 12px;
    font-weight: 500
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceUnit_xqqPQ {
    color: var(--yq-text-caption);
    font-size: 10px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_oldMemberPrice_4QJhr {
    display: block;
    text-align: right;
    font-size: 10px;
    color: var(--yq-text-caption);
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_desc_5wzpx {
    position: absolute;
    bottom: 16px;
    margin-top: 6px;
    width: 160px;
    font-size: 12px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_active_HZTq5 {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-theme)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: hsla(0,0%,100%,.5);
    cursor: not-allowed
}

html[data-kumuhana=pouli] .index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    background-color: rgba(0,0,0,.5)
}

.index-module_tooltip_lMvUZ {
    font-size: 14px;
    white-space: nowrap
}

.index-module_tooltip_lMvUZ a {
    margin-left: 8px
}

.BuyModal-module_row_k9bGD {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyModal-module_row_k9bGD h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 {
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 span {
    color: var(--yq-text-caption)
}

.BuyModal-module_row_k9bGD .BuyModal-module_right_-\+dqF {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq {
    display: flex;
    align-items: center
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq svg {
    margin-right: 6px
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payBtn_JujzZ {
    margin: 12px auto;
    width: 100%
}

.BuyModal-module_promoCode_IT7ha a,.BuyModal-module_promoCode_IT7ha a:hover {
    color: var(--yq-text-primary)
}

.BuyModal-module_tips_fuHYC {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingContainer_tqVEA {
    display: flex;
    justify-content: space-between;
    padding: 32px;
    min-height: 688px;
    background-color: var(--yq-yuque-grey-2);
    background-position: 0 100%;
    background-repeat: no-repeat;
    border-radius: 8px
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*z00vQYi5mvAAAAAAAAAAAAAAARQnAQ);
    background-size: 274px 215px
}

html[data-kumuhana=pouli] .BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*4iv1S4HXxg4AAAAAAAAAAAAADvuFAQ/original)
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_enterprise_KQPTt {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*h_IRR6H2EB8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 238px 170px
}

.BuyModal-module_pricingLeft_X4ns6 {
    width: 324px;
    padding-top: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG .BuyModal-module_icon_UPf-t {
    margin-left: 8px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentDesc_7e1Uv {
    margin: 4px 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL {
    display: inline-block;
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL .BuyModal-module_icon_UPf-t {
    margin-left: 0;
    vertical-align: text-top
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe {
    margin-bottom: 40px;
    position: relative;
    padding-left: 48px;
    height: 46px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_iconWarpper_j2vhb {
    position: absolute;
    top: 50%;
    left: 0;
    width: 36px;
    height: 36px;
    transform: translateY(-50%);
    background-color: var(--yq-bg-primary);
    box-shadow: 0 2px 9px 0 rgba(0,0,0,.02);
    border-radius: 6px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_icon_UPf-t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_title_aoygt {
    margin-bottom: 4px;
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_desc_nWmKR {
    color: var(--yq-text-caption);
    font-size: 12px
}

.BuyModal-module_pricingRight_p54lJ {
    position: relative;
    padding: 24px 24px 98px 24px;
    width: 420px;
    min-height: 624px;
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .BuyModal-module_pricingRight_p54lJ {
    background: var(--yq-bg-tertiary)
}

.BuyModal-module_avatar_SRhTt {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background-color: var(--yq-bg-tertiary)
}

.BuyModal-module_termsContainer_i187T span {
    color: var(--yq-text-body)!important
}

.BuyModal-module_termsContainer_i187T .ant-checkbox+span {
    padding-right: 0
}

.BuyModal-module_statsWrapper_ZDvF1 .BuyModal-module_statsDesc_1iKOA {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 28px
}

.BuyModal-module_compact_qVAW5 h3 {
    margin-bottom: 4px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_active_SC0g\+ {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_settle_6sm-p {
    margin-left: 12px
}

.BuyModal-module_channel_aD1hq button[disabled] {
    padding: 4px 12px;
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyModal-module_channel_aD1hq button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.ant-btn.BuyModal-module_channel_aD1hq {
    padding: 4px 12px
}

.BuyModal-module_name_bL3Ia {
    max-width: 230px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BuyModal-module_memberSizeInput_xIYHO {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.BuyModal-module_memberSizeInput_xIYHO .BuyModal-module_errorMsg_ZbqFE {
    position: absolute;
    bottom: -24px;
    max-width: 200px;
    white-space: nowrap;
    color: var(--yq-function-error);
    font-weight: 400;
    font-size: 14px
}

.BuyModal-module_footer_eF3uw {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 48px);
    transform: translateX(-50%)
}

.BuyMember-module_row_9srYf {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyMember-module_row_9srYf h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og {
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og span {
    color: var(--yq-text-caption)
}

.BuyMember-module_row_9srYf .BuyMember-module_right_Oni7O {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT {
    display: flex;
    align-items: center
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT svg {
    margin-right: 6px
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payBtn_2QBqR {
    margin: 12px auto;
    width: 100%
}

.BuyMember-module_promoCode_3q8my a,.BuyMember-module_promoCode_3q8my a:hover {
    color: var(--yq-text-primary)
}

.BuyMember-module_tips_0MaeW {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyMember-module_pricingContainer_oSImw h2 {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_expiredDesc_kyKaA {
    margin-bottom: 24px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BuyMember-module_channel_oCioT.BuyMember-module_active_hyrgK {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyMember-module_channel_oCioT button[disabled] {
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyMember-module_channel_oCioT button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_right_Oni7O>:not(:first-child) {
    margin-left: 12px
}

.Pay-module_row_02i1B {
    padding-bottom: 24px
}

.Pay-module_row_02i1B:nth-child(6) {
    padding-bottom: 0
}

.Pay-module_row_02i1B .Pay-module_left_u3-Jq {
    padding-bottom: 8px
}

.Pay-module_row_02i1B .Pay-module_name_4PPzr {
    font-size: 16px;
    color: var(--yq-text-primary);
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px
}

.Pay-module_row_02i1B .Pay-module_nameRow_iKvRZ {
    display: flex
}

.Pay-module_row_02i1B .Pay-module_version_C36aU {
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-8);
    border-radius: 4px;
    font-size: 12px;
    padding: 1px 3px;
    background-color: var(--yq-blue-1)
}

.Pay-module_row_02i1B .Pay-module_version_C36aU.Pay-module_paidVersion_pBnh8 {
    color: var(--yq-yellow-7);
    border-color: var(--yq-yellow-7);
    background-color: var(--yq-yellow-1)
}

.Pay-module_cardList_OD1er {
    display: flex;
    margin-bottom: 24px
}

.Pay-module_memberSize_Snqz8 {
    overflow: hidden;
    opacity: 0;
    height: 0;
    transition: all .3s
}

.Pay-module_memberSize_Snqz8.Pay-module_show_u86Cd {
    opacity: 1;
    height: 81px
}

.Pay-module_totalInfo_YM7Fb {
    height: 45px;
    margin: 4px 0 18px 0;
    flex-wrap: wrap;
    font-weight: 600
}

.Pay-module_footer_MsuSK,.Pay-module_totalInfo_YM7Fb {
    display: flex;
    align-items: center
}

.Pay-module_footer_MsuSK .Pay-module_termsContainer_MMlAf {
    margin-left: 12px;
    display: flex
}

.Pay-module_pricingContainer_hjkUd {
    padding-bottom: 8px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_desc_a1fXI {
    margin-top: -20px;
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_dashline_y-S0k {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 24px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingInfo_NRstM {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingMainTitle_pinlM {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingSubTitle_FlFF9 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_inputNumber_Uaz26 {
    height: 32px;
    width: 200px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingDesc_SK8fY {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz {
    font-size: 28px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 45px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz.Pay-module_hasDiscount_KLNoP {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz {
    margin-left: 16px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz .Pay-module_discountPriceValue_CUsg2 {
    font-size: 28px;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWith_vFDxq {
    margin: 8px 0 20px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayIcon_SjV5X {
    width: 24px;
    margin-top: -2px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayText_r3AWK {
    padding-left: 8px
}

.Pay-module_uploadPackages_VO59r {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 32px
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL {
    width: 160px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL:nth-child(3n) {
    margin-right: 0
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL.Pay-module_selectedUploadCard_yWd2Y {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(37,184,100,.05)
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadSize_WpwBc {
    font-size: 16px;
    font-weight: 600
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackagePrice_zQPrm {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_buyMemberCon_CB8\+C {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q,.Pay-module_buyMemberCon_CB8\+C .Pay-module_upgradeMember_6duT1 {
    background-color: var(--yq-bg-secondary);
    padding: 16px;
    width: 244px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q .Pay-module_memberValue_l6w8U {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0
}

.Pay-module_tips_BIW\+T {
    margin-top: 6px;
    color: var(--yq-text-caption);
    width: 500px
}

.Pay-module_discountInfo_Uq4\+K {
    color: var(--yq-text-caption);
    padding-bottom: 8px
}

.Pay-module_promo_sD6ap {
    margin-top: -6px
}

.Pay-module_promo_sD6ap .Pay-module_left_u3-Jq {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 120px
}

.Pay-module_promo_sD6ap .Pay-module_inputNumber_Uaz26 {
    padding-top: 8px;
    padding-bottom: 40px
}

.Qrcode-module_info_XwHqZ {
    flex: auto;
    max-width: 332px
}

.Qrcode-module_infoTitle_nNa5e {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500
}

.Qrcode-module_subInfoTip_DYh05 {
    color: var(--yq-text-body);
    margin-top: 10px
}

.Qrcode-module_infoTip_wp2zv>span {
    margin-right: 28px
}

@media only screen and (max-width: 575px) {
    .Qrcode-module_infoTip_wp2zv>span {
        display:block
    }
}

.Qrcode-module_desc_T1-nf {
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi {
    padding-bottom: 8px
}

.Qrcode-module_pricingContainer_2\+hyi h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_dashline_n20jZ {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingInfo_Pe1Ai {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingMainTitle_D0fu0 {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingSubTitle_3SVt2 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P .Qrcode-module_select_2Topv {
    width: 140px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingDesc_aTeur {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_priceTag_gSsWI {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWith_Orohw {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_totalInfo_-\+rtG {
    margin: 4px 0 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayIcon_Hoi7K {
    width: 24px;
    margin-top: -2px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayText_wbuGd {
    padding-left: 8px
}

.Qrcode-module_processContainer_5BqWV {
    text-align: center;
    padding: 10px 0
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processImg_MgbCi {
    width: 120px
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processInfo_ST\+BP {
    margin-top: 12px
}

.Qrcode-module_termsContainer_Uqaqj {
    margin-bottom: 12px;
    display: flex
}

.Qrcode-module_bindingContainer_b3OnQ {
    padding: 24px 0;
    text-align: center
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingImg_sEWSW {
    width: 80px
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingInfo_qhBjF {
    color: var(--yq-text-primary);
    margin: 24px 0
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingFooter_7PjRS .Qrcode-module_bindingVerify_CgNHw {
    margin-right: 8px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi {
    position: relative
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 img {
    width: 100%
}

@media only screen and (max-width: 768px) {
    .Qrcode-module_qrcodeImageCon_yy\+Gi .alipayIconInQrCode {
        width:36px;
        height: 36px;
        margin-left: -18px;
        margin-top: -18px
    }
}

.Qrcode-module_successContainer_RvR5s {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfoCon_Yt35q {
    padding: 10px 40px 30px 40px
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfo_w7k6O {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.Qrcode-module_qrContainer_K9uEQ {
    margin-bottom: 20px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeImg_GAmpl {
    width: 72px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeInfo_8NXdM {
    font-size: 14px;
    font-weight: 400;
    padding: 24px 0 0 30px;
    color: var(--yq-text-body);
    line-height: 1.8
}

.Qrcode-module_qrcodeContainer_c79eX {
    min-height: 444px;
    text-align: center
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_qrcodeImage_UEIyZ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    width: 55%
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc1_ArZR9 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc2_E5sDl {
    margin-top: 10px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_link_ZEjlM {
    margin-top: 8px
}

.Qrcode-module_receiptTypeTitle_jIYOl {
    margin-bottom: 10px
}

.Qrcode-module_receiptTypeTitleText_8q0XQ {
    margin-bottom: 8px
}

.Qrcode-module_applyReceiptButton_Wfqmq {
    margin-left: 10px
}

.Qrcode-module_contactTips_ETe9S {
    margin-bottom: 10px
}

.Qrcode-module_buttonCon_Jic-k {
    margin-top: 8px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleDesc_OOQby {
    font-size: 16px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleValue_PBwE\+ {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.PayModal-module_noLineModal_r4djn .ant-modal-header {
    border-bottom: 0
}

.PayModal-module_warnContent_TTDov {
    margin-top: 20px;
    margin-bottom: 24px
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-body {
    padding: 0
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px
}

.ProcessingModal-module_processContainer_dOZ9t {
    text-align: center;
    padding: 10px 0
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processImg_Fl\+7o {
    width: 120px
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processInfo_VPh0u {
    margin-top: 12px
}

.paid-success-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.paid-success-modal .ant-modal-body {
    padding: 0
}

.paid-success-modal .ant-modal-body img {
    width: 100%
}

.paid-success-modal .ant-col {
    padding-top: 10px
}

.paid-success-modal .ant-col-18 {
    font-weight: 600
}

.PaidSuccessModal-module_successModalContainer_gb6Iw {
    color: var(--yq-text-body);
    border-radius: 8px;
    overflow: hidden
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoCon_aLoy0 {
    padding: 20px 24px 30px 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfo_TQeHl {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoTitle_fmm0I {
    font-size: 28px;
    margin-top: 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns {
    margin-top: 32px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_applyReceiptButton_BrptN {
    margin-left: 12px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_checkUploadButton_n0Sct {
    margin-left: 12px;
    margin-top: 1px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalApplyReceiptButton_hbmRn {
    margin-left: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalSubTitle_Hq8oy {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalItem_EBnTl {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalBlank_3ENdm {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.ReceiptModal-module_contactTips_f0fAJ,.ReceiptModal-module_receiptTypeTitle_mZi8G {
    margin-bottom: 10px
}

.ReceiptModal-module_receiptTypeTitleText_QQBhY {
    margin-bottom: 8px
}

.ReceiptModal-module_receiptDesc_fY2bw {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

@keyframes SubAccountInfoModal-module_loadingCircle_mPmQ5 {
    to {
        transform: rotate(1turn)
    }
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p:first-child {
    border-bottom: 1px dashed var(--yq-border-primary);
    padding-bottom: 16px;
    margin-bottom: 32px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldTitle_tgwmv {
    display: inline-block;
    font-weight: 600;
    min-width: 80px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p p {
    margin-bottom: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldPaymentAlert_UOLk7 {
    margin-bottom: 20px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e {
    margin-top: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e .SubAccountInfoModal-module_btn_WuNy4 {
    margin-right: 16px
}

.SubAccountInfoModal-module_loading_sNKQL {
    padding: 32px 0;
    text-align: center
}

.SubAccountInfoModal-module_loading_sNKQL .SubAccountInfoModal-module_loadingIcon_M7IJr {
    animation: SubAccountInfoModal-module_loadingCircle_mPmQ5 1s linear infinite
}

.SubAccountInfoModal-module_loading_sNKQL .larkui-icon {
    font-size: 24px
}

.SubAccountInfoModal-module_loadingDesc_QHGbP {
    margin-top: 16px
}

.index-module_buyButton_pN7y0.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_buyButton_pN7y0a.ant-btn span {
    line-height: 1
}

.index-module_upgradeButton_nJqrZ.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_upgradeButton_nJqrZa.ant-btn span {
    line-height: 1
}

.index-module_orgVersionLabel_egPu3 {
    border-width: 1px;
    border-radius: 4px;
    border-style: solid;
    padding: 0 4px;
    margin-left: 16px
}

.index-module_tip_rQx-e {
    margin-top: 2px;
    margin-bottom: 2px;
    display: flex;
    justify-content: space-between
}

.index-module_tip_rQx-e a:before {
    position: relative
}

.index-module_tip_rQx-e span span {
    margin-left: 12px
}

.index-module_tip_rQx-e .index-module_payment_pQExj {
    color: var(--yq-yuque-grey-8)
}

.index-module_tip_rQx-e .index-module_paymentLink_GbvgZ {
    color: var(--yq-blue-6);
    line-height: 60px
}

.index-module_paymentGuideWrapper_2jg39 {
    max-width: 584px;
    margin-left: auto;
    margin-right: auto;
    padding: 24px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_thumb_Lw5zW {
    display: block;
    width: 148px;
    height: 120px;
    margin: 0 auto 16px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100%
}

.index-module_paymentGuideWrapper_2jg39 .index-module_title_EykNL {
    text-align: center;
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 700
}

.index-module_paymentGuideWrapper_2jg39 .index-module_body_\+1-cr {
    margin: 12px auto 32px auto;
    max-width: 400px;
    color: var(--yq-text-body);
    text-align: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_link_zDdoa {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB {
    display: flex;
    align-items: center;
    justify-content: end
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB .index-module_btnTryout_5GVmK {
    margin-right: 8px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_btnTryout_5GVmK {
    margin-bottom: 12px;
    width: 148px;
    height: 32px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_link_zDdoa {
    width: 148px;
    text-align: center
}

.index-module_actions_LRgdL .index-module_btn_vRXZD {
    width: 160px;
    height: 32px
}

.index-module_premiumFeaturesLabel_q\+uFd {
    width: 86px;
    height: 37px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*pIrCQLn06McAAAAAAAAAAAAAARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_alignCenter_h648T {
    display: flex;
    align-items: center;
    position: relative
}

.index-module_orgExpiredTip_k7iLg {
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgExpiredTipOpt_QDhkZ,.index-module_orgExpiredTipText_gQuZw {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    width: 140px
}

.index-module_orgExpiredTipText_gQuZw {
    white-space: nowrap
}

.index-module_orgExpiredTipText_gQuZw span {
    color: var(--yq-yuque-grey-9)
}

.index-module_payflowTitle_LM3aR {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px
}

.index-module_freeWrapper_BlI2K {
    display: flex;
    justify-content: space-between
}

.index-module_payflowLink_Wt3fM {
    margin-left: 0;
    display: inline-block
}

.index-module_isExpired_UgOgs {
    border-bottom: 1px solid var(--yq-yuque-grey-4);
    padding-bottom: 10px
}

.index-module_freeTips_ybaQ- {
    display: flex;
    justify-content: space-between
}

.index-module_upgrade_qL2nR .ant-btn-link {
    padding: 0;
    height: auto
}

.index-module_wrapper_ulVDd .ant-modal-body {
    padding: 0!important
}

.index-module_wrapper_ulVDd .anticon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-information {
    display: none
}

.index-module_wrapper_ulVDd .ant-modal-confirm-content {
    width: 480px;
    margin-top: 0
}

.index-module_wrapper_ulVDd .ant-modal-confirm-btns {
    display: none
}

.index-module_wrapper_ulVDd .index-module_content_SO1HL {
    margin: 28px 0;
    padding: 0 24px
}

.index-module_wrapper_ulVDd .index-module_title_SVPzd {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_wrapper_ulVDd .index-module_desc_MlCQ9 {
    margin-top: 16px;
    margin-bottom: 32px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_wrapper_ulVDd .index-module_img_SBaEY {
    width: 480px
}

.index-module_trialButton_tH3iD {
    margin-left: 10px
}

.index-module_footer_cL9eu {
    display: flex;
    justify-content: flex-end
}

.index-module_footer_cL9eu .index-module_btn_XNqZC {
    margin-left: 10px
}

.index-module_title_INHx3 {
    font-size: 16px;
    line-height: 18px;
    vertical-align: middle;
    margin-right: 10px
}

.index-module_help_AVFYJ {
    font-size: 14px;
    line-height: 18px;
    vertical-align: middle;
    font-weight: 400
}

.index-module_tip_T1jyO {
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary);
    margin-bottom: 6px
}

.index-module_errMessage_nSIYH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5)
}

.index-module_form_W9V8N .larkui-form-item {
    margin-bottom: 0
}

.index-module_footer_G0PQG {
    margin-top: 16px
}

.index-module_submitBtn_\+TZ\+Q {
    margin-right: 8px
}

.MemberLevelIcon-module_memberLevelIcon_5CHat a {
    margin-left: 5px;
    display: flex;
    align-items: center
}

.MemberLevelIcon-module_memberLevelIcon_5CHat .MemberLevelIcon-module_icon_ivQ-f {
    vertical-align: middle;
    z-index: 1
}

.MemberLevelIcon-module_memberLevelIcon_5CHat .MemberLevelIcon-module_levelName_yHocY {
    font-size: 20px;
    height: 32px;
    line-height: 32px;
    margin-left: -10px;
    padding-left: 22px;
    padding-right: 16px;
    border-radius: 0 30px 30px 0;
    font-weight: 500;
    transform: scale(.5);
    transform-origin: left
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIcon1_HGaOq .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#f4d06f,#fcf0cb);
    color: #6a5619
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIcon2_STP5o .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#424241,#958d7c);
    color: #fbf7ee
}

.MemberLevelIcon-module_memberLevelIcon_5CHat.MemberLevelIcon-module_memberLevelIconExpired_5bwcs .MemberLevelIcon-module_levelName_yHocY {
    background: linear-gradient(270deg,#dcdcdc,#ebebeb);
    color: #646464
}

.board-module_memberBoardContainer_gN7pi {
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--yq-yuque-grey-2)
}

.board-module_memberBoardContainer_gN7pi .ant-btn-link {
    padding-left: 0;
    padding-right: 0
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardRight_6DlQt {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgIcon_r70ZR {
    margin-right: 16px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgNameWrapper_OCGfs .board-module_orgName_BE6gK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500;
    display: flex
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_orgNameWrapper_OCGfs .board-module_expiredDesc_AbfLB {
    margin-top: 4px;
    font-size: 14px;
    color: var(--yq-text-caption);
    font-weight: 400
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ a {
    font-size: 14px;
    font-weight: 400;
    margin-left: 4px;
    line-height: 24px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_certification_b5\+ky {
    margin-left: 6px;
    font-size: 14px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardName_YNQRQ .board-module_certification_b5\+ky a {
    line-height: 20px;
    height: 20px
}

.board-module_memberBoardContainer_gN7pi .board-module_expiredDesc_AbfLB {
    color: var(--yq-text-caption)
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardNameCon_i3Jda {
    min-height: 32px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardMainName_UiUuz {
    color: var(--yq-yellow-7);
    margin-right: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardGroupIcon_rIq\+p {
    margin-left: -8px;
    position: relative;
    top: 2px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardRenewButton_Gg-zI {
    margin-left: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardCountdown_RqOSU {
    border: 1px solid var(--yq-yuque-green-3);
    border-radius: 2px;
    background: var(--yq-bg-tertiary);
    color: var(--yq-yuque-green-3);
    margin-left: 8px;
    font-size: 10px;
    padding: 1px 4px;
    position: relative;
    top: -1px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardDesc_AnX25 {
    color: var(--yq-yuque-grey-7);
    margin-top: 4px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardLink_uaK6n {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 8px
}

.board-module_memberBoardContainer_gN7pi .board-module_memberBoardOrgDesc_yTvNM {
    margin-left: 8px
}

.selector-module_memberSelectorContainer_SzVML {
    position: relative
}

.selector-module_memberSelectorContainer_SzVML .ant-radio-button-wrapper {
    background: transparent!important;
    flex: 1;
    text-align: center;
    height: 56px;
    padding-top: 7px;
    line-height: 20px
}

.selector-module_memberSelectorContainer_SzVML .ant-radio-group {
    display: flex
}

.selector-module_memberSelectorContainer_SzVML .selector-module_memberSelectorTips_vqgUs {
    position: absolute;
    bottom: -36px;
    left: 0;
    color: var(--yq-text-caption);
    font-size: 12px
}

.DocCollaboratorTip-module_contentText_3pLj- {
    color: var(--yq-yuque-grey-8);
    font-size: 14px;
    padding: 8px 20px 0 20px
}

.DocCollaboratorTip-module_contentCountWrapper_-NPtH {
    margin: 14px 0 4px
}

.DocCollaboratorTip-module_contentIcon_o95Dj {
    margin-right: 4px;
    color: var(--yq-text-body)
}

.DocCollaboratorTip-module_contentCount_dj5PU {
    color: var(--yq-text-body);
    font-size: 12px;
    position: relative;
    top: -2px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageAction_9Iy5y {
    color: var(--yq-text-link);
    cursor: pointer;
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz {
    margin-left: 16px
}

.message-module_memberMessageContainer_7bp54 .message-module_memberMessageClose_QyNkz .larkui-icon-close {
    color: var(--yq-text-body);
    top: 0;
    font-size: 14px;
    cursor: pointer
}

.message-module_memberMessageContainer_7bp54 .message-module_showMemberIntro_spXu1 {
    margin-left: 4px;
    margin-right: -8px
}

.message-module_orgPayBtn_DLLTR {
    margin-left: 20px
}

.groupintro-module_groupIntroContainer_XUWAC {
    padding: 80px 40px
}

.groupintro-module_groupIntroContainer_XUWAC .groupintro-module_groupIntroMainImage_wg4Nf img {
    width: 100%
}

.groupintro-module_groupIntroContainer_XUWAC .groupintro-module_groupIntroMore_4N4Va {
    color: var(--yq-text-link);
    margin-top: 40px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteContainer_1caED {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V {
    position: relative;
    top: 8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -8px;
    left: -8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteH5Container_ituBE {
    position: relative;
    left: 2px
}

.badgeIcon-module_container_xawzG {
    position: absolute;
    top: 12px;
    right: 12px;
    text-align: center;
    cursor: pointer
}

.badgeIcon-module_container_xawzG .badgeIcon-module_icon_qa6QH {
    display: block;
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.organizationUsage-module_orgUsageBoard_R68jU h3 {
    font-size: 14px
}

.organizationUsage-module_orgUsageBoard_R68jU a,.organizationUsage-module_orgUsageBoard_R68jU a:active,.organizationUsage-module_orgUsageBoard_R68jU a:focus,.organizationUsage-module_orgUsageBoard_R68jU a:hover {
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn {
    margin-top: 16px;
    width: 100%;
    padding: 16px 20px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.organizationUsage-module_usageBoard_\+jcHn.organizationUsage-module_homepageMode_8ax\+5 {
    margin-top: 24px;
    padding: 13px 20px;
    border-radius: 8px
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu {
    display: flex;
    align-items: center
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_informationIcon_Rp7jc {
    margin-left: 8px;
    transform: translateY(1px)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px;
    transform: translateY(1px);
    margin-right: 12px
}

html[data-kumuhana=pouli] .organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF {
    background: var(--yq-yuque-grey-4)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_icon_Ce2AF svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP {
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary);
    line-height: 18px;
    display: flex;
    align-items: center
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP .organizationUsage-module_iconInfo_cGcpv {
    margin-left: 8px
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_title_z39UP .organizationUsage-module_buyBtn_wyCzR {
    margin-left: 8px;
    color: var(--yq-blue-600);
    font-weight: 400
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 b {
    color: var(--yq-text-primary);
    font-size: 18px;
    line-height: 18px;
    font-weight: 500
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_header_QARUu .organizationUsage-module_content_NywO7 .organizationUsage-module_percent_0izvV {
    font-size: 12px;
    color: var(--yq-yuque-grey-700);
    line-height: 18px;
    font-weight: 400
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_tip_trHRY {
    margin-top: 16px;
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_reachedText_DPIJ7 {
    margin: 16px 0;
    display: flex;
    color: var(--yq-text-caption)
}

.organizationUsage-module_usageBoard_\+jcHn .organizationUsage-module_reachedText_DPIJ7 .ant-btn-link {
    padding: 0
}

.organizationUsage-module_content_NywO7 {
    font-size: 12px
}

.organizationUsage-module_content_NywO7 b {
    color: var(--yq-text-primary);
    font-size: 20px;
    font-weight: 500
}

.organizationUsage-module_usageBar_1vk-H {
    position: relative;
    margin: 22px auto 22px 0;
    width: 100%;
    height: 6px;
    border-radius: 2px;
    background: var(--yq-yuque-grey-4)
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: var(--yq-blue-4);
    border-radius: 12px 0 0 12px
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J .organizationUsage-module_vernier_WO3Ko {
    position: absolute;
    top: 50%;
    right: 0;
    padding: 3px 4px;
    min-width: 34px;
    height: 18px;
    color: var(--yq-text-primary);
    font-size: 12px;
    line-height: 12px;
    background: var(--yq-white);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12);
    border-radius: 4px;
    transform: translate(100%,-50%);
    text-align: center;
    font-weight: 500
}

html[data-kumuhana=pouli] .organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J .organizationUsage-module_vernier_WO3Ko {
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.organizationUsage-module_usageBar_1vk-H .organizationUsage-module_innerBar_jiX-J.organizationUsage-module_full_hWfHT .organizationUsage-module_vernier_WO3Ko {
    transform: translateY(-50%)
}

.UsagePercentShow-module_usageContainer_jKUGw {
    margin-bottom: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY {
    width: 100%;
    padding: 16px 16px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl {
    display: flex;
    align-items: center
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px
}

html[data-kumuhana=pouli] .UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw {
    background: var(--yq-bg-primary-hover)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_titleIconWrapper_-\+Wzw svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- {
    display: flex;
    align-items: center;
    color: var(--yq-text-primary)
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_title_qgGNo {
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_count_HLwuB {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDesc_p4Fzl .UsagePercentShow-module_content_b\+Gk- .UsagePercentShow-module_limit_RdfnJ {
    font-size: 14px;
    font-weight: 400
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s {
    width: 92%
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarItem_MxMUl {
    position: absolute;
    top: 0;
    height: 100%
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarItem_MxMUl:first-child {
    border-radius: 5px 0 0 5px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarCon_ny839 {
    background: var(--yq-yuque-grey-4);
    border-radius: 5px;
    height: 8px;
    position: relative;
    margin-top: 21px;
    margin-bottom: 5px
}

.UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarPercent_2o48U {
    text-align: right;
    position: absolute;
    top: -6px;
    color: var(--yq-black);
    background: var(--yq-white);
    height: 18px;
    font-size: 10px;
    padding: 0 4px 0 3px;
    border-radius: 5px;
    line-height: 18px;
    font-weight: 500;
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12)
}

html[data-kumuhana=pouli] .UsagePercentShow-module_usageContainer_jKUGw .UsagePercentShow-module_usageBox_AA0WY .UsagePercentShow-module_usageDetail_6nf3s .UsagePercentShow-module_usageBarPercent_2o48U {
    background: var(--yq-mask-bg);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.userUsage-module_userUsageContainer_Vg08f>.userUsage-module_title_tEvgb {
    display: flex;
    align-items: center;
    line-height: 22px;
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 16px;
    justify-content: space-between
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8 {
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8>span {
    margin-right: 3px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_rightsLink_NVrG8 svg {
    transform: translateY(1px)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageRow_5Ni-3 {
    display: flex;
    flex-wrap: wrap
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo {
    width: 100%;
    padding: 16px 16px;
    background: var(--yq-bg-secondary);
    border-radius: 12px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq {
    display: flex;
    align-items: center
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY {
    position: relative;
    width: 28px;
    height: 28px;
    background: var(--yq-bg-primary);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.04);
    border-radius: 6px
}

html[data-kumuhana=pouli] .userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY {
    background: var(--yq-bg-primary-hover)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_titleIconWrapper_Kt\+AY svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 {
    display: flex;
    align-items: center;
    color: var(--yq-text-primary)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_title_tEvgb {
    font-size: 14px;
    font-weight: 400;
    margin-left: 12px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_count_Nurz0 {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_limit_rEjzt {
    font-size: 14px;
    font-weight: 400
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDesc_jR\+jq .userUsage-module_content_I7Z11 .userUsage-module_exceedLimit_XKiwc {
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_exceedTip_91hDB {
    margin-top: 16px;
    color: var(--yq-yuque-grey-7)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y {
    width: 100%
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarItem_GU6oR {
    position: absolute;
    top: 0;
    height: 100%
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarItem_GU6oR:first-child {
    border-radius: 5px 0 0 5px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescItems_22Xa0 {
    margin-top: 10px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescItem_LMkQY {
    margin-right: 35px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarDescIcon_WjuBz {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 2px;
    margin-right: 8px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarCon_29GIy {
    background: var(--yq-yuque-grey-4);
    border-radius: 5px;
    height: 8px;
    position: relative;
    margin-top: 21px;
    margin-bottom: 5px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarPercent_mDaqi {
    text-align: right;
    position: absolute;
    top: -6px;
    color: var(--yq-black);
    background: var(--yq-white);
    height: 18px;
    font-size: 10px;
    padding: 0 4px 0 3px;
    border-radius: 5px;
    line-height: 18px;
    font-weight: 500;
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.12)
}

html[data-kumuhana=pouli] .userUsage-module_userUsageContainer_Vg08f .userUsage-module_userUsageBox_0afdo .userUsage-module_usageDetail_Y019y .userUsage-module_usageBarPercent_mDaqi {
    background: var(--yq-mask-bg);
    box-shadow: 0 1px 6px 0 rgba(0,0,0,.6)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_docAndNoteUsage_fPljT {
    margin-bottom: 16px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_docAndNoteUsage_fPljT .userUsage-module_titleIconWrapper_Kt\+AY {
    color: var(--yq-yuque-green-4)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_dataFlowUsage_6tbSW .userUsage-module_titleIconWrapper_Kt\+AY {
    color: var(--yq-blue-3)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW {
    margin-top: 16px;
    position: relative
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_titleIconWrapper_Kt\+AY {
    margin-right: 16px
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcode_pIXqT {
    cursor: pointer;
    display: flex;
    position: absolute;
    right: 16px;
    justify-content: center;
    align-items: center;
    color: var(--yq-text-body)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcode_pIXqT .userUsage-module_qrcodeDesc_8fKRX {
    margin-right: 4px;
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_titleIcon_WBzIt {
    color: var(--yq-orange-4)
}

.userUsage-module_userUsageContainer_Vg08f .userUsage-module_wechatGroup_9xMsW .userUsage-module_qrcodeIcon_8TtwM {
    color: var(--yq-yuque-grey-8)
}

.userUsage-module_qrcodeWrapper_yTpsS {
    padding: 8px 4px
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_qrcodeContent_gIsg6 {
    width: 212px;
    height: 212px;
    border-radius: 8px;
    border: 1px solid var(--yq-bg-primary-hover-light);
    display: flex;
    justify-content: center;
    align-items: center
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_qrcode_pIXqT {
    width: 180px;
    height: 180px
}

.userUsage-module_qrcodeWrapper_yTpsS .userUsage-module_desc_jviUQ {
    margin-top: 16px;
    color: var(--yq-text-primary);
    text-align: center
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card {
    margin: 24px 0 8px;
    background: var(--yq-bg-primary)!important
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-extra {
    padding: 10px 12px
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-extra .larkicon {
    cursor: pointer
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-body {
    border: none;
    padding: 0
}

.uploadExhaustReminder-module_UploadExhaustReminderCard_cBlO2.ant-card .ant-card-head {
    position: absolute;
    right: 0;
    top: 0;
    border: none;
    padding: 0
}

.uploadExhaustReminder-module_UploadExhaustReminderEnContainer_WNK2j .uploadExhaustReminder-module_UploadExhaustReminderUsageTitle_ytgI6 {
    min-height: 42px!important
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr {
    color: var(--yq-text-body);
    padding: 20px 20px 20px 66px;
    font-size: 12px;
    min-height: 144px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSubTitle_hv3if {
    margin-top: 8px;
    font-size: 14px;
    width: 520px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderIcon_Y2-xC {
    font-size: 26px;
    color: var(--yq-yellow-6);
    position: absolute;
    left: 21px;
    top: 21px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsage_85c0m {
    margin-top: 24px;
    width: 520px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageTitle_ytgI6 {
    font-size: 14px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageText_qkfd4 {
    margin-top: 8px;
    font-size: 24px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageSubTitle_yLn4D {
    margin-left: 4px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderUsageSubIcon_T0Dse {
    position: relative;
    top: 3px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderButtonCon_mHoxS {
    margin-top: 16px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderContactUs_YLq5Q {
    margin-left: 16px
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderImage_zteOX {
    position: absolute;
    right: 30px;
    bottom: 0;
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderImage_zteOX img {
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSmallImage_3HP6j {
    position: absolute;
    right: 40px;
    bottom: 0;
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderSmallImage_3HP6j img {
    height: 100%
}

.uploadExhaustReminder-module_UploadExhaustReminderContainer_XX4Gr .uploadExhaustReminder-module_UploadExhaustReminderOwners_N0DgD {
    margin: 0 8px
}

.order-detail-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.order-detail-modal .ant-modal-body {
    padding: 0
}

.order-detail-modal .ant-modal-body img {
    width: 100%
}

.order-detail-modal .ant-col {
    padding-top: 10px
}

.order-detail-modal .ant-col-18 {
    font-weight: 600
}

.index-module_successModalContainer_-aK4t {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfoCon_7ecQr {
    padding: 20px 40px 30px 40px
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfo_m-ZJc {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.index-module_successModalContainer_-aK4t .index-module_successModalInfoTitle_ByhnY {
    font-size: 28px;
    margin-top: 4px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY {
    margin-top: 12px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY .index-module_applyReceiptButton_MoqCc {
    margin-left: 12px
}

.index-module_successModalContainer_-aK4t .index-module_successModalButtonCon_ymzRY .index-module_checkUploadButton_1BWTN {
    margin-left: 12px;
    margin-top: 1px
}

.index-module_successModalContainer_-aK4t .index-module_successModalApplyReceiptButton_Zqpff {
    margin-left: 10px
}

.index-module_successModalContainer_-aK4t .index-module_successModalSubTitle_kd51r {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.index-module_successModalContainer_-aK4t .index-module_successModalItem_\+QbaR {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.index-module_successModalContainer_-aK4t .index-module_successModalBlank_KXceZ {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.index-module_successModalContainer_-aK4t .index-module_successModalDingQRCon_yUyWK {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.index-module_successModalContainer_-aK4t .index-module_successModalDingQRCon_yUyWK img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.orderReceipt-module_container_0\+bHT {
    padding-bottom: 36px
}

.orderReceipt-module_table_ulliM {
    margin-bottom: 24px
}

.orderReceipt-module_table_ulliM.orderReceipt-module_border_eZgzZ .ant-table-thead>tr>th {
    border-bottom: 0
}

.orderReceipt-module_table_ulliM.orderReceipt-module_border_eZgzZ .ant-table-tbody>tr>td {
    border: 1px solid var(--yq-border-light);
    border-radius: 8px
}

.orderReceipt-module_table_ulliM .ant-table-title {
    background-color: var(--yq-bg-primary)
}

.orderReceipt-module_table_ulliM .ant-table-pagination {
    margin-right: 16px
}

.orderReceipt-module_table_ulliM .ant-btn-link {
    color: var(--yq-text-link);
    padding: 0;
    height: auto
}

.orderReceipt-module_tableHeader_FQ\+zb {
    font-weight: 600
}

.orderReceipt-module_actions_tViEs,.orderReceipt-module_amount_tDmhL,.orderReceipt-module_content_wbMym,.orderReceipt-module_timer_WuSe8,.orderReceipt-module_user_TgHTF {
    color: var(--yq-text-caption)
}

@keyframes RegisterTrialModal-module_showIcon_8EOMg {
    0% {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
    }

    to {
        background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original)
    }
}

.RegisterTrialModal-module_modal_hmMaz .ant-modal-body {
    padding: 0
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--yq-icon-colorbg);
    cursor: pointer
}

.RegisterTrialModal-module_modal_hmMaz .RegisterTrialModal-module_close_7FK9d svg {
    position: absolute;
    top: 50%;
    left: 50%;
    height: 10px;
    fill: #fff;
    transform: translate(-50%,-50%);
    color: #fff
}

.RegisterTrialModal-module_wrapper_v2OGS {
    padding-bottom: 28px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*IosSQ5LRirYAAAAAAAAAAAAADvuFAQ/original)
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(0) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:first-child .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease .8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(2) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(3) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 1.6s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(4) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(5) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.4s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX:nth-child(6) .RegisterTrialModal-module_icon_RVUe4 {
    animation: RegisterTrialModal-module_showIcon_8EOMg .8s ease 2.8s forwards
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_trialSuccess_lO7Q5 .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    width: 188px;
    height: 323px;
    left: 38px;
    top: 0;
    padding-left: 40px;
    opacity: 0
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_tip_\+NtIs {
    width: 45px;
    height: 21px;
    font-size: 12px;
    font-weight: 500;
    color: var(--yq-text-primary);
    position: absolute;
    right: 0;
    top: 0;
    background: var(--yq-bg-primary-hover);
    border-radius: 0 4px 0 12px;
    text-align: center
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(78px);
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT {
    left: 242px
}

.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K,.RegisterTrialModal-module_wrapper_v2OGS.RegisterTrialModal-module_showTrialExpire_ZqY1v.RegisterTrialModal-module_ready_jlXB3 .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    transform: translateX(0);
    padding-left: 10px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_img_bAwkI {
    width: 480px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_highLight_AL6MB {
    color: var(--yq-theme)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: #fdf9e4;
    text-align: center;
    line-height: 32px;
    font-size: 20px;
    color: #e4495b;
    font-weight: 500;
    margin: 0 9px
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_title_CFs\+R .RegisterTrialModal-module_hour_2neFb {
    background: var(--yq-yellow-100)
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC {
    font-size: 20px;
    text-align: center;
    margin-top: 27px;
    margin-bottom: 24px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_successTitle_nMTcC .RegisterTrialModal-module_img_bAwkI {
    width: 22px;
    height: 22px;
    margin-right: 12px;
    margin-top: -2px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE {
    height: 323px;
    position: relative;
    top: 0
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_arrowImg_C5i8j {
    width: 107px;
    height: 55px;
    position: absolute;
    left: 87px;
    top: 170px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 {
    opacity: 1;
    position: absolute;
    left: 32px;
    top: 44px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 12px;
    width: 128px;
    height: 154px;
    padding-left: 28px;
    padding-top: 16px;
    transition: all 1s ease
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K {
    margin-top: 18px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_leftSide_dT5e9 .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    margin-top: 8px;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 20px;
    list-style: disc
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    position: absolute;
    left: 168px;
    right: 32px;
    top: 0;
    background-image: linear-gradient(125deg,#fdf9e4,#fff 57%,#fdf9e4);
    border: .5px solid #f5da80;
    border-radius: 12px;
    height: 323px;
    padding: 16px 20px 0;
    transition: all 1s ease
}

html[data-kumuhana=pouli] .RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT {
    background: rgba(245,210,97,.05);
    border: .5px solid #776118
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK {
    font-size: 16px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 500;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK span {
    line-height: 24px;
    height: 24px;
    display: inline-block
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_yuan_4C\+Ad {
    font-size: 16px;
    color: #c99b03;
    font-weight: 500;
    margin-left: 6px;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_price_f4gnN {
    font-size: 24px;
    color: #c99b03;
    font-weight: 500;
    position: relative;
    top: 1px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_subTitle_O7pYK .RegisterTrialModal-module_time_QNDxs {
    font-size: 14px;
    color: #c99b03;
    margin-left: 5px;
    font-weight: 400
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K {
    margin-top: 20px;
    padding-left: 32px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-body);
    line-height: 20px;
    margin-top: 12px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_icon_RVUe4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*sEkgQb2wqa8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 16px 16px;
    display: inline-block;
    margin-right: 12px;
    width: 16px;
    height: 16px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_list_LQq-K .RegisterTrialModal-module_item_vbrwX .RegisterTrialModal-module_bold_rwjpD {
    color: var(--yq-text-primary);
    font-weight: 600
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_button_UWEco {
    left: 20px;
    right: 20px;
    position: absolute;
    bottom: 20px
}

.RegisterTrialModal-module_wrapper_v2OGS .RegisterTrialModal-module_mainContent_e8QXE .RegisterTrialModal-module_rightSide_jXUrT .RegisterTrialModal-module_bg_xLr\+V {
    width: 69px;
    height: 154px;
    position: absolute;
    right: 0;
    top: 99px
}

.DocNoteLimitIncentiveModal-module_modal_aJrQn .ant-modal-content {
    overflow: hidden
}

.DocNoteLimitIncentiveModal-module_modal_aJrQn .ant-modal-body {
    padding: 0
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv {
    padding: 24px;
    overflow: hidden
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv h1 {
    font-size: 18px;
    margin-top: 6px;
    color: var(--yq-yuque-grey-9)
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv p {
    margin-top: 12px
}

.DocNoteLimitIncentiveModal-module_content_oO6Wv>p {
    color: var(--yq-yuque-grey-8)
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ {
    width: 434px;
    height: 326px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*G6NcQYnMre4AAAAAAAAAAAAAARQnAQ) no-repeat;
    background-size: cover;
    margin-top: 24px;
    border-radius: 8px;
    padding: 16px;
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardName_NaU7l {
    font-size: 20px;
    color: var(--yq-yuque-grey-9);
    font-weight: 700;
    display: flex;
    align-items: center
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardCoreRight_xCY6c {
    margin-top: 23px;
    color: #585a5a;
    font-weight: 700
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ .DocNoteLimitIncentiveModal-module_memberCardRight_ILEan {
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*eIylQZH5QBsAAAAAAAAAAAAAARQnAQ) no-repeat;
    padding-left: 20px;
    background-size: 13px 12px;
    background-position: 0
}

.DocNoteLimitIncentiveModal-module_memberCard_yo4QJ p {
    display: flex;
    align-items: center
}

.DocNoteLimitIncentiveModal-module_footer_r2nm- {
    margin-top: 24px
}

.DocNoteLimitIncentiveModal-module_footerAction_6SUEP {
    float: right
}

.DocNoteLimitIncentiveModal-module_actionBtn_3odCr {
    margin-left: 8px
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.CollectionModal-module_collectionModal_sJKOy .CollectionModal-module_collectionModalContent_6NiUd {
    padding-bottom: 6px
}

.SelectGroup-module_selectGroup_PPbXd {
    width: 282px;
    position: relative
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_selectGroupBoxShadow_\+mRm5 {
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW {
    padding: 14px 16px 0
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW .SelectGroup-module_p1_iKXf5 {
    font-size: 16px;
    color: var(--yq-text-primary);
    margin-bottom: 4px;
    font-weight: 500
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW .SelectGroup-module_p2_vn1NJ {
    font-size: 14px;
    color: var(--yq-text-caption)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd {
    min-height: 65px;
    max-height: 287px;
    overflow: auto;
    padding: 20px 16px 12px 16px;
    color: var(--yq-text-primary)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_empty_k0F6W {
    color: var(--yq-text-caption);
    text-align: center;
    line-height: 26px
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD {
    margin-bottom: 12px
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_checkboxWrapper_5aHCt {
    width: 100%
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_checkboxWrapper_5aHCt span:nth-child(2) {
    display: flex;
    min-width: 0
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD span {
    color: var(--yq-text-primary)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_nameText_exIya {
    color: var(--yq-text-primary);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_footer_09b\+K {
    border-top: 1px solid var(--yq-border-primary);
    height: 52px;
    line-height: 52px;
    font-size: 14px;
    color: var(--yq-text-body);
    padding-left: 16px;
    cursor: pointer
}

.index-module_btn_4WYB-.ant-btn {
    display: flex;
    align-items: center;
    background: none!important;
    min-width: 48px;
    text-align: center;
    height: 28px
}

.index-module_btn_4WYB-.ant-btn .index-module_icon_hhi0u {
    height: 14px;
    font-size: 14px
}

.index-module_btn_4WYB-.ant-btn.ant-btn-sm {
    height: 22px;
    line-height: 20px;
    font-size: 12px
}

.index-module_btn_4WYB-.ant-btn.ant-btn-sm .index-module_icon_hhi0u {
    height: 12px;
    font-size: 12px
}

.index-module_btn_4WYB-.ant-btn.ant-btn-sm .index-module_icon_hhi0u+span {
    margin-left: 6px
}

.index-module_btn_4WYB-.ant-btn.index-module_marked_dxNEz {
    border-color: var(--yq-border-primary);
    color: var(--yq-text-body)
}

.index-module_btn_4WYB-.ant-btn.index-module_marked_dxNEz:hover {
    border-color: var(--yq-yuque-grey-5);
    color: var(--yq-text-body)
}

.index-module_markedHover_PaCO8 {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    background-color: var(--yq-bg-primary-hover)
}

.index-module_markedNormal_lQdFH {
    width: 100%
}

.index-module_smallMark_FfS6d {
    border-color: var(--yq-yuque-grey-5);
    display: flex;
    align-items: center;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
    padding: 0 7px
}

.index-module_smallMark_FfS6d .index-module_icon_hhi0u {
    font-size: 12px;
    margin-right: 6px
}

.index-module_mark_3qbSl {
    border-color: var(--yq-yuque-grey-5);
    display: flex;
    align-items: center;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
    padding: 0 12px
}

.index-module_mark_3qbSl .index-module_icon_hhi0u {
    font-size: 12px;
    margin-right: 6px
}

.index-module_markIconContent_GN4G\+ {
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap
}

.index-module_isMiniTypeMark_Q9Bf6 {
    width: 32px;
    height: 32px;
    line-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.index-module_isMiniTypeMark_Q9Bf6 .index-module_icon_hhi0u {
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center
}

.select-group-mark-wrapper .index-module_iconMarked_vNmDS {
    color: var(--yq-yellow-6)
}

.App-module_wrapper_h-4rq .layout-container {
    padding-left: 36px;
    padding-right: 36px;
    max-width: 1664px;
    margin-left: 0
}

.App-module_settings_lN66e {
    padding-left: 36px;
    padding-right: 36px
}

.App-module_settings_lN66e .layout-container {
    padding-left: 0;
    padding-right: 0;
    max-width: 1080px;
    margin: 0 auto
}

.App-module_siteTip_x-bnl {
    position: fixed;
    bottom: 85px;
    right: 25px;
    z-index: 999
}
