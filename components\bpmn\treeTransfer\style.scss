.tree-transfer-container {
  display: flex !important;
  flex-direction: row !important;
  width: 100% !important;
  height: 400px !important;
  align-items: stretch !important;
  box-sizing: border-box !important;
  
  // 左右两侧容器共同样式
  .transfer-list {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    border: 1px solid #d9d9d9 !important;
    border-radius: 2px !important;
    overflow: hidden !important;
    height: 400px !important;
    max-height: 400px !important;
    
    // 标题区域
    .transfer-list-header {
      display: flex !important;
      flex-direction: column !important;
      padding: 8px 12px !important;
      background-color: #f5f5f5 !important;
      border-bottom: 1px solid #d9d9d9 !important;
      
      .header-title {
        font-weight: 500 !important;
        margin-bottom: 5px !important;
      }
      
      .header-search {
        input {
          width: 100% !important;
          padding: 4px 8px !important;
          border: 1px solid #d9d9d9 !important;
          border-radius: 2px !important;
          
          &:focus {
            outline: none !important;
            border-color: #1890ff !important;
          }
        }
      }
    }
    
    // 树容器
    .tree-wrapper {
      flex: 1 !important;
      overflow: auto !important;
      padding: 8px !important;
    }
  }
  
  // 穿梭操作按钮区域
  .transfer-operation {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    margin: 0 8px !important;
    width: 60px !important;
    flex-shrink: 0 !important;
    
    .operation-button {
      margin: 4px 0 !important;
      padding: 4px 12px !important;
      background-color: #00b96b !important;
      color:#fff;
      border-radius: 2px !important;
      cursor: pointer !important;
      transition: all 0.3s !important;
      white-space: nowrap !important;
      border-radius: 5px !important;
      
      // &:hover:not(:disabled) {
      //   color: #1890ff !important;
      //   border-color: #1890ff !important;
      // }
      
      &:disabled {
        color: rgba(0, 0, 0, 0.25) !important;
        background-color: #f5f5f5 !important;
        cursor: not-allowed !important;
      }
    }
  }
  
  // 左侧树容器
  .left-tree-container {
    margin-right: 0 !important;
  }
  
  // 右侧树容器
  .right-tree-container {
    margin-left: 0 !important;
  }
  // 树节点样式优化
  .ant-tree {
    .ant-tree-node-content-wrapper {
      flex: 1 !important;
      line-height: 24px !important;
    }
    
    .ant-tree-title {
      display: inline-block !important;
      width: 100% !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
    
    .ant-tree-checkbox {
      margin-right: 8px !important;
    }
    
    .ant-tree-treenode {
      padding: 2px 0 !important;
      
      &.ant-tree-treenode-disabled {
        .ant-tree-title {
          color: rgba(0, 0, 0, 0.25) !important;
        }
      }
    }
  } 
}
