<template>
    <div id="user_modify">
        <a-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleFormRef"
                label-align="right" :label-col="{ span: 4 }"
                :wrapper-col="{ span: 18 }"
        >
            <a-form-item label="登录ID">
                <a-input autocomplete="off" v-model:value="ruleForm.loginId" disabled :value="ruleForm.loginId" />
            </a-form-item>
            <a-form-item label="密码" prop="pwd" v-show="pass">
                <a-input-password v-model:value="ruleForm.pwd" />
            </a-form-item>
            <a-form-item label="确认密码" prop="repeatPwd" v-show="repass">
                <a-input-password v-model:value="ruleForm.repeatPwd" />
            </a-form-item>
            <a-form-item label="姓名" prop="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" />
            </a-form-item>
            <a-form-item label="业务条线" prop="business">
                <a-checkbox-group v-model:value="ruleForm.business" @change="checkboxGet" :filterOption="filterOption" showSearch>
                    <a-checkbox v-for="item in businessList" :key="item.uuid" :value="item.name" :name="item.name">{{ item.name }}</a-checkbox>
                </a-checkbox-group>
            </a-form-item>
            <a-form-item label="身份证号">
                <a-input autocomplete="off" v-model:value="ruleForm.idNumber" disabled />
            </a-form-item>
            <a-form-item label="性别">
                <a-select v-model:value="ruleForm.grade" placeholder="请选择">
                    <a-select-option v-for="item in options" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="电邮">
                <a-input autocomplete="off" v-model:value="ruleForm.email" />
            </a-form-item>
            <a-form-item label="电话">
                <a-input autocomplete="off" v-model:value="ruleForm.phone" />
            </a-form-item>
            <a-form-item label="移动电话">
                <a-input autocomplete="off" v-model:value="ruleForm.mobile" />
            </a-form-item>
            <a-form-item label="是否可用">
                <a-radio-group v-model:value="ruleForm.state">
                    <a-radio value="1">启用</a-radio>
                    <a-radio value="0">关闭</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
    import { getUserPwdKey } from "@/api/user";
    import {
        saveOrUpdate,
        getUserById,
        checkUserByLoginId,
        getAllBusinessByLoginUser,
        getUserBusinessIdsById,
        getLoginType
    } from "@/api/userManagement";

    const message = inject('message')
    const props = defineProps({
        id: {
            type: String,
            required: true,
        },
    });
    const ruleForm = reactive({
        id: "",
        loginId: "",
        pwd: "",
        pwdKey: "",
        repeatPwd: "",
        name: "",
        business: [] as string[],
        idNumber: "",
        grade: "M",
        email: "",
        mobile: "",
        phone: "",
        state: "1",
        isAdmin: "",
    });

    const options = [
        {
            value: "M",
            label: "男",
        },
        {
            value: "F",
            label: "女",
        },
    ];

    const rules = {
        pwd: [
            { required: true, message: "密码不能为空", trigger: "blur" },
            { pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,20}$/, message: '密码长度为8-20位,必须包含字母大写、小写字母、特殊符号和数字' }
        ],
        repeatPwd: [
            { required: true, message: "密码不能为空", trigger: "blur" },
            {
                validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                    if (value !== ruleForm.pwd) {
                        callback(new Error("两次密码不一致"));
                    } else {
                        callback();
                    }
                },
                trigger: "blur",
            },
        ],
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        business: [
            { required: true, message: "业务条线不能为空", trigger: "change" },
        ],
    };

    const businessList = ref([]);
    const businessChangeArr = ref([]);
    const pass = ref(true);
    const repass = ref(true);
    const ruleFormRef = ref();
    const defaultProps = {
        key: "id",
    };
    const getUserInfo = () => {
        getLoginType().then((res) => {
            if (res.data && res.data === '03') {
                pass.value = false;
                repass.value = false;
            } else {
                pass.value = true;
                repass.value = true;
            }
        });
    };

    const submitFun = (callback) => {
        ruleFormRef.value.validate().then(valid => {
            if (valid) {
                const prams = {
                    loginId: ruleForm.loginId,
                    password: ruleForm.pwd,
                    name: ruleForm.name,
                    businessLineIds: businessChangeArr.value.toString(),
                    idNumber: ruleForm.idNumber,
                    gender: ruleForm.grade,
                    email: ruleForm.email,
                    mobile: ruleForm.mobile,
                    phone: ruleForm.phone,
                    state: ruleForm.state,
                    id: ruleForm.id,
                    isAdmin: ruleForm.isAdmin,
                };
                saveOrUpdate(prams).then((res) => {
                    if (res.code ===20000){
                        message.success('修改成功')
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }else {
                        message.error(res.data)
                    }

                });
            }
        });
    };

    const checkboxGet = (name: string[]) => {
        businessChangeArr.value = [];
        name.forEach((n) => {
            const item = businessList.value.find((b) => b.name === n);
            if (item) {
                businessChangeArr.value.push(item.uuid);
            }
        });
    };

    onMounted(() => {
        getUserInfo();
        // 业务条线
        getAllBusinessByLoginUser().then((res) => {
            businessList.value = res.data;
            // 用户拥有条线
            if (props.id) {
                getUserBusinessIdsById({
                    id: props.id,
                }).then((res) => {
                    let businessId = res.data.split(",");
                    for (const i in businessId) {
                        businessList.value.filter((item, index) => {
                            if (businessId[i] === item.uuid) {
                                ruleForm.business.push(item.name);
                            }
                            return item.uuid === businessId[i];
                        });
                    }
                    checkboxGet(ruleForm.business);
                });
            }
            // 用户信息
            if (props.id) {
                getUserById({
                    id: props.id,
                }).then((res) => {
                    const data = res.data;
                    ruleForm.loginId = data.loginId;
                    ruleForm.pwd = data.password;
                    ruleForm.repeatPwd = data.password;
                    ruleForm.name = data.name;
                    ruleForm.idNumber = data.idNumber || "";
                    ruleForm.grade = data.gender;
                    ruleForm.email = data.email || "";
                    ruleForm.mobile = data.mobile || "";
                    ruleForm.phone = data.phone || "";
                    ruleForm.state = data.state || "";
                    ruleForm.id = data.id;
                    ruleForm.isAdmin = data.isAdmin;
                });
            }
        });
    });
    defineExpose({
        submitFun,
    });
</script>
