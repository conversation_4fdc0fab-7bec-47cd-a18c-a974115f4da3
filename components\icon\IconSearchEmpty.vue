<!-- 搜索空结果图标样式 -->

<script setup lang="ts">
    interface Props {
        size?: number
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 120
    })
</script>

<template>
    <svg :width="`${size}px`" :height="`${size}px`" style="color:var(--color-brand1-6, #1b9aee)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72.85 77.2"><g id="控件" fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g id="缺省/2" transform="translate(-95.15 -25)"><g id="主体/无内容" transform="translate(74)"><path id="矩形备份-11" d="M0 0H120V120H0z"></path><g id="编组-3" transform="translate(26 25)"><path id="矩形备份-22" fill="#DAE0E5" d="M34.8651395 36.8120489 68.3122385 36.8120489 68.3122385 69.6877615 41.8120489 69.6877615 34.8120489 62.6877615z" transform="rotate(-90 51.562 53.25)" data-spm-anchor-id="a2cl9.projex_devops2020_goldlog_workitemDetail_Bug.0.i120.39ef5c8fbokC4N"></path><path id="矩形备份-22" fill="#E9EDF0" d="M34.7920719.28569316 68.2391709.28569316 68.2391709 33.1614058 39.9052512 33.1614058 34.8451626 33.1614058z" transform="rotate(-90 51.516 16.724)"></path><path id="矩形备份-23" fill="#E9EDF0" d="M-.28569316.28569316 33.1614058.28569316 33.1614058 33.1614058 4.82748605 33.1614058-.232602526 33.1614058z" transform="rotate(-90 16.438 16.724)"></path><path id="矩形备份-24" fill="#E9EDF0" d="M-.28569316 36.8385942 33.1614058 36.8385942 33.1614058 69.7143068 4.82748605 69.7143068-.232602526 69.7143068z" transform="rotate(-90 16.438 53.276)"></path><path id="直线-4备份-7" stroke="#C6CDD4" stroke-linecap="square" stroke-width=".746" d="M7.133 7.167 25.742 7.167"></path><path id="直线-4备份-8" stroke="#C6CDD4" stroke-linecap="square" stroke-width=".746" d="M7.133 11.167 25.742 11.167"></path><path id="直线-4备份-9" stroke="#C6CDD4" stroke-linecap="square" stroke-width=".746" d="M7.133 15.167 25.742 15.167"></path><path id="路径-2备份-2" fill="#C6CDD4" d="M60.9793788 70 67.9793788 63 60.9793788 63z"></path></g><g id="辅助/图形1" fill-rule="nonzero" transform="translate(21.15 74.7)"><path id="形状" fill="currentColor" d="M11.137 3.438a7.699 7.699 0 0 1 6.134 12.351l5.932 5.886-1.517 1.528-5.945-5.897a7.667 7.667 0 0 1-4.604 1.526 7.699 7.699 0 1 1 0-15.395Zm0 2.153a5.545 5.545 0 1 0 0 11.088 5.545 5.545 0 1 0 0-11.088Z"></path></g></g></g></g></svg>
</template>
