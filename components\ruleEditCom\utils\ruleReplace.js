import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import {
  cloneDeep
} from "lodash";
const {
  ConditionGenerate,
  ActionGenerate
} = util;
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};
const c = `'color: green';'background: yellow'`
export const mxReplace = {
  data: () => {
    return {
      replaceIShow: false,
      replaceIErrInfo: [],
      replaceConId: "",
      replaceVariable: [],
      replaceComVariable: [],
      replaceType: '',
      replaceFlag: '',
      replacePos: '',
      preReplacePos: '',
      preReplaceFlag: '',
      tempVariable: [],
      tempRightTxt: '',
      tempRightMethod: [],
      tempsetMethod: [],
      tempConRightMethod: [],
      newContents: [],
      methodNewContents: [],
      childrenNum: 0,
      isStart: false,
      isIndex: 0,
      childrenSym: [],
      methodSymbol: []
    }
  },
  methods: {
    // 处理规则输入文本，支持自动识别条件和动作混合文本，可分别处理
    replaceItemSure(replaceTxt,callback) {
      
      // 预处理输入文本
      let processedText = replaceTxt;
      
      // 自动检测文本类型（当replaceType为空时）
      if (!this.replaceType || this.replaceType === '') {
        // 查找"条件【"的位置 - 可能不在开头，通过 (?<!\S) 负向后查找确保“条件”前只能是空白字符或字符串开头
        const conditionStartRegex = /(?<!\S)条件\s*【/u;
        const conditionMatch = processedText.match(conditionStartRegex);
        const conditionStartIndex = conditionMatch ? conditionMatch.index : -1;
        
        // 查找"执行动作"的位置
        const actionStartRegex = /执行动作/;
        const actionMatch = processedText.match(actionStartRegex);
        const actionStartIndex = actionMatch ? actionMatch.index : -1;
        
        // 判断文本类型
        const hasCondition = conditionStartIndex !== -1;
        const hasAction = actionStartIndex !== -1;
        
        // 检查是否同时包含条件和动作（且条件在前）
        const containsBothInOrder = hasCondition && hasAction && (conditionStartIndex < actionStartIndex);
        
        if (containsBothInOrder) {
          // 保存原来的类型
          const originalReplaceType = this.replaceType;
          
          // 从条件【开始截取条件部分到执行动作之前
          const conditionPart = processedText.substring(conditionStartIndex, actionStartIndex).trim();
          // 截取执行动作及之后的部分
          const actionPart = processedText.substring(actionStartIndex).trim();
          
          
          // 确保条件部分不为空且包含变量标记再处理
          if (conditionPart && /\【.*?\】/.test(conditionPart)) {
            // 设置类型为条件并处理
            this.replaceType = 'contion';
            this.replaceItemSure(conditionPart);
            
            // 设置类型为动作并处理
            setTimeout(() => {
              this.replaceType = 'action';
              this.aiWriterFlag = 'action'
              this.replaceItemSure(actionPart);
            }, 0);
            
            // 恢复原来的类型
            setTimeout(() => {
              this.replaceType = originalReplaceType;
              this.replaceIShow = false;
              callback && callback()
            }, 0);
            // 处理完后返回，避免重复处理
            return;
          }
        } else if (hasAction && !hasCondition) {
          // 只有动作
          this.replaceType = 'action'
          this.aiWriterFlag = 'action'
          // 从执行动作开始截取
          processedText = processedText.substring(actionStartIndex);
        } else if (hasCondition && !hasAction) {
          // 只有条件
          this.replaceType = 'contion'
          // 从条件【开始截取
          processedText = processedText.substring(conditionStartIndex);
        } else if (/\【.*?\】/.test(processedText)) {
          // 包含变量标记但没有明确标识，默认作为条件处理
          this.replaceType = 'contion'
        }
      }
      
      // 1. 移除"条件"前缀，但仅当它后面紧跟【变量标记时
      processedText = processedText.replace(/^条件\s*(?=\【)/i, '');
      if (this.replaceType === 'contion') {
        // 2. 处理没有换行的条件文本，在逻辑关系词前添加换行符
        const logicKeywords = ['并且', '或者', '并且非', '或者非'];
        
        // 先检测文本中是否包含【】变量标记，以及是否没有换行符但包含逻辑关系词
        const hasVariable = /\【.*?\】/.test(processedText);
        const hasNewline = /[\r\n]/.test(processedText);
        const containsLogicWord = logicKeywords.some(word => processedText.includes(word));
        
        // 只有当文本包含变量标记，且不包含换行符但包含逻辑关系词时才进行处理
        if (hasVariable && !hasNewline && containsLogicWord) {
          // 创建正则表达式匹配逻辑关系词（确保它们是单独的词，不是其他词的一部分）
          const logicPattern = new RegExp(`(${logicKeywords.join('|')})(?=\\s*\\【)`, 'g');
          
          // 在逻辑关系词前添加换行符
          processedText = processedText.replace(logicPattern, '\n$1');
        }
        
        const regex = /[\r\n]/g;
        let aReplaceTxt = processedText.split(regex);
        const aSymbol = [{
          txt: '并且',
          val: 'and'
        }, {
          txt: '或者',
          val: 'or'
        }, {
          txt: '并且非',
          val: 'unAnd'
        }, {
          txt: '或者非',
          val: 'unOr'
        }]

        this.newContents = []
        this.methodNewContents = []
        for (let i = 0; i < aReplaceTxt.length; i++) {
          const txt = aReplaceTxt[i].trim()
          let nextTxt = null
          if (aReplaceTxt.length > 1 && i < aReplaceTxt.length - 1) {
            nextTxt = aReplaceTxt[i + 1].trim()
          }
          aReplaceTxt[i] = {
            txt,
            sub: false,
            subStart: false
          }
          // if (i > 0) {
          let symbolT = txt.substring(0, 2)
          let symbolU = txt.substring(0, 3)
          let findVT = aSymbol.find(item => {
            return item.txt == symbolT
          })
          let findVU = aSymbol.find(item => {
            return item.txt == symbolU
          })
          if (findVT) {
            aReplaceTxt[i].symbol = findVT.val
          }
          if (findVU) {
            aReplaceTxt[i].symbol = findVU.val
          }
          if (this.isStart) {
            this.childrenNum += 1
            this.childrenSym.push(aReplaceTxt[i].symbol)
          }
          if (txt.substring(3, 4) === '(' || txt.substring(4, 5) === '(') {
            aReplaceTxt[i].sub = true
            aReplaceTxt[i].subStart = true
            this.isStart = true
            this.isIndex = i
            this.childrenNum = 1
            this.childrenSym.push(aReplaceTxt[i].symbol)
          }
          if (nextTxt) {
            if (nextTxt.substring(nextTxt.length - 1) === ')') {
              // aReplaceTxt[i].sub = true
              aReplaceTxt[this.isIndex].childNum = this.childrenNum + 1
              aReplaceTxt[this.isIndex].childrenSym = this.childrenSym

              this.isStart = false
            }
            if (nextTxt.substring(3, 4) === '(' || nextTxt.substring(4, 5) === '(') {
              aReplaceTxt[i].sub = true
              aReplaceTxt[this.isIndex].childNum = this.childrenNum + 1
              aReplaceTxt[this.isIndex].childrenSym = this.childrenSym

              this.isStart = false
            }
          }
          if (txt.substring(txt.length - 1) === ')') {
            aReplaceTxt[i].sub = true
            // aReplaceTxt[this.isIndex].childNum = this.childrenNum
            // aReplaceTxt[this.isIndex].childrenSym = this.childrenSym

            this.isStart = false
          }
        }
        let aReplaceTxtFilter = aReplaceTxt.filter(item=>item.txt.trim().length > 0)
        for (let i = 0; i < aReplaceTxtFilter.length; i++) {
          this.replaceVariable = [];
          this.replaceComVariable = [];
          this.tempVariable = []
          this.tempRightTxt = ''
          this.tempRightMethod = []
          this.tempsetMethod = []
          this.tempConRightMethod = []
          this.contionReplace(aReplaceTxtFilter[i], i, aReplaceTxtFilter)
        }
        this.isStart = false
        this.childrenNum = 0
      } else if (this.replaceType === 'action') {
        // 预处理输入文本，移除"执行动作"前缀，但仅当满足条件：前缀后面紧跟"设置"或"调用方法"，且这些关键词后面最终跟着【变量标记
        processedText = processedText.replace(/^执行动作\s*((设置|调用方法|设置调用方法)\s*\【)/i, '$1');
        
        const regex = /[\r\n\;]/g;
        const aReplaceTxt = processedText.split(regex);

        this.newContents = []
        this.methodNewContents = []
        let aReplaceTxtFilter = aReplaceTxt.filter(item=>item.trim().length > 0)
        for (let i = 0; i < aReplaceTxtFilter.length; i++) {
          this.replaceVariable = [];
          this.replaceComVariable = [];
          this.tempVariable = []
          this.tempRightTxt = ''
          this.tempRightMethod = []
                    this.tempsetMethod = []
          this.tempConRightMethod = []
          this.actionReplace(aReplaceTxtFilter[i], i, aReplaceTxtFilter.length)
        }
        this.preReplacePos = ''
        this.preReplaceFlag = ''
      }
      setTimeout(() => {
        this.replaceIShow = false;
        callback && callback()
      }, 0);
    },
    findValueForViewName(options = [], viewName) {
      let value = '';
      viewName && options && options.forEach(item => {
        if (item.viewName === viewName) {
          value = item.value;
        }
      });
      return value;
    },
    getRefValueType(valueType) {
      let refType = "String";
      switch (valueType) {
        case "String":
          refType = "String";
          break;
        case "Short":
        case "Integer":
        case "Float":
        case "Double":
        case "BigDecimal":
        case "Long":
          refType = "Number";
          break;
        case "Date":
          refType = "Date";
          break;
        case "Enum":
          refType = "Enum";
          break;
        default:
          refType = "String";
      }
      return refType;
    },
    getMethodVal(val, param, enumdicName, firstFieldItemList) {
      this.tempVariable = []
      if (typeof val === "string" || !isNaN(val)) {
        const fregex = /\【.*?\】/g;
        const aComReplaceTxt = val.match(fregex);
        let newVariable = {}
        if (aComReplaceTxt && aComReplaceTxt.length > 0) {
          // 变量
          for (let i = 0; i < aComReplaceTxt.length; i++) {
            let aReplaceLength = aComReplaceTxt[i].length;
            let objLabel = aComReplaceTxt[i].slice(1, aReplaceLength - 1);
            if (this.tempVariable.length > 0) {
              this.getReplaceVItem(
                [this.tempVariable[this.tempVariable.length - 1]],
                objLabel,
                this.tempVariable
              );
            } else {
              this.getReplaceVFirstItem(firstFieldItemList, objLabel,
                this.tempVariable);
            }
          }
          if (this.tempVariable.length > 0) {
            const {
              newVariableObj,
              finalValueType
            } = this.getNewVariableData(
              this.tempVariable
            );
            const isBool = finalValueType === "Boolean";
            if (isBool) {
              if (newVariableObj.next) {
                newVariableObj.next.domain = "boolean";
              }
            }
            newVariable = newVariableObj
          }
        } else {
          // 常量
          newVariable = {
            value: val,
            valueType: param.valueType,
            enumDictName: enumdicName || null,
            variableType: 'constant',
          };
          if (param.valueType === "Boolean") {
            newVariable.domain = "boolean";
          }
          if (this.getRefValueType(param.valueType) === "Number") {
            if (param.valueType && param.valueType !== "null") {
              newVariable.valueType = param.valueType;
            } else {
              if (val&&val.toString()&&val.toString().indexOf(".") !== -1) {
                newVariable.valueType = "Double";
              } else {
                newVariable.valueType = "Integer";
              }
            }
          }
        }
        return newVariable
      }
    },
    methodContentsRight(replaceTxt, filterLastLeftTxt, targetNode, isPreCon, currentData) {
      let comparatorObj = null;
      let aValue = []
      let comparatorStartIndex = replaceTxt.lastIndexOf(this.tempRightTxt)
      let comparatorStr = replaceTxt.slice(comparatorStartIndex)
      const regLabel = /\(.*?\)/g;
      const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false/g;
      let methodTxt = filterLastLeftTxt
      if (this.tempRightMethod.length > 0) {
        const {
          newVariableObj,
          finalValueType
        } = this.getNewVariableData(
          this.tempRightMethod,
          methodTxt
        );
        const isBool = finalValueType === "Boolean";
        const {
          dictName
        } = newVariableObj;
        const domain = util.getRealDomain(newVariableObj);
        if (isBool) {
          if (newVariableObj.next) {
            newVariableObj.next.domain = "boolean";
          }
        }
        return newVariableObj
      }
    },
    methodContents(replaceTxt, lastLeftIndex, targetNode, isPreCon, currentData, hasMeth, symbol) {
      let comparatorObj = null;
      let aValue = []
      let comparatorStartIndex = replaceTxt.lastIndexOf(this.tempRightTxt)
      let comparatorStr = replaceTxt.slice(comparatorStartIndex)
      const regLabel = /\(.*?\)/g;
      const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false/g;
      let methodTxt = replaceTxt.slice(lastLeftIndex, comparatorStartIndex)
      this.methodSymbol.push(symbol || 'and')
      if (this.replaceVariable.length > 0) {
        const {
          newVariableObj,
          finalValueType
        } = this.getNewVariableData(
          this.replaceVariable,
          methodTxt
        );
        const isBool = finalValueType === "Boolean";
        const {
          dictName
        } = newVariableObj;
        const domain = util.getRealDomain(newVariableObj);
        if (isBool) {
          if (newVariableObj.next) {
            newVariableObj.next.domain = "boolean";
          }
        }
        const newContents = {
          variable: newVariableObj,
          conditionValueType: finalValueType,
        };
        let comparatorData = null;
        if (isBool) {
          comparatorData = {
            operatorName: "",
            enumDictName: "boolean",
          };
        } else if (dictName || domain) {
          comparatorData = {
            operatorName: "",
            enumDictName: dictName || domain || null,
          };
        } else {
          comparatorData = {
            operatorName: "",
          };
        }
        newContents.comparator = comparatorData;

        const aComparatorVal = comparatorStr.match(regs);
        const operatorTypeList =
          store.getters.listMap[this.ruleUuid].initModelData.operatorTypeList;
        const comOperatorList = operatorTypeList[finalValueType];
        const dictMap =
          store.getters.listMap[this.ruleUuid].dictMap;
        const firstFieldItemList =
          store.getters.listMap[this.ruleUuid].initModelData.firstFieldItemList || [];
        let filterComparatorTxt = comparatorStr;
        const fregex = /\【.*?\】/g;
        const aComReplaceTxt = filterComparatorTxt.match(fregex) || [];
        let _operatorParam = null

        // filterComparatorTxt 不等于【帮助】将字符串"2"转为小数
        if (hasMeth) {
          filterComparatorTxt = this.tempRightTxt
        }
        if (aComparatorVal) {
          for (let j = 0; j < aComparatorVal.length; j++) {
            // 去掉item
            filterComparatorTxt = filterComparatorTxt.replace(
              aComparatorVal[j],
              ""
            );
          }

          // 过滤掉label里()的内容
          if (comOperatorList) {
            for (let i = 0; i < comOperatorList.length; i++) {
              let filterItem = comOperatorList[i].label;
              let aFilterOperaItem = filterItem.match(regLabel);
              // return
              if (aFilterOperaItem) {
                for (let j = 0; j < aFilterOperaItem.length; j++) {
                  // 去掉item
                  filterItem = filterItem.replace(aFilterOperaItem[j], "");
                }
              }
              if (filterComparatorTxt === filterItem) {
                comparatorObj = comOperatorList[i];
              }
            }
          }

          // get value
          if (aComparatorVal.length === 1) {
            if (aComparatorVal[0].indexOf("(") === 0) {
              let valueTxt = aComparatorVal[0].slice(
                1,
                aComparatorVal[0].length - 1
              );
              aValue = valueTxt.split(",");
            } else {
              aValue = aComparatorVal;
            }
          } else {
            aValue = aComparatorVal;
          }
          for (let j = 0; j < aValue.length; j++) {
            if (aValue[j].indexOf("(") === 0) {
              aValue[j] = aValue[j].slice(1);
            }
            if (aValue[j][aValue[j].length - 1] === ")") {
              aValue[j] = aValue[j].slice(0, aValue[j].length - 1);
            }
            if (
              (aValue[j].indexOf("'") === 0 &&
                aValue[j][aValue[j].length - 1] === "'") ||
              (aValue[j].indexOf('"') === 0 &&
                aValue[j][aValue[j].length - 1] === '"')
            ) {
              aValue[j] = aValue[j].slice(1, aValue[j].length - 1);
            }
          }
        } else {
          // comoperatorItem 无参数（）
          if (aComReplaceTxt.length === 0 && comOperatorList) {
            for (let i = 0; i < comOperatorList.length; i++) {
              let filterItem = comOperatorList[i].label;
              if (filterComparatorTxt === filterItem) {
                comparatorObj = comOperatorList[i];
              }
            }
          } else {
            // comoperatorItem 变量
            if (comOperatorList) {
              let filterT = filterComparatorTxt.slice(0, filterComparatorTxt.indexOf('【'))
              // 去 label()
              for (let i = 0; i < comOperatorList.length; i++) {
                let filterItem = comOperatorList[i].label;
                let aFilterOperaItem = filterItem.match(regLabel);
                if (aFilterOperaItem) {
                  for (let j = 0; j < aFilterOperaItem.length; j++) {
                    // 去 item
                    filterItem = filterItem.replace(aFilterOperaItem[j], "");
                  }
                }
                if (filterT === filterItem) {
                  comparatorObj = comOperatorList[i];
                }
              }
            }
            _operatorParam = this.getComVari(firstFieldItemList, aComReplaceTxt)
          }
        }
        // if (hasMeth) {
        //   let labelI = filterComparatorTxt.indexOf(aComReplaceTxt[0])
        //   let aReplaceLength = aComReplaceTxt[0].length;
        //   let objLabel = aComReplaceTxt[0].slice(1, aReplaceLength - 1);
        //   if (filterComparatorTxt.slice(aReplaceLength + 2, aReplaceLength + 3) !== '的') {
        //     // 方法 
        //     this.tempConRightMethod = []
        //     this.getReplaceVFirstItem(firstFieldItemList, objLabel,
        //       this.tempConRightMethod);
        //     filterComparatorTxt = filterComparatorTxt.slice(labelI + aReplaceLength)
        //     let _aComparatorVal = aComparatorVal.slice(1)
        //     // _operatorParam = this.actionMethod(_aComparatorVal, filterComparatorTxt, comparatorObj, aValueIndex, aValue, regs, firstFieldItemList)
        //   }
        // }
        if (comparatorObj) {
          const {
            label,
            paramTypes,
            paramQuantity,
            value
          } = comparatorObj;
          const num = label ? label.split("(").length - 1 : 0;
          const paramTypeArr = paramTypes ?
            paramTypes.split(",") : ["object"];
          const {
            enumDictName
          } = newContents.comparator;
          const enumdicName = enumDictName || null
          const newOperatorData = {
            enumDictName: enumDictName || null,
            value: [value],
            operatorParams: [],
            paramQuantity,
          };
          if (hasMeth) {
            if (aComReplaceTxt.length === 1) {
              // 方法
              let lastLeftLabel = aComReplaceTxt[0]
              let lastLableIndex = comparatorStr.indexOf(lastLeftLabel)
              let lastLeftLabelLength = lastLeftLabel.length
              let filterLastLeftTxt = comparatorStr.slice(
                lastLableIndex + lastLeftLabelLength
              );
              let filterLastLeftTxtBack = filterLastLeftTxt

              // let lastLeftLabel = aReplaceTxt[lastLeftIndex];
              // let lastLableIndex = replaceTxt.indexOf(lastLeftLabel);
              // let lastLeftLabelLength = lastLeftLabel.length;
              // let filterLastLeftTxt = replaceTxt.slice(
              //   lastLableIndex + lastLeftLabelLength
              // );

              const filterRegs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
              const aTempVal = filterLastLeftTxt.match(filterRegs);
              this.replaceComVariable = []

              if (aTempVal) {
                for (let j = 0; j < aTempVal.length; j++) {
                  // 去掉item
                  filterLastLeftTxt = filterLastLeftTxt.replace(
                    aTempVal[j],
                    ""
                  );
                }
              }
              let itemT = lastLeftLabel.substring(1, lastLeftLabel.length - 1)
              if (this.tempRightMethod.length === 0) {
                this.getReplaceVFirstItem(firstFieldItemList, itemT,
                  this.tempRightMethod);
              }
              if (this.tempRightMethod.length > 0) {
                this.getMethodItem(this.tempRightMethod[0].children, filterLastLeftTxt, this.tempRightMethod)
              }
              let methodObj = this.methodContentsRight(replaceTxt, filterLastLeftTxtBack)
              newOperatorData.operatorParams.push(methodObj);
            }
          } else if (_operatorParam) {
            newOperatorData.operatorParams.push(_operatorParam);
          } else {
            for (let i = 0; i < aValue.length; i++) {
              const newVairable = {
                variableType: "constant",
                enumDictName: enumDictName || null,
                value: this.findValueForViewName(dictMap[enumdicName], aValue[i]) || aValue[i] || "",
              };
              newVairable.valueType = paramTypeArr[i] ?
                paramTypeArr[i] :
                paramTypeArr[0];
              newOperatorData.operatorParams.push(newVairable);
            }
          }
          if (targetNode.ruleCondition.contents.comparator) {
            const _newOperatorData =
              targetNode.ruleCondition.contents.comparator._derive(
                newOperatorData
              );
            newContents.comparator = _newOperatorData;
          } else {
            newContents.comparator = newOperatorData;
          }
          let oldData = cloneDeep(targetNode);
          if (isPreCon) {
            this.onConditionChange(this.replacePos, newContents)
            this.resetReplaceVari()
          } else {
            this.newContents.push(newContents)
            for (let i = 0; i < this.newContents.length; i++) {
              const {
                conditionData,
                currentData
              } = this.getCurrentData();
              const {
                parentNode,
                targetNode,
                arrIndex
              } =
              util.findTargetNodeInfoById(conditionData, this.replaceConId + i);
              targetNode.ruleCondition.contents = new ConditionGenerate({
                variable: {
                  valueType: "String",
                  variableType: "dataEntry",
                },
                leftValueType: "String",
              })
              setTimeout(() => {
                if (targetNode.logicalSymbol && symbol) {
                  targetNode.logicalSymbol = this.methodSymbol[i]
                }
                targetNode.ruleCondition.contents = new ConditionGenerate(
                  this.newContents[i]
                );
                if (currentData) {
                  // this.updateRuleStore(
                  //   currentData,
                  //   Number(this.replaceConId),
                  //   "conditionData",
                  //   oldData,
                  //   "change"
                  // );
                }
                // this.resetReplaceVari()
              }, 0);
            }

          }
        }
      }
    },
    replaceItem(pos, conditionId, layer) {
      this.replaceConId = conditionId;
      this.replaceIShow = true;
      this.replaceType = 'contion'
      this.replacePos = pos
      pos && (this.preReplacePos = pos)
    },
    replaceActionItem(flag = "action", pos) {
      this.replaceIShow = true;
      this.replaceType = 'action'
      this.replaceFlag = flag
      this.replacePos = pos
      flag && (this.preReplaceFlag = flag)
      pos && (this.preReplacePos = pos)
    },
    replaceItemHide() {
      this.replaceConId = "";
      this.replaceIShow = false;
      this.replaceVariable = [];
      this.replaceComVariable = [];
      this.replaceType = ''
      this.replaceFlag = ''
      this.replacePos = ''
      this.replaceIErrInfo = []
      this.tempVariable = []
      this.tempRightTxt = ''
      this.tempRightMethod = []
      this.tempsetMethod = []
      this.tempConRightMethod = []
    },
    resetReplaceVari() {
      this.replaceItemHide()
    },
    // 条件
    contionReplace(oReplaceTxt, conI, aReplaceTxt) {
      const replaceTxt = oReplaceTxt.txt
      const isSubStart = oReplaceTxt.subStart
      const isSub = oReplaceTxt.sub
      const symbol = oReplaceTxt.symbol
      const childNum = oReplaceTxt.childNum
      const childrenSym = oReplaceTxt.childrenSym
      let globalTxt = false
      if(!this.replaceConId){
        this.replaceConId = 1
        globalTxt = true
      }
      let preISSubStart = ''
      let preIsSub = ''
      if (conI > 0) {
        preISSubStart = aReplaceTxt[conI - 1].subStart
        preIsSub = aReplaceTxt[conI - 1].sub
      }
      if (this.replaceConId && replaceTxt) {
        const isPreCon = this.isPreCondition()
        const {
          conditionData,
          currentData
        } = this.getCurrentData();
        globalTxt && conditionData?.children?.splice(1)
        let findData = util.findTargetNodeInfoById(conditionData, this.replaceConId + conI);
        let parentNode = undefined,
          grandparentNode = undefined,
          targetNode = undefined,
          arrIndex = undefined,
          layer = undefined;
        if (!findData) {
          findData = util.findTargetNodeInfoById(conditionData, this.replaceConId + conI - 1);
          if (findData && !isSubStart && !isSub) {

            parentNode = findData.parentNode
            grandparentNode = findData.grandparentNode
            targetNode = findData.targetNode
            arrIndex = findData.arrIndex
            layer = findData.layer
            let indent = targetNode.indent
            let arr = [];
            if (preIsSub) {
              arr = grandparentNode.children;
              indent = parentNode.indent
            } else {
              arr = parentNode.children;
            }
            // return
            util.updateLayers(layer, conditionData, 1);
            arr.splice(arrIndex + 1, 0, {
              indent: indent,
              logicalSymbol: symbol || "and",
              ruleCondition: {
                layer: layer + 1,
                showLayer: layer + 1,
                conditionId: Number(this.replaceConId) + Number(conI),
                contents: new ConditionGenerate(initConditionData),
              },
            });
          }
        } else {
          parentNode = findData.parentNode
          grandparentNode = findData.grandparentNode
          targetNode = findData.targetNode
          arrIndex = findData.arrIndex
          layer = findData.layer
        }
        if (isSubStart) {
          findData = util.findTargetNodeInfoById(conditionData, this.replaceConId + conI - 1);
          if (findData) {
            parentNode = findData.parentNode
            grandparentNode = findData.grandparentNode
            targetNode = findData.targetNode
            arrIndex = findData.arrIndex
            layer = findData.layer

            const arr = parentNode.children;
            util.updateLayers(layer, conditionData, 2);
            let childStr = []
            for (let i = 0; i < childNum; i++) {
              let oTemp = {}
              oTemp.indent = targetNode.indent + 1
              i > 0 && (oTemp.logicalSymbol = childrenSym[i] || 'and')
              oTemp.ruleCondition = {
                layer: layer + i + 1,
                showLayer: layer + i + 1,
                conditionId: Number(this.replaceConId )+ conI + i,
                contents: new ConditionGenerate(initConditionData),
              }
              if (i === childNum.length - 1) {
                this.replaceConId = this.replaceConId + conI + i
              }
              childStr.push(oTemp)
            }
            arr.splice(arrIndex + 1, 1, {
              indent: targetNode.indent,
              logicalSymbol: symbol || "and",
              fold: false,
              children: childStr,
            });
          }
        }

        const regex = /\【.*?\】/g;
        const aReplaceTxt = replaceTxt.match(regex);
        let aValue = [];
        if (aReplaceTxt) {
          const firstFieldItemList =
            store.getters.listMap[this.ruleUuid].initModelData.firstFieldItemList || [];
          const lastLeftIndex = this.getLeftIndex(replaceTxt, aReplaceTxt)
          for (let i = 0; i < lastLeftIndex + 1; i++) {
            // variable (的、加上、- 四则运算)
            // let replaceIndex = "";
            // let nextReplaceIndex  = "";
            // let joinIndex = "";
            // replaceIndex = replaceTxt.indexOf(aReplaceTxt[i]);
            // joinIndex = replaceIndex + aReplaceLength;
            let aReplaceLength = aReplaceTxt[i].length;
            let objLabel = aReplaceTxt[i].slice(1, aReplaceLength - 1);

            if (this.replaceVariable.length > 0) {
              this.getReplaceVItem(
                [this.replaceVariable[this.replaceVariable.length - 1]],
                objLabel,
                this.replaceVariable
              );
            } else {
              this.getReplaceVFirstItem(firstFieldItemList, objLabel,
                this.replaceVariable);
            }
          }
          if (this.replaceVariable.length > 0) {
            const {
              newVariableObj,
              finalValueType
            } = this.getNewVariableData(
              this.replaceVariable
            );
            const isBool = finalValueType === "Boolean";
            const {
              dictName
            } = newVariableObj;
            const domain = util.getRealDomain(newVariableObj);
            if (isBool) {
              if (newVariableObj.next) {
                newVariableObj.next.domain = "boolean";
              }
            }
            const newContents = {
              variable: newVariableObj,
              conditionValueType: finalValueType,
            };
            let comparatorData = null;
            if (isBool) {
              comparatorData = {
                operatorName: "",
                enumDictName: "boolean",
              };
            } else if (dictName || domain) {
              comparatorData = {
                operatorName: "",
                enumDictName: dictName || domain || null,
              };
            } else {
              comparatorData = {
                operatorName: "",
              };
            }
            newContents.comparator = comparatorData;
            // comparator
            let lastLabel = aReplaceTxt[lastLeftIndex];
            let lastLableIndex = replaceTxt.indexOf(lastLabel);
            let lastLabelLength = lastLabel.length;
            let comparatorTxt = replaceTxt.slice(
              lastLableIndex + lastLabelLength
            ).trim();
            let comparatorObj = null;

            const regLabel = /\(.*?\)/g;
            const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false/g;
            const regex = /\【.*?\】/g;
            const aFilterRightTxt = comparatorTxt.match(regex);
            const aComparatorVal = comparatorTxt.match(regs);
            const operatorTypeList =
              store.getters.listMap[this.ruleUuid].initModelData.operatorTypeList;
            const comOperatorList = operatorTypeList[finalValueType];
            const dictMap =
              store.getters.listMap[this.ruleUuid].dictMap;
            let filterComparatorTxt = comparatorTxt;
            const aComReplaceTxt = filterComparatorTxt.match(regex) || [];
            let _operatorParam = null
            let isMethod = []
            let rightLabel = '';
            let hasMethod = false;
            if (aFilterRightTxt) {
              // 方法
              for (let i = 0; i < aFilterRightTxt.length; i++) {
                // right is method
                let aItem = aFilterRightTxt[i]
                let aItemIndex = comparatorTxt.indexOf(aItem)
                let aItemLength = aItem.length
                let aItemTxtBefore = comparatorTxt.slice(aItemIndex - 2, aItemIndex)
                let aItemTxtAfter = comparatorTxt.slice(aItemIndex + aItemLength, aItemIndex + aItemLength + 2)
                if (aItemTxtBefore !== '】的' && aItemTxtAfter !== '的【') {
                  isMethod.push(true)
                } else {
                  isMethod.push(false)
                }
                let aItemTxt = comparatorTxt.slice(
                  aItemIndex + aItemLength
                );

                if (aItemTxt.substring(0, 2) !== '的【') {
                  rightLabel = comparatorTxt.slice(
                    0, aItemIndex
                  );
                }
                if (aFilterRightTxt.length === 1) {

                }
              }
            }
            if (isMethod.find(item => {
                return item === true
              })) {
              hasMethod = true
            }
            if (aComparatorVal) {
              for (let j = 0; j < aComparatorVal.length; j++) {
                // 去掉item
                filterComparatorTxt = filterComparatorTxt.replace(
                  aComparatorVal[j],
                  ""
                );
              }
              if (hasMethod) {
                filterComparatorTxt = rightLabel
              }
              // 过滤掉label里()的内容
              if (comOperatorList) {
                for (let i = 0; i < comOperatorList.length; i++) {
                  let filterItem = comOperatorList[i].label;
                  let aFilterOperaItem = filterItem.match(regLabel);
                  // return
                  if (aFilterOperaItem) {
                    for (let j = 0; j < aFilterOperaItem.length; j++) {
                      // 去掉item
                      filterItem = filterItem.replace(aFilterOperaItem[j], "");
                    }
                  }
                  if (filterComparatorTxt === filterItem) {
                    comparatorObj = comOperatorList[i];
                  }
                }
              }
              // get value
              if (aComparatorVal.length === 1) {
                if (aComparatorVal[0].indexOf("(") === 0) {
                  let valueTxt = aComparatorVal[0].slice(
                    1,
                    aComparatorVal[0].length - 1
                  );
                  aValue = valueTxt.split(",");
                } else {
                  aValue = aComparatorVal;
                }
              } else {
                aValue = aComparatorVal;
              }
              for (let j = 0; j < aValue.length; j++) {
                if (aValue[j].indexOf("(") === 0) {
                  aValue[j] = aValue[j].slice(1);
                }
                if (aValue[j][aValue[j].length - 1] === ")") {
                  aValue[j] = aValue[j].slice(0, aValue[j].length - 1);
                }
                if (
                  (aValue[j].indexOf("'") === 0 &&
                    aValue[j][aValue[j].length - 1] === "'") ||
                  (aValue[j].indexOf('"') === 0 &&
                    aValue[j][aValue[j].length - 1] === '"')
                ) {
                  aValue[j] = aValue[j].slice(1, aValue[j].length - 1);
                }
              }
            } else {
              // comoperatorItem 无参数（） 常量
              if (aComReplaceTxt.length === 0 && comOperatorList) {
                for (let i = 0; i < comOperatorList.length; i++) {
                  let filterItem = comOperatorList[i].label;
                  if (filterComparatorTxt === filterItem) {
                    comparatorObj = comOperatorList[i];
                  }
                }
              } else {
                // comoperatorItem 无参数（） 变量
                if (comOperatorList) {
                  let filterT = filterComparatorTxt.slice(0, filterComparatorTxt.indexOf('【'))
                  // 去 label()
                  for (let i = 0; i < comOperatorList.length; i++) {
                    let filterItem = comOperatorList[i].label;
                    let aFilterOperaItem = filterItem.match(regLabel);
                    if (aFilterOperaItem) {
                      for (let j = 0; j < aFilterOperaItem.length; j++) {
                        // 去 item
                        filterItem = filterItem.replace(aFilterOperaItem[j], "");
                      }
                    }
                    if (filterT === filterItem) {
                      comparatorObj = comOperatorList[i];
                    }
                  }
                }
                _operatorParam = this.getComVari(firstFieldItemList, aComReplaceTxt)
              }
            }
            // targetNode.ruleCondition.contents = newContents;

            if (comparatorObj) {
              // right 
              const {
                label,
                paramTypes,
                paramQuantity,
                value
              } = comparatorObj;
              const num = label ? label.split("(").length - 1 : 0;
              const paramTypeArr = paramTypes ?
                paramTypes.split(",") : ["object"];
              const {
                enumDictName
              } = newContents.comparator;
              const enumdicName = enumDictName || null
              const newOperatorData = {
                enumDictName: enumDictName || null,
                value: [value],
                operatorParams: [],
                paramQuantity,
              };
              if (hasMethod) {
                if (aFilterRightTxt.length === 1) {
                  // 方法
                  let lastLeftLabel = aFilterRightTxt[0]
                  let lastLableIndex = comparatorTxt.indexOf(lastLeftLabel)
                  let lastLeftLabelLength = lastLeftLabel.length
                  let filterLastLeftTxt = comparatorTxt.slice(
                    lastLableIndex + lastLeftLabelLength
                  );
                  let filterLastLeftTxtBack = filterLastLeftTxt

                  // let lastLeftLabel = aReplaceTxt[lastLeftIndex];
                  // let lastLableIndex = replaceTxt.indexOf(lastLeftLabel);
                  // let lastLeftLabelLength = lastLeftLabel.length;
                  // let filterLastLeftTxt = replaceTxt.slice(
                  //   lastLableIndex + lastLeftLabelLength
                  // );
                  const filterRegs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
                  const aTempVal = filterLastLeftTxt.match(filterRegs);
                  this.replaceComVariable = []
                  if (aTempVal) {
                    for (let j = 0; j < aTempVal.length; j++) {
                      // 去掉item
                      filterLastLeftTxt = filterLastLeftTxt.replace(
                        aTempVal[j],
                        ""
                      );
                    }
                  }
                  let itemT = lastLeftLabel.substring(1, lastLeftLabel.length - 1)
                  if (this.tempRightMethod.length === 0) {
                    this.getReplaceVFirstItem(firstFieldItemList, itemT,
                      this.tempRightMethod);
                  }
                  if (this.tempRightMethod.length > 0) {
                    this.getMethodItem(this.tempRightMethod[0].children, filterLastLeftTxt, this.tempRightMethod)
                  }
                  let methodObj = this.methodContentsRight(replaceTxt, filterLastLeftTxtBack)
                  newOperatorData.operatorParams.push(methodObj);
                }
              } else if (_operatorParam) {
                newOperatorData.operatorParams.push(_operatorParam);
              } else {
                for (let i = 0; i < aValue.length; i++) {
                  const newVairable = {
                    variableType: "constant",
                    enumDictName: enumDictName || null,
                    value: this.findValueForViewName(dictMap[enumdicName], aValue[i]) || aValue[i] || "",
                  };
                  newVairable.valueType = paramTypeArr[i] ?
                    paramTypeArr[i] :
                    paramTypeArr[0];
                  newOperatorData.operatorParams.push(newVairable);
                }
              }
              if (targetNode.ruleCondition.contents.comparator) {
                const _newOperatorData =
                  targetNode.ruleCondition.contents.comparator._derive(
                    newOperatorData
                  );
                newContents.comparator = _newOperatorData;
              } else {
                newContents.comparator = newOperatorData;
              }

              let oldData = cloneDeep(targetNode);
              if (isPreCon) {
                this.onConditionChange(this.replacePos, newContents)
                this.resetReplaceVari()
              } else {
                this.newContents.push(newContents)
                // return 
                for (let i = 0; i < this.newContents.length; i++) {
                  const {
                    conditionData,
                    currentData
                  } = this.getCurrentData();
                  const {
                    parentNode,
                    targetNode,
                    arrIndex
                  } =
                  util.findTargetNodeInfoById(conditionData, this.replaceConId + i);
                  targetNode.ruleCondition.contents = new ConditionGenerate({
                    variable: {
                      valueType: "String",
                      variableType: "dataEntry",
                    },
                    leftValueType: "String",
                  })
                  setTimeout(() => {
                    targetNode.ruleCondition.contents = new ConditionGenerate(
                      this.newContents[i]
                    );
                    if (currentData) {
                      // this.updateRuleStore(
                      //   currentData,
                      //   Number(this.replaceConId + i),
                      //   "conditionData",
                      //   oldData,
                      //   "change"
                      // );
                    }
                    // this.replaceIShow = false;
                    // this.resetReplaceVari()
                  }, 0);
                  // }
                }
              }
            } else {
              // 方法
              let lastLeftLabel = aReplaceTxt[lastLeftIndex];
              let lastLableIndex = replaceTxt.indexOf(lastLeftLabel);
              let lastLeftLabelLength = lastLeftLabel.length;
              let filterLastLeftTxt = replaceTxt.slice(
                lastLableIndex + lastLeftLabelLength
              );
              const filterRegs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
              const aTempVal = filterLastLeftTxt.match(filterRegs);
              const regex = /\【.*?\】/g;
              const aFilterRightTxt = filterLastLeftTxt.match(regex);
              this.replaceComVariable = []
              let isMeth = []
              let hasMeth = false
              if (aFilterRightTxt) {
                // 方法
                for (let i = 0; i < aFilterRightTxt.length; i++) {
                  // right is method
                  let aItem = aFilterRightTxt[i]
                  let aItemIndex = comparatorTxt.indexOf(aItem)
                  let aItemLength = aItem.length
                  let aItemTxtBefore = comparatorTxt.slice(aItemIndex - 2, aItemIndex)
                  let aItemTxtAfter = comparatorTxt.slice(aItemIndex + aItemLength, aItemIndex + aItemLength + 2)
                  if (aItemTxtBefore !== '】的' && aItemTxtAfter !== '的【') {
                    isMeth.push(true)
                  } else {
                    isMeth.push(false)
                  }
                  let aItemTxt = comparatorTxt.slice(
                    aItemIndex + aItemLength
                  );

                  if (aItemTxt.substring(0, 2) !== '的【') {
                    rightLabel = comparatorTxt.slice(
                      0, aItemIndex
                    );
                  }
                }
                if (isMethod.find(item => {
                    return item === true
                  })) {
                  hasMeth = true
                }
                if (hasMeth) {
                  filterLastLeftTxt = rightLabel
                }
              }
              if (aTempVal) {
                for (let j = 0; j < aTempVal.length; j++) {
                  // 去掉item
                  filterLastLeftTxt = filterLastLeftTxt.replace(
                    aTempVal[j],
                    ""
                  );
                }
              }
              if (this.replaceVariable.length > 0 && this.replaceVariable[lastLeftIndex]) {
                const methodTemp = this.replaceVariable[lastLeftIndex]
                const tempChild = methodTemp.children
                if (tempChild) {
                  this.getMethodItem(tempChild, filterLastLeftTxt, this.replaceVariable)
                  if (hasMeth) {

                  }
                  this.methodContents(replaceTxt, lastLableIndex + lastLabelLength, targetNode, isPreCon, currentData, hasMeth, symbol)
                }
              }
              // }
            }
          }
        } else {
          // 未匹配
        }
      }
    },
    // 动作
    actionReplace(replaceTxtStr, conI, aReplaceTxtFilterLength) {
      let replaceTxt = replaceTxtStr.trim()
      if (replaceTxt) {
        // 预处理输入文本，移除"执行动作"前缀，但仅当满足条件：前缀后面紧跟"设置"或"调用方法"，且这些关键词后面最终跟着【变量标记
        replaceTxt = replaceTxt.replace(/^执行动作\s*((设置|调用方法|设置调用方法)\s*\【)/i, '$1');
        
        // 特殊处理"设置调用方法【"的情况,将其转换为"调用方法【"
        replaceTxt = replaceTxt.replace(/^设置调用方法\s*\【/i, '调用方法【');
        
        let key = this.replaceFlag === "action" ? "actionData" : "elseActionData";
        if (!this.replacePos) {
          const posStr = this.preReplacePos
          if (posStr) {
            const [path, index] = posStr.split('_')
            this.replacePos = `${path}_${Number(index+1)}`
            this.preReplacePos = this.replacePos
          }
        }
        if (!this.replaceFlag) {
          const posStr = this.preReplaceFlag || this.aiWriterFlag
          posStr && (this.replaceFlag = posStr)
        }
        let globalTxt=false
        if(!this.replacePos){
          this.replacePos = `r_${conI}`
          globalTxt=true
        }
        let {
          actionItem,
          currentData,
          actionItemIndex,
          actionData,
          elseActionData
        } =
        this.findActionNodeByPosition(this.replacePos, this.replaceFlag);
        globalTxt && aReplaceTxtFilterLength && actionData.splice(aReplaceTxtFilterLength)
        
        this.updateIndex(this.editData.ruleData[key]);
        const regex = /\【.*?\】/g;
        const aReplaceTxt = replaceTxt.match(regex);
        let aValue = [];
        if (aReplaceTxt) {
          const firstFieldItemList =
            store.getters.listMap[this.ruleUuid].initModelData.firstFieldItemList || [];
          let selectopt = []
          if (replaceTxt.substring(0, 2) === '设置') {
            selectopt = [{
              label: '设置',
              value: 'setValue'
            }]
            if(!actionItem){
              actionItem = {
                actionId: conI,
                actionType: "setValue",
                actionParams: [{
                  variableType: "field",
                  valueType: "String",
                  value: [],
                  },
                  {
                    variableType: "constant",
                    valueType: "String",
                    value: "",
                  }
                ]
              }
            }
          } else if (replaceTxt.substring(0, 4) === '调用方法') {
            selectopt = [{
              label: '调用方法',
              value: 'invokeMethod'
            }]
            if(!actionItem){
              actionItem = {
                actionId: conI,
                actionType: "invokeMethod",
                actionParams: [{
                  variableType: "field",
                  valueType: "String",
                  value: [],
                }
                ]
              }
            }
          }
          if (selectopt.length > 0) {
            let newActionData;
            const {
              value
            } = selectopt[0];
            const initSetValueAction = {
              actionId: actionItem.actionId,
              actionType: "setValue",
              actionParams: [{
                  variableType: "field",
                  valueType: "String",
                  value: [],
                },
                {
                  variableType: "constant",
                  valueType: "String",
                  value: "",
                },
              ],
            };
            const initInvokeMethodActionData = {
              actionId: actionItem.actionId,
              actionType: "invokeMethod",
              actionParams: [{
                variableType: "field",
                valueType: "String",
                value: [],
              }, ],
            };
            if (value === "invokeMethod") {
              newActionData = initInvokeMethodActionData;
            }
            if (value === "setValue") {
              newActionData = initSetValueAction;
            }
            const lastLeftIndex = this.getLeftIndex(replaceTxt, aReplaceTxt)
            for (let i = 0; i < lastLeftIndex + 1; i++) {
              let aReplaceLength = aReplaceTxt[i].length;
              let objLabel = aReplaceTxt[i].slice(1, aReplaceLength - 1);
              if (this.replaceVariable.length > 0) {
                this.getReplaceVItem(
                  [this.replaceVariable[this.replaceVariable.length - 1]],
                  objLabel,
                  this.replaceVariable
                );
              } else {
                this.getReplaceVFirstItem(firstFieldItemList, objLabel,
                  this.replaceVariable);
              }
            }
            if (this.replaceVariable.length > 0) {
              // comparator
              let lastLabel = aReplaceTxt[lastLeftIndex];
              let lastLableIndex = replaceTxt.indexOf(lastLabel);
              let lastLabelLength = lastLabel.length;
              let comparatorTxt = replaceTxt.slice(
                lastLableIndex + lastLabelLength
              );

              /**
               * 去掉包裹参数的括号，以兼容：调用方法 【核保结果】向(this)添加核保结论("此规则名称","2","转人工"); 这种参数被括号包裹的情况
               */
              comparatorTxt = comparatorTxt.replaceAll('("', '"')
              comparatorTxt = comparatorTxt.replaceAll('")', '"')

              let comparatorObj = null;
              let aValueIndex = []
              const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
              const aComparatorVal = comparatorTxt.match(regs);
              const dictMap =
                store.getters.listMap[this.ruleUuid].dictMap;
              let filterComparatorTxt = comparatorTxt;
              const fregex = /\【.*?\】/g;
              const aComReplaceTxt = filterComparatorTxt.match(fregex);
              let _operatorParam = null
              if (value === "invokeMethod") {
                // 调用方法
                if (aComparatorVal) {
                  // 调用方法参数为常量
                  for (let j = 0; j < aComparatorVal.length; j++) {
                    // 去掉item过滤后的txt
                    filterComparatorTxt = filterComparatorTxt.replace(
                      aComparatorVal[j],
                      ""
                    );
                  }
                  // 过滤掉label里()的内容 firstFieldItemList
                  for (let i = 0; i < this.replaceVariable[0].children.length; i++) {
                    let filterItem = this.replaceVariable[0].children[i].label;

                    /**
                     * 兼容 "为(this)增加规则信息(#1,<此规则名称>
#2，<规则类型>)(#3，<提示信息>)(#4，<外部提示信息>) “ 这种带多余前后空格的情况
                     */
                    // 去掉前后空格
                    filterItem = filterItem.trim();

                    /**
                     * 兼容 "向(this)添加核保结论((#1,<此规则名称>),(#2,<规则描述>),(#3,<规则状态>))" 这种将参数放到括号中的情况
                     */
                    // 去掉双层括号
                    filterItem = filterItem.replace('((', '(');
                    filterItem = filterItem.replace('))', ')');

                    let aFilterOperaItem = filterItem.match(regs);
                    let _aValueIndex = []
                    if (aFilterOperaItem) {
                      for (let j = 0; j < aFilterOperaItem.length; j++) {
                        if (aFilterOperaItem[j].indexOf('#') !== -1) {
                          _aValueIndex.push(j)
                        }
                        // 去掉item
                        filterItem = filterItem.replace(aFilterOperaItem[j], "");
                      }
                    }
                    if (filterComparatorTxt === filterItem) {
                      aValueIndex = _aValueIndex
                      comparatorObj = this.replaceVariable[0].children[i];
                    }
                  }
                  // 找到opt comparatorObj
                  // 为(this)添加规则执行信息为(#1,<此规则名称>),(#2,<提示信息>),(#3,<保单号>),根据#填充变量值，排除(this)干扰项
                  // get value
                  if (aComparatorVal.length === 1) {
                    if (aComparatorVal[0].indexOf("(") === 0) {
                      let valueTxt = aComparatorVal[0].slice(
                        1,
                        aComparatorVal[0].length - 1
                      );
                      aValue = valueTxt.split(",");
                    } else {
                      aValue = aComparatorVal;
                    }
                  } else {
                    aValue = aComparatorVal;
                  }
                  for (let j = 0; j < aValueIndex.length; j++) {
                    if (aValue[aValueIndex[j]].indexOf("(") === 0) {
                      aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(1);
                    }
                    if (aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === ")") {
                      aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(0, aValue[aValueIndex[j]].length - 1);
                    }
                    if (
                      (aValue[aValueIndex[j]].indexOf("'") === 0 &&
                        aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === "'") ||
                      (aValue[aValueIndex[j]].indexOf('"') === 0 &&
                        aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === '"')
                    ) {
                      aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(1, aValue[aValueIndex[j]].length - 1);
                    }
                  }
                }
                if (comparatorObj) {
                  this.replaceVariable.push(comparatorObj)
                  const {
                    newVariableObj,
                    finalValueType
                  } = this.getNewVariableData(
                    this.replaceVariable
                  );
                  // const {
                  //   dictName
                  // } = newVariableObj;
                  // const domain = util.getRealDomain(newVariableObj);
                  // const enumdicName = domain || dictName || null
                  const next = newVariableObj.next
                  const {
                    methodParams,
                    paramsList
                  } = next;
                  for (let i = 0; i < aValueIndex.length; i++) {
                    let _value = ''
                    if (paramsList[i].domain) {
                      _value = this.findValueForViewName(dictMap[paramsList[i].domain], aValue[aValueIndex[i]]) || aValue[aValueIndex[i]] || ""
                    } else {
                      _value = aValue[aValueIndex[i]] || ""
                    }
                    let newChildVariableData = this.getMethodVal(_value, paramsList[i], paramsList[i].domain || null, firstFieldItemList)
                    methodParams[i] = newChildVariableData;
                  }
                  // const valueType = util.getRealValueType(next);
                  newActionData.actionParams = [newVariableObj]
                  this.newContents.push(newActionData)
                  newActionData = new ActionGenerate(newActionData);
                  const oldData = cloneDeep(actionItem);
                  if (this.replaceFlag === "action") {
                    actionData[actionItemIndex] = newActionData;
                  } else if (this.replaceFlag === "elseAction") {
                    elseActionData[actionItemIndex] = newActionData;
                  }
                  if (currentData) {
                    this.updateRuleStore(currentData, actionItemIndex, key, oldData, "change");
                  }
                }
              } else {
                // 设置
                if (aComReplaceTxt && aComReplaceTxt.length > 0) {
                  let labelI = filterComparatorTxt.indexOf(aComReplaceTxt[0])
                  let aReplaceLength = aComReplaceTxt[0].length;
                  let objLabel = aComReplaceTxt[0].slice(1, aReplaceLength - 1);
                  if (filterComparatorTxt.slice(aReplaceLength + 2, aReplaceLength + 3) !== '的') {
                    // 方法 
                    this.tempsetMethod = []
                    this.getReplaceVFirstItem(firstFieldItemList, objLabel,
                      this.tempsetMethod);
                    filterComparatorTxt = filterComparatorTxt.slice(labelI + aReplaceLength)
                    let _aComparatorVal = aComparatorVal.slice(1)
                    _operatorParam = this.actionMethod(_aComparatorVal, filterComparatorTxt, comparatorObj, aValueIndex, aValue, regs, firstFieldItemList)
                  } else {
                    // comoperatorItem 变量
                    _operatorParam = this.getComVari(firstFieldItemList, aComReplaceTxt)
                  }
                } else if (aComparatorVal) {
                  // get value 常量
                  if (aComparatorVal.length === 1) {
                    if (aComparatorVal[0].indexOf("(") === 0) {
                      let valueTxt = aComparatorVal[0].slice(
                        1,
                        aComparatorVal[0].length - 1
                      );
                      aValue = valueTxt.split(",");
                    } else {
                      aValue = aComparatorVal;
                    }
                  } else {
                    aValue = aComparatorVal;
                  }
                  for (let j = 0; j < aValue.length; j++) {
                    if (aValue[j].indexOf("(") === 0) {
                      aValue[j] = aValue[j].slice(1);
                    }
                    if (aValue[j][aValue[j].length - 1] === ")") {
                      aValue[j] = aValue[j].slice(0, aValue[j].length - 1);
                    }
                    if (
                      (aValue[j].indexOf("'") === 0 &&
                        aValue[j][aValue[j].length - 1] === "'") ||
                      (aValue[j].indexOf('"') === 0 &&
                        aValue[j][aValue[j].length - 1] === '"')
                    ) {
                      aValue[j] = aValue[j].slice(1, aValue[j].length - 1);
                    }
                  }
                }
                const {
                  newVariableObj,
                  finalValueType
                } = this.getNewVariableData(
                  this.replaceVariable
                );
                const {
                  dictName
                } = newVariableObj;
                const domain = util.getRealDomain(newVariableObj);
                const enumdicName = domain || dictName || null
                let _rightVariable = {}
                if (_operatorParam) {
                  _rightVariable = _operatorParam
                } else {
                  _rightVariable = {
                    value: this.findValueForViewName(dictMap[enumdicName], aValue[0]) || aValue[0] || "",
                    valueType: finalValueType,
                    variableType: "constant",
                    enumDictName: enumdicName
                  };
                }
                newActionData.actionParams = [newVariableObj, _rightVariable]
                this.newContents.push(newActionData)

                newActionData = new ActionGenerate(newActionData);
                const oldData = cloneDeep(actionItem);
                if (this.replaceFlag === "action") {
                  actionData[actionItemIndex] = newActionData;
                } else if (this.replaceFlag === "elseAction") {
                  elseActionData[actionItemIndex] = newActionData;
                }
                if (currentData) {
                  this.updateRuleStore(currentData, actionItemIndex, key, oldData, "change");
                }
              }

              // for (let i = 0; i < this.newContents.length; i++) {
              //   setTimeout(() => {
              //     // const oldData = cloneDeep(actionItem);
              //     if (this.replaceFlag === "action") {
              //       actionData[actionItemIndex] = new ActionGenerate(this.newContents[i]);
              //     } else if (this.replaceFlag === "elseAction") {
              //       elseActionData[actionItemIndex] = new ActionGenerate(this.newContents[i]);
              //     }
              //     // if (currentData) {
              //     //   this.updateRuleStore(currentData, actionItemIndex, key, oldData, "change");
              //     // }
              //   }, 0)
              // }
            }
          }
        } else {
          // 未匹配
        }
      }
      this.resetReplaceVari()
    },
    // actionMethon
    actionMethod(aComparatorVal, filterComparatorTxt, comparatorObj, aValueIndex, aValue, regs, firstFieldItemList) {
      const dictMap =
        store.getters.listMap[this.ruleUuid].dictMap;
      // 方法
      if (aComparatorVal) {
        // 调用方法参数为常量
        for (let j = 0; j < aComparatorVal.length; j++) {
          // 去掉item过滤后的txt
          filterComparatorTxt = filterComparatorTxt.replace(
            aComparatorVal[j],
            ""
          );
        }
        // 过滤掉label里()的内容 firstFieldItemList
        for (let i = 0; i < this.tempsetMethod[0].children.length; i++) {
          let filterItem = this.tempsetMethod[0].children[i].label;
          let aFilterOperaItem = filterItem.match(regs);
          let _aValueIndex = []
          if (aFilterOperaItem) {
            for (let j = 0; j < aFilterOperaItem.length; j++) {
              if (aFilterOperaItem[j].indexOf('#') !== -1) {
                _aValueIndex.push(j)
              }
              // 去掉item
              filterItem = filterItem.replace(aFilterOperaItem[j], "");
            }
          }
          if (filterComparatorTxt === filterItem) {
            aValueIndex = _aValueIndex
            comparatorObj = this.tempsetMethod[0].children[i];
          }
        }
        // 找到opt comparatorObj
        // 为(this)添加规则执行信息为(#1,<此规则名称>),(#2,<提示信息>),(#3,<保单号>),根据#填充变量值，排除(this)干扰项
        // get value
        if (aComparatorVal.length === 1) {
          if (aComparatorVal[0].indexOf("(") === 0) {
            let valueTxt = aComparatorVal[0].slice(
              1,
              aComparatorVal[0].length - 1
            );
            aValue = valueTxt.split(",");
          } else {
            aValue = aComparatorVal;
          }
        } else {
          aValue = aComparatorVal;
        }
        for (let j = 0; j < aValueIndex.length; j++) {
          if (aValue[aValueIndex[j]].indexOf("(") === 0) {
            aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(1);
          }
          if (aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === ")") {
            aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(0, aValue[aValueIndex[j]].length - 1);
          }
          if (
            (aValue[aValueIndex[j]].indexOf("'") === 0 &&
              aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === "'") ||
            (aValue[aValueIndex[j]].indexOf('"') === 0 &&
              aValue[aValueIndex[j]][aValue[aValueIndex[j]].length - 1] === '"')
          ) {
            aValue[aValueIndex[j]] = aValue[aValueIndex[j]].slice(1, aValue[aValueIndex[j]].length - 1);
          }
        }
      }
      if (comparatorObj) {
        this.tempsetMethod.push(comparatorObj)
        const {
          newVariableObj,
          finalValueType
        } = this.getNewVariableData(
          this.tempsetMethod
        );
        // const {
        //   dictName
        // } = newVariableObj;
        // const domain = util.getRealDomain(newVariableObj);
        // const enumdicName = domain || dictName || null
        const next = newVariableObj.next
        const {
          methodParams,
          paramsList
        } = next;
        for (let i = 0; i < aValueIndex.length; i++) {
          let _value = ''
          if (paramsList[i].domain) {
            _value = this.findValueForViewName(dictMap[paramsList[i].domain], aValue[aValueIndex[i]]) || aValue[aValueIndex[i]] || ""
          } else {
            _value = aValue[aValueIndex[i]] || ""
          }
          let newChildVariableData = this.getMethodVal(_value, paramsList[i], paramsList[i].domain || null, firstFieldItemList)
          methodParams[i] = newChildVariableData;
        }
        // const valueType = util.getRealValueType(next);
        return newVariableObj
        // newActionData.actionParams = [newVariableObj]
        // newActionData = new ActionGenerate(newActionData);
        // const oldData = cloneDeep(actionItem);
        // if (this.replaceFlag === "action") {
        //   actionData[actionItemIndex] = newActionData;
        // } else if (this.replaceFlag === "elseAction") {
        //   elseActionData[actionItemIndex] = newActionData;
        // }
        // if (currentData) {
        //   this.updateRuleStore(currentData, actionItemIndex, key, oldData, "change");
        // }
      }
    },
    // 变量
    getComVari(firstFieldItemList, aComReplaceTxt) {
          for (let i = 0; i < aComReplaceTxt.length; i++) {
            let aReplaceLength = aComReplaceTxt[i].length;
            let objLabel = aComReplaceTxt[i].slice(1, aReplaceLength - 1);

        if (this.replaceComVariable.length > 0) {
              this.getReplaceVItem(
            [this.replaceComVariable[this.replaceComVariable.length - 1]],
                objLabel,
            this.replaceComVariable
              );
            } else {
              this.getReplaceVFirstItem(firstFieldItemList, objLabel,
            this.replaceComVariable);
            }
          }
      if (this.replaceComVariable.length > 0) {
            const {
              newVariableObj,
              finalValueType
            } = this.getNewVariableData(
          this.replaceComVariable
            );
            const isBool = finalValueType === "Boolean";
        const domain = util.getRealDomain(newVariableObj);
            if (isBool) {
              if (newVariableObj.next) {
                newVariableObj.next.domain = "boolean";
              }
            }
        return newVariableObj
      }
    },
    getLeftIndex(replaceTxt, aReplaceTxt) {
      for (let i = 0; i < aReplaceTxt.length; i++) {
        let aReplaceLength = aReplaceTxt[i].length;
        let replaceIndex = "";
        let nextReplaceIndex = "";
        let joinIndex = "";
        replaceIndex = replaceTxt.indexOf(aReplaceTxt[i]);
        joinIndex = replaceIndex + aReplaceLength;
        if (replaceTxt.slice(joinIndex, joinIndex + 2) !== '的【') {
          return i
        }
      }
    },
    getReplaceVFirstItem(itemList, objLabel, obj) {
      if (itemList.length > 0) {
        for (let i = 0; i < itemList.length; i++) {
          if (itemList[i].label === objLabel) {
            obj.push(itemList[i]);
            return;
          }
        }
      }
    },
    getReplaceVItem(itemList, objLabel, obj) {
      if (itemList.length > 0) {
        for (let i = 0; i < itemList.length; i++) {
          if (itemList[i].label === objLabel) {
            obj.push(itemList[i]);
            return;
          } else if (itemList[i].children) {
            this.getReplaceVItem(itemList[i].children, objLabel, obj);
          }
        }
      }
    },
    getMethodItem(itemList, objLabel, obj) {
      if (itemList.length > 0) {
        const filterRegs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
        for (let i = 0; i < itemList.length; i++) {
          let filterItem = itemList[i].label;
          let aFilterOperaItem = filterItem.match(filterRegs);
          if (aFilterOperaItem) {
            for (let j = 0; j < aFilterOperaItem.length; j++) {
              // 去掉item
              filterItem = filterItem.replace(aFilterOperaItem[j], "");
            }
          }
          if (filterItem === objLabel.slice(0, filterItem.length)) {
            this.tempRightTxt = objLabel.slice(filterItem.length)
            obj.push(itemList[i]);
            return;
          } else if (itemList[i].children) {
            this.getReplaceVItem(itemList[i].children, objLabel, obj);
          }
        }
      }
    },
    getVariableData(option, nextVariableData, methodTxt) {
      const firstFieldItemList =
        store.getters.listMap[this.ruleUuid].initModelData.firstFieldItemList || [];
      const {
        value,
        valueType,
        paramsList,
        isDatasetItem,
        isMethodItem,
        dictName,
        label,
        // domain,
        display,
      } = option;
      let {
        domain
      } = option;
      const dictMap =
        store.getters.listMap[this.ruleUuid].dictMap;
      if (valueType === "Boolean") {
        domain = "boolean";
      }
      const newValueObj = {};

      if (isMethodItem) {
        let aValue = []
        let aValueIndex = []
        let _aValueIndex = []
        if (methodTxt) {
          const regs = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|\d+\.\d+|\d+|true|false|【[^】]+】(的【[^】]+】)*/g;
          let aFilterOperaItem = label.match(regs);
          let aFilterMethodTxt = methodTxt.match(regs)
          let filterComparatorTxt = methodTxt
          if (aFilterOperaItem) {
            let filterItem = label
            if (aFilterOperaItem.length === aFilterMethodTxt.length) {
              aFilterOperaItem = label.match(regs);
            } else {
              // 取金额113和金额245中较大的数
              // 取金额1和金额2中较大的数
              const tempItemNum = aFilterMethodTxt.map((item) => {
                if (isNaN(item)) {
                  return false
                } else {
                  return true
                }
              })
              // number  
              if (!tempItemNum.find(item => {
                  return item === false
                })) {
                const nanRes = /\(.*?\)|\(.*?\"|\".*?\)|\".*?\"|true|false|【[^】]+】(的【[^】]+】)*/g;
                const numRes = /\d+\.\d+|\d+/g;
                aFilterOperaItem = label.match(nanRes);
                let aPosition = []
                for (let i = 0; i < aFilterOperaItem.length; i++) {
                  let itemI = label.indexOf(aFilterOperaItem[i])
                  let beforeItem = label.slice(0, itemI)
                  let afterItem = label.slice(itemI + aFilterOperaItem[i].length, itemI + aFilterOperaItem[i].length + 1)
                  let afterItemNum = afterItem.match(numRes) && afterItem.match(numRes)[0]
                  let beforeItemNum = beforeItem.match(numRes) && beforeItem.match(numRes)[0]
                  if (afterItemNum && !isNaN(afterItemNum) && beforeItemNum && !isNaN(beforeItemNum)) {
                    aPosition[i] = {
                      position: 'm',
                      length: [beforeItemNum.length, afterItemNum.length]
                    }
                  } else if (afterItemNum && !isNaN(afterItemNum)) {
                    aPosition[i] = {
                      position: 'a',
                      length: [afterItemNum.length]
                    }
                  } else if (beforeItemNum && !isNaN(beforeItemNum)) {
                    aPosition[i] = {
                      position: 'b',
                      length: [beforeItemNum.length]
                    }
                  }
                }
                for (let i = 0; i < aFilterMethodTxt.length; i++) {
                  const {
                    position,
                    length
                  } = aPosition[i]
                  if (position === 'b') {
                    aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(length[0])
                  } else if (position === 'a') {
                    aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(0, aFilterMethodTxt[i].length - length[0])
                  } else if (position === 'm') {
                    aFilterMethodTxt[i] = aFilterMethodTxt[i].slice(length[0]) + aFilterMethodTxt[i].slice(0, aFilterMethodTxt[i].length - length[1])
                  }
                }
              }
            }
            if (aFilterOperaItem) {
              let tempT = filterItem
              for (let j = 0; j < aFilterOperaItem.length; j++) {
                // 去掉item过滤后的txt
                tempT = tempT.replace(
                  aFilterOperaItem[j],
                  ""
                );
              }
              filterComparatorTxt = tempT
            }
            // if (aFilterMethodTxt) {
            //   for (let j = 0; j < aFilterMethodTxt.length; j++) {
            //     // 去掉item过滤后的txt
            //     filterComparatorTxt = filterComparatorTxt.replace(
            //       aFilterMethodTxt[j],
            //       ""
            //     );
            //   }
            // }
            for (let j = 0; j < aFilterOperaItem.length; j++) {
              if (aFilterOperaItem[j].indexOf('#') !== -1) {
                _aValueIndex.push(j)
              }
              // 去掉item
              filterItem = filterItem.replace(aFilterOperaItem[j], "");
              if (filterComparatorTxt === filterItem) {
                aValueIndex = _aValueIndex
              }
            }
          }
          // get value
          if(aFilterMethodTxt){
            if (aFilterMethodTxt.length === 1) {
              if (aFilterMethodTxt[0].indexOf("(") === 0) {
                let valueTxt = aFilterMethodTxt[0].slice(
                  1,
                  aFilterMethodTxt[0].length - 1
                );
                aValue = valueTxt.split(",");
              } else {
                aValue = aFilterMethodTxt;
              }
            } else {
              aValue = aFilterMethodTxt;
            }
          }
          for (let j = 0; j < aValue.length; j++) {
            if (aValue[j].indexOf("(") === 0) {
              aValue[j] = aValue[j].slice(1);
            }
            if (aValue[j][aValue[j].length - 1] === ")") {
              aValue[j] = aValue[j].slice(0, aValue[j].length - 1);
            }
            if (
              (aValue[j].indexOf("'") === 0 &&
                aValue[j][aValue[j].length - 1] === "'") ||
              (aValue[j].indexOf('"') === 0 &&
                aValue[j][aValue[j].length - 1] === '"')
            ) {
              aValue[j] = aValue[j].slice(1, aValue[j].length - 1);
            }
          }
        }

        newValueObj.value = [value];
        newValueObj.valueType = valueType;
        newValueObj.domain = domain;
        newValueObj.variableType = "method";
        if (nextVariableData) {
          newValueObj.next = nextVariableData;
        }
        if (paramsList && paramsList.length > 0) {
          const fregex = /\【.*?\】/g;
          newValueObj.paramsList = paramsList;
          newValueObj.methodParams = paramsList.map((item, index) => {
            let tempV = null
            let tempaV = []
            let resObj = {}
            if (aValue && aValueIndex) {
              tempV = aValue[aValueIndex[index]]
            }
            if (tempV) {
              tempaV = tempV.match(fregex)
            }
            if (tempaV && tempaV.length > 0) {
              // 变量
              this.replaceComVariable = []
              resObj = this.getComVari(firstFieldItemList, tempaV)
        } else {
          // 常量
              const {
                domain: item_domain,
                valueType: item_valueType
              } = item;
              resObj = {
                value: aValue[aValueIndex[index]] || "",
                valueType: item_valueType,
                variableType: "constant",
              };
              if (item_domain) {
                // domain
                resObj.enumDictName = item_domain;
                resObj.value = this.findValueForViewName(dictMap[item_domain], aValue[aValueIndex[index]]) || aValue[aValueIndex[index]] || ""
              }
            }
            return resObj;
          });
        }
      } else if (isDatasetItem) {
        // dataEntry 类型，需要将value进行合并，除非下一层（next）是method类型；
        if (nextVariableData && nextVariableData.variableType === "dataEntry") {
          newValueObj.value = nextVariableData.value;
          newValueObj.value.unshift(value);
          newValueObj.valueType = nextVariableData.valueType;
          newValueObj.dictName = nextVariableData.dictName;
          newValueObj.variableType = "dataEntry";
          if (nextVariableData.next) {
            newValueObj.next = nextVariableData.next;
          }
            } else {
          newValueObj.value = [value];
          newValueObj.valueType = valueType;
          newValueObj.variableType = "dataEntry";
          newValueObj.dictName = dictName;
          if (nextVariableData) {
            newValueObj.next = nextVariableData;
          }
        }
              } else {
        newValueObj.value = [value];
        newValueObj.valueType = valueType;
        newValueObj.domain = domain;
        newValueObj.variableType = "field";
        newValueObj.display = display;
        if (nextVariableData) {
          newValueObj.next = nextVariableData;
        }
      }
      return newValueObj;
    },
    getNewVariableData(optionList, methodTxt) {
      const len = optionList.length;
      let newVariableObj = {};
      let finalValueType;
      for (let i = len; i > 0; i--) {
        const option = optionList[i - 1];
        if (i === len) {
          newVariableObj = this.getVariableData(option, null, methodTxt);
          finalValueType = option.valueType;
        } else {
          newVariableObj = this.getVariableData(option, newVariableObj, methodTxt);
        }
      }
      return {
          newVariableObj,
          finalValueType
      };
    },
  }
};
