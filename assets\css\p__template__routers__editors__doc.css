.module-error {
    padding: 80px 0 120px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center
}

.module-error .error-tip {
    text-align: center;
    line-height: 2;
    color: var(--yq-text-caption)
}

.module-error .error-tip>h3 {
    font-size: 32px;
    font-weight: 400;
    line-height: 2.2;
    color: var(--yq-text-body)
}

.module-error .image {
    max-width: 400px
}

@media screen and (max-width: 768px) {
    .module-error .image {
        max-width:170px
    }
}

.index-module_size_wVASz {
    width: 24px;
    height: 24px
}

.index-module_more_sDgGm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--yq-black);
    border-radius: 8px
}

.ant-popover-open.index-module_more_sDgGm,.index-module_more_sDgGm:hover {
    border-radius: 8px
}

.toggle-publicity {
    color: var(--yq-text-link)!important;
    margin-left: 12px
}

.index-module_label_qcR\+D {
    border-radius: 2px;
    background-color: var(--yq-bg-tertiary);
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption);
    text-align: center
}

.DocTask-module_docTask_NywmO {
    padding: 30px 0 20px
}

.DocTask-module_docTask_NywmO .larkui-icon-check-outlined-circle {
    color: var(--yq-theme);
    font-size: 22px
}

.DocTask-module_docTask_NywmO .larkui-icon-close-circle,.DocTask-module_docTask_NywmO .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error);
    font-size: 20px
}

.DocTask-module_docTask_NywmO .action,.DocTask-module_docTask_NywmO .error,.DocTask-module_docTask_NywmO .icon,.DocTask-module_docTask_NywmO .tip {
    line-height: 27px;
    text-align: center;
    color: var(--yq-text-primary)
}

.DocTask-module_docTask_NywmO .error {
    color: var(--yq-function-error)
}

.DocTask-module_settingFile_bFy51 {
    margin-top: 16px;
    margin-bottom: 16px
}

.DocTask-module_hr_y8Wz9 {
    background-color: var(--yq-yuque-grey-5);
    height: 1px;
    margin: 15px 0 5px
}

.DocTask-module_settingFileIcon_\+\+xRm {
    width: 64px;
    height: 64px;
    margin-right: 5px
}

.DocTask-module_settingFileIcon_\+\+xRm img {
    width: 100%
}

.DocTask-module_settingContent_leSlb {
    min-height: 140px
}

.DocTask-module_settingContent_leSlb .ant-checkbox-wrapper,.DocTask-module_settingContent_leSlb .ant-radio-wrapper {
    margin-top: 8px;
    margin-left: 0;
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form .ant-legacy-form-item {
    margin-bottom: 0;
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-control {
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-label>label {
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-radio-group {
    border-top: 1px solid var(--yq-yuque-grey-5);
    margin-top: 10px
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label {
    display: block
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label>label.ant-legacy-form-item-no-colon:after {
    display: none
}

.DocTask-module_settingContent_leSlb .DocTask-module_isFirst_RPJNh .ant-radio-group {
    border-top: 0 none;
    margin-top: 0;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    padding-bottom: 10px
}

.DocTask-module_settingTitlt2_OZfjd {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 400;
    margin: 8px 0
}

.DocTask-module_settingFileTypeName_S4Bv1 {
    color: var(--yq-yuque-grey-9)
}

.DocTask-module_settingFileTypeExt_GCoBd {
    color: var(--yq-text-caption);
    font-size: 12px
}

.index-module_fileTypeSelector_73BHW {
    margin: 16px 0
}

.index-module_fileTypeSelector_73BHW .index-module_item_H3xgY {
    height: 152px
}

.index-module_fileTypeSelector_73BHW .index-module_fileType_l65Jm {
    width: 142px;
    text-align: center;
    padding: 16px 0 20px;
    cursor: pointer;
    transition: all .3s linear
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM {
    width: 70px;
    height: 70px;
    margin: 0 auto
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM img {
    width: 100%
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeMeta_4eYr8 {
    text-align: center;
    line-height: 24px;
    margin-top: 4px
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeName_9B0Rn {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeExt_wTTFB {
    font-size: 12px;
    line-height: 1;
    color: var(--yq-text-caption)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeReady_TAUvL {
    filter: grayscale(1);
    cursor: default;
    opacity: .45
}

.EvernoteDirUpload-module_modal_AERn- {
    padding: 16px
}

.EvernoteDirUpload-module_modal_AERn- .ant-upload {
    width: 100%
}

.EvernoteDirUpload-module_modal_AERn- .ant-modal-close-x {
    width: 24px;
    height: 24px;
    line-height: 32px
}

.EvernoteDirUpload-module_dragZone_qrdZx {
    border: dashed 1px var(--yq-border-primary);
    border-radius: 8px;
    width: 100%;
    height: 180px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_tip_FtuKl {
    padding-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption)
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_icon_ZYE-k {
    color: var(--yq-text-caption);
    margin-bottom: 12px
}

.EvernoteDirUpload-module_dragZone_qrdZx.EvernoteDirUpload-module_dragOver_0DaAV {
    border-color: var(--yq-blue-5)
}

.EvernoteDirUpload-module_dragZone_qrdZx:hover {
    border-color: var(--yq-border-primary-active)
}

.index-module_noWrap_glNxF {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%
}

.index-module_center_zSt9A {
    text-align: center
}

.index-module_inherit_TQ-oW {
    text-align: inherit
}

.index-module_justify_BBNTE {
    text-align: justify
}

.index-module_left_melIP {
    text-align: left
}

.index-module_right_BcDRZ {
    text-align: right
}

.index-module_common-link_LkRIl {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_common-link_LkRIl.active,.index-module_common-link_LkRIl:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-link_LkRIl:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_common-tab_EdL1P {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_common-tab_EdL1P.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_common-menu_OiYt2 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_common-menu_OiYt2.active,.index-module_common-menu_OiYt2:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_common-icon_60unB {
    display: inline-flex;
    align-items: center
}

.index-module_common-flex_YpvU1 {
    display: flex
}

.index-module_common-iconButton_9fUSh {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_common-iconButton_9fUSh svg {
    z-index: 1
}

.index-module_common-iconButton_9fUSh:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_common-iconButton_9fUSh:hover {
    color: var(--yq-text-primary)
}

.index-module_common-iconButton_9fUSh:hover:after {
    visibility: visible
}

.index-module_common-menu-item_wENsl {
    display: flex;
    align-items: center
}

.index-module_headline1_gNn3d {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 32px;
    line-height: 40px
}

.index-module_headline1-link_hpiWt {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline1-link_hpiWt.active,.index-module_headline1-link_hpiWt:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-link_hpiWt:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline1-tab_2tnKG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline1-tab_2tnKG.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline1-menu_kn1nZ {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline1-menu_kn1nZ.active,.index-module_headline1-menu_kn1nZ:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline1-icon_Ugsfz {
    display: inline-flex;
    align-items: center
}

.index-module_headline1-flex_XKxAU {
    display: flex
}

.index-module_headline1-iconButton_JxTPN {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline1-iconButton_JxTPN svg {
    z-index: 1
}

.index-module_headline1-iconButton_JxTPN:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline1-iconButton_JxTPN:hover {
    color: var(--yq-text-primary)
}

.index-module_headline1-iconButton_JxTPN:hover:after {
    visibility: visible
}

.index-module_headline1-menu-item_6KtqL {
    display: flex;
    align-items: center
}

.index-module_headline1-secondary_oSaxw {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline2_xyzBy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 24px;
    line-height: 32px
}

.index-module_headline2-link_bkLCB {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline2-link_bkLCB.active,.index-module_headline2-link_bkLCB:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-link_bkLCB:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline2-tab_n1Akb {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline2-tab_n1Akb.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline2-menu_QL2po {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline2-menu_QL2po.active,.index-module_headline2-menu_QL2po:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline2-icon_Ge-vk {
    display: inline-flex;
    align-items: center
}

.index-module_headline2-flex_KZxEZ {
    display: flex
}

.index-module_headline2-iconButton_uBIj0 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline2-iconButton_uBIj0 svg {
    z-index: 1
}

.index-module_headline2-iconButton_uBIj0:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline2-iconButton_uBIj0:hover {
    color: var(--yq-text-primary)
}

.index-module_headline2-iconButton_uBIj0:hover:after {
    visibility: visible
}

.index-module_headline2-menu-item_1iIzo {
    display: flex;
    align-items: center
}

.index-module_headline2-secondary_t1vqD {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline3_6OwWy {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 18px;
    line-height: 26px
}

.index-module_headline3-link_wcUG4 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline3-link_wcUG4.active,.index-module_headline3-link_wcUG4:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-link_wcUG4:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline3-tab_GRz8p {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline3-tab_GRz8p.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline3-menu_pdEQ6 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline3-menu_pdEQ6.active,.index-module_headline3-menu_pdEQ6:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline3-icon_maylx {
    display: inline-flex;
    align-items: center
}

.index-module_headline3-flex_EdA3C {
    display: flex
}

.index-module_headline3-iconButton_ljBJY {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline3-iconButton_ljBJY svg {
    z-index: 1
}

.index-module_headline3-iconButton_ljBJY:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline3-iconButton_ljBJY:hover {
    color: var(--yq-text-primary)
}

.index-module_headline3-iconButton_ljBJY:hover:after {
    visibility: visible
}

.index-module_headline3-menu-item_FneW1 {
    display: flex;
    align-items: center
}

.index-module_headline3-secondary_gdmSF {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_headline4_cCWAs {
    font-weight: 600;
    color: var(--yq-text-primary);
    font-size: 16px;
    line-height: 24px
}

.index-module_headline4-link_pksrg {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_headline4-link_pksrg.active,.index-module_headline4-link_pksrg:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-link_pksrg:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_headline4-tab_AZhw7 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_headline4-tab_AZhw7.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_headline4-menu_Zh\+Gf {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_headline4-menu_Zh\+Gf.active,.index-module_headline4-menu_Zh\+Gf:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_headline4-icon_I8A2c {
    display: inline-flex;
    align-items: center
}

.index-module_headline4-flex_-WCmH {
    display: flex
}

.index-module_headline4-iconButton_unM1q {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_headline4-iconButton_unM1q svg {
    z-index: 1
}

.index-module_headline4-iconButton_unM1q:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_headline4-iconButton_unM1q:hover {
    color: var(--yq-text-primary)
}

.index-module_headline4-iconButton_unM1q:hover:after {
    visibility: visible
}

.index-module_headline4-menu-item_HZvZs {
    display: flex;
    align-items: center
}

.index-module_headline4-secondary_mwc2q {
    color: var(--yq-text-body);
    font-weight: 400
}

.index-module_title_AMQeB {
    font-weight: 400;
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 22px
}

.index-module_title-link_LhWVV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_title-link_LhWVV.active,.index-module_title-link_LhWVV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-link_LhWVV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_title-tab_PEcOM {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_title-tab_PEcOM.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_title-menu_yRd7C {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_title-menu_yRd7C.active,.index-module_title-menu_yRd7C:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_title-icon_jD1r3 {
    display: inline-flex;
    align-items: center
}

.index-module_title-flex_6ud\+f {
    display: flex
}

.index-module_title-iconButton_eKfGH {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_title-iconButton_eKfGH svg {
    z-index: 1
}

.index-module_title-iconButton_eKfGH:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_title-iconButton_eKfGH:hover {
    color: var(--yq-text-primary)
}

.index-module_title-iconButton_eKfGH:hover:after {
    visibility: visible
}

.index-module_title-menu-item_\+2INX {
    display: flex;
    align-items: center
}

.index-module_title-secondary_\+7kF8 {
    color: var(--yq-text-body)
}

.index-module_title-strong_rB2kd {
    font-weight: 500
}

.index-module_body_0t8B1 {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_body-link_1Ezeh {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-link_1Ezeh.active,.index-module_body-link_1Ezeh:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-link_1Ezeh:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-tab_ZArAR {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-tab_ZArAR.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-menu_rkEZj {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-menu_rkEZj.active,.index-module_body-menu_rkEZj:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-icon_dtRXT {
    display: inline-flex;
    align-items: center
}

.index-module_body-flex_xUh97 {
    display: flex
}

.index-module_body-iconButton_zEAAb {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-iconButton_zEAAb svg {
    z-index: 1
}

.index-module_body-iconButton_zEAAb:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-iconButton_zEAAb:hover {
    color: var(--yq-text-primary)
}

.index-module_body-iconButton_zEAAb:hover:after {
    visibility: visible
}

.index-module_body-menu-item_530BT {
    display: flex;
    align-items: center
}

.index-module_body-secondary_jzKzN {
    color: var(--yq-text-caption)
}

.index-module_body-strong_B8Xyf {
    font-weight: 500
}

.index-module_body-small_M0nB9 {
    font-weight: 400;
    color: var(--yq-text-body);
    font-size: 12px;
    line-height: 20px
}

.index-module_body-small-link_dpTlG {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_body-small-link_dpTlG.active,.index-module_body-small-link_dpTlG:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-link_dpTlG:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_body-small-tab_HAI-k {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_body-small-tab_HAI-k.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_body-small-menu_\+sZ91 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_body-small-menu_\+sZ91.active,.index-module_body-small-menu_\+sZ91:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_body-small-icon_9yvQH {
    display: inline-flex;
    align-items: center
}

.index-module_body-small-flex_TzPYW {
    display: flex
}

.index-module_body-small-iconButton_Ry5bn {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_body-small-iconButton_Ry5bn svg {
    z-index: 1
}

.index-module_body-small-iconButton_Ry5bn:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_body-small-iconButton_Ry5bn:hover {
    color: var(--yq-text-primary)
}

.index-module_body-small-iconButton_Ry5bn:hover:after {
    visibility: visible
}

.index-module_body-small-menu-item_5h34h {
    display: flex;
    align-items: center
}

.index-module_body-small-secondary_dJo56 {
    color: var(--yq-text-caption)
}

.index-module_disable_bsdL7 {
    font-weight: 400;
    color: var(--yq-text-disable);
    font-size: 14px;
    line-height: 22px
}

.index-module_disable-link_fsP5Q {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-link_fsP5Q.active,.index-module_disable-link_fsP5Q:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-link_fsP5Q:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-tab_1L8n1 {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1 .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-tab_1L8n1.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-menu_h0UA9 {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-menu_h0UA9.active,.index-module_disable-menu_h0UA9:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-icon_szLm2 {
    display: inline-flex;
    align-items: center
}

.index-module_disable-flex_EDI9g {
    display: flex
}

.index-module_disable-iconButton_41jru {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-iconButton_41jru svg {
    z-index: 1
}

.index-module_disable-iconButton_41jru:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-iconButton_41jru:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-iconButton_41jru:hover:after {
    visibility: visible
}

.index-module_disable-menu-item_tolga {
    display: flex;
    align-items: center
}

.index-module_caption_bWso- {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 14px;
    line-height: 22px
}

.index-module_caption-link_iUcKX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-link_iUcKX.active,.index-module_caption-link_iUcKX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-link_iUcKX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-tab_Pahmo {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-tab_Pahmo.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-menu_FYIeq {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-menu_FYIeq.active,.index-module_caption-menu_FYIeq:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-icon_Y3NyS {
    display: inline-flex;
    align-items: center
}

.index-module_caption-flex_FI2Np {
    display: flex
}

.index-module_caption-iconButton_hWPqs {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-iconButton_hWPqs svg {
    z-index: 1
}

.index-module_caption-iconButton_hWPqs:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-iconButton_hWPqs:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-iconButton_hWPqs:hover:after {
    visibility: visible
}

.index-module_caption-menu-item_1kLnU {
    display: flex;
    align-items: center
}

.index-module_caption-secondary_SFdcH {
    color: var(--yq-text-disable)
}

.index-module_caption-small_B13mr {
    font-weight: 400;
    color: var(--yq-text-caption);
    font-size: 12px;
    line-height: 20px
}

.index-module_caption-small-link_8T\+\+a {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_caption-small-link_8T\+\+a.active,.index-module_caption-small-link_8T\+\+a:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-link_8T\+\+a:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_caption-small-tab_ibUZP {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_caption-small-tab_ibUZP.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_caption-small-menu_G8H-J {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_caption-small-menu_G8H-J.active,.index-module_caption-small-menu_G8H-J:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_caption-small-icon_PtjdH {
    display: inline-flex;
    align-items: center
}

.index-module_caption-small-flex_\+EFJk {
    display: flex
}

.index-module_caption-small-iconButton_33CTf {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_caption-small-iconButton_33CTf svg {
    z-index: 1
}

.index-module_caption-small-iconButton_33CTf:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_caption-small-iconButton_33CTf:hover {
    color: var(--yq-text-primary)
}

.index-module_caption-small-iconButton_33CTf:hover:after {
    visibility: visible
}

.index-module_caption-small-menu-item_cqmp0 {
    display: flex;
    align-items: center
}

.index-module_caption-small-secondary_H2H1r {
    color: var(--yq-text-disable)
}

.index-module_primary-button-default_Vw1-y {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white)
}

.index-module_primary-button-default-secondary_R\+QUT {
    color: var(--yq-text-caption)
}

.index-module_primary-button-default-strong_Htk5A {
    font-weight: 500
}

.index-module_primary-button-default-link_oQhLa {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-default-link_oQhLa.active,.index-module_primary-button-default-link_oQhLa:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-link_oQhLa:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-default-tab_rf8zH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-tab_rf8zH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-default-menu_JESyh {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-default-menu_JESyh.active,.index-module_primary-button-default-menu_JESyh:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-default-icon_E0TCd {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-default-flex_pNGzV {
    display: flex
}

.index-module_primary-button-default-iconButton_bguHS {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-default-iconButton_bguHS svg {
    z-index: 1
}

.index-module_primary-button-default-iconButton_bguHS:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-default-iconButton_bguHS:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-default-iconButton_bguHS:hover:after {
    visibility: visible
}

.index-module_primary-button-default-menu-item_EY5rA {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong_tCPsX {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-white);
    font-weight: 500
}

.index-module_primary-button-strong-secondary_rR6X1 {
    color: var(--yq-text-caption)
}

.index-module_primary-button-strong-strong_KuhtF {
    font-weight: 500
}

.index-module_primary-button-strong-link_FFk3N {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_primary-button-strong-link_FFk3N.active,.index-module_primary-button-strong-link_FFk3N:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-link_FFk3N:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_primary-button-strong-tab_iu42A {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-tab_iu42A.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_primary-button-strong-menu_MUMyr {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_primary-button-strong-menu_MUMyr.active,.index-module_primary-button-strong-menu_MUMyr:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_primary-button-strong-icon_-xloa {
    display: inline-flex;
    align-items: center
}

.index-module_primary-button-strong-flex_uWzkw {
    display: flex
}

.index-module_primary-button-strong-iconButton_f6r63 {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_primary-button-strong-iconButton_f6r63 svg {
    z-index: 1
}

.index-module_primary-button-strong-iconButton_f6r63:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_primary-button-strong-iconButton_f6r63:hover {
    color: var(--yq-text-primary)
}

.index-module_primary-button-strong-iconButton_f6r63:hover:after {
    visibility: visible
}

.index-module_primary-button-strong-menu-item_RzDwe {
    display: flex;
    align-items: center
}

.index-module_primary-button-strong-ghost_LfbnS {
    color: var(--yq-yuque-green-600)
}

.index-module_secondary-button-default_nN4ts {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-link_P4JJV {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_secondary-button-default-link_P4JJV.active,.index-module_secondary-button-default-link_P4JJV:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-link_P4JJV:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_secondary-button-default-tab_sTKaj {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-tab_sTKaj.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_secondary-button-default-menu_CnLQt {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_secondary-button-default-menu_CnLQt.active,.index-module_secondary-button-default-menu_CnLQt:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_secondary-button-default-icon_EBrIo {
    display: inline-flex;
    align-items: center
}

.index-module_secondary-button-default-flex_myAK8 {
    display: flex
}

.index-module_secondary-button-default-iconButton_3l8nj {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_secondary-button-default-iconButton_3l8nj svg {
    z-index: 1
}

.index-module_secondary-button-default-iconButton_3l8nj:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_secondary-button-default-iconButton_3l8nj:hover {
    color: var(--yq-text-primary)
}

.index-module_secondary-button-default-iconButton_3l8nj:hover:after {
    visibility: visible
}

.index-module_secondary-button-default-menu-item_OZFHO {
    display: flex;
    align-items: center
}

.index-module_secondary-button-default-secondary_iH73C {
    color: var(--yq-text-caption)
}

.index-module_secondary-button-default-strong_U1PcZ {
    font-weight: 500
}

.index-module_disable-button-default_do2Ky {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable)
}

.index-module_disable-button-default-link_Cc4nX {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_disable-button-default-link_Cc4nX.active,.index-module_disable-button-default-link_Cc4nX:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-link_Cc4nX:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_disable-button-default-tab_uzzYS {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-tab_uzzYS.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_disable-button-default-menu_ki5Hs {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_disable-button-default-menu_ki5Hs.active,.index-module_disable-button-default-menu_ki5Hs:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_disable-button-default-icon_\+eAaQ {
    display: inline-flex;
    align-items: center
}

.index-module_disable-button-default-flex_pSS0P {
    display: flex
}

.index-module_disable-button-default-iconButton_Ns-ZT {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_disable-button-default-iconButton_Ns-ZT svg {
    z-index: 1
}

.index-module_disable-button-default-iconButton_Ns-ZT:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover {
    color: var(--yq-text-primary)
}

.index-module_disable-button-default-iconButton_Ns-ZT:hover:after {
    visibility: visible
}

.index-module_disable-button-default-menu-item_H7X58 {
    display: flex;
    align-items: center
}

.index-module_disable-button-default-secondary_sJwUn {
    color: var(--yq-text-caption)
}

.index-module_disable-button-default-strong_ApPSw {
    font-weight: 500
}

.index-module_link_4lMzA {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_link-link_MSu6v {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_link-link_MSu6v.active,.index-module_link-link_MSu6v:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-link_MSu6v:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_link-tab_UaSsE {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_link-tab_UaSsE.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_link-menu_X9-jl {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_link-menu_X9-jl.active,.index-module_link-menu_X9-jl:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_link-icon_GlYMK {
    display: inline-flex;
    align-items: center
}

.index-module_link-flex_y87zq {
    display: flex
}

.index-module_link-iconButton_hFUyW {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_link-iconButton_hFUyW svg {
    z-index: 1
}

.index-module_link-iconButton_hFUyW:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_link-iconButton_hFUyW:hover {
    color: var(--yq-text-primary)
}

.index-module_link-iconButton_hFUyW:hover:after {
    visibility: visible
}

.index-module_link-menu-item_QrtKv {
    display: flex;
    align-items: center
}

.index-module_link-secondary_cYp8F {
    color: var(--yq-text-caption)
}

.index-module_link-strong_6cupy {
    font-weight: 500
}

.index-module_link_4lMzA:hover {
    color: var(--yq-blue-6)
}

.index-module_linkBtn_viBrQ {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-body);
    color: var(--yq-text-disable);
    padding: 4.5px;
    display: flex;
    align-items: center;
    border-radius: 6px
}

.index-module_linkBtn-link_fRNiK {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between
}

.index-module_linkBtn-link_fRNiK.active,.index-module_linkBtn-link_fRNiK:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-link_fRNiK:after {
    content: attr(data-title) "";
    content: attr(data-title)/"";
    height: 0;
    visibility: hidden;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    pointer-events: none;
    font-weight: 500
}

.index-module_linkBtn-tab_xLPRH {
    display: inline-flex;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH .index-module_active_cmLTu:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH:hover:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_linkBtn-tab_xLPRH.tabActive:after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_linkBtn-menu_K8vUm {
    cursor: pointer;
    border-radius: 6px;
    padding: 5px 30px
}

.index-module_linkBtn-menu_K8vUm.active,.index-module_linkBtn-menu_K8vUm:hover {
    background-color: var(--yq-yuque-grey-3);
    color: var(--yq-text-body)
}

.index-module_linkBtn-icon_bNwRp {
    display: inline-flex;
    align-items: center
}

.index-module_linkBtn-flex_Xds\+s {
    display: flex
}

.index-module_linkBtn-iconButton_nWnGl {
    display: inline-flex;
    align-items: center;
    position: relative;
    cursor: pointer
}

.index-module_linkBtn-iconButton_nWnGl svg {
    z-index: 1
}

.index-module_linkBtn-iconButton_nWnGl:after {
    background-color: var(--yq-yuque-grey-2);
    position: absolute;
    content: "";
    left: -6px;
    top: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
    border-radius: 7px;
    visibility: hidden;
    z-index: 0
}

.index-module_linkBtn-iconButton_nWnGl:hover {
    color: var(--yq-text-primary)
}

.index-module_linkBtn-iconButton_nWnGl:hover:after {
    visibility: visible
}

.index-module_linkBtn-menu-item_rDBK\+ {
    display: flex;
    align-items: center
}

.index-module_linkBtn-secondary_pxTMi {
    color: var(--yq-text-caption)
}

.index-module_linkBtn-strong_5PlMk {
    font-weight: 500
}

.index-module_linkBtn_viBrQ:hover {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-yuque-grey-9)
}

html[data-kumuhana=pouli] .index-module_linkBtn_viBrQ:hover {
    color: var(--yq-yuque-grey-9)!important
}

.index-module_yuque-green-1_t\+Yes {
    color: var(--yq-yuque-green-1)
}

.index-module_yuque-green-2_PSwCn {
    color: var(--yq-yuque-green-2)
}

.index-module_yuque-green-3_990Pj {
    color: var(--yq-yuque-green-3)
}

.index-module_yuque-green-4_LGQYT {
    color: var(--yq-yuque-green-4)
}

.index-module_yuque-green-5_Wdkpl {
    color: var(--yq-yuque-green-5)
}

.index-module_yuque-green-6_sp\+C1 {
    color: var(--yq-yuque-green-6)
}

.index-module_yuque-green-7_Yz0nb {
    color: var(--yq-yuque-green-7)
}

.index-module_yuque-green-8_TdQoj {
    color: var(--yq-yuque-green-8)
}

.index-module_yuque-green-9_78uX8 {
    color: var(--yq-yuque-green-9)
}

.index-module_yuque-grey-1_MFlzw {
    color: var(--yq-yuque-grey-1)
}

.index-module_yuque-grey-2_1MPla {
    color: var(--yq-yuque-grey-2)
}

.index-module_yuque-grey-3_Xwd3D {
    color: var(--yq-yuque-grey-3)
}

.index-module_yuque-grey-4_Iaalm {
    color: var(--yq-yuque-grey-4)
}

.index-module_yuque-grey-5_dTMGn {
    color: var(--yq-yuque-grey-5)
}

.index-module_yuque-grey-6_9P9mX {
    color: var(--yq-yuque-grey-6)
}

.index-module_yuque-grey-7_fo9a2 {
    color: var(--yq-yuque-grey-7)
}

.index-module_yuque-grey-8_5ZItk {
    color: var(--yq-yuque-grey-8)
}

.index-module_yuque-grey-9_2KrCK {
    color: var(--yq-yuque-grey-9)
}

.index-module_blue-1_9NXmy {
    color: var(--yq-blue-1)
}

.index-module_blue-2_71Xfa {
    color: var(--yq-blue-2)
}

.index-module_blue-3_QzBMA {
    color: var(--yq-blue-3)
}

.index-module_blue-4_eIuv6 {
    color: var(--yq-blue-4)
}

.index-module_blue-5_--rJ2 {
    color: var(--yq-blue-5)
}

.index-module_blue-6_EM\+ye {
    color: var(--yq-blue-6)
}

.index-module_blue-7_dcd5v {
    color: var(--yq-blue-7)
}

.index-module_blue-8_i6IXc {
    color: var(--yq-blue-8)
}

.index-module_blue-9_8WLz4 {
    color: var(--yq-blue-9)
}

.index-module_organge-1_QF-zP {
    color: var(--yq-orange-1)
}

.index-module_organge-2_zYIHH {
    color: var(--yq-orange-2)
}

.index-module_organge-3_EaUTx {
    color: var(--yq-orange-3)
}

.index-module_organge-4_KEzWt {
    color: var(--yq-orange-4)
}

.index-module_organge-5_uWCqi {
    color: var(--yq-orange-5)
}

.index-module_organge-6_SWaCL {
    color: var(--yq-orange-6)
}

.index-module_organge-7_UCnnq {
    color: var(--yq-orange-7)
}

.index-module_organge-8_yxbUr {
    color: var(--yq-orange-8)
}

.index-module_organge-9_Wqbx6 {
    color: var(--yq-orange-9)
}

.index-module_yellow-1_9BdDH {
    color: var(--yq-yellow-1)
}

.index-module_yellow-2_mmpuD {
    color: var(--yq-yellow-2)
}

.index-module_yellow-3_1MgEh {
    color: var(--yq-yellow-3)
}

.index-module_yellow-4_twlST {
    color: var(--yq-yellow-4)
}

.index-module_yellow-5_Z7Kqp {
    color: var(--yq-yellow-5)
}

.index-module_yellow-6_EZhZz {
    color: var(--yq-yellow-6)
}

.index-module_yellow-7_lCkCE {
    color: var(--yq-yellow-7)
}

.index-module_yellow-8_ZQ7hX {
    color: var(--yq-yellow-8)
}

.index-module_yellow-9_NAs4Y {
    color: var(--yq-yellow-9)
}

.index-module_red-1_Z-G\+I {
    color: var(--yq-red-1)
}

.index-module_red-2_ec26p {
    color: var(--yq-red-2)
}

.index-module_red-3_CitaX {
    color: var(--yq-red-3)
}

.index-module_red-4_Rf4VB {
    color: var(--yq-red-4)
}

.index-module_red-5_8DEQO {
    color: var(--yq-red-5)
}

.index-module_red-6_e40Pl {
    color: var(--yq-red-6)
}

.index-module_red-7_ND3LM {
    color: var(--yq-red-7)
}

.index-module_red-8_S4EAE {
    color: var(--yq-red-8)
}

.index-module_red-9_s0fc1 {
    color: var(--yq-red-9)
}

.index-module_magenta-1_BVOfc {
    color: var(--yq-magenta-1)
}

.index-module_magenta-2_LeAiv {
    color: var(--yq-magenta-2)
}

.index-module_magenta-3_anmFf {
    color: var(--yq-magenta-3)
}

.index-module_magenta-4_iyuji {
    color: var(--yq-magenta-4)
}

.index-module_magenta-5_XFO8v {
    color: var(--yq-magenta-5)
}

.index-module_magenta-6_83nvI {
    color: var(--yq-magenta-6)
}

.index-module_magenta-7_EylhI {
    color: var(--yq-magenta-7)
}

.index-module_magenta-8_H7UoN {
    color: var(--yq-magenta-8)
}

.index-module_magenta-9_BZu8L {
    color: var(--yq-magenta-9)
}

.index-module_purple-1_4Mt5K {
    color: var(--yq-purple-1)
}

.index-module_purple-2_ZU8FB {
    color: var(--yq-purple-2)
}

.index-module_purple-3_XZk6m {
    color: var(--yq-purple-3)
}

.index-module_purple-4_RkQAl {
    color: var(--yq-purple-4)
}

.index-module_purple-5_UwSof {
    color: var(--yq-purple-5)
}

.index-module_purple-6_VE5X4 {
    color: var(--yq-purple-6)
}

.index-module_purple-7_4qxQ3 {
    color: var(--yq-purple-7)
}

.index-module_purple-8_34VRx {
    color: var(--yq-purple-8)
}

.index-module_purple-9_S5yo0 {
    color: var(--yq-purple-9)
}

.index-module_cyan-1_XcHSS {
    color: var(--yq-cyan-1)
}

.index-module_cyan-2_8Z0Qc {
    color: var(--yq-cyan-2)
}

.index-module_cyan-3_8EpV- {
    color: var(--yq-cyan-3)
}

.index-module_cyan-4_BTV0V {
    color: var(--yq-cyan-4)
}

.index-module_cyan-5_OOfUa {
    color: var(--yq-cyan-5)
}

.index-module_cyan-6_8F8Ne {
    color: var(--yq-cyan-6)
}

.index-module_cyan-7_tQGo7 {
    color: var(--yq-cyan-7)
}

.index-module_cyan-8_nBcrC {
    color: var(--yq-cyan-8)
}

.index-module_cyan-9_pp0HZ {
    color: var(--yq-cyan-9)
}

.index-module_pea-green-1_YZ7VF {
    color: var(--yq-pea-green-1)
}

.index-module_pea-green-2_cB-wj {
    color: var(--yq-pea-green-2)
}

.index-module_pea-green-3_0aW\+j {
    color: var(--yq-pea-green-3)
}

.index-module_pea-green-4_V3TMD {
    color: var(--yq-pea-green-4)
}

.index-module_pea-green-5_UQ6\+a {
    color: var(--yq-pea-green-5)
}

.index-module_pea-green-6_VBEAH {
    color: var(--yq-pea-green-6)
}

.index-module_pea-green-7_fz-pg {
    color: var(--yq-pea-green-7)
}

.index-module_pea-green-8_vhbae {
    color: var(--yq-pea-green-8)
}

.index-module_pea-green-9_iLSdb {
    color: var(--yq-pea-green-9)
}

.index-module_white_-Ikm2 {
    color: var(--yq-white)
}

.index-module_yuque-color-text-disable_H8og1 {
    color: var(--yq-text-disable)
}

.index-module_flexFix_gj50B {
    width: 0;
    flex: 1
}

.BookExport-module_docExport_44PUl .BookExport-module_title_Hezdm {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.BookExport-module_docExport_44PUl .BookExport-module_tips_VJJKo {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.BookExport-module_setting_PXAtw .BookExport-module_settingTitle_pEiZb {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookExport-module_setting_PXAtw .ant-upload-select,.BookExport-module_setting_PXAtw .BookExport-module_settingUpload_sNiNV {
    width: 100%
}

.index-module_content_v7lvp,.index-module_form_5k71F .larkui-form-item {
    margin-bottom: 16px
}

.index-module_form_5k71F .larkui-form-item:last-child {
    margin-bottom: 0
}

.index-module_tip_yNrai {
    line-height: 40px;
    color: var(--yq-text-primary)
}

.BookAction-module_wrap_AiMmc .ant-menu {
    min-width: 89px;
    padding: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.BookAction-module_wrap_AiMmc .ant-menu-item-divider {
    margin: 4px 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item {
    color: var(--yq-text-body);
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:not(:last-child) {
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_bookTitle_lUUrB {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB {
    padding-left: 12px;
    flex: 1;
    overflow: hidden
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_group_xaGsx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption);
    font-size: 13px;
    line-height: 18px
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_text_UrMgl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 21px;
    padding-bottom: 4px
}

.index-module_bookTitle_lUUrB .index-module_checked_3fCif {
    padding: 0 4px;
    color: var(--yq-text-link);
    font-size: 20px;
    width: 28px
}

.index-module_moreActions_YYACs {
    margin-left: 8px
}

.book-link,.book-name,.lark-book-title {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-book-title .book-icon {
    margin-right: 8px
}

.book-name {
    display: flex;
    align-items: center
}

.book-name .book-name-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.book-name .book-name-scope {
    position: relative
}

.book-name .book-name-scope a {
    pointer-events: none
}

.book-name .book-name-scope .icon-svg {
    margin-left: 5px
}

.book-name .book-name-orglabel {
    margin-left: 12px
}

.book-name .icon-svg {
    display: block
}

.book-name-split {
    margin: 0 4px
}

.permission__public__tip {
    margin-top: 12px;
    line-height: 22px
}

.permission__public__tip .highlight {
    color: var(--yq-function-error)
}

.belong-icon {
    font-size: 12px;
    margin-left: 4px
}

.index-module_placeholder_3rydn {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_placeholder_3rydn .index-module_loaded_kAsRx {
    opacity: 1
}

.index-module_placeholder_3rydn img {
    opacity: 0;
    transition: opacity .2s ease-in
}

.index-module_placeholderLoaded_0zPqS {
    background-color: transparent
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.styles-module_selector_Z3\+Wx {
    padding: 24px;
    position: relative
}

.styles-module_title_Misjv {
    font-size: 16px;
    line-height: 24px
}

.Template-module_headerTitleWrapper_lL39i {
    display: flex;
    align-items: center;
    height: 60px
}

.Template-module_headerTitleWrapper_lL39i .Template-module_headCrumb_hRt-t {
    display: flex;
    margin-right: 8px
}

.lark-editor-header {
    position: relative;
    height: 60px;
    padding-right: 24px;
    background: var(--yq-bg-primary);
    z-index: 999
}

.lark-editor-header[type=Doc] {
    border-bottom: 1px solid var(--yq-border-light)
}

.lark-editor-header .lark-editor-header-content {
    width: 100%;
    position: relative;
    height: 60px;
    display: flex;
    flex: 1 1 auto
}

.lark-editor-header .lark-editor-header-back {
    display: flex;
    width: 60px;
    border-right: 1px solid var(--yq-border-light);
    align-items: center;
    justify-content: center
}

.lark-editor-header .lark-editor-header-action {
    position: absolute;
    top: 14px;
    right: 0;
    z-index: 301;
    width: auto;
    display: flex
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-users {
    padding: 4px 0
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-wrapper {
    display: flex;
    justify-content: center;
    align-items: center
}

.lark-editor-header .lark-editor-header-action .lark-editor-collab-wrapper .lark-editor-collab-icon {
    width: 20px;
    height: 20px;
    position: relative;
    cursor: pointer;
    color: var(--yq-black)
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-action-item {
    padding-left: 8px
}

.lark-editor-header .lark-editor-header-action .book-custom-index-publish-button {
    padding-right: 12px
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-more {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    margin-left: 20px
}

.lark-editor-header .lark-editor-header-action .lark-editor-header-more-trigger {
    padding: 0 4px;
    line-height: 1
}

.lark-editor-header .editor-publish-button .ant-btn-primary[disabled] {
    color: var(--yq-text-caption)
}

.lark-editor-header-info {
    padding: 8px 0;
    margin-left: 16px;
    width: 40%
}

.lark-editor-header-title {
    display: flex;
    align-items: center;
    align-self: center;
    height: 24px;
    line-height: 24px
}

.lark-editor-header-title .name-title {
    cursor: pointer;
    font-weight: 700;
    font-size: 16px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.lark-editor-header-title .larkicon-lock {
    color: var(--yq-text-caption)
}

.lark-editor-header-title .doc-access-scope {
    line-height: 24px
}

.lark-editor-header-title .lark-editor-header-title-input {
    padding: 2px 3px;
    margin-left: -4px;
    font-weight: 700;
    font-size: 16px;
    min-width: 200px;
    max-width: 300px
}

.lark-editor-header-crumb-status {
    display: flex;
    height: 20px
}

.lark-editor-header-crumb-status .lark-breadcrumb .lark-breadcrumb-current {
    font-weight: 400
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb {
    height: 20px;
    line-height: 20px;
    max-width: 300px
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb>span:last-child a {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-link:hover,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb:hover,.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb>span:last-child a:hover {
    color: var(--yq-text-body)
}

.lark-editor-header-crumb-status .lark-breadcrumb .ant-breadcrumb .ant-breadcrumb-separator,.lark-editor-header-crumb-status .split {
    color: var(--yq-yuque-grey-5);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.lark-editor-header-crumb-status .split {
    position: relative;
    top: -1px;
    line-height: 20px;
    font-size: 12px;
    margin: 0 8px
}

.lark-editor-header-history {
    display: flex;
    align-items: center;
    padding: 4px 0 4px 12px
}

.lark-editor-save-tip {
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    min-width: 85px
}

.lark-editor-save-tip,.lark-editor-save-tip a:hover {
    color: var(--yq-text-caption)
}

.lark-editor-save-tip>.icon-svg {
    margin-left: 4px
}

.lark-editor-net-status {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-disable)
}

.lark-editor-net-status .larkui-icon-delete-solid {
    margin-right: 5px;
    color: var(--yq-function-error)
}

.book-custom-index-save-tip {
    padding: 7px 0 7px 21px;
    line-height: 1;
    font-size: 14px;
    color: var(--yq-text-caption);
    display: flex;
    align-items: center
}

.book-custom-index-save-tip .icon-svg {
    margin-right: 8px
}

.book-custom-index-header-title {
    padding-left: 18px;
    display: flex;
    align-items: center
}

.book-custom-index-header-title a {
    color: var(--yq-text-body);
    display: inline-flex;
    align-items: center
}

.book-custom-index-header-title a:hover {
    color: var(--yq-text-disable)
}

.book-custom-index-header-title span {
    padding-left: 6px
}

.book-custom-index-header-title .book-custom-index-file-title {
    border-left: 1px solid var(--yq-border-primary);
    margin-left: 12px;
    padding-left: 12px;
    font-weight: 500
}

.lark-editor-header .doc-title-edit-view {
    float: left
}

.lark-editor-conflict-tip {
    position: absolute;
    top: 0;
    width: 100%
}

.lark-editor-conflict-tip .ant-alert {
    margin: 0 auto;
    width: 400px
}

.lark-editor-header .lark-editor-user {
    position: relative;
    display: inline-block
}

.template-editor-header-doc {
    border-bottom: 1px solid var(--yq-border-primary)
}

.template-editor-header .lark-editor-header-action .lark-editor-header-more {
    padding: 7px 0 7px 21px
}

.template-header-delete-confirmation {
    display: flex
}

.template-header-delete-confirmation span {
    margin: 4px 8px 0 0
}

.templateTag .ant-tag {
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.template-header-use-template {
    margin-right: 8px
}

.template-header-title {
    display: flex;
    align-items: center;
    margin-left: 24px
}

.template-header-title .name-split {
    margin: 0 4px
}

.template-header-title .book-name-scope {
    position: relative;
    top: 1px
}

.template-header-title .book-name-scope .icon-svg {
    margin-left: 2px
}

.template-header-title .lark-breadcrumb .icon-svg {
    display: inline
}

.template-header-title a {
    color: var(--yq-text-body)
}

.template-header-title a:hover {
    color: var(--yq-text-caption)
}

.template-header-title input {
    width: auto
}

.template-header-title .header-crumb {
    padding-right: 8px
}

.StatusView-module_wrap_8c-mq {
    position: fixed;
    top: 56px;
    right: 0;
    left: 0;
    z-index: 1002
}

.StatusView-module_wrap_8c-mq .larkui-icon-info-circle,.StatusView-module_wrap_8c-mq .larkui-icon-information {
    color: var(--yq-function-info)
}

.StatusView-module_wrap_8c-mq .larkui-icon-error-circle {
    color: var(--yq-function-error)
}

.StatusView-module_wrap_8c-mq .larkui-icon-exclamation-circle,.StatusView-module_wrap_8c-mq .larkui-icon-exclamationcircle {
    color: var(--yq-function-warning);
    font-size: 24px;
    line-height: 24px;
    vertical-align: middle;
    margin-right: 12px
}

.StatusView-module_wrap_8c-mq .StatusView-module_tip_DFrDV {
    margin-top: 96px
}

.StatusView-module_wrap_8c-mq .StatusView-module_collab-tip_-YV1P {
    display: inline-block;
    text-align: center;
    background: var(--yq-bg-primary);
    border-radius: 2px;
    margin: 0 auto;
    padding: 4px 8px
}

.StatusView-module_wrap_8c-mq .StatusView-module_error_cvob- {
    border-radius: 4px;
    margin: 0 auto;
    width: 420px;
    padding: 32px;
    background: var(--yq-bg-primary)
}

.StatusView-module_wrap_8c-mq .StatusView-module_title_8Zp5n {
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.StatusView-module_wrap_8c-mq .StatusView-module_content_80A7- {
    line-height: 24px;
    padding-left: 20px
}

.StatusView-module_wrap_8c-mq .StatusView-module_contentIcon_vp-6I {
    line-height: 24px;
    padding-left: 20px;
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-9);
    font-size: 14px
}

.StatusView-module_wrap_8c-mq .StatusView-module_contentIcon_vp-6I svg {
    color: var(--yq-yellow-600);
    margin-right: 16px
}

.StatusView-module_wrap_8c-mq .StatusView-module_infoList_JcjpV {
    list-style: inside;
    padding: 10px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 {
    font-size: 16px;
    text-align: right;
    margin-top: 24px;
    color: var(--yq-text-body);
    font-weight: 700
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 button {
    margin-left: 8px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 .ant-btn {
    margin-left: 10px
}

.StatusView-module_wrap_8c-mq .StatusView-module_action_FlBG8 .larkicon {
    color: var(--yq-text-caption);
    font-size: 14px
}

.StatusView-module_noticeWrap_LtD\+K {
    width: 100%;
    height: 56px;
    padding: 6px;
    text-align: center;
    position: fixed;
    pointer-events: none;
    top: 0
}

.StatusView-module_noticeWrap_LtD\+K .StatusView-module_notice_73bog {
    display: inline-block;
    padding: 10px 16px;
    background: var(--yq-bg-primary);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,.15);
    pointer-events: all
}

.StatusView-module_mask_CFGUv {
    bottom: 0;
    height: calc(100% - 56px);
    background: rgba(0,0,0,.1)
}

.StatusView-module_actionTips_4SMlq {
    max-width: 160px
}

.StatusView-module_editorMessage_swR0c {
    font-size: 12px
}

.StatusView-module_rightBtns_I4V0J {
    padding: 16px 0;
    text-align: right
}

.StatusView-module_mask_mobile_vHbQp {
    height: 100%;
    top: 0
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidBadgeLiteContainer_5srvq .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-text-caption);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yuque-grey-4),var(--yq-yuque-grey-5));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberUnpaidNomeBadgeLiteContainer_okzfD .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteContainer_1caED {
    margin-top: 10px;
    text-align: center;
    cursor: pointer
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteContainer_1caED .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 {
    margin-top: 10px;
    text-align: center
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .badgelite-module_memberBadgeLiteTextAlibaba_eY\+40 {
    color: var(--yq-orange-7);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -7px;
    left: -8px
}

.badgelite-module_memberBadgeLiteNomeContainer_9AJD2 .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V {
    position: relative;
    top: 8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .badgelite-module_memberBadgeLiteText_2xxqK {
    color: var(--yq-orange-8);
    font-size: 12px;
    padding: 3px 10px 3px 16px;
    background-image: linear-gradient(140deg,var(--yq-yellow-1),var(--yq-yellow-2));
    border-radius: 4px;
    position: relative;
    top: -8px;
    left: -8px
}

.badgelite-module_memberBadgeLiteGroupContainer_6-b6V .icon-svg {
    position: relative;
    top: 0;
    left: 3px;
    z-index: 9
}

.badgelite-module_memberBadgeLiteH5Container_ituBE {
    position: relative;
    left: 2px
}

.btn-follow .hover-text,.btn-follow:hover .default-text {
    display: none
}

.btn-follow:hover .hover-text {
    display: inline
}

.btn-follow.ant-btn-default:focus,.btn-follow.ant-btn-default:hover {
    color: var(--yq-text-body);
    border-color: var(--yq-border-primary);
    outline: none
}

.btn-follow.ant-btn-clicked:after {
    display: none
}

.btn-plain-follow {
    color: var(--yq-text-caption)
}

.btn-plain-follow .larkui-icon {
    margin-right: 4px;
    color: var(--yq-text-caption)
}

.btn-plain-follow>a,.btn-plain-follow>a .larkui-icon {
    color: var(--yq-text-caption)
}

.btn-plain-follow>a:hover,.btn-plain-follow>a:hover .larkui-icon {
    color: var(--yq-text-body)
}

.index-module_count_pDHQn {
    margin-left: 8px;
    font-weight: 500
}

.index-module_offlineButton_XZkWh {
    cursor: not-allowed
}

.index-module_disabledStyle_MeBOz {
    opacity: .4;
    cursor: not-allowed;
    pointer-events: none
}

.index-module_mainInput_x4D9t.ant-input-affix-wrapper {
    max-width: 300px;
    width: 172px
}

.index-module_verifyResult_pdxsH {
    margin-top: 4px;
    margin-bottom: -16px;
    height: 20px;
    color: var(--yq-red-5);
    font-weight: 400
}

.index-module_verifyResult_pdxsH.index-module_success_NhSta {
    color: var(--yq-pea-green-6)
}

.index-module_inputVerify_kl-bC {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-weight: 500
}

.index-module_editIcon_eXpln {
    margin-left: 8px
}

.index-module_promoCodeDefault_6iUh6 {
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: var(--yq-text-body);
    font-weight: 400;
    text-align: right;
    cursor: pointer
}

.index-module_orgCostDetail_cLnEB {
    cursor: pointer;
    font-weight: 500
}

.index-module_content_oAhH8 {
    width: 378px
}

.index-module_content_oAhH8 .ant-divider {
    margin: 12px 0
}

.index-module_row_by2z2 {
    display: flex;
    justify-content: space-between;
    padding: 6px 0
}

.index-module_row_by2z2:last-child {
    font-weight: 700
}

.index-module_row_by2z2 .index-module_right_3Ce8Y {
    display: flex
}

.index-module_tips_EUcPA {
    padding: 2px 0;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_title_J6b5y {
    padding: 8px 8px 8px 0
}

.index-module_title_J6b5y a {
    color: var(--yq-text-caption)
}

.index-module_hasDiscount_TqEsK {
    color: var(--yq-orange-7)
}

.index-module_rawPrice_2UTkU {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_detailsWrapper_rnkfa .index-module_detailTitle_-gjun {
    color: var(--yq-text-primary)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt {
    height: 72px;
    max-height: 80px;
    overflow-y: auto
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.index-module_detailsWrapper_rnkfa .index-module_details_E5fEt .index-module_detailRow_e7YNK span:nth-child(2n) {
    color: var(--yq-text-body)
}

.index-module_summary_TYNAM {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_summary_TYNAM .index-module_left_m83qX {
    display: block
}

.index-module_summary_TYNAM .index-module_left_m83qX .index-module_summaryTitle_FTeFf {
    color: var(--yq-text-primary)
}

.index-module_summary_TYNAM .index-module_left_m83qX span {
    margin-top: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_summary_TYNAM .index-module_right_3Ce8Y .index-module_price_5CyQB {
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_divider_Rppou {
    margin: 12px auto
}

.index-module_paymentSelector_60wvq {
    margin: 0 0 24px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_paymentSelector_60wvq h4 {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-body)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e {
    position: relative;
    padding: 16px 10px;
    height: 100%;
    border-radius: 8px;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid transparent;
    cursor: pointer;
    overflow: hidden
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 {
    display: flex;
    justify-content: space-between
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_icon_sjp\+e {
    margin-left: 6px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_title_87CwY .index-module_paymentType_rBxoj {
    margin-left: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL {
    display: flex;
    align-items: center
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceNumber_6EuVJ {
    color: var(--yq-text-primary);
    font-size: 12px;
    font-weight: 500
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_header_KM0S4 .index-module_price_n7XGL .index-module_priceUnit_xqqPQ {
    color: var(--yq-text-caption);
    font-size: 10px
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_oldMemberPrice_4QJhr {
    display: block;
    text-align: right;
    font-size: 10px;
    color: var(--yq-text-caption);
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e .index-module_desc_5wzpx {
    position: absolute;
    bottom: 16px;
    margin-top: 6px;
    width: 160px;
    font-size: 12px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_active_HZTq5 {
    background-color: rgba(0,185,107,.02);
    border-color: var(--yq-theme)
}

.index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: "";
    background-color: hsla(0,0%,100%,.5);
    cursor: not-allowed
}

html[data-kumuhana=pouli] .index-module_paymentSelector_60wvq .index-module_paymentItem_86B2e.index-module_disable_\+DYkl:before {
    background-color: rgba(0,0,0,.5)
}

.index-module_tooltip_lMvUZ {
    font-size: 14px;
    white-space: nowrap
}

.index-module_tooltip_lMvUZ a {
    margin-left: 8px
}

.BuyModal-module_row_k9bGD {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyModal-module_row_k9bGD h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 {
    color: var(--yq-text-primary)
}

.BuyModal-module_row_k9bGD .BuyModal-module_left_U5aM7 span {
    color: var(--yq-text-caption)
}

.BuyModal-module_row_k9bGD .BuyModal-module_right_-\+dqF {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq {
    display: flex;
    align-items: center
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq svg {
    margin-right: 6px
}

.BuyModal-module_payModal_RkJau .BuyModal-module_channel_aD1hq span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyModal-module_payBtn_JujzZ {
    margin: 12px auto;
    width: 100%
}

.BuyModal-module_promoCode_IT7ha a,.BuyModal-module_promoCode_IT7ha a:hover {
    color: var(--yq-text-primary)
}

.BuyModal-module_tips_fuHYC {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingContainer_tqVEA {
    display: flex;
    justify-content: space-between;
    padding: 32px;
    min-height: 688px;
    background-color: var(--yq-yuque-grey-2);
    background-position: 0 100%;
    background-repeat: no-repeat;
    border-radius: 8px
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*z00vQYi5mvAAAAAAAAAAAAAAARQnAQ);
    background-size: 274px 215px
}

html[data-kumuhana=pouli] .BuyModal-module_pricingContainer_tqVEA.BuyModal-module_pro_J6mfJ {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*4iv1S4HXxg4AAAAAAAAAAAAADvuFAQ/original)
}

.BuyModal-module_pricingContainer_tqVEA.BuyModal-module_enterprise_KQPTt {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*h_IRR6H2EB8AAAAAAAAAAAAADvuFAQ/original);
    background-size: 238px 170px
}

.BuyModal-module_pricingLeft_X4ns6 {
    width: 324px;
    padding-top: 24px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG {
    display: flex;
    align-items: center;
    font-size: 20px;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_payment_jheFG .BuyModal-module_icon_UPf-t {
    margin-left: 8px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentDesc_7e1Uv {
    margin: 4px 0;
    font-size: 20px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL {
    display: inline-block;
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_paymentCaption_sAndL .BuyModal-module_icon_UPf-t {
    margin-left: 0;
    vertical-align: text-top
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe {
    margin-bottom: 40px;
    position: relative;
    padding-left: 48px;
    height: 46px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_iconWarpper_j2vhb {
    position: absolute;
    top: 50%;
    left: 0;
    width: 36px;
    height: 36px;
    transform: translateY(-50%);
    background-color: var(--yq-bg-primary);
    box-shadow: 0 2px 9px 0 rgba(0,0,0,.02);
    border-radius: 6px
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_icon_UPf-t {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_title_aoygt {
    margin-bottom: 4px;
    color: var(--yq-text-primary);
    font-size: 14px;
    font-weight: 400
}

.BuyModal-module_pricingLeft_X4ns6 .BuyModal-module_benefitItem_4iuZe .BuyModal-module_desc_nWmKR {
    color: var(--yq-text-caption);
    font-size: 12px
}

.BuyModal-module_pricingRight_p54lJ {
    position: relative;
    padding: 24px 24px 98px 24px;
    width: 420px;
    min-height: 624px;
    border-radius: 8px;
    background: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .BuyModal-module_pricingRight_p54lJ {
    background: var(--yq-bg-tertiary)
}

.BuyModal-module_avatar_SRhTt {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    background-color: var(--yq-bg-tertiary)
}

.BuyModal-module_termsContainer_i187T span {
    color: var(--yq-text-body)!important
}

.BuyModal-module_termsContainer_i187T .ant-checkbox+span {
    padding-right: 0
}

.BuyModal-module_statsWrapper_ZDvF1 .BuyModal-module_statsDesc_1iKOA {
    margin-top: 12px;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 28px
}

.BuyModal-module_compact_qVAW5 h3 {
    margin-bottom: 4px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_active_SC0g\+ {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyModal-module_channel_aD1hq.BuyModal-module_settle_6sm-p {
    margin-left: 12px
}

.BuyModal-module_channel_aD1hq button[disabled] {
    padding: 4px 12px;
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyModal-module_channel_aD1hq button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.ant-btn.BuyModal-module_channel_aD1hq {
    padding: 4px 12px
}

.BuyModal-module_name_bL3Ia {
    max-width: 230px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BuyModal-module_memberSizeInput_xIYHO {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.BuyModal-module_memberSizeInput_xIYHO .BuyModal-module_errorMsg_ZbqFE {
    position: absolute;
    bottom: -24px;
    max-width: 200px;
    white-space: nowrap;
    color: var(--yq-function-error);
    font-weight: 400;
    font-size: 14px
}

.BuyModal-module_footer_eF3uw {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: calc(100% - 48px);
    transform: translateX(-50%)
}

.BuyMember-module_row_9srYf {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BuyMember-module_row_9srYf h3 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og {
    color: var(--yq-text-primary)
}

.BuyMember-module_row_9srYf .BuyMember-module_left_kE3og span {
    color: var(--yq-text-caption)
}

.BuyMember-module_row_9srYf .BuyMember-module_right_Oni7O {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT {
    display: flex;
    align-items: center
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT svg {
    margin-right: 6px
}

.BuyMember-module_payModal_ID058 .BuyMember-module_channel_oCioT span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_payBtn_2QBqR {
    margin: 12px auto;
    width: 100%
}

.BuyMember-module_promoCode_3q8my a,.BuyMember-module_promoCode_3q8my a:hover {
    color: var(--yq-text-primary)
}

.BuyMember-module_tips_0MaeW {
    margin-top: 4px;
    margin-bottom: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--yq-text-caption)
}

.BuyMember-module_pricingContainer_oSImw h2 {
    margin-bottom: 4px;
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BuyMember-module_expiredDesc_kyKaA {
    margin-bottom: 24px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BuyMember-module_channel_oCioT.BuyMember-module_active_hyrgK {
    background: rgba(0,185,107,.02);
    border: 1px solid var(--yq-yuque-green-6);
    border-radius: 6px
}

.BuyMember-module_channel_oCioT button[disabled] {
    background: var(--yq-yuque-grey-2);
    opacity: .5;
    display: flex;
    align-items: center
}

.BuyMember-module_channel_oCioT button[disabled] span {
    color: var(--yq-text-primary);
    font-weight: 500
}

.BuyMember-module_right_Oni7O>:not(:first-child) {
    margin-left: 12px
}

.Pay-module_row_02i1B {
    padding-bottom: 24px
}

.Pay-module_row_02i1B:nth-child(6) {
    padding-bottom: 0
}

.Pay-module_row_02i1B .Pay-module_left_u3-Jq {
    padding-bottom: 8px
}

.Pay-module_row_02i1B .Pay-module_name_4PPzr {
    font-size: 16px;
    color: var(--yq-text-primary);
    font-weight: 700;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 320px
}

.Pay-module_row_02i1B .Pay-module_nameRow_iKvRZ {
    display: flex
}

.Pay-module_row_02i1B .Pay-module_version_C36aU {
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-8);
    border-radius: 4px;
    font-size: 12px;
    padding: 1px 3px;
    background-color: var(--yq-blue-1)
}

.Pay-module_row_02i1B .Pay-module_version_C36aU.Pay-module_paidVersion_pBnh8 {
    color: var(--yq-yellow-7);
    border-color: var(--yq-yellow-7);
    background-color: var(--yq-yellow-1)
}

.Pay-module_cardList_OD1er {
    display: flex;
    margin-bottom: 24px
}

.Pay-module_memberSize_Snqz8 {
    overflow: hidden;
    opacity: 0;
    height: 0;
    transition: all .3s
}

.Pay-module_memberSize_Snqz8.Pay-module_show_u86Cd {
    opacity: 1;
    height: 81px
}

.Pay-module_totalInfo_YM7Fb {
    height: 45px;
    margin: 4px 0 18px 0;
    flex-wrap: wrap;
    font-weight: 600
}

.Pay-module_footer_MsuSK,.Pay-module_totalInfo_YM7Fb {
    display: flex;
    align-items: center
}

.Pay-module_footer_MsuSK .Pay-module_termsContainer_MMlAf {
    margin-left: 12px;
    display: flex
}

.Pay-module_pricingContainer_hjkUd {
    padding-bottom: 8px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_desc_a1fXI {
    margin-top: -20px;
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_dashline_y-S0k {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 24px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingInfo_NRstM {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingMainTitle_pinlM {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingSubTitle_FlFF9 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_pricingContainer_hjkUd .Pay-module_inputNumber_Uaz26 {
    height: 32px;
    width: 200px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_pricingDesc_SK8fY {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz {
    font-size: 28px;
    font-weight: 600;
    color: var(--yq-text-primary);
    line-height: 45px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_priceTag_ot6Kz.Pay-module_hasDiscount_KLNoP {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz {
    margin-left: 16px;
    color: var(--yq-orange-7);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_discountPrice_HbCLz .Pay-module_discountPriceValue_CUsg2 {
    font-size: 28px;
    font-weight: 600
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWith_vFDxq {
    margin: 8px 0 20px 0
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayIcon_SjV5X {
    width: 24px;
    margin-top: -2px
}

.Pay-module_pricingContainer_hjkUd .Pay-module_payWithAlipayText_r3AWK {
    padding-left: 8px
}

.Pay-module_uploadPackages_VO59r {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    margin-bottom: 32px
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL {
    width: 160px;
    height: 64px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    text-align: center;
    margin-right: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL:nth-child(3n) {
    margin-right: 0
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackageCard_6UXsL.Pay-module_selectedUploadCard_yWd2Y {
    border-color: var(--yq-theme);
    color: var(--yq-theme);
    background-color: rgba(37,184,100,.05)
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadSize_WpwBc {
    font-size: 16px;
    font-weight: 600
}

.Pay-module_uploadPackages_VO59r .Pay-module_uploadPackagePrice_zQPrm {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Pay-module_buyMemberCon_CB8\+C {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32px
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q,.Pay-module_buyMemberCon_CB8\+C .Pay-module_upgradeMember_6duT1 {
    background-color: var(--yq-bg-secondary);
    padding: 16px;
    width: 244px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center
}

.Pay-module_buyMemberCon_CB8\+C .Pay-module_currentMember_4XK3Q .Pay-module_memberValue_l6w8U {
    font-size: 16px;
    font-weight: 600;
    margin: 10px 0
}

.Pay-module_tips_BIW\+T {
    margin-top: 6px;
    color: var(--yq-text-caption);
    width: 500px
}

.Pay-module_discountInfo_Uq4\+K {
    color: var(--yq-text-caption);
    padding-bottom: 8px
}

.Pay-module_promo_sD6ap {
    margin-top: -6px
}

.Pay-module_promo_sD6ap .Pay-module_left_u3-Jq {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 120px
}

.Pay-module_promo_sD6ap .Pay-module_inputNumber_Uaz26 {
    padding-top: 8px;
    padding-bottom: 40px
}

.Qrcode-module_info_XwHqZ {
    flex: auto;
    max-width: 332px
}

.Qrcode-module_infoTitle_nNa5e {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500
}

.Qrcode-module_subInfoTip_DYh05 {
    color: var(--yq-text-body);
    margin-top: 10px
}

.Qrcode-module_infoTip_wp2zv>span {
    margin-right: 28px
}

@media only screen and (max-width: 575px) {
    .Qrcode-module_infoTip_wp2zv>span {
        display:block
    }
}

.Qrcode-module_desc_T1-nf {
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi {
    padding-bottom: 8px
}

.Qrcode-module_pricingContainer_2\+hyi h3 {
    font-size: 14px;
    font-weight: 700;
    color: var(--yq-text-body)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_dashline_n20jZ {
    border-top: 1px dashed var(--yq-border-primary);
    margin: 1.5rem 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingInfo_Pe1Ai {
    position: relative;
    margin: 8px 0 20px 0;
    border-radius: 4px;
    padding: 2px 16px 10px 16px;
    background-color: var(--yq-bg-tertiary)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingMainTitle_D0fu0 {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingSubTitle_3SVt2 {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_inputNumber_a6u0P .Qrcode-module_select_2Topv {
    width: 140px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_pricingDesc_aTeur {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 20px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_priceTag_gSsWI {
    font-size: 24px;
    font-weight: 700;
    color: var(--yq-text-primary);
    padding-right: 10px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWith_Orohw {
    margin: 8px 0 20px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_totalInfo_-\+rtG {
    margin: 4px 0 8px 0
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayIcon_Hoi7K {
    width: 24px;
    margin-top: -2px
}

.Qrcode-module_pricingContainer_2\+hyi .Qrcode-module_payWithAlipayText_wbuGd {
    padding-left: 8px
}

.Qrcode-module_processContainer_5BqWV {
    text-align: center;
    padding: 10px 0
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processImg_MgbCi {
    width: 120px
}

.Qrcode-module_processContainer_5BqWV .Qrcode-module_processInfo_ST\+BP {
    margin-top: 12px
}

.Qrcode-module_termsContainer_Uqaqj {
    margin-bottom: 12px;
    display: flex
}

.Qrcode-module_bindingContainer_b3OnQ {
    padding: 24px 0;
    text-align: center
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingImg_sEWSW {
    width: 80px
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingInfo_qhBjF {
    color: var(--yq-text-primary);
    margin: 24px 0
}

.Qrcode-module_bindingContainer_b3OnQ .Qrcode-module_bindingFooter_7PjRS .Qrcode-module_bindingVerify_CgNHw {
    margin-right: 8px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi {
    position: relative
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 {
    background: var(--yq-bg-primary);
    width: 50px;
    height: 50px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 4px;
    margin-left: -25px;
    margin-top: -25px
}

.Qrcode-module_qrcodeImageCon_yy\+Gi .Qrcode-module_alipayIconInQrCode_5Ng19 img {
    width: 100%
}

@media only screen and (max-width: 768px) {
    .Qrcode-module_qrcodeImageCon_yy\+Gi .alipayIconInQrCode {
        width:36px;
        height: 36px;
        margin-left: -18px;
        margin-top: -18px
    }
}

.Qrcode-module_successContainer_RvR5s {
    color: var(--yq-text-body);
    border-radius: 4px;
    overflow: hidden
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfoCon_Yt35q {
    padding: 10px 40px 30px 40px
}

.Qrcode-module_successContainer_RvR5s .Qrcode-module_successInfo_w7k6O {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.Qrcode-module_qrContainer_K9uEQ {
    margin-bottom: 20px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeImg_GAmpl {
    width: 72px
}

.Qrcode-module_qrContainer_K9uEQ .Qrcode-module_qrcodeInfo_8NXdM {
    font-size: 14px;
    font-weight: 400;
    padding: 24px 0 0 30px;
    color: var(--yq-text-body);
    line-height: 1.8
}

.Qrcode-module_qrcodeContainer_c79eX {
    min-height: 444px;
    text-align: center
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_qrcodeImage_UEIyZ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 1px;
    width: 55%
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc1_ArZR9 {
    color: var(--yq-text-body);
    font-size: 14px;
    margin-top: 24px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_desc2_E5sDl {
    margin-top: 10px
}

.Qrcode-module_qrcodeContainer_c79eX .Qrcode-module_link_ZEjlM {
    margin-top: 8px
}

.Qrcode-module_receiptTypeTitle_jIYOl {
    margin-bottom: 10px
}

.Qrcode-module_receiptTypeTitleText_8q0XQ {
    margin-bottom: 8px
}

.Qrcode-module_applyReceiptButton_Wfqmq {
    margin-left: 10px
}

.Qrcode-module_contactTips_ETe9S {
    margin-bottom: 10px
}

.Qrcode-module_buttonCon_Jic-k {
    margin-top: 8px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleDesc_OOQby {
    font-size: 16px
}

.Qrcode-module_qrcodeTitleCon_-LJr2 .Qrcode-module_qrcodeTitleValue_PBwE\+ {
    color: var(--yq-orange-6);
    font-size: 28px;
    margin: 5px 10px;
    font-weight: 600
}

.PayModal-module_noLineModal_r4djn .ant-modal-header {
    border-bottom: 0
}

.PayModal-module_warnContent_TTDov {
    margin-top: 20px;
    margin-bottom: 24px
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-body {
    padding: 0
}

.PayModal-module_payModal_1VmRL.PayModal-module_largeModal_reIw0 .ant-modal-close-x {
    width: 36px;
    height: 36px;
    line-height: 36px
}

.ProcessingModal-module_processContainer_dOZ9t {
    text-align: center;
    padding: 10px 0
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processImg_Fl\+7o {
    width: 120px
}

.ProcessingModal-module_processContainer_dOZ9t .ProcessingModal-module_processInfo_VPh0u {
    margin-top: 12px
}

.paid-success-modal .ant-modal-header {
    border-bottom: none;
    padding-bottom: 8px
}

.paid-success-modal .ant-modal-body {
    padding: 0
}

.paid-success-modal .ant-modal-body img {
    width: 100%
}

.paid-success-modal .ant-col {
    padding-top: 10px
}

.paid-success-modal .ant-col-18 {
    font-weight: 600
}

.PaidSuccessModal-module_successModalContainer_gb6Iw {
    color: var(--yq-text-body);
    border-radius: 8px;
    overflow: hidden
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoCon_aLoy0 {
    padding: 20px 24px 30px 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfo_TQeHl {
    font-size: 14px;
    border-top: 1px solid var(--yq-border-primary);
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 20px 0 10px 0;
    padding-bottom: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalInfoTitle_fmm0I {
    font-size: 28px;
    margin-top: 24px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns {
    margin-top: 32px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_applyReceiptButton_BrptN {
    margin-left: 12px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalButtonCon_BP8Ns .PaidSuccessModal-module_checkUploadButton_n0Sct {
    margin-left: 12px;
    margin-top: 1px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalApplyReceiptButton_hbmRn {
    margin-left: 10px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalSubTitle_Hq8oy {
    font-size: 14px;
    font-weight: 600;
    margin: 16px 0 16px;
    color: var(--yq-black)
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalItem_EBnTl {
    font-size: 14px;
    color: var(--yq-text-body);
    margin: 8px 0
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalBlank_3ENdm {
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 16px
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy {
    padding-right: 32px;
    padding-top: 64px;
    font-size: 12px;
    color: var(--yq-text-disable);
    position: absolute;
    bottom: 36px;
    right: 0;
    text-align: right
}

.PaidSuccessModal-module_successModalContainer_gb6Iw .PaidSuccessModal-module_successModalDingQRCon_mfDmy img {
    width: 120px!important;
    height: 120px;
    margin-bottom: 8px
}

.receipt-module_contactInfoTitle_\+WTvl {
    border-bottom: 1px solid var(--yq-border-primary);
    margin: 10px 0
}

.receipt-module_receiptButtons_tBTuB {
    margin-top: 8px
}

.receipt-module_cancelButton_1xXKN {
    margin-left: 10px
}

.ReceiptModal-module_contactTips_f0fAJ,.ReceiptModal-module_receiptTypeTitle_mZi8G {
    margin-bottom: 10px
}

.ReceiptModal-module_receiptTypeTitleText_QQBhY {
    margin-bottom: 8px
}

.ReceiptModal-module_receiptDesc_fY2bw {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    color: var(--yq-text-caption)
}

@keyframes SubAccountInfoModal-module_loadingCircle_mPmQ5 {
    to {
        transform: rotate(1turn)
    }
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p:first-child {
    border-bottom: 1px dashed var(--yq-border-primary);
    padding-bottom: 16px;
    margin-bottom: 32px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldTitle_tgwmv {
    display: inline-block;
    font-weight: 600;
    min-width: 80px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoField_IMZ6p p {
    margin-bottom: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_infoFieldPaymentAlert_UOLk7 {
    margin-bottom: 20px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e {
    margin-top: 16px
}

.SubAccountInfoModal-module_wrapper_P6mlj .SubAccountInfoModal-module_buttons_rfW-e .SubAccountInfoModal-module_btn_WuNy4 {
    margin-right: 16px
}

.SubAccountInfoModal-module_loading_sNKQL {
    padding: 32px 0;
    text-align: center
}

.SubAccountInfoModal-module_loading_sNKQL .SubAccountInfoModal-module_loadingIcon_M7IJr {
    animation: SubAccountInfoModal-module_loadingCircle_mPmQ5 1s linear infinite
}

.SubAccountInfoModal-module_loading_sNKQL .larkui-icon {
    font-size: 24px
}

.SubAccountInfoModal-module_loadingDesc_QHGbP {
    margin-top: 16px
}

.index-module_buyButton_pN7y0.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_buyButton_pN7y0a.ant-btn span {
    line-height: 1
}

.index-module_upgradeButton_nJqrZ.ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1
}

.index-module_upgradeButton_nJqrZa.ant-btn span {
    line-height: 1
}

.index-module_orgVersionLabel_egPu3 {
    border-width: 1px;
    border-radius: 4px;
    border-style: solid;
    padding: 0 4px;
    margin-left: 16px
}

.index-module_tip_rQx-e {
    margin-top: 2px;
    margin-bottom: 2px;
    display: flex;
    justify-content: space-between
}

.index-module_tip_rQx-e a:before {
    position: relative
}

.index-module_tip_rQx-e span span {
    margin-left: 12px
}

.index-module_tip_rQx-e .index-module_payment_pQExj {
    color: var(--yq-yuque-grey-8)
}

.index-module_tip_rQx-e .index-module_paymentLink_GbvgZ {
    color: var(--yq-blue-6);
    line-height: 60px
}

.index-module_paymentGuideWrapper_2jg39 {
    max-width: 584px;
    margin-left: auto;
    margin-right: auto;
    padding: 24px;
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_thumb_Lw5zW {
    display: block;
    width: 148px;
    height: 120px;
    margin: 0 auto 16px;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 100%
}

.index-module_paymentGuideWrapper_2jg39 .index-module_title_EykNL {
    text-align: center;
    margin-bottom: 32px;
    font-size: 16px;
    font-weight: 700
}

.index-module_paymentGuideWrapper_2jg39 .index-module_body_\+1-cr {
    margin: 12px auto 32px auto;
    max-width: 400px;
    color: var(--yq-text-body);
    text-align: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_link_zDdoa {
    margin-right: 8px;
    color: var(--yq-text-body)
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB {
    display: flex;
    align-items: center;
    justify-content: end
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_horizontal_rYkfB .index-module_btnTryout_5GVmK {
    margin-right: 8px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_btnTryout_5GVmK {
    margin-bottom: 12px;
    width: 148px;
    height: 32px
}

.index-module_paymentGuideWrapper_2jg39 .index-module_actions_LRgdL.index-module_vertical_K\+cm2 .index-module_link_zDdoa {
    width: 148px;
    text-align: center
}

.index-module_actions_LRgdL .index-module_btn_vRXZD {
    width: 160px;
    height: 32px
}

.index-module_premiumFeaturesLabel_q\+uFd {
    width: 86px;
    height: 37px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*pIrCQLn06McAAAAAAAAAAAAAARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_alignCenter_h648T {
    display: flex;
    align-items: center;
    position: relative
}

.index-module_orgExpiredTip_k7iLg {
    font-size: 14px;
    color: var(--yq-yuque-grey-9)
}

.index-module_orgExpiredTipOpt_QDhkZ,.index-module_orgExpiredTipText_gQuZw {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    width: 140px
}

.index-module_orgExpiredTipText_gQuZw {
    white-space: nowrap
}

.index-module_orgExpiredTipText_gQuZw span {
    color: var(--yq-yuque-grey-9)
}

.index-module_payflowTitle_LM3aR {
    font-size: 14px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px
}

.index-module_freeWrapper_BlI2K {
    display: flex;
    justify-content: space-between
}

.index-module_payflowLink_Wt3fM {
    margin-left: 0;
    display: inline-block
}

.index-module_isExpired_UgOgs {
    border-bottom: 1px solid var(--yq-yuque-grey-4);
    padding-bottom: 10px
}

.index-module_freeTips_ybaQ- {
    display: flex;
    justify-content: space-between
}

.placeholder-avatar {
    display: inline-block;
    border-radius: 50%;
    color: var(--yq-white);
    text-align: center
}

.index-module_btnNewOrg_V6nKz {
    display: flex;
    align-items: flex-start;
    line-height: 1.35;
    padding-left: 43px!important
}

.index-module_btnNewOrg_V6nKz h6 {
    position: relative!important
}

.index-module_btnNewOrg_V6nKz .icon-svg {
    position: absolute;
    left: -28px;
    top: 1px;
    color: var(--yq-yuque-grey-9)
}

.index-module_btnNewOrg_V6nKz .index-module_orgAddText_98X0I {
    margin-left: 11px
}

.index-module_btnNewOrg_V6nKz .index-module_tag_DSBkQ {
    display: inline-flex;
    margin-left: 8px;
    padding: 0 6px;
    color: var(--yq-yuque-green-7);
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    background: var(--yq-yuque-green-1);
    border-radius: 3px
}

.index-module_upgrade_qL2nR .ant-btn-link {
    padding: 0;
    height: auto
}

.index-module_wrapper_ulVDd .ant-modal-body {
    padding: 0!important
}

.index-module_wrapper_ulVDd .anticon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-info-circle,.index-module_wrapper_ulVDd .larkui-icon-information {
    display: none
}

.index-module_wrapper_ulVDd .ant-modal-confirm-content {
    width: 480px;
    margin-top: 0
}

.index-module_wrapper_ulVDd .ant-modal-confirm-btns {
    display: none
}

.index-module_wrapper_ulVDd .index-module_content_SO1HL {
    margin: 28px 0;
    padding: 0 24px
}

.index-module_wrapper_ulVDd .index-module_title_SVPzd {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: var(--yq-text-primary)
}

.index-module_wrapper_ulVDd .index-module_desc_MlCQ9 {
    margin-top: 16px;
    margin-bottom: 32px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.index-module_wrapper_ulVDd .index-module_img_SBaEY {
    width: 480px
}

.index-module_trialButton_tH3iD {
    margin-left: 10px
}

.index-module_footer_cL9eu {
    display: flex;
    justify-content: flex-end
}

.index-module_footer_cL9eu .index-module_btn_XNqZC {
    margin-left: 10px
}

.OrgUserInfo-module_departmentInfo_3udmp {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-light)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_cardHeader_502Md {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq {
    margin: 0 24px 0 0
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: center;
    margin-right: 24px;
    line-height: 26px;
    color: var(--yq-text-body)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq .OrgUserInfo-module_icon_okrek,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz .OrgUserInfo-module_icon_okrek {
    margin-right: 8px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_fullWidth_zv0Qo {
    width: 100%;
    line-height: 32px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6:nth-child(n+2) {
    margin-top: 16px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97 {
    display: inline-block;
    margin-left: 4px;
    width: 100%;
    font-weight: 500
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97.OrgUserInfo-module_h5_M\+nrk {
    margin-left: 8px;
    font-weight: 400;
    font-size: 16px
}

.OrgUserInfo-module_divider_igZMz {
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background-color: var(--yq-border-light)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or {
    padding: 16px;
    width: 280px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 8px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 {
    position: relative;
    padding-left: 56px;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_avatar_iMqQ\+ {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_wrapper_cpsZr {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_nameWrapper_u01vS {
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_name_9rYrf {
    max-width: 100%;
    color: var(--yq-text-primary);
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_tag_dWOJO {
    margin-left: 4px;
    padding: 0 4px;
    background-color: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    color: var(--yq-text-body);
    font-size: 12px;
    height: 20px;
    line-height: 20px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_description_4Q94H {
    margin-top: 4px;
    font-size: 14px;
    line-height: 18px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_icon_okrek {
    color: var(--yq-icon-secondary)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: flex-start;
    width: 100%
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6 span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    margin-left: 8px;
    color: var(--yq-text-body);
    font-size: 14px;
    line-height: 22px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    margin-top: 12px;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6:nth-child(n+3) {
    margin-top: 12px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_icon_okrek {
    transform: translateY(4px)
}

.index-module_userCard_NOTbr {
    width: 330px
}

.index-module_userCard_NOTbr .index-module_body_20bgh {
    display: flex;
    padding: 20px 18px 20px 24px
}

.index-module_userCard_NOTbr img.index-module_avatar_OBHFJ {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    flex-shrink: 0
}

.index-module_userCard_NOTbr .index-module_userInfos_B16Pa {
    margin-left: 20px;
    width: 220px
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    word-break: break-all;
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_name_QeZm4 {
    color: var(--yq-text-primary)
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_dingtalk_kYXBj {
    height: 22px;
    vertical-align: middle;
    display: inline-block
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_badge_lsJBP {
    top: -2px;
    height: 22px
}

.index-module_userCard_NOTbr .index-module_dingding_l7pi0 {
    margin-left: 4px;
    width: 18px;
    height: 18px
}

.index-module_userCard_NOTbr .index-module_infoWithBg_JGcdP {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    color: var(--yq-text-body);
    background-color: var(--yq-bg-tertiary);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 400;
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_signature_aQ\+Wz {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-body);
    margin-top: 8px;
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_infoList_Y58li {
    margin-top: 10px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 {
    display: flex;
    margin-bottom: 8px;
    min-height: 20px;
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7:last-child {
    margin-bottom: 0
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 .index-module_icon_MGwU8 {
    margin-top: 2px;
    margin-right: 8px;
    color: var(--yq-icon-primary)
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 span {
    color: var(--yq-text-primary);
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 a {
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_footer_FNHCP {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--yq-border-light);
    padding: 12px 18px 12px 24px
}

.index-module_userCard_NOTbr .index-module_follow_1HgLb {
    display: flex
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j {
    margin-right: 24px;
    color: var(--yq-text-disable);
    margin-top: 6px
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j:last-child {
    margin-right: 0
}

.index-module_userCard_NOTbr .index-module_number_NahUE {
    margin-left: 5px;
    color: var(--yq-text-primary);
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_userLink_i0XYI {
    float: right
}

.index-module_userCard_NOTbr .index-module_skeleton_Z4M4v {
    width: 100%;
    height: 50px;
    margin-top: 16px;
    overflow: hidden
}

.index-module_userCard_NOTbr .index-module_infoDetail_dKXCO {
    word-break: break-all
}

.index-module_popover_Xyidp {
    display: inline-block
}

.index-module_overlay_A0ouW .ant-popover-inner-content {
    padding: 0
}

.index-module_wrap_iKZPE {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body)
}

.index-module_wrap_iKZPE:hover .index-module_close_mZbMN {
    display: flex
}

.index-module_icon_gxtpV {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: none
}

.index-module_close_mZbMN {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_closeIcon_kM1zW {
    font-size: 16px
}

.doc-draft-tip {
    font-weight: 400
}

.doc-draft-tip-content .update-info {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.doc-draft-tip-content .update-info a {
    color: var(--yq-text-body)
}

.ant-tag.doc-template-tag {
    margin: 0 0 0 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.doc-title {
    font-size: 14px;
    line-height: 21px;
    text-overflow: ellipsis;
    color: var(--yq-text-body);
    font-family: Chinese Quote,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji
}

.doc-title-draft {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 4px;
    font-weight: 400
}

.doc-icon {
    margin-right: 4px
}

.doc-access-scope {
    margin-left: 8px
}

.doc-belong,.doc-belong a {
    color: var(--yq-text-caption)
}

.doc-belong a {
    margin: 0 4px
}

.doc-belong a:first-child {
    margin-left: 0
}

.doc-contributors,.doc-contributors span a {
    color: var(--yq-text-caption)
}

.index-module_articleTitle_VJTLJ {
    word-break: break-word
}

.index-module_popover_nfMC3 {
    display: inline
}

.index-module_belongMenu_2QmLB {
    outline: none;
    cursor: pointer
}

.index-module_belongMenu_2QmLB .larkui-icon {
    display: inline-block;
    font-size: 12px;
    margin-left: 4px
}

.index-module_belongText_TkCAl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu {
    min-width: 188px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item {
    display: flex;
    align-items: center
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .larkui-icon-check-outlined {
    margin-right: 6px;
    visibility: hidden;
    font-size: 16px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 280px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>.larkui-icon,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>span {
    display: inline-block;
    vertical-align: middle
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item:hover {
    background-color: var(--yq-bg-tertiary);
    font-weight: 400
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active .larkui-icon-check-outlined {
    visibility: visible
}

@media only screen and (max-width: 575px) {
    .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
        max-width:140px
    }
}

.index-module_privacy_QkaFB {
    display: inline-block;
    padding: 3px 5px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    color: var(--yq-text-body);
    line-height: 14px;
    font-size: 10px;
    font-weight: 400;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
    margin: 0 6px 0
}

.lakex-editor-wrapper {
    position: relative;
    height: calc(100vh - 56px)
}

.lakex-editor-wrapper .lakex-editor-container {
    height: 100%
}
