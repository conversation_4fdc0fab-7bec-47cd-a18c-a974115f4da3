<template>
  <span class="inputItem">
    <span class="txtItem">
      <!-- <span v-if="refType !== 'EnumMultiple'&&(refType === 'Enum' || enumDictName)">{{refType}}</span> -->
      <enum-select
        v-if="refType !== 'EnumMultiple' && (refType === 'Enum' || enumDictName)"
        :value="value ? value : undefined"
        :disabled="disabled"
        :enumDictName="enumDictName"
        :pos="pos"
        @onChange="onChange"
        :titleStr="titleStr"
        :refListData="refListData"
      />
      <enum-multiple
        v-else-if="refType === 'EnumMultiple' && isTable"
        class="list-enum-multiple"
        :value="value ? value.split(',') : undefined"
        :disabled="disabled"
        :enumDictName="enumDictName"
        :pos="pos"
        @onChange="(value) => handleChange(value.join(','))"
        :titleStr="titleStr"
        :refListData="refListData"
      />
      <enum-select
        v-else-if="refType === 'EnumMultiple' && !isTable"
        :value="value ? value : undefined"
        :disabled="disabled"
        :enumDictName="enumDictName"
        :pos="pos"
        @onChange="onChange"
        :titleStr="titleStr"
        :refListData="refListData"
      />
      <str-input
        v-else-if="refType === 'String'"
        :disabled="disabled"
        :value="value"
        :inputtype="inputtype"
        @onChange="onChange"
        :titleStr="titleStr"
      />
      <num-input
        v-else-if="refType === 'Number'"
        :disabled="disabled"
        :value="value"
        @onChange="onChange"
        :titleStr="titleStr"
      />
      <date-input
        v-else-if="refType === 'Date'"
        :disabled="disabled"
        :value="value"
        @onChange="onChange"
        :titleStr="titleStr"
      />
    </span>
  </span>
</template>

<script setup>
import { getRefValueType } from "@/components/ruleEditCom/utils/inputItmeUtil";
import EnumSelect from "./enumSelect.vue";
import StrInput from "./strInput.vue";
import NumInput from "./numInput.vue";
import DateInput from "./dateInput.vue";
import EnumMultiple from "./enumMultiple.vue";

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  inputtype: {
    type: String,
    default: "",
  },
  variableData: {
    type: Object,
    default: () => ({}),
  },
  locked: {
    type: Boolean,
    default: false,
  },
  enumDictName: {
    type: String,
    default: "",
  },
  methodFilterType: {
    type: String,
    default: "",
  },
  titleStr: {
    type: String,
    default: "",
  },
  refListData: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits(['onChange']);

// 计算属性
const valueType = computed(() => props.variableData.valueType);
const variableType = computed(() => props.variableData.variableType);
const refType = computed(() => getRefValueType(props.variableData.valueType, props.variableData.enumDictName));
const disabled = computed(() => props.locked);
const value = computed(() => props.variableData.value);


// 处理变化的方法
const handleChange = (value) => {
  let val = "";
  if (typeof value === "object") {
    val = value.target.value;
  } else {
    val = value;
  }
  if (val && typeof val === "string") {
    val = val.split(/[,，;；]/g).toString();
  }
  if (typeof val === "string" || !isNaN(val)) {
    const newVariable = {
      value: val,
      valueType: valueType.value,
      variableType: variableType.value,
    };
    if (props.enumDictName) {
      newVariable.enumDictName = props.enumDictName;
    }
    if (valueType.value === "Boolean") {
      newVariable.domain = "boolean";
    }
    if (refType.value === "Number") {
      if (props.methodFilterType && props.methodFilterType !== "null") {
        newVariable.valueType = props.methodFilterType;
      } else {
        if (val && val.toString() && val.toString().indexOf(".") !== -1) {
          if (
            props.methodFilterType === "BigDecimal" ||
            props.methodFilterType === "Double" ||
            props.methodFilterType === "Float"
          ) {
            newVariable.valueType = props.methodFilterType;
          } else {
            newVariable.valueType = "Double";
          }
        } else {
          newVariable.valueType = "Integer";
        }
      }
    }
    emit("onChange", props.pos, newVariable, newVariable.valueType);
  }
};

// 变量变化时触发的方法
const onChange = (val) => {
  if (typeof val === "string" || !isNaN(val)) {
    const newVariable = {
      value: val,
      valueType: valueType.value,
      variableType: variableType.value,
    };
    if (props.enumDictName) {
      newVariable.enumDictName = props.enumDictName;
    }
    if (valueType.value === "Boolean") {
      newVariable.domain = "boolean";
    }
    if (refType.value === "EnumMultiple" && !props.isTable) {
      newVariable.valueType = "string";
    }
    if (refType.value === "Number") {
      if (props.methodFilterType && props.methodFilterType !== "null") {
        newVariable.valueType = props.methodFilterType;
      } else {
        if (val && val.toString() && val.toString().indexOf(".") !== -1) {
          if (
            props.methodFilterType === "BigDecimal" ||
            props.methodFilterType === "Double" ||
            props.methodFilterType === "Float"
          ) {
            newVariable.valueType = props.methodFilterType;
          } else {
            newVariable.valueType = "Double";
          }
        } else {
          newVariable.valueType = "Integer";
        }
      }
    }
    emit("onChange", props.pos, newVariable, newVariable.valueType);
  }
};
</script>

<style scoped lang="scss">
.inputItem {
  .txtItem {
    display: inline-block;
  }
}
</style>
