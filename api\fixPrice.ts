import request from '@/utils/request'

// 根据发布环境获取搜索字段
export function getSearchDictionaryByEnvironment(params) {
  return request({
    url: '/erule/demand2/fixPrice/getSearchDictionaryByEnvironment',
    method: 'get',
    params
  })
}

// 获取定价目标上下限
export function getDictionary(params) {
  return request({
    url: '/erule/demand2/fixPrice/getDictionary',
    method: 'get',
    params
  })
}

// 保存定价目标参数
export function saveFixPrice(params) {
  return request({
    url: '/erule/demand2/fixPrice/saveFixPrice?inputStr=' + params,
    method: 'get'
  })
}
