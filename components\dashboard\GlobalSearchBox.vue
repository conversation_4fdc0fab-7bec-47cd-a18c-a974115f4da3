<!-- 全局搜索框 -->

<script setup lang="ts">
import { globalSearch } from '@/api/dashboardApi'
import { debounce } from 'lodash'

interface SearchResult {
  uuid: string
  engUuid: string
  folderUuid: string
  ruleName: string
  packageNameAll?: string
  type?: string
}

const searchValue = ref('')
const searchResults = ref<SearchResult[]>([])
const loading = ref(false)
const isSearchModalShow = ref(false)
const hoveredItemId = ref<string | null>(null)

//搜索类型设置
const { ruleTypes } = useRuleTypes()
const ruleType = ref('')

//搜索类型改变监听
const changeRuleType = () => {
  handleSearch(searchValue.value,false);
}
// 使用防抖处理搜索
const handleSearch = debounce(async (value: string | number | unknown,type) => {
  const searchStr = String(value)
  //type为false时为搜索类型改变，支持空字符串搜索
  if (!searchStr.trim() && type) {
    searchResults.value = []
    return
  }

  loading.value = true
  try {
    const res = await globalSearch({ ruleName: searchStr,type:ruleType.value })
    searchResults.value = res.data === null ? [] : res.data
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
  } finally {
    loading.value = false
  }
}, 300)

// 显示搜索对话框
const showSearchModal = () => {
  isSearchModalShow.value = true
  nextTick(() => {
    const searchInput = document.querySelector('.index-module_searchModalWrapper_vtDAq .ant-input') as HTMLInputElement
    searchInput?.focus()
    searchValue.value = '';
    searchResults.value = [];
    ruleType.value = '';
    loading.value = false;
  })
}

// 隐藏搜索对话框
const hideSearchModal = () => {
  isSearchModalShow.value = false;
  searchValue.value = '';
  searchResults.value = [];
  ruleType.value = '';
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key.toLowerCase() === 'j') {
    event.preventDefault()
    showSearchModal()
  }
}

// 实现在新标签页打开链接的功能
const navigateTo = (url: string) => {
  // 使用window.open在新标签页打开链接
  window.open(url, '_blank')
}

const handleMouseEnter = (uuid: string) => {
  hoveredItemId.value = uuid
}

const handleMouseLeave = () => {
  hoveredItemId.value = null
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<template>
  <div>
    <div class="index-module_searchContainer_7EKIH" @click="showSearchModal" style="width: 100%">
      <span class="ant-input-affix-wrapper ant-input-affix-wrapper-disabled larkui-input index-module_search_GTiOp">
        <span class="ant-input-prefix">
          <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
            class="larkui-icon larkui-icon-help-search">
            <path
              d="M114 20c51.362 0 93 41.638 93 93 0 21.782-7.489 41.816-20.032 57.666l45.82 46.277c4.275 4.317 4.24 11.282-.077 15.556-4.317 4.275-11.281 4.24-15.556-.077l-45.774-46.23C155.576 198.602 135.652 206 114 206c-51.362 0-93-41.638-93-93s41.638-93 93-93Zm0 20c-40.317 0-73 32.683-73 73s32.683 73 73 73 73-32.683 73-73-32.683-73-73-73Z"
              fill="currentColor" fill-rule="nonzero">
            </path>
          </svg>
        </span>
        <input autocomplete="false" class="ant-input ant-input-disabled" disabled placeholder="搜索" type="text" value="">
        <span class="ant-input-suffix">Ctrl J</span>
      </span>
    </div>
  </div>

  <!-- 搜索对话框 -->
  <a-modal v-model:open="isSearchModalShow" v-if="isSearchModalShow" title="" :closable="false" :footer="null" centered width="720px"
    wrap-class-name="index-module_searchModalWrapper_vtDAq">
    <button type="button" aria-label="Close" class="ant-modal-close" @click="hideSearchModal">
      <span class="ant-modal-close-x">
        <span role="img" aria-label="close" class="larkui-icon larkui-icon-close ant-modal-close-icon">
          <svg viewBox="64 64 896 896" focusable="false" data-icon="close" width="1em" height="1em" fill="currentColor"
            aria-hidden="true">
            <path
              d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7-287.7c-.04-.04-.06-.05-.09-.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z">
            </path>
          </svg>
        </span>
      </span>
    </button>
    <div class="ant-modal-body">
      <div class="index-module_modalSearch_ypoIP">
        <span
          class="ant-input-affix-wrapper ant-input-affix-wrapper-lg ant-input-affix-wrapper-borderless larkui-input ant-dropdown-trigger index-module_searchInput_MvuM1 ant-dropdown-open larkui-dropdown-trigger" style="padding-bottom: 5px">
          <span class="ant-input-prefix">
            <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
              class="larkui-icon larkui-icon-help-search icon-svg JumpMenu-module_jumpIconButton_PYy2Z index-module_size_wVASz"
              style="width: 18px; min-width: 18px; height: 18px;">
              <path
                d="M114 20c51.362 0 93 41.638 93 93 0 21.782-7.489 41.816-20.032 57.666l45.82 46.277c4.275 4.317 4.24 11.282-.077 15.556-4.317 4.275-11.281 4.24-15.556-.077l-45.774-46.23C155.576 198.602 135.652 206 114 206c-51.362 0-93-41.638-93-93s41.638-93 93-93Zm0 20c-40.317 0-73 32.683-73 73s32.683 73 73 73 73-32.683 73-73-32.683-73-73-73Z"
                fill="currentColor" fill-rule="nonzero"></path>
            </svg>
          </span>
          <input autocomplete="nope" class="ant-input ant-input-lg ant-input-borderless" data-searchsence="modal"
            data-testid="jump:search:input" maxlength="250" placeholder="请输入规则标题关键词进行搜索" type="text"
            v-model="searchValue" @keydown.enter="handleSearch(searchValue,true)">
          <a-button @click="handleSearch(searchValue,true)" type="primary">搜 索</a-button>
        </span>

        <div style="position: absolute; top: 0px; left: 0px; width: 100%;">
          <!-- 添加分割线 -->
          <div class="search-divider"></div>

          <div style="margin-left: 15px;margin-bottom: 5px">
            <b>规则类型：</b> <a-radio-group v-model:value="ruleType" placeholder="请选择"  @change="changeRuleType">
              <a-radio-button value="" style="margin-right: 20px; ">全部类型</a-radio-button>
              <a-radio-button style="margin-right: 20px;" v-for="item in ruleTypes" :key="item.code"
                               :value="item.code">{{item.name}}</a-radio-button>
            </a-radio-group>
          </div>
          <div v-if="loading"  class="search-empty-container" style="margin-bottom: 10px">
            <a-spin size="large" />
          </div>
          <div v-else>
            <div
              class="ant-dropdown index-module_dropdown_SVXx9 search-dropdown larkui-dropdown ant-dropdown-placement-topLeft"
              style="position: relative; min-width: 720px; left: 0px; top: -999px;">
              <div class="JumpMenu-module_jumpMenu_Fr5h9" style="margin-bottom: 10px">
                <a-empty v-show="searchResults.length === 0" class="search-empty-container">
                  <template #description>
                    <div style="margin-top: 30px;">
                      <IconSearchEmpty/>
                    </div>
                  </template>
                  <div style="font-size: var(--font-size-body-2,14px);margin-bottom: 30px;color:var(--color-text1-2,#6e6e6e)">
                    <span>暂无搜索结果</span>
                  </div>
                </a-empty>
                <a-menu mode="vertical" class="JumpMenu-module_jumpMenuList_OJGJg search-results-container" v-show="searchResults.length>0">
                  <a-menu-item disabled class="JumpMenu-module_menu_m-ETp JumpMenu-module_title_XgpJ5"
                    key="search-result">
                    <span>搜索结果</span>
                  </a-menu-item>

                  <div>
                    <a-menu-item v-for="result in searchResults" :key="result.uuid"
                                 class="JumpMenu-module_menu_m-ETp JumpMenu-module_menuActive_o-LPE"
                                 :class="{ 'ant-menu-item-selected': hoveredItemId === result.uuid }"
                                 @mouseenter="handleMouseEnter(result.uuid)"
                                 @mouseleave="handleMouseLeave"
                                 @click="navigateTo(`/ruleBase-${result.engUuid}/rulePackage-${result.folderUuid || 'all'}/ruleContent-${result.uuid}?backFlag=dashboard`)">
                      <div class="JumpMenu-module_jumpItemWrapper_vfjfO">
                        <!--<svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                          class="larkui-icon larkui-icon-doc-type-default icon-svg JumpMenu-module_jumpItemIcon_bOWlR index-module_size_wVASz"
                          style="width: 18px; min-width: 18px; height: 18px;">
                          <g fill="none" fill-rule="evenodd">
                            <path
                              d="M4.75 1.267h10.5a2 2 0 0 1 2 2v13.5a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-13.5a2 2 0 0 1 2-2Z"
                              stroke="#3471AF" fill="#FFF"></path>
                            <path
                              d="M6.3 4.5h7.4a.8.8 0 0 1 .8.8v1.9a.8.8 0 0 1-.8.8H6.3a.8.8 0 0 1-.8-.8V5.3a.8.8 0 0 1 .8-.8Z"
                              fill="#3B8EE3"></path>
                            <path
                              d="M14 10.75a.5.5 0 0 1 .09.992l-.09.008H6a.5.5 0 0 1-.09-.992L6 10.75h8Zm-3 3a.5.5 0 0 1 .09.992l-.09.008H6a.5.5 0 0 1-.09-.992L6 13.75h5Z"
                              fill="#9DC6F1" fill-rule="nonzero"></path>
                          </g>
                        </svg>-->
                        <div v-for="item in ruleTypes">
                          <component :is="item.icon" :size="18" v-if="item.code === result.type"
                                     class="HeadNewButton-module_iconContainer_HmX2B" />
                        </div>
                        <span class="JumpMenu-module_jumpItemLabel_OPbMQ">{{ result.ruleName }}</span>
                        <span class="JumpMenu-module_jumpItemTime_inSO2">
                            <RulePath
                                    :path="result.packageNameAll || ''"
                            />
                        </span>
                      </div>
                    </a-menu-item>
                  </div>

                </a-menu>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<style lang="scss" scoped>
  /*去除单选框边框及阴影*/
  :deep(.ant-radio-button-wrapper) {
    border: none;
  }
  :deep(.ant-radio-button-wrapper:not(:first-child)::before) {
    display: none !important;
  }
  :deep(.ant-radio-button-wrapper-checked:not([class*=' ant-radio-button-wrapper-disabled']).ant-radio-button-wrapper:first-child) {
    border: 0;
  }
  :deep(.ant-radio-button-wrapper:focus-within) {
    box-shadow: 0 0 0 3px transparent !important;
  }
  :deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
    border-color: transparent !important;
  }
  :deep(.ant-radio-button-wrapper:first-child) {
    border-left: none;
  }

  /* 添加垂直居中样式 */
  :deep(.HeadNewButton-module_iconContainer_HmX2B) {
    display: flex;
    align-items: center;
    margin-top: 0;
  }

  :deep(.JumpMenu-module_jumpItemLabel_OPbMQ) {
    display: flex;
    align-items: center;
  }

  /* 分割线样式 */
  .search-divider {
    height: 0.5px;
    background-color: var(--yq-yuque-grey-3);
    margin: 12px 0;
    width: 100%;
  }

  /* 添加固定高度容器样式 */
  .search-empty-container,
  .search-results-container {
    min-height: 400px;
    display: flex;
    flex-direction: column;
  }
  .search-empty-container{
    justify-content: center;
  }

  :deep(.ant-empty) {
    padding: 40px 0;
  }

  :deep(.JumpMenu-module_jumpMenuList_OJGJg) {
    height: 400px;
    overflow-y: auto;
  }
</style>
