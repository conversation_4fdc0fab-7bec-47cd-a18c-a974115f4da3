<script setup lang="ts">
import RuleBaseContent from '@/businessComponents/ruleBaseManage/RuleBaseContent.vue';
import RuleSidebar from '@/businessComponents/ruleBaseManage/RuleSidebar.vue';
import SidebarContentLayout from '@/components/layout/SidebarContentLayout.vue';

const route = useRoute()
// 添加对当前侧边栏宽度的引用
const currentSidebarWidth = ref(260);
// 添加对RuleSidebar组件的引用
const ruleSidebarRef = ref<Record<string, any>>({});
// 添加布局组件引用
const layoutRef = ref<Record<string, any>>({});

// 获取路由和标题更新方法
const updateRouteTitle = inject('updateRouteTitle') as (path: string, title: string) => void;

// 重置侧边栏宽度到默认值的方法
const resetSidebarToDefaultWidth = () => {
    if (layoutRef.value && layoutRef.value.resetToDefaultWidth) {
        layoutRef.value.resetToDefaultWidth();
    }
};

// 组件挂载后，确保初始宽度为默认值
onMounted(() => {
    // 添加一个小延迟确保组件已完全挂载
    setTimeout(() => {
        resetSidebarToDefaultWidth();
    }, 100);
});

// 监听ruleSidebarRef的变化，获取其中的ruleBaseName值
watchEffect(() => {
    if (ruleSidebarRef.value && ruleSidebarRef.value.ruleBaseName) {
        setTimeout(() => {
            // 更新面包屑标题
            if (updateRouteTitle && route.query.backFlag !== 'dashboard') {
                if(route.path.indexOf('ruleBase-all')!==-1&&ruleSidebarRef.value.ruleBaseName==='所有规则库'){
                    updateRouteTitle(route.path, '规则浏览');
                }else{
                    updateRouteTitle(route.path, ruleSidebarRef.value.ruleBaseName);
                }

            } else {
                //console.error('未找到updateRouteTitle方法');
            }
        });
    }
});

// 监听侧边栏宽度变化并更新RuleSidebar组件宽度
const updateSidebarWidth = (width: number) => {
    currentSidebarWidth.value = width;
    // 如果ruleSidebarRef已经挂载，则更新其宽度
    if (ruleSidebarRef.value && ruleSidebarRef.value.updateSidebarWidth) {
        ruleSidebarRef.value.updateSidebarWidth(width);
    }
    
    // 更新布局组件宽度
    if (layoutRef.value && layoutRef.value.currentWidth) {
        layoutRef.value.currentWidth = width;
    }
};

// 提供ruleSidebarRef给子组件使用
provide('ruleSidebarRef', ruleSidebarRef);
// 提供updateSidebarWidth给子组件使用
provide('updateSidebarWidth', updateSidebarWidth);
// 提供resetSidebarToDefaultWidth方法给子组件使用
provide('resetSidebarToDefaultWidth', resetSidebarToDefaultWidth);
</script>

<template>
    <!-- 监听宽度变化事件 -->
    <SidebarContentLayout ref="layoutRef" @update:width="updateSidebarWidth">
        <template #sidebar-content="{ width }">
            <!-- 使用KeepAlive包裹RuleSidebar组件，确保它不会在路由切换时重新创建 -->
            <keep-alive>
                <RuleSidebar ref="ruleSidebarRef"/>
            </keep-alive>
        </template>

        <!-- 加KeepAlive是为了打开规则详情或规则内容编辑页再返回时，不重新加载组件，以便能保持住表格分页等状态 -->
        <keep-alive>
            <NuxtPage v-if="route.params.rulePackageId" />
            <RuleBaseContent v-else :rule-sidebar-ref="ruleSidebarRef" />
        </keep-alive>
    </SidebarContentLayout>
</template>
