<template>
  <div>
    <!-- 如果没有标题，则显示"如果"字样，并在未锁定且没有子条件时显示"添加"按钮 -->
    <div v-if="!hasTitle" class="structuralWord">
      <span>如果</span>
      <div
        v-if="
          !locked &&
          conditionData &&
          (!conditionData.children ||
            conditionData.children.length === 0 ||
            !conditionData.children[0].indent)
        "
      >
        <a-button
          type="link"
          @click="conditionInit"
          :style="{
            color: 'rgba(0,0,0,0.4)',
            fontSize: '12px',
            marginBottom: '8px',
          }"
        >
          <EditOutlined />添加
        </a-button>
      </div>
    </div>
    <!-- 条件组件的容器，设置最小宽度为10000px -->
    <div class="conditionCom" style="min-width: 10000px">
      <div ref="refNode">
        <!-- 如果有条件数据，则渲染逻辑按钮和条件组件 -->
        <template v-if="conditionData">
          <LogicBtn
            v-for="item in logicBtns"
            :key="item.props.pos"
            :style="item.style"
            v-bind="item.props"
            @onClick="item.on.onClick"
          />
          <ConditionCom
            v-for="condition in conditions"
            :key="condition.key"
            v-bind="condition.props"
            :isTable="isTable"
            :noRuleCellUnit="noRuleCellUnit"
            @addRule="addRule"
            @addUp="addUp"
            @addChildCondition="addChildCondition"
            @addTailItem="addTailItem"
            @replaceItem="replaceItem"
            @decreaseRule="decreaseRule"
            @conditionChange="onConditionChange"
            @conditionInit="conditionInit"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import ConditionCom from "@/components/ruleEditCom/condition/condition.vue";
import Raphael from "raphael";
import * as util from "@/components/ruleEditCom/utils/util.js";
import { LogicBtn } from "@/components/ruleEditCom/utils/";

// 定义组件的 props
const props = defineProps({
  pos: String,
  conditionData: {
    type: Object,
    default: () => ({}),
  },
  validList: {
    type: Array,
    default: () => [],
  },
  locked: Boolean,
  isTrack: Boolean,
  hasTitle: Boolean,
  showAddUp: {
    type: Boolean,
    default: true,
  },
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义组件的 emits
const emit = defineEmits([
  "conditionChange",
  "decreaseRule",
  "addUp",
  "conditionInit",
  "logicBtnClick",
  "addRule",
  "replaceItem",
]);

// 定义响应式数据
const refNode = ref(null); // 用于挂载 Raphael 的 DOM 元素
const Paper = ref(null); // Raphael 实例
const baseT = ref(0); // 基础顶部位置
const baseL = ref(0); // 基础左侧位置
const layerHeight = ref(30); // 每层的高度，从26改为30
const indentLength = ref(56); // 缩进长度

// 计算属性，获取有效的验证列表
const conditionValidList = computed(() => props.validList);

// 初始化方法，在组件挂载后执行
const init = async () => {
  await nextTick();
  if (refNode.value) {
    Paper.value = new Raphael(refNode.value); // 初始化 Raphael 实例
    const { conditionData } = props;
    conditionData && renderConditions(conditionData, false); // 渲染条件数据
  }
};

// 渲染条件数据的方法
const renderConditions = (conditionData, needRenderNode) => {
  resetSize(); // 重置大小
  clear(); // 清除之前的绘图
  return getConditionList(
    conditionData,
    { needRenderNode },
    conditionValidList.value
  ); // 获取条件列表
};

// 删除规则的方法
const decreaseRule = ({ pos, conditionId, layer }) => {
  emit("decreaseRule", { pos, conditionId, layer }); // 触发删除规则事件
};

// 添加规则的方法
const addUp = (pos, conditionId, layer) => {
  emit("addUp", pos, conditionId, layer); // 触发添加规则事件
};

// 获取条件列表的方法
const getConditionList = (conditionData, options, conditionValidList = []) => {
  const {
    needRenderNode,
    startLayer = 1,
    startIndent = 0,
    indexPath = "",
    parentFold,
  } = options;
  const { ruleCondition, children, fold, indent } = conditionData;
  const blockIndent = indent;
  const blockLayer =
    util.getFirstRule(conditionData) &&
    util.getFirstRule(conditionData).showLayer;
  Paper.value &&
    drawLine(blockIndent, blockLayer, startLayer, startIndent, conditionData); // 绘制线条

  if (ruleCondition) {
    if (needRenderNode) {
      const style = `position: absolute;top:${
        baseT.value + (ruleCondition.showLayer - 1) * layerHeight.value
      }px;left: ${baseL.value + blockIndent * indentLength.value}px`;
      const { layer, conditionId } = ruleCondition;
      const conditionProps = {
        style,
        conditionData: props.conditionData,
        isTrack: props.isTrack,
        locked: props.locked,
        validList: props.validList,
        pos: props.pos + "_" + conditionId,
        fold: parentFold,
        indexPath: indexPath, // path记录数据在每一层数组中的位置，以此判断是否显示折叠三角、逻辑符号的位置
        indentLength: indentLength.value,
        dataSource: conditionData,
        conditionValid: conditionValidList[layer - 1],
        preIndex: props.preIndex,
        predefineLine: props.predefineLine,
        predefineCon: props.predefineCon,
        showAddUp: props.showAddUp,
      };
      return [
        {
          key: ruleCondition.conditionId,
          props: conditionProps,
        },
      ];
    }
  } else if (fold) {
    const _options = {
      needRenderNode: true,
      startLayer: blockLayer,
      startIndent: blockIndent,
      indexPath: indexPath + "|" + 0,
      parentFold: fold,
    };
    return getConditionList(children[0], _options, conditionValidList); // 递归渲染折叠的第一个子条件
  } else {
    if (children) {
      return children.flatMap((item, i) => {
        const _options = {
          needRenderNode: true,
          startLayer: blockLayer,
          startIndent: blockIndent,
          indexPath: indexPath + "|" + i,
          parentFold: fold,
        };
        return getConditionList(item, _options, conditionValidList); // 递归渲染子条件
      });
    } else {
      return [];
    }
  }
};

// 条件变化时触发的方法
const onConditionChange = (pos, newContents) => {
  emit("conditionChange", pos, newContents); // 触发条件变化事件
};

// 添加规则的方法
const addRule = (pos, conditionId, layer) => {
  emit("addRule", pos, conditionId, layer);
};

// 添加子条件的方法
const addChildCondition = (pos, conditionId, layer) => {
  emit("addChildCondition", pos, conditionId, layer);
};

// 添加尾部项的方法
const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};

// 替换项的方法
const replaceItem = (pos, conditionId, layer) => {
  emit("replaceItem", pos, conditionId, layer);
};

// 初始化条件的方法
const conditionInit = () => {
  emit("conditionInit"); // 触发初始化条件事件
};

// 绘制线条的方法
const drawLine = (indent, layer, startLayer, startIndent, conditionData) => {
  // 如果节点不包含有效规则，不绘制线条
  if (!util.hasValidRule(conditionData)) {
    return;
  }
  const { executeRes } = conditionData;
  let lineColor = "#DCDFE6"; // 默认线条颜色
  if (executeRes) {
    const { executeStatus } = executeRes;
    lineColor =
      executeStatus === true
        ? "green"
        : executeStatus === false
        ? "red"
        : "#989898"; // 根据执行状态设置线条颜色
  }

  const _ox = startIndent * indentLength.value + baseL.value;
  const _oy =
    (startLayer - 1) * layerHeight.value +
    baseT.value +
    layerHeight.value * 0.45; // 微调线条的垂直位置
  const _mx1 = 0;
  const _my1 = (layer - startLayer) * layerHeight.value;
  const _mx2 = (indent - startIndent) * indentLength.value;
  const _my2 = 0;

  const line = Paper.value.path(
    `M ${_ox} ${_oy} l ${_mx1} ${_my1} l ${_mx2} ${_my2}`
  );
  line.attr({
    "arrow-end": "classic", // 改为经典箭头样式
    "stroke-width": 1.2, // 稍微增加线条粗细
    stroke: lineColor,
  }); // 设置线条属性
};

// 清除绘图的方法
const clear = () => {
  if (Paper.value) {
    Paper.value.clear(); // 清除 Raphael 实例中的所有绘图
  }
};

// 重置大小的方法
const resetSize = () => {
  if (Paper.value) {
    const { conditionData } = props;
    const newHeight =
      layerHeight.value * util.countDisplayedRules(conditionData) + 10;
    const newWidth = 10000;
    Paper.value.setSize(newWidth, newHeight); // 设置 Raphael 实例的大小
  }
};

// 渲染逻辑按钮的方法
const renderLogicBtns = (conditionData, locked, isTrack) => {
  const arr = util.getLogicData(conditionData);
  return arr.map((item) => {
    const { logicalSymbol, blockIndent, blockLayer, blockShowLayer } = item;
    const style = `position: absolute;z-index: 1;top:${
      baseT.value + (blockShowLayer - 1) * layerHeight.value + 5
    }px;left: ${
      baseL.value + (blockIndent - 1) * indentLength.value + 20
    }px;font-size: 12px;text-align:right;line-height:16px;box-shadow:none`;
    const logicProps = {
      pos: props.pos + "_" + blockIndent + "_" + blockLayer,
      text: logicalSymbol,
      locked: locked,
      isTrack: isTrack,
    };
    const logicMethod = {
      onClick: (pos, log) => {
        emit("logicBtnClick", pos, log);
      }, // 触发逻辑按钮点击事件
    };
    return {
      style,
      props: logicProps,
      on: logicMethod,
    };
  });
};

// 响应式数据，用于存储条件和逻辑按钮
const conditions = ref([]);
const logicBtns = ref([]);

// 监听 conditionData 的变化，重新渲染条件和逻辑按钮
watch(
  () => props.conditionData,
  (newConditionData) => {
    if (newConditionData) {
      conditions.value = renderConditions(newConditionData, true);
      logicBtns.value = renderLogicBtns(
        newConditionData,
        props.locked,
        props.isTrack
      );
    }
  },
  { immediate: true, deep: true }
);

// 监听 conditionValidList 的变化，更新 conditions
watch(
  () => conditionValidList.value,
  (newConditionValidList) => {
    if (props.conditionData) {
      conditions.value = renderConditions(props.conditionData, true);
    }
  },
  { immediate: true, deep: true }
);

// 组件挂载后初始化
onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.eRuleEditorContainer .designView .ruleBody .conditionCom .condition {
  white-space: nowrap;
}

.structuralWord {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  
  span {
    font-size: 14px;
    line-height: 30px;
    margin-right: 5px;
    vertical-align: middle;
    margin-top: 8px;
  }
}
</style>
