<!-- 文件列表 -->
<script setup lang="ts">
    const message = inject('message')
    import { demandFileList, download, fileDelete } from "@/api/task";
    const modal = inject('modal')
    interface File {
        fileName: string;
        createdTimeStr: string;
    }
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`,
        },
        {
            title: '文件名称',
            dataIndex: 'fileName',
            key: 'fileName',
        },
        {
            title: '上传日期',
            dataIndex: 'createdTimeStr',
            key: 'createdTimeStr',
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
        },
    ];
    const files = ref<File[]>([]);
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });
    const props = defineProps({
        infoData: {
            type: Object,
        },
    });
    watch(
        () => props.infoData,
        (newValue) => {
            try {
                fetchFiles();

            } catch (error) {
            }
        },
        {
            immediate: true,
        }
    );
    onMounted(()=>{
        fetchFiles();
    })
    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        fetchFiles();
    };
    const fetchFiles = () => {
        pagination.value.isLoading = true;
        try {
            demandFileList({
                appNo: props.infoData.appNo,
                page: pagination.value.current,
                several: pagination.value.pageSize,
            }).then((res) => {
                if (res.code == 20000) {
                    files.value = res.data.data;
                    pagination.value.total = res.data.totalCount;
                    pagination.value.isLoading = false;
                }
            });

        } catch (error) {
            pagination.value.isLoading = false;
            message.error('获取文件失败');
        }
    };
    const handleDelete = (record) => {
        modal.confirm({
            title: "将永久删除该文件, 是否继续?",
            onOk() {
                fileDelete(record.uuid).then((res) => {
                    if (res.code === 20000) {
                        message.success('删除成功')
                        fetchFiles()
                    } else {
                        message.error(res.data);
                    }
                });
            },
            onCancel() {
                message.info('已取消删除')
                fetchFiles()
            }
        });
    }
    // 下载
    const fileDownload = (row) => {
        //console.log(row);
        download(row.uuid).then((response) => {
            const a = document.createElement("a");

            var filename = row.fileName;

            const blob = new Blob([response]);

            const objectUrl = URL.createObjectURL(blob);

            a.setAttribute("href", objectUrl);

            a.setAttribute("download", filename);

            a.click();
        });
    }
</script>
<template>
    <a-table :columns="columns" :data-source="files" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading" size="small"
             @change="handlePageChange">
        <template v-slot:bodyCell="{column,record,index}">
            <template v-if="column.dataIndex == 'fileName'">
                <a
                        href="javascript:;"
                        @click="fileDownload(record)"
                        style="color: #409eff"
                >{{ record.fileName }}</a
                >
            </template>
            <template v-if="column.dataIndex == 'action'">
                <a-button type="link" size="small" @click="handleDelete(record)">删除</a-button>
            </template>
        </template>
    </a-table>
</template>
