import Cookies from 'js-cookie'

const Token<PERSON>ey = 'token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey)
}

export function setButtonRole(buttonRole) {
  Cookies.set("buttonRole", buttonRole)
}

export function getButtonRole() {
  return Cookies.get("buttonRole") || '[]'
}

export function removeButtonRole() {
  Cookies.remove("buttonRole")
}

export function setUnid(unid) {
  Cookies.set("unid", unid)
}

export function getUnid() {
  return Cookies.get("unid") || ''
}

export function removeUnid() {
  Cookies.remove("unid")
}
