import request from '@/utils/request'

/**
 * 通用批量导出接口
 * @param {object} data 导出参数
 * @returns {Promise} 导出结果
 */
export function batchExport(data) {
  return request({
    url: '/erule/manage/common/batchExport',
    method: 'post',
    data,
    responseType: "arraybuffer",
    headers: {
      response: true
    }
  })
}

/**
 * 获取导出字段配置
 * @param {string} module 模块名称
 * @returns {Promise} 字段配置
 */
export function getExportFields(module) {
  return request({
    url: '/erule/manage/common/exportFields',
    method: 'get',
    params: { module }
  })
} 