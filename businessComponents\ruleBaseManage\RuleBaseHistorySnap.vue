<!-- 规则历史信息基线 -->
<template>
    <div id="rule_base_history_snap">
        <a-table :columns="columns" :data-source="tableData" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading"
                 @change="handlePageChange">
            <template v-slot:bodyCell="{column,record,index}">
                <template v-if="column.dataIndex == 'publishStatus'">
                    <span v-if="record.publishStatus == 0">否</span>
                    <span v-if="record.publishStatus == 1">是</span>
                </template>
            </template>

        </a-table>
    </div>
</template>

<script setup lang="ts">
    const props = defineProps({
        dataId: {
            type: String,
            default: () => '',
        }
    })
    import $ from 'jquery';
    const message = inject('message')
    import { getSnapShootListByRuleHisUuid } from "@/api/baseline_Management";
    onMounted(()=>{
        snapShootList();
    })

    //页脚参数
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });


    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        snapShootList();
    };
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`
        },
        {
            title: '规则库名称',
            dataIndex: 'engChineseName',
            key: 'engChineseName',
        },
        {
            title: '版本',
            dataIndex: 'edition',
            key: 'edition',
        },
        {
            title: '创建时间',
            dataIndex: 'createdTimeStr',
            key: 'createdTimeStr',
        },
        {
            title: '基线描述',
            dataIndex: 'descs',
            key: 'descs',
        },
        {
            title: '是否发布',
            dataIndex: 'publishStatus',
            key: 'publishStatus',
        }
    ];
    //定义参数
    const tableData = ref<RuleHistory[]>([]);
    interface RuleHistory {
        engChineseName: string;
        edition: string;
        createdTimeStr: string;
        descs: string;
        publishStatus: string;
    }

    const snapShootList = () => {
        pagination.value.isLoading = true;
        getSnapShootListByRuleHisUuid({
            ruleHisUuid: props.dataId,
            page: pagination.value.current,
            several: pagination.value.pageSize,
        }).then((res) => {
            pagination.value.isLoading = false;
            tableData.value = res.data.data;
            pagination.value.total = res.data.totalCount;
        });
    }
</script>
