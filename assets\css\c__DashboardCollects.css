.LoadMore-module_loadMore_0F1jT {
    cursor: pointer;
    padding: 16px
}

.LoadMore-module_loadMore_0F1jT.LoadMore-module_loading_T6gmY {
    text-align: center
}

.Search-module_search_wrapper_Wud17 {
    display: flex;
    align-items: center;
    position: relative;
    width: 32px;
    height: 32px
}

.Search-module_search_wrapper_Wud17 .Search-module_searchBar_s7ct4 {
    width: 45px;
    display: none
}

.Search-module_search_wrapper_Wud17 .Search-module_searchBar_s7ct4.Search-module_active_\+GxCW {
    transition: width .2s ease-in;
    width: 200px;
    display: block;
    position: absolute;
    right: 0;
    top: 0
}

.Search-module_search_wrapper_Wud17 .Search-module_searchBar_s7ct4 .ant-input-affix-wrapper {
    transition: none;
    border: 1px solid var(--yq-border-primary)!important
}

.Search-module_search_wrapper_Wud17 .Search-module_searchBar_s7ct4 .ant-input-affix-wrapper-focused {
    border: 1px solid var(--yq-border-primary-active)!important;
    box-shadow: none
}

.Search-module_search_wrapper_Wud17 .Search-module_webAppSearchBar_HWYG3.Search-module_searchBar_s7ct4.Search-module_active_\+GxCW {
    width: 130px
}

.Search-module_search_wrapper_Wud17 .Search-module_webAppSearchBar_HWYG3.Search-module_searchBar_s7ct4 .ant-input-affix-wrapper,.Search-module_search_wrapper_Wud17 .Search-module_webAppSearchBar_HWYG3.Search-module_searchBar_s7ct4 .ant-input-affix-wrapper input {
    background-color: var(--yq-bg-tertiary);
    border: none!important;
    box-shadow: none!important
}

.Search-module_search_wrapper_Wud17 .Search-module_searchMini_qyE24 {
    font-size: 14px;
    text-align: right;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    padding: 6px 0 6px 4px;
    opacity: 0;
    pointer-events: none;
    min-width: 76px
}

.Search-module_search_wrapper_Wud17 .Search-module_searchMiniShow_aJtK3 {
    transition: opacity .3s ease-in;
    opacity: 1;
    pointer-events: auto
}

.Search-module_search_wrapper_Wud17 .Search-module_searchMiniShow_aJtK3.Search-module_webapp_Vrtz6 {
    top: -2px
}

.Search-module_search_wrapper_Wud17 .Search-module_searchMini_qyE24 .Search-module_searchIcon_1ensy {
    vertical-align: middle;
    color: var(--yq-icon-primary)
}

.Search-module_search_wrapper_Wud17 .Search-module_searchMini_qyE24 .larkui-icon-help-search {
    font-size: 16px
}

.Search-module_search_wrapper_Wud17.Search-module_mobile_Zrz-t {
    width: 100%;
    padding: 16px 16px 0 16px
}

.Search-module_search_wrapper_Wud17.Search-module_mobile_Zrz-t .Search-module_searchBar_s7ct4 {
    width: 100%;
    opacity: 1;
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    border: 1px solid var(--yq-border-primary)
}

.Search-module_search_wrapper_Wud17.Search-module_mobile_Zrz-t .Search-module_searchBar_s7ct4 .ant-input-affix-wrapper {
    border-bottom-width: 0
}

.FilterBar-module_wrapper_vVKNb {
    color: var(--yq-text-body);
    padding: 0 4px;
    position: relative;
    display: flex;
    vertical-align: middle;
    justify-content: center;
    align-items: center
}

.FilterBar-module_wrapper_vVKNb.FilterBar-module_active_9\+PCU {
    background: var(--yq-bg-tertiary);
    border-radius: 4px
}

.FilterBar-module_menu_kOnYc {
    width: 120px
}

.FilterBar-module_menu_kOnYc .larkui-icon-check-outlined {
    color: var(--yq-text-body);
    font-size: 14px;
    position: absolute;
    left: 10px;
    top: 13px
}

.FilterBar-module_menu_kOnYc .FilterBar-module_menuItem_oWeT- {
    padding-left: 40px;
    position: relative
}

.FilterBar-module_menu_kOnYc .FilterBar-module_menuItem_oWeT-:first-child {
    border-bottom: 1px solid var(--yq-border-light)
}

.FilterBar-module_menu_kOnYc .ant-dropdown-menu-item {
    line-height: 28px
}

.FilterBar-module_filterText_PZwi8 {
    margin-left: 4px
}

.FilterBar-module_filterIcon_V8fKL {
    vertical-align: middle;
    color: var(--yq-icon-primary)
}

.Marks-module_mytable_GlX5k {
    margin-bottom: 48px
}

.Marks-module_mytable_GlX5k .ant-card-head {
    padding: 0 16px
}

.Marks-module_mytable_GlX5k .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Marks-module_mytable_GlX5k .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.Marks-module_mytable_GlX5k .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Marks-module_mytable_GlX5k .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Marks-module_mytable_GlX5k .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Marks-module_mytable_GlX5k .ant-card-body {
    position: relative;
    padding: 0
}

.Marks-module_mytable_GlX5k .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.Marks-module_mytable_GlX5k .ant-table a {
    color: var(--yq-text-body)
}

.Marks-module_mytable_GlX5k .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Marks-module_mytable_GlX5k .ant-table-thead>tr:first-child>th:first-child,.Marks-module_mytable_GlX5k .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Marks-module_mytable_GlX5k .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Marks-module_mytable_GlX5k .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.Marks-module_mytable_GlX5k .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.Marks-module_mytable_GlX5k .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.Marks-module_mytable_GlX5k .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.Marks-module_mytable_GlX5k .Marks-module_activeRow_oX2iZ {
    background: var(--yq-yuque-green-1)
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 a,.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .book-icon,.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .doc-icon,.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .Marks-module_titleIcon_hRflE {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .doc-link {
    display: block
}

.Marks-module_mytable_GlX5k .ant-table .Marks-module_columnsTitle_fsKl9 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.Marks-module_mytable_GlX5k .Marks-module_columnsTitleLabel_xTH6Q {
    margin-left: 12px
}

.Marks-module_mytable_GlX5k .Marks-module_columnsBelong_yTv94 {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Marks-module_mytable_GlX5k .Marks-module_columnsDesc_0lVB8 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Marks-module_mytable_GlX5k .Marks-module_columnsDesc_0lVB8.Marks-module_mini_Zgyev {
    max-width: 200px
}

.Marks-module_mytable_GlX5k .Marks-module_columnsTime_MI-z- {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.Marks-module_mytable_GlX5k .Marks-module_columnsAction_Kz7zL {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.Marks-module_mytable_GlX5k .Marks-module_columnsAction_Kz7zL.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Marks-module_mytable_GlX5k .Marks-module_columnsAction_Kz7zL .ant-btn {
    border-radius: 4px
}

.Marks-module_mytable_GlX5k .Marks-module_emptyView_eYLYR {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Marks-module_mytable_GlX5k .Marks-module_emptyView_eYLYR .Marks-module_emptyViewImg_9a-df {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.Marks-module_mytable_GlX5k .Marks-module_emptyView_eYLYR a {
    color: var(--yq-text-link)
}

.Marks-module_mytable_GlX5k .Marks-module_emptyView_eYLYR a:hover {
    color: var(--yq-ant-link-hover-color)
}

.Marks-module_noPaddingTable_t9DZC .ant-card-head {
    padding: 0 16px
}

.Marks-module_noPaddingTable_t9DZC .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Marks-module_noPaddingTable_t9DZC .ant-card-head .ant-card-head-title,.Marks-module_noPaddingTable_t9DZC .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Marks-module_noPaddingTable_t9DZC .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Marks-module_noPaddingTable_t9DZC .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Marks-module_noPaddingTable_t9DZC .ant-card-body {
    position: relative;
    padding: 0
}

.Marks-module_noPaddingTable_t9DZC .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Marks-module_noPaddingTable_t9DZC .ant-table a {
    color: var(--yq-text-caption)
}

.Marks-module_noPaddingTable_t9DZC .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Marks-module_noPaddingTable_t9DZC .ant-table-thead>tr:first-child>th:first-child,.Marks-module_noPaddingTable_t9DZC .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Marks-module_noPaddingTable_t9DZC .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Marks-module_noPaddingTable_t9DZC .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.Marks-module_noPaddingTable_t9DZC .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.Marks-module_noPaddingTable_t9DZC .ant-table-footer {
    padding: 0;
    border-top: 0
}

.Marks-module_noPaddingTable_t9DZC .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Marks-module_noPaddingTable_t9DZC .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_activeRow_oX2iZ {
    background: var(--yq-yuque-green-1)
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9.Marks-module_mini_Zgyev {
    max-width: 300px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 a,.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 .book-icon,.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 .doc-icon,.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 .Marks-module_titleIcon_hRflE {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitle_fsKl9 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTitleLabel_xTH6Q {
    margin-left: 12px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsBelong_yTv94 {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsDesc_0lVB8 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsDesc_0lVB8.Marks-module_mini_Zgyev {
    max-width: 200px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsTime_MI-z- {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsAction_Kz7zL {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsAction_Kz7zL a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Marks-module_noPaddingTable_t9DZC .Marks-module_columnsAction_Kz7zL .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .Marks-module_mytable_GlX5k .Marks-module_columnsDesc_0lVB8,.Marks-module_mytable_GlX5k .Marks-module_columnsTime_MI-z- {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .Marks-module_mytable_GlX5k .Marks-module_columnsBelong_yTv94 {
        display:none
    }
}

.Marks-module_mytable_GlX5k .ant-card {
    border: none
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.Marks-module_mytable_GlX5k .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.Marks-module_mytable_GlX5k .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.Marks-module_mytable_GlX5k .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.Marks-module_mytable_GlX5k .ant-table-tbody>tr.ant-table-row-selected>td,.Marks-module_mytable_GlX5k td.ant-table-column-sort {
    background: none
}

.Marks-module_mytable_GlX5k .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.Marks-module_mytable_GlX5k .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.Marks-module_mytable_GlX5k .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.Marks-module_mytable_GlX5k .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .Marks-module_mytable_GlX5k .ant-card-extra .ant-btn,.Marks-module_mytable_GlX5k .ant-card-head-title {
        display:none
    }

    .Marks-module_mytable_GlX5k .ant-card-extra {
        float: none;
        flex: auto
    }

    .Marks-module_mytable_GlX5k .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.Marks-module_wrapper_dtO2N {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .Marks-module_wrapper_dtO2N {
        padding:0
    }
}

.Marks-module_titleIcon_hRflE {
    margin-right: 16px!important
}

.Marks-module_isInDashboard_jerHj.Marks-module_marksContainer_shN3a {
    height: auto
}

.Marks-module_isInDashboard_jerHj .Marks-module_mymarks_FNKJk {
    overflow-y: hidden
}

.Marks-module_mymarks_FNKJk {
    padding: 20px 24px;
    width: 100%;
    overflow-y: auto;
    height: 100%
}

.Marks-module_mymarks_FNKJk .Marks-module_title_\+W0tq {
    display: inline-block
}

.Marks-module_mymarks_FNKJk .Marks-module_split_FTFNn {
    padding: 0 5px
}

.Marks-module_mymarks_FNKJk .larkicon-lock {
    margin-left: 4px;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Marks-module_mymarks_FNKJk .ant-table {
    margin-left: 0!important;
    margin-right: 0!important
}

.Marks-module_mymarks_FNKJk .ant-empty-description {
    display: none
}

.Marks-module_mymarks_FNKJk .ant-table-cell,.Marks-module_mymarks_FNKJk .ant-table-column-sorters {
    padding-left: 0!important
}

.Marks-module_user_AHEpS .group-avatar {
    vertical-align: middle;
    margin-right: 16px;
    margin-bottom: 2px
}

.Marks-module_list_\+tG8u>img {
    height: 24px;
    width: auto;
    border-radius: 4px;
    margin-right: 16px
}

.Marks-module_columnsTitle_fsKl9 a,.Marks-module_columnsTitleDesktop_4Wby6 {
    width: 300px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Marks-module_columnsTitleDesktop_4Wby6 {
    cursor: pointer
}

.Marks-module_editIcon_10vlM {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 4px 0
}

.Marks-module_filterContent_Y3mf\+ {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    z-index: 100;
    right: 0;
    opacity: 1;
    transition: opacity .3s ease-in
}

.Marks-module_filterContent_Y3mf\+ .Marks-module_filterBar_ln6Yo {
    margin-left: 16px;
    margin-right: 5px;
    margin-top: 2px;
    height: 24px;
    line-height: 24px
}

.Marks-module_filterContent_Y3mf\+ .Marks-module_shareButton_8jODM {
    margin-right: 10px
}

.Marks-module_collectionOperation_IIbvc {
    display: flex;
    vertical-align: middle;
    align-items: center;
    justify-content: flex-end
}

.Marks-module_marksContainer_shN3a {
    display: flex;
    height: 100%;
    overflow: hidden
}

.Marks-module_marksContainer_shN3a.Marks-module_isInDashboard_jerHj .Marks-module_collectionList_dAxye {
    padding-top: 0;
    padding-left: 0;
    padding-right: 16px
}

.Marks-module_marksContainer_shN3a.Marks-module_isInDashboard_jerHj .Marks-module_mymarks_FNKJk {
    padding-top: 0
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye {
    height: 100%;
    overflow: auto;
    width: 235px;
    min-width: 235px;
    border-right: 1px solid var(--yq-border-light);
    padding: 0 12px 20px 32px;
    margin-top: 20px
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionTitle_jCkZN {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    margin-bottom: 20px;
    color: var(--yq-text-caption);
    display: flex;
    align-items: center;
    justify-content: space-between
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionTitle_jCkZN .Marks-module_asideAdd_fUEYI {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 4px
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionTitle_jCkZN .Marks-module_asideAdd_fUEYI:hover {
    background-color: var(--yq-bg-tertiary)
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionTitle_jCkZN .Marks-module_asideAddIcon_hO1rS {
    color: var(--yq-icon-primary)
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO {
    height: 74px;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 4px
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 .Marks-module_name_ZaPov,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO .Marks-module_name_ZaPov {
    font-size: 14px;
    color: var(--yq-text-body);
    display: flex;
    align-items: center;
    justify-content: space-between
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 .Marks-module_name_ZaPov .Marks-module_nameText_\+uhle,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO .Marks-module_name_ZaPov .Marks-module_nameText_\+uhle {
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 .Marks-module_actionCount_TuLrw,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO .Marks-module_actionCount_TuLrw {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-top: 12px
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 .Marks-module_countNumber_EzeSk,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO .Marks-module_countNumber_EzeSk {
    font-family: sans-serif
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0:hover,.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_collectionWrap_qZnDO:hover {
    cursor: pointer;
    background: var(--yq-bg-primary-hover)
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 {
    background: var(--yq-bg-primary-hover)
}

.Marks-module_marksContainer_shN3a .Marks-module_collectionList_dAxye .Marks-module_activeCollection_5VIM0 .Marks-module_name_ZaPov {
    color: var(--yq-text-primary);
    font-weight: 500
}

.Marks-module_marksContainer_shN3a .Marks-module_mark_count_WlBOz {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Marks-module_collectionMenuItem_\+q2-W {
    width: 104px
}

.Marks-module_mytable_GlX5k,.Marks-module_wrapper_dtO2N {
    margin-bottom: 0!important
}

.Marks-module_card_0iKIU,.Marks-module_card_0iKIU .ant-card-head {
    background-color: var(--yq-bg-primary)
}

.Marks-module_withScrollbar_yCfpg::-webkit-scrollbar {
    display: block;
    width: 4px;
    height: 4px
}

.Marks-module_withScrollbar_yCfpg::-webkit-scrollbar-thumb {
    display: block;
    background: transparent;
    border-radius: 10px
}

.Marks-module_withScrollbar_yCfpg:hover::-webkit-scrollbar-thumb {
    background: var(--yq-yuque-grey-5)
}

.Marks-module_asideAddIcon_hO1rS {
    color: var(--yq-yuque-grey-7)
}

.Marks-module_markOperation_Gt7FS {
    background-color: var(--yq-white)
}

.Marks-module_isMobileMarkContainer_d73UE,.Marks-module_mobileMymarks_3gJsN {
    height: auto
}

@media only screen and (max-width: 575px) {
    .Marks-module_mymarks_FNKJk {
        padding:0
    }

    .Marks-module_marksContainer_shN3a {
        height: auto
    }
}

.Marks-module_searchFilter_6dW\+9 {
    position: absolute;
    top: 22px;
    right: 10px
}

.desktopApp .Marks-module_searchFilter_6dW\+9 {
    right: 20px
}

.desktopApp .Marks-module_marksContainer_shN3a {
    margin-top: 30px
}

.Marks-module_placeholder_jkAyu {
    height: 100px
}

.DashboardTitle-module_titleWarp_CQylY {
    font-weight: 500;
    font-size: 18px;
    color: var(--yq-yuque-grey-9);
    margin-bottom: 22px
}

.Collects-module_mytable_FWycx {
    margin-bottom: 48px
}

.Collects-module_mytable_FWycx .ant-card-head {
    padding: 0 16px
}

.Collects-module_mytable_FWycx .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Collects-module_mytable_FWycx .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.Collects-module_mytable_FWycx .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Collects-module_mytable_FWycx .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Collects-module_mytable_FWycx .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Collects-module_mytable_FWycx .ant-card-body {
    position: relative;
    padding: 0
}

.Collects-module_mytable_FWycx .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.Collects-module_mytable_FWycx .ant-table a {
    color: var(--yq-text-body)
}

.Collects-module_mytable_FWycx .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Collects-module_mytable_FWycx .ant-table-thead>tr:first-child>th:first-child,.Collects-module_mytable_FWycx .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Collects-module_mytable_FWycx .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Collects-module_mytable_FWycx .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.Collects-module_mytable_FWycx .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.Collects-module_mytable_FWycx .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.Collects-module_mytable_FWycx .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Collects-module_mytable_FWycx .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Collects-module_mytable_FWycx .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.Collects-module_mytable_FWycx .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.Collects-module_mytable_FWycx .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.Collects-module_mytable_FWycx .Collects-module_activeRow_00nr6 {
    background: var(--yq-yuque-green-1)
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn a,.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .book-icon,.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .Collects-module_titleIcon_vACFK,.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .doc-icon {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .doc-link {
    display: block
}

.Collects-module_mytable_FWycx .ant-table .Collects-module_columnsTitle_AZ7kn .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.Collects-module_mytable_FWycx .Collects-module_columnsTitleLabel_x8iAK {
    margin-left: 12px
}

.Collects-module_mytable_FWycx .Collects-module_columnsBelong_KS0QV {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Collects-module_mytable_FWycx .Collects-module_columnsDesc_bqu2j {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Collects-module_mytable_FWycx .Collects-module_columnsDesc_bqu2j.Collects-module_mini_QuNb3 {
    max-width: 200px
}

.Collects-module_mytable_FWycx .Collects-module_columnsTime_5WNRQ {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.Collects-module_mytable_FWycx .Collects-module_columnsAction_as2do {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.Collects-module_mytable_FWycx .Collects-module_columnsAction_as2do.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Collects-module_mytable_FWycx .Collects-module_columnsAction_as2do .ant-btn {
    border-radius: 4px
}

.Collects-module_mytable_FWycx .Collects-module_emptyView_jHLW- {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Collects-module_mytable_FWycx .Collects-module_emptyView_jHLW- .Collects-module_emptyViewImg_GeLY9 {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.Collects-module_mytable_FWycx .Collects-module_emptyView_jHLW- a {
    color: var(--yq-text-link)
}

.Collects-module_mytable_FWycx .Collects-module_emptyView_jHLW- a:hover {
    color: var(--yq-ant-link-hover-color)
}

.dashboard-dropdown-menu {
    max-height: 500px;
    overflow: auto
}

.dashboard-dropdown-menu .ant-dropdown-menu-item {
    padding: 0 20px;
    line-height: 40px;
    min-width: 120px
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a {
    padding: 0;
    margin: 0
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a:hover {
    color: var(--yq-text-primary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected {
    background: var(--yq-bg-tertiary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected a {
    background: transparent;
    color: var(--yq-text-body)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active {
    background: var(--yq-yuque-green-1)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active a {
    background: transparent;
    color: var(--yq-text-body)
}

.Collects-module_noPaddingTable_qdpgB .ant-card-head {
    padding: 0 16px
}

.Collects-module_noPaddingTable_qdpgB .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Collects-module_noPaddingTable_qdpgB .ant-card-head .ant-card-head-title,.Collects-module_noPaddingTable_qdpgB .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Collects-module_noPaddingTable_qdpgB .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Collects-module_noPaddingTable_qdpgB .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Collects-module_noPaddingTable_qdpgB .ant-card-body {
    position: relative;
    padding: 0
}

.Collects-module_noPaddingTable_qdpgB .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Collects-module_noPaddingTable_qdpgB .ant-table a {
    color: var(--yq-text-caption)
}

.Collects-module_noPaddingTable_qdpgB .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Collects-module_noPaddingTable_qdpgB .ant-table-thead>tr:first-child>th:first-child,.Collects-module_noPaddingTable_qdpgB .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Collects-module_noPaddingTable_qdpgB .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Collects-module_noPaddingTable_qdpgB .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.Collects-module_noPaddingTable_qdpgB .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.Collects-module_noPaddingTable_qdpgB .ant-table-footer {
    padding: 0;
    border-top: 0
}

.Collects-module_noPaddingTable_qdpgB .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Collects-module_noPaddingTable_qdpgB .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_activeRow_00nr6 {
    background: var(--yq-yuque-green-1)
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn.Collects-module_mini_QuNb3 {
    max-width: 300px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn a,.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn .book-icon,.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn .Collects-module_titleIcon_vACFK,.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn .doc-icon {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitle_AZ7kn .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTitleLabel_x8iAK {
    margin-left: 12px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsBelong_KS0QV {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsDesc_bqu2j {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsDesc_bqu2j.Collects-module_mini_QuNb3 {
    max-width: 200px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsTime_5WNRQ {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsAction_as2do {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsAction_as2do a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Collects-module_noPaddingTable_qdpgB .Collects-module_columnsAction_as2do .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .Collects-module_mytable_FWycx .Collects-module_columnsDesc_bqu2j,.Collects-module_mytable_FWycx .Collects-module_columnsTime_5WNRQ {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .Collects-module_mytable_FWycx .Collects-module_columnsBelong_KS0QV {
        display:none
    }
}

.Collects-module_mytable_FWycx .ant-card {
    border: none
}

.Collects-module_mytable_FWycx .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.Collects-module_mytable_FWycx .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.Collects-module_mytable_FWycx .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.Collects-module_mytable_FWycx .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.Collects-module_mytable_FWycx .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.Collects-module_mytable_FWycx .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.Collects-module_mytable_FWycx .ant-table-tbody>tr.ant-table-row-selected>td,.Collects-module_mytable_FWycx td.ant-table-column-sort {
    background: none
}

.Collects-module_mytable_FWycx .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.Collects-module_mytable_FWycx .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.Collects-module_mytable_FWycx .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.Collects-module_mytable_FWycx .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .Collects-module_mytable_FWycx .ant-card-extra .ant-btn,.Collects-module_mytable_FWycx .ant-card-head-title {
        display:none
    }

    .Collects-module_mytable_FWycx .ant-card-extra {
        float: none;
        flex: auto
    }

    .Collects-module_mytable_FWycx .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.Collects-module_wrapper_a6puR {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .Collects-module_wrapper_a6puR {
        padding:0
    }
}

.Collects-module_wrapper_a6puR {
    padding: 0
}

.Collects-module_wrapper_a6puR .ant-card {
    border: none
}

.Collects-module_webWrapper_D9poc {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column
}

.Collects-module_mytable_FWycx {
    margin-bottom: 0!important;
    padding-right: 0!important
}

.Collects-module_dashboardTitle_3jko8 {
    padding: 26px 36px 0;
    margin-bottom: 0!important
}

.Collects-module_desktopDashboardTitle_2TQ0f {
    padding-top: 30px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.Search-module_wrapper_Scz\+2 {
    display: flex;
    flex-flow: column;
    justify-content: center;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Search-module_wrapper_Scz\+2 .Search-module_img_CAujI {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.index-module_popMenu_aOUBY {
    border-radius: 12px 12px 0 0
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j {
    background-color: var(--yq-bg-tertiary);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    color: var(--yq-text-primary);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_item_-0ClC {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_cancel_i36-H {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 8px
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    display: flex;
    align-items: center;
    justify-content: center
}

.CollectionModal-module_collectionModal_sJKOy .CollectionModal-module_collectionModalContent_6NiUd {
    padding-bottom: 6px
}

.SelectGroup-module_selectGroup_PPbXd {
    width: 282px;
    position: relative
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_selectGroupBoxShadow_\+mRm5 {
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW {
    padding: 14px 16px 0
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW .SelectGroup-module_p1_iKXf5 {
    font-size: 16px;
    color: var(--yq-text-primary);
    margin-bottom: 4px;
    font-weight: 500
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_title_62QjW .SelectGroup-module_p2_vn1NJ {
    font-size: 14px;
    color: var(--yq-text-caption)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd {
    min-height: 65px;
    max-height: 287px;
    overflow: auto;
    padding: 20px 16px 12px 16px;
    color: var(--yq-text-primary)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_empty_k0F6W {
    color: var(--yq-text-caption);
    text-align: center;
    line-height: 26px
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD {
    margin-bottom: 12px
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_checkboxWrapper_5aHCt {
    width: 100%
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_checkboxWrapper_5aHCt span:nth-child(2) {
    display: flex;
    min-width: 0
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD span {
    color: var(--yq-text-primary)
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_content_J8Ejd .SelectGroup-module_checkContent_hhsnD .SelectGroup-module_nameText_exIya {
    color: var(--yq-text-primary);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.SelectGroup-module_selectGroup_PPbXd .SelectGroup-module_footer_09b\+K {
    border-top: 1px solid var(--yq-border-primary);
    height: 52px;
    line-height: 52px;
    font-size: 14px;
    color: var(--yq-text-body);
    padding-left: 16px;
    cursor: pointer
}
