<template>
  <span class="operator-variable variableCom" @click="stopClick">
    <com-type-handler
      v-if="
        !(hideFrontBtn || variableType === 'expression' || locked || isTrack)
      "
      :pos="pos"
      :currentType="variableType"
      :disabledItems="disabledItems"
      @changeComType="changeComType"
    />
    <operator-field
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :enumDictName="enumDictName"
      :propOptions="propOptions"
      :variableData="dataSource"
      @onChange="onChildChange"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      :preIndex="preIndex"
      ref="df_ref"
    />
    <calculate-sign
      v-if="!(hideEndBtn || variableType === 'expression' || !!enumDictName)"
      :pos="pos"
      :value="signValue"
      :locked="locked"
      :isTrack="isTrack"
      :isLastOne="isLastOne"
      @onChange="changeCalculateSign"
    />
  </span>
</template>

<script setup>
import ComTypeHandler from "@/components/ruleEditCom/ruleItemList/operationMenu/comTypeHandler.vue";
import CalculateSign from "@/components/ruleEditCom/ruleItemList/operationMenu/calculateSign.vue";
import OperatorField from "./operatorField.vue";
import * as util from "@/components/ruleEditCom/utils/util";

// 定义组件的 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  dataSource: {
    default: () => ({}),
  },
  signValue: {
    type: String,
    default: "",
  },
  hideFrontBtn: Boolean,
  hideEndBtn: Boolean,
  propOptions: [Object, Array],
  locked: Boolean,
  isTrack: Boolean,
  isLastOne: Boolean,
  enumDictName: String,
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
});

// 定义组件的 emits
const emit = defineEmits(['onChange', 'onCalSignChange']);

const inputTypesArr = [
  "Short",
  "Integer",
  "Long",
  "Float",
  "Double",
  "BigDecimal",
  "String",
  "Character",
  "Boolean",
  "Date",
  "Enum",
];

// 计算属性，根据 dataSource 返回变量类型
const variableType = computed(() => {
  return props.dataSource.variableType;
});

// 计算属性，根据 dataSource 返回值类型
const valueType = computed(() => {
  return props.dataSource.valueType;
});

// 计算属性，根据 dataSource 返回是否为参数
const isParam = computed(() => {
  return props.dataSource.__context.isParam;
});

// 计算属性，根据 dataSource 返回是否为根变量
const isRootVar = computed(() => {
  return props.dataSource.__context.isRootVar;
});

// 计算属性，根据 dataSource 返回实际值类型
const realValueType = computed(() => {
  return util.getRealValueType(props.dataSource);
});

// 计算属性，根据各种条件返回禁用项
const disabledItems = computed(() => {
  const disabledItems = [];

  if (
    !isRootVar.value ||
    isParam.value === "actionSetLeft" ||
    !(inputTypesArr.indexOf(realValueType.value) > -1)
  ) {
    disabledItems.push("constant");
  }
  if (
    variableType.value === "constant" &&
    valueType.value === "Enum" &&
    isParam.value === "expressionParam"
  ) {
    disabledItems.push("propSelect");
    disabledItems.push("expression");
  }
  return disabledItems;
});

// 处理子组件变化的方法
const onChildChange = (pos, newValueObj, finalValueType) => {
  onChange(pos, newValueObj, finalValueType);
};

// 处理组件类型变化的方法
const changeComType = (pos, type) => {
  const { dataSource } = props;
  const newVariableData = {};
  newVariableData.valueType = dataSource._getRealValueType();
  if (type === "constant") {
    newVariableData.variableType = "constant";
    newVariableData.value = "";
    if (props.enumDictName) {
      newVariableData.enumDictName = props.enumDictName;
    }
  } else if (type === "propSelect") {
    newVariableData.variableType = "field";
    newVariableData.value = [];
  } else if (type === "expression") {
    newVariableData.variableType = "expression";
    newVariableData.expressionTreeData = {
      type: "expression",
      symbols: [],
      params: [
        {
          type: "expression",
          symbols: [],
          params: [{ type: "variable", data: dataSource }],
        },
      ],
    };
  }
  const _newVariableData = dataSource._derive(newVariableData);
  onChange(pos, _newVariableData, dataSource.valueType);
};

// 处理计算符号变化的方法
const changeCalculateSign = (pos, sign, paramType) => {
  const valueType = util.getRealValueType(props.dataSource);
  const newParamItem = util.getExpressionItem(paramType, valueType);
  emit("onCalSignChange", pos, sign, newParamItem);
};

// 停止点击事件冒泡的方法
const stopClick = (event) => {
  event.stopPropagation();
};

// 发送变化事件的方法
const onChange = (pos, newValueObj, finalValueType) => {
  emit("onChange", pos, newValueObj, finalValueType);
};
</script>
