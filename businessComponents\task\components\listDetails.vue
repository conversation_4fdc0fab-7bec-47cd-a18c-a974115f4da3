<!-- 任务详情 -->
<template>
    <div class="form">
        <a-form
                laba-width="100px"
                ref="ruleForm"
                :model="data.ruleForm"
                :rules="data.rules"
                style="text-align:left; margin-right:5px"
                :label-col="{ span: 6 }" :wrapper-col="{ span: 17 }"
        >
            <a-row>
                <a-col :span="8">
                    <a-form-item label="任务编码 ：">
                        <template v-if="true">
                            <span class="ant-form-text">{{ data.ruleForm.appNo }}</span>
                        </template>
                        <template v-else>
                            <a-input
                                    v-model:value="data.ruleForm.appNo"
                                    placeholder="请输入任务编码"
                            ></a-input>
                        </template>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="isNotAddWithPolicyOrAddPolicy">
                    <a-form-item label="任务名称 ：" name="demandName">
                        <template v-if="isReadOnly">
                            <span class="ant-form-text">{{ data.ruleForm.demandName }}</span>
                        </template>
                        <template v-else>
                            <a-input
                                    v-model:value="data.ruleForm.demandName"
                                    placeholder="请输入内容"
                            ></a-input>
                        </template>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="isAddWithPolicyOrAddPolicy">
                    <a-form-item label="任务名称 ：" name="demandName">
                        <a-input
                                v-model:value="data.ruleForm.demandName"
                                placeholder="请输入内容"
                        ></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="isNotAddWithPolicyOrAddPolicy">
                    <a-form-item label="归属机构 ：" name="applyOrgId">
                        <template v-if="isReadOnly">
                            <span class="ant-form-text">
                                {{ data.model_type_options.find(item => item.id === data.ruleForm.applyOrgId)?.orgName || '' }}
                            </span>
                        </template>
                        <template v-else>
                            <a-select
                                    v-model:value="data.ruleForm.applyOrgId"
                                    placeholder="请选择"
                                    filterable
                            >
                                <a-select-option label="请选择" value="">请选择</a-select-option>
                                <a-select-option
                                        v-for="item in data.model_type_options"
                                        :key="item.id"
                                        :value="item.id"
                                >{{item.orgName}}</a-select-option>
                            </a-select>
                        </template>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="isAddWithPolicyOrAddPolicy">
                    <a-form-item label="归属机构 ：" name="applyOrgId">
                        <a-select
                                v-model:value="data.ruleForm.applyOrgId"
                                placeholder="请选择"
                                filterable
                        >
                            <a-select-option label="请选择" value="">请选择</a-select-option>
                            <a-select-option
                                    v-for="item in data.model_type_options"
                                    :key="item.id"
                                    :value="item.id"
                            >{{item.orgName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row v-if="isNotAddOrUpdate">
                <a-col :span="8">
                    <a-form-item label="审核机构 ：">
                        <template v-if="isReadOnly">
                            <span class="ant-form-text">{{ data.ruleForm.orgName }}</span>
                        </template>
                        <template v-else>
                            <a-input
                                    v-model:value="data.ruleForm.orgName"
                                    placeholder="请输入内容"
                            ></a-input>
                        </template>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="申请人 ：">
                        <template v-if="isReadOnly">
                            <span class="ant-form-text">{{ data.ruleForm.createdName }}</span>
                        </template>
                        <template v-else>
                            <a-input
                                    v-model:value="data.ruleForm.createdName"
                                    placeholder="请输入内容"
                            ></a-input>
                        </template>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row v-if="isNotAddOrUpdate">
                <a-col :span="24">
                    <a-form-item label="描述：" name="describeInfo" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
                        <template v-if="isReadOnly">
                            <a-tooltip :title="data.ruleForm.describeInfo">
                                <span class="ant-form-text describe-text" style="line-height: 32px; display: inline-block; vertical-align: middle;">{{ data.ruleForm.describeInfo }}</span>
                            </a-tooltip>
                        </template>
                        <template v-else>
                            <a-textarea :auto-size="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="500" :rows="5"
                                    v-model:value="data.ruleForm.describeInfo"></a-textarea>
                        </template>
                    </a-form-item> </a-col
                ></a-row>
            <a-row v-if="isAddOrUpdate">
                <a-col :span="24">
                    <a-form-item label="描述：" name="describeInfo" :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="500" :rows="5"
                                    v-model:value="data.ruleForm.describeInfo"></a-textarea>
                    </a-form-item> </a-col
                ></a-row>
            <!--<a-row v-if="props.infoData.type == 'release'">
               <a-col :span="8">
                <RelevanceRule></RelevanceRule>
              </a-col>
            </a-row>-->
            <a-row v-if="props.infoData?.type === 'taskDetails'">
                <a-col :span="6">
                    <a-form-item label="发布意见" name="ruleCode">
                        <a-select
                                v-model:value="data.ruleForm.ruleCode"
                                placeholder="请选择"
                                @change="getName"
                        >
                            <a-select-option
                                    v-for="item in data.ruleArrsel"
                                    :key="item.code"
                                    :label="item.name"
                                    :value="item.code"
                            ></a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <div v-if="isFileUploadVisible">
                <a-row>
                    <a-col :span="8">
                        <a-form-item label="文件：" >
                            <a-upload
                                    :multiple="false"
                                    name="file"
                                    :customRequest="uploadFile"
                                    :remove="fileRemove"
                                    class="hide-progress-bar"
                            >
                                <a-button slot="trigger" size="small" type="primary"
                                >选取文件</a-button
                                >
                                <!-- <a-button
                                  style="margin-left: 10px"
                                  size="small"
                                  type="success"
                                  @click="submitUpload"
                                  >上传到服务器</a-button
                                > -->
                            </a-upload>
                        </a-form-item>
                    </a-col>
                </a-row>
            </div>
            <!-- 政策审核 -->
            <a-row v-if="props.infoData?.type == 'policyAudit'">
                 <a-col :span="8">
                    <a-form-item label="审批意见：" name="demandUuid" class="policy_select">
                        <a-select v-model:value="data.ruleForm.demandUuid" placeholder="请选择">
                            <a-select-option label="请选择" value="">请选择</a-select-option>
                            <a-select-option
                                    v-for="item in data.policyCheck_options"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                            ></a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- 规则调整 -->
            <a-row v-if="props.infoData?.type == 'adjustment'">
                <a-col :span="24">
                    <a-form-item label="发布环境：" name="environmentIds"  :label-col="{ span: 2 }" :wrapper-col="{ span: 20 }">
                        <a-checkbox-group v-model:value="data.ruleForm.environmentIds">
                            <a-checkbox v-for="item in data.environmentIds_Option" :key="item.id" :value="item.id">{{ item.environmentName }}</a-checkbox>
                        </a-checkbox-group>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row v-if="props.infoData?.type == 'submission'">
                 <a-col :span="8">
                    <a-form-item label="审批意见：" name="subType">
                        <a-select v-model:value="data.ruleForm.subType" placeholder="请选择">
                            <a-select-option value="">请选择</a-select-option>
                            <a-select-option value="1">测试通过</a-select-option>
                            <a-select-option value="0">退回修改</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- 任务审核 -->
            <a-row v-if="props.infoData?.type == 'updateRuleCheck'">
                 <a-col :span="8">
                    <a-form-item label="审批意见：" name="demandUuid">
                        <a-select
                                @change="getChange"
                                v-model:value="data.ruleForm.demandUuid"
                                placeholder="请选择"
                        >
                            <a-select-option label="请选择" value="">请选择</a-select-option>
                            <a-select-option
                                    v-for="item in data.demandUuid_option"
                                    :key="item.value"
                                    :value="item.value"
                            >{{item.name}}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="8" v-if="data.ruleForm.demandUuid == '3'">
                    <a-form-item label="发布环境：" name="environmentId">
                        <a-select v-model:value="data.ruleForm.environmentId" placeholder="请选择" style="width: 200px">
                            <a-select-option label="请选择" value="">请选择</a-select-option >
                            <a-select-option
                                    v-for="item in data.environmentId_option"
                                    :key="item.id"
                                    :value="item.id"
                            >{{item.environmentName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>

            <a-row v-if="props.infoData?.type == 'submission'">
                 <a-col :span="8">
                    <a-form-item label="测试单号：" name="logContent">
                        <a-input autocomplete="off" type="textarea" :autosize="{minRows:2,maxRows:8}" :show-word-limit="true" maxlength="1000" v-model:value="data.ruleForm.logContent"></a-input>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- 需求审核 -->
            <a-row v-if="props.infoData?.type == 'audit'">
                 <a-col :span="8">
                    <a-form-item label="审批意见：" name="demandUuid" >
                        <a-select v-model:value="data.ruleForm.demandUuid" placeholder="请选择" style="width: 300px">
                            <a-select-option label="请选择" value="">请选择</a-select-option>
                            <a-select-option
                                    v-for="item in data.demandUuid_options"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                            >
                                {{item.name}}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- 任务发布 -->
            <a-row v-if="props.infoData?.type == 'release'">
                 <a-col :span="8">
                    <a-form-item label="发布意见：" name="auditType">
                        <a-select v-model:value="data.ruleForm.auditType" placeholder="请选择">
                            <a-select-option label="请选择" value="">请选择</a-select-option>
                            <a-select-option label="发布退回" value="2">发布退回</a-select-option>
                            <a-select-option label="立即发布" value="4">立即发布</a-select-option>
                            <a-select-option label="暂停发布" value="5">暂停发布</a-select-option>
                            <a-select-option label="恢复发布" value="6">恢复发布</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row v-if="props.infoData?.type == 'release'">
                 <a-col :span="8">
                    <a-form-item label="审核意见：" name="logContents">
                        <a-input autocomplete="off" type="textarea" v-model:value="data.ruleForm.logContents" :autosize="{minRows:2,maxRows:8}" :show-word-limit="true" maxlength="1000"></a-input>
                    </a-form-item>
                </a-col>
            </a-row>
            <!-- 需求审核 -->
            <a-row v-if="isAuditRelated">
                 <a-col :span="8">
                    <a-form-item label="审核意见：" name="logContents">
                        <a-textarea :auto-size="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="1000"
                                v-model:value="data.ruleForm.logContents"></a-textarea>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
    </div>
</template>
<script setup lang="ts">
    import {
        userOrgList,
        getEnvironmentIdsOption,
        fileUpLoad,
        getDemandAppNo,
        getAuditTypeList,
        policyAudit,
        policySwitch,
        getDemandApplyByUuid,
    } from "@/api/task";
    import { audit } from "@/api/baseline_Management";
    import dayjs from "dayjs";
    import { computed } from 'vue';
    const message = inject('message')
    const modal = inject('modal')
    const ruleForm = ref(null);
    const policySwitchValue = ref('0');

    const data = ref({
        fileList: [],
        model_type_option: [],
        model_type_options: [],
        environmentId_option: [],
        environmentIds_Option: [],
        ruleArrsel: [],
        demandUuid_option: [],
        demandUuid_options: [],
        policyCheck_options: [],
        value: "",
        ruleForm: {
            type: [],
            file: "",
            appNo: "",
            applyOrgId: "",
            businessLine: "",
            demandName: "",
            describeInfo: "",
            uuid: "",
            orgName: "",
            createdName: "",
            creatdeId: "",
            environmentIds: [],
            subType: "",
            logContent: "", //规则测试
            demandUuid: "",
            auditType: "",
            logContents: "", //任务审核
            environmentId: "",
        },
        upForm: null,
        rules: {
            demandName: [
                { required: true, message: "请输入任务名称", trigger: "blur" },
                {
                    min: 1,
                    max: 50,
                    message: "长度在 1 到 30 个字符",
                    trigger: "blur",
                },
            ],
            applyOrgId: [
                { required: true, message: "请选择归属机构", trigger: "change" },
            ],
            describeInfo: [
                { required: true, message: "请输入描述内容", trigger: "blur" },
                {
                    min: 1,
                    max: 500,
                    message: "长度在 1 到 500 个字符",
                    trigger: "blur",
                },
            ],
            subType: [
                {
                    required: true,
                    message: "至少选择一个审批意见",
                    trigger: "blur",
                },
            ],
            logContent: [
                { required: true, message: "请输入测试单号", trigger: "blur" },
                {
                    min: 1,
                    max: 1000,
                    message: "长度在 1 到 1000 个字符",
                    trigger: "blur",
                },
            ],
            logContents: [
                { required: true, message: "请输入审核意见", trigger: "blur" },
                {
                    min: 1,
                    max: 1000,
                    message: "长度在 1 到 1000 个字符",
                    trigger: "blur",
                },
            ],
            demandUuid: [
                {
                    required: true,
                    message: "至少选择一个审批意见",
                    trigger: "blur",
                },
            ],

            desc: [{ required: true, message: "请输入描述内容", trigger: "blur" }],
            environmentIds: [
                {
                    type: "array",
                    required: true,
                    message: "至少选择一个发布环境",
                    trigger: "change",
                },
            ],
            environmentId: [
                {
                    required: true,
                    message: "至少选择一个发布环境",
                    trigger: "change",
                },
            ],
            auditType: [
                {
                    required: true,
                    message: "至少选择一个发布意见",
                    trigger: "blur",
                },
            ],
            demUid: "",
            businessUuid: "",
            policySwitch: 0,
        },
    });
    const props = defineProps({
        infoData: {
            type: Object,
        },
    });
    const route = useRoute()
    watch(
        () => props.infoData,
        (newValue) => {
            if (newValue) {
                data.value.demUid = props.infoData.uuid;
                data.value.businessUuid = props.infoData.businessCode;

                userOrgList({
                    businessCode: props.infoData.businessCode,
                }).then((res) => {
                    data.value.model_type_options = res.data;
                    data.value.ruleForm.businessLine = props.infoData.businessCode;
                    if (
                        route.path.indexOf('ruleAdjustment') !== -1 && props.infoData.type === 'adjustment'
                    ) {
                        getEnvironmentIds("environmentIds_Option", 0);
                    }
                });
                policySwitch().then((res) => {
                    policySwitchValue.value = res.data;
                });
                if (props.infoData.type!=="addPolicy" && props.infoData.type!=="add"){
                    getDemandApplyByUuid({
                        uuid: props.infoData.uuid,
                        appNo: props.infoData.appNo,
                        bizKey: props.infoData.bizKey,
                    }).then((res) => {
                        if (res.code == 20000) {
                            data.value.ruleForm.uuid = res.data.uuid;
                            data.value.ruleForm.appNo = res.data.appNo;
                            data.value.ruleForm.bizKey = res.data.bizKey;
                            data.value.ruleForm.applyOrgId = res.data.applyOrgId;
                            data.value.ruleForm.businessLine = res.data.businessLine;
                            data.value.ruleForm.demandName = res.data.demandName;
                            data.value.ruleForm.describeInfo = res.data.describeInfo;
                            data.value.ruleForm.orgName = res.data.orgName;
                            data.value.ruleForm.createdName = res.data.createdName;
                            data.value.ruleForm.createdTime = res.data.createdTime;
                            data.value.ruleForm.createdId = res.data.createdId;
                            data.value.ruleForm.subType = res.data.subType;
                            data.value.ruleForm.logContent = res.data.logContent;
                            data.value.ruleForm.auditType = res.data.auditType;
                            data.value.ruleForm.environmentId = res.data.environmentId;
                            data.value.ruleForm.demandApplyQO = res.data.demandApplyQO;
                            data.value.ruleForm.roleName = res.data.roleName;
                        }
                    });
                }

            }
        },
        {
            immediate: true,
        }
    );
    // 任务审核审批意见
    const getChange = (value) => {
        data.value.ruleForm.environmentId = "";
        if (value == 3) {
            getEnvironmentIds("environmentId_option", 1);
        }
    };
    const onChange = (file, fileList) => {
        if (fileList && fileList.length) {
            data.value.uploadHidden = true;
            data.value.$refs.upload.submit();
        }
    };
    // 新增获取自定义编号
    const getAppNo = () => {
        if (data.value.ruleForm.appNo == "") {
            getDemandAppNo().then((res) => {
                data.value.ruleForm.appNo = res.data;
            });
        }
    };
    //获取归属机构选项
    const getCreatedName = async () => {

    };
    const getEnvironmentIds = (data1, type) => {
        getEnvironmentIdsOption({
            businessLine: props.infoData.businessCode,
            environmentType: type,
        }).then((res) => {
            if(data1 === 'environmentId_option'){
                data.value.environmentId_option = res.data;
            }else{
                data.value.environmentIds_Option = res.data;
            }

        });
    };
    // 任务审批意见
    const approval = () => {
        // if (props.infoData.uuid && props.infoData.uuid !== "") {
        getAuditTypeList({
            demandUuid: props.infoData.uuid,
        }).then((res) => {
            //console.log(props.infoData.uuid);
            data.value.demandUuid_option = [];
            for (var i in res.data) {
                data.value.demandUuid_option.push({
                    name: res.data[i],
                    value: i,
                });
            }
        });
        // }
    };
    // 任务审核
    // getAuditTypeList(){
    //   getAuditTypeList({
    //     demandUuid :props.infoData.uuid
    //   }).then((res)=>{
    //     this
    //   })
    // },
    // 需求审核审批意见
    const requirement = () => {
        if (props.infoData.uuid && props.infoData.uuid !== "") {
            audit({
                demandUuid: props.infoData.uuid,
            }).then((res) => {
                data.value.demandUuid_options = [];
                for (var i in res.data) {
                    data.value.demandUuid_options.push({
                        name: res.data[i],
                        value: i,
                    });
                }
            });
        }
    };
    const compressImage = (file) => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                let width = img.width;
                let height = img.height;
                let maxWidth=1920
                let maxHeight=1080
                if (width > maxWidth) {
                    height *= maxWidth / width;
                    width = maxWidth;
                }
                if (height > maxHeight) {
                    width *= maxHeight / height;
                    height = maxHeight;
                }
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                canvas.toBlob(
                    blob => {
                        resolve(blob);
                    },
                    file.type,
                    0.8
                );
            };
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    };
    const uploadFile = (params) => {
        const file = params.file,
            name = file.name.substring(0, file.name.indexOf(".")),
            aExtension=['xlsx','xls','doc','docx','ppt','pptx','jpg','png','jpge','bmp','gif','webp','pdf','zip','rar','7z','tar','gz','bz2','txt'],
            aExtensionImg=['jpg','png','jpge','bmp','gif','webp'],
            resFil=aExtension.filter((item)=>{return file.name.indexOf(item)!==-1}),
            imgFil=aExtensionImg.filter((item)=>{return file.name.indexOf(item)!==-1})
        if(resFil.length===0){
            message.error("文件格式不正确！");
            params.onError('文件格式不正确');
            return;
        }
        if(imgFil.length>0){
            compressImage(file).then((item)=> {
                item.lastModifiedDate = new Date()
                item.name = file["name"]
                let aafile = new File([item], file["name"], {type: file["type"]});
                data.value.upForm = new FormData();
                data.value.upForm.append("file", aafile);
                // 上传
                modal.confirm({
                    title: '提示',
                    content: "是否确定上传名称为'" + file.name + "'的文件?",
                    okText: '确定',
                    cancelText: '取消',
                    type: 'warning',
                    onOk() {
                        submitUpload();
                        params.onSuccess({'code':200}, params);
                    }, onCancel() {
                        params.onError('已取消上传');
                        message.info("已取消上传");
                    }
                });
            })
        }else{
            data.value.upForm = new FormData();
            data.value.upForm.append("file", file);
            // 上传
            modal.confirm({
                title: '提示',
                content: "是否确定上传名称为'"+file.name+"'的文件?",
                okText: '确定',
                cancelText: '取消',
                type: 'warning',
                onOk() {
                    submitUpload();
                    params.onSuccess({'code':200}, params);
                },onCancel(){
                    params.onError('已取消上传');
                    message.info("已取消上传");
                }
            });
        }
    };
    // 上传文件
    const submitUpload = () => {
        // 政策填报生成临时任务号码
        var appNo = props.infoData?.appNo || data.value.ruleForm.appNo;
        if(props.infoData?.type == 'addPolicy' && appNo == ''){
            appNo = "TEMP" + (dayjs(new Date()).format('yyMMDDHHmmssSSS'));
            data.value.ruleForm.appNo = appNo;
        }
        fileUpLoad(
            data.value.upForm,
            appNo
        ).then((res) => {
            if (res.code === 20000) {
                message.success(res.data);
            } else {
                message.error(res.data);
            }
        });
    };


    const handleRemove = (file, fileList) => {
        //console.log(file, fileList);
    };
    const handlePreview = (file) => {
        //console.log(file);
    };
    const getPolicyCheckOptions = () =>{
        if (props.infoData.uuid && props.infoData.uuid !== "") {
            policyAudit({
                demandUuid: props.infoData.uuid,
            }).then((res) => {
                data.value.policyCheck_options = [];
                for (var i in res.data) {
                    data.value.policyCheck_options.push({
                        name: res.data[i],
                        value: i,
                    });
                }
            });
        }
    };
    const getPolicySwitch = async () => {

    };

    //定义页面ref参数
    const upload = ref(null);
    interface FileInfo {
        file: FileItem;
        fileList: FileItem[];
    }
    //文件处理
    interface FileItem {
        uid: string;
        name?: string;
        status?: string;
        response?: string;
        url?: string;
        type?: string;
        size: number;
        originFileObj: any;
    }
    const fileRemove=(info:FileInfo)=> {
        data.value.uploadHidden = false;
        info.fileList = [];
    }
    onMounted(() => {
        if (
            route.path.indexOf("taskAudit") !== -1
        ) {
            approval();
        }
        if (
            route.path.indexOf("requirementAudit") !== -1
        ) {
            requirement();
        }
        if (
            route.path.indexOf("taskAdd") !== -1
        ) {
            getAppNo();
        }
        if(route.path.indexOf("policyAudit") !== -1){
            getPolicyCheckOptions();
        }
    });
    defineExpose({
        data,
        ruleForm
    })

    // 计算属性：用于存储重复的条件判断
    //  表单：任务名称，归属机构，审核机构，申请人描述 不可编辑只读条件
    const isReadOnly = computed(() => {
        return props.infoData?.uuid
            ? true
            : false ||
              props.infoData?.type == 'info' ||
              props.infoData?.type == 'adjustment' ||
              props.infoData?.type == 'test' ||
              props.infoData?.type == 'submission' ||
              props.infoData?.type == 'updateRuleCheck' ||
              props.infoData?.type == 'release' ||
              props.infoData?.type == 'audit' ||
              props.infoData?.type == 'taskInquiry' ||
              props.infoData?.type == 'policyInfo' ||
              props.infoData?.type == 'policyAudit';
    });

    // 计算属性：用于表单显示条件
    //表单：可编辑描述 显示条件
    const isAddOrUpdate = computed(() => {
        return props.infoData?.type == 'add' ||
               props.infoData?.type == 'addPolicy' ||
               props.infoData?.type == 'update';
    });
    //表单：不可编辑描述,审核机构 显示条件
    const isNotAddOrUpdate = computed(() => {
        return props.infoData?.type !== 'add' &&
               props.infoData?.type !== 'addPolicy' &&
               props.infoData?.type !== 'update';
    });
    //表单：可编辑任务名称，归属机构 显示条件
    const isAddWithPolicyOrAddPolicy = computed(() => {
        return (props.infoData?.type == 'add' && policySwitchValue.value != '1') ||
               props.infoData?.type == 'addPolicy' ||
               props.infoData?.type == 'update';
    });
    //表单：不可编辑任务名称，归属机构 显示条件
    const isNotAddWithPolicyOrAddPolicy = computed(() => {
        return (props.infoData?.type !== 'add' || policySwitchValue.value == '1') &&
               props.infoData?.type !== 'addPolicy' &&
               props.infoData?.type !== 'update';
    });
    //表单：文件上传按钮 显示条件
    const isFileUploadVisible = computed(() => {
        return props.infoData?.type == 'add' ||
               props.infoData?.type == 'update' ||
               props.infoData?.type == 'audit' ||
               props.infoData?.type == 'adjustment' ||
               props.infoData?.type == 'submission' ||
               props.infoData?.type == 'addPolicy' ||
               props.infoData?.type == 'policyAudit';
    });
    //表单：审核意见 显示条件
    const isAuditRelated = computed(() => {
        return props.infoData?.type == 'updateRuleCheck' ||
               props.infoData?.type == 'audit' ||
               props.infoData?.type == 'policyAudit';
    });
</script>

<style scoped>
:deep(.ant-form-item-label > label) {
    font-weight: bold;
}

.describe-text {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
