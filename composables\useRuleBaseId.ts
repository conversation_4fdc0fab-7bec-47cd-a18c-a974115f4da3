/**
 * @file 根据路由参数和组件属性设置规则库id和任务id
 */
export default function (props: any,ruleBaseId: Ref<string>,demandUuid: Ref<string>) {
    const route = useRoute();

    onMounted(() => {
        if(props.ruleBaseId.indexOf('demandUuid') !== -1) {
            demandUuid.value = props.ruleBaseId.split('demandUuid-')[1].split('-businessUuid-')[0];
        }

        if (props.ruleBaseId === 'all' || props.ruleBaseId.indexOf('demandUuid') !== -1) {
            ruleBaseId.value = route.query.ruleBaseId;
        }else{
            ruleBaseId.value = props.ruleBaseId;
        }

    })
}
