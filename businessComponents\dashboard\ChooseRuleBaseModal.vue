<!-- 选择规则库对话框 -->
<script setup lang="ts">
import { list as getRuleBaseList } from '@/api/rule_base'
import { debounce } from 'lodash'

interface Props {
  modelValue: boolean
  title: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
  (e: 'select', ruleBase: RuleBase): void
}

interface RuleBase {
  uuid: string
  name: string
  chineseName: string
  engName: string
  description: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchValue = ref('')
const ruleBaseList = ref<RuleBase[]>([])
const loading = ref(false)

// 使用防抖处理搜索
const handleSearch = debounce(async (value: string) => {
  loading.value = true
  try {
    const res = await getRuleBaseList({
      several: 7,
      page: 1,
      chineseName: value.trim()
    })
    ruleBaseList.value = res.data.data
  } catch (error) {
    console.error('搜索规则库失败:', error)
  } finally {
    loading.value = false
  }
}, 300)

// 监听搜索关键词变化
watch(searchValue, (value) => {
  handleSearch(value)
})

// 监听对话框打开
watch(() => props.modelValue, async (newVal) => {
  if (newVal) {
    await handleSearch('')
  } else {
    searchValue.value = ''
    ruleBaseList.value = []
  }
})

// 处理选择规则库
const handleSelect = (ruleBase: RuleBase) => {
  emit('select', ruleBase)
  emit('update:modelValue', false)
}

// 处理关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}
</script>

<template>
  <a-modal v-model:open="props.modelValue" title="" :closable="false" :footer="null" centered
    wrap-class-name="ant-modal-confirm ant-modal-confirm-info styles-module_modal_8nT+9">
    <div class="styles-module_selector_Z3+Wx" style="height: 500px;">
      <div class="modal-header">
        <button type="button" aria-label="Close" class="ant-modal-close" @click="handleClose">
          <span class="ant-modal-close-x">
            <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
              class="larkui-icon larkui-icon-close-outlined">
              <path
                d="M34.41 34.499c3.906-3.905 10.238-3.905 14.143 0l79.903 79.902 79.903-79.902c3.905-3.905 10.237-3.905 14.142 0 3.905 3.905 3.905 10.237 0 14.142l-79.903 79.903 79.903 79.903c3.834 3.834 3.904 10.008.21 13.927l-.21.215c-3.905 3.906-10.237 3.906-14.142 0l-79.903-79.903-79.903 79.903c-3.905 3.906-10.237 3.906-14.142 0-3.906-3.905-3.906-10.237 0-14.142l79.902-79.903L34.41 48.641c-3.835-3.834-3.904-10.007-.21-13.927Z"
                fill="currentColor" fill-rule="evenodd"></path>
            </svg>
          </span>
        </button>
        <h3 class="styles-module_title_Misjv">{{ props.title }}</h3>
        <p class="GlobalBookSelector-module_tip_RhjZ7">选择一个规则库</p>
      </div>
      <div class="modal-content">
        <div class="GlobalBookSelector-module_searchBox_c2zph">
          <span class="ant-input-affix-wrapper larkui-input-search">
            <span class="ant-input-prefix">
              <span role="img" aria-label="search" class="anticon anticon-search">
                <svg viewBox="64 64 896 896" focusable="false" data-icon="search" width="1em" height="1em" fill="currentColor"
                  aria-hidden="true">
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z">
                  </path>
                </svg>
              </span>
            </span>
            <input v-model="searchValue" autocomplete="off" placeholder="请输入规则库名称进行搜索" spellcheck="true"
              class="ant-input" type="text">
          </span>
        </div>
        <div class="rule-base-list-container">
          <div v-if="loading" style="display: flex; justify-content: center; padding: 40px 0;">
            <a-spin size="large" />
          </div>
          <ul v-else class="GlobalBookSelector-module_bookSelector_6khZ0 global-book-selector-list"
            data-testid="global-book-selector-list">
            <li v-for="ruleBase in ruleBaseList" :key="ruleBase.uuid"
              class="GlobalBookSelector-module_bookSelectorItem_EBxLN" @click="handleSelect(ruleBase)">
              <span class="lark-book-title">
                <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
                  class="larkui-icon larkui-icon-book-type-default icon-svg book-icon larkui-tooltip index-module_size_wVASz"
                  group="[object Object]" data-name="BookTypeDefault" style="width: 24px; height: 24px; min-width: 24px;">
                  <g fill="none" fill-rule="evenodd">
                    <path d="M4.75 1.267h10.5a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-14a2 2 0 0 1 2-2Z"
                      fill="#C4DCFF"></path>
                    <path d="M4.75 1.267h2.215v18H5.75a3 3 0 0 1-3-3v-13a2 2 0 0 1 2-2Z" fill="#679FF4"></path>
                    <path stroke="#397ABD" d="M7.25 1.1v17.667"></path>
                    <path stroke="#397ABD" stroke-linecap="round" stroke-linejoin="round" d="M10.85 5.394h3.4"></path>
                    <path
                      d="M4.25 1.267h11.5a1.5 1.5 0 0 1 1.5 1.5v14.5a1.5 1.5 0 0 1-1.5 1.5H4.25a1.5 1.5 0 0 1-1.5-1.5v-14.5a1.5 1.5 0 0 1 1.5-1.5Z"
                      stroke="#397ABD"></path>
                  </g>
                </svg>
                <span class="book-name">
                  <span>{{ ruleBase.chineseName }}</span>
                </span>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.styles-module_selector_Z3+Wx {
  display: flex;
  flex-direction: column;
}

.modal-header {
  flex-shrink: 0;
}

.modal-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.rule-base-list-container {
  margin-top: 16px;
  flex-grow: 1;
}

.GlobalBookSelector-module_bookSelector_6khZ0 {
  height: 100%;
}
</style>
