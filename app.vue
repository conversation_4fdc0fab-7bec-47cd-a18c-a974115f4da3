<script setup lang="ts">
import "@/assets/css/deps.css"
import "@/assets/css/larkui.css"
import "@/assets/css/pc.css"
import "@/assets/css/c__SideBarMenu.css"
import "@/assets/css/c__DashboardOrgWiki.css"
import "@/assets/css/c__RepositorySelector.css"
import "@/assets/css/c__NewRepoModal.css"
import "@/assets/css/c__ScopedSearch.css"
import "@/assets/css/p__search__routers__Global.css"
import "@/assets/css/p__dashboard__routers__App.css"
import "@/assets/css/c__DeskTopDashboardSideBar~c__SideBar.css"
import "@/assets/css/c__DocEntry.css"
import "@/assets/css/doc_editor.css"
import "@/assets/css/p__doc__routers.css"
import "@/assets/css/c__Reading.css"
import "@/assets/css/c__DashboardRecent.css"
import "@/assets/css/p__dashboard__modules__group__routers__App.css"
import "@/assets/css/c__DashboardBooks.css"
import "@/assets/css/c__BookOverview.css"
import "@/assets/css/c__CatalogModule.css"
import "@/assets/css/c__BookContributors.css"
import "@/assets/css/c__DocReader.css"
import "@/assets/css/c__ReaderMeta.css"
import "@/assets/css/c__DocCreationGuide~c__DocWizard.css"
import "@/assets/css/c__CommonEditPage.css"
import "@/assets/css/c__DocCreationGuide~c__DocWizard.css"
import "@/assets/css/c__CommonEditPage.css"
import "@/assets/css/c__DashboardCollects.css"
import "@/assets/css/c__TemplateDocViewer.css"
import "@/assets/css/c__TemplateSelector.css"
import "@/assets/css/c__AIDocModal.css"
import "@/assets/css/c__DashboardRecycles.fccadeda.chunk.css"
import "@/assets/css/p__template__routers__editors__doc.css"
import "@/assets/css/eruleStyle.scss"
import "@/assets/css/global.css"
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import settings from '@/settings'

dayjs.locale('zh-cn');

const route = useRoute()

// 监听路由元信息中的标题变化
watch(() => route.meta.title, (newTitle) => {
  useHead({
    title: newTitle ? newTitle + ' · ' + settings.SITE_NAME : settings.SITE_NAME
  })
}, { immediate: true })
</script>

<template>
  <a-config-provider :locale="zhCN">
    <a-style-provider :transformers="[{ visit: () => ({}) }]"><!-- 禁用 ant-design-vue 自带样式 -->
      <a-app><!-- 解决通过message.error等调出的提示信息组件没有禁用ant-design-vue 自带样式的问题 -->
        <AppPage>
          <NuxtLayout>
            <NuxtPage />
          </NuxtLayout>
        </AppPage>
      </a-app>
    </a-style-provider>
  </a-config-provider>
</template>
