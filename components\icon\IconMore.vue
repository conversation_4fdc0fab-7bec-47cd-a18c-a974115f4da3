<!-- 竖向三个点表示的更多 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" 
        class="larkui-icon larkui-icon-more icon-svg index-module_size_wVASz"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path d="M128 227.008c11.046 0 20-8.954 20-20s-8.954-20-20-20-20 8.954-20 20 8.954 20 20 20ZM128 148c11.046 0 20-8.954 20-20s-8.954-20-20-20-20 8.954-20 20 8.954 20 20 20Zm0-78.992c11.046 0 20-8.954 20-20s-8.954-20-20-20-20 8.954-20 20 8.954 20 20 20Z" 
            fill="currentColor" fill-rule="nonzero"></path>
    </svg>
</template> 