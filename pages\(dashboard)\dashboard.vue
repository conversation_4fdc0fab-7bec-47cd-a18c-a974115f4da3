<!-- 开始页 -->
<script setup lang="ts">
import DashboardAudit from "@/businessComponents/dashboard/DashboardAudit.vue";
import DashboardDefault from "@/businessComponents/dashboard/DashboardDefault.vue";
import { getUserButtons } from "@/api/role";
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
const userType = ref('');
onMounted(async() => {
// 获取菜单信息并判断进入哪个开始页
/*let res = await getUserButtons();
if(res.data !== 'admin'){
  let roleGroup = [];
  let dataArr = Object.values(res.data);
  for (let i = 0; i < dataArr.length; i++) {
    let menu = dataArr[i] as Array<{menuPath: string, name: string}>;
    for (let j = 0; j < menu.length; j++) {
      roleGroup.push(menu[j].menuPath + '|' + menu[j].name);
    }
  }
  //如果没有规则编辑权限
  if(roleGroup.indexOf(RULE_PERMISSION.RULE.EDIT) === -1){
    userType.value = 'audit'
  }
}
if(userType.value === ''){
    userType.value = 'default'
}*/
})
</script>

<template>
    <DashboardDefault />
    <!--注释掉统计开始页-->
    <!--<DashboardAudit  v-if="userType === 'audit'"/>-->
</template>
