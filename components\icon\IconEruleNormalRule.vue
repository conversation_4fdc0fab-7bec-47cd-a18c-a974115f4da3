<!-- 普通规则图标组件 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 16,
    class: ''
})
</script>

<template>
    <svg :width="props.size" :height="props.size" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'icon-svg',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path d="M102.006154 50.963692c-32.571077 0-59.076923 26.505846-59.076923 59.076923v787.692308c0 32.571077 26.505846 59.076923 59.076923 59.076923h787.692308c32.571077 0 59.076923-26.505846 59.076923-59.076923v-787.692308c0-32.571077-26.505846-59.076923-59.076923-59.076923h-787.692308z m787.692308 945.23077h-787.692308a98.540308 98.540308 0 0 1-98.461539-98.461539v-787.692308c0-54.311385 44.110769-98.461538 98.461539-98.461538h787.692308c54.311385 0 98.461538 44.150154 98.461538 98.461538v787.692308c0 54.350769-44.150154 98.461538-98.461538 98.461539z" fill="#2c2c2c"></path>
        <path d="M275.692308 204.169846a15.753846 15.753846 0 1 1-15.753846-17.329231 16.541538 16.541538 0 0 1 15.753846 17.289847v0.039384z m-55.532308 244.972308c26.387692-2.363077 30.326154-7.483077 30.326154-31.507692v-85.464616c0-26.387692-2.756923-28.750769-26.387692-32.689231v-5.513846c16.108308-3.072 31.665231-8.664615 46.08-16.541538v140.209231c0 24.024615 4.332308 29.144615 31.113846 31.507692v5.513846H220.16v-5.513846z" fill="#2c2c2c"></path>
        <path d="M319.803077 449.142154c25.206154 0 28.750769-6.695385 28.750769-32.689231v-118.153846H319.015385v-4.726154l11.027692-8.664615h20.086154c-0.827077-14.808615 0.393846-29.617231 3.544615-44.11077 5.986462-18.038154 16.541538-34.185846 30.72-46.867692a90.269538 90.269538 0 0 1 44.110769-21.661538c14.572308 0 33.870769 12.996923 33.87077 21.267692 0 8.270769-9.452308 12.209231-12.603077 12.209231a5.828923 5.828923 0 0 1-4.726154-2.756923 51.357538 51.357538 0 0 0-35.84-11.815385 32.334769 32.334769 0 0 0-24.418462 10.24 123.234462 123.234462 0 0 0-14.178461 64.590769v18.904616h50.018461c0.393846 1.969231 0.393846 3.938462 0 5.907692a8.270769 8.270769 0 0 1-4.332307 7.483077H369.427692v118.153846c0 25.6 3.150769 30.72 39.384616 33.476923v4.726154h-86.646154l-2.363077-5.513846zM447.803077 440.871385a18.510769 18.510769 0 0 1 17.683692-19.298462h0.393846c10.24 0 18.510769 8.270769 18.51077 18.510769v0.787693a17.723077 17.723077 0 0 1-18.51077 18.510769 17.329231 17.329231 0 0 1-18.116923-16.462769v-2.048M545.870769 440.871385a18.510769 18.510769 0 0 1 17.683693-19.298462h0.393846c10.24 0 18.510769 8.270769 18.510769 18.510769v0.787693a17.723077 17.723077 0 0 1-18.510769 18.510769 17.329231 17.329231 0 0 1-18.116923-16.462769v-2.048M643.938462 440.871385a18.510769 18.510769 0 0 1 17.683692-19.298462h0.393846c10.24 0 18.510769 8.270769 18.510769 18.510769v0.787693a17.723077 17.723077 0 0 1-18.510769 18.510769 17.329231 17.329231 0 0 1-18.116923-16.462769v-2.048M246.547692 719.714462h85.07077c10.633846 0 10.633846-4.332308 10.633846-6.695385a43.716923 43.716923 0 0 0-47.261539-43.716923c-19.298462 0.393846-41.353846 16.935385-47.655384 50.412308h-0.787693z m118.153846 81.526153c-15.36 19.337846-37.454769 32.137846-61.833846 35.84a78.769231 78.769231 0 0 1-78.848-78.690461c0-1.181538 0-2.363077 0.07877-3.623385a95.704615 95.704615 0 0 1 83.101538-98.067692 60.258462 60.258462 0 0 1 60.652308 57.895385c0 8.270769 0 9.452308-6.695385 11.027692-6.695385 1.575385-59.470769 5.513846-116.184615 7.089231 0 62.621538 37.021538 85.070769 65.378461 85.070769a65.378462 65.378462 0 0 0 49.624616-22.843077l4.726153 6.301538zM385.969231 827.234462c25.993846 0 30.326154-7.483077 30.326154-33.083077v-192.984616c0-28.356923-3.150769-31.507692-29.538462-33.870769v-5.12a169.747692 169.747692 0 0 0 49.230769-13.784615v245.366153c0 25.6 3.938462 31.507692 31.113846 33.083077v5.513847H385.969231v-5.12zM495.064615 776.428308c7.876923 26.387692 26.387692 51.593846 51.593847 51.593846a30.326154 30.326154 0 0 0 32.68923-32.689231c0-21.661538-13.784615-32.295385-34.264615-39.384615-20.48-7.089231-48.836923-23.630769-48.836923-49.624616a53.169231 53.169231 0 0 1 57.383384-48.600615l1.693539 0.157538c11.027692-0.275692 21.937231 2.048 31.901538 6.695385 3.465846 12.957538 6.104615 26.112 7.876923 39.384615l-4.332307 5.12c-6.695385-20.873846-22.055385-41.747692-42.929231-41.747692a28.750769 28.750769 0 0 0-30.72 30.326154c0 20.48 20.086154 30.326154 36.627692 36.627692 28.750769 11.421538 49.230769 25.993846 49.23077 52.775385a55.926154 55.926154 0 0 1-59.785847 51.790769l-2.048-0.196923a66.166154 66.166154 0 0 1-44.504615-13.784615 265.609846 265.609846 0 0 1-6.301538-44.898462l4.726153-3.544615zM649.846154 719.714462h85.464615c10.24 0 10.24-4.332308 10.24-6.695385a43.716923 43.716923 0 0 0-44.032-43.401846l-2.048 0.078769c-20.48 0-42.535385 16.541538-48.836923 50.018462h-0.787692z m118.153846 81.526153a96.098462 96.098462 0 0 1-61.44 35.84 78.769231 78.769231 0 0 1-78.848-78.690461c0-1.181538 0-2.363077 0.078769-3.623385a95.704615 95.704615 0 0 1 82.707693-98.067692 60.258462 60.258462 0 0 1 61.046153 57.895385c0 8.270769 0 9.452308-7.08923 11.027692-7.089231 1.575385-59.470769 5.513846-116.184616 7.089231 0 62.621538 37.021538 85.070769 65.378462 85.070769 19.219692 0.039385 37.494154-8.270769 50.018461-22.843077l4.332308 6.301538z" fill="#2c2c2c"></path>
    </svg>
</template> 