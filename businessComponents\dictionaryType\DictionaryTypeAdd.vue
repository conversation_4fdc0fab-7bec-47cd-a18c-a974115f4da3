<!-- 新增字典类型 -->

<template>
    <div id="data_add">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
            <a-form-item label="代码" name="code">
                <a-input autocomplete="off" v-model:value="form.code" placeholder="请输入代码"></a-input>
            </a-form-item>
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="form.name" placeholder="请输入名称"></a-input>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { dictionsaveOrUpdate} from '@/api/dictionarytype'

const message = inject('message')

const router = useRouter();

const form = reactive({
    name: '',
    code:''
});


const rules = {
    code: [
        { required: true, message: '代码不能为空', trigger: 'change' }
    ],
    name: [
        { required: true, message: '名称不能为空', trigger: 'change' }
    ],
};


const defaultProps = {
    key: "datar",
};

onMounted(() => {

});


const submitFun = (callback) => {
    ruleForm.value.validate().then(() => {
        dictionsaveOrUpdate({
            ...form
        }).then((res) => {
            if (res.code === 20000) {
                message.success(res.data);
                if (typeof callback === 'function') {
                    callback(true);
                }
            } else {
                message.error(res.data);
            }
        })
    }).catch((error) => {
        console.log(error);
    })
};


const ruleForm = ref(null);

defineExpose({
    submitFun
});
</script>

<style lang="scss" scoped>
#data_add ::v-deep {

    .ant-input,
    .ant-select,
    .ant-upload-dragger,
    .ant-upload-list,
    .ant-textarea {
        width: 400px;
    }

    .ant-textarea {
        height: 400px;
    }

    .ant-upload-dragger {
        height: 100px;
    }

    .ant-upload-dragger .anticon-upload {
        margin: 0;
        line-height: 50px;
        font-size: 50px;
    }

    .jarUpload.hidden .ant-upload {
        display: none;
    }

    .ant-tabs {
        width: 70%;
        min-height: 100px;
        margin: 30px 30px 30px 120px;
    }

    .ant-textarea,
    textarea {
        height: 150px !important;
    }

    .ant-tabs-card {
        box-shadow: unset;
    }

    .params_form {
        border-bottom: 1px solid #dcdfe6;

        h4 {
            font-weight: unset;

            span {
                margin-left: 100px;
            }

            span.primary {
                color: #409eff;
            }

            span.danger {
                color: #f56c6c;
            }
        }

        label {
            color: #99a9bf;
        }

        span {
            color: #606266;
        }
    }

    .params_form:last-child {
        border-bottom: unset;
    }

    .form_flex_div,
    .form_flex_div .ant-form-item {
        display: flex;
        flex-direction: row;
        flex: 1;

        .ant-form-item {
            overflow: auto;
        }
    }

    .ant-tree {
        font-size: 14px;
    }

    .ant-main {
        padding: 0 0 10px 0;
    }
}
</style>
