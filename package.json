{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "postgenerate": "node scripts/postGenerate.mjs", "preview": "nuxt preview", "postinstall": "nuxt prepare", "serve": "npx serve .output/public"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/language": "^6.10.8", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.2", "@lezer/highlight": "^1.2.1", "axios": "^1.7.8", "bpmn-js": "^7.3.0", "codemirror": "^6.0.1", "echarts": "^5.6.0", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "markdown-it": "^14.1.0", "moment": "^2.30.1", "mutt": "^0.1.0", "nuxt": "^3.14.159", "qs": "^6.13.1", "raphael": "^2.3.0", "vue": "latest", "vue-codemirror": "^6.1.1", "vue-router": "latest", "vuex": "^4.1.0"}, "devDependencies": {"@ant-design-vue/nuxt": "^1.4.6", "@types/lodash": "^4.17.13", "@types/markdown-it": "^14.1.2", "babel-runtime": "^6.26.0", "classnames": "^2.5.1", "sass-embedded": "^1.80.6"}}