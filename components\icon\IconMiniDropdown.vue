<!-- 树形结构多层级展开/收起图标 -->

<script setup lang="ts">
    interface Props {
        size?: number
    }

    const props = withDefaults(defineProps<Props>(), {
        size: 16
    })
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-mini-dropdown icon-svg catalogTreeItem-module_collapseIcon_1ZFIu index-module_size_wVASz" data-name="MiniDropdown" style="width: 16px; min-width: 16px; height: 16px;"><path d="M180.58 108.184c3.84 3.839 3.905 10.023.196 13.941l-.195.2-38.184 38.185c-7.73 7.729-20.21 7.81-28.039.241l-.245-.241-38.184-38.184c-3.905-3.905-3.905-10.237 0-14.142 3.839-3.84 10.023-3.904 13.941-.195l.201.195 38.184 38.184 38.184-38.184c3.905-3.905 10.236-3.905 14.142 0Z" fill="currentColor" fill-rule="nonzero"></path></svg>
</template>
