<!-- 表单搜索功能组件 -->
<!-- 用于搜索列显示隐藏，表单列表项显示隐藏等功能 -->
<template>
    <a-tooltip v-if="columns.length > 0">
        <a-popover placement="bottomRight" trigger="click">
            <template #content>
                <a-checkbox-group style="display: grid;" v-model:value="checkedColumns" @change="changeTableList">
                    <a-checkbox v-for="item in columns" :key="item.key" :value="item.title" style="margin: 5px 0px 5px 0px">{{ item.title }}</a-checkbox>
                </a-checkbox-group>

            </template>
            <a-button shape="circle" size="small" style="float: right">
                <template #icon><AppstoreOutlined /></template>
            </a-button>
        </a-popover>

        <template #title><span>显隐列</span></template>
    </a-tooltip>
    <a-tooltip v-if="reloadFun">
        <a-button shape="circle" @click="reloadFun" size="small" style="float: right;margin-right: 5px">
            <template #icon><ReloadOutlined /></template>
        </a-button>
        <template #title><span>重置</span></template>
    </a-tooltip>
    <!--<a-tooltip v-if="searchFlag !== undefined">
        <a-button shape="circle" @click="showSearch" size="small" style="float: right;margin-right: 5px">
            <template #icon><SearchOutlined /></template>
        </a-button>
        <template #title><span>{{ searchFlag?'搜索框折叠':'搜索框展开'}}</span></template>
    </a-tooltip>-->
</template>
<script setup>

    const props = defineProps({
        columns: {
            type: Object,
        },
        checkedColumns: {
            type: Object,
        },
        reloadFun:Function
    })
    const emit = defineEmits(['handleSearchFlag','handleTableListFlag']);
    const searchFlag = ref(true);
    const showSearch = () => {
        searchFlag.value = !searchFlag.value;
        emit('handleSearchFlag',searchFlag.value);
    }

    const checkedColumns = ref([]);

    const changeTableList = (val) => {
        const filterValue = props.columns.filter(item => {
            if (val.includes(item.title)){
                // 当暴露值为true时，循环遍历的值会赋给filterValue
                return true
            }
            return false
        })
        emit('changeTableList',filterValue);
    }
    onMounted(()=>{
        if(!props.checkedColumns && props.columns){
            let filterValue = [];
            props.columns.forEach((item) => {
                //如果列表的checked不为false，则添加进默认列表
                if(item.checked !== false){
                    checkedColumns.value.push(item.title);
                    filterValue.push(item)
                }
            })
            emit('changeTableList',filterValue);
        }else{
            checkedColumns.value = props.checkedColumns;
        }

    })

</script>
