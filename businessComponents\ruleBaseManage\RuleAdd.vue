<!-- 规则新增 -->

<template>
    <div id="rule_add">
        <a-form :model="form" :rules="rules" ref="formRef" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="规则名称" name="ruleName">
                <a-input autocomplete="off" v-model:value="form.ruleName" placeholder="规则名称" />
            </a-form-item>
            <a-form-item label="规则编号" name="ruleNumber">
                <a-input autocomplete="off" v-model:value="form.ruleNumber" placeholder="规则编号" />
            </a-form-item>
            <a-form-item label="有效状态" name="validState">
                <a-select v-model:value="form.validState" placeholder="请选择">
                    <a-select-option v-for="item in validStateOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则类型" name="ruleType">
                <a-select v-model:value="form.ruleType" placeholder="请选择" @change="ruleTypeChange">
                    <a-select-option v-for="item in ruleTypeOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则优先级" v-show="salienceShow">
                <a-select v-model:value="form.priority" placeholder="请选择">
                    <a-select-option v-for="item in priorityOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则流类型" v-show="flowTypeShow" name="ruleFlowType">
                <a-select v-model:value="form.ruleFlowType" placeholder="请选择"  @change="ruleFlowTypeChange">
                    <a-select-option v-for="item in ruleFlowTypeOptions" :key="item.code" :value="item.code">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="规则描述">
                <a-textarea v-model:value="form.desc" :auto-size="{ minRows: 2, maxRows: 8 }" />
            </a-form-item>
            <a-form-item label="生效时间：" name="effectDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="effectDate"
                        v-model:pubTime="effectTime"
                        @change="(date, time) => handleEffectDateChange(date, time)"
                    />
                </div>
            </a-form-item>
            <a-form-item label="失效时间：" name="expiredDate" >
                <div style="display: flex; width: 400px;">
                    <DateTimePicker
                        v-model:pubDate="expiredDate"
                        v-model:pubTime="expiredTime"
                        @change="(date, time) => handleExpiredDateChange(date, time)"
                    />
                </div>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { getDicValueByTypeCode, ruleSaveOrUpdate, templateTreeList } from '@/api/rule_base';
import {ref} from "vue";

const message = inject('message')

const props = defineProps({
    ruleBaseId: {
        type: String,
        default: () => '',
    },
    rulePackageId: {
        type: String,
        default: () => '',
    },
})

const form = reactive({
    ruleName: '',
    validState: '0',
    ruleType: '1',
    priority: '0',
    desc: '',
    ruleNumber: '',
    ruleFlowType:'',
});

// 生效时间相关变量
const effectDate = ref('');
const effectTime = ref('');

// 失效时间相关变量
const expiredDate = ref('');
const expiredTime = ref('');

const rules = reactive({
    ruleName: [
        { required: true, message: '不能为空', trigger: 'blur' },
        { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\d\(\)\（\）、\-\%]+$/, message: '规则名称只能包括中文、数字、字母、下划线、中英文小括号和、-% 符号' },
    ],
    ruleNumber: [
        { pattern: /^[0-9a-zA-Z]{2,20}$/, message: '规则编号长度必须在2-50之间,且只能包含字母和数字' },
    ],
    validState: [{ required: true, message: '不能为空', trigger: 'blur' }],
    ruleType: [{ required: true, message: '不能为空', trigger: 'blur' }],
    desc: [{ min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }],
});

const validStateOptions = ref([]);
const ruleTypeOptions = ref([]);
const priorityOptions = ref([]);
const ruleFlowTypeOptions = ref([]);
const ruleTreeOptions = ref([]);
const defaultKey = ref([]);
const templateUuid = ref(null);
const flowTypeShow = ref(false);
const salienceShow = ref(true);
const formRef = ref(null);

const ruleBaseId = ref('');
const demandUuid = ref('');

const ruleTypeChange = (val) => {
    if (val === '5') {
        rules.ruleFlowType = [{ required: true, message: '不能为空', trigger: 'change' }];
        salienceShow.value = false;
        flowTypeShow.value = true;
    } else {
        salienceShow.value = true;
        flowTypeShow.value = false;
        delete rules['ruleFlowType'];
        form.priority = '0';
    }
};

const ruleFlowTypeChange = (val) => {
    if(val == "1") {
        form.priority = "1";
    } else {
        form.priority = "0";
    }
}
const getDicValue = (type, fn) => {
    getDicValueByTypeCode({ typeCode: type }).then((res) => {
        fn(res.data);
    });
};

const handleSubmit = (callback) => {
    formRef.value.validate()
        .then(() => {
            // 验证时间格式
            if (effectDate.value && !effectTime.value) {
                message.error('请选择完整的生效时间');
                return Promise.reject('生效时间不完整');
            }
            if (expiredDate.value && !expiredTime.value) {
                message.error('请选择完整的失效时间');
                return Promise.reject('失效时间不完整');
            }

            // 验证时间先后顺序
            const effectDateTime = new Date(`${effectDate.value} ${effectTime.value}:00`);
            const expiredDateTime = new Date(`${expiredDate.value} ${expiredTime.value}:00`);
            if(effectDate.value && expiredDate.value){
                if (effectDateTime >= expiredDateTime) {
                    message.error('失效时间必须晚于生效时间');
                    return Promise.reject('时间顺序错误');
                }
            }


            // 格式化日期为后端期望的格式
            const formatDate = (date) => {
                const d = new Date(date);
                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const hours = String(d.getHours()).padStart(2, '0');
                const minutes = String(d.getMinutes()).padStart(2, '0');
                const seconds = String(d.getSeconds()).padStart(2, '0');
                const milliseconds = String(d.getMilliseconds()).padStart(3, '0');
                const timezone = '+0800'; // 中国时区
                return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}${timezone}`;
            };

            if (ruleBaseId.value) {
                const params = {
                    ruleName: form.ruleName.trim(),
                    ruleNumber: form.ruleNumber,
                    validStatus: form.validState,
                    type: form.ruleType,
                    salience: form.priority,
                    descs: form.desc,
                    folderUuid: props.rulePackageId==='all'?'':props.rulePackageId,
                    engUuid: ruleBaseId.value,
                    uuid: '',
                    status: '2',
                    effectDate: formatDate(new Date(`${effectDate.value} ${effectTime.value}:00`)),
                    expiredDate: formatDate(new Date(`${expiredDate.value} ${expiredTime.value}:00`)),
                };

                return ruleSaveOrUpdate(params, ruleBaseId.value, demandUuid.value, templateUuid.value)
                    .then((res) => {
                        const reg = new RegExp(/^[0-9a-zA-Z]*$/g);
                        if (res.code === 20000 && reg.test(res.data)) {
                            message.success('新增成功！');
                            if (typeof callback === 'function') {
                                callback(res.data);
                            }
                        } else {
                            message.error(res.data || '保存失败');
                            return Promise.reject(res.data);
                        }
                    })
                    .catch(error => {
                        console.error('保存失败:', error);
                        message.error('保存失败，请检查输入数据是否正确');
                        return Promise.reject(error);
                    });
            }
        })
        .catch((error) => {
            console.log('验证失败:', error);
        });
};

const _setDefauktKey = (arr, fn) => {
    arr.forEach((v) => {
        fn(v);
        if (v.children && v.children.length) {
            _setDefauktKey(v.children, fn);
        }
    });
};
useRuleBaseId(props,ruleBaseId,demandUuid)
onMounted(() => {
    getDicValue('ruleValidStatus', (arr) => {
        validStateOptions.value = arr;
    });
    getDicValue('type_rule', (arr) => {
        ruleTypeOptions.value = arr;
    });
    getDicValue('salience_rule', (arr) => {
        priorityOptions.value = arr;
    });
    getDicValue('ruleFlowType', (arr) => {
        ruleFlowTypeOptions.value = arr;
    });
    templateTreeList({
        businessLine: '',
        ifTemplateLib: '1',
    }).then((res) => {
        const data = res.data;
        data.forEach((item) => {
            if (item.uuid == '') {
                item.uuid = item.engUuid;
            }
        });
        ruleTreeOptions.value = data;
        _setDefauktKey(data, (v) => {
            if (v.state === 'open') {
                defaultKey.value.push(v.uuid);
            }
        });
    });
});

// 添加时间变化处理函数
const handleEffectDateChange = (date, time) => {
    effectDate.value = date;
    effectTime.value = time;
};

const handleExpiredDateChange = (date, time) => {
    expiredDate.value = date;
    expiredTime.value = time;
};

defineExpose({
    handleSubmit,
    form,
    ruleTypeChange
});
</script>

<style lang="scss" scoped>
#rule_add {
    :deep(.ant-input),
    :deep(.ant-select),
    :deep(.ant-upload-dragger),
    :deep(.ant-upload-list),
    :deep(.ant-input-textarea) {
        width: 100%;
        max-width: 400px;
    }

    :deep(.ant-input-textarea) {
        height: 120px !important;
        resize: none;
    }

    :deep(.ant-upload-dragger) {
        height: 100px;
    }

    :deep(.ant-upload-dragger .anticon-upload) {
        margin: 0;
        line-height: 50px;
        font-size: 50px;
    }

    :deep(.jarUpload.hidden .ant-upload) {
        display: none;
    }

    :deep(.ant-tabs) {
        width: 70%;
        min-height: 100px;
        margin: 15px 15px 15px 60px;
    }

    :deep(.ant-tabs-card) {
        box-shadow: unset;
    }

    :deep(.params_form) {
        border-bottom: 1px solid #dcdfe6;

        h4 {
            font-weight: unset;

            span {
                margin-left: 100px;
            }

            span.primary {
                color: #409eff;
            }

            span.danger {
                color: #f56c6c;
            }
        }

        label {
            color: #99a9bf;
        }

        span {
            color: #606266;
        }
    }

    :deep(.params_form:last-child) {
        border-bottom: unset;
    }

    :deep(.form_flex_div),
    :deep(.form_flex_div .ant-form-item) {
        display: flex;
        flex-direction: row;
        flex: 1;

        .ant-form-item {
            overflow: auto;
        }
    }

    :deep(.ant-form) {
        padding: 20px;
    }

    :deep(.ant-form-item) {
        margin-bottom: 16px;
    }

    :deep(.ant-form-item:last-child) {
        margin-bottom: 0;
    }

    :deep(.ant-form-item-explain-error) {
        color: #ff4d4f !important;
    }

    :deep(.a-tree2) {
        width: 400px;
        position: absolute;
        background: #fff;
        border: 1px solid #dcdfe6;
        z-index: 10;
        overflow: auto;
        height: 300px;
    }

    :deep(.tree_click_div) {
        display: flex;
        flex: 1;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 400px;
        cursor: pointer;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        font-size: inherit;
        padding-left: 15px;
        padding-right: 11px;
        height: 40px;
        line-height: 40px;
        color: #606266;

        i {
            color: #c0c4cc;
        }
    }

    :deep(span[contenteditable]:empty:before) {
        content: attr(placeholder);
        color: #cccccc;
    }

    :deep(span[contenteditable]:focus) {
        content: none;
    }
}
</style>
