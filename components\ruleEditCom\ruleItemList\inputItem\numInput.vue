<template>
  <div class="span-con-else" style="display: inline-flex">
    <!-- {{delElseFlag}}{{readonly}}{{delRead}}{{elseFlag}} -->
    <a-input-number
      v-model:value.lazy="innerValue"
      :disabled="disabled"
      :style="width"
      @blur="handleChange"
      @keydown="handleKeyDown"
      :title="titleStr"
      :data-val="innerValue"
      v-if="(delElseFlag && !readonly) || (delRead && !elseFlag) || (!elseFlag && !readonly)"
    />
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <TableConditionOperationBox
      class="table-opera-box"
      :index="index"
      :col="col"
      :rowData="rowData"
      :colData="colData"
      :record="record"
      :elseFlag="elseFlag"
      :delElseFlag="delElseFlag"
      :readonly="readonly"
      v-if="col && col[4] !== '2' && !readonly"
      @operaChange="operaChange"
    />
  </div>
</template>

<script setup>
import { calculateWidth } from "@/components/ruleEditCom/utils/inputItmeUtil";
import TableConditionOperationBox from "@/components/ruleEditCom/ruleItemList/operationMenu/tableConditionOperationBox";

// 定义组件的 props
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  value: {
    type: [String, Number],
    default: null,
  },
  titleStr: {
    type: String,
    default: "",
  },
  index: {
    type: Number,
    default: 0,
  },
  col: {
    type: String,
    default: "",
  },
  elseFlag: {
    type: Boolean,
    default: false,
  },
  delElseFlag: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  delRead: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Array,
    default: () => [],
  },
  colData: {
    type: Array,
    default: () => [],
  },
  record: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件的 emits
const emit = defineEmits(['onChange', 'operaChange']);

// 定义响应式数据
const innerValue = ref(props.value);

// 计算属性
const width = computed(() => ({
  width: calculateWidth(innerValue.value),
}));

// 方法
const operaChange = (action) => {
  if (props.elseFlag && action === "otherwise") {
    return;
  } else if (action === "otherwise") {
    emit("onChange", { value: "否则", elseFlag: true });
  } else {
    emit("onChange", '');
  }
  const dataIndex = props.rowData.findIndex((item) => item.key === props.record.key);
  emit("operaChange", {
    action,
    index: dataIndex,
    col: props.col,
  });
};

const handleChange = () => {
  emit("onChange", innerValue.value);
};

const handleKeyDown = (e) => {
  e && e.stopPropagation && e.stopPropagation();
};

// 监听 value 的变化
watch(
  () => props.value,
  (newVal) => {
    innerValue.value = newVal;
  },
  { deep: true, immediate: true }
);
</script>

<style scoped lang="scss">
.span-con-else {
  display: inline-flex;
}
</style>
