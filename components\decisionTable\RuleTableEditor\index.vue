<template>
  <div :class="['eRuleEditorContainer', 'oneDark_Light', isFullscreen ? 'fullscreen-mode' : '']">
    <div class="eRuleEditor">
      <ToolBarTable
        :locked="!locked"
        :viewStatus="viewStatus"
        :uuid="uuid"
        :showFullScreen="true"
        :isFullscreen="isFullscreen"
        @validate="toValidate"
        @toSave="toSave"
        @changeLock="changeLock"
        @changeViewStatus="changeViewStatus"
        @handleExport="handleExport"
        @handleImport="handleImport"
        @fullScreen="fullScreen"
        ref="refToolBar"
      />
      <div class="editorBody">
        <PredefineView
          id="predefineView"
          ref="refPredefineView"
          :style="viewStatus === 'predefineView' ? { display: 'block' } : { display: 'none' }"
          class="table-prede"
          :predefineList="predefineList"
          :initModelData="initModelData.initModelData"
          :locked="locked"
          :validateResult="predefineValidList"
          :uuid="uuid"
          @changePredefineList="changePredefineList"
        />
        <RuleTable
          :style="viewStatus === 'designView' ? { display: 'block' } : { display: 'none' }"
          class="table-design"
          :ruleContent="defaultRuleContent"
          :isEditing="!locked"
          :predefineDom="predefineDom"
          :viewStatus="viewStatus"
          @onChange="onTableChange"
          @getPredefine="getPredefine"
        />
        <!-- 规则文本视图区域 -->
        <TextView
                v-show="viewStatus === 'ruleTextView'"
                class="rule-text"
                :uuid="uuid"
                :dateNow="Date.now()"
                :viewStatus="viewStatus"
        />
        <ValidateView
          v-if="viewStatus === 'ValidateView' && Object.keys(toBeValida).length > 0"
          :validateResult="self_validateResult"
          :ruleContent="toBeValida"
          class="table-val"
        />
        <CodeView
          v-if="viewStatus === 'codeView'"
          :ruleDrl="ruleDrl"
          class="table-code"
          :ruleName="ruleName"
        />
      </div>
    </div>
    <!-- 全屏退出按钮 -->
    <a-tooltip placement="bottom" title="退出全屏" :mouseEnterDelay="0" :mouseLeaveDelay="0" trigger="hover" :open="tooltipExitVisible">
      <div v-if="isFullscreen" class="exit-fullscreen-btn" @click="fullScreen" 
        @mouseenter="tooltipExitVisible = true" 
        @mouseleave="tooltipExitVisible = false">
        <FullscreenExitOutlined />
      </div>
    </a-tooltip>
  </div>
</template>

<script setup>
import { cloneDeep } from "lodash";
import CodeView from "@/components/ruleEditCom/codeView/codeView.vue";
import ValidateView from "./layout/ValidateView.vue";
import PredefineView from "@/components/ruleEditCom/predefine/predefineView.vue";
import ToolBarTable from "./layout/toolBarTable.vue";
import RuleTable from "./ruleTable.vue";
import TextView from "@/components/ruleEditCom/textView/textView.vue";
import { FullscreenExitOutlined } from '@ant-design/icons-vue';
import { getRuleDrl } from "@/api/rule_editor";

// 定义props
const props = defineProps({
  predefineList: {
    type: Array,
    default: () => [],
  },
  p_locked: {
    type: Boolean,
    default: true,
  },
  ruleData: {
    type: Object,
    default: () => ({}),
  },
  validateResult: {
    type: Array,
    default: () => [],
  },
  uuid: String,
  initModelData: {
    type: Object,
    default: () => ({}),
  },
  ruleName: String,
  isFullscreenMode: {
    type: Boolean,
  default: false
  }
});

// 定义data
const viewStatus = ref("designView");
const locked = ref(props.p_locked);
const defaultRuleContent = ref(props.ruleData);
const predefineValidList = ref([]);
const self_validateResult = ref([]);
const self_predefineList = ref([]);
const toBeData = ref({});
const toBeValida = ref({});
const predefineDom = ref("");
const isFullscreen = ref(false);
const tooltipExitVisible = ref(false);
// 定义refs
const refPredefineView = ref(null);
const refToolBar = ref(null);
const ruleDrl = ref(null);

// 定义emit
const emit = defineEmits([
  "validate",
  "save",
  "changePredefineList",
  "handleExport",
  "handleImport",
  "setCascaderClose"
]);

// 定义watch
watch(
  () => props.validateResult,
  (newVal) => {
    if (newVal.length > 0) {
      self_validateResult.value = newVal;
      predefineValidList.value= newVal[0].predefineValidList;
    }
  },
  { deep: true }
);

watch(
  () => props.ruleData,
  (newVal) => {
    defaultRuleContent.value = newVal;
  },
  { deep: true }
);

watch(
  () => props.predefineList,
  (newVal) => {
    self_predefineList.value = newVal;
  },
  { deep: true }
);

// 定义computed
const self_valida = computed(() => props.validateResult);

// 定义methods
const getPredefine = () => {
  nextTick(() => {
    const objPV = refPredefineView.value;
    predefineDom.value = objPV.$el.innerHTML;
  });
};

const saveValiChange = () => {
  toBeValida.value = cloneDeep(toBeData.value);
  if (refToolBar.value) {
    refToolBar.value.changeView();
  } else {
    // 直接修改视图状态作为备选方案
    viewStatus.value = 'ValidateView';
  }
};

const toValidate = () => {
  emit("validate", toBeData.value);
  toBeValida.value = cloneDeep(toBeData.value);
};

const toSave = () => {
  emit("save", toBeData.value);
};

const changeLock = () => {
  locked.value = !locked.value;
};

const changeViewStatus = (newViewStatus = "designView") => {
  viewStatus.value = newViewStatus;
  if (newViewStatus === "codeView") {
    getRuleDrl(props.uuid).then((res) => {
      if (res && res.data) {
        ruleDrl.value = res.data.ruleDrl || null;
      }
    });
  }
  emit("setCascaderClose");
};

const changePredefineList = (val = [], del, change) => {
  emit("changePredefineList", val, false, del, change);
};

const onTableChange = (val) => {
  if (val && val.rows) {
    val.rows.map((item) => {
      if (item.cells) {
        item.cells.map((_item) => {
          if (_item.variables && _item.variables.length > 1) {
            _item.variables.map((__item) => {
              if (
                __item.variableType === "constant" &&
                (__item.valueType === "Double" ||
                  __item.valueType === "Integer" ||
                  __item.valueType === "Float" ||
                  __item.valueType === "Long" ||
                  __item.valueType === "Short" ||
                  __item.valueType === "Number" ||
                  __item.valueType === "BigDecimal")
              ) {
                __item.value === "" ? (__item.value = null) : __item.value;
              }
            });
          }
        });
      }
    });
  }
  toBeData.value = val;
};

const handleExport = () => {
  emit("handleExport", toBeData.value);
};

const handleImport = (val) => {
  emit("handleImport", val);
};

//打开全屏编辑
const fullScreen = () => {
  isFullscreen.value = !isFullscreen.value;
  tooltipExitVisible.value = false;
};

// 组件的生命周期钩子
onMounted(() => {
  self_predefineList.value = props.predefineList;
  defaultRuleContent.value = props.ruleData;
  self_validateResult.value = props.validateResult;
});

// 暴露方法给外部组件
defineExpose({
  saveValiChange
});
</script>

<style lang="scss">
@use "@/assets/css/ruleEditor.scss" as *;

.eRuleEditorContainer.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #fff;
  padding: 50px 10px 10px 10px;
}

.eRuleEditorContainer.fullscreen-mode .eRuleEditor {
  height: 100%;
}

.eRuleEditorContainer.fullscreen-mode .editorBody {
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
}

.eRuleEditorContainer.fullscreen-mode .table-prede, 
.eRuleEditorContainer.fullscreen-mode .table-design, 
.eRuleEditorContainer.fullscreen-mode .table-code, 
.eRuleEditorContainer.fullscreen-mode .table-val,
.eRuleEditorContainer.fullscreen-mode .rule-text {
  flex: 1;
  overflow: auto;
  max-height: calc(100vh - 140px);
}

.exit-fullscreen-btn {
  position: fixed;
  top: 10px;
  right: 20px;
  cursor: pointer;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  transform: scale(1);
}

.exit-fullscreen-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.85);
}
</style>
