<!--
/**
 * 批量导出配置对话框组件
 * 
 * 功能说明:
 * - 提供字段选择界面，让用户选择需要导出的列
 * - 支持系统字段和自定义字段导出
 * - 支持搜索过滤字段
 * - 提供全选/取消全选功能
 * - 支持恢复默认选项
 * - 支持选择导出格式（Excel/Html）
 * 
 * 使用方法:
 * <ExportConfigModal
 *   v-model:visible="exportModalVisible"
 *   :fields="exportFields"
 *   :export-type="exportType"
 *   :selected-ids="selectedIds"
 *   :total-count="pagination.total"
 *   :export-module="'ruleList'"
 *   :module-params="{ engUuid: ruleBaseId }"
 *   :search-params="searchForm"
 *   :show-pattern="false"
 *   @cancel="handleCancel"
 *   @export="handleExport"
 * />
 * 
 * 在父组件中的使用示例:
 * 
 * // 导出相关数据
 * const exportModalVisible = ref(false)
 * const exportFields = ref([])
 * const exportType = ref('selected')
 * const selectedIds = ref(['uuid1', 'uuid2'])
 * 
 * // 生成导出字段
 * const generateExportFields = () => {
 *   return columns.value.map(col => ({
 *     key: col.key || col.dataIndex,
 *     label: col.title,
 *     checked: true, // 默认选中
 *     type: 'system'
 *   })).filter(field => field.key !== 'action') // 过滤操作列
 * }
 * 
 * // 处理导出
 * const handleExport = async (exportParams) => {
 *   try {
 *     const res = await batchExport(exportParams)
 *     const success = handleExportDownload(res)
 *     if (success) {
 *       message.success('导出成功')
 *     } else {
 *       message.error('导出失败')
 *     }
 *   } catch (error) {
 *     console.error('导出失败:', error)
 *     message.error('导出失败')
 *   }
 * }
 * 
 * 字段数据格式:
 * [
 *   {
 *     key: 'fieldKey',       // 字段标识符
 *     label: '字段名称',      // 字段显示名称
 *     checked: true,         // 是否选中
 *     type: 'system'         // 类型：'system'系统字段或'custom'自定义字段
 *   },
 *   ...
 * ]
 * 
 * 导出参数格式（新规范）:
 * {
 *   exportModule: 'ruleList',
 *   exportType: 'selected',
 *   fields: [
 *     { fieldKey: 'ruleName', fieldLabel: '规则名称' },
 *     { fieldKey: 'status', fieldLabel: '状态' }
 *   ],
 *   pattern: 'Excel',
 *   selectedIds: ['uuid1', 'uuid2'],
 *   ...moduleParams,
 *   filters: {
 *     ...searchParams
 *   }
 * }
 */
-->
<template>
  <a-modal
    :visible="visible"
    title="选择导出属性"
    :maskClosable="false"
    @cancel="handleCancel"
    :width="600"
    :footer="null"
    :bodyStyle="{ paddingBottom: '8px', paddingTop: '8px' }"
  >
    <div class="export-config-container">
      <div class="search-wrapper">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索字段..."
          class="custom-search-input"
        />
      </div>
      
      <div class="system-fields">
        <div class="field-group-title">系统字段</div>
        <div class="field-group">
          <a-checkbox :indeterminate="systemIndeterminate" 
                      :checked="systemCheckAll" 
                      @change="onSystemCheckAllChange">全部</a-checkbox>
          
          <div class="fields-grid">
            <div v-for="field in filteredSystemFields" 
                 :key="field.key"
                 class="field-item">
              <a-checkbox 
                v-model:checked="field.checked">
                {{ field.label }}
              </a-checkbox>
            </div>
          </div>
        </div>
      </div>
      
      <div class="custom-fields" v-if="filteredCustomFields.length > 0">
        <div class="field-group-title">自定义属性</div>
        <div class="field-group">
          <a-checkbox :indeterminate="customIndeterminate" 
                      :checked="customCheckAll" 
                      @change="onCustomCheckAllChange">全部</a-checkbox>
          
          <div class="fields-grid">
            <div v-for="field in filteredCustomFields" 
                 :key="field.key"
                 class="field-item">
              <a-checkbox 
                v-model:checked="field.checked">
                {{ field.label }}
              </a-checkbox>
            </div>
          </div>
        </div>
      </div>
      
      <div class="export-format-section" v-if="props.showPattern">
        <div class="field-group-title">导出格式</div>
        <div class="field-group">
          <a-radio-group v-model:value="exportFormat">
            <a-radio value="Excel">Excel</a-radio>
            <a-radio value="Html">Html</a-radio>
          </a-radio-group>
        </div>
      </div>
      
      <div class="modal-footer">
        <div class="export-info">
          共<span class="export-count">{{ exportCount }}</span>条数据导出
        </div>
        <div class="footer-buttons">
          <a-button @click="handleCancel">取消</a-button>
          <a-button @click="handleResetDefault">恢复默认</a-button>
          <a-button type="primary" @click="handleExport" :loading="loading">
            导出
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject } from 'vue'

interface ExportField {
  key: string
  label: string
  checked: boolean
  type: 'system' | 'custom'
}

interface Props {
  visible: boolean
  fields: ExportField[]
  exportType: 'selected' | 'all'
  selectedIds: string[]
  totalCount: number
  exportModule: string
  moduleParams?: Record<string, any>
  searchParams?: Record<string, any>
  showPattern?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  fields: () => [],
  exportType: 'selected',
  selectedIds: () => [],
  totalCount: 0,
  exportModule: 'ruleList',
  moduleParams: () => ({}),
  searchParams: () => ({}),
  showPattern: false
})

const emit = defineEmits(['update:visible', 'cancel', 'export'])

const loading = ref(false)
const searchText = ref('')
const exportFormat = ref('Excel')

// 注入message
const message = inject('message') as any

// 复制props中的字段，以便修改checked状态
const localFields = ref<ExportField[]>([])
// 保存原始字段状态，用于"恢复默认"功能
const originalFields = ref<ExportField[]>([])

// 初始化本地字段
watch(() => props.fields, (newFields) => {
  // 深拷贝，避免修改原始数据
  const fieldsData = JSON.parse(JSON.stringify(newFields))
  // 保存原始状态
  originalFields.value = JSON.parse(JSON.stringify(fieldsData))
  // 设置本地字段
  localFields.value = fieldsData
}, { immediate: true })

// 系统和自定义字段分开处理
const systemFields = computed(() => 
  localFields.value.filter(field => field.type === 'system')
)

const customFields = computed(() => 
  localFields.value.filter(field => field.type === 'custom')
)

// 搜索过滤
const filteredSystemFields = computed(() => {
  // 先过滤掉空标签和操作列
  const validFields = systemFields.value.filter(field => 
    field.label && field.key !== 'action'
  )
  
  if (!searchText.value) return validFields
  return validFields.filter(field => 
    field.label.toLowerCase().includes(searchText.value.toLowerCase()) ||
    field.key.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

const filteredCustomFields = computed(() => {
  // 先过滤掉空标签和操作列
  const validFields = customFields.value.filter(field => 
    field.label && field.key !== 'action'
  )
  
  if (!searchText.value) return validFields
  return validFields.filter(field => 
    field.label.toLowerCase().includes(searchText.value.toLowerCase()) ||
    field.key.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 全选状态
const systemCheckAll = computed(() => {
  return systemFields.value.length > 0 && 
    systemFields.value.every(field => field.checked)
})

const systemIndeterminate = computed(() => {
  const checkedCount = systemFields.value.filter(field => field.checked).length
  return checkedCount > 0 && checkedCount < systemFields.value.length
})

const customCheckAll = computed(() => {
  return customFields.value.length > 0 && 
    customFields.value.every(field => field.checked)
})

const customIndeterminate = computed(() => {
  const checkedCount = customFields.value.filter(field => field.checked).length
  return checkedCount > 0 && checkedCount < customFields.value.length
})

// 全选/取消全选
const onSystemCheckAllChange = (e: any) => {
  const checked = e.target.checked
  systemFields.value.forEach(field => {
    field.checked = checked
  })
}

const onCustomCheckAllChange = (e: any) => {
  const checked = e.target.checked
  customFields.value.forEach(field => {
    field.checked = checked
  })
}

// 导出数量
const exportCount = computed(() => {
  return props.exportType === 'selected' 
    ? props.selectedIds.length 
    : props.totalCount
})

// 恢复默认值 - 将字段勾选状态恢复到初始状态
const handleResetDefault = () => {
  localFields.value = JSON.parse(JSON.stringify(originalFields.value))
  exportFormat.value = 'Excel'
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 导出
const handleExport = () => {
  // 验证是否选择了字段
  const checkedFields = localFields.value.filter(field => field.checked)
  if (checkedFields.length === 0) {
    message.warning('请至少选择一个导出字段')
    return
  }
  
  // 验证导出选中记录时是否有选中的记录
  if (props.exportType === 'selected' && props.selectedIds.length === 0) {
    message.warning('请先选择要导出的记录')
    return
  }
  
  loading.value = true
  console.log(props,'---props')
  // 按照新规范构建导出参数
  const exportParams: Record<string, any> = {
    exportModule: props.exportModule,
    exportType: props.exportType,
    fields: checkedFields.map(field => ({
      fieldKey: field.key,
      fieldLabel: field.label
    })),
    pattern: exportFormat.value,
    // 模块特定参数
    ...props.moduleParams,
    // 将所有筛选和查询参数放入filters对象
    filters: {
      ...props.searchParams,
      ...(props.moduleParams?.filters || {})  // 如果moduleParams中有filters，也合并进来
    }
  }
  
  // 添加选中记录ID（当导出类型为selected时）
  if (props.exportType === 'selected') {
    exportParams.selectedIds = props.selectedIds
  }
  
  // 调用导出
  emit('export', exportParams)
  
  // 关闭对话框
  setTimeout(() => {
    loading.value = false
    handleCancel()
  }, 500)
}
</script>

<style lang="scss" scoped>
.export-config-container {
  padding: 0 0 10px 0;
  
  .search-wrapper {
    margin-bottom: 16px;
  }
  
  .field-group-title {
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .field-group {
    margin-bottom: 16px;
    margin-left: 16px;
    
    .fields-grid {
      margin-top: 8px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 8px;
      max-height: 200px;
      overflow-y: auto;
      padding-right: 8px;
      
      .field-item {
        display: flex;
        align-items: center;
        min-height: 32px;
      }
    }
  }
  
  .export-format-section {
    margin-bottom: 16px;
    
    .field-group {
      margin-left: 16px;
    }
  }
  
  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    
    .export-info {
      .export-count {
        color: rgb(0, 106, 212);
        margin: 0 4px;
      }
    }
    
    .footer-buttons {
      button + button {
        margin-left: 8px;
      }
    }
  }
  
  :deep(.custom-search-input) {
    .ant-input-search-button {
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .ant-input {
      height: 32px;
    }
  }
}
</style> 