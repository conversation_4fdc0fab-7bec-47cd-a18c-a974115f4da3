<script setup lang="ts">
// 导入必要的API和库
// 引入WebSocket相关方法
import { createChatWebSocket, sendChatMessage } from '@/api/ai'
// markdown-it: 用于将Markdown文本渲染为HTML的库
import MarkdownIt from 'markdown-it'

// 初始化markdown-it解析器，设置配置项
const md = new MarkdownIt({
  html: true,  // 允许HTML标签，确保我们可以插入自定义HTML标签如<div class="gray-text">
  breaks: true, // 将换行符转换为<br>，使文本换行正确显示
  linkify: true // 自动识别URL并转换为链接
});

// 定义响应式状态变量
const messages = ref<{ role: string, content: string, complete?: boolean }[]>([]); // 存储对话历史记录，包含用户和AI的消息
const userInput = ref(''); // 存储用户当前输入内容
const loading = ref(false); // 控制加载状态，显示加载动画
const streamingContent = ref(''); // 临时存储流式响应的累积内容
const responseContent = ref(''); // 临时存储正式回复的累积内容
const lastUserMessage = ref(''); // 存储最后一次用户发送的消息，用于"重新生成"功能
const aiOutputComplete = ref(false); // 跟踪AI输出是否已完成
const lastSubmitTime = ref(0); // 上次提交时间戳
const submitCooldown = 2000; // 提交冷却时间(毫秒)
const ws = ref<WebSocket | null>(null); // WebSocket连接实例

// 定义组件对外暴露的事件
const emit = defineEmits(['showErrorMsg', 'close', 'loadingChange', 'applyResult']);

// 定义组件接收的属性
const props = defineProps({
  engUuid: String, // 引擎UUID，用于API调用
  ruleId: String, // 规则库ID
  loginId: String, // 登录ID
});

// 定义WebSocket响应类型
interface WebSocketResponse {
  type: 'connected' | 'thinking' | 'response' | 'error' | 'complete';
  content: string;
}

// 初始化WebSocket连接
function initWebSocket() {
  // 创建WebSocket连接
  ws.value = createChatWebSocket({
    onOpen: () => {
      console.log('AI对话WebSocket连接已建立');
    },
    onMessage: (response: WebSocketResponse) => {
      handleWebSocketMessage(response);
    },
    onClose: () => {
      console.log('AI对话WebSocket连接已关闭');
      // 5秒后尝试重新连接
      setTimeout(initWebSocket, 5000);
    },
    onError: (error: Event) => {
      console.error('AI对话WebSocket错误:', error);
      emit('showErrorMsg', '连接服务器失败，请稍后重试');
    }
  });
}

// 处理WebSocket消息
function handleWebSocketMessage(response: WebSocketResponse) {
  // 如果正在加载，关闭加载状态
  if (loading.value) loading.value = false;
  
  // 根据消息类型处理不同的响应
  switch (response.type) {
    case 'connected':
      // 连接成功消息，可以忽略或显示
      console.log(response.content);
      break;
      
    case 'thinking':
      // AI思考过程，添加为灰色文本
      handleThinkingMessage(response.content);
      break;
      
    case 'response':
      // AI正式回复，添加为普通文本
      handleResponseMessage(response.content);
      break;
      
    case 'error':
      // 错误消息
      handleErrorMessage(response.content);
      break;
      
    case 'complete':
      // 处理完成，重置状态
      markCurrentMessageComplete();
      break;
  }
}

// 处理思考过程消息
function handleThinkingMessage(content: string) {
  // 如果内容是分隔符标记，则忽略不累加 
  if (content.indexOf('==思考过程==') > -1) {
    return;
  }
  // 获取当前AI消息索引
  const currentMsgIndex = messages.value.length - 1;
  
  // 更新流式内容缓存
  streamingContent.value += content;
  
  // 将思考内容包装在灰色文本区域中
  const formattedContent = `<div class="gray-text">${md.render(streamingContent.value)}</div>`;
  
  // 更新消息内容
  messages.value[currentMsgIndex].content = formattedContent;
}

// 处理正式回复消息
function handleResponseMessage(content: string) {
  // 如果内容是分隔符标记，则忽略不累加
  if (content.indexOf('==完整回复==') > -1) {
    return;
  }
  
  // 累积响应内容
  responseContent.value += content;
  
  // 获取当前AI消息索引
  const currentMsgIndex = messages.value.length - 1;
  
  // 如果当前消息已经包含了思考过程(灰色部分)
  if (messages.value[currentMsgIndex].content.includes('<div class="gray-text">')) {
    // 获取当前消息内容
    const currentContent = messages.value[currentMsgIndex].content;
    // 提取灰色部分
    const grayPart = currentContent.substring(
      currentContent.indexOf('<div class="gray-text">'),
      currentContent.indexOf('</div>') + 6
    );
    
    // 添加格式化的响应内容
    const formattedResponse = postFormatContent(responseContent.value);
    
    // 组合最终内容：灰色思考过程 + 正式回复
    messages.value[currentMsgIndex].content = `${grayPart}${md.render(formattedResponse)}`;
  } else {
    // 如果还没有思考过程，直接更新为回复内容
    const formattedResponse = postFormatContent(responseContent.value);
    messages.value[currentMsgIndex].content = md.render(formattedResponse);
  }
}

// 处理错误消息
function handleErrorMessage(content: string) {
  // 获取当前AI消息索引
  const currentMsgIndex = messages.value.length - 1;
  
  // 显示错误消息
  messages.value[currentMsgIndex].content = `<div class="error-message">${content}</div>`;
  
  // 标记消息为完成状态
  messages.value[currentMsgIndex].complete = true;
  
  // 关闭加载状态
  loading.value = false;
  
  // 错误提示
  emit('showErrorMsg', content);
}

// 标记当前消息为完成状态
function markCurrentMessageComplete() {
  const currentMsgIndex = messages.value.length - 1;
  if (currentMsgIndex >= 0) {
    messages.value[currentMsgIndex].complete = true;
  }
  
  // 重置状态
  loading.value = false;
  aiOutputComplete.value = true;
}

// 监听loading状态变化，当状态改变时向父组件发送事件
watch(() => loading.value, (newValue) => {
  emit('loadingChange', newValue);
});

/**
 * 将Markdown文本转换为HTML
 * @param text 需要渲染的文本
 * @returns 渲染后的HTML
 */
function renderMarkdown(text: string) {
  // 检查文本是否已经包含灰色文本HTML标记
  if (text.includes('<div class="gray-text">')) {
    return text; // 如果已经处理过，直接返回，避免重复渲染
  }

  // 正常渲染markdown文本为HTML
  return md.render(text);
}

/**
 * 发送消息并处理AI响应
 * 实现WebSocket多轮对话
 * @param message 可选参数，如果提供则使用该消息，否则使用userInput
 */
async function sendMessage(message?: string) {
  // 使用提供的消息或用户输入
  const userMessage = message || userInput.value.trim();

  // 输入验证，确保用户输入不为空
  if (!userMessage) {
    emit('showErrorMsg', '请输入问题');
    return;
  }

  // 检查是否处于冷却时间内
  const now = Date.now();
  const timeSinceLastSubmit = now - lastSubmitTime.value;

  // 如果是相同消息且在冷却时间内，阻止提交
  if (!message && timeSinceLastSubmit < submitCooldown && userMessage === lastUserMessage.value) {
    emit('showErrorMsg', `请等待${Math.ceil((submitCooldown - timeSinceLastSubmit) / 1000)}秒后再试`);
    return;
  }

  // 更新最后提交时间
  lastSubmitTime.value = now;

  // 保存最后一次用户消息，用于重新生成功能
  lastUserMessage.value = userMessage;

  // 重置AI输出完成状态
  aiOutputComplete.value = false;

  // 如果没有提供消息参数（即非重新生成），则添加新的用户消息
  if (!message) {
    // 添加用户消息到消息列表
    messages.value.push({ role: 'user', content: userMessage });
    // 清空输入框，准备接收下一条消息
    userInput.value = '';
  }

  // 添加一个空的助手消息，用于填充响应
  messages.value.push({ role: 'assistant', content: '', complete: false });
  
  // 设置加载状态，显示加载动画
  loading.value = true;
  // 清空流式内容缓存
  streamingContent.value = '';
  // 清空响应内容缓存
  responseContent.value = '';
  
  // 检查WebSocket连接状态
  if (!ws.value || ws.value.readyState !== WebSocket.OPEN) {
    // 如果连接不存在或已关闭，尝试重新连接
    initWebSocket();
    
    // 等待连接建立
    setTimeout(() => {
      if (ws.value && ws.value.readyState === WebSocket.OPEN) {
        // 发送消息
        sendChatMessageViaWebSocket(userMessage);
      } else {
        emit('showErrorMsg', '连接服务器失败，请稍后重试');
        loading.value = false;
      }
    }, 1000);
  } else {
    // 如果连接已建立，直接发送消息
    sendChatMessageViaWebSocket(userMessage);
  }
}

/**
 * 通过WebSocket发送聊天消息
 * @param message 用户消息
 */
function sendChatMessageViaWebSocket(message: string) {
  if (!ws.value) return;
  
  // 检查必要参数是否存在
  if (!props.engUuid) {
    emit('showErrorMsg', '缺少规则库ID参数，无法发送消息');
    loading.value = false;
    return;
  }
  // 准备发送的数据
  const data = {
    message: message,
    engUuid: props.engUuid || '',
    ruleId: props.ruleId,
    loginId: props.loginId || ''
  };
  
  // 发送消息
  const success = sendChatMessage(ws.value, data);
  if (!success) {
    emit('showErrorMsg', '发送消息失败，请稍后重试');
    loading.value = false;
  }
}

/**
 * 重新生成回复
 * 使用对应的用户输入重新调用AI
 * @param index 需要重新生成的AI消息索引
 */
function regenerateResponse(index: number) {
  // 查找对应的用户消息（通常是AI消息的前一条）
  let userMsgIndex = -1;
  // 从当前AI消息向前找到最近的用户消息
  for (let i = index - 1; i >= 0; i--) {
    if (messages.value[i].role === 'user') {
      userMsgIndex = i;
      break;
    }
  }

  if (userMsgIndex === -1) {
    emit('showErrorMsg', '找不到相关的用户提问');
    return;
  }

  const userMessage = messages.value[userMsgIndex].content;

  // 先添加用户消息，模拟用户重新提问
  messages.value.push({ role: 'user', content: userMessage });

  // 再调用sendMessage发送请求
  sendMessage(userMessage);
}

/**
 * 应用指定AI生成的结果
 * @param index 需要应用的AI消息索引
 */
function applyResult(index: number) {
  // 确保是AI回复
  if (index < messages.value.length && messages.value[index].role === 'assistant') {
    // 获取指定AI回复的内容
    const aiContent = messages.value[index].content;

    // 从内容中提取非灰色部分（正式结果）
    let result = aiContent;

    // 如果内容包含灰色文本区域和正常区域，只提取正常区域
    if (aiContent.includes('<div class="gray-text">') && aiContent.includes('</div>')) {
      // 提取非灰色部分（去除HTML标签后的纯文本）
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = aiContent;

      // 移除灰色文本区域
      const grayTextDiv = tempDiv.querySelector('.gray-text');
      if (grayTextDiv) {
        grayTextDiv.remove();
      }

      // 获取剩余内容
      result = tempDiv.textContent || tempDiv.innerText || '';
    }
    // 发送结果给父组件
    emit('applyResult', result.trim());
  }
}

/**
 * 格式化累积的全部消息内容
 * 添加适当的换行和格式化，提高可读性
 * @param fullContent 需要格式化的文本内容
 * @returns 格式化后的文本
 */
function postFormatContent(fullContent: string) {
  // 清理可能存在的data:前缀
  let result = fullContent.replace(/data:/gm, '');

  // 处理标题格式：在Markdown标题前添加换行
  // 匹配##, ###, ####等标题格式
  result = result.replace(/(#{2,6}\s*[^#\n]+?[:：]?)/gm, '\n$1');

  // 合并多余的空行，保持最多两个连续换行
  result = result.replace(/\n{3,}/g, '\n\n');

  return result;
}

/**
 * 清空聊天记录
 * 重置消息列表和流式内容
 */
function clearChat() {
  messages.value = [];
  streamingContent.value = '';
  lastUserMessage.value = '';
  aiOutputComplete.value = false;
}

/**
 * 处理键盘输入事件
 * 实现shift+回车换行，普通回车发送消息
 * @param event 键盘事件对象
 */
function handleKeyDown(event: KeyboardEvent) {
  // 如果按下回车键
  if (event.key === 'Enter') {
    // 如果同时按下了Shift键，允许换行，不拦截事件
    if (event.shiftKey) {
      return;
    }

    // 普通回车键，拦截事件并发送消息
    event.preventDefault();
    sendMessage();
  }
}

// 组件挂载时初始化WebSocket连接
onMounted(() => {
  initWebSocket();
});

// 组件卸载时关闭WebSocket连接
onUnmounted(() => {
  if (ws.value) {
    ws.value.close();
    ws.value = null;
  }
});
</script>

<template>
  <!-- 主容器，使用a-spin组件实现加载中效果 -->
  <a-spin :spinning="loading">
    <div style="padding: 24px">
      <!-- 头部区域：包含标题和图标 -->
      <div class="Header-module_header_JGdBP">
        <!-- 左侧：标题和图标 -->
        <div class="Header-module_inline_p-JWf">
          <!-- AI图标 SVG -->
          <svg width="1em" height="1em" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"
            class="larkui-icon larkui-icon-icon-ai-drawing tablet icon-svg index-module_size_wVASz"
            data-name="IconAiDrawingTablet" style="width: 24px; min-width: 24px; height: 24px">
            <defs>
              <linearGradient x1="12.096%" y1="16.702%" x2="88.744%" y2="76.647%" id="2582478412a">
                <stop stop-color="var(--yq-yuque-green-500)" offset="0%"></stop>
                <stop stop-color="var(--yq-yuque-green-600)" offset="100%"></stop>
              </linearGradient>
              <linearGradient x1="61.343%" y1="48.99%" x2="93.484%" y2="68.428%" id="2582478412b">
                <stop stop-color="#138747" offset="0%"></stop>
                <stop stop-color="#2ABD6C" offset="100%"></stop>
              </linearGradient>
            </defs>
            <g fill="none" fill-rule="evenodd">
              <path
                d="M12 1.56c5.747 0 10.406 4.66 10.406 10.407S17.747 22.373 12 22.373H4.781a3.188 3.188 0 0 1-3.187-3.187v-7.22C1.594 6.22 6.253 1.562 12 1.562ZM9.357 7.028a1.125 1.125 0 0 0-1.392.772L5.661 15.84a.938.938 0 0 0 .643 1.16l.026.007a.938.938 0 0 0 1.134-.65l.325-1.136h2.516l.325 1.136a.937.937 0 1 0 1.802-.517L10.128 7.8a1.125 1.125 0 0 0-.771-.772Zm6.862-.029h-1.875a.937.937 0 1 0 0 1.875h.047v6.281h-.047a.937.937 0 1 0 0 1.875h1.875a.938.938 0 0 0 .045-1.874l.002-.048V8.872a.938.938 0 0 0-.047-1.874Zm-7.172 3.834.72 2.515h-1.44l.72-2.515Z"
                fill="url(#2582478412a)" transform="translate(4 4.033)"></path>
              <path
                d="M15.557 4.64c.925.12 1.73.51 2.413 1.167a4.426 4.426 0 0 1 1.301 2.402l2.593.442c-.698-1.788-1.38-2.95-2.048-3.489-.667-.537-2.086-.712-4.259-.523Z"
                fill="url(#2582478412b)" transform="translate(4 4.033)"></path>
              <path
                d="M23.35 4.647a.28.28 0 0 1 .19.188l.247.836a3.344 3.344 0 0 0 2.252 2.252l.836.248a.28.28 0 0 1 0 .536l-.836.248a3.344 3.344 0 0 0-2.252 2.252l-.248.835a.28.28 0 0 1-.535 0l-.249-.835a3.344 3.344 0 0 0-2.252-2.252l-.835-.248a.28.28 0 0 1 0-.536l.835-.248a3.344 3.344 0 0 0 2.252-2.252l.249-.836a.28.28 0 0 1 .347-.188ZM27.427 2.196a.135.135 0 0 1 .091.091l.12.405a1.62 1.62 0 0 0 1.092 1.091l.404.12a.135.135 0 0 1 0 .26l-.404.12a1.62 1.62 0 0 0-1.091 1.091l-.12.405a.135.135 0 0 1-.26 0l-.12-.405a1.62 1.62 0 0 0-1.091-1.09l-.405-.121a.135.135 0 0 1 0-.26l.405-.12a1.62 1.62 0 0 0 1.09-1.09l.12-.406a.135.135 0 0 1 .17-.09Z"
                fill="#F5DA80" fill-rule="nonzero"></path>
              <path d="M0 0h32v32H0z"></path>
            </g>
          </svg>
          <span class="Header-module_title_9Nbta">AI帮你写</span>
        </div>
      </div>

      <!-- 聊天主体容器 -->
      <div class="chat-container">
        <!-- 聊天消息列表区域 -->
        <div class="messages-container">
          <!-- 空状态提示：当没有消息时显示 -->
          <template v-if="messages.length === 0">
            <div class="empty-chat">
              <p>欢迎使用<img style="width: 25px; height: 25px;margin:0 2px;position: relative;top:-2px" src="/img/dp.jpeg" alt="AI" />帮你写规则</p>
            </div>
          </template>
          <!-- 消息列表：当有消息时显示 -->
          <template v-else>
            <!-- 遍历所有消息 -->
            <div v-for="(msg, index) in messages" :key="index" class="message-wrapper">
              <div :class="['message', msg.role === 'user' ? 'user-message' : 'ai-message']">
                <!-- 消息头像 -->
                <div class="message-avatar">
                  <span v-if="msg.role === 'user'">我</span>
                  <img v-else src="/img/dp.jpeg" alt="AI" />
                </div>
                <!-- 消息内容区域 -->
                <div class="message-content">
                  <!-- 用户消息使用普通文本显示 -->
                  <p v-if="msg.role === 'user'" style="white-space: pre-wrap;">{{ msg.content }}</p>
                  <!-- AI消息使用markdown渲染 -->
                  <div v-else class="markdown-body" v-html="renderMarkdown(msg.content)"></div>
                </div>
              </div>

              <!-- AI消息下方的操作按钮，只在消息完成后显示 -->
              <div v-if="msg.role === 'assistant' && msg.complete" class="message-actions">
                <div class="action-item" @click="regenerateResponse(index)">
                  <svg viewBox="0 0 24 24" width="12" height="12" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 2v6h6"></path>
                    <path d="M3 8C5.33333 5.33333 8 3.33333 12 3.33333C16.6667 3.33333 20 7.33333 20 12"></path>
                    <path d="M21 22v-6h-6"></path>
                    <path d="M21 16c-2.3333 2.6667-5 4.6667-9 4.6667C7.33333 20.6667 4 16.6667 4 12"></path>
                  </svg>
                  <span>重新生成</span>
                </div>
                <div class="action-item" @click="applyResult(index)">
                  <svg viewBox="0 0 24 24" width="12" height="12" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="9 11 12 14 22 4"></polyline>
                    <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                  </svg>
                  <span>应用结果</span>
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- 底部输入区域 -->
        <div class="input-container">
          <!-- 替换原有的输入框为文本域 -->
          <a-textarea
            v-model:value="userInput"
            placeholder="请输入需求描述；通过shift+回车换行"
            :disabled="loading"
            @keydown="handleKeyDown"
            class="message-input"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          />
          <!-- 操作按钮组 -->
          <div class="button-wrapper">
            <a-button @click="clearChat" :disabled="messages.length === 0">清屏</a-button>
            <a-button type="primary" @click="sendMessage()">发送</a-button>
          </div>
        </div>

        <!-- 页脚提示信息 -->
        <div class="footer-tip">
          <span>本内容由阿里百炼 DeepSeek R1 大模型生成，仅供参考。</span>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style lang="scss" scoped>
/* 头部样式 - 包含标题和图标 */
.Header-module_header_JGdBP {
  display: flex;
  align-items: center;
  /* 垂直居中对齐 */
  justify-content: space-between;
  /* 两端对齐 */
  height: 32px;
  line-height: 32px;
  margin-bottom: 16px;
  position: relative;
  padding-right: 40px;

  /* 标题区域样式 */
  .Header-module_inline_p-JWf {
    display: flex;
    align-items: center;
    gap: 8px;
    /* 元素间距 */

    .Header-module_title_9Nbta {
      font-size: 16px;
      font-weight: 500;
      /* 标题加粗 */
    }
  }
}

/* 聊天容器样式 - 整体布局 */
.chat-container {
  display: flex;
  flex-direction: column;
  /* 垂直排列 */
  height: 550px;
  /* 固定高度 */
}

/* 消息列表容器样式 */
.messages-container {
  flex: 1;
  /* 占满剩余空间 */
  overflow-y: auto;
  /* 内容溢出时显示滚动条 */
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #f9f9f9;
  margin-bottom: 16px;

  /* 空状态样式 - 无消息时显示 */
  .empty-chat {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #999;
  }

  /* 消息包装器 - 包含消息和操作按钮 */
  .message-wrapper {
    margin-bottom: 20px;
  }

  /* 消息气泡样式 */
  .message {
    display: flex;

    /* 头像样式 */
    .message-avatar {
      width: 32px;
      height: 32px;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      margin-right: 8px;
      flex-shrink: 0;
      /* 防止头像缩小 */
      font-weight: bold;

      /* 头像图片样式 */
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        /* 确保图片填满容器 */
        border-radius: 5px;
      }
    }

    /* 消息内容样式 */
    .message-content {
      background-color: #fff;
      padding: 10px 14px;
      border-radius: 8px;
      max-width: calc(100% - 50px);
      /* 限制最大宽度 */

      p {
        margin: 0;
      }

      /* Markdown渲染内容样式 */
      .markdown-body {
        line-height: 1.5;

        /* 标题样式 */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 12px;
          margin-bottom: 8px;
          font-weight: 600;
        }

        /* 段落样式 */
        p {
          margin-bottom: 8px;
        }

        /* 列表样式 */
        ul,
        ol {
          padding-left: 20px;
          margin-bottom: 8px;
        }

        /* 代码样式 */
        code {
          background-color: #f0f0f0;
          padding: 2px 4px;
          border-radius: 3px;
        }

        /* 代码块样式 */
        pre code {
          display: block;
          padding: 10px;
          overflow-x: auto;
          /* 允许水平滚动 */
        }
      }
    }
  }

  /* AI消息下方的操作按钮区域 */
  .message-actions {
    display: flex;
    gap: 12px;
    margin-left: 40px;
    /* 与消息内容对齐 */
    padding: 0 8px;

    .action-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      height: 24px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;

      svg {
        color: #999;
        transition: all 0.2s ease;
      }

      &:hover {
        color: #1890ff;

        svg {
          color: #1890ff;
        }
      }
    }
  }

  /* 用户消息特殊样式 - 显示在右侧 */
  .user-message {
    flex-direction: row-reverse;
    /* 反转排列顺序，头像在右 */

    .message-avatar {
      margin-right: 0;
      margin-left: 8px;
      background-color: var(--yq-yuque-green-600);
      color: white;
    }

    .message-content {
      background-color: #e6f7ff;
      /* 用户消息背景色 */
    }
  }
}

/* 输入区域样式 */
.input-area {
  margin-bottom: 8px;
}

/* 输入容器样式 */
.input-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;

  .message-input {
    border: none;
    outline: none;
    padding: 8px;

    /* 隐藏文本域右下角的调整大小控件 */
    :deep(.ant-input) {
      resize: none;
    }
  }

  .button-wrapper {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 8px;
  }
}

/* 页脚提示样式 */
.footer-tip {
  font-size: 12px;
  color: #999;
  text-align: center;
}
</style>

<style>
/* 全局样式，不使用scoped，确保可以应用到动态生成的内容 */

/* 灰色文本区域基本样式 */
.markdown-body {

  .gray-text {
    color: #888 !important;
    /* 灰色文字 */
    margin-bottom: 16px;
    padding: 8px;
    background-color: #f9f9f9;
    /* 浅灰色背景 */
    border-left: 3px solid #ddd;
    /* 左侧边框 */
    border-radius: 4px;
  }

  /* 确保灰色文本区域内的所有元素都使用灰色字体 */
  .gray-text h1,
  .gray-text h2,
  .gray-text h3,
  .gray-text h4,
  .gray-text h5,
  .gray-text h6,
  .gray-text p,
  .gray-text ul,
  .gray-text ol,
  .gray-text li,
  .gray-text strong,
  .gray-text em,
  .gray-text code,
  .gray-text pre {
    color: #888 !important;
    /* 使用!important确保样式优先级 */
  }

  /* 灰色区域内的链接样式 */
  .gray-text a {
    color: #888 !important;
    text-decoration: underline;
  }

  /* 美化灰色区域内的标题间距 */
  .gray-text h1,
  .gray-text h2,
  .gray-text h3,
  .gray-text h4,
  .gray-text h5,
  .gray-text h6 {
    margin-top: 8px;
    margin-bottom: 4px;
  }

  /* 灰色区域内段落间距 */
  .gray-text p {
    margin-bottom: 4px;
  }

  /* 灰色区域内代码块样式 */
  .gray-text pre code {
    background-color: #f0f0f0;
    border: 1px solid #eee;
  }
}
</style>
