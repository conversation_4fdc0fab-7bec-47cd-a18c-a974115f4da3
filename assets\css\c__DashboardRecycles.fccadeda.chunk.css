.index-module_repoSelector_2Z0BO .ant-select {
    width: 100%
}

.index-module_repoSelectorGroup_0chHn,.index-module_repoSelectorRepo_nq0Ar {
    width: 160px
}

.index-module_repoSelectorRepo_nq0Ar .ant-select-selection-item {
    height: 100%
}

.index-module_repoSelectorItem_-\+1uF {
    display: flex;
    align-items: center;
    height: 100%
}

.index-module_repoSelectorItem_-\+1uF .book-icon {
    margin: 0 8px 0 0;
    width: 20px;
    height: 20px;
    min-width: 20px
}

.index-module_repoSelectorItem_-\+1uF .book-name-text {
    max-width: 80%
}

.index-module_repoSelectorItem_-\+1uF .book-name .book-name-scope .icon-svg {
    display: block
}

.index-module_loading_6Xjs9 {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    text-align: center;
    font-size: 24px;
    color: var(--yq-icon-secondary);
    padding: 30vh 0 70vh 0;
    z-index: 999
}

.index-module_titleWrapper_NRfy4>div {
    height: 100%
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree .ant-tree-treenode,.index-module_wrapper_G8dbv.aside-catalog .ant-tree .ant-tree-treenode.catalog-treenode-level-1 {
    padding: 2px 0;
    margin: 0 0 0 16px
}

.index-module_wrapper_G8dbv.book-index-wrapper .ant-tree .ant-tree-treenode.catalog-treenode-level-1 {
    padding: 0
}

.index-module_wrapper_G8dbv .ant-tree-drop-indicator {
    bottom: -1px!important
}

.index-module_wrapper_G8dbv .ant-tree-checkbox {
    margin: 15px 4px
}

.index-module_wrapper_G8dbv .ant-tree-checkbox .ant-tree-checkbox-inner {
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree {
    background-color: transparent
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-treenode {
    padding: 0
}

.index-module_wrapper_G8dbv .ant-tree-list:hover .ant-tree-list-scrollbar {
    display: block!important
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-drop-indicator {
    background-color: var(--yq-function-info)
}

.index-module_wrapper_G8dbv .ant-tree .ant-tree-drop-indicator:after {
    border-color: var(--yq-function-info)
}

.index-module_wrapper_G8dbv .ant-tree-treenode.ant-tree-treenode-leaf-last.drag-over-gap-bottom .ant-tree-drop-indicator {
    margin-left: -4px
}

.index-module_wrapper_G8dbv .ant-tree-treenode.drop-container>[draggable] {
    box-shadow: none
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher {
    color: var(--yq-icon-secondary);
    text-align: right
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-switcher_close .ant-tree-switcher-icon {
    transform: rotate(270deg)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode .ant-tree-node-content-wrapper.ant-tree-node-selected {
    color: var(--yq-text-body)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:before {
    transition: none;
    background: transparent
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper {
    min-width: 0
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled .ant-tree-node-content-wrapper {
    cursor: not-allowed;
    color: var(--yq-text-caption)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled .ant-tree-switcher {
    color: var(--yq-text-caption)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled:before,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-disabled:hover:before {
    background-color: var(--yq-bg-secondary)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode.catalog-item-highlight:before {
    background: var(--yq-bg-secondary)
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before {
    background: var(--yq-bg-primary-hover);
    border-radius: 4px
}

.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover .name,.index-module_wrapper_G8dbv .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tail-info {
    background: transparent
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover {
    border-radius: 4px;
    background: var(--yq-bg-primary-hover)
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected .name,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected .tail-info,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover .name,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover .tail-info {
    background: var(--yq-bg-primary-hover)
}

.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode-selected:before,.index-module_wrapper_G8dbv.aside-catalog .ant-tree.ant-tree-directory .ant-tree-treenode:hover:before {
    background: var(--yq-bg-primary-hover);
    border-radius: 4px
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1 {
    position: relative
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:after,.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:before {
    content: "";
    pointer-events: none;
    position: absolute;
    z-index: 9;
    left: 0;
    right: 0;
    height: 60px;
    opacity: 0;
    transition: opacity .15s ease-in-out
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:before {
    top: 0;
    background: linear-gradient(180deg,var(--yq-bg-secondary),hsla(0,0%,98%,.5) 84%,hsla(0,0%,98%,.13))
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1:after {
    bottom: 0;
    background: linear-gradient(180deg,hsla(0,0%,98%,.13),hsla(0,0%,98%,.5) 16%,var(--yq-bg-secondary))
}

.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1.index-module_bottomScrolled_G\+APF:after,.index-module_wrapper_G8dbv.index-module_scrolled_ZXFD1.index-module_topScrolled_8VE-9:before {
    opacity: 1
}

.index-module_wrapper_G8dbv.index-module_dragging_fFNee .ant-tree .ant-tree-indent-unit {
    position: relative;
    height: 100%
}

.index-module_wrapper_G8dbv.index-module_dragging_fFNee .ant-tree .ant-tree-indent-unit:before {
    content: "";
    position: absolute;
    top: 0;
    right: 4px;
    bottom: -8px;
    border-right: 1px solid var(--yq-border-primary)
}

.index-module_normalWrapper_9CiWJ>div {
    padding-top: 8px;
    padding-bottom: 8px
}

.index-module_normalWrapper_9CiWJ .ant-tree .ant-tree-node-content-wrapper {
    min-height: 28px;
    line-height: 28px
}

.index-module_normalWrapper_9CiWJ .ant-tree-node-content-wrapper[draggable=true],.index-module_normalWrapper_9CiWJ .ant-tree-switcher {
    line-height: 28px
}

.index-module_normalWrapper_9CiWJ .ant-tree-title>.index-module_titleWrapper_NRfy4 {
    height: 28px
}

.index-module_largeWrapper_SUwil>div {
    padding-bottom: 16px
}

.index-module_largeWrapper_SUwil .ant-tree .ant-tree-node-content-wrapper {
    min-height: 46px;
    line-height: 46px
}

.index-module_largeWrapper_SUwil .ant-tree-node-content-wrapper[draggable=true],.index-module_largeWrapper_SUwil .ant-tree-switcher {
    line-height: 46px
}

.index-module_largeWrapper_SUwil .ant-tree-title>.index-module_titleWrapper_NRfy4 {
    height: 46px
}

.index-module_ListTocWrapper_4wy2S {
    padding-left: 24px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-treenode .ant-tree-drop-indicator {
    margin-left: -28px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-treenode.drag-over-gap-bottom .ant-tree-drop-indicator,.index-module_ListTocWrapper_4wy2S .ant-tree-treenode.drag-over-gap-top .ant-tree-drop-indicator {
    margin-left: -4px
}

.index-module_ListTocWrapper_4wy2S .ant-tree-switcher-noop {
    display: none
}

.SearchCatalogTrigger-module_actionItem_rk-NO {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    width: 20px;
    min-width: 20px;
    height: 20px;
    color: var(--yq-text-caption);
    cursor: pointer;
    transition: background .35s ease-in-out
}

.SearchCatalogTrigger-module_actionItem_rk-NO:hover {
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 2px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH {
    position: relative
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    padding-left: 27px;
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single .ant-select-selector .ant-select-selection-search {
    left: 27px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .ant-select-single .ant-select-selector .ant-select-selection-item .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: 30px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_searchInput_4Tgor {
    position: absolute;
    right: 0;
    top: -2px;
    width: 240px;
    height: 32px;
    font-size: 14px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_prefixIconWrapper_Ub-Vo {
    position: absolute;
    display: flex;
    top: -2px;
    right: 213px;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 32px
}

.SearchCatalogTrigger-module_searchContainer_Zv6BH .SearchCatalogTrigger-module_searchIcon_RK482 {
    color: var(--yq-text-body)
}

.CatalogSelector-module_card_EnAh7 {
    position: relative
}

.CatalogSelector-module_card_EnAh7 .ant-tree.ant-tree-directory .ant-tree-treenode span.ant-tree-node-content-wrapper.ant-tree-node-selected:before {
    background-color: var(--yq-bg-tertiary)!important
}

.CatalogSelector-module_card_EnAh7 .ant-card-body {
    padding-top: 8px;
    padding-bottom: 8px
}

.CatalogSelector-module_card_EnAh7.CatalogSelector-module_disabled_DdXXq:before {
    content: "";
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-bg-primary);
    opacity: .5
}

.CatalogSelector-module_titleWrapper_mcDOG {
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.CatalogSelector-module_titleWrapper_mcDOG .CatalogSelector-module_title_ixnVK {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: auto;
    min-width: 0
}

.CatalogSelector-module_docCatalogItem_yI0aV {
    padding: 0 8px;
    background-color: var(--yq-bg-tertiary);
    border-radius: 2px;
    height: 24px;
    line-height: 24px
}

.CatalogSelector-module_emptyViewInner_FIALf {
    padding: 16px 0;
    text-align: center;
    color: var(--yq-text-caption)
}

.CatalogSelector-module_placeholder_topjs {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: -2px;
    right: 0;
    height: 1px;
    background-color: var(--yq-blue-6)
}

.CatalogSelector-module_placeholder_topjs .CatalogSelector-module_caret_jxv72 {
    position: absolute;
    top: -3px;
    left: -16px;
    color: var(--yq-text-link)
}

.CatalogSelector-module_placeholder_topjs.CatalogSelector-module_placeholderSibling_-rdTP {
    left: 0;
    right: 0
}

.CatalogSelector-module_placeholder_topjs.CatalogSelector-module_placeholderChild_uP7D6 {
    left: 32px;
    right: 0
}

.CatalogSelector-module_actions_HKHke {
    margin-left: 12px;
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-caption);
    line-height: 22px;
    white-space: nowrap
}

.CatalogSelector-module_actions_HKHke .ant-radio-group {
    margin-left: 8px;
    display: flex;
    align-items: center
}

.CatalogSelector-module_actions_HKHke .ant-radio-wrapper {
    font-size: 12px
}

.CatalogSelector-module_actions_HKHke .ant-radio {
    position: relative;
    top: 3px;
    transform: scale(.75);
    margin: 1px
}

.CatalogSelector-module_actions_HKHke span.ant-radio+* {
    padding-left: 4px;
    padding-right: 4px
}

.CatalogSelector-module_bottomSimulator_HSsiS,.CatalogSelector-module_topSimulator_t0qhG {
    position: relative;
    width: 100%;
    height: 0;
    border-radius: 6px;
    cursor: pointer
}

.CatalogSelector-module_bottomSimulator_HSsiS .CatalogSelector-module_textTip_LK8n1,.CatalogSelector-module_topSimulator_t0qhG .CatalogSelector-module_textTip_LK8n1 {
    display: none;
    padding: 0 8px;
    position: absolute;
    bottom: 0;
    right: 0;
    line-height: 24px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.CatalogSelector-module_bottomSimulator_HSsiS.CatalogSelector-module_selected_JDZUv,.CatalogSelector-module_bottomSimulator_HSsiS:hover,.CatalogSelector-module_topSimulator_t0qhG.CatalogSelector-module_selected_JDZUv,.CatalogSelector-module_topSimulator_t0qhG:hover {
    background-color: var(--yq-bg-tertiary)
}

.CatalogSelector-module_bottomSimulator_HSsiS .CatalogSelector-module_placeholder_topjs,.CatalogSelector-module_topSimulator_t0qhG .CatalogSelector-module_placeholder_topjs {
    left: 18px;
    bottom: -2px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptions_Om8RW {
    display: flex;
    align-items: center
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptionItem_oqA8J {
    margin-right: 8px;
    height: 32px;
    line-height: 32px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_submitOptionItem_oqA8J .larkui-icon {
    position: relative;
    top: 1px;
    color: var(--yq-text-caption)
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutTip_BR\+Yo {
    font-size: 14px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 {
    display: flex
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 .CatalogSelector-module_shortcutAction_u\+U2K {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    margin-left: 8px
}

.CatalogSelector-module_wrapper_tsHiF .CatalogSelector-module_shortcutArea_AgvQv .CatalogSelector-module_shortcutActionCont_l0Ug2 .CatalogSelector-module_shortcutActionDisabled_\+nyVB {
    opacity: .3
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_repoSelector_xEuzZ {
    margin-bottom: 25px
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_selectBookTip_Ju7F8 {
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    padding: 16px;
    min-height: 356px;
    color: var(--yq-text-caption);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    flex-direction: column
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_selectBookTip_Ju7F8 .CatalogTransfer-module_emptyIcon_tkaXF {
    width: 200px;
    margin-bottom: 24px
}

.CatalogTransfer-module_wrapper_PCdb1 .CatalogTransfer-module_loading_MvRPo {
    width: 100%;
    display: flex;
    align-items: center
}

.CatalogTransfer-module_footer_XgWWv {
    margin-top: 24px
}

.CatalogTransfer-module_footer_XgWWv,.CatalogTransfer-module_footer_XgWWv .CatalogTransfer-module_actions_3DruK {
    display: flex;
    justify-content: flex-end;
    align-items: center
}

.CatalogTransfer-module_footer_XgWWv .CatalogTransfer-module_actions_3DruK .ant-btn {
    margin-left: 8px
}

.CatalogTransfer-module_selectorOptions_evk3C {
    position: absolute;
    bottom: 24px
}

.RecycleTable-module_mytable_xMxm8 {
    margin-bottom: 48px
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head {
    padding: 0 16px
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.RecycleTable-module_mytable_xMxm8 .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.RecycleTable-module_mytable_xMxm8 .ant-card-body {
    position: relative;
    padding: 0
}

.RecycleTable-module_mytable_xMxm8 .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.RecycleTable-module_mytable_xMxm8 .ant-table a {
    color: var(--yq-text-body)
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead>tr:first-child>th:first-child,.RecycleTable-module_mytable_xMxm8 .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.RecycleTable-module_mytable_xMxm8 .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.RecycleTable-module_mytable_xMxm8 .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_activeRow_H7G34 {
    background: var(--yq-yuque-green-1)
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ a,.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .book-icon,.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .doc-icon,.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .RecycleTable-module_titleIcon_9v8ip {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .doc-link {
    display: block
}

.RecycleTable-module_mytable_xMxm8 .ant-table .RecycleTable-module_columnsTitle_Y4\+GQ .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsTitleLabel_JrJrK {
    margin-left: 12px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsBelong_GXF2u {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsDesc_n4l1d {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsDesc_n4l1d.RecycleTable-module_mini_NPF41 {
    max-width: 200px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsTime_DOsCI {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsAction_Am8SF {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsAction_Am8SF.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsAction_Am8SF .ant-btn {
    border-radius: 4px
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_emptyView_8z8dd {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_emptyView_8z8dd .RecycleTable-module_emptyViewImg_5m9m- {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_emptyView_8z8dd a {
    color: var(--yq-text-link)
}

.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_emptyView_8z8dd a:hover {
    color: var(--yq-ant-link-hover-color)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head {
    padding: 0 16px
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head .ant-card-head-title,.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card-body {
    position: relative;
    padding: 0
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table a {
    color: var(--yq-text-caption)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-thead>tr:first-child>th:first-child,.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-footer {
    padding: 0;
    border-top: 0
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.RecycleTable-module_noPaddingTable_Y9K8- .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_activeRow_H7G34 {
    background: var(--yq-yuque-green-1)
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ.RecycleTable-module_mini_NPF41 {
    max-width: 300px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ a,.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ .book-icon,.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ .doc-icon,.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ .RecycleTable-module_titleIcon_9v8ip {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitle_Y4\+GQ .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTitleLabel_JrJrK {
    margin-left: 12px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsBelong_GXF2u {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsDesc_n4l1d {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsDesc_n4l1d.RecycleTable-module_mini_NPF41 {
    max-width: 200px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsTime_DOsCI {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsAction_Am8SF {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsAction_Am8SF a {
    color: var(--yq-text-link);
    font-size: 14px
}

.RecycleTable-module_noPaddingTable_Y9K8- .RecycleTable-module_columnsAction_Am8SF .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsDesc_n4l1d,.RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsTime_DOsCI {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .RecycleTable-module_mytable_xMxm8 .RecycleTable-module_columnsBelong_GXF2u {
        display:none
    }
}

.RecycleTable-module_mytable_xMxm8 .ant-card {
    border: none
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody>tr.ant-table-row-selected>td,.RecycleTable-module_mytable_xMxm8 td.ant-table-column-sort {
    background: none
}

.RecycleTable-module_mytable_xMxm8 .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.RecycleTable-module_mytable_xMxm8 .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.RecycleTable-module_mytable_xMxm8 .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .RecycleTable-module_mytable_xMxm8 .ant-card-extra .ant-btn,.RecycleTable-module_mytable_xMxm8 .ant-card-head-title {
        display:none
    }

    .RecycleTable-module_mytable_xMxm8 .ant-card-extra {
        float: none;
        flex: auto
    }

    .RecycleTable-module_mytable_xMxm8 .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.RecycleTable-module_wrapper_rUU9g {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .RecycleTable-module_wrapper_rUU9g {
        padding:0
    }
}

.RecycleTable-module_mytable_xMxm8 {
    margin: 0
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody tr td:not(:first-child) {
    padding-left: 16px!important
}

.RecycleTable-module_mytable_xMxm8 .ant-table-tbody>tr>td:first-child,.RecycleTable-module_mytable_xMxm8 .ant-table-thead>tr>.ant-table-cell .ant-table-column-sorters:first-child {
    padding-left: 0
}

.RecycleTable-module_columnsTime_DOsCI {
    padding-left: 16px!important
}

.RecycleTable-module_normal_N-qK2 {
    font-size: 14px
}

.RecycleTable-module_title_l4qVj {
    display: flex
}

.RecycleTable-module_recover_pf420 {
    font-size: 14px;
    margin-right: 16px
}

.RecycleTable-module_name_Id8QH {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-primary);
    display: inline-block
}

.RecycleTable-module_title_l4qVj .RecycleTable-module_templateTag_ygwhg .ant-tag {
    margin: 0 0 0 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.RecycleTable-module_modal_9auoV .RecycleTable-module_modalTitle_l3p4k {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500
}

.RecycleTable-module_modal_9auoV .RecycleTable-module_modalDescription_PGgjT {
    margin-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: var(--yq-text-body)
}

.RecycleTable-module_emptyView_8z8dd .RecycleTable-module_emptyViewImg_5m9m- {
    background-image: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*wPMVTriBo40AAAAAAAAAAAAAARQnAQ)
}

.RecycleTable-module_popConfirm_wDQS0 .RecycleTable-module_confirmContainer_t6h\+P .RecycleTable-module_confirmText_c6RM8 {
    font-weight: 700;
    color: var(--yq-text-primary)
}

.RecycleTable-module_popConfirm_wDQS0 .RecycleTable-module_confirmContainer_t6h\+P .RecycleTable-module_confirmDesc_TyN0f {
    color: var(--yq-text-caption)
}

.RecycleTable-module_popConfirm_wDQS0 [class~=ant-popover-message-title] {
    max-width: -webkit-max-content;
    max-width: -moz-max-content;
    max-width: max-content
}

.Recycles-module_mytable_1fj45 {
    margin-bottom: 48px
}

.Recycles-module_mytable_1fj45 .ant-card-head {
    padding: 0 16px
}

.Recycles-module_mytable_1fj45 .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Recycles-module_mytable_1fj45 .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.Recycles-module_mytable_1fj45 .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Recycles-module_mytable_1fj45 .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Recycles-module_mytable_1fj45 .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Recycles-module_mytable_1fj45 .ant-card-body {
    position: relative;
    padding: 0
}

.Recycles-module_mytable_1fj45 .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.Recycles-module_mytable_1fj45 .ant-table a {
    color: var(--yq-text-body)
}

.Recycles-module_mytable_1fj45 .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Recycles-module_mytable_1fj45 .ant-table-thead>tr:first-child>th:first-child,.Recycles-module_mytable_1fj45 .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Recycles-module_mytable_1fj45 .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Recycles-module_mytable_1fj45 .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.Recycles-module_mytable_1fj45 .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.Recycles-module_mytable_1fj45 .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.Recycles-module_mytable_1fj45 .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.Recycles-module_mytable_1fj45 .Recycles-module_activeRow_LcMBn {
    background: var(--yq-yuque-green-1)
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 a,.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .book-icon,.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .doc-icon,.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .Recycles-module_titleIcon_hnu5O {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .doc-link {
    display: block
}

.Recycles-module_mytable_1fj45 .ant-table .Recycles-module_columnsTitle_d3sD2 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsTitleLabel_yloxt {
    margin-left: 12px
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsBelong_pjW\+k {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsDesc_y9Ipk {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsDesc_y9Ipk.Recycles-module_mini_lU\+ma {
    max-width: 200px
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsTime_aJwPC {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsAction_o-65f {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsAction_o-65f.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Recycles-module_mytable_1fj45 .Recycles-module_columnsAction_o-65f .ant-btn {
    border-radius: 4px
}

.Recycles-module_mytable_1fj45 .Recycles-module_emptyView_C6q4o {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Recycles-module_mytable_1fj45 .Recycles-module_emptyView_C6q4o .Recycles-module_emptyViewImg_oECYq {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.Recycles-module_mytable_1fj45 .Recycles-module_emptyView_C6q4o a {
    color: var(--yq-text-link)
}

.Recycles-module_mytable_1fj45 .Recycles-module_emptyView_C6q4o a:hover {
    color: var(--yq-ant-link-hover-color)
}

.dashboard-dropdown-menu {
    max-height: 500px;
    overflow: auto
}

.dashboard-dropdown-menu .ant-dropdown-menu-item {
    padding: 0 20px;
    line-height: 40px;
    min-width: 120px
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a {
    padding: 0;
    margin: 0
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a:hover {
    color: var(--yq-text-primary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected {
    background: var(--yq-bg-tertiary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected a {
    background: transparent;
    color: var(--yq-text-body)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active {
    background: var(--yq-yuque-green-1)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active a {
    background: transparent;
    color: var(--yq-text-body)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head {
    padding: 0 16px
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head .ant-card-head-title,.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card-body {
    position: relative;
    padding: 0
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table a {
    color: var(--yq-text-caption)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-thead>tr:first-child>th:first-child,.Recycles-module_noPaddingTable_JC9Z3 .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-footer {
    padding: 0;
    border-top: 0
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.Recycles-module_noPaddingTable_JC9Z3 .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_activeRow_LcMBn {
    background: var(--yq-yuque-green-1)
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2.Recycles-module_mini_lU\+ma {
    max-width: 300px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 a,.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 .book-icon,.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 .doc-icon,.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 .Recycles-module_titleIcon_hnu5O {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitle_d3sD2 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTitleLabel_yloxt {
    margin-left: 12px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsBelong_pjW\+k {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsDesc_y9Ipk {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsDesc_y9Ipk.Recycles-module_mini_lU\+ma {
    max-width: 200px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsTime_aJwPC {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsAction_o-65f {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsAction_o-65f a {
    color: var(--yq-text-link);
    font-size: 14px
}

.Recycles-module_noPaddingTable_JC9Z3 .Recycles-module_columnsAction_o-65f .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .Recycles-module_mytable_1fj45 .Recycles-module_columnsDesc_y9Ipk,.Recycles-module_mytable_1fj45 .Recycles-module_columnsTime_aJwPC {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .Recycles-module_mytable_1fj45 .Recycles-module_columnsBelong_pjW\+k {
        display:none
    }
}

.Recycles-module_mytable_1fj45 .ant-card {
    border: none
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.Recycles-module_mytable_1fj45 .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.Recycles-module_mytable_1fj45 .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.Recycles-module_mytable_1fj45 .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.Recycles-module_mytable_1fj45 .ant-table-tbody>tr.ant-table-row-selected>td,.Recycles-module_mytable_1fj45 td.ant-table-column-sort {
    background: none
}

.Recycles-module_mytable_1fj45 .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.Recycles-module_mytable_1fj45 .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.Recycles-module_mytable_1fj45 .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.Recycles-module_mytable_1fj45 .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .Recycles-module_mytable_1fj45 .ant-card-extra .ant-btn,.Recycles-module_mytable_1fj45 .ant-card-head-title {
        display:none
    }

    .Recycles-module_mytable_1fj45 .ant-card-extra {
        float: none;
        flex: auto
    }

    .Recycles-module_mytable_1fj45 .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.Recycles-module_wrapper_jPSDM {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .Recycles-module_wrapper_jPSDM {
        padding:0
    }
}

.Recycles-module_h5Wrapper_VJGhT .ant-card-head {
    display: none
}

.Recycles-module_desktopWrapper_XKIY8 {
    padding-top: 28px
}

.index-module_more_sDgGm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--yq-black);
    border-radius: 8px
}

.ant-popover-open.index-module_more_sDgGm,.index-module_more_sDgGm:hover {
    border-radius: 8px
}

.toggle-publicity {
    color: var(--yq-text-link)!important;
    margin-left: 12px
}

.index-module_label_qcR\+D {
    border-radius: 2px;
    background-color: var(--yq-bg-tertiary);
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption);
    text-align: center
}

.DocTask-module_docTask_NywmO {
    padding: 30px 0 20px
}

.DocTask-module_docTask_NywmO .larkui-icon-check-outlined-circle {
    color: var(--yq-theme);
    font-size: 22px
}

.DocTask-module_docTask_NywmO .larkui-icon-close-circle,.DocTask-module_docTask_NywmO .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error);
    font-size: 20px
}

.DocTask-module_docTask_NywmO .action,.DocTask-module_docTask_NywmO .error,.DocTask-module_docTask_NywmO .icon,.DocTask-module_docTask_NywmO .tip {
    line-height: 27px;
    text-align: center;
    color: var(--yq-text-primary)
}

.DocTask-module_docTask_NywmO .error {
    color: var(--yq-function-error)
}

.DocTask-module_settingFile_bFy51 {
    margin-top: 16px;
    margin-bottom: 16px
}

.DocTask-module_hr_y8Wz9 {
    background-color: var(--yq-yuque-grey-5);
    height: 1px;
    margin: 15px 0 5px
}

.DocTask-module_settingFileIcon_\+\+xRm {
    width: 64px;
    height: 64px;
    margin-right: 5px
}

.DocTask-module_settingFileIcon_\+\+xRm img {
    width: 100%
}

.DocTask-module_settingContent_leSlb {
    min-height: 140px
}

.DocTask-module_settingContent_leSlb .ant-checkbox-wrapper,.DocTask-module_settingContent_leSlb .ant-radio-wrapper {
    margin-top: 8px;
    margin-left: 0;
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form .ant-legacy-form-item {
    margin-bottom: 0;
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-control {
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-label>label {
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-radio-group {
    border-top: 1px solid var(--yq-yuque-grey-5);
    margin-top: 10px
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label {
    display: block
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label>label.ant-legacy-form-item-no-colon:after {
    display: none
}

.DocTask-module_settingContent_leSlb .DocTask-module_isFirst_RPJNh .ant-radio-group {
    border-top: 0 none;
    margin-top: 0;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    padding-bottom: 10px
}

.DocTask-module_settingTitlt2_OZfjd {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 400;
    margin: 8px 0
}

.DocTask-module_settingFileTypeName_S4Bv1 {
    color: var(--yq-yuque-grey-9)
}

.DocTask-module_settingFileTypeExt_GCoBd {
    color: var(--yq-text-caption);
    font-size: 12px
}

.index-module_fileTypeSelector_73BHW {
    margin: 16px 0
}

.index-module_fileTypeSelector_73BHW .index-module_item_H3xgY {
    height: 152px
}

.index-module_fileTypeSelector_73BHW .index-module_fileType_l65Jm {
    width: 142px;
    text-align: center;
    padding: 16px 0 20px;
    cursor: pointer;
    transition: all .3s linear
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM {
    width: 70px;
    height: 70px;
    margin: 0 auto
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM img {
    width: 100%
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeMeta_4eYr8 {
    text-align: center;
    line-height: 24px;
    margin-top: 4px
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeName_9B0Rn {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeExt_wTTFB {
    font-size: 12px;
    line-height: 1;
    color: var(--yq-text-caption)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeReady_TAUvL {
    filter: grayscale(1);
    cursor: default;
    opacity: .45
}

.EvernoteDirUpload-module_modal_AERn- {
    padding: 16px
}

.EvernoteDirUpload-module_modal_AERn- .ant-upload {
    width: 100%
}

.EvernoteDirUpload-module_modal_AERn- .ant-modal-close-x {
    width: 24px;
    height: 24px;
    line-height: 32px
}

.EvernoteDirUpload-module_dragZone_qrdZx {
    border: dashed 1px var(--yq-border-primary);
    border-radius: 8px;
    width: 100%;
    height: 180px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_tip_FtuKl {
    padding-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption)
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_icon_ZYE-k {
    color: var(--yq-text-caption);
    margin-bottom: 12px
}

.EvernoteDirUpload-module_dragZone_qrdZx.EvernoteDirUpload-module_dragOver_0DaAV {
    border-color: var(--yq-blue-5)
}

.EvernoteDirUpload-module_dragZone_qrdZx:hover {
    border-color: var(--yq-border-primary-active)
}

.BookExport-module_docExport_44PUl .BookExport-module_title_Hezdm {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.BookExport-module_docExport_44PUl .BookExport-module_tips_VJJKo {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.BookExport-module_setting_PXAtw .BookExport-module_settingTitle_pEiZb {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookExport-module_setting_PXAtw .ant-upload-select,.BookExport-module_setting_PXAtw .BookExport-module_settingUpload_sNiNV {
    width: 100%
}

.index-module_content_v7lvp,.index-module_form_5k71F .larkui-form-item {
    margin-bottom: 16px
}

.index-module_form_5k71F .larkui-form-item:last-child {
    margin-bottom: 0
}

.index-module_tip_yNrai {
    line-height: 40px;
    color: var(--yq-text-primary)
}

.BookAction-module_wrap_AiMmc .ant-menu {
    min-width: 89px;
    padding: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.BookAction-module_wrap_AiMmc .ant-menu-item-divider {
    margin: 4px 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item {
    color: var(--yq-text-body);
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:not(:last-child) {
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_bookTitle_lUUrB {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB {
    padding-left: 12px;
    flex: 1;
    overflow: hidden
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_group_xaGsx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption);
    font-size: 13px;
    line-height: 18px
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_text_UrMgl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 21px;
    padding-bottom: 4px
}

.index-module_bookTitle_lUUrB .index-module_checked_3fCif {
    padding: 0 4px;
    color: var(--yq-text-link);
    font-size: 20px;
    width: 28px
}

.index-module_moreActions_YYACs {
    margin-left: 8px
}

.book-link,.book-name,.lark-book-title {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-book-title .book-icon {
    margin-right: 8px
}

.book-name {
    display: flex;
    align-items: center
}

.book-name .book-name-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.book-name .book-name-scope {
    position: relative
}

.book-name .book-name-scope a {
    pointer-events: none
}

.book-name .book-name-scope .icon-svg {
    margin-left: 5px
}

.book-name .book-name-orglabel {
    margin-left: 12px
}

.book-name .icon-svg {
    display: block
}

.book-name-split {
    margin: 0 4px
}

.permission__public__tip {
    margin-top: 12px;
    line-height: 22px
}

.permission__public__tip .highlight {
    color: var(--yq-function-error)
}

.belong-icon {
    font-size: 12px;
    margin-left: 4px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.index-module_groupName_DppYx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: var(--yq-text-body)
}

.index-module_groupName_DppYx:hover {
    color: var(--yq-text-primary)
}

.index-module_groupName_DppYx>.index-module_groupNameText_XYhrt {
    margin-right: 4px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_groupName_DppYx>.icon-svg {
    color: var(--yq-text-caption)
}

.index-module_groupName_DppYx .index-module_groupNameScope_qP0XF {
    position: relative;
    top: 3px;
    color: var(--yq-text-caption)
}

.index-module_card_MJe8k {
    padding-top: 8px;
    padding-bottom: 8px;
    max-width: 290px;
    min-width: 240px
}

.index-module_cardBody_C-l0H {
    display: flex
}

.index-module_cardAvatar_S1GOD {
    margin-right: 8px
}

.index-module_cardInfo_1BnUz {
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz,.index-module_cardInfo_1BnUz>h6 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_cardInfo_1BnUz>h6 {
    margin-top: 2px;
    font-size: 16px
}

.index-module_cardInfo_1BnUz>h6>a {
    color: var(--yq-text-primary)
}

.index-module_cardInfo_1BnUz>h6>a:hover {
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz>p {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption)
}

.index-module_cardFooter_iT7Xq {
    margin-top: 16px;
    border-top: 1px solid var(--yq-border-primary);
    padding-top: 12px;
    display: flex;
    justify-content: flex-end
}

.index-module_cardFooter_iT7Xq>a {
    color: var(--yq-text-body)
}

.index-module_cardFooter_iT7Xq>a:hover {
    color: var(--yq-text-caption)
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_contentArea_0xSfa {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_switchTitle_qW8aM {
    color: var(--yq-text-primary)
}

.index-module_switchButton_xHUSN .ant-switch-small {
    min-width: 32px;
    height: 18px;
    line-height: 18px
}

.index-module_switchButton_xHUSN .ant-switch-small.ant-switch-checked .ant-switch-handle {
    left: calc(100% - 16px)
}

.index-module_switchButton_xHUSN .ant-switch-small .ant-switch-handle {
    width: 14px;
    height: 14px
}

.index-module_tip_eCtT0 {
    background: var(--yq-yuque-grey-1);
    border-radius: 8px;
    padding: 8px;
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    margin-top: -16px;
    margin-bottom: 24px
}

/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:02.731Z
 */
.cropper-container {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.cropper-container img {
    backface-visibility: hidden;
    display: block;
    height: 100%;
    image-orientation: 0deg;
    max-height: none!important;
    max-width: none!important;
    min-height: 0!important;
    min-width: 0!important;
    width: 100%
}

.cropper-canvas,.cropper-crop-box,.cropper-drag-box,.cropper-modal,.cropper-wrap-box {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.cropper-canvas,.cropper-wrap-box {
    overflow: hidden
}

.cropper-drag-box {
    background-color: #fff;
    opacity: 0
}

.cropper-modal {
    background-color: #000;
    opacity: .5
}

.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid #39f;
    outline-color: rgba(51,153,255,.75);
    overflow: hidden;
    width: 100%
}

.cropper-dashed {
    border: 0 dashed #eee;
    display: block;
    opacity: .5;
    position: absolute
}

.cropper-dashed.dashed-h {
    border-bottom-width: 1px;
    border-top-width: 1px;
    height: 33.33333%;
    left: 0;
    top: 33.33333%;
    width: 100%
}

.cropper-dashed.dashed-v {
    border-left-width: 1px;
    border-right-width: 1px;
    height: 100%;
    left: 33.33333%;
    top: 0;
    width: 33.33333%
}

.cropper-center {
    display: block;
    height: 0;
    left: 50%;
    opacity: .75;
    position: absolute;
    top: 50%;
    width: 0
}

.cropper-center:after,.cropper-center:before {
    background-color: #eee;
    content: " ";
    display: block;
    position: absolute
}

.cropper-center:before {
    height: 1px;
    left: -3px;
    top: 0;
    width: 7px
}

.cropper-center:after {
    height: 7px;
    left: 0;
    top: -3px;
    width: 1px
}

.cropper-face,.cropper-line,.cropper-point {
    display: block;
    height: 100%;
    opacity: .1;
    position: absolute;
    width: 100%
}

.cropper-face {
    background-color: #fff;
    left: 0;
    top: 0
}

.cropper-line {
    background-color: #39f
}

.cropper-line.line-e {
    cursor: ew-resize;
    right: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-n {
    cursor: ns-resize;
    height: 5px;
    left: 0;
    top: -3px
}

.cropper-line.line-w {
    cursor: ew-resize;
    left: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-s {
    bottom: -3px;
    cursor: ns-resize;
    height: 5px;
    left: 0
}

.cropper-point {
    background-color: #39f;
    height: 5px;
    opacity: .75;
    width: 5px
}

.cropper-point.point-e {
    cursor: ew-resize;
    margin-top: -3px;
    right: -3px;
    top: 50%
}

.cropper-point.point-n {
    cursor: ns-resize;
    left: 50%;
    margin-left: -3px;
    top: -3px
}

.cropper-point.point-w {
    cursor: ew-resize;
    left: -3px;
    margin-top: -3px;
    top: 50%
}

.cropper-point.point-s {
    bottom: -3px;
    cursor: s-resize;
    left: 50%;
    margin-left: -3px
}

.cropper-point.point-ne {
    cursor: nesw-resize;
    right: -3px;
    top: -3px
}

.cropper-point.point-nw {
    cursor: nwse-resize;
    left: -3px;
    top: -3px
}

.cropper-point.point-sw {
    bottom: -3px;
    cursor: nesw-resize;
    left: -3px
}

.cropper-point.point-se {
    bottom: -3px;
    cursor: nwse-resize;
    height: 20px;
    opacity: 1;
    right: -3px;
    width: 20px
}

@media (min-width: 768px) {
    .cropper-point.point-se {
        height:15px;
        width: 15px
    }
}

@media (min-width: 992px) {
    .cropper-point.point-se {
        height:10px;
        width: 10px
    }
}

@media (min-width: 1200px) {
    .cropper-point.point-se {
        height:5px;
        opacity: .75;
        width: 5px
    }
}

.cropper-point.point-se:before {
    background-color: #39f;
    bottom: -50%;
    content: " ";
    display: block;
    height: 200%;
    opacity: 0;
    position: absolute;
    right: -50%;
    width: 200%
}

.cropper-invisible {
    opacity: 0
}

.cropper-bg {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC")
}

.cropper-hide {
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.cropper-hidden {
    display: none!important
}

.cropper-move {
    cursor: move
}

.cropper-crop {
    cursor: crosshair
}

.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point {
    cursor: not-allowed
}

.index-module_iconListContainer_2kTPj {
    min-width: 324px;
    padding: 0;
    display: flex;
    flex-direction: column
}

.index-module_iconListHeader_zgb1B {
    width: 100%;
    height: 42px;
    display: flex;
    align-items: center;
    padding-left: 27px;
    gap: 8px;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_iconListHeaderTab_Rb7UW {
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.index-module_iconListHeaderTabActive_l9CpK {
    color: var(--yq-text-primary);
    font-weight: 500
}

.index-module_iconListHeaderTabActive_l9CpK:after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    height: 2px;
    width: 100%;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_iconListContent_5acPP {
    height: 170px;
    display: grid;
    grid-template-rows: repeat(3,1fr);
    grid-template-columns: repeat(6,1fr);
    grid-gap: 8px;
    gap: 8px;
    padding: 16px
}

.index-module_iconListIconContainer_W2jPd {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: center;
    justify-self: center;
    width: 27px;
    height: 27px
}

.index-module_iconListIconContainerActive_m\+9aX:after {
    visibility: visible
}

.index-module_groupIconBtn_1pBes {
    width: 40px!important;
    height: 40px!important
}

.index-module_groupIconBtn_1pBes,.index-module_groupIconBtnLarge_PGNAs {
    display: flex!important;
    align-items: center;
    justify-content: center;
    padding: 0!important
}

.index-module_groupIconBtnLarge_PGNAs {
    width: 56px!important;
    height: 56px!important
}

.index-module_uploader_uEToK {
    min-height: 170px;
    padding: 16px
}

.index-module_dragger_yzTL7 {
    height: 138px!important;
    padding: 1px
}

.index-module_dragger_yzTL7 .ant-upload {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_imageContainer_lklS2 {
    padding: 8px 8px 0 8px
}

.index-module_uploaderContainer_Y0VkK {
    display: flex;
    align-items: flex-start
}

.index-module_uploaderContainer_Y0VkK .cropper-container {
    background-color: var(--yq-bg-secondary)
}

.index-module_reupload_3agQP {
    margin: 23px 0
}

.index-module_uploaderFooter_F5A9k {
    height: 50px;
    border-top: 1px solid var(--yq-border-primary);
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    gap: 8px;
    margin: 0 -24px;
    padding: 0 24px
}

.index-module_previewContainer_s7urA {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center
}

.index-module_preview_8GFmF {
    width: 108px;
    height: 108px;
    overflow: hidden;
    background-color: var(--yq-bg-secondary)
}

.index-module_newGroupModal_An\+Qw .ant-modal-header {
    border-bottom: none;
    padding-top: 20px;
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-modal-body {
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-form-vertical .ant-form-item-label>label {
    font-weight: 500
}

.index-module_newGroupModal_An\+Qw .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding: 0 24px 24px 24px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv {
    font-size: 16px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv .index-module_titleDesc_EmDkC {
    margin-top: 8px;
    color: var(--yq-text-caption);
    font-weight: 400;
    font-size: 14px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 {
    height: 40px;
    display: flex;
    margin-bottom: -12px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 .index-module_avatarGroup_olnac {
    border-radius: 44px;
    width: 44px;
    height: 44px;
    margin-right: 8px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 {
    width: 384px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a {
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a .index-module_btnText_6V0fp {
    margin-left: 4px;
    font-weight: 400
}

.index-module_newGroupModal_An\+Qw .index-module_submitBtn_ZJkHv {
    max-width: 100%;
    width: 384px;
    height: 40px
}

.group-selector .ant-select-item-group {
    background: var(--yq-bg-secondary)
}

.group-selector .group-selector-item {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 100%
}

.group-selector .group-selector-item>.larkicon {
    margin-left: 8px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-avatar {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 24px 24px;
    margin-right: 12px
}

.group-selector .group-selector-item .group-avatar .larkicon-svg-group-intranet,.group-selector .group-selector-item .group-avatar .larkicon-svg-group-lock {
    width: 12px;
    height: 12px
}

.group-selector .group-selector-item .group-selector-org-icon {
    margin-right: 12px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-selector-org-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.group-selector-disabled .selector-static {
    background: var(--yq-bg-secondary);
    cursor: not-allowed;
    border: 1px solid var(--yq-border-primary)
}

.group-selector-disabled .selector-static:before {
    display: none
}

.group-selector .ant-select-dropdown {
    width: 280px
}

.group-selector .ant-select-item-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.group-selector .ant-select-item-option-content .larkui-icon-check-outlined {
    margin: 0 0 0 8px;
    color: var(--yq-theme)
}

.group-selector .ant-select-selector {
    font-size: 14px
}

.index-module_name_2g47z {
    max-width: 200px
}

.Search-module_wrapper_Scz\+2 {
    display: flex;
    flex-flow: column;
    justify-content: center;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Search-module_wrapper_Scz\+2 .Search-module_img_CAujI {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.index-module_popMenu_aOUBY {
    border-radius: 12px 12px 0 0
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j {
    background-color: var(--yq-bg-tertiary);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    color: var(--yq-text-primary);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_item_-0ClC {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_popMenu_aOUBY .index-module_menu_0W97j .index-module_cancel_i36-H {
    background-color: var(--yq-bg-primary);
    text-align: center;
    font-size: 16px;
    padding: 14px 0;
    border-top: 1px solid var(--yq-border-primary);
    margin-top: 8px
}
