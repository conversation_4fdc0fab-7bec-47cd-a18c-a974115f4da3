<!-- 规则审核管理 -->

<script setup lang="ts">
import { rule_auditList, ruleType } from "@/api/rule_audit_management";
import { getAllBusinessByLoginUser } from "@/api/rule_base";
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import useTableConfig from '@/composables/useTableConfig';
import RuleAuditDetail from '@/businessComponents/ruleAudit/RuleAuditDetail.vue';

definePageMeta({
    title: '规则审核管理',
    path: '/ruleAuditManage'
})

const message = inject<any>("message");

const fetchRules = async (params: Record<string, any> = {}) => {
    try {
        let pars = {
            chineseName: params.rule_Basename,
            businessLine: params.businessLine,
            ruleName: params.rule_name,
            type: params.ruletext,
            page: params.page || 1,
            several: params.pageSize || 10,
        };
        const res = await rule_auditList(pars);
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取规则数据失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

interface Rule {
    ruleName: string;
    engName: string;
    checkStatus: string;
    checkId: string;
    checkTimeStr: string;
    uuid: string;
}

// 定义不包含序号列和操作列的表格列
const tableColumns = [
    {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        align: 'left',
        width: 250,
    },
    {
        title: '规则路径',
        dataIndex: 'rulePath',
        key: 'rulePath',
        align: 'left',
        width: 300
    },
    {
        title: '规则类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        width: 100
    },
    {
        title: '更新时间',
        dataIndex: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        align: 'center',
        width: 180
    },
    {
        title: '最后修改人',
        dataIndex: 'modifiedId',
        align: 'center',
        key: 'modifiedId',
        width: 100
    },
    {
        title: '规则状态',
        dataIndex: 'status',
        align: 'center',
        key: 'status',
        width: 100
    },
];

//字典值
const businessLineOptions = ref<Array<{name: string, code: string}>>([]);
const ruleTypeOptions = ref<Array<{name: string, code: string}>>([]);

// 规则类型
const getRuletype = async () => {
    try {
        const res = await ruleType({
            typeCode: "type_rule",
        });
        ruleTypeOptions.value = res.data;

        // 更新搜索配置中的规则类型选项
        const ruleTypeField = searchConfig.advancedSearchFields.find(field => field.field === 'ruletext');
        if (ruleTypeField && ruleTypeField.compConfig) {
            ruleTypeField.compConfig.options = ruleTypeOptions.value.map(item => ({
                name: item.name,
                value: item.code
            }));
        }
    } catch (error) {
        message.error('获取规则类型失败');
    }
}

// 获取业务条线
const getBusinessLines = async () => {
    try {
        const res = await getAllBusinessByLoginUser();
        businessLineOptions.value = res.data;

        // 更新搜索配置中的业务条线选项
        const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
        if (businessLineField && businessLineField.compConfig) {
            businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
                name: item.name,
                value: item.code
            }));
        }
    } catch (error) {
        message.error('获取业务条线失败');
    }
}

//组件挂载后
onMounted(() => {
    getRuletype();
    getBusinessLines();
});

// 添加抽屉相关的响应式变量
const auditDrawerVisible = ref(false);
const currentName = ref('');
const currentAuditUid = ref('');

// 修改审核处理函数
const handleAudit = (record: Rule) => {
    currentAuditUid.value = record.uuid;
    currentName.value = record.ruleName;
    auditDrawerVisible.value = true;
};

// 添加抽屉关闭处理函数
const handleAuditDrawerClose = () => {
    auditDrawerVisible.value = false;
    currentName.value = '';
    currentAuditUid.value = '';
};

// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '规则名称',
        field: 'rule_name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '规则名称',
            field: 'rule_name',
            compType: 'input' as const
        },
        {
            label: '规则库名称',
            field: 'rule_Basename',
            compType: 'input' as const
        },
        {
            label: '业务条线',
            field: 'businessLine',
            compType: 'select' as const,
            compConfig: {
                options: [] as {name: string, value: string}[]
            }
        },
        {
            label: '规则类型',
            field: 'ruletext',
            compType: 'select' as const,
            compConfig: {
                options: [] as {name: string, value: string}[]
            }
        },
        {
            label: '开始时间',
            field: 'fristTime',
            compType: 'datePicker' as const,
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false
            }
        },
        {
            label: '结束时间',
            field: 'endTime',
            compType: 'datePicker' as const,
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false
            }
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加空的新建事件以满足接口要求
    addNewEvent: () => {},
    // 不显示新建按钮的权限
    addPermission: '',
    // 新增：表单处理器配置
    formHandler: {
        // 日期字段特殊处理
        dateFields: {
            startField: 'fristTime',
            endField: 'endTime',
        },
        // 查询方法
        queryMethod: fetchRules
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: any) => {
    return [
        {
            key: 'audit',
            label: '审核',
            permission: RULE_PERMISSION.RULE_CHECK.AUDIT,
            onClick: () => handleAudit(record)
        }
    ];
};

const ruleAuditDetailRef = ref();

const handleSubmit = () => {
    if (ruleAuditDetailRef.value) {
        ruleAuditDetailRef.value.submitBtn('ruleForm');
    }
};

const handleSubmitSuccess = () => {
    handleAuditDrawerClose();
    listLayout.value?.refresh(); // 刷新列表数据
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

// 自定义渲染列
const customRenderColumns = ['rulePath', 'type'];
</script>

<template>
    <ListPage
        ref="listLayout"
        title="规则审核管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="false"
        :tableColumns="tableColumns"
        :queryMethod="fetchRules"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
    >
        <template #rulePath="{ record }">
            <RulePath
                :path="record.packageNameAll"
                showCopyButton
            />
        </template>

        <template #type="{ record }">
            <span v-if="record.type === '决策表'">决策表</span>
            <span v-else-if="record.type === '普通规则'">普通规则</span>
            <span v-else-if="record.type === '规则流' && record.salience === '1'">规则流</span>
            <span v-else-if="record.type === '规则流' && record.salience === '0'">子规则流</span>
            <span v-else-if="record.type === '决策树'" color="error">决策树</span>
        </template>
    </ListPage>

    <!-- 添加审核抽屉组件 -->
    <FlexDrawer
        :visible="auditDrawerVisible"
        @close="handleAuditDrawerClose"
        :title="`规则审核 - ${currentName}`"
        :width="1200"
        v-if="auditDrawerVisible"
    >
        <RuleAuditDetail
            :sid="currentAuditUid"
            ref="ruleAuditDetailRef"
            @submit-success="handleSubmitSuccess"
        />
        <template #footer>
            <div style="text-align: right">
                <a-button type="primary" @click="handleSubmit">提交</a-button>
            </div>
        </template>
    </FlexDrawer>
</template>

<style lang="scss" scoped>
.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}
</style>
