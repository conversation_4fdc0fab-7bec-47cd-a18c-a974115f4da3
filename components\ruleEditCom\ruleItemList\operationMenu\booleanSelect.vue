<template>
  <span class="actionTypeCom" style="color: #000090; font-weight: bold">
    <span v-if="locked || isTrack" class="actionTypeItem txtItem">{{ txt }}</span>
    <a-cascader
      v-else
      :options="selfData"
      expand-trigger="hover"
      :value="[value]"
      @change="onSelectChange"
    >
      <span class="actionTypeItem txtItem">{{ txt }}</span>
    </a-cascader>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { findLabel } from '@/components/ruleEditCom/utils/displayUtil.js'

interface Props {
  value?: string
  pos: string
  locked: boolean
  isTrack: boolean
}

const props = withDefaults(defineProps<Props>(), {
  value: 'null'
})

const emit = defineEmits<{
  (e: 'onChange', pos: string, value: string): void
}>()

const selfData = [
  {
    value: 'null',
    label: '请选择'
  },
  {
    value: 'true',
    label: '是'
  },
  {
    value: 'false',
    label: '否'
  }
]

const txt = computed(() => {
  return findLabel(selfData, [props.value])
})

const onSelectChange = (value: any, selectedOptions?: any) => {
  const selectedValue = Array.isArray(value) ? value[0] : value
  emit('onChange', props.pos, selectedValue)
}
</script>
