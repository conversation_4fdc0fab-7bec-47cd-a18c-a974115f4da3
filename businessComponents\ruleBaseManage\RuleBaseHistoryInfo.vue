<!-- 规则历史信息详情 -->

<template>
    <div class="rule-base-history-info">
        <RuleInfoDisplay
            :data="tableData"
            :config="historyConfig"
            :showRuleContent="true"
            :hisFlag="true"
            :historyId="props.dataId"
        />
    </div>
</template>

<script setup lang="ts">
import { read2 } from "@/api/rule_base";
import { ref, inject } from 'vue';

const props = defineProps({
    dataId: {
        type: String,
        default: () => '',
    }
});

const tableData = ref({});

// 历史信息配置
const historyConfig = [
    { label: '规则名称', field: 'ruleName', span: 12 },
    { label: '规则路径', field: 'packageNameAll', span: 12 },
    { label: '规则状态', field: 'status', span: 12 },
    { label: '加锁人', field: 'lockId', span: 12 },
    { label: '修改时间', field: 'lastModifiedTimeStr', span: 12 },
    { label: '修改人', field: 'modifiedId', span: 12 },
    { label: '创建时间', field: 'createdTimeStr', span: 12 },
    { label: '创建人', field: 'createdId', span: 12 },
    { label: '规则描述', field: 'descs', span: 23 }
];

onMounted(async () => {
    if (props.dataId) {
        let res = await read2({
            uuid: props.dataId,
        });
        tableData.value = res.data;
    }
});
</script>

<style lang="scss" scoped>
.rule-base-history-info {
    padding: 20px;
}
</style>
