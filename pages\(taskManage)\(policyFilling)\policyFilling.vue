<!-- 政策填报页 -->

<script setup lang="ts">

    import { useRouter } from 'vue-router';
    import { getBaseLine } from '@/api/baseline_Management';
    import { policyFilling, userOrgList, deletePolicy } from '@/api/task';
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    definePageMeta({
        title: '政策填报'
    })

    const router = useRouter();
    const modal = inject('modal');
    const message = inject('message');
    interface FormState {
        uuid: string;
        appNo: string;
        endDate: string;
        applyOrgId: string;
        demandName: string;
        businessLine: string;
        startDate: string;
        page: number;
        several: number;
        applyOrgName: string;
        createdTimeStr: string;
        createdId: string;
        createdName: string;
        demandUuid: string;
        bizKey: string;
        demandApplyQO: string;
        state: string;
        endTime: string;
        fristTime: string;
    }

    const form = ref<FormState>({
        uuid: '',
        appNo: '',
        endDate: '',
        applyOrgId: '',
        demandName: '',
        businessLine: '',
        startDate: '',
        page: 1,
        several: 10,
        applyOrgName: '',
        createdTimeStr: '',
        createdId: '',
        createdName: '',
        demandUuid: '',
        bizKey: '',
        demandApplyQO: '',
        state: '',
        endTime: '',
        fristTime: '',
    });

    const columns = reactive([{
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        align:'center',
        ellipsis: true,
        width: 50,
        fixed: 'left',
        customRender: ({ index }) => `${(paginations.value.page - 1) * paginations.value.limit + index + 1}`,
    },...tableColumns]);
    const paginations = ref({
        loading: true,
        total: 0, // 总数
        limit: 10, // 一页显示都是条
        page: 1, // 当前页
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条数据`,
    });

    const loading = ref(false); // 添加 loading 变量

    const currentPage = ref(0); // 当前页码
    const businessLineOption = ref([]);
    const modelTypeOptions = ref([]);
    const taskStateOptions = [
        { value: '24', label: '政策填报中' },
        { value: '27', label: '政策审核退回' },
    ];

    onMounted(() => {
            //list();
            getOption();
    });

    // 确认删除
    const openDelete = (row: any) => {
        modal.confirm({
            title: '温馨提示',
            content: '您确定删除当前政策吗?',
            okText: '确定',
            cancelText: '取消',
            type: 'warning',
            onOk() {
                deletePolicy({ uuid: row.uuid }).then((res: any) => {
                    if (res.code === 20000) {
                        message.success('删除成功')
                        list();
                    }
                });
            },
            onCancel() {
                message.info('已取消')
            },
        });
    };

    // 新增
    const addPolicy = () => {
      navigateTo('policyAdd')
    };

    const getCreatedName = (value: string) => {
        form.value.applyOrgId = '';
        let name = '';
        businessLineOption.value.map((i: any) => {
            if (i.code === value) name = i.name;
        });
        userOrgList({
            roleName: 'DEMAND_POLICY_ROLE_DECLARE',
            businessCode: value,
        }).then((res: any) => {
            modelTypeOptions.value = res.data;
        });
    };

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLineOption.value = res.data;
        });
    };

    const reset = () => {
        form.value.appNo = '';
        form.value.demandName = '';
        form.value.fristTime = '';
        form.value.endTime = '';
        form.value.businessLine = '';
        form.value.createdName = '';
        form.value.applyOrgId = '';
        form.value.createdTimeStr = '';
        form.value.state = '';
        list();
    };

    const startTimeChange = (val) => {
        form.value.fristTime = `${val} 00:00:00`;
    };

    const endTimeChange = (val) => {
        form.value.endTime = `${val} 23:59:59`;
    };

    const queryList = () => {
        paginations.value.page = 1;
        currentPage.value = 1;
        list();
    };
    const tableDate = ref([]);
    const list = () => {
        loading.value = true; // 查询开始时设置 loading 为 true
        policyFilling({
            appNo: form.value.appNo,
            applyOrgId: form.value.applyOrgId,
            businessLine: form.value.businessLine,
            createdName: form.value.createdName,
            demandName: form.value.demandName,
            startDate: form.value.fristTime,
            endDate: form.value.endTime,
            page: paginations.value.page,
            several: paginations.value.limit,
            createdTimeStr: form.value.createdTimeStr,
            state: form.value.state,
        }).then((res: any) => {
            tableDate.value = res.data.data;
            paginations.value.total = res.data.totalCount;
            loading.value = false; // 查询结束时设置 loading 为 false
        });
    };

    const updateFun = (row: any) => {
        router.push({
            name: 'policyInfos',
            query: {
                type: 'addPolicy',
                uuid: row.uuid,
                appNo: row.appNo,
                bizKey: row.bizKey,
                file: row.file,
                roleName: row.ruleName,
                businessCode: row.businessLine,
                createdId: row.createdId,
                createdName: row.createdName,
            },
        });
    };

    const toInfo = (row: any) => {
        router.push({
            name: 'policyInfo',
            query: {
                type: 'policyInfo',
                uuid: row.uuid,
                appNo: row.appNo,
                bizKey: row.bizKey,
                businessCode: row.businessLine,
                createdTime: row.createdTime,
            },
        });
    };

    const pagin = (cur,pageSize) => {
        paginations.value.page = cur;
        paginations.value.limit = pageSize;
        list();
    };
    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'addPolicy') {
            datar.value = {
                type: "addPolicy",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                file: record.file,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                createdId: record.createdId,
                createdName: record.createdName,
            };
        }
        if (type === 'policyInfo') {
            datar.value = {
                type: "policyInfo",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        isModalVisible.value = true;
    };
    const close = (flag) => {
        if(flag){//如果是数据提交，则刷新表单
            list();
        }
        isModalVisible.value = false;
    }

</script>

<template>
    <ListPageLayout  :reloadFun="reset" :columns="columns">

        <template #title>
            <!-- 页面标题 -->
            政策填报
        </template>

        <template #search>
            <!-- 搜索区域 -->
           <a-form v-modal:model="form" layout="inline" label-width="100px" @keyup.enter="queryList">
                <a-form-item label="任务编码">
                    <a-input autocomplete="off" v-model:value="form.appNo" placeholder="任务编码" allow-clear="true" />
                </a-form-item>
                <a-form-item label="任务名称">
                    <a-input autocomplete="off" v-model:value="form.demandName" placeholder="任务名称" allow-clear="true" />
                </a-form-item>
                <a-form-item label="业务条线">
                    <a-select
                            @change="getCreatedName"
                            v-model:value="form.businessLine"
                            placeholder="请选择"
                            style="width: 200px"
                            :filterOption="filterOption" showSearch
                    >
                        <a-select-option
                                v-for="item in businessLineOption"
                                :key="item.uuid"
                                :value="item.code"
                                :name="item.name"
                        >
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="归属机构">
                    <a-select
                            v-model:value="form.applyOrgId"
                            placeholder="请选择"
                            filterable
                            style="width: 200px"
                    >
                        <a-select-option
                                v-for="item in modelTypeOptions"
                                :key="item.id"
                                :value="item.id"
                        >
                            {{ item.orgName }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="申请时间" name="fristTime" v-model:value="form.createdTimeStr">
                    <a-date-picker v-model:value="form.fristTime" valueFormat="YYYY-MM-DD 00:00:00" format="YYYY-MM-DD"/>
                </a-form-item>
                <a-form-item label="至" name="endTime">
                    <a-date-picker v-model:value="form.endTime" valueFormat="YYYY-MM-DD 23:59:59" format="YYYY-MM-DD"/>
                </a-form-item>
                <a-form-item label="申请人">
                    <a-input autocomplete="off" v-model:value="form.createdName" placeholder="申请人" allow-clear="true" />
                </a-form-item>
                <a-form-item label="任务状态">
                    <a-select v-model:value="form.state" placeholder="请选择" style="width: 150px">
                        <a-select-option
                                v-for="item in taskStateOptions"
                                :key="item.value"
                                :value="item.value"
                        >
                            {{ item.label }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item class="buttonItem">
                    <a-button
                            type="primary"
                            @click="queryList"
                            style="margin-right: 8px"
                    >
                        查询
                    </a-button>
                    <a-button @click="reset" style="margin-right: 8px">
                        重置
                    </a-button>
                    <a-button  @click="addPolicy"  type="success" >
                        新增
                    </a-button>
                </a-form-item>
            </a-form>
        </template>

        <template #table="slotProps">
            <!-- 表格区域 -->
            <a-table :scroll="{ x: 'max-content', y: slotProps.scrollY }" :columns="slotProps.filterColumn" :data-source="tableDate" :pagination="false" row-key="uuid" :loading="loading" size="small">
                <template v-slot:bodyCell="{column,record,index}">
                    <template v-if="column.dataIndex === 'demandName'">
                        <a-button type="link" size="small" @click="showModal('policyInfo',record)">{{record.demandName}}</a-button>
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <span>{{ TASK_STATE[record.state] }}</span>
                    </template>
                    <template v-if="column.dataIndex === 'action'">
                        <a-button type="link" @click="showModal('addPolicy',record)" size="small">更新</a-button>
                        <a-button type="link" @click="openDelete(record)" size="small">删除</a-button>
                    </template>
                </template>
            </a-table>
            <Pagination
                    :paginations="paginations"
                    @change="pagin"
                    :scrollY="slotProps.scrollY"
            />
        </template>
        <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" v-if="isModalVisible"/>
    </ListPageLayout>
</template>
