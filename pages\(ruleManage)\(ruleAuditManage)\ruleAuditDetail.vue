<!-- 规则审核管理页 -->

<script setup lang="ts">
    import {
        getRuleinfo,
        auditBack,
        auditPass,
        getHistoryInfo,
    } from "@/api/rule_audit_management";
    import { differ, beautyTable } from "@/api/table_diff";
    // @ts-ignore
    import qs from "qs";
    // @ts-ignore
    import $ from 'jquery';
    import { computed, ref, inject } from 'vue';
    import RuleCompareContent from "@/components/ruleDetailCom/RuleCompareContent.vue";

    interface AuditData {
        ruleVO?: any;
        ruleHisVO?: any;
        packageNameAll?: string;
        ruleName?: string;
        ruleFlowInfo?: string;
        uuid?: string;
        lastModifiedTimeStr?: string;
        modifiedId?: string;
        createdTimeStr?: string;
        createdId?: string;
        salience?: string;
        descs?: string;
        validStatus?: string;
        edition?: string;
        type?: string;
        textDtl?: string;
        tableContent?: string;
        tableContentHis?: string;
        style?: CSSStyleDeclaration;
    }

    interface FormData {
        result_check: string;
        result_Descriptor: string;
        fristTime?: string;
        endTime?: string;
    }

    definePageMeta({
        title: '提交审核详情',
        path: '/ruleAuditManage/ruleAuditDetail',
        name: 'ruleAuditDetail'
    })

    const message = inject("message") as any;
    const router = useRouter();
    const ruleInfo = ref<HTMLElement | null>(null);
    const ruleForm = ref(null);

    const data = ref({
        activeName: "first",
        auditId: "",
        auditAllRuleInfo: {} as AuditData,
        auditAll: {} as AuditData,
        auditHistory: {} as AuditData,
        strPackageNameAll: "",
        strRuleName: "",
        strAll: [] as string[],
        straudit: "",
        aduitVal: "",
        resultText: false,
        ruleNameColor: false,
        validColor: false,
        hisValidColor: false,
        salienceColor: false,
        curDataId: "datatable_tc",
        hisDataId: "datatable2_tc",
        sliderValue: 100,
        tableScale: 1,
        tableFullscreen: false,
        tip: "",
        tipTop: "0px",
        tipLeft: "0px",
        showTip: false,
        tabHeight: "600px",
        switchVal: false,
        form: {
            result_check: "1",
            result_Descriptor: "同意",
        } as FormData,
        rules: {
            result_check: [{ required: true, message: "不能为空", trigger: "change" }],
            result_Descriptor: [{ required: true, message: "不能为空", trigger: "change" }]
        },
        checkedValue:'长文本隐藏',
    })
    //组件挂载后
    onMounted(() => {
        data.value.auditId = router.currentRoute.value.query.sid as string;
        getRuleAudit();

    });

    // 获取展示信息
    const getRuleAudit = () => {
        let pars = {
            ruleUuid: data.value.auditId,
        };
        getRuleinfo(pars).then((res) => {
            data.value.auditAllRuleInfo = res.data;
            data.value.auditAll = data.value.auditAllRuleInfo.ruleVO || {};
            data.value.auditHistory = data.value.auditAllRuleInfo.ruleHisVO || {};
            data.value.strPackageNameAll = data.value.auditAll.packageNameAll || '';
            data.value.strRuleName = data.value.auditAll.ruleName || '';
            data.value.straudit = data.value.strPackageNameAll + "." + data.value.strRuleName;

            if (ruleInfo.value && data.value.auditAll.ruleFlowInfo === "当前规则库无规则流") {
                ruleInfo.value.style.color = "red";
            }

            const docHeight = document.documentElement.clientHeight;
            const rowH = document.querySelector("#a-row")?.offsetHeight || 0;
            data.value.tabHeight = `${docHeight - rowH - 130 - 180 - 38 + 220}px`;

            setTimeout(() => {
                const tableO = document.querySelectorAll(".datatable");
                const aHeight: number[] = [];
                for (let i = 0; i < tableO.length; i++) {
                    const height = tableO[i].clientHeight;
                    if (height) aHeight.push(height);
                }
                aHeight.sort((a, b) => b - a);
                if (aHeight.length > 0 && aHeight[0] < 150) {
                    data.value.tabHeight = `${docHeight - rowH - 130 - 180 - 38}px`;
                }
            });
        });
    }
    const startTimeChange = (val: string) => {
        data.value.form.fristTime = `${val} 00:00:00`;
    }
    const endTimeChange = (val: string) => {
        data.value.form.endTime = `${val} 23:59:59`;
    }
    //提交 (通过/退回)
    const submitBtn = (formName: string) => {

        if (!ruleForm.value) return;
        ruleForm.value.validate().then((valid) => {
            if (valid) {
                let backData = qs.stringify({
                    failRemark: data.value.form.result_Descriptor,
                    ruleUuid: data.value.auditId,
                });
                let passData = qs.stringify({
                    ruleUuid: data.value.auditId,
                    checkComment: data.value.form.result_Descriptor
                });
                if(data.value.form.result_check == '0'){
                    auditBack(backData).then((res) => {
                        message.success(res.data);
                        router.push('/ruleAuditManage')
                    });
                } else {
                    auditPass(passData).then((res) => {
                        message.success(res.data);
                        router.push('/ruleAuditManage')
                    });
                }
            }
        })
    }

    const resetForm = () => {
        navigateTo("/ruleAuditManage")
    };

    // 处理提示框的显示和隐藏
    const tipHover = () => {
        data.value.showTip = true;
    };

    const tipLeave = () => {
        data.value.showTip = false;
    };

    // 当前版本配置
    const currentVersionConfig = [
        { label: '规则名称', field: 'ruleName', class: computed(() => data.value.ruleNameColor ? 'biaoColor' : '') },
        { label: '规则路径', field: 'packageNameAll', format: (value, data) => data.straudit },
        { label: '修改时间', field: 'lastModifiedTimeStr' },
        { label: '修改人', field: 'modifiedId' },
        { label: '创建时间', field: 'createdTimeStr' },
        { label: '优先级', field: 'salience', class: computed(() => data.value.salienceColor ? 'biaoColor' : '') },
        { label: '规则描述', field: 'descs', span: 12 },
        { label: '规则流信息', field: 'ruleFlowInfo', span: 12, ref: 'ruleInfo' }
    ];

    // 历史版本配置
    const historyVersionConfig = [
        { label: '规则名称', field: 'ruleName' },
        { label: '规则包', field: 'packageNameAll' },
        { label: '规则版本', field: 'edition' },
        { label: '有效状态', field: 'validStatus', class: computed(() => data.value.hisValidColor ? 'biaoColor' : '') },
        { label: '修改时间', field: 'lastModifiedTimeStr' },
        { label: '修改人', field: 'modifiedId' },
        { label: '创建时间', field: 'createdTimeStr' },
        { label: '创建人', field: 'createdId' },
        { label: '规则描述', field: 'descs', span: 12 }
    ];
</script>

<template>
    <NotListPageLayout>

        <template #title>
            <!-- 页面标题 -->
            提交审核详情
        </template>

        <template #search>
            <div class="rule_audit">
                <a-tabs type="card" v-model="data.activeName">
                    <a-tab-pane
                            tab="当前版本信息"
                            key="first"
                            style="margin-bottom: 30px;"
                            class="first-tab"
                    >
                        <div>
                            <a-form :model="data.form" laba-width="100px" style="height: 100%" :rules="data.rules" ref="ruleForm">
                                <RuleInfoDisplay
                                    :data="data.auditAll"
                                    :config="currentVersionConfig"
                                    :showRuleContent="false"
                                    :maxHeight="270"
                                />

                                <!-- 添加规则内容对比部分 -->
                                <a-row type="flex" class="row-bg fullrow fulltab" style="margin-left: 5px">
                                    <a-col class="col-detail" :span="24">
                                        <RuleCompareContent
                                            :rule-compare-data="{
                                                ruleHisVO1: data.auditAllRuleInfo.ruleVO,
                                                ruleHisVO2: data.auditAllRuleInfo.ruleHisVO
                                            }"
                                            :tabHeight="270"
                                        />
                                    </a-col>
                                </a-row>

                                <a-row class="custom-height" align="middle" style="margin-top: 5px">
                                    <a-col :span="20">
                                        <a-form-item label="审核结果:" name="result_check">
                                            <a-select
                                                    v-model:value="data.form.result_check"
                                                    placeholder="请选择审核结果"
                                            >
                                                <a-select-option value="1">提交通过</a-select-option>
                                                <a-select-option value="0">提交退回</a-select-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row class="custom-height" align="middle" style="margin-top: 10px">
                                    <a-col :span="20">
                                        <a-form-item
                                                label="审核意见:"
                                                name="result_Descriptor"
                                        >
                                            <a-textarea v-model:value="data.form.result_Descriptor" :auto-size="{ minRows: 1, maxRows: 4 }" :maxlength="500"
                                                        show-word-limit />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row>
                                    <a-col :span="20">
                                        <a-form-item
                                                style="margin-top: 10px;float: right"
                                                laba-width="80px"
                                                class="btn-item"
                                        >
                                            <a-button @click="submitBtn('ruleForm')" type="primary" style="margin-right: 8px">提交</a-button>
                                            <a-button @click="resetForm('ruleForm')">返回</a-button>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane tab="历史版本信息" key="second">
                        <div>
                            <RuleInfoDisplay
                                    :data="data.auditHistory"
                                    :config="historyVersionConfig"
                                    :showRuleContent="false"
                                    :showDetailFlag="true"
                            />
                        </div>
                    </a-tab-pane>
                </a-tabs>
                <div
                        id="tip-div"
                        v-html="data.tip"
                        :style="{ top: data.tipTop, left: data.tipLeft }"
                        v-show="data.showTip"
                        @mouseenter="tipHover"
                        @mouseleave="tipLeave"
                ></div>
            </div>
        </template>


    </NotListPageLayout>
</template>
<style lang="scss" scoped>
    :deep(.rule_audit) {
        .custom-height {
            height: 30px; /* 自定义高度 */
        }
        .a-input-number--mini .a-input-number__decrease,
        .a-input-number--mini .a-input-number__increase {
            width: 15px;
            height: 15px;
            line-height: 15px;
            top: 4px;
        }
        .fullrow {
            background: #fff;
        }
        .biaoColor {
            color: red;
        }
        .a-form {
            margin-bottom: 30px;
        }
        .a-form-item {
            margin-bottom: 0px;
        }
        .a-row {
            margin-top: 0px;
        }
    }
</style>
