/* eslint-disable no-unused-vars */
import inherits from 'inherits'

import <PERSON>Renderer from 'diagram-js/lib/draw/BaseRenderer'
import {
  isObject,
  assign,
  forEach
} from 'lodash';
import {
  append as svgAppend,
  create as svgCreate,
  classes as svgClasses
} from 'tiny-svg'

import {
  customElements,
  customConfig
} from '../../utils/util_tree'
/**
 * A renderer that knows how to render custom elements.
 */
export default function CustomRenderer(eventBus, styles, textRenderer) {
  BaseRenderer.call(this, eventBus, 2000)

  var computeStyle = styles.computeStyle

  function renderLabel(parentGfx, label, options) {

    options = assign({
      size: {
        width: 100
      }
    }, options);

    var text = textRenderer.createText(label || '', options);

    svgClasses(text).add('djs-label');

    svgAppend(parentGfx, text);

    return text;
  }

  this.drawCustomElements = function (parentNode, element) {
    let type = element.type // 获取到类型
    if (type !== 'label') {
      if (customElements.includes(type)) { // or customConfig[type]
        const {
          url,
          attr
        } = customConfig[type]
        const customIcon = svgCreate('image', {
          ...attr,
          href: url
        })
        element['width'] = attr.width
        element['height'] = attr.height
        svgAppend(parentNode, customIcon)

        // 判断是否有name属性来决定是否要渲染出label
        if (element.type === 'bpmn:Task') {
          const busObj = element.businessObject
          if (busObj.name) {
            const text = svgCreate('text', {
              x: attr.x-5,
              y: attr.y + attr.height + 13,
              "font-size": "11",
              "fill": "#000"
            })
            text.innerHTML = busObj.name
            svgAppend(parentNode, text)
            renderLabel(parentNode, element.label)
          }
        }
        return customIcon
      }
      //   const shape = this.bpmnRenderer.drawShape(parentNode, element)
      //   return shape
    } else {
      element
    }
  }
}

inherits(CustomRenderer, BaseRenderer)

CustomRenderer.$inject = ['eventBus', 'styles', 'textRenderer']

CustomRenderer.prototype.canRender = function (element) {
  // ignore labels
  return true
  // return !element.labelTarget;
}

CustomRenderer.prototype.drawShape = function (p, element) {
  if (customElements.includes(element.type)) {
    return this.drawCustomElements(p, element)
  }
}

CustomRenderer.prototype.getShapePath = function (shape) {

}
