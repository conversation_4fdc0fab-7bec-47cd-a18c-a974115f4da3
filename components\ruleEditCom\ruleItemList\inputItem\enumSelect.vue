<template>
  <span v-if="disabled" style="min-width: 150px">{{ label }}</span>
  <!-- {{delElseFlag}}{{readonly}}{{elseFlag}} -->
  <div v-else class="span-con-else" style="display: inline-flex">
    <a-select
      :style="width"
      :class="enumDictName === 'boolean' ? 'booleanSelect' : ''"
      v-model:value="innerValue"
      allowClear
      showSearch
      :filter-option="false"
      placeholder="输入进行搜索"
      @change="handleChange"
      @search="handleSearch"
      :title="titleStr"
      v-if="
        (delElseFlag && !readonly) ||
        (delRead && !elseFlag) ||
        (!elseFlag && !readonly)
      "
    >
      <a-select-option
        :title="item.viewName"
        :value="item.value"
        v-for="(item, index) in filteredOptions"
        :key="index + '_' + item.value"
      >
        {{ item.viewName }}
      </a-select-option>
    </a-select>
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <TableConditionOperationBox
      class="table-opera-box"
      :index="index"
      :col="col"
      :rowData="rowData"
      :colData="colData"
      :record="record"
      :elseFlag="elseFlag"
      :delElseFlag="delElseFlag"
      :readonly="readonly"
      v-if="col && col[4] !== '2' && !readonly"
      @operaChange="operaChange"
    />
  </div>
</template>

<script setup>
import store from "@/store";
import { calculateWidth } from "@/components/ruleEditCom/utils/inputItmeUtil";
import { findViewName } from "@/components/ruleEditCom/utils/displayUtil";
import TableConditionOperationBox from "@/components/ruleEditCom/ruleItemList/operationMenu/tableConditionOperationBox";

// 注入 ruleUuid
const ruleUuid = inject('ruleUuid', '');

// 定义组件的 props
const props = defineProps({
  isTable: {
    type: Boolean,
    default: false,
  },
  enumDictName: {
    type: String,
    default: "",
  },
  value: {
    type: [String, Array],
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  titleStr: {
    type: String,
    default: "",
  },
  refListData: {
    type: Boolean,
    default: false,
  },
  index: {
    type: Number,
    default: 0,
  },
  col: {
    type: String,
    default: "",
  },
  elseFlag: {
    type: Boolean,
    default: false,
  },
  delElseFlag: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  delRead: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Array,
    default: () => [],
  },
  colData: {
    type: Array,
    default: () => [],
  },
  record: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件的 emits
const emit = defineEmits(['onChange', 'operaChange']);

// 定义响应式数据
const optionData = ref([]);
const innerValue = ref(props.value);
// 添加响应式数据来存储过滤后的选项
const filteredOptions = ref([]);

// 计算属性
const label = computed(() => findViewName(optionData.value, innerValue.value));
const width = computed(() => ({
  width: calculateWidth(label.value, "enum"),
  color: props.enumDictName === "boolean" ? "#000090 !important" : "unset",
  fontWeight: props.enumDictName === "boolean" ? "bold !important" : "normal",
}));

// 方法
const operaChange = (action) => {
  if (props.elseFlag && action === "otherwise") {
    return;
  } else if (action === "otherwise") {
    emit("onChange", { value: "否则", elseFlag: true });
  } else {
    emit("onChange", "");
  }
  let dataIndex = props.rowData.findIndex((item) => item.key === props.record.key);
  emit("operaChange", {
    action,
    index: dataIndex,
    col: props.col,
  });
};

const fetchData = () => {
  if (props.enumDictName) {
    const list = store.getters.listMap[ruleUuid].dictMap;
    optionData.value = list[props.enumDictName];
    // 确保每个选项的 viewName 和 value 都是字符串类型
    if (optionData.value && optionData.value.length > 0) {
      optionData.value = optionData.value.map(item => ({
        ...item,
        viewName: String(item.viewName || ''),
        value: String(item.value || '')
      }));
    }
  }
};

const handleChange = (value = null) => {
  emit("onChange", value);
};

const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 处理搜索事件
const handleSearch = debounce((value) => {
  if (!value) {
    filteredOptions.value = optionData.value;
    return;
  }
  
  const inputValue = value.toLowerCase().trim();
  
  filteredOptions.value = optionData.value.filter(item => {
    // 确保处理 null 或 undefined 值
    const viewName = String(item.viewName || '').toLowerCase();
    const itemValue = String(item.value || '').toLowerCase();
    
    return viewName.includes(inputValue) || itemValue.includes(inputValue);
  });
}, 200); // 减少防抖时间，提高响应速度

// 监听 enumDictName 的变化
watch(
  () => props.enumDictName,
  () => {
    fetchData();
  }
);

// 监听 value 的变化
watch(
  () => props.value,
  (newVal) => {
    innerValue.value = newVal;
  },
  { deep: true }
);

// 监听 optionData 的变化
watch(() => optionData.value, (newVal) => {
  filteredOptions.value = newVal;
}, { immediate: true });

// 组件挂载后初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
.inputItem {
  .txtItem {
    display: inline-block;
  }
}
</style>
