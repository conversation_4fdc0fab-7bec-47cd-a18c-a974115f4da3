<!-- 规则顶部栏组件 -->
<script setup lang="ts">import globalEventEmitter from '~/utils/eventBus';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from "vue";
import {
    RULE_NAVIGATE_ACTION,
    RULE_NAVIGATION_COMPLETE,
    RULE_SUBMIT_ACTION,
    RULE_WITHDRAW_ACTION,
    RULE_UPDATE_ACTION,
    RULE_DELETE_ACTION,
    RULE_LOCK_ACTION,
    RULE_UNLOCK_ACTION,
    RULE_HISTORY_ACTION,
    RULE_UPDATED
} from '@/consts/globalEventConsts';
import { RULE_STATUS } from '@/consts/ruleStatusConsts';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";
import {
    isLock,
    ruleDelete,
    toLock,
    unLock,
    submitRule,
    getRuleByUuid,
    getRuleHistoryList,
    ruleCall,
} from "@/api/rule_base";
import qs from 'qs';
import RuleHistory from "@/businessComponents/ruleBaseManage/RuleHistory";
import RuleUpdate from "@/businessComponents/ruleBaseManage/RuleUpdate";
import { recordAccess, collectRule, CollectObjectType } from '@/api/dashboardApi';
import { useRoute } from 'vue-router';

const props = defineProps({
    ruleTitle: {
        type: String,
        default: ''
    },
    packageName: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: ''
    },
    uuid: {
        type: String,
        default: ''
    },
    ifCollect: {
        type: Boolean,
        default: false
    },
    ruleInfo: {
        type: Object,
        default: () => ({})
    },
    engUuid: {
        type: String,
        default: ''
    },
    demandUuid: {
        type: String,
        default: ''
    },
    symbol: {
        type: Boolean,
        default: true
    },
    status: {
        type: String,
        default: ''
    }
});

const message = inject<any>('message');
const modal = inject('modal');
const route = useRoute();
// 是否是第一个规则
const isFirstRule = ref(false);
// 是否是最后一个规则
const isLastRule = ref(false);
// 组件内部状态
const innerCollect = ref(props.ifCollect);

// 监听props变化同步内部状态
watch(() => props.ifCollect, (newVal) => {
    innerCollect.value = newVal;
});

// 关闭 - 直接实现，不使用emit
const goClose = () => {
    window.close();
};

// 处理收藏 - 直接实现，不使用emit
const handleCollect = async () => {
    try {
        await collectRule({
            uuid: props.uuid as string,
            type: CollectObjectType.RULE
        });
        innerCollect.value = !innerCollect.value;
        message.success(innerCollect.value ? '收藏成功' : '取消收藏成功');
        refresh();
    } catch (error) {
        message.error(innerCollect.value ? '收藏失败' : '取消收藏失败');
        console.error('收藏操作失败:', error);
    }
};

/**
 * 生成规则URL
 * @returns {{fullUrl: string, basePath: string, lastUrl: string}} 返回完整URL和组成部分
 */
const generateRuleUrl = () => {
    // 获取当前URL
    const currentUrl = window.location.href;
    // 截取到ruleBase-之前的部分，包括域名和协议
    const basePath = currentUrl.split('/ruleBase-')[0];
    // 构建剩余路径
    const lastUrl = `/ruleBase-${props.engUuid}/rulePackage-${props.demandUuid || 'all'}/ruleContent-${props.uuid}?backFlag=dashboard`;

    return {
        fullUrl: basePath + lastUrl,
        basePath,
        lastUrl
    };
};

// 复制当前规则链接 - 使用更可靠的方法
const copyRuleLink = () => {
    // 使用提取的函数获取URL
    const { fullUrl } = generateRuleUrl();

    // 尝试使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(fullUrl)
            .then(() => {
                message.success('链接已复制到剪贴板');
            })
            .catch(() => {
                // 如果 Clipboard API 失败，回退到传统方法
                fallbackCopyTextToClipboard(fullUrl);
            });
    } else {
        // 不支持 Clipboard API 的浏览器使用传统方法
        fallbackCopyTextToClipboard(fullUrl);
    }
};

// 传统复制方法作为备选方案
const fallbackCopyTextToClipboard = (text: string) => {
    try {
        // 创建临时输入元素
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 设置样式使其不可见
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 执行复制命令
        const successful = document.execCommand('copy');

        // 移除临时元素
        document.body.removeChild(textArea);

        if (successful) {
            message.success('链接已复制到剪贴板');
        } else {
            message.error('复制失败');
        }
    } catch (err) {
        message.error('复制失败');
        console.error('复制失败:', err);
    }
};

const formatValue = (value: any, format: string) => {
    if (format === 'status') return RULE_STATUS[value as keyof typeof RULE_STATUS];
    return value;
};

// 内部数据状态
const internalRuleTitle = ref(props.ruleTitle);
const internalPackageName = ref(props.packageName);
const internalType = ref(props.type);
const internalUuid = ref(props.uuid);
const internalEngUuid = ref(props.engUuid);
const internalDemandUuid = ref(props.demandUuid);
const internalRuleInfo = ref(props.ruleInfo);
const internalStatus = ref(props.status);
// 监听props变化更新内部状态
watch(() => props.ruleTitle, (val) => internalRuleTitle.value = val);
watch(() => props.packageName, (val) => internalPackageName.value = val);
watch(() => props.type, (val) => internalType.value = val);
watch(() => props.uuid, (val) => internalUuid.value = val);
watch(() => props.engUuid, (val) => internalEngUuid.value = val);
watch(() => props.demandUuid, (val) => internalDemandUuid.value = val);
watch(() => props.ruleInfo, (val) => internalRuleInfo.value = val);
watch(() => props.status, (val) => internalStatus.value = val);

// 获取数据 - 直接实现，不使用emit
const getData = () => {
    getRuleByUuid({
        uuids: props.uuid,
    }).then((res) => {
        internalRuleTitle.value = res.data.ruleName;
        internalPackageName.value = res.data.packageNameAll;
        internalType.value = res.data.type;
        internalUuid.value = res.data.uuid;
        internalEngUuid.value = res.data.engUuid;
        internalDemandUuid.value = res.data.folderUuid;
        innerCollect.value = res.data.ifCollect;
        internalRuleInfo.value = res.data;
        internalStatus.value = res.data.status;
        // 异步记录访问历史
        recordAccess({ ruleUuid: props.uuid }).catch(console.error);
    });
};

// 页面加载时获取数据
onMounted(() => {
    // 初始化检查规则位置 - 立即执行一次
    checkRulePosition();

    // 数据加载完成后再次检查位置，确保按钮状态正确 - 300ms 延迟
    nextTick(() => {
        setTimeout(() => {
            checkRulePosition();
        }, 300);
    });

    // 再次检查位置 - 600ms 延迟，确保DOM完全加载
    setTimeout(() => {
        checkRulePosition();
    }, 600);

    // 最终检查位置 - 1000ms 延迟，确保所有数据已加载
    setTimeout(() => {
        checkRulePosition();
        // 调试按钮状态
        debugChecking();
    }, 1000);

    // 监听规则导航完成事件
    globalEventEmitter.on(RULE_NAVIGATION_COMPLETE, (data: any) => {
        if (data) {
            if (data.ruleUuid === props.uuid) {
                // 如果当前规则是导航目标，更新位置信息
                isFirstRule.value = data.isFirst;
                isLastRule.value = data.isLast;
            } else {
                // 否则重新检查当前规则位置
                checkRulePosition();
            }
        }
    });

    // 监听规则更新事件
    globalEventEmitter.on(RULE_UPDATED, (data: any) => {
        if (data && data.uuid === internalUuid.value) {
            // 更新规则名称
            internalRuleTitle.value = data.ruleName;
            // 如果有其他需要更新的字段，可以在这里添加
        }
    });
});
// 组件卸载时移除事件监听
onUnmounted(() => {
    globalEventEmitter.off(RULE_NAVIGATION_COMPLETE);
    globalEventEmitter.off(RULE_UPDATED);
});
// 检查当前规则在列表中的位置
const checkRulePosition = () => {
    // 尝试获取RuleBaseContent组件的方法
    try {
        // 使用更稳定的方式获取父组件
        const ruleBaseContentElement = document.querySelector('#rule-base-content-component');

        if (ruleBaseContentElement) {
            // 尝试从DOM元素获取绑定的实例
            if ((ruleBaseContentElement as any).__ruleBaseInstance &&
                typeof (ruleBaseContentElement as any).__ruleBaseInstance.checkRulePosition === 'function') {

                const position = (ruleBaseContentElement as any).__ruleBaseInstance.checkRulePosition(props.uuid);
                isFirstRule.value = position.isFirst;
                isLastRule.value = position.isLast;
                return;
            }

            // 如果无法通过绑定实例获取，尝试Vue实例
            let ruleBaseContent = null;

            // 尝试多种可能的Vue 3实例访问方式 - 使用类型断言
            if ((ruleBaseContentElement as any).__vue__) {
                ruleBaseContent = (ruleBaseContentElement as any).__vue__;
            } else if ((ruleBaseContentElement as any).__vueParentComponent) {
                ruleBaseContent = (ruleBaseContentElement as any).__vueParentComponent.ctx;
            } else if ((ruleBaseContentElement as any)._vnode && (ruleBaseContentElement as any)._vnode.component) {
                ruleBaseContent = (ruleBaseContentElement as any)._vnode.component.ctx;
            }

            if (ruleBaseContent && typeof ruleBaseContent.checkRulePosition === 'function') {
                const position = ruleBaseContent.checkRulePosition(props.uuid);
                isFirstRule.value = position.isFirst;
                isLastRule.value = position.isLast;
                return;
            } else {
            }
        }

        // 如果无法通过组件实例获取，则使用DOM查询方法
        useBackupPositionCheck();

    } catch (error) {
        useBackupPositionCheck();
    }
};

// 使用备用方法检查规则位置
const useBackupPositionCheck = () => {
    try {
        // 在整个文档中查找所有规则项
        const allRuleItems = document.querySelectorAll('.rule-item');
        if (allRuleItems && allRuleItems.length > 0) {
            // 查找当前规则在列表中的位置
            let currentIndex = -1;
            for (let i = 0; i < allRuleItems.length; i++) {
                if (allRuleItems[i].getAttribute('data-uuid') === props.uuid) {
                    currentIndex = i;
                    break;
                }
            }

            if (currentIndex !== -1) {
                isFirstRule.value = currentIndex === 0;
                isLastRule.value = currentIndex === allRuleItems.length - 1;
            }
        }
    } catch (error) {
    }
};

// 用于调试按钮状态的函数
const debugChecking = () => {
    // 检查DOM中按钮的状态
    const prevButton = document.querySelector('.nav-buttons .action-button:first-child');
    const nextButton = document.querySelector('.nav-buttons .action-button:last-child');
};

// 前往上一个规则
const goPrevRule = () => {
    debugChecking(); // 调试当前按钮状态

    if (isFirstRule.value) {
        return; // 如果是第一个规则，不执行操作
    }

    // 发送导航请求到RuleBaseContent组件
    globalEventEmitter.emit(RULE_NAVIGATE_ACTION, {
        uuid: props.uuid,
        direction: 'prev'
    });
}

// 前往下一个规则
const goNextRule = () => {
    debugChecking(); // 调试当前按钮状态

    if (isLastRule.value) {
        return; // 如果是最后一个规则，不执行操作
    }

    // 发送导航请求到RuleBaseContent组件
    globalEventEmitter.emit(RULE_NAVIGATE_ACTION, {
        uuid: props.uuid,
        direction: 'next'
    });
}
// 在新窗口打开
const openInNewWindow = () => {
    // 使用提取的函数获取URL
    const { basePath, lastUrl } = generateRuleUrl();

    // 在新窗口中打开，使用_blank确保每次都在新窗口打开
    window.open(basePath + lastUrl, '_blank');
}
// ==== 弹窗相关逻辑 ====
const isModalVisible = ref(false);
const ruleUpdateComponent = ref();
const modalType = ref();

// 提交数据前状态判断
function checkSubmitStatus(ruleInfo: any, type: string) {
    let uuids = '';
    //状态判断
    let status = '';
    if (ruleInfo.uuid) {
        uuids = ruleInfo.uuid;
        if (ruleInfo.status === '已删除') {
            status = ruleInfo.status;
        }
        //锁定/解锁数据不可再次提交
        if ((ruleInfo.lockId && type === '已锁定') || (!ruleInfo.lockId && type === '已解锁')) {
            status = type;
        }
    }
    return [uuids, status];
}

// // 提交规则
// function handleSubmitRule() {
//     if (props.symbol) {
//         // 发送自定义事件，传递当前规则信息
//         globalEventEmitter.emit(RULE_SUBMIT_ACTION, {
//             record: {
//                 uuid: props.uuid,
//                 status: props.status,
//                 ruleName: props.ruleTitle
//             },
//             tabKey: '编辑' + props.uuid // 添加标签页标识，用于在删除后关闭标签页
//         });
//         getData();
//     }
//     else {
//         let [uuids, status] = checkSubmitStatus(internalRuleInfo.value, '');
//         if (status) {
//             message.error('状态为' + status + '规则不可提交');
//             return;
//         }
//         if (uuids) {
//             let pars = qs.stringify({
//                 uuids: uuids,
//             })
//             submitRule(pars).then((res: any) => {
//                 if (res.code === 20000) {
//                     if (res.data == '规则置为提交待审核成功！') {
//                         message.success('规则置为提交待审核成功，不允许操作，即将关闭页面！');
//                         // 添加2秒延时，让用户有时间看到提示信息
//                         setTimeout(() => {
//                             goClose();
//                         }, 2000);
//                     } else {
//                         message.success(res.data);
//                     }
//                 } else {
//                     message.error(res.data);
//                 }
//             });
//         }
//     }
// };

//撤回
function handleWithdraw() {
    if (props.symbol) {
        // 发送自定义事件，传递当前规则uuid
        globalEventEmitter.emit(RULE_WITHDRAW_ACTION, {
            record: { uuid: props.uuid, status: props.status }
        });
        getData();
    } else {
        let [uuids, status] = checkSubmitStatus(internalRuleInfo.value, '');
        if (status) {
            message.error('状态为' + status + '规则不可撤回');
            return;
        }
        if (uuids) {
            ruleCall({
                uuids: uuids
            }).then((res: any) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    getData();
                } else {
                    message.error(res.data);
                }
            });
        }
    }

};

// 更新属性
const handleUpdateRule = () => {
    if (props.symbol) {
        // 发送自定义事件，传递当前规则信息
        globalEventEmitter.emit(RULE_UPDATE_ACTION, {
            record: {
                uuid: props.uuid,
                demandUuid: props.demandUuid,
            }
        });
        getData();
    } else {
        showModal('update', internalRuleInfo.value);
    }
};

const showModal = (type: string, record: any = {}) => {
    modalType.value = type;
    if (type === 'update') {
        isLock(
            qs.stringify({
                uuids: record.uuid,
                demandUuid: internalDemandUuid.value,
            })
        ).then((res: any) => {
            if (res.code === 20000) {
                if (ruleUpdateComponent.value) {
                    ruleUpdateComponent.value.getRuleInfo(record.uuid);
                }
                isModalVisible.value = true;
            } else {
                message.error(res.data);
            }
        });
    }
};

const handleSubmit = () => {
    let submitFun;
    switch (modalType.value) {
        case 'update':
            submitFun = ruleUpdateComponent.value.handleSubmit;
            break;
    }
    submitFun && submitFun(() => {
        isModalVisible.value = false;
        // 刷新页面数据
        getData();
        refresh();
    });
};

const handleCancel = () => {
    isModalVisible.value = false;
};

//删除
function handleDeleteRule() {
    if (props.symbol) {
        // 发送自定义事件，传递当前规则信息
        globalEventEmitter.emit(RULE_DELETE_ACTION, {
            record: {
                uuid: props.uuid,
                ruleName: props.ruleTitle,
            },
            tabKey: '编辑' + props.uuid // 添加标签页标识，用于在删除后关闭标签页
        });
        getData();
    } else {
        const record = internalRuleInfo.value;
        let uuids = '';
        let ruleNames = '';
        //判断数据是否存在
        let delFlag = false;
        if (record.uuid) {
            delFlag = true;
            uuids = record.uuid;
            ruleNames = record.ruleName;
        }
        if (delFlag) {
            modal.confirm({
                title: '确定要删除【' + ruleNames + '】吗？',
                icon: createVNode(ExclamationCircleOutlined),
                onOk() {
                    ruleDelete(
                        qs.stringify({
                            uuids: uuids,
                            demandUuid: internalDemandUuid.value,
                        })
                    ).then((res: any) => {
                        if (res.code === 20000) {
                            message.success('删除成功');
                            goClose();
                            refresh();
                        } else {
                            message.error(res.data);
                        }
                    });
                },
                onCancel() { }
            });
        }
    }
}

// 锁定
function handleLockRule() {
    if (props.symbol) {
        // 发送自定义事件，传递当前规则信息
        globalEventEmitter.emit(RULE_LOCK_ACTION, {
            record: internalRuleInfo.value
        });
    } else {
        const record = internalRuleInfo.value;
        let [uuids, status] = checkSubmitStatus(record, '已锁定');
        if (status) {
            message.error('状态为' + status + '规则不可锁定');
            return;
        }
        if (uuids) {
            toLock(
                qs.stringify({
                    uuids: uuids,
                })
            ).then((res: any) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    getData();
                } else {
                    message.error(res.data);
                }
            });
        }
    }
};

// 解锁
function handleUnlockRule() {
    if (props.symbol) {
        // 发送解锁规则事件到RuleBaseContent组件
        globalEventEmitter.emit(RULE_UNLOCK_ACTION, {
            record: internalRuleInfo.value
        });
        getData();
    } else {
        const record = internalRuleInfo.value;
        let [uuids, status] = checkSubmitStatus(record, '已解锁');
        if (status) {
            message.error('状态为' + status + '规则不可解锁');
            return;
        }
        if (uuids) {
            unLock(
                qs.stringify({
                    uuids: uuids,
                })
            ).then((res: any) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    getData();
                } else {
                    message.error(res.data);
                }
            });
        }
    }
};

// 历史抽屉逻辑
const ruleHistoryDrawerVisible = ref(false);
const datar = ref();

// 历史
function handleHistoryRule() {
    if (props.symbol) {
        // 发送自定义事件，传递当前规则信息
        globalEventEmitter.emit(RULE_HISTORY_ACTION, {
            record: {
                uuid: props.uuid,
            }
        });
    } else {
        const record = internalRuleInfo.value;
        showGlobalLoading('加载中，请耐心等待');
        getRuleHistoryList({
            ruleUuid: record.uuid,
            page: 1,
            several: 10000,
        }).then((res: any) => {
            if (res.code == 20000) {
                datar.value = res.data;
                hideGlobalLoading();
                ruleHistoryDrawerVisible.value = true;
            } else {
                message.error(res.data);
            }
        });
        hideGlobalLoading();
    }
}

const closeDrawer = () => {
    ruleHistoryDrawerVisible.value = false;
}

// 刷新方法 - 直接实现，不使用emit
const refresh = () => {
    // 通过 localStorage 发送跨窗口通知
    localStorage.setItem('rule_updated', JSON.stringify({
        timestamp: new Date().getTime(),
        type: 'refresh'
    }));
};
</script>

<template>
    <!-- 顶部栏 -->
    <div class="rule-top-bar">
        <span class="rule-path">
            <IconRuleTypeDecisionTable v-if="internalType === '2'" :size="16" style="margin-right: 5px;" />
            <IconRuleTypeOrdinaryRule v-else-if="internalType === '1'" :size="16" style="margin-right: 5px;" />
            <IconRuleTypeRuleFlow v-else-if="internalType === '5'" :size="16" style="margin-right: 5px;" />
            <IconRuleTypeDecisionTree v-else-if="internalType === '4'" :size="16" style="margin-right: 5px;" />
            规则路径：
            <RulePath :path="internalPackageName" :ruleName="internalRuleTitle" :pathLength="0" showCopyButton />
        </span>
        <div class="actions-container">
            <!-- 导航按钮组 -->
            <div class="nav-buttons" v-show="props.symbol">
                <a-tooltip title="上一个">
                    <div class="action-button" @click="goPrevRule" :class="{ 'button-disabled': isFirstRule }">
                        <IconNavArrow direction="up" />
                    </div>
                </a-tooltip>
                <a-tooltip title="下一个">
                    <div class="action-button" @click="goNextRule" :class="{ 'button-disabled': isLastRule }">
                        <IconNavArrow direction="down" />
                    </div>
                </a-tooltip>
            </div>
            <!-- 其他操作按钮 -->
            <div class="other-buttons">
                <a-tooltip title="复制链接">
                    <div class="action-button" @click="copyRuleLink">
                        <IconCopyLink />
                    </div>
                </a-tooltip>

                <a-tooltip :title="innerCollect ? '取消收藏' : '收藏'">
                    <div class="action-button" @click="handleCollect">
                        <IconStar v-if="innerCollect" :size="20" />
                        <IconStarOutlined v-else :size="20" />
                    </div>
                </a-tooltip>

                <a-tooltip title="在新窗口打开">
                    <div class="action-button" @click="openInNewWindow" v-show="props.symbol">
                        <IconNewWindow />
                    </div>
                </a-tooltip>

                <a-tooltip title="更多操作">
                    <a-dropdown :trigger="['click']" placement="bottomLeft">
                        <template #overlay>
                            <a-menu style="width: 250px;padding: 10px">
                                <span style="font-size: 18px;margin-left: 20px;font-weight:800">更多功能</span>
<!--                                <a-menu-item key="1" @click="handleSubmitRule"-->
<!--                                    v-if="checkPermi([RULE_PERMISSION.RULE.SUBMIT])">-->
<!--                                    <span style="margin-left: 10px">提交规则</span>-->
<!--                                </a-menu-item>-->
<!--                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.SUBMIT])"></a-menu-divider>-->
                                <!--                                <a-menu-item  key="2" @click="handleWithdraw"-->
                                <!--                                             v-if="checkPermi([RULE_PERMISSION.RULE.WITHDRAW])">-->
                                <!--                                    <span style="margin-left: 10px">撤回提交</span>-->
                                <!--                                </a-menu-item>-->
                                <!--                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.WITHDRAW])"></a-menu-divider>-->
                                <a-menu-item key="3" @click="handleUpdateRule"
                                    v-if="checkPermi([RULE_PERMISSION.RULE.EDIT])">
                                    <span style="margin-left: 10px">更新属性</span>
                                </a-menu-item>
                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.EDIT])"></a-menu-divider>
                                <a-menu-item key="4" @click="handleDeleteRule"
                                    v-if="checkPermi([RULE_PERMISSION.RULE.DELETE])">
                                    <span style="margin-left: 10px">删除规则</span>
                                </a-menu-item>
                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.DELETE])"></a-menu-divider>
                                <a-menu-item key="5" @click="handleLockRule"
                                    v-if="checkPermi([RULE_PERMISSION.RULE.LOCK])">
                                    <span style="margin-left: 10px">锁定规则</span>
                                </a-menu-item>
                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.LOCK])"></a-menu-divider>
                                <a-menu-item key="6" @click="handleUnlockRule"
                                    v-if="checkPermi([RULE_PERMISSION.RULE.UNLOCK])">
                                    <span style="margin-left: 10px">解锁规则</span>
                                </a-menu-item>
                                <a-menu-divider v-if="checkPermi([RULE_PERMISSION.RULE.UNLOCK])"></a-menu-divider>
                                <a-menu-item key="7" @click="handleHistoryRule">
                                    <span style="margin-left: 10px">历史版本</span>
                                </a-menu-item>

                                <!-- 规则信息区域 -->
                                <div class="rule-info-area">
                                    <div class="rule-info-item"><label>规则状态：</label>{{
                                        formatValue(internalRuleInfo?.status, "status") }}</div>
                                    <div class="rule-info-item"><label>加锁人：</label>{{ internalRuleInfo?.lockId}}
                                    </div>
                                    <div class="rule-info-item"><label>规则编号：</label>{{ internalRuleInfo?.ruleNumber }}
                                    </div>
                                    <div class="rule-info-item"><label>创建人：</label>{{ internalRuleInfo?.createdId }}
                                    </div>
                                    <div class="rule-info-item"><label>修改时间：</label>{{
                                        internalRuleInfo?.lastModifiedTimeStr }}</div>
                                    <div class="rule-info-item"><label>规则描述：</label>{{ internalRuleInfo?.descs}}
                                    </div>
                                </div>
                            </a-menu>
                        </template>
                        <a-button type="text" class="action-button">
                            <IconMoreHorizontal :size="20" :enableHover="true" @click="getData" />
                        </a-button>
                    </a-dropdown>
                </a-tooltip>
                <a-tooltip title="关闭">
                    <a-button shape="circle" size="small" @click="goClose()" v-if="props.symbol == false"><template
                            #icon>
                            <CloseOutlined />
                        </template></a-button>
                </a-tooltip>
            </div>
        </div>
    </div>

    <!-- 添加模态框 -->
    <a-modal title="更新属性" :visible="isModalVisible" @ok="handleSubmit" @cancel="handleCancel" :destroyOnClose="true">
        <div style="max-height: 60vh; overflow-y: auto;">
            <RuleUpdate ref="ruleUpdateComponent" v-if="modalType === 'update'" :ruleId="internalRuleInfo?.uuid"
                :ruleBaseId="internalEngUuid" :rulePackageId="internalDemandUuid" />
        </div>
    </a-modal>

    <!-- 规则历史抽屉 -->
    <FlexDrawer :visible="ruleHistoryDrawerVisible" @close="closeDrawer" title="规则历史" :width="1200">
        <RuleHistory v-if="ruleHistoryDrawerVisible" :ruleId="internalRuleInfo?.uuid" :datar="datar" />
    </FlexDrawer>
</template>

<style scoped>
.rule-path-container {
    position: relative;
}

.rule-top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #ffffff;
}

.rule-path {
    font-size: 14px;
    color: rgb(88, 90, 90);
}

.actions-container {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.nav-buttons {
    display: flex;
    flex-direction: row;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-right: 15px;
    overflow: hidden;
}

.nav-buttons .action-button {
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.nav-buttons .action-button:first-child {
    border-right: 1px solid #e8e8e8;
}

.other-buttons {
    display: flex;
    align-items: center;
}

.action-button {
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
}

.button-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.rule-content {
    padding: 15px;
}

.rule-info-area {
    padding: 10px 15px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f9f9f9;
}

.rule-info-item {
    font-size: 13px;
    color: #333;
    line-height: 1.6;
    display: flex;
}

.rule-info-item label {
    text-align: right;
    min-width: 80px;
    margin-right: 2px;
}
</style>
