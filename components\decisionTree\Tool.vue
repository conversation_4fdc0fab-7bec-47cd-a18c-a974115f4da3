<template>
  <div class="tool">
    <a-tooltip placement="bottom" title="全屏编辑">
      <a-button
        size="small"
        @click="emit('fullScreen')"
        v-if="showFullScreen"
      >
        <template #icon>
          <FullscreenOutlined />
        </template>
      </a-button>
    </a-tooltip>
    <a-button-group key="scale-control">
      <a-tooltip effect="light">
        <template #title>缩小视图</template>
        <a-button
          size="small"
          :disabled="p_defaultZoom < 0.2"
          @click="emit('processZoomOut')"
        >
          <template #icon>
            <ZoomOutOutlined />
          </template>
        </a-button>
      </a-tooltip>
      <a-button>{{
        Math.floor(p_defaultZoom * 10 * 10)
          ? Math.floor(p_defaultZoom * 10 * 10) + "%"
          : "100%"
      }}</a-button>
      <a-tooltip effect="light">
        <template #title>放大视图</template>
        <a-button
          size="small"
          :disabled="p_defaultZoom > 4"
          @click="emit('processZoomIn')"
        >
          <template #icon>
            <ZoomInOutlined />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip effect="light">
        <template #title>重置视图并居中</template>
        <a-button size="small" @click="emit('processReZoom')">
          <template #icon>
            <OneToOneOutlined />
          </template>
        </a-button>
      </a-tooltip>
    </a-button-group>
    <a-button-group key="stack-control">
      <a-tooltip effect="light">
        <template #title>撤销</template>
        <a-button
          size="small"
          :disabled="!p_revocable"
          @click="emit('processUndo')"
        >
          <template #icon>
            <RedoOutlined />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip effect="light">
        <template #title>恢复</template>
        <a-button
          size="small"
          :disabled="!p_recoverable"
          @click="emit('processRedo')"
        >
          <template #icon>
            <UndoOutlined />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip effect="light">
        <template #title>重新绘制</template>
        <a-button size="small" @click="emit('processRestart')">
          <template #icon>
            <SyncOutlined />
          </template>
        </a-button>
      </a-tooltip>
    </a-button-group>
    <a-button-group key="file-control">
      <a-button type="primary" @click="emit('toSaveFlow')"
        >保存</a-button
      >
    </a-button-group>
    <a-button
            v-if="checkPermi([RULE_PERMISSION.RULE.SUBMIT])"
            type="primary"
            @click="emit('handleSubmitRule')"
    >提交</a-button>
    <!-- 用于打开本地文件-->
    <input
      type="file"
      id="files"
      ref="refFile"
      style="display: none"
      accept=".xml, .bpmn"
      @change="emit('importLocalFile', refFile)"
    />
  </div>
</template>

<script setup>
  import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
  import { checkPermi } from "@/directive/permission/permission";
// 定义props
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
  defaultZoom: {
    type: Number,
    default: 1,
  },
  recoverable: {
    type: Boolean,
    default: false,
  },
  revocable: {
    type: Boolean,
    default: false,
  },
  showFullScreen: {
    type: Boolean,
    default: true
  }
});

// 定义emits
const emit = defineEmits([
  "downloadProcessAsXml",
  "downloadProcessAsSvg",
  "downloadProcessAsBpmn",
  "toSaveFlow",
  "processZoomOut",
  "processZoomIn",
  "processReZoom",
  "processUndo",
  "processRedo",
  "processRestart",
  "importLocalFile",
  "elementsAlign",
  "fullScreen",
  "handleSubmitRule"
]);

// 定义data
const p_recoverable = ref(false);
const p_revocable = ref(false);
const p_defaultZoom = ref(1);
const refFile = ref(null);

// 定义watch
watch(
  () => props.defaultZoom,
  (newVal) => {
    p_defaultZoom.value = newVal;
  }
);

watch(
  () => props.recoverable,
  (newVal) => {
    p_recoverable.value = newVal;
  }
);

watch(
  () => props.revocable,
  (newVal) => {
    p_revocable.value = newVal;
  }
);

// 定义methods
const alignCommand = (command) => {
  emit("elementsAlign", command);
};
</script>

<style lang="scss" scoped>
::v-deep .tool {
  background-color: #fff;
  position: absolute;
  width: 100%;
  height: 36px;
  z-index: 1;
  line-height: 36px;
  top: 0;
  left: 0;
}

/* 按钮放大20% */
:deep(.ant-btn.ant-btn-sm) {
  width:32px;
  height:32px;
  padding: 2.4px 0;
  margin:0 !important;
}


</style>
