<!--政策新增页-->

<script setup lang="ts">
    import { ref, reactive, onMounted } from 'vue';
    import { useRouter } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    definePageMeta({
        title: '政策新增'
    })
    const router = useRouter();

    const ruleForm = reactive({
        ruleName: "",
        demandApplyQO: "",
        bizKey: "",
        appNo: "",
        ruleCode: "",
    });

    const ruleArrsel = ref([]);

    const rules = {
        ruleCode: [{ required: true, message: "请选择", trigger: "change" }],
    };

    const getName = (value: string) => {
        ruleArrsel.value.map((i) => {
            if (i.code === value) {
                ruleForm.ruleName = i.name;
            }
        });
    };

    const getRuleInfosel = async () => {
        const res = await getBaseLine();
        ruleArrsel.value = res.data;
    };

    onMounted(() => {
        getRuleInfosel();
    });
    const ruleFormRef = ref();
    const submitForm = () => {
        ruleFormRef.value.validate().then((valid: boolean) => {
            if (valid) {
                showModal('addPolicy')
            } else {
                return false;
            }
        });
    };

    const back = () => {
        router.go(-1);
    };
    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'addPolicy') {
            datar.value = {
                type: "addPolicy",
                roleName: ruleForm.ruleName,
                demandApplyQO: ruleForm.demandApplyQO,
                appNo: ruleForm.appNo,
                bizKey: ruleForm.bizKey,
                businessCode: ruleForm.ruleCode,
            };
        }
        isModalVisible.value = true;
    };
    const close = (flag) => {
        isModalVisible.value = false;
    }
</script>
<template>
    <NotListPageLayout>
        <template #title>
            <!-- 页面标题 -->
            政策新增
        </template>

        <template #search>
            <a-form
                    label-width="100px"
                    ref="ruleFormRef"
                    :model="ruleForm"
                    :rules="rules"
                    layout="inline"
            >
                <a-form-item label="业务条线" prop="ruleCode" style="width: 300px">
                    <a-select
                            v-model:value="ruleForm.ruleCode"
                            placeholder="请选择"
                            @change="getName"
                            :filterOption="filterOption" showSearch
                    >
                        <a-select-option
                                v-for="item in ruleArrsel"
                                :key="item.code"
                                :value="item.code"
                                :name="item.name"
                        >
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
            <div style="margin-top: 15px">
                <a-button type="primary" @click="submitForm" style="margin-right: 8px">下一步</a-button>
                <a-button @click="back">返回</a-button>
            </div>
            <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" v-if="isModalVisible"/>
        </template>
    </NotListPageLayout>
</template>
