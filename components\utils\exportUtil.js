/**
 * 导出相关工具函数
 */

/**
 * 处理导出文件下载
 * @param {Object} response 导出接口响应
 * @param {string} defaultFilename 默认文件名
 * @returns {boolean} 是否成功
 */
export function handleDownload(response, defaultFilename = 'export.xlsx') {
  try {
    const headers = response.headers
    const blob = new Blob([response.data], {
      type: headers['content-type'] || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    const link = document.createElement('a')
    const url = window.URL.createObjectURL(blob)
    
    // 从响应头获取文件名
    const contentDisposition = headers['content-disposition']
    let fileName = defaultFilename
    
    if (contentDisposition) {
      try {
        const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (matches != null && matches[1]) {
          fileName = decodeURI(matches[1].replace(/['"]/g, ''))
        }
      } catch (e) {
        // 如果解析文件名失败，使用默认文件名
        console.warn('解析文件名失败，使用默认文件名:', e)
      }
    }
    
    link.href = url
    link.download = fileName
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('下载失败:', error)
    return false
  }
}

// 导出别名以支持新命名
export const handleExportDownload = handleDownload


/**
 * 构建导出参数
 * @param {Object} config 导出配置
 * @returns {Object} 导出参数
 */
export function buildExportParams(config) {
  const {
    exportModule,
    exportType,
    fields,
    selectedIds = [],
    moduleParams = {},
    searchParams = {},
    pattern = 'Excel'
  } = config
  
  const params = {
    exportModule,
    exportType,
    fields: fields.map(field => ({
      fieldKey: field.key,
      fieldLabel: field.label
    })),
    pattern,
    ...moduleParams,
    ...searchParams
  }
  
  // 添加选中记录ID（当导出类型为selected时）
  if (exportType === 'selected' && selectedIds.length > 0) {
    params.selectedIds = selectedIds
  }
  
  return params
}

/**
 * 验证导出参数
 * @param {Object} config 导出配置
 * @returns {Object} 验证结果 {valid: boolean, message: string}
 */
export function validateExportParams(config) {
  const { exportType, fields, selectedIds = [] } = config
  
  if (!fields || fields.length === 0) {
    return { valid: false, message: '请至少选择一个导出字段' }
  }
  
  if (exportType === 'selected' && selectedIds.length === 0) {
    return { valid: false, message: '请先选择要导出的记录' }
  }
  
  return { valid: true, message: '' }
} 