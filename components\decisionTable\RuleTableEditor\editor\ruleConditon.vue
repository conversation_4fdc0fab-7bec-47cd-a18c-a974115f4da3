<template>
  <div class="designView" style="font-weight: 500">
    <div class="ruleBody" style="padding-left: 0; padding-top: 10px">
      <!-- 使用 ConditionListTable 组件，并传递相应的 props 和事件 -->
      <ConditionListTable
        has-title="true"
        pos="r"
        :condition-data="conditionData"
        :valid-list="validList"
        :locked="locked"
        @condition-change="onConditionChange"
        @add-rule="addRule"
        @add-child-condition="addChildCondition"
        @add-tail-item="addTailItem"
        @decrease-rule="decreaseRule"
        @switcher-change="onSwitcherChange"
        @logic-btn-click="onLogicBtnClick"
        :is-table="isTable"
        :no-rule-cell-unit="true"
      />
    </div>
  </div>
</template>

<script setup>
// 导入所需的工具函数和组件
import * as util from "@/components/ruleEditCom/utils/util";
import ConditionListTable from "./conditionList.vue";
const message = inject("message");
// 解构出所需的工具函数
const { ConditionGenerate } = util;

// 初始化条件数据
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry"
  },
  leftValueType: "String"
};

// 初始化规则数据
const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#"
};

// 用于生成唯一ID的引用对象
const idRef = {
  current: 1000
};

// 定义组件的 props
const props = defineProps({
  conditions: {
    type: Object,
    default: () => ({})
  },
  validList: {
    type: Array,
    default: () => []
  },
  locked: {
    type: Boolean,
    default: false
  },
  isTable: {
    type: Boolean,
    default: false
  }
});


// 使用 ref 定义 conditionData 状态
const conditionData = ref(props.conditions);

// 监听 conditions 的变化，更新 conditionData
watch(
  () => props.conditions,
  (newConditions) => {
    conditionData.value = newConditions;
  },
  { immediate: true, deep: true }
);

// 定义方法
const onConditionChange = (pos, newContents) => {
  const [, conditionId] = pos.split("_");
  const { targetNode } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  targetNode.ruleCondition.contents = new ConditionGenerate(newContents);
  // 触发 onchange 事件，传递更新后的条件数据
  emit("onchange", conditionData.value);
};

const addRule = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 1);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData)
    }
  });
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

const addChildCondition = (pos, conditionId, layer) => {
  const { parentNode, targetNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  util.updateLayers(layer, conditionData.value, 2);
  arr.splice(arrIndex + 1, 0, {
    indent: targetNode.indent,
    logicalSymbol: "and",
    fold: false,
    children: [
      {
        indent: targetNode.indent + 1,
        ruleCondition: {
          layer: layer + 1,
          showLayer: layer + 1,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData)
        }
      },
      {
        indent: targetNode.indent + 1,
        logicalSymbol: "and",
        ruleCondition: {
          layer: layer + 2,
          showLayer: layer + 2,
          conditionId: idRef.current++,
          contents: new ConditionGenerate(initConditionData)
        }
      }
    ]
  });
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

const addTailItem = (pos, conditionId) => {
  const { parentNode, targetNode } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  const lastCondition = util.getLastNode(parentNode);
  const { layer } = lastCondition;
  util.updateLayers(layer, conditionData.value, 1);

  arr.push({
    indent: targetNode.indent,
    logicalSymbol: "and",
    ruleCondition: {
      layer: layer + 1,
      showLayer: layer + 1,
      conditionId: idRef.current++,
      contents: new ConditionGenerate(initConditionData)
    }
  });
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

const decreaseRule = ({ pos, conditionId, layer }) => {
  const { parentNode, arrIndex } = util.findTargetNodeInfoById(
    conditionData.value,
    conditionId
  );
  const arr = parentNode.children;
  if (arr.length === 1) {
    // 提示用户至少保留一条数据
    message.info("请至少保留一条数据！");
    return;
  }
  if (arr.length > 2) {
    arr.splice(arrIndex, 1);
  } else if (arr.length === 2) {
    arr.splice(arrIndex, 1);
    const _logicalSymbol = parentNode.logicalSymbol;
    // 如果数组中只有两组数据，删除后，子节点逻辑组合，将转化为一个普通的子节点；
    if (parentNode !== conditionData.value || !!arr[0].children) {
      if (arr[0].ruleCondition) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
        delete parentNode.children;
      } else if (arr[0].children) {
        for (const i in arr[0]) {
          parentNode[i] = arr[0][i];
        }
      }
      parentNode.logicalSymbol = _logicalSymbol;
      util.updateIndent(parentNode, -1);
    }
  }
  if (arr && arrIndex / 1 === 0) {
    delete arr[0].logicalSymbol;
  }
  util.updateLayers(layer, conditionData.value, -1);
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

const onSwitcherChange = (pos, conditionId, layer) => {
  util.updateNodeFold(conditionData.value, conditionId, layer);
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

const onLogicBtnClick = (pos) => {
  const [, targetIndent, targetLayer] = pos.split("_");
  const logicNode = util.findLogicNode(
    conditionData.value,
    targetIndent,
    targetLayer
  );
  logicNode.logicalSymbol =
    logicNode.logicalSymbol === "and"
      ? "or"
      : logicNode.logicalSymbol === "or"
      ? "and"
      : null;
  emit("onchange", util.getConditionDataToBE(conditionData.value, {}, props.ruleUuid));
};

// 定义 emit 事件
const emit = defineEmits([
  "onchange",
  "onConditionChange",
  "onAddRule",
  "onAddChildCondition",
  "onAddTailItem",
  "onDecreaseRule",
  "onSwitcherChange",
  "onLogicBtnClick"
]);
</script>

<style scoped>
/* 添加样式 */
.designView {
  font-weight: 500;
}

.ruleBody {
  padding-left: 0;
  padding-top: 10px;
}
</style>
