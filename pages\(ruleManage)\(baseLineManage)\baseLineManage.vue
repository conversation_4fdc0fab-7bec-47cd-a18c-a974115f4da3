<!-- 基线管理页 -->
<script setup lang="ts">

import { getBaseLineInfo, batchExportData, batchPublishJudge,getBaseLineadd, getRuleSel, getInCompleteRuleForEng } from "@/api/baseline_Management";
import { getAllBusinessByLoginUser } from "@/api/rule_base";
import { batchExportNum } from "@/api/parameter";
import { pubEnvUUid } from '@/api/pub_environment';
import { getLoginType } from '@/api/userManagement';
import type { FormInstance, UploadProps, TableProps } from 'ant-design-vue';
import { getBaseLine,getPubEnv, subImport,getInCompleteRuleForBatchEng, batchPublish, getSnapCompareList} from "@/api/baseline_Management";
import { useRouter } from 'vue-router';
import qs from 'qs';
import dayjs, {Dayjs} from "dayjs";
import BaseLineAdd from "@/businessComponents/baseLineManage/BaseLineAdd";
import BaseLineImport from "@/businessComponents/baseLineManage/BaseLineImport";
import useTableConfig from '@/composables/useTableConfig';
import BaseLineDetails from '@/businessComponents/baseLineManage/baseLineDetails.vue';
// 导入版本比对相关组件
import BaseLineCompareList from '@/businessComponents/baseLineManage/baseLineCompareList.vue';
// 导入基线发布组件
import BaseLineRelease from '@/businessComponents/baseLineManage/baseLineRelease.vue';
// 导入基线导出管理组件
import BaseLineExport from '@/businessComponents/baseLineManage/baseLineExport.vue';
// 导入基线复制组件
import BaseLineCopy from '@/businessComponents/baseLineManage/baseLineCopy.vue';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";


definePageMeta({
    title: '基线管理',
    path: '/baseLineManage',
    name: 'baseLineManage'
})


const message = inject('message')
const modal = inject('modal')
const router = useRouter();
interface Rule {
    engName: string;
    edition: string;
    createdId: string;
    lastPublishId: string;
    createdTimeStr: string;
    descs: string;
    publishStatus: number;
    uuid: string;
    engUuid: string;
    key: string;
}

// 定义不包含序号列和操作列的表格列
const tableColumns = reactive([
    {
        title: '规则库名称',
        dataIndex: 'engName',
        key: 'engName',
        align: 'left',
        width:250,
    },
    {
        title: '版本',
        dataIndex: 'edition',
        key: 'edition',
        align: 'center',
        width:100,
    },
    {
        title: '是否发布',
        dataIndex: 'publishStatus',
        key: 'publishStatus',
        align: 'center',
        width:80,
    },
    {
        title: '规则集版本号',
        dataIndex: 'ruleMusterEdition',
        key: 'ruleMusterEdition',
        align: 'center',
        width:100,
    },
    {
        title: '发布环境',
        dataIndex: 'environmentName',
        key: 'environmentName',
        align: 'center',
        width:100,
    },
    {
        title: '创建时间',
        dataIndex: 'createdTimeStr',
        key: 'createdTimeStr',
        align: 'center',
        width:250,
    },
    {
        title: '创建人',
        dataIndex: 'createdId',
        key: 'createdId',
        align: 'center',
        width:100,
        checked: false
    },
    {
        title: '发布人',
        dataIndex: 'lastPublishId',
        key: 'lastPublishId',
        align: 'center',
        width:100,
        checked: false
    },
    {
        title: '基线描述',
        dataIndex: 'descs',
        key: 'descs',
        align: 'left',
        width:200,
        ellipsis: true,
        checked: false
    },
]);

const form = ref({
    rule_Basename: '',
    new_baselines: '1',
    newest_baselines: '1',
    baseline_dec: '',
    fristTime: '',
    endTime: '',
    businessLine: '',
    publish_status: '',
});

const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
});

const loading = ref<boolean>(false);
const rules = ref<Rule[]>([]);
const currentPage = ref(1); // 当前页码

const businessLineOptions = ref<any[]>([]);

const batchExportUuids = ref<string[]>([]);
const batchEngUUids = ref<string[]>([]);
const batchSnapUuids = ref<string[]>([]);
const dialogFormVisible = ref<boolean>(false);
const anybatchExportNum = ref<any[]>();
const isExporting = ref(false); // 添加导出状态标记

// 标签页系统相关变量
const activeTab = ref('main');
const tabs = ref([
    {
        key: 'main',
        title: '基线管理',
        closable: false,
    }
]);
const ruleDetailData = ref({});

onMounted(() => {
    pagination.value.isLoading = true; // 初始设置加载状态
    // 设置默认筛选条件
    form.value.new_baselines = "1";
    form.value.newest_baselines = "1";

    getAllBusinessByLoginUser().then((res) => {
        businessLineOptions.value = res.data;
        // 在获取到业务条线数据后更新搜索配置
        updateBusinessLineOptions();
        // 获取完业务条线数据后执行查询
        // getbaseline(); // 页面加载时获取基线数据
    });
    batchExportNum().then((res) => {
        anybatchExportNum.value = res.data;
    });
    getLoginTypeInfo();
});

// 更新业务条线选项
const updateBusinessLineOptions = () => {
    // 找到业务条线搜索字段并更新options
    const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
    if (businessLineField && businessLineField.compConfig) {
        businessLineField.compConfig.options = businessLineOptions.value.map(item => ({
            name: item.name,
            value: item.code
        }));
    }
};

const queryList = () => {
    currentPage.value = 1;
    getbaseline();
};

const getbaseline = async (params: Record<string, any> = {}) => {
    //清空列表多选选中数据
    selectKeys.value = [];
    batchExportUuids.value = [];
    batchEngUUids.value = [];
    batchSnapUuids.value = [];
    try {
        // 设置参数
        const defaultParams = {
            chineseName: form.value.rule_Basename,
            businessLine: form.value.businessLine,
            desc: form.value.baseline_dec,
            hasRuleToPublish: form.value.new_baselines,
            showNewestSnapShoot: form.value.newest_baselines,
            publishStatus: form.value.publish_status,
            startDate: form.value.fristTime,
            endDate: form.value.endTime,
            page: params.current || pagination.value.current,
            several: params.pageSize || pagination.value.pageSize
        };

        const mergedParams = { ...defaultParams, ...params };

        const res = await getBaseLineInfo(mergedParams);

        if (res.data.data && Array.isArray(res.data.data)) {
            rules.value = res.data.data.map((item: any, index: number) => ({
                ...item,
                key: item.uuid || index,
            }));

            // 更新分页信息
            pagination.value.total = res.data.totalCount || 0;
            pagination.value.current = mergedParams.page;
            pagination.value.pageSize = mergedParams.several;
        } else {
            rules.value = [];
            pagination.value.total = 0;
        }

        return {
            data: rules.value,
            total: pagination.value.total,
            success: true
        };
    } catch (error) {
        console.error('获取列表失败:', error);
        return {
            data: [],
            total: 0,
            success: false
        };
    }
};

const startTimeChange = (val: string | Dayjs) => {
    form.value.fristTime = `${val} 00:00:00`;
};

const endTimeChange = (val: string | Dayjs) => {
    form.value.endTime = `${val} 23:59:59`;
};

//表格行选择
const selectedRowKeys = ref<(string | number)[]>([]);
const selectedRows = ref([]);
const selectKeys  = ref<string[]>([]);//刷新列表后清空
const rowSelection: TableProps['rowSelection'] = {
    selectedRowKeys:selectKeys,
    onChange: (selectedRowKeys: string[], selectedRows: Rule[]) => {
        selectKeys.value = selectedRowKeys;
        batchExportUuids.value = [];
        batchEngUUids.value = [];
        batchSnapUuids.value = [];
        selectedRows.forEach((item) => {
            batchExportUuids.value.push(item.uuid);
            batchEngUUids.value.push(item.engUuid);
            batchSnapUuids.value = [item.uuid];
        });
    },
    getCheckboxProps: (record: Rule) => ({
        disabled: record.name === 'Disabled User', // Column configuration not to be checked
        name: record.name,
    }),
};

//批量导出
interface ExportForm {
    format: string;
}
const exportForm = reactive({
    format: "",
});
const batchExport = () => {
    exportForm.format = "";
    const sum = batchExportUuids.value.length;
    if (sum === 0) {
        message.info("请勾选要操作的数据");
    } else if (sum > batchExportNum.value) {
        message.info(`批量导出的数据不能超过${batchExportNum.value}条`);
    } else {
        dialogFormVisible.value = true;
    }
};

const checkRadio = (val: string) => {
    if (isExporting.value) return; // 如果正在导出，则不执行

    const target = val.target as HTMLInputElement;
    const selectedValue = target.value;

    // 添加确认对话框
    modal.confirm({
        title: "提示",
        content: `您确定要导出选中的${batchExportUuids.value.length}条数据吗?`,
        okText: "确定",
        cancelText: "取消",
        onOk() {
            executeExport(selectedValue);
        },
        onCancel() {
            // 取消导出，重置选中的格式
            exportForm.format = "";
        },
    });
};

// 执行导出操作的函数
const executeExport = (format: string) => {
    isExporting.value = true; // 设置导出状态为true
    const data = {
        uuids: batchExportUuids.value.join(","),
        pattern: format,
    };
    const loadingMessage = message.loading('正在导出...', 0);
    batchExportData(data).then((res) => {
        dialogFormVisible.value = false;
        const headers = res.headers;
        const blob = new Blob([res.data], {
            type: headers["content-type"],
        });
        const link = document.createElement("a");
        const url = window.URL.createObjectURL(blob);

        const fileName = headers["content-disposition"]
            .split(";")[1]
            .split("=")[1];

        link.href = url;
        link.download = decodeURI(fileName);
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        loadingMessage();

        // 清除表格选中状态
        selectKeys.value = [];
        batchExportUuids.value = [];
        batchEngUUids.value = [];
        batchSnapUuids.value = [];

        // 提示导出成功
        message.success('导出成功');

        isExporting.value = false; // 重置导出状态
    }).catch(err => {
        console.error(err);
        loadingMessage();
        message.error('导出失败');
        isExporting.value = false; // 重置导出状态
    });
};

function getBatchExportNum() {
    throw new Error("Function not implemented.");
}

//批量发布
const visibleRelease = ref(false);
const formRefRelease = ref();

const ruleFormRelease = reactive({
    env: '',
    ruleSetdec: '',
});
const rulesRelease = {
    env: [{ required: true, message: '请选择发布环境', trigger: 'change' }],
    ruleSetdec: [
        { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' },
    ],
};
const envArrRelease = ref([]);
const strEnvRelease = ref('');
const loginTypeRelease = ref('');
const businessLineRelease = ref('');
const snapUUidsRelease = ref('');
const engUuidsRelease = ref('');
const batch_publish = () => {
    const sum = batchEngUUids.value.length;
    if (sum === 0) {
        message.info("请勾选要操作的数据");
    } else if (sum > 10) {
        message.info("批量发布的数据不能超过10条");
    } else {
        const engUUidSet = new Set(batchEngUUids.value); // 去重
        if (engUUidSet.size !== batchEngUUids.value.length) {
            message.info("批量发布不允许发布相同的规则库");
        } else {
            const data = {
                engUuids: batchEngUUids.value.join(","),
            };
            batchPublishJudge(data).then((res) => {
                if (res.data && res.data !== "") {
                    businessLineRelease.value = res.data
                    snapUUidsRelease.value = batchSnapUuids.value.join(",")
                    engUuidsRelease.value = batchEngUUids.value.join(",")
                    getEnvInfo();
                    visibleRelease.value = true
                } else {
                    message.info("请选择相同条线的基线进行发布");
                }
            });
        }
    }
};

const handleOk2 = () => {
    formRefRelease.value.validate().then(() => {
        const data = qs.stringify({
            environmentId: strEnvRelease.value,
            snapShootUuids: snapUUidsRelease.value,
        });
        if (loginTypeRelease.value === '04') {
            getInCompleteRuleForBatchEng({ engUuids: engUuidsRelease.value }).then((res) => {
                if (res && res.data !== '') {
                    modal.confirm({
                        title: '提示',
                        content: `规则库：<strong>${res.data}</strong>中存在未完成的规则，请确认是否发布？`,
                        okText: '发布',
                        cancelText: '取消',
                        dangerouslyUseHTMLString: true,
                        onOk() {
                            publish(data);
                            visibleRelease.value = false;
                        },
                        onCancel() {
                            message.info('已取消');
                        },
                    });
                } else {
                    modal.confirm({
                        title: '提示',
                        content: '您确定要发布吗?',
                        okText: '确定',
                        cancelText: '取消',
                        onOk() {
                            publish(data);
                            visibleRelease.value = false;
                        },
                        onCancel() {
                            message.info('已取消');
                        },
                    });
                }
            });
        } else {
            modal.confirm({
                title: '提示',
                content: '您确定要发布吗?',
                okText: '确定',
                cancelText: '取消',
                onOk() {
                    publish(data);
                    visibleRelease.value = false;
                },
                onCancel() {
                    message.info('已取消');
                },
            });
        }
    });
};

const handleCancel2 = () => {
    visibleRelease.value = false;
};

const getEnvInfo = async () => {
    try {
        const pars = {
            businessLine: businessLineRelease.value,
        };
        const res = await getPubEnv(pars);
        const choseSel = {
            environmentName: '请选择',
            id: '',
        };
        res.data.unshift(choseSel);
        envArrRelease.value = res.data;
        ruleFormRelease.env = res.data[1].environmentName;
        strEnvRelease.value = res.data[1].id;
        if (ruleFormRelease.env !== '' && res.data[0].environmentName === '请选择') {
            strEnvRelease.value = res.data[1].id;
        }
    } catch (error) {
        console.error('获取环境信息失败:', error);
    }
};

const selEnvRelease = (vid: string) => {
    const obj = envArrRelease.value.find((item) => item.id === vid);
    if (obj) {
        strEnvRelease.value = obj.id;
    }
};

const publish = (data: string) => {
    batchPublish(data).then((res) => {
        message.success({
            content: res.data,
            duration: 2,
        });
        // 清除表格选中状态
        selectKeys.value = [];
        batchExportUuids.value = [];
        batchEngUUids.value = [];
        batchSnapUuids.value = [];
    });
};

const getLoginTypeInfo = () => {
    getLoginType().then((res) => {
        loginTypeRelease.value = res.data;
    });
};
const handleCancel3 = ()=>{
    dialogFormVisible.value = false
}
const modalType = ref(''); // 对话框类型：'add' 或 'update'
const isModalVisible = ref(false); // 对话框显示状态

const showModal = (type: string, record = {}) => {
    modalType.value = type;
    if (type === 'update') {
        // 更新时的逻辑
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    if (modalType.value === 'add' && baseLineAddComponent.value) {
        baseLineAddComponent.value.resetForm();
    } else if (modalType.value === 'import' && baseLineImportComponent.value) {
        baseLineImportComponent.value.resetForm();
    }
    isModalVisible.value = false;
};

// 新增组件
const baseLineAddComponent = ref()
// 更新组件
const baseLineImportComponent = ref()

//确定按钮加载
const submitLoading = ref(false);
const changeSubmitLoading = (val: boolean) => {
    submitLoading.value = val;
}
// 处理modal ok 事件
function handleModalOk() {
    let submitFun
    switch (modalType.value) {
        case 'add':
            submitFun = baseLineAddComponent.value.submitForm;
            break;
        case 'import':
            submitFun = baseLineImportComponent.value.submitForm;
            break;
    }
    submitFun && submitFun(() => {
        isModalVisible.value = false;
    });
}
// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 批量操作事件
    batchEvent: [
        {
            key: 'batchExport',
            label: '批量导出',
            handler: batchExport,
            disabled: isExporting.value
        },
        {
            key: 'batchPublish',
            label: '批量发布',
            handler: batch_publish,
            permission: RULE_PERMISSION.SNAPSHOOT.PUBLISH
        }
    ],
    moreEvent: [
        {
            key: 'import',
            label: '导入',
            handler: () => {
                /* 处理逻辑 */
                showModal('import')
            }
        }
    ],
    //新建事件权限
    addPermission: RULE_PERMISSION.SNAPSHOOT.ADD,
    // 新建事件
    addNewEvent: () => {showModal('add')},
    // 新增：表单处理器配置
    formHandler: {
        // 日期字段特殊处理
        dateFields: {
            startField: 'fristTime',
            endField: 'endTime',
        },
        // 查询方法
        queryMethod: getbaseline
    }
};
// 搜索配置
const searchConfig = reactive({
    // 简单搜索字段
    simpleSearchField: {
        label: '规则库名称',
        field: 'chineseName'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '规则库名称',
            field: 'chineseName',
            compType: 'input',
            compConfig:{
                defaultValue: ''
            }
        },
        {
            label: '业务条线',
            field: 'businessLine',
            compType: 'select',
            compConfig: {
                options: [], // 初始为空数组，后续通过updateBusinessLineOptions方法更新
                showSearch: true,
                filterOption: (input: string, option: any) => {
                    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                },
                defaultValue: null
            }
        },
        {
            label: '可建基线',
            field: 'hasRuleToPublish',
            compType: 'select',
            compConfig: {
                options: [
                    { name: '是', value: '1' },
                    { name: '否', value: '0' }
                ],
                defaultValue: '1'
            }
        },
        {
            label: '最新版基线',
            field: 'showNewestSnapShoot',
            compType: 'select',
            compConfig: {
                options: [
                    { name: '是', value: '1' },
                    { name: '否', value: '0' }
                ],
                defaultValue: '1'
            }
        },
        {
            label: '是否发布',
            field: 'publish_status',
            compType: 'select',
            compConfig: {
                options: [
                    { name: '是', value: '1' },
                    { name: '否', value: '0' }
                ],
                defaultValue: null
            }
        },
        {
            label: '开始时间',
            field: 'fristTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false,
                valueFormat: 'YYYY-MM-DD 00:00:00',
                defaultValue: null
            }
        },
        {
            label: '结束时间',
            field: 'endTime',
            compType: 'datePicker',
            compConfig: {
                format: 'YYYY-MM-DD',
                showTime: false,
                valueFormat: 'YYYY-MM-DD 23:59:59',
                defaultValue: null
            }
        }
    ]
});

//发布抽屉相关
const releaseDrawerVisible = ref(false);
const currentReleaseData = ref({
    uuid: '',
    engUuid: ''
});

//发布
const btn_release = (rel: any) => {
    currentReleaseData.value = {
        uuid: rel.uuid,
        engUuid: rel.engUuid
    };
    releaseDrawerVisible.value = true;
};

// 关闭发布抽屉
const closeReleaseDrawer = () => {
    releaseDrawerVisible.value = false;
}

// 发布成功后的处理
const handleReleaseSuccess = () => {
    closeReleaseDrawer();
    getbaseline(); // 刷新数据
}

//导出管理抽屉相关
const exportDrawerVisible = ref(false);
const currentExportData = ref({
    uuid: '',
    engUuid: ''
});

//导出管理
const btn_export = (rel: any) => {
    currentExportData.value = {
        uuid: rel.uuid,
        engUuid: rel.engUuid
    };
    exportDrawerVisible.value = true;
}

// 关闭导出管理抽屉
const closeExportDrawer = () => {
    exportDrawerVisible.value = false;
}

//详情抽屉相关
const detailsDrawerVisible = ref(false);
const currentDetailData = ref({
    detailsId: '',
    detailsName: '',
    edition: ''
});

//详情
const btn_details = (rel: any)=>{
    currentDetailData.value = {
        detailsId: rel.uuid,
        detailsName: rel.engName,
        edition: rel.edition
    };
    detailsDrawerVisible.value = true;
}

//复制抽屉相关
const copyDrawerVisible = ref(false);
const currentCopyData = ref({
    uuid: '',
    engUuid: '',
    engName: '',
    edition: ''
});

//复制
const btn_copy=(rel: any)=>{
    currentCopyData.value = {
        uuid: rel.uuid,
        engUuid: rel.engUuid,
        engName: rel.engName,
        edition: rel.edition
    };
    copyDrawerVisible.value = true;
}


// 复制成功后的处理
const handleCopySuccess = () => {
    closeCopyDrawer();
    getbaseline(); // 刷新数据
}

//版本对比抽屉相关
const compareDrawerVisible = ref(false);
const currentCompareData = ref({});
const currentCompareResultData = ref({});

//版本对比
const btn_compare = (compareVal: any)=>{
    currentCompareData.value = compareVal;
    compareDrawerVisible.value = true;
}


// 获取操作菜单项
const getActionMenuItems = (record: any) => {
    return [
        {
            key: 'release',
            label: '发布',
            permission: RULE_PERMISSION.SNAPSHOOT.PUBLISH,
            onClick: () => btn_release(record)
        },
        {
            key: 'details',
            label: '详情',
            permission: RULE_PERMISSION.SNAPSHOOT.DETAIL,
            onClick: () => btn_details(record)
        },
        {
            key: 'copy',
            label: '复制',
            permission: RULE_PERMISSION.SNAPSHOOT.COPY,
            onClick: () => btn_copy(record)
        },
        {
            key: 'export',
            label: '导出管理',
            onClick: () => btn_export(record)
        },
        {
            key: 'compare',
            label: '版本对比',
            permission: RULE_PERMISSION.SNAPSHOOT.COMPARE_VERSION,
            onClick: () => btn_compare(record)
        }
    ];
};

// 关闭详情抽屉
const closeDetailsDrawer = () => {
    detailsDrawerVisible.value = false;
}

// 关闭复制抽屉
const closeCopyDrawer = () => {
    copyDrawerVisible.value = false;
}

// 关闭版本比对抽屉
const closeCompareDrawer = () => {
    compareDrawerVisible.value = false;
}

</script>

<template>
    <ListPage
        ref="listLayout"
        title="基线管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumns"
        :batchActions="eventConfig.batchEvent"
        :queryMethod="getbaseline"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="['publishStatus']"
        :rowSelection="rowSelection"
        :rowKey="record => record.uuid"
        :moreEvent="eventConfig.moreEvent"
        :pagination="pagination"
    >
        <template #publishStatus="{ record }">
            <span v-if="record.publishStatus == 0">否</span>
            <span v-if="record.publishStatus == 1">是</span>
        </template>
    </ListPage>

    <!-- 新增/导入对话框 -->
    <a-modal v-model:visible="isModalVisible" :title="modalType === 'add' ? '新增基线' : '导入基线'" @ok="handleModalOk"
             @cancel="handleCancel" :confirmLoading="submitLoading">
        <div style="max-height: 60vh; overflow-y: auto;">
            <BaseLineAdd ref="baseLineAddComponent" v-if="modalType === 'add'" @changeSubmitLoading="changeSubmitLoading"/>
            <BaseLineImport ref="baseLineImportComponent" v-if="modalType === 'import'" @changeSubmitLoading="changeSubmitLoading"/>
        </div>
    </a-modal>

    <!-- 批量发布对话框 -->
    <a-modal v-if="visibleRelease" title="批量基线发布" :visible="visibleRelease" @ok="handleOk2" @cancel="handleCancel2">
        <a-form :model="ruleFormRelease" :rules="rulesRelease" ref="formRefRelease" label-width="100px" class="demo-ruleForm">
            <a-form-item label="发布环境" name="envRelease">
                <a-select v-model:value="ruleFormRelease.env" placeholder="请选择" @change="selEnvRelease">
                    <a-select-option v-for="item in envArrRelease" :key="item.id" :value="item.id">
                        {{ item.environmentName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
    </a-modal>

    <!-- 导出对话框 -->
    <a-modal v-if="dialogFormVisible" title="提示信息" :visible="dialogFormVisible" @cancel="handleCancel3" :footer="null">
        <a-form :model="exportForm">
            <div class="dialogTip">请选择需要导出的格式：</div>
            <a-form-item>
                <a-radio-group v-model:value="exportForm.format" @change="checkRadio" :disabled="isExporting">
                    <a-radio value="Excel">导出excel</a-radio>
                    <a-radio value="Html">导出html</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </a-modal>

    <!-- 基线详情抽屉 -->
    <FlexDrawer
        :visible="detailsDrawerVisible"
        @close="closeDetailsDrawer"
        title="基线详情"
        :width="1200"
    >
        <BaseLineDetails v-if="detailsDrawerVisible" v-bind="currentDetailData" />
    </FlexDrawer>

    <!-- 版本比对抽屉 -->
    <FlexDrawer
        :visible="compareDrawerVisible"
        @close="closeCompareDrawer"
        title="版本对比"
        :width="1200"
    >
        <BaseLineCompareList v-if="compareDrawerVisible" :compareVal="currentCompareData" />
    </FlexDrawer>

    <!-- 基线发布抽屉 -->
    <FlexDrawer
        :visible="releaseDrawerVisible"
        @close="closeReleaseDrawer"
        title="基线发布"
        :width="1200"
    >
        <BaseLineRelease v-if="releaseDrawerVisible" :releaseData="currentReleaseData" @on-success="handleReleaseSuccess" />
    </FlexDrawer>

    <!-- 导出管理抽屉 -->
    <FlexDrawer
        :visible="exportDrawerVisible"
        @close="closeExportDrawer"
        title="导出管理"
        :width="1200"
    >
        <BaseLineExport v-if="exportDrawerVisible" :u="currentExportData.uuid" :rel="currentExportData.engUuid" />
    </FlexDrawer>

    <!-- 复制抽屉 -->
    <FlexDrawer
        :visible="copyDrawerVisible"
        @close="closeCopyDrawer"
        title="复制基线"
        :width="1200"
    >
        <BaseLineCopy v-if="copyDrawerVisible" :copyData="currentCopyData" @on-success="handleCopySuccess" />
    </FlexDrawer>
</template>

<style lang="scss" scoped>
.dialogTip {
    margin-bottom: 16px;
}

.action-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}
</style>

