<template>
  <span class="variableCom" @click="stopClick">
    <!-- 根据 dataSource 的 valueType 和 isTable 属性渲染不同的内容 -->
    <span
      v-if="dataSource && dataSource.valueType === 'List<String>' && !isTable"
    >
      <com-type-handler
        v-if="
          !(hideFrontBtn || variableType === 'expression' || locked || isTrack)
        "
        :pos="pos"
        :currentType="variableType"
        :disabledItems="disabledItems"
        @changeComType="changeComType"
        ref="comTypeHandler_com"
        :hiddenExpress="true"
      />
      <template v-if="dataSource && dataSource.singleParams">
        <span
          v-for="(paramItem, index) in dataSource.singleParams"
          :key="pos + '_operator_method_' + index + 'wrapper'"
        >
          <span v-show="index !== 0">,</span>
          <display-field
            :pos="pos + '_singleParams_' + index"
            :locked="locked"
            :isTrack="isTrack"
            :enumDictName="enumDictName"
            :propOptions="propOptions"
            :variableData="paramItem"
            :filterType="filterType"
            :methodFilterType="methodFilterType"
            @onChange="onChildChange"
            :predefineLine="predefineLine"
            :predefineCon="predefineCon"
            :preIndex="preIndex"
            :selectChange="selectChange"
            ref="displayField_com_enu"
            @changeComType="changeComType"
            :titleStr="titleStr"
            :refListData="refListData"
            :isTable="isTable"
            :noRuleCellUnit="noRuleCellUnit"
          />
          <OperatorParamTool
            v-show="!locked"
            @click.stop
            @onChange="onOperatorParamChange"
            :paramLength="dataSource.singleParams.length"
            :enumDictName="enumDictName"
            :index="index"
          />
        </span>
      </template>
      <template v-else>
        <display-field
          :pos="pos"
          :locked="locked"
          :isTrack="isTrack"
          :enumDictName="enumDictName"
          :propOptions="propOptions"
          :variableData="dataSource"
          :filterType="filterType"
          :methodFilterType="methodFilterType"
          @onChange="onChildChange"
          :predefineLine="predefineLine"
          :predefineCon="predefineCon"
          :preIndex="preIndex"
          :selectChange="selectChange"
          ref="displayField_com"
          @changeComType="changeComType"
          :titleStr="titleStr"
          :refListData="refListData"
          :isTable="isTable"
          :noRuleCellUnit="noRuleCellUnit"
        />
      </template>
    </span>

    <span
      v-else-if="
        dataSource && dataSource.valueType === 'List<String>' && isTable
      "
    >
      <com-type-handler
        v-if="
          !(hideFrontBtn || variableType === 'expression' || locked || isTrack)
        "
        :pos="pos"
        :currentType="variableType"
        :disabledItems="disabledItems"
        @changeComType="changeComType"
        ref="comTypeHandler_com"
        :hiddenExpress="true"
      />
      <span :key="pos + '_operator_method_'+ Number(pos.split('_')[pos.split('_').length - 1])+ 'wrapper'"
      >
        <template v-if="dataSource.valueType === 'List<String>'">
          <template v-if="enumDictName">
            <display-field
              :pos="pos + '_methodlist_'+Number(pos.split('_')[pos.split('_').length - 1])"
              :locked="locked"
              :isTrack="isTrack"
              :enumDictName="enumDictName"
              :propOptions="propOptions"
              :variableData="dataSource"
              :filterType="filterType"
              :methodFilterType="methodFilterType"
              @onChange="onChildChange"
              :predefineLine="predefineLine"
              :predefineCon="predefineCon"
              :preIndex="preIndex"
              :selectChange="selectChange"
              ref="displayField_com_enu"
              @changeComType="changeComType"
              :titleStr="titleStr"
              :refListData="refListData"
              :isTable="isTable"
              :noRuleCellUnit="noRuleCellUnit"
            />
            <!-- <Teleport to="body">
              <CheckAddVars 
                :checkListOption="optionData" :checkAddisShow="checkAddisShow" @checkboxHide="checkboxHide"
                @addVarsSure="addVarsSure" 
                @searchChange="searchChange" 
              />
            </Teleport> -->
            <a-dropdown>
              <ToolOutlined />
              <template #overlay>
                <a-menu @click="onOperatorParamChangeTableHead">
                  <a-menu-item key="checkAddVars">
                    <span>批量勾选增加</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template v-else>
            <display-field
              :pos="pos + '_methodlist_'+Number(pos.split('_')[pos.split('_').length - 1])"
              :locked="locked"
              :isTrack="isTrack"
              :enumDictName="enumDictName"
              :propOptions="propOptions"
              :variableData="dataSource"
              :filterType="filterType"
              :methodFilterType="methodFilterType"
              @onChange="onChildChange"
              :predefineLine="predefineLine"
              :predefineCon="predefineCon"
              :preIndex="preIndex"
              :selectChange="selectChange"
              ref="displayField_com"
              @changeComType="changeComType"
              :titleStr="titleStr"
              :refListData="refListData"
              :isTable="isTable"
              :noRuleCellUnit="noRuleCellUnit"
            />
            <!-- <a-dropdown>
              <ToolOutlined />
              <template #overlay>
                <a-menu @click="onOperatorParamChangeTableHead">
                  <a-menu-item key="textAddVars">
                    <span>批量文本增加</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown> -->
          </template>
        </template>
      </span>
      <!-- <span
        v-for="(paramItem, index) in methodParamsList"
        :key="pos + '_operator_method_' + index + 'wrapper'"
      >
        <template v-if="paramItem.valueType === 'List<String>'">
          <template v-if="enumDictName">
            <span v-if="index !== 0">,</span>
            <span>test{{index}}</span>
            <display-field
              :pos="pos + '_methodlist_' + index"
              :locked="locked"
              :isTrack="isTrack"
              :enumDictName="enumDictName"
              :propOptions="propOptions"
              :variableData="paramItem"
              :filterType="filterType"
              :methodFilterType="methodFilterType"
              @onChange="onChildChange"
              :predefineLine="predefineLine"
              :predefineCon="predefineCon"
              :preIndex="preIndex"
              :selectChange="selectChange"
              ref="displayField_com_enu"
              @changeComType="changeComType"
              :titleStr="titleStr"
              :refListData="refListData"
              :isTable="isTable"
              :noRuleCellUnit="noRuleCellUnit"
            />
            <a-dropdown>
              <ToolOutlined />
              <template #overlay>
                <a-menu @click="onOperatorParamChangeTableHead">
                  <a-menu-item key="checkAddVars">
                    <span>批量勾选增加</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
          <template v-else>
            <display-field
              :pos="pos + '_methodlist_' + index"
              :locked="locked"
              :isTrack="isTrack"
              :enumDictName="enumDictName"
              :propOptions="propOptions"
              :variableData="paramItem"
              :filterType="filterType"
              :methodFilterType="methodFilterType"
              @onChange="onChildChange"
              :predefineLine="predefineLine"
              :predefineCon="predefineCon"
              :preIndex="preIndex"
              :selectChange="selectChange"
              ref="displayField_com"
              @changeComType="changeComType"
              :titleStr="titleStr"
              :refListData="refListData"
              :isTable="isTable"
              :noRuleCellUnit="noRuleCellUnit"
            />
          </template>
        </template>
      </span> -->
    </span>
    <span v-else>
      <com-type-handler
        v-if="
          !(hideFrontBtn || variableType === 'expression' || locked || isTrack)
        "
        :pos="pos"
        :currentType="variableType"
        :disabledItems="disabledItems"
        @changeComType="changeComType"
        ref="comTypeHandler_com"
      />
      <display-field
        :pos="pos"
        :locked="locked"
        :isTrack="isTrack"
        :enumDictName="enumDictName"
        :propOptions="propOptions"
        :variableData="dataSource"
        :filterType="filterType"
        :methodFilterType="methodFilterType"
        @onChange="onChildChange"
        :predefineLine="predefineLine"
        :predefineCon="predefineCon"
        :preIndex="preIndex"
        :selectChange="selectChange"
        ref="displayField_com"
        @changeComType="changeComType"
        :titleStr="titleStr"
        :refListData="refListData"
        :isTable="isTable"
        :noRuleCellUnit="noRuleCellUnit"
      />
    </span>
    <calculate-sign
      v-if="!(hideEndBtn || variableType === 'expression' || !!enumDictName)"
      :pos="pos"
      :value="signValue"
      :locked="locked"
      :isTrack="isTrack"
      :isLastOne="isLastOne"
      :realValueType="realValueType"
      @onChange="changeCalculateSign"
    />
    <Teleport to="body">
      <TextAddVars 
        :textAddisShow="textAddisShow" :elAlertErrInfo="elAlertErrInfo" @textAddHide="textAddHide"
        @addVarsSure="addVarsSure" 
      />
      <CheckAddVars 
        :checkListOption="optionData" :checkAddisShow="checkAddisShow" @checkboxHide="checkboxHide"
        @addVarsSure="addVarsSure" 
        @searchChange="searchChange" 
      />
    </Teleport>
  </span>
</template>

<script setup>
import store from "@/store";
import ComTypeHandler from "@/components/ruleEditCom/ruleItemList/operationMenu/comTypeHandler.vue";
import CalculateSign from "@/components/ruleEditCom/ruleItemList/operationMenu/calculateSign.vue";
import DisplayField from "./displayField.vue";
import * as util from "@/components/ruleEditCom/utils/util";
import OperatorParamTool from "@/components/ruleEditCom/ruleItemList/operationMenu/operatorParamTool.vue";
import globalEventEmitter from "@/utils/eventBus";
import TextAddVars from "@/components/ruleEditCom/ruleItemList/operationMenu/textAddVars.vue";
import CheckAddVars from "@/components/ruleEditCom/ruleItemList/operationMenu/checkAddVars.vue";

// 注入 ruleUuid
const ruleUuid = inject("ruleUuid", "");

// 定义 props
const props = defineProps({
  pos: {
    type: String,
    default: "erule",
  },
  dataSource: {
    default: () => ({}),
  },
  signValue: {
    type: String,
    default: "",
  },
  methodFilterType: {
    type: String,
    default: "null",
  },
  filterType: {
    type: String,
    default: "null",
  },
  enumDictName: String,
  hideFrontBtn: Boolean,
  hideEndBtn: Boolean,
  propOptions: [Object, Array],
  locked: Boolean,
  isTrack: Boolean,
  isLastOne: Boolean,
  predefineLine: String,
  predefineCon: String,
  preIndex: Number,
  titleStr: String,
  changeFun: Boolean,
  variableData: {
    type: Object,
    default: () => ({}),
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});
const attrs = useAttrs();

// 定义 emit
const emit = defineEmits(["onChange", "change", "onCalSignChange"]);

// 定义 data
const inputTypesArr = [
  "Short",
  "Integer",
  "Long",
  "Float",
  "Double",
  "BigDecimal",
  "String",
  "Character",
  "Boolean",
  "Date",
  "Enum",
];
const msg = ref("reluEditor");
const cacheName = ref("");
const selectChange = ref(false);
const refListData = ref(false);
const cacheValueType = ref(null);
const methodParamsList = ref([]);
const listEnumDictName = ref("");
const listValueType = ref("");
const listVariableType = ref("");
const optionData = ref([]);
const checkAddisShow = ref(false);
const textAddisShow = ref(false);
const _oldOptionD = ref([]);
const checkboxSearchTxt = ref('');
const aError = ref([]);
const elAlertErrInfo = ref([]);

// 计算属性
const variableType = computed(() => {
  return (props.dataSource && props.dataSource.variableType) || "";
});
const valueType = computed(() => {
  return props.dataSource && props.dataSource.valueType;
});
const isParam = computed(() => {
  return props.dataSource && props.dataSource.__context.isParam;
});
const isRootVar = computed(() => {
  return props.dataSource && props.dataSource.__context.isRootVar;
});
const realValueType = computed(() => {
  return props.dataSource && util.getRealValueType(props.dataSource);
});
const disabledItems = computed(() => {
  const disabledItems = [];
  const {
    isRootVar,
    isParam,
    inputTypesArr,
    valueType,
    variableType,
    realValueType,
    variableData,
  } = props;
  if (
    (!isRootVar ||
      isParam === "actionSetLeft" ||
      !(inputTypesArr.indexOf(realValueType) > -1)) &&
    ((realValueType && realValueType != "List<String>") ||
      (variableData &&
        variableType === "field" &&
        variableData.paramsList &&
        variableData.paramsList.length === 1 &&
        variableData.paramsList[0].valueType !== "List<String>"))
  ) {
    disabledItems.push("constant");
  }
  if (
    variableType === "constant" &&
    valueType === "Enum" &&
    isParam === "expressionParam"
  ) {
    disabledItems.push("propSelect");
    disabledItems.push("expression");
  }
  return disabledItems;
});
const methodParamsListValue = computed(() => {
  if (methodParamsList.value && methodParamsList.value.value) {
    return methodParamsList.value.value.split(",");
  } else {
    return [];
  }
});

// 监听器
watch(
  () => props.changeFun,
  (newVal) => {
    if (newVal) {
      refListData.value = newVal;
    }
  }
);
watch(
  () => props.variableData,
  (newVal) => {
    if (newVal) {
      setMethodParamsList();
    }
  }
);
watch(
  () => checkboxSearchTxt.value,
  (newVal) => {
    optionData.value = JSON.parse(JSON.stringify(_oldOptionD.value));
    if (newVal) {
      if (optionData.value && optionData.value.length) {
        optionData.value = optionData.value.filter((item) => {
          return (
            item["viewName"]
              .toLowerCase()
              .indexOf(checkboxSearchTxt.value.toLowerCase()) !== -1
          );
        });
      }
    }
  }
);
watch(
  () => props.dataSource,
  (newVal) => {
    if (newVal) {
      optionData.value =
          store.getters.listMap[ruleUuid].dictMap[
          props.dataSource["enumDictName"]
          ];
    }
  }
);

// 生命周期钩子
onMounted(() => {
  if (props.variableData) {
    setMethodParamsList();
  }
  optionData.value =  store.getters.listMap[ruleUuid].dictMap[ props.dataSource["enumDictName"]];
});

// 方法
function setMethodParamsList() {
  const { methodParams, paramsList, valueType, variableType } =
    props.variableData;
  methodParamsList.value = methodParams;
  if (
    methodParams &&
    methodParams.length > 0 &&
    methodParams[0].singleParams &&
    !props.noRuleCellUnit
  ) {
    methodParamsList.value = methodParams[0].singleParams;
  }
  if (paramsList && paramsList[0].domain) {
    listEnumDictName.value = paramsList[0].domain;
  }
  listValueType.value = valueType;
  listVariableType.value = variableType;
}

function onChange(pos, newValueObj, finalValueType, operatorOptions) {
  emit("onChange", pos, newValueObj, finalValueType, operatorOptions);
  emit("change", pos, newValueObj, finalValueType, operatorOptions);
}

function onChildChange(pos, newValueObj, finalValueType, operatorOptions) {
  onChange(pos, newValueObj, finalValueType, operatorOptions);
}

function changeComType(pos, type, selectChangeTag) {
  const { dataSource } = props;
  const { valueType } = dataSource;
  const newVariableData = {};
  newVariableData.valueType = dataSource._getRealValueType();
  if (selectChangeTag === "selectChange") {
    selectChange.value = true;
  } else {
    selectChange.value = false;
  }
  if (type === "constant") {
    newVariableData.variableType = "constant";
    newVariableData.value = "";
    if (
      (props.enumDictName || cacheName.value) &&
      cacheValueType.value === valueType
    ) {
      newVariableData.enumDictName = props.enumDictName || cacheName.value;
    }
  } else if (type === "propSelect") {
    newVariableData.variableType = "field";
    newVariableData.value = [];
    if (props.enumDictName) {
      cacheName.value = props.enumDictName;
    }
    valueType && (cacheValueType.value = valueType);
  } else if (type === "expression") {
    newVariableData.variableType = "expression";
    newVariableData.expressionTreeData = {
      type: "expression",
      symbols: [],
      params: [
        {
          type: "expression",
          symbols: [],
          params: [{ type: "variable", data: dataSource }],
        },
      ],
    };
    if (props.enumDictName) {
      cacheName.value = props.enumDictName;
    }
  }
  const _newVariableData = dataSource._derive(newVariableData);
  onChange(pos, _newVariableData, dataSource.valueType, type);
}

function changeCalculateSign(pos, sign, paramType) {
  const valueType = util.getRealValueType(props.dataSource);
  const newParamItem = util.getExpressionItem(paramType, valueType);
  emit("onCalSignChange", pos, sign, newParamItem);
}

function stopClick(event) {
  event.stopPropagation();
}

function onOperatorParamChange(key, index, event) {
  if (key === "addVar") {
    let newVairable = {
      variableType: "constant",
      valueType: "String",
      value: "",
    };
    if (props.enumDictName) {
      newVairable.enumDictName = props.enumDictName;
    }
    const _newVariableData = props.dataSource._derive(newVairable);
    if (props.dataSource && props.dataSource.singleParams) {
      props.dataSource.singleParams.push(newVairable);
    } else {
      methodParamsList.value.push(newVairable);
    }
  } else if (key === "addVars") {
    textAddisShow.value = true;
  } else if (key === "checkAddVars") {
    checkAddisShow.value = true;
    enumCheckbox();
  } else if (key === "deleteVar") {
    if (props.dataSource && props.dataSource.singleParams) {
      props.dataSource.singleParams.splice(index, 1);
    } else {
      methodParamsList.value.splice(index, 1);
    }
  }
}
function enumCheckbox() {
  if (props.dataSource) {
    let _enumDictName = props.dataSource["enumDictName"];
    if (_enumDictName) {
      // 获取原始数据，避免直接修改 store 中的数据
      const originalData = store.getters.listMap[ruleUuid].dictMap[props.dataSource["enumDictName"]];
      
      // 创建新的数组，避免直接修改原始数据
      optionData.value = originalData.map((_opItem) => ({
        value: _opItem.value,
        viewName: _opItem.viewName,
        checked: false, // 默认未选中
      }));

      // 根据不同的数据结构设置已选中的项
      if (props.isTable) {
        // 决策表：数据存储在 dataSource.value 中，以逗号分隔的字符串形式
        
        if (props.dataSource.value) {
          const selectedValues = props.dataSource.value.toString().split(',').filter(val => val.trim() !== '');
          
          selectedValues.forEach((selectedValue) => {
            const foundIndex = optionData.value.findIndex(opItem => opItem.value === selectedValue.trim());
            if (foundIndex !== -1) {
              optionData.value[foundIndex].checked = true;
            }
          });
        }
      } else {
        if (props.dataSource?.singleParams?.length > 0) {
          props.dataSource.singleParams.forEach((item) => {
            const foundIndex = optionData.value.findIndex(opItem => opItem.value === item.value);
            if (foundIndex !== -1) {
              optionData.value[foundIndex].checked = true;
            }
          });
        }
      }
      _oldOptionD.value = JSON.parse(JSON.stringify(optionData.value));
    }
  }
}
const checkboxHide = () => {
  checkAddisShow.value = false;
};
const textAddHide = () => {
  elAlertErrInfo.value = [];
  textAddisShow.value = false;
};


const searchChange = (val) => {
  checkboxSearchTxt.value = val;
};

const addVarsSure = (list, checkOpt) => {
  let aVars = [];
  const { singleParams } = props.dataSource;
  let _enumDictName = props.dataSource["enumDictName"];
  let _operatorParamsListLoopStart = 0;
  aError.value = [];
  elAlertErrInfo.value = [];
  if (checkOpt === true) {
    aVars = list;
  } else {
    list && (aVars = list.split(/[,，;；]/g));
  }
  if (_enumDictName && checkOpt !== true) {
    (!optionData.value || optionData.value.length === 0) &&
      (optionData.value = store.getters.listMap[ruleUuid].dictMap[props.dataSource["enumDictName"]]);
    for (let i = 0; i < aVars.length; i++) {
      aError.value.push({
        isErr: optionData.value.some((v) => {
          return v.viewName === aVars[i];
        }),
        value: aVars[i],
      });
    }
  }
  if (
    aError.value.some((obj) => {
      return obj.isErr === false;
    })
  ) {
    aError.value.some((item) => {
      if (item.isErr === false) {
        elAlertErrInfo.value.push(item.value);
      }
    });
    return;
  }
  if (checkOpt === true && _enumDictName) {
    if (singleParams && singleParams.length > 1) {
      singleParams.splice(1);
    }
  }
  if (!props.isTable) {
    if (_enumDictName) {
      // domain
      let aItem = aVars.length ? aVars[0] : "";
      let _val = "";

      if (checkOpt === true) {
        _val = aItem;
        if (_val) {
          props.dataSource.singleParams[0].value = _val;
        }
        _operatorParamsListLoopStart = 1;
      } else {
        if (singleParams.length === 1 && !singleParams[0].value) {
          // 没有值
          _val = aItem
            ? optionData.value[
                optionData.value.findIndex((v) => {
                  return v.viewName === aItem;
                })
              ]["value"]
            : "";
          if (_val) {
            props.dataSource.singleParams[0].value = _val;
          }
          _operatorParamsListLoopStart = 1;
        }
      }
    } else {
      if (singleParams.length === 1 && !singleParams[0].value) {
        // 没有值
        if (aVars.length) {
          props.dataSource.singleParams[0].value = aVars[0];
        }
        _operatorParamsListLoopStart = 1;
      }
    }
    if (aVars.length === 1) {
      createVars(aVars[0], true, true);
    }
    for (let i = _operatorParamsListLoopStart; i < aVars.length; i++) {
      setTimeout(() => {
        if (checkOpt === true) {
          createVars(aVars[i], true);
        } else {
          createVars(aVars[i]);
        }
      }, 0);
    }
  } else {
    createVars(aVars);
  }
  textAddisShow.value = false;
  checkAddisShow.value = false;
}
  // 创建批量数据
const createVars = (val, checkOpt, first) => {
  const { enumDictName } = props.dataSource;
  if (!props.isTable) {
    // domain
    enumDictName &&
      nextTick(() => {
        let _val = "";
        if (checkOpt === true) {
          _val = val;
        } else {
          _val =
            optionData.value[
              optionData.value.findIndex((v) => {
                return v.viewName === val;
              })
            ]["value"];
        }
        if (typeof _val === "string" || !isNaN(_val)) {
          const newVariable = {
            variableType: "constant",
            valueType: "String",
            value: _val,
            enumDictName,
          };
          if (first) {
            props.dataSource.singleParams = [newVariable];
          } else {
            props.dataSource.singleParams.push(newVariable);
          }
        }
      });
    // 字符串
    !enumDictName &&
      nextTick(() => {
        const newVariableData = {
          valueType: "String",
          variableType: "constant",
          value: val,
        };
        props.dataSource.singleParams.push(newVariableData);
      });
  } else {
    nextTick(() => {
      props.dataSource.value = val.toString();
    });
  }
};
function onOperatorParamChangeTableHead(e) {
  const {key} = e
  if(key === 'checkAddVars'){
    checkAddisShow.value = true;
    enumCheckbox();
  }else if(key === 'textAddVars'){
    textAddisShow.value = true;
  }
}
</script>
