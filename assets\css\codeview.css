/* codeview theme */

.cm-s-codeview.CodeMirror { background: #fff; color: #409EFF;font-size: 13px; margin-top: 30px;font-family: -apple-system, Consolas, 'Courier New', monospace, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'}
.cm-s-codeview div.CodeMirror-selected { background: #f1f1f1; }
.cm-s-codeview .CodeMirror-line::selection, .cm-s-codeview .CodeMirror-line > span::selection, .cm-s-codeview .CodeMirror-line > span > span::selection { background: #f1f1f1; }
.cm-s-codeview .CodeMirror-line::-moz-selection, .cm-s-codeview .CodeMirror-line > span::-moz-selection, .cm-s-codeview .CodeMirror-line > span > span::-moz-selection { background: #f1f1f1; }
.cm-s-codeview .CodeMirror-gutters { background: #fff; border-right: 0; }
.cm-s-codeview .CodeMirror-guttermarker { color: #E6A23C; }
.cm-s-codeview .CodeMirror-guttermarker-subtle { color: #888; }
.cm-s-codeview .CodeMirror-linenumber { color: #888; }
.cm-s-codeview .CodeMirror-cursor { border-left: 1px solid #A7A7A7; }

.cm-s-codeview .cm-keyword { color: #E6A23C; }
.cm-s-codeview .cm-atom { color: #67C23A; }
.cm-s-codeview .cm-number { color: #67C23A; }
.cm-s-codeview .cm-def { color: #8DA6CE; }
.cm-s-codeview .cm-variable { color: #F56C6C; }
.cm-s-codeview .cm-operator { color: #E6A23C; }
.cm-s-codeview .cm-comment { color: #AEAEAE; }
.cm-s-codeview .cm-string { color: #67C23A; }
.cm-s-codeview .cm-string-2 { color: #67C23A; }
.cm-s-codeview .cm-meta { color: #67C23A; }
.cm-s-codeview .cm-builtin { color: #8DA6CE; }
.cm-s-codeview .cm-tag { color: #8DA6CE; }
.cm-s-codeview .cm-attribute { color: #8DA6CE; }
.cm-s-codeview .cm-header { color: #F56C6C; }
.cm-s-codeview .cm-hr { color: #AEAEAE; }
.cm-s-codeview .cm-link { color: #8DA6CE; }
.cm-s-codeview .cm-error { background: #9D1E15; color: #409EFF; }

.cm-s-codeview .CodeMirror-activeline-background { background: #3C3636; }
.cm-s-codeview .CodeMirror-matchingbracket { outline:1px solid grey;color:white !important; }
