<template>
    <div class="logo-container" :style="containerStyle">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" :width="computedWidth"
            :height="height" viewBox="0 0 98 26" version="1.1">
            <defs>
                <linearGradient x1="82.745802%" y1="0.0364365082%" x2="20.2771185%" y2="79.3849246%"
                    id="linearGradient-1">
                    <stop stop-color="#4A9FE5" offset="0%" />
                    <stop stop-color="#006AD4" offset="100%" />
                </linearGradient>
                <rect id="path-2" x="0" y="0" width="26.0094787" height="26" rx="6.24" />
                <radialGradient cx="60.2119822%" cy="64.1956281%" fx="60.2119822%" fy="64.1956281%" r="51.0053278%"
                    gradientTransform="translate(0.602120,0.641956),scale(0.852171,1.000000),rotate(-68.407630),translate(-0.602120,-0.641956)"
                    id="radialGradient-4">
                    <stop stop-color="var(--yq-yuque-green-500)" offset="0%" />
                    <stop stop-color="var(--yq-yuque-green-600)" stop-opacity="0.8" offset="100%" />
                </radialGradient>
                <path
                    d="M9.85844087,0 L9.9312233,0 L9.99553048,0 L9.99553048,0 L10.0610901,0.000331576155 L10.0610901,0.000331576155 L10.127886,0.000681979331 L10.127886,0.000681979331 L10.1959096,0.00116250403 L10.1959096,0.00116250403 L10.26515,0.00177843386 L10.26515,0.00177843386 L10.3355968,0.0025350524 L10.3355968,0.0025350524 L10.4072393,0.00343764326 L10.4072393,0.00343764326 L10.4556599,0.00412307541 L10.4556599,0.00412307541 L10.5046042,0.00487729792 L10.5046042,0.00487729792 L10.5789953,0.00614103816 L10.5789953,0.00614103816 L10.6545468,0.0075683627 L10.6545468,0.0075683627 L10.731248,0.00916455514 L10.731248,0.00916455514 L10.7830155,0.0103251081 L10.7830155,0.0103251081 L10.8616092,0.0122144884 L10.8616092,0.0122144884 L10.9413243,0.0142868261 L10.9413243,0.0142868261 L11.0221501,0.0165474049 L11.0221501,0.0165474049 L11.0766458,0.018161645 L11.0766458,0.018161645 L11.1592993,0.0207477003 L11.1592993,0.0207477003 L11.2430353,0.0235360862 L11.2430353,0.0235360862 L11.2994553,0.0255100253 L11.2994553,0.0255100253 L11.3563486,0.0275778029 L11.3563486,0.0275778029 L11.413712,0.0297409845 L11.413712,0.0297409845 L11.4715422,0.0320011357 L11.4715422,0.0320011357 L11.5298363,0.0343598219 L11.5298363,0.0343598219 L11.588591,0.0368186086 L11.588591,0.0368186086 L11.6775798,0.0406979018 L11.6775798,0.0406979018 L11.7374723,0.0434137891 L11.7374723,0.0434137891 L11.8281532,0.0476860714 L11.8281532,0.0476860714 L11.8891638,0.0506688435 L11.8891638,0.0506688435 L11.9506163,0.0537611093 L11.9506163,0.0537611093 L12.0436163,0.0586082331 L12.0436163,0.0586082331 L12.1061594,0.061981082 L12.1061594,0.061981082 L12.1691332,0.0654689039 L12.1691332,0.0654689039 L12.2325346,0.0690732641 L12.2325346,0.0690732641 L12.2963604,0.0727957283 L12.2963604,0.0727957283 L12.3606076,0.0766378619 L12.3606076,0.0766378619 L12.4252729,0.0806012304 L12.4252729,0.0806012304 L12.4903532,0.0846873994 L12.4903532,0.0846873994 L12.5558454,0.0888979343 L12.5558454,0.0888979343 L12.6217464,0.0932344007 L12.6217464,0.0932344007 L12.6880528,0.0976983641 L12.6880528,0.0976983641 C15.0688559,0.259796072 15.6993879,1.96792848 15.7664808,2.17232082 L15.7700096,2.18329556 L15.7700096,2.18329556 L15.7727503,2.19228685 L15.7727503,2.19228685 L16.8062949,2.24835336 L16.8062949,2.24835336 C16.8614495,2.24835336 16.9061611,2.29289806 16.9061611,2.34784674 C16.9061611,2.38723092 16.8831917,2.42127034 16.8498709,2.43739409 C15.7889724,3.0093152 15.4648644,4.1714334 15.6554104,4.92842573 C15.7168213,5.17239652 15.8122342,5.38211813 15.9184548,5.60378714 L15.9855673,5.74331821 C16.2314462,6.25567571 16.5075123,6.87607121 16.5525933,8.1246874 C16.6535066,10.9196977 14.1874424,13.4317662 11.2694366,13.4317662 L11.237506,13.4317788 L11.237506,13.4317788 L11.1693533,13.4318803 L11.1693533,13.4318803 L11.0563958,13.4322227 L11.0563958,13.4322227 L10.9739384,13.4325778 L10.9739384,13.4325778 L10.8395238,13.4333006 L10.8395238,13.4333006 L10.7427617,13.4339094 L10.7427617,13.4339094 L10.6402777,13.4346195 L10.6402777,13.4346195 L10.4758231,13.435875 L10.4758231,13.435875 L10.2984943,13.4373588 L10.2984943,13.4373588 L10.1082912,13.4390708 L10.1082912,13.4390708 L9.83466043,13.4417086 L9.83466043,13.4417086 L9.61441739,13.4439533 L9.61441739,13.4439533 L9.21873617,13.4482017 L9.21873617,13.4482017 L8.7872931,13.4530841 L8.7872931,13.4530841 L8.3200882,13.4586007 L8.3200882,13.4586007 L8.02259957,13.4622149 L8.02259957,13.4622149 L7.60592143,13.4673891 L7.60592143,13.4673891 L7.05288809,13.4744274 L7.05288809,13.4744274 L6.46409291,13.4820999 L6.46409291,13.4820999 L5.96730824,13.4886944 L5.96730824,13.4886944 L5.17921702,13.499347 L5.17921702,13.499347 L4.6252134,13.506956 L4.6252134,13.506956 L3.60063383,13.5212483 L3.60063383,13.5212483 L2.50596103,13.5367834 L2.50596103,13.5367834 L1.51188159,13.5510884 L1.51188159,13.5510884 L0.995530476,13.5585833 L7.23561653,6.48906312 L7.29216481,6.42484653 L7.29216481,6.42484653 L7.34855968,6.36094751 L7.34855968,6.36094751 L7.40478833,6.29734935 L7.40478833,6.29734935 L7.68298947,5.98328638 L7.68298947,5.98328638 L7.73795156,5.92114234 L7.73795156,5.92114234 L7.79265768,5.85918213 L7.79265768,5.85918213 L7.84709504,5.79738905 C7.85614482,5.7871032 7.86518287,5.77682362 7.87420892,5.76654996 L7.92821907,5.70497623 C7.93719605,5.69472485 7.9461605,5.68447869 7.95511214,5.67423741 L8.00866625,5.6128454 L8.00866625,5.6128454 L8.06190032,5.55155366 C8.68104762,4.83700317 9.23223448,4.13950919 9.62386318,3.33961638 C10.0585826,2.11638587 9.61603759,1.20361183 9.16429212,0.650548948 C9.12941297,0.607847103 9.09447897,0.567289621 9.05988967,0.528899171 C8.90589196,0.332507516 9.01074479,0.00530922209 9.30400548,0.00530922209 C9.32790115,0.00530922209 9.35228234,0.00494128632 9.37714676,0.00446664733 L9.43390619,0.00332885546 C9.44670018,0.00308498022 9.45961454,0.00286341029 9.472649,0.00269679974 L9.52679543,0.00205054544 L9.52679543,0.00205054544 L9.58227212,0.00148243835 L9.58227212,0.00148243835 L9.61999038,0.00114971997 L9.61999038,0.00114971997 L9.71683142,0.000490388031 L9.71683142,0.000490388031 L9.79689645,0.000153206697 L9.79689645,0.000153206697 L9.85844087,0 L9.85844087,0 Z"
                    id="path-5" />
            </defs>
            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g transform="translate(0.000000, -0.000000)">
                    <text x="33" y="20" font-family="Arial, sans-serif" font-size="18" font-weight="bold"
                        fill="#2c3e50">
                        eRule
                    </text>
                    <g transform="translate(0.000000, 0.000000)">
                        <mask id="mask-3" fill="white">
                            <use xlink:href="#path-2" />
                        </mask>
                        <use fill="url(#linearGradient-1)" xlink:href="#path-2" />
                        <text x="5" y="19" font-family="Helvetica, Arial, sans-serif" font-size="24" font-weight="bold"
                            fill="white" font-style="italic">
                            e
                        </text>
                    </g>
                </g>
            </g>
        </svg>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    width: {
        type: [String, Number],
        default: ''
    },
    height: {
        type: [String, Number],
        default: '52'
    },
    customStyle: {
        type: Object,
        default: () => ({})
    }
});

// 原始SVG的宽高比例
const aspectRatio = 98 / 26;

// 计算宽度：如果提供了宽度就使用，否则根据高度和比例计算
const computedWidth = computed(() => {
    if (props.width) {
        return props.width;
    }

    // 将高度转换为数字并乘以宽高比
    const heightValue = Number(props.height);
    return Math.round(heightValue * aspectRatio);
});

const containerStyle = computed(() => {
    return {
        display: 'inline-flex',
        alignItems: 'center',
        ...props.customStyle
    };
});
</script>

<style scoped>
.logo-container {
    line-height: 0;
}
</style>
