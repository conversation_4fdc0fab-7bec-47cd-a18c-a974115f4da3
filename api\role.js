import request from '@/utils/request'
// 角色

// 菜单关联按钮树 @RequestParam 点击角色分配按钮时获得
export function getInfobtn(params) {
    return request({
      url: 'sys/menu/getMenuButtonTreeByRoleId',
      method: 'get',
      params,
    })
}

// 根据菜单id查询按钮列表信息
export function getrolebtn(params) {
  return request({
    url: 'sys/menu/getButtonsByMenuId',
    method: 'get',
    params,
    headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
      },
  })
}
export function getInfosub(data) {
  return request({
    url: 'sys/role/setButtonAction',
    method: 'post',
    data,
  })
}
// 根据角色id和菜单id查询可分配按钮列表
export function showbtn(params) {
  return request({
    url: 'sys/role/getButtonIdsByRoleIdAndMenuId',
    method: 'get',
    params,
  })
}
export function getRulebase(params) {
  return request({
    url: 'erule/manage/engineering/getEnginesByBusinessUuid',
    method: 'get',
    params,
  })
}
export function getRule(data) {
  return request({
    url: 'sys/role/setEngAction',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}

// 获得所有可用菜单
export function getAvailableMenus(params) {
  return request({
    url: 'sys/menu/getAvailableMenus',
    method: 'get',
    params
  })
}

// 通过角色id查询菜单列表ids
export function getMenuIdsById(params) {
  return request({
    url: 'sys/role/getMenuIdsById',
    method: 'get',
    params
  })
}

// 更新菜单保存到数据库
export function setEngAction(params) {
  return request({
    url: 'sys/role/setMenuAction',
    method: 'post',
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
    data: params
  })
}

// 打开角色数据列表
export function list(params) {
  return request({
    url: 'sys/role/list',
    method: 'get',
    params
  })
}
// 删除
export function roleDel(data) {
  return request({
    url: 'sys/role/delRole',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}
// 添加角色

export function roleAdd(data) {
  return request({
    url: 'sys/role/addAction',
    method: 'post',
    data,

  })
}
// 业务条线
export function roleBusin() {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get',
  })
}

// 分配规则库状态
export function roleRuleStates(params) {
  return request({
    url: '/sys/role/getEngineIdsById',
    method: 'get',
    params,
  })
}

//获取用户所有按钮权限
export function getUserButtons() {
  
  return request({
    url: '/sys/menu/getUserButtons',
    method: 'get'
  })
}
