<template>
  <div>
    <a-table
      class="editor-table design"
      :columns="columns"
      :data-source="data"
      :pagination="pagination"
      size="small"
      bordered
      rowKey="key"
      :scroll="{ x: '100%', y: 'calc(100vh - 312px)' }"
      :customRow="customRowProps"
      >
    </a-table>
  </div>
</template>
<script lang="jsx">
import store from "@/store";
import { cloneDeep } from "lodash";
import EnumSelect from "@/components/ruleEditCom/ruleItemList/inputItem/enumSelect.vue";
import EnumMultiple from "@/components/ruleEditCom/ruleItemList/inputItem/enumMultiple.vue";
import StringMultiple from "@/components/ruleEditCom/ruleItemList/inputItem/stringMultiple.vue";
import NumInput from "@/components/ruleEditCom/ruleItemList/inputItem/numInput.vue";
import DateInput from "@/components/ruleEditCom/ruleItemList/inputItem/dateInput.vue";
import TableCellText from "@/components/ruleEditCom/ruleItemList/inputItem/tableCellText.vue";
import TableStrInput from "@/components/ruleEditCom/ruleItemList/inputItem/tableStrInput.vue";
import globalEventEmitter from '~/utils/eventBus';
import { CLOSE_POPOVER } from '@/consts/globalEventConsts';

/**
 * 查找最内层的 next 属性
 * @param obj 目标对象
 * @returns next属性值
 */
function findDeepestNextProperty(obj) {
  if (obj?.next?.next) {
    return findDeepestNextProperty(obj.next);
  } else {
    return obj.next;
  }
}

export default {
  name: "TableEditor",
  inject: {
    ruleUuid: { default: "" },
  },
  components: {
    "enum-select": EnumSelect,
    "enum-multiple": EnumMultiple,
    "num-input": NumInput,
    "date-input": DateInput,
    "table-cell-text": TableCellText,
    "string-multiple": StringMultiple,
    "table-str-input": TableStrInput
  },
  props: {
    props_columns: {
      type: Array,
      default: () => [],
    },
    props_data: {
      type: Array,
      default: () => [],
    },
    isEditing: {
      type: Boolean,
      default: true,
    },
    props_predefine: String,
  },
  computed: {
    colObjKeys() {
      const list = [];
      this.props_columns.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children.map((items) => {
            list.push(items.dataIndex);
          });
        } else {
          list.push(item.dataIndex);
        }
      });
      return list;
    },
  },
  data() {
    this.cacheData = cloneDeep(this.props_data);
    return {
      data: cloneDeep(this.props_data),
      // columns: this.props_columns,
      columns: this.getCol(),
      editingKey: "",
      booleanTrue: true,
      pagination: {
        defaultPageSize: 10,
        showTotal: (total) => `共${total}条`,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ["10", "20", "50", "100", "200", "500", "1000"],
      },
      rowHtml: "",
      rowPredefine: "",
      rowCondition: "",
      rowInfo: {},
      conditionsRule: {},
      actionsRule: [],
      // 使用一个对象来存储每行的显示状态
      popoverVisibleMap: {},
      conditionTotal: 0,
      curFlag: 0,
      tableParams: [],
      express: false,
      colObjList: [],
      colObjKeyArr: [],
    };
  },
  watch: {
    props_columns: {
      handler: function (val, oldVal) {
        this.columns = this.getCol();
        const list = [];
        this.props_columns.map((item) => {
          if (item.children && item.children.length > 0) {
            item.children.map((items) => {
              list.push(items.dataIndex);
            });
          } else {
            list.push(item.dataIndex);
          }
        });
        this.colObjList = list;
      },
      deep: true,
    },
    props_data: {
      handler: function (val, oldVal) {
        this.cacheData = cloneDeep(val);
        this.data = cloneDeep(val);
      },
      deep: true,
    },
    isEditing: {
      handler: function (val, oldVal) {
        this.columns = this.getCol();
      },
      deep: true,
    },
    props_predefine: {
      handler: function (val, oldVal) {
        if (val) {
          // 处理预定义
          let inReg = /<input.*?">/g;
          let aInRes = val.match(inReg);
          let delReg =
            /<.[^>]+\bclass\s*=\s*['"]([^'"]*(?:el-icon-delete|icon-add|ant-dropdown-trigger|el-icon-edit|icon-validate)[^'"]*)['"][^>]*><\/.*?>/g;
          let inNumReg =
            /<.[^>]+\bclass\s*=\s*['"]([^'"]*\bant-input-number\b[^'"]*)['"][^>]*>.*?<.[^>]+\bclass\s*=\s*['"]([^'"]*\bant-input-number-input-wrap\b[^'"]*)['"][^>]*>.*?<\/div>/g;
          let inCalendarReg =
            /<.[^>]+\bclass\s*=\s*['"]([^'"]*\bant-calendar-picker\b[^'"]*)['"][^>]*>.*?<.[^>]+\bclass\s*=\s*['"]([^'"]*\bant-calendar-picker-input\b[^'"]*)['"][^>]*>.*?<\/div>/g;
          let aInNumRes = val.match(inNumReg);
          let aInCalendarRes = val.match(inCalendarReg);
          val = val.replace(delReg, "");
          val = val.replace(`<span class="el-link--inner">添加</span>`, "");
          val = this.setPreviewVal(aInRes, val, "ant-input");
          val = this.setPreviewVal(aInNumRes, val, "ant-input-number");
          val = this.setPreviewVal(aInCalendarRes, val, "ant-calendar-picker");
          this.rowPredefine = val;
          this.rowHtml = this.rowPredefine + this.rowCondition;
        }
      },
      deep: true,
    }
  },
  mounted() {
    document.addEventListener("click", this.bodyClick);
    // 创建一个容器来放置所有弹出框
    this.popoverContainer = document.createElement('div');
    this.popoverContainer.id = 'rule-preview-popover-container';
    document.body.appendChild(this.popoverContainer);
    // 监听全局事件
    globalEventEmitter.on(CLOSE_POPOVER, this.closePopover);

  },
  methods: {
    /**
     * action: otherwise/otherDel
     * col: colName
     */
    operaChange(obj) {
      const { action, index, col } = obj
      const colKey = col.slice(0, 8);
      const findCol = this.props_columns.find((item) => {
        return item.key === colKey;
      });
      const colChild = findCol.children
      if (action === 'otherwise') {
        delete this.data[index][col + "delElseFlag"]
        delete this.data[index][col + "delReadOnly"]
        this.data[index][col + 'tempElseFlag'] = true;
        if (colChild && colChild.length > 0) {
          for (let ii = 0; ii < colChild.length; ii++) {
            if (!this.data[index][col.slice(0, 8) + ii + 'childtempElseFlag']) {
              delete this.data[index][col.slice(0, 8) + ii + 'childdelElseFlag']
              delete this.data[index][col.slice(0, 8) + ii + 'childdelReadOnly']
              this.data[index][col.slice(0, 8) + ii + 'child'] = null;
              this.data[index][col.slice(0, 8) + ii + 'childtempReadOnly'] = true;
            }
          }
        }
      } else {
        delete this.data[index][col + "tempElseFlag"]

        this.data[index][col + 'delElseFlag'] = true;
        if (colChild && colChild.length > 0) {
          for (let ii = 0; ii < colChild.length; ii++) {
            this.data[index][col.slice(0, 8) + ii + 'childdelReadOnly'] = true;
            delete this.data[index][col.slice(0, 8) + ii + 'childtempReadOnly']
          }
        }
      }
    },
    getDelElseFlag(col, record) {
      let dataIndex = this.props_data.findIndex(
        (item) => item.key === record.key
      );
      if (this.data[dataIndex][col + "delElseFlag"]) {
        return true
      }
      return false
    },
    getRead(col, record) {
      let dataIndex = this.props_data.findIndex(
        (item) => item.key === record.key
      );
      if (this.data[dataIndex][col + "tempReadOnly"] || (this.data[dataIndex][col + "ReadOnly"] && !this.data[dataIndex][col + "delReadOnly"])) {
        return true
      }
      return false
    },
    getDelRead(col, record) {
      let dataIndex = this.props_data.findIndex(
        (item) => item.key === record.key
      );
      if (this.data[dataIndex][col + "delReadOnly"]) {
        return true
      }
      return false
    },
    getElseFlag(col, record) {
      let dataIndex = this.props_data.findIndex(
        (item) => item.key === record.key
      );
      if (this.data[dataIndex][col + "tempElseFlag"] || this.props_data[dataIndex][col + "elseFlag"]) {
        return true;
      }
      return false
    },
    getColChildrenItem(col) {
      if (col[4] !== "2") {
        let findCol = {};
        if (col.includes("child")) {
          const colKey = col.slice(0, 8);
          findCol = this.props_columns.find((item) => {
            return item.key === colKey;
          });
        } else {
          findCol = this.props_columns.find((item) => {
            return item.key === col;
          });
        }
        const index = col.length > 8 ? col.slice(8, -5) : 0;
        const colChildren = findCol.children;
        return colChildren[index]
      }
      return false
    },
    getTableParams(methodParams = [], aExpressParam = [], objVal) {
      const _this = this;
      return methodParams.map((item, index) => {
        const { value = false, valueType } = item;
        if (item.variableType && item.variableType === "expression") {
          const expressionTreeData = item.expressionTreeData;
          if (expressionTreeData && expressionTreeData.params) {
            loopExpressionTreeData(expressionTreeData.params);
          }
          function loopExpressionTreeData(data) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].data && data[i].data.next) {
                const { next } = data[i].data;
                if (next) {
                  const _methodParams = next && next.methodParams;
                  if (_methodParams && _methodParams.length > 0) {
                    _this.express = true;
                    _this.getTableParams(_methodParams, aExpressParam, objVal);
                  } else {
                    const { value = false, valueType } = next;
                    // if (!valueType.includes(".")) {
                    // if (_this.express) {
                    aExpressParam.push(
                      !!(value && value.toString().length > 0)
                    );
                    // }
                    // objVal[index] = !!(value && value.toString().length > 0);
                    // _this.express = false;
                    // return !!(value && value.toString().length > 0);
                    // }
                  }
                }
              } else if (data[i].type === "variable") {
                const _data = data[i].data;
                if (!_data.valueType.includes(".")) {
                  // if (_this.express) {
                  aExpressParam.push(
                    !!(_data.value && _data.value.toString().length > 0)
                  );
                  // }
                  // objVal[index] = aExpressParam;
                  // return !!(_data.value && _data.value.toString().length > 0);
                }
              } else if (data[i].type === "expression") {
                loopExpressionTreeData(data[i].params);
              }
            }
          }
        } else {
          if (!valueType.includes(".")) {
            if (_this.express) {
              aExpressParam.push(!!(value && value.toString().length > 0));
            }
            // objVal[index] = !!(value && value.toString().length > 0);
            _this.express = false;
            aExpressParam = [];
            return !!(value && value.toString().length > 0);
          }
        }
        objVal[index] = aExpressParam;
        aExpressParam = [];
        _this.express = false;
        if (!valueType.includes(".")) {
          return !!(value && value.toString().length > 0);
        }
        return true;
      });
    },
    bodyClick(e) {
      // 如果点击的不是序号和不是弹出框内容，则关闭所有弹出框
      if (e.target.id !== "popoverSpanRule" && !e.target.closest('.custom-popover')) {
        Object.keys(this.popoverVisibleMap).forEach(key => {
          if (this.popoverVisibleMap[key]) {
            this.closeCustomPopover(key);
          }
        });
      }
    },
    closePopover() {
      // 关闭所有弹出框
      Object.keys(this.popoverVisibleMap).forEach(key => {
        this.closeCustomPopover(key);
      });
      this.conditionTotal = 0;
      this.curFlag = 0;
    },
    // 预定义数据
    setPreviewVal(data, val, classN) {
      let minusLength = classN === "ant-input-number" ? 7 : 1;
      let divStr = classN === "ant-input-number" ? "</div>" : "";
      if (data) {
        for (let i = 0; i < data.length; i++) {
          if (
            data[i].indexOf(classN) !== -1 &&
            (data[i].indexOf("data-val") !== -1 ||
              data[i].indexOf("aria-valuenow") !== -1)
          ) {
            let newItem = "";
            let itemValStr =
              data[i].match(/data-val=".*?"/g) ||
              data[i].match(/aria-valuenow=".*?"/g);
            let itemVal = itemValStr[0].substring(
              itemValStr[0].indexOf("=") ? itemValStr[0].indexOf("=") + 2 : 10,
              itemValStr[0].length - 1
            );
            if (classN === "ant-calendar-picker") {
              let inReg = /<input.*?>/g;
              let aInRes = data[i].match(inReg);
              let tempVal = `${aInRes[0].substring(
                0,
                aInRes[0].length - minusLength
              )} value="${itemVal}" readonly>${divStr}`;
              newItem = data[i].replace(aInRes[0], tempVal);
            } else {
              newItem = `${data[i].substring(
                0,
                data[i].length - minusLength
              )} value="${itemVal}" readonly>${divStr}`;
            }
            val = val.replace(data[i], newItem);
          }
        }
      }
      return val;
    },
    // 动作值
    setActionInfo(e, record) {
      this.closePopover()
      this.rowInfo = record;
      // 触发预览内容更新
      this.showPreview();
      
      // 添加延迟等待数据更新
      setTimeout(() => {
        this.popoverVisibleMap[record.key] = true;
        this.$nextTick(() => {
          this.renderPopover(record.key, e.target);
        });
      }, 100); // 小延迟等待预定义数据加载
    },
    // 渲染弹出框到body
    renderPopover(key, targetElement) {
      // 清理之前的弹出框
      const oldPopover = document.querySelector(`.custom-popover-${key}`);
      if (oldPopover) {
        oldPopover.remove();
      }

      // 只有在需要显示时才创建
      if (!this.popoverVisibleMap[key]) return;

      // 创建弹出框元素
      const popoverDiv = document.createElement('div');
      popoverDiv.className = `custom-popover custom-popover-${key}`;
      popoverDiv.style.cssText = `
        position: absolute;
        z-index: 999999;
        width: 800px;
        max-height: 400px;
        background: white;
        border: 1px solid #eee;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        border-radius: 4px;
        padding: 12px;
        text-align: left;
        overflow: auto;
      `;

      // 创建内容容器
      const contentDiv = document.createElement('div');
      contentDiv.className = 'rule-preview-popover';
      contentDiv.style.cssText = `
        height: 100%;
        min-width: 100%;
        display: inline-block;
      `;
      
      // 添加内联样式以优化预览显示
      const styleEl = document.createElement('style');
      styleEl.textContent = `
        .custom-popover .pre-rule-content {
          margin: 3px 0;
          padding: 5px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex !important;
          align-items: center;
        }
        .custom-popover .common-highlight {
          display: inline-flex !important;
          align-items: center;
        }
        .custom-popover .pre-title {
          display: inline-flex !important;
          margin: 0 !important;
          padding: 0 !important;
          color: #000090;
          font-weight: bold;
          white-space: nowrap;
        }
        .custom-popover .pre-content {
          display: inline-flex !important;
          align-items: center;
          white-space: nowrap;
        }
        .custom-popover .pre-rule-content > div {
          display: inline-flex !important;
          white-space: nowrap;
          margin: 0 !important;
          padding: 0 !important;
        }
        .custom-popover .predefineView {
          padding: 5px;
          margin-bottom: 0;
          margin-top: 0;
        }
        .custom-popover .structuralWord {
          font-weight: bold;
          margin: 8px 0;
          color: #000090;
        }
        .custom-popover .editorBody > div:not(.predefineView) {
          margin-top: 0 !important;
          padding-top: 0 !important;
        }
        .custom-popover * {
          writing-mode: horizontal-tb !important;
          text-orientation: mixed !important;
        }
        /* 减少预定义和如果之间的空白 */
        .custom-popover .editorBody {
          display: flex;
          flex-direction: column;
          gap: 0;
        }
        .custom-popover .editorBody > div {
          margin: 0 !important;
          padding: 0 !important;
        }
        .custom-popover .structuralWord {
          margin: 5px 0 !important;
        }
      `;
      
      // 使用完整的HTML内容，确保预定义部分显示
      contentDiv.innerHTML = `
        <div class="eRuleEditorContainer oneDark_Light">
          <div class="eRuleEditor">
            <div class="editorBody" style="margin:0;padding:0;">
              <div class="predefineView" style="margin:0;padding:5px 5px 0 5px;">
                ${this.rowPredefine || '<div style="color: #999;">无预定义内容</div>'}
              </div>
              <div style="margin:0;padding:0 5px 5px 5px;">
                ${this.rowCondition || ''}
              </div>
            </div>
          </div>
        </div>
      `;

      // 组装弹出框
      popoverDiv.appendChild(styleEl);
      popoverDiv.appendChild(contentDiv);

      // 添加到body
      this.popoverContainer.appendChild(popoverDiv);

      // 定位弹出框
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const popoverHeight = popoverDiv.offsetHeight;
        
        // 判断是否靠近底部(距离底部小于弹框高度),如果是则向上弹出
        const isNearBottom = (viewportHeight - rect.bottom) < popoverHeight;
        
        if(isNearBottom) {
          // 向上弹出,设置底部位置
          popoverDiv.style.top = `${rect.top - popoverHeight - 5}px`;
        } else {
          // 向下弹出,设置顶部位置 
          popoverDiv.style.top = `${rect.bottom + window.scrollY + 5}px`;
        }
        
        popoverDiv.style.left = `${rect.left + window.scrollX}px`;
      }
      
      // 增加处理：在弹出框显示后，调整内部元素为行内显示
      setTimeout(() => {
        // 找到弹出框
        const popover = document.querySelector(`.custom-popover-${key}`);
        if (!popover) return;

        // 处理所有"之中"元素
        const allSpans = popover.querySelectorAll('span');
        for (let i = 0; i < allSpans.length; i++) {
          const span = allSpans[i];
          if (span.textContent === '之中') {
            // 获取前一个元素
            const prev = span.previousElementSibling;
            if (prev && prev.tagName.toLowerCase() === 'span') {
              // 合并内容
              prev.textContent += '之中';
              // 隐藏当前元素
              span.style.display = 'none';
            }
          }
        }
        
        // 隐藏所有"满足条件"文字，但保留其后的内容
        const conditions = popover.querySelectorAll('.pre-title');
        for (let i = 0; i < conditions.length; i++) {
          // 找到包含"满足条件"文字的元素
          const titleTexts = conditions[i].childNodes;
          for (let j = 0; j < titleTexts.length; j++) {
            const node = titleTexts[j];
            // 如果是文本节点且包含"满足条件"
            if (node.nodeType === 3 && node.textContent.trim() === '满足条件') {
              node.textContent = ''; // 清空文本内容
            }
            // 如果是span且内容为"满足条件"
            if (node.nodeType === 1 && node.tagName.toLowerCase() === 'span' && node.textContent.trim() === '满足条件') {
              node.style.display = 'none'; // 隐藏该元素
            }
          }
          // 调整元素样式，使其后续内容直接跟随定义部分
          conditions[i].style.display = 'inline-flex';
          conditions[i].style.marginLeft = '0';
          conditions[i].style.marginRight = '0';
          conditions[i].style.paddingLeft = '0';
        }
        
        // 处理预定义和如果之间的空白
        const predefineView = popover.querySelector('.predefineView');
        const ifSection = popover.querySelector('.structuralWord');
        
        if (predefineView && ifSection) {
          // 移除预定义和如果之间的空白元素
          let currentNode = predefineView.nextSibling;
          while (currentNode && !(currentNode.classList && currentNode.classList.contains('structuralWord'))) {
            // 如果是空白文本节点或无内容的元素，移除它
            if (
              (currentNode.nodeType === 3 && !currentNode.textContent.trim()) || 
              (currentNode.nodeType === 1 && !currentNode.textContent.trim() && !currentNode.querySelector('*'))
            ) {
              const nodeToRemove = currentNode;
              currentNode = currentNode.nextSibling;
              nodeToRemove.parentNode.removeChild(nodeToRemove);
            } else {
              // 如果不是空白，则减少其外边距
              if (currentNode.nodeType === 1) {
                currentNode.style.margin = '0';
                currentNode.style.padding = '0';
              }
              currentNode = currentNode.nextSibling;
            }
          }
          
          // 调整如果部分的上边距
          if (ifSection) {
            ifSection.style.marginTop = '0';
          }
        }
      }, 0);
    },
    // 添加新方法: 格式化预览内容使其更紧凑
    formatPreviewContent(htmlContent) {
      let content = htmlContent;
      
      // 将"满足条件"替换为空字符串
      content = content.replace(
        /<div\s+class="pre-title"[^>]*>\s*<span[^>]*>满足条件<\/span>/g,
        '<div class="pre-title" style="display:inline-flex;margin:0;padding:0;">'
      );
      
      // 将"满足条件"文本替换为空字符串
      content = content.replace(
        /<div\s+class="pre-title"[^>]*>\s*满足条件/g,
        '<div class="pre-title" style="display:inline-flex;margin:0;padding:0;">'
      );
      
      // 处理"之中"文本 - 将带有"之中"的span替换为空
      content = content.replace(
        /<span[^>]*>\s*之中\s*<\/span>/g,
        ''
      );
      
      // 处理直接包含"之中"的文本节点
      content = content.replace(
        /(\S+)\s*之中/g,
        '$1'
      );
      
      // 处理满足条件后的关闭div标签
      content = content.replace(
        /<\/div>\s*(?=<div|$)/g,
        '</span>'
      );
      
      // 移除可能导致换行的多余空白
      content = content.replace(/>\s+</g, '> <');
      
      // 减少预定义和如果之间的空白
      content = content.replace(
        /<\/div>\s*<div\s+class="predefineView"[^>]*>([\s\S]*?)<\/div>\s*<div[^>]*>([\s\S]*?)<div\s+class="structuralWord"[^>]*>/g,
        '</div><div class="predefineView" style="margin:0;padding:0;">$1</div><div style="margin:0;padding:0;">$2<div class="structuralWord" style="margin-top:0;">'
      );
      
      return content;
    },
    // 关闭自定义弹出框
    closeCustomPopover(key) {
      this.popoverVisibleMap[key] = false;
      // 移除DOM元素
      const popoverDiv = document.querySelector(`.custom-popover-${key}`);
      if (popoverDiv) {
        popoverDiv.remove();
      }
    },
    // 规则预览
    showPreview() {
      this.conditionTotal = 0;
      this.curFlag = 0;
      this.$emit("getPredefine");
      this.getRule();
    },
    // 获取规则
    getRule() {
      let itemList =
        store.getters.listMap[this.ruleUuid].initModelData.firstFieldItemList;
      let dictMap = store.getters.listMap[this.ruleUuid].dictMap;
      let operatorList =
        store.getters.listMap[this.ruleUuid].initModelData.operatorTypeList;
      let aConItem = [];
      let aActItem = [];
      let conStr = "";
      let actStr = "";
      for (let i = 0; i < this.props_columns.length; i++) {
        if (this.props_columns[i].colType === "condition") {
          // 条件部分
          getConditionItem(
            this.props_columns[i].properties.rule.children[0].ruleCondition
              .contents,
            this.props_columns[i].key,
            this.props_columns[i].children
          );
        }
        if (this.props_columns[i].colType === "action") {
          this.curFlag = 0;
          let ruleD = this.props_columns[i].properties.rule[0];
          let propColChildren = [];
          let paramChildren = [];
          propColChildren =
            this.props_columns[i] && this.props_columns[i].children;
          if (ruleD.actionType === "invokeMethod") {
            let allNoVal = [];
            getParChildren(ruleD.actionParams[0]);
            if (
              propColChildren.length > 0 &&
              propColChildren.length === paramChildren.length
            ) {
              for (let u = 0; u < propColChildren.length; u++) {
                if (this.rowInfo[propColChildren[u].key]) {
                  allNoVal.push(true);
                } else {
                  allNoVal.push(false);
                }
              }
            }
            if (
              allNoVal.length > 0 &&
              !allNoVal.find((item) => {
                return item === true;
              })
            ) {
              continue;
            }
          }
          function getParChildren(data) {
            if (
              data.value &&
              Array.isArray(data.value) &&
              data.value.length > 0
            ) {
              if (data.methodParams) {
                paramChildren = data.methodParams;
              }
              if (data.next) {
                getParChildren(data.next);
              }
              return;
            }
          }
          // 动作部分
          getActionItem(
            this.props_columns[i].properties.rule[0],
            this.props_columns[i].key,
            this.props_columns[i].children
          );
        }
      }
      // 条件部分
      function getConditionItem(data, key, children) {
        const { variable, comparator, conditionValueType } = data;
        if (variable) {
          getVariableItemLabel(itemList, variable, key);
        }
        if (comparator && conditionValueType) {
          getComparatorItemLabel(
            operatorList[conditionValueType],
            comparator,
            key,
            children
          );
        }
      }
      // 动作部分
      function getActionItem(data, key, children) {
        const { actionType, actionParams } = data;
        if (actionType === "invokeMethod") {
          getInvokeMethodItemLabel(itemList, actionParams[0], key, children);
        }
        if (actionType === "setValue") {
          getSetValueItemLabel(itemList, actionParams[0], key, children);
          let oItem = aActItem.find((item) => item.key === key);
          let iOItem = {
            label: `等于(<${getTxt(actionParams[1].valueType)}>)`,
            variableType: actionParams[1].variableType,
            type: "setValue",
            typeRight: true,
          };
          if (oItem) {
            oItem.data.push(iOItem);
          } else {
            aActItem.push({
              key,
              data: [iOItem],
            });
          }
        }
      }
      function getTxt(type) {
        if (!type) {
          return "";
        }
        switch (type) {
          case "Date":
            return "日期";
          case "Short":
          case "Integer":
          case "Float":
          case "Double":
          case "BigDecimal":
          case "Long":
            return "数字";
          case "Boolean":
            return "布尔值";
          default:
            return "字符串";
        }
      }
      // comparator
      function getComparatorItemLabel(list, data, key, children) {
        if (data.value && Array.isArray(data.value) && data.value.length > 0) {
          for (let j in list) {
            if (list[j].value === data.value[0]) {
              let oItem = aConItem.find((item) => item.key === key);
              let iOItem = { label: list[j].label, type: "comparator" };
              if (children) {
                iOItem.children = children.map((item) => {
                  return item.key;
                });
              }
              if (oItem) {
                oItem.data.push(iOItem);
              } else {
                aConItem.push({
                  key,
                  data: [iOItem],
                });
              }
              return;
            }
          }
        }
      }
      // variable
      function getVariableItemLabel(list, data, key, symbol, bracket, aCPar) {
        const aType = [
          "Short",
          "Integer",
          "Long",
          "Float",
          "Double",
          "BigDecimal",
          "Number",
          "String",
        ];
        if (data.variableType === "constant" && data.value !== undefined) {
          let oItem = aConItem.find((item) => item.key === key);
          let iOItem = {
            label: `<span style="color:rgb(65,8,246);font-weight:bold">(${data.value})</span>`,
            variableType: data.variableType,
            type: "variable",
          };
          symbol && aType.includes(data.valueType) && (iOItem.symbol = symbol);
          bracket && (iOItem.bracket = bracket);
          if (!aCPar) {
            if (oItem) {
              oItem.data.push(iOItem);
            } else {
              aConItem.push({
                key,
                data: [iOItem],
              });
            }
          }
          if (aCPar) {
            let _oItem = aCPar.find((item) => item.key === key);
            if (_oItem) {
              _oItem.data.push(iOItem);
            } else {
              aCPar.push({
                key,
                data: [iOItem],
              });
            }
          }
        } else if (
          data.value &&
          Array.isArray(data.value) &&
          data.value.length > 0
        ) {
          for (let j in list) {
            if (
              list[j].valueType === data.valueType &&
              list[j].value === data.value[0]
            ) {
              let oItem = aConItem.find((item) => item.key === key);
              let iOItem = {
                label: list[j].label,
                variableType: data.variableType,
                type: "variable",
              };
              symbol &&
                aType.includes(data.valueType) &&
                (iOItem.symbol = symbol);
              bracket && (iOItem.bracket = bracket);
              if (data.methodParams) {
                iOItem.methodParams = data.methodParams;
              }
              if (!aCPar) {
                if (oItem) {
                  oItem.data.push(iOItem);
                } else {
                  aConItem.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (aCPar) {
                let _oItem = aCPar.find((item) => item.key === key);
                if (_oItem) {
                  _oItem.data.push(iOItem);
                } else {
                  aCPar.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (data.next) {
                getVariableItemLabel(
                  list[j].children,
                  data.next,
                  key,
                  symbol,
                  bracket,
                  aCPar
                );
              }
              return;
            }
            // else if (list[j].children) {
            //   getVariableItemLabel(
            //     list[j].children,
            //     data,
            //     key,
            //     symbol,
            //     bracket,
            //     aCPar
            //   );
            // }
          }
        } else if (data.variableType === "expression") {
          let { expressionTreeData } = data;
          if (expressionTreeData && expressionTreeData.params) {
            loopExpressionTreeData(
              expressionTreeData.params,
              expressionTreeData,
              bracket,
              aCPar
            );
          }
          function loopExpressionTreeData(data, parD, bracket, aCPar) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].type === "variable") {
                getVariableItemLabel(
                  list,
                  data[i].data,
                  key,
                  i < data.length - 1 ? parD.symbols[i] : null,
                  bracket,
                  aCPar
                );
              } else if (data[i].type === "expression") {
                loopExpressionTreeData(data[i].params, data[i], true, aCPar);
              }
            }
          }
        }
      }
      // invokeMethod
      function getInvokeMethodItemLabel(list, data, key, children, aPar) {
        if (data.value && Array.isArray(data.value) && data.value.length > 0) {
          for (let j in list) {
            if (
              list[j].valueType === data.valueType &&
              list[j].value === data.value[0]
            ) {
              let oItem = aActItem.find((item) => item.key === key);
              let iOItem = {
                label: list[j].label,
                variableType: data.variableType,
                type: "invokeMethod",
              };
              if (children && data.variableType === "method") {
                iOItem.children = children;
              }
              if (data.methodParams) {
                iOItem.methodParams = data.methodParams;
              }
              if (!aPar) {
                if (oItem) {
                  oItem.data.push(iOItem);
                } else {
                  aActItem.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (aPar) {
                let _oItem = aPar.find((item) => item.key === key);
                if (_oItem) {
                  _oItem.data.push(iOItem);
                } else {
                  aPar.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (data.next) {
                getInvokeMethodItemLabel(
                  list[j].children,
                  data.next,
                  key,
                  children,
                  aPar
                );
              }
              return;
            } else if (list[j].children) {
              getInvokeMethodItemLabel(
                list[j].children,
                data,
                key,
                children,
                aPar
              );
            }
          }
        }
      }
      // setValue
      function getSetValueItemLabel(list, data, key, children, aPar) {
        if (data.value && Array.isArray(data.value) && data.value.length > 0) {
          for (let j in list) {
            if (
              list[j].valueType === data.valueType &&
              list[j].value === data.value[0]
            ) {
              let oItem = aActItem.find((item) => item.key === key);
              let iOItem = {
                label: list[j].label,
                variableType: data.variableType,
                type: "setValue",
              };
              if (children && data.variableType === "method") {
                iOItem.children = children;
              }
              if (data.methodParams) {
                iOItem.methodParams = data.methodParams;
              }
              if (!aPar) {
                if (oItem) {
                  oItem.data.push(iOItem);
                } else {
                  aActItem.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (aPar) {
                let _oItem = aPar.find((item) => item.key === key);
                if (_oItem) {
                  _oItem.data.push(iOItem);
                } else {
                  aPar.push({
                    key,
                    data: [iOItem],
                  });
                }
              }
              if (data.next) {
                getSetValueItemLabel(
                  list[j].children,
                  data.next,
                  key,
                  children,
                  aPar
                );
              }
              return;
            } else if (list[j].children) {
              getSetValueItemLabel(list[j].children, data, key, children, aPar);
            }
          }
        }
      }
      conditionConcat(aConItem, this);
      // condition
      function conditionConcat(aData, _this, parConcat) {
        _this.conditionTotal = 0;
        for (let i = 0; i < aData.length; i++) {
          // 值非完全空
          const dataC = aData[i].data;
          const colV = _this.rowInfo[aData[i].key];
          let isCon = false;
          let aComVal = [];
          let isCom = false;
          let compChildren = [];
          if (
            dataC[dataC.length - 1] &&
            dataC[dataC.length - 1].type === "comparator" &&
            dataC[dataC.length - 1].children
          ) {
            compChildren = dataC[dataC.length - 1].children;
          }
          // 始终显示规则行，不管是否有值
          isCon = true;

          // 仍然计算是否有子条件值（用于其他逻辑）
          if (
            dataC[dataC.length - 1] &&
            dataC[dataC.length - 1].type === "comparator" &&
            dataC[dataC.length - 1].children
          ) {
            for (
              let comi = 0;
              comi < dataC[dataC.length - 1].children.length;
              comi++
            ) {
              if (
                _this.rowInfo[dataC[dataC.length - 1].children[comi]] !==
                "false" &&
                _this.rowInfo[dataC[dataC.length - 1].children[comi]] !== 0 &&
                _this.rowInfo[dataC[dataC.length - 1].children[comi]]
              ) {
                aComVal.push(true);
              } else {
                aComVal.push(false);
              }
            }
          }
          if (
            aComVal.find((item) => {
              return item === true;
            })
          ) {
            isCom = true;
          } else {
            isCom = false;
          }
          if (isCon) {
            _this.conditionTotal++;
            let variableStr = "";
            if (aData[i].data && aData[i].data.length > 0) {
              for (let j = 0; j < aData[i].data.length; j++) {
                let joinStr = "";
                let labelStr = aData[i].data[j].label;
                if (aData[i].data[j].symbol) {
                  joinStr = `<span style="color:#000;font-weight:normal;margin:0 2px">${aData[i].data[j].symbol}</span>`;
                } else if (
                  aData[i].data[j + 1] &&
                  aData[i].data[j + 1].variableType !== "method" &&
                  aData[i].data[j + 1].variableType !== "Boolean" &&
                  aData[i].data[j + 1].type !== "comparator"
                ) {
                  joinStr = `<span class="joiner">的</span>`; 
                }
                if (aData[i].data[j].bracket) {
                  if (aData[i].data[j - 1] && !aData[i].data[j - 1].bracket) {
                    labelStr = `<span style="margin: 0 2px;font-weight:bold">(</span>${labelStr}`;
                  }
                  if (aData[i].data[j + 1] && !aData[i].data[j + 1].bracket) {
                    labelStr = `${labelStr}<span style="margin: 0 2px;font-weight:bold">)</span>`;
                  }
                }
                if (aData[i].data[j].type === "variable") {
                  if (aData[i].data[j].methodParams) {
                    let paramReg = /\(#.*?\)/g;
                    let aLabelStr = labelStr.match(paramReg);
                    let aCParTemp = [];
                    // methodParams value
                    for (
                      let parI = 0;
                      parI < aData[i].data[j].methodParams.length;
                      parI++
                    ) {
                      getVariableItemLabel(
                        itemList,
                        aData[i].data[j].methodParams[parI],
                        aData[i].key,
                        null,
                        null,
                        aCParTemp
                      );
                      let parCon = conditionConcat(aCParTemp, _this, true);
                      if (parCon) {
                        labelStr = labelStr.replace(
                          aLabelStr[parI],
                          parCon ? `(${parCon})` : ""
                        );
                      }
                      aCParTemp = [];
                    }
                  }

                  if (j === 0) {
                    let connectStr = `<span style="width:20px;display:inline-block"></span>`;
                    if (i !== 0 && _this.conditionTotal > 1) {
                      connectStr = `<span style="color:rgb(65,8,246);font-weight:bold;width:20px;display:inline-block">且</span>`;
                    }
                    if (parConcat) {
                      variableStr = `<span class="variableCom"><span><span class="fieldCom"><span class="pointer"><span class="textItem">${labelStr}</span></span>${joinStr}<span><next></span></span></span></span>`;
                    } else {
                      variableStr = `<div class="condition">${connectStr}<div class="LeftValueCom"><span class="variableCom"><span><span class="fieldCom"><span class="pointer"><span class="textItem">${labelStr}</span></span>${joinStr}<span><next></span></span></span></span></div><comparator></div>`;
                    }
                  } else if (aData[i].data[j].variableType === "method") {
                    if (aData[i].data[j].symbol) {
                      variableStr = variableStr.replace(
                        "<next>",
                        `<span class="variableCom"><span><span class="methodCom"><span class="pointer"><span class="textItem" style="font-weight:bold">${labelStr}</span></span><span style="color:#000;font-weight:normal;margin:0 2px">${aData[i].data[j].symbol}</span><span><next></span></span></span></span>`
                      );
                    } else {
                      variableStr = variableStr.replace(
                        "<next>",
                        `<span class="variableCom"><span><span class="methodCom"><span class="pointer"><span class="textItem" style="font-weight:bold">${labelStr}</span></span>${joinStr}<span><next></span></span></span></span>`
                      );
                    }
                  } else {
                    variableStr = variableStr.replace(
                      "<next>",
                      `<span class="variableCom"><span><span class="fieldCom"><span class="pointer"><span class="textItem">${labelStr}</span></span>${joinStr}<span><next></span></span></span></span>`
                    );
                  }
                }
                if (aData[i].data[j].type === "comparator") {
                  let paramReg = /\(#.*?\)/g;
                  let labelStr = aData[i].data[j].label;
                  let aLabelStr = labelStr.match(paramReg);

                  if (aData[i].data[j].children) {
                    aData[i].data[j].children.map((item, index) => {
                      let colType = _this.colType(item);
                      let rowItem = _this.rowInfo[item];
                      if (colType === "Enum" || colType === "EnumMultiple") {
                        let enumName = _this.colTypeEnumName(item);
                        let aEnu = dictMap[enumName];
                        if (aEnu && aEnu.length > 0 && rowItem) {
                          let tempItem = aEnu.find((item) => {
                            return item.value === rowItem;
                          });
                          tempItem && (rowItem = tempItem.viewName);
                        }
                      }
                      labelStr = labelStr.replace(
                        aLabelStr[index],
                        rowItem || rowItem === 0 ? `(${rowItem})` : "(     )"
                      );
                    });
                  } else {
                    let colType = _this.colType(aData[i].key);
                    let rowItem = _this.rowInfo[aData[i].key];
                    if (colType === "Enum" || colType === "EnumMultiple") {
                      let enumName = _this.colTypeEnumName(aData[i].key);
                      let aEnu = dictMap[enumName];
                      if (aEnu && aEnu.length > 0 && rowItem) {
                        let tempItem = aEnu.find((item) => {
                          return item.value === rowItem;
                        });
                        tempItem && (rowItem = tempItem.viewName);
                      }
                    }
                    labelStr = labelStr.replace(
                      aLabelStr[0],
                      rowItem || rowItem === 0 ? `(${rowItem})` : ""
                    );
                  }
                  variableStr = variableStr.replace(
                    "<comparator>",
                    `<div class="operator"><span style="color:rgb(65,8,246)">${labelStr}</span></div>`
                  );
                }
              }
              variableStr = variableStr.replace("<span><next></span>", "");
              if (!parConcat) {
                conStr += variableStr;
              }
              if (parConcat) {
                return variableStr;
              }
            }
          }
        }
      }
      actionConcat(aActItem, this);
      // action
      function actionConcat(aData, _this, parConcat) {
        for (let k = 0; k < aData.length; k++) {
          let invokeStr = "";
          if (aData[k].data && aData[k].data.length > 0) {
            for (let l = 0; l < aData[k].data.length; l++) {
              _this.curFlag = 0;
              if (aData[k].data[l].type === "invokeMethod") {
                let joinStr2 = "";
                if (
                  aData[k].data[l + 1] &&
                  aData[k].data[l + 1].variableType !== "method" &&
                  aData[k].data[l + 1].variableType !== "Boolean" &&
                  !aData[k].data[l + 1].typeRight
                ) {
                  joinStr2 = `<span class="joiner">的</span>`;
                }
                if (l === 0) {
                  if (parConcat) {
                    invokeStr = `<span class="variableCom"><span class="fieldCom"><span class="txtItem">${aData[k].data[l].label}</span></span>${joinStr2}<next></span>`;
                  } else {
                    invokeStr = `<div class="action"><span class="actionTypeCom"><span class="actionTypeItem txtItem">调用方法</span></span><span class="actionMethod"><span class="variableCom"><span class="fieldCom"><span class="txtItem">${aData[k].data[l].label}</span></span><next></span></span></div>`;
                  }
                } else if (aData[k].data[l].variableType === "method") {
                  let paramReg = /\(#.*?\)/g;
                  let labelStr = aData[k].data[l].label;
                  let aLabelStr = labelStr.match(paramReg);
                  if (aData[k].data[l].methodParams) {
                    let aParTemp = [];
                    // methodParams value
                    for (
                      let parI = 0;
                      parI < aData[k].data[l].methodParams.length;
                      parI++
                    ) {
                      if (aData[k].data[l].methodParams[parI].value) {
                        if (
                          Array.isArray(
                            aData[k].data[l].methodParams[parI].value
                          )
                        ) {
                          // aPar  temp
                          getInvokeMethodItemLabel(
                            itemList,
                            aData[k].data[l].methodParams[parI],
                            aData[k].key,
                            aData[k].data[l].children,
                            aParTemp
                          );
                          let parCon = actionConcat(aParTemp, _this, true);
                          if (parCon) {
                            labelStr = labelStr.replace(
                              aLabelStr[parI],
                              parCon ? `(${parCon})` : ""
                            );
                          }
                          aParTemp = [];
                        } else {
                          let rowItem =
                            aData[k].data[l].methodParams[parI].value;
                          if (
                            aData[k].data[l].methodParams[parI].enumDictName
                          ) {
                            let aEnu =
                              dictMap[
                              aData[k].data[l].methodParams[parI].enumDictName
                              ];
                            if (aEnu && aEnu.length > 0 && rowItem) {
                              let tempItem = aEnu.find((item) => {
                                return item.value === rowItem;
                              });
                              tempItem && (rowItem = tempItem.viewName);
                            }
                          }
                          labelStr = labelStr.replace(
                            aLabelStr[parI],
                            `<span style="color:rgb(0,128,0)">(${rowItem})</span>`
                          );
                        }
                      } else {
                        // let iKey = aData[k].data[l].children
                        //   ? aData[k].data[l].children.find((item) => {
                        //       return (
                        //         item.key ===
                        //         `${aData[k].key.toString() + parI}child`
                        //       );
                        //     })
                        //     ? aData[k].data[l].children.find((item) => {
                        //         return (
                        //           item.key ===
                        //           `${aData[k].key.toString() + parI}child`
                        //         );
                        //       }).dataIndex
                        //     : aData[k].key
                        //   : aData[k].key;
                        let iKey = "";
                        if (aData[k].data[l].children) {
                          iKey = aData[k].data[l].children[_this.curFlag]
                            ? aData[k].data[l].children[_this.curFlag].dataIndex
                            : aData[k].key;
                          _this.curFlag++;
                        } else {
                          iKey = aData[k].key;
                        }
                        // let iKey = aData[k].data[l].children
                        //   ? aData[k].data[l].children.find((item) => {
                        //       return (
                        //         item.key ===
                        //         `${aData[k].key.toString() + parI}child`
                        //       );
                        //     })
                        //     ? aData[k].data[l].children.find((item) => {
                        //         return (
                        //           item.key ===
                        //           `${aData[k].key.toString() + parI}child`
                        //         );
                        //       }).dataIndex
                        //     : aData[k].key
                        //   : aData[k].key;

                        let colType = _this.colType(iKey);
                        let rowItem = _this.rowInfo[iKey];
                        if (colType === "Enum" || colType === "EnumMultiple") {
                          let enumName = _this.colTypeEnumName(iKey);
                          let aEnu = dictMap[enumName];
                          if (aEnu && aEnu.length > 0 && rowItem) {
                            let tempItem = aEnu.find((item) => {
                              return item.value === rowItem;
                            });
                            tempItem && (rowItem = tempItem.viewName);
                          }
                        }
                        labelStr = labelStr.replace(
                          aLabelStr[parI],
                          rowItem
                            ? `<span style="color:rgb(0,128,0)">(${rowItem})</span>`
                            : `<span style="color:rgb(0,128,0)">(    )</span>`
                        );
                      }
                    }
                  }
                  if (!aData[k].data[l].children) {
                    let repIndex = -1;
                    if (aData[k].data[l] && aData[k].data[l].methodParams) {
                      repIndex = aData[k].data[l].methodParams.findIndex(
                        (item) => {
                          return item.value === undefined;
                        }
                      );
                    }
                    if (repIndex !== -1) {
                      labelStr = labelStr.replace(
                        aLabelStr[repIndex],
                        _this.rowInfo[aData[k].key]
                          ? `<span style="color:rgb(0,128,0)">(${_this.rowInfo[aData[k].key]
                          })</span>`
                          : `<span style="color:rgb(0,128,0)">()</span>`
                      );
                    }
                  }
                  invokeStr = invokeStr.replace(
                    "<next>",
                    `<span class="variableCom"><span><span class="methodCom"><span class="pointer"><span class="textItem" style="font-weight:bold">${labelStr}</span></span></span></span></span>`
                  );
                } else {
                  if (parConcat) {
                    invokeStr = invokeStr.replace(
                      "<next>",
                      `<span class="variableCom"><span class="fieldCom"><span class="txtItem">${aData[k].data[l].label}</span></span>${joinStr2}<next></span>`
                    );
                  } else {
                    invokeStr = invokeStr.replace(
                      "<next>",
                      `<span class="variableCom"><span class="fieldCom"><span class="txtItem">${aData[k].data[l].label}</span></span><next></span>`
                    );
                  }
                }
              }
              if (aData[k].data[l].type === "setValue") {
                let joinStr = "";
                if (
                  aData[k].data[l + 1] &&
                  aData[k].data[l + 1].variableType !== "method" &&
                  aData[k].data[l + 1].variableType !== "Boolean" &&
                  !aData[k].data[l + 1].typeRight
                ) {
                  joinStr = `<span class="joiner">的</span>`;
                }
                if (l === 0) {
                  if (!_this.rowInfo[aData[k].key]) {
                    continue;
                  }
                  invokeStr = `<div class="action"><span class="actionTypeCom"><span class="actionTypeItem txtItem">设置</span></span><span class="actionMethod"><span class="variableCom"><span class="fieldCom"><span class="txtItem" style="margin:0">${aData[k].data[l].label}</span></span>${joinStr}<next></span></span></div>`;
                } else if (aData[k].data[l].typeRight) {
                  let paramReg = /<.*?>/g;
                  let labelStr = aData[k].data[l].label;
                  let aLabelStr = labelStr.match(paramReg);
                  let colType = _this.colType(aData[k].key);
                  let rowItem = _this.rowInfo[aData[k].key];
                  if (colType === "Enum" || colType === "EnumMultiple") {
                    let enumName = _this.colTypeEnumName(aData[k].key);
                    let aEnu = dictMap[enumName];
                    if (aEnu && aEnu.length > 0 && rowItem) {
                      let tempItem = aEnu.find((item) => {
                        return item.value === rowItem;
                      });
                      tempItem && (rowItem = tempItem.viewName);
                    }
                  }
                  labelStr = labelStr.replace(
                    aLabelStr[0],
                    rowItem
                      ? `<span style="color:rgb(0,128,0)">${rowItem}</span>`
                      : ""
                  );
                  invokeStr = invokeStr.replace(
                    "<next>",
                    `<span class="variableCom"><span><span class="methodCom"><span class="pointer"><span class="textItem">${labelStr}</span></span></span></span></span>`
                  );
                } else {
                  invokeStr = invokeStr.replace(
                    "<next>",
                    `<span class="variableCom"><span class="fieldCom"><span class="txtItem" style="margin:0">${aData[k].data[l].label}</span></span>${joinStr}<next></span>`
                  );
                }
              }
            }
            invokeStr = invokeStr.replace("<span><next></span>", "");
            if (!parConcat) {
              actStr += invokeStr;
            }
            if (parConcat) {
              return invokeStr;
            }
          }
        }
      }
      this.rowCondition = `<p class="structuralWord"><span>如果</span></p><div class="conditionCom">${conStr}</div><p class="structuralWord" style="margin-top:15px"><span>那么</span></p><div class="actionCom" style="margin-bottom:15px">${actStr}</div>`;
      this.rowHtml = this.rowPredefine + this.rowCondition;
    },
    customRowProps(record) {
      var that = this;
      return {
        // 事件
        onDblclick: (event) => {
          that.edit(record.key);
        }
      };
    },
    colTypeEnumName(key) {
      let findCol = {};
      if (key.includes("child")) {
        const colKey = key.slice(0, 8);
        findCol = this.columns.find((item) => {
          return item.key === colKey;
        });
      } else {
        findCol = this.columns.find((item) => {
          return item.key === key;
        });
      }
      if (!findCol) return "";

      if (key[4] !== "2") {
        const { complexSelect, allCellUnit } = findCol?.properties?.rule;
        // if (complexSelect) {
        //   return "boolean";
        // } else {
        const { children } = findCol;
        const contents =
          findCol.properties.rule.children[0]?.ruleCondition?.contents;
        const comparator = contents?.comparator || {};
        if (children && children.length > 0) {
          const keyChilItem = children.find((item) => {
            return key === item.key;
          });
          if (keyChilItem) {
            const enumName = keyChilItem.enumDictName || "";
            const type = keyChilItem.valueType;
            if (enumName) {
              return enumName;
            }
            if (type === "Boolean") {
              return "boolean";
            }
          }
        } else {
          const enumName = comparator?.enumDictName || "";
          if (complexSelect && !allCellUnit) {
            return "boolean";
          } else if (enumName) {
            return enumName;
          } else {
            return "";
          }
        }
        // }
      } else if (key[4] === "2") {
        if (findCol.properties.rule[0].actionType === "setValue") {
          const setValueParams = findCol.properties.rule[0].actionParams[1];

          if (setValueParams.enumDictName) {
            return setValueParams.enumDictName;
          } else {
            const next = setValueParams.next?.next ?? setValueParams.next

            if (next?.methodParams[0]?.enumDictName) {
              return next.methodParams[0].enumDictName;
            }
          }
        } else {

          const next = findDeepestNextProperty(
            findCol.properties.rule[0].actionParams[0]
          );

          const actionParams = next?.methodParams || [];
          const tableParams = [];
          // actionParams.map((item) => {
          //   const { value = [], valueType } = item;
          //   if (!valueType.includes(".")) {
          //     if (
          //       value.length === 0 ||
          //       (value.length > 0 && value[0] === undefined)
          //     )
          //       tableParams.push(item);
          //   }
          // });
          actionParams.map((item) => {
            const _this = this;
            const { value = [], valueType } = item;
            if (item.variableType && item.variableType === "expression") {
              const expressionTreeData = item.expressionTreeData;
              if (expressionTreeData && expressionTreeData.params) {
                loopExpressionTreeData(expressionTreeData.params);
              }
              function loopExpressionTreeData(data) {
                for (let i = 0; i < data.length; i++) {
                  if (data[i].data && data[i].data.next) {
                    const { next } = data[i].data;
                    if (next) {
                      const _methodParams = next && next.methodParams;
                      if (_methodParams && _methodParams.length > 0) {
                        _this.express = true;
                        for (let j = 0; j < _methodParams.length; j++) {
                          const { value = false, valueType } = _methodParams[j];
                          if (!valueType.includes(".")) {
                            if (
                              !value ||
                              value.length === 0 ||
                              (value.length > 0 && value[0] === undefined)
                            )
                              tableParams.push(_methodParams[j]);
                          }
                        }
                      } else {
                        const { value = false, valueType } = next;
                        if (!valueType.includes(".")) {
                          if (
                            !value ||
                            value.length === 0 ||
                            (value.length > 0 && value[0] === undefined)
                          )
                            tableParams.push(next);
                        }
                      }
                    }
                  } else if (data[i].type === "variable") {
                    const _data = data[i].data;
                    const { value = false, valueType } = _data;
                    if (!valueType.includes(".")) {
                      if (
                        !value ||
                        value.length === 0 ||
                        (value.length > 0 && value[0] === undefined)
                      )
                        tableParams.push(_data);
                    }
                  } else if (data[i].type === "expression") {
                    loopExpressionTreeData(data[i].params);
                  }
                }
              }
            } else {
              if (!valueType.includes(".")) {
                if (
                  !value ||
                  value.length === 0 ||
                  (value.length > 0 && value[0] === undefined)
                )
                  tableParams.push(item);
              }
            }
          });
          if (tableParams && tableParams.length) {
            if (tableParams.length === 1) {
              if (tableParams[0]?.enumDictName) {
                return tableParams[0].enumDictName;
              }
            } else {
              // const indexChild = key[8];
              const indexChild = key.slice(8, -5);
              if (tableParams[indexChild]?.enumDictName) {
                return tableParams[indexChild].enumDictName;
              }
            }
          }
        }
      }
      return "";
    },
    colType(key) {
      // if (this.colObjKeyArr.find((item)=>{return item===key})) {
      //   return;
      // } else {
      //   this.colObjKeyArr.push(key);
      // }
      if (key[4] !== "2") {
        let findCol = {};
        if (key.includes("child")) {
          const colKey = key.slice(0, 8);
          findCol = this.columns.find((item) => {
            return item.key === colKey;
          });
        } else {
          findCol = this.columns.find((item) => {
            return item.key === key;
          });
        }
        const index = key.length > 8 ? key.slice(8, -5) : 0;
        if (!findCol) return "String";
        const { cellUnit } = findCol.properties.rule;
        if (cellUnit) {
          findCol.properties.rule.complexSelect = true;
        }
        const { complexSelect, allCellUnit } = findCol?.properties?.rule;
        // if (complexSelect) {
        //   return "Enum";
        // } else {
        const contents =
          findCol.properties.rule.children[index]?.ruleCondition?.contents;
        const comparator = contents?.comparator || {};
        const enumName = comparator?.enumDictName || "";
        const paramQuantity =
          comparator.paramQuantity || comparator.paramQuantity === 0
            ? comparator.paramQuantity
            : undefined;
        const operatorParams = comparator.operatorParams || [];
        const colChildren = findCol.children;
        let enumDictName = colChildren[index]?.enumDictName;
        if (enumName && !colChildren) {
          if (paramQuantity === -1 && enumDictName) {
            return "EnumMultiple";
          } else {
            const index = key.length > 8 ? key.slice(8, -5) : 0;
            let type = colChildren[index]?.valueType;
            if (type === "Date") {
              return "Date";
            }
            if (type === "List<String>" && enumDictName) {
              return "EnumMultiple";
            }
            if (type === "List<String>" && !enumDictName) {
              return "StringMultiple";
            }
            if (type === "Object" && !enumDictName) {
              return "StringMultiple";
            }
            if (enumDictName || type === "Boolean") {
              return "Enum";
            }
            const numList = [
              "Short",
              "Integer",
              "Float",
              "Double",
              "BigDecimal",
              "Long",
            ];
            if (numList.includes(type)) {
              return "Number";
            }
            if (type === "String") {
              return "String";
            }
            return "Enum";
          }
        } else if (colChildren && colChildren.length > 0) {
          const index = key.length > 8 ? key.slice(8, -5) : 0;
          let type = colChildren[index]?.valueType;
          let enumDictName = colChildren[index]?.enumDictName;
          let _paramQuantity = colChildren[index]?.paramQuantity || comparator?.paramQuantity;
          let _title = colChildren[index]?.title;
          if (_paramQuantity === -1 && enumDictName) {
            return "EnumMultiple";
          } else {
            if (type === "Date") {
              return "Date";
            }
            if (type === "List<String>" && enumDictName || (type === "String" && enumDictName && (_title.indexOf('中的一个') !== -1 || _title.indexOf('集合') !== -1))) {
              return "EnumMultiple";
            }
            if (type === "List<String>" && !enumDictName) {
              return "StringMultiple";
            }
            if ((type === "Object" || type === 'String') && !enumDictName && (_title.indexOf('中的一个') !== -1 || _title.indexOf('集合') !== -1)) {
              return "StringMultiple";
            }
            if (enumDictName || type === "Boolean") {
              return "Enum";
            }
            const numList = [
              "Short",
              "Integer",
              "Float",
              "Double",
              "BigDecimal",
              "Long",
            ];
            if (numList.includes(type)) {
              return "Number";
            }
            if (type === "String") {
              return "String";
            }
          }
        } else {
          if (operatorParams && operatorParams.length > 0) {
            const index = key.length > 8 ? key.slice(8, -5) : 0;
            const type = operatorParams[index].valueType;
            const value = operatorParams[index].value;
            if (!value) {
              if (type === "Date") {
                return "Date";
              }
              if (type === "Boolean") {
                return "Enum";
              }
              const numList = [
                "Short",
                "Integer",
                "Float",
                "Double",
                "BigDecimal",
                "Long",
              ];
              if (numList.includes(type)) {
                return "Number";
              }
              if (type === "String") {
                return "String";
              }
            } else {
              if (complexSelect && !allCellUnit) {
                return "Enum";
              }
            }
          }
        }
        if (paramQuantity || paramQuantity === 0) {
          const index = key.length > 8 ? key.slice(8, -5) : 0;
          if (colChildren && colChildren.length > 0) {
            let type = colChildren[index]?.valueType;
            let _title = colChildren[index]?.title;
            let enumDictName = colChildren[index]?.enumDictName;
            if (type === "Date") {
              return "Date";
            }
            if (type === "List<String>" && enumDictName) {
              return "EnumMultiple";
            }
            if (type === "List<String>" && !enumDictName) {
              return "StringMultiple";
            }
            if ((type === "Object" || type === 'String') && !enumDictName && (_title.indexOf('中的一个') !== -1 || _title.indexOf('集合') !== -1)) {
              return "StringMultiple";
            }
            if (enumDictName || type === "Boolean") {
              return "Enum";
            }
            const numList = [
              "Short",
              "Integer",
              "Float",
              "Double",
              "BigDecimal",
              "Long",
            ];
            if (numList.includes(type)) {
              return "Number";
            }
          }
          // else {
          //   const type = operatorParams[index].valueType;
          //   if (type === "Date") {
          //     return "Date";
          //   }
          //   const numList = [
          //     "Short",
          //     "Integer",
          //     "Float",
          //     "Double",
          //     "BigDecimal",
          //     "Long",
          //   ];
          //   if (numList.includes(type)) {
          //     return "Number";
          //   }
          // }
        }
        if (complexSelect && !allCellUnit) {
          return "Enum";
        }
        return "String";
        // }
      } else if (key[4] === "2") {
        let findColAction = {};
        if (key.includes("child")) {
          const colKey = key.slice(0, 8);
          findColAction = this.columns.find((item) => {
            return item.key === colKey;
          });
        } else {
          findColAction = this.columns.find((item) => {
            return item.key === key;
          });
        }

        if (!findColAction) {
          return "String";
        }

        if (findColAction.properties.rule[0].actionType === "setValue") {
          const setValueParams =
            findColAction.properties.rule[0].actionParams[1];

          const next = setValueParams.next?.next ?? setValueParams.next

          if (setValueParams.enumDictName || next?.methodParams[0]?.enumDictName) {
            return "Enum";
          }
          const setType = setValueParams.valueType;
          if (setType === "Date") {
            return "Date";
          }
          const numList = [
            "Short",
            "Integer",
            "Float",
            "Double",
            "BigDecimal",
            "Long",
          ];
          if (numList.includes(setType)) {
            return "Number";
          }
          return "String";
        } else {
          //           const paramNext = findCol.properties.rule[0].actionParams[0].next
          //           if (!paramNext.methodParams) {
          //  return
          //           }
          const next = findDeepestNextProperty(
            findColAction.properties.rule[0].actionParams[0]
          );

          const methodParams = next && next.methodParams;
          if (!next || !methodParams || methodParams.length == 0) {
            return "String";
          }
          const tableParams = [];
          let objVal = {};
          let tempTablePar = this.getTableParams(methodParams, [], objVal);
          if (Object.keys(objVal).length > 0) {
            for (let i = 0; i < tempTablePar.length; i++) {
              if (objVal[i] && objVal[i].length > 0) {
                tempTablePar[i] = objVal[i];
              }
            }
          }
          for (let i = 0; i < tempTablePar.length; i++) {
            if (Array.isArray(tempTablePar[i])) {
              let isAllTrue = tempTablePar[i].every((item) => {
                return item === true;
              });
              if (isAllTrue) {
                tempTablePar[i] = "true2";
              } else {
              }
            }
          }
          let fristT = false;
          for (let i = 0; i < tempTablePar.length; i++) {
            if (tempTablePar[i] === "true2") {
              !fristT && methodParams.splice(i, 1);
              fristT = true;
            }
          }
          fristT = false;
          // function flattenArray(arr) {
          //   let result = [];
          //   for (let i = 0; i < arr.length; i++) {
          //     if (Array.isArray(arr[i])) {
          //       result = result.concat(flattenArray(arr[i]));
          //     } else {
          //       result.push(arr[i]);
          //     }
          //   }
          //   return result;
          // }
          // let resArr = flattenArray(tableParams2);
          // tableParams2 = resArr;
          methodParams.map((item) => {
            const { value = [], valueType } = item;
            if (!valueType.includes(".")) {
              if (
                value.length === 0 ||
                (value.length > 0 && value[0] === undefined)
              )
                tableParams.push(item);
            }
          });
          if (tableParams.length === 1) {
            if (tableParams[0].enumDictName) {
              return "Enum";
            }
            const type = tableParams[0].valueType;
            if (type === "Date") {
              return "Date";
            }
            const numList = [
              "Short",
              "Integer",
              "Float",
              "Double",
              "BigDecimal",
              "Long",
            ];
            if (numList.includes(type)) {
              return "Number";
            }
            if (type === "List<String>") {
              return "StringMultiple";
            }
          } else {
            const aTableParams = [];
            const indexChild = key.slice(8, -5);
            for (let i = 0; i < tableParams.length; i++) {
              const item = tableParams[i];
              const { value = [], valueType, next, variableType } = item;
              if (variableType && variableType === "expression") {
                const expressionTreeData = item.expressionTreeData;
                if (expressionTreeData && expressionTreeData.params) {
                  loopExpressionTreeData(expressionTreeData.params);
                }
                function loopExpressionTreeData(data) {
                  for (let i = 0; i < data.length; i++) {
                    if (data[i].data && data[i].data.next) {
                      // 函数
                      const methodParams = data[i].data.next.methodParams;
                      if (methodParams && methodParams.length > 0) {
                        methodParams &&
                          methodParams.map((mitem, index) => {
                            const { value = [], valueType } = mitem;
                            if (!valueType.includes(".")) {
                              if (
                                !value ||
                                value.length === 0 ||
                                (value.length > 0 && value[0] === undefined)
                              )
                                aTableParams.push(mitem);
                            }
                          });
                      } else {
                        const next = data[i].data.next;
                        const { value = false, valueType } = next;
                        if (!valueType.includes(".")) {
                          if (
                            !value ||
                            value.length === 0 ||
                            (value.length > 0 && value[0] === undefined)
                          ) {
                            aTableParams.push(next);
                          }
                        }
                      }
                    } else if (data[i].type === "variable") {
                      const { valueType, value } = data[i].data;
                      if (!valueType.includes(".")) {
                        if (
                          !value ||
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        ) {
                          aTableParams.push(data[i].data);
                        }
                      }
                    } else if (data[i].type === "expression") {
                      loopExpressionTreeData(data[i].params);
                    }
                  }
                }
              } else if (!valueType.includes(".")) {
                if (
                  !value ||
                  value.length === 0 ||
                  (value.length > 0 && value[0] === undefined)
                )
                  aTableParams.push(item);
              } else if (next) {
                const methodParams = next.methodParams;
                if (methodParams && methodParams.length > 0) {
                  methodParams &&
                    methodParams.map((item, index) => {
                      const { value = [], valueType } = item;
                      if (!valueType.includes(".")) {
                        if (
                          !value ||
                          value.length === 0 ||
                          (value.length > 0 && value[0] === undefined)
                        )
                          aTableParams.push(item);
                      }
                    });
                }
              }
            }
            const resTableParams =
              aTableParams.length > 0 ? aTableParams : tableParams;
            if (resTableParams && indexChild && resTableParams[indexChild]) {
              if (resTableParams[indexChild].enumDictName) {
                return "Enum";
              }
              const type = resTableParams[indexChild].valueType;
              if (type === "Date") {
                return "Date";
              }
              const numList = [
                "Short",
                "Integer",
                "Float",
                "Double",
                "BigDecimal",
                "Long",
              ];
              if (numList.includes(type)) {
                return "Number";
              }
            }
          }
        }
      }
      return "String";
    },
    customRender(col, text, record, index) {
      return (
        <div key={col} class="td-div-com" style={this.getRead(col, record) ? 'background: #f7f5ee; width: 100%; display: block;height: calc((100vh - 311px) / 10);' : ''}>
          {record.editable ? (
            <>
              {text === '-' ? (
                <span>{text}</span>
              ) : (
                <>
                  {this.colType(col) === 'String' ? (
                    <table-str-input
                      style="width: 100%"
                      value={text}
                      disabled={!this.isEditing}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : this.colType(col) === 'Enum' ? (
                    <enum-select
                      style="width: 100%; z-index: 0 !important"
                      value={text}
                      disabled={!this.isEditing}
                      enumDictName={this.colTypeEnumName(col)}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : this.colType(col) === 'EnumMultiple' ? (
                    <enum-multiple
                      style="width: 100%; z-index: 0 !important"
                      value={text ? text.toString().split(',') : []}
                      enumDictName={this.colTypeEnumName(col)}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : this.colType(col) === 'StringMultiple' ? (
                    <string-multiple
                      style="width: 100%; z-index: 0 !important"
                      value={text}
                      disabled={!this.isEditing}
                      isTable={true}
                      onOnChange={(e) => this.handleChange(e, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : this.colType(col) === 'Date' ? (
                    <date-input
                      style="width: 100%"
                      value={text}
                      disabled={!this.isEditing}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : this.colType(col) === 'Number' ? (
                    <num-input
                      style="width: 100%"
                      value={text}
                      disabled={!this.isEditing}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  ) : (
                    <table-str-input
                      style="width: 100%"
                      value={text}
                      disabled={!this.isEditing}
                      isTable={true}
                      onOnChange={(value) => this.handleChange(value, record.key, col)}
                      col={col}
                      index={index}
                      rowData={this.data}
                      colData={this.props_columns}
                      record={record}
                      elseFlag={this.getElseFlag(col, record)}
                      delElseFlag={this.getDelElseFlag(col, record)}
                      readonly={this.getRead(col, record)}
                      delRead={this.getDelRead(col, record)}
                      onOperaChange={this.operaChange}
                    />
                  )}
                </>
              )}
            </>
          ) : (
            <>
              {text === '-' ? (
                <span>{text}</span>
              ) : this.getElseFlag(col, record) ? (
                <span style="font-weight:bold">{text}</span>
              ) : this.colType(col) === 'Enum' ? (
                <table-cell-text value={text} enumDictName={this.colTypeEnumName(col)} />
              ) : this.colType(col) === 'EnumMultiple' ? (
                <table-cell-text
                  value={text ? text.toString().split(',') : []}
                  enumDictName={this.colTypeEnumName(col)}
                />
              ) : (
                <span>
                  {text ? (
                    <a-tooltip placement="top" title={(
                      <span>{text}</span>
                    )}>
                      <div class="clip-box">
                        <p class="clip-text">{text}</p>
                      </div>
                    </a-tooltip>
                  ) : (
                    <div class="clip-box">
                      <p class="clip-text">{text}</p>
                    </div>
                  )}
                </span>
              )}
            </>
          )}
        </div>
      );
    },
    /**
     * 获取表格列配置
     * 根据是否处于编辑模式（this.isEditing）来生成不同的列配置，并在每列中设置自定义渲染函数。
     * 最终返回包含所有列配置的数组。
     */
    getCol() {
      let col = []; // 初始化列配置数组
      if (this.isEditing) { // 如果处于编辑模式
        const copyData = cloneDeep(this.props_columns); // 深拷贝原始列配置，避免直接修改传入的数据

        // 遍历每一列配置项
        copyData.map((item) => {
          // 设置列标题为带有操作按钮的 JSX 元素
          item.title = this.iconAndTh(item.viewName, item.key);

          // 如果当前列有子列（即复合列）
          if (item.children && item.children.length > 0) {
            // 遍历子列并为每个子列设置自定义渲染函数
            item.children.map((items) => {
              items.customRender = ({ text, record, index }) => {
                return this.customRender(items.dataIndex, text, record, index);
              };
            });
          } else {
            // 如果当前列没有子列，则直接为该列设置自定义渲染函数
            item.customRender = ({ text, record, index }) => {
              return this.customRender(item.dataIndex, text, record, index);
            };
          }
        });

        // 添加一个固定在右侧的操作列
        copyData.push({
          width: 110,
          fixed: "right",
          title: "操作",
          dataIndex: "operation",
          align: "center",
          customRender: ({ text, record }) => {
            return this.operationCustomRender(text, record);
          },
        });

        col = copyData; // 将处理后的列配置赋值给最终的列配置数组
      } else { // 如果不处于编辑模式
        const copyData = cloneDeep(this.props_columns); // 深拷贝原始列配置
        // 遍历每一列配置项
        copyData.map((item) => {
          // 如果当前列有子列（即复合列）
          if (item.children.length > 0) {
            // 遍历子列并为每个子列设置自定义渲染函数
            item.children.map((items) => {
              items.customRender = ({ text, record, index }) => {
                return this.customRender(items.dataIndex, text, record, index);
              };
            });
          } else {
            // 如果当前列没有子列，则直接为该列设置自定义渲染函数
            item.customRender = ({ text, record, index }) => {
              return this.customRender(item.dataIndex, text, record, index);
            };
          }
        });

        col = copyData; // 将处理后的列配置赋值给最终的列配置数组
      }

      // 在最前面插入一个序号列
      col.unshift({
        width: 30,
        title: "序号",
        dataIndex: "rowNumber",
        align: "center",
        fixed: "left",
        customRender: ({ text, record }) => {
          return this.rowNumberCustomRender(text, record);
        },
      });

      return col; // 返回最终的列配置数组
    },
    operationCustomRender(text, record) {
      // 渲染操作列的内容
      return (
        <div class="editable-row-operations">
          {record.editable ? (
            <span>
              <a onClick={() => this.save(record.key)}>确定</a>
              <a onClick={() => this.cancel(record.key)} style={{ marginLeft: '15px' }}>取消</a>
            </span>
          ) : (
            <a-popover content={(
              <div>
                <a-button size="small" shape="round" onClick={() => this.onRowInsertrowAbove(record.key)}>上增</a-button>
                <a-button size="small" shape="round" onClick={() => this.onRowInsertrowBelow(record.key)}>下增</a-button>
                <a-button size="small" shape="round" type="danger" onClick={() => this.onRowDelete(record.key)}>删除</a-button>
              </div>
            )}
            >
              <a onClick={() => this.edit(record.key)}>编辑</a>
            </a-popover>
          )}
        </div>
      );
    },
    rowNumberCustomRender(text, record) {
      // 渲染序号列的内容
      return (
        <span
          style="
          display: block;
          width: 100%;
          height: 100%;
          overflow: auto;
          cursor: pointer;
          position: sticky;
        "
          title="点击预览规则"
          onClick={(event) => this.setActionInfo(event, record)}
          id="popoverSpanRule"
        >
          {this.props_data.findIndex((item) => item.key === record.key) + 1}
        </span>
      );
    },
    iconAndTh(name, key) {
      const titleJSX = (
        <div>
          <a-popover content={(
            <div>
              <a-button
                type="primary"
                size="small"
                shape="round"
                onClick={() => this.onColEdit(key)}
              >
                编辑
              </a-button>
              <a-button
                size="small"
                shape="round"
                onClick={() => this.onColInsertrowLeft(key)}
              >
                左增
              </a-button>
              <a-button
                size="small"
                shape="round"
                onClick={() => this.onColInsertrowRight(key)}
              >
                右增
              </a-button>
              <a-button
                type="danger"
                size="small"
                shape="round"
                onClick={() => this.onColDelete(key)}
              >
                删除
              </a-button>
            </div>
          )}>
            <span title={name} onClick={() => this.onColEdit(key)}>
              {name}
            </span>
          </a-popover>
        </div>
      );
      return titleJSX;
    },
    handleChange(value, key, column) {
      let _val = "";
      let isPush = null;
      let elseFlag = null;
      if (value && typeof value === "object") {
        if (Array.isArray(value)) {
          _val = value.join(',')
          isPush = null;
          elseFlag = null;
        } else {
          if (value.target) {
            _val = value.target.value;
            isPush = value.target.isPush;
          }
          if (value.value) {
            _val = value.value;
            elseFlag = value.elseFlag;
          }
        }
      } else {
        _val = value;
        isPush = null;
        elseFlag = null;
      }
      if (_val && typeof _val === "string") {
        _val = _val.split(/[,，;；]/g).toString();
      }
      const newData = [...this.data];

      newData.filter((item, index) => {
        if (key === item.key) {
          if (isPush) {
            this.data[index][column] = this.data[index][column] ? this.data[index][column] + "," + _val : _val;
            this.data[index][column + 'elseFlag'] = elseFlag;
          } else {
            this.data[index][column] = _val;
            this.data[index][column + 'elseFlag'] = elseFlag;
          }
        }
      });
    },
    edit(key) {
      const newData = [...this.data];
      newData.filter((item, index) => {
        if (key === item.key) {
          newData[index].editable = true;
          this.data[index] = newData[index];
        }
      });
    },
    save(key) {
      const newData = [...this.data];
      const newCacheData = [...this.cacheData];
      const target = newData.filter((item, index) => {
        if (key === item.key) {
          delete newData[index].editable;
          for (let ikey in item) {
            if (ikey.includes('tempElseFlag')) {
              delete this.data[index][ikey];
              delete this.props_data[index][ikey];
              delete newData[index][ikey];
            }
            if (ikey.includes('childtempReadOnly')) {
              let tempIKey = ikey.replace('childtempReadOnly', 'childReadOnly')
              this.data[index][tempIKey] = true;
              this.props_data[index][tempIKey] = true;
              newData[index][tempIKey] = true;
              delete this.data[index][ikey];
              delete this.props_data[index][ikey];
              delete newData[index][ikey];
            }
            if (ikey.includes('delReadOnly')) {
              let tempIKey = ikey.replace('delReadOnly', 'ReadOnly')
              // delete this.data[index]['childtempReadOnly'];
              // delete this.props_data[index]['childtempReadOnly'];
              // delete newData[index]['childtempReadOnly'];
              delete this.data[index][tempIKey];
              delete this.props_data[index][tempIKey];
              delete newData[index][tempIKey];
            }
            if (ikey.includes('delElseFlag')) {
              delete this.data[index][ikey];
              delete this.props_data[index][ikey];
              delete newData[index][ikey];
            }
          }
          this.data[index] = newData[index];
          return item;
        }
      })[0];
      const targetCache = newCacheData.filter((item) => key === item.key)[0];
      if (target && targetCache) {
        Object.assign(targetCache, target);
        this.cacheData = newCacheData;
        this.$emit("handleRowSave", newData);
      }
    },
    cancel(key) {
      const target = this.data.find((item) => key === item.key);
      if (target) {
        Object.assign(
          target,
          this.cacheData.find((item) => key === item.key)
        );
        this.data.map((item, index) => {
          if (item.key === target.key) {
            delete this.data[index].editable;
            for (let ikey in target) {
              if (ikey.includes('tempElseFlag')) {
                delete this.data[index][ikey];
                delete this.props_data[index][ikey];
              }
              if (ikey.includes('childtempReadOnly')) {
                delete this.data[index][ikey];
                delete this.props_data[index][ikey];
              }
              if (ikey.includes('delElseFlag')) {
                delete this.data[index][ikey];
                delete this.props_data[index][ikey];
              }
              if (ikey.includes('delReadOnly')) {
                delete this.data[index][ikey];
                delete this.props_data[index][ikey];
              }
            }
            this.data[index] = target;
          }
        });
      }
    },
    onColInsertrowLeft(key) {
      this.$emit("colInsertrowLeft", key);
    },
    onColInsertrowRight(key) {
      this.$emit("colInsertrowRight", key);
    },
    onColDelete(key) {
      this.$emit("colDelete", key);
    },
    onRowInsertrowAbove(key) {
      this.$emit("rowInsertrowAbove", key);
    },
    onRowInsertrowBelow(key) {
      this.$emit("rowInsertrowBelow", key);
    },
    onRowDelete(key) {
      this.$emit("rowDelete", key);
    },
    onColEdit(key) {
      this.$emit("colEdit", key);
    },
  },
  beforeDestroy() {
    document.removeEventListener("click", this.bodyClick);
    // 清理弹出框容器
    if (this.popoverContainer && document.body.contains(this.popoverContainer)) {
      document.body.removeChild(this.popoverContainer);
    }
    // 移除全局事件监听器
    globalEventEmitter.off(CLOSE_POPOVER, this.closePopover);
  }
}
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}

.clip-box {
  text-align: center;
  vertical-align: middle;
}

.clip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: block;
  height: 21px;
  line-height: 21px;
  text-align: center;
  /* margin-top: 14px !important; */
}

.editor-table {
  padding-right: 17px;
}
</style>
<style lang="scss">
.eRuleEditorContainer .td-div-com .ant-select-selector,
.eRuleEditorContainer .td-div-com .ant-input,
.eRuleEditorContainer .td-div-com .ant-picker,
.eRuleEditorContainer .td-div-com .ant-input-number {
  height: auto !important;
}

.editor-table.design .ant-table-pagination.ant-pagination {
  .ant-select-selector {
    border-style: solid !important;
    height: 24px !important;
    line-height: 24px !important;
    padding: 0 7px !important;
    margin-top: 5px;
  }
}

.editor-table {
  .ant-table-small>.ant-table-content .ant-table-header {
    // margin-bottom: -6px !important;
    min-width: 0 !important;
  }

  .ant-table-body-outer {
    margin-bottom: 0 !important;

    // margin-top: 6px;
    .ant-table-body-inner {
      margin-right: -2px;
    }
  }

  .ant-table-header .ant-table-hide-scrollbar {
    margin-bottom: -6px !important;
  }

  .ant-table-small.ant-table-bordered .ant-table-content {
    border-right: none;
  }

  .ant-spin-container {
    height: calc(100vh - 180px);
  }

  .ant-table {
    font-size: 12px;
  }

  .ant-table-small.ant-table-fixed-header>.ant-table-content>.ant-table-scroll>.ant-table-body {
    padding: 0 !important;
    margin: 0 !important;
  }

  .ant-table-row {
    height: calc((100vh - 311px) / 10);
  }

  &.vali {
    .ant-table-row {
      height: calc((100vh - 276px) / 10);
    }
  }

  .clip-text .ant-table-thead>tr>th {
    padding: 2px 2px !important;
  }

  .ant-table-tbody>tr>td {
    padding: 0 !important;
  }

  .ant-table-small>.ant-table-content>.ant-table-fixed-right>.ant-table-header>table>.ant-table-thead>tr>th,
  .ant-table-small>.ant-table-content .ant-table-header {
    background: #fafafa;
  }

  .ant-select-selection {
    border-style: solid !important;
  }

  .ant-pagination.mini .ant-pagination-item {
    line-height: 23px !important;
    margin: 0 4px !important;
    color: #606266;
  }

  .ant-pagination-item a {
    background-color: #f4f4f5;
    color: #606266;
    min-width: 30px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 700;
  }

  .ant-pagination-item-active a {
    background-color: #409eff !important;
    color: #fff !important;
  }

  .ant-pagination.mini .ant-pagination-prev,
  .ant-pagination.mini .ant-pagination-next {
    margin: 0 5px;
    background-color: #f4f4f5;
    min-width: 30px;
    border-radius: 2px;
    font-weight: 700;
    font-size: 12px !important;
  }

  .ant-pagination.mini .ant-pagination-next .ant-pagination-item-link {
    color: #606266 !important;
  }

  .ant-pagination-disabled a,
  .ant-pagination-disabled .ant-pagination-item-link {
    color: #c0c4cc !important;
  }

  .ant-pagination.mini .ant-pagination-total-text {
    margin-right: 10px;
    font-weight: 400;
    color: #606266;
    font-size: 12px;
    min-width: 35.5px;
    height: 28px;
    line-height: 28px;
  }

  .ant-table-pagination.ant-pagination {
    text-align: center !important;
    float: unset !important;
    margin-bottom: 0 !important;
  }

  .ant-pagination.mini .ant-pagination-options-quick-jumper,
  .ant-pagination-options-size-changer.ant-select {
    margin: 0 5px 0 10px;
    font-weight: 400;
    color: #606266;
    font-size: 12px;
    min-width: 35.5px;
  }

  .ant-pagination.mini .ant-pagination-options-quick-jumper {
    margin-right: 0;
  }

  .ant-select-dropdown-menu-item {
    font-size: 12px;
    color: #606266;
    font-weight: 400;
  }

  .ant-select-dropdown-menu-item-selected {
    background: #f5f7fa !important;
  }

  .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
    background: #f5f7fa !important;
    color: #409eff;
    font-weight: 700;
  }

  &.design .ant-table-pagination.ant-pagination {
    position: absolute !important;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 700;

    @media (max-width: 739px) {
      bottom: -11px !important;
    }

    @media (max-width: 575px) {
      bottom: 11px !important;
    }

    @media (max-width: 490px) {
      bottom: -11px !important;
    }

    @media (max-width: 275px) {
      bottom: -31px !important;
    }

    @media (max-width: 200px) {
      bottom: -61px !important;
    }

    @media (max-width: 173px) {
      bottom: -81px !important;
    }
  }

  // .ant-table-body::-webkit-scrollbar {
  //   height: 14px;
  //   background-color: transparent;
  // }
  // .ant-table-body-inner {
  //   // overflow-x: hidden;
  //   overflow-y: scroll;
  // }
  // .ant-table-body-inner::-webkit-scrollbar {
  //   width: 14px;
  //   background-color: transparent;
  // }
  .td-div-com {
    display: flex;
    justify-content: center;

    &>div {
      padding: 2px;
    }

    .span-con-else {
      width: 100%;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;

      &>div:first-child {
        width: 100% !important;
        z-index: 0 !important;
      }
    }
  }

  .ant-table-scroll {

    .ant-table-header .ant-table-fixed th[key='rowNumber'],
    .ant-table-body .ant-table-fixed td:first-child {
      position: sticky;
      left: 0;
      z-index: 9;
    }

    .ant-table-header .ant-table-fixed th[key='rowNumber'] {
      background: #fafafa !important;
    }

    .ant-table-body .ant-table-fixed td:first-child {
      background: #fff !important;
    }
  }

  .ant-table.ant-table-scroll-position-right .ant-table-fixed-right,
  .ant-table-body-outer .ant-table-body-inner {
    background: #fff !important;
  }
}

#rule-preview-popover-container {
  cursor: text;

  svg {
    display: none;
  }

  .predefine-con {
    margin-bottom: 15px !important;

    .pre-rule-contentlast-c {
      margin-left: 20px;
    }

    .action div:last-child {
      display: none;
    }
  }

  .structuralWord {
    font-size: 14px;
    font-weight: bold;
    color: #000090;
  }

  .condition {
    display: flex;
  }

  .actionCom .action {
    margin-left: 20px;
  }

  /* 设置滚动条样式 */
  .custom-popover {
    &::-webkit-scrollbar {
      width: 12px;
      height: 12px;
      background-color: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c1c1c1;
      border: 2px solid #f1f1f1;
      border-radius: 6px;
      cursor: pointer;
      
      &:hover {
        background-color: #a8a8a8;
      }
    }

    &::-webkit-scrollbar-button {
      display: block;
      width: 12px;
      height: 12px;
      background-color: #f1f1f1;
      
      &:start {
        border-radius: 3px 3px 0 0;
      }
      
      &:end {
        border-radius: 0 0 3px 3px;
      }
      
      &:hover {
        background-color: #e8e8e8;
      }
    }

    /* 垂直滚动条按钮样式 */
    &::-webkit-scrollbar-button:vertical:start {
      background: #f1f1f1 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23666" d="M6 4l3 4H3z"/></svg>') no-repeat center;
    }

    &::-webkit-scrollbar-button:vertical:end {
      background: #f1f1f1 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23666" d="M6 8l3-4H3z"/></svg>') no-repeat center;
    }

    /* 水平滚动条按钮样式 */
    &::-webkit-scrollbar-button:horizontal:start {
      background: #f1f1f1 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23666" d="M4 6l4-3v6z"/></svg>') no-repeat center;
    }

    &::-webkit-scrollbar-button:horizontal:end {
      background: #f1f1f1 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path fill="%23666" d="M8 6l-4-3v6z"/></svg>') no-repeat center;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
  }
}
</style>
