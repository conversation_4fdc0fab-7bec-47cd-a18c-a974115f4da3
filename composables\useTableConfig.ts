import type { ComputedRef, Ref } from 'vue';
import TableActionHeader from '@/components/TableActionHeader.vue';
import { h, isRef, ref } from 'vue';

/**
 * 表格配置管理 Composable
 *
 * 该函数用于统一管理表格列的显示、隐藏和序号计算，提供了以下功能：
 * 1. 自动添加序号列和操作列
 * 2. 提供序号计算函数，支持分页场景下的序号连续性
 * 3. 处理列的显示/隐藏逻辑，保证序号列和操作列始终可见
 * 4. 提供统一的分页配置，可全局使用
 *
 * @param columnsWithoutIndexAction 不包含序号列和操作列的表格列配置
 * @param handleColumnChange 列变化处理函数
 * @param paginationConfig 可选的分页配置，用于覆盖默认配置
 * @param showActionColumn 是否显示操作列，默认为true
 * @returns {
 *   columns: 完整的表格列配置,
 *   calculateIndex: 序号计算函数,
 *   updateVisibleColumns: 更新可见列的函数,
 *   pagination: 统一的分页配置
 * }
 */
export default function useTableConfig(
  columnsWithoutIndexAction: any[],
  handleColumnChange: (columns: any[]) => void,
  showActionColumn: boolean = true,
  paginationConfig?: Partial<{
    page: number;
    limit: number;
    total: number;
    showSizeChanger: boolean;
    showTotal: (total: number) => string;
  }>,
) {
  // 统一的分页配置
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    showSizeChanger: true,
    isLoading: false,
    showTotal: (total: number) => `共 ${total} 条数据`,
    ...(paginationConfig || {})
  });

  // 计算序号的函数
  const calculateIndex = (index: number): number => {
    // 确保使用是数字类型且保持连续性
    const page = Number(pagination.value.page || 1);
    const limit = Number(pagination.value.limit || 10);
    return (page - 1) * limit + index + 1;
  };

  // 添加序号列
  const indexColumn = {
    title: '序号',
    align: 'center',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    fixed: "left",
    customRender: ({ index }: { index: number }) => {
      // 确保使用正确的页码和每页条数计算序号
      return calculateIndex(index);
    }
  };

  // 添加操作列
  const actionColumn = {
    title: () => {
      return h(TableActionHeader, {
        columns: [...columnsWithoutIndexAction],
        onChangeTableList: handleColumnChange
      });
    },
    key: 'action',
    align: 'center',
    dataIndex: 'action',
    width: 76,
    fixed: 'right',
  };

  // 合并所有列
  const columns = [
    indexColumn,
    ...columnsWithoutIndexAction,
    ...(showActionColumn ? [actionColumn] : [])
  ];

  /**
   * 处理列显示/隐藏
   * @param visibleColumns 当前可见的列
   * @returns 更新后的表格列配置
   */
  const updateVisibleColumns = (visibleColumns: any[]): any[] => {
    const visibleKeys = visibleColumns.map(col => col.key);

    return [
      // 序号列始终显示
      indexColumn,
      // 根据可见性过滤业务列
      ...columnsWithoutIndexAction.filter(col => visibleKeys.includes(col.key)),
      // 根据配置决定是否显示操作列
      ...(showActionColumn ? [actionColumn] : [])
    ];
  };

  return {
    columns,
    calculateIndex,
    updateVisibleColumns,
    pagination
  };
}
