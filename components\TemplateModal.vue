<!-- 模板信息 -->
<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

const templateName = ref('')
const errorMessage = ref('')
const emit = defineEmits(['cancel', 'confirm'])

const handleConfirm = () => {
  if (!templateName.value || !templateName.value.trim()) {
    errorMessage.value = '请输入模板名称'
    return
  }
  errorMessage.value = ''
  emit('confirm', templateName.value.trim())
}

const handleCancel = () => {
  templateName.value = '' // 关闭时清空输入
  errorMessage.value = '' // 清空错误信息
  emit('cancel')
}

// 输入时清除错误信息
const handleInput = () => {
  if (errorMessage.value && templateName.value.trim()) {
    errorMessage.value = ''
  }
}
</script>

<template>
  <a-modal
    title="模板信息"
    :open="open"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
    :keyboard="false"
    :centered="true"
    :width="520"
    destroyOnClose
  >
    <div class="template-form">
      <div class="form-item">
        <label class="form-label" style="white-space: nowrap;"><span class="required-mark">*</span>模板名称：</label>
        <div class="input-container">
          <a-input
            v-model:value="templateName"
            placeholder="请输入模板名称"
            class="form-input"
            :status="errorMessage ? 'error' : ''"
            @input="handleInput"
          />
          <div class="error-message" :class="{ 'has-error': errorMessage }">{{ errorMessage || ' ' }}</div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm">确定</a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
.template-form {
  padding: 24px 0;

  .form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .form-label {
      width: 80px;
      text-align: right;
      margin-right: 8px;
      color: rgba(0, 0, 0, 0.85);
      padding-top: 5px;
    }

    .required-mark {
      color: #ff4d4f;
      margin-right: 4px;
    }

    .input-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .form-input {
      width: 100%;
    }

    .error-message {
      color: transparent;
      font-size: 12px;
      margin-top: 4px;
      height: 18px;
      line-height: 18px;
      
      &.has-error {
        color: #ff4d4f;
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style> 
