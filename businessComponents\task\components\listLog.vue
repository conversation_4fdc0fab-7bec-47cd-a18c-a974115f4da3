<!-- 流程日志 -->
<script setup lang="ts">
    const message = inject('message')
    import { Process } from "@/api/task";
    interface Log {
        state: string;
        operator: string;
        logContent1: string;
        logContent2: string;
        createdTimeStr: string;
    }
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align:'center',
            width:50,
            customRender: ({ index }) => `${(pagination.value.current - 1) * pagination.value.pageSize + index + 1}`,
        },
        {
            title: '环节',
            dataIndex: 'state',
            key: 'state',
            align:'left'
        },
        {
            title: '操作人',
            dataIndex: 'operator',
            key: 'operator',
            align:'left'
        },
        {
            title: '审核意见',
            dataIndex: 'logContent1',
            key: 'logContent1',
            align:'left',
            width:200,
            ellipsis: true,
        },
        {
            title: '备注',
            dataIndex: 'logContent2',
            key: 'logContent2',
            align:'left'
        },
    ];
    const formState = ref({
        bizKey: "",
        uuid: "",
        appNo: "",
    });
    const logs = ref<Log[]>([]);
    const pagination = ref({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger:true,
        isLoading :false,
        showTotal: (total) => `共 ${total} 条数据`,
    });
    const props = defineProps({
        infoData: {
            type: Object,
        },
    });
    watch(
        () => props.infoData,
        (newValue) => {
            try {
                fetchLogs();

            } catch (error) {
            }
        },
        {
            immediate: true,
        }
    );
    onMounted(()=>{
        fetchLogs();
    })
    const handlePageChange = (page: number) => {
        pagination.value.current = page.current;;
        pagination.value.pageSize = page.pageSize;
        pagination.value.showSizeChanger = page.showSizeChanger;
        pagination.value.total = page.total;
        fetchLogs();
    };
    const fetchLogs = () => {
        pagination.value.isLoading = true;
        try {
            const res = Process({
                bizKey: props.infoData.appNo,
                page: pagination.value.current,
                several: pagination.value.pageSize,
            }).then((res) => {
                logs.value = res.data.data;
                pagination.value.total = res.data.totalCount;
                pagination.value.isLoading = false;
            });


        } catch (error) {

            pagination.value.isLoading = false;
            message.error('获取日志失败');
        }
    };
    //计算自适应高度
    const basePoint = ref();
    const { scrollY } = useResize(basePoint);
</script>
<template>
    <div ref="basePoint">
        <a-table :columns="columns" :data-source="logs" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading" size="small"
                 @change="handlePageChange" :scroll="{  y: scrollY-100 }">

        </a-table>
    </div>

</template>
