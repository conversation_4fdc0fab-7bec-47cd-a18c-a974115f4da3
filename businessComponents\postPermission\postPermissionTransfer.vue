<!-- 转移机构页页 -->

<script setup lang="ts">

import { useRouter, useRoute } from 'vue-router';
import { getSelect, getOrd, getSaveInfo, getPostson } from '@/api/post_permissions';
definePageMeta({
    title: '转移机构'
})


interface Option {
    code: string;
    name: string;
}

interface Form {
    business: string;
    landarrsel: string;
    county_city: string;
    parentName: string;
    name: string;
    code: string;
    level: string;
}

interface ArrAll {
    orgName: string;
    orgId: string;
    level: string;
}

const router = useRouter();
const route = useRoute();
const message = inject('message')

// 定义props
const props = defineProps({
    record: {
        type: Object,
        required: true
    }
});

// 定义emit
const emit = defineEmits(['close', 'success']);

const form = ref<Form>({
    business: '',
    landarrsel: '',
    county_city: '',
    parentName: '',
    name: '',
    code: '',
    level: '',
});

const arrAll = ref<ArrAll>({
    orgName: '',
    orgId: '',
    level: '',
});

const province = ref<Option[]>([]);
const landarr = ref<Option[]>([]);
const sumsel = ref<Option[]>([]);
const county = ref<Option[]>([]);
const tranCode = ref<string>('');
const headbase = ref<string>('');
const selecQuartId = ref<string>('');
const selectartQuName = ref<string>('');
const selectprovinceId = ref<string>('');
const selectprovinceName = ref<string>('');
const selectlandId = ref<string>('');
const selectlandName = ref<string>('');
const selectcountyId = ref<string>('');
const selectcountyName = ref<string>('');
const plaseChoose = ref<Option>({
    name: '请选择',
    code: '',
});

onMounted(() => {
    form.value = props.record;
    selhead();
    getInpinfo();
    getcodeInfo();
});

const getcodeInfo = () => {
    const pars = {
        businessUuid: form.value.businessLineId,
    };
    getPostson(pars).then((res) => {
        tranCode.value = res.data;
    });
};

const getInfoByVid = (arr: Option[], vid: string) => {
    return arr.find((i) => (vid ? i.code === vid : false));
};

const selhead = () => {
    const pars = {
        parentCode: '',
        parentTypeCode: '',
        typeCode: 'head_office',
    };
    getSelect(pars).then((res) => {
        if (res.data) {
            res.data.unshift(plaseChoose.value);
            sumsel.value = res.data;
            headbase.value = res.data[1].code;
            if (res.data[1].name) {
                selectQuart(res.data[1].code);
            }
        }
    });
};

const getInpinfo = () => {
    const pars = {
        id: form.value.id,
    };
    getOrd(pars).then((res) => {
        form.value = res.data;
    });
};

const selectQuart = (vid: string) => {
    selecQuartId.value = vid;
    selectartQuName.value = getInfoByVid(sumsel.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'head_office',
            typeCode: 'org_province',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                province.value = res.data;
            }
        });
    } else {
        province.value = [];
        form.value.business = '';
        selectprovinceId.value = '';
        selectprovinceName.value = '';
        landarr.value = [];
        form.value.landarrsel = '';
        selectlandId.value = '';
        selectlandName.value = '';
    }
};

const selectprovince = (vid: string) => {
    selectprovinceId.value = vid;
    selectprovinceName.value = getInfoByVid(province.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_province',
            typeCode: 'org_prefecture_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                landarr.value = res.data;
            }
        });
    } else {
        landarr.value = [];
        form.value.landarrsel = '';
        selectlandId.value = '';
        selectlandName.value = '';
        county.value = [];
        form.value.county_city = '';
        selectcountyId.value = '';
        selectcountyName.value = '';
    }
};

const selectland = (vid: string) => {
    selectlandId.value = vid;
    selectlandName.value = getInfoByVid(landarr.value, vid)?.name || '';
    if (vid) {
        getSelect({
            parentCode: vid,
            parentTypeCode: 'org_prefecture_city',
            typeCode: 'org_county_city',
        }).then((res) => {
            if (res.data) {
                res.data.unshift(plaseChoose.value);
                county.value = res.data;
            }
        });
    } else {
        county.value = [];
        form.value.county_city = '';
        selectcountyId.value = '';
        selectcountyName.value = '';
    }
};

const selectcounty = (vid: string) => {
    selectcountyId.value = vid;
    selectcountyName.value = getInfoByVid(county.value, vid)?.name || '';
    if (!vid) {
        form.value.county_city = '';
    }
};

const onSubmit = () => {
    const data = {
        businessLineId: form.value.businessLineId,
        id: form.value.id,
        orgId: form.value.orgId,
        orgName: form.value.orgName,
        parentOrgId: selecQuartId.value,
        type: '1',
    };
    getSaveInfo(data).then((res) => {
        message.success(res.data);
        emit('success');
        emit('close');
    });
};

const resetForm = () => {
    emit('close');
};

const organizationValue = computed(() => {
    return `${selectartQuName.value}${selectprovinceName.value}${selectlandName.value}${selectcountyName.value}`;
});

// 暴露方法给父组件
defineExpose({
    onSubmit
});
</script>

<template>
    <a-form :model="form" label-width="120px" label-align="right" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="总部" prop="headbase">
            <a-select placeholder="请选择" v-model:value="headbase" @change="selectQuart">
                <a-select-option
                        v-for="item in sumsel"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="省级" prop="business">
            <a-select v-model:value="form.business" placeholder="请选择" @change="selectprovince">
                <a-select-option
                        v-for="item in province"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="地级" prop="landarrsel">
            <a-select v-model:value="form.landarrsel" placeholder="请选择" @change="selectland">
                <a-select-option
                        v-for="item in landarr"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="县级市" prop="county_city">
            <a-select v-model:value="form.county_city" placeholder="请选择" @change="selectcounty">
                <a-select-option
                        v-for="item in county"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                >
                    {{ item.name }}
                </a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="父机构名称">
            <a-input autocomplete="off" :value="organizationValue" disabled />
        </a-form-item>
        <a-form-item label="当前机构名称">
            <a-input autocomplete="off" v-model:value="form.orgName" disabled />
        </a-form-item>
        <a-form-item label="当前机构代码">
            <a-input autocomplete="off" v-model:value="form.orgId" disabled />
        </a-form-item>
        <a-form-item label="级别">
            <a-input autocomplete="off" v-model:value="form.level" disabled />
        </a-form-item>
    </a-form>
</template>

