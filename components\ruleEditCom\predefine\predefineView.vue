<template>
  <div class="predefineView">
    <div>
      <div class="predefine-con" style="margin-bottom: 50px">
        <div class="action">
          <div class="structuralWord" style="margin-bottom: 20px">预定义</div>
          <PredefineList
            v-for="(item, index) in predefineList"
            :key="item.id"
            :preViewName="item.preViewName"
            :inputvariableValue="item.inputvariableValue"
            :selectVariableValue="item.selectVariableValue"
            :variableType="item.variableType"
            :inputOrSelect="item.inputOrSelect"
            :operatorType="item.operatorType"
            :conditions="item.conditions"
            :hasConditions="item.hasConditions"
            :initModelData="initModelData"
            :id="item.id"
            :locked="locked"
            :validateResult="validateResult[index]"
            @predefineListChange="predefineListChange"
            @delPredefineList="delPredefineList"
            @addPredefineList="addPredefineList"
            :preIndex="index + 1"
            :classN="predefineList.length - 1 === index ? 'last-c' : ''"
          />
          <div v-if="!locked">
            <a-button
              type="link"
              @click="addPredefineList"
              :style="{
                color: 'rgba(0,0,0,0.4)',
                fontSize: '12px',
                marginBottom: '8px',
              }"
            >
              <EditOutlined />添加
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PredefineList from "./predefineList.vue";

const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};
const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
};

const props = defineProps({
  locked: Boolean,
  predefineList: {
    type: Array,
    default: () => [],
  },
  validateResult: {
    type: Array,
    default: () => [],
  },
  initModelData: {
    type: Object,
    default: () => {},
  },
  uuid: String,
});

let addNum = ref(props.predefineList.length + 1);

const addPredefineList = () => {
  const addObj = {
    id: props.uuid + ".erule.predefine" + addNum.value,
    preViewName: "变量" + addNum.value,
    variableType: "String",
    variableTypeName: "字符串",
    operatorType: "equals",
    inputOrSelect: "Input",
    inputvariableValue: "",
    selectVariableValue: initRuleData,
    hasConditions: false,
    conditions: initRuleData,
  };
  addNum.value += 1;
  emit("changePredefineList", [...props.predefineList, addObj], false, false);
};

const delPredefineList = (id, del) => {
  const copyList = props.predefineList.filter((item) => item.id !== id);
  emit("changePredefineList", copyList, del);
};

const predefineListChange = (obj, change) => {
  const copyList = props.predefineList.map((item) => {
    if (item.id === obj.id) {
      return {
        ...item,
        ...obj,
      };
    } else {
      return item;
    }
  });
  emit("changePredefineList", copyList, false, change);
};

const emit = defineEmits(["changePredefineList"]);
</script>
