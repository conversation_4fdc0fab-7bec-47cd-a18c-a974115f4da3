import request from '@/utils/request'
//参数配置
//列表展示
export function parameterList(params) {
    return request({
      url: 'sys/parameter/list',
      method: 'get',
      params, 
    })
  }
//获取修改文本框的值
export function parameterUpdate(params) {
    return request({
      url: 'sys/parameter/getParameterById',
      method: 'get',
      params, 
    })
  }
//修改提交
export function parameterSub(data) {
    return request({
      url: 'sys/parameter/update',
      method: 'post',
      data, 
    })
  }

// 获取批量导出数量
export function batchExportNum() {
  return request({
    url: '/sys/parameter/getBatchExportNum',
    method: 'get'
  })
}
