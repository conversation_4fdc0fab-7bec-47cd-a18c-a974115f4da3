<!-- 任务新增页 -->

<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    definePageMeta({
        title: '任务新增'
    })
    const router = useRouter();

    const ruleForm = reactive({
        ruleName: "",
        demandApplyQO: "",
        bizKey: "",
        appNo: "",
    });

    const ruleArrsel = ref([]);

    const rules = {
        ruleCode: [{ required: true, message: "请选择", trigger: "change" }],
    };

    const getName = (value: string) => {
        const selectedRule = ruleArrsel.value.find((i: any) => i.code === value);
        if (selectedRule) {
            ruleForm.ruleName = selectedRule.name;
        }
    };

    const getRuleInfosel = async () => {
        const res = await getBaseLine();
        ruleArrsel.value = res.data;
    };

    onMounted(() => {
        getRuleInfosel();
    });

    const ruleFormRef = ref();
    const submitForm = () => {
        ruleFormRef.value.validate().then((valid: boolean) => {
            if (valid) {
                showModal('add')
            } else {
                return false;
            }
        });
    };

    const back = () => {
        router.go(-1);
    };
    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'add') {
            datar.value = {
                type: "add",
                roleName: ruleForm.ruleName,
                demandApplyQO: ruleForm.demandApplyQO,
                appNo: ruleForm.appNo,
                bizKey: ruleForm.bizKey,
                businessCode: ruleForm.ruleCode,
            };
        }
        isModalVisible.value = true;
    };
    const close = (flag) => {
        isModalVisible.value = false;
    }
</script>

<template>
    <NotListPageLayout>

        <template #title>
            <!-- 页面标题 -->
            任务新增
        </template>

        <template #search>
            <!-- 搜索区域 -->
            <a-form
                    label-width="100px"
                    ref="ruleFormRef"
                    :model="ruleForm"
                    :rules="rules"
                    layout="inline"
            >
                <a-form-item label="业务条线" prop="ruleCode"  style="width: 300px">
                    <a-select
                            v-model:value="ruleForm.ruleCode"
                            placeholder="请选择"
                            @change="getName"
                            :filterOption="filterOption" showSearch
                    >
                        <a-select-option
                                v-for="item in ruleArrsel"
                                :key="item.code"
                                :value="item.code"
                                :name="item.name"
                        >
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
            <a-button type="primary" @click="submitForm" style="margin-right: 8px">下一步</a-button>
            <a-button @click="back">返回</a-button>
        </template>
        <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" v-if="isModalVisible"/>
    </NotListPageLayout>
</template>

