<template>
  <div :style="style" :class="['condition', classFlag]" id="tableCom">
    <div class="LeftValueCom">
      <VariableCom
        :key="pos + '_va'"
        :pos="pos"
        :dataSource="variable"
        :signValue="''"
        :locked="locked"
        :isTrack="isTrack"
        :isLastOne="true"
        :isTable="isTable"
        :noRuleCellUnit="noRuleCellUnit"
        @onChange="onVariableComChange"
        @onCalSignChange="changeCalculateSign"
      />
    </div>

    <RuleOperator
      v-if="comparator"
      :key="pos + '_op'"
      :pos="pos"
      :operatorData="operatorTypeList"
      :type="conditionValueType"
      :dataSource="comparator"
      :locked="locked"
      :isTrack="isTrack"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
      @onChange="onComparatorChange"
    />
  </div>
</template>

<script setup>
import store from "@/store";
import * as util from "@/components/ruleEditCom/utils/util";
import { cloneDeep } from "lodash";

// 注入属性
const ruleUuid = inject('ruleUuid', '');

// 定义 props
const props = defineProps({
  dataSource: {
    type: Object,
    default: () => ({}),
  },
  indentLength: Number,
  fold: Boolean,
  pos: String,
  style: {
    type: Object,
    default: () => ({}),
  },
  change: Function,
  locked: Boolean,
  isTrack: Boolean,
  conditionValid: {
    type: Object,
    default: () => ({}),
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

// 定义 data
const operatorTypeList = ref({});
const oldVari = ref(null);
const classFlag = ref('');
const variable = ref(null);
const comparator = ref(null);
const conditionValueType = ref(null);


// 计算属性
const style = computed(() => props.style);

// 生命周期钩子
onMounted(() => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable } = contents;
  if (oldVari.value === null) {
    oldVari.value = cloneDeep(variable);
  }
  operatorTypeList.value =
    store.getters.listMap[ruleUuid].initModelData.operatorTypeList;
});

// 方法
const caculatePath = (indexPath = "") => {
  const arr = indexPath.split("|").reverse();
  let num = 0;
  for (let i = 0, len = arr.length; i < len; i++) {
    if (arr[i] / 1 === 0) {
      num++;
    } else {
      break;
    }
  }
  return {
    num,
    arrIndex: arr[0],
  };
};

const onVariableComChange = (pos, newVariableData, conditionValueType) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator } = contents;
  const isBool = conditionValueType === "Boolean";
  const { dictName } = newVariableData;
  const { next } = newVariableData;
  if (next) {
    const { methodParams, paramsList } = next;
    if (methodParams && methodParams.length > 0) {
      const { valueType } = paramsList[0];
      if (valueType === "List<String>") {
        const { singleParams } = methodParams[0];
        if (singleParams) {
          for (let i = 0; i < singleParams.length; i++) {
            const { value, enumDictName } = singleParams[i];
            methodParams[i].variableType = "constant";
            methodParams[i].valueType = "List<String>";
            methodParams[i].value = value || "";
            enumDictName && (methodParams[i].enumDictName = enumDictName);
          }
          delete methodParams[0].singleParams;
        }
      }
    }
  }
  const domain = util.getRealDomain(newVariableData);
  if (isBool) {
    if (newVariableData.next) {
      newVariableData.next.domain = "boolean";
    }
  }
  const newContents = {
    variable: newVariableData,
    conditionValueType: conditionValueType,
  };
  let comparatorData = null;

  if (isBool) {
    comparatorData = {
      operatorName: "",
      enumDictName: "boolean",
    };
  } else if (dictName || domain) {
    comparatorData = {
      operatorName: "",
      enumDictName: dictName || domain || null,
    };
  } else {
    comparatorData = {
      operatorName: "",
    };
  }
  let aOldValue = null;
  let aNewValue = null;
  let comparaRes = null;
  if (oldVari.value) {
    getLastOV(oldVari.value);
    getLastNV(newVariableData);
    comparaRes = getComparaRes(aOldValue, aNewValue);
  }
  if (comparator && comparator.value && comparaRes) {
    newContents.comparator = comparator;
  } else {
    oldVari.value = cloneDeep(newVariableData);
    newContents.comparator = comparatorData;
  }
  emit("conditionChange", pos, newContents);

  function getLastNV(data) {
    if (data.next) {
      getLastNV(data.next);
    } else {
      aNewValue = data.value;
    }
  }

  function getLastOV(data) {
    if (data.next) {
      getLastOV(data.next);
    } else {
      aOldValue = data.value;
    }
  }
};

const getComparaRes = (oVal, nVal) => {
  let bRes = false;
  const aO = oVal;
  const aN = nVal;
  const aR = [];
  if (aO && aN) {
    if (aO.length !== aN.length) {
      bRes = false;
    } else {
      for (let i = 0; i < aO.length; i++) {
        if (aO[i] === aN[i]) {
          aR.push(true);
        } else {
          aR.push(false);
        }
      }
      if (aR.includes(false)) {
        bRes = false;
      } else {
        bRes = true;
      }
    }
  }
  return bRes;
};

const onComparatorChange = (pos, newOperatorData) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const newContents = {
    variable: contents.variable,
    conditionValueType: contents.conditionValueType,
    comparator: newOperatorData,
  };
  emit("conditionChange", pos, newContents);
};

const conditionHandler = (type) => {
  const { pos, dataSource } = props;
  const { addChildCondition, addTailItem } = props.$listeners;
  const { ruleCondition } = dataSource;
  const { conditionId, layer } = ruleCondition;
  switch (type) {
    case "delete":
      emit("decreaseRule", { pos, conditionId, layer });
      break;
    case "addChildren":
      addChildCondition(pos, conditionId, layer);
      break;
    case "addTailItem":
      addTailItem(pos, conditionId);
      break;
  }
};

const changeCalculateSign = (pos, sign, newParamItem) => {
  const { dataSource } = props;
  const { ruleCondition } = dataSource;
  const { contents } = ruleCondition;
  const { variable, comparator, conditionValueType } = contents;
  const targetOption = variable;
  const valueType = targetOption._getRealValueType();
  if (sign === "delete" || !newParamItem) {
    return;
  }
  const newVariable = {
    valueType: valueType,
    variableType: "expression",
    expressionTreeData: {
      type: "expression",
      symbols: [sign],
      params: [
        {
          type: "variable",
          data: {
            ...targetOption,
          },
        },
        newParamItem,
      ],
    },
  };

  const newComparator = comparator || { operatorName: "" };
  const _newVariable = targetOption._derive(newVariable, {
    owner: targetOption._getOwner(),
    isRootVar: true,
  });
  const _newComparator = targetOption._derive(newComparator, {
    owner: targetOption._getOwner(),
  });

  const newContents = {
    variable: _newVariable,
    conditionValueType: conditionValueType,
    comparator: _newComparator,
  };

  emit("conditionChange", pos, newContents);
};

const getLastMethodParams = (data) => {
  const { next, methodParams } = data;
  if (next) {
    return getLastMethodParams(next);
  }
  if (methodParams) {
    return methodParams;
  }
};

// 定义 emit
const emit = defineEmits(['conditionChange', 'decreaseRule']);

watch(() => props.dataSource.executeRes, (newExecuteRes) => {
  if (newExecuteRes) {
    const { executeStatus } = newExecuteRes;
    classFlag.value = executeStatus === true
      ? "executeTrue"
      : executeStatus === false
      ? "executeFalse"
      : "";
  } else {
    classFlag.value = "";
  }
}, { immediate: true, deep: true });

watch(() => props.dataSource.ruleCondition.contents.variable, (newVariable) => {
  variable.value = newVariable;
}, { immediate: true, deep: true });

watch(() => props.dataSource.ruleCondition.contents.comparator, (newComparator) => {
  comparator.value = newComparator;
}, { immediate: true, deep: true });

watch(() => props.dataSource.ruleCondition.contents.conditionValueType, (newValue) => {
  conditionValueType.value = newValue;
}, { immediate: true, deep: true });

</script>

<style lang="scss" scoped>
.tableCon > div > svg {
  height: 55px !important;
  width: 10000px;
}
#tableCom {
  width: 10000px;
}
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}
</style>
