<!-- 模板中心图标 -->

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: number | string
  iconClass?: string
  name?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: '20px',
  iconClass: '',
  name: 'TemplateCenter'
})

const iconStyle = computed(() => ({
  width: props.size,
  minWidth: props.size,
  height: props.size
}))
</script>

<template>
  <svg
    :width="size"
    :height="size"
    viewBox="0 0 256 256"
    xmlns="http://www.w3.org/2000/svg"
    :class="['larkui-icon', 'larkui-icon-template-center', 'icon-svg', 'index-module_size_wVASz', iconClass]"
    :data-name="name"
    :style="iconStyle"
  >
    <g fill="none" fill-rule="evenodd">
      <path d="M0-2h256v256H0z"></path>
      <path d="M85.347 68.498c4.184 0 8.268.43 12.21 1.25l-10.487 42.1-.115.482c-2.668 11.56 1.745 23.148 10.454 30.14l.196.155-25.044 44.135C45.708 180.9 25.6 156.958 25.6 128.315c0-33.036 26.75-59.817 59.747-59.817Z" fill="#3384F5"></path>
      <path d="m138.617 27.454.308.074c.003 0 .006.001.006.01l75.654 18.886c8.708 2.183 14.009 11.004 11.848 19.718l-18.87 75.777c-2.137 8.614-10.78 13.897-19.394 11.925l-.307-.073c-.003-.001-.006-.002-.007-.011l-2.925-.73-23.394-41.224a26.698 26.698 0 0 0-10.042-10.042c-12.824-7.278-29.12-2.781-36.397 10.042l-10.586 18.654c-3.805-3.903-5.565-9.626-4.157-15.303l18.87-75.777c2.137-8.614 10.78-13.897 19.393-11.926Z" fill="#ECB604"></path>
      <path d="m149.63 118.563 51.308 90.407c3.546 6.249 1.355 14.189-4.893 17.735a13.01 13.01 0 0 1-6.421 1.695H87.01c-7.185 0-13.01-5.824-13.01-13.01 0-2.25.585-4.462 1.696-6.42l51.307-90.407c3.546-6.249 11.486-8.44 17.735-4.894a13.01 13.01 0 0 1 4.893 4.894Z" fill="#E4495B"></path>
    </g>
  </svg>
</template>

<style scoped>
/* 如果有特定样式，可以在这里添加 */
</style>