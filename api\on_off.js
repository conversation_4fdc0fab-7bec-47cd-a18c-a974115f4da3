import request from '@/utils/request'
//开关控制管理(工作队列)
//列表
export function offList(params) {
    return request({
      url: 'erule/manage/engineeringCheck/list',
      method: 'get',
      params, 
    })
  }
// 开关状态
export function offstate(data) {
    return request({
      url: 'erule/manage/engineeringCheck/changeStatus',
      method: 'post',
      data,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },

    })
  }
//待审核查询
export function ruleList(params) {
    return request({
      url: 'erule/manage/engineeringCheck/ruleList',
      method: 'get',
      params, 
    })
  }
//审核通过
export function on_pass(data) {
  return request({
    url: 'erule/manage/engineeringCheck/pass',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}