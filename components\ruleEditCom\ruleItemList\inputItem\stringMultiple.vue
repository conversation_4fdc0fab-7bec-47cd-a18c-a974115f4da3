<template>
  <div class="span-con-else" style="display: inline-flex">
    <a-textarea
      :disabled="disabled"
      :value="value"
      @change="handleChange"
      style="width: 100%"
      auto-size
      v-if="(delElseFlag && !readonly) || (delRead && !elseFlag) || (!elseFlag && !readonly)"
    />
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <div style="display: flex; flex-direction: column">
      <TableConditionOperationBox
        class="table-opera-box"
        :index="index"
        :col="col"
        :rowData="rowData"
        :colData="colData"
        :record="record"
        :elseFlag="elseFlag"
        :delElseFlag="delElseFlag"
        :readonly="readonly"
        v-if="col && col[4] !== '2' && !readonly"
        @operaChange="operaChange"
      />
      <a-dropdown v-if="isTable">
        <ToolOutlined />
        <template #overlay>
          <a-menu @click="onMenuChange" style="width: 105px">
            <a-menu-item key="addVars">
              <span>批量文本增加</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <Teleport to="body">
        <text-add-vars
          :textAddisShow="textAddisShow"
          @textAddHide="textAddHide"
          @addVarsSure="addVarsSure"
          :isTable="isTable"
        />
      </Teleport>
    </div>
  </div>
</template>

<script>
import TextAddVars from "../operationMenu/textAddVars.vue";
import TableConditionOperationBox from "../operationMenu/tableConditionOperationBox";

export default {
  name: "StringMultiple",
  components: {
    TextAddVars,
    TableConditionOperationBox,
  },
  props: {
    value: {
      type: String,
    },
    disabled: {
      type: Boolean,
    },
    isTable: {
      type: Boolean,
      default: false,
    },
    index: Number,
    col: String,
    elseFlag: {
      type: Boolean,
      default: false,
    },
    delElseFlag: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    delRead: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Array,
      default: () => [],
    },
    colData: {
      type: Array,
      default: () => [],
    },
    record: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      textAddisShow: false,
    };
  },
  methods: {
    operaChange(action) {
      if (this.comElse && action === "otherwise") {
        return;
      } else if (action === "otherwise") {
        this.$emit("onChange", {value: "否则", elseFlag: true});
      } else {
        this.$emit("onChange", "");
      }
      let dataIndex = this.rowData.findIndex(
        (item) => item.key === this.record.key
      );
      this.$emit("operaChange", {
        action,
        index: dataIndex,
        col: this.col,
      });
    },
    handleChange(value = null) {
      this.$emit("onChange", value);
    },
    onInputKeyDown(e) {
      e && e.stopPropagation && e.stopPropagation();
    },
    onMenuChange(obj) {
      obj.domEvent.stopPropagation();
      this.textAddisShow = true;
    },
    textAddHide() {
      this.textAddisShow = false;
    },
    addVarsSure(list, key) {
      this.textAddisShow = false;
      this.$emit("onChange", list);
    },
  },
};
</script>
