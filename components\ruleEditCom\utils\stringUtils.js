/**
 * 
 */
class StringUtils {
  constructor() {
    this.pCount = 0
  }
  static getParamName() {
    this.pCount++;
    return "\"p" + this.pCount + "\"";
  }
  static replaceAll(oldString, pattern, replacedStr) {
    if (oldString && pattern) {
      let result = oldString.replace(pattern, replacedStr);
      while (result.indexOf(pattern) >= 0) {
        result = result.replace(pattern, replacedStr);
      }
      return result;
    } else {
      return oldString;
    }
  }
  // xml格式化
  static formatXml(str) {
    let text = str
    //去掉多余的空格
    text = '\n' + text.replace(/(<\w+)(\s.*?>)/g, function ($0, name, props) {
      return name + ' ' + props.replace(/\s+(\w+=)/g, " $1");
    }).replace(/>\s*?</g, ">\n<");

    //把注释编码
    text = text.replace(/\n/g, '\r').replace(/<!--(.+?)-->/g, function ($0, text) {
      let ret = '<!--' + escape(text) + '-->';
      return ret;
    }).replace(/\r/g, '\n');

    //调整格式
    let rgx = /\n(<(([^\?]).+?)(?:\s|\s*?>|\s*?(\/)>)(?:.*?(?:(?:(\/)>)|(?:<(\/)\2>)))?)/mg;
    let nodeStack = [];
    let output = text.replace(rgx, ($0, all, name, isBegin, isCloseFull1, isCloseFull2, isFull1, isFull2) => {
      let isClosed = (isCloseFull1 == '/') || (isCloseFull2 == '/') || (isFull1 == '/') || (isFull2 == '/');
      let prefix = '';
      if (isBegin == '!') {
        prefix = this.getPrefix(nodeStack.length);
      } else {
        if (isBegin != '/') {
          prefix = this.getPrefix(nodeStack.length);
          if (!isClosed) {
            nodeStack.push(name);
          }
        } else {
          nodeStack.pop();
          prefix = this.getPrefix(nodeStack.length);
        }

      }
      let ret = '\n' + prefix + all;
      return ret;
    });

    let prefixSpace = -1;
    let outputText = output.substring(1);

    //把注释还原并解码，调格式
    outputText = outputText.replace(/\n/g, '\r').replace(/(\s*)<!--(.+?)-->/g, function ($0, prefix, text) {
      if (prefix.charAt(0) == '\r')
        prefix = prefix.substring(1);
      text = unescape(text).replace(/\r/g, '\n');
      let ret = '\n' + prefix + '<!--' + text.replace(/^\s*/mg, prefix) + '-->';
      return ret;
    });

    outputText = outputText.replace(/\s+$/g, '').replace(/\r/g, '\r\n');


    return outputText

  }
  static getPrefix(prefixIndex) {
    let span = '    ';
    let output = [];
    for (let i = 0; i < prefixIndex; ++i) {
      output.push(span);
    }

    return output.join('');
  }
  static getPatternCount(text, pattern) {
    if(text) {
      return text.toString().split(pattern).length - 1;
    } else {
      return 0;
    }
  }

}
export default StringUtils
