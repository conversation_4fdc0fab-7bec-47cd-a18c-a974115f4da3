<template>
    <div id="user_copy">
        <a-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleFormRef"
                label-width="100px"
                label-align="right" :label-col="{ span: 4 }"
                :wrapper-col="{ span: 18 }"
        >
            <a-form-item label="登录ID" name="loginId">
                <a-input autocomplete="off" v-model:value="ruleForm.loginId"></a-input>
            </a-form-item>
            <a-form-item label="密码" name="pwd">
                <a-input-password v-model:value="ruleForm.pwd"></a-input-password>
            </a-form-item>
            <a-form-item label="确认密码" name="repeatPwd">
                <a-input-password v-model:value="ruleForm.repeatPwd"></a-input-password>
            </a-form-item>
            <a-form-item label="姓名" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name"></a-input>
            </a-form-item>
            <a-form-item label="业务条线" name="business">
                <a-checkbox-group v-model:value="ruleForm.business" @change="checkboxGet" :filterOption="filterOption" showSearch>
                    <a-checkbox v-for="item in businessList" :key="item.uuid" :value="item.name" :name="item.name">{{ item.name }}</a-checkbox>
                </a-checkbox-group>
            </a-form-item>
            <a-form-item label="身份证号">
                <a-input autocomplete="off" v-model:value="ruleForm.idNumber"></a-input>
            </a-form-item>
            <a-form-item label="性别">
                <a-select v-model:value="ruleForm.grade" placeholder="请选择">
                    <a-select-option
                            v-for="item in options"
                            :key="item.value"
                            :value="item.value"
                    >
                        {{ item.label }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="电邮">
                <a-input autocomplete="off" v-model:value="ruleForm.email"></a-input>
            </a-form-item>
            <a-form-item label="电话">
                <a-input autocomplete="off" v-model:value="ruleForm.phone"></a-input>
            </a-form-item>
            <a-form-item label="移动电话">
                <a-input autocomplete="off" v-model:value="ruleForm.mobile"></a-input>
            </a-form-item>
            <a-form-item label="是否可用">
                <a-radio-group v-model:value="ruleForm.state">
                    <a-radio value="1">启用</a-radio>
                    <a-radio value="0">关闭</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
    import { copySave, checkUserByLoginId, getAllBusinessByLoginUser } from '@/api/userManagement';

    const message = inject('message')
    const props = defineProps({
        id: {
            type: String,
            required: true,
        },
    });
    const businessList = ref<any[]>([]);
    const businessChangeArr = ref<string[]>([]);
    const ruleForm = reactive({
        loginId: '',
        pwd: '',
        repeatPwd: '',
        name: '',
        business: [] as string[],
        idNumber: '',
        grade: 'M',
        email: '',
        mobile: '',
        phone: '',
        state: '1',
    });

    const options = [
        {
            value: 'M',
            label: '男',
        },
        {
            value: 'F',
            label: '女',
        },
    ];

    const rules = {
        loginId: [
            { required: true, message: 'id不能为空', trigger: 'blur' },
            {
                min: 2,
                max: 20,
                message: '长度在 2 到 20 个字符',
                trigger: 'blur',
            },
            {
                validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                    checkUserByLoginId({
                        loginId: value
                    }).then(res => {
                        if (res.data) {
                            callback(new Error("登录ID已存在，请重新输入"));
                        } else {
                            callback();
                        }
                    });
                },
                trigger: 'blur',
            },
        ],
        pwd: [
            { required: true, message: '密码不能为空', trigger: 'blur' },
            {
                min: 5,
                max: 20,
                message: '长度在 5 到 20 个字符',
                trigger: 'blur',
            },
        ],
        repeatPwd: [
            { required: true, message: '密码不能为空', trigger: 'blur' },
            { pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,20}$/, message: '密码长度为8-20位,必须包含字母大写、小写字母、特殊符号和数字' },
            {
                validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                    if (value !== ruleForm.pwd) {
                        callback(new Error("两次密码不一致"));
                    } else {
                        callback();
                    }
                },
                trigger: 'blur',
            },
        ],
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        business: [
            { required: true, message: '业务条线不能为空', trigger: 'change' },
        ],
    };

    const ruleFormRef = ref();

    const submitFun = async (callback) => {
        try {
            await ruleFormRef.value.validate();
            const prams = {
                loginId: ruleForm.loginId,
                password: ruleForm.pwd,
                name: ruleForm.name,
                businessLineIds: businessChangeArr.value.toString(),
                idNumber: ruleForm.idNumber,
                gender: ruleForm.grade,
                email: ruleForm.email,
                mobile: ruleForm.mobile,
                phone: ruleForm.phone,
                state: ruleForm.state,
                isAdmin: 0,
            };
            if (props.id) {
                await copySave(prams, props.id).then((res)=>{
                    if (res.code === 20000){
                        message.success('复制成功')
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }else {
                        message.error(res.data)
                    }
                });
            }
        } catch (error) {
            console.log('error submit!!', error);
        }
    };

    const postsel = async () => {
        const res = await getAllBusinessByLoginUser();
        businessList.value = res.data;
    };

    const checkboxGet = (name: string[]) => {
        businessChangeArr.value = [];
        name.forEach((n) => {
            const item = businessList.value.find((i) => i.name === n);
            if (item) {
                businessChangeArr.value.push(item.uuid);
            }
        });
    };

    onMounted(() => {
        postsel();
    });
    defineExpose({
        submitFun,
    });
</script>
