<!-- 公告修改 -->

<template>
    <div id="data_edit">
        <a-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
            <a-form-item label="标题" name="title">
                <a-input autocomplete="off" v-model:value="form.title" placeholder="请输入标题"></a-input>
            </a-form-item>
            <a-form-item label="作者" name="writer">
                <a-input autocomplete="off" v-model:value="form.writer" placeholder="请输入作者"></a-input>
            </a-form-item>
            <a-form-item label="状态" name="bulState">
                <a-select v-model:value="form.bulState" placeholder="请选择状态">
                    <a-select-option value="writting">编辑中</a-select-option>
                    <a-select-option value="publishing">已发布</a-select-option>
                    <a-select-option value="cancel">无效</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="内容" name="content">
                <a-textarea :auto-size="{ minRows: 2, maxRows: 8 }" :show-word-limit="true" :maxlength="500"
                            v-model:value="form.content"></a-textarea>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
    import {updateBulletin} from "@/api/bulletin";

    const message = inject('message')

    const router = useRouter();

    const form = reactive({
        id:"",
        title:"",
        bulState:"",
        writer:"",
        content:"",
    });


    const rules = {
        title: [
            { required: true, message: "标题不能为空", trigger: "blur" },
            { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        writer: [
            { required: true, message: "作者不能为空", trigger: "blur" },
            { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        bulState: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        content: [
            { required: true, message: "内容不能为空", trigger: "blur" },
            { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
    };


    const props = defineProps({
        datar: {
            type: Object,
        },
    });

    watch(
        () => props.datar,
        (newValue) => {
            if (newValue) {
                let newForm = {
                    id:"",
                    title:"",
                    bulState:"",
                    writer:"",
                    content:"",
                };
                Object.assign(form, newForm);
                console.log(props.datar)
                Object.assign(form, props.datar);
            }
        },
        {
            immediate: true,
        }
    );


    const submitFun = (callback) => {
        ruleForm.value.validate().then(() => {
            updateBulletin({
                ...form
            }).then((res) => {
                if (res.code === 20000) {
                    message.success(res.data);
                    if (typeof callback === 'function') {
                        callback();
                    }
                } else {
                    message.error(res.data);
                }
            })
        }).catch((error) => {
            console.log(error);
        })
    };


    const ruleForm = ref(null);

    defineExpose({
        submitFun
    });
</script>

<style lang="scss" scoped>
    #data_edit ::v-deep {

        .ant-input,
        .ant-select,
        .ant-upload-dragger,
        .ant-upload-list,
        .ant-textarea {
            width: 400px;
        }

        .ant-textarea {
            height: 400px;
        }

        .ant-upload-dragger {
            height: 100px;
        }

        .ant-upload-dragger .anticon-upload {
            margin: 0;
            line-height: 50px;
            font-size: 50px;
        }

        .jarUpload.hidden .ant-upload {
            display: none;
        }

        .ant-tabs {
            width: 70%;
            min-height: 100px;
            margin: 30px 30px 30px 120px;
        }

        .ant-textarea,
        textarea {
            height: 150px !important;
        }

        .ant-tabs-card {
            box-shadow: unset;
        }

        .params_form {
            border-bottom: 1px solid #dcdfe6;

            h4 {
                font-weight: unset;

                span {
                    margin-left: 100px;
                }

                span.primary {
                    color: #409eff;
                }

                span.danger {
                    color: #f56c6c;
                }
            }

            label {
                color: #99a9bf;
            }

            span {
                color: #606266;
            }
        }

        .params_form:last-child {
            border-bottom: unset;
        }

        .form_flex_div,
        .form_flex_div .ant-form-item {
            display: flex;
            flex-direction: row;
            flex: 1;

            .ant-form-item {
                overflow: auto;
            }
        }

        .ant-tree {
            font-size: 14px;
        }

        .ant-main {
            padding: 0 0 10px 0;
        }
    }
</style>
