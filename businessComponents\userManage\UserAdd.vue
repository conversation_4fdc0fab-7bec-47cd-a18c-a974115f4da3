<template>
    <div id="user_add">
        <a-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleFormRef"
                label-align="right" :label-col="{ span: 4 }"
                :wrapper-col="{ span: 18 }"
        >
            <a-form-item label="登录ID" name="loginId">
                <a-input autocomplete="off" v-model:value="ruleForm.loginId"></a-input>
            </a-form-item>
            <a-form-item label="密码" name="pwd" v-show="passAdd">
                <a-input-password v-model:value="ruleForm.pwd"></a-input-password>
            </a-form-item>
            <a-form-item label="确认密码" name="repeatPwd" v-show="repePass">
                <a-input-password v-model:value="ruleForm.repeatPwd"></a-input-password>
            </a-form-item>
            <a-form-item label="姓名" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name"></a-input>
            </a-form-item>
            <a-form-item label="业务条线" name="business">
                <a-checkbox-group v-model:value="ruleForm.business" @change="checkboxGet" :filterOption="filterOption" showSearch>
                    <a-checkbox v-for="item in businessList" :key="item.uuid" :value="item.name" :name="item.name">{{ item.name }}</a-checkbox>
                </a-checkbox-group>
            </a-form-item>
            <a-form-item label="身份证号">
                <a-input autocomplete="off" v-model:value="ruleForm.idNumber"></a-input>
            </a-form-item>
            <a-form-item label="性别">
                <a-select v-model:value="ruleForm.grade" placeholder="请选择">
                    <a-select-option v-for="item in options" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="电邮">
                <a-input autocomplete="off" v-model:value="ruleForm.email"></a-input>
            </a-form-item>
            <a-form-item label="电话">
                <a-input autocomplete="off" v-model:value="ruleForm.phone"></a-input>
            </a-form-item>
            <a-form-item label="移动电话">
                <a-input autocomplete="off" v-model:value="ruleForm.mobile"></a-input>
            </a-form-item>
            <a-form-item label="是否可用">
                <a-radio-group v-model:value="ruleForm.state">
                    <a-radio value="1">启用</a-radio>
                    <a-radio value="0">关闭</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
    import { saveOrUpdate, checkUserByLoginId, getAllBusinessByLoginUser, getLoginType, checkUser } from "@/api/userManagement";
    const message = inject('message')
    const props = defineProps({

    })
    interface RuleForm {
        loginId: string;
        pwd: string;
        repeatPwd: string;
        name: string;
        business: string[];
        idNumber: string;
        grade: string;
        email: string;
        mobile: string;
        phone: string;
        state: string;
    }

    const ruleForm = reactive<RuleForm>({
        loginId: "",
        pwd: "",
        repeatPwd: "",
        name: "",
        business: [],
        idNumber: "",
        grade: "M",
        email: "",
        mobile: "",
        phone: "",
        state: "1",
    });

    const options = [
        {
            value: "M",
            label: "男",
        },
        {
            value: "F",
            label: "女",
        },
    ];

    const rules = {
        loginId: [
            { required: true, message: "id不能为空", trigger: "blur" },
            {
                min: 2,
                max: 20,
                message: "长度在 2 到 20 个字符",
                trigger: "blur",
            },
            {
                validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                    checkUserByLoginId({
                        loginId: value
                    }).then(res => {
                        if (res.data) {
                            callback(new Error("登录ID已存在，请重新输入"));
                        } else {
                            callback();
                        }
                    });
                },
                trigger: "blur",
            }
        ],
        pwd: [
            { required: true, message: "密码不能为空", trigger: "blur" },
            { pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,20}$/, message: '密码长度为8-20位,必须包含字母大写、小写字母、特殊符号和数字' },
        ],
        repeatPwd: [
            { required: true, message: "密码不能为空", trigger: "blur" },
            {
                validator: (rule: any, value: string, callback: (error?: Error) => void) => {
                    if (value !== ruleForm.pwd) {
                        callback(new Error("两次密码不一致"));
                    } else {
                        callback();
                    }
                },
                trigger: "blur",
            },
        ],
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        business: [
            { required: true, message: "业务条线不能为空", trigger: "change" },
        ],
    };

    const businessList = ref([]);
    const businessChangeArr = ref([]);
    const passAdd = ref(true);
    const repePass = ref(true);
    const ruleFormRef = ref();

    const getUserInfo = () => {
        getLoginType().then((res) => {
            if (res.data && res.data === '04') {
                passAdd.value = false;
                repePass.value = false;
            } else {
                passAdd.value = true;
                repePass.value = true;
            }
        });
    };

    const submitFun = (callback) => {
        ruleFormRef.value.validate().then(valid => {
            if (valid) {
                const prams = {
                    loginId: ruleForm.loginId,
                    password: ruleForm.pwd,
                    name: ruleForm.name,
                    businessLineIds: businessChangeArr.value.toString(),
                    idNumber: ruleForm.idNumber,
                    gender: ruleForm.grade,
                    email: ruleForm.email,
                    mobile: ruleForm.mobile,
                    phone: ruleForm.phone,
                    state: ruleForm.state,
                    isAdmin: 0,
                };
                const checkParams = {
                    id: "",
                    loginId: ruleForm.loginId,
                    name: ruleForm.name,
                    idNumber: ruleForm.idNumber,
                };
                checkUser(checkParams).then((res) => {
                    if (res.code === 20000) {
                        saveOrUpdate(prams).then(() => {
                           message.success('新增成功')
                            if (typeof callback === 'function') {
                                callback();
                            }
                        });
                    } else {
                        // 错误提示
                        message.error(res.data);
                    }
                });
            }
        });
    };

    const postsel = () => {
        getAllBusinessByLoginUser().then((res) => {
            businessList.value = res.data;
        });
    };

    const checkboxGet = (name: string[]) => {
        businessChangeArr.value = [];
        name.forEach((n) => {
            const item = businessList.value.find(i => i.name === n);
            if (item) {
                businessChangeArr.value.push(item.uuid);
            }
        });
    };
    onMounted(() => {
        postsel();
        getUserInfo();
    });
    defineExpose({
        submitFun,
    });
</script>

