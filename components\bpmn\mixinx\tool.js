// tool.js
export default {
  inject: {
    message: { default: () => {} },
  },
  data() {
    return {
      defaultZoom: 1,
      recoverable: false,
      revocable: false,
    };
  },
  methods: {
    downloadProcessAsXml() {
      this.downloadProcess("xml");
    },
    downloadProcessAsBpmn() {
      this.downloadProcess("bpmn");
    },
    downloadProcessAsSvg() {
      this.downloadProcess("svg");
    },
    // 下载流程图到本地
    async downloadProcess(type, name) {
      try {
        const _this = this;
        // 按需要类型创建文件并下载
        if (type === "xml" || type === "bpmn") {
          const { err, xml } = await this.bpmnModeler.saveXML();
          // 读取异常时抛出异常
          if (err) {
            console.error(`[Process Designer Warn ]: ${err.message || err}`);
          }
          let { href, filename } = _this.setEncoded(
            type.toUpperCase(),
            name,
            xml
          );
          downloadFunc(href, filename);
        } else {
          const { err, svg } = await this.bpmnModeler.saveSVG();
          // 读取异常时抛出异常
          if (err) {
            return console.error(err);
          }
          let { href, filename } = _this.setEncoded("SVG", name, svg);
          downloadFunc(href, filename);
        }
      } catch (e) {
        console.error(`[Process Designer Warn ]: ${e.message || e}`);
      }
      // 文件下载方法
      function downloadFunc(href, filename) {
        if (href && filename) {
          let a = document.createElement("a");
          a.download = filename; //指定下载的文件名
          a.href = href; //  URL对象
          a.click(); // 模拟点击
          URL.revokeObjectURL(a.href); // 释放URL 对象
        }
      }
    },
    // 保存
    toSaveFlow() {
      if (this.type === '4') {
        this.saveFlow();
      } else {
        this.checkFlow();
        if (this.flowErrorMsg.length !== 0) {
          this.$confirm("规则流绘制错误, 是否继续保存?", "提示", {
            confirmButtonText: "保存",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.saveFlow();
            })
            .catch(() => {});
        } else {
          this.saveFlow();
        }
      }
    },
    elementsAlign(align) {
      const Align = this.bpmnModeler.get("alignElements");
      const Selection = this.bpmnModeler.get("selection");
      const SelectedElements = Selection.get();
      if (!SelectedElements || SelectedElements.length <= 1) {
        this.$message.warning("请框选 或 按住Ctrl键选择多个元素对齐");
        return;
      }
      Align.trigger(SelectedElements, align);
    },
    processZoomIn(_defaultZoom, zoomStep = 0.1) {
      let newZoom = Math.floor(_defaultZoom * 100 + zoomStep * 100) / 100;
      if (newZoom > 4) {
        return;
      }
      this.defaultZoom = newZoom;
      this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
    },
    processZoomOut(_defaultZoom, zoomStep = 0.1) {
      let newZoom = Math.floor(_defaultZoom * 100 - zoomStep * 100) / 100;
      if (newZoom < 0.2) {
        return;
      }
      this.defaultZoom = newZoom;
      this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
    },
    processReZoom() {
      this.defaultZoom = 1;
      this.bpmnModeler.get("canvas").zoom("fit-viewport", "auto");
    },
    processRedo() {
      this.bpmnModeler.get("commandStack").redo();
    },
    processUndo() {
      this.bpmnModeler.get("commandStack").undo();
    },
    processRestart() {
      this.recoverable = false;
      this.revocable = false;
      this.createNewDiagram(null).then(() =>
        this.bpmnModeler.get("canvas").zoom(1, "auto")
      );
    },
    // 加载本地文件
    importLocalFile(refFile) {
      const that = this;
      const file = refFile.files[0];
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = function () {
        let xmlStr = this.result;
        that.createNewDiagram(xmlStr);
      };
    },
    // 根据所需类型进行转码并返回下载地址
    setEncoded(type, filename = "diagram", data) {
      const encodedData = encodeURIComponent(data);
      return {
        filename: `${filename}.${type}`,
        href: `data:application/${
          type === "svg" ? "text/xml" : "bpmn20-xml"
        };charset=UTF-8,${encodedData}`,
        data: data,
      };
    },
  },
};
