<template>
  <div class="toolBar">
    <a-tabs
      :activeKey="viewStatus"
      size="small"
      @change="(key) => changeViewStatus(String(key))"
      v-model:value="tabActiveKey"
    >
      <a-tab-pane tab="预定义" key="predefineView"></a-tab-pane>
      <a-tab-pane tab="规则内容" key="designView"></a-tab-pane>
      <a-tab-pane tab="DRL" key="codeView"></a-tab-pane>
      <a-tab-pane tab="校验结果" key="ValidateView"></a-tab-pane>
      <a-tab-pane tab="规则文本" key="ruleTextView"></a-tab-pane>
      <template #rightExtra>
        <a-tooltip placement="bottom" :title="'全屏编辑'" :open="tooltipVisible.fullscreen">
          <a-button
                  size="small"
                  @click="fullScreen"
                  v-if="showFullScreen && !props.isFullscreen"
                  @mouseenter="tooltipVisible.fullscreen = true"
                  @mouseleave="tooltipVisible.fullscreen = false"
          >
            <template #icon>
              <FullscreenOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light">
          <template #title>导入</template>
          <a-button
            v-show="viewStatus !== 'predefineView' && locked"
            size="small"
            @click="handleImport"
          >
            <template #icon>
              <ImportOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light">
          <template #title>导出</template>
          <a-button
            v-show="viewStatus !== 'predefineView' && locked"
            size="small"
            @click="handleExport"
            ><template #icon> <ExportOutlined /> </template
          ></a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light">
          <template #title>{{ locked ? "锁定" : "解锁" }}</template>
          <a-button size="small" @click="changeLock">
            <template #icon>
              <LockOutlined v-if="locked" />
              <UnlockOutlined v-else />
            </template>
          </a-button>
        </a-tooltip>

        <a-button v-if="locked" size="small" @click="validate">
            验证
          </a-button>
        <a-button v-if="locked" type="primary" size="small" @click="toSave"
          >保存
        </a-button>
        <a-button
                v-if="locked && checkPermi([RULE_PERMISSION.RULE.SUBMIT])"
                type="primary"
                size="small"
                @click="handleSubmitRule()"
        >提交</a-button>
      </template>
    </a-tabs>
    <a-modal
      v-model:open="dialogUploadVisible"
      title="导入决策表"
      :closable="attrBoolean"
      @cancel="closeUpload"
      :footer="null"
      class="table-modal-upload"
    >
      <a-form class="dialogForm">
        <a-form-item name="choiceFile">
          <a-upload-dragger
            accept=".xlsx, .xls"
            :multiple="false"
            v-model:fileList="fileList"
            @change="uploadChange"
            @remove="fileRemove"
            :customRequest="customUpload"
            :class="uploadHidden ? 'hid eruleUpload' : 'eruleUpload'"
            ref="uploadRef"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined></inbox-outlined>
            </p>
            <div class="a-upload__text">
              将文件拖到此处，或
              <em>点击上传</em>
            </div>
          </a-upload-dragger>
        </a-form-item>
      </a-form>
      <div class="dialog-footer" style="text-align:right">
        <a-button size="small" @click="closeUpload">取 消</a-button>
        <a-button size="small" type="primary" @click="dialogUploadSubmit" style="margin-left:15px"
          >确 定</a-button
        >
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import type { UploadChangeParam } from 'ant-design-vue'
import globalEventEmitter from '~/utils/eventBus';
import { RULE_SUBMIT_ACTION } from '@/consts/globalEventConsts';
import { CLOSE_POPOVER } from '@/consts/globalEventConsts';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  ImportOutlined,
  ExportOutlined,
  LockOutlined,
  UnlockOutlined,
  InboxOutlined
} from '@ant-design/icons-vue';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";
import qs from "qs/lib/index";
import { submitRule, getRuleByUuid } from '@/api/rule_base';
const message = inject("message") as any;
// Props 定义
interface Props {
  uuid: string;
  mode?: string;
  viewStatus?: string;
  locked?: boolean;
  showFullScreen?: boolean;
  isFullscreen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  mode: '',
  viewStatus: '',
  locked: false,
  showFullScreen: true,
  isFullscreen: false
})

// Emits 定义
const emit = defineEmits<{
  (e: 'changeViewStatus', key: string): void;
  (e: 'changeLock'): void;
  (e: 'validate'): void;
  (e: 'toSave'): void;
  (e: 'handleExport'): void;
  (e: 'handleImport', form: FormData): void;
  (e: 'fullScreen'): void;
}>()

const dialogUploadVisible = ref(false)
const uploadHidden = ref(false)
const tabActiveKey = ref('designView')
const attrBoolean = ref(true)
const upForm = ref<FormData | null>(null)
const fileList = ref<any[]>([])
const uploadRef = ref<any>(null)
const tooltipVisible = ref({
  fullscreen: false
})

const customUpload = (options: any) => {
  const { file, onSuccess, onError } = options

  // 检查文件格式
  const aExtension = ["xlsx", "xls"]

  // 处理文件类型
  let fileName = '';
  if (typeof file === 'string') {
    fileName = file;
  } else if (file instanceof File) {
    fileName = file.name;
  } else if (file && typeof file === 'object' && 'name' in file && typeof file.name === 'string') {
    fileName = file.name;
  } else {
    message.error("不支持的文件类型");
    onError && onError(new Error("不支持的文件类型"));
    return;
  }

  const isValidFormat = aExtension.some(ext => fileName.toLowerCase().endsWith(ext))

  if (!isValidFormat) {
    message.error("文件格式不正确！")
    onError && onError(new Error("文件格式不正确"));
    return
  }

  // 创建FormData
  upForm.value = new FormData()
  upForm.value.append("ruleUuid", props.uuid)
  upForm.value.append("file", file as any)
  uploadHidden.value = true

  // 通知上传组件文件上传成功
  setTimeout(() => {
    if (onSuccess) {
      onSuccess("上传成功");
    }
  }, 100);
}

// 文件上传相关方法
const uploadChange = (info: UploadChangeParam) => {

  fileList.value = info.fileList.slice(-1) // 只保留最后一个文件

  if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败`)
  }
}

const resetUploadState = () => {
  uploadHidden.value = false
  upForm.value = null
  fileList.value = []
}

const fileRemove = () => {
  uploadHidden.value = false
  upForm.value = null
}

// 其他功能方法
const dialogUploadSubmit = () => {
  if (!upForm.value) {
    message.warning('请先上传文件')
    return
  }
  emit('handleImport', upForm.value)
  dialogUploadVisible.value = false
  resetUploadState()
}

const toSave = () => {
  emit('toSave')
}

const validate = () => {
  try {
    // 先触发验证事件
    emit('validate')
    // 添加成功提示
    message.success('验证已开始，请等待结果')
    // 延迟一小段时间后再切换视图，确保验证事件先被处理
    setTimeout(() => {
      changeView()
    }, 100)
  } catch (error) {
    console.error('验证过程中出现错误:', error)
    message.error('验证过程中出现错误')
  }
}

const changeView = () => {
  tabActiveKey.value = 'ValidateView'
  changeViewStatus('ValidateView')
}

const handleImport = () => {
  dialogUploadVisible.value = true
  resetUploadState()
}

const handleExport = () => {
  emit('handleExport')
}

const changeLock = () => {
  emit('changeLock')
}

const changeViewStatus = (key: string) => {
  emit('changeViewStatus', key);
  tabActiveKey.value = key;
  globalEventEmitter.emit(CLOSE_POPOVER)

  // 当切换到预定义视图时，延迟执行一次DOM元素位置修正
  if (key === 'predefineView') {
    setTimeout(() => {
      const conditionElements = document.querySelectorAll('.pre-title');
      if (conditionElements && conditionElements.length > 0) {
        conditionElements.forEach(el => {
          // 确保元素可见
          const element = el as HTMLElement;
          element.style.position = 'relative';
          element.style.zIndex = '10';
          element.style.display = 'block';
        });
      }
    }, 200);
  }
}

const closeUpload = () => {
  resetUploadState()
  dialogUploadVisible.value = false
}

const fullScreen = () => {
  tooltipVisible.value.fullscreen = false
  emit('fullScreen')
}
// 提交规则
const urlFlag = ref(true);
const ruleInfoSub = ref()
// 监听
watch(() => window.location.href, (newUrl) => {
  urlFlag.value = !newUrl.includes('ruleContent');
}, {
  immediate: true
});
// 提交数据前状态判断
function checkSubmitStatus(ruleInfo, type) {
  let uuids = '';
  //状态判断
  let status = '';

  if (ruleInfo && ruleInfo.uuid) {
    uuids = ruleInfo.uuid;
    if (ruleInfo.status === '已删除') {
      status = ruleInfo.status;
    }
    //锁定/解锁数据不可再次提交
    if ((ruleInfo.lockId && type === '已锁定') || (!ruleInfo.lockId && type === '已解锁')) {
      status = type;
    }
  }
  return [uuids, status];
}
// 提交规则
// 添加一个防抖变量
const isSubmitting = ref(false);

function handleSubmitRule() {
  // 1. 先检查是否正在提交
  if(isSubmitting.value) {
    console.log('[DEBUG] 提交操作被阻止：正在提交中');
    return;
  }

  // 2. 设置提交状态
  isSubmitting.value = true;

  try {
    if(urlFlag.value) {
      // 3. 检查必要的属性是否存在
      if (!props.uuid) {
        console.warn('[DEBUG] 提交失败：缺少必要的uuid属性');
        return;
      }

      // 4. 构建事件数据
      const eventData = {
        record: {
          uuid: props.uuid,
          status: props.status || '',
          ruleName: props.ruleTitle || ''
        },
        tabKey: '编辑' + props.uuid
      };

      console.log('[DEBUG] 准备发送事件，数据：', eventData);

      // 5. 使用Promise包装事件发送
      Promise.resolve().then(() => {
        globalEventEmitter.emit(RULE_SUBMIT_ACTION, eventData);
        console.log('[DEBUG] 事件发送完成');
      }).catch(error => {
        console.error('[DEBUG] 事件发送失败：', error);
      });

    } else {
      // 先获取最新的规则信息，然后再进行状态检查和提交
      getRuleByUuid({
        uuids: props.uuid,
      }).then((res) => {
        if (res.code === 20000) {
          const ruleData = res.data;

          // 检查状态
          let [uuids, status] = checkSubmitStatus(ruleData, '');

          if (status) {
            message.error('状态为' + status + '规则不可提交');
            return;
          }

          if (uuids) {
            let pars = qs.stringify({
              uuids: uuids,
            });

            submitRule(pars).then((res) => {
              if (res.code === 20000) {
                if (res.data == '规则置为提交待审核成功！') {
                  message.success('规则置为提交待审核成功，不允许操作，即将关闭页面！');
                  // 添加2秒延时，让用户有时间看到提示信息
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                } else {
                  message.success(res.data);
                }
              } else {
                message.error(res.data);
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[DEBUG] 提交过程发生错误：', error);
  } finally {
    // 6. 确保状态重置
    setTimeout(() => {
      isSubmitting.value = false;
      console.log('[DEBUG] 提交状态已重置');
    }, 1000);
  }
}
</script>

<style lang="scss" scoped>
.eruleUpload {
  text-align: center;
}
.a-dialog {
  width: 580px;
}
.dialogForm {
  .a-form-item {
    margin-bottom: 0;
  }
}
.a-upload-dragger {
  height: 100px;
  width: 410px;
}
.a-upload-dragger .el-icon-upload {
  margin: 0;
  line-height: 50px;
  font-size: 50px;
}
:deep(.ant-tabs) {
  .ant-tabs-nav-wrap{
    padding-left: 17px !important;
  }
  .ant-tabs-extra-content{
    margin-right: 17px;
  }
}
</style>
<style lang="scss">
.table-modal-upload .ant-upload-list-item {
  display: flex;
  align-items: center;
  .ant-upload-list-item-name{
    flex: unset !important;
    width: unset !important;
  }
}
.hid.eruleUpload.ant-upload {
    display: none !important;
  }
</style>

<style scoped>
/* 按钮放大20% */
:deep(.ant-tabs-extra-content .ant-btn) {
  transform: scale(1.2);
  margin: 0 8px; /* 增加间距 */
  font-size: 12px; /* 添加较小的字体大小 */
}

/* 为第一个按钮添加左边距 */
:deep(.ant-tabs-extra-content .ant-btn:first-child) {
  margin-left: 4px;
}

/* 为最后一个按钮添加右边距 */
:deep(.ant-tabs-extra-content .ant-btn:last-child) {
  margin-right: 4px;
}
</style>
