<!-- 发布环境管理 -->

<script setup lang="ts">
    import { pubList, pubDel, pubstart } from "@/api/pub_environment";
    import PublishAdd from "@/businessComponents/publishEnvironmentManage/PublishAdd";
    import PublishUpdate from "@/businessComponents/publishEnvironmentManage/PublishUpdate";
    import qs from "qs";
    import { checkPermi } from "@/directive/permission/permission";

    definePageMeta({
        title: '发布环境管理'
    })

    const message = inject('message')
    const modal = inject('modal')

    // 表格列配置
    const tableColumns = [
        {
            title: '环境名称',
            dataIndex: 'environmentName',
            align: 'left',
            key: 'environmentName',
            width: 200,
        },
        {
            title: '业务条线',
            dataIndex: 'businessLineName',
            align: 'left',
            key: 'businessLineName',
            width: 120
        },
        {
            title: '描述',
            dataIndex: 'desc',
            align: 'left',
            key: 'desc',
            ellipsis: true,
            width:200,
            checked: false
        },
        {
            title: '数据源名称',
            dataIndex: 'jndiName',
            align: 'left',
            key: 'jndiName',
            width: 300
        },
        {
            title: '环境类型',
            dataIndex: 'environmentTypeName',
            align: 'center',
            key: 'environmentTypeName',
            width:80,
        },
        {
            title: '停用启用开关',
            dataIndex: 'isUse',
            align: 'center',
            key: 'isUse',
            width:150,
        },
    ];

    // 搜索配置
    const searchConfig = {
        simpleSearchField: {
            label: '环境名称',
            field: 'name'
        },
        advancedSearchFields: [
            {
                label: '环境名称',
                field: 'name',
                compType: 'input'
            }
        ]
    };

    // 获取列表数据
    const fetchPubList = async (params: Record<string, any> = {}) => {
        try {
            const res = await pubList({
                environmentName: params.name,
                page: params.page || 1,
                several: params.pageSize || 10,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取发布环境列表失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const onDelete = (record) => {
        modal.confirm({
            title: '确定删除吗？',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                pubDel(
                    qs.stringify({
                        id: record.id,
                    })
                ).then(() => {
                    message.success("删除成功");
                    listLayout.value?.refresh();
                });
            }
        });
    };

    const pubtab = (vid) => {
        const { id, isUse } = vid;
        let data = qs.stringify({
            id: id,
            isUse: isUse,
        });
        let checkStr = (isUse==='1'?'停用':'启用');
        modal.confirm({
            title: () => "提示",
            content: () => "您确定要"+checkStr+"发布环境状态吗?",
            okText: () => '确定',
            okType: 'warning',
            cancelText: () => '取消',
            onOk() {
                pubstart(data).then((res) => {
                    message.success(checkStr+'成功');
                    listLayout.value?.refresh();
                });
            },
            onCancel() {
            },
        });
    };

    const modalType = ref(''); // 对话框类型：'add' 或 'update'
    const publish = ref({});
    const isModalVisible = ref(false); // 对话框显示状态

    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'update') {
            publish.value = record;
        }
        isModalVisible.value = true;
    };

    const handleCancel = () => {
        isModalVisible.value = false;
    };

    // 发布环境新增组件
    const publishAddComponent = ref()

    // 发布环境更新组件
    const publishUpdateComponent = ref()

    // 处理modal ok 事件
    function handleModalOk() {
        let submitFun
        switch (modalType.value) {
            case 'add':
                submitFun = publishAddComponent.value.submitFun;
                break;
            case 'update':
                submitFun = publishUpdateComponent.value.submitFun;
                break;
        }
        submitFun && submitFun(() => {
            listLayout.value?.refresh();
            isModalVisible.value = false;
        });
    }

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: () => {},
        // 添加事件
        addNewEvent: () => showModal('add'),
        formHandler: {
            queryMethod: fetchPubList
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record) => {
        return [
            {
                key: 'delete',
                label: '删除',
                onClick: () => onDelete(record)
            },
            {
                key: 'update',
                label: '修改',
                onClick: () => showModal('update', record)
            }
        ];
    };

    // 组件引用
    const listLayout = ref(null);

    const customRenderColumns = ['desc', 'environmentTypeName', 'isUse'];

</script>

<template>
    <ListPage
        ref="listLayout"
        title="发布环境管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumns"
        :queryMethod="fetchPubList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="true"
    >
        <template #desc="{ record }">
            <a-tooltip placement="topLeft" :title="record.desc">
                {{ record.desc }}
            </a-tooltip>
        </template>

        <template #environmentTypeName="{ record }">
            <a-tag :color="record.environmentTypeName === '生产环境' ? 'green' : (record.environmentTypeName === '测试环境' ? 'orange' : '')">
                {{ record.environmentTypeName }}
            </a-tag>
        </template>

        <template #isUse="{ record }">
            <div style="height: 32px; display: flex; justify-content: center; align-items: center;">
                <a-switch
                    :checked="record.isUse === '1'"
                    @click="pubtab(record)"
                    size="default"
                />
            </div>
        </template>
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal :visible="isModalVisible" :title="modalType === 'add' ? '新增发布环境' : '修改发布环境'" @ok="handleModalOk"
             v-if="isModalVisible"
             @cancel="handleCancel" okText="保存">
        <div style="max-height: 60vh; overflow-y: auto;">
            <PublishAdd ref="publishAddComponent" v-if="modalType === 'add' && isModalVisible"/>
            <PublishUpdate ref="publishUpdateComponent" v-if="modalType === 'update' && isModalVisible" :data="publish" />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>
