<template>
  <span class="setValueCom">
    <div class="setValueComLeft">
      <VariableCom
        :pos="pos + '_setValueCom_0'"
        :locked="locked"
        :isTrack="isTrack"
        :dataSource="leftOption"
        :hideFrontBtn="true"
        :hideEndBtn="true"
        signValue=""
        @onChange="onLeftValueChange"
      />
    </div>
    <span v-if="valueType" class="operator eq">
      等于
      <div class="setValueComRight">
        <VariableCom
          :pos="pos + '_setValueCom_1'"
          :locked="locked"
          :isTrack="isTrack"
          :dataSource="rightOption"
          :hideFrontBtn="false"
          :hideEndBtn="false"
          signValue=""
          :enumDictName="rightOption && rightOption.enumDictName"
          @onChange="onRightValueChange"
          @onCalSignChange="onCalSignChange"
        />
      </div>
    </span>
  </span>
</template>

<script setup>
import VariableCom from "@/components/ruleEditCom/variable/variableCom.vue";
import * as util from "@/components/ruleEditCom/utils/util";

// 定义 props
const props = defineProps({
  actionData: {
    type: Array,
    default: () => [],
  },
  pos: {
    type: String,
    required: true,
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
});

// 定义 emit 事件
const emit = defineEmits(['onChange']);

// 计算属性
const valueType = computed({
  get() {
    return getTxt(props.actionData[props.actionData.length - 1].valueType);
  },
  set(value) {
    return value;
  },
});

// 计算 leftOption 和 rightOption
const leftOption = computed(() => props.actionData[0]);
const rightOption = computed(() => props.actionData[1]);

// 方法
const onChange = (pos, newActionItem) => {
  emit("onChange", pos, newActionItem);
};

const getFinalValue = (variable = {}) => {
  const { value = [], next } = variable;
  if (next) {
    return getFinalValue(next);
  }
  return value[value.length - 1];
};

const onLeftValueChange = (pos, newVariable, conditionValueType) => {
  const { dictName } = newVariable;
  const domain = util.getRealDomain(newVariable);
  const _initRightVariable = {
    value: "",
    valueType: conditionValueType,
    variableType: "constant",
    enumDictName: domain || dictName || null,
  };
  valueType.value = getTxt(conditionValueType);
  onChange(pos, [newVariable, _initRightVariable]);
};

const getTxt = (type) => {
  if (!type) {
    return "";
  }
  switch (type) {
    case "Date":
      return "日期";
    case "Short":
    case "Integer":
    case "Float":
    case "Double":
    case "BigDecimal":
    case "Long":
      return "数字";
    case "Boolean":
      return "布尔值";
    default:
      return "字符串";
  }
};

const onRightValueChange = (pos, variable) => {
  const newVariable = variable;
  const [leftOption] = props.actionData;
  onChange(pos, [leftOption, newVariable]);
};

const onCalSignChange = (childPos, sign, newParam) => {
  const pathArr = childPos.split("_");
  const index = Number(pathArr[pathArr.length - 1]);
  const targetOption = props.actionData[index];
  const valueType = util.getRealValueType(targetOption);

  if (sign === "delete" || !newParam) {
    return;
  }

  const _newVariable = {
    valueType,
    variableType: "expression",
    expressionTreeData: {
      type: "expression",
      symbols: [sign],
      params: [{ type: "variable", data: targetOption }, newParam],
    },
  };

  if (index === 0) {
    onLeftValueChange(props.pos, _newVariable, valueType);
  } else {
    onRightValueChange(props.pos, _newVariable);
  }
};
</script>
