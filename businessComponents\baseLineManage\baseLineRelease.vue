<!-- 基线发布组件 -->
<script setup lang="ts">
import {
    getBaseLineRel,
    getSuffix,
    getRelSub,
    getExport,
    getSnapList,
    getInCompleteRuleForEng
} from "@/api/baseline_Management";
import { pubEnvUUid } from '@/api/pub_environment';
import { getLoginType } from '@/api/userManagement';
import qs from 'qs';
import { reactive, ref, onMounted, watch, onBeforeUnmount, nextTick, inject } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import TableSkeleton from '@/components/TableSkeleton.vue';
import Pagination from '@/components/Pagination.vue';
import useResize from '@/composables/useResize';
import DateTimePicker from '@/components/DateTimePicker.vue';
import {saveOrUpdateNotifyConfig,getNotifyConfigById} from '@/api/notifyConfig';
import { getUsersWithNotification } from "@/api/userManagement";
import NotifyUserSelect from '@/components/NotifyUserSelect.vue';

const message = inject('message')
const modal = inject('modal')

const props = defineProps({
    releaseData: {
        type: Object,
        default: () => ({
            uuid: '',
            engUuid: ''
        })
    }
});

const emit = defineEmits(['on-success']);

const tableDate = ref<any[]>([]);
const timer = ref<NodeJS.Timeout | null>(null);
const relId = ref('');
const envArr = ref<any[]>([]);
const SuffixData = ref('');
const ruleSet = ref('');
const obj = ref({});
const strEnv = ref('');
const disabled = ref(true);

interface RuleForm {
    engUuid: string;
    engName: string;
    engChineseName: string;
    createdTime: string;
    createdTimeStr: string;
    edition: string;
    descs: string;
    env: string;
    enableNotification: string;
    notifyConfigIds: string[];
    grayscaleReleaseType: string;
}

const ruleForm = reactive<RuleForm>({
    engUuid: '',
    engName: '',
    engChineseName: '',
    createdTime: '',
    createdTimeStr: '',
    edition: '',
    descs: '',
    env: '',
    enableNotification: '0',
    notifyConfigIds: [],
    grayscaleReleaseType: '1'
});

const baseEngUuid = ref('');
const ruleForm1 = reactive({
    env: '',
    ruleSetdec: '',
    isTimePublish: '0',
    taskTime: '',
    enableNotification: '0',
    notifyConfigIds: [],
    grayscaleReleaseType:''
});
const rules = {
    env: [{ required: true, message: '请选择发布环境', trigger: 'change' }],
    ruleSetdec: [
        {
            min: 1,
            max: 60,
            message: '长度在 1 到 60 个字符',
            trigger: 'blur',
        },
    ],
    enableNotification: [{ required: false, message: '请选择是否开启通知', trigger: 'change' }],
    notifyConfigIds: [
        {
            type: 'array',
            required: false,
            validator: (rule: any, value: string[]) => {
                if (ruleForm1.enableNotification === '1' && (!value || value.length === 0)) {
                    return Promise.reject(new Error('请至少选择一个通知用户'));
                }
                return Promise.resolve();
            }
        }
    ],
};
const paginations = reactive({
    loading: true,
    total: 0, // 总数
    limit: 10, // 一页显示都是条
    page: 1, // 当前页
    showSizeChanger:true,
    showTotal: (total) => `共 ${total} 条数据`,
});
const currentPage = ref(0); // 当前页码
const loginType = ref('');
const pubDate = ref('');
const pubTime = ref('');
const pubTimeShow = ref(false);
const tableLoading = ref(false);

const notifyConfigList = ref([]);
const notifyConfigShow = ref(false);

watch(() => ruleForm1.enableNotification, async (newVal: string) => {
    notifyConfigShow.value = newVal === '1';
    if (newVal === '1') {
        try {
            const res = await getUsersWithNotification();
            if (res.data) {
                notifyConfigList.value = res.data;
            }
        } catch (err) {
            console.error('获取通知配置失败:', err);
            message?.error('获取通知配置失败');
        }
    } else {
        ruleForm1.notifyConfigIds = [];
    }
});

// 表格列定义
const columns = ref([
    {
        title: '规则集名称',
        dataIndex: 'musterName',
        key: 'musterName',
        ellipsis: true
    },
    {
        title: '规则集版本',
        dataIndex: 'edition',
        key: 'edition',
        ellipsis: true
    },
    {
        title: '发布环境',
        dataIndex: 'environmentName',
        key: 'environmentName',
        ellipsis: true
    },
    {
        title: '状态',
        key: 'status',
        ellipsis: true
    },
    {
        title: '发布时间',
        dataIndex: 'rlsTimeStr',
        key: 'rlsTimeStr',
        width: 180,
        ellipsis: true
    },
    {
        title: '发布异常信息',
        dataIndex: 'exceptionInfo',
        key: 'exceptionInfo',
        ellipsis: true
    }
]);

const getPubList = async (noChange?: boolean) => {
    if (!noChange) {
        tableLoading.value = true;
    }
    const pars = {
        snapUuid: relId.value,
        page: paginations.page,
        several: paginations.limit,
    };
    try {
        const res = await getSnapList(pars);
        tableDate.value = res.data.data;
        paginations.total = res.data.totalCount;
    } catch (err) {
        console.log(err);
    } finally {
        tableLoading.value = false;
    }
};

const pagin = (cur) => {
    paginations.page = cur;
    getPubList();
};

const getReleaseInfo = async () => {
    const pars = {
        uuid: relId.value,
    };
    try {
        const res = await getBaseLineRel(pars);
        getSuffixInfo();
        Object.assign(ruleForm, res.data);
    } catch (err) {
        console.log(err);
    }
};

const getEnvInfo = async () => {
    const pars = {
        engUuid: baseEngUuid.value,
    };
    try {
        const res = await pubEnvUUid(pars);
        const choseSel = {
            environmentName: '请选择',
            id: '',
        };
        res.data.unshift(choseSel);
        envArr.value = res.data;
        ruleForm1.env = res.data[1].environmentName;
        strEnv.value = res.data[1].id;
        if (ruleForm1.env !== '' && res.data[0].environmentName === '请选择') {
            strEnv.value = res.data[1].id;
        }
    } catch (err) {
        console.log(err);
    }
};

const selEnv = (vid: string) => {
    obj.value = envArr.value.find((item) => item.id === vid)!;
    strEnv.value = obj.value.id;
};

const getSuffixInfo = async () => {
    try {
        const res = await getSuffix();
        SuffixData.value = res.data;
        ruleSet.value = ruleForm.engName + SuffixData.value;
    } catch (err) {
        console.log(err);
    }
};
const ruleFormRef = ref();
const submitForm = () => {
    ruleFormRef.value.validate().then((valid: boolean) => {
        if (valid) {
            let taskTimeString = null
            if (ruleForm1.isTimePublish === '1') {
                if (!pubDate.value || !pubTime.value) {
                    message.info('请选择定时发布时间！');
                    return;
                }
                taskTimeString = `${pubDate.value} ${pubTime.value}:00`;
            }
            const data = qs.stringify({
                descs: ruleForm1.ruleSetdec,
                environmentId: strEnv.value,
                ruleMusterName: ruleSet.value,
                snapShootUuid: relId.value,
                isTimePublish: ruleForm1.isTimePublish,
                taskTime: taskTimeString,
                grayscaleReleaseType:ruleForm1.grayscaleReleaseType,
            });

            if (loginType.value === '04') {
                getInCompleteRuleForEng({ engUuid: baseEngUuid.value }).then((res) => {
                    if (res && res.data > 0) {
                        modal.confirm({
                            title: '确认发布',
                            content: '规则库中存在未完成的规则，请确认是否发布？',
                            onOk() {
                                publish(data);
                            },
                            onCancel() {
                                message.info('已取消');
                            },
                        });
                    } else {
                        modal.confirm({
                            title: '确认发布',
                            content: '您确定要发布吗?',
                            onOk() {
                                publish(data);
                            },
                            onCancel() {
                                message.info('已取消');
                            },
                        });
                    }
                });
            } else {
                modal.confirm({
                    title: '确认发布',
                    content: '您确定要发布吗?',
                    onOk() {
                        publish(data);
                    },
                    onCancel() {
                        message.info('已取消');
                    },
                });
            }
        }
    });
};

// 更新通知配置的异步方法
const updateNotifyConfigs = async () => {
    try {
        // 遍历所有选中的通知配置ID
        for (const notifyId of ruleForm1.notifyConfigIds) {
            const notifyRes = await getNotifyConfigById({id:parseInt(notifyId, 10)});
            if (notifyRes.data == null) {
                await saveOrUpdateNotifyConfig({
                    userId:notifyId,
                    enablePublishNotification: '1'
                });
            }else {
                await saveOrUpdateNotifyConfig({
                    ...notifyRes.data,
                    enablePublishNotification: '1'
                });
            }
        }
    } catch (err) {
        console.error('更新通知配置失败:', err);
        throw err;
    }
};

const publish = async (data: string) => {
    try {
        // 如果开启了通知，先更新通知配置
        await updateNotifyConfigs();
        const res = await getRelSub(data);
        message.success("发布成功");
        emit('on-success'); // 通知父组件发布成功
    } catch (err) {
        message.error(err);
        console.log(err);
    }
};

const getLoginTypes = async () => {
    try {
        const res = await getLoginType();
        loginType.value = res.data;
    } catch (err) {
        console.log(err);
    }
};

const selPub = (data: string) => {
    pubTimeShow.value = data === '1';
};

const initialize = () => {
    // 清空之前的数据
    tableDate.value = [];
    ruleForm1.ruleSetdec = '';
    ruleForm1.isTimePublish = '0';
    ruleForm1.grayscaleReleaseType = '1';
    pubDate.value = '';
    pubTime.value = '';
    pubTimeShow.value = false;

    // 初始化数据
    getReleaseInfo();
    getEnvInfo();
    getLoginTypes();
    getPubList();

    // 设置定时更新
    if (timer.value) {
        clearInterval(timer.value);
    }
    timer.value = setInterval(() => {
        getPubList(true);
    }, 10000);
};

// 监听props变化，当抽屉显示时初始化数据
watch(
    () => props.releaseData,
    (newVal) => {
        if (newVal && newVal.uuid) {
            relId.value = newVal.uuid;
            baseEngUuid.value = newVal.engUuid;
            initialize();
        }
    },
    { immediate: true, deep: true }
);


onBeforeUnmount(() => {
    clearInterval(timer.value!);
    timer.value = null;
});

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);
</script>

<template>
    <div class="baseline-release-container">
        <a-form
            :rules="rules"
            :model="ruleForm1"
            ref="ruleFormRef"
            label-width="100px"
            :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }"
            class="release-form"
        >
            <a-collapse class="base-info-collapse">
                <a-collapse-panel key="1" header="基线信息详情">
                    <a-row :gutter="16">
                        <a-col :span="12">
                            <a-form-item label="规则库名称" prop="engUuid">
                                <a-typography-text>{{ ruleForm.engChineseName }}</a-typography-text>
                            </a-form-item>
                            <a-form-item label="基线创建时间">
                                <a-typography-text>{{ ruleForm.createdTimeStr }}</a-typography-text>
                            </a-form-item>
                            <a-form-item label="规则集名称">
                                <a-typography-text>{{ ruleSet }}</a-typography-text>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="规则库标识" prop="engName">
                                <a-typography-text>{{ ruleForm.engName }}</a-typography-text>
                            </a-form-item>
                            <a-form-item label="基线版本号" prop="edition">
                                <a-typography-text>{{ ruleForm.edition }}</a-typography-text>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-collapse-panel>
            </a-collapse>

            <a-row :gutter="16" style="margin-top: 16px;">
                <a-col :span="12">
                    <a-form-item label="发布环境" prop="env">
                        <a-select v-model:value="ruleForm1.env" placeholder="请选择" @change="selEnv">
                            <a-select-option v-for="item in envArr" :key="item.id" :value="item.id">
                                {{ item.environmentName }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="是否定时发布" prop="isTimePublish">
                        <a-select v-model:value="ruleForm1.isTimePublish" placeholder="请选择" @change="selPub">
                            <a-select-option value="1">是</a-select-option>
                            <a-select-option value="0">否</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="发布时间" prop="taskTime" v-if="pubTimeShow">
                        <div style="display: flex;">
                            <DateTimePicker
                                    v-model:pubDate="pubDate"
                                    v-model:pubTime="pubTime"
                            />
                        </div>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="规则集描述" prop="ruleSetdec">
                        <a-input placeholder="请输入规则集描述" v-model:value="ruleForm1.ruleSetdec" />
                    </a-form-item>
                    <a-form-item label="发布方式" prop="grayscaleReleaseType">
                        <a-select v-model:value="ruleForm1.grayscaleReleaseType" placeholder="请选择">
                            <a-select-option value="1">正式发布</a-select-option>
                            <a-select-option value="2">灰度发布</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="开启邮件通知" name="enableNotification">
                        <a-select v-model:value="ruleForm1.enableNotification" placeholder="请选择">
                            <a-select-option value="1">是</a-select-option>
                            <a-select-option value="0">否</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-show="notifyConfigShow">
                    <a-form-item label="选择通知用户" name="notifyConfigIds">
                        <NotifyUserSelect
                            v-model="ruleForm1.notifyConfigIds"
                            :notifyConfigList="notifyConfigList"
                            :autoSelect="false"
                        />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-form-item class="form-actions">
                <a-button type="primary" @click="submitForm">提交</a-button>
            </a-form-item>
        </a-form>

        <div class="table-container">
            <h3>发布历史</h3>
            <div ref="basePoint">
                <!-- 骨架屏 -->
                <TableSkeleton
                    v-if="tableLoading"
                    :columns="columns"
                    :limit="paginations.limit"
                    :scrollY="scrollY-30"
                />
                <!-- 数据表格 -->
                <a-table
                    v-else
                    :data-source="tableDate"
                    style="width: 100%"
                    class="baselin-table"
                    :pagination="false"
                    :scroll="{y: scrollY-30 }"
                >
                    <a-table-column title="规则集名称" data-index="musterName" :ellipsis="true" />
                    <a-table-column title="规则集版本" data-index="edition" :ellipsis="true" />
                    <a-table-column title="发布环境" data-index="environmentName" :ellipsis="true" />
                    <a-table-column title="状态" :ellipsis="true">
                        <template #default="{ record }">
                            <span>{{ record.status === '2' ? '发布成功' : record.status === '3' ? '发布失败' : '发布中' }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column title="发布时间" data-index="rlsTimeStr" width="180" :ellipsis="true" />
                    <a-table-column title="发布异常信息" data-index="exceptionInfo" :ellipsis="true" />
                </a-table>
                <Pagination
                        :paginations="paginations"
                        @change="pagin"
                        :scrollY="scrollY-30"
                />
            </div>

        </div>
    </div>
</template>

<style lang="scss" scoped>
.baseline-release-container {
    padding: 5px 20px;
    .release-form {
        margin-bottom: 24px;

        :deep(.ant-form-item-label > label) {
            font-weight: bold;
        }
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
    }

    .base-info-collapse {
        margin: 0;

        :deep(.ant-collapse-header) {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            font-weight: 500;
        }

        :deep(.ant-collapse-content-box) {
            padding: 16px;
        }
    }

    .table-container {
        margin-top: 24px;

        h3 {
            margin-bottom: 16px;
            font-weight: 500;
        }
    }

    :deep(.ant-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 8px 24px;

        .ant-checkbox-wrapper {
            margin-right: 0;
            min-width: 120px;
        }
    }
}
</style>
