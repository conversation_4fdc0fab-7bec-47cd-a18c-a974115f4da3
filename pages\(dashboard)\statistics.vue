<!--统计-->
<template>
    <div class="DashboardLayout-module_wrapper_bRvE0">
        <div class="ant-row" style="row-gap: 0px;">
            <div class="ant-col DashboardLayout-module_main_6rn+d DashboardLayout-module_superSidebarInvisible_vap-Q">
                <div class="Books-module_container_w2bB6">
                    <div class="overview-con" style="display: flex; justify-content: space-between;">
                        <a-card v-for="(item, index) in overviewList" :key="index" class="overview" style="width: 22%">
                            <div style="display: flex; align-items: center;">
                                <div class="overview-icon">
                                    <UserOutlined class="icon" v-if="item.label === '用户数量'" />
                                    <ProfileOutlined class="icon" v-if="item.label === '规则库数量'" />
                                    <BranchesOutlined class="icon" v-if="item.label === '规则数量'" />
                                    <BarsOutlined class="icon" v-if="item.label === '模型数量'" />
                                </div>
                                <div class="overview-list">
                                    <a-statistic :title="item.label" :value="item.value" />
                                </div>
                            </div>
                        </a-card>
                    </div>
                    <div class="flex" style="display: flex; justify-content: space-between; width: 100%;margin-top: 100px">
                        <a-card class="ruleType">
                            <template #title>
                                <span class="ruleTypeTitle">规则类型统计</span>
                            </template>
                            <div id="ruleType" style="width: 100%; height: 350px;"></div>
                        </a-card>
                        <a-card class="barChart">
                            <template #title>
                                <span class="ruleTypeTitle">当日规则操作次数统计</span>
                            </template>
                            <div id="barChart" style="width: 100%; height: 350px;"></div>
                        </a-card>
                        <a-card class="demand">
                            <template #title>
                                <span class="ruleTypeTitle">任务统计</span>
                            </template>
                            <div id="demand" style="width: 100%; height: 350px;"></div>
                        </a-card>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
    import { init,use, graphic } from 'echarts/core';
    import { BarChart, PieChart } from 'echarts/charts';
    import {
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        GridComponent
    } from 'echarts/components';
    import { CanvasRenderer } from 'echarts/renderers';

    import {
        getTotalStatistics,
        getRuleTypeStatistics,
        getRuleStatusDayStatistics,
        getDemandApplyStatistics,
    } from '@/api/page';
    definePageMeta({
        title: '统计'
    })
    use([
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        GridComponent,
        BarChart,
        PieChart,
        CanvasRenderer
    ]);

    const overviewList = ref([
        {  label: '用户数量', value: '' },
        {  label: '规则库数量', value: '' },
        {  label: '规则数量', value: '' },
        {  label: '模型数量', value: '' },
    ]);
    let timerStr: NodeJS.Timeout | null = null;
    let timerRuleType: NodeJS.Timeout | null = null;
    let timerBar: NodeJS.Timeout | null = null;
    let timerT: NodeJS.Timeout | null = null;

    const animateValue = (obj: any, start: number, end: number, duration: number, step: number) => {
        let range = end - start;
        if (range > 0) {
            let current = start;
            let stepTime = Math.abs(Math.floor(duration / range));

            timerStr = setInterval(() => {
                let increment = Math.ceil((end - current) / 10);
                if (increment < 1) increment = 1;

                current += increment;
                if (current >= end) {
                    current = end;
                    obj.value = current;
                    obj.textContent = current;
                    clearInterval(timerStr);
                } else {
                    obj.value = current;
                    obj.textContent = current;
                }
            }, stepTime);
        }
    };

    const calculateStep = (value: number): number => {
        if (value < 2) return 1;
        if (value < 1000) return 2;
        if (value < 9000) return 50;
        if (value < 20000) return 100;
        if (value < 50000) return 150;
        if (value < 100000) return 200;
        return 250;
    };

    const initializeCharts = async () => {
        try {
            showGlobalLoading('加载中，请耐心等待');
            const [totalStats, ruleTypeStats, ruleStatusStats, demandStats] = await Promise.all([
                getTotalStatistics(),
                getRuleTypeStatistics(),
                getRuleStatusDayStatistics(),
                getDemandApplyStatistics()
            ]);
            //用户数量,规则库数量,规则数量,模型数量
            const { userCount, engCount, ruleCount, modelCount } = totalStats.data;
            const aCount = [userCount, engCount, ruleCount, modelCount];
            aCount.forEach((item, index) => {
                let step = calculateStep(Number(item));
                overviewList.value[index].value = item;
                animateValue(overviewList.value[index], 0, Number(item), 2000, step);
            });
            // 规则类型统计
            const ruleTypeChart = init(document.getElementById('ruleType'));
            ruleTypeChart.setOption({
                tooltip: { trigger: 'item' },
                legend: {
                    itemWidth: 15,
                    itemHeight: 15,
                    orient: 'vertical',
                    bottom: '20%',
                    right: '13%',
                    textStyle: {
                        color: '#14c8d4',
                        fontSize: 12,
                    }
                },
                color: ['#0184d5', '#00d887', '#00ffea'],
                series: [{
                    name: '',
                    type: 'pie',
                    center: ['34%', '50%'],
                    radius: ['30%', '60%'],
                    avoidLabelOverlap: false,
                    label: { show: false, position: 'left' },
                    labelLine: { show: false },
                    data: [
                        { value: ruleTypeStats.data.generalRuleCount, name: '普通规则' },
                        { value: ruleTypeStats.data.decisionTableCount, name: '决策表' },
                        { value: ruleTypeStats.data.ruleFlowCount, name: '规则流' }
                    ]
                }]
            });
            //当日规则操作次数统计
            const barChart = init(document.getElementById('barChart'));
            barChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                grid: {
                    left: '10%',
                    right: '5%',
                    top: '10%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: {
                    data: ['新增', '修改', '删除', '审核'],
                    axisLine: { lineStyle: { color: '#14c8d4' } },
                    splitLine: { show: false }
                },
                yAxis: {
                    splitLine: { show: false },
                    axisLine: { lineStyle: { color: '#14c8d4' } }
                },
                series: [{
                    name: '',
                    type: 'bar',
                    barWidth: 10,
                    itemStyle: {
                        borderRadius: 2,
                        color: new graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#14c8d4' },
                            { offset: 1, color: '#43eec6' }
                        ])
                    },
                    data: [
                        ruleStatusStats.data.addRuleCount,
                        ruleStatusStats.data.editRuleCount,
                        ruleStatusStats.data.delRuleCount,
                        ruleStatusStats.data.checkRuleCount
                    ]
                }]
            });
            //任务统计
            const demandChart = init(document.getElementById('demand'));
            demandChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                grid: {
                    left: '10%',
                    right: '5%',
                    top: '10%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: {
                    data: ['任务总数', '当日新增', '当日完成'],
                    axisLine: { lineStyle: { color: '#14c8d4' } },
                    splitLine: { show: false }
                },
                yAxis: {
                    splitLine: { show: false },
                    axisLine: { lineStyle: { color: '#14c8d4' } }
                },
                series: [{
                    name: '',
                    type: 'bar',
                    barWidth: 10,
                    itemStyle: {
                        borderRadius: 2,
                        color: new graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#14c8d4' },
                            { offset: 1, color: '#43eec6' }
                        ])
                    },
                    data: [
                        demandStats.data.demandTotal,
                        demandStats.data.demandAddCount,
                        demandStats.data.demandFinishCount
                    ]
                }]
            });
        } catch (error) {
            console.error('初始化图表失败:', error);
            message.error('加载统计数据失败');
        }finally {
            hideGlobalLoading();
        }
    };

    onMounted(() => {
        initializeCharts();
    });

    onBeforeUnmount(() => {
        if (timerStr) clearInterval(timerStr);
        if (timerRuleType) clearInterval(timerRuleType);
        if (timerBar) clearInterval(timerBar);
        if (timerT) clearInterval(timerT);
    });
</script>
<style lang="scss" scoped>
    .overview-icon{
        margin-right: 15px;
    }
    .icon{
        font-size: 300%;
    }
    .flex{
        text-align: center;
        .ant-card {
            width: 30%;
        }
    }
    .ant-card {
        background-color: #FAFAFA;
    }
    .ruleTypeTitle{
        font-size: 18px;
    }
</style>
