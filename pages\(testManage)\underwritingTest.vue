<!-- 核保测试 -->
<script setup lang="ts">
    definePageMeta({
        title: '核保测试'
    })
    // const props = defineProps<{
    //     url: {
    //         type: String,
    //         required: true
    //     }
    // }>();
    // const url = 'http://*************:9001/auto-vehicle-underwriting-service/test'
    const  url = 'http://************:8088/test'
    const src = ref('');
    const unId = getUnid();
    onMounted(() => {
        if (src.value.indexOf('?') !==-1) {
            src.value = `${url}&id=${unId}`;
        } else {
            src.value = `${url}?id=${unId}`;
        }
    });

    const getIframeInfo = computed(() => {
        return window.innerHeight - 50 + 'px';
    });
</script>
<template>
    <a-card>
        <iframe
                ref="ifHeight"
                :src="src"
                frameborder="0"
                width="100%"
                :key="url"
                :style="{ height: getIframeInfo }"
        ></iframe>
    </a-card>
</template>


