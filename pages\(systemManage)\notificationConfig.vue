<template>
    <ListPage
        ref="listLayout"
        title="通知配置"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="columns"
        :queryMethod="fetchNotifyConfigs"
        rowKey="id"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
    >
    </ListPage>

    <!-- 新增/更新对话框 -->
    <a-modal
        v-if="isModalVisible"
        :width='600'
        :visible="isModalVisible"
        :title="modalType === 'add' ? '新增通知配置' : '更新通知配置'"
        @ok="handleModalOk"
        @cancel="handleCancel"
        okText="保存"
        :okButtonProps="{ loading: isSaving }"
        class="notify-config-modal"
    >
        <div style="max-height: 60vh; overflow-y: auto;">
            <a-form
                ref="formRef"
                :model="formData"
                :rules="rules"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 16 }"
            >
                <a-form-item label="名称" name="name">
                    <a-input v-model:value="formData.name" placeholder="请输入名称" />
                </a-form-item>
                <a-form-item label="邮箱" name="email">
                    <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
                </a-form-item>
            </a-form>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {
    getNotifyConfigList,
    searchNotifyConfig,
    saveOrUpdateNotifyConfig,
    deleteNotifyConfig
} from '@/api/notifyConfig';

const message = inject('message')
/**
 * 通知配置接口参数
 */
interface NotifyConfig {
    id?: number;
    name: string;
    email: string;
}

/**
 * 通知配置查询参数
 */
interface NotifyConfigQuery {
    name?: string;
    email?: string;
    page?: number;
    several?: number;
}

definePageMeta({
    title: '通知配置'
})

// 表格列配置
const columns = [
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'left',
        width: 200,
        ellipsis: true
    },
    {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        align: 'left',
        width: 250,
        ellipsis: true
    }
];

// 搜索配置
const searchConfig = reactive({
    // 简单搜索字段
    simpleSearchField: {
        label: '名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '名称',
            field: 'name',
            compType: 'input'
        },
        {
            label: '邮箱',
            field: 'email',
            compType: 'input'
        }
    ]
});

// 获取通知配置列表数据
const fetchNotifyConfigs = async (params: Record<string, any> = {}) => {
    try {
        const res = await getNotifyConfigList({
            name: params.name,
            email: params.email,
            page: params.page || 1,
            several: params.pageSize || 10
        });

        if (res.code === 20000) {
            return {
                data: res.data.data || [],
                totalCount: res.data.totalCount
            };
        } else {
            message.error(res.message || '获取通知配置失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    } catch (error) {
        message.error('获取通知配置失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加事件
    addNewEvent: () => showModal('add'),
    // 表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchNotifyConfigs,
        // 添加分页配置
        pagination: {
            pageSize: 10,
            current: 1,
            total: 0
        }
    }
};

// Modal相关状态和方法
const isModalVisible = ref(false);
const modalType = ref<'add' | 'update'>('add');
const isSaving = ref(false);
const formRef = ref();
const formData = ref<NotifyConfig>({
    name: '',
    email: ''
});

const rules = {
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ]
};

const showModal = (type: 'add' | 'update', record: NotifyConfig = {
    name: '',
    email: ''
}) => {
    modalType.value = type;
    if (type === 'update') {
        formData.value = { ...record };
    } else {
        formData.value = {
            name: '',
            email: ''
        };
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    isModalVisible.value = false;
    formData.value = {
        name: '',
        email: ''
    };
};

const handleModalOk = async () => {
    try {
        await formRef.value.validate();
        isSaving.value = true;

        const res = await saveOrUpdateNotifyConfig(formData.value);
        if (res.code === 20000) {
            message.success('保存成功');
            handleCancel();
            listLayout.value?.refresh();
        } else {
            message.error(res.message || '保存失败');
        }
    } catch (error) {
        message.error('表单验证失败');
    } finally {
        isSaving.value = false;
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: NotifyConfig) => {
    return [
        {
            key: 'update',
            label: '编辑',
            onClick: () => showModal('update', record)
        },
        {
            key: 'delete',
            label: '删除',
            onClick: () => handleDelete(record)
        }
    ];
};

// 删除操作
const handleDelete = async (record: NotifyConfig) => {
    if (!record.id) {
        message.error('记录ID不存在');
        return;
    }

    try {
        const res = await deleteNotifyConfig(record.id);
        if (res.code === 20000) {
            message.success('删除成功');
            listLayout.value?.refresh();
        } else {
            message.error(res.message || '删除失败');
        }
    } catch (error) {
        message.error('删除失败');
    }
};

const listLayout = ref();

// 数据加载完成处理函数
const handleDataLoaded = (response: any) => {
    console.log('数据加载完成：', response);
    if (response && response.data) {
        // 可以在这里处理数据加载完成后的逻辑
    }
};
</script>

<style lang="scss" scoped>
.notify-config-modal {
    :deep(.ant-modal-body) {
        padding: 20px 24px;
        overflow: visible;
    }

    :deep(.ant-modal-footer) {
        border-top: 1px solid #f0f0f0;
        padding: 10px 24px;
        margin-top: 0;
    }
}
</style>
