<template>
    <FlexDrawer
        :visible="visible"
        :title="title"
        :width="1100"
        @close="handleClose"
        :showCloseBtn="false"
        class="rule-detail-drawer"
    >
        <div v-if="currentRuleDetail" class="rule-detail-drawer-content">
            <!-- 比较视图区域 -->
            <div class="comparison-container">
                <div class="comparison-panel latest-rule">
                    <div class="panel-header">
                        <span class="panel-title">最新规则</span>
                    </div>
                    <div class="rule-info">
                        <div class="info-row">
                            <div class="info-label">规则名称:</div>
                            <div class="info-value" :class="ruleNameColor ? 'highlight-diff' : ''">{{ detailData?.ruleHisVO1?.ruleName }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则类型:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO1?.type }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则版本:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO1?.edition }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则状态:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO1?.status }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">有效状态:</div>
                            <div class="info-value" :class="newValidStatus ? 'highlight-diff' : ''">{{ detailData?.ruleHisVO1?.validStatus }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则优先级:</div>
                            <div class="info-value" :class="salienceColor ? 'highlight-diff' : ''">{{ detailData?.ruleHisVO1?.salience }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则包:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO1?.packageNameAll }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则描述:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO1?.descs }}</div>
                        </div>
                    </div>
                </div>

                <div class="comparison-panel baseline-rule">
                    <div class="panel-header">
                        <span class="panel-title">基线规则</span>
                    </div>
                    <div class="rule-info">
                        <div class="info-row">
                            <div class="info-label">规则名称:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.ruleName }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则类型:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.type }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则版本:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.edition }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则状态:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.status }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">有效状态:</div>
                            <div class="info-value" :class="baselineValidStatus ? 'highlight-diff' : ''">{{ detailData?.ruleHisVO2?.validStatus }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则优先级:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.salience }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则包:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.packageNameAll }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">规则描述:</div>
                            <div class="info-value">{{ detailData?.ruleHisVO2?.descs }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则内容区域 -->
            <div v-if="detailData?.ruleHisVO1?.type !== '规则流' && detailData?.ruleHisVO1?.type !== '决策树'" class="rule-content-container">
                <div class="content-panel">
                    <div class="panel-header">
                        <span class="panel-title">最新内容</span>
                    </div>
                    <div class="panel-body">
                        <RuleCompareContent :ruleCompareData="ruleCompareData" />
                    </div>
                </div>

                <div class="content-panel">
                    <div class="panel-header">
                        <span class="panel-title">基线内容</span>
                    </div>
                    <div class="panel-body">
                        <RuleDetailContent :ruleInfoData="baselineRuleInfoData" :ruleInfoFlag="true"/>
                    </div>
                </div>
            </div>
            <div v-else class="rule-content-container">
                <div class="content-panel">
                    <div class="panel-header">
                        <span class="panel-title">最新内容</span>
                    </div>
                    <div class="panel-body">
                        <div v-if="detailData?.ruleHisVO1?.type == '决策树'">
                            <DecisionTreeDetail :ruleInfo="detailData?.ruleHisVO1" :baseLineFlag="true"/>
                        </div>
                        <div v-if="detailData?.ruleHisVO1?.type == '规则流'">
                            <RuleFlowDetail :ruleInfo="detailData?.ruleHisVO1" :baseLineFlag="true"/>
                        </div>
                    </div>
                </div>

                <div class="content-panel">
                    <div class="panel-header">
                        <span class="panel-title">基线内容</span>
                    </div>
                    <div class="panel-body">
                        <div v-if="detailData?.ruleHisVO2?.type == '决策树'">
                            <DecisionTreeDetail :ruleInfo="detailData?.ruleHisVO1" :baseLineFlag="true"/>
                        </div>
                        <div v-if="detailData?.ruleHisVO2?.type == '规则流'">
                            <RuleFlowDetail :ruleInfo="detailData?.ruleHisVO1" :baseLineFlag="true"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </FlexDrawer>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch, computed } from 'vue';
import RuleCompareContent from '@/components/ruleDetailCom/RuleCompareContent.vue';
import RuleDetailContent from '@/components/ruleDetailCom/RuleDetailContent.vue';
import RuleFlowDetail from "@/businessComponents/ruleAudit/RuleFlowDetail.vue";
import DecisionTreeDetail from "@/businessComponents/ruleAudit/DecisionTreeDetail.vue";

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '规则详情'
    },
    currentRuleDetail: {
        type: Object,
        default: null
    },
    detailData: {
        type: Object,
        default: () => ({
            ruleHisVO1: {},
            ruleHisVO2: {}
        })
    },
    ruleCompareData: {
        type: Object,
        default: () => ({})
    },
    baselineRuleInfoData: {
        type: Object,
        default: () => ({})
    },
    ruleNameColor: {
        type: Boolean,
        default: false
    },
    newValidStatus: {
        type: Boolean,
        default: false
    },
    baselineValidStatus: {
        type: Boolean,
        default: false
    },
    salienceColor: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close']);

// 处理关闭抽屉
const handleClose = () => {
    emit('close');
};

// 使用计算属性监控数据变化
watch(() => props.visible, (newVal) => {
    if (newVal && !props.currentRuleDetail) {
        console.warn('RuleDetailDrawer: currentRuleDetail is empty');
    }
}, { immediate: true });
</script>

<style lang="scss" scoped>
:deep(.rule-detail-drawer) {
    .ant-drawer-body {
        padding: 0 !important;
        background-color: #f5f7fa !important;
    }

    .ant-drawer-header {
        border-bottom: 1px solid #f0f0f0;

        .ant-drawer-title {
            font-weight: 600;
            font-size: 16px;
            color: #262626;
        }
    }
}

// 规则详情抽屉样式
.rule-detail-drawer-content {
    padding: 20px !important;
    background-color: #f5f7fa !important;
    height: 100% !important;
    overflow: auto !important;
    box-sizing: border-box !important;

    .comparison-container {
        display: flex !important;
        margin-bottom: 30px !important;
        gap: 20px !important;

        @media (max-width: 1200px) {
            flex-direction: column !important;
        }
    }

    .comparison-panel {
        flex: 1 !important;
        background: #fff !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
        }

        &.latest-rule {
            border-left: 4px solid #1890ff !important;
        }

        &.baseline-rule {
            border-left: 4px solid #52c41a !important;
        }
    }

    .panel-header {
        background: #fafafa !important;
        padding: 14px 20px !important;
        border-bottom: 1px solid #f0f0f0 !important;

        .panel-title {
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #262626 !important;
            position: relative !important;
            padding-left: 12px !important;

            &::before {
                content: '' !important;
                position: absolute !important;
                left: 0 !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                width: 4px !important;
                height: 16px !important;
                background-color: #1890ff !important;
                border-radius: 2px !important;
            }
        }
    }

    .baseline-rule .panel-title::before {
        background-color: #52c41a !important;
    }

    .rule-info {
        padding: 16px 20px !important;
    }

    .info-row {
        display: flex !important;
        padding: 8px 0 !important;
        border-bottom: 1px dashed #f0f0f0 !important;
        align-items: flex-start !important;

        &:last-child {
            border-bottom: none !important;
        }

        .info-label {
            width: 100px !important;
            flex-shrink: 0 !important;
            color: #595959 !important;
            font-weight: 500 !important;
            padding-right: 10px !important;
        }

        .info-value {
            flex: 1 !important;
            color: #262626 !important;
            word-break: break-word !important;
            line-height: 1.5 !important;

            &.highlight-diff {
                position: relative !important;
                color: #f5222d !important;
                font-weight: 500 !important;
                background-color: rgba(245, 34, 45, 0.05) !important;
                padding: 2px 6px !important;
                border-radius: 3px !important;
                display: inline-block !important;
            }
        }
    }

    .rule-content-container {
        display: flex !important;
        flex-direction: column !important;
        gap: 30px !important;
        margin-bottom: 30px !important;
    }

    .content-panel {
        background: #fff !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;

        &:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
        }
    }

    .panel-body {
        padding: 20px !important;
    }
}
</style>
