<template>
  <div id="rule_edit">
    <!-- 根据 type 属性显示不同的组件 -->
    <RuleSet v-if="type === '1'" :ruleInfo="ruleInfo" :isTemplate="isTemplate" @setCascaderClose="setCascaderClose" ref="ruleSetRef" />
    <DecisionTable v-if="type === '2'" :ruleInfo="ruleInfo" :isTemplate="isTemplate" @setCascaderClose="setCascaderClose" ref="decisionTableRef" />
    <DecisionTree v-if="type === '4'" :ruleInfo="ruleInfo" :isTemplate="isTemplate" ref="decisionTreeRef" />
    <RuleFlow v-if="type === '5'" :ruleInfo="ruleInfo" :isTemplate="isTemplate" ref="ruleFlowRef" />
    <!-- 级联选择器组件 -->
    <CustomCascader
      :options="selfData"
      :acTop="acTop"
      :acLeft="acLeft"
      :open="casOpen"
      :ruleUuid="uuid"
      @onSelectChange="onSelectChange"
    />
  </div>
</template>

<script setup>
import store from "@/store";
import globalEventEmitter from "@/utils/eventBus";
import CustomCascader from '@/components/ruleEditCom/ruleItemList/cascader/CustomCascader.vue'
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";
// 定义组件的 props
const props = defineProps({
  uuid: String,
  engUuid: String,
  demandUuid: String,
  packageName: String,
  type: String, // type 1：普通规则 2：决策表 3：评分卡 4：决策树 5：规则流
  ruleName: String,
  jsonContentObject: Object,
  isTemplate: Boolean
});

// 定义响应式数据 ruleInfo
const ruleInfo = reactive({
  type: props.type,
  uuid: props.uuid,
  engUuid: props.engUuid,
  demandUuid: props.demandUuid,
  packageNameAll: props.packageName,
  ruleName: props.ruleName,
  jsonContentObject: props.jsonContentObject
});

// 定义其他响应式数据
let propFn = reactive("");
const currentPos = ref("");
const flag = ref("");
const acTop = ref("0px");
const acLeft = ref("0px");
let selfData = ref([]);
const casOpen = ref(false);

// 获取子组件的引用
const ruleSetRef = ref(null);
const decisionTableRef = ref(null);
const decisionTreeRef = ref(null);
const ruleFlowRef = ref(null);

// 暴露获取规则数据的方法
const getRulesData = () => {
  // 根据类型调用相应组件的方法
  if (props.type === '1' && ruleSetRef.value) {
    return ruleSetRef.value.getRulesData();
  } else if (props.type === '2' && decisionTableRef.value) {
    return decisionTableRef.value.getRulesData();
  } else if (props.type === '4' && decisionTreeRef.value) {
    return decisionTreeRef.value.getRulesData();
  } else if (props.type === '5' && ruleFlowRef.value) {
    return ruleFlowRef.value.getRulesData();
  }
  return null;
};

// 将方法暴露给父组件
defineExpose({
  getRulesData
});

// 关闭级联选择器的方法
const setCascaderClose = () => {
  casOpen.value = false;
};


// 显示级联选择器的方法
const showACascader = (params) => {
  const { e, pos, filterOData, fn, flag: _flag } = params;
  // 阻止事件冒泡已经在源头comTypeHandler处理，这里不需要再次阻止
  
  // 如果已经有级联选择器打开，先关闭它
  if (casOpen.value) {
    casOpen.value = false;
    
  }
  // 短暂延迟后再打开新的级联选择器，确保DOM已更新
  setTimeout(() => {
    openNewCascader(params);
  }, 50);
};

// 打开新的级联选择器
const openNewCascader = (params) => {
  const { e, pos, filterOData, fn, flag: _flag } = params;
  currentPos.value = pos;
  
  // 处理 filterOData 数据，设置 isLeaf 属性
  if (filterOData && filterOData.length) {
    loopIsLeaf(filterOData);
    function loopIsLeaf(data) {
      data.map((item) => {
        if (item.children) {
          item.isLeaf && (item.isLeaf = false);
          loopIsLeaf(item.children);
        } else {
          !item.isLeaf && (item.isLeaf = true);
        }
      });
    }
  }
  
  // 设置级联选择器数据和状态
  if (filterOData && filterOData.length) {
    selfData.value = filterOData;
  }
  
  // 设置级联选择器位置
  acLeft.value = `${e.pageX - e.offsetX}px`;
  acTop.value = `${e.pageY + 21 - e.offsetY}px`;
  
  // 保存回调函数和标记
  propFn = fn;
  flag.value = _flag;
  
  // 显示级联选择器
  casOpen.value = true;
};

// 级联选择器选项改变时的处理方法
const onSelectChange = (value, selectedOptions) => {
  // 如果是空选择，表示从blur事件发出的关闭信号
  if (!value.length && !selectedOptions.length) {
    casOpen.value = false;
    return;
  }
  
  // 分离变量和操作符数据
  const variableOptions = selectedOptions.filter(opt => !opt.isOperator);
  const operatorOptions = selectedOptions.filter(opt => opt.isOperator);
  
  let variableValue = [];
  variableOptions.forEach(item => {
    variableValue.push(item.value);
  });
  setCascaderClose();
  // 传递变量数据给回调函数
  nextTick(() => {
    propFn(currentPos.value, variableValue, variableOptions, operatorOptions);
  });
};

// 在组件挂载时执行的逻辑
onMounted(() => {
  // 设置当前规则ID到 Vuex store
  store.commit("setCurrentRuleId", ruleInfo.engUuid);
  // 监听全局事件
  globalEventEmitter.on(SHOW_ACASCADER, showACascader);
});

// 在组件卸载时执行的逻辑
onUnmounted(() => {
  // 移除全局事件监听器
  globalEventEmitter.off(SHOW_ACASCADER, showACascader);
});

</script>
<style lang="scss">
@use "@/assets/css/ruleEditor.scss" as *;
@use "@/assets/css/iconfontbtn.css" as *;
.tree-rule {
  .eRuleEditorContainer .rule-design {
    height: auto !important;
  }
  .action-list-div {
    margin-top: 0 !important;
  }
}
</style>
