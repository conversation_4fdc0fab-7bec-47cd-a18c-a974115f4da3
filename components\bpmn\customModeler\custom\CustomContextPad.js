export default class CustomContextPad {
  constructor(config, contextPad, create, elementFactory, injector, translate, modeling, bpmnFactory) {
    this.create = create;
    this.elementFactory = elementFactory;
    this.translate = translate;
    this.modeling = modeling;
    this.bpmnFactory = bpmnFactory;

    if (config.autoPlace !== false) {
      this.autoPlace = injector.get('autoPlace', false);
    }

    contextPad.registerProvider(this); // // 定义这是一个contextPad
  }

  getContextPadEntries(element) {
    const {
      autoPlace,
      create,
      elementFactory,
      translate,
      modeling,
      bpmnFactory
    } = this;

    function appendElement(type, name, gatewayDirection) {
      return function (event, element) {
        let businessObject = null
        if (gatewayDirection) {
          businessObject = bpmnFactory.create(type, {
            name,
            gatewayDirection
          });
        } else {
          businessObject = bpmnFactory.create(type, {
            name
          });
        }
        const shape = elementFactory.createShape({
          type,
          businessObject
        });
        if (autoPlace) {
          autoPlace.append(element, shape);
        } else {
          create.start(event, shape, element);
        }
      }
    }

    return {
      'append.end-event': {
        group: 'model',
        className: 'entry iconfont icon-end',
        title: translate('追加结束节点'),
        action: {
          click: appendElement('bpmn:EndEvent', '结束节点'),
        }
      },
      'append.inclusive-gateway': {
        group: 'model',
        className: 'entry iconfont icon-policy',
        title: translate('追加决策网关'),
        action: {
          click: appendElement('bpmn:ExclusiveGateway', '决策分支', 'Diverging')
        }
      },
      'append.business-rule-task': {
        group: 'model',
        className: 'entry iconfont icon-task',
        title: translate('追加规则节点'),
        action: {
          click: appendElement('bpmn:BusinessRuleTask', '规则节点')
        }
      },
      'append.exclusive-gateway': {
        group: 'model',
        className: 'entry iconfont icon-convergence',
        title: translate('追加汇聚网关'),
        action: {
          click: appendElement('bpmn:ExclusiveGateway', '汇聚分支', 'Converging')
        }
      },
      'append.call-activity': {
        group: 'model',
        className: 'entry iconfontflow icon-subflow',
        title: translate('追加子规则流'),
        action: {
          click: appendElement('bpmn:CallActivity', '子规则流', 'Activity')
        }
      },
      'append.delete': {
        group: 'model',
        className: 'entry bpmn-icon-trash dir',
        title: translate('删除'),
        action: {
          click: () => {
            modeling.removeElements([element])
          }
        }
      },
    }
  }
}

CustomContextPad.$inject = [
  'config',
  'contextPad',
  'create',
  'elementFactory',
  'injector',
  'translate',
  'modeling',
  'bpmnFactory'
];
