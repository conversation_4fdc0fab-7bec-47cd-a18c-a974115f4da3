<!-- 表格骨架屏组件 -->
<template>
  <div class="table-skeleton">
    <a-table
      :columns="columns"
      :data-source="Array(Math.max(1, limit || 3)).fill({})"
      :pagination="false"
      row-key="id"
      :loading="false"
      size="small"
      :scroll="{ y: scrollY }"
    >
      <template #bodyCell="{ column, index }">
        <div class="skeleton-cell">
          <a-skeleton-input 
            active 
            :style="getSkeletonStyle(column)" 
            :delay="index * 100" 
          />
        </div>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ColumnType } from 'ant-design-vue/es/table';

const props = defineProps<{
  columns: ColumnType<any>[];
  limit: number;
  scrollY: number;
}>();

const getSkeletonStyle = (column: ColumnType<any>) => {
  const styles = {
    width: '100%',
    height: '20px'
  };
  
  // 确保column对象存在
  if (!column) {
    return styles;
  }
  
  // 根据列的宽度设置骨架屏的宽度
  if (column.width) {
    styles.width = typeof column.width === 'number' ? `${column.width - 16}px` : column.width;
  } else {
    // 根据列的类型设置默认宽度
    switch (column?.key) {
      case 'index':
        styles.width = '50px';
        break;
      case 'action':
        styles.width = '120px';
        break;
      case 'name':
      case 'title':
        styles.width = '200px';
        break;
      case 'description':
        styles.width = '300px';
        break;
      default:
        styles.width = '100px';
    }
  }
  
  return styles;
};
</script>

<style lang="scss" scoped>
.table-skeleton {
  .skeleton-cell {
    padding: 8px;
    
    :deep(.ant-skeleton-input) {
      border-radius: 2px;
      background: #f2f2f2;
      height: 16px;
    }
  }
}
</style>
