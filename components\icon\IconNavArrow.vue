<template>
  <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="16" height="16">
    <path v-if="direction === 'up'" d="M512 362L212 662h116.3L512 478.3 695.7 662H812L512 362z" fill="#2c2c2c"></path>
    <path v-else d="M512 712l300-300H695.7L512 595.7 328.3 412H212l300 300z" fill="#2c2c2c"></path>
  </svg>
</template>

<script>
export default {
  name: 'IconNavArrow',
  props: {
    direction: {
      type: String,
      default: 'up',
      validator: value => ['up', 'down'].includes(value)
    }
  }
}
</script> 