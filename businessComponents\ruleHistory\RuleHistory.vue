<template>
    <div class="rule_details">
        <a-form>
            <a-row style="height:40px">
                <a-col :span="8">
                    <a-form-item label="规则库名称：" >
                        <span>{{ spanInfo.engUuid }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="规则集版本：">
                        <span>{{ spanInfo.edition }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="基线版本：">
                        <span>{{ spanInfo.snapShootEdition }}</span>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row style="height:40px">
                <a-col :span="8">
                    <a-form-item label="规则名称">
                        <a-col :span="15">
                            <a-input autocomplete="off" v-model:value="form.rule_name" placeholder="规则名称" />
                        </a-col>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="规则类型">
                        <a-col :span="15">
                            <a-select v-model:value="form.rule_type" placeholder="请选择">
                            <a-select-option v-for="item in ruleTypeOptions" :key="item.uuid" :value="item.code">
                                {{ item.name }}
                            </a-select-option>
                            </a-select>
                        </a-col>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-form-item>
                <a-button @click="queryList" type="primary" >搜索</a-button>
                <a-button @click="reset" style="margin-left: 8px">重置</a-button>
                <a-button @click="exportRule" style="margin-left: 8px" v-hasPermi="[RULE_PERMISSION.RULE_MUSTER.EXPORT_HTML]">导出规则</a-button>
                <a-button @click="statisticsRuleHandle"  style="margin-left: 8px">统计规则</a-button>
            </a-form-item>
            <div ref="basePoint">
                <!-- 骨架屏 -->
                <TableSkeleton 
                    v-if="loading" 
                    :columns="tableColumns" 
                    :limit="paginations.limit" 
                    :scrollY="scrollY-30" 
                />
                <!-- 数据表格 -->
                <a-table 
                    v-else
                    size="small" 
                    :scroll="{ x: 'max-content',y:scrollY-30 }" 
                    ref="multipleTable" 
                    :data-source="tableDate" 
                    :loading="loading" 
                    :pagination="false" 
                    :filterMultiple="true" 
                    :rowSelection="rowSelection" 
                    :rowKey="record=>record.uuid" 
                    :key="uuid"
                >
                    <a-table-column align="center"  ellipsis="true" title="序号" key="index" :width="50" fixed="left">
                        <template #default="{ index }">
                            <span>{{ (paginations.page - 1) * paginations.limit + index + 1 }}</span>
                        </template>
                    </a-table-column>
                    <a-table-column align="left" ellipsis="true" title="规则名称" dataIndex="ruleName" key="ruleName" :width="150" fixed="left">
                        <template #default="{ record,text }">
                            <span class="rule-name-link" @click="relDetails(record)">
                                <a-tooltip :title="text">
                                    <span>{{ text }}</span>
                                </a-tooltip>
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column align="left" ellipsis="true" title="规则路径" dataIndex="packageNameAll" key="packageNameAll" :width="300">
                        <template #default="{ record }">
                            <RulePath
                                    :path="record.packageNameAll"
                                    showCopyButton
                            />
                        </template>
                    </a-table-column>
                    <a-table-column align="center" ellipsis="true" title="版本" dataIndex="edition" key="edition" :width="80">
                        <template #default="{ text }">
                            <a-tooltip>
                                <template #title>
                                    {{ text }}
                                </template>
                                <span>{{ text }}</span>
                            </a-tooltip>
                        </template>
                    </a-table-column>
                    <a-table-column align="center"  ellipsis="true" title = "类型" dataIndex="type" key="type" :width="80">
                        <template #default="{ record }">
                            <span v-if="record.type === '决策表'">决策表</span>
                            <span v-else-if="record.type === '普通规则'">普通规则</span>
                            <span v-else-if="record.type === '规则流' && record.salience === '1'">规则流</span>
                            <span v-else-if="record.type === '规则流' && record.salience === '0'">子规则流</span>
                            <span v-else-if="record.type === '决策树'">决策树</span>
                        </template>
                    </a-table-column>
                    <a-table-column align="center"  ellipsis="true" title = "规则数量" dataIndex="count" key="count" :width="80"/>
                    <a-table-column align="center" ellipsis="true" title="描述" dataIndex="descs" key="descs" :width="200">
                        <template #default="{ text }">
                            <a-tooltip>
                                <template #title>
                                    {{ text }}
                                </template>
                                <span>{{ text }}</span>
                            </a-tooltip>
                        </template>
                    </a-table-column>
                </a-table>
                <Pagination
                        :paginations="paginations"
                        @change="pagin"
                        :scrollY="scrollY-100"
                        mode="ruleManage"
                />
            </div>
        </a-form>

        <a-modal v-if="statisticsRuleDialog" :visible="statisticsRuleDialog" width="30%" @cancel="handleCancel" :footer="null">
            <div slot="title" class="dialog_title">
                <span>统计规则结果</span>
            </div>
            <a-form class="dialogForm">
                <a-form-item label="普通规则数量 :">
                    <span>{{ statisticsRuleData.ruleCount }}</span>
                </a-form-item>
                <a-form-item label="决策表数量 :">
                    <span>{{ statisticsRuleData.decisionTableCount }}</span>
                </a-form-item>
                <a-form-item label="决策表总行数 :">
                    <span>{{ statisticsRuleData.decisionTableRowCount }}</span>
                </a-form-item>
                <a-form-item label="普通规则和决策表行数合计 :">
                    <span>{{ statisticsRuleData.total }}</span>
                </a-form-item>
            </a-form>
         </a-modal>
    </div>
</template>

<script setup lang="ts">
import { getRule_histDetails,getRule_Head,getExportRule,statisticsRule } from '~/api/rule_release_history';
import { getDicValueByTypeCode } from "~/api/rule_base";
import { defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import store from  '@/store';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import TableSkeleton from '@/components/TableSkeleton.vue';
import Pagination from '@/components/Pagination.vue';
import RulePath from '@/components/ruleDetailCom/RulePath.vue';
import useResize from '@/composables/useResize';

interface RuleRecord {
    uuid: string;
    ruleName: string;
    packageNameAll: string;
    edition: string;
    type: string;
    salience: string;
    count: number;
    descs: string;
    [key: string]: any;
}

interface DictionaryItem {
    uuid: string;
    code: string;
    name: string;
    [key: string]: any;
}

const props = defineProps({
    uuid: String
})

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);
const router = useRouter();
const currentPage = ref(1); // 当前页码
const statisticsRuleDialog = ref(false);       // 规则统计弹框
const loading = ref(false);
const tabUuid = ref("");
const ruleTypeOptions = ref<DictionaryItem[]>([]);
const form = ref({
    rule_name: "",
    rule_type: ""
})
const spanInfo = reactive({
    engUuid: "",
    edition: "",
    snapShootEdition: ""
})
const statisticsRuleData = reactive({
    ruleCount: 0,
    decisionTableCount: 0,
    decisionTableRowCount: 0,
    total: 0
})
const paginations = ref({
    loading: true,
    total: 1, // 总数
    limit: 10, // 一页显示都是条
    page: 1, // 当前页
    showSizeChanger:true,
    showTotal: (total: number) => `共 ${total} 条数据`,
});
const tableDate = ref<RuleRecord[]>([])
const modal = inject<any>('modal')
const message = inject<any>('message')

const handleCancel = () => {
    statisticsRuleDialog.value = false;
}
//获取当前页顶部数据
const getRuleHeadList = () => {
    let params = {
        uuid: tabUuid.value
    }
    getRule_Head(params).then(res => {
        spanInfo.engUuid = res.data.engUuid
        spanInfo.edition = res.data.edition
        spanInfo.snapShootEdition = res.data.snapShootEdition
    })
}
//获取列表数据
const getRuleList = () => {
    loading.value = true;
    let params = {
        uuid: tabUuid.value,
        ruleName: form.value.rule_name,
        ruleType: form.value.rule_type,
        page: paginations.value.page,
        several: paginations.value.limit
    }
    getRule_histDetails(params).then((res) => {
        tableDate.value =  res.data.data;
        paginations.value.total = res.data.totalCount;
        loading.value = false;
    }).catch(() => {
        loading.value = false;
    });
}

//统计规则
const statisticsRuleHandle = () => {
    const loadingMessage = message.loading('正在统计...', 0);
    let param = {
        uuid: tabUuid.value,
        page: 1,
        several: 100000
    }
    statisticsRule(param).then((res) => {
        loadingMessage();
        statisticsRuleDialog.value = true;
        statisticsRuleData.ruleCount = res.data.ruleCount;
        statisticsRuleData.decisionTableCount = res.data.decisionTableCount;
        statisticsRuleData.decisionTableRowCount = res.data.decisionTableRowCount;
        statisticsRuleData.total = res.data.total;
        message.success('统计完成');
    }).catch(err => {
        loadingMessage();
        message.error('统计失败');
        console.log(err);
    });
}


const exportRule = () => {
    modal.confirm({
        title: () => "导出确认",
        content: () => `您确定要导出规则库 ${spanInfo.engUuid} 的规则详情吗？`,
        okText: () => '确定',
        okType: 'primary',
        cancelText: () => '取消',
        onOk() {
            const loadingMessage = message.loading('正在导出...', 0);
            getExportRule({
                uuid: tabUuid.value,
                snapShootEdition: spanInfo.snapShootEdition
            }).then((res) => {
                loadingMessage();
                let headers = res.headers;
                let blob = new Blob([res.data], {
                  type: headers["content-type"],
                });
                let link = document.createElement("a");
                let url = window.URL.createObjectURL(blob);
                let fileName = headers["content-disposition"]
                  .split(";")[1]
                  .split("=")[1];
                link.href = url;
                link.download = decodeURI(fileName);
                link.style.display = "none";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                message.success('导出成功');
            }).catch(err => {
                loadingMessage();
                message.error('导出失败');
                console.log(err);
            });
        }
    });
}
const queryList = () => {
    paginations.value.page = 1;
    currentPage.value = 1;
    getRuleList();
}
const reset = () => {
    form.value.rule_name = "";
    form.value.rule_type = "";
    getRuleList();
}

const getDicValue = (type: string, fn: (data: any) => void) => {
    getDicValueByTypeCode({
        typeCode: type
    }).then((res) => {
        fn && fn(res.data)
    })
}

//返回
const ruleForm = () =>{
    navigateTo("/rulePublishHistory")
}

//详情
const emit = defineEmits(['parentmehods']);
const relDetails = (record: RuleRecord) => {
    store.commit("settings/Base_ID",record);
    emit("parentmehods", record);
}

// 组件被挂载时执行
onMounted(() => {
   if (props.uuid) {
       tabUuid.value = props.uuid;
       getRuleHeadList();
       //getRuleList();
   }
   getDicValue("type_rule", (arr: DictionaryItem[]) => {
        ruleTypeOptions.value = arr;
   });
});

const pagin = (cur: number, pageSize: number) => {
    paginations.value.page = cur;
    paginations.value.limit = pageSize;
    getRuleList();
};

// 表格列定义
const tableColumns = ref([
    {
        title: '序号',
        key: 'index',
        width: 50,
        align: 'center',
        fixed: 'left' as const
    },
    {
        title: '规则名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        width: 150,
        align: 'left',
        ellipsis: true,
        fixed: 'left' as const
    },
    {
        title: '规则路径',
        dataIndex: 'packageNameAll',
        key: 'packageNameAll',
        width: 300,
        align: 'left',
        ellipsis: true
    },
    {
        title: '版本',
        dataIndex: 'edition',
        key: 'edition',
        width: 80,
        align: 'center',
        ellipsis: true
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 80,
        align: 'center',
        ellipsis: true
    },
    {
        title: '规则数量',
        dataIndex: 'count',
        key: 'count',
        width: 80,
        align: 'center',
        ellipsis: true
    },
    {
        title: '描述',
        dataIndex: 'descs',
        key: 'descs',
        width: 200,
        align: 'center',
        ellipsis: true
    }
]);
</script>

<style lang="scss">
.rule_details ::v-deep {
  .dialog_title{
    font-size: 16px;
    color: #409EFF;
  }
  .el-dialog__header {
    background: #e6e9f1;
  }
  .el-dialog__body {
    padding: 20px 20px;
  }
  .dialogForm {
    .el-form-item {
      margin-bottom: 5px;
    }
  }
  
  // Export modal styling
  .ant-modal-confirm-title {
    font-weight: 600;
  }
  
  .ant-modal-confirm-content {
    margin-top: 16px;
    margin-bottom: 16px;
  }
}

.rule-name-link {
  color: black;
  cursor: pointer;
  &:hover {
    color: #1890ff; /* Ant Design 默认蓝色 */
  }
}
</style>
