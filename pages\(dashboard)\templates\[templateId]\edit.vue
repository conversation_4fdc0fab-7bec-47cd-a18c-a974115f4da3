<!-- 模板编辑页面 - 用于编辑和发布规则模板 -->

<script setup lang="ts">
// 导入必要的API和方法
import { getTemplateDetail, saveRuleTemplate } from '@/api/dashboardApi'
import { getEngineeringByUuid } from '@/api/rule_base'

// 设置页面元数据
definePageMeta({
    layout: false
})

// 页面状态和响应式数据
const templateId = useRoute().params.templateId as string  // 从路由获取模板ID
const loading = ref(false)  // 加载状态
const templateDetail = ref<any>(null)  // 模板详情数据
const ruleBaseDetail = ref<any>(null)  // 规则库详情数据
const message = inject<any>('message')  // 注入消息提示服务
const ruleEditor = ref<any>(null)  // 规则编辑器引用
// 规则相关数据
const jsonContentObject = ref<Record<string, any>>({});  // 规则内容对象
const demandUuid = ref("");  // 需求UUID
const type = ref("");  // 规则类型
const engUuid = ref("");  // 工程UUID

// 规则编辑器实例接口定义
interface RuleEditorInstance {
    getCurrentRuleData: () => any;
}

/**
 * 获取模板详细信息
 * 从API获取模板数据和相关的规则库信息
 */
const fetchTemplateDetail = async () => {
    loading.value = true
    try {
        // 获取模板详情
        const res = await getTemplateDetail({ uuid: templateId })
        if (!res.data) {
            message?.error('获取模板详情失败：模板不存在')
            return
        }
        templateDetail.value = res.data
        engUuid.value = templateDetail.value?.engUuid || ''
        type.value = templateDetail.value?.ruleType || ''
        // 解析JSON格式的规则内容
        jsonContentObject.value = templateDetail.value?.jsonContent ? JSON.parse(templateDetail.value.jsonContent) : {};
        // 获取规则库信息
        if (templateDetail.value?.engUuid) {
            const ruleBaseRes = await getEngineeringByUuid({ uuid: templateDetail.value.engUuid })
            ruleBaseDetail.value = ruleBaseRes.data
        }
        // 设置页面标题为模板名称
        if (templateDetail.value?.templateName) {
            useHead({
                title: templateDetail.value.templateName
            })
        }
    } catch (error: unknown) {
        message?.error('获取模板详情失败：' + ((error as Error).message || '未知错误'))
    } finally {
        loading.value = false
    }
}

/**
 * 发布模板
 * 获取当前规则编辑器中的规则数据并保存到服务器
 */
const publishTemplate = async () => {
    try {
        // 检查规则编辑器是否初始化
        if (!ruleEditor.value) {
            message?.error('规则编辑器未初始化');
            return;
        }
        
        // 获取规则数据
        const currentRuleData = ruleEditor.value.getRulesData();
        if (!currentRuleData) {
            message?.error('获取规则数据失败');
            return;
        }
        
        // 调用API保存规则模板
        await saveRuleTemplate({
            uuid: templateId,
            templateName: templateDetail.value?.templateName,
            ruleType: type.value,
            engUuid: engUuid.value,
            jsonContentObject: currentRuleData as unknown as JSON  // 类型转换以满足API要求
        })
        message?.success('发布成功')
    } catch (error: unknown) {
        message?.error('发布失败：' + ((error as Error).message || '未知错误'))
    }
}

// 组件挂载时获取模板详情
onMounted(() => {
    fetchTemplateDetail()
})
</script>

<template>
    <div data-testid="lock-based-doclet-editor">
        <!-- 加载状态包装器 -->
        <a-spin :spinning="loading">
            <div class="lark-editor lark-editor-lake">
                <!-- 编辑器头部区域 -->
                <div class="lark-editor-header template-editor-header template-editor-header-doc"
                    data-testid="template-editor-header">
                    <div class="lark-editor-header-content">
                        <div class="Template-module_headerTitleWrapper_lL39i">
                            <!-- 模板标题区域 -->
                            <div class="template-header-title"><svg
            width="1em"
            height="1em"
            viewBox="0 0 256 256"
            xmlns="http://www.w3.org/2000/svg"
            class="larkui-icon larkui-icon-template-center icon-svg index-module_size_wVASz"
            data-name="TemplateCenter"
            style="width: 22px; min-width: 22px; height: 22px"
          >
            <g fill="none" fill-rule="evenodd">
              <path d="M0-2h256v256H0z"></path>
              <path
                d="M85.347 68.498c4.184 0 8.268.43 12.21 1.25l-10.487 42.1-.115.482c-2.668 11.56 1.745 23.148 10.454 30.14l.196.155-25.044 44.135C45.708 180.9 25.6 156.958 25.6 128.315c0-33.036 26.75-59.817 59.747-59.817Z"
                fill="#3384F5"
              ></path>
              <path
                d="m138.617 27.454.308.074c.003 0 .006.001.006.01l75.654 18.886c8.708 2.183 14.009 11.004 11.848 19.718l-18.87 75.777c-2.137 8.614-10.78 13.897-19.394 11.925l-.307-.073c-.003-.001-.006-.002-.007-.011l-2.925-.73-23.394-41.224a26.698 26.698 0 0 0-10.042-10.042c-12.824-7.278-29.12-2.781-36.397 10.042l-10.586 18.654c-3.805-3.903-5.565-9.626-4.157-15.303l18.87-75.777c2.137-8.614 10.78-13.897 19.393-11.926Z"
                fill="#ECB604"
              ></path>
              <path
                d="m149.63 118.563 51.308 90.407c3.546 6.249 1.355 14.189-4.893 17.735a13.01 13.01 0 0 1-6.421 1.695H87.01c-7.185 0-13.01-5.824-13.01-13.01 0-2.25.585-4.462 1.696-6.42l51.307-90.407c3.546-6.249 11.486-8.44 17.735-4.894a13.01 13.01 0 0 1 4.893 4.894Z"
                fill="#E4495B"
              ></path>
            </g></svg
          >
                                <!-- 模板路径导航区域 -->
                                <div class="Template-module_headCrumb_hRt-t">
                                    <span>{{ ruleBaseDetail?.chineseName }}</span>
                                    <span class="section name-split">/</span>
                                    <!-- 模板名称编辑区域，带有悬浮提示 -->
                                    <template v-if="templateDetail">
                                        <a-tooltip :title="templateDetail?.templateName">
                                            <a-input 
                                                :value="templateDetail?.templateName"
                                                @update:value="val => templateDetail && (templateDetail.templateName = val)"
                                                class="template-name-input"
                                                :disabled="loading"
                                                placeholder="请输入模板名称"
                                            />
                                        </a-tooltip>
                                    </template>
                                    <span v-else>加载中...</span>
                                </div>
                            </div>
                            <!-- 模板标签 -->
                            <span class="templateTag"><span class="ant-tag">模板</span></span>
                        </div>
                        <!-- 右侧操作区域 -->
                        <div class="lark-editor-header-action">
                            <div data-testid="template-editor-header-publish"><button type="button"
                                    class="ant-btn ant-btn-primary" @click="publishTemplate"><span>发布</span></button></div>
                        </div>
                    </div>
                </div>
                <!-- 编辑器主体内容区域 -->
                <div id="lark-doclet-edit-root">
                    <div id="lark-text-editor" class="lakex-editor-wrapper">
                        <div class="lakex-editor-container ne-doc-major-editor">
                            <!-- 规则内容编辑区域 -->
                      <RuleEdit
                        v-if="type"
                              ref="ruleEditor"
                        :type="type"
                        :jsonContentObject="jsonContentObject"
                        :engUuid="engUuid"
                        :demandUuid="demandUuid"
                              :isTemplate="true"
                      />
                        </div>
                    </div>
                </div>
            </div>
        </a-spin>
    </div>
</template>

<style scoped>
/* 模板标题样式 */
.template-header-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 模板路径导航样式 */
.Template-module_headCrumb_hRt-t {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 模板名称输入框样式 */
.template-name-input {
    width: 200px;
}

/* 模板标签样式 */
.templateTag {
    margin-left: 8px;
    display: flex;
    align-items: center;
}

/* 标题包装器样式 */
.Template-module_headerTitleWrapper_lL39i {
    display: flex;
    align-items: center;
}

/* 头部内容区域样式 */
.lark-editor-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0px;
}

/* 编辑器根容器内边距 */
#lark-doclet-edit-root {
    padding: 0 5px;
}

/* 编辑器容器内边距 */
.lakex-editor-container {
    padding: 0 5px;
}
</style>
