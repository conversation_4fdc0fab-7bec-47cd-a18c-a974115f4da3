<template>
  <div class="span-con-else">
    <a-select
      mode="multiple"
      :disabled="disabled"
      v-model:value="innerValue"
      @change="handleChange"
      style="width: 100%"
      v-if="
        (delElseFlag && !readonly) ||
        (delRead && !elseFlag) ||
        (!elseFlag && !readonly)
      "
    >
      <a-select-option
        :value="item.value"
        :title="item.viewName"
        v-for="(item, index) in optionData"
        :key="index + '_' + item.value"
        style="width: 100%"
      >
        {{ item.viewName }}
      </a-select-option>
    </a-select>
    <div v-else-if="readonly && !delRead"></div>
    <div v-else style="width: 100%">否则</div>
    <div style="display: flex; flex-direction: column">
      <TableConditionOperationBox
        class="table-opera-box"
        :index="index"
        :col="col"
        :rowData="rowData"
        :colData="colData"
        :record="record"
        :elseFlag="elseFlag"
        :delElseFlag="delElseFlag"
        :readonly="readonly"
        v-if="col && col[4] !== '2' && !readonly"
        @operaChange="operaChange"
      />
      <a-dropdown v-if="isTable">
        <ToolOutlined />
        <template #overlay>
          <a-menu @click="onMenuChange" style="width: 105px">
            <a-menu-item v-show="enumDictName" key="checkAddVars">
              <span>批量勾选增加</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <Teleport to="body">
        <CheckAddVars
          :checkListOption="optionData"
          :checkAddisShow="checkAddisShow"
          @checkboxHide="checkboxHide"
          @addVarsSure="addVarsSure"
          @searchChange="searchChange"
          :isTable="isTable"
        />
      </Teleport>
    </div>
  </div>
</template>

<script setup>
import store from "@/store";
import { findViewName } from "@/components/ruleEditCom/utils/displayUtil";
import CheckAddVars from "@/components/ruleEditCom/ruleItemList/operationMenu/checkAddVars.vue";
import TableConditionOperationBox from "@/components/ruleEditCom/ruleItemList/operationMenu/tableConditionOperationBox";

// 注入 ruleUuid
const ruleUuid = inject('ruleUuid', '');

// 定义组件的 props
const props = defineProps({
  enumDictName: {
    type: String,
    default: "",
  },
  value: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  index: {
    type: Number,
    default: 0,
  },
  col: {
    type: String,
    default: "",
  },
  elseFlag: {
    type: Boolean,
    default: false,
  },
  delElseFlag: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  delRead: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Array,
    default: () => [],
  },
  colData: {
    type: Array,
    default: () => [],
  },
  record: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件的 emits
const emit = defineEmits(['onChange', 'operaChange']);

// 定义响应式数据
const optionData = ref([]);
const nameList = ref('');
const checkAddisShow = ref(false);
const checkboxSearchTxt = ref('');
const innerValue = ref(props.value);
const _oldOptionD = ref([]);

// 计算属性
const label = (val) => {
  const nameListArr = [];
  val.forEach((item) => {
    nameListArr.push(findViewName(optionData.value, item));
  });
  return nameListArr.join(',');
};

// 方法
const operaChange = (action) => {
  if (props.elseFlag && action === "otherwise") {
    return;
  } else if (action === "otherwise") {
    emit("onChange", { value: "否则", elseFlag: true });
  } else {
    emit("onChange", '');
  }
  const dataIndex = props.rowData.findIndex((item) => item.key === props.record.key);
  emit("operaChange", {
    action,
    index: dataIndex,
    col: props.col,
  });
};

const fetchData = () => {
  if (props.enumDictName) {
    const list = store.getters.listMap[ruleUuid].dictMap;
    optionData.value = list[props.enumDictName];
  }
};

const handleChange = (value = null) => {
  emit("onChange", value);
};

const onMenuChange = (obj) => {
  obj.domEvent.stopPropagation();
  checkAddisShow.value = true;
  enumCheckbox();
};

const enumCheckbox = () => {
  if (props.enumDictName) {
    if (!optionData.value) {
      optionData.value = store.getters.listMap[ruleUuid].dictMap[props.enumDictName];
    }
  }
  optionData.value.forEach((opItem, index) => {
    optionData.value[index] = {
      value: opItem.value,
      viewName: opItem.viewName,
      checked: false,
    };
  });
  props.value.forEach((item) => {
    optionData.value.forEach((opItem, index) => {
      if (item === opItem.value) {
        optionData.value[index] = {
          value: opItem.value,
          viewName: opItem.viewName,
          checked: true,
        };
      }
    });
  });
  _oldOptionD.value = JSON.parse(JSON.stringify(optionData.value));
};

const checkboxHide = () => {
  checkAddisShow.value = false;
};

const addVarsSure = (list, key) => {
  checkAddisShow.value = false;
  emit("onChange", list);
};

const searchChange = (val) => {
  checkboxSearchTxt.value = val;
};

// 监听 disabled 的变化
watch(
  () => props.disabled,
  () => {
    fetchData();
  }
);

// 监听 value 的变化
watch(
  () => props.value,
  (newVal) => {
    innerValue.value = newVal;
    nameList.value = label(newVal);
  },
  { deep: true, immediate: true }
);

// 监听 checkboxSearchTxt 的变化
watch(
  () => checkboxSearchTxt.value,
  (val) => {
    optionData.value = JSON.parse(JSON.stringify(_oldOptionD.value));
    if (val) {
      if (optionData.value && optionData.value.length) {
        optionData.value = optionData.value.filter((item) => {
          return (
            item["viewName"]
              .toLowerCase()
              .indexOf(checkboxSearchTxt.value.toLowerCase()) !== -1
          );
        });
      }
    }
  },
  { deep: true }
);

// 组件挂载后初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
.date-picker {
  width: 125px;
}
</style>
