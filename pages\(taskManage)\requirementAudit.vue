<!-- 需求审核页 -->

<script setup lang="ts">
    import { useRouter, useRoute } from 'vue-router';
    import { getBaseLine } from "@/api/baseline_Management";
    import { Requirement, userOrgList } from "@/api/task";
    import { TASK_STATE,tableColumns } from '@/consts/taskManageConsts';
    import RuleInfo from "@/businessComponents/task/RuleInfo";
    import useTableConfig from '@/composables/useTableConfig';

    definePageMeta({
        title: '需求审核'
    })
    const router = useRouter();
    const route = useRoute();
    const modal = inject('modal') as any;
    const message = inject('message') as any;
    const tableDate = ref<any[]>([]);
    const form = ref({
        appNo: "",
        businessLine: "",
        createdName: "",
        curOrgId: "",
        demandName: "",
        endDate: "",
        page: "",
        several: "",
        startDate: "",
        auditType: "",
        demandUuid: "",
        logContent: "",
        endTime: "",
        fristTime: "",
        state: "",
    });

    // 定义不包含序号列和操作列的表格列
    const tableColumnsDetail = tableColumns.filter(column => column.key !== 'action');

    // 添加 loading 变量
    const loading = ref(false);

    const businessLine_option = ref<any[]>([]);
    const model_type_options = ref<any[]>([]);
    const task_state_options = ref([
        { value: "2", label: "需求待审核" },
        { value: "3", label: "需求审核中" },
    ]);

    // 搜索配置
    const searchConfig = reactive({
        // 简单搜索字段
        simpleSearchField: {
            label: '任务名称',
            field: 'demandName'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '任务编码',
                field: 'appNo',
                compType: 'input'
            },
            {
                label: '任务名称',
                field: 'demandName',
                compType: 'input'
            },
            {
                label: '业务条线',
                field: 'businessLine',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: (value: string) => getCreatedName(value),
                    clearFields: ['curOrgId']
                }
            },
            {
                label: '当前审核机构',
                field: 'curOrgId',
                compType: 'select',
                compConfig: {
                    options: []
                }
            },
            {
                label: '申请时间',
                field: 'fristTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 00:00:00'
                }
            },
            {
                label: '结束时间',
                field: 'endTime',
                compType: 'datePicker',
                compConfig: {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD 23:59:59'
                }
            },
            {
                label: '申请人',
                field: 'createdName',
                compType: 'input'
            },
            {
                label: '任务状态',
                field: 'state',
                compType: 'select',
                compConfig: {
                    options: task_state_options.value.map(item => ({
                        name: item.label,
                        value: item.value
                    }))
                }
            }
        ]
    });

    // 获取列表数据
    const fetchTaskList = async (params: Record<string, any> = {}) => {
        try {
            const res = await Requirement({
                appNo: params.appNo,
                businessLine: params.businessLine,
                createdName: params.createdName,
                curOrgId: params.curOrgId,
                demandName: params.demandName,
                startDate: params.fristTime,
                endDate: params.endTime,
                page: params.page || 1,
                several: params.pageSize || 10,
                state: params.state,
            });
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            message.error('获取任务列表失败');
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const getOption = () => {
        getBaseLine().then((res: any) => {
            businessLine_option.value = res.data;

            // 更新搜索配置中的业务条线选项
            const businessLineField = searchConfig.advancedSearchFields.find(field => field.field === 'businessLine');
            if (businessLineField && businessLineField.compConfig) {
                businessLineField.compConfig.options = businessLine_option.value.map(item => ({
                    name: item.name,
                    value: item.code
                }));
            }
        });
    };

    const getCreatedName = (value: string) => {
        form.value.curOrgId = "";
        let name = "";
        businessLine_option.value.map((i) => {
            if (i.code === value) {
                name = i.name;
            }
        });
        userOrgList({
            roleName: "DEMAND_ROLE_MANAGEMENT",
            businessCode: value,
        }).then((res: any) => {
            model_type_options.value = res.data;

            // 更新搜索配置中的归属机构选项
            const curOrgField = searchConfig.advancedSearchFields.find(field => field.field === 'curOrgId');
            if (curOrgField && curOrgField.compConfig) {
                // 先清空数组
                curOrgField.compConfig.options = [];
                if(model_type_options.value) {
                    // 使用新数组替换原有options，保证响应式更新
                    const newOptions = model_type_options.value.map(item => ({
                        name: item.orgName,
                        value: item.id
                    }));
                    // 使用nextTick确保DOM更新后再填充数据
                    nextTick(() => {
                        curOrgField.compConfig.options = [...newOptions];
                    });
                }
            }
        });
    };

    const reset = () => {
        form.value = {
            appNo: "",
            businessLine: "",
            createdName: "",
            curOrgId: "",
            demandName: "",
            endDate: "",
            page: "",
            several: "",
            startDate: "",
            auditType: "",
            demandUuid: "",
            logContent: "",
            endTime: "",
            fristTime: "",
            state: "",
        };
        listLayout.value?.refresh();
    };

    const handleSearch = (formValue: any) => {
        // 根据formValue更新form值
        Object.keys(formValue).forEach(key => {
            if (form.value.hasOwnProperty(key)) {
                form.value[key] = formValue[key];
            }
        });
        pagination.value.page = 1;
        listLayout.value?.refresh();
    };

    const handlePageChange = (cur: number, pageSize: number) => {
        pagination.value.page = cur;
        pagination.value.limit = pageSize;
        listLayout.value?.refresh();
    };

    const listWithReset = () => {
        pagination.value.page = 1;
        listLayout.value?.refresh();
    };

    onMounted(() => {
        getOption();
    });

    //显示详情对话框
    const datar = ref({});
    const modalType = ref('');
    const isModalVisible = ref<boolean>(false)
    const showModal = (type: string, record: any = {}) => {
        modalType.value = type;
        if (type === 'AuditOperation') {
            datar.value = {
                type: "AuditOperation",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                businessCode: record.businessLine,
                createdTime: record.createdTime,
            };
        }
        if (type === 'audit') {
            datar.value = {
                type: "audit",
                uuid: record.uuid,
                appNo: record.appNo,
                bizKey: record.bizKey,
                file: record.file,
                roleName: record.ruleName,
                businessCode: record.businessLine,
                createdId: record.createdId,
                createdName: record.createdName,
            };
        }
        isModalVisible.value = true;
    };
    const close = (flag: boolean) => {
        if(flag){//如果是数据提交，则刷新表单
            listLayout.value?.refresh();
        }
        isModalVisible.value = false;
    }

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: handleSearch,
        // 添加事件
        addNewEvent: () => {},
        // 表单处理器配置
        formHandler: {
            // 表单对象引用
            form: form,
            // 查询方法
            queryMethod: listWithReset
        }
    };

    // 获取操作菜单项
    const getActionMenuItems = (record: any) => {
        return [
            {
                key: 'audit',
                label: '审核',
                onClick: () => showModal('audit', record)
            }
        ];
    };

    // 组件引用
    const listLayout = ref(null);

    // 自定义渲染列
    const customRenderColumns = ['demandName', 'state'];
</script>

<template>
    <ListPage
        ref="listLayout"
        title="需求审核"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :tableColumns="tableColumnsDetail"
        :queryMethod="fetchTaskList"
        :actionMenuGetter="getActionMenuItems"
        :customRenderColumns="customRenderColumns"
        :showAddButton="false"
    >
        <template #demandName="{ record }">
            <a-button type="link" size="small" @click="showModal('AuditOperation',record)">{{record.demandName}}</a-button>
        </template>

        <template #state="{ record }">
            <span>{{ TASK_STATE[record.state] }}</span>
        </template>
    </ListPage>

    <RuleInfo :isModalVisible="isModalVisible" @close="close" :datar="datar" :title="'需求审核'" />
</template>

<style lang="scss" scoped>
    // 加粗所有表头文字
    :deep(.ant-table-thead > tr > th) {
        font-weight: bold !important;
        color: #000 !important;
    }

    // 添加表格行hover效果
    :deep(.ant-table-tbody > tr:hover > td) {
        background-color: #e6f7ff !important;
    }
</style>

