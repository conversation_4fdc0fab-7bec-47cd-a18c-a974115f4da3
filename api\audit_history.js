import request from '@/utils/request'
//新增/修改字典项提交
export function auditList(params) {
    return request({
      url: 'erule/manage/ruleCheckHis/list',
      method: 'get',
      params, 
    })
  }
//审核状态
export function auditstate(params) {
    return request({
      url: 'sys/dictionaryValue/getDicValueByTypeCode',
      method: 'get',
      params,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
    })
  }
  //获取展示的规则信息
  export function getRuleInfo(params) {
    return request({
      url: 'erule/manage/ruleCheckHis/getRuleCheckLogByUuid',
      method: 'get',
      params,
      headers: {
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
    })
  }
    //获取审核失败展示的规则信息/审核信息
    export function getPassInfo(params) {
      return request({
        url: 'erule/manage/ruleCheckHis/getRuleHisFailInfo',
        method: 'get',
        params,
        headers: {
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
      })
    }
    //获取审核成功展示的规则信息/审核信息
    export function getBuckInfo(params) {
      return request({
        url: 'erule/manage/ruleCheckHis/getRuleHisInfo',
        method: 'get',
        params,
        headers: {
          'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
      })
    }
