<template>
  <div :class="'pre-rule-content' + classN">
    <!-- 修改：使用通用高亮效果类 -->
    <div style="margin-top: -20px; display: inline-block; white-space: nowrap;" class="common-highlight">
      <span style="margin-right: 5px; color: #000090; font-weight: bold;">定义</span>
      <InputItem
        v-bind="inputItemOption"
        inputtype="inputObj"
        @onChange="inputItemChange"
      />
      <span class="pre-txt" style="color: #000090; font-weight: bold;">为</span>
      <span
        v-if="operatorType === 'fromIn'"
        style="color: #b203b2; font-weight: bold;"
        >一个</span
      >
      <PredefineTypeCom
        v-bind="predefineTypeOption"
        @onChange="changePredefindComType"
      />
      <PreOperatorTypeCom
        v-bind="preOperatorTypeOption"
        @onChange="changePreOperatorTypeCom"
      />
      <InputItem
        v-if="inputOrSelect === 'Input'"
        v-bind="inputOption"
        @onChange="inputvariableValueChange"
      />
      <BooleanInput
        v-if="inputOrSelect === 'BooleanInput'"
        v-bind="boolInputOption"
        @onChange="inputvariableValueChangeB"
      />
      <PreOperatorVariable
        v-if="inputOrSelect === 'Select'"
        v-bind="preOperatorVariableOption"
        @onchange="changePreOperatVariable"
      />
      <span
        v-if="operatorType === 'fromIn'"
        class="pre-txt color-red"
        style="color: #000; font-weight: bold;"
        >之中</span
      >

      <span
        v-if="!locked"
        class="iconContainer"
        style="margin-top: 0px; font-size: 14px; line-height: 16px; color: rgba(0, 0, 0, 0.4); vertical-align: middle;"
        @click="delPredefineList"
      >
        <DeleteOutlined />
      </span>

      <a-tooltip v-if="validate===false">
        <template #title>{{msg}}</template>
        <CloseCircleOutlined :style="{ color: validate ? 'green' : 'red', verticalAlign: 'middle' }" />
      </a-tooltip>
      <a-tooltip v-if="validate">
        <template #title>{{msg}}</template>
        <CheckCircleOutlined :style="{ color: validate ? 'green' : 'red', verticalAlign: 'middle' }" />
      </a-tooltip>
    </div>

    <!-- 修改：为没有条件的情况下，使用行内按钮样式替代整行高亮 -->
    <div
      v-if="!hasConditions"
      class="pre-title"
      style="position: relative; z-index: 5; display: block; font-weight: 500; margin-left: 15px; color: #000090; font-weight: bold; margin-bottom: 20px;"
    >
      <span>满足条件</span>
      <span v-if="!locked" style="margin-left: 8px;">
        <a-button
          type="link"
          size="small"
          @click="addPredefineCondition"
          style="padding: 2px 4px; font-size: 12px; height: auto; color: rgba(0,0,0,0.5); line-height: 1;"
        >
          <PlusOutlined />
        </a-button>
      </span>
      <span v-else style="margin-left: 10px; color: rgba(0,0,0,0.5);">无</span>
    </div>
    <div
      v-else
      class="pre-title"
      style="position: relative; z-index: 5; display: block; font-weight: 500; margin-left: 15px; color: #000090; font-weight: bold; margin-bottom: 0;"
    >
      满足条件
      <PreCondition
        v-bind="preConditionOption"
        @onchange="changePredefineCondition"
      />
    </div>
  </div>
</template>

<script setup>
import { InputItem } from "@/components/ruleEditCom/utils/index";
import BooleanInput from "@/components/ruleEditCom/ruleItemList/operationMenu/booleanSelect.vue";
import PredefineTypeCom from "./predefineTypeCom.vue";
import PreOperatorTypeCom from "./preOperatorTypeCom.vue";
import PreOperatorVariable from "./preOperatorVariable.vue";
import PreCondition from "./preConditionCom.vue";
import { DeleteOutlined, EditOutlined, CloseCircleOutlined, CheckCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';

// 定义组件的 props
const props = defineProps({
  locked: Boolean,
  id: String,
  preViewName: String,
  inputvariableValue: [String, Number],
  variableType: String,
  inputOrSelect: String,
  operatorType: String,
  classN: String,
  preIndex: Number,
  selectVariableValue: {
    type: Object,
    default: () => ({}),
  },
  initModelData: {
    type: Object,
    default: () => ({}),
  },
  conditions: {
    type: Object,
    default: () => ({}),
  },
  validateResult: {
    type: Object,
    default: () => ({}),
  },
  hasConditions: Boolean,
});

// 定义组件的 emits
const emit = defineEmits([
  "predefineListChange",
  "delPredefineList",
  "addPredefineList",
]);

// 定义响应式数据
const validate = ref(null);
const msg = ref("");

// 初始化条件数据
const initConditionData = {
  variable: {
    valueType: "String",
    variableType: "dataEntry",
  },
  leftValueType: "String",
};

const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: "#",
};

// 定义响应式选项对象
const inputItemOption = computed(() => ({
  key: props.id + "in",
  pos: props.id,
  locked: props.locked,
  variableData: {
    value: props.preViewName,
    valueType: "String",
    variableType: "constant",
  },
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const inputOption = computed(() => ({
  key: props.id + "in",
  pos: props.id,
  locked: props.locked,
  variableData: {
    value: props.inputvariableValue,
    valueType: props.variableType,
    variableType: "constant",
  },
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const boolInputOption = computed(() => ({
  key: props.id + "in",
  pos: props.id,
  locked: props.locked,
  value: props.inputvariableValue || "null",
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const predefineTypeOption = computed(() => ({
  value: props.variableType,
  initModelData: props.initModelData,
  locked: props.locked,
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const preOperatorTypeOption = computed(() => ({
  value: props.operatorType,
  locked: props.locked,
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const preOperatorVariableOption = computed(() => ({
  selectVariableValue: props.selectVariableValue,
  locked: props.locked,
  variableType: props.variableType,
  preIndex: props.preIndex,
  predefineLine: "predeLine",
}));

const preConditionOption = computed(() => ({
  conditions: props.conditions,
  pos: "r",
  validList: props.validateResult.predefineRule || [],
  locked: props.locked,
  preIndex: props.preIndex,
  predefineLine: "predeLine",
  predefineCon: "predefineCon",
}));

// 监听 validateResult 的变化
watch(
  () => props.validateResult,
  (newVal) => {
    if (newVal && newVal.predefine) {
      validate.value = newVal.predefine.validate;
      msg.value = newVal.predefine.msg;
      preConditionOption.value.validList = newVal.predefineRule || [];
    } else {
      validate.value = null;
      msg.value = "";
      preConditionOption.value.validList = [];
    }
  },
  { deep: true, immediate: true }
);

// 监听 conditions 的变化
watch(
  () => props.conditions,
  (newVal) => {
    preConditionOption.value.conditions = newVal;
  },
  { deep: true, immediate: true }
);

// 方法
const inputItemChange = (pos, valObj) => {
  emit(
    "predefineListChange",
    { preViewName: valObj.value, id: props.id },
    true
  );
};

const inputvariableValueChange = (pos, valObj) => {
  emit(
    "predefineListChange",
    { inputvariableValue: valObj.value, id: props.id },
    true
  );
};

const inputvariableValueChangeB = (pos, value) => {
  emit(
    "predefineListChange",
    { inputvariableValue: value, id: props.id },
    true
  );
};

const changePredefindComType = (pos, selectedOptions) => {
  let type;
  if (selectedOptions[1]) {
    type = selectedOptions[1].value;
  } else {
    type = "Select";
  }
  emit(
    "predefineListChange",
    {
      variableType: selectedOptions[0].value,
      variableTypeName: selectedOptions[0].label,
      inputOrSelect: type,
      id: props.id,
    },
    true
  );
};

const changePreOperatorTypeCom = (pos, selectedOptions) => {
  emit(
    "predefineListChange",
    { operatorType: selectedOptions[0].value, id: props.id },
    true
  );
};

const changePreOperatVariable = (newCondition) => {
  emit(
    "predefineListChange",
    { selectVariableValue: newCondition, id: props.id },
    true
  );
};

const addPredefineCondition = () => {
  emit("predefineListChange", { hasConditions: true, id: props.id }, false);
};

const changePredefineCondition = (condition) => {
  const newCondition = {
    conditions: condition.conditions,
    conditionExpression: condition.conditionExpression
      ? condition.conditionExpression.slice(
          1,
          condition.conditionExpression.length - 1
        )
      : null,
  };
  emit(
    "predefineListChange",
    {
      conditions: Object.keys(condition).length > 0 ? newCondition : {},
      hasConditions: Object.keys(condition).length > 0,
      id: props.id,
    },
    true
  );
};

const delPredefineList = () => {
  emit("delPredefineList", props.id, true);
};
</script>

<style scoped>
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}

.arrow-icon {
  margin: 0 5px !important;
  transition: color 0.3s !important;
  cursor: pointer !important;
  color: #666666 !important;
  font-size: 14px !important;
}

.arrow-icon:hover {
  color: #1890ff !important;
}
</style>
