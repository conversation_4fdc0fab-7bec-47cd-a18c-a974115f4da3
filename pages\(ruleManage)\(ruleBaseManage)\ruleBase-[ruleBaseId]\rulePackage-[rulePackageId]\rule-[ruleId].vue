<!-- 规则详情 -->
<script setup lang="ts">
import { getRuleByUuid } from '@/api/rule_base'
import { recordAccess, collectRule, CollectObjectType } from '@/api/dashboardApi'

interface RuleInfo {
    uuid: string
    ruleName: string
    packageNameAll: string
    status: string
    lockId: string
    lastModifiedTimeStr: string
    modifiedId: string
    createdTimeStr: string
    createdId: string
    ruleNumber: string
    descs: string
    textDtl: string
    ifCollect: boolean
}

const ruleInfoData = ref<RuleInfo>({} as RuleInfo)

definePageMeta({
    layout: 'default',
    title: ' '
})

const router = useRouter();
const route = useRoute()

const goClose = () => {
    //判断是否为首页进入，是则返回首页
    window.close()
};

// 获取路由和标题更新方法
const updateRouteTitle = inject('updateRouteTitle') as (path: string, title: string) => void;

// 监听规则信息变化，更新页面标题
watch(ruleInfoData, (newVal) => {
    if (newVal.ruleName && newVal.packageNameAll) {
        updateRouteTitle(route.path, newVal.ruleName);
    }
}, { deep: true })
// 获取规则信息
function getRuleInfo(ruleId: string) {
    getRuleByUuid({
        uuids: ruleId
    }).then(res => {
        ruleInfoData.value = res.data
        // 异步记录访问历史
        recordAccess({ ruleUuid: ruleId }).catch(console.error)
    })
}

function getData() {
    const ruleId = route.params.ruleId as string
    getRuleInfo(ruleId)
}

onActivated(() => {
    getData()
})

onMounted(() => {
    // getData()
})

const message = inject<any>('message')

// 处理收藏
const handleCollect = async () => {
    try {
        await collectRule({
            uuid: route.params.ruleId as string,
            type: CollectObjectType.RULE // 使用枚举类型代替硬编码的"3"
        })
        ruleInfoData.value.ifCollect = !ruleInfoData.value.ifCollect
        message.success(ruleInfoData.value.ifCollect ? '收藏成功' : '取消收藏成功')
    } catch (error) {
        message.error(ruleInfoData.value.ifCollect ? '收藏失败' : '取消收藏失败')
        console.error('收藏操作失败:', error)
    }
}
</script>

<template>
    <div>
        <div style="float: right;margin-top:10px;margin-right: 15px;">
            <div style="display: flex; align-items: center;">
                <a style="color: black; margin-right: 10px;">
                    <a-tooltip :title="ifCollect ? '取消收藏' : '收藏'">
                        <div @click="handleCollect">
                            <IconStar v-if="ifCollect" size="20" />
                            <IconStarOutlined v-else size="20" />
                        </div>
                    </a-tooltip>
                </a>
                <a-button type="primary" style="margin-right: 10px;" @click="navigateTo(route.path.replace('/rule-', '/ruleContent-').replace('/rulePackage-', '/rulePackage--')+'?backFlag='+route.query.backFlag)">编辑</a-button>
                <a-button type="button"
                          @click="goClose">
                    <span>关闭</span>
                </a-button>
            </div>
        </div>

    </div>
    <RuleDetail :ruleInfoData="ruleInfoData"></RuleDetail>
</template>
<style lang="scss" scoped>
    .row-bg {
       margin-top: 3px;
    }
</style>
