<!-- 决策表图标 -->

<script setup lang="ts">
interface Props {
    size?: number
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 18,
    class: ''
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"
        :class="[
            'larkui-icon',
            'larkui-icon-doc-type-sheet',
            'icon-svg',
            'index-module_size_wVASz',
            props.class
        ]"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <g fill="none" fill-rule="evenodd">
            <path
                d="M4.75 1.267h10.5a2 2 0 0 1 2 2v13.5a2 2 0 0 1-2 2H4.75a2 2 0 0 1-2-2v-13.5a2 2 0 0 1 2-2Z"
                stroke="#469171" stroke-width="0.976" fill="#FFF" stroke-linecap="round"
                stroke-linejoin="round"></path>
            <path
                d="M9.6 9h5.3a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H9.6a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6ZM5.1 9h1.8a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H5.1a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6ZM9.6 4.5h5.3a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H9.6a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6ZM5.1 4.5h1.8a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H5.1a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6Z"
                fill="#5CC887"></path>
            <path
                d="M9.6 13.5h5.3a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H9.6a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6ZM5.1 13.5h1.8a.6.6 0 0 1 .6.6v.8a.6.6 0 0 1-.6.6H5.1a.6.6 0 0 1-.6-.6v-.8a.6.6 0 0 1 .6-.6Z"
                fill="#B0E8C3"></path>
        </g>
    </svg>
</template> 