<!--字典类型-->

<script setup lang="ts">
const modal = inject('modal');
import { dictionList, dictionDel } from "@/api/dictionarytype";
import DictionaryTypeAdd from "@/businessComponents/dictionaryType/DictionaryTypeAdd";
import DictionaryTypeUpdate from "@/businessComponents/dictionaryType/DictionaryTypeUpdate";
const message = inject('message')
import qs from "qs";
import { ref, reactive, computed, onMounted } from "vue";
import { checkPermi } from "@/directive/permission/permission";
import useTableConfig from '@/composables/useTableConfig';
definePageMeta({
    title: '字典类型'
})

// 定义表格列
const tableColumns = [
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'left',
        ellipsis: true,
    },
    {
        title: '代码',
        dataIndex: 'code',
        key: 'code',
        align: 'left',
        ellipsis: true,
    }
];

const fetchDictionaryType = async (params: Record<string, any> = {}) => {
    try {
        const res = await dictionList({
            code: params.code,
            name: params.name,
            page: params.page || 1,
            several: params.pageSize || 10,
        });
        return {
            data: res.data.data,
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取字典类型失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

const handleDelete = (record) => {
    modal.confirm({
        title: '确定删除吗？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
            dictionDel(
                qs.stringify({
                    uuid: record.uuid,
                })
            ).then(() => {
                message.success("删除成功");
                listLayout.value?.refresh();
            });
        }
    });
};

const isModalVisible = ref(false);
const modalType = ref('');
const datar = ref('');

// 字典类型新增组件
const dictionaryTypeAddComponent = ref()
// 字典类型更新组件
const dictionaryTypeUpdateComponent = ref()
const isSaving = ref(false);

// 处理modal ok 事件
function handleModalOk() {
    isSaving.value = true;
    let submitFun
    switch (modalType.value) {
        case 'add':
            submitFun = dictionaryTypeAddComponent.value.submitFun;
            break;
        case 'update':
            submitFun = dictionaryTypeUpdateComponent.value.submitFun;
            break;
    }
    submitFun && submitFun((success) => {
        if (success) {
            listLayout.value?.refresh();
            isModalVisible.value = false;
        }
        isSaving.value = false;
    });
}

const showModal = (type, record = {}) => {
    modalType.value = type;
    if (type === 'update') {
        datar.value = record;
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    isModalVisible.value = false;
    isSaving.value = false;
};

// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '名称',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '代码',
            field: 'code',
            compType: 'input'
        },
        {
            label: '名称',
            field: 'name',
            compType: 'input'
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加事件
    addNewEvent: () => showModal('add'),
    // 表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchDictionaryType
    }
};

// 获取操作菜单项
const getActionMenuItems = (record) => {
    const items = [];

    // 更新操作
    items.push({
        key: 'update',
        label: '更新',
        onClick: () => showModal('update', record)
    });

    // 删除操作
    items.push({
        key: 'delete',
        label: '删除',
        onClick: () => handleDelete(record)
    });

    return items;
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

// 数据加载完成处理函数
const handleDataLoaded = (response: any) => {
    if (response && response.data) {
        // 可以在这里处理数据加载完成后的逻辑
    }
};
</script>

<template>
    <ListPage
        ref="listLayout"
        title="字典类型"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="true"
        :tableColumns="tableColumns"
        :queryMethod="fetchDictionaryType"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
    >
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal v-if="isModalVisible" :visible="isModalVisible" :title="modalType === 'add' ? '新增字典类型' : '更新字典类型'" @ok="handleModalOk"
        @cancel="handleCancel" okText="保存" :okButtonProps="{ disabled: isSaving }" class="dictionary-type-modal" :width="600">
        <div style="max-height: 60vh; overflow-y: auto;">
            <DictionaryTypeAdd ref="dictionaryTypeAddComponent" v-if="modalType === 'add'" />
            <DictionaryTypeUpdate ref="dictionaryTypeUpdateComponent" v-if="modalType === 'update'" :datar="datar" />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.dictionary-type-modal {
    :deep(.ant-modal-body) {
        padding: 20px 24px;
        overflow: visible;
    }

    :deep(.ant-modal-footer) {
        border-top: 1px solid #f0f0f0;
        padding: 10px 24px;
        margin-top: 0;
    }
}
</style>
