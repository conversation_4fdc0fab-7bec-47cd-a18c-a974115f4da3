/**
 * A palette that allows you to create BPMN _and_ custom elements.
 */
export default function PaletteProvider(palette, create, elementFactory, bpmnFactory, translate, handTool, lassoTool, globalConnect) {
  this.create = create
  this.elementFactory = elementFactory
  this.bpmnFactory = bpmnFactory
  this.translate = translate;
  this.handTool = handTool;
  this.lassoTool = lassoTool;
  this.globalConnect = globalConnect;

  palette.registerProvider(this)
}

PaletteProvider.$inject = [
  'palette',
  'create',
  'elementFactory',
  'bpmnFactory',
  'translate',
  'handTool',
  'lassoTool',
  'globalConnect'
]

PaletteProvider.prototype.getPaletteEntries = function (element) {
  const {
    create,
    elementFactory,
    bpmnFactory,
    translate,
    handTool,
    lassoTool,
    globalConnect
  } = this;

  // 创建开始节点
  function createStratEvent() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:StartEvent', {
        name: '开始节点'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:StartEvent',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建决策网关 bpmn:ExclusiveGateway
  function createInclusiveGateway() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:ExclusiveGateway', {
        name: '决策分支',
        gatewayDirection: 'Diverging'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:ExclusiveGateway',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建规则节点
  function createBusinessRuleTask() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:BusinessRuleTask', {
        name: '规则节点'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:BusinessRuleTask',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建汇聚网关
  function createExclusiveGateway() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:ExclusiveGateway', {
        name: '汇聚分支',
        gatewayDirection: 'Converging'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:ExclusiveGateway',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建结束节点
  function createEndEvent() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:EndEvent', {
        name: '结束节点'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:EndEvent',
        businessObject
      });
      create.start(event, shape);
    }
  }
  // 创建子规则流节点
  function createCallActivity() {
    return function (event) {
      const businessObject = bpmnFactory.create('bpmn:CallActivity', {
        name: '子规则流'
      });
      const shape = elementFactory.createShape({
        type: 'bpmn:CallActivity',
        businessObject
      });
      create.start(event, shape);
    }
  }

  return {
    'hand-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-hand-tool dir-tool',
      title: translate('激活抓手工具'),
      action: {
        click: function (event) {
          handTool.activateHand(event);
        }
      }
    },
    'lasso-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-lasso-tool dir-tool',
      title: translate('激活套索工具'),
      action: {
        click: function (event) {
          lassoTool.activateSelection(event);
        }
      }
    },
    'global-connect-tool': {
      group: 'tools',
      className: 'entry bpmn-icon-connection-multi dir-tool',
      title: translate('激活连接工具'),
      action: {
        click: function (event) {
          globalConnect.toggle(event);
        }
      }
    },
    'tool-separator': {
      group: 'tools',
      separator: true
    },
    'create.start-event': {
      // group: 'event',
      group: 'model',
      // className: 'entry bpmn-icon-start-event-none dir-shape',
      className: 'entry iconfont icon-start dir-shape',
      title: translate('创建开始节点'),
      action: {
        dragstart: createStratEvent(),
        click: createStratEvent()
      }
    },
    // bpmn:ExclusiveGateway
    'create.inclusive-gateway': {
      // group: 'gateway',
      group: 'model',
      // className: 'entry bpmn-icon-gateway-or dir-shape',
      className: 'entry iconfont icon-policy dir-shape',
      title: translate('创建决策网关'),
      action: {
        dragstart: createInclusiveGateway(),
        click: createInclusiveGateway()
      }
    },
    'create.business-rule-task': {
      // group: 'activity',
      group: 'model',
      className: 'entry iconfont icon-task dir-shape',
      title: translate('创建规则节点'),
      action: {
        dragstart: createBusinessRuleTask(),
        click: createBusinessRuleTask()
      }
    },
    'create.exclusive-gateway': {
      // group: 'gateway',
      group: 'model',
      // className: 'entry bpmn-icon-gateway-xor dir-shape',
      className: 'entry iconfont icon-convergence dir-shape',
      title: translate('创建汇聚网关'),
      action: {
        dragstart: createExclusiveGateway(),
        click: createExclusiveGateway()
      }
    },
    'create.call-activity': {
      // group: 'event',
      group: 'model',
      // className: 'entry bpmn-icon-end-event-none dir-shape',
      className: 'entry iconfontflow icon-subflow dir-shape',
      title: translate('创建子规则流节点'),
      action: {
        dragstart: createCallActivity(),
        click: createCallActivity()
      }
    },
    'create.end-event': {
      // group: 'event',
      group: 'model',
      // className: 'entry bpmn-icon-end-event-none dir-shape',
      className: 'entry iconfont icon-end dir-shape',
      title: translate('创建结束节点'),
      action: {
        dragstart: createEndEvent(),
        click: createEndEvent()
      }
    }
  }
}
