<!-- 用户信息弹窗 -->

<template>
  <a-popover placement="bottomLeft"
    overlayClassName="index-module_userInfoPopover_p+TgW head-user-info-popover larkui-popover"
    v-model:open="isUserInfoPopoverShow"
    trigger="click">
    <template #content>
      <div>
        <ul class="ant-menu ant-menu-root ant-menu-vertical ant-menu-light index-module_userMenu_y1kcz" role="menu"
          tabindex="0" data-menu-list="true" data-testid="sidebar-user-menu">
          <li class="ant-menu-item ant-menu-item-only-child user-info-menu-item index-module_userInfoMenuItem_ojmYC"
            role="menuitem" tabindex="-1" data-menu-id="rc-menu-uuid-54130-5-tmp_key-0">
            <div class="index-module_userInfoAvatar_TAIYp index-module_persianUserAvatar_TVns8">
              <div data-aspm-click="d73387"><img data-testid="img-avatar"
                  src="https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*khrYRYi6VN0AAAAAAAAAAAAADvuFAQ/original"
                  class="img"
                  style="width: 36px; min-width: 36px; height: 36px; border-radius: 18px; border-color: rgba(0, 0, 0, 0.06); border-width: 1px; border-style: solid;">
              </div>
              <p class="index-module_userInfoDesc_6x2Dz">
              <div class="index-module_sideBarBadge_eeNVn" style="margin-top: 10px;">
                <div class="badge-module_memberBadgeContainer_truSH member-badge-container" data-testid="badge">
                  <div class="badge-module_memberBadgeName_Rb9bH member-badge-name">
                    <span class="badge-module_memberBadgeNameCon_UOZEP">{{ username }}</span>
                  </div>
                  <!--                   <div class="badge-module_memberBadgeDesc_nc5JL member-badge-desc">
                    <div>
                      <p><span class="badge-module_memberDesc_iad4N">管理员</span>
                      </p>
                    </div>
                  </div> -->
                  <div data-testid="pay-flow">
                    <div></div>
                  </div>
                </div>
              </div>
              </p>
            </div>
          </li>
          <li class="ant-menu-item-divider index-module_usermenuDivider_A8fnx"></li>
          <div>
            <li class="ant-menu-item ant-menu-item-active ant-menu-item-only-child" role="menuitem" tabindex="-1"
              data-menu-id="rc-menu-uuid-54130-5-undefined" @click="showChangePasswordModal">
                <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                  class="larkui-icon larkui-icon-setting index-module_icon_TGSiA">
                  <path
                    d="m107.862 18.747-.657.12-.338.066c-6.873 1.403-12.248 6.823-13.523 13.754L90.76 46.595l-.095.043a89.598 89.598 0 0 0-14.256 8.116l-.992.701-13.428-4.751c-6.647-2.33-14.024-.428-18.668 4.823l-.601.696c-8.465 9.957-15.019 21.135-19.518 33.263l-.39 1.068-.286.825a17.223 17.223 0 0 0 5.122 18.57l10.903 9.27-.084.904a90.683 90.683 0 0 0-.34 7.871l.006 1.135a90.53 90.53 0 0 0 .334 6.757l.083.884-10.868 9.24c-5.366 4.551-7.392 11.911-5.164 18.574l.333.959a110.206 110.206 0 0 0 19.838 34.185l.438.51.245.28c4.623 5.177 11.948 7.086 18.566 4.784l13.477-4.77.117.085a89.173 89.173 0 0 0 14.174 8.3l1.056.485 2.585 13.916c1.294 7.033 6.834 12.52 13.877 13.806l.592.109a111.934 111.934 0 0 0 39.578.008l.654-.12.355-.07c6.856-1.399 12.231-6.82 13.506-13.75l2.559-13.801.124-.055a89.147 89.147 0 0 0 15.264-8.782l.116-.086 13.29 4.707c6.649 2.33 14.026.427 18.67-4.823l.6-.696c8.466-9.957 15.02-21.135 19.519-33.263l.39-1.068.268-.77c2.272-6.626.238-13.996-5.104-18.58l-10.721-9.129.098-1.03c.231-2.632.348-5.273.348-7.908l-.007-1.13a90.43 90.43 0 0 0-.341-6.78l-.098-1.03 10.692-9.104c5.36-4.546 7.386-11.906 5.159-18.569l-.331-.953a109.95 109.95 0 0 0-19.84-34.191l-.439-.51-.245-.28c-4.622-5.177-11.948-7.086-18.565-4.784l-13.351 4.726-.137-.098a89.752 89.752 0 0 0-14.254-8.32l-1.105-.505-2.562-13.816c-1.294-7.036-6.834-12.524-13.877-13.809l-.593-.109c-13.008-2.338-26.61-2.338-39.574-.008Zm34.054 19.358.677.11 4.234 22.829 5.296 1.982a69.844 69.844 0 0 1 19.748 11.366l4.359 3.56 21.917-7.761.493.597a89.82 89.82 0 0 1 14.255 24.58l.239.634-17.649 15.029.913 5.57a70.76 70.76 0 0 1 .932 11.416c0 3.807-.314 7.64-.932 11.414l-.913 5.571 17.635 15.017-.223.593a90.385 90.385 0 0 1-14.29 24.623l-.462.561-21.928-7.764-4.361 3.577a69.308 69.308 0 0 1-19.733 11.353l-5.296 1.982-4.235 22.83-.9.145a92.146 92.146 0 0 1-14.066 1.08l-1.087-.006a92.191 92.191 0 0 1-13.001-1.08l-.88-.141-4.256-22.917-5.292-1.982c-7.057-2.643-13.646-6.453-19.583-11.314l-4.36-3.57-22.062 7.808-.453-.55a90.097 90.097 0 0 1-14.315-24.686l-.218-.574 17.843-15.172-.93-5.59a68.609 68.609 0 0 1-.905-11.231c0-3.815.303-7.595.907-11.24l.924-5.585L42.119 96l.238-.624a90.385 90.385 0 0 1 14.29-24.623l.461-.562 22.06 7.808 4.36-3.57a69.726 69.726 0 0 1 19.605-11.323l5.273-1.989 4.254-22.903.715-.115a92.841 92.841 0 0 1 28.542.006h-.001ZM128 90c-20.987 0-38 17.013-38 38s17.013 38 38 38 38-17.013 38-38-17.013-38-38-38Zm0 20c9.941 0 18 8.059 18 18s-8.059 18-18 18-18-8.059-18-18 8.059-18 18-18Z"
                    fill="currentColor" fill-rule="nonzero"></path>
                </svg>
                <span class="text"><span>修改密码</span></span>
            </li>
          </div>
          <li class="ant-menu-item-divider index-module_usermenuDivider_A8fnx"></li>
          <li class="ant-menu-item login-out-menu-item" role="menuitem" tabindex="-1"
            data-menu-id="rc-menu-uuid-54130-5-tmp_key-6" @click="logoutFunc">
            <div class="index-module_loginOutWarp_gaK7d"><span><svg width="1em" height="1em" viewBox="0 0 256 256"
                  xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-logout index-module_icon_TGSiA">
                  <path
                    d="M169 28c5.523 0 10 4.477 10 10s-4.477 10-10 10H58c-5.43 0-9.848 4.327-9.996 9.72L48 58v140c0 5.43 4.327 9.848 9.72 9.996L58 208h110.498c5.522 0 9.998 4.476 9.998 9.998 0 5.522-4.476 9.998-9.998 9.998L58 228c-16.403 0-29.731-13.164-29.996-29.504L28 198V58c0-16.403 13.164-29.731 29.504-29.996L58 28h111Zm21.437 49.72.215.209 28.968 28.968c11.599 11.599 11.715 30.332.348 42.073l-.348.353-29.345 29.346c-3.905 3.905-10.237 3.905-14.142 0-3.835-3.834-3.904-10.008-.21-13.927l.21-.215 26.332-26.333L99.981 138c-5.523-.01-9.991-4.496-9.981-10.019.01-5.43 4.345-9.84 9.74-9.978l.279-.003 102.614.194-26.123-26.123c-3.905-3.905-3.905-10.237 0-14.142 3.834-3.834 10.008-3.904 13.927-.21Z"
                    fill="currentColor" fill-rule="nonzero"></path>
                </svg><span class="text"><span>退出登录</span></span></span></div>
          </li>
        </ul>
      </div>
    </template>
    <div class="index-module_userAvatar_ERAUQ">
      <div class="index-module_userInfoAvatar_rtges user-info-avatar yuque-persion-avatar larkui-popover-trigger"
           @click="toggleUserInfoPopover">
        <div class="index-module_headerUser_z5NUu"><img data-testid="img-avatar" src="/img/avatar.png" class="img"
            style="width: 24px; min-width: 24px; height: 24px; border-radius: 12px;"></div>
      </div>
    </div>
  </a-popover>

  <!-- 修改密码弹窗 -->
  <a-modal v-model:open="isChangePasswordModalVisible" title="修改密码" @cancel="cancelChangePassword" width="420px">
    <div class="user-change-pwd">
      <a-form
        ref="formRef"
        :model="ruleForm"
        :rules="rules"
        label-align="right" 
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="登录ID">
          <a-input autocomplete="off" v-model:value="ruleForm.loginId" disabled />
        </a-form-item>

        <a-form-item label="密码" name="pwd" v-show="pass">
          <a-input-password v-model:value="ruleForm.pwd" />
        </a-form-item>

        <a-form-item label="确认密码" name="repeatPwd" v-show="repass">
          <a-input-password v-model:value="ruleForm.repeatPwd" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button type="primary" @click="submitForm">提交</a-button>
      <a-button @click="cancelChangePassword" style="margin-left: 8px">取消</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { changePwd, loginUserInfo } from '@/api/userManagement'
import { ref, reactive, computed, inject } from 'vue'
import type { Rule, FormInstance } from 'ant-design-vue/es/form'

const isUserInfoPopoverShow = ref(false)
const isChangePasswordModalVisible = ref(false)

// 切换用户信息弹框显示状态
function toggleUserInfoPopover() {
  isUserInfoPopoverShow.value = !isUserInfoPopoverShow.value
}

// 从 sessionStorage 获取用户名
const username = computed(() => {
  return getUnid()
})

// 修改密码表单相关
const formRef = ref<FormInstance>()
const pass = ref(true)
const repass = ref(true)
const message: any = inject('message')

const ruleForm = reactive({
  id: '',
  loginId: '',
  pwd: '',
  pwdKey: '',
  repeatPwd: '',
  name: ''
})

const validateRepeatPwd = async (_rule: Rule, value: string) => {
  if (value !== ruleForm.pwd) {
    throw new Error('两次密码不一致')
  }
}

const rules = {
  pwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' } as Rule,
    {
      pattern: /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,20}$/,
      message: '密码长度为8-20位,必须包含字母大写、小写字母、特殊符号和数字'
    } as Rule
  ],
  repeatPwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' } as Rule,
    { validator: validateRepeatPwd, trigger: 'blur' } as Rule
  ]
}

function logoutFunc() {
  logout()
}

// 显示修改密码弹窗
function showChangePasswordModal() {
  isUserInfoPopoverShow.value = false
  isChangePasswordModalVisible.value = true
  getLoginUserInfo()
}

// 获取用户信息
const getLoginUserInfo = async () => {
  try {
    const res = await loginUserInfo()
    const data = res.data
    ruleForm.loginId = data.loginId
    ruleForm.pwd = data.password
    ruleForm.repeatPwd = data.password
    ruleForm.name = data.name
    ruleForm.id = data.id
  } catch (error) {
    console.log(error)
  }
}

// 提交表单
const submitForm = async () => {
  try {
    if (formRef.value) {
      await formRef.value.validate()
      const params = {
        loginId: ruleForm.loginId,
        password: ruleForm.pwd,
        name: ruleForm.name,
        id: ruleForm.id
      }
      await changePwd(params)
      message.success('修改成功')
      isChangePasswordModalVisible.value = false
    }
  } catch (error) {
    console.log(error)
  }
}

// 取消修改密码
const cancelChangePassword = () => {
  isChangePasswordModalVisible.value = false
}
</script>

<style lang="scss" scoped>
.index-module_loginOutWarp_gaK7d {
  width: 100%;
}

/* 确保所有图标颜色一致 */
.larkui-icon {
  color: #666666 !important;
}

.user-change-pwd {
  :deep(.ant-input),
  :deep(.ant-input-password),
  :deep(.ant-select) {
    width: 240px;
  }

  :deep(.ant-form-item-label) {
    > label {
      min-width: 80px;
      justify-content: flex-end;
    }
  }
}
</style>
