<template>
  <span class="actionTypeCom" style="color: #b203b2; font-weight: bold">
    <span v-if="locked || isTrack" class="actionTypeItem txtItem">{{ txt }}</span>
    <span v-else class="actionTypeItem txtItem" @click="predefineTypeSelector">{{ txt }}</span>
  </span>
</template>

<script setup>
import { findLabel } from "@/components/ruleEditCom/utils/displayUtil.js";
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";

const props = defineProps({
  value: String,
  pos: String,
  locked: Boolean,
  isTrack: Boolean,
  initModelData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['onChange']);

const getInitData = () => {
  const list = props.initModelData.complexModels;
  let initList = [];
  list.map(item => {
    const obj = {
      value: item.valueType,
      label: item.viewName
      // label: `一个${item.viewName}`
    };
    initList.push(obj);
  });
  return initList;
};

const selfData = ref([
  {
    value: "String",
    label: "字符串",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Integer",
    label: "数字(Integer)",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Long",
    label: "数字(Long)",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Float",
    label: "数字(Float)",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Double",
    label: "数字(Double)",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Date",
    label: "日期",
    children: [
      {
        value: "Input",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  {
    value: "Boolean",
    label: "布尔值",
    children: [
      {
        value: "BooleanInput",
        label: "手动输入"
      },
      {
        value: "Select",
        label: "属性选择"
      }
    ]
  },
  ...getInitData()
]);

const txt = computed(() => findLabel(selfData.value, [props.value]));

const onSelectChange = (_value, selectedOptions) => {
  emit("onChange", props.pos, selectedOptions);
};

const predefineTypeSelector = (e) => {
  globalEventEmitter.emit(SHOW_ACASCADER, {
    e,
    pos: props.pos,
    filterOData: selfData.value,
    fn: (pos, value, selectedOptions, cascaderOptions) => {
      onSelectChange(value, selectedOptions);
    },
    flag: 'predefineType',
    cascaderSelectedValue: props.value
  });
};
</script>