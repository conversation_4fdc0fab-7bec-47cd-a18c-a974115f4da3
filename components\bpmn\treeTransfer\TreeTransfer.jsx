import { defineComponent, ref, computed, watch } from 'vue';
import { Tree } from 'ant-design-vue';
import './style.scss';
import { message } from 'ant-design-vue';
import IconRulePackage from '@/components/icon/IconRulePackage.vue';
import IconRuleBase from '@/components/icon/IconRuleBase.vue';
import IconRuleTypeOrdinaryRule from '@/components/icon/IconRuleTypeOrdinaryRule.vue';
import IconRuleTypeDecisionTable from '@/components/icon/IconRuleTypeDecisionTable.vue';
import IconRuleTypeDecisionTree from '@/components/icon/IconRuleTypeDecisionTree.vue';
import IconRuleTypeRuleFlow from '@/components/icon/IconRuleTypeRuleFlow.vue';

export default defineComponent({
  name: 'TreeTransfer',
  props: {
    targetKeys: {
      type: Array,
      default: () => []
    },
    fromData: {
      type: Array,
      default: () => []
    },
    toData: {
      type: Array,
      default: () => []
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    searchPlaceholder: {
      type: String,
      default: '请输入搜索内容'
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    defaultExpandAll: {
      type: Boolean,
      default: true
    },
    isSubFlow: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:targetKeys', 'change', 'search', 'selectChange'],
  setup(props, { emit }) {
    // 内部状态
    const selectedKeys = ref([]);
    const leftExpandedKeys = ref([]);
    const rightExpandedKeys = ref([]);
    const leftCheckedKeys = ref([]);
    const rightCheckedKeys = ref([]);
    const searchValue = ref({
      left: '',
      right: ''
    });

    // 递归收集所有子节点的key
    const collectChildKeys = (node) => {
      const keys = [];
      
      const traverse = (item) => {
        if (!item) return;
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            keys.push(child.key);
            traverse(child);
          });
        }
      };
      
      traverse(node);
      return keys;
    };

    // 计算所有可展开的keys
    const getAllKeys = (data) => {
      const keys = [];
      const traverse = (nodes) => {
        if (!nodes) return;
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            traverse(node.children);
          }
        });
      };
      traverse(data);
      return keys;
    };

    // 构建节点映射表（用于查找节点信息）
    const buildNodeMap = (data) => {
      const map = new Map();
      
      const traverse = (items) => {
        if (!items) return;
        items.forEach(item => {
          map.set(item.key, item);
          if (item.children) {
            traverse(item.children);
          }
        });
      };
      
      traverse(data);
      return map;
    };


    // 初始化展开的keys
    watch([() => props.fromData, () => props.toData], ([fromData, toData]) => {
      if (props.defaultExpandAll) {
        const fromKeys = getAllKeys(fromData);
        const toKeys = getAllKeys(toData);
        leftExpandedKeys.value = [...new Set([...fromKeys])];
        rightExpandedKeys.value = [...new Set([...toKeys])];
      }
    }, { immediate: true });

    // 判断子节点是否需要一起移动
    const shouldMoveWithChildren = (key, treeData) => {
      // 查找节点
      const findNode = (nodes, key) => {
        if (!nodes) return null;
        
        for (const node of nodes) {
          if (node.key === key) return node;
          if (node.children) {
            const found = findNode(node.children, key);
            if (found) return found;
          }
        }
        return null;
      };
      
      const node = findNode(treeData, key);
      if (!node || !node.children || node.children.length === 0) return [key];
      
      // 如果有子节点，收集所有子节点的key
      const childKeys = collectChildKeys(node);
      return [key, ...childKeys];
    };

    // 处理穿梭
    const handleChange = (keys, direction, moveKeys) => {
      // 处理子节点跟随父节点移动
      let allMoveKeys = [];
      
      // 根据方向选择正确的树数据
      const sourceData = direction === 'right' ? props.fromData : props.toData;
      
      // 为每个移动的key收集其所有子节点
      moveKeys.forEach(key => {
        const keysToMove = shouldMoveWithChildren(key, sourceData);
        allMoveKeys = [...allMoveKeys, ...keysToMove];
      });
      
      // 去重
      allMoveKeys = [...new Set(allMoveKeys)];
      
      // 计算最终的targetKeys
      let newTargetKeys = [];
      
      // 根据穿梭方向处理
      if (direction === 'right') {
        // 向右穿梭：添加新的键
        // 如果是子规则流且已经选中了一个，不允许再选择
        if (props.isSubFlow && props.targetKeys && props.targetKeys.length >= 1) {
          message.warning('只能选择一个子规则流');
          return;
        }
        newTargetKeys = [...props.targetKeys, ...allMoveKeys];
      } else {
        // 向左穿梭：从targetKeys中移除选中的键
        newTargetKeys = props.targetKeys.filter(key => !allMoveKeys.includes(key));
        // 左穿梭时重置选中状态，防止状态混乱
        selectedKeys.value = [];
        rightCheckedKeys.value = [];
      }
      
      // 再次确保去重
      newTargetKeys = [...new Set(newTargetKeys)];
      
      // 先触发目标键更新
      emit('update:targetKeys', newTargetKeys);
      
      // 构建右侧树数据用于传递
      const rightTreeData = buildRightTreeData(props.fromData, newTargetKeys);
      
      // 先确保右侧树展开状态
      if (direction === 'right') {
        const allExpandKeys = getAllKeys(rightTreeData);
        rightExpandedKeys.value = [...allExpandKeys];
      }
      
      // 然后触发change事件
      setTimeout(() => {
        emit('change', {
          targetKeys: newTargetKeys,
          direction,
          moveKeys: allMoveKeys,
          rightTreeData: rightTreeData
        });
      }, 10);
    };

    // 处理搜索
    const handleSearch = (dir, value) => {
      searchValue.value[dir] = value;
      emit('search', dir, value);
    };

    // 处理左侧勾选变化
    const handleLeftCheck = (checked, e) => {
      const { node, checked: isChecked } = e;
      
      // 只在isSubFlow为true时限制只能选一个
      if (props.isSubFlow && isChecked && leftCheckedKeys.value.length >= 1) {
        message.warning('只能选择一个子规则流');
        return;
      }
      
      // 更新左侧勾选状态
      leftCheckedKeys.value = checked;
      
      // 收集所有子节点
      let keysToSelect = [node.key];
      if (isChecked && node.children) {
        keysToSelect = [...keysToSelect, ...collectChildKeys(node)];
      }
      
      // 更新选中状态
      if (isChecked) {
        // 添加当前节点和所有子节点
        selectedKeys.value = [...new Set([...selectedKeys.value, ...keysToSelect])];
      } else {
        // 移除当前节点和所有子节点
        selectedKeys.value = selectedKeys.value.filter(key => !keysToSelect.includes(key));
      }
    };

    // 处理右侧勾选变化
    const handleRightCheck = (checked, e) => {
      const { node, checked: isChecked } = e;
      
      // 只在isSubFlow为true时限制只能选一个
      if (props.isSubFlow && isChecked && rightCheckedKeys.value.length >= 1) {
        message.warning('只能选择一个子规则流');
        return;
      }
      
      // 更新右侧勾选状态
      rightCheckedKeys.value = checked;
      
      // 收集所有子节点
      let keysToSelect = [node.key];
      if (isChecked && node.children) {
        keysToSelect = [...keysToSelect, ...collectChildKeys(node)];
      }
      
      // 更新选中状态 - 从selectedKeys中移除所有未选中的右侧节点，然后只添加当前选中的节点
      if (isChecked) {
        // 保留所有已选中的左侧节点
        const leftSelectedKeys = selectedKeys.value.filter(key => !props.targetKeys.includes(key));
        
        // 如果是子规则流，则清空已选右侧节点，只添加当前选中节点
        if (props.isSubFlow) {
          selectedKeys.value = [...leftSelectedKeys, ...keysToSelect.filter(key => props.targetKeys.includes(key))];
        } else {
          // 如果不是子规则流，保留已选的右侧节点，并添加新选中的节点
          const rightSelectedKeys = selectedKeys.value.filter(key => props.targetKeys.includes(key));
          const newKeysToAdd = keysToSelect.filter(key => props.targetKeys.includes(key) && !rightSelectedKeys.includes(key));
          selectedKeys.value = [...leftSelectedKeys, ...rightSelectedKeys, ...newKeysToAdd];
        }
      } else {
        // 移除当前节点和所有子节点
        selectedKeys.value = selectedKeys.value.filter(key => !keysToSelect.includes(key));
      }
    };

    // 过滤树数据
    const filterTreeData = (treeData, keyword, parentMatched = false) => {
      if (!treeData || !Array.isArray(treeData)) return [];
      if (!keyword) return treeData;
      
      const searchLower = keyword.toLowerCase();
      
      return treeData
        .map(node => {
          // 复制节点，避免修改原始数据
          const newNode = { ...node };
          
          // 检查当前节点是否匹配关键字
          const title = String(node.title || '');
          const isMatch = title.toLowerCase().includes(searchLower);
          
          // 处理子节点
          const childrenResult = node.children 
            ? filterTreeData(node.children, keyword, isMatch || parentMatched) 
            : [];
            
          // 如果有子节点匹配，则设置子节点
          if (childrenResult.length > 0) {
            newNode.children = childrenResult;
            return newNode;
          }
          
          // 如果当前节点匹配或者父节点匹配，则保留该节点
          if (isMatch || parentMatched) {
            // 保留原本的children属性，如果为空则设为undefined
            newNode.children = node.children && node.children.length > 0 
              ? childrenResult 
              : undefined;
            return newNode;
          }
          
          // 其他情况不保留该节点
          return null;
        })
        .filter(Boolean); // 过滤掉null值
    };

    // 构建右侧树数据
    const buildRightTreeData = (fromData, targetKeys) => {
      if (!fromData || !targetKeys || targetKeys.length === 0) {
        return [];
      }
      
      
      // 建立节点映射，用于快速查找
      const nodeMap = new Map();
      const buildNodeMap = (data) => {
        if (!data) return;
        
        data.forEach(node => {
          nodeMap.set(node.key, node);
          if (node.children) {
            buildNodeMap(node.children);
          }
        });
      };
      
      buildNodeMap(fromData);
      
      // 收集节点的所有父节点
      const parentMap = new Map();
      const buildParentMap = (data, parentIds = []) => {
        if (!data) return;
        
        data.forEach(node => {
          parentMap.set(node.key, [...parentIds]);
          if (node.children) {
            buildParentMap(node.children, [...parentIds, node.key]);
          }
        });
      };
      
      buildParentMap(fromData);
      
      // 获取所有需要显示的节点（包括父节点，但不包括在targetKeys中）
      const displayKeys = new Set();
      targetKeys.forEach(key => {
        if (!nodeMap.has(key)) {
          console.warn(`节点 ${key} 在源数据中不存在`);
          return;
        }
        
        // 添加目标节点
        displayKeys.add(key);
        
        // 添加父节点路径
        const parentKeys = parentMap.get(key) || [];
        parentKeys.forEach(parentKey => displayKeys.add(parentKey));
      });
      
      // 递归构建树结构
      const targetKeySet = new Set(targetKeys);
      const buildTreeNode = (data) => {
        if (!data) return [];
        
        return data
          .map(node => {
            // 只处理需要显示的节点
            if (!displayKeys.has(node.key)) {
              return null;
            }
            
            // 递归处理子节点
            const children = node.children ? buildTreeNode(node.children) : [];
            
            // 如果是叶子节点但不在目标键中，则不显示
            if (children.length === 0 && !targetKeySet.has(node.key)) {
              return null;
            }
            
            // 复制节点，保留原始数据
            const newNode = {
              ...node,
              children: children.length > 0 ? children : undefined,
              // 如果节点不在目标键中，标记为禁用
              disabled: false // 确保所有节点都不被禁用
            };
            
            return newNode;
          })
          .filter(Boolean);
      };
      
      return buildTreeNode(fromData);
    };

    // 左侧树数据
    const filteredLeftTreeData = computed(() => {
      // 添加图标到节点
      const processTreeData = (nodes) => {
        if (!nodes) return [];
        
        return nodes.map(node => {
          const newNode = { ...node };
          if (node.folder) {
            // 判断是否为顶层规则包
            if (!node.parentId) {
              newNode.icon = <IconRuleBase size={16} style={{ marginRight: '5px' }} />;
            } else {
              newNode.icon = <IconRulePackage size={16} style={{ marginRight: '5px' }} />;
            }
          } else if(node.type === '1'){
            newNode.icon = <IconRuleTypeOrdinaryRule size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '2'){
            newNode.icon = <IconRuleTypeDecisionTable size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '4'){
            newNode.icon = <IconRuleTypeDecisionTree size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '5'){
            newNode.icon = <IconRuleTypeRuleFlow size={16} style={{ marginRight: '5px' }} />;
          }

          
          // 只有满足folder为false或者folder为true且有子节点时，才应用禁用逻辑
          if ((node.folder === false) || (node.folder === true && node.children && node.children.length > 0)) {
            if (props.targetKeys.includes(node.key)) {
              newNode.disabled = true; // 禁用已选中的节点
            } else {
              newNode.disabled = false;
            }
          }
          if (newNode.children) {
            // 判断节点是否已在右侧树中
            newNode.children = processTreeData(newNode.children);
          }
          
          return newNode;
        });
      };
      
      const treeData = filterTreeData(props.fromData, searchValue.value.left);
      return processTreeData(treeData);
    });

    // 右侧树数据
    const filteredRightTreeData = computed(() => {
      // 添加图标到节点
      const processTreeData = (nodes) => {
        if (!nodes) return [];
        
        return nodes.map(node => {
          const newNode = { ...node };
          if (node.folder) {
            // 判断是否为顶层规则包
            if (!node.parentId) {
              newNode.icon = <IconRuleBase size={16} style={{ marginRight: '5px' }} />;
            } else {
              newNode.icon = <IconRulePackage size={16} style={{ marginRight: '5px' }} />;
            }
          } else if(node.type === '1'){
            newNode.icon = <IconRuleTypeOrdinaryRule size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '2'){
            newNode.icon = <IconRuleTypeDecisionTable size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '4'){
            newNode.icon = <IconRuleTypeDecisionTree size={16} style={{ marginRight: '5px' }} />;
          } else if(node.type === '5'){
            newNode.icon = <IconRuleTypeRuleFlow size={16} style={{ marginRight: '5px' }} />;
          }
            
          if (newNode.children) {
            newNode.children = processTreeData(newNode.children);
          }
          
          return newNode;
        });
      };
      
      const rightTreeData = buildRightTreeData(props.fromData, props.targetKeys);
      
      // 每次构建树数据时，同时更新展开状态
      if (rightTreeData && rightTreeData.length > 0) {
        const allExpandKeys = getAllKeys(rightTreeData);
        rightExpandedKeys.value = [...allExpandKeys]; // 强制更新展开状态
      }
      
      const treeData = filterTreeData(rightTreeData, searchValue.value.right);
      return processTreeData(treeData);
    });

    // 自定义节点类名
    const getNodeClassName = (node) => {
      return node.children && node.children.length > 0 ? 'folder-node' : 'file-node';
    };

    // 使用fieldNames来自定义节点属性映射
    const fieldNames = {
      children: 'children',
      title: 'title',
      key: 'key'
    };

    // 自定义样式
    const customStyle = {
      container: {
        display: 'flex',
        width: '100%'
      },
      transferList: {
        flex: 1
      },
      operation: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        margin: '0 8px'
      },
      button: {
        display: 'inline-block',
        margin: '4px 0',
        padding: '4px 12px',
        border: '1px solid #d9d9d9',
        borderRadius: '2px',
        backgroundColor: '#fff',
        cursor: 'pointer'
      }
    };

    return () => (
      <div class="tree-transfer-container" style={customStyle.container}>
        <div class="transfer-list left-tree-container" style={customStyle.transferList}>
          <div class="transfer-list-header">
            <div class="header-title">未绑定</div>
            {props.showSearch && (
              <div class="header-search">
                <input 
                  type="text" 
                  placeholder={props.searchPlaceholder} 
                  value={searchValue.value.left}
                  onInput={(e) => handleSearch('left', e.target.value)}
                />
              </div>
            )}
          </div>
          <div class="tree-wrapper">
            <Tree
              checkable
              showLine={false}
              blockNode
              fieldNames={fieldNames}
              selectable={false}
              clickRowToExpand={true}
              expandOnSelectorClick
              checkStrictly={false}
              showIcon={true}
              checkedKeys={leftCheckedKeys.value}
              expandedKeys={leftExpandedKeys.value}
              defaultExpandAll={props.defaultExpandAll}
              treeData={filteredLeftTreeData.value}
              onExpand={(keys) => leftExpandedKeys.value = keys}
              onCheck={(checked, e) => {
                handleLeftCheck(checked, e);
              }}
              treeNodeProps={(node) => ({
                class: getNodeClassName(node)
              })}
            />
          </div>
        </div>

        <div class="transfer-operation" style={customStyle.operation}>
          <button 
            class="operation-button right" 
            style={customStyle.button}
            disabled={selectedKeys.value.filter(key => !props.targetKeys.includes(key)).length === 0}
            onClick={() => {
              const keysToMove = selectedKeys.value.filter(key => !props.targetKeys.includes(key));
              if (keysToMove.length === 0) return;
              
              handleChange([...props.targetKeys, ...keysToMove], 'right', keysToMove);
              
              // 清空选中状态
              selectedKeys.value = [];
              leftCheckedKeys.value = [];
            }}
          >
            添加 &gt;
          </button>
          <button 
            class="operation-button left" 
            style={customStyle.button}
            disabled={selectedKeys.value.filter(key => props.targetKeys.includes(key)).length === 0}
            onClick={() => {
              const keysToMove = selectedKeys.value.filter(key => props.targetKeys.includes(key));
              if (keysToMove.length === 0) return;
              
              const newTargetKeys = props.targetKeys.filter(key => !keysToMove.includes(key));
              handleChange(newTargetKeys, 'left', keysToMove);
              
              // 清空选中状态
              selectedKeys.value = [];
              rightCheckedKeys.value = [];
            }}
          >
            &lt; 移除
          </button>
        </div>

        <div class="transfer-list right-tree-container" style={customStyle.transferList}>
          <div class="transfer-list-header">
            <div class="header-title">已绑定</div>
            {props.showSearch && (
              <div class="header-search">
                <input 
                  type="text" 
                  placeholder={props.searchPlaceholder} 
                  value={searchValue.value.right}
                  onInput={(e) => handleSearch('right', e.target.value)}
                />
              </div>
            )}
          </div>
          <div class="tree-wrapper">
            <Tree
              checkable
              showLine={false}
              blockNode
              fieldNames={fieldNames}
              selectable={false}
              clickRowToExpand={true}
              expandOnSelectorClick
              checkStrictly={false}
              showIcon={true}
              checkedKeys={rightCheckedKeys.value}
              expandedKeys={rightExpandedKeys.value}
              defaultExpandAll={props.defaultExpandAll}
              treeData={filteredRightTreeData.value}
              onExpand={(keys) => rightExpandedKeys.value = keys}
              onCheck={(checked, e) => {
                handleRightCheck(checked, e);
              }}
              treeNodeProps={(node) => ({
                class: getNodeClassName(node)
              })}
            />
          </div>
        </div>
      </div>
    );
  }
}); 