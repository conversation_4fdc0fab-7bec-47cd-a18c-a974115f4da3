<template>
  <div class="ReaderLayout-module_wrapper_l-8F+" style="padding-left: 276px;"><div id="headerOverlayContainer" class="ViewerHeader-module_headerOverlayContainer_30mty"></div><div class="BookReader-module_wrapper_s6Jdt BookReader-module_docTypographyClassic_tUB5r" data-testid="content"><div class="BookReader-module_content_BGKYX" id="main"><div class="BookReader-module_docContainer_mQ3Tk"><div class="DocReader-module_wrapper_t3Z8X" data-doc-layout="fixed" data-doc-sidebar="false"><div class=""><div id="doc-reader-content" class="DocReader-module_content_AcIMy "><div class="DocReader-module_header_xAOtU"><div><div class="DocReader-module_title_fXOQi"><h1 id="article-title" class="index-module_articleTitle_VJTLJ doc-article-title">普通规则</h1></div></div></div><div><article id="content" class="article-content" tabindex="0" style="outline-style: none;"><div class="ne-doc-major-viewer"><div class="yuque-doc-content" data-df="lake" style="position: relative;"><div><div class="ne-viewer lakex-yuque-theme-light ne-typography-classic ne-paragraph-spacing-relax ne-viewer-layout-mode-fixed" data-viewer-mode="normal" id="u138b"><div class="ne-viewer-header"><button type="button" class="ne-ui-exit-max-view-btn" style="background-image:url(https://gw.alipayobjects.com/zos/bmw-prod/09ca6e30-fd03-49ff-b2fb-15a2fbd8042a.svg)">返回文档</button></div><div class="ne-viewer-body"><ne-p id="u0c1dde21" data-lake-id="u0c1dde21"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="545d5033aab1f24fb3a6fc1538245daa" data-lake-id="545d5033aab1f24fb3a6fc1538245daa"><ne-text id="ub0c66a61"> 一个普通规则主体是由如果、那么、否则构成, 如果那么中间编写逻辑语句，语句间的关联是逻辑关系: 且、或、且非、或非以及联合条件。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ac948a16645ea59078b2efb64c25f004" data-lake-id="ac948a16645ea59078b2efb64c25f004"><ne-card data-card-name="image" data-card-type="inline" id="ue9d20d07" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1367" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1742882560256-04bff954-87f1-402b-a531-5ef243538f43.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>1</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="ubbf33f18" data-lake-id="ubbf33f18"><ne-text id="u58943369">点击需要添加规则的规则包，然后在右侧添加 新增</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="ua60279e9" data-lake-id="ua60279e9" ne-alignment="left"><ne-card data-card-name="image" data-card-type="inline" id="u2ca741e6" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="1483.2" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1743390420475-9022aa2e-9eb2-49c6-85ab-ed8036fd6f37.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="18f1c3a2bba6482d7b3996a51061f976" data-lake-id="18f1c3a2bba6482d7b3996a51061f976"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>2</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="ue86eed82" data-lake-id="ue86eed82"><ne-text id="ud9c3c3b9">点击新增 填写规则名称和选择规则类型，默认普通规则</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="u1cda6875" data-lake-id="u1cda6875"><ne-card data-card-name="image" data-card-type="inline" id="uab61757f" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="587.2" class="ne-image ne-image-preview" alt="image.png" draggable="true" src="/img/doc/1742882758484-1ed52467-ac18-4429-a426-54c36bbdf79a.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="8660430e453ee104ac5b01a40c11a17f" data-lake-id="8660430e453ee104ac5b01a40c11a17f"><ne-card data-card-name="image" data-card-type="inline" id="u67033f98" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-style-none ne-image-loaded" data-testid="ne-card-image"><div class="ne-image-box" style="background-color: rgb(254, 249, 249); width: 1020px; height: 0px; padding-bottom: 47.9412%;"><img width="1020" class="ne-image ne-image-preview" alt="" draggable="true"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-oli index-type="0"><ne-oli-i><span class="ne-list-symbol" data-type="0" data-level="0"><span>3</span></span></ne-oli-i><ne-oli-c class="ne-oli-content" id="uc41a6557" data-lake-id="uc41a6557"><ne-text id="ub224db8d">对新增的规则进行系统编写</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-oli-c></ne-oli><ne-p id="3d1c97d402fff39ecdd9523a68972320" data-lake-id="3d1c97d402fff39ecdd9523a68972320"><ne-card data-card-name="image" data-card-type="inline" id="IrgRM" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="940" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737571400-4f13c335-b2a7-48e3-874b-03301ed5f803.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="3e4bfc4db0bf661ec4e9c520787ae5e6" data-lake-id="3e4bfc4db0bf661ec4e9c520787ae5e6"><ne-text id="u08ebb285">‘</ne-text><ne-card data-card-name="image" data-card-type="inline" id="WBF8E" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="34" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737571630-7c0dac8a-b710-4afb-b387-3cdc8228bcc2.png"></div></div></div></ne-card><ne-text id="u60f6aa05">’点击默认往下添加语句，点击逻辑 （ 且、或、且非、或非）相互更换。</ne-text><ne-card data-card-name="image" data-card-type="inline" id="ve0jt" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="34" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737571837-3cb601a9-bbaa-4961-a7ee-e287fb1dfa2e.png"></div></div></div></ne-card><ne-text id="u5aed9330"> 三个点 ，是添加联合条件（多条语句为一组的复合条件），和其他辅助操作</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="be3332f556e36eb460f7ebd86de624cd" data-lake-id="be3332f556e36eb460f7ebd86de624cd"><ne-text id="u81adc157">“那么”里面编写动作列，对如果配置的条件满足后配置对应的操作功能，动作列可以设置系数和因子也可以添加函数方法个性化方法等。“否则”同理（否则可以不填写动作）。</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="41c2384b73c03823cb811247474ac41c" data-lake-id="41c2384b73c03823cb811247474ac41c"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="84b47d01c20fe5a333a909f5e2d0a4ef" data-lake-id="84b47d01c20fe5a333a909f5e2d0a4ef"><ne-text id="u4dd6fec2">☆预定义</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="59149f33374b4c4bb025f24cca40e9a9" data-lake-id="59149f33374b4c4bb025f24cca40e9a9"><ne-card data-card-name="image" data-card-type="inline" id="s9cWy" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="948" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737572036-fad94cb7-acf2-4bb4-8502-ec739e0dfb39.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="eedcdae0dfc537b9ed2cbc59ff3fe3fa" data-lake-id="eedcdae0dfc537b9ed2cbc59ff3fe3fa"><ne-text id="ufe1c7cca">预定义可以用于规则的前置条件也可以用于变量定义使用</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="855ab22c593daa28d3ab0430c823cfe0" data-lake-id="855ab22c593daa28d3ab0430c823cfe0"><ne-text id="u0438a829">前置条件如 ：商业险执行该规则内容</ne-text><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="b04926b4ce644e7389efafd9dc1b7ac8" data-lake-id="b04926b4ce644e7389efafd9dc1b7ac8"><ne-card data-card-name="image" data-card-type="inline" id="j42m7" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="930" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737572261-e68ce0c7-f888-47ee-9244-fa2c39e14fd4.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="f2a074784bebc1b1fcea73f117308b13" data-lake-id="f2a074784bebc1b1fcea73f117308b13"><ne-text id="u3b0d17db">变量定义：可以遍历险别信息，获取险别记录中的某险别：险别代码、保额、保费、免赔额等 </ne-text><ne-card data-card-name="image" data-card-type="inline" id="AGoU5" data-event-boundary="card"><div class="ne-card-container"><div class="ne-image-wrap ne-image-loaded ne-image-style-none" data-testid="ne-card-image"><div class="ne-image-box" style=""><img width="914" class="ne-image ne-image-preview" alt="" draggable="true" src="/img/doc/1740737572454-b2ca80f5-3805-40a8-b57a-db93efab8f8a.png"></div></div></div></ne-card><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="302d3e5372dac45b782269b1c856f991" data-lake-id="302d3e5372dac45b782269b1c856f991"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="ff9637ec679792b674d4e78857ef8086" data-lake-id="ff9637ec679792b674d4e78857ef8086"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="e791f87d0dd60e362a59549555ebdf83" data-lake-id="e791f87d0dd60e362a59549555ebdf83"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="904ae8c3452fbf0e33a2f65fd4eb503e" data-lake-id="904ae8c3452fbf0e33a2f65fd4eb503e"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="473191283e8b1bf646b2b9c9a2d9ab47" data-lake-id="473191283e8b1bf646b2b9c9a2d9ab47"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-p id="8748b1d7ca90aad355022e3d04ca50dd" data-lake-id="8748b1d7ca90aad355022e3d04ca50dd"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p><ne-h2 id="hc8FC" data-lake-id="hc8FC"><ne-heading-ext><ne-heading-anchor><span><div class="ne-icon ne-icon-card-h2" data-name="card-h2"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-h2"></use></svg></div></span></ne-heading-anchor><ne-heading-fold><div class="ne-heading-folding-inner"><div class="ne-icon ne-lark-icon" data-name="ReviewArrowDown"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="larkui-icon larkui-icon-review-arrow-down"><defs><path id="3742204692a" d="M0 0h256v256H0z"></path></defs><g fill="none" fill-rule="evenodd"><mask id="3742204692b" fill="#fff"><use xlink:href="#3742204692a"></use></mask><path d="M181.1 118.925 135 165.096a9.889 9.889 0 0 1-14 0l-46.1-46.17c-3.866-3.873-3.866-10.15 0-14.022a9.892 9.892 0 0 1 7-2.904h92.2c5.468 0 9.9 4.439 9.9 9.915a9.922 9.922 0 0 1-2.9 7.01Z" fill="currentColor" fill-rule="nonzero" mask="url(#3742204692b)"></path></g></svg></div></div></ne-heading-fold></ne-heading-ext><ne-heading-content><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-heading-content></ne-h2><ne-p id="ceda95415f88f1fd89e4ab2df5715781" data-lake-id="ceda95415f88f1fd89e4ab2df5715781"><span class="ne-viewer-b-filler" ne-filler="block"><br></span></ne-p></div><div class="ne-drawer-box" style="overflow: hidden;"></div><div class="ne-inner-overlay-container"><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay"><div class="count-tips-module_tipsContainer___5agc"></div></ne-overlay-tmp><ne-overlay-tmp contenteditable="false" data-event-boundary="overlay" style="z-index: 2;"><div class="paragraph-tips-module_tipsContainer__3KAJw paragraph-tips-module_hide__3y04q" data-paragraph-comment-container="true" style="position: absolute; left: 867px; top: 511px; padding-left: 6px; height: 27.3px;"><div class="paragraph-tips-module_tipsWrapper__3Uzpe"><div class="ne-icon ne-icon-card-tb-comment" style="font-size: 16px;" data-name="card-tb-comment"><svg class="ne-icon-symbol" aria-hidden="true"><use xlink:href="#ne-card-tb-comment"></use></svg></div></div></div></ne-overlay-tmp></div></div><div style="height: 0px; overflow: hidden;">​</div></div></div></div></article></div></div><div></div><div></div></div><div></div></div></div></div></div></div>
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>