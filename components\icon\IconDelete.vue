<!-- 删除图标 -->

<script setup lang="ts">
interface Props {
    size?: number
}

const props = withDefaults(defineProps<Props>(), {
    size: 16
})
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-icon-delete icon-svg TemplateTreeItem-module_actionIcon_haD5C index-module_size_wVASz"
        :style="{ width: `${size}px`, minWidth: `${size}px`, height: `${size}px` }">
        <path
            d="M218 68c5.523 0 10 4.477 10 10s-4.477 10-10 10h-16.56l-6.16 107.936c-.97 17.07-14.466 30.764-31.52 31.984l-2.432.08H94.672c-18.028.004-32.925-14.065-33.952-32.064L54.56 88H38c-5.523 0-10-4.477-10-10s4.477-10 10-10h180Zm-36.592 20H74.576l6.112 106.8a14 14 0 0 0 12.288 13.104l1.696.096h66.656c7.424.004 13.56-5.788 13.984-13.2L181.408 88ZM108 112c5.52 0 10 4.48 10 10v44c0 5.523-4.477 10-10 10s-10-4.477-10-10v-44c0-5.52 4.48-10 10-10Zm40 0c5.52 0 10 4.48 10 10v44c0 5.523-4.477 10-10 10s-10-4.477-10-10v-44c0-5.52 4.48-10 10-10Zm26-84c5.523 0 10 4.477 10 10s-4.477 10-10 10H82c-5.523 0-10-4.477-10-10s4.477-10 10-10h92Z"
            fill="currentColor" fill-rule="nonzero"></path>
    </svg>
</template>