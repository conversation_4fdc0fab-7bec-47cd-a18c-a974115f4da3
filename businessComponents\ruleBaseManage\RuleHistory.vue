<!-- 规则历史组件 -->

<template>
    <div class="rule_audit">
        <a-tabs type="card" v-model:activeKey="activeName" @change="tabClick">
            <a-tab-pane
                    tab="规则历史信息"
                    key="ruleCompare"
                    style="marbin-bottom: 30px"
                    :forceRender="true"
            >
                <a-table :columns="columns" :data-source="tableData" :pagination="pagination" row-key="uuid" :loading="pagination.isLoading"
                         @change="handlePageChange" :rowSelection="rowSelection" size="small">
                    <template v-slot:bodyCell="{column,record,index}">
                        <template v-if="column.dataIndex == 'action'">
                            <a-button type="link" size="small" @click="handleDetail(record)" v-hasPermi="[RULE_PERMISSION.RULE_HISTORY.DETAIL]">详情</a-button>
                            <a-button type="link" size="small" @click="handleBack(record)" v-hasPermi="[RULE_PERMISSION.RULE_HISTORY.ROLLBACK]" >回滚</a-button>
                            <a-button type="link" size="small" @click="handleShoot(record)"  v-hasPermi="[RULE_PERMISSION.RULE_HISTORY.BASELINE]">基线</a-button>
                        </template>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane
                    tab="审核历史信息"
                    key="hisExamine"
                    style="marbin-bottom: 30px"
                    :forceRender="true"
            >
                <a-table :columns="columnsT" :data-source="checkTableData" :pagination="paginationT" row-key="uuid" :loading="paginationT.isLoading"
                         @change="handlePageChangeT" size="small">
                    <template v-slot:bodyCell="{column,record,index}">
                        <template v-if="column.dataIndex == 'action'">
                            <a-button type="link" size="small" @click="handleCheckDetail(record)">详情</a-button>
                        </template>
                    </template>
                </a-table>
            </a-tab-pane>
            <!--<table
                    class="datatable"
                    id="datatable_tc"
                    :style="{
                            transform: 'scale(' + tableScale + ')',
                            transformOrigin: 'left top',
                          }"
            ></table>-->
            <a-tab-pane
                    tab="规则版本对比"
                    key="ruleEditionCompare"
                    style="marbin-bottom: 30px"
                    :forceRender="true"
            >
                <RuleCompareContent :rule-compare-data="ruleCompareData" />
            </a-tab-pane>
        </a-tabs>
    </div>
    <!-- 详情对话框 -->
    <FullModel
            :isModalVisible="isModalVisible"
            :isFullscreen="isFullscreen"
            :onFullscreenToggle="onFullscreenToggle"
            :titleText="titleText"
            :handleCancel="handleCancel"
            :handleOk="() => {}"
    >
        <template #default>
            <div :style="{maxHeight: isFullscreen?'90vh':'60vh', overflowY: 'auto'}">
                <RuleBaseHistoryInfo v-if="modalType === 'detail'" :dataId="dataId" />
                <RuleBaseCheckHistoryInfo v-if="modalType === 'checkDetail'" :dataId="dataId" />
                <RuleBaseHistorySnap v-if="modalType === 'shoot'" :dataId="dataId" />
            </div>
        </template>
    </FullModel>
</template>

<script setup lang="ts">
import { getRuleHistoryList, ruleCheckList, rollBack } from "@/api/rule_base";
import qs from 'qs'
import RuleBaseHistoryInfo from "@/businessComponents/ruleBaseManage/RuleBaseHistoryInfo.vue";
import RuleBaseCheckHistoryInfo from "@/businessComponents/ruleBaseManage/RuleBaseCheckHistoryInfo.vue";
import RuleBaseHistorySnap from "@/businessComponents/ruleBaseManage/RuleBaseHistorySnap.vue";
import { compareRule } from "@/api/task";
import type { TableProps } from 'ant-design-vue';
import type { Message } from '@/types/global';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import globalEventEmitter from '~/utils/eventBus';
import { REFRESH_RULE_LIST } from '@/consts/globalEventConsts';
const message = inject<Message>('message')
const isModalVisible = ref(false); // 对话框显示状态

const titleText = ref('');
const props = defineProps({
    datar: {
        type: Object,
    },
    ruleId: {
        type: String,
        default: () => '',
    },
})
const handleCancel = () => {
    isModalVisible.value = false;
};
const modalType = ref(''); // 对话框类型
const activeName = ref('ruleCompare');
//规则历史信息
const columns = [
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        width: 50,
        align: 'center',
        ellipsis: true,
        customRender: ({ index }: { index: number }) => `${(paginationT.value.current - 1) * paginationT.value.pageSize + index + 1}`
    },
    {
        title: '名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '版本',
        dataIndex: 'edition',
        key: 'edition',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '修改时间',
        dataIndex: 'lastModifiedTimeStr',
        key: 'lastModifiedTimeStr',
        align: 'center',
        width:180,
        ellipsis: true,
    },
    {
        title: '操作',
        key: 'action',
        align: 'center',
        ellipsis: true,
        dataIndex: 'action',
    },
];
const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger:true,
    isLoading :false,
    showTotal: (total: number) => `共 ${total} 条数据`,
})
interface TablePaginationConfig {
    current: number;
    pageSize: number;
    showSizeChanger: boolean;
    total: number;
}
const handlePageChange = (page: TablePaginationConfig) => {
    pagination.value.current = page.current;
    pagination.value.pageSize = page.pageSize;
    pagination.value.showSizeChanger = page.showSizeChanger;
    pagination.value.total = page.total;
    getRuleHistoryList1();
};
const getRuleHistoryList1 = () => {
    getRuleHistoryList({
        ruleUuid: props.ruleId,
        page: pagination.value.current,
        several: pagination.value.pageSize,
    }).then((res) => {
        tableData.value = res.data.data;
        pagination.value.total = res.data.totalCount;
    });
}

const handleDetail = (record) => {
    showModal('detail',record)
}
const handleBack = (record) => {
    rollBack(
        qs.stringify({
            uuid: record.uuid,
        })
    ).then((res) => {
        if (res.code === 20000) {
            message.success(res.data);
            //更新上级列表状态
            globalEventEmitter.emit(REFRESH_RULE_LIST);
        } else {
            message.error(res.data);
        }
    });
}
const handleShoot = (record) => {
    showModal('shoot',record)
}
//审核历史信息
const tableData = ref<RuleHistory[]>([]);
const columnsT = [
    {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        width: 50,
        align: 'center',
        ellipsis: true,
        customRender: ({ index }) => `${(paginationT.value.current - 1) * paginationT.value.pageSize + index + 1}`
    },
    {
        title: '名称',
        dataIndex: 'ruleName',
        key: 'ruleName',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '审核人',
        dataIndex: 'checkId',
        key: 'checkId',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '审核状态',
        dataIndex: 'checkStatus',
        key: 'checkStatus',
        align: 'center',
        ellipsis: true,
    },
    {
        title: '审核时间',
        dataIndex: 'checkTimeStr',
        key: 'checkTimeStr',
        align: 'center',
        width:180,
        ellipsis: true,
    },
    {
        title: '操作',
        key: 'action',
        dataIndex: 'action',
        align: 'center',
        ellipsis: true,
    },
];
interface RuleHistory {
    ruleName: string;
    checkId: string;
    checkStatus: string;
    checkTimeStr: string;
    uuid?: string;
    edition?: string;
    type?: string;
    lastModifiedTimeStr?: string;
}

const form = reactive({
    ruleName: [],
});

const ruleForm = ref(null);
const formData = ref([]);
// todo 下面这两个ref感觉不需要，待进一步确认
const BaseId = ref('');
const demandUuid = ref('');

const emit = defineEmits(['removeTab', 'addTab']);

const handleSubmit = (callback) => {
    callback();
};
const paginationT = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger:true,
    isLoading :false,
    showTotal: (total) => `共 ${total} 条数据`,
})

const checkTableData = ref<RuleHistory[]>([]);


const handlePageChangeT = (page: number) => {
    paginationT.value.current = page;
    paginationT.value.pageSize = 10;
    paginationT.value.showSizeChanger = true;
    paginationT.value.total = 0;
    ruleCheckList1();
};

const ruleCheckList1 = () => {
    paginationT.value.isLoading = true;
    ruleCheckList({
        ruleUuid: props.ruleId,
        page: paginationT.value.current,
        several: paginationT.value.pageSize,
    }).then((res) => {
        paginationT.value.isLoading = false;
        checkTableData.value = res.data.data;
        paginationT.value.total = res.data.totalCount;
    });
}
const dataId = ref('');
const handleCheckDetail = (record) => {
    showModal('checkDetail',record)
}
const showModal = (type, record = {}) => {
    modalType.value = type;
    dataId.value = record.uuid;
    if(type === 'detail' || type === 'shoot'){
        titleText.value =  `${record.ruleName}（${record.edition}）`
    }else{
        titleText.value = `${record.ruleName}`;
    }

    isModalVisible.value = true;
}
onMounted(async () => {
    tableData.value = props.datar.data;
    pagination.value.total = props.datar.totalCount;
    ruleCheckList1();
});

const checkedData = ref<RuleHistory[]>([]);
const rowSelection = {
    onChange: (selectedRowKeys: string[], selectedRows: RuleHistory[]) => {
        checkedData.value = [];
        selectedRows.forEach((item: RuleHistory) => {
            checkedData.value.push(item)
        });
    }
};
// 检查选中记录条数
const checkSelectedRow = (single) => {
    if (checkedData.value.length) {
        if (single) {
            if (checkedData.value.length > 1) {
                message.error("只能选择一条记录");
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    } else {
        message.error("请选择要操作的记录");
        return false;
    }
}
//规则类参数
interface RuleHistoryVO {
    edition?: string;
    textDtl?: string;
    type?: string;
    uuid?: string;
    tableContent?: string;
    tableContentHis?: string;
    validStatus?: string;
    ruleUuid?: string;
}

interface RuleCompareDataType {
    ruleHisVO1: RuleHistoryVO;
    ruleHisVO2: RuleHistoryVO;
    uuid1?: string;
    uuid2?: string;
}

//两个规则类对比整合
const ruleCompareData = ref<RuleCompareDataType>({
    ruleHisVO1: {
        edition: "",
        textDtl: "",
        type: "",
        uuid: "",
        tableContent: "",
        tableContentHis: "",
        validStatus: "",
    },
    ruleHisVO2: {
        edition: "",
        textDtl: "",
        type: "",
        uuid: "",
        tableContent: "",
        tableContentHis: "",
        validStatus: "",
    },
});

const tabClick = (key: string) => {
    ruleCompareData.value = {
        ruleHisVO1: {
            edition: "",
            textDtl: "",
            type: "",
            uuid: "",
            tableContent: "",
            tableContentHis: "",
            validStatus: "",
        },
        ruleHisVO2: {
            edition: "",
            textDtl: "",
            type: "",
            uuid: "",
            tableContent: "",
            tableContentHis: "",
            validStatus: "",
        },
    };

    if(key === "ruleEditionCompare") {
        if (checkSelectedRow(false)) {
            if (checkedData.value.length === 2) {
                setTimeout(() => {
                    // 根据版本对比
                    let uuid1 = "";
                    let uuid2 = "";
                    if (
                        Number(checkedData.value[0].edition) >
                        Number(checkedData.value[1].edition)
                    ) {
                        uuid1 = checkedData.value[0].uuid;
                        uuid2 = checkedData.value[1].uuid;
                    } else {
                        uuid1 = checkedData.value[1].uuid;
                        uuid2 = checkedData.value[0].uuid;
                    }
                    compareRule({
                        uuid1: uuid1,
                        uuid2: uuid2,
                    }).then((res) => {
                        if (res.code == 20000) {
                            ruleCompareData.value = res.data;
                            ruleCompareData.value.uuid1 = uuid1;
                            ruleCompareData.value.uuid2 = uuid2;
                        }
                    });
                });
            } else {
                message.error("请选择两条记录");
                activeName.value = "ruleCompare"
            }
        }else{
            activeName.value = "ruleCompare"
        }
    }
}
//全屏切换
const isFullscreen = ref(false)
const onFullscreenToggle = () => {
    isFullscreen.value = !isFullscreen.value
}
defineExpose({
    handleSubmit
});
</script>

<style lang="scss" scoped>
.rule_audit {
    :deep(.datatable tr:first-child td),
    :deep(.datatable2 tr:first-child td) {
        text-align: left !important;
    }
    :deep(.datatable.rule tr:first-child td),
    :deep(.datatable2.rule tr:first-child td) {
        text-align: left !important;
        white-space: normal !important;
        word-wrap: break-word;
        word-break: break-all;
    }
    :deep(.fakeContainer) {
        margin: 20px;
        padding: 0px;
        border: none;
        width: 95%;
        // overflow: hidden;
        overflow:scroll;
    }
    :deep(.datatable th){
        word-wrap: break-word; word-break: normal;
        border:1px solid #ccc;	/* 行名称边框 */
        background:#f2f2f2;/*url(../img/201407061920.gif) repeat-x;*/	/* 行名称背景色 */
        color:#333;				/* 行名称颜色 */
        font-weight:bold;
        height:30px;line-height:24px;
        padding-left:5px; padding-right:5px;
        text-align:center;
        white-space: nowrap;
    }
    :deep(.datatable td){
        border:1px solid #ccc;	/* 单元格边框 */
        text-align:left;
        padding-top:4px; padding-bottom:4px;
        padding-left:10px; padding-right:10px;
        white-space: nowrap;
    }
    :deep(.datatable tr:hover, .datatable tr.altrow){
        background-color:#f2f2f2;	/* 动态变色 */
    }
    & {
        table {
            width: 100%;
        }
        th,
        td {
            max-width: 90px !important;
            text-overflow: ellipsis;
            overflow-x: hidden;
        }
        #tip-div {
            position: fixed;
            top: 0;
            left: 0;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #000;
            padding: 10px;
            z-index: 2000;
            font-size: 14px;
            line-height: 1.2;
            min-width: 10px;
            word-wrap: break-word;
            overflow: auto;
            max-height: 300px;
            max-width: 600px;
        }

        // table {
        //   border-collapse: collapse;
        // }
        .fixed-table {
            overflow: auto;
            height: 100%; /* 设置固定高度 */
            width: 100%;
            // position: absolute
        }

        .fixed-table td,
        .fixed-table th {
            /* 设置td,th宽度高度 */
            border: 1px solid #c7d8ee;
            width: 150px;
            min-width: 150px;
            // height: 30px;
            // padding: 5px;
        }

        .fixed-table th {
            position: sticky;
            top: 0; /* 首行永远固定在头部  */
        }
        .fixed-table th.th2 {
            top: 30px;
        }
        .fixed-table th.th3 {
            top: 60px;
        }
        .fixed-table td:first-child,
        .fixed-table th.xh {
            position: sticky;
            left: 0; /* 首列永远固定在左侧 */
            z-index: 1;
            background-color: #fff;
        }

        .fixed-table th.xh {
            z-index: 2; /*表头的首列要在上面*/
            background-color: #f2f2f2;
        }

        .fixed-table th > div {
            width: 100%;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
        }
        .slider-div-left {
            display: flex;
            align-items: center;
            .a-alert {
                margin-right: 20px;
            }
        }
        .slider-alert-div {
            display: flex;
            justify-content: space-between;
            .a-alert {
                width: auto;
            }
        }
        .slider-div {
            width: 170px;
            display: flex;
            float: right;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            .slider-block {
                width: 140px;
                .a-slider__runway.show-input {
                    width: 50px !important;
                    top: -2px;
                }
            }
        }
        .fakeContainer {
            margin: 0 !important;
            margin-top: 20px;
            width: 100% !important;
            overflow: auto;
        }
        .a-form-item__label,
        .a-form-item__content,
        .a-input__inner {
            line-height: 30px;
        }
        .a-input__inner {
            height: 30px;
        }
        .a-input--mini {
            height: 16px !important;
        }
        .a-input--mini .a-input__inner {
            height: 16px !important;
            line-height: 16px !important;
            padding: 0;
        }
        .a-slider__input {
            width: 70px;
            font-size: 12px;
        }
        .a-input-number--mini .a-input-number__decrease,
        .a-input-number--mini .a-input-number__increase {
            width: 15px;
            height: 15px;
            line-height: 15px;
            top: 4px;
        }
        .fullrow {
            background: #fff;
        }
        .biaoColor {
            color: red;
        }
        .a-form {
            margin-bottom: 30px;
        }
        .a-form-item {
            margin-bottom: 0px;
        }
        .a-row {
            margin-top: 0px;
        }
    }
}

</style>
