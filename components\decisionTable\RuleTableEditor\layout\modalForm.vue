<template>
  <div>
    <!-- <div
      :visible="!visible"
      @cancel="handleCancel"
      width="700px"
    > -->
    <a-modal
      v-model:open="visible"
      title="规则详情"
      :footer="null"
      @cancel="handleCancel"
      :destroy-on-close="true"
      width="80%"
    >
      <div v-if="!isEditName">
        <div class="rule-name">
          <span class="name-lable">列名：</span>
          <a-input autocomplete="off" style="name-input" v-model:value="aliasName" />
        </div>
        <div class="rule-name">
          <span class="name-lable">详情：</span>
          <span v-if="!isAction">
            <a-switch
              v-model:checked="complexModel"
              active-color="#13ce66"
              inactive-color="#ddd"
              @change="compChange"
            >
            </a-switch>
            <span style="margin-left: 10px; position: relative; top: 2px"
              >复杂编辑模式</span
            >
          </span>
        </div>
        <div
          class="eRuleEditorContainer oneDark_Light"
          :style="
            complexModel
              ? 'height: 260px !important'
              : 'height: 113px !important'
          "
        >
          <div class="eRuleEditor">
            <div class="editorBody" style="padding-top: 5px">
              <ruleCondition
                v-if="!isAction && !complexModel && visible"
                :conditions="rule"
                @onchange="onchange"
                :isTable="true"
              />
              <RuleConditonComplex
                v-if="!isAction && complexModel && visible"
                :conditions="rule"
                @onchange="onchange"
                @setComplexSelect="setComplexSelect"
                @setAllCellUnit="setAllCellUnit"
                :isTable="true"
                :noRuleCellUnit="true"
                class="complex-editor"
              />
              <RuleAction
                v-if="isAction"
                :actions="rule"
                @onchange="onchange"
                :isTable="true"
              />
            </div>
          </div>
        </div>
        <div style="text-align: right; margin-top: 10px">
          <a-button @click="handleCancel" style="margin-right: 15px;">取消</a-button>
          <a-button @click="handleOk" type="primary">确认</a-button>
        </div>
      </div>
      <div v-else>
        <div class="rule-name" v-for="item in nameList" :key="item.key">
          <span class="name-lable">{{ item.name }}</span>
          <a-input autocomplete="off" style="name-input" v-model:value="item.value" />
        </div>
        <div style="text-align: right; margin-top: 10px">
          <a-button @click="handleNameOk" type="primary">确认</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import RuleCondition from "../editor/ruleConditon.vue";
import RuleConditonComplex from "../editor/ruleConditonComplex.vue";
import RuleAction from "../editor/ruleAction.vue";
import { cloneDeep } from "lodash";
import * as util from "@/components/ruleEditCom/utils/util";

const { ConditionGenerate } = util;
export default {
  name: "ModalForm",
  components: {
    RuleCondition,
    RuleConditonComplex,
    RuleAction,
  },
  inject: {
    message: { default: () => {} },
  },
  props: {
    colEditingKey: String,
    properties: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    colEditingKey: {
      handler: function (val, oldVal) {
        const { cellUnit, allCellUnit } = this.properties.rule;
        this.complexModel = cellUnit;
        this.backRule = cloneDeep(this.properties.rule);

        if (cellUnit) {
          this.properties.rule.complexSelect = true;
          // this.backRule?.complexSelect = true;
        } else {
          this.properties.rule.complexSelect = false;
          // this.backRule?.complexSelect = false;
        }
        if (allCellUnit !== undefined) {
          this.properties.rule.allCellUnit = allCellUnit;
        }
        this.visible = val !== "";
        this.aliasName = this.properties.aliasName;
        this.rule = this.properties.rule;
        this.isAction = this.colEditingKey && this.colEditingKey[4] === "2";
        this.toBeRule = this.properties.rule;
        this.isEditName = false;
        this.nameList = [];
        this.res = null;
      },
    },
  },
  mounted() {
    const { cellUnit, allCellUnit } = this.properties.rule;
    this.complexModel = cellUnit;
    if (cellUnit) {
      this.properties.rule.complexSelect = true;
      // this.backRule?.complexSelect = true;
    } else {
      this.properties.rule.complexSelect = false;
      // this.backRule?.complexSelect = false;
    }
    if (allCellUnit !== undefined) {
      this.properties.rule.allCellUnit = allCellUnit;
    }
  },
  data() {
    return {
      isTrue: true,
      visible: false,
      aliasName: this.properties?.aliasName,
      rule: this.properties?.rule,
      toBeRule: {},
      isAction: this.colEditingKey && this.colEditingKey[4] === "2",
      isEditName: false,
      nameList: [],
      res: null,
      express: false,
      complexModel: false,
      backRule: null,
      listNameList: [],
      okBtn: false
    };
  },
  methods: {
    setAllCellUnit(val) {
      this.properties.rule.allCellUnit = val;
    },
    setComplexSelect(val) {
      this.complexModel = val;
      if (val) {
        this.properties.rule.cellUnit = true;
        this.properties.rule.complexSelect = true;
        // this.backRule?.complexSelect = true;
      } else {
        this.properties.rule.cellUnit = false;
        this.properties.rule.complexSelect = false;
        // this.backRule?.complexSelect = false;
      }
      this.$emit("setComplexSelect", val, this.colEditingKey);
    },
    compChange(val) {
      const initConditionData = {
        variable: {
          valueType: "String",
          variableType: "dataEntry",
        },
        leftValueType: "String",
      };
      if (!val) {
        this.backRule.complexSelect = false;
        this.backRule.cellUnit = false;
        this.backRule.children = [
          {
            indent: 1,
            ruleCondition: {
              layer: 1,
              showLayer: 1,
              conditionId: 1,
              contents: new ConditionGenerate(initConditionData),
            },
          },
        ];
        this.rule = cloneDeep(this.backRule);
        this.toBeRule = cloneDeep(this.backRule);
      } else {
        this.rule = cloneDeep(this.properties.rule);
        this.toBeRule = cloneDeep(this.properties.rule);
      }
      this.setComplexSelect(val);
    },
    handleNameOk() {
      const copyList = cloneDeep(this.nameList);
      const copyListNameList = cloneDeep(this.listNameList);
      const copyval = cloneDeep(this.res);
      this.isEditName = false;
      this.nameList = [];
      this.listNameList = [];
      this.res = null;
      if (copyListNameList.length > 0) {
        // condition list
        this.$emit("ok", copyval, copyListNameList, true);
      } else {
        this.$emit("ok", copyval, copyList);
      }
    },
    setNameList(
      data,
      allTableParams,
      nameList,
      conditionsIndex,
      operator_tableParams,
      i,
      val,
      oChildren,
      parseI,
      isChildren,
      objParams
    ) {
      const { ruleCondition, children } = data;
      if (children) {
        for (let ci = 0; ci < children.length; ci++) {
          if (i === 0) {
            parseI = ci
          } else {
            parseI++
          }
          this.setNameList(
            children[ci],
            allTableParams,
            nameList,
            conditionsIndex,
            operator_tableParams,
            i,
            val,
            oChildren,
            parseI,
            isChildren,
            objParams
          );
        }
      }else{
        if(i>0&&!isChildren){
          parseI++
        }
        if (ruleCondition) {
          const { contents } = ruleCondition;
          if (contents) {
            const { variable, comparator } = contents;
            let _methodParams = this.getContentMethodParams(variable, []);
            let _operatorParams = this.getContentOperatorParams(comparator);
            if (_operatorParams && _operatorParams.length > 0) {
              _operatorParams.map((item, ii) => {
                const { next } = item;
                if (next) {
                  let operator_methodParams = this.getContentMethodParams(
                    next,
                    []
                  );
                  if (operator_methodParams) {
                    operator_tableParams = this.setTableParsms(
                      operator_methodParams
                    );
                    const length = operator_tableParams.filter(
                      (item) => item === false
                    ).length;
                    if (length > 0) {
                      allTableParams.push(false);
                    } else {
                      allTableParams.push(true);
                    }
                  }
                } else {
                  if (item.value && item.value.toString().length > 0) {
                    allTableParams.push(true);
                  } else {
                    allTableParams.push(false);
                  }
                }
              });
            } else {
              // if(comparator.value && comparator.value.toString().length > 0){
              //   if(isArray(comparator.value)){
              //     if(!comparator.value[0]==="isNotNull"&&!comparator.value[0]==="isNull"){
              //       allTableParams.push(true);
              //     }else{
              //       allTableParams.push(false);
              //     }
              //   }
              // }
              allTableParams.push(true);
            }

            let tableParams = this.setTableParsms(_methodParams);
            // operator_tableParams.length > 0 &&
            //   (tableParams = tableParams.concat(operator_tableParams));
            tableParams.forEach((item,index)=>{
              objParams.push(index)
            })
            const length = tableParams.filter((item) => {
              if(item.value===false&&item.colTitle==='值'){
                // allTableParams.push(false);
                return item.value === false
              }else{
                // allTableParams.push(false);
                return item === false
              }

            }).length;
            let stepi=0;
            if(objParams.length>tableParams.length){
              stepi=objParams.length-tableParams.length
            }
            if (length > 0) {
              allTableParams.push(false);
              tableParams.forEach((item, index) => {
                conditionsIndex++;

                if(item&&item.value===false&&item.colTitle==='值'){
                  const obj = {
                    key: "值" + Number(stepi+1+index),
                    name: "值" + Number(stepi+1+index),
                    value: "值" + Number(stepi+1+index),
                    keyI: index + stepi,
                    colKey: this.colEditingKey,
                  };
                  // const obj = {
                  //   key: "值" + Number(parseI+1+index),
                  //   name: "值" + Number(parseI+1+index),
                  //   value: "值" + Number(parseI+1+index),
                  //   keyI: index + i,
                  //   colKey: this.colEditingKey,
                  // };
                  nameList.push(obj);
                }else{
                  if (!item) {
                    const obj = {
                      key: "参数" + Number(stepi+1+index),
                      name: "参数" + Number(stepi+1+index),
                      value: "参数" + Number(stepi+1+index),
                      keyI: index + stepi,
                      colKey: this.colEditingKey,
                    };
                    nameList.push(obj);
                  }
                }
              });

              this.nameList = nameList;
              this.listNameList = nameList;
              this.isEditName = true;
              this.res = cloneDeep(val);
              if (i === oChildren.length - 1) {
                return {
                  allTableParams,
                  nameList,
                  conditionsIndex,
                  operator_tableParams,
                  parseI,
                  returnFor:true
                };
              }
            } else {
              allTableParams.push(true);
            }
          }
        }
      }
      return {
        allTableParams,
        nameList,
        conditionsIndex,
        operator_tableParams,
        parseI
      };
    },
    handleOk() {
      this.okBtn=true
      const val = {
        aliasName: this.aliasName,
        conditionData: this.isAction ? {} : { ...this.toBeRule },
        actionData: this.isAction ? this.toBeRule : {},
        complexModel: this.complexModel,
      };
      if (!val.aliasName) {
        message.error("列名不能为空");
        return;
      }
      if (this.isAction) {
        if (this.toBeRule[0].actionType === "invokeMethod") {
          const actionParams = this.toBeRule[0].actionParams[0];
          const methodParams = this.getLastMethodParams(actionParams);
          if (!methodParams || methodParams.length === 0) {
            this.$emit("ok", val, "void");
            return;
          } else {
            let objVal = {};
            let tableParams = this.getTableParams(methodParams, [], objVal);
            if (Object.keys(objVal).length > 0) {
              for (let i = 0; i < tableParams.length; i++) {
                if (objVal[i] && objVal[i].length > 0) {
                  tableParams[i] = objVal[i];
                }
              }
            }
            function flattenArray(arr) {
              let result = [];
              for (let i = 0; i < arr.length; i++) {
                if (Array.isArray(arr[i])) {
                  result = result.concat(flattenArray(arr[i]));
                } else {
                  result.push(arr[i]);
                }
              }
              return result;
            }
            let resArr = flattenArray(tableParams);
            tableParams = resArr;
            if (!tableParams.includes(false)) {
              this.nameList = "void";

              if (val?.actionData?.[0]) {
                // titleComplete是后端需要的一个标识，其值为true的时候表示方法参数已在表头中写死
                val.actionData[0].titleComplete = true;
              }
            } else {
              const length = tableParams.filter(
                (item) => item === false
              ).length;
              if (length > 0) {
                const nameList = [];
                tableParams.forEach((item, index) => {
                  if (!item) {
                    const obj = {
                      key: "参数" + (index + 1),
                      name: "参数" + (index + 1),
                      value: "参数" + (index + 1),
                      keyI: index,
                    };
                    nameList.push(obj);
                  }
                });

                this.nameList = nameList;
                this.isEditName = true;
                this.res = cloneDeep(val);
                return;
              }
            }
          }
        }
        // 动作列是设置值类型的
        else if (this.toBeRule[0].actionType === "setValue") {
          let value = val.actionData[0]?.actionParams[1]?.value;

          const next = val.actionData[0]?.actionParams[1]?.next?.next ?? val.actionData[0]?.actionParams[1]?.next
          const valueInNext = next?.methodParams?.[0]?.singleParams?.[0]?.value ?? next?.methodParams?.[0]?.value

          // 如果有next属性中的话，就用next属性中的值
          value = valueInNext ?? value

          // 如果值在表头中写死了的话
          if (value !== undefined && value !== null && value !== "") {
            // 通过设置nameList使表格单元格中显示"-"，表示该列不可编辑
            this.nameList = "void";

            if (val?.actionData?.[0]) {
              // titleComplete是后端需要的一个标识，其值为true的时候表示方法参数已在表头中写死
              val.actionData[0].titleComplete = true;
            }
          }
        }
      } else {
        console.log(this.toBeRule,'----toBeRule')
        const { children } = this.toBeRule;
        if (children && children.length > 0) {
          // 多行规则的tableParams
          let allTableParams = [];
          let nameList = [];
          let conditionsIndex = 0;
          let operator_tableParams = [];
          let parseI = 0;
          let returnFor=undefined;
          let objParams=[]
          for (let i = 0; i < children.length; i++) {
            const _children=children[i].children
            let isChildren=false
            if(_children){
              isChildren=true
            }
            const valObj = this.setNameList(
              children[i],
              allTableParams,
              nameList,
              conditionsIndex,
              operator_tableParams,
              i,
              val,
              children,
              parseI,
              isChildren,
              objParams
            );
            allTableParams = valObj.allTableParams;
            nameList = valObj.nameList;
            conditionsIndex = valObj.conditionsIndex;
            operator_tableParams = valObj.operator_tableParams;
            parseI=valObj.parseI
            returnFor=valObj.returnFor
            if(returnFor){ return}
          }
          if (this.properties.rule.complexSelect) {
            if (this.properties.rule.allCellUnit) {
              allTableParams.push(true);
              val.conditionData.allCellUnit=this.properties.rule.allCellUnit;
              val.allCellUnit = this.properties.rule.allCellUnit;
            } else {
              allTableParams.push(false);
              delete val.conditionData.allCellUnit;
              delete val.allCellUnit;
            }
          }
          if (!allTableParams.includes(false)) {
            return message.error("请至少保留一个表格录入项");
          }
        }
      }
      this.$emit("ok", val, cloneDeep(this.nameList));
    },
    getContentMethodParams(data, res = []) {
      const { next, methodParams, expressionTreeData,params,expressionParams } = data;
      if(data.variableType==="constant" && !next){
        data.colTitle='值'
        res.push(data)
      }
      if (methodParams) {
        if (!next) {
          res.push(...methodParams);
          return res;
        } else {
          res.push(...methodParams);
          return this.getContentMethodParams(next, res);
        }
      } else if (res.length > 0 && !next) {
        return res;
      }
      if(expressionTreeData){
        return this.getContentMethodParams(expressionTreeData, res);
      }
      if (params&&Array.isArray(params)) {
        for(let i=0;i<params.length;i++){
          if(params[i].params){
            if(i===params.length-1){
              return this.getContentMethodParams(params[i].params, res);
            }else{
              this.getContentMethodParams(params[i].params, res);
            }
          }else if(params[i].data){
            if(i===params.length-1){
              return this.getContentMethodParams(params[i].data, res);
            }else{
              this.getContentMethodParams(params[i].data, res);
            }
          }
        }
      }
      if (expressionParams&&Array.isArray(expressionParams)) {
        for(let i=0;i<expressionParams.length;i++){
          if(expressionParams[i]){
            if(i===expressionParams.length-1){
              return getLastMethodParams(expressionParams[i], res);
            }else{
              getLastMethodParams(expressionParams[i], res);
            }
          }
        }
      }
      if (next) {
        return this.getContentMethodParams(next, res);
      }
      if(Array.isArray(data)){
        for(let i=0;i<data.length;i++){
          if(data[i].params){
            if(i===data.length-1){
              return this.getContentMethodParams(data[i].params, res);
            }else{
              this.getContentMethodParams(data[i].params, res);
            }
          }else if(data[i].data){
            if(i===data.length-1){
              return this.getContentMethodParams(data[i].data, res);
            }else{
              this.getContentMethodParams(data[i].data, res);
            }

          }
        }
      }
      return null
    },
    getContentOperatorParams(data) {
      const { next, operatorParams } = data;
      if (next) {
        return this.getContentOperatorParams(next);
      }
      if (operatorParams) {
        return operatorParams;
      }
    },
    handleCancel() {
      if(this.okBtn){
        this.okBtn=false;
        this.visible=false
        this.$emit("cancel");
        return
      }
      if(Object.keys(this.backRule).length>0&&this.backRule.complexSelect===undefined){
        const initConditionData = {
          variable: {
            valueType: "String",
            variableType: "dataEntry",
          },
          leftValueType: "String",
        };
        this.backRule.complexSelect = false;
        this.backRule.cellUnit = false;
        this.backRule.children = [
          {
            indent: 1,
            ruleCondition: {
              layer: 1,
              showLayer: 1,
              conditionId: 1,
              contents: new ConditionGenerate(initConditionData),
            },
          },
        ];
        this.rule = cloneDeep(this.backRule);
        this.toBeRule = cloneDeep(this.backRule);

        this.setComplexSelect(false);
      }
      this.okBtn=false
      this.visible=false
      this.$emit("cancel");
    },
    onchange(val) {
      this.toBeRule = val;
    },
    getLastMethodParams(data) {
      const { next, methodParams } = data;
      if (next) {
        return this.getLastMethodParams(next);
      }
      if (methodParams) {
        return methodParams;
      }
    },
    getLastNext(data) {
      const { next } = data;
      if (next) {
        return this.getLastNext(next);
      }
      return data;
    },
    setTableParsms(data) {
      let objVal = {};
      let tableParams = this.getTableParams(data, [], objVal);
      if (Object.keys(objVal).length > 0) {
        for (let i = 0; i < tableParams.length; i++) {
          if (objVal[i] && objVal[i].length > 0) {
            tableParams[i] = objVal[i];
          }
        }
      }
      // 这个函数用于将嵌套数组展平为一维数组
      function flattenArray(arr) {
        let result = [];
        for (let i = 0; i < arr.length; i++) {
          if (Array.isArray(arr[i])) {
            // 如果元素是数组，则递归调用flattenArray函数
            result = result.concat(flattenArray(arr[i]));
          } else {
            // 如果元素不是数组，则直接将其添加到结果数组中
            result.push(arr[i]);
          }
        }
        return result;
      }
      let resArr = flattenArray(tableParams);
      tableParams = resArr;
      return tableParams;
    },
    getTableParams(methodParams = [], aExpressParam = [], objVal = {},pushAExpressParam=false,parentIndex) {
      const _this = this;
      if(methodParams){
        return methodParams.map((item, index) => {
          const { value = false, valueType } = item;
          if (item.variableType && item.variableType === "expression") {
            const expressionTreeData = item.expressionTreeData;
            if (expressionTreeData && expressionTreeData.params) {
              loopExpressionTreeData(expressionTreeData.params);
            }
            function loopExpressionTreeData(data) {
              for (let i = 0; i < data.length; i++) {
                if (data[i].data && data[i].data.next) {
                  const { next } = data[i].data;
                  let lastNext=null
                  pushAExpressParam=true
                  if(next.next){
                    lastNext=_this.getLastNext(next)
                  }else{
                    lastNext=next
                  }
                  if (lastNext) {
                    const _methodParams = next && next.methodParams;
                    if (_methodParams && _methodParams.length > 0) {
                      _this.express = true;
                      _this.getTableParams(_methodParams, aExpressParam, objVal, true, (parentIndex||parentIndex===0)?parentIndex:index);
                    } else {
                      const { value = false, valueType,colTitle } = lastNext;
                      // if (!valueType.includes(".")) {
                      // if (_this.express) {
                      let oTemp={}
                      if(colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                      }else{
                        if(pushAExpressParam){  
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                      }
                      // }
                      // objVal[index] = !!(value && value.toString().length > 0);
                      // _this.express = false;
                      // return !!(value && value.toString().length > 0);
                      // }
                    }
                  }
                } else if (data[i].type === "variable") {
                  const _data = data[i].data;
                  if (!_data.valueType.includes(".")) {
                    let oTemp={}
                    if(_data.colTitle){
                      oTemp.value=!!((_data.value || _data.value === 0) && _data.value.toString().length > 0)
                      oTemp.colTitle=_data.colTitle
                      if(pushAExpressParam){
                        aExpressParam.push(oTemp)
                      }
                    }else{
                      if(pushAExpressParam){  
                        aExpressParam.push(!!((_data.value || _data.value === 0) && _data.value.toString().length > 0));
                      }
                    }
                  }
                } else if (data[i].type === "expression") {
                  loopExpressionTreeData(data[i].params);
                }
              }
            }
          } else {
            if (!valueType.includes(".")) {
              if (_this.express) {
                let oTemp={}
                if(item.colTitle){
                  oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                  oTemp.colTitle=item.colTitle
                  if(pushAExpressParam){
                    aExpressParam.push(oTemp)
                  }
                }else{
                  if(pushAExpressParam){
                    aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                  }
                }
              } else {
                const { next, value = false,singleParams } = item;
                if (next) {
                  const _methodParams = next.methodParams;
                  if (_methodParams && _methodParams.length > 0) {
                    _this.express = true;
                    return _this.getTableParams(
                      _methodParams,
                      aExpressParam,
                      objVal,
                      true,
                      (parentIndex||parentIndex===0)?parentIndex:index
                    );
                  } else {
                    const { value = false } = next;
                    if (_this.express) {
                      let oTemp={}
                      if(next.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=next.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                      }
                    } else {
                      let oTemp={}
                      if(next.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=next.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                        return oTemp
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                        return !!((value || value === 0) && value.toString().length > 0);
                      }
                    }
                  }
                } if(singleParams){
                  for(let si=0;si<singleParams.length;si++){
                    const {value=false}=singleParams[si]
                    if (_this.express) {
                      let oTemp={}
                      if(item.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=item.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                      }
                    } else {
                      let oTemp={}
                      if(item.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=item.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                        return oTemp
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                        return !!((value || value === 0) && value.toString().length > 0);
                      }
                    }
                  }
                }else {
                  if (_this.express) {
                    let oTemp={}
                    if(item.colTitle){
                      oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                      oTemp.colTitle=item.colTitle
                      if(pushAExpressParam){
                        aExpressParam.push(oTemp)
                      }
                    }else{
                      if(pushAExpressParam){
                        aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                      }
                    }
                  } else {
                    let oTemp={}
                    if(item.colTitle){
                      oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                      oTemp.colTitle=item.colTitle
                      if(pushAExpressParam){
                        aExpressParam.push(oTemp)
                      }
                      return oTemp
                    }else{
                      if(pushAExpressParam){
                        aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                      }
                      return !!((value || value === 0) && value.toString().length > 0);
                    }
                  }
                }
              }
              // objVal[index] = !!(value && value.toString().length > 0);
              // _this.express = false;
              // aExpressParam = [];
              if(item.colTitle){
                let oTemp={}
                oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                oTemp.colTitle=item.colTitle
                return oTemp
              }else{
                return !!((value || value === 0) && value.toString().length > 0);
              }
            } else {
              const { next, value = false } = item;
              if (next) {
                const _methodParams = next.methodParams;
                if (_methodParams && _methodParams.length > 0) {
                  _this.express = true;
                  return _this.getTableParams(
                    _methodParams,
                    aExpressParam,
                    objVal,
                    true,
                    (parentIndex||parentIndex===0)?parentIndex:index
                  );
                } else {
                  const { next:_next, value = false } = next;
                  if(_next){
                    const _methodParams = _next.methodParams;
                    if (_methodParams && _methodParams.length > 0) {
                      _this.express = true;
                      return _this.getTableParams(
                        _methodParams,
                        aExpressParam,
                        objVal,
                        true,
                        (parentIndex||parentIndex===0)?parentIndex:index
                      );
                    }else{
                      if (_this.express) {
                        let oTemp={}
                        if(next.colTitle){
                          oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                          oTemp.colTitle=next.colTitle
                          if(pushAExpressParam){
                            aExpressParam.push(oTemp)
                          }
                        }else{
                          if(pushAExpressParam){
                            aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                          }
                        }
                      } else {
                        let oTemp={}
                        if(next.colTitle){
                          oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                          oTemp.colTitle=next.colTitle
                          if(pushAExpressParam){
                            aExpressParam.push(oTemp)
                          }
                          return oTemp
                        }else{
                          if(pushAExpressParam){
                            aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                          }
                          return !!((value || value === 0) && value.toString().length > 0);
                        }
                      }
                    }
                  }else{
                    if (_this.express) {
                      let oTemp={}
                      if(next.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=next.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                      }
                    } else {
                      let oTemp={}
                      if(next.colTitle){
                        oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                        oTemp.colTitle=next.colTitle
                        if(pushAExpressParam){
                          aExpressParam.push(oTemp)
                        }
                        return oTemp
                      }else{
                        if(pushAExpressParam){
                          aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                        }
                        return !!((value || value === 0) && value.toString().length > 0);
                      }
                    }
                  }
                }
              } else {
                if (_this.express) {
                  let oTemp={}
                  if(item.colTitle){
                    oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                    oTemp.colTitle=item.colTitle
                    if(pushAExpressParam){
                      aExpressParam.push(oTemp)
                    }
                  }else{
                    if(pushAExpressParam){
                      aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                    }
                  }
                } else {
                  let oTemp={}
                  if(item.colTitle){
                    oTemp.value=!!((value || value === 0) && value.toString().length > 0)
                    oTemp.colTitle=item.colTitle
                    if(pushAExpressParam){
                      aExpressParam.push(oTemp)
                    }
                    return oTemp
                  }else{
                    if(pushAExpressParam){
                      aExpressParam.push(!!((value || value === 0) && value.toString().length > 0));
                    }
                    return !!((value || value === 0) && value.toString().length > 0);
                  }
                }
              }
            }
          }
          objVal[parentIndex||index] = aExpressParam;
          aExpressParam = [];
          _this.express = false;
          if (!valueType.includes(".")) {
            if(item.colTitle){
              let oTemp={}
              oTemp.value=!!((value || value === 0) && value.toString().length > 0)
              oTemp.colTitle=item.colTitle
              return oTemp
            }else{
              return !!((value || value === 0) && value.toString().length > 0);
            }
          }
          if(item.colTitle){
              let oTemp={}
              oTemp.value=true
              oTemp.colTitle=item.colTitle
              return oTemp
            }else{

              return true;
            }
        });
      }else{
        return []
      }
    },
  },
};
</script>

<style scoped>
.rule-name {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.name-lable {
  width: 8%;
  color: #303133f1;
}
.name-input {
  width: 55%;
}
</style>
