import request from '@/utils/request'
// 岗位权限查询
export function postQuery(params) {
  return request({
    url: 'sys/org/selectList',
    method: 'get',
    params,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}
//岗位权限业务条线下拉框
export function postLine() {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get',
  })
}
//删除
export function postDel(data) {
  return request({
    url: 'sys/org/delete',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
//根据uuid查code(添加子机构)

export function getPostson(params) {
  return request({
    url: 'sys/org/getBusinessCodeByUuid',
    method: 'get',
    params,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
// 获取子机构文本框的值
export function getOrd(params) {
  return request({
    url: 'sys/org/getOrgById',
    method: 'get',
    params,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
//   省/地 下拉列表
export function getSelect(params) {
  return request({
    url: 'sys/dictionaryValue/getDicValueByTypeCode',
    method: 'get',
    params,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
//保存
export function getSaveInfo(data) {
  return request({
    url: 'sys/org/saveOrUpdateOrg',
    method: 'post',
    data,
  })
}
// 复制中的业务条线
export function getcopyline() {
  return request({
    url: 'sys/org/getRemainBusiness',
    method: 'get',
  })
}
//复制
export function copyTree(data) {
  return request({
    url: 'sys/org/copy',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8' // 设置完以后 传入的params对象就会时候用formdata传参的方式
    },
  })
}
//业务条线
export function getAllBusinessByLoginUser(params) {
  return request({
    url: 'sys/dictionaryValue/getAllBusinessByLoginUser',
    method: 'get',
    params
  })
}
//
export function getRoleById(params) {
  return request({
    url: 'sys/role/getRoleById',
    method: 'get',
    params
  })
}
//
export function updateAction(data) {
  return request({
    url: 'sys/role/updateAction',
    method: 'post',
    data
  })
}
//保存默认任务审核机构
export function defaultAuditOrg(data) {
  return request({
    url: 'sys/org/saveDefaultAuditOrg',
    method: 'post',
    data,
  })
}

//删除默认任务审核机构
export function delDefaultAuditOrg(data) {
  return request({
    url: 'sys/org/delDefaultAuditOrg',
    method: 'post',
    data,
  })
}
