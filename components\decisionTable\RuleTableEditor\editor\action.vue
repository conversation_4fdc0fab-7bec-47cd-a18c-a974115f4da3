<template>
  <div class="action">
    <!-- 动作类型选择组件 -->
    <ActionTypeCom
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :value="actionType"
      :noRuleCellUnit="noRuleCellUnit"
      :isTable="isTable"
      @onChange="changeActionComType"
    />
    <!-- 设置值组件 -->
    <SetValueTableCom
      v-if="actionType === 'setValue'"
      :key="pos"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :actionData="actionParams"
      @onChange="onSetValueComChange"
    />
    <!-- 调用方法组件 -->
    <ActionMethod
      v-if="actionType === 'invokeMethod'"
      :key="pos"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :dataSource="actionParams[0]"
      :hideFrontBtn="true"
      :hideEndBtn="true"
      :signValue="''"
      :noRuleCellUnit="noRuleCellUnit"
      :isTable="isTable"
      @onChange="onMehtodChange"
    />
  </div>
</template>

<script setup>
import * as util from "@/components/ruleEditCom/utils/util";

const { ActionGenerate } = util;

// 定义 props
const props = defineProps({
  pos: {
    type: String,
  },
  actionData: {
    type: Object,
    default: () => ({}),
  },
  actionValid: {
    type: Object,
    default: () => ({}),
  },
  locked: {
    type: Boolean,
    default: false,
  },
  isTrack: {
    type: Boolean,
    default: false,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  noRuleCellUnit: {
    type: Boolean,
    default: false,
  },
});

const actionType = ref("");
const actionParams = ref([]);

// 定义 emit
const emit = defineEmits(["onChange", "deleteActionItem", "addActionItem"]);

// 初始化动作类型和参数
const init = () => {
  const { actionType: acType = "setValue", actionParams: actPar = [] } =
    props.actionData;
  actionType.value = acType;
  actionParams.value = actPar;
};

// 监听 actionData 的变化
watch(
  () => props,
  () => {
    init();
  },
  { deep: true, immediate: true }
);

// 处理设置值组件变化事件
const onSetValueComChange = (_pos, _data) => {
  const newActionData = {
    actionType: "setValue",
    actionParams: _data,
  };
  emit("onChange", _pos, newActionData);
};

// 处理调用方法组件变化事件
const onMehtodChange = (_pos, _data) => {
  const newActionData = {
    actionType: "invokeMethod",
    actionParams: [_data],
  };
  emit("onChange", _pos, newActionData);
};

// 处理动作类型变化事件
const changeActionComType = (_pos, selectOptions) => {
  let newActionData;
  const { value } = selectOptions[0];
  const initSetValueAction = {
    actionType: "setValue",
    actionParams: [
      {
        variableType: "field",
        valueType: "String",
        value: [],
      },
      {
        variableType: "constant",
        valueType: "String",
        value: "",
      },
    ],
  };
  const initInvokeMethodActionData = {
    actionType: "invokeMethod",
    actionParams: [
      {
        variableType: "field",
        valueType: "String",
        value: [],
      },
    ],
  };

  if (value === "invokeMethod") {
    newActionData = new ActionGenerate(initInvokeMethodActionData);
  }
  if (value === "setValue") {
    newActionData = new ActionGenerate(initSetValueAction);
  }
  emit("onChange", _pos, newActionData);
};

// 处理删除动作项事件
const handleDeleteActionItem = () => {
  emit("deleteActionItem", pos.value);
};

// 处理添加动作项事件
const handleAddActionItem = () => {
  emit("addActionItem", pos.value);
};

// 初始化组件
onMounted(() => {
  init();
});
</script>

<style scoped>
.icon-validate {
  font-size: 16px;
  margin-left: 20px;
}
</style>
