<!--字典项-->

<script setup lang="ts">
    const modal = inject('modal');
    import { dictionTypeSel } from "@/api/dictionarytype";
    import {
        dicEctryList,
        dictionParentsel,
        dicEntryDel,
    } from "@/api/diction_entry";
    import DictionaryAdd from "@/businessComponents/dictionary/DictionaryAdd";
    import DictionaryUpdate from "@/businessComponents/dictionary/DictionaryUpdate";
    const message = inject('message')
    import qs from "qs";
    definePageMeta({
        title: '字典项'
    })

    interface Dictionary {
        typeCode: string;
        code: string;
        name: string;
        parentTypeCode: string;
        parentCode: string;
        uuid: string;
    }

    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            align: 'left',
            ellipsis: true,
            width:180,
            key: 'name',
        },
        {
            title: '代码',
            dataIndex: 'code',
            align: 'center',
            ellipsis: true,
            width:180,
            key: 'code',
        },
        {
            title: '字典类型',
            dataIndex: 'typeCode',
            align: 'center',
            ellipsis: true,
            width:200,
            key: 'typeCode',
        },
        {
            title: '父字典类型',
            dataIndex: 'parentTypeCode',
            align: 'center',
            ellipsis: true,
            width:180,
            key: 'parentTypeCode',
            checked:false
        },
        {
            title: '父字典名称',
            dataIndex: 'parentCode',
            align: 'center',
            ellipsis: true,
            width:180,
            key: 'parentCode',
        }
    ];

    const fetchDictionary = async (params: Record<string, any> = {}) => {
        try {
            let par = {
                code: params.code,
                name: params.name,
                parentCode: params.arrmenu2,
                parentTypeCode: params.arrmenu1,
                typeCode: params.arrmenu,
                page: params.page || 1,
                several: params.pageSize || 10,
            };
            const res = await dicEctryList(par);
            return {
                data: res.data.data,
                totalCount: res.data.totalCount
            };
        } catch (error) {
            if (message && typeof message === 'object' && 'error' in message) {
                message.error('获取字典失败');
            }
            return {
                data: [],
                totalCount: 0
            };
        }
    };

    const selOption = ref<any[]>([]);
    const sel1Option = ref<any[]>([]);
    const pardicOption = ref<any[]>([]);

    const getdictionsel = async () => {
        try {
            const res = await dictionTypeSel();
            let dicAdd = {
                name: "请选择",
                code: "",
            };
            res.data.unshift(dicAdd);
            selOption.value = res.data;
            sel1Option.value = res.data;
            // 更新搜索配置中的选项
            searchConfig.advancedSearchFields.forEach(field => {
                if (field.field === 'arrmenu' || field.field === 'arrmenu1') {
                    field.compConfig.options = selOption.value.map(item => ({
                        name: item?.name || '',
                        value: item?.code || ''
                    }));
                }
            });
        } catch (error) {
            if (message && typeof message === 'object' && 'error' in message) {
                message.error('获取字典类型失败');
            }
        }
    }

    const selectGet = (vid: string) => {
        let pars = {
            parentCode: "",
            parentTypeCode: "",
            typeCode: vid,
        };
        dictionParentsel(pars).then((res) => {
            pardicOption.value = res.data;
            // 更新父字典选项
            searchConfig.advancedSearchFields.forEach(field => {
                if (field.field === 'arrmenu2') {
                    field.compConfig.options = pardicOption.value.map(item => ({
                        name: item?.name || '',
                        value: item?.code || ''
                    }));
                }
            });
        });
    }

    const isModalVisible = ref(false);
    const modalType = ref('');
    const datar = ref<Record<string, any> | null>(null);
    const isSaving = ref(false);

    const handleDelete = (record: Record<string, any>) => {
        if (modal && typeof modal === 'object' && 'confirm' in modal) {
            modal.confirm({
                title: '确定删除吗？',
                okText: '确定',
                cancelText: '取消',
                onOk() {
                    dicEntryDel(
                        qs.stringify({
                            uuid: record.uuid,
                        })
                    ).then(() => {
                        if (message && typeof message === 'object' && 'success' in message) {
                            message.success("删除成功");
                        }
                        listLayout.value?.refresh();
                    });
                }
            });
        }
    };

    const handleCancel = () => {
        isModalVisible.value = false;
        isSaving.value = false;
    };

    // 字典新增组件
    const dictionaryAddComponent = ref()
    // 字典更新组件
    const dictionaryUpdateComponent = ref()

    // 处理modal ok 事件
    function handleModalOk() {
        isSaving.value = true;
        let submitFun
        switch (modalType.value) {
            case 'add':
                submitFun = dictionaryAddComponent.value.submitFun;
                break;
            case 'update':
                submitFun = dictionaryUpdateComponent.value.submitFun;
                break;
        }
        submitFun && submitFun((success) => {
            if (success) {
                listLayout.value?.refresh();
                isModalVisible.value = false;
            }
            isSaving.value = false;
        });
    }

    const showModal = (type: string, record: Record<string, any> = {}) => {
        modalType.value = type;
        if (type === 'update') {
            datar.value = record;
        }
        isModalVisible.value = true;
    };

    interface SearchField {
        label: string;
        field: string;
        compType: 'input' | 'select' | 'datePicker';
        compConfig?: Record<string, any>;
    }

    interface SearchConfig {
        simpleSearchField: {
            label: string;
            field: string;
        };
        advancedSearchFields: SearchField[];
    }

    // 搜索配置
    const searchConfig = {
        // 简单搜索字段
        simpleSearchField: {
            label: '名称',
            field: 'name'
        },
        // 高级搜索字段
        advancedSearchFields: [
            {
                label: '字典类型',
                field: 'arrmenu',
                compType: 'select',
                compConfig: {
                    options: [],
                    showSearch: true,
                    filterOption: true
                }
            },
            {
                label: '代码',
                field: 'code',
                compType: 'input'
            },
            {
                label: '名称',
                field: 'name',
                compType: 'input'
            },
            {
                label: '父字典类型',
                field: 'arrmenu1',
                compType: 'select',
                compConfig: {
                    options: [],
                    onChange: selectGet
                }
            },
            {
                label: '父字典',
                field: 'arrmenu2',
                compType: 'select',
                compConfig: {
                    options: []
                }
            }
        ]
    };

    // 事件配置
    const eventConfig = {
        // 搜索事件
        searchEvent: () => {},
        // 添加事件
        addNewEvent: () => showModal('add'),
        // 表单处理器配置
        formHandler: {
            // 查询方法
            queryMethod: fetchDictionary
        }
    };

    interface MenuItem {
        key: string;
        label: string;
        onClick: () => void;
    }

    // 获取操作菜单项
    const getActionMenuItems = (record: Record<string, any>): MenuItem[] => {
        return [
            {
                key: 'update',
                label: '更新',
                onClick: () => showModal('update', record)
            },
            {
                key: 'delete',
                label: '删除',
                onClick: () => handleDelete(record)
            }
        ];
    };

    const listLayout = ref<InstanceType<typeof ListPage> | null>(null);

    // 数据加载完成处理函数
    const handleDataLoaded = (response: any) => {
        if (response && response.data) {
            // 可以在这里处理数据加载完成后的逻辑
        }
    };

    //组件挂载后
    onMounted(async () => {
        await getdictionsel();
    });
</script>

<template>
    <ListPage
        ref="listLayout"
        title="字典项"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="true"
        :tableColumns="columns"
        :queryMethod="fetchDictionary"
        rowKey="uuid"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
    >
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal
        v-if="isModalVisible"
        :width='600'
        :visible="isModalVisible"
        :title="modalType === 'add' ? '新增字典' : '更新字典'"
        @ok="handleModalOk"
        @cancel="handleCancel"
        okText="保存"
        :okButtonProps="{ disabled: isSaving }"
        class="dictionary-modal"
    >
        <div style="max-height: 60vh; overflow-y: auto;">
            <DictionaryAdd ref="dictionaryAddComponent" v-if="modalType === 'add'" />
            <DictionaryUpdate ref="dictionaryUpdateComponent" v-if="modalType === 'update'" :datar="datar" />
        </div>
    </a-modal>
</template>

<style lang="scss" scoped>
.dictionary-modal {
    :deep(.ant-modal-body) {
        padding: 20px 24px;
        overflow: visible;
    }

    :deep(.ant-modal-footer) {
        border-top: 1px solid #f0f0f0;
        padding: 10px 24px;
        margin-top: 0;
    }
}
</style>
