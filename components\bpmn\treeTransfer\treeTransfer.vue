<template>
  <div class="tree-transfer-wrapper" style="width: 100%; height: 400px;">
    <!-- 原始树穿梭组件 -->
    <tree-transfer class="custom-tree-transfer" :target-keys="adaptedTargetKeys" :from-data="adaptedFromData"
      :to-data="adaptedToData" :default-checked-keys="adaptedDefaultCheckedKeys" :search-placeholder="'请输入搜索内容'"
      :show-search="true" :default-expand-all="true" :key="treeKey" :is-sub-flow="props.isSubFlow" @update:targetKeys="handleUpdateTargetKeys"
      @change="handleChange" @select-change="handleSelectChange" />

  </div>
</template>

<script setup lang="jsx">
import TreeTransfer from './TreeTransfer';

const props = defineProps({
  fromData: {
    type: Array,
    default: () => []
  },
  toData: {
    type: Array,
    default: () => []
  },
  selectedKeys: {
    type: Array,
    default: () => []
  },
  isInternalChange: {
    type: Boolean,
    default: false
  },
  isSubFlow: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["onChange"]);

// 内部状态
const targetKeys = ref([]);
const treeKey = ref(0);
const selectedKeys = ref([]);


// 数据适配层 - 为了确保数据格式统一
const adaptedFromData = computed(() => {
  // 确保数据格式正确，特别是id/key和text/title属性
  return formatTreeData(props.fromData);
});

const adaptedToData = computed(() => {
  if (props.toData && props.toData.length > 0) {
    return formatTreeData(props.toData);
  } else {
    return [];
  }
});

const adaptedTargetKeys = computed(() => {
  // 确保targetKeys中每个key在fromData中都有对应节点
  return [...targetKeys.value];
});

const adaptedDefaultCheckedKeys = computed(() => {
  // 合并选中的键
  return [...selectedKeys.value, ...targetKeys.value];
});

// 格式化数据并保持树结构
const formatTreeData = (data = []) => {
  if (!data || !Array.isArray(data)) return [];

  // 给每个节点分配唯一ID
  const nodeIdCounter = ref(1);

  // 深拷贝确保不改变原始数据
  const deepClone = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    const copy = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        copy[key] = deepClone(obj[key]);
      }
    }
    return copy;
  };

  // 保存当前左侧树节点的禁用状态
  const disabledStateMap = new Map();
  const saveDisabledState = (nodes) => {
    if (!nodes) return;
    nodes.forEach(node => {
      if (node.key) {
        disabledStateMap.set(node.key, node.disabled);
      }
      if (node.children) {
        saveDisabledState(node.children);
      }
    });
  };

  // 如果有已存在的左侧树数据，保存其禁用状态
  if (adaptedFromData.value && adaptedFromData.value.length > 0) {
    saveDisabledState(adaptedFromData.value);
  }

  const processNodes = (items, parentId = null) => {
    if (!items || !Array.isArray(items)) return [];

    return items.map((item) => {
      const clonedItem = deepClone(item);

      // 确保每个节点都有key和title
      const nodeKey = clonedItem.id || clonedItem.key || `node_${nodeIdCounter.value++}`;
      const nodeTitle = clonedItem.text || clonedItem.title || clonedItem.name || `节点${nodeKey}`;
      // 检查是否有已存在的禁用状态
      const existingDisabledState = disabledStateMap.has(nodeKey)
        ? disabledStateMap.get(nodeKey)
        : clonedItem.disabled;

      // 判断节点类型，为不同类型设置对应的图标类名
      const isFolder = clonedItem.children && clonedItem.children.length > 0;
      const className = isFolder ? 'folder-node' : 'file-node';

      const node = {
        key: nodeKey,
        title: nodeTitle,
        parentId: parentId,
        // 优先使用已存在的禁用状态，其次是原始数据中的禁用状态
        disabled: existingDisabledState,
        children: clonedItem.children ? processNodes(clonedItem.children, nodeKey) : undefined,
        isLeaf: !clonedItem.children || clonedItem.children.length === 0,
        folder: clonedItem.folder,
        type: clonedItem.type ? clonedItem.type : '',
        id: clonedItem.id,
        text: clonedItem.text,
        parent: clonedItem.parent,
        className, // 添加样式类名
        // 保留原始数据
        originData: {
          ...clonedItem,
          id: nodeKey,
          text: nodeTitle
        }
      };

      return node;
    });
  };

  return processNodes(data);
};

// 获取所有叶子节点的keys
const getLeafNodeKeys = (data) => {
  const keys = [];
  const traverse = (items) => {
    if (!items) return;
    items.forEach(item => {
      // 如果是叶子节点，添加key
      if (!item.children || item.children.length === 0) {
        keys.push(item.key);
      } else {
        traverse(item.children);
      }
    });
  };
  traverse(data);
  return keys;
};


// 处理 update:targetKeys 事件
const handleUpdateTargetKeys = (newKeys) => {
  // 直接更新内部状态
  targetKeys.value = [...newKeys];
};

// 处理数据变化
const handleChange = ({ targetKeys: newTargetKeys, direction, moveKeys, rightTreeData }) => {

  // 更新目标键
  targetKeys.value = [...newTargetKeys];

  // 递归修改节点的禁用状态
  const updateNodeDisabledState = (nodes, keysToUpdate, disabled) => {
    if (!nodes) return;

    nodes.forEach(node => {
      // 如果节点在待处理的键中，设置禁用状态
      if (keysToUpdate.includes(node.key)) {
        node.disabled = disabled;
      }

      // 递归处理子节点
      if (node.children) {
        updateNodeDisabledState(node.children, keysToUpdate, disabled);
      }
    });
  };

  // 根据穿梭方向设置对应的禁用状态
  if (direction === 'left' && moveKeys.length > 0) {
    // 如果是向左穿梭，解除左侧节点的禁用状态
    updateNodeDisabledState(adaptedFromData.value, moveKeys, false);
  } else if (direction === 'right' && moveKeys.length > 0) {
    // 如果是向右穿梭，设置左侧节点为禁用状态
    updateNodeDisabledState(adaptedFromData.value, moveKeys, true);
  }

  // 如果TreeTransfer.jsx没有传递rightTreeData，则需要导入它的buildRightTreeData函数并调用
  const finalRightTreeData = rightTreeData || [];

  // 发送变更事件
  emit("onChange", {
    targetKeys: newTargetKeys,
    rightTreeData: finalRightTreeData,
    direction
  });

  // 强制刷新视图
  refreshTree();
};


// 处理选择变化
const handleSelectChange = (keys) => {
  selectedKeys.value = keys;
};

// 强制刷新树
const refreshTree = () => {
  treeKey.value += 1;
};

// 初始化数据
onMounted(() => {

  if (props.toData && props.toData.length > 0) {
    const formattedData = formatTreeData(props.toData);
    const initialTargetKeys = getLeafNodeKeys(formattedData);
    targetKeys.value = initialTargetKeys;

    // 稍后再刷新视图
    setTimeout(() => {
      refreshTree();
    }, 100);
  }
});

// 监听fromData变化
watch(
  () => props.fromData,
  (newVal) => {
    if (newVal && props.isInternalChange) {
      targetKeys.value = [];
      refreshTree();
    }
  },
  { deep: true }
);

// 监听toData变化
watch(
  () => props.toData,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      const formattedData = formatTreeData(newVal);
      const newTargetKeys = getLeafNodeKeys(formattedData);
      targetKeys.value = newTargetKeys;

      // 延迟刷新视图
      setTimeout(() => {
        refreshTree();
      }, 100);
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.tree-transfer-wrapper {
  width: 100%;
  height: 400px;
  box-sizing: border-box;
}

.custom-tree-transfer {
  height: 400px;
  width: 100%;

  :deep(.ant-tree) {

    // 移除树结构线条
    .ant-tree-indent-unit::before {
      display: none;
    }

    // 修改展开/收起图标样式为AntD穿梭组件风格
    .ant-tree-switcher {
      width: 18px;
      height: 18px;
      position: relative;

      &.ant-tree-switcher_open::before {
        content: "▼";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 10px;
        color: #666;
        margin-top: 2px;
      }

      &.ant-tree-switcher_close::before {
        content: "▶";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 10px;
        color: #666;
        margin-top: 2px;
      }

      // 隐藏原始图标
      .ant-tree-switcher-icon {
        display: none;
      }
    }

    // 自定义树节点样式
    .ant-tree-treenode {
      padding: 2px 0;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    // 调整节点内容区域的间距
    .ant-tree-node-content-wrapper {
      display: flex;
      align-items: center;
      padding: 0 4px;

      // 确保图标和文本对齐
      .ant-tree-title {
        line-height: 20px;
      }

      // 自定义图标样式
      .folder-icon {
        color: #1890ff;
        margin-right: 4px;
        font-size: 14px;
      }

      .file-icon {
        color: #555;
        margin-right: 4px;
        font-size: 14px;
      }
    }

    // 自定义选中状态
    .ant-tree-node-selected {
      background-color: #e6f7ff !important;
    }

    // 隐藏默认图标
    // .ant-tree-icon__customize,
    // .ant-tree-switcher-leaf-line,
    // .ant-tree-icon__close,
    // .ant-tree-icon__open,
    // .ant-tree-icon__docu {
    //   display: none !important;
    // }
  }
}

.debug-panel {
  margin-top: 20px;
  padding: 10px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  background-color: #f0f8ff;
}

.debug-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;

  button {
    padding: 4px 8px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #40a9ff;
    }
  }
}

.debug-data {
  max-height: 300px;
  overflow: auto;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;

  pre {
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 12px;
  }
}
</style>
