import request from '@/utils/request'

/**
 * 服务器监控配置接口
 */
interface ServerMonitorConfig {
  id: number;
  [key: string]: any; // 其他可能的配置字段
}

/**
 * @description 更新服务器监控配置
 * @param data 监控配置数据
 */
export function updateServerMonitorConfig(data: ServerMonitorConfig) {
  return request({
    url: '/server/monitor-config/update',
    method: 'put',
    data
  })
}

/**
 * @description 获取最新的服务器监控配置
 */
export function getLatestServerMonitorConfig() {
  return request<ServerMonitorConfig>({
    url: '/server/monitor-config/latest',
    method: 'get'
  })
} 