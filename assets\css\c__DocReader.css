.index-module_SideTipContainer_GOO1q {
    position: fixed;
    right: 36px;
    z-index: 100
}

.index-module_hovered_0kEK0 {
    right: 0!important
}

.index-module_hidden_D4-zu {
    right: 0!important;
    transform: translateX(60%)!important
}

.index-module_hideIcon_H0k4M {
    position: absolute;
    top: 0;
    right: -22px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: rgba(0,0,0,.15);
    border-radius: 20px;
    transform: scale(0);
    cursor: pointer;
    opacity: 0;
    transition: all .18s ease-out .18s
}

.index-module_hideIcon_H0k4M:hover {
    background: rgba(0,0,0,.35)
}

.index-module_hideIconHovered_qClFl {
    transform: scale(1);
    opacity: 1
}

.index-module_hideIcon_H0k4M .index-module_minus_Q1aw\+ {
    width: 6px;
    height: 2px;
    background: var(--yq-bg-primary);
    border-radius: 4px
}

.index-module_domIcon_V0vda {
    z-index: 997
}

.hljs-comment {
    color: #8e908c
}

.hljs-link,.hljs-meta {
    color: #4078c0
}

.hljs-addition {
    color: #b9ca4a
}

.hljs-deletion {
    color: #d54e53
}

.css .hljs-class,.css .hljs-id,.css .hljs-pseudo,.hljs-attribute,.hljs-regexp,.hljs-tag,.hljs-variable,.html .hljs-doctype,.ruby .hljs-constant,.xml .hljs-doctype,.xml .hljs-pi,.xml .hljs-tag .hljs-title {
    color: #c82829
}

.hljs-built_in,.hljs-constant,.hljs-literal,.hljs-number,.hljs-params,.hljs-pragma,.hljs-preprocessor {
    color: #f5871f
}

.css .hljs-rule .hljs-attribute,.ruby .hljs-class .hljs-title {
    color: #eab700
}

.hljs-header,.hljs-inheritance,.hljs-name,.hljs-string,.hljs-value,.ruby .hljs-symbol,.xml .hljs-cdata {
    color: #718c00
}

.css .hljs-hexcolor,.hljs-title {
    color: #3e999f
}

.coffeescript .hljs-title,.hljs-function,.javascript .hljs-title,.perl .hljs-sub,.python .hljs-decorator,.python .hljs-title,.ruby .hljs-function .hljs-title,.ruby .hljs-title .hljs-keyword {
    color: #4271ae
}

.hljs-keyword,.javascript .hljs-function {
    color: #8959a8
}

.hljs {
    display: block;
    overflow-x: auto;
    background: var(--yq-bg-primary);
    color: #4d4d4c;
    padding: .5em;
    -webkit-text-size-adjust: none
}

.coffeescript .javascript,.javascript .xml,.tex .hljs-formula,.xml .css,.xml .hljs-cdata,.xml .javascript,.xml .vbscript {
    opacity: .5
}

.token.block-comment,.token.cdata,.token.comment,.token.doctype,.token.prolog {
    color: #708090
}

.token.punctuation {
    color: #999
}

.namespace {
    opacity: .7
}

.token.boolean,.token.constant,.token.deleted,.token.function-name,.token.number,.token.property,.token.symbol,.token.tag {
    color: #905
}

.token.attr-name,.token.builtin,.token.char,.token.function,.token.inserted,.token.selector,.token.string {
    color: #690
}

.language-css .token.string,.style .token.string,.token.entity,.token.operator,.token.url,.token.variable {
    color: #a67f59;
    background: hsla(0,0%,100%,.5)
}

.token.atrule,.token.attr-value,.token.keyword {
    color: #07a
}

.token.function {
    color: #dd4a68
}

.token.important,.token.regex,.token.variable {
    color: #e90
}

.token.bold,.token.important {
    font-weight: 700
}

.token.italic {
    font-style: italic
}

.token.entity {
    cursor: help
}

.mermaid .label {
    color: #333
}

.mermaid .node circle,.mermaid .node ellipse,.mermaid .node polygon,.mermaid .node rect {
    fill: #f7f7f7;
    stroke: #d0d0d0;
    stroke-width: 1px
}

.mermaid .edgePath .path {
    stroke: #333
}

.mermaid .edgeLabel {
    background-color: #e8e8e8
}

.mermaid .cluster rect {
    fill: #ffffde!important;
    rx: 4!important;
    stroke: #aa3!important;
    stroke-width: 1px!important
}

.mermaid .cluster text {
    fill: #333
}

.mermaid .actor {
    stroke: #d0d0d0;
    fill: #f7f7f7
}

.mermaid text.actor {
    fill: #000;
    stroke: none
}

.mermaid .actor-line {
    stroke: grey
}

.mermaid .messageLine0 {
    marker-end: "url(#arrowhead)"
}

.mermaid .messageLine0,.mermaid .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: "2 2";
    stroke: #333
}

.mermaid #arrowhead {
    fill: #333
}

.mermaid #crosshead path {
    fill: #333!important;
    stroke: #333!important
}

.mermaid .messageText {
    fill: #333;
    stroke: none
}

.mermaid .labelBox {
    stroke: #d0d0d0;
    fill: #f7f7f7
}

.mermaid .labelText,.mermaid .loopText {
    fill: #000;
    stroke: none
}

.mermaid .loopLine {
    stroke-width: 2;
    stroke-dasharray: "2 2";
    marker-end: "url(#arrowhead)";
    stroke: #d0d0d0
}

.mermaid .note {
    stroke: #e0ad4f;
    fill: #fff4a7
}

.mermaid .noteText {
    fill: #000;
    stroke: none;
    font-family: trebuchet ms,verdana,arial;
    font-size: 14px
}

.mermaid .section {
    stroke: none;
    opacity: .2
}

.mermaid .section0 {
    fill: #dedede
}

.mermaid .section2 {
    fill: #fff969
}

.mermaid .section1,.mermaid .section3 {
    fill: #fff;
    opacity: .2
}

.mermaid .sectionTitle0,.mermaid .sectionTitle1,.mermaid .sectionTitle2,.mermaid .sectionTitle3 {
    fill: #333
}

.mermaid .sectionTitle {
    text-anchor: start;
    font-size: 11px;
    text-height: 14px
}

.mermaid .grid .tick {
    stroke: #d3d3d3;
    opacity: .3;
    shape-rendering: crispEdges
}

.mermaid .grid path {
    stroke-width: 0
}

.mermaid .today {
    fill: none;
    stroke: red;
    stroke-width: 2px
}

.mermaid .task {
    stroke-width: 2
}

.mermaid .taskText {
    text-anchor: middle;
    font-size: 11px
}

.mermaid .taskTextOutsideRight {
    fill: #000;
    text-anchor: start;
    font-size: 11px
}

.mermaid .taskTextOutsideLeft {
    fill: #000;
    text-anchor: end;
    font-size: 11px
}

.mermaid .taskText0,.mermaid .taskText1,.mermaid .taskText2,.mermaid .taskText3 {
    fill: #fff
}

.mermaid .task0,.mermaid .task1,.mermaid .task2,.mermaid .task3 {
    fill: #52b565;
    stroke: #2c8a3e
}

.mermaid .taskTextOutside0,.mermaid .taskTextOutside1,.mermaid .taskTextOutside2,.mermaid .taskTextOutside3 {
    fill: #000
}

.mermaid .active0,.mermaid .active1,.mermaid .active2,.mermaid .active3 {
    fill: #a2e4af;
    stroke: #2c8a3e
}

.mermaid .activeText0,.mermaid .activeText1,.mermaid .activeText2,.mermaid .activeText3 {
    fill: #000!important
}

.mermaid .done0,.mermaid .done1,.mermaid .done2,.mermaid .done3 {
    stroke: #a8c1a8;
    fill: #f3fbf3;
    stroke-width: 1px
}

.mermaid .doneText0,.mermaid .doneText1,.mermaid .doneText2,.mermaid .doneText3 {
    fill: #000!important
}

.mermaid .crit0,.mermaid .crit1,.mermaid .crit2,.mermaid .crit3 {
    stroke: #d27a2f;
    fill: #ff8d4e;
    stroke-width: 2
}

.mermaid .activeCrit0,.mermaid .activeCrit1,.mermaid .activeCrit2,.mermaid .activeCrit3 {
    stroke: #ff8d45;
    fill: #ffdac3;
    stroke-width: 2
}

.mermaid .doneCrit0,.mermaid .doneCrit1,.mermaid .doneCrit2,.mermaid .doneCrit3 {
    stroke: #ff8d45;
    fill: #fff8f4;
    stroke-width: 2;
    cursor: pointer;
    shape-rendering: crispEdges
}

.mermaid .activeCritText0,.mermaid .activeCritText1,.mermaid .activeCritText2,.mermaid .activeCritText3,.mermaid .doneCritText0,.mermaid .doneCritText1,.mermaid .doneCritText2,.mermaid .doneCritText3 {
    fill: #000!important
}

.mermaid .titleText {
    text-anchor: middle;
    font-size: 18px;
    fill: #000
}

.mermaid .node text {
    font-family: trebuchet ms,verdana,arial;
    font-size: 14px
}

div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: trebuchet ms,verdana,arial;
    font-size: 12px;
    background: #ffffde;
    border: 1px solid #aa3;
    border-radius: 2px;
    pointer-events: none;
    z-index: 100
}

.graphviz {
    cursor: zoom-in
}

.graphviz svg {
    max-width: 100%
}

.file-download {
    line-height: 1.5;
    padding: 10px;
    display: table;
    border: 1px solid transparent;
    border-radius: 3px
}

.file-download:hover {
    border: 1px solid #e9e9e9
}

.file-download:hover .file-download-downclick,.file-download:hover .file-download-preview {
    visibility: visible
}

.file-download-typeicon {
    pointer-events: none;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    width: 20px;
    height: 24px;
    margin-right: 5px
}

.file-download-filename {
    margin-right: 10px
}

.file-download-preview {
    margin-right: 10px;
    visibility: hidden;
    transition: .3s
}

.file-download-preview span {
    color: #666
}

.file-download-downclick {
    visibility: hidden;
    transition: .3s
}

.file-download-downclick span {
    color: #666
}

.typo {
    position: relative;
    width: 100%;
    height: auto;
    min-height: 600px;
    word-wrap: break-word;
    color: var(--yq-text-primary);
    font-family: Chinese Quote,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: .05em;
    padding: 0 50px
}

.typo .typo-safe-word {
    color: var(--yq-red-6);
    margin-top: 4px;
    cursor: pointer
}

.typo h1.typo-title {
    font-size: 32px;
    font-family: Chinese Quote,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
    line-height: 40px;
    padding: 0;
    margin-bottom: 32px;
    word-break: break-word;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
    letter-spacing: .02em
}

.typo h1.typo-title .typo-title-warning-icon {
    font-size: 14px;
    color: var(--yq-yellow-6);
    vertical-align: middle;
    margin-left: 16px;
    margin-top: -4px;
    cursor: pointer
}

.typo>:first-child {
    margin-top: 0!important
}

.typo a,.typo a:active {
    word-wrap: break-word;
    color: var(--yq-text-link)
}

.typo a:hover {
    color: var(--yq-ant-link-hover-color)
}

.typo a:not([href]) {
    color: inherit;
    -webkit-text-decoration: none;
    text-decoration: none
}

.typo b,.typo strong {
    font-weight: 700
}

.typo span strong {
    color: inherit
}

.typo del,.typo del * {
    -webkit-text-decoration: line-through;
    text-decoration: line-through
}

.typo mark {
    background-color: #ff0;
    padding: 0
}

.typo input {
    margin: 0
}

.typo html input[disabled] {
    cursor: default
}

.typo input[type=checkbox] {
    box-sizing: border-box;
    padding: 0
}

.typo h1,.typo h2,.typo h3,.typo h4,.typo h5,.typo h6 {
    word-spacing: 1px;
    font-weight: 700
}

.typo h1 {
    font-size: 28px;
    line-height: 36px;
    padding: 7px 0
}

.typo h2 {
    font-size: 24px;
    line-height: 32px;
    padding: 7px 0
}

.typo h3 {
    font-size: 20px;
    line-height: 28px;
    padding: 7px 0
}

.typo h4 {
    font-size: 16px
}

.typo h4,.typo h5 {
    line-height: 24px;
    padding: 7px 0
}

.typo h5,.typo h6 {
    font-size: 14px
}

.typo h6 {
    line-height: 24px;
    padding: 7px 0;
    font-weight: 400
}

.typo blockquote,.typo dl,.typo h1,.typo h2,.typo h3,.typo h4,.typo h5,.typo h6,.typo hr,.typo ol,.typo p,.typo pre,.typo table,.typo ul {
    position: relative;
    margin-top: 24px;
    margin-bottom: 24px
}

.typo .select::-ms-expand {
    opacity: 0
}

.typo .octicon {
    font: normal normal normal 24px/1 Consolas,Liberation Mono,Menlo,Courier,monospace;
    display: inline-block;
    font-weight: lighter
}

.typo .octicon-link:before {
    content: "#"
}

.typo .anchor {
    position: absolute;
    left: -18px;
    top: 0;
    display: inline-block;
    padding-right: 0
}

.typo .anchor,.typo .anchor:hover {
    border-bottom: none
}

.typo .anchor:focus {
    outline: none
}

.typo h1 .octicon-link,.typo h2 .octicon-link,.typo h3 .octicon-link,.typo h4 .octicon-link,.typo h5 .octicon-link,.typo h6 .octicon-link {
    color: var(--yq-text-caption);
    vertical-align: middle;
    visibility: hidden
}

.typo h1:hover .anchor,.typo h2:hover .anchor,.typo h3:hover .anchor,.typo h4:hover .anchor,.typo h5:hover .anchor,.typo h6:hover .anchor {
    -webkit-text-decoration: none;
    text-decoration: none
}

.typo h1:hover .anchor .octicon-link,.typo h2:hover .anchor .octicon-link,.typo h3:hover .anchor .octicon-link,.typo h4:hover .anchor .octicon-link,.typo h5:hover .anchor .octicon-link,.typo h6:hover .anchor .octicon-link {
    visibility: visible
}

.typo ol,.typo ul {
    padding-left: 2em;
    text-indent: 0
}

.typo ol ol,.typo ol ul,.typo ul ol,.typo ul ul {
    margin-top: 0;
    margin-bottom: 0
}

.typo ul {
    list-style-type: disc
}

.typo ul ul {
    list-style-type: circle
}

.typo ul ul ul {
    list-style-type: square
}

.typo ul ul ol {
    list-style-type: lower-alpha
}

.typo ul ol {
    list-style-type: lower-roman
}

.typo ul ol ul {
    list-style-type: square
}

.typo ul ol ol {
    list-style-type: lower-alpha
}

.typo ol {
    list-style-type: decimal
}

.typo ol ol {
    list-style-type: lower-roman
}

.typo ol ol ul {
    list-style-type: square
}

.typo ol ol ol {
    list-style-type: lower-alpha
}

.typo ol ul {
    list-style-type: circle
}

.typo ol ul ul {
    list-style-type: square
}

.typo ol ul ol {
    list-style-type: lower-alpha
}

.typo li>p {
    margin-top: 0;
    margin-bottom: 0
}

.typo hr {
    position: relative;
    height: 2px;
    padding: 0;
    background-color: var(--yq-bg-primary-hover-light);
    border: none;
    margin-right: 0;
    margin-left: 0
}

.typo blockquote {
    padding: 0 0 0 1em;
    color: var(--yq-text-caption);
    border-left: 3px solid var(--yq-border-primary)
}

.typo blockquote:first-child {
    margin-top: 0
}

.typo blockquote:last-child {
    margin-bottom: 0
}

.typo code {
    position: relative;
    font-family: monospace;
    font-size: inherit;
    background-color: rgba(0,0,0,.06);
    padding: 0 2px;
    border: 1px solid rgba(0,0,0,.08);
    border-radius: 2px 2px;
    line-height: inherit;
    word-wrap: break-word;
    word-break: break-all;
    text-indent: 0
}

.typo kbd {
    position: relative;
    top: -1px;
    display: inline-block;
    line-height: 18px;
    padding: 0 4px;
    margin: 0 1px;
    border: 1px solid var(--yq-border-primary);
    border-bottom-color: var(--yq-yuque-grey-6);
    border-radius: 3px 3px;
    box-shadow: inset 0 -1px 0 var(--yq-yuque-grey-6)
}

.typo kbd,.typo pre {
    font-family: Consolas,Menlo,Courier,monospace;
    font-size: 13px
}

.typo pre {
    line-height: 21px;
    overflow: auto;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    margin: 5px 0;
    color: var(--yq-text-body);
    background: var(--yq-bg-secondary);
    border-radius: 2px 2px;
    border: 1px solid var(--yq-border-primary);
    padding: 16px 16px
}

.typo pre code {
    border: none;
    background: none;
    top: 0;
    margin: 0;
    padding: 0;
    font-size: inherit;
    line-height: inherit
}

.typo pre code:after,.typo pre code:before {
    content: normal
}

.typo video {
    width: 100%;
    height: auto
}

.typo img {
    border: none;
    border-radius: 2px 2px
}

.typo img:not(.emoji) {
    max-width: 100%;
    box-sizing: content-box;
    background-color: var(--yq-bg-primary);
    cursor: pointer
}

.typo figure {
    position: relative
}

.typo figcaption {
    position: relative;
    width: 100%;
    text-align: center;
    left: 0;
    margin-top: 0;
    font-weight: 400;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.typo table {
    table-layout: fixed;
    position: relative;
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    word-break: normal;
    word-break: keep-all;
    overflow: auto;
    white-space: normal
}

.typo table tr {
    background: var(--yq-bg-primary);
    border-top: 1px solid var(--yq-border-primary)
}

.typo table tr:nth-child(odd) {
    background-color: var(--yq-bg-secondary)
}

.typo table thead tr:nth-child(odd) {
    background-color: var(--yq-bg-tertiary)
}

.typo table td,.typo table th {
    padding: 6px 8px;
    border: 1px solid var(--yq-border-primary);
    white-space: normal;
    word-wrap: break-word
}

.typo table p {
    margin-top: 0;
    margin-bottom: 0
}

.typo .bi-table {
    width: 100%;
    margin-top: 24px;
    margin-bottom: 24px;
    overflow: auto
}

.typo .bi-table table {
    margin-top: 0;
    margin-bottom: 0
}

.typo .bi-table table tr {
    min-height: 34px
}

.typo .align-right {
    max-width: 36%;
    margin: 14px 0 14px 24px;
    float: right
}

.typo .align-left {
    max-width: 36%;
    margin: 14px 24px 14px 0;
    float: left
}

.typo .align-center {
    display: block;
    margin: 14px 0
}

.typo .task-list-item {
    list-style-type: none
}

.typo .task-list-item input {
    margin: 0 2px 0 -18px;
    vertical-align: middle
}

.typo :checked+.radio-label {
    z-index: 1;
    position: relative;
    border-color: var(--yq-blue-6)
}

.typo .footnotes {
    margin-top: 32px;
    padding-top: 14px;
    padding-bottom: 14px;
    border-top: 1px solid var(--yq-border-primary);
    font-size: 12px;
    color: var(--yq-text-body)
}

.typo .footnotes>li>p {
    margin: 0
}

.typo .alert {
    margin-top: 18px;
    margin-bottom: 18px;
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 2px 2px
}

.typo .alert p {
    margin: 0
}

.typo .alert-success {
    background-color: var(--yq-yuque-green-1);
    border-color: var(--yq-pea-green-1)
}

.typo .alert-info {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-cyan-2)
}

.typo .alert-warning {
    background-color: var(--yq-yellow-1);
    border-color: var(--yq-yellow-1)
}

.typo .alert-danger {
    background-color: var(--yq-red-1);
    border-color: var(--yq-red-2)
}

.typo [data-type=contain-block] {
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 2px 2px
}

.typo [data-type=contain-block][data-value=info] {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-cyan-2)
}

.typo [data-type=contain-block][data-value=danger] {
    background-color: var(--yq-red-1);
    border-color: var(--yq-red-2)
}

.typo [data-type=contain-block][data-value=warning] {
    background-color: var(--yq-yellow-1);
    border-color: var(--yq-yellow-1)
}

.typo [data-type=contain-block][data-value=success] {
    background-color: var(--yq-yuque-green-1);
    border-color: var(--yq-pea-green-1)
}

.typo [data-type=contain-block][data-value=tips] {
    background-color: var(--yq-yellow-2);
    border-color: var(--yq-yuque-grey-6)
}

.typo .emoji,.typo [data-type=emoji] {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: inherit;
    vertical-align: middle;
    background-size: contain;
    background-position: 0 0;
    background-repeat: no-repeat;
    margin-top: -3px;
    margin-left: 1px;
    margin-right: 1px
}

.typo [data-type=file] {
    position: relative;
    display: block;
    border-radius: 3px 3px;
    padding: 12px 14px 12px 46px;
    border: 1px solid var(--yq-border-primary);
    font-size: 14px;
    height: auto
}

.typo [data-type=file] .icon {
    position: absolute;
    left: 12px;
    top: 12px;
    width: 24px;
    height: 24px;
    font-size: 20px;
    text-align: center;
    line-height: 24px;
    color: var(--yq-text-caption);
    display: block;
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat;
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/FzhyAWEdgvDhnNkxjRqr.svg)
}

.typo [data-type=file] .icon-pdf {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/STpfVJwsRMXqbtsJMubu.svg)
}

.typo [data-type=file] .icon-xls,.typo [data-type=file] .icon-xlsx {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/PPlomOouVdpvoThfdAbJ.svg)
}

.typo [data-type=file] .icon-gzip,.typo [data-type=file] .icon-zip {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/ulKOptHdKPCuVPtOtbxd.svg)
}

.typo [data-type=file] .icon-psd {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/GZDSCUWoKLTTgcXHKlkT.svg)
}

.typo [data-type=file] .icon-rar {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/iYAySZgzdgtjAUShpysb.svg)
}

.typo [data-type=file] .icon-rp {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/tfzNsMpWIxSlidMCOrdo.svg)
}

.typo [data-type=file] .icon-doc,.typo [data-type=file] .icon-docx {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/mHAwzIfXYePwBupeHcEg.svg)
}

.typo [data-type=file] .icon-sketch {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/VYyVFKSsgTGHYjQmpzFt.svg)
}

.typo [data-type=file] .icon-png {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/yLmkZoijULIGxNKvsPYU.svg)
}

.typo [data-type=file] .icon-mov {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/AMTSLstcLPufUQYwLsfM.svg)
}

.typo [data-type=file] .icon-numbers {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/gsDMwRSoqadVLbQOxCum.svg)
}

.typo [data-type=file] .icon-ppt,.typo [data-type=file] .icon-pptx {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/HnSWhOcctnuiSBQVqiKU.svg)
}

.typo [data-type=file] .icon-mp4 {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/JBcWzVsJsUPYhcDmFPMo.svg)
}

.typo [data-type=file] .icon-gif {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/qsazRnPMjUhBQYPJTdea.svg)
}

.typo [data-type=file] .icon-ai {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/oKVqRsutRPbyJzLMUSkl.svg)
}

.typo [data-type=file] .icon-keynote {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/LxkoOiDRxGjcvpPBuUZU.svg)
}

.typo [data-type=file] .icon-jpeg {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/xGUpGYmgMjAOGiuIXxEG.svg)
}

.typo [data-type=file] .icon-md {
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/QqGSdjwuVWKaIazAlzvm.svg)
}

.typo [data-type=file] .name {
    display: inline-block;
    font-size: 14px;
    color: var(--yq-text-primary);
    line-height: 24px;
    font-weight: 400
}

.typo [data-type=file] .name:hover {
    border-bottom: 0
}

.typo [data-type=file] .size {
    display: inline-block;
    padding: 0 8px;
    line-height: 24px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.typo [data-type=file] .download,.typo [data-type=file] .preview {
    position: absolute;
    top: 50%;
    margin-top: -12px;
    right: 16px;
    width: 20px;
    height: 24px;
    text-indent: -9999em;
    text-align: center;
    overflow: hidden;
    color: var(--yq-text-body);
    visibility: hidden;
    opacity: 0
}

.typo [data-type=file] .download:before,.typo [data-type=file] .preview:before {
    text-indent: 0;
    font-family: larkicon,sans-serif!important;
    display: block
}

.typo [data-type=file] .download:hover,.typo [data-type=file] .preview:hover {
    border-bottom: none;
    color: var(--yq-text-primary)
}

.typo [data-type=file] .preview {
    right: 48px
}

.typo [data-type=file] .download:before {
    content: "\E63E"
}

.typo [data-type=file] .preview:before {
    content: "\E640"
}

.typo [data-type=file]:hover {
    border: 1px solid var(--yq-border-primary)
}

.typo [data-type=file]:hover .download,.typo [data-type=file]:hover .preview {
    opacity: 1;
    visibility: visible
}

.typo ol[data-type=ordered-list],.typo ol[data-type=task-list],.typo ol[data-type=unordered-list],.typo ul[data-type=ordered-lsit],.typo ul[data-type=task-list],.typo ul[data-type=unordered-list] {
    white-space: normal;
    margin: 0
}

.typo [data-type=ordered-list] [data-type=list-item][data-list-type=task-list],.typo [data-type=task-list] [data-type=list-item][data-list-type=task-list],.typo [data-type=unordered-list] [data-type=list-item][data-list-type=task-list] {
    list-style: none;
    position: relative;
    display: flex;
    padding: 0 10px;
    margin-left: -2em
}

.typo [data-type=ordered-list] [data-type=list-item] [data-type=list-item-checkbox],.typo [data-type=task-list] [data-type=list-item] [data-type=list-item-checkbox],.typo [data-type=unordered-list] [data-type=list-item] [data-type=list-item-checkbox] {
    position: relative;
    display: block;
    height: 24px;
    text-align: center;
    vertical-align: middle;
    line-height: inherit
}

.typo [data-type=ordered-list] [data-type=list-item] [data-type=list-item-inner],.typo [data-type=task-list] [data-type=list-item] [data-type=list-item-inner],.typo [data-type=unordered-list] [data-type=list-item] [data-type=list-item-inner] {
    flex: 1;
    padding-left: 8px
}

.typo [data-type=indent][data-value="0"] {
    text-indent: 2em
}

.typo [data-type=indent][data-value="1"] {
    padding-left: 4em
}

.typo [data-type=indent][data-value="2"] {
    padding-left: 6em
}

.typo [data-type=indent][data-value="3"] {
    padding-left: 8em
}

.typo [data-type=indent][data-value="4"] {
    padding-left: 10em
}

.typo [data-type=indent][data-value="5"] {
    padding-left: 12em
}

.typo [data-type=indent][data-value="6"] {
    padding-left: 14em
}

.typo [data-type=indent][data-value="7"] {
    padding-left: 16em
}

.typo [data-type=indent][data-value="8"] {
    padding-left: 18em
}

.typo [data-type=indent][data-value="9"] {
    padding-left: 20em
}

.typo [data-type=indent][data-value="10"] {
    padding-left: 22em
}

.typo [data-type=indent][data-value="11"] {
    padding-left: 24em
}

.typo [data-type=indent][data-value="12"] {
    padding-left: 26em
}

.typo .toc-tree {
    list-style: square
}

.typo .toc-tree .toc-level-1 {
    margin-left: 0
}

.typo .toc-tree .toc-level-2 {
    margin-left: 2em
}

.typo .toc-tree .toc-level-3 {
    margin-left: 4em
}

.typo .toc-tree .toc-level-4 {
    margin-left: 6em
}

.typo .toc-tree .toc-level-5 {
    margin-left: 8em
}

.typo [data-type=image] {
    position: relative;
    display: inline-block
}

.typo [data-type=image]>span {
    position: relative;
    order: 1;
    flex: 0 0 auto;
    max-width: 100%
}

.typo [data-type=math] {
    position: relative;
    display: inline-block
}

.typo [data-type=puml] {
    position: relative;
    display: block
}

.typo [data-display=inline] {
    display: inline-block;
    text-indent: 0
}

.typo [data-display=block] {
    display: block
}

.typo [data-display=block][data-align=center],.typo [data-display=block][data-align=left],.typo [data-display=block][data-align=right] {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start
}

.typo [data-display=block][data-align=right] {
    text-align: right;
    justify-content: flex-end
}

.typo [data-display=block][data-align=left] {
    text-align: left;
    justify-content: flex-start
}

.typo [data-display=block][data-align=center] {
    text-align: center;
    justify-content: center
}

.typo-yuque [data-type=file] .preview {
    display: none
}

.typo-bi blockquote,.typo-bi pre {
    margin-top: 5px;
    margin-bottom: 5px
}

.typo-bi hr {
    margin-top: 18px;
    margin-bottom: 18px
}

.typo-bi h1,.typo-bi h2,.typo-bi h3,.typo-bi h4,.typo-bi h5,.typo-bi h6 {
    margin-top: 0;
    margin-bottom: 0;
    white-space: pre-wrap
}

.typo-bi h1 .anchor,.typo-bi h2 .anchor,.typo-bi h3 .anchor,.typo-bi h4 .anchor,.typo-bi h5 .anchor,.typo-bi h6 .anchor {
    position: absolute;
    left: -30px;
    top: 7px;
    display: inline-block;
    padding-right: 0;
    -webkit-text-decoration: none;
    text-decoration: none;
    color: var(--yq-text-caption);
    visibility: hidden;
    line-height: inherit;
    font-weight: 400;
    width: 30px;
    text-align: center;
    border-bottom: 0;
    font-family: serif
}

.typo-bi h1 .anchor:after,.typo-bi h2 .anchor:after,.typo-bi h3 .anchor:after,.typo-bi h4 .anchor:after,.typo-bi h5 .anchor:after,.typo-bi h6 .anchor:after {
    content: "#"
}

.typo-bi h1 .anchor:focus,.typo-bi h2 .anchor:focus,.typo-bi h3 .anchor:focus,.typo-bi h4 .anchor:focus,.typo-bi h5 .anchor:focus,.typo-bi h6 .anchor:focus {
    outline: none
}

.typo-bi h1:hover .anchor,.typo-bi h2:hover .anchor,.typo-bi h3:hover .anchor,.typo-bi h4:hover .anchor,.typo-bi h5:hover .anchor,.typo-bi h6:hover .anchor {
    visibility: visible
}

.typo-bi h1:target .anchor,.typo-bi h2:target .anchor,.typo-bi h3:target .anchor,.typo-bi h4:target .anchor,.typo-bi h5:target .anchor,.typo-bi h6:target .anchor {
    color: var(--yq-yuque-green-6);
    visibility: visible
}

.typo-bi [data-type=p],.typo-bi p {
    min-height: 24px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 0;
    white-space: pre-wrap
}

.typo-bi-errorboundary {
    min-height: auto;
    transition: background .3s ease-in
}

.typo-bi-errorboundary:hover {
    background: var(--yq-bg-secondary)
}

.lark-table {
    position: relative;
    white-space: normal
}

.lark-table-trigger {
    width: 24px;
    left: -30px;
    z-index: 100;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    line-height: 24px;
    cursor: pointer;
    visibility: hidden;
    opacity: 0;
    transition: opacity .3s ease-in-out;
    border-bottom: 1px solid transparent
}

.lark-table-trigger,.lark-table-trigger:before {
    height: 100%;
    position: absolute;
    top: 0;
    display: inline-block
}

.lark-table-trigger:before {
    content: " ";
    width: 30px;
    z-index: 10;
    right: -10px
}

.lark-table-trigger:after {
    width: 24px;
    height: 24px;
    color: var(--yq-text-caption);
    display: block;
    font-family: larkicon,sans-serif!important;
    content: "\e95f";
    background: var(--yq-bg-tertiary);
    border-radius: 2px 2px
}

.lark-table-trigger:hover {
    border-bottom: 1px solid transparent!important
}

.lark-table-trigger:hover:after {
    color: var(--yq-theme)
}

.lark-table:hover .lark-table-trigger {
    visibility: visible;
    opacity: 1
}

.lark-table-zoom {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1024
}

.lark-table-zoom-head {
    position: fixed;
    opacity: 1;
    display: block;
    z-index: 1000;
    width: 100%;
    top: 0;
    height: 60px;
    transition: box-shadow .3s ease-in
}

.lark-table-zoom-head-active {
    box-shadow: 0 2px 5px rgba(0,0,0,.08)
}

.lark-table-zoom-body {
    width: 100%;
    max-width: 1400px;
    height: calc(100vh - 60px);
    overflow: hidden;
    margin-top: 59px;
    margin-left: auto;
    margin-right: auto
}

.lark-table-zoom-body .typo {
    min-height: auto;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.lark-table-zoom-body .typo-bi .typo-content {
    padding-bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.lark-table-zoom-body .typo-bi .typo-content .bi-table {
    width: 100%;
    height: 100%;
    margin-top: 0;
    margin-bottom: 0
}

.lark-table-zoom-body .typo-bi .typo-content table {
    cursor: inherit
}

.lark-table-zoom-body .typo-bi .typo-content table thead tr:nth-child(2n) {
    background: var(--yq-bg-primary)
}

.lark-table-zoom-body .typo-bi .typo-content table thead tr:nth-child(odd) {
    background: var(--yq-bg-secondary)
}

.lark-table-zoom-body .typo-bi .typo-content table thead tr:nth-child(odd) th {
    font-weight: 700
}

.lark-table-zoom-body .typo-bi .typo-content table tbody tr:nth-child(2n) td {
    background: var(--yq-bg-secondary)
}

.lark-table-zoom-body .typo-bi .typo-content table tbody tr:nth-child(odd) td {
    background: var(--yq-bg-primary)
}

.lark-table-zoom-back {
    opacity: 1;
    visibility: visible;
    position: absolute;
    bottom: 32px;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    color: var(--yq-white);
    max-width: 280px;
    padding: 7px 20px;
    line-height: 12px;
    font-size: 12px;
    transition: background .2s;
    border-radius: 3px;
    box-shadow: 0 0 5px 0 rgba(0,0,0,.1);
    align-items: center;
    background: rgba(0,0,0,.72);
    justify-content: space-between;
    z-index: 1024
}

.lark-table-zoom-back .cancel-btn {
    color: var(--yq-white);
    padding: 10px 15px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 3px;
    cursor: pointer
}

.lark-video {
    position: relative;
    width: 100%;
    height: 420px;
    background: var(--yq-bg-tertiary);
    border-radius: 2px 2px
}

.lark-video video {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: rotate(0deg) scale(1)
}

.note-status-module_dev_g73YZ {
    background-color: var(--yq-yuque-grey-6);
    padding: 0 4px;
    position: absolute;
    right: 0;
    top: 0;
    opacity: .5;
    pointer-events: none
}

.note-status-module_publicInfo_8wLHo {
    background: var(--yq-bg-secondary);
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-5);
    padding: 2px 4px;
    border-radius: 4px;
    margin-left: 8px;
    font-size: 12px;
    cursor: pointer
}

.note-status-module_text_ZLRRs {
    font-size: 12px;
    padding-left: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    color: var(--yq-text-body);
    background-color: var(--yq-bg-secondary);
    border-color: var(--yq-border-primary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border: 1px solid var(--yq-blue-1);
    color: var(--yq-blue-9)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    color: var(--yq-icon-secondary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-icon-primary)
}

.TagList-module_wrapper_wAu1a {
    padding: 12px 0 0 16px;
    position: relative;
    font-size: 0
}

.TagList-module_wrapper_wAu1a.TagList-module_hideMore_2Kx5n {
    height: 50px;
    overflow: hidden
}

.TagList-module_wrapper_wAu1a.TagList-module_expand_J-EtW {
    height: auto;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 0 0 4px 4px
}

.TagList-module_wrapper_wAu1a.TagList-module_expandForMini_G9K8m {
    height: 40px!important;
    width: auto;
    display: flex
}

.TagList-module_list_g-vtQ {
    margin-bottom: 0
}

.TagList-module_list_g-vtQ .TagList-module_line_pgNjs {
    margin-right: 8px;
    width: 1px;
    height: 16px;
    background-color: var(--yq-yuque-grey-4);
    display: inline-block;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 8px
}

.TagList-module_list_g-vtQ .TagList-module_addTag_P9XTi {
    display: inline-block;
    height: 30px;
    margin-bottom: 8px;
    vertical-align: middle
}

.TagList-module_list_g-vtQ .TagList-module_rightSide_65\+zS {
    float: right;
    display: flex
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    vertical-align: middle;
    display: inline-flex;
    max-width: 100%;
    border-radius: 15px;
    margin-bottom: 8px;
    height: 30px;
    color: var(--yq-text-body);
    padding: 0 8px;
    background-color: var(--yq-bg-primary);
    margin-right: 8px;
    border: 1px solid var(--yq-border-primary);
    justify-content: center;
    align-items: center
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg {
    margin-right: 4px
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg path {
    fill: currentColor
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8:hover {
    color: var(--yq-text-body)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border-color: var(--yq-blue-1);
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_canSelect_XVnT1 {
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ {
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    height: 30px;
    line-height: 30px
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ.TagList-module_highLight_Fx\+g3 {
    color: var(--yq-red-6)
}

.TagList-module_list_g-vtQ .TagList-module_count_JG6aO {
    margin-left: 4px;
    font-size: 14px;
    height: 30px;
    line-height: 28px
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    width: 18px;
    position: relative;
    display: inline-block;
    left: 8px;
    margin-right: 4px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 svg {
    height: 12px;
    width: 12px
}

.TagList-module_listForMini_n1Fjy {
    display: flex;
    width: 415px;
    overflow-x: auto;
    overflow-y: hidden
}

.TagList-module_expandBtn_kA52P {
    padding: 0 12px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.TagList-module_expandBtn_kA52P.TagList-module_expanded_tVFvP .TagList-module_icon_zLTbc {
    transform: rotate(-90deg)
}

.TagList-module_expandBtn_kA52P .TagList-module_icon_zLTbc {
    transform: rotate(90deg);
    font-size: 12px;
    transition: transform .3s ease-in
}

html[data-kumuhana=pouli] .index-module_toolBarBtn_kG8Cd,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_viewBlank_ecM7l,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J .ant-card-body {
    background: var(--yq-bg-secondary)
}

html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_tagPinned_--NI2 {
    color: var(--yq-theme)
}

.index-module_toolBarBtn_kG8Cd {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_descMoreIcon_wlBa3 {
    color: var(--yq-blue-4)
}

.index-module_wrapper_8ethE {
    min-height: calc(100vh - 178px)
}

.index-module_wrapper_8ethE .ne-editor-wrap-content {
    max-height: none!important
}

.index-module_wrapper_8ethE .ne-card-video [data-testid=controls] [data-testid=time] {
    display: none
}

.index-module_wrapper_8ethE .view-more-mask .yuque-doc-content:after {
    content: "";
    display: block;
    position: absolute;
    bottom: 7px;
    z-index: 1;
    height: 26px;
    width: 100%;
    background-image: linear-gradient(180deg,hsla(0,0%,100%,0),var(--yq-white))
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m {
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    align-items: center;
    margin-bottom: 16px
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m:hover {
    cursor: pointer;
    background: transparent!important
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowUp_lRGKc {
    color: var(--yq-yuque-grey-7);
    margin-left: 8px;
    transform: rotate(0deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowDown_3-EN3 {
    transform: rotate(-90deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj {
    margin-top: 16px
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj:hover {
    cursor: auto
}

.index-module_wrapper_8ethE .index-module_hidden_M8z9j {
    display: none
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 0 8px 0 0;
    top: 0;
    right: 0;
    overflow: hidden
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT .index-module_tagPinned_--NI2 {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--yq-yuque-green-2)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 {
    padding: 0 64px;
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItem_dVp5J {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItemExpand_PjrCj {
    padding-bottom: 64px
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .note-list-toolbar,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_leftToolBar_JMyy5,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_noteItem_dVp5J {
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_loading_uVMCS {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    text-align: center;
    padding-top: 80px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_mask_-nOYf {
    position: absolute;
    left: 0;
    top: 0;
    background-color: hsla(0,0%,100%,.8);
    height: 100%;
    width: 100%
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list {
    margin-top: 4px;
    padding: 0;
    background: transparent
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list li {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .ant-card-body .yuque-doc-content {
    min-height: 30px;
    padding-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewBlank_ecM7l,.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewMore_gsQbm {
    visibility: hidden
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J {
    padding: 20px 12px 8px;
    position: relative;
    box-shadow: 0 0 1px -1px rgba(0,0,0,.08),0 1px 2px 0 rgba(0,0,0,.04),0 2px 4px 1px rgba(0,0,0,.02);
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--yq-bg-primary);
    transition: box-shadow .2s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_searchMode_jG3fm {
    padding-left: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL {
    font-size: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    min-height: 24px;
    justify-content: space-between;
    position: relative;
    line-height: 24px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-checked:after,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-inner {
    border-radius: 50%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_radio_2HGaM {
    margin-right: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm {
    border: none;
    min-height: 40px;
    position: relative;
    transition: box-shadow .5s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .ant-card-body {
    padding: 8px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI {
    max-height: none!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content>div {
    max-height: none!important;
    overflow: auto!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content:after {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ {
    max-height: none;
    overflow: visible;
    background-color: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor {
    min-height: 30px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-editor-wrap-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-engine {
    min-height: 30px!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-editor-body {
    background-color: var(--yq-bg-secondary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-engine {
    min-height: 100vh!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor-wrapper {
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-tag-add {
    margin-bottom: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_hasTag_wtzsZ {
    max-height: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewBlank_ecM7l {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 16px;
    z-index: 9;
    background: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 8px 0 0 12px;
    cursor: pointer;
    z-index: 9;
    font-size: 12px;
    color: var(--yq-text-body)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm svg {
    position: relative;
    top: 2px;
    right: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMoreShow_pN6H7 {
    display: block
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view div[data-card-type=block] .lake-card-toolbar,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view span[data-card-type=inline] .lake-card-toolbar {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp {
    margin: 0 64px 12px;
    padding: 20px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_date_095hy {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 {
    display: flex;
    flex-wrap: wrap
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU {
    width: 80px;
    height: 80px;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU img {
    width: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU {
    margin-bottom: 8px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU a {
    margin-top: 8px;
    color: var(--yq-text-primary);
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_title_6RJyp {
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_desc_WTwbK {
    color: var(--yq-text-caption);
    font-size: 14px;
    margin-top: 8px;
    white-space: nowrap;
    word-break: break-all;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b {
    color: var(--yq-text-body);
    font-size: 12px;
    margin-top: 14px;
    display: flex;
    align-items: center;
    min-height: 18px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b img {
    width: 12px;
    height: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b .index-module_text_bro5m {
    margin-left: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_left_s-MXM {
    width: calc(100% - 140px);
    padding: 11px 18px 11px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX {
    width: 116px;
    height: 76px;
    border-radius: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX img {
    height: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU {
    margin-bottom: 4px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 10px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM {
    display: flex;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_name_fKT1c {
    color: var(--yq-text-primary);
    font-size: 14px;
    margin-left: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_size_xQNXJ {
    color: var(--yq-text-disable);
    padding-left: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_right_RJVuX a {
    color: #3f4b63
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    text-align: center;
    padding-top: calc(50vh - 200px);
    overflow: hidden;
    font-size: 14px;
    color: var(--yq-text-caption);
    margin-top: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k img {
    margin-bottom: 8px;
    width: 120px
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W {
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W,.index-module_wrapper_8ethE .index-module_theEndTips_tq\+NY {
    text-align: center;
    padding: 16px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_8ethE .index-module_blank_2HJdh {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 16px 0 16px;
    color: var(--yq-text-caption);
    text-align: center
}

.index-module_wrapper_8ethE.index-module_desktop_oahLD {
    min-height: calc(100vh - 96px)
}

.note-public-modal .ant-modal-body {
    padding: 0!important
}

.index-module_leftToolBar_JMyy5,.index-module_toolbarStyle_cNHWw {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out
}

.index-module_leftToolBar_JMyy5 {
    left: 0
}

.index-module_leftToolBar_JMyy5.index-module_batchProcess_BqhA4 {
    opacity: 1
}

.index-module_leftToolBar_JMyy5 .index-module_checkBox_mz\+g\+ {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    margin-left: 30px
}

.index-module_rightToolBar_e1gvN {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out;
    width: 64px;
    right: 0
}

.index-module_rightToolBar_e1gvN.index-module_batchProcess_BqhA4 {
    display: none
}

.index-module_rightToolBar_e1gvN .note-list-toolbar {
    opacity: 1
}

.index-module_rightToolBarCollapse_cPTfb {
    color: var(--yq-text-body);
    top: calc(100% - 70px)
}

.index-module_rightToolBarCollapse_cPTfb,.index-module_rightToolBarSave_vhDcz {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_rightToolBarSave_vhDcz {
    color: var(--yq-yuque-green-6);
    top: 0
}

.index-module_tagContainer_nVqVt {
    width: 1px;
    height: 1px;
    position: absolute;
    right: 0;
    top: 80px
}

.index-module_tagContainer_nVqVt .note-tag-popover .ant-popover-inner {
    margin-top: -170px
}

.search-module_wrapper_S445p {
    display: flex;
    align-items: center;
    position: relative
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl {
    width: 64px;
    opacity: 0
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_active_eYHT\+ {
    transition: width .15s ease;
    width: 200px;
    opacity: 1
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper {
    transition: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper-focused {
    border-color: none;
    box-shadow: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_isLiteMode_C4hZN {
    width: 100%
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h {
    font-size: 14px;
    text-align: right;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: -1px;
    padding: 6px 0 6px 4px;
    opacity: 0;
    pointer-events: none;
    min-width: 76px
}

.search-module_wrapper_S445p .search-module_searchMiniShow_xFcgm {
    transition: opacity .3s ease-in;
    opacity: 1;
    pointer-events: auto
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h .larkui-icon-help-search {
    color: var(--yq-icon-primary)!important;
    font-size: 16px;
    transform: translateY(2px)
}

.sidebar-toolbar-module_wrapper_oE4lW {
    padding: 0;
    cursor: pointer;
    transition: opacity .5s ease-in-out
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--yq-text-link)
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c svg {
    margin-right: 4px
}

.index-module_wrapper_kkqhF {
    overflow-x: hidden
}

.index-module_wrapper_kkqhF .note-list {
    height: calc(100vh - 320px);
    overflow-y: scroll;
    min-height: 0
}

.index-module_wrapper_kkqhF .note-list-item {
    padding: 0;
    margin-bottom: 12px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-yuque-grey-1);
    border-radius: 4px
}

.index-module_wrapper_kkqhF .note-item-normal {
    background: var(--yq-bg-secondary)
}

.index-module_wrapper_kkqhF .note-item {
    box-shadow: none!important
}

.index-module_wrapper_kkqhF .note-item,.index-module_wrapper_kkqhF .view-blank {
    background: var(--yq-bg-secondary)!important
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 {
    height: 77px;
    overflow-y: hidden;
    margin-top: 8px;
    display: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4.index-module_show_oAR0z {
    display: block
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch {
    height: auto
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transform: rotate(-180deg)
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD {
    cursor: pointer;
    margin-top: 2px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transition: transform .3s ease-in;
    position: relative;
    left: 5px;
    top: 3px
}

.index-module_wrapper_kkqhF .index-module_notesList_xu6a8 {
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B {
    padding: 0;
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B .note-tag-list {
    background-color: transparent!important;
    padding: 0
}

.material-library-view {
    width: 100%
}

.material-library-view .ant-tabs {
    height: 100%
}

.material-library-view .ant-tabs .ant-tabs-tab {
    padding-top: 0
}

.material-library-view .ant-tabs .ant-tabs-ink-bar {
    background: var(--yq-yuque-grey-9)
}

.material-library-view .ant-tabs .ant-tabs-content-holder {
    display: flex;
    flex: 1
}

.index-module_modal_BQbSj {
    height: 320px;
    width: 614px
}

.index-module_modal_BQbSj .ant-modal-content {
    overflow: hidden
}

.index-module_modal_BQbSj .ant-modal-body {
    padding: 0
}

.index-module_modal_BQbSj .ant-modal-confirm-content {
    margin-top: 0
}

.index-module_modal_BQbSj .ant-modal-confirm-btns {
    display: none
}

.index-module_loading_LY7-z {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 300px;
    padding: 80px 0;
    text-align: center;
    font-size: 24px;
    color: var(--yq-text-caption)
}

.index-module_content_GvanO,.index-module_header_\+SmFd {
    position: relative
}

.index-module_content_GvanO .yuque-doc-content {
    min-height: 600px
}

.index-module_reward_QafG4 {
    max-width: 850px;
    margin: 96px auto 0 auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_likeLayoutMode_L4kPJ {
    max-width: 750px;
    margin-left: auto
}

@media only print {
    .index-module_reward_QafG4 {
        display: none
    }
}

.index-module_lakeFallback_RSH7y {
    display: block;
    max-width: 750px;
    margin-left: auto;
    margin-right: auto
}

:root {
    --viewer-center-align-right: calc(50vw - 425px);
    --viewer-center-align-value: 0px;
    --viewer-center-table-placeholder-left-flex: 0;
    --viewer-center-left-max-offset: 0;
    --viewer-center-table-placeholder-right-flex: 0;
    --viewer-center-right-max-offset: 0
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-adapt .ne-viewer-body>*,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>* {
    margin-right: calc(50vw - 425px);
    margin-right: var(--viewer-center-align-right)
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole {
    width: 100%;
    width: calc(100% - var(--viewer-center-align-value))
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole.ne-full-width,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole.ne-full-width,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole.ne-full-width {
    width: 100%
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-root-card-hole.ne-full-width,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-table-hole.ne-full-width {
    display: flex;
    justify-content: space-between;
    width: 100%
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-root-card-hole.ne-full-width:before,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-table-hole.ne-full-width:before {
    content: "";
    display: inline;
    flex: 0;
    flex: var(--viewer-center-table-placeholder-left-flex);
    max-width: 0;
    max-width: var(--viewer-center-left-max-offset)
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-root-card-hole.ne-full-width:after,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body ne-table-hole.ne-full-width:after {
    content: "";
    display: inline;
    flex: 0;
    flex: var(--viewer-center-table-placeholder-right-flex);
    max-width: 0;
    max-width: var(--viewer-center-right-max-offset)
}

.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-adapt .ne-viewer-body>ne-alert-hole,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-adapt .ne-viewer-body>ne-container-hole,.article-content .ne-doc-major-viewer .ne-viewer-layout-mode-adapt .ne-viewer-body>ne-hole {
    max-width: 100%;
    max-width: calc(100% - var(--viewer-center-align-value))
}

@media only print {
    .ne-viewer-toc-sidebar {
        display: none!important
    }

    .ne-doc-major-viewer .ne-viewer-body>* {
        width: 100%!important;
        max-width: 100%!important;
        margin-right: auto!important
    }
}

@media only screen and (min-device-width: 768px) and (max-device-width:1024px) {
    #main-right-content {
        overflow-y:hidden
    }
}

.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-sidebar=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-sidebar=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-sidebar=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-sidebar=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-root-card-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-sidebar=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-table-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-root-card-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=true] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-table-hole.ne-full-width {
    width: calc(100% - 305px);
    margin-right: 305px
}

.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false]:not([data-doc-sidebar]) .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false]:not([data-doc-sidebar]) .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false]:not([data-doc-sidebar]) .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false]:not([data-doc-sidebar]) .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-root-card-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false]:not([data-doc-sidebar]) .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-table-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false][data-doc-sidebar=false] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-alert-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false][data-doc-sidebar=false] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-container-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false][data-doc-sidebar=false] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false][data-doc-sidebar=false] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-root-card-hole.ne-full-width,.DocReader-module_wrapper_t3Z8X[data-doc-layout=fixed][data-doc-toc=false][data-doc-sidebar=false] .article-content .ne-doc-major-viewer .ne-viewer-layout-mode-fixed .ne-viewer-body>ne-table-hole.ne-full-width {
    width: calc(100% - 39px);
    margin-right: 39px
}

.DocReader-module_wrapper_t3Z8X[data-doc-layout=adapt] .article-content-reward,.DocReader-module_wrapper_t3Z8X[data-doc-layout=adapt] .DocReader-module_comment_eDglS,.DocReader-module_wrapper_t3Z8X[data-doc-layout=adapt] .DocReader-module_header_xAOtU,.DocReader-module_wrapper_t3Z8X[data-doc-layout=adapt] .DocReader-module_info_yXA4e {
    max-width: 100%
}

.DocReader-module_wrapper_t3Z8X .article-content-reward,.DocReader-module_wrapper_t3Z8X .DocReader-module_comment_eDglS,.DocReader-module_wrapper_t3Z8X .DocReader-module_header_xAOtU,.DocReader-module_wrapper_t3Z8X .DocReader-module_info_yXA4e {
    margin-right: calc(50vw - 425px);
    margin-right: var(--viewer-center-align-right)
}

.DocReader-module_title_fXOQi {
    margin: 24px 0 32px
}

.DocReader-module_title_fXOQi>h1 {
    font-family: Chinese Quote,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji;
    font-size: 36px;
    line-height: 1.389;
    font-weight: 700;
    color: var(--yq-text-primary);
    display: inline;
    margin-right: 20px;
    font-feature-settings: none;
    font-variant-ligatures: none
}

.DocReader-module_title_fXOQi>h1:last-child {
    margin-right: 0
}

.DocReader-module_content_AcIMy:after {
    content: "placeholder for content width;placeholder for content width;placeholder for content width;placeholder for content width;placeholder for content width;placeholder for content width;placeholder for content width;placeholder for content width;";
    display: block;
    width: 100%;
    visibility: hidden;
    line-height: 0;
    z-index: -1
}

.DocReader-module_insetTitleBar_5p09z .ne-editor-header,.DocReader-module_insetTitleBar_5p09z .ne-viewer-header {
    padding-left: 32px
}

.DocReader-module_selectedBadge_C3ECZ {
    display: inline-block;
    vertical-align: top;
    height: 50px;
    line-height: 50px
}

.DocReader-module_selectedBadge_C3ECZ:before {
    content: attr(data-text);
    display: inline-block;
    background: rgba(0,185,107,.08) url(https://gw.alipayobjects.com/zos/basement_prod/bd4b79e9-63bb-40a9-9e85-9dcbf34d4bbc.svg) 10px no-repeat;
    color: var(--yq-yuque-green-6);
    height: 32px;
    line-height: 32px;
    padding: 0 10px 0 36px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600
}

.DocReader-module_meta_gZvM4 {
    display: flex;
    justify-content: space-between;
    margin-top: 28px;
    margin-bottom: 48px
}

.DocReader-module_comment_eDglS,.DocReader-module_header_xAOtU,.DocReader-module_info_yXA4e {
    max-width: 850px;
    padding: 0 50px;
    margin: 0 calc(50vw - 425px) 0 auto
}

.DocReader-module_tipWrap_j06P3 {
    display: flex;
    flex-wrap: wrap
}

.DocReader-module_tipWrap_j06P3>div {
    flex: 0 0 auto;
    margin-right: 8px;
    max-width: 100%
}

.DocReader-module_tipWrap_j06P3>div:last-of-type {
    margin-right: 0
}

@media only print {
    .DocReader-module_title_fXOQi>h1 {
        font-size: 20pt;
        line-height: 1.5
    }

    .DocReader-module_comment_eDglS,.DocReader-module_tipWrap_j06P3 {
        display: none
    }
}

@media only screen and (max-width: 575px) {
    .DocReader-module_title_fXOQi {
        margin-bottom:16px
    }

    .DocReader-module_title_fXOQi>h1 {
        font-size: 24px
    }

    .DocReader-module_meta_gZvM4 {
        margin-bottom: 32px
    }

    .DocReader-module_reward_CmZHM {
        margin-top: 40px
    }
}

.wm-wrapper {
    position: relative
}

.wm-wrapper .wm {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: .2;
    filter: gray;
    -webkit-filter: grayscale(100%);
    pointer-events: none
}
