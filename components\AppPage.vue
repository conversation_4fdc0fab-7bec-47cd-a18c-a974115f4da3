<script lang="ts" setup>
/**
 * @description 封装 App.useApp()
 * 避免在子组件中重复调用
 * 详见 https://antdv.com/components/app-cn
 */

import globalEventEmitter from '~/utils/eventBus';

const loading = ref(false);
const loadingText = ref('');

const { message, modal, notification } = App.useApp();

provide('message', message)
provide('modal', modal)
provide('notification', notification)
import { SHOW_MESSAGE,SHOW_MODAL,SHOW_GLOBAL_LOADING,HIDE_GLOBAL_LOADING } from '@/consts/globalEventConsts';
// 全局消息显示函数
// 用这种方式来实现消息显示是为了解决在非vue组件中通过message.error等调出的提示信息组件没有禁用ant-design-vue 自带样式，从而显示不正常的问题
const handleShowMessage = (payload: unknown) => {
    const typedPayload = payload as { type: 'success' | 'info' | 'error'; content: string; duration?: number };

    const type = typedPayload.type;
    const content = typedPayload.content;
    const duration = typedPayload.duration;

    if (type && content) {
        if (duration) {
            message[type](content, duration);
        } else {
            message[type](content);
        }
    }
};

// 全局对话框显示函数
const handleShowModal = (payload: unknown) => {
    const typedPayload = payload as { type: 'success' | 'info' | 'error' | 'warning' | 'confirm'; config: any };

    const type = typedPayload.type;
    const config = typedPayload.config;

    if (type && config) {
        modal[type](config);
    }
};

/**
 * 全局loading显示函数
 */
const handleShowLoading = (text: string) => {
    loading.value = true;
    loadingText.value = text || '';
};

// 全局loading隐藏函数
const handleHideLoading = () => {
    loading.value = false;
};

onMounted(() => {
    globalEventEmitter.on(SHOW_MESSAGE, handleShowMessage);
    globalEventEmitter.on(SHOW_MODAL, handleShowModal);
    globalEventEmitter.on(SHOW_GLOBAL_LOADING, handleShowLoading);
    globalEventEmitter.on(HIDE_GLOBAL_LOADING, handleHideLoading);
});

onUnmounted(() => {
    globalEventEmitter.off(SHOW_MESSAGE, handleShowMessage);
    globalEventEmitter.off(SHOW_MODAL, handleShowModal);
    globalEventEmitter.off(SHOW_GLOBAL_LOADING, handleShowLoading);
    globalEventEmitter.off(HIDE_GLOBAL_LOADING, handleHideLoading);
});
</script>

<template>
    <a-spin :spinning="loading" :tip="loadingText">
        <slot />
    </a-spin>
</template>
