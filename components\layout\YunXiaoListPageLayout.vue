<!-- 云效列表页布局组件-->
<template>
  <div class="yunxiao-list-page-layout">
    <!-- 上部栏：标题、筛选、批量操作和新建按钮 -->
    <div class="top-bar">
      <!-- 统一的标题区域 -->
      <div class="title-area">
        <!-- 默认标题显示 -->
        <h2 class="page-title" v-if="!hasActiveFilters">
          {{ showSidebarButton ? ruleSidebarRef?.selectedNodeName : title }}
        </h2>

        <!-- 搜索筛选条件标签 -->
        <div class="filter-tags" v-else>
          <div class="filter-tag-group">
            <template v-for="(value, field) in getActiveFilters()" :key="field">
              <a-tag class="filter-tag" closable @close="clearFilter(field)">
                {{ getFieldLabel(field) }} 包含 "{{ formatDisplayValue(field, value) }}"
              </a-tag>
            </template>
            <a class="clear-all-link" @click="clearAllFilters">清空</a>
          </div>
        </div>
      </div>

      <div class="search-area">
        <!-- 简单搜索 -->
        <div class="simple-search">
          <a-input
            v-model:value="simpleSearchValue"
            :placeholder="`输入${searchConfig.simpleSearchField.label}，回车搜索`"
            @search="handleSimpleSearch"
            @change="handleSimpleSearchChange"
            @keydown.enter="handleSimpleSearch"
            style="width: 220px"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>

          <!-- 高级筛选按钮 -->
          <a-popover
            v-model:open="advancedSearchVisible"
            trigger="click"
            placement="bottomRight"
            :overlay-style="{ width: '400px' }"
          >
            <template #content>
              <div class="advanced-search-panel">
                <a-form layout="horizontal" :model="formValue" :label-col="{ style: { width: '100px', textAlign: 'right' } }" @keypress.enter.prevent="handleAdvancedSearch">
                  <a-form-item
                    v-for="item in searchConfig.advancedSearchFields"
                    :key="item.field"
                    :label="`${item.label}：`"
                  >
                    <!-- 输入框 -->
                    <a-input
                      v-if="item.compType === 'input'"
                      v-model:value="formValue[item.field]"
                      :placeholder="`请输入${item.label}`"
                      @keydown.enter="handleAdvancedSearch"
                    />

                    <!-- 下拉选择框 -->
                    <a-select
                      v-else-if="item.compType === 'select'"
                      v-model:value="formValue[item.field]"
                      :placeholder="`请选择${item.label}`"
                      style="width: 100%"
                      :options="item.compConfig?.options?.map(option => ({
                        label: option.name,
                        value: option.value
                      })) || []"
                      :default-value="item.compConfig?.defaultValue"
                      :show-search="true"
                      :filter-option="(input: string, option: any) => {
                        return option?.label?.toString().toLowerCase().includes(input.toLowerCase())
                      }"
                      @change="(value) => item.compConfig?.onChange && item.compConfig.onChange(value)"
                      @keydown.enter="handleAdvancedSearch"
                    />

                    <!-- 日期选择器 -->
                    <a-date-picker
                      v-else-if="item.compType === 'datePicker'"
                      v-model:value="formValue[item.field]"
                      :placeholder="`请选择${item.label}`"
                      style="width: 100%"
                      valueFormat="YYYY-MM-DD 00:00:00"
                      :format="item.compConfig?.format || 'YYYY-MM-DD'"
                      :show-time="item.compConfig?.showTime"
                      @keydown.enter="handleAdvancedSearch"
                    />
                  </a-form-item>

                  <div class="form-actions" style="text-align: right">
                    <a-button @click="resetForm" style="margin-right: 8px">重置</a-button>
                    <a-button type="primary" @click="handleAdvancedSearch">筛选</a-button>
                  </div>
                </a-form>
              </div>
            </template>
            <a-button class="filter-button" style="padding: 0 8px;">
              <template #icon><filter-outlined :style="{ color: isFiltered ? '#1890ff' : undefined }" /></template>
              高级筛选
            </a-button>
          </a-popover>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-area">
          <!-- 批量操作下拉按钮 -->
          <a-dropdown :trigger="['click']" v-if="batchActions && batchActions.length > 0" :dropdown-style="{ width: '90px' }">
            <template #overlay>
              <a-menu style="width: 90px">
                <template v-for="action in batchActions">
                  <a-menu-item
                      :key="action.key"
                      @click="action.handler"
                      v-if="action.permission ? checkPermi([action.permission]) : true"
                  >
                    {{ action.label }}
                  </a-menu-item>
                </template>
              </a-menu>
            </template>
            <a-button class="batch-operation-btn" style="margin-right: 16px; min-width: 80px; padding: 0 8px !important;">
              批量操作 <down-outlined />
            </a-button>
          </a-dropdown>

          <!-- 自定义右侧按钮插槽 -->
          <slot name="rightButtons"></slot>
          
          <!-- 新建按钮 -->
          <div v-if="showAddButton" class="create-button-group" v-hasPermi="eventConfig.addPermission ? [eventConfig.addPermission] : []">
            <!-- 主新建按钮 - 保持原有功能 -->
            <a-button type="primary" @click="handleAddNew" :class="{'main-create-button': moreEvent && moreEvent.length > 0}">
              <template #icon><plus-outlined /></template>
              新建
            </a-button>

            <!-- 下拉菜单按钮 - 仅当有额外创建操作时显示 -->
            <a-dropdown v-if="moreEvent && moreEvent.length > 0" placement="bottomRight" :dropdown-style="{ minWidth: '110px' }">
              <template #overlay>
                <a-menu style="min-width: 110px">
                  <template v-for="event in moreEvent">
                  <a-menu-item
                    :key="event.key"
                    @click="event.handler"
                    v-if="event.permission ? checkPermi([event.permission]) : true"
                  >
                    {{ event.label }}
                  </a-menu-item>
                  </template>
                </a-menu>
              </template>
              <a-button type="primary" class="dropdown-create-button">
                <down-outlined />
              </a-button>
            </a-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 下部区域：表格内容 -->
    <div class="table-area" ref="basePoint">
      <slot name="table" 
        :formValue="formValue" 
        :scrollY="scrollY"
        :calculateIndex="calculateIndex">
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { SearchOutlined, FilterOutlined, PlusOutlined, DownOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
//权限判断
import { checkPermi } from "@/directive/permission/permission";
// 定义搜索字段接口
interface SearchField {
  label: string;
  field: string;
  compType?: 'input' | 'select' | 'datePicker';
  compConfig?: {
    options?: Array<{
      name: string;
      value: string | number;
    }>;
    format?: string;
    showTime?: boolean;
    defaultValue?: string | number;
    // 添加依赖关系配置，当该字段值变化时需要清空的其他字段
    clearFields?: string[];
    [key: string]: any;
  };
}

// 定义搜索配置接口
interface SearchConfig {
  simpleSearchField: SearchField;
  advancedSearchFields: SearchField[];
}

// 定义批量操作接口
interface BatchAction {
  key: string;
  label: string;
  handler: () => void;
  permission?: string;
}

// 定义创建操作接口
interface CreateAction {
  key: string;
  label: string;
  handler: () => void;
  permission?: string;
}

// 定义事件配置接口
interface EventConfig {
  searchEvent: (formValue: Record<string, any>) => void;
  addNewEvent: () => void;
  // 新增：用于处理表单值改变时的事件
  formHandler?: {
    // 表单对象引用
    form: any;
    // 表单字段映射，将searchConfig字段映射到实际form字段
    fieldMap?: Record<string, string>;
    // 日期字段特殊处理
    dateFields?: {
      startField?: string;
      endField?: string;
      startFormat?: string;
      endFormat?: string;
    };
    // 查询方法
    queryMethod: () => void;
  };
  // 新增按钮权限
  addPermission?: string;
}

// 定义组件属性
const props = defineProps({
  // 页面标题
  title: {
    type: String,
    required: true
  },
  // RuleSidebar组件引用
  ruleSidebarRef: {
    type: Object,
    default: null
  },
  // 搜索配置
  searchConfig: {
    type: Object as () => SearchConfig,
    required: true,
    default: () => ({
      // 简单搜索字段
      simpleSearchField: {
        label: '名称',
        field: 'name'
      },
      // 高级搜索字段
      advancedSearchFields: []
    })
  },
  // 事件配置
  eventConfig: {
    type: Object as () => EventConfig,
    required: true,
    default: () => ({
      // 搜索事件
      searchEvent: () => {},
      // 新建事件
      addNewEvent: () => {},
      // 表单处理器
      formHandler: {
        queryMethod: () => {}
      },
      // 新建按钮权限
      addPermission: {
          type: String,
          default: ''
      },
    })
  },
  // 批量操作配置
  batchActions: {
    type: Array as () => BatchAction[],
    default: () => []
  },
  // 创建操作配置
  moreEvent: {
    type: Array as () => CreateAction[],
    default: () => []
  },

  // 重新加载函数
  reloadFun: Function,
  // 是否显示新建按钮
  showAddButton: {
    type: Boolean,
    default: true
  },
  // 是否显示侧边栏按钮
  showSidebarButton: {
    type: Boolean,
    default: false
  },
})

// 简单搜索值
const simpleSearchValue = ref('')

// 高级搜索面板可见性
const advancedSearchVisible = ref(false)

// 添加一个新的状态变量，用于跟踪是否已执行搜索操作
const hasSearchedOrFiltered = ref(false)

// 表单值
const formValue = reactive<Record<string, any>>({})

// 添加一个新的状态，用于跟踪已提交的筛选条件
const submittedFilters = reactive<Record<string, any>>({})

// 初始化表单值
const initFormValue = () => {
  if (props.searchConfig.advancedSearchFields) {
    props.searchConfig.advancedSearchFields.forEach((field: SearchField) => {
      // 设置默认值（如果有）
      if (field.compType === 'select' && field.compConfig?.defaultValue !== undefined) {
        formValue[field.field] = field.compConfig.defaultValue;
      } else {
        formValue[field.field] = undefined;
      }
    })
  }
  // 初始化简单搜索字段
  formValue[props.searchConfig.simpleSearchField.field] = ''

  // 清空已提交筛选条件
  Object.keys(submittedFilters).forEach(key => {
    delete submittedFilters[key];
  });
}

// 组件挂载时初始化表单
initFormValue()

// 添加监听字段变化的逻辑，处理字段依赖关系
const setupFieldDependencies = () => {
  // 获取所有定义了clearFields的字段
  const fieldsWithDependencies = props.searchConfig.advancedSearchFields.filter(
    field => field.compConfig?.clearFields && field.compConfig.clearFields.length > 0
  );

  // 为每个字段添加监听
  fieldsWithDependencies.forEach(field => {
    watch(() => formValue[field.field], (newValue, oldValue) => {
      // 如果值发生变化且不为空，执行清空操作
      if (newValue !== oldValue && newValue !== undefined && newValue !== null && newValue !== '') {
        // 获取需要清空的字段
        const clearFields = field.compConfig?.clearFields || [];

        // 清空每个依赖字段
        clearFields.forEach(fieldName => {
          // 设置字段值为undefined
          formValue[fieldName] = undefined;

          // 如果有表单处理器，同步更新后台表单
          if (props.eventConfig.formHandler && props.eventConfig.formHandler.form && props.eventConfig.formHandler.form.value) {
            const formField = props.eventConfig.formHandler.fieldMap?.[fieldName] || fieldName;
            if (props.eventConfig.formHandler.form.value.hasOwnProperty(formField)) {
              props.eventConfig.formHandler.form.value[formField] = "";
            }
          }

        });
      }
    });
  });
};

// 在组件挂载时设置字段依赖关系监听
onMounted(() => {
  setupFieldDependencies();
});

// 监听高级筛选面板打开，同步主表单值
watch(() => advancedSearchVisible.value, (newVal) => {
  if (newVal && props.eventConfig.formHandler?.form?.value) {
    // 当面板打开时，同步主表单值到筛选表单
    const mainForm = props.eventConfig.formHandler.form.value;
    props.searchConfig.advancedSearchFields.forEach((field) => {
      const fieldName = field.field;
      // 映射字段名
      const formFieldName = props.eventConfig.formHandler.fieldMap?.[fieldName] || fieldName;
      if (mainForm[formFieldName] !== undefined) {
        formValue[fieldName] = mainForm[formFieldName];
      }
    });
  }
});

// 处理简单搜索变更
const handleSimpleSearchChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  formValue[props.searchConfig.simpleSearchField.field] = target.value
}

// 处理简单搜索
const handleSimpleSearch = async () => {
  // 将简单搜索值同步到表单值
  formValue[props.searchConfig.simpleSearchField.field] = simpleSearchValue.value

  // 更新已提交的筛选条件
  updateSubmittedFilters();

  // 设置已执行搜索标志
  hasSearchedOrFiltered.value = true

  try {
    // 如果提供了表单处理器，则使用表单处理器
    if (props.eventConfig.formHandler) {
      await new Promise<void>((resolve) => {
        handleFormValueChange(formatFormValues(formValue));
        // 使用setTimeout模拟异步等待后台方法完成
        setTimeout(() => {
          resolve();
        }, 100);
      });
    } else {
      // 否则调用原有的搜索事件
      props.eventConfig.searchEvent(formatFormValues(formValue));
      // 使用setTimeout模拟异步等待后台方法完成
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 100);
      });
    }
  } catch (error) {
    console.error('搜索出错:', error);
  }
}

// 处理高级搜索
const handleAdvancedSearch = async () => {
  // 同步高级搜索的值到简单搜索框
  if (formValue[props.searchConfig.simpleSearchField.field]) {
    simpleSearchValue.value = formValue[props.searchConfig.simpleSearchField.field]
  }

  // 更新已提交的筛选条件
  updateSubmittedFilters();

  try {
    // 如果提供了表单处理器，则使用表单处理器
    if (props.eventConfig.formHandler) {
      await new Promise<void>((resolve) => {
        handleFormValueChange(formatFormValues(formValue));
        // 使用setTimeout模拟异步等待后台方法完成
        setTimeout(() => {
          resolve();
        }, 100);
      });
    } else {
      // 否则调用原有的搜索事件
      props.eventConfig.searchEvent(formatFormValues(formValue));
      // 使用setTimeout模拟异步等待后台方法完成
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 100);
      });
    }
    // 后台方法完成后，关闭高级搜索面板
    advancedSearchVisible.value = false;
  } catch (error) {
    console.error('搜索出错:', error);
    // 即使出错也关闭面板
    advancedSearchVisible.value = false;
  }
}

// 更新已提交的筛选条件
const updateSubmittedFilters = () => {
  // 清空原有条件
  Object.keys(submittedFilters).forEach(key => {
    delete submittedFilters[key];
  });

  // 添加新条件
  Object.keys(formValue).forEach(key => {
    const value = formValue[key];
    // 检查值是否有效 - 不为空且不为undefined/null
    if (value !== undefined && value !== null && value !== '') {
      // 检查是否是默认值，若是select类型且值等于默认值，则不添加到筛选条件
      const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === key);
      const isDefaultValue = fieldConfig?.compType === 'select' &&
                           fieldConfig.compConfig?.defaultValue !== undefined &&
                           fieldConfig.compConfig.defaultValue === value;

      if (!isDefaultValue) {
        submittedFilters[key] = value;
      }
    }
  });

  // 设置已执行搜索标志
  hasSearchedOrFiltered.value = Object.keys(submittedFilters).length > 0;
}

// 格式化表单值，特别处理日期类型
const formatFormValues = (values: Record<string, any>) => {
  const formattedValues: Record<string, any> = {};

  // 只保留有效值
  Object.keys(values).forEach(key => {
    const value = values[key];
    if (value !== undefined && value !== null && value !== '') {
      // 检查是否是默认值，若是select类型且值等于默认值，则不添加
      const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === key);
      const isDefaultValue = fieldConfig?.compType === 'select' &&
                           fieldConfig.compConfig?.defaultValue !== undefined &&
                           fieldConfig.compConfig.defaultValue === value;

      if (!isDefaultValue) {
        formattedValues[key] = value;
      }
    }
  });

  return formattedValues;
}

// 重置表单
const resetForm = () => {
  initFormValue()
  simpleSearchValue.value = ''
  // 设置已执行搜索标志为false
  hasSearchedOrFiltered.value = false
  if (props.reloadFun) {
    props.reloadFun()
  }
  // 关闭高级搜索面板
    setTimeout(()=>{
        advancedSearchVisible.value = false
    },150)

}

// 处理新建
const handleAddNew = () => {
  props.eventConfig.addNewEvent()
}

// 处理设置常用
const handleSetAlwaysUsed = () => {
  if (props.ruleSidebarRef) {
    props.ruleSidebarRef.setAlwaysUsed();
  }
}

// 处理导入
const handleImportFun = () => {
  if (props.ruleSidebarRef) {
    props.ruleSidebarRef.importFun();
  }
}

// 判断是否有筛选条件
const isFiltered = computed(() => {
  return Object.values(formValue).some(value => {
    if (value === undefined || value === null || value === '') {
      return false
    }
    if (Array.isArray(value) && value.length === 0) {
      return false
    }
    return true
  })
})
// 计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);

// 新增的过滤相关逻辑
const getActiveFilters = () => {
  // 修改为使用已提交的筛选条件，而不是当前表单值
  return submittedFilters;
}

const getFieldLabel = (field: string) => {
  if (field === props.searchConfig.simpleSearchField.field) {
    return props.searchConfig.simpleSearchField.label
  }
  const fieldInfo = props.searchConfig.advancedSearchFields.find(f => f.field === field)
  return fieldInfo?.label || field
}

// 格式化显示值，根据不同类型显示不同格式
const formatDisplayValue = (field: string, value: any) => {
  // 查找字段定义
  const fieldInfo = props.searchConfig.advancedSearchFields.find(f => f.field === field)

  if (!fieldInfo) {
    return value
  }

  // 根据组件类型处理显示
  if (fieldInfo.compType === 'datePicker') {
    // 如果是日期，尝试格式化
    const format = fieldInfo.compConfig?.format || 'YYYY-MM-DD'
    // 移除时间部分，只显示日期
    return value.split(' ')[0]
  } else if (fieldInfo.compType === 'select') {
    // 如果是下拉选择，尝试显示label
    const option = fieldInfo.compConfig?.options?.find((opt: any) => opt.value === value)
    return option ? option.name : value
  }

  return value
}

// 修改清除过滤器函数，确保更好的同步
const clearFilter = async (field: string) => {
  // 清除表单值
  formValue[field] = undefined

  // 同时从已提交筛选条件中移除
  delete submittedFilters[field];

  // 如果是简单搜索字段，同时更新简单搜索框的值
  if (field === props.searchConfig.simpleSearchField.field) {
    simpleSearchValue.value = ''
  }

  // 查找字段配置，以便特殊处理
  const fieldConfig = props.searchConfig.advancedSearchFields.find(f => f.field === field)
  if (fieldConfig?.compType === 'select' && fieldConfig.compConfig?.defaultValue !== undefined) {
    // 如果是下拉选择且有默认值，恢复默认值
    formValue[field] = fieldConfig.compConfig.defaultValue
  }

  // 准备搜索条件 - 只包含有效的剩余条件
  const remainingFilters = Object.keys(submittedFilters).reduce((acc, key) => {
    // 只保留有效的条件
    if (submittedFilters[key] !== undefined && submittedFilters[key] !== null && submittedFilters[key] !== '') {
      acc[key] = submittedFilters[key];
    }
    return acc;
  }, {} as Record<string, any>);

  try {
    // 如果提供了表单处理器，则使用表单处理器
    if (props.eventConfig.formHandler) {
      await new Promise<void>((resolve) => {
        handleFormValueChange(remainingFilters);
        // 使用setTimeout模拟异步等待后台方法完成
        setTimeout(() => {
          resolve();
        }, 100);
      });
    } else {
      // 否则使用搜索事件
      props.eventConfig.searchEvent(remainingFilters);
      // 使用setTimeout模拟异步等待后台方法完成
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 100);
      });
    }

    // 如果没有过滤条件了，则重置搜索状态
    if (Object.keys(submittedFilters).length === 0) {
      hasSearchedOrFiltered.value = false
    }
  } catch (error) {
    console.error('清除过滤器出错:', error);
  }
}

// 修改计算属性，只有在有过滤条件且执行过搜索时才返回true
const hasActiveFilters = computed(() => {
  return hasSearchedOrFiltered.value && Object.keys(getActiveFilters()).length > 0
})

// 监听formValue中的simpleSearchField值变化，同步到simpleSearchValue
watch(() => formValue[props.searchConfig.simpleSearchField.field], (newValue) => {
  // 避免循环引用，检查值是否不同才更新
  if (newValue !== simpleSearchValue.value) {
    simpleSearchValue.value = newValue || '';
  }
});

// 监听simpleSearchValue变化，同步到formValue
watch(simpleSearchValue, (newValue) => {
  // 避免循环引用，检查值是否不同才更新
  if (newValue !== formValue[props.searchConfig.simpleSearchField.field]) {
    formValue[props.searchConfig.simpleSearchField.field] = newValue;
  }
});

// 处理清除所有过滤器
const clearAllFilters = async () => {
  // 重置表单所有字段
  initFormValue();
  // 清空简单搜索值
  simpleSearchValue.value = '';
  // 设置搜索状态为false
  hasSearchedOrFiltered.value = false;

  try {
    // 如果提供了表单处理器，则重置表单并调用查询方法
    if (props.eventConfig.formHandler) {
      // 清空实际表单值
      const form = props.eventConfig.formHandler.form;
      if (form && form.value) {
        Object.keys(form.value).forEach(key => {
          form.value[key] = "";
        });
      }

      const queryMethod = props.eventConfig.formHandler.queryMethod;
      if (typeof queryMethod === 'function') {
        await new Promise<void>((resolve) => {
          // 调用查询方法
          queryMethod();
          // 使用setTimeout模拟异步等待后台方法完成
          setTimeout(() => {
            resolve();
          }, 100);
        });
      }
    } else if (props.reloadFun && typeof props.reloadFun === 'function') {
      // 没有提供表单处理器，调用重新加载函数
      await new Promise<void>((resolve) => {
        // 再次检查确保函数存在
        if (props.reloadFun) {
          props.reloadFun();
        }
        // 使用setTimeout模拟异步等待后台方法完成
        setTimeout(() => {
          resolve();
        }, 100);
      });
    }

    // 关闭高级搜索面板
    advancedSearchVisible.value = false;
  } catch (error) {
    console.error('清除所有过滤器出错:', error);
    // 即使出错也关闭面板
    advancedSearchVisible.value = false;
  }
};

// 新增：处理表单值变更 - 统一处理表单值同步和查询调用
const handleFormValueChange = (formValues: Record<string, any>) => {
  if (!props.eventConfig.formHandler) return;

  const { form, fieldMap = {}, dateFields, queryMethod } = props.eventConfig.formHandler;

  if (form && form.value) {
    // 先重置表单所有字段为空值
    Object.keys(form.value).forEach(key => {
      form.value[key] = "";
    });

    // 遍历处理表单值
    Object.keys(formValues).forEach(key => {
      if (formValues[key] === undefined || formValues[key] === null || formValues[key] === '') {
        return; // 跳过空值
      }

      // 获取实际的表单字段名（使用映射表或原始字段名）
      const formField = fieldMap[key] || key;

      // 检查是否是表单字段
      if (!form.value.hasOwnProperty(formField)) {
        return; // 不是表单字段，跳过
      }

      // 特殊处理日期字段
      if (dateFields &&
          ((dateFields.startField && formField === dateFields.startField) ||
           (dateFields.endField && formField === dateFields.endField))) {

        const isStartField = formField === dateFields.startField;
        const format = isStartField ? (dateFields.startFormat || ' 00:00:00') : (dateFields.endFormat || ' 23:59:59');

        // 处理日期格式
        form.value[formField] = typeof formValues[key] === 'string' ?
          formValues[key] :
          `${formValues[key]}${format}`;
      } else {
        // 常规字段处理
        form.value[formField] = formValues[key];
      }
    });

    // 调用查询方法
    queryMethod();
  }
};

// 表格序号计算函数，用于列表页引用
const calculateIndex = (index, pagination) => {
  if (!pagination) return index + 1;
  return (pagination.current - 1) * pagination.pageSize + index + 1;
};

// 暴露方法给父组件
defineExpose({
  resetForm,
  clearAllFilters,
  handleFormValueChange,
  calculateIndex
})
</script>

<style lang="scss" scoped>
.yunxiao-list-page-layout {
  display: flex;
  flex-direction: column;
  height: 100%;

  .top-bar {
    background-color: #fff;
    padding: 16px 16px;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-area {
      display: flex;
      align-items: center;
      gap: 8px;  // 添加间距
      .page-title {
        color: #555;
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
      .vertical-divider {
        height: 16px;
        width: 1px;
        background-color: #d0cece;
        margin: 0 4px;
      }

      .filter-tags {
        display: flex;
        align-items: center;

        .filter-tag-group {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;

          .filter-tag {
            margin-right: 0;
            background-color: rgb(246, 247, 249);  // 更改背景颜色为浅蓝色
            border-color: rgba(0, 0, 0, 0);      // 更改边框颜色
            color: rgb(125, 128, 137);            // 设置字体颜色为蓝色
            border-radius: 2px;        // 设置四个角的弧度
          }

          .clear-all-link {
            color: rgb(0, 106, 212);
            cursor: pointer;
            margin-left: 8px;
            font-size: 14px;

            &:hover {
              color: #40a9ff;
            }
          }
        }
      }
    }

    .search-area {
      display: flex;
      align-items: center;

      .simple-search {
        display: flex;
        align-items: center;
        margin-right: 16px;

        .filter-button {
          margin-left: 8px;
          padding: 0 8px;
        }
      }
    }
  }

  .table-area {
    flex: 1;
    background-color: #fff;
    padding: 0 16px 16px 16px;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .advanced-search-panel {
    padding: 8px;

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;

      button {
        margin-left: 8px;
      }
    }
  }

  .create-button-group {
    display: inline-flex;

    .main-create-button {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .dropdown-create-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: 1px solid rgba(255, 255, 255, 0.5);
      padding: 0 8px;
    }
  }

  .action-area {
    .ant-btn {
      &.batch-operation-btn {
        padding: 0 8px !important;
      }
    }
  }
}
</style>
