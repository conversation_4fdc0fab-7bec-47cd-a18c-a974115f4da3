import * as util from "@/components/RuleEditor/editor/rule/util";

export const COLTYPE = {
  conditions: 'conditons',
  action: 'action',
};

export const CONDITION = 'condition';

export const ACTION = 'action';

const initConditionData = {
  variable: {
    valueType: 'String',
    variableType: 'dataEntry',
  },
  leftValueType: 'String',
};

// const initAction = {
//   actionType: 'setValue',
//   actionParams: [
//     {
//       variableType: 'field',
//       valueType: 'String',
//       value: [],
//     },
//     {
//       variableType: 'constant',
//       valueType: 'String',
//       value: '',
//     },
//   ],
// };
const initAction = {
  actionType: "invokeMethod",
  actionParams: [
    {
      variableType: "field",
      valueType: "String",
      viewName: '',
      value: []
    },
  ],
};

export const initRuleData = {
  conditions: [initConditionData],
  conditionExpression: '#',
  actions: [initAction],
};

export const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 10 },
};

export const _conditionData = util.getConditionTree(
  initRuleData.conditionExpression,
  initRuleData.conditions,
);

export const _actionData = util.getActionData(
  initRuleData.actions,
);

// export const _conditionData = {
//   conditions: [initConditionData],
//   conditionExpression: "#",
// };

// export const _actionData = {
//   actions: [initAction],
// };



