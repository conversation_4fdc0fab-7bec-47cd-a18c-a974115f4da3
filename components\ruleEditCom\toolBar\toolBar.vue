<template>
  <div class="toolBar">
    <a-tabs :activeKey="viewStatus" size="small" @change="changeViewStatus">
      <a-tab-pane tab="预定义" key="predefineView"></a-tab-pane>
      <a-tab-pane tab="规则内容" key="designView"></a-tab-pane>
      <a-tab-pane tab="DRL" key="codeView" v-if="!isTemplate"></a-tab-pane>
      <a-tab-pane tab="规则文本" key="ruleTextView"></a-tab-pane>
      <template #rightExtra>
        <a-tooltip placement="bottom" :title="'全屏编辑'" :open="tooltipVisible.fullscreen">
          <a-button
                  size="small"
                  @click="fullScreen"
                  v-if="showFullScreen && !props.isFullscreen"
                  @mouseenter="tooltipVisible.fullscreen = true"
                  @mouseleave="tooltipVisible.fullscreen = false"
          >
            <template #icon>
              <FullscreenOutlined />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" title="AI帮你写">
          <a-button
            size="small"
            @click="showAIWriter"
            v-show="(ruleData && ruleData.conditionExpression === '')||(ruleData.conditionExpression === '#'&&ruleData.conditions.length > 0&&!ruleData.conditions[0].variable.name)"
          >
            <template #icon>
              <IconAI :size="16" />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" title="从模板新建">
          <a-button
            size="small"
            @click="showTemplateCenter"
            v-show="(ruleData && ruleData.conditionExpression === '')||(ruleData.conditionExpression === '#'&&ruleData.conditions.length > 0&&!ruleData.conditions[0].variable.name)"
          >
            <template #icon>
              <IconTemplateCenter :size="16" />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" title="添加为模板">
          <a-button
            size="small"
            @click="addTemplate"
            v-show="ruleData.conditionExpression !== ''&&ruleData.conditions.length > 0&&ruleData.conditions[0].variable.name && !isTemplate"
          >
            <template #icon>
              <IconAddTemplate :size="16" />
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light">
          <template #title>后退</template>
          <a-button
            v-show="viewStatus !== 'predefineView' && lockedInner && !isTemplate"
            size="small"
            @click="undo"
          >
            <template #icon><ArrowLeftOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light">
          <template #title>前进</template>
          <a-button
            v-show="
              viewStatus !== 'predefineView' && mode !== 'track' && lockedInner && !isTemplate
            "
            size="small"
            @click="redo"
          >
            <template #icon><ArrowRightOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light" v-if="lockedInner && !isTemplate">
          <template #title>锁定</template>
          <a-button size="small" @click="changeLockInner">
            <template #icon><LockOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip placement="bottom" effect="light" v-else>
          <template #title>解锁</template>
          <a-button size="small" @click="changeLockInner" v-if="!isTemplate">
            <template #icon><UnlockOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-button
          v-if="mode !== 'track' && lockedInner && !isTemplate"
          @click="validate"
          size="small"
          >验证</a-button
        >
        <a-button
          v-if="lockedInner && !isTemplate"
          type="primary"
          size="small"
          @click="toSave"
          >保存</a-button
        >
        <a-button
          v-if="lockedInner && !isTemplate && checkPermi([RULE_PERMISSION.RULE.SUBMIT])"
          type="primary"
          size="small"
          @click="handleSubmitRule()"
        >提交</a-button>
      </template>
    </a-tabs>

    <!-- 添加模板对话框 -->
    <TemplateModal
      v-if="templateModalVisible"
      :open="templateModalVisible"
      @cancel="templateModalVisible = false"
      @confirm="saveTemplate"
    />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import TemplateCenter from '~/components/TemplateCenter.vue'
import AIWriter from '~/components/AIWriter.vue'
import type { Key } from 'ant-design-vue/es/table/interface'
import { saveRuleTemplate } from '@/api/dashboardApi'
import TemplateModal from '~/components/TemplateModal.vue'
import { getUnid } from '@/utils/auth'
import { FullscreenOutlined, FullscreenExitOutlined, ArrowLeftOutlined, ArrowRightOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons-vue'
import globalEventEmitter from '~/utils/eventBus';
import { RULE_SUBMIT_ACTION } from '@/consts/globalEventConsts';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import { checkPermi } from "@/directive/permission/permission";
import qs from "qs/lib/index";
import { submitRule, getRuleByUuid } from '@/api/rule_base'
interface Props {
  mode?: string
  viewStatus?: string
  ruleAttributes?: Record<string, any>
  lockedInner?: boolean,
  eng_uuid: string,
  ruleData: any,
  packageNameAll: string,
  ruleName: string,
  type: string,
  uuid: string,
  isTemplate?: boolean
  showFullScreen?: boolean
  isFullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: '',
  viewStatus: '',
  ruleAttributes: () => ({}),
  lockedInner: false,
  eng_uuid: '',
  ruleData: {},
  packageNameAll: '',
  ruleName: '',
  type: '',
  uuid: '',
  isTemplate: false,
  isFullscreen: false,
})
const emit = defineEmits(['undo', 'redo', 'toSave', 'validate', 'changeLockInner', 'changeViewStatus', 'updateRuleContent', 'replaceItemSure','fullScreen'])

const message = inject<any>('message')
const modal = inject<any>('modal')

// data
const tooltipVisible = ref({
  fullscreen: false
})

const optionList = ref([
  { value: 'true', viewName: '是' },
  { value: 'false', viewName: '否' }
])

// 添加模板对话框的控制变量
const templateModalVisible = ref(false);

const fieldList = ref([
  [
    {
      type: 'TEXT',
      key: 'ruleName',
      label: '规则名',
      initValue: 'ruleFile',
      offset: 1,
      span: 22
    }
  ],
  [
    {
      type: 'NUMBER',
      key: 'salience',
      label: '优先级',
      initValue: 0,
      offset: 1,
      span: 22
    }
  ],
  [
    {
      type: 'RADIO',
      key: 'enabled',
      label: '是否启用',
      initValue: 'true',
      offset: 1,
      span: 22,
      optionList: optionList.value
    }
  ],
  [
    {
      type: 'TEXT',
      key: 'timer',
      label: '执行时间',
      placeholder: '格式：YYYY-MM-DD',
      offset: 1,
      span: 22,
      optionList: optionList.value
    }
  ],
  [
    {
      type: 'DATE',
      key: 'date-effective',
      label: '生效日期',
      offset: 1,
      span: 22,
      optionList: optionList.value
    }
  ],
  [
    {
      type: 'DATE',
      key: 'date-expires',
      label: '失效日期',
      offset: 1,
      span: 22,
      optionList: optionList.value
    }
  ]
])

// computed
const _ruleAttributes = computed(() => {
  const data = { ...props.ruleAttributes }
  if (data['date-effective']) {
    data['date-effective'] = dayjs(data['date-effective'])
  }
  if (data['date-expires']) {
    data['date-expires'] = dayjs(data['date-expires'])
  }
  return data
})

// methods
const undo = () => emit('undo')
const redo = () => emit('redo')
const toSave = () => emit('toSave')
const validate = () => emit('validate')
const changeLockInner = () => emit('changeLockInner')
const changeViewStatus = (key: Key) => emit('changeViewStatus', key)

const fullScreen = () => {
  tooltipVisible.value.fullscreen = false
  emit('fullScreen')
}

/**
 * 显示模板中心
 */
function showTemplateCenter() {
  let modalInstance: any;

  const config = {
    footer: null,
    icon: null,
    closable: true,
    centered: true,
    width: 1140,
    class: 'styles-module_modal_8nT+9',
    content: h(TemplateCenter, {
      modal: {
        destroy: () => modalInstance?.destroy()
      },
      message,
      eng_uuid: props.eng_uuid,
      type: props.type,
      onUpdateRuleContent: (content: Record<string, any>) => {
        emit('updateRuleContent', content,'template');
        modalInstance?.destroy();
      }
    }),
  };

  modalInstance = modal.confirm(config);
}

/**
 * 显示AI帮你写
 */
function showAIWriter() {
  let modalInstance: any;
  const config = {
    footer: null,
    icon: null,
    closable: true,
    centered: true,
    width: '76vw',
    class: 'styles-module_modal_8nT+9 ai-doc-modal',
    content: h(AIWriter, {
      engUuid: props.eng_uuid,
      loginId: getUnid(),
      ruleId: props.uuid,
      onShowErrorMsg: (msg: string) => message.error(msg),
      onClose: () => modalInstance?.destroy(),
      onLoadingChange: (loading: boolean) => updateModalConfig(loading),
      onSuccess: (ruleContent: any) => {
        emit('updateRuleContent', ruleContent);
        modalInstance?.destroy();
      },
      onApplyResult: (ruleContent: any) => {
        emit('replaceItemSure', ruleContent);
        modalInstance?.destroy();
      }
    })
  };

  // 创建一个方法来更新modal的配置
  const updateModalConfig = (loading: boolean) => {
    if (modalInstance) {
      modalInstance.update({
        ...config,
        closable: !loading,
        keyboard: !loading,
        maskClosable: !loading,
      });
    }
  };

  modalInstance = modal.confirm(config);
}

/**
 * 显示模板信息弹窗
 */
function addTemplate() {
  templateModalVisible.value = true;
}

function saveTemplate(templateName: string) {
  saveRuleTemplate({
    templateName,
    ruleType: props.type,
    engUuid: props.eng_uuid,
    jsonContentObject: props.ruleData
  }).then((res) => {
    const templateId = res.data
    if (templateId) {
      message.success('添加为模板成功');
      templateModalVisible.value = false;
    }
  }).catch((error) => {
    console.error('添加模板失败：' + (error.message || '未知错误'));
  });
}
const urlFlag = ref(true);
// 监听
watch(() => window.location.href, (newUrl) => {
  urlFlag.value = !newUrl.includes('ruleContent');
}, {
  immediate: true
});
// 提交数据前状态判断
function checkSubmitStatus(ruleInfo, type) {
  let uuids = '';
  //状态判断
  let status = '';

  if (ruleInfo && ruleInfo.uuid) {
    uuids = ruleInfo.uuid;
    if (ruleInfo.status === '已删除') {
      status = ruleInfo.status;
    }
    //锁定/解锁数据不可再次提交
    if ((ruleInfo.lockId && type === '已锁定') || (!ruleInfo.lockId && type === '已解锁')) {
      status = type;
    }
  }
  return [uuids, status];
}
// 添加一个防抖变量
const isSubmitting = ref(false);

function handleSubmitRule() {
  // 1. 先检查是否正在提交
  if(isSubmitting.value) {
    return;
  }

  // 2. 设置提交状态
  isSubmitting.value = true;

  try {
    if(urlFlag.value) {
      // 3. 检查必要的属性是否存在
      if (!props.uuid) {
        console.warn('[DEBUG] 提交失败：缺少必要的uuid属性');
        return;
      }

      // 4. 构建事件数据
      const eventData = {
        record: {
          uuid: props.uuid,
          status: props.status || '',  // 提供默认值
          ruleName: props.ruleName || props.ruleTitle || '' // 使用可用的名称属性
        },
        tabKey: '编辑' + props.uuid
      };


      // 5. 使用Promise包装事件发送
      Promise.resolve().then(() => {
        globalEventEmitter.emit(RULE_SUBMIT_ACTION, eventData);
      }).catch(error => {
        console.error('[DEBUG] 事件发送失败：', error);
      });

    } else {
      // 先获取最新的规则信息，然后再进行状态检查和提交
      getRuleByUuid({
        uuids: props.uuid,
      }).then((res) => {
        if (res.code === 20000) {
          const ruleData = res.data;

          // 检查状态
          let [uuids, status] = checkSubmitStatus(ruleData, '');

          if (status) {
            message.error('状态为' + status + '规则不可提交');
            return;
          }

          if (uuids) {
            let pars = qs.stringify({
              uuids: uuids,
            });

            submitRule(pars).then((res) => {
              if (res.code === 20000) {
                if (res.data == '规则置为提交待审核成功！') {
                  message.success('规则置为提交待审核成功，不允许操作，即将关闭页面！');
                  // 添加2秒延时，让用户有时间看到提示信息
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                } else {
                  message.success(res.data);
                }
              } else {
                message.error(res.data);
              }
            });
          }
        }
      });
    }
  } catch (error) {
    console.error('[DEBUG] 提交过程发生错误：', error);
  } finally {
    // 6. 确保状态重置
    setTimeout(() => {
      isSubmitting.value = false;
    }, 1000);
  }
}
</script>

<style scoped>
.tab-btn {
  margin-right: 5px;
}

/* 按钮大小调整 */
:deep(.ant-tabs-extra-content .ant-btn) {
  transform: scale(1.2);
  margin: 0 8px;
  font-size: 12px;
}

/* 为第一个按钮添加左边距 */
:deep(.ant-tabs-extra-content .ant-btn:first-child) {
  margin-left: 4px;
}

/* 为最后一个按钮添加右边距 */
:deep(.ant-tabs-extra-content .ant-btn:last-child) {
  margin-right: 4px;
}
</style>
