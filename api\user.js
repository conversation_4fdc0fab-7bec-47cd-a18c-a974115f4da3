import request from '@/utils/request'
// 登录
export function login(data, flag,code) {
  let urlObj = {
    'erule': 'login',
    'uaa': 'uaa/login',
    'singleYC':'loginSingleSignYC?code='+code,
    'wxQRCodeGSC':'wxQRCodeLoginGSC?code='+code,
  }
  return request({
    url: urlObj[flag],
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    },
  })
}
// 是否初始化
export function getisInt() {
  return request({
    url: '/isInit',
    method: 'get'
  })
}

export function getSysIsint(data) {
  return request({
    url: '/sysInit',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
    }
  })
}

//获取公钥
export function getUserPwdKey() {
  return request({
    url: 'sys/user/getPubKey',
    method: 'get'
  })
}

// 获取菜单信息
export function getAvailableMenuTree() {
  return request({
    url: 'sys/menu/getAvailableMenuTree',
    method: 'get'
  })
}
// 获取系统版本
export function getEdition() {
  return request({
    url: '/sys/parameter/getBRMSEdition',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: 'logout',
    method: 'post'
  })
}
// 
export function getCodeImg(data) {
  return request({
    url: 'randomImg',
    method: 'post',
    responseType: "arraybuffer",
    data,
  })
}

export function getYcMark() {
  return request({
    url: '/sys/parameter/getYcMark',
    method: 'get'
  })
}

export function getQRCodeParamForGSC() {
  return request({
    url: 'getQRCodeParamForGSC',
    method: 'get'
  })
}
