<template>
  <a-dropdown :trigger="['hover']">
    <span
      class="iconContainer iconfontbtn icon-menu"
      :style="bgImg"
      @mouseenter="recordEvent"
      style="position: relative"
    >
      <input
        style="
          width: 0px;
          height: 0px;
          position: absolute;
          right: 0;
          top: 0;
          opacity: 0;
          z-index: -1;
        "
        id="icon-temp-input"
      />
    </span>
    <template #overlay>
      <a-menu @click="onMenuChange">
        <a-menu-item key="otherwise" :disabled="itemDisabled">
          <span>否则</span>
        </a-menu-item>
        <a-menu-item key="otherDel" :disabled="!elseFlag">
          <span>删除否则</span>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup>

// 定义组件属性
const props = defineProps({
  index: {
    type: Number,
    default: -1,
  },
  col: String,
  rowData: {
    type: Array,
    default: () => [],
  },
  colData: {
    type: Array,
    default: () => [],
  },
  elseFlag: {
    type: Boolean,
    default: false,
  },
  delElseFlag: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  record: {
    type: Object,
    default: () => ({}),
  },
})

// 定义emit事件
const emit = defineEmits(['operaChange'])

// 计算背景样式
const bgImg = computed(() => ({
  color: '#409EFF',
  'line-height': '16px',
}))

// 计算是否禁用菜单项
const itemDisabled = computed(() => {
  // 当前行索引
  let dataIndex = props.rowData.findIndex((item) => item.key === props.record.key)
  if (dataIndex > 0) {
    let aEmpt = []
    let aVEmpt = []
    let aData = []
    let pattern = new RegExp(`rule\\d*child$`)
    
    // 检查当前值往上是否有值
    for (let i = 0; i < dataIndex; i++) {
      const colKey = props.col.slice(0, 8)
      const findCol = props.colData.find((item) => item.key === colKey)
      const colChild = findCol.children
      const aColEmpt = []
      const oColParam = {}
      
      // 处理多参数情况
      if (colChild && colChild.length > 1) {
        for (let coCI = 0; coCI < colChild.length; coCI++) {
          if (
            props.rowData[i][colChild[coCI].key] === 0 ||
            props.rowData[i][colChild[coCI].key]
          ) {
            aColEmpt.push(true)
            oColParam[colChild[coCI].key] = true
          } else {
            aColEmpt.push(false)
            oColParam[colChild[coCI].key] = false
          }
        }
        // 只要有一个单元格有值，就全为true
        if (aColEmpt.length > 0) {
          if (aColEmpt.includes(true)) {
            for (let oIK in oColParam) {
              oColParam[oIK] = true
            }
          }
        }
        for (let oIK in oColParam) {
          if (props.col === oIK) {
            aEmpt.push(oColParam[oIK])
          }
        }
      } else {
        // 处理单参数情况
        if (props.rowData[i][props.col] === 0 || props.rowData[i][props.col]) {
          aEmpt.push(true)
        } else {
          aEmpt.push(false)
        }
      }
    }

    // 判断当前值的左侧值与上一行的值是否相等
    for (let rK in props.rowData[dataIndex - 1]) {
      const index = rK.length > 8 ? rK.slice(8, -5) : 0
      if (rK === props.col) {
        if (index > 0) {
          aVEmpt.push(`end${index}`)
        } else {
          aVEmpt.push('end')
        }
      } else if (pattern.test(rK) && rK[4] !== '2') {
        if (
          props.rowData[dataIndex - 1][rK] === props.rowData[dataIndex][rK] ||
          (props.rowData[dataIndex - 1][rK] !== 0 &&
            props.rowData[dataIndex][rK] !== 0 &&
            !props.rowData[dataIndex - 1][rK] &&
            !props.rowData[dataIndex][rK])
        ) {
          aVEmpt.push(true)
        } else {
          aVEmpt.push(false)
        }
        aData.push(props.rowData[dataIndex - 1][rK])
      }
    }

    // 过滤多余值
    aVEmpt.map((item, i) => {
      if (aData[i] === 0 || aData[i]) {
        aData[i] = true
      } else {
        aData[i] = false
      }
      if (item && item.toString().includes('end') && item !== 'end') {
        let itemL = item.slice(3)
        let itemLoopI = 0
        aVEmpt.splice(i + 1)
        for (let ii = 0; ii < itemL; ii++) {
          itemLoopI++
          aVEmpt[aVEmpt.length - 1 - itemLoopI] = true
        }
        aVEmpt.splice(i)
        aData.splice(i)
      }
      if (item === 'end') {
        aVEmpt.splice(i)
        aData.splice(i)
      }
    })

    if (
      (!aVEmpt.includes(false) && aData.includes(true)) ||
      aVEmpt.length === 0
    ) {
      aVEmpt.push(true)
    } else {
      aVEmpt.push(false)
    }

    // 当前值往上没有值或者左侧值不相等
    if (!aEmpt.includes(true) || aVEmpt.includes(false)) {
      return true
    }
  }
  // 第一行或者当前值为否则
  if (dataIndex === 0 || (props.elseFlag && !props.delElseFlag)) {
    return true
  }
  return false
})

// 事件处理方法
const recordEvent = (event) => {
  event.target.querySelector('#icon-temp-input').focus()
}

const onMenuChange = (obj) => {
  emit('operaChange', obj.key)
}

const onMouseEnter = (e) => {
  e.stopPropagation()
  e.preventDefault()
}

const stopClick = (e) => {
  e.stopPropagation()
}
</script>
