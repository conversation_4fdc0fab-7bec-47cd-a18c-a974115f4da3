<template>
  <span>
    <input-item
      v-if="variableType === 'constant' || enumDictName"
      :key="pos + 'in'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :enumDictName="enumDictName"
      :methodFilterType="methodFilterType"
      @onChange="onChange"
      :titleStr="getTitle || tipRes"
      :refListData="refListData"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
    />
    <prop-select-com
      v-else-if="variableType === 'dataEntry' || variableType === 'field'"
      :flag="variableType"
      :key="`${pos}${variableType === 'dataEntry' ? 'da' : 'fi'}`"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :predefineLine="predefineLine"
      :predefineCon="predefineCon"
      :preIndex="preIndex"
      ref="propSelect_com"
      @onChange="onChange"
      @calcPropData="calcPropData"
      :nameList="nameList"
      :next="next"
      :nextOptions="nextOptions"
      :titleStrD="getTitle || tipRes"
      :refListData="refListData"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
    />
    <method-com
      v-else-if="variableType === 'method'"
      :key="pos + 'me'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      :dataForLoad="fieldsDataList"
      :methodData="getData"
      @onChange="onChange"
      ref="method_com"
      :titleStr="getTitle || tipRes"
      :refListData="refListData"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
    />
    <expression-com
      v-else-if="variableType === 'expression'"
      :key="pos + 'ex'"
      :pos="pos"
      :locked="locked"
      :isTrack="isTrack"
      :variableData="variableData"
      @onChange="onChange"
      ref="expression_com"
      :titleStr="getTitle || tipRes"
      :refListData="refListData"
      :isTable="isTable"
      :noRuleCellUnit="noRuleCellUnit"
    />
    <span v-else :key="pos + 'er'">渲染错误</span>
  </span>
</template>

<script>
import store from "@/store";
import InputItem from "@/components/ruleEditCom/ruleItemList/inputItem/inputItem.vue";
import MethodCom from "@/components/ruleEditCom/ruleItemList/method/MethodCom";
import ExpressionCom from "@/components/ruleEditCom/ruleItemList/expression/expressionCom.vue";
import { getLabelArrByValues } from "@/components/ruleEditCom/utils/displayUtil";
import * as util from "@/components/ruleEditCom/utils/util";
import globalEventEmitter from "@/utils/eventBus";
import { SHOW_ACASCADER } from "@/consts/globalEventConsts";
export default {
  name: "displayField",
  inject: {
    ruleUuid: { default: "" },
  },
  components: {
    "input-item": InputItem,
    "prop-select-com": () =>
      import(
        "@/components/ruleEditCom/ruleItemList/propSelect/propSelectCom.vue"
      ),
    "method-com": MethodCom,
    "expression-com": ExpressionCom,
  },
  props: {
    pos: {
      type: String,
      default: "erule",
    },
    enumDictName: String,
    methodFilterType: {
      type: String,
      default: "null",
    },
    filterType: {
      type: String,
      default: "null",
    },
    variableData: {
      type: Object,
      default: () => {},
    },
    propOptions: {
      type: Array,
      default: () => [],
    },
    locked: Boolean,
    isTrack: Boolean,
    predefineLine: String,
    predefineCon: String,
    preIndex: Number,
    selectChange: Boolean,
    titleStr: String,
    refListData: Boolean,
    isTable: {
      type: Boolean,
      default: false,
    },
    noRuleCellUnit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fieldsDataList: [],
      dataEntryList: [],
      firstFieldItemList: [],
      methodsDataList: [],
      defaultOptions: [],
      defOptions: [],
      aNameList: [],
      tempD: [],
      tipRes: "",
    };
  },
  watch: {
    methodFilterType: {
      handler: function (newVal, oldVal) {
        this.updateCache();
      },
    },
  },
  created() {
    this.updateCache();
    this.$watch(
      () => store.getters.listMap[this.ruleUuid].order,
      () => {
        this.updateCache();
      },
      { deep: true }
    );
  },
  computed: {
    valueType() {
      return this.variableData.valueType;
    },
    variableType() {
      if (this.variableData && this.variableData.variableType) {
        if (this.pos.indexOf("_pv_0_method_") !== -1 && !this.enumDictName) {
          this.setNoEnumDictNameTipStr();
        }
        // 方法_pv_0_method_参数默认：手动输入
        let poskeyArr = this.pos.split("_pv_0_method_");
        let posVarkeyArr = this.pos.split("_pv_0_methodVar_0_method_");
        let posKey = null;
        let posVarKey = null;
        if (poskeyArr && poskeyArr.length > 1) {
          posKey = poskeyArr[poskeyArr.length - 1].indexOf("_");
        }
        if (posVarkeyArr && posVarkeyArr.length > 1) {
          posVarKey = posVarkeyArr[posVarkeyArr.length - 1].indexOf("_");
        }
        if (
          !this.selectChange &&
          ((this.pos.indexOf("_pv_0_method_") !== -1 && posKey === -1) ||
            (this.pos.indexOf("_pv_0_methodVar_0_method_") !== -1 &&
              posVarKey === -1)) &&
          this.variableData.variableType !== "constant" &&
          (this.variableData.value == "" ||
            (Array.isArray(this.variableData.value) &&
              !this.variableData.value[0])) &&
          (this.variableData.valueType === "String" ||
            this.variableData.valueType === "Character" ||
            this.variableData.valueType === "Short" ||
            this.variableData.valueType === "Integer" ||
            this.variableData.valueType === "Long" ||
            this.variableData.valueType === "Float" ||
            this.variableData.valueType === "BigDecimal" ||
            this.variableData.valueType === "Number" ||
            this.variableData.valueType === "Double" ||
            (this.variableData.enumDictName &&
              this.variableData.enumDictName.indexOf("domain") !== -1) ||
            this.methodFilterType === "List<String>")
        ) {
          this.variableData.value = "";
          this.variableData.variableType = "constant";
        }
        return this.variableData.variableType;
      }
    },
    // 参数提示
    getTitle() {
      if (this.titleStr) {
        return this.titleStr;
      } else {
        this.setNoEnumDictNameTipStr();
      }
    },
    getData() {
      let data = null;
      const { variableType, propOptions } = this;
      if (variableType === "dataEntry" || variableType === "field") {
        data = propOptions.length > 0 ? propOptions : this.defaultOptions;
      } else if (variableType === "method") {
        data = propOptions.length > 0 ? propOptions : this.methodsDataList;
      }
      return data;
    },
    getPreData() {
      let data = null;
      const { variableType, propOptions } = this;
      if (variableType === "dataEntry" || variableType === "field") {
        data = propOptions.length > 0 ? propOptions : this.defOptions;
      } else if (variableType === "method") {
        data = propOptions.length > 0 ? propOptions : this.methodsDataList;
      }
      return data;
    },
    value() {
      return this.variableData.value;
    },
    nameList() {
      let ml =
        this.getData && this.value && this.value.length > 0
          ? getLabelArrByValues(this.getData, this.value)
          : [];
      return ml;
    },
    next() {
      return this.variableData.next;
    },
    nextOptions() {
      return this.next ? this.getNextOptions(this.valueType) : [];
    },
    // nameArr(){
    //   return this.getMethodNameArr(this.getData, this.variableData.value);
    // }
  },
  methods: {
    setNoEnumDictNameTipStr() {
      if (this.variableData && !this.variableData._getOwner) return;
      let aValue = [];
      let methodValueType = "";
      let parentValue = "";
      let variable = {};
      let actionParams = null;
      if (this.variableData && this.variableData._getOwner()) {
        let owner = this.variableData._getOwner();
        if (owner.variable && owner.variable.valueType) {
          parentValue = owner.variable.valueType;
        }
        if (owner.variable) {
          variable = owner.variable;
        }
        if (owner.actionParams) {
          actionParams = owner.actionParams;
        }
      }
      let methodItem = [];
      let tipLabel = "";
      let tipIndex = "";
      // let aTip = [];
      // let aItemTip = [];
      if (actionParams) {
        for (let i in actionParams) {
          actionParams[i] &&
            actionParams[i].next &&
            getLastV(actionParams[i].next, actionParams[i]);
        }
        methodItem = store.getters.listMap[
          this.ruleUuid
        ].initModelData.methodsDataList.find((item) => {
          return methodValueType === item.valueType;
        });
      } else {
        variable && variable.next && getLastV(variable.next, variable);
        methodItem = store.getters.listMap[
          this.ruleUuid
        ].initModelData.methodsDataList.find((item) => {
          return parentValue === item.valueType;
        });
      }
      function getLastV(data, parentData) {
        if (data.next) {
          getLastV(data.next, data);
        } else {
          if (data.variableType === "method") {
            aValue = data.value;
            parentData &&
              parentData.valueType &&
              (methodValueType = parentData.valueType);
          }
        }
      }
      if (methodItem && methodItem.children && methodItem.children.length > 0) {
        for (let i in aValue) {
          let resA = methodItem.children.find((item) => {
            tipIndex = i;
            return aValue[i] === item.value;
          });
          tipLabel = resA && resA.label;
        }
      } else {
        for (let i in aValue) {
          if (
            methodValueType &&
            store.getters.listMap[this.ruleUuid].initModelData
              .baseMethodListMap[methodValueType]
          ) {
            let resA = store.getters.listMap[
              this.ruleUuid
            ].initModelData.baseMethodListMap[methodValueType].find((item) => {
              tipIndex = i;
              return aValue[i] === item.value;
            });
            tipLabel = resA && resA.label;
          }
        }
      }
      // aItemTip = tipLabel && tipLabel.split(",<");
      // aItemTip &&
      //   aItemTip.map((item) => {
      //     if (item.indexOf(">") !== -1) {
      //       aTip.push(item.substring(0, item.indexOf(">")));
      //     }
      //   });
      // this.tipRes = aTip[tipIndex];
      this.tipRes = tipLabel;
    },
    updateNameListObj() {
      let ml =
        this.getPreData && this.value && this.value.length > 0
          ? getLabelArrByValues(this.getPreData, this.value)
          : [];
      this.$refs["propSelect_com"] &&
        this.$refs["propSelect_com"].updateNameListObj(ml);
    },
    calcPropData(
      e,
      pos,
      preIndex,
      predefineLine,
      predefineCon,
      variableData,
      fn,
      flag
    ) {
      let filterOData = this.filterOptionData(
        this.getPreData.length ? this.getPreData : this.getData,
        variableData
      );
      if (pos.indexOf("setValueCom") !== -1) {
        let aData = JSON.parse(JSON.stringify(filterOData));
        filterOData = JSON.parse(JSON.stringify(this.filterOptSetData(aData)));

        /**
         * 过滤掉没有子节点的项，以用来解决现鼠标滑上之后直接选中的问题
         */
        const filterODataWithchildren = filterOData.filter((item) => {
          return item.children && item.children.length > 0;
        });

        /**
         * 如果过滤后的数据为空，则说明是最后一个变量选择器，这时需要使用过滤前的数据，否则会出现点最后变量选择器，显示的是其前面的选择器的数据的问题
         * 如果过滤后的数据不为空，则说明不是最后一个变量选择器，这时需要使用过滤后的数据，以解决鼠标滑上之后直接选中的问题
         */
        if (filterODataWithchildren.length > 0) {
          filterOData = filterODataWithchildren;
        }
      }
      if (preIndex && predefineLine) {
        filterOData = filterOData.filter((item) => {
          let temp_str = item.value && item.value.split(".erule.predefine");
          if (predefineCon === "predefineCon") {
            if (temp_str[1] <= preIndex) {
              return item;
            } else {
              return item.value.indexOf(`predefine`) === -1;
            }
          } else {
            if (temp_str[1] < preIndex) {
              return item;
            } else {
              return item.value.indexOf(`predefine`) === -1;
            }
          }
        });
      }

      // 级联选择器默认选中值
      const cascaderSelectedValue = [];
      // 根据传入的数据生成级联选择器默认选中值
      function generateCascaderSelectedValue(data) {
        if (data && data.next) {
          generateCascaderSelectedValue(data.next);
        }

        const value =
          data && data.value && data.value.length > 0 && data.value[0];
        if (value) {
          cascaderSelectedValue.unshift(value);
        }
      }
      // 根据变量数据生成级联选择器默认选中值
      generateCascaderSelectedValue(this.variableData);
      globalEventEmitter.emit(SHOW_ACASCADER, {
        e,
        pos,
        filterOData,
        fn,
        flag,
        cascaderSelectedValue,
      });
    },
    // 屏蔽字符串、数、日期、布尔、列表的下一级选项"Short", "Integer", "Long", "Float", "Double", "BigDecimal", "String", "Character", "Number", "Boolean", "Date", "List"
    filterOptSetData(data) {
      for (let i = data.length - 1; i >= 0; i--) {
        if (
          data[i].valueType === "Short" ||
          data[i].valueType === "Integer" ||
          data[i].valueType === "Long" ||
          data[i].valueType === "Float" ||
          data[i].valueType === "Double" ||
          data[i].valueType === "BigDecimal" ||
          data[i].valueType === "String" ||
          data[i].valueType === "Character" ||
          data[i].valueType === "Number" ||
          data[i].valueType === "Boolean" ||
          data[i].valueType === "Date" ||
          data[i].valueType.indexOf("List") !== -1
        ) {
          delete data[i].children;
          data[i].isLeaf = true;
        } else if (data[i].isMethodItem) {
          data.splice(i, 1);
        } else if (data[i].children && data[i].children.length) {
          this.filterOptSetData(data[i].children);
        }
      }
      return data;
    },
    filterOptionData(argdata, variableData) {
      const { __context = {} } = variableData;
      const { owner = {}, isParam, isRootVar } = __context;
      const { actionType } = owner;
      const withoutMethod = (data) => {
        return data.map((item) => {
          const { children } = item;
          if (children && children.length > 0) {
            const childrenArray = withoutMethod(children);
            return { ...item, children: childrenArray };
          }
          return item;
        });
      };
      if (actionType === "setValue" && isParam === "actionSetLeft") {
        let _argdata = withoutMethod(argdata);
        let posStr = "_setValueCom_";
        if (this.pos.indexOf(posStr) !== -1) {
          let itemIndex = this.pos.split(posStr)[1].split("_");
          if (itemIndex && itemIndex.length === 1) {
            return this.filterSetInvoke(_argdata, false);
          }
        }
        return _argdata;
      }
      if (actionType === "invokeMethod" && isRootVar) {
        let _argdata = withoutMethod(argdata);
        let posStr = "_actionMethod_";
        if (this.pos.indexOf(posStr) !== -1) {
          let itemIndex = this.pos.split(posStr)[1].split("_");
          if (itemIndex && itemIndex.length === 1) {
            return this.filterSetInvoke(_argdata, true);
          }
        }
        return _argdata;
      }
      return argdata;
    },
    /**
     * 过滤选项
     * 用于过滤掉setValue中，方法调用的变量，以及invokeMethod中，非方法调用的变量
     * @param data 待处理的数据
     * @param isMethod 是否是要过滤掉出是方法的项，若其值为true，则只保留方法项，否则只保留非方法项 默认为false
     */
    filterSetInvoke(data, isMethod) {
      /**
       * 递归删除不是方法的项
       * @param data 待处理的数据
       * @param level 当前处理的层级，默认为0
       */
      function removeItemsNotMethod(data, level = 0) {
        // 遍历数据
        for (let i = data.length - 1; i >= 0; i--) {
          // 如果当前项有子项，则递归调用自身处理子项
          if (data[i].children && data[i].children.length) {
            removeItemsNotMethod(data[i].children, level + 1);
          }

          // 如果子项列表为空，则删除子项列表属性
          if (data[i].children && data[i].children.length === 0) {
            delete data[i].children;
          }

          // 如果当前项没有子项，则检查其是否符合删除条件，若符合则删除
          if (
            !data[i].children &&
            (level === 0 || // 层级为0
              (isMethod && data[i] && !data[i].isMethodItem) || // 想要过滤出是方法项，而当前项不是方法项
              (!isMethod && data[i] && data[i].isMethodItem)) // 想要过滤出非方法项，而当前项是方法项
          ) {
            data.splice(i, 1);
          }
        }
      }

      removeItemsNotMethod(data);

      return data;
    },
    // getMethodNameArr(data, value) {
    //   const nameList =
    //     data && value && value.length > 0
    //       ? getLabelArrByValues(data, value)
    //       : [];
    //   const targetNameObj = nameList.length > 0 ? nameList.splice(0, 1)[0] : {};
    //   const nameStr = targetNameObj.name || "";
    //   const reg1 = /#.+?>/g;
    //   const nameArr = nameStr ? nameStr.split(reg1) : [];
    //   return nameArr;
    // },
    getNextOptions(valueType) {
      const childModelOptions = this.getChildModelOptions(valueType);
      const baseMethodList = this.getBaseMethodList(valueType);
      const nextOptions = [];
      childModelOptions.forEach((item) => {
        loopIsLeaf(item);
        function loopIsLeaf(data) {
          if (Array.isArray(data)) {
            data.map((item) => {
              if (item.children) {
                item.isLeaf && (item.isLeaf = false);
                loopIsLeaf(item.children);
              } else {
                !item.isLeaf && (item.isLeaf = true);
              }
            });
          } else {
            if (item.children) {
              item.isLeaf && (item.isLeaf = false);
              loopIsLeaf(item.children);
            } else {
              !item.isLeaf && (item.isLeaf = true);
            }
          }
        }
        nextOptions.push(item);
      });
      baseMethodList.forEach((item) => {
        item.isLeaf = true;
        nextOptions.push(item);
      });
      return nextOptions;
    },
    getBaseMethodList(valueType) {
      const { owner = {}, isParam } = this.variableData.__context;
      const { actionType } = owner;

      // 对于action中的值统一不再返回基础数据类型方法；
      if (actionType && isParam === "actionSetLeft") {
        return [];
      } else {
        const baseMethodListMap =
          store.getters.listMap[this.ruleUuid].initModelData.baseMethodListMap;
        const _baseMethodList = baseMethodListMap[valueType]
          ? baseMethodListMap[valueType]
          : valueType.includes("List<")
          ? baseMethodListMap["List"]
          : [];
        // return util.cloneRuleData(_baseMethodList);
        return _baseMethodList;
      }
    },
    getChildModelOptions(valueType) {
      const { getData = [] } = this;
      let childModelOptions;
      getData.forEach((item) => {
        if (
          item.valueType === valueType &&
          item.children &&
          item.children.length > 0
        ) {
          childModelOptions = this.filterChildOptions(item.children);
        }
      });
      // return util.cloneRuleData(childModelOptions) || [];
      return childModelOptions || [];
    },
    filterChildOptions(options = []) {
      const { owner = {}, isParam } = this.variableData.__context;
      const { actionType = "" } = owner;
      return options.filter((item) => {
        if (!item.isMethodItem || item.isMethodItem) {
          return true;
        } else if (item.position === "02" && isParam !== "actionSetLeft") {
          return true;
        } else if (
          item.position === "01" &&
          actionType &&
          isParam !== "actionSetLeft"
        ) {
          return true;
        }
        return false;
      });
    },
    // TODO 未处理缓存数据存取逻辑 editorCache
    updateCache() {
      if (this.filterType !== "null" || this.methodFilterType !== "null") {
        const {
          initModelData: {
            fieldsDataList,
            dataEntryList,
            firstFieldItemList,
            methodsDataList,
          },
        } = store.getters.listMap[this.ruleUuid];
        this.fieldsDataList = this.dataFilter(fieldsDataList) || [];
        this.dataEntryList = dataEntryList || [];

        this.firstFieldItemList = this.dataFilter(firstFieldItemList) || [];
        this.methodsDataList = this.dataFilter(methodsDataList) || [];
        const option = [...this.dataEntryList, ...this.firstFieldItemList];
        const optionAndFields = [...option, ...this.fieldsDataList];
        this.defaultOptions = option.length > 0 ? option : optionAndFields;
      } else {
        this.fieldsDataList =
          store.getters.listMap[this.ruleUuid].initModelData.fieldsDataList ||
          [];
        this.dataEntryList =
          store.getters.listMap[this.ruleUuid].initModelData.dataEntryList ||
          [];
        this.firstFieldItemList =
          store.getters.listMap[this.ruleUuid].initModelData
            .firstFieldItemList || [];
        this.methodsDataList =
          store.getters.listMap[this.ruleUuid].initModelData.methodsDataList ||
          [];
        this.defaultOptions = [
          ...this.dataEntryList,
          ...this.firstFieldItemList,
        ];
      }
    },
    updatePredefine() {
      if (this.filterType !== "null" || this.methodFilterType !== "null") {
        const {
          preListMap: {
            initModelData: {
              fieldsDataList,
              dataEntryList,
              firstFieldItemList,
              methodsDataList,
            },
          },
        } = this;
        let _fieldsDataList = this.dataFilter(fieldsDataList) || [];
        let _dataEntryList = dataEntryList || [];

        let _firstFieldItemList = this.dataFilter(firstFieldItemList) || [];
        let _methodsDataList = this.dataFilter(methodsDataList) || [];
        const option = [..._dataEntryList, ..._firstFieldItemList];
        const optionAndFields = [...option, ..._fieldsDataList];
        this.defOptions = option.length > 0 ? option : optionAndFields;
      } else {
        let _fieldsDataList =
          store.getters.preListMap.initModelData.fieldsDataList || [];
        let _dataEntryList =
          store.getters.preListMap.initModelData.dataEntryList || [];
        let _firstFieldItemList =
          store.getters.preListMap.initModelData.firstFieldItemList || [];
        let _methodsDataList =
          store.getters.preListMap.initModelData.methodsDataList || [];

        this.defOptions = [..._dataEntryList, ..._firstFieldItemList];
      }
    },
    dataFilter(fieldsDataList) {
      const filterArray = this.filterFn(fieldsDataList);

      return filterArray;
    },
    onChange(pos, _newVariableData, finalValueType, operatorOptions) {
      this.$emit("onChange", pos, _newVariableData, finalValueType, operatorOptions);
    },
    sameFilterType(type) {
      if (type.includes("List<")) {
        const regStr = type.substring(5, type.length - 1);
        return regStr;
      }
      return `List<${type}>`;
    },
    filterNumbFn(type, itemType) {
      const numList = [
        "Short",
        "Integer",
        "Float",
        "Double",
        "BigDecimal",
        "Long",
      ];
      if (numList.includes(type)) {
        return numList.includes(itemType);
      }
      return false;
    },
    filterFn(list) {
      const { filterType, methodFilterType } = this;
      let res = [];
      list.forEach((item) => {
        if (item.valueType.includes(".") && item.children) {
          if (methodFilterType !== "null") {
            if (
              item.valueType === methodFilterType ||
              item.valueType === this.sameFilterType(methodFilterType) ||
              this.filterNumbFn(methodFilterType, item.valueType) ||
              this.filterNumbFn(
                methodFilterType,
                this.sameFilterType(methodFilterType)
              )
            ) {
              res.push({ ...item });
              return;
            }
          } else if (filterType !== "null") {
            if (
              item.valueType === filterType ||
              item.valueType === this.sameFilterType(filterType) ||
              this.filterNumbFn(methodFilterType, item.valueType) ||
              this.filterNumbFn(
                methodFilterType,
                this.sameFilterType(methodFilterType)
              )
            ) {
              res.push({ ...item });
              return;
            }
          }
          const childList = this.filterFn(item.children);
          if (childList.length > 0) {
            res.push({
              ...item,
              children: childList,
            });
          }
        } else {
          if (methodFilterType !== "null") {
            if (
              item.valueType === methodFilterType ||
              item.valueType === this.sameFilterType(methodFilterType) ||
              this.filterNumbFn(methodFilterType, item.valueType) ||
              this.filterNumbFn(
                methodFilterType,
                this.sameFilterType(methodFilterType)
              )
            ) {
              res.push(item);
              return;
            }
          } else if (filterType !== "null") {
            if (
              item.valueType === filterType ||
              item.valueType === this.sameFilterType(filterType) ||
              this.filterNumbFn(methodFilterType, item.valueType) ||
              this.filterNumbFn(
                methodFilterType,
                this.sameFilterType(methodFilterType)
              )
            ) {
              res.push(item);
            }
          }
        }
      });
      return res;
    },
  },
};
</script>
