<!-- 菜单管理页 -->

<script setup lang="ts">

    import { useRouter } from 'vue-router';
    import { menu, ParentMenu, menuEnable, changeOrder } from "@/api/menuList";
    import qs from "qs";
    import MenuAdd from "@/businessComponents/menuManage/MenuAdd";
    import MenuUpdate from "@/businessComponents/menuManage/MenuUpdate";
    import TableSkeleton from '@/components/TableSkeleton';
    definePageMeta({
        title: '菜单管理'
    })


    const router = useRouter();
    const message = inject('message')
    const modal = inject('modal')
    const form = ref({
        region: "",
    });

    // 添加loading状态控制
    const loading = ref(false);
    const arrmenu = ref("");
    const arrmenuid = ref("");
    const arrId = ref("");
    const obj = ref({});
    const strmenu = ref("1");
    const menusList = ref([]);
    const tableData = ref([]);
    const sel_role = (v: any) => {
        router.push({
            name: "menu_update",
            query: {
                menuId: v.id,
                menuParentId: v.parentId,
                menuType: v.type,
                menuName: v.name,
            },
        });
    };
    const sel_menu = () => {
        loading.value = true;
        ParentMenu().then((res) => {
            arrmenu.value = res.data[0].name;
            arrmenuid.value = res.data[0].id;
            menusList.value = res.data;
            // 如果已经加载了菜单列表，继续加载菜单数据
            if (menusList.value && menusList.value.length > 0) {
                getMenu();
            } else {
                loading.value = false;
            }
        }).catch(() => {
            loading.value = false;
        });
    };

    const selectGet = (vid: string) => {
        obj.value = menusList.value.find((item) => item.id === vid) || {};
        strmenu.value = obj.value.id;
    };

    const getMenu = () => {
        loading.value = true;
        const pars = {
            parentId: strmenu.value,
        };
        menu(pars).then((res) => {
            tableData.value = res.data;
            loading.value = false;
        }).catch(() => {
            loading.value = false;
        });
    };

    const handleClick = (str: any) => {

        const data = qs.stringify({
            parentId: str.id,
            state: str.state,
        });
        menuEnable(data)
            .then((res) => {
                if (res.data === "存在可用菜单，禁用失败") {
                    message.success(res.data);
                } else {
                    getMenu();
                    message.success('操作成功');
                }
            })
            .catch((err) => {});
    };

    const getSameLevelMenuListByParentId = (row: any) => {
        const currentLevelList: any[] = [];
        const data = tableData.value;
        if (row.menuLevel === 1) {
            for (let i = 0; i < data.length; i++) {
                currentLevelList.push(data[i]);
            }
        } else {
            const parentId = row.parentId;
            for (let i = 0; i < data.length; i++) {
                if (parentId === data[i].id) {
                    currentLevelList.push(...data[i].children);
                    break;
                }
            }
        }
        return currentLevelList;
    };

    const top = (row: any) => {
        const currentLevelList = getSameLevelMenuListByParentId(row);
        let otherId = -1;
        for (let i = 0; i < currentLevelList.length; i++) {
            if (currentLevelList[i].id === row.id) {
                if (i === 0) {
                    message.info("已经是第一条，不可上移！");
                    return;
                } else {
                    otherId = currentLevelList[i - 1].id;
                    break;
                }
            }
        }
        const data = qs.stringify({
            meId: row.id,
            otherId: otherId,
        });
        changeOrder(data)
            .then((res) => {
                if (res.code === 20000) {
                    getMenu();
                    message.success('操作成功');
                }
            })
            .catch((err) => {});
    };

    const down = (row: any) => {
        const currentLevelList = getSameLevelMenuListByParentId(row);
        let otherId = -1;
        for (let i = 0; i < currentLevelList.length; i++) {
            if (currentLevelList[i].id === row.id) {
                if (i === currentLevelList.length - 1) {
                    message.info("已经是最后一条，不可下移！");
                    return;
                } else {
                    otherId = currentLevelList[i + 1].id;
                    break;
                }
            }
        }
        const data = qs.stringify({
            meId: row.id,
            otherId: otherId,
        });
        changeOrder(data)
            .then((res) => {
                if (res.code === 20000) {
                    getMenu();
                    message.success('操作成功');
                }
            })
            .catch((err) => {});
    };

    onMounted(() => {
        loading.value = true;
        sel_menu();
    });
    const modalType = ref(''); // 对话框类型：'add' 或 'update'
    const menuId = ref({});
    const menuParentId = ref({});
    const menuType = ref({});
    const menuName = ref({});
    const isModalVisible = ref(false); // 对话框显示状态

    const showModal = (type, record = {}) => {
        modalType.value = type;
        if (type === 'update') {
            menuId.value = record.id;
            menuParentId.value= record.parentId
            menuType.value= record.type
            menuName.value= record.name
        }
        isModalVisible.value = true;
    };

    const handleCancel = () => {
        isModalVisible.value = false;
    };

    // 角色新增组件
    const menuAddComponent = ref()

    // 角色修改组件
    const menuUpdateComponent = ref()

    // 处理modal ok 事件
    function handleModalOk() {
        let submitFun
        switch (modalType.value) {
            case 'add':
                submitFun = menuAddComponent.value.submitForm;
                break;
            case 'update':
                submitFun = menuUpdateComponent.value.submitForm;
                break;
        }
        submitFun && submitFun(() => {
            getMenu();
            isModalVisible.value = false;
        });
    }
</script>

<template>
    <NotListPageLayout>

        <template #title>
            <!-- 页面标题 -->
            菜单管理
        </template>

        <template #search >
            <!-- 搜索区域 -->
           <a-form ref="form" :model="form" layout="inline" >
                <a-form-item label="上级菜单" prop="region">
                    <a-select
                            v-model:value="arrmenu"
                            placeholder="请选择"
                            @change="selectGet"
                            style="width: 200px"
                    >
                        <a-select-option
                                v-for="item in menusList"
                                :key="item.id"
                                :value="item.id"
                        >
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item class="buttonItem">
                    <a-button  type="primary"  @click="getMenu" style="margin-right: 8px">
                        查询
                    </a-button>
                    <a-button @click="showModal('add')"  type="success" >
                        新增
                    </a-button>
                </a-form-item>

            </a-form>
        </template>

        <template #table="slotProps">
            <!-- 表格区域 -->
            <!-- 加载中显示骨架屏 -->
            <TableSkeleton 
                v-if="loading"
                :columns="[
                    { title: '名称', key: 'name', width: 380 },
                    { title: '地址', key: 'routePath', width: 150 },
                    { title: '描述', key: 'description', width: 280 },
                    { title: '操作', key: 'action', width: 180 }
                ]"
                :limit="10"
                :scrollY="slotProps.scrollY"
            />
            <!-- 加载完成显示表格数据 -->
            <a-table
                v-else-if="tableData && tableData.length"
                :scroll="{ y: slotProps.scrollY }"
                :data-source="tableData"
                style="width: 100%; margin-bottom: 20px"
                row-key="id"
                :pagination="false"
                :defaultExpandAllRows="true"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                size="small"
            >
                <a-table-column key="name" title="名称" data-index="name" width="380" align="left" :ellipsis="true">
                    <template #default="{ record }">
                        <span style="margin-right: 5px">{{ record.name }}</span>
                        <a-tooltip title="上移">
                            <CaretUpOutlined @click="top(record)" style="color: rgb(140, 140, 140);"/>
                        </a-tooltip>
                        <a-tooltip title="下移">
                            <CaretDownOutlined @click="down(record)" style="color: rgb(140, 140, 140);"/>
                        </a-tooltip>
<!--                        <a-icon type="caret-up" @click="top(record)" />-->
<!--                        <a-icon type="caret-down" @click="top(record)"/>-->
                    </template>
                </a-table-column>
                <a-table-column key="routePath" title="地址" data-index="routePath" align="left" :ellipsis="true" />
                <a-table-column key="description" title="描述" data-index="description" :ellipsis="true" width="280" align="left" />
                <a-table-column key="action" title="操作" width="180" align="center">
                    <template #default="{ record }">
                        <a-button type="link" size="small" @click="showModal('update',record)">
                            编辑
                        </a-button>
                        <a-switch
                                @change="handleClick(record)"
                                :checked="record.state === '1'"
                                style="margin-left: 20px"
                        />
                    </template>
                </a-table-column>
            </a-table>
            <!-- 无数据时显示空状态 -->
            <a-empty v-else description="暂无数据" />
        </template>
        <!-- 新增和更新对话框 -->
        <a-modal v-if="isModalVisible" :visible="isModalVisible" :title="modalType === 'add' ? '新增菜单' : '编辑菜单'" @ok="handleModalOk"
                 @cancel="handleCancel" okText="保存">
            <div style="max-height: 60vh; overflow-y: auto;">
                <MenuAdd ref="menuAddComponent" v-if="modalType === 'add'" />
                <MenuUpdate ref="menuUpdateComponent" v-if="modalType==='update'"
                            :key="modalType + '-update-' + menuId"
                            :menuId="menuId"
                            :menuParentId="menuParentId"
                            :menuType="menuType"
                            :menuName="menuName"
                />
            </div>
        </a-modal>
    </NotListPageLayout>
</template>
