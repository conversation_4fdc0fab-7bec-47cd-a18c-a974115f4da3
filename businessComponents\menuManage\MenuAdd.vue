<!--菜单新增页-->
<template>
    <div id="menu_add">
        <a-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px" label-align="right" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="地址" name="routePath">
                <a-input autocomplete="off" v-model:value="ruleForm.routePath" placeholder="请输入"></a-input>
            </a-form-item>
            <a-form-item label="上级菜单" name="region">
                <a-select v-model:value="ruleForm.region" placeholder="请选择"  @change="selectGet" style="width:100%">
                    <a-select-option v-for="item in menusList" :key="item.id" :value="item.id">
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="类型" name="menutype">
                <a-select v-model:value="ruleForm.menutype" placeholder="请选择" style="width:100%">
                    <a-select-option :key="0" value="0">模块</a-select-option>
                    <a-select-option :key="1" value="1">页面</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="描述">
                <a-input autocomplete="off" type="textarea"  v-model:value="ruleForm.description" placeholder="请输入"></a-input>
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">

    import { useRouter } from 'vue-router';
    import { menuAddsub, ParentMenu } from "@/api/menuList";

    const router = useRouter();
    const message = inject('message')
    interface RuleForm {
        name: string;
        routePath: string;
        region: string;
        menutype: string;
        description: string;
    }

    const ruleForm = reactive<RuleForm>({
        name: '',
        routePath: '',
        region: '',
        menutype: '',
        description: '',
    });

    const obj = ref({});
    const strmenu = ref("");
    const menusList = ref([]);

    const rules = {
        name: [
            { required: true, message: '名称不能为空', trigger: 'change' }
        ],
        region: [
            { required: true, message: '请选择', trigger: 'change' }
        ],
        menutype: [
            { required: true, message: '请选择', trigger: 'change' }
        ],
        description: [
            {
                min: 1,
                max: 100,
                message: "长度在 1 到 100 个字符",
                trigger: "blur",
            },
        ]
    };

    const ruleFormRef = ref();

    const submitForm = (callback) => {
        ruleFormRef.value.validate().then(() => {
            const data = {
                name: ruleForm.name,
                routePath: ruleForm.routePath,
                parentId: strmenu.value,
                state: "1",
                type: ruleForm.menutype,
                description: ruleForm.description,
            };
            menuAddsub(data).then((res) => {
                if (res.code === 20000){
                    message.success(res.data);
                    if (typeof callback === 'function') {
                        callback();
                    }
                }else {
                    message.error(res.data)
                }

            });
        }).catch(() => {
            console.log('error submit!!');
        });
    };

    const addlist = () => {
        ParentMenu().then(res => {
            menusList.value = res.data;
        });
    };

    const selectGet = (vid: string) => {
        obj.value = menusList.value.find((item) => item.id === vid);
        strmenu.value = obj.value.id;
    };

    onMounted(() => {
        addlist();
    });
    defineExpose({
        submitForm,
    });
</script>
