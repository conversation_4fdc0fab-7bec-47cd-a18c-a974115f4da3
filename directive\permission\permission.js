import store from '@/store'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
  const all_permission = "*:*:*";
  if (value && value instanceof Array && value.length > 0) {
    // const permissions = store.getters && store.getters.permissions
    let btnRole = getButtonRole() === "admin" ? ["*:*:*"] : JSON.parse(getButtonRole());
    const permissions = btnRole
    const permissionDatas = value

    const hasPermission = permissions?.some(permission => {
      return all_permission === permission || permissionDatas.includes(permission)
    })
    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkPermi="['system:user:add','system:user:edit']"`)
    return false
  }
}
