<!--角色修改页-->
<template>
    <div id="role_update">
        <a-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleFormRef"
                label-align="right" :label-col="{ span: 4 }"
                :wrapper-col="{ span: 18 }"
        >
            <a-form-item label="名称" name="name">
                <a-input autocomplete="off" v-model:value="ruleForm.name" />
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-input autocomplete="off" v-model:value="ruleForm.description" />
            </a-form-item>
            <a-form-item label="业务条线">
                <a-select v-model:value="ruleForm.businessLineId" placeholder="请选择" :filterOption="filterOption" showSearch>
                    <a-select-option
                            v-for="item in menusList"
                            :disabled="true"
                            :key="item.uuid"
                            :value="item.uuid"
                            :name="item.name"
                    >
                        {{ item.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="类型" name="type">
                <a-select v-model:value="ruleForm.type" placeholder="请选择">
                    <a-select-option
                            v-for="item in options"
                            :key="item.value"
                            :value="item.value"
                    >
                        {{ item.label }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="等级" name="lever">
                <a-input autocomplete="off" v-model:value="ruleForm.lever" />
            </a-form-item>
            <a-form-item label="标记" name="flag">
                <a-input autocomplete="off" v-model:value="ruleForm.flag" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup lang="ts">
    import { useRouter } from 'vue-router';
    import qs from 'qs';
    import { getAllBusinessByLoginUser, getRoleById, updateAction } from '@/api/post_permissions';

    const router = useRouter();
    const message = inject('message')
    const props = defineProps({
        id: {
            type: String,
            required: true,
        },
    });
    const ruleFormRef = ref();
    const ruleForm = ref({
        name: '',
        description: '',
        businessLineId: '',
        type: '',
        lever: '',
        flag: '',
    });

    const menusList = ref([]);
    const options = [
        { value: '1', label: '规则管理' },
        { value: '2', label: '规则审核' },
        { value: '3', label: '系统管理' },
    ];

    const rules = {
        name: [
            { required: true, message: '名称不能为空', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
        ],
        description: [
            { required: true, message: '描述不能为空', trigger: 'blur' },
            { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
        ],
        type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
    };

    const postsel = async () => {
        const res = await getAllBusinessByLoginUser();
        menusList.value = res.data;
    };

    const getRoleupd = async () => {
        const res = await getRoleById({ id: props.id });
        ruleForm.value = res.data;
    };

    const submitForm = async (callback) => {
        try {
            await ruleFormRef.value.validate();
            const res = await updateAction({
                id: ruleForm.value.id,
                name: ruleForm.value.name,
                description: ruleForm.value.description,
                businessLineId: ruleForm.value.businessLineId,
                type: ruleForm.value.type,
                flag: ruleForm.value.flag,
                lever: ruleForm.value.lever,
            });
            if (res.data === "失败，已经存在该名称的角色！") {
                message.error(res.data);
            } else {
                message.success('修改成功');
                if (typeof callback === 'function') {
                    callback();
                }
            }
        } catch (error) {
            console.error(error);
        }
    };
    onMounted(() => {
        postsel();
        getRoleupd();
    });
    defineExpose({
        submitForm,
    });
</script>
