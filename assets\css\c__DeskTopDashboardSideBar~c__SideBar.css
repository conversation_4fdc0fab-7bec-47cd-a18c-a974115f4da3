.KnowledgePieGuide-module_wrapper_McOcd {
    max-width: 218px;
    height: 76px;
    position: relative;
    border-radius: 8px;
    background-color: var(--yq-yuque-green-600);
    color: var(--yq-white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px
}

.KnowledgePieGuide-module_wrapper_McOcd .KnowledgePieGuide-module_close_Hta-f {
    position: absolute;
    cursor: pointer;
    top: 8px;
    right: 8px
}

.KnowledgePieGuide-module_breathBorder_JnuNa {
    max-width: 259px;
    height: 55px;
    position: absolute;
    background-color: rgba(0,185,107,.1);
    border: 2px solid var(--yq-yuque-green-600);
    animation: KnowledgePieGuide-module_breathe_hzZ03 2s ease-in-out infinite;
    border-radius: 8px;
    left: 224px;
    top: -20px
}

@keyframes KnowledgePieGuide-module_breathe_hzZ03 {
    0% {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }

    50% {
        box-shadow: 0 0 0 2px var(--yq-yuque-green-600)
    }

    to {
        box-shadow: 0 0 0 0 var(--yq-yuque-green-600)
    }
}

.index-module_container_YifTz {
    white-space: break-spaces;
    z-index: 1020
}

.index-module_container_YifTz .ant-tooltip-inner {
    background-color: var(--yq-yuque-grey-2);
    padding: 0
}

.index-module_container_YifTz .ant-tooltip-arrow-content {
    background-color: var(--yq-yuque-grey-2)
}

.index-module_container_YifTz p {
    font-size: 14px;
    font-weight: 700
}

.index-module_container_YifTz h4,.index-module_container_YifTz p {
    color: var(--yq-white);
    line-height: 22px
}

.index-module_titleContainer_E19Ts {
    position: relative
}

.index-module_content_u6ksX {
    background-color: var(--yq-blue-5);
    padding: 4px 10px;
    padding-right: 30px;
    border-radius: 6px;
    min-width: 250px;
    margin-top: 6px;
    margin-left: -2px
}

.index-module_close_xIF4s {
    position: absolute!important;
    top: 2px;
    right: -2px;
    color: var(--yq-white)
}

.index-module_close_xIF4s :hover {
    color: var(--yq-white)
}

.index-module_dragBarContainer_4KyrG.index-module_desktop_iHrjW .index-module_dragbarResizeWrapper_sw7k9 {
    width: 22px;
    right: 8px
}

.index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM {
    position: absolute;
    z-index: 1010;
    top: 0;
    bottom: 0;
    right: -6px;
    width: 6px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    cursor: col-resize
}

.index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    transition: all .2s ease-in-out
}

.index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:after {
    content: "";
    transition: backgroundcolor .2s ease-in-out;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 6px;
    width: 100%;
    border-right: 1px solid var(--yq-yuque-grey-3)
}

.index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:hover:after {
    border-right: 1px solid var(--yq-yuque-grey-4)
}

.index-module_asidePinned_\+\+Nry .index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM {
    cursor: pointer
}

.index-module_asidePinned_\+\+Nry .index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:after {
    pointer-events: none;
    background-color: var(--yq-border-primary)
}

.index-module_asidePinned_\+\+Nry:hover .index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:before {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarActive_tlsM7:after {
    border-right: 1px solid var(--yq-yuque-grey-4)
}

.index-module_dragBarContainer_4KyrG .index-module_dragBar_-J0vM:hover+.index-module_dragBarResizeWrapper_l0\+k2 .index-module_dragBarResize_KZ3\+u {
    display: block
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarResizeWrapper_l0\+k2 {
    width: 32px;
    height: 70px;
    position: absolute;
    top: 213px;
    right: 0;
    left: auto;
    transform: translateX(50%);
    padding-top: 15px;
    cursor: pointer;
    z-index: 1010
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarResizeWrapper_l0\+k2:hover,.index-module_dragBarContainer_4KyrG .index-module_dragBarResizeWrapper_l0\+k2:hover .index-module_dragBarResize_KZ3\+u {
    display: block
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarResizeWrapper_l0\+k2:hover .index-module_dragBarResize_KZ3\+u svg {
    color: var(--yq-yuque-grey-9)
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarResize_KZ3\+u {
    height: 44px;
    width: 14px;
    background-color: var(--yq-white);
    border: 1px solid var(--yq-yuque-grey-4);
    box-shadow: 0 2px 8px 0 rgba(0,0,0,.06);
    border-radius: 8px;
    line-height: 28px;
    text-align: center;
    z-index: 999;
    margin-left: 10px;
    display: none
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarResize_KZ3\+u svg {
    position: relative;
    top: 11px;
    left: -4px;
    width: 20px;
    height: 20px;
    color: var(--yq-yuque-grey-7)
}

.index-module_dragBarContainer_4KyrG .index-module_dragBarWrapper_CU--R {
    height: 100%
}

.index-module_dragBarMask_wu3pz {
    position: fixed;
    cursor: col-resize;
    z-index: 1011;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0
}

.index-module_pinTip_1i0BS {
    text-align: center;
    display: block;
    font-size: 12px
}

.DashboardSideBar-module_sidebar_7dYv4 {
    width: 100%;
    height: 100vh;
    position: fixed;
    z-index: 101
}

.DashboardSideBar-module_sidebar_7dYv4 .DashboardSideBar-module_sidebarMenuWrapper_YyteY {
    border-right: 1px solid var(--yq-border-light);
    background-color: var(--yq-bg-secondary);
    height: 100%;
    padding: 10px 0 0 0
}

.DashboardSideBar-module_sidebar_7dYv4 .DashboardSideBar-module_sidebarMenuWrapper_YyteY.DashboardSideBar-module_isInsetTitleBar_\+12H3 {
    padding-top: 25px
}

.DashboardSideBar-module_sidebar_7dYv4 .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: var(--yq-bg-primary-hover)
}

.DashboardSideBar-module_sidebar_7dYv4 p {
    margin-bottom: 0
}
