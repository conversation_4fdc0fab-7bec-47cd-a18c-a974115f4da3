<!-- 空心星标，用于表示收藏 -->

<script setup lang="ts">
interface Props {
  size?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  size: 16
})

// 计算样式
const iconStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  minWidth: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size
}))
</script>

<template>
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
        class="larkui-icon larkui-icon-star-outlined icon-svg index-module_icon_hhi0u index-module_size_wVASz"
        data-name="StarOutlined" :style="iconStyle">
        <path
            d="m109.698 24.732-22.514 51.09-55.546 5.624a20 20 0 0 0-12.899 6.573c-7.36 8.236-6.649 20.88 1.587 28.24l41.632 37.2-11.815 54.565a20 20 0 0 0 2.265 14.298l.185.313c5.643 9.302 17.723 12.402 27.163 6.904L128 201.44l48.244 28.1a20 20 0 0 0 14.298 2.264c10.796-2.338 17.652-12.984 15.315-23.78l-11.815-54.566 41.632-37.2a20 20 0 0 0 6.572-12.898c1.113-10.99-6.894-20.8-17.884-21.914l-55.546-5.625-22.514-51.09a20 20 0 0 0-10.237-10.235c-10.107-4.455-21.912.128-26.367 10.236ZM128 32.798l22.515 51.089A20 20 0 0 0 166.8 95.719l55.546 5.626-41.631 37.2a20 20 0 0 0-6.22 19.146l11.814 54.566-48.244-28.099a20 20 0 0 0-20.132 0L69.69 212.257l11.815-54.566a20 20 0 0 0-6.221-19.146l-41.631-37.2 55.546-5.626a20 20 0 0 0 16.286-11.832L128 32.798Z"
            fill="currentColor" fill-rule="nonzero"></path>
    </svg>
</template>