<template>

        <a-form class="baseline-form">
          <a-row :gutter="16">
            <a-col :span="12">
            <a-form-item label="规则库名称 :">
                <span>{{ engName }}</span>
            </a-form-item>
            </a-col>
            <a-col :span="12">
            <a-form-item label="基线版本 ：">
                <span>{{ edition }}</span>
            </a-form-item>
            </a-col>
          </a-row>
            <a-form-item style="float: right;margin-bottom: 8px">
                <a-button type="primary" @click="exportRule" style="margin-right: 8px" :disabled="isExporting">导出html</a-button>
                <a-button type="primary" @click="exportExcelRule" style="margin-right: 8px" :disabled="isExporting">导出excel</a-button>
            </a-form-item>
            <div ref="basePoint">
            <TableSkeleton
                v-if="loading"
                :columns="getTableColumns()"
                :limit="paginations.limit"
                :scrollY="scrollY-30"
            />
            <a-table
                    v-else
                    :data-source="tableDate"
                    :loading="loading"
                    :pagination="false"
                    :scroll="{y: scrollY-30 }"
                    size="small"
            >
                <a-table-column title="序号" key="index" :width="50" align="center">
                    <template #default="{ index }">
                        <span>{{ (paginations.page - 1) * paginations.limit + index + 1 }}</span>
                    </template>
                </a-table-column>
                <a-table-column title="规则名称" dataIndex="ruleName" key="ruleName" align="left" :width="200">
                    <template #default="{ record }">
                        <a
                            @click="rel_details(record)"
                            class="rule-name-link"
                            v-hasPermi="[RULE_PERMISSION.SNAPSHOOT.RULE_DETAIL]"
                        >{{ record.ruleName }}</a>
                    </template>
                </a-table-column>
                <a-table-column title="规则路径" dataIndex="packageNameAll" key="packageNameAll" align="left" :width="300">
                    <template #default="{ record }">
                        <RulePath
                                :path="record.packageNameAll"
                                showCopyButton
                        />
                    </template>
                </a-table-column>
                <a-table-column title="描述" dataIndex="descs" key="descs" align="left" :width="200"  ellipsis="true"/>
                <a-table-column title="版本" dataIndex="edition" key="edition" align="center" :width="70" />
                <a-table-column title="类型" dataIndex="type" key="type" align="left" :width="100" />
                <a-table-column title="DTL数量" dataIndex="beforeTextDtl" key="beforeTextDtl" align="center" :width="100"/>
            </a-table>
                <div style="margin-right: 20px">
                    <Pagination
                            :paginations="paginations"
                            @change="pagin"
                            :scrollY="scrollY-100"
                            mode="ruleBase"
                    />
                </div>
            </div>
        </a-form>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import store from '@/store';
import { RULE_PERMISSION } from "@/consts/rulePermissionConsts";
import TableSkeleton from '@/components/TableSkeleton.vue';

import { getBaseLineRuleInfo, getExportRule, getExportExcelRule } from '@/api/baseline_Management';

//计算自适应高度
const basePoint = ref();
const { scrollY } = useResize(basePoint);
const message = inject('message')
const modal = inject('modal')
const router = useRouter();
const tableDate = ref<any[]>([]);
const ruleUid = ref('');
const paginations = ref({
    loading: true,
    total: 0, // 总数
    limit: 8, // 一页显示都是条
    page: 1, // 当前页
    showSizeChanger:true,
    showTotal: (total) => `共 ${total} 条数据`,
});
const currentPage = ref(0); // 当前页码
const engName = ref(''); // 规则库名称
const edition = ref('');
const loading = ref(false);
const pageStyle = ref('');
const tableStyleChanged = ref(false);
const defaultMarginTop = ref(0);
const trHeight = ref(0);
const marTop = ref(0);
const isExporting = ref(false); // 添加导出状态标记

const linkOptions = defineProps<{ linkOptions: object }>();

onMounted(() => {
    ruleUid.value = (linkOptions.linkOptions as any).detailsId || '';
    engName.value = (linkOptions.linkOptions as any).detailsName || '';
    edition.value = (linkOptions.linkOptions as any).edition || '';

    if (ruleUid.value) {
        getRuleBaseInfo();
    }
});

const getRuleBaseInfo = async () => {
    loading.value = true;
    try {
        const res = await getBaseLineRuleInfo({
            uuid: ruleUid.value,
            page: paginations.value.page,
            several: paginations.value.limit,
        });
        loading.value = false;
        tableDate.value = res.data.data;
        paginations.value.total = res.data.totalCount;
        await nextTick();
        const docHeight = document.documentElement.clientHeight;
        if (!tableStyleChanged.value) {
            const formHeight = document.querySelector('.baseline_Management')?.offsetHeight || 0;
            marTop.value = docHeight - formHeight - 75;
            trHeight.value = Math.floor(marTop.value / 11) + 32;
            const elemTr = document.querySelectorAll('.el-table tr');
            if (marTop.value > 0) {
                elemTr.forEach(tr => {
                    tr.style.height = `${trHeight.value}px`;
                });
            }
            const _formHeight = document.querySelector('.baseline_Management')?.offsetHeight || 0;
            const _marTop = docHeight - _formHeight - 75;
            if (_marTop > 0) {
                pageStyle.value = `margin-top:${_marTop - 1}px`;
                defaultMarginTop.value = _marTop - 1;
            }
        } else {
            if (tableDate.value.length < 10) {
                pageStyle.value = `margin-top:${
                    defaultMarginTop.value +
                    (10 - tableDate.value.length) * trHeight.value
                }px`;
            } else {
                const elemTr = document.querySelectorAll('.el-table tr');
                if (marTop.value > 0) {
                    elemTr.forEach(tr => {
                        tr.style.height = `${trHeight.value}px`;
                    });
                }
                pageStyle.value = `margin-top:${defaultMarginTop.value}px`;
            }
        }
        tableStyleChanged.value = true;
    } catch (error) {
        loading.value = false;
        message.error('获取数据失败');
    }
};

const pagin = (cur: number,pageSize = parseInt((scrollY.value)/40)) => {
    paginations.value.page = cur;
    paginations.value.limit = pageSize;
    getRuleBaseInfo();
};

const emit = defineEmits(['datailsTest']);
const rel_details = (targetName: any) => {

    store.commit("settings/Base_ID", targetName);

    emit("datailsTest", targetName);
};

const exportRule = async () => {
    if (isExporting.value) return; // 如果正在导出，则不执行

    modal.confirm({
        title: () => "提示",
        content: () => "您确定要导出HTML格式文件吗?",
        okText: () => '确定',
        okType: 'primary',
        cancelText: () => '取消',
        onOk: async () => {
            // 确认后执行导出
            isExporting.value = true; // 设置导出状态为true
            const loadingMessage = message.loading('正在导出...', 0);
            try {
                const res = await getExportRule({ uuid: ruleUid.value });
                const headers = res.headers;
                const blob = new Blob([res.data], {
                    type: headers['content-type'],
                });
                const link = document.createElement('a');
                const url = window.URL.createObjectURL(blob);
                const fileName = headers['content-disposition']
                    .split(';')[1]
                    .split('=')[1];
                link.href = url;
                link.download = decodeURI(fileName);
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                loadingMessage();
                message.success('导出成功');
            } catch (error) {
                message.error('导出失败');
            } finally {
                isExporting.value = false; // 重置导出状态
                loadingMessage();
            }
        }
    });
};

const exportExcelRule = async () => {
    if (isExporting.value) return; // 如果正在导出，则不执行

    modal.confirm({
        title: () => "提示",
        content: () => "您确定要导出Excel格式文件吗?",
        okText: () => '确定',
        okType: 'primary',
        cancelText: () => '取消',
        onOk: async () => {
            // 确认后执行导出
            isExporting.value = true; // 设置导出状态为true
            const loadingMessage = message.loading('正在导出...', 0);
            try {
                const res = await getExportExcelRule({ uuid: ruleUid.value });
                const headers = res.headers;
                const blob = new Blob([res.data], {
                    type: headers['content-type'],
                });
                const link = document.createElement('a');
                const url = window.URL.createObjectURL(blob);
                const fileName = headers['content-disposition']
                    .split(';')[1]
                    .split('=')[1];
                link.href = url;
                link.download = decodeURI(fileName);
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                message.success('导出成功');
            } catch (error) {
                message.error('导出失败');
            } finally {
                isExporting.value = false; // 重置导出状态
                loadingMessage();
            }
        }
    });
};

// 获取表格列定义，用于骨架屏
const getTableColumns = () => {
    return [
        { title: '序号', key: 'index', width: 50, align: 'center' },
        { title: '规则名称', key: 'ruleName', width: 200, align: 'left' },
        { title: '规则路径', key: 'packageNameAll', width: 300, align: 'left' },
        { title: '描述', key: 'descs', width: 200, align: 'left' },
        { title: '版本', key: 'edition', width: 70, align: 'center' },
        { title: '类型', key: 'type', width: 100, align: 'left' },
        { title: 'DTL数量', key: 'beforeTextDtl', width: 100, align: 'center' }
    ];
};
</script>
<style lang="scss" scoped>
.baseline-form {
    margin-right:20px;
    margin-left:20px;
    :deep(.ant-form-item) {
        .ant-form-item-label > label {
            font-weight: 600;
            font-size: 14px;
            margin-left: 20px;
        }
    }
}

.rule-name-link {
    color: #1890ff;
    cursor: pointer;
    &:hover {
        text-decoration: none;
    }
}
</style>
