// ==UserScript==
// @name         语雀文档转内置文档生成器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  从语雀文档自动生成并导出内置文档格式的工具脚本
// <AUTHOR> Name
// @match        https://teratech.yuque.com/org-wiki-teratech-ebpxyp/*
// @grant        GM_xmlhttpRequest
// @connect      cdn.nlark.com
// @connect      teratech.yuque.com
// @connect      cdn.jsdelivr.net
// @require      https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置项
    const CONFIG = {
        // 按钮配置
        BUTTON_TEXT: '提取erule3.0帮助文档',
        LOADING_TEXT: '正在提取...',
        
        // 目标配置
        TARGET: {
            MENU_NAME: "erule3.0帮助文档"
        },
        
        // 操作延迟配置
        MENU_EXPAND_DELAY: 2000,
        TOOLTIP_DELAY: 500,
        RETRY_COUNT: 3,
        RETRY_DELAY: 1000,
        DEBOUNCE_DELAY: 1000,
        
        // DOM选择器配置
        SELECTORS: {
            EXPAND_ICON: '.larkui-icon-catalog-fold, .larkui-icon-catalog-expand',
            EXPAND_BUTTON: '.CatalogTreeCollapseTrigger-module_actionItem_ym\\+tH, [class*="CatalogTreeCollapseTrigger"]',
            TOOLTIP: '.ant-tooltip-inner',
            MENU_CONTAINER: '.lark-virtual-tree',  // 虚拟滚动容器
            MENU_CONTENT: '.lark-virtual-tree > div',  // 内容容器
            MENU_ITEM: '.catalogTreeItem-module_CatalogItem_xkX7p, [class*="catalogTreeItem-module_CatalogItem"], .menu-item',
            MENU_TITLE: '.catalogTreeItem-module_title_P4GeN, [class*="catalogTreeItem-module_title"], .menu-title',
            MENU_GROUP: '.hasChildren, .menu-group',
            MENU_TREE_CONTAINER: '.lark-virtual-tree div, [class*="lark-virtual-tree"] div, .doc-menu',
            MENU_VIRTUAL_TREE: '.lark-virtual-tree',  // 虚拟树容器
            MENU_VIRTUAL_CONTENT: '.lark-virtual-tree > div',  // 虚拟树内容容器
            MENU_LINK: '[class*="catalogTreeItem-module_content"]',  // 菜单项链接
            MENU_ITEM_ACTIONS: '.catalogTreeItem-module_catalogItemActionsWrapper_rlWUC',  // 菜单项操作按钮容器
            DOC_CONTENT: '.ReaderLayout-module_wrapper_l-8F\\+',  // 文档内容容器
            DOC_CONTENT_FALLBACK: '.ne-doc-major',  // 备用文档内容选择器
            READER_CONTAINER: '[data-testid="ReaderLayout:reader-main"]',  // 阅读器主容器
            CONTENT: [
                '.ReaderLayout-module_wrapper_l-8F\\+',   // 首选选择器
                '[class*="ReaderLayout-module_wrapper"]', // 备用，用属性选择器
                '.lake-content',                         // 备用选择器1
                '.ne-doc-major',                         // 备用选择器2
                '#reader-main',                          // 备用选择器3
                '.article-content',                      // 备用选择器4
                'main'                                   // 备用选择器5
            ]
        },
        
        // 文本匹配配置
        TEXT: {
            TOOLTIP_EXPANDED: '全部折叠'
        },
        DELAY: 2000,                   // 通用延迟时间
        DELAY_AFTER_LOAD: 2000,        // 页面加载后的延迟时间
        DELAY_BETWEEN_REQUESTS: 1000,  // 请求之间的延迟时间 
        TIMEOUT: 30000                // 加载超时时间
    };
    
    // 调试日志函数
    function log(message, ...args) {
        console.log(`[DocExtractor] ${message}`, ...args);
    }
    
    // 延迟函数
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 图片下载和处理函数
    async function downloadImage(imageUrl) {
        try {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: imageUrl,
                    responseType: 'blob',
                    onload: function(response) {
                        if (response.status >= 200 && response.status < 300) {
                            resolve(response.response);
                        } else {
                            reject(new Error(`图片下载失败: ${response.status} ${response.statusText}`));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error(`图片下载请求错误: ${error}`));
                    }
                });
            });
        } catch (error) {
            log(`下载图片失败 ${imageUrl}:`, error);
            return null;
        }
    }
    
    function getImageFileName(url) {
        // 从URL中提取文件名
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        
        // 提取原始文件名
        let filename = pathname.split('/').pop();
        
        // 处理查询参数（例如：image.jpg?x-oss-process=image）
        if (filename.includes('?')) {
            filename = filename.split('?')[0];
        }
        
        // 确保有正确的扩展名
        if (!/\.(jpg|jpeg|png|gif|webp|svg)$/i.test(filename)) {
            // 从Content-Type或URL推断扩展名
            if (url.includes('.jpg') || url.includes('.jpeg')) {
                filename += '.jpg';
            } else if (url.includes('.png')) {
                filename += '.png';
            } else if (url.includes('.gif')) {
                filename += '.gif';
            } else if (url.includes('.webp')) {
                filename += '.webp';
            } else if (url.includes('.svg')) {
                filename += '.svg';
            } else {
                // 默认扩展名
                filename += '.png';
            }
        }
        
        return filename;
    }
    
    // 检查菜单展开状态并在需要时展开
    async function checkAndExpandMenu(retryCount = CONFIG.RETRY_COUNT) {
        for (let i = 0; i < retryCount; i++) {
            try {
                log(`开始第${i + 1}次检查菜单展开状态...`);
                
                const expandButton = document.querySelector(CONFIG.SELECTORS.EXPAND_ICON)
                    .closest(CONFIG.SELECTORS.EXPAND_BUTTON);
                
                if (!expandButton) {
                    log('未找到展开按钮');
                    continue;
                }
                
                log('找到展开按钮:', expandButton);
                
                // 修改这里：使用更简单的方式触发hover事件
                // 不再使用 MouseEvent 构造函数
                expandButton.dispatchEvent(new Event('mouseover', { bubbles: true }));
                await delay(CONFIG.TOOLTIP_DELAY);
                
                const tooltipText = document.querySelector(CONFIG.SELECTORS.TOOLTIP)?.textContent?.trim();
                log('初始Tooltip文字:', tooltipText);
                
                if (tooltipText === CONFIG.TEXT.TOOLTIP_EXPANDED) {
                    // 当前是"全部折叠"状态，需要先点击使其展开，再点击使其折叠
                    log('按钮当前是"全部折叠"状态，需要双击确保完全展开');
                    
                    // 第一次点击 - 变为"全部展开"
                    await clickExpandButton(expandButton);
                    await delay(CONFIG.MENU_EXPAND_DELAY);
                    
                    // 第二次点击 - 变回"全部折叠"
                    const success = await clickExpandButton(expandButton);
                    if (success) return true;
                    
                } else {
                    // 当前是"全部展开"状态，只需点击一次
                    log('按钮当前是"全部展开"状态，点击一次即可');
                    const success = await clickExpandButton(expandButton);
                    if (success) return true;
                }
                
                // 如果点击失败，继续重试
                continue;
                
            } catch (error) {
                log(`第${i + 1}次展开尝试失败:`, error);
                if (i < retryCount - 1) {
                    await delay(CONFIG.RETRY_DELAY);
                }
            }
        }
        return false;
    }
    
    // 点击展开按钮并处理结果
    async function clickExpandButton(button) {
        log('尝试点击展开按钮...');
        
        try {
            // 使用事件分发代替直接调用click方法
            const clickEvent = new Event('click', {
                bubbles: true,
                cancelable: true
            });
            button.dispatchEvent(clickEvent);
            log('已分发点击事件到展开按钮');
            
            await delay(CONFIG.MENU_EXPAND_DELAY);
            
            // 再次检查tooltip文字
            const newTooltipText = document.querySelector(CONFIG.SELECTORS.TOOLTIP)?.textContent?.trim();
            log('点击后Tooltip文字:', newTooltipText);
            
            const nowExpanded = newTooltipText === CONFIG.TEXT.TOOLTIP_EXPANDED;
            log(`点击后菜单${nowExpanded ? '已展开' : '仍未展开'}`);
            
            return nowExpanded;
        } catch (error) {
            log('点击展开按钮时出错:', error);
            return false;
        }
    }
    
    // 自动滚动菜单以确保所有项都被加载
    async function scrollMenuToLoadAll() {
        log('开始自动滚动菜单以加载所有项...');
        
        // 找到菜单滚动容器
        const menuContainer = document.querySelector(CONFIG.SELECTORS.MENU_VIRTUAL_TREE);
        if (!menuContainer) {
            log('未找到菜单滚动容器，无法自动滚动');
            return;
        }
        
        // 获取容器高度
        const containerHeight = menuContainer.getBoundingClientRect().height;
        log(`菜单容器高度: ${containerHeight}px`);
        
        // 记录初始滚动位置
        const initialScrollTop = menuContainer.scrollTop;
        
        // 滚动步长和暂停时间
        const scrollStep = 200; // 每次滚动200px
        const pauseTime = 300;  // 每次滚动后暂停300ms
        
        // 自动滚动到底部，然后再滚回顶部
        let currentScrollTop = 0;
        
        // 先滚到顶部确保从头开始
        menuContainer.scrollTop = 0;
        await delay(pauseTime);
        
        // 向下滚动直到到达底部
        log('向下滚动菜单...');
        let reachedBottom = false;
        let lastScrollTop = -1;
        
        while (!reachedBottom) {
            currentScrollTop = menuContainer.scrollTop;
            
            // 如果滚动位置没有变化，说明已经到达底部
            if (currentScrollTop === lastScrollTop) {
                reachedBottom = true;
                break;
            }
            
            lastScrollTop = currentScrollTop;
            menuContainer.scrollTop += scrollStep;
            await delay(pauseTime);
        }
        
        log('到达菜单底部，开始向上滚动...');
        
        // 滚回顶部
        menuContainer.scrollTop = 0;
        await delay(pauseTime);
        
        log('菜单自动滚动完成，所有项应该已加载');
        
        // 恢复到原始滚动位置
        menuContainer.scrollTop = initialScrollTop;
        
        return true;
    }
    
    // 恢复并优化目标菜单结构获取函数
    async function getTargetMenuStructure() {
        // 等待菜单完全展开
        await checkAndExpandMenu();
        log('菜单已完全展开，开始提取菜单结构');
        
        // 自动滚动菜单以确保所有项都被加载
        await scrollMenuToLoadAll();
        
        // 获取菜单容器
        const menuContainer = document.querySelector(CONFIG.SELECTORS.MENU_TREE_CONTAINER);
        if (!menuContainer) {
            throw new Error('未找到菜单容器');
        }
        
        // 获取整个菜单的外层容器结构
        const outerMenuContainer = document.querySelector('.BookCatalog-module_sideCatalog_wpCdP') || 
                                  document.querySelector('[class*="BookCatalog-module"]') ||
                                  menuContainer.closest('.BookCatalog-module_sideCatalog_wpCdP') ||
                                  menuContainer.closest('[class*="BookCatalog-module"]');
        
        log(`找到外层菜单容器: ${outerMenuContainer ? '是' : '否'}`);
        
        // 克隆整个菜单DOM结构
        let menuDom = outerMenuContainer ? outerMenuContainer.cloneNode(true) : menuContainer.cloneNode(true);
        log('成功克隆完整菜单DOM结构');
        
        // 移除所有操作按钮
        const actionButtons = menuDom.querySelectorAll('.catalogTreeItem-module_catalogItemActionsWrapper_rlWUC');
        actionButtons.forEach(button => button.remove());
        log(`已移除 ${actionButtons.length} 个操作按钮`);
        
        // 修复虚拟滚动容器的高度样式
        const virtualScrollContainer = menuDom.querySelector('.lark-virtual-tree');
        if (virtualScrollContainer) {
            // 修改虚拟滚动容器本身的高度为100%
            const containerStyle = virtualScrollContainer.getAttribute('style');
            const newContainerStyle = containerStyle.replace(/height:\s*\d+px/, 'height: 100%');
            virtualScrollContainer.setAttribute('style', newContainerStyle);
            log('已将虚拟滚动容器高度修改为100%');

            // 修改内容容器的高度
            const contentContainer = virtualScrollContainer.children[0];
            if (contentContainer) {
                const contentStyle = contentContainer.getAttribute('style');
                const newContentStyle = contentStyle.replace(/height:\s*\d+px/, 'height: auto');
                contentContainer.setAttribute('style', newContentStyle);
                log('已将内容容器高度修改为自适应');
            }

            // 移除多余的包裹层
            const wrapperDiv = virtualScrollContainer.parentElement;
            if (wrapperDiv && wrapperDiv.style.overflow === 'visible' && 
                wrapperDiv.style.height === '0px' && wrapperDiv.style.width === '0px') {
                // 将虚拟滚动容器移到包裹层的父元素下
                const parentElement = wrapperDiv.parentElement;
                parentElement.insertBefore(virtualScrollContainer, wrapperDiv);
                wrapperDiv.remove();
                log('已移除多余的包裹层div');
            }
        }
        
        // 找到目标菜单项（erule3.0帮助文档）
        const targetMenuName = CONFIG.TARGET.MENU_NAME;
        const menuItems = Array.from(menuDom.querySelectorAll(CONFIG.SELECTORS.MENU_ITEM));
        let targetMenuElement = null;
        
        for (const item of menuItems) {
            const titleEl = item.querySelector(CONFIG.SELECTORS.MENU_TITLE);
            const title = titleEl ? titleEl.textContent.trim() : '';
            if (title === targetMenuName) {
                targetMenuElement = item;
                // 找到目标菜单后，将其收起按钮替换为占位空间
                const collapseButton = targetMenuElement.querySelector('.catalogTreeItem-module_collapseContent_09v0m.catalogTreeItem-module_hasChildren_TrI8X');
                if (collapseButton) {
                    // 创建一个占位 div，保持相同宽度
                    const placeholder = document.createElement('div');
                    placeholder.style.width = '28px';
                    placeholder.style.display = 'inline-block';
                    // 替换原有的收起按钮
                    collapseButton.parentNode.replaceChild(placeholder, collapseButton);
                    log('已将根菜单的收起按钮替换为占位空间');
                }
                break;
            }
        }
        
        if (!targetMenuElement) {
            log(`未找到目标菜单: "${targetMenuName}"`);
            return { menuDom, menuItemsInfo: [] };
        }
        
        log(`找到目标菜单: "${targetMenuName}"`);
        
        // 修改计算深度的函数，同时获取元素的位置信息
        const getMenuItemInfo = (element) => {
            const contentDiv = element.querySelector('[class*="catalogTreeItem-module_content"]');
            if (!contentDiv) return { depth: 0, top: 0 };
            const paddingLeft = parseInt(contentDiv.style.paddingLeft) || 0;
            const rect = contentDiv.getBoundingClientRect();
            return {
                depth: Math.floor(paddingLeft / 24), // 假设每级缩进24px
                top: rect.top
            };
        };

        // 获取目标菜单的信息
        const targetInfo = getMenuItemInfo(targetMenuElement);
        
        // 找出目标菜单的所有子菜单
        const relevantMenuItems = menuItems.filter((item, index) => {
            if (index < menuItems.indexOf(targetMenuElement)) return false;
            
            const itemInfo = getMenuItemInfo(item);
            
            if (index === menuItems.indexOf(targetMenuElement)) return true; // 保留目标菜单
            
            // 判断是否为子菜单：
            // 1. 深度必须大于目标菜单
            // 2. 位置必须在目标菜单之后
            // 3. 如果遇到同级或更浅的菜单，说明已经超出了目标菜单的范围
            if (itemInfo.depth <= targetInfo.depth) {
                return false; // 不是子菜单
            }
            
            // 查找当前项到目标菜单之间是否有同级或更浅的菜单
            for (let i = menuItems.indexOf(targetMenuElement) + 1; i < index; i++) {
                const intermediateInfo = getMenuItemInfo(menuItems[i]);
                if (intermediateInfo.depth <= targetInfo.depth) {
                    return false; // 发现中间有同级或更浅的菜单，说明已经跳出了目标菜单的范围
                }
            }
            
            return true; // 是子菜单
        });
        
        log(`找到相关菜单项: ${relevantMenuItems.length} 个`);
        
        // 移除不相关的菜单项
        menuItems.forEach(item => {
            if (!relevantMenuItems.includes(item)) {
                item.remove();
            }
        });
        
        // 收集菜单信息
        const menuItemsInfo = relevantMenuItems.map(item => {
            const titleEl = item.querySelector(CONFIG.SELECTORS.MENU_TITLE);
            const title = titleEl ? titleEl.textContent.trim() : '';
                    const link = item.querySelector('a');
            const href = link ? link.getAttribute('href') : null;
            const isGroup = item.classList.contains('hasChildren') || 
                           item.querySelector(CONFIG.SELECTORS.MENU_GROUP) !== null;

            return {
                                title,
                                isGroup,
                                link: href,
                slug: href ? href.split('/').pop() : null,
                hasLink: !!href
            };
        });
        
        log(`提取了 ${menuItemsInfo.length} 个菜单项信息`);
        
        return { menuDom, menuItemsInfo };
    }
    
    function getMenuItemLevel(item) {
        // 尝试从样式或结构中获取层级信息
        // 这里使用一个简单的实现，可以根据实际DOM结构调整
        let level = 0;
        
        // 检查是否有缩进样式或类
        const indent = item.style.marginLeft || item.style.paddingLeft;
        if (indent) {
            // 假设每16px是一个层级
            level = parseInt(indent) / 16;
        }
        
        // 检查是否在子菜单容器中
        let parent = item.parentElement;
        while (parent) {
            if (parent.tagName === 'UL' || parent.tagName === 'OL') {
                level++;
            }
            parent = parent.parentElement;
        }
        
        // 检查是否有层级相关的类名
        if (item.classList.contains('level-1')) level = 1;
        if (item.classList.contains('level-2')) level = 2;
        if (item.classList.contains('level-3')) level = 3;
        
        // 默认返回0表示顶级菜单
        return Math.max(0, level);
    }
    
    // 修改生成Vue组件内容的函数
    function generateVueComponent(menuDom) {
        if (!menuDom) {
            console.warn('Menu DOM is empty')
            return `
              <template>
                <div class="BookCatalog-module_sideCatalog_wpCdP">Failed to generate menu</div>
              </template>
            `
        }

        try {
            // 获取菜单DOM的HTML
            let menuHTML = menuDom.outerHTML;
            
            // 替换链接标签和路径
            menuHTML = menuHTML
                .replace(/<a\s/g, '<NuxtLink ') 
                .replace(/<\/a>/g, '</NuxtLink>')
                // 替换 href 为 to，并添加正确的类绑定
                .replace(/href="\/org-wiki-teratech-ebpxyp\/tl5g77\/([^"]+)"/g, (match, slug) => {
                    return `to="/doc/${slug}" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/${slug}' }"`;
                })
                // 处理可能存在的绝对路径URL
                .replace(/href="https?:\/\/[^\/]+\/org-wiki-teratech-ebpxyp\/tl5g77\/([^"]+)"/g, (match, slug) => {
                    return `to="/doc/${slug}" :class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '/doc/${slug}' }"`;
                })
                // 移除原有的选中和激活状态类
                .replace(/\s*(catalogTreeItem-module_selected_dK6Ji|active)\s*/g, '')
                // 修复空类绑定的问题
                .replace(/:class="\{\s*'\s*':\s*currentPath\s*===\s*'([^']+)'\s*\}"/g, 
                    (match, path) => `:class="{ 'catalogTreeItem-module_selected_dK6Ji': currentPath === '${path}' }"`);
            
            // 创建Vue组件
            return `
<template>
  <div class="BookCatalog-module_sideCatalog_wpCdP">
    <div class="lark-virtual-tree" style="position: relative; height: 100%; width: 259px; overflow: auto; will-change: transform; direction: ltr;">
      <div style="height: auto; width: 100%;">
        ${menuHTML}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const currentPath = computed(() => route.path)

onMounted(() => {
  const collapseButtons = document.querySelectorAll('.catalogTreeItem-module_hasChildren_TrI8X')
  
  collapseButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation()
      e.preventDefault()
      
      const iconWrapper = button.querySelector('.catalogTreeItem-module_collapseIconWrapper_XcS8B')
      iconWrapper.classList.toggle('catalogTreeItem-module_collapsed_u8yPq')
      
      const menuItem = button.closest('.catalogTreeItem-module_CatalogItem_xkX7p')
      const currentPadding = parseInt(menuItem.querySelector('.catalogTreeItem-module_content_fLFbS').style.paddingLeft) || 0
      
      let nextItem = menuItem.nextElementSibling
      let offset = 0
      let itemsToUpdate = []
      
      // 首先收集需要隐藏的项目和它们的高度
      while (nextItem) {
        const nextPadding = parseInt(nextItem.querySelector('.catalogTreeItem-module_content_fLFbS').style.paddingLeft) || 0
        if (nextPadding <= currentPadding) break
        
        itemsToUpdate.push(nextItem)
        offset += 36 // 每个项的高度
        nextItem = nextItem.nextElementSibling
      }
      
      // 获取所有后续项目
      const allItems = Array.from(document.querySelectorAll('.catalogTreeItem-module_CatalogItem_xkX7p'))
      const startIndex = allItems.indexOf(menuItem)
      const subsequentItems = allItems.slice(startIndex + itemsToUpdate.length + 1)
      
      if (iconWrapper.classList.contains('catalogTreeItem-module_collapsed_u8yPq')) {
        // 收起状态
        itemsToUpdate.forEach(item => {
          item.style.display = 'none'
        })
        
        // 更新后续项目的位置
        subsequentItems.forEach(item => {
          const currentTop = parseInt(item.style.top)
          item.style.top = \`\${currentTop - offset}px\`
        })
      } else {
        // 展开状态
        itemsToUpdate.forEach(item => {
          item.style.display = ''
        })
        
        // 恢复后续项目的位置
        subsequentItems.forEach(item => {
          const currentTop = parseInt(item.style.top)
          item.style.top = \`\${currentTop + offset}px\`
        })
      }
      
      // 更新容器高度
      const container = document.querySelector('.lark-virtual-tree > div')
      if (container) {
        const visibleItems = document.querySelectorAll('.catalogTreeItem-module_CatalogItem_xkX7p:not([style*="display: none"])')
        container.style.height = \`\${visibleItems.length * 36}px\`
      }
    }, { capture: true })
  })
})
</script>

<style scoped>
.BookCatalog-module_sideCatalog_wpCdP {
  height: calc(100vh - 165px);
}

.catalogTreeItem-module_collapseIconWrapper_XcS8B {
  transition: transform 0.2s ease;
}

.catalogTreeItem-module_collapsed_u8yPq {
  transform: rotate(-90deg);
}

.catalogTreeItem-module_CatalogItem_xkX7p {
  transition: top 0.2s ease;
}
</style>`;
        } catch (error) {
            console.error('Error generating Vue component:', error)
            return `
              <template>
                <div class="BookCatalog-module_sideCatalog_wpCdP">Error generating menu: ${error.message}</div>
              </template>
            `
        }
    }
    
    // 修改生成文档Vue组件
    function generateDocComponent(content) {
        return `<template>
  ${content}
</template>

<script setup>
definePageMeta({
  layout: 'layout-doc'
})
</script>`;
    }
    
    // 修改浏览文档的函数，增强对未加载图片的处理
    async function browseDocument(docUrl, title, updateProgress) {
        log(`开始浏览文档: ${title}`);
        updateProgress(`正在浏览文档: ${title}`);
        
        // 打开新窗口
        const win = window.open(docUrl, '_blank');
        if (!win) {
            throw new Error('无法打开新窗口，请允许弹出窗口');
        }
        
        try {
            // 等待页面基本加载完成
            await new Promise((resolve, reject) => {
                const initialCheck = () => {
                    try {
                        if (win.closed) {
                            reject(new Error('窗口被关闭'));
                            return;
                        }
                        
                        if (win.document.readyState === 'complete') {
                            resolve();
                        } else {
                            setTimeout(initialCheck, 1000);
                        }
                    } catch (error) {
                        setTimeout(initialCheck, 2000);
                    }
                };
                
                setTimeout(initialCheck, 3000);
                
                // 设置初始加载超时
                setTimeout(() => {
                    resolve(); // 即使超时也继续，避免完全失败
                }, 15000);
            });
            
            log(`文档 "${title}" 基本加载完成，开始滚动加载所有内容...`);
            updateProgress(`正在加载文档 "${title}" 的所有内容...`);
            
            // 添加辅助函数，强制加载未加载的图片
            const forceImagesLoad = () => {
                try {
                    // 查找所有图片包装器，特别是那些没有ne-image-loaded类的
                    const imageWraps = win.document.querySelectorAll('.ne-image-wrap:not(.ne-image-loaded)');
                    log(`找到 ${imageWraps.length} 个未加载的图片包装器`);
                    
                    imageWraps.forEach(wrap => {
                        // 尝试触发滚动事件来加载图片
                        const rect = wrap.getBoundingClientRect();
                        if (rect.top < win.innerHeight * 2) {
                            // 如果图片在可视区域内或接近可视区域，模拟滚动到该位置
                            win.scrollTo(0, Math.max(0, rect.top + win.pageYOffset - 100));
                        }
                        
                        // 添加已加载类
                        wrap.classList.add('ne-image-loaded');
                        
                        // 查找图片元素
                        const imgBox = wrap.querySelector('.ne-image-box');
                        const img = imgBox ? imgBox.querySelector('img') : null;
                        
                        if (img && !img.src) {
                            // 尝试从各种可能的属性获取图片URL
                            const possibleSrc = img.dataset.src || 
                                              img.dataset.original || 
                                              wrap.dataset.src || 
                                              imgBox.dataset.src;
                            
                            if (possibleSrc) {
                                img.src = possibleSrc;
                                log(`为未加载图片设置了src: ${possibleSrc}`);
                            } else {
                                // 如果没有找到src，尝试从背景图片中提取
                                const style = window.getComputedStyle(imgBox);
                                const bgImage = style.backgroundImage;
                                if (bgImage && bgImage !== 'none') {
                                    const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                                    if (urlMatch && urlMatch[1]) {
                                        img.src = urlMatch[1];
                                        log(`从背景图片提取了src: ${urlMatch[1]}`);
                                    }
                                }
                            }
                        }
                    });
                    
                    // 查找所有懒加载图片容器
                    const lazyImageBoxes = win.document.querySelectorAll('.ne-image-box-loading');
                    log(`找到 ${lazyImageBoxes.length} 个懒加载图片容器`);
                    
                    lazyImageBoxes.forEach(box => {
                        // 移除loading类
                        box.classList.remove('ne-image-box-loading');
                        
                        // 查找隐藏的图片
                        const hiddenImg = box.querySelector('.ne-image-hide');
                        if (hiddenImg) {
                            // 移除隐藏类
                            hiddenImg.classList.remove('ne-image-hide');
                            
                            // 如果图片没有src属性，尝试从data-src获取
                            if (!hiddenImg.src && hiddenImg.dataset.src) {
                                hiddenImg.src = hiddenImg.dataset.src;
                            }
                            
                            // 如果图片没有src属性，尝试从data-original获取
                            if (!hiddenImg.src && hiddenImg.dataset.original) {
                                hiddenImg.src = hiddenImg.dataset.original;
                            }
                        }
                    });
                    
                    // 查找所有没有src的图片
                    const imagesWithoutSrc = win.document.querySelectorAll('img:not([src]), img[src=""]');
                    log(`找到 ${imagesWithoutSrc.length} 个没有src的图片`);
                    
                    imagesWithoutSrc.forEach(img => {
                        // 尝试从data-src或data-original获取src
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                        } else if (img.dataset.original) {
                            img.src = img.dataset.original;
                        }
                    });
                } catch (error) {
                    log(`强制加载图片时出错: ${error.message}`);
                }
            };
            
            // 滚动页面以加载所有内容
            await new Promise((resolve) => {
                try {
                    // 获取文档高度
                    const getDocHeight = () => {
                        const body = win.document.body;
                        const html = win.document.documentElement;
                        return Math.max(
                            body.scrollHeight, body.offsetHeight,
                            html.clientHeight, html.scrollHeight, html.offsetHeight
                        );
                    };
                    
                    // 获取当前滚动位置
                    const getScrollPosition = () => {
                        return win.scrollY || win.pageYOffset;
                    };
                    
                    // 获取窗口高度
                    const getWindowHeight = () => {
                        return win.innerHeight;
                    };
                    
                    // 判断是否已滚动到底部
                    const isAtBottom = () => {
                        const scrollPosition = getScrollPosition();
                        const windowHeight = getWindowHeight();
                        const docHeight = getDocHeight();
                        
                        // 考虑到一些误差，如果滚动位置+窗口高度接近文档高度，就认为到达底部
                        return scrollPosition + windowHeight >= docHeight - 50;
                    };
                    
                    let scrollAttempts = 0;
                    const maxScrollAttempts = 30; // 最多尝试滚动30次
                    
                    const scrollDown = async () => {
                        try {
                            if (win.closed) {
                                resolve();
                                return;
                            }
                            
                            scrollAttempts++;
                            
                            // 每次滚动都尝试强制加载图片
                            forceImagesLoad();
                            
                            // 如果已经到达底部或者尝试次数过多，则结束滚动
                            if (isAtBottom() || scrollAttempts > maxScrollAttempts) {
                                log(`文档 "${title}" 已滚动到底部或达到最大尝试次数`);
                                
                                // 最后再滚动一次到绝对底部确保所有内容加载
                                const docHeight = getDocHeight();
                                win.scrollTo(0, docHeight);
                                
                                // 最后再次尝试强制加载图片
                                forceImagesLoad();
                                
                                // 等待3秒让图片加载
                                await new Promise(r => setTimeout(r, 3000));
                                
                                // 再次尝试强制加载图片
                                forceImagesLoad();
                                
                                resolve();
                                return;
                            }
                            
                            // 获取当前滚动位置和窗口高度
                            const currentPosition = getScrollPosition();
                            const windowHeight = getWindowHeight();
                            
                            // 每次滚动一个窗口高度的90%，确保有重叠部分
                            const scrollStep = Math.floor(windowHeight * 0.9);
                            const newPosition = currentPosition + scrollStep;
                            
                            // 执行滚动
                            win.scrollTo(0, newPosition);
                            
                            // 记录滚动前的高度
                            const heightBeforeWait = getDocHeight();
                            
                            // 等待500ms让内容加载
                            await new Promise(r => setTimeout(r, 500));
                            
                            // 检查高度是否变化
                            const heightAfterWait = getDocHeight();
                            if (heightAfterWait > heightBeforeWait) {
                                // 如果高度变化，说明有新内容加载，多等待一会
                                await new Promise(r => setTimeout(r, 500));
                            }
                            
                            // 继续滚动
                            scrollDown();
                        } catch (error) {
                            log(`滚动过程中出错: ${error.message}`);
                            // 出错时也继续尝试
                            setTimeout(scrollDown, 1000);
                        }
                    };
                    
                    // 开始滚动
                    scrollDown();
                } catch (error) {
                    log(`设置滚动时出错: ${error.message}`);
                    resolve(); // 出错时也继续流程
                }
            });
            
            // 最后再次尝试强制加载所有图片
            log(`最后尝试强制加载所有图片...`);
            
            // 再次尝试强制加载图片
            const finalForceImagesLoad = () => {
                try {
                    // 查找所有未加载的图片包装器
                    const imageWraps = win.document.querySelectorAll('.ne-image-wrap:not(.ne-image-loaded)');
                    log(`最终检查: 找到 ${imageWraps.length} 个未加载的图片包装器`);
                    
                    // 直接修改DOM，强制显示图片
                    imageWraps.forEach(wrap => {
                        // 添加已加载类
                        wrap.classList.add('ne-image-loaded');
                        
                        // 查找图片元素
                        const imgBox = wrap.querySelector('.ne-image-box');
                        const img = imgBox ? imgBox.querySelector('img') : null;
                        
                        if (img && !img.src) {
                            // 尝试从各种可能的属性获取图片URL
                            const possibleSrc = img.dataset.src || 
                                              img.dataset.original || 
                                              wrap.dataset.src || 
                                              imgBox.dataset.src;
                            
                            if (possibleSrc) {
                                img.src = possibleSrc;
                                log(`为未加载图片设置了src: ${possibleSrc}`);
                            } else {
                                // 如果没有找到src，尝试从背景图片中提取
                                try {
                                    const style = window.getComputedStyle(imgBox);
                                    const bgImage = style.backgroundImage;
                                    if (bgImage && bgImage !== 'none') {
                                        const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                                        if (urlMatch && urlMatch[1]) {
                                            img.src = urlMatch[1];
                                            log(`从背景图片提取了src: ${urlMatch[1]}`);
                                        }
                                    }
                                } catch (e) {
                                    log(`提取背景图片时出错: ${e.message}`);
                                }
                            }
                        }
                    });
                    
                    // 查找所有懒加载图片容器
                    const lazyImageBoxes = win.document.querySelectorAll('.ne-image-box-loading');
                    log(`最终检查: 找到 ${lazyImageBoxes.length} 个懒加载图片容器`);
                    
                    // 直接修改DOM，强制显示图片
                    lazyImageBoxes.forEach(box => {
                        // 移除loading类
                        box.classList.remove('ne-image-box-loading');
                        
                        // 查找隐藏的图片
                        const hiddenImg = box.querySelector('.ne-image-hide');
                        if (hiddenImg) {
                            // 移除隐藏类
                            hiddenImg.classList.remove('ne-image-hide');
                            
                            // 如果图片没有src属性，尝试从其他属性获取
                            if (!hiddenImg.src) {
                                // 尝试从各种可能的属性获取图片URL
                                const possibleSrc = hiddenImg.dataset.src || 
                                                  hiddenImg.dataset.original || 
                                                  hiddenImg.getAttribute('original') ||
                                                  box.dataset.src;
                                
                                if (possibleSrc) {
                                    hiddenImg.src = possibleSrc;
                                    log(`为隐藏图片设置了src: ${possibleSrc}`);
                                }
                            }
                        }
                    });
                    
                    // 查找所有没有src的图片
                    const imagesWithoutSrc = win.document.querySelectorAll('img:not([src]), img[src=""]');
                    log(`最终检查: 找到 ${imagesWithoutSrc.length} 个没有src的图片`);
                    
                    imagesWithoutSrc.forEach(img => {
                        // 尝试从各种可能的属性获取图片URL
                        const possibleSrc = img.dataset.src || 
                                          img.dataset.original || 
                                          img.getAttribute('original');
                        
                        if (possibleSrc) {
                            img.src = possibleSrc;
                            log(`为空src图片设置了src: ${possibleSrc}`);
                        }
                    });
                } catch (error) {
                    log(`最终强制加载图片时出错: ${error.message}`);
                }
            };
            
            // 执行最终的图片加载
            finalForceImagesLoad();
            
            // 等待额外的时间让图片加载
            await new Promise(r => setTimeout(r, 5000));
            
            // 再次执行最终的图片加载
            finalForceImagesLoad();
            
            // 确保所有图片加载完成
            await new Promise((resolve) => {
                try {
                    const checkImages = () => {
                        try {
                            if (win.closed) {
                                resolve();
                                return;
                            }
                            
                            // 再次尝试强制加载图片
                            finalForceImagesLoad();
                            
                            const images = win.document.querySelectorAll('img');
                            const totalImages = images.length;
                            const loadedImages = Array.from(images).filter(img => img.complete && img.src).length;
                            const unloadedWraps = win.document.querySelectorAll('.ne-image-wrap:not(.ne-image-loaded)').length;
                            
                            log(`文档 "${title}" 图片加载状态: ${loadedImages}/${totalImages}, 未加载包装器: ${unloadedWraps}`);
                            updateProgress(`正在加载文档 "${title}" 的图片: ${loadedImages}/${totalImages}`);
                            
                            // 如果所有图片都加载完成，或者加载了大部分图片，并且没有未加载的包装器
                            if ((loadedImages === totalImages || loadedImages > 0.95 * totalImages) && unloadedWraps === 0) {
                                resolve();
                            } else {
                                // 继续等待
                                setTimeout(checkImages, 1000);
                            }
                        } catch (error) {
                            log(`检查图片时出错: ${error.message}`);
                            setTimeout(checkImages, 1000);
                        }
                    };
                    
                    checkImages();
                    
                    // 设置最长等待时间
                    setTimeout(() => {
                        log(`文档 "${title}" 图片加载超时，继续处理`);
                        resolve();
                    }, 20000); // 最多等待20秒
                } catch (error) {
                    log(`设置图片检查时出错: ${error.message}`);
                    resolve(); // 出错时也继续流程
                }
            });
            
            log(`文档 "${title}" 内容和图片加载完成，开始提取内容`);
            
            // 获取文档内容
            const doc = win.document;
            
            // 最后一次尝试处理未加载的图片
            const processUnloadedImages = () => {
                // 查找所有未加载的图片包装器
                const imageWraps = doc.querySelectorAll('.ne-image-wrap:not(.ne-image-loaded)');
                log(`内容提取前: 找到 ${imageWraps.length} 个未加载的图片包装器`);
                
                imageWraps.forEach(wrap => {
                    // 添加已加载类
                    wrap.classList.add('ne-image-loaded');
                    
                    // 查找图片元素
                    const imgBox = wrap.querySelector('.ne-image-box');
                    const img = imgBox ? imgBox.querySelector('img') : null;
                    
                    if (img && !img.src) {
                        // 如果没有找到src，尝试从背景图片中提取
                        try {
                            // 尝试从语雀的图片服务器构造一个可能的URL
                            // 这是一个猜测，基于观察到的语雀图片URL模式
                            const imgId = wrap.id || imgBox.id || Math.random().toString(36).substring(2, 15);
                            const possibleYuqueUrl = `https://cdn.nlark.com/yuque/${imgId}.png`;
                            img.src = possibleYuqueUrl;
                            log(`为未加载图片构造了可能的URL: ${possibleYuqueUrl}`);
                        } catch (e) {
                            log(`构造图片URL时出错: ${e.message}`);
                        }
                    }
                });
                
                // 查找所有懒加载图片容器
                const lazyImageBoxes = doc.querySelectorAll('.ne-image-box-loading');
                log(`内容提取前: 找到 ${lazyImageBoxes.length} 个懒加载图片容器`);
                
                lazyImageBoxes.forEach(box => {
                    // 移除loading类
                    box.classList.remove('ne-image-box-loading');
                    
                    // 查找隐藏的图片
                    const hiddenImg = box.querySelector('.ne-image-hide');
                    if (hiddenImg) {
                        // 移除隐藏类
                        hiddenImg.classList.remove('ne-image-hide');
                    }
                });
            };
            
            // 执行最后的图片处理
            processUnloadedImages();
            
            // 收集图片信息
            const images = Array.from(doc.querySelectorAll('img')).map(img => ({
                src: img.src || img.dataset.src || img.dataset.original,
                width: img.width,
                height: img.height,
                alt: img.alt || '',
                class: img.className
            })).filter(info => info.src && (info.src.includes('cdn.nlark.com') || info.src.includes('/yuque/')));
            
            log(`找到 ${images.length} 张图片`);
            
            // 克隆文档内容
            const content = doc.cloneNode(true);
            
            return {
                doc: content,
                images
            };
            
        } catch (error) {
            log(`浏览文档出错:`, error);
            throw error;
        } finally {
            // 关闭窗口
            win.close();
        }
    }
    
    // 修改下载文件函数，顺序下载所有文件
    async function downloadFiles(vueContent, { documents, imageFiles }) {
        try {
            // 下载菜单组件
            log('下载菜单组件...');
            const menuBlob = new Blob([vueContent], { type: 'text/plain' });
            const menuUrl = URL.createObjectURL(menuBlob);
            const menuLink = document.createElement('a');
            menuLink.href = menuUrl;
            menuLink.download = 'DocMenu.vue';
            document.body.appendChild(menuLink);
            menuLink.click();
            document.body.removeChild(menuLink);
            URL.revokeObjectURL(menuUrl);
            
            await delay(300);
            
            // 下载文档组件
            log('下载文档组件...');
            for (const doc of documents) {
                if (!doc.slug || doc.slug === 'undefined') {
                    log(`跳过无效文档: ${doc.title} (slug无效)`);
                    continue;
                }
                
                const fileName = `${doc.slug}.vue`;
                const docBlob = new Blob([doc.content], { type: 'text/plain' });
                const docUrl = URL.createObjectURL(docBlob);
                const docLink = document.createElement('a');
                docLink.href = docUrl;
                docLink.download = fileName;
                document.body.appendChild(docLink);
                docLink.click();
                document.body.removeChild(docLink);
                URL.revokeObjectURL(docUrl);
                
                // 添加延迟避免浏览器限制
                await delay(300);
            }
            
            log('文档组件下载完成');
            
            // 下载图片
            log('开始下载图片...');
            for (const img of imageFiles) {
                const url = URL.createObjectURL(img.content);
                const a = document.createElement('a');
                a.href = url;
                a.download = img.fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                // 添加延迟避免浏览器限制
                await delay(300);
            }
            
            log(`图片下载完成，共 ${imageFiles.length} 张图片`);
            log("所有文件导出完成");
            
        } catch (error) {
            log('下载文件时出错:', error);
            throw error;
        }
    }
    
    // 修改fetchDocumentContent函数，确保图片路径正确更新
    async function fetchDocumentContent(menuItemsInfo, updateProgress) {
        const documents = [];
        const imageFiles = [];
        
        log(`开始提取 ${menuItemsInfo.length} 个文档内容...`);
        updateProgress(`开始提取 ${menuItemsInfo.length} 个文档内容...`);
        
        try {
            // 过滤出有效的文档
            const validDocuments = menuItemsInfo.filter(item => item.hasLink && !item.isGroup);
            log(`找到 ${validDocuments.length} 个有效文档链接`);
            
            // 处理每个文档
            for (let i = 0; i < validDocuments.length; i++) {
                const { title, slug, link } = validDocuments[i];
                
                try {
                    // 构建完整URL
                    const baseUrl = window.location.origin;
                    const docUrl = link.startsWith('http') ? link : 
                                  (link.startsWith('/') ? baseUrl + link : baseUrl + '/' + link);
                    
                    // 浏览文档并获取内容
                    const { doc, images } = await browseDocument(docUrl, title, updateProgress);
                    
                    // 处理图片
                    const imgElements = doc.querySelectorAll('img');
                    for (const img of imgElements) {
                        const imgSrc = img.src;
                        if (!imgSrc) continue;
                        
                        // 只处理来自 cdn.nlark.com 或 yuque 的图片，过滤掉其他域名的图片
                        if (!(imgSrc.includes('cdn.nlark.com') || imgSrc.includes('/yuque/'))) {
                            log(`跳过非目标域名图片: ${imgSrc}`);
                            continue;
                        }
                        
                        // 检查是否已经处理过相同的图片URL
                        const existingImg = imageFiles.find(file => file.originalSrc === imgSrc);
                        
                        if (existingImg) {
                            // 如果已经处理过，直接更新图片路径
                            // 确保路径格式正确，直接设置为绝对路径
                            img.src = `/img/doc/${existingImg.fileName}`;
                            log(`使用已存在的图片: ${existingImg.fileName}`);
                        } else {
                            try {
                                // 下载新图片
                                updateProgress(`正在下载文档 "${title}" 的图片...`);
                                const imgContent = await downloadImage(imgSrc);
                                // 如果下载失败，跳过此图片
                                if (!imgContent) {
                                    log(`图片下载失败，跳过: ${imgSrc}`);
                                    continue;
                                }
                                
                                const fileName = getImageFileName(imgSrc);
                                
                                // 更新图片路径 - 确保使用正确的绝对路径格式
                                img.src = `/img/doc/${fileName}`;
                                
                                // 添加到图片文件列表
                                imageFiles.push({
                                    fileName,
                                    content: imgContent,
                                    originalSrc: imgSrc
                                });
                                
                                log(`已处理图片: ${fileName}`);
                            } catch (error) {
                                log(`处理图片失败: ${imgSrc}`, error);
                                // 出错时，将图片路径替换为占位图或移除
                                img.src = '/img/doc/placeholder.png';
                            }
                        }
                        
                        // 额外检查，确保没有错误的路径格式
                        if (img.src.includes('/BRMS/erule-web/&/img/doc/') || 
                            img.src.includes('&/img/doc/') ||
                            img.src.includes('&amp;/img/doc/')) {
                            // 修复错误的路径
                            const correctPath = img.src.replace(/\/BRMS\/erule-web\/&\/img\/doc\//, '/img/doc/')
                                                   .replace(/&\/img\/doc\//, '/img/doc/')
                                                   .replace(/&amp;\/img\/doc\//, '/img/doc/');
                            img.src = correctPath;
                            log(`修复了错误的图片路径: ${correctPath}`);
                        }
                    }
                    
                    // 生成文档组件
                    const contentElement = doc.querySelector('.ReaderLayout-module_wrapper_l-8F\\+');
                    if (contentElement) {
                        // 移除不需要的元素
                        const selectorsToRemove = [
                            '#header',
                            '.article-content-reward',
                            '.DocReader-module_info_yXA4e',
                            '.DocReader-module_comment_eDglS',
                            '#doc-reader-comment',
                            '.index-module_wordCount_Wzj9E',
                            '.index-module_commentPanel_rHJ\\+M',
                            '#footer'
                        ];
                        
                        selectorsToRemove.forEach(selector => {
                            contentElement.querySelectorAll(selector).forEach(el => el.remove());
                        });
                        
                        // 最后再检查一次所有图片路径，确保格式正确
                        const allImagesInContent = contentElement.querySelectorAll('img');
                        allImagesInContent.forEach(img => {
                            if (img.src.includes('/BRMS/erule-web/&/img/doc/') || 
                                img.src.includes('&/img/doc/') ||
                                img.src.includes('&amp;/img/doc/')) {
                                // 修复错误的路径
                                const correctPath = img.src.replace(/\/BRMS\/erule-web\/&\/img\/doc\//, '/img/doc/')
                                                       .replace(/&\/img\/doc\//, '/img/doc/')
                                                       .replace(/&amp;\/img\/doc\//, '/img/doc/');
                                img.src = correctPath;
                                log(`最终检查时修复了错误的图片路径: ${correctPath}`);
                            }
                        });
                        
                        const component = generateDocComponent(contentElement.outerHTML);
                        
                        // 处理slug
                        let cleanSlug = slug.includes('tl5g77/') ? slug.split('tl5g77/')[1] : slug;
                        
                        documents.push({
                            title,
                            slug: cleanSlug,
                            content: component,
                            originalSlug: slug,
                            originalLink: link
                        });
                        
                        log(`成功提取文档 "${title}"`);
                    }
                    
                } catch (error) {
                    log(`处理文档 "${title}" 时出错:`, error);
                }
                
                // 避免频繁请求
                await delay(1000);
            }
            
        } catch (error) {
            log('提取文档内容时出错:', error);
        }
        
        log(`共提取了 ${documents.length} 个文档，${imageFiles.length} 张图片`);
        return { documents, imageFiles };
    }
    
    // 创建控制按钮
    function createControlButton() {
        const button = document.createElement('button');
        button.textContent = CONFIG.BUTTON_TEXT;
        button.style.position = 'fixed';
        button.style.bottom = '20px';
        button.style.right = '20px';
        button.style.zIndex = '10000';
        button.style.padding = '8px 16px';
        button.style.backgroundColor = '#1890ff';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '4px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';
        
        // 创建进度提示元素
        const progressInfo = document.createElement('div');
        progressInfo.style.position = 'fixed';
        progressInfo.style.bottom = '60px';
        progressInfo.style.right = '20px';
        progressInfo.style.zIndex = '10000';
        progressInfo.style.padding = '8px 16px';
        progressInfo.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        progressInfo.style.color = 'white';
        progressInfo.style.borderRadius = '4px';
        progressInfo.style.display = 'none';
        document.body.appendChild(progressInfo);
        
        // 更新进度信息的函数
        const updateProgress = (text) => {
            progressInfo.textContent = text;
            progressInfo.style.display = 'block';
        };
        
        // 修改按钮点击处理函数
        const controlButtonClickHandler = async () => {
            try {
                // 禁用按钮,防止重复点击
                button.disabled = true;
                button.textContent = '正在处理...请稍候';
                
                // 提取菜单结构
                updateProgress('正在提取菜单结构...');
                log('开始提取菜单结构...');
                const menuStructure = await getTargetMenuStructure();
                log('菜单结构提取完成');
                
                // 生成Vue组件
                updateProgress('正在生成菜单组件...');
                log('开始生成菜单组件...');
                const vueContent = generateVueComponent(menuStructure.menuDom);
                log('菜单组件生成完成');
                
                // 提取文档内容和图片
                log('开始提取文档内容和图片...');
                const { documents, imageFiles } = await fetchDocumentContent(menuStructure.menuItemsInfo, updateProgress);
                log(`文档内容提取完成，共 ${documents.length} 个文档，${imageFiles.length} 张图片`);
                
                // 下载所有文件
                updateProgress('正在下载文件...');
                log('开始下载文件...');
                await downloadFiles(vueContent, { documents, imageFiles });
                log('文件下载完成');
                
                // 恢复按钮状态
                button.textContent = '提取成功！点击重新提取';
                button.disabled = false;
                
            } catch (error) {
                // 恢复按钮状态
                button.textContent = '提取失败！点击重试';
                button.disabled = false;
                
                log('提取过程中发生错误:', error);
                updateProgress(`提取过程中发生错误: ${error.message}`);
                alert(`提取过程中发生错误: ${error.message}\n\n请刷新页面重试或联系开发者。`);
            }
        };
        
        button.addEventListener('click', debounce(controlButtonClickHandler, CONFIG.DEBOUNCE_DELAY));
        
        document.body.appendChild(button);
        return button;
    }
    
    // 主函数
    async function main() {
        log('文档提取脚本已加载');
        
        if (document.readyState !== 'complete') {
            await new Promise(resolve => window.addEventListener('load', resolve, { once: true }));
        }
        
        await delay(1000);
        createControlButton();
    }
    
    // 添加错误边界处理
    window.addEventListener('unhandledrejection', event => {
        log('未处理的Promise错误:', event.reason);
        alert('操作出现意外错误，请刷新页面重试');
    });
    
    // 启动
    main();
})();