@use "@/assets/css/oneDark.scss" as *;

// theme oneDark_Light
.eRuleEditorContainer.oneDark_Light {
  color: $oneDark_Light-editor-fontColor;
  border-color: $oneDark_Light-editor-border-color;
  border: red;

  .designView {
    .ruleBody {

      .actionCom {
        .actionTypeItem {
          color: $oneDark_Light-editor-action-actionTypeItem-color;
          font-weight: bold;
        }
      }
    }
  }

  .switcher {
    color: $oneDark_Light-editor-switcher-color;
  }

  .logicBtn {
    background-color: $oneDark_Light-editor-ruleBody-bgc;
  }

  .logicBtn.logicBtnAnd,
  .logicBtn.logicBtnAnd span {
    border-color: $oneDark_Light-editor-logicBtn-and-color;
    color: $oneDark_Light-editor-logicBtn-and-color;
    font-weight: bold;
  }


  .logicBtn.logicBtnOr {
    border-color: $oneDark_Light-editor-logicBtn-or-color;
    color: $oneDark_Light-editor-logicBtn-or-color;
    font-weight: bold;
  }


  .methodCom {
    color: $oneDark_Light-editor-condition-methodCom-color;
  }

  .inputItem input,
  .inputItem span {
    font-weight: bold;
  }

  .ant-calendar-picker-input.ant-input {
    color: #000;
    font-weight: normal;
  }

  .operator,
  .methodCom .txtItem {
    color: #000;
    font-weight: bold;
  }

  .operator .ant-select-selection-selected-value,
  .methodCom .fieldCom .txtItem,
  .methodCom .ant-select,
  .anticon-tool {
    color: #000;
    font-weight: normal !important;
  }

  span[enumdictname*='domain'] .ant-select-selection-selected-value {
    color: #000;
    font-weight: bold !important;
  }

  .operator .booleanSelect .ant-select-selection-selected-value {
    color: #000090;
    font-weight: bold !important;
  }

  .calculateSign,
  .brackets {
    color: $oneDark_Light-editor-condition-calculateSign-color;
  }

  .txtItem:hover,
  .comTypeHandler:hover {
    border-color: $oneDark_Light-editor-condition-txtItem-hoverBorder-color;
  }

  .joiner {
    color: $oneDark_Light-editor-joiner-color;
    font-weight: bold;
  }
}

.eRuleEditorContainer {
  width: 100%;

  .table-prede,
  .table-code,
  .rule-prede,
  .rule-design,
  .rule-code {
    height: calc(100vh - 190px) !important;
  }

  .table-design,
  .table-val {
    height: 100%;
  }

  .eRuleEditor {
    height: 100%;
    display: flex;
    flex-direction: column;

    .toolBar {
      .ant-tabs-tab:not(:last-child) {
        span {
          display: inline-block;
          position: relative;
        }

        span::after {
          content: "";
          position: absolute;
          right: -30px;
          display: inline-block;
          width: 1px;
          height: 100%;
          background-color: #e8e8e8;
        }
      }

      .btnList {
        .btnItem {
          .iconfont {
            color: #3483f8;
          }
        }

        .btnItem:last-child {
          .ant-divider {
            display: none;
          }
        }
      }
    }

    .editorBody {
      flex: 1;
      overflow: hidden;
      font-size: 12px;

      .codeView {
        height: 100%;
        border: 1px solid lightgrey;

        .react-codemirror2 {
          height: 100%;

          .CodeMirror {
            height: 100%;
          }
        }
      }
    }
  }

  .predefineView {
    margin-left: 16px;
    margin-top: 5px;
    overflow: auto !important;
    padding-bottom: 400px !important;

    .iconContainer {
      display: inline-block;
      margin-left: 5px !important;
      width: 16px;
      height: 16px;
      cursor: pointer;
      vertical-align: bottom;
      background-size: contain;
      position: relative;
      top: -2px
    }

    .pre-none {
      margin-top: 20px;
      margin-left: 40px;
      font-size: 12px;
      color: gray;
    }

    .pre-txt {
      margin: 0 5px;
    }

    .color-red {
      color: #000
    }

    .pre-title {
      font-size: 12px;
      font-weight: 600;
      margin-top: 10px;
    }

    .pre-rule-content {
      position: relative;
      padding-left: 15px;
      padding-top: 10px;

      .designView {
        margin-top: 0;
        .ruleBody {
          padding-bottom: 0 !important;
          overflow-y: unset !important;
        }
      }
    }
    .pre-rule-contentlast-c{
      position: relative;
      padding-left: 15px;
      padding-top: 10px;
      .designView {
        .ruleBody {
          padding-bottom: 0 !important;
          overflow-y: unset !important;
        }
      }
    }

    .pre-rule-add {
      margin-bottom: 40px;
    }
  }

  p {
    margin-bottom: 0;
  }

  .designView {
    .ruleBody {
      padding-left: 16px;
      margin-top: 10px;
      font-size: 12px;
      font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
      overflow-y: auto;
      height: 90%;
      
      .conditionCom {
        position: relative;
        margin-left: 20px;
        position: relative;
        top: -2px;
        &.tableCon{
          top: 0px;
        }
        
        .condition {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-left: 2px;
          border-radius: 2px;
          padding: 4px;

          &>span,
          &>button {
            z-index: 1;
          }
        }

        .condition.executeTrue {
          box-shadow: 0px 0px 3px 0px green;
        }

        .condition.executeFalse {
          box-shadow: 0px 0px 3px 0px red;
        }
      }

      .actionCom {
        display: inline-block;
        margin-left: 15px;
        white-space: nowrap;

        .setValueCom {
          display: inline-block;

          .setValueComLeft {
            display: inline-block;
          }

          .operator.eq {
            margin-right: 4px;
          }

          .setValueComRight {
            display: inline-block;
          }
        }
      }

      .iconContainer {
        display: inline-block;
        margin-left: 5px !important;
        width: 14px;
        height: 14px;
        cursor: pointer;
        background-size: contain;
      }

      // 如连接字符"的"
      .joiner {
        padding-left: 0px;
        padding-right: 0px;
        margin-left: -3px;
        margin-right: 1px;
      }

      .structuralWord {
        font-size: 14px;
        font-weight: bold;
        color: #000090;
      }
    }
  }

  // ruleItemList样式
  .switcher {
    width: 17px;
    height: 17px;
    border: 0;
    border-radius: 50%;
    padding: 0;
    display: inline-block;
    text-align: center;
    position: absolute;
    transform: translateX(-50%) translateY(0px);
    background-color: #fff;
    cursor: pointer;
  }

  .logicBtn.ant-btn {
    height: 18px;
    padding: 0;
    border: 0;
    transform: translateX(-50%);
    overflow: hidden;

    div {
      width: 100%;
      height: 100%;
    }
  }


  .fieldCom,
  .methodCom,
  .dataEntryCom,
  .expressionCom {
    display: inline-block;
    position: relative;

    &>.ant-cascader-picker {
      position: absolute;
      top: 2.2em;
      left: 0px;
      width: 100px;
    }
  }

  .inputItem {
    display: inline-flex;
    align-items: center;
    height: 28px;

    .ant-input-number {
      height: 100%;
      border: none;
      border-bottom: 1px solid #d9d9d9;

      .ant-input-number-input {
        height: 100%;
        background-color: transparent;
        color: #0000ff !important;
        font-weight: nor;
        font-size: 12px;
      }
    }

    .ant-input {
      height: 100%;
      border: none;
      border-bottom: 1px solid #d9d9d9;
      background-color: transparent;
      padding: 0px 10px;
    }

    .txtItem {
      height: 100%;
    }
  }

  .ant-select-arrow {
    font-size: 9px !important;
  }

  .operator:focus,
  input:focus,
  .operator>span:focus {
    outline: none;
  }

  .symbolCom {
    display: inline-block;
    margin: 0 5px;
  }

  .txtItem {
    border: 1px dotted transparent;
    margin-right: 4px;
    display: inline-block;
  }

  .txtItem:hover {
    border: 1px dotted;
    cursor: pointer;
  }

  .lockedText,
  .lockedText .txtItem {
    border: none !important;
    cursor: default;
  }


  // 其他样式
  div::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
  }

  div::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
  }

  div::-webkit-scrollbar-track-piece {
    background: transparent;
  }

  .ant-cascader-picker {
    width: 100%;
  }

  .float-right {
    float: right;
  }

  // 媒体查询相关
  .ruleDescription1 {
    height: 46px;
    line-height: 46px;
    text-align: center;
    margin-top: 10px;
    margin-left: 10px;

    .ruleDescriptionInput {
      width: 350px;
      height: 36px;
      line-height: 36px;
    }
  }

  .ruleDescription2 {
    text-align: center;
    margin-top: 10px;
    margin-left: 10px;

    span {
      position: relative;
      top: -76px;
    }

    .ruleDescriptionText {
      width: 350px;
      line-height: 46px;
    }
  }

  // 覆盖 antd 的样式
  // 覆盖Cascader下拉框，避免向右的箭头和内容重合

  .rc-cascader-menu-item {
    width: 130px;
  }

  .ant-cascader-menu-item.ant-cascader-menu-item-expand {
    padding-right: 22px;
  }

  .ant-cascader-menus {
    &>div {
      ::-webkit-scrollbar {
        width: 4px;
        background-color: #fff;
      }

      ::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 2px;
      }
    }
  }


  .ant-tabs-bar {
    margin: 0
  }

  .ant-select-selection {
    background-color: transparent;
    border-style: none none solid none;
  }

  .txtItem {
    .ant-select-selection {
      background-color: transparent;
      border-style: none none solid none;
    }

    .ant-input-number {
      background-color: transparent;
    }

    // 枚举选择框的高度
    .ant-select-selection--single {
      height: inherit;

      .ant-select-selection__rendered {
        line-height: inherit;
      }
    }
  }

  .ant-select-selection-selected-value {
    font-size: 12px;
  }

  .variableCom {
    .inputItem {
      .txtItem {
        .ant-input {
          font-size: 12px;
          outline: none !important;
        }
      }
    }
  }

  .predefineView {
    .action {
      .inputItem {
        .txtItem {
          .ant-input {
            font-size: 12px;
            outline: none !important;
          }
        }
      }
    }
  }

  .variableCom:focus,
  .inputItem:focus,
  .txtItem:focus,
  .ant-input:focus,
  .ant-select-selection:focus,
  .ant-select:focus,
  .ant-select-selection__rendered:focus,
  .ant-select-selection-selected-value:focus,
  .variableCom,
  .inputItem,
  .txtItem,
  .ant-input,
  .ant-select-selection,
  .ant-select,
  .ant-select-selection__rendered,
  .ant-select-selection-selected-value,
  .ant-input-number {
    outline: none !important;
    box-shadow: none !important;
  }

  .ant-select-selection,
  .ant-select-selection:hover,
  .ant-select-selection:active {
    border-color: #d9d9d9 !important
  }

  .ant-cascader-menu-item.ant-cascader-menu-item-expand {
    max-width: 250px;
  }

  .ant-select {
    min-width: 100px;
  }

  .action {
    min-width: 2000px;

    .structuralWord {
      font-size: 14px;
      font-weight: bold;
      color: #000090;
    }
  }

  .ant-cascader-menu {
    max-width: 300px;
  }

  .conditionCom .condition .fieldCom,
  .actionCom .fieldCom {
    color: #827700;
    font-weight: bold;
  }

  .actionCom .fieldCom .fieldCom,
  .actionCom .fieldCom .methodCom,
  .conditionCom .condition .fieldCom .fieldCom,
  .conditionCom .condition .fieldCom .methodCom {
    color: $oneDark_Light-editor-condition-fieldCom-color;
    font-weight: normal;
  }

  .calculateSign.txtItem.ant-dropdown-trigger {
    color: #000;
    font-weight: normal;
  }

  .eRuleEditorContainer .predefineView .pre-rule-content .designView {
    margin-top: 0;
  }

  .eRuleEditorContainer .actionTypeItem.txtItem {
    font-weight: bold;
  }

  .editor-table {
    margin-top: 20px;
  }
  .ant-select-selector,.ant-input,.ant-picker,.ant-input-number{
    border-radius: 0 !important;
    border-style: none none solid none !important;
    font-size: 12px !important;
    height: 21px !important;
    line-height: 21px !important;
    padding: 0 !important;
  }
  .ant-input-number-input,.ant-input{
    text-align: center !important;

  }
  #check_add_vars,#text_add_vars{
    .ant-input{
      text-align: left !important;
    }
  }
  textarea.ant-input{
    border-style: solid !important;
    height: unset !important;
  }
  .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder{
    line-height: 20px !important;
  }

  .designView {
    .ruleBody {
      .conditionCom {
        .condition {
          transition: all 0.3s ease;
          
          &:hover {
            background-color: rgba(19, 129, 216, 0.05);
            box-shadow: 0 0 0 1px rgba(19, 129, 216, 0.3);
          }
        }
      }

      .actionCom {
        display: inline-block;
        margin-left: 15px;
        white-space: nowrap;

        .setValueCom {
          display: inline-block;

          .setValueComLeft {
            display: inline-block;
          }

          .operator.eq {
            margin-right: 4px;
          }

          .setValueComRight {
            display: inline-block;
          }
        }
      }
    }
  }

}
.global-cascader-container{
  z-index: 999999999 !important;
}
.global-cascader.ant-select-dropdown,.ant-select-dropdown {
  border: none !important;
  overflow: unset !important;
}
.ant-message{
  left: 50% !important;
}

// 整行容器只有边框高亮，不改变背景色
.action-content-highlight {
  border: 1px dotted transparent;
  transition: all 0.2s ease-in-out;
}

// 通用高亮效果样式
.common-highlight {
  border: 1px dotted transparent;
  transition: all 0.3s ease;
  border-radius: 3px;
  padding: 4px;
  min-height: 26px;
  line-height: 26px;
  display: inline-block;
}

.common-highlight:hover {
  background-color: rgba(19, 129, 216, 0.05);
  box-shadow: 0 0 0 1px rgba(19, 129, 216, 0.3);
  border-radius: 3px;
  cursor: pointer;
}

// 预定义页面中满足条件位置修复
.pre-title {
  position: relative !important;
  z-index: 10 !important;
  display: block !important;
  
  .ant-btn-link {
    &:hover {
      background-color: rgba(19, 129, 216, 0.05);
      color: #1381d8 !important;
    }
  }
}

// 图标容器悬停样式
.iconContainer {
  cursor: pointer;
  
  &:hover {
    color: #1381d8 !important;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}

.predefineView {
  .pre-rule-content {
    position: relative !important;
    z-index: 5 !important;
  }
  
  .designView {
    position: relative !important;
    z-index: 5 !important;
    
    .ruleBody {
      position: relative !important;
      z-index: 5 !important;
    }
  }
}