<!-- 默认模版 -->
<script setup lang="ts">
  import UserInfoPopover from '@/components/UserInfoPopover.vue'
  import MainMenu from '@/businessComponents/dashboard/MainMenu.vue'
  import GlobalSearchBox from '@/components/dashboard/GlobalSearchBox.vue'
  import ChooseRuleBaseModal from '~/businessComponents/dashboard/ChooseRuleBaseModal.vue'
  import { MenuFoldOutlined, MenuUnfoldOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';

  // 侧边栏收起状态
  const collapsed = ref(false);

  // 侧边栏宽度
  const sidebarWidth = computed(() => collapsed.value ? 60 : 180);

  // 切换侧边栏收起状态
  const toggleCollapsed = () => {
    collapsed.value = !collapsed.value;
  };

  // 获取当前路由
  const route = useRoute();

  // 监听路由变化，当路径包含ruleBase-时自动收起侧边栏
  watch(
    () => route.path,
    (newPath) => {
      if (newPath.includes('ruleBase-')) {
        collapsed.value = true;
      }
    },
    { immediate: true } // 页面加载时立即执行一次
  );

  // 创建动态标题映射
  const dynamicTitles = ref(new Map());

  // 提供全局方法更新标题
  const updateRouteTitle = (path: string, title: string) => {
    dynamicTitles.value.set(path, title);
  };

  // 全局提供更新标题方法
  provide('updateRouteTitle', updateRouteTitle);

  // 路由名称映射表（作为备用）
  const routeTitles: Record<string, string> = {
    'dashboard': '首页',
    'ruleBase': ' ',
    'rulePackage': ' ',
    'statistics': '统计',
    'recycles': '回收站',
    'doc': '文档'
  };

  // 获取路由标题
  const getRouteTitle = (path: string, routeObj?: any): string => {
    // 首先检查动态标题映射
    const routePath = route.path;
    if (dynamicTitles.value.has(routePath)) {
      return dynamicTitles.value.get(routePath);
    }
    // 如果有路由对象，尝试从meta中获取title
    // 注意：在页面中需要使用definePageMeta来定义页面标题，例如：
    // definePageMeta({
    //   title: '页面标题'
    // });
    if (routeObj && routeObj.meta && routeObj.meta.title) {
      return routeObj.meta.title;
    }

    // 处理动态路由参数
    const parts = path.split('-');
    const basePath = parts[0];

    // 尝试从路由名称映射表中获取标题（作为备用）
    if (routeTitles[basePath]) {
      return routeTitles[basePath];
    }

    // 如果没有匹配的标题，则格式化路径名
    return basePath.charAt(0).toUpperCase() + basePath.slice(1);
  };

  // 生成面包屑导航数据
  const breadcrumbItems = computed(() => {
    const router = useRouter();
    const routes = router.getRoutes();

    // 处理路径，移除末尾的斜杠
    let normalizedPath = route.path;
    if (normalizedPath.endsWith('/') && normalizedPath.length > 1) {
      normalizedPath = normalizedPath.slice(0, -1);
    }

    const paths = normalizedPath.split('/').filter(Boolean);
    const items = [];

    // 添加首页
    const homeRoute = routes.find(r => r.path === '/dashboard' || r.path === '/');
    items.push({
      title: homeRoute?.meta?.title || '首页',
      path: '/dashboard'
    });

    // 如果当前就是首页，则只显示首页
    if (normalizedPath === '/dashboard' || normalizedPath === '/') {
      return items;
    }

    // 根据路径生成面包屑
    let currentPath = '';
    // 用于跟踪已添加的标题，防止重复
    const addedTitles = new Set();

    for (let i = 0; i < paths.length; i++) {
      // 跳过dashboard路径，因为已经添加了首页
      if (paths[i] === 'dashboard' && i === 0) {
        continue;
      }

      // 跳过空路径
      if (!paths[i]) {
        continue;
      }

      currentPath += '/' + paths[i];

      // 查找匹配的路由
      let matchedRoute = routes.find(r => r.path === currentPath);

      // 如果没有精确匹配，尝试匹配动态路由
      if (!matchedRoute) {
        matchedRoute = routes.find(r => {
          // 将路由模式转换为正则表达式
          const pattern = r.path.replace(/:[^/]+/g, '[^/]+');
          const regex = new RegExp(`^${pattern}$`);
          return regex.test(currentPath);
        });
      }

      // 获取路由标题
      // 构建完整路径用于检查动态标题
      const fullPath = currentPath;

      // 获取路由标题 - 优先检查动态标题
      let title = '';
      if (dynamicTitles.value.has(fullPath)) {
        title = dynamicTitles.value.get(fullPath);
      } else {
        title = getRouteTitle(paths[i], matchedRoute);
      }

      // 如果是规则库或规则包，尝试从路由参数中获取实际名称
      let displayTitle = title;
      //判断是否为规则库管理/调整进入，是则添加父级路径
      if (paths[i].startsWith('ruleBase-') && paths[i].indexOf('all')===-1 && paths[i].indexOf('demandUuid')===-1){
        if(!items.find((item) => item.path === '/ruleBaseManage')){
          items.push({
            title: '规则库管理',
            path: '/ruleBaseManage?backPage='+(route.query.backPage?route.query.backPage:1),
          });
        }
      }
      if (paths[i].startsWith('ruleBase-') && paths[i].indexOf('demandUuid')!==-1){
        if(!items.find((item) => item.path === '/ruleBaseManage')){
          items.push({
            title: '规则调整',
            path: '/ruleAdjustment?backPage='+(route.query.backPage?route.query.backPage:1),
          });
        }
      }
      if (paths[i].startsWith('ruleBase-') && route.params.ruleBaseName) {
        displayTitle = String(route.params.ruleBaseName);
      } else if (paths[i].startsWith('rulePackage-') && route.params.rulePackageName) {
        displayTitle = String(route.params.rulePackageName);
      }
      //从首页进入的规则详情/编辑需要去除前两级
      if(paths[i].startsWith('rule-')||paths[i].startsWith('ruleContent-')){
        // 过滤掉包含'ruleBase-'的面包屑项
        const filteredItems = items.filter(item => !item.path.includes('ruleBase-'));
        items.length = 0; // 清空原数组
        items.push(...filteredItems); // 将过滤后的项添加回数组
      }
      //若为规则库首页进入的规则详情/编辑，则去除规则库/包的空白路径
      if(displayTitle.trim()){
        // 检查标题是否已添加过，防止重复项
        if (!addedTitles.has(displayTitle)) {
          items.push({
            title: displayTitle,
            path: currentPath
          });
          // 将标题添加到已添加集合中
          addedTitles.add(displayTitle);
        }
      }
    }
    // 创建一个标题数组副本并反转它（从后往前）
    const titleArray = [...items].reverse().map(item => {
      // 如果标题是"首页"，替换为"erule规则管理平台"
      return item.title === '首页' ? '' : item.title;
    });
    route.meta.title = titleArray.filter(title => title !== '').join(' · ')
    return items;
  });

  // 是否显示新建内容菜单浮层
  const isAddContentPopoverShow = ref(false)
  // 是否显示选择规则库对话框
  const isChooseRuleBaseModalShow = ref(false)
  // 是否显示新建规则库对话框
  // const isAddRuleBaseModalShow = ref(false)

  // 新建内容类型
  const chooseRuleBaseModalTitle = ref('')

  /**
   * 显示选择规则库对话框
   */
  function showChooseRuleBaseModal(title: string, onSelect?: (ruleBase: { uuid: string, name: string }) => void) {
    chooseRuleBaseModalTitle.value = title

    isAddContentPopoverShow.value = false
    isChooseRuleBaseModalShow.value = true

    // 保存回调函数
    currentSelectCallback.value = onSelect
  }

  // 保存当前选择回调函数
  const currentSelectCallback = ref<((ruleBase: { uuid: string, name: string }) => void) | undefined>()

  // 处理规则库选择
  const handleRuleBaseSelect = (ruleBase: { uuid: string, name: string }) => {
    if (currentSelectCallback.value) {
      currentSelectCallback.value(ruleBase)
    } else {
      // 从标题中解析出规则类型名称
      const ruleTypeName = chooseRuleBaseModalTitle.value.replace('新建', '')
      const ruleType = ruleTypes.find(item => item.name === ruleTypeName)
      const ruleTypeCode = ruleType?.code
      navigateTo(`/ruleBase-${ruleBase.uuid}/rulePackage-all?openCreateDialog=true&ruleType=${ruleTypeCode}`)
    }

    hideChooseRuleBaseModal()
  }

  /**
   * 隐藏选择规则库对话框
   */
  function hideChooseRuleBaseModal() {
    isChooseRuleBaseModalShow.value = false
  }

  /**
   * 显示新建规则库对话框
   */
  /* function showAddRuleBaseModal() {
    isAddContentPopoverShow.value = false
    isAddRuleBaseModalShow.value = true
  } */

  /**
   * 隐藏新建规则库对话框
   */
  /* function hideAddRuleBaseModal() {
    isAddRuleBaseModalShow.value = false
  } */

  provide('showChooseRuleBaseModal', showChooseRuleBaseModal)
  // provide('showAddRuleBaseModal', showAddRuleBaseModal)

  // const ruleBaseAddRef = ref()

  // 处理新建规则库对话框确定按钮点击
  /* const handleAddRuleBaseOk = () => {
    ruleBaseAddRef.value.submitFun(() => {
      isAddRuleBaseModalShow.value = false
      message.success('新建成功')
    })
  } */

  // 处理新建规则库对话框取消按钮点击
  /* const handleAddRuleBaseCancel = () => {
    isAddRuleBaseModalShow.value = false
  } */

  const { ruleTypes } = useRuleTypes()
</script>
<template>
  <a-layout>
    <!-- 固定头部 -->
    <a-layout-header class="header" :style="{ position: 'fixed', zIndex: 1000, width: '100%', padding: '0 20px' }">
      <div class="header-content">
        <!--左侧区域：Logo和面包屑 -->
        <div class="header-left">
          <!-- Logo -->
          <div class="header-logo" style="height: 48px;">
            <NuxtLink to="/dashboard">
               <IconLogo height="28" style="margin-top: 12px;"/>
            </NuxtLink>
          </div>
          <div class="breadcrumb" style="margin-top: 5px">
              <a-breadcrumb v-for="(item, index) in breadcrumbItems" :key="item.path" separator="">
                  <a-breadcrumb-item  :class="{ 'current-page': index === breadcrumbItems.length - 1 }">
<!--                    <span v-if="index>0" style="color: rgb(100, 106, 115);margin: 0px 4px;">&nbsp;>&nbsp;</span>-->
                    <svg v-if="index>0" style="margin: 0px 9px 0px 9px;"  width="15" height="15" viewBox="0 0 20 15" class="arrow"><path fill="#8f959e" fill-rule="evenodd" d="M12.6 10.36L7.84 5.6A.75.75 0 018.9 4.53l5.3 5.3a.75.75 0 010 1.06l-5.3 5.3a.75.75 0 11-1.06-1.05l4.78-4.78z"></path></svg>
                    <!--判断若为最后一级则不进行路由改变，否则会参数丢失-->
                    <NuxtLink :to="item.path" v-if="index !== breadcrumbItems.length - 1">{{ item.title }}</NuxtLink>
                    <span v-else class="last-breadcrumb-item">{{ item.title }}</span>
                  </a-breadcrumb-item>
              </a-breadcrumb>
          </div>
        </div>
        <!-- 右侧区域：搜索框、帮助和用户信息 -->
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="index-module_sidebarAction_yE6Ka">
            <div class="index-module_inputSearchWrapper_Kly-7">
              <GlobalSearchBox />
            </div>
          </div>
          <!-- 帮助链接 -->
          <div class="help-link">
            <NuxtLink to="/doc/xyu3hsey0hrb4kxs" target="_blank">
              <a-button type="text">
                <template #icon><QuestionCircleOutlined /></template>
                帮助
              </a-button>
            </NuxtLink>
          </div>
          <!-- 用户信息 -->
          <div class="index-module_userAction_+C+L4">
            <UserInfoPopover />
          </div>
        </div>
      </div>
    </a-layout-header>
    <a-layout>
      <a-layout-sider :collapsedWidth="sidebarWidth" :width="sidebarWidth" :collapsed="collapsed" style="background: #fff; position: fixed; top: 48px; height: 100vh; overflow-y: auto; z-index: 10;">
        <div id="dragBarContainer"
             class="index-module_dragBarContainer_4KyrG dashboard-sidebar DashboardSideBar-module_sidebar_7dYv4"
             :style="{ width: `${sidebarWidth}px` }">
          <div class="index-module_dragBarWrapper_CU--R">
            <div class="DashboardSideBar-module_sidebarMenuWrapper_YyteY">
              <div class="index-module_sidebarMenuWrapper_iw4HJ">
                <MainMenu :collapsed="collapsed" />
                <ul
                        class="ant-menu ant-menu-root ant-menu-inline ant-menu-light index-module_menuWrapper_Y2ft1 index-module_otherMenuWrapper_ay541 index-module_defaultMenu_qjIBj"
                        role="menu" tabindex="0" data-menu-list="true" data-testid="sidebar-sidebar-menu" style="margin-bottom: 48px">
                  <a-popover placement="right" trigger="click"
                             overlayClassName="index-module_navLinkPopover_SeFzl larkui-popover"
                             :overlayInnerStyle="{ marginBottom: '10px' }">
                    <template #content>
                      <div class="MoreLink-module_moreLink_3qeJT" data-testid="sidebar-more-link">
                        <p class="MoreLink-module_moreText_reLWV">更多</p>
                        <div class="MoreLink-module_iconLink_Xl3Un">
                          <div>
                            <li>
                              <NuxtLink to="/statistics">
                                <p class="MoreLink-module_iconWarp_Ktp-I">
                                  <svg width="16" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                                       class="larkui-icon larkui-icon-line-data index-module_icon_TGSiA">
                                    <g fill="currentColor" fill-rule="nonzero">
                                      <path
                                              d="M185.453 92.73c4.29-3.902 10.925-3.583 14.821.713 3.896 4.297 3.578 10.944-.712 14.846l-42.004 38.218a10.479 10.479 0 0 1-13.588.444l-23.372-18.636-32.94 30.852c-4.156 3.892-10.632 3.748-14.611-.272l-.219-.227c-3.957-4.24-3.734-10.89.498-14.854l39.555-37.047a10.479 10.479 0 0 1 13.7-.546l23.474 18.716 35.398-32.207Z">
                                      </path>
                                      <path
                                              d="M38 29c5.43 0 9.848 4.327 9.996 9.72L48 39v155c0 7.627 6.1 13.83 13.687 13.997L62 208h156c5.523 0 10 4.477 10 10 0 5.43-4.327 9.848-9.72 9.996L218 228H62c-18.59 0-33.695-14.92-33.995-33.438L28 194V39c0-5.523 4.477-10 10-10ZM196.719 80.985c6.2-.65 11.755 3.848 12.41 10.05l2.704 25.723a7.529 7.529 0 0 1-14.974 1.574l-2.312-21.98-21.999 2.311a7.529 7.529 0 0 1-8.239-6.42l-.035-.28a7.529 7.529 0 0 1 6.701-8.275l25.744-2.703Z">
                                      </path>
                                    </g>
                                  </svg>
                                </p>
                                <p class="MoreLink-module_iconWarpText_iL2XP">
                                <p class="MoreLink-module_iconTitle_QSL4A">统计</p>
                                <p class="MoreLink-module_iconContent_u4edp">查看统计信息</p>
                                </p>
                              </NuxtLink>
                            </li>
                            <li>
                              <NuxtLink to="/recycles">
                                <p class="MoreLink-module_iconWarp_Ktp-I"><svg width="16" height="1em"
                                                                               viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                                                                               class="larkui-icon larkui-icon-icon-delete">
                                  <path
                                            d="M218 68c5.523 0 10 4.477 10 10s-4.477 10-10 10h-16.56l-6.16 107.936c-.97 17.07-14.466 30.764-31.52 31.984l-2.432.08H94.672c-18.028.004-32.925-14.065-33.952-32.064L54.56 88H38c-5.523 0-10-4.477-10-10s4.477-10 10-10h180Zm-36.592 20H74.576l6.112 106.8a14 14 0 0 0 12.288 13.104l1.696.096h66.656c7.424.004 13.56-5.788 13.984-13.2L181.408 88ZM108 112c5.52 0 10 4.48 10 10v44c0 5.523-4.477 10-10 10s-10-4.477-10-10v-44c0-5.52 4.48-10 10-10Zm40 0c5.52 0 10 4.48 10 10v44c0 5.523-4.477 10-10 10s-10-4.477-10-10v-44c0-5.52 4.48-10 10-10Zm26-84c5.523 0 10 4.477 10 10s-4.477 10-10 10H82c-5.523 0-10-4.477-10-10s4.477-10 10-10h92Z"
                                          fill="currentColor" fill-rule="nonzero"></path>
                                </svg></p>
                                <div class="MoreLink-module_iconWarpText_iL2XP">
                                  <p class="MoreLink-module_iconTitle_QSL4A">回收站</p>
                                  <p class="MoreLink-module_iconContent_u4edp">找回删除的文档与内容</p>
                                </div>
                              </NuxtLink>
                            </li>
                          </div>
                        </div>
                        <!--注释更多中的帮助-->
                        <!--<ul
                                class="ant-menu ant-menu-root ant-menu-vertical ant-menu-light MoreLink-module_defaultLink_8Mvnq"
                                role="menu" tabindex="0" data-menu-list="true">
                          <li class="ant-menu-item ant-menu-item-only-child" role="menuitem" tabindex="-1"
                              data-menu-id="rc-menu-uuid-54130-6-/help">
                            <NuxtLink to="/doc/xyu3hsey0hrb4kxs">帮助</NuxtLink>
                          </li>
                        </ul>-->
                      </div>
                    </template>
                    <li
                            class="ant-menu-item ant-menu-item-active index-module_menuItem_S3OA1 navlink-third larkui-popover-trigger"
                            title="更多" role="menuitem" tabindex="-1" aria-disabled="false"
                            data-menu-id="rc-menu-uuid-77669-3-undefined" style="padding-left: 0px;"><svg width="16" height="16"
                                                                                                          viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"
                                                                                                          class="larkui-icon larkui-icon-comment-more icon ant-menu-item-icon">
                      <g fill="none" fill-rule="evenodd">
                        <path
                                d="M74 143c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15Zm54 0c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15Zm54 0c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15Z"
                                fill="currentColor" fill-rule="nonzero"></path>
                        <circle stroke="currentColor" stroke-width="21.333" cx="128" cy="128" r="103.111"></circle>
                      </g>
                    </svg><span class="index-module_span_nCoEo">更多</span></li>
                  </a-popover>
                </ul>
              </div>
            </div>
            <div class="index-module_dragBar_-J0vM dargBar-resize" :style="{ left: `${sidebarWidth}px`, display: 'none' }"></div>
            <div class="index-module_dragBarResizeWrapper_l0+k2 larkui-tooltip" style=""><div data-testid="dargBar-resizeBar" class="index-module_dragBarResize_KZ3+u" style="display: block !important;"><svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" class="larkui-icon larkui-icon-review-arrow-right" @click="toggleCollapsed" :style="{ transform: collapsed ? 'rotateY(0deg)' : 'rotateY(180deg)' }"><path d="m115.925 74.9 46.171 46.1a9.889 9.889 0 0 1 0 14l-46.17 46.1c-3.873 3.866-10.15 3.866-14.022 0a9.892 9.892 0 0 1-2.904-7V81.9c0-5.468 4.439-9.9 9.915-9.9a9.922 9.922 0 0 1 7.01 2.9Z" fill="currentColor" fill-rule="nonzero"></path></svg></div></div>
          </div>
        </div>
      </a-layout-sider>
      <!--内容区域-->
      <a-layout :style="{ marginLeft: `${sidebarWidth}px`, marginTop: '48px' }">
        <a-layout-content
                :style="{ background: '#fff',  margin: 0, minHeight: '100%' }"
        >
          <div id="main-right-content" class="WebPureLayout-module_rightMainContent_88QOi main-right-content">
            <div class="WebPureLayout-module_rightMainContentChildren_hiyzf">
              <slot />
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<style lang="scss" scoped>
  .header {
    background: #fff;
    height: 48px;
    line-height: 48px;
    border-top: none;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: none;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    height: 100%;
    overflow: hidden;
  }

  .header-right {
    display: flex;
    align-items: center;
  }
  .help-link {
    margin-right: 16px;
  }

  /* 面包屑导航样式 */
  .breadcrumb {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 900px;
    height: 100%;
    display: flex;
    align-items: center;
  }

  :deep(.ant-breadcrumb) {
    font-size: 14px;
    line-height: 21px;
    color: rgb(100, 106, 115);
  }

  :deep(.ant-breadcrumb-link) {
    transition: color 0.3s;
  }

  :deep(.ant-breadcrumb-link a) {
    color: rgb(100, 106, 115);
    transition: color 0.3s;
  }

  /* 修改模板部分，为最后一个面包屑项添加class */
  /* 当前页面的导航项加粗 */
  :deep(.current-page .ant-breadcrumb-link a) {
    color: rgb(31, 35, 41) !important;
  }

  :deep(.current-page .ant-breadcrumb-link .last-breadcrumb-item) {
    color: rgb(31, 35, 41);
  }

  :deep(.ant-breadcrumb-link a:hover) {
    color: rgb(31, 35, 41);
  }

  :deep(.ant-breadcrumb-separator) {
    margin: 0 5px;
    color: rgb(100, 106, 115);
  }
  /* 侧边栏收起时的样式 */
  :deep(.ant-layout-sider-collapsed) .index-module_span_nCoEo {
    display: none !important;
  }
  /* 确保菜单项正常显示 */
  .ant-menu-item {
    display: flex;
    align-items: center;
  }
  .index-module_dragBarResize_KZ3+u{
    display: block !important;
  }
  
  /* 更多按钮悬停效果 */
  .index-module_menuItem_S3OA1:hover,
  .ant-menu-item-active.index-module_menuItem_S3OA1:hover {
    background-color: #e6f7ff !important;
  }

  /* 更多菜单弹出项悬停效果 */
  :deep(.MoreLink-module_iconLink_Xl3Un li:hover) {
    background-color: #e6f7ff !important;
  }

  /* 点击后的样式 */
  :deep(.MoreLink-module_iconLink_Xl3Un li:active),
  :deep(.MoreLink-module_iconLink_Xl3Un li.active) {
    background-color: #e6f7ff !important;
  }
</style>
