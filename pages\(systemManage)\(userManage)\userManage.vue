<!-- 用户管理页 -->

<script setup lang="ts">
import {
    list,
    getUserById,
    saveOrUpdate,
    deleteUser,
    getEnginesByUserId,
} from "@/api/userManagement";
import qs from "qs";
import UserAdd from "@/businessComponents/userManage/UserAdd";
import UserModify from "@/businessComponents/userManage/UserModify";
import UserCopy from "@/businessComponents/userManage/UserCopy";
import { ref, onMounted, reactive, computed } from 'vue';
import { RULE_PERMISSION } from '@/consts/rulePermissionConsts';
import useTableConfig from '@/composables/useTableConfig';
import userPostAuthority from '@/businessComponents/userManage/userPostAuthority.vue';
import userRuleBase from '@/businessComponents/userManage/userRuleBase.vue';

definePageMeta({
    title: '用户管理'
})

const message: any = inject('message')
const modal: any = inject('modal')


const loading = ref(false);

// 定义表格列
const tableColumns = [
    {
        title: "姓名",
        dataIndex: "name",
        key: "name",
        align: "left",
        ellipsis: true,
    },
    {
        title: "登录ID",
        dataIndex: "loginId",
        key: "loginId",
        width: 200,
        align: "left",
        ellipsis: true,
    },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 80,
        align: "center",
    },
    {
        title: "手机",
        dataIndex: "mobile",
        key: "mobile",
        width: 150,
        align: "center",
        ellipsis: true,
    }
];

// 获取用户列表
const fetchUsers = async (params: Record<string, any> = {}) => {
    try {
        const res = await list({
            loginId: params.id,
            name: params.name,
            page: params.page || 1,
            several: params.pageSize || 10,
        });
        return {
            data: res.data.data.map((item: any) => ({
                ...item,
                gender: item.gender === "M" ? "男" : "女",
            })),
            totalCount: res.data.totalCount
        };
    } catch (error) {
        message.error('获取用户列表失败');
        return {
            data: [],
            totalCount: 0
        };
    }
};

// 删除
const onDeleteUser = (row: any) => {
    modal.confirm({
        title: '温馨提示',
        content: '您确定删除当前用户吗?',
        okText: '确定',
        cancelText: '取消',
        type: 'warning',
        onOk() {
            deleteUser(
                qs.stringify({
                    id: row.id,
                })
            ).then(() => {
                if (listLayout.value) {
                    listLayout.value?.refresh();
                }
            });
        },
        onCancel() {
        },
    });
};

// 抽屉相关状态
const drawerVisible = ref(false);
const currentUserId = ref('');
const currentLoginId = ref('');
const ruleBaseDrawerVisible = ref(false);

// 岗位权限设置
const postAuthoritySet = (row: any) => {
    currentUserId.value = row.id;
    currentLoginId.value = row.loginId;
    drawerVisible.value = true;
};

// 处理抽屉关闭
const handleDrawerClose = () => {
    drawerVisible.value = false;
    currentUserId.value = '';
    currentLoginId.value = '';
};

// 分配规则库
const ruleBaseSet = (row: any) => {
    getEnginesByUserId({
        id: row.id,
    }).then((res) => {
        if (res.data && res.data.length) {
            currentUserId.value = row.id;
            ruleBaseDrawerVisible.value = true;
        } else {
            message.error('没有可分配的规则库');
        }
    });
};

// 处理规则库抽屉关闭
const handleRuleBaseDrawerClose = () => {
    ruleBaseDrawerVisible.value = false;
    currentUserId.value = '';
};

const modalType = ref(''); // 对话框类型：'add' 或 'update'
const id = ref({});
const isModalVisible = ref(false); // 对话框显示状态

const showModal = (type: string, record: any = {}) => {
    modalType.value = type;
    if (type === 'update') {
        id.value = record.id;
    } else if (type === 'copy') {
        id.value = record.id;
    }
    isModalVisible.value = true;
};

const handleCancel = () => {
    isModalVisible.value = false;
};

// 用户新增组件
const userAddComponent = ref();

// 用户修改组件
const userModifyComponent = ref();

// 用户复制组件
const userCopyComponent = ref();

// 规则库组件引用
const userRuleBaseRef = ref();

// 处理规则库提交
const handleRuleBaseSubmit = () => {
    if (userRuleBaseRef.value) {
        userRuleBaseRef.value.submitForm();
    }
};

// 处理规则库重置
const handleRuleBaseReset = () => {
    if (userRuleBaseRef.value) {
        userRuleBaseRef.value.resetForm();
    }
};

// 处理modal ok 事件
function handleModalOk() {
    let submitFun;
    switch (modalType.value) {
        case 'add':
            submitFun = userAddComponent.value.submitFun;
            break;
        case 'update':
            submitFun = userModifyComponent.value.submitFun;
            break;
        case 'copy':
            submitFun = userCopyComponent.value.submitFun;
            break;
    }
    submitFun && submitFun(() => {
        if (listLayout.value) {
            listLayout.value.refresh();
        }
        isModalVisible.value = false;
    });
}

// 搜索配置
const searchConfig = {
    // 简单搜索字段
    simpleSearchField: {
        label: '姓名',
        field: 'name'
    },
    // 高级搜索字段
    advancedSearchFields: [
        {
            label: '登录ID',
            field: 'id',
            compType: 'input'
        },
        {
            label: '姓名',
            field: 'name',
            compType: 'input'
        }
    ]
};

// 事件配置
const eventConfig = {
    // 搜索事件
    searchEvent: () => {},
    // 添加事件
    addNewEvent: () => showModal('add'),
    // 新增按钮权限
    addPermission: RULE_PERMISSION.SYS_USER.ADD,
    // 表单处理器配置
    formHandler: {
        // 查询方法
        queryMethod: fetchUsers
    }
};

// 获取操作菜单项
const getActionMenuItems = (record: any) => {
    const items = [];

    // 删除操作
    items.push({
        key: 'delete',
        label: '删除',
        permission: RULE_PERMISSION.SYS_USER.DELETE,
        onClick: () => onDeleteUser(record)
    });

    // 修改操作
    items.push({
        key: 'update',
        label: '修改',
        permission: RULE_PERMISSION.SYS_USER.UPDATE,
        onClick: () => showModal('update', record)
    });

    // 复制操作
    items.push({
        key: 'copy',
        label: '复制',
        permission: RULE_PERMISSION.SYS_USER.COPY,
        onClick: () => showModal('copy', record)
    });

    // 岗位权限设置
    if (record.id != 1) {
        items.push({
            key: 'postAuthoritySet',
            label: '岗位权限设置',
            permission: RULE_PERMISSION.SYS_USER.ORG,
            onClick: () => postAuthoritySet(record)
        });
    }

    // 分配规则库
    items.push({
        key: 'ruleBaseSet',
        label: '分配规则库',
        permission: RULE_PERMISSION.SYS_USER.RULE_BASE,
        onClick: () => ruleBaseSet(record)
    });

    return items;
};

const listLayout = ref<InstanceType<typeof ListPage> | null>(null);
</script>

<template>
    <ListPage
        ref="listLayout"
        title="用户管理"
        :searchConfig="searchConfig"
        :eventConfig="eventConfig"
        :showAddButton="true"
        :tableColumns="tableColumns"
        :queryMethod="fetchUsers"
        rowKey="id"
        :actionMenuGetter="getActionMenuItems"
        @dataLoaded="handleDataLoaded"
    >
    </ListPage>

    <!-- 新增和更新对话框 -->
    <a-modal v-if="isModalVisible" :visible="isModalVisible" :title="modalType === 'add' ? '新增用户' :modalType === 'update' ? '修改用户' : '复制用户'" @ok="handleModalOk"
             @cancel="handleCancel" okText="保存">
        <div style="max-height: 60vh; overflow-y: auto;">
            <UserAdd ref="userAddComponent" v-if="modalType === 'add'" />
            <UserModify v-if="modalType === 'update'" :key="modalType + '-update-' + id" ref="userModifyComponent" :id="id" />
            <UserCopy ref="userCopyComponent" v-if="modalType==='copy'" :id="id"/>
        </div>
    </a-modal>

    <!-- 岗位权限设置抽屉 -->
    <FlexDrawer
        v-model:visible="drawerVisible"
        title="岗位权限设置"
        @close="handleDrawerClose"
        v-if="drawerVisible"
    >
        <userPostAuthority
            :userId="currentUserId"
            :loginId="currentLoginId"
            @close="handleDrawerClose"
        />
    </FlexDrawer>

    <!-- 分配规则库抽屉 -->
    <FlexDrawer
        v-model:visible="ruleBaseDrawerVisible"
        title="分配规则库"
        @close="handleRuleBaseDrawerClose"
        v-if="ruleBaseDrawerVisible"
    >
        <userRuleBase
            ref="userRuleBaseRef"
            :userId="currentUserId"
            @close="handleRuleBaseDrawerClose"
        />
        <template #footer>
            <div style="text-align: right">
                <a-button type="primary" @click="handleRuleBaseSubmit" style="margin-right: 8px;">提交</a-button>
                <a-button @click="handleRuleBaseReset" style="margin-right: 8px;">重置</a-button>
            </div>
        </template>
    </FlexDrawer>
</template>

