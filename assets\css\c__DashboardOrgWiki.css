.group-selector .group-selector-item .tip-icon {
    display: flex;
    align-items: center;
    margin-left: 6px;
    color: var(--yq-icon-caption)
}

.group-selector .ant-select-item-option-content .anticon-check {
    margin: 0 0 0 8px;
    color: var(--yq-theme)
}

.group-selector .ant-select-item-option-content .larkui-icon-check {
    color: var(--yq-theme)
}

.index-module_name_7hN0N {
    max-width: 200px
}

.index-module_contentArea_0xSfa {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_switchTitle_qW8aM {
    color: var(--yq-text-primary)
}

.index-module_switchButton_xHUSN .ant-switch-small {
    min-width: 32px;
    height: 18px;
    line-height: 18px
}

.index-module_switchButton_xHUSN .ant-switch-small.ant-switch-checked .ant-switch-handle {
    left: calc(100% - 16px)
}

.index-module_switchButton_xHUSN .ant-switch-small .ant-switch-handle {
    width: 14px;
    height: 14px
}

.index-module_tip_eCtT0 {
    background: var(--yq-yuque-grey-1);
    border-radius: 8px;
    padding: 8px;
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    margin-top: -16px;
    margin-bottom: 24px
}

.group-avatar {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

.group-avatar .lock {
    position: absolute;
    z-index: 1;
    bottom: 0;
    right: -5px;
    border: 1px solid var(--yq-white);
    border-radius: 100%
}

.group-avatar .lock>img,.group-avatar .lock>svg {
    display: block
}

.group-avatar>.placeholder-avatar,.group-avatar>img {
    display: block;
    width: 100%
}

.default-group-avatar .larkicon {
    font-size: 32px;
    line-height: 36px;
    color: var(--yq-yuque-grey-5)
}

.index-module_groupName_DppYx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    color: var(--yq-text-body)
}

.index-module_groupName_DppYx:hover {
    color: var(--yq-text-primary)
}

.index-module_groupName_DppYx>.index-module_groupNameText_XYhrt {
    margin-right: 4px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_groupName_DppYx>.icon-svg {
    color: var(--yq-text-caption)
}

.index-module_groupName_DppYx .index-module_groupNameScope_qP0XF {
    position: relative;
    top: 3px;
    color: var(--yq-text-caption)
}

.index-module_card_MJe8k {
    padding-top: 8px;
    padding-bottom: 8px;
    max-width: 290px;
    min-width: 240px
}

.index-module_cardBody_C-l0H {
    display: flex
}

.index-module_cardAvatar_S1GOD {
    margin-right: 8px
}

.index-module_cardInfo_1BnUz {
    font-size: 14px;
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz,.index-module_cardInfo_1BnUz>h6 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_cardInfo_1BnUz>h6 {
    margin-top: 2px;
    font-size: 16px
}

.index-module_cardInfo_1BnUz>h6>a {
    color: var(--yq-text-primary)
}

.index-module_cardInfo_1BnUz>h6>a:hover {
    color: var(--yq-text-body)
}

.index-module_cardInfo_1BnUz>p {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption)
}

.index-module_cardFooter_iT7Xq {
    margin-top: 16px;
    border-top: 1px solid var(--yq-border-primary);
    padding-top: 12px;
    display: flex;
    justify-content: flex-end
}

.index-module_cardFooter_iT7Xq>a {
    color: var(--yq-text-body)
}

.index-module_cardFooter_iT7Xq>a:hover {
    color: var(--yq-text-caption)
}

.larkui-member-selector-container .ant-modal-header {
    padding: 0;
    border-bottom: 0 none;
    overflow: hidden
}

.larkui-member-selector-container .ant-modal-body {
    padding: 0
}

.larkui-member-selector-container .ant-modal-close-x {
    width: 46px;
    height: 46px
}

.larkui-member-selector-container .ant-modal-close-x .larkui-icon {
    vertical-align: 0
}

.larkui-member-selector-container-select-by {
    padding-top: 0
}

.larkui-member-selector-container-select-by .ant-menu-horizontal {
    background-color: revert
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected,.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item:hover {
    color: var(--yq-text-primary);
    font-weight: 700;
    background: none
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item {
    margin: 0;
    margin-left: 32px;
    padding: 0;
    color: var(--yq-text-body);
    font-weight: 400
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item:first-child {
    margin-left: 20px
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item :hover {
    font-weight: 700
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected {
    color: var(--yq-text-primary);
    font-weight: 700;
    border-bottom-width: 2px
}

.larkui-member-selector-container-select-by .ant-menu-horizontal>.ant-menu-item-selected:hover {
    color: var(--yq-text-primary)
}

.larkui-member-selector-role {
    width: 80px
}

.larkui-member-selector-role-role-select {
    width: 295px
}

.larkui-member-selector-role-role-select .ant-select {
    width: 100%
}

.larkui-member-selector-role-role-tip {
    margin-right: 4px
}

.larkui-member-selector-role-role-label {
    font-weight: 500
}

.ellipsis-wrapper {
    display: inline-flex;
    min-width: 0
}

.ellipsis-wrapper .ellipsis-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.larkui-member-selector-user-search,.larkui-member-selector-user-search-container {
    width: 100%
}

.larkui-member-selector-user-search-container .ant-select-selector {
    background: none!important;
    border: 0 none!important;
    box-shadow: none!important
}

.larkui-member-selector-user-search-container .ant-select-arrow {
    color: var(--yq-text-body)
}

.larkui-member-selector-user-search-container-normal {
    width: 100%
}

.larkui-member-selector-user-search-user-selector-tag {
    height: 32px;
    border-radius: 16px;
    margin-right: 8px;
    padding: 4px;
    color: var(--yq-black);
    border: none;
    background-color: var(--yq-bg-tertiary);
    margin-bottom: 8px
}

.larkui-member-selector-user-search-user-selector-tag-avatar {
    margin-right: 4px
}

.larkui-member-selector-user-search-user-selector-tag-label {
    display: inline-block;
    line-height: 20px;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar-wrapper {
    display: inline-block;
    width: 18px;
    height: 18px;
    vertical-align: middle;
    line-height: 18px;
    text-align: center;
    margin-right: 3px;
    padding-top: 2px
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar-wrapper:hover {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 50%
}

.larkui-member-selector-user-search-user-selector-tag-close-avatar {
    color: var(--yq-text-caption)
}

.larkui-member-selector-user-search-menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.larkui-member-selector-user-search-menu-item .ant-dropdown-menu-item-disabled .icon,.larkui-member-selector-user-search-menu-item .ant-dropdown-menu-item-disabled .login {
    color: var(--yq-ant-disabled-color)
}

.larkui-member-selector-user-search-user {
    padding: 3px 10px;
    display: flex;
    align-items: center;
    height: 40px
}

.larkui-member-selector-user-search-isdep {
    height: 52px
}

.larkui-member-selector-user-search-title-wrap {
    min-width: 0
}

.larkui-member-selector-user-search-title {
    display: flex;
    align-items: center
}

.larkui-member-selector-user-search-icon {
    min-width: 32px;
    margin-right: 16px
}

.larkui-member-selector-user-search-name {
    margin-right: 8px
}

.larkui-member-selector-user-search-dep,.larkui-member-selector-user-search-login {
    color: var(--yq-text-caption);
    font-size: 12px
}

.larkui-member-selector-user-search-dep {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 295px;
    margin-top: 2px
}

.larkui-member-selector-user-search-notfound {
    padding: 50px 0;
    text-align: center;
    color: var(--yq-text-body)
}

.ellipsis-wrapper {
    width: 100%
}

.larkui-member-selector-user-search-tag-icon {
    margin-right: 2px
}

.larkui-member-selector-search {
    height: 450px;
    position: relative
}

.larkui-member-selector-search-footer {
    position: absolute;
    bottom: 29px;
    left: 24px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    row-gap: 5px;
    width: calc(100% - 48px)
}

.larkui-member-selector-search-meta {
    color: var(--yq-text-caption);
    margin-right: 16px;
    white-space: nowrap;
    flex-shrink: 0
}

.larkui-member-selector-search-action-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1
}

.larkui-member-selector-search-role {
    flex-shrink: 0;
    color: var(--yq-text-caption)
}

.larkui-member-selector-search-role .larkui-dropdown-trigger {
    color: var(--yq-text-body)
}

.larkui-member-selector-search-footer-btn {
    flex-shrink: 0
}

.larkui-member-selector-search-role-text {
    color: var(--yq-text-body)
}

.larkui-member-selector-search-content {
    padding: 20px 0 20px 8px;
    height: 100%;
    max-height: 380px;
    overflow-y: auto
}

.larkui-member-selector-search-selector {
    height: 450px
}

.larkui-member-selector-search-selector .ant-select-selection--multiple .ant-select-selection__rendered .ant-select-selection__choice {
    border-radius: 4px;
    height: 22px;
    font-size: 12px;
    background: var(--yq-bg-tertiary);
    border: 1px solid var(--yq-border-primary)
}

.larkui-member-selector-search-quick-selector-container {
    position: relative;
    border-left: 1px solid var(--yq-border-light);
    height: 450px
}

.larkui-member-selector-search-quick-selector-choose {
    padding: 18px 24px
}

.larkui-member-selector-search-quick-selector-subtitle,.larkui-member-selector-search-quick-selector-title {
    color: var(--yq-text-primary);
    font-size: 14px;
    margin-bottom: 16px;
    font-weight: 400
}

.larkui-member-selector-search-quick-selector-subtitle {
    padding: 16px 24px 0
}

.larkui-member-selector-search-quick-selector-subtitle>span {
    cursor: pointer
}

.larkui-member-selector-search-quick-selector-item {
    height: 40px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--yq-text-body)
}

.larkui-member-selector-search-quick-selector-item img {
    display: block
}

.larkui-member-selector-search-selector-icon {
    margin-right: 8px;
    vertical-align: text-bottom
}

.larkui-member-selector-search-quick-selector-icon,.larkui-member-selector-search-quick-selector-subicon {
    margin-right: 8px
}

.larkui-member-selector-search-quick-selector-main-list {
    height: 396px;
    overflow-y: auto
}

.larkui-member-selector-search-outsourcer-tip-link {
    color: var(--yq-blue-4)
}

.larkui-member-selector-search-member-type-switch-container {
    position: absolute;
    padding: 0 24px;
    bottom: 30px;
    left: 0
}

.index-module_actions_vhNlZ {
    display: block;
    padding: 6px
}

.index-module_actionItem_gm0UP {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 7px
}

.index-module_actionItem_gm0UP:hover {
    cursor: pointer;
    background: var(--yq-bg-primary-hover);
    border-radius: 8px
}

.index-module_actionItem_gm0UP .index-module_actionTitle_N9iRs {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-9)
}

.index-module_actionItem_gm0UP .index-module_actionTitleLast_KuIDA {
    color: var(--yq-red-6)
}

.index-module_actionItem_gm0UP .index-module_actionMore_8wkrV {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--yq-text-caption)
}

.index-module_actionItem_gm0UP .index-module_actionMoreIcon_5FA3\+ {
    margin-left: 2px;
    transform: rotate(-90deg)
}

.index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq {
    margin-right: 12px;
    color: var(--yq-icon-primary)
}

.index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq.index-module_actionTitleIconLast_2BO7h {
    color: var(--yq-red-6)
}

html[data-kumuhana=pouli] .index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq {
    color: var(--yq-icon-secondary)
}

html[data-kumuhana=pouli] .index-module_actionItem_gm0UP .index-module_actionTitleIcon_XFMgq.index-module_actionTitleIconLast_2BO7h {
    color: var(--yq-red-6)
}

.index-module_actionItem_gm0UP:last-child {
    margin-bottom: 0
}

.index-module_actionItem_gm0UP:last-child:hover {
    cursor: pointer;
    background-color: var(--yq-red-1)
}

.ShareChannels-module_shareChannels_gfpjT {
    padding: 16px 24px;
    background-color: var(--yq-bg-tertiary);
    font-weight: 700;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    display: flex;
    justify-content: flex-start;
    align-items: center
}

.ShareChannels-module_shareChannels_gfpjT .icon-svg+.icon-svg,.ShareChannels-module_shareChannels_gfpjT span+.icon-svg {
    margin-left: 16px
}

.ShareChannels-module_shareChannels_gfpjT .icon-svg {
    cursor: pointer
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 {
    position: relative;
    padding: 20px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ant-dropdown {
    padding: 0 12px 12px;
    width: 185px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04),0 2px 8px 0 rgba(0,0,0,.08),0 1px 4px -2px rgba(0,0,0,.13)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_label_tDypL {
    margin-bottom: 4px;
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_desc_u1Wyf {
    color: var(--yq-text-caption);
    font-size: 12px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_collaborator_2YM9N {
    padding-bottom: 0
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_inviteSelector_UvnYT {
    padding-top: 16px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6.ShareChannels-module_bookPublicSetting_skCSj {
    margin-top: -8px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_bookPublicTitle_Nvtb5 {
    margin-bottom: -8px;
    color: var(--yq-yuque-grey-9);
    line-height: 22px;
    font-size: 14px;
    font-weight: 700
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_title_FCiiD {
    text-align: left;
    display: inline-flex;
    align-items: center;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: var(--yq-yuque-grey-9);
    cursor: pointer
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_backIcon_Xa1gT {
    cursor: pointer;
    margin-top: 2px;
    margin-left: -4px;
    margin-right: 4px;
    transform: rotate(90deg)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_titleDesc_Qr9Q3 {
    margin-top: 12px;
    color: var(--yq-yuque-grey-8)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_titleDesc_Qr9Q3.ShareChannels-module_bookPublicSet_HaiHZ {
    color: var(--yq-yuque-grey-7)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_contentArea_WAxw- {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLinkWrapper_zNH1v {
    flex: 1;
    margin-right: 8px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkTitleDesc_RdIXM {
    margin-top: 12px;
    color: var(--yq-yuque-grey-9)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkTitleDesc_RdIXM span {
    font-weight: 500
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_linkContentArea_\+a59s {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLink_3mHuZ {
    height: 40px;
    line-height: 20px;
    padding: 12px;
    margin-right: 8px;
    color: var(--yq-yuque-grey-6);
    overflow: hidden
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_copyLink_3mHuZ {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    color: rgba(190,192,191,.26)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_inviteCopyBtn_ju\+VP {
    color: #fff;
    font-weight: 700;
    flex: none
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigSettings_b\+fk7 {
    margin-top: 12px;
    border-radius: 8px;
    background-color: var(--yq-yuque-grey-1)
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigSettings_b\+fk7 {
    background-color: var(--yq-yuque-grey-3)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigToggler_caced {
    line-height: 20px;
    margin-top: 12px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_moreConfigsTip_gtVy7 {
    color: var(--yq-yuque-grey-7);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIcon_MQJP8 {
    color: var(--yq-yuque-grey-6);
    position: relative;
    top: 3px;
    transform: rotate(0);
    transition: all .3s
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIcon_MQJP8[data-activated=true] {
    transform: rotate(-180deg);
    transition: all .3s
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_expandIconColor_BoJlD {
    color: var(--yq-black)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBlock_dU5pI {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--yq-yuque-grey-1);
    border: 1px solid var(--yq-yuque-grey-4);
    border-radius: 6px;
    padding: 9px 12px;
    margin-right: 12px;
    font-size: 14px;
    color: var(--yq-yuque-grey-6);
    height: 40px;
    width: 292px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBlock_dU5pI.ShareChannels-module_bookPublicSet_HaiHZ {
    width: 304px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v {
    height: 40px;
    padding: 12px;
    margin-right: 12px;
    color: var(--yq-yuque-grey-6);
    width: 292px;
    background: var(--yq-yuque-grey-1);
    border: 1px solid var(--yq-yuque-grey-4)
}

html[data-kumuhana=pouli] .ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v {
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    color: rgba(190,192,191,.26)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdLink_M9e3v.ShareChannels-module_bookPublicSet_HaiHZ {
    width: 304px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_pwdBtn_oJupk {
    font-weight: 700;
    width: 76px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_passwordText_gaCkx {
    color: var(--yq-yuque-grey-7);
    line-height: 22px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetBtn_aOPqB {
    display: flex;
    align-items: center;
    padding: 0;
    color: var(--yq-text-body)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetBtn_aOPqB:hover {
    color: var(--yq-text-caption)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_resetIcon_rn-tx {
    margin-right: 4px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L {
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L:last-child {
    margin-top: 16px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorItem_l7C7L.ShareChannels-module_notOwner_oGq\+h {
    cursor: not-allowed
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectorIcon_4e0e0 {
    color: #25b864
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_disabledItem_1Ei-3 {
    color: var(--yq-yuque-grey-6)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_shareTime_cjwGm {
    display: flex;
    align-items: center
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_memberAbleIcon_xR\+WM {
    margin-left: 4px
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_shareNotOwner_imImM {
    margin-top: 2px;
    font-size: 12px;
    color: var(--yq-yuque-grey-7)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_switchTitle_dS9uP {
    color: var(--yq-text-primary)
}

.ShareChannels-module_shareDetailSettingPanel_RhPY6 .ShareChannels-module_selectOption_W3LKu {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.ShareChannels-module_collaboratorContent_0fEPi {
    border-bottom: 1px solid var(--yq-yuque-grey-3)
}

.ShareChannels-module_shareQrCodeContent_14F29 {
    width: 232px;
    margin: 24px auto 20px auto
}

.ShareChannels-module_shareQrCodeArea_crX\+x {
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px;
    margin-bottom: 24px
}

.ShareChannels-module_shareQrCodeButton_xGFZ6 {
    padding: 10px 80px
}

.styles-module_sharePanel_Sxg7e {
    width: 420px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ {
    padding: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item {
    padding-top: 24px;
    padding-bottom: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-title {
    margin-bottom: 2px;
    color: var(--yq-yuque-grey-9)
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-description {
    font-size: 12px;
    color: var(--yq-yuque-grey-7);
    line-height: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-meta-avatar {
    margin-right: 12px;
    margin-top: 2px;
    height: 44px
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-action-split {
    display: none
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .ant-list-item-action>li {
    padding: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_firstItem_oD4je,.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_inviteItem_NZWji {
    padding-top: 0
}

.styles-module_sharePanel_Sxg7e .styles-module_main_yGeSJ .styles-module_mainIntro_U4BmK .styles-module_shareDescText_Aj\+aY {
    margin-bottom: 20px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz {
    display: flex;
    align-items: center
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz .styles-module_memberBadge_tyo4x {
    margin-left: 4px;
    font-size: 10px;
    background: linear-gradient(270deg,var(--yq-yellow-3),var(--yq-yellow-1));
    padding: 0 8px;
    color: var(--yq-yellow-9);
    border-radius: 8px;
    height: 18px;
    line-height: 18px;
    cursor: default
}

.styles-module_sharePanel_Sxg7e .styles-module_itemTitle_HBJAz .styles-module_memberAbleIcon_Qqdnq {
    height: 22px!important;
    margin-left: 4px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY {
    padding: 9px;
    color: #fff;
    border-radius: 8px
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY.styles-module_appointIcon_4sk\+S {
    background-color: #4b73b3
}

.styles-module_sharePanel_Sxg7e .styles-module_itemIcon_x0RNY.styles-module_publicIcon_zEz8l {
    background-color: #23ad73
}

.styles-module_sharePanel_Sxg7e .styles-module_inviteIconLi_8F87a {
    margin-left: 16px
}

.styles-module_sharePanel_Sxg7e .styles-module_dot_Z8pRl .ant-badge-dot {
    background: var(--yq-blue-5)
}

.styles-module_sharePanel_Sxg7e .styles-module_inviteIcon_\+3x-U {
    padding: 6px;
    color: var(--yq-yuque-grey-9);
    background: var(--yq-bg-pre-secondary);
    border-radius: 50%;
    border: .5px solid var(--yq-yuque-grey-3);
    cursor: pointer
}

html[data-kumuhana=pouli] .styles-module_sharePanel_Sxg7e .styles-module_inviteIcon_\+3x-U {
    background: none;
    border-color: var(--yq-sheetborder)
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkSet_k942E {
    width: 380px;
    padding: 0 12px 4px 12px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkSet_k942E+.styles-module_description_j9YFu {
    margin-top: 16px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkShow_bMh8i {
    margin-right: 12px;
    height: 32px;
    line-height: 20px;
    padding: 12px;
    color: var(--yq-text-disable);
    background-color: var(--yq-bg-tertiary);
    border-color: var(--yq-border-primary);
    overflow: hidden;
    width: 272px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareLinkShow_bMh8i.styles-module_qrCode_suE8x {
    width: 228px;
    margin-right: 8px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareButton_L93gj {
    padding: 5px 11px;
    font-weight: 500;
    color: var(--yq-yuque-grey-9);
    border-color: var(--yq-border-primary);
    border-radius: 6px;
    font-size: 14px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareButton_L93gj.styles-module_sharePublic_495Qd {
    padding: 5px 15px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareQrCode_xBxWb {
    padding: 7px 6px;
    margin-left: 8px;
    color: var(--yq-icon-primary);
    border-color: var(--yq-border-primary);
    border-radius: 6px
}

.styles-module_sharePanel_Sxg7e .styles-module_shareQrCodeIcon_uR90f {
    color: var(--yq-icon-primary)
}

.styles-module_sharePanel_Sxg7e .styles-module_moreConfigs_qgWYG {
    margin-top: 16px;
    padding-top: 16px;
    background-color: var(--yq-yuque-grey-1);
    border-radius: 8px
}

html[data-kumuhana=pouli] .styles-module_sharePanel_Sxg7e .styles-module_moreConfigs_qgWYG {
    background-color: var(--yq-yuque-grey-3)
}

.styles-module_sharePanel_Sxg7e .styles-module_loadingIcon_pQzuM {
    margin: 0 7px
}

.styles-module_popover_9iY7y .ant-popover-inner {
    border-radius: 6px!important
}

.styles-module_popover_9iY7y .ant-popover-inner-content {
    padding: 0!important
}

.larkui-member-selector-invite-reset-icon {
    margin-right: 5px
}

.larkui-member-selector-batch-batchTip .tip {
    margin-bottom: 35px;
    color: var(--yq-text-primary);
    text-align: center;
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox {
    padding: 24px
}

.larkui-member-selector-batch-batchBox p {
    margin-bottom: 18px;
    color: rgba(0,0,0,.85);
    font-weight: 600;
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox .ant-table-wrapper {
    overflow-y: scroll;
    max-height: 300px;
    width: 600px
}

.larkui-member-selector-batch-batchBox ::-webkit-scrollbar {
    display: none
}

.larkui-member-selector-batch-batchBox .desc {
    margin-bottom: 28px;
    color: rgba(0,0,0,.85);
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox .info {
    color: var(--yq-text-caption);
    font-size: 16px;
    line-height: 24px
}

.larkui-member-selector-batch-batchBox svg {
    display: block;
    margin: 0 auto
}

.larkui-member-selector-batch-batchBox .ant-upload {
    padding: 24px;
    border-radius: 8px;
    background-color: #f9fafa
}

.larkui-member-selector-batch-batchBox .ant-upload-hint {
    margin-top: 12px
}

.larkui-member-selector-batch-batchBox .footer {
    margin-top: 24px;
    text-align: right
}

.larkui-member-selector-batch-batchBox .footer .ant-btn {
    margin-right: 8px
}

.larkui-member-selector-batch-batchBox .footer .ant-btn:last-child {
    margin-right: 0
}

.larkui-member-selector-batch-batchBox .downloadBtn {
    margin-left: 20px
}

.larkui-member-selector-batch-batchBox .iconBtn {
    padding-right: 12px;
    padding-left: 12px
}

.larkui-member-selector-batch-batchBox .iconBtn>span:last-child {
    margin-left: 0
}

.larkui-member-selector-batch-batchBox .bodyContainer {
    padding: 16px 24px;
    min-height: 290px
}

.larkui-member-selector-batch-batchBox .uploading {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center
}

.larkui-member-selector-batch-batchBox .uploading>p {
    width: 100%;
    text-align: center
}

.larkui-member-selector-batch-batchBox .infoIcon {
    margin-bottom: 16px;
    font-size: 40px
}

.larkui-member-selector-batch-batchBox .infoIcon.success {
    color: var(--yq-ant-success-color)
}

.larkui-member-selector-batch-batchBox .infoIcon.warning {
    color: var(--yq-function-warning)
}

.larkui-member-selector-batch-batchBox .infoIcon.failed {
    color: var(--yq-function-error)
}

.larkui-member-selector-list .ant-list .ant-list-item {
    padding: 0;
    border-bottom: 0
}

.larkui-member-selector-list .ant-breadcrumb .ant-breadcrumb-link {
    font-weight: 400;
    cursor: pointer;
    color: var(--yq-text-primary)
}

.larkui-member-selector-list .ant-breadcrumb>span:last-child .ant-breadcrumb-link {
    font-weight: 500;
    color: var(--yq-black)
}

.larkui-member-selector-list-breadcrumb {
    margin-bottom: 16px;
    padding: 0 24px
}

.larkui-member-selector-list-breadcrumb .ant-breadcrumb {
    line-height: 2
}

.larkui-member-selector-list-item {
    padding-left: 24px;
    height: 42px;
    line-height: 42px;
    text-align: left;
    cursor: pointer;
    border-radius: 4px;
    color: var(--yq-text-primary);
    position: relative;
    padding-right: 70px;
    overflow: hidden;
    display: flex;
    align-items: center
}

.larkui-member-selector-list-item:hover {
    background: var(--yq-bg-secondary)
}

.larkui-member-selector-list-item-larkicon {
    color: var(--yq-text-caption);
    margin-right: 4px;
    vertical-align: middle
}

.larkui-member-selector-list-item-icon {
    color: var(--yq-text-caption)
}

.larkui-member-selector-list-item-name {
    color: var(--yq-text-primary);
    margin-left: 8px
}

.larkui-member-selector-list-item-action {
    position: absolute;
    right: 24px;
    top: 0;
    color: var(--yq-text-body);
    font-weight: 400
}

.larkui-member-selector-list-item-action .icon {
    display: inline-block;
    line-height: 42px;
    cursor: pointer;
    margin-left: 12px;
    transition: .2s ease-in-out
}

.larkui-member-selector-list-role-filter {
    position: absolute;
    top: 16px;
    right: 24px
}

.larkui-member-selector-list-select-dep {
    padding: 0 24px;
    margin-bottom: 18px;
    margin-top: 18px
}

.larkui-member-selector-list-select-dep-name {
    padding-left: inherit
}

.larkui-member-selector-list-select-all {
    padding: 0 24px;
    margin-bottom: 12px
}

.larkui-member-selector-list-user {
    padding: 3px 10px
}

.larkui-member-selector-list-icon {
    margin-right: 12px
}

.larkui-member-selector-list-name {
    margin-right: 16px
}

.larkui-member-selector-list-login {
    color: var(--yq-text-caption);
    font-size: 12px
}

.larkui-member-selector-list-title {
    align-items: baseline
}

.larkui-member-selector-list-member-type-switch-container {
    display: flex;
    align-items: center;
    color: var(--yq-text-body);
    font-size: 14px;
    padding: 15px 24px;
    border-top: solid 1px var(--yq-border-primary)
}

.larkui-member-selector-list-member-type-switch-container span {
    margin-left: 8px
}

.larkui-member-selector-list-member-switch {
    overflow: hidden
}

.larkui-member-selector-list-member-switch-inner {
    height: 345px;
    overflow: auto;
    scrollbar-width: none
}

.larkui-member-selector-list-member-switch-inner::-webkit-scrollbar {
    display: none
}

.larkui-member-selector-list-empty-view {
    text-align: center;
    color: var(--yq-yuque-grey-7)
}

.larkui-member-selector-list-empty-view img {
    padding-top: 48px;
    width: 80px;
    margin-bottom: 20px
}

.larkui-member-selector-import {
    padding: 24px
}

.larkui-member-selector-import-desc {
    margin-bottom: 16px
}

.DingTalkGroupSelector-module_groups_GrxWm .ant-list-item {
    padding: 8px 24px;
    height: 42px;
    line-height: 42px;
    text-align: left;
    cursor: pointer;
    border-radius: 4px;
    color: var(--yq-text-primary);
    position: relative;
    padding-right: 70px;
    overflow: hidden;
    display: flex;
    align-items: center;
    border-bottom: none
}

.DingTalkGroupSelector-module_groups_GrxWm .ant-list-item:hover {
    background: var(--yq-bg-secondary)
}

.DingTalkGroupSelector-module_checkbox_ryHx- {
    display: flex;
    align-items: center
}

.DingTalkGroupSelector-module_checkbox_ryHx- .ant-checkbox {
    position: relative;
    top: 0
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr {
    margin-left: 8px;
    display: flex;
    align-items: center
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr .DingTalkGroupSelector-module_avatar_7hoxt {
    display: inline-block;
    margin-right: 12px
}

.DingTalkGroupSelector-module_checkbox_ryHx- .DingTalkGroupSelector-module_user_e26Pr .DingTalkGroupSelector-module_name_xw1v1 {
    display: inline-block;
    max-width: 210px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.larkui-member-selector-list-select-all .ant-checkbox-wrapper span:last-child {
    padding-left: 15px
}

.larkui-member-selector-list-user {
    display: flex;
    justify-content: center;
    align-items: center
}

.ant-modal-content .larkui-member-selector-list .ant-list {
    background-color: revert
}

.larkui-member-selector-list .ant-list .ant-list-item {
    padding: 8px 0!important
}

.larkui-member-selector-list .ant-list .ant-list-item:hover {
    background-color: var(--yq-bg-secondary)
}

.larkui-member-selector-list-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px!important;
    max-width: 120px
}

.larkui-member-selector-list-item {
    padding-right: 0!important;
    height: auto!important;
    line-height: normal!important
}

.larkui-member-selector-list .ant-checkbox-wrapper {
    align-items: center;
    display: flex
}

.larkui-member-selector-list .ant-checkbox-wrapper .ant-checkbox {
    top: auto
}

.larkui-member-selector-list-title {
    display: flex
}

.larkui-member-selector-list-login {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100px
}

.larkui-member-selector-list-item-action {
    top: 5px!important
}

.OrgSearchNotFound-module_container_MlZtJ {
    width: 320px;
    height: 200px;
    text-align: center
}

.OrgSearchNotFound-module_icon_oJoZx {
    margin-top: 65px
}

.OrgSearchNotFound-module_content_CgmVs {
    font-size: 14px;
    margin-top: 15px;
    color: var(--yq-text-caption)
}

.OrgSearchNotFound-module_goToContacts_mFsnK {
    display: inline-flex;
    align-items: center;
    color: var(--yq-yuque-grey-7)
}

/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:02.731Z
 */
.cropper-container {
    direction: ltr;
    font-size: 0;
    line-height: 0;
    position: relative;
    -ms-touch-action: none;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.cropper-container img {
    backface-visibility: hidden;
    display: block;
    height: 100%;
    image-orientation: 0deg;
    max-height: none!important;
    max-width: none!important;
    min-height: 0!important;
    min-width: 0!important;
    width: 100%
}

.cropper-canvas,.cropper-crop-box,.cropper-drag-box,.cropper-modal,.cropper-wrap-box {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.cropper-canvas,.cropper-wrap-box {
    overflow: hidden
}

.cropper-drag-box {
    background-color: #fff;
    opacity: 0
}

.cropper-modal {
    background-color: #000;
    opacity: .5
}

.cropper-view-box {
    display: block;
    height: 100%;
    outline: 1px solid #39f;
    outline-color: rgba(51,153,255,.75);
    overflow: hidden;
    width: 100%
}

.cropper-dashed {
    border: 0 dashed #eee;
    display: block;
    opacity: .5;
    position: absolute
}

.cropper-dashed.dashed-h {
    border-bottom-width: 1px;
    border-top-width: 1px;
    height: 33.33333%;
    left: 0;
    top: 33.33333%;
    width: 100%
}

.cropper-dashed.dashed-v {
    border-left-width: 1px;
    border-right-width: 1px;
    height: 100%;
    left: 33.33333%;
    top: 0;
    width: 33.33333%
}

.cropper-center {
    display: block;
    height: 0;
    left: 50%;
    opacity: .75;
    position: absolute;
    top: 50%;
    width: 0
}

.cropper-center:after,.cropper-center:before {
    background-color: #eee;
    content: " ";
    display: block;
    position: absolute
}

.cropper-center:before {
    height: 1px;
    left: -3px;
    top: 0;
    width: 7px
}

.cropper-center:after {
    height: 7px;
    left: 0;
    top: -3px;
    width: 1px
}

.cropper-face,.cropper-line,.cropper-point {
    display: block;
    height: 100%;
    opacity: .1;
    position: absolute;
    width: 100%
}

.cropper-face {
    background-color: #fff;
    left: 0;
    top: 0
}

.cropper-line {
    background-color: #39f
}

.cropper-line.line-e {
    cursor: ew-resize;
    right: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-n {
    cursor: ns-resize;
    height: 5px;
    left: 0;
    top: -3px
}

.cropper-line.line-w {
    cursor: ew-resize;
    left: -3px;
    top: 0;
    width: 5px
}

.cropper-line.line-s {
    bottom: -3px;
    cursor: ns-resize;
    height: 5px;
    left: 0
}

.cropper-point {
    background-color: #39f;
    height: 5px;
    opacity: .75;
    width: 5px
}

.cropper-point.point-e {
    cursor: ew-resize;
    margin-top: -3px;
    right: -3px;
    top: 50%
}

.cropper-point.point-n {
    cursor: ns-resize;
    left: 50%;
    margin-left: -3px;
    top: -3px
}

.cropper-point.point-w {
    cursor: ew-resize;
    left: -3px;
    margin-top: -3px;
    top: 50%
}

.cropper-point.point-s {
    bottom: -3px;
    cursor: s-resize;
    left: 50%;
    margin-left: -3px
}

.cropper-point.point-ne {
    cursor: nesw-resize;
    right: -3px;
    top: -3px
}

.cropper-point.point-nw {
    cursor: nwse-resize;
    left: -3px;
    top: -3px
}

.cropper-point.point-sw {
    bottom: -3px;
    cursor: nesw-resize;
    left: -3px
}

.cropper-point.point-se {
    bottom: -3px;
    cursor: nwse-resize;
    height: 20px;
    opacity: 1;
    right: -3px;
    width: 20px
}

@media (min-width: 768px) {
    .cropper-point.point-se {
        height:15px;
        width: 15px
    }
}

@media (min-width: 992px) {
    .cropper-point.point-se {
        height:10px;
        width: 10px
    }
}

@media (min-width: 1200px) {
    .cropper-point.point-se {
        height:5px;
        opacity: .75;
        width: 5px
    }
}

.cropper-point.point-se:before {
    background-color: #39f;
    bottom: -50%;
    content: " ";
    display: block;
    height: 200%;
    opacity: 0;
    position: absolute;
    right: -50%;
    width: 200%
}

.cropper-invisible {
    opacity: 0
}

.cropper-bg {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC")
}

.cropper-hide {
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.cropper-hidden {
    display: none!important
}

.cropper-move {
    cursor: move
}

.cropper-crop {
    cursor: crosshair
}

.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point {
    cursor: not-allowed
}

.index-module_iconListContainer_2kTPj {
    min-width: 324px;
    padding: 0;
    display: flex;
    flex-direction: column
}

.index-module_iconListHeader_zgb1B {
    width: 100%;
    height: 42px;
    display: flex;
    align-items: center;
    padding-left: 27px;
    gap: 8px;
    border-bottom: 1px solid var(--yq-border-primary)
}

.index-module_iconListHeaderTab_Rb7UW {
    cursor: pointer;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.index-module_iconListHeaderTabActive_l9CpK {
    color: var(--yq-text-primary);
    font-weight: 500
}

.index-module_iconListHeaderTabActive_l9CpK:after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    height: 2px;
    width: 100%;
    border-radius: 1px;
    background-color: var(--yq-text-primary)
}

.index-module_iconListContent_5acPP {
    height: 170px;
    display: grid;
    grid-template-rows: repeat(3,1fr);
    grid-template-columns: repeat(6,1fr);
    grid-gap: 8px;
    gap: 8px;
    padding: 16px
}

.index-module_iconListIconContainer_W2jPd {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: center;
    justify-self: center;
    width: 27px;
    height: 27px
}

.index-module_iconListIconContainerActive_m\+9aX:after {
    visibility: visible
}

.index-module_groupIconBtn_1pBes {
    width: 40px!important;
    height: 40px!important
}

.index-module_groupIconBtn_1pBes,.index-module_groupIconBtnLarge_PGNAs {
    display: flex!important;
    align-items: center;
    justify-content: center;
    padding: 0!important
}

.index-module_groupIconBtnLarge_PGNAs {
    width: 56px!important;
    height: 56px!important
}

.index-module_uploader_uEToK {
    min-height: 170px;
    padding: 16px
}

.index-module_dragger_yzTL7 {
    height: 138px!important;
    padding: 1px
}

.index-module_dragger_yzTL7 .ant-upload {
    background-color: var(--yq-yuque-grey-1)
}

.index-module_imageContainer_lklS2 {
    padding: 8px 8px 0 8px
}

.index-module_uploaderContainer_Y0VkK {
    display: flex;
    align-items: flex-start
}

.index-module_uploaderContainer_Y0VkK .cropper-container {
    background-color: var(--yq-bg-secondary)
}

.index-module_reupload_3agQP {
    margin: 23px 0
}

.index-module_uploaderFooter_F5A9k {
    height: 50px;
    border-top: 1px solid var(--yq-border-primary);
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    gap: 8px;
    margin: 0 -24px;
    padding: 0 24px
}

.index-module_previewContainer_s7urA {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center
}

.index-module_preview_8GFmF {
    width: 108px;
    height: 108px;
    overflow: hidden;
    background-color: var(--yq-bg-secondary)
}

.index-module_newGroupModal_An\+Qw .ant-modal-header {
    border-bottom: none;
    padding-top: 20px;
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-modal-body {
    padding-bottom: 0
}

.index-module_newGroupModal_An\+Qw .ant-form-vertical .ant-form-item-label>label {
    font-weight: 500
}

.index-module_newGroupModal_An\+Qw .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding: 0 24px 24px 24px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv {
    font-size: 16px
}

.index-module_newGroupModal_An\+Qw .index-module_title_gTevv .index-module_titleDesc_EmDkC {
    margin-top: 8px;
    color: var(--yq-text-caption);
    font-weight: 400;
    font-size: 14px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 {
    height: 40px;
    display: flex;
    margin-bottom: -12px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_nameWrapper_OFOD6 .index-module_avatarGroup_olnac {
    border-radius: 44px;
    width: 44px;
    height: 44px;
    margin-right: 8px
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 {
    width: 384px;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a {
    cursor: pointer;
    color: var(--yq-text-link)
}

.index-module_newGroupModal_An\+Qw .index-module_newGroupModalContent_LFB42 .index-module_addMember_i0kI8 .index-module_addMemberBtn_nvC2a .index-module_btnText_6V0fp {
    margin-left: 4px;
    font-weight: 400
}

.index-module_newGroupModal_An\+Qw .index-module_submitBtn_ZJkHv {
    max-width: 100%;
    width: 384px;
    height: 40px
}

.group-selector .ant-select-item-group {
    background: var(--yq-bg-secondary)
}

.group-selector .group-selector-item {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 100%
}

.group-selector .group-selector-item>.larkicon {
    margin-left: 8px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-avatar {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 24px 24px;
    margin-right: 12px
}

.group-selector .group-selector-item .group-avatar .larkicon-svg-group-intranet,.group-selector .group-selector-item .group-avatar .larkicon-svg-group-lock {
    width: 12px;
    height: 12px
}

.group-selector .group-selector-item .group-selector-org-icon {
    margin-right: 12px;
    transform: scale(.8);
    color: var(--yq-text-caption)
}

.group-selector .group-selector-item .group-selector-org-name {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.group-selector-disabled .selector-static {
    background: var(--yq-bg-secondary);
    cursor: not-allowed;
    border: 1px solid var(--yq-border-primary)
}

.group-selector-disabled .selector-static:before {
    display: none
}

.group-selector .ant-select-dropdown {
    width: 280px
}

.group-selector .ant-select-item-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.group-selector .ant-select-item-option-content .larkui-icon-check-outlined {
    margin: 0 0 0 8px;
    color: var(--yq-theme)
}

.group-selector .ant-select-selector {
    font-size: 14px
}

.index-module_name_2g47z {
    max-width: 200px
}

.form-container .ant-card,.form .ant-card,.setting-form .ant-card {
    margin-bottom: 16px
}

.card-form>.ant-card-head {
    border-bottom: none
}

.card-form .card-form-title {
    font-size: 24px;
    line-height: 2;
    color: #262626;
    font-weight: 700
}

.ant-legacy-form .ant-legacy-form-item {
    color: var(--yq-text-primary)
}

.ant-legacy-form .ant-legacy-form-item:last-child {
    margin-bottom: 0
}

.ant-legacy-form .ant-legacy-form-item-required:before {
    display: none
}

.ant-legacy-form .ant-legacy-form-item-label .tip {
    font-size: 12px;
    color: var(--yq-ant-text-color-secondary)
}

.ant-legacy-form .ant-legacy-form-explain {
    clear: both
}

.ant-legacy-form .ant-card-body {
    color: var(--yq-text-primary)
}

.ant-legacy-form-extra .lark-btn-text {
    color: var(--yq-ant-link-color)
}

.ant-legacy-form-extra .lark-btn-text:hover {
    color: var(--yq-ant-link-hover-color)
}

.ant-legacy-form-extra .lark-btn-text:focus {
    color: var(--yq-text-link)
}

.setting-form .ant-card-body {
    color: var(--yq-text-primary)
}

.ant-legacy-form-explain {
    margin-top: 4px
}

.BookIcon-module_bookIconPopover_-uyNA .ant-popover-inner-content {
    padding: 0!important
}

.BookIcon-module_bookTitle_hK56I {
    display: inline-block;
    vertical-align: middle
}

.BookIcon-module_bookIconSelector_yaICm {
    width: 260px;
    border-radius: 4px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorTab_SP0xx {
    height: 34px;
    border-bottom: 1px solid var(--yq-border-primary);
    line-height: 32px;
    overflow: hidden
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorTab_SP0xx li {
    margin-left: 15px;
    font-size: 12px;
    transition: all .3s;
    display: inline-block;
    vertical-align: middle;
    color: var(--yq-text-primary);
    cursor: pointer
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorTab_SP0xx .BookIcon-module_iconSelectorTabActive_l6Wg3 {
    font-weight: 700
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContent_3acOH,.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE {
    height: 150px;
    overflow: auto;
    overflow-x: hidden;
    line-height: 32px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContent_3acOH li,.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE li {
    margin-top: 19px;
    margin-left: 19px;
    width: 20px;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContent_3acOH li svg,.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE li svg {
    width: 28px;
    height: 28px;
    position: relative;
    left: -4px;
    top: -1px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContent_3acOH li svg:hover,.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE li svg:hover {
    background-color: var(--yq-bg-primary-hover);
    outline: 3px solid var(--yq-border-primary)
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE {
    height: 138px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorContentTab2_sapYE li svg {
    width: 28px;
    height: 28px;
    position: relative;
    left: -4px;
    top: -1px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorColorPicker_W\+uxH {
    margin: 0 20px;
    border-top: 1px solid var(--yq-border-primary);
    line-height: 54px;
    height: 54px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorColorPicker_W\+uxH span {
    background: var(--yq-yuque-grey-7);
    color: var(--yq-black);
    display: inline-block;
    vertical-align: middle;
    margin-right: 20px;
    width: 20px;
    height: 20px;
    border-radius: 2px;
    text-align: center;
    line-height: 20px;
    cursor: pointer
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorColorPicker_W\+uxH span.BookIcon-module_spanActive_zUcmt:after {
    content: "";
    display: block;
    height: 100%;
    background: 50% url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAJCAMAAAA8eE0hAAAAOVBMVEVHcEz///////////////////////////////////////////////////////////////////////99PJZNAAAAEnRSTlMAp5i04FoJ9lLIKWUOR4Z9MCOYVTs+AAAAQUlEQVQI1y3LCw7AIAgD0LoxAXW/3v+wYwoJ4aUpwJrqewqFnurUtvQoD8Bih3CLQHj9xRo0pVPf2QvT8vuUe94PSQUBse78ocEAAAAASUVORK5CYII=) no-repeat;
    background-size: 8px 8px
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorColorPicker_W\+uxH span:last-child {
    margin-right: 0
}

.BookIcon-module_bookIconSelector_yaICm .BookIcon-module_iconSelectorColorPicker_W\+uxH span:hover {
    outline: 3px solid var(--yq-white);
    box-shadow: 0 5px 7px 0 rgba(0,0,0,.3),5px 0 7px 0 rgba(0,0,0,.3)
}

.index-module_newRepoWrap_dEIFH {
    display: flex;
    position: relative;
    height: 100%;
    padding-bottom: 48px
}

.index-module_newRepoWrap_dEIFH .ant-select-selector {
    font-size: 14px
}

.index-module_newRepoWrap_dEIFH .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: var(--yq-bg-secondary)!important;
    border-color: var(--yq-yuque-grey-5)
}

.index-module_newRepoWrap_dEIFH .ant-btn-primary {
    border: none
}

.index-module_newRepoWrap_dEIFH .ant-btn-primary[disabled] {
    border: none;
    color: var(--yq-white);
    font-weight: 500
}

.index-module_newRepoWrap_dEIFH .ant-form-vertical .ant-form-item-label>label {
    font-weight: 500
}

.index-module_newRepoWrap_dEIFH .group-avatar {
    display: inline-flex!important;
    background-color: var(--yq-yuque-grey-2);
    border-radius: 6px
}

.index-module_newRepoWrap_dEIFH .index-module_content_Paldp {
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 24px 20px;
    margin-top: 24px;
    height: 100%
}

.index-module_newRepoWrap_dEIFH .index-module_content_Paldp .index-module_basicInfoNormal_0J70G {
    display: flex;
    margin-bottom: -12px
}

.index-module_newRepoWrap_dEIFH .index-module_content_Paldp .index-module_basicInfoNormal_0J70G .index-module_bookIcon_LCBKs {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    padding: 6px;
    width: 40px;
    height: 40px;
    margin-right: 6px;
    position: relative;
    line-height: 20px;
    transition: all .3s;
    text-align: center
}

.index-module_newRepoWrap_dEIFH .index-module_submitBtnWrapper_D\+zDy {
    position: absolute;
    bottom: 24px;
    width: 100%;
    text-align: center;
    background-color: var(--yq-bg-primary)
}

html[data-kumuhana=pouli] .index-module_newRepoWrap_dEIFH .index-module_submitBtnWrapper_D\+zDy {
    background-color: transparent
}

.index-module_newRepoWrap_dEIFH .index-module_submitBtnWrapper_D\+zDy .index-module_submitBtn_ANP9f {
    width: calc(100% - 48px);
    height: 40px
}

.index-module_spinArea_HqKpu {
    z-index: 99999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--yq-white);
    opacity: .5;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_newRepoModal_Z6mze .ant-modal-content {
    border-radius: 8px
}

.index-module_newRepoModal_Z6mze .ant-modal-header {
    border-bottom: none;
    padding: 20px 24px 0
}

.index-module_newRepoModal_Z6mze .ant-modal-body {
    padding: 0
}

.index-module_newRepoModal_Z6mze .index-module_title_ECR9O {
    font-size: 16px
}

.index-module_more_sDgGm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--yq-black);
    border-radius: 8px
}

.ant-popover-open.index-module_more_sDgGm,.index-module_more_sDgGm:hover {
    border-radius: 8px
}

.toggle-publicity {
    color: var(--yq-text-link)!important;
    margin-left: 12px
}

.index-module_label_qcR\+D {
    border-radius: 2px;
    background-color: var(--yq-bg-tertiary);
    padding: 0 4px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    color: var(--yq-text-caption);
    text-align: center
}

.DocTask-module_docTask_NywmO {
    padding: 30px 0 20px
}

.DocTask-module_docTask_NywmO .larkui-icon-check-outlined-circle {
    color: var(--yq-theme);
    font-size: 22px
}

.DocTask-module_docTask_NywmO .larkui-icon-close-circle,.DocTask-module_docTask_NywmO .larkui-icon-closecircleoutlined {
    color: var(--yq-function-error);
    font-size: 20px
}

.DocTask-module_docTask_NywmO .action,.DocTask-module_docTask_NywmO .error,.DocTask-module_docTask_NywmO .icon,.DocTask-module_docTask_NywmO .tip {
    line-height: 27px;
    text-align: center;
    color: var(--yq-text-primary)
}

.DocTask-module_docTask_NywmO .error {
    color: var(--yq-function-error)
}

.DocTask-module_settingFile_bFy51 {
    margin-top: 16px;
    margin-bottom: 16px
}

.DocTask-module_hr_y8Wz9 {
    background-color: var(--yq-yuque-grey-5);
    height: 1px;
    margin: 15px 0 5px
}

.DocTask-module_settingFileIcon_\+\+xRm {
    width: 64px;
    height: 64px;
    margin-right: 5px
}

.DocTask-module_settingFileIcon_\+\+xRm img {
    width: 100%
}

.DocTask-module_settingContent_leSlb {
    min-height: 140px
}

.DocTask-module_settingContent_leSlb .ant-checkbox-wrapper,.DocTask-module_settingContent_leSlb .ant-radio-wrapper {
    margin-top: 8px;
    margin-left: 0;
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form .ant-legacy-form-item {
    margin-bottom: 0;
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-control {
    line-height: 1.5
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-item-label>label {
    color: var(--yq-text-body)
}

.DocTask-module_settingContent_leSlb .ant-radio-group {
    border-top: 1px solid var(--yq-yuque-grey-5);
    margin-top: 10px
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label {
    display: block
}

.DocTask-module_settingContent_leSlb .ant-legacy-form-horizontal .ant-legacy-form-item-label>label.ant-legacy-form-item-no-colon:after {
    display: none
}

.DocTask-module_settingContent_leSlb .DocTask-module_isFirst_RPJNh .ant-radio-group {
    border-top: 0 none;
    margin-top: 0;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    padding-bottom: 10px
}

.DocTask-module_settingTitlt2_OZfjd {
    font-size: 14px;
    color: var(--yq-text-primary);
    font-weight: 400;
    margin: 8px 0
}

.DocTask-module_settingFileTypeName_S4Bv1 {
    color: var(--yq-yuque-grey-9)
}

.DocTask-module_settingFileTypeExt_GCoBd {
    color: var(--yq-text-caption);
    font-size: 12px
}

.index-module_fileTypeSelector_73BHW {
    margin: 16px 0
}

.index-module_fileTypeSelector_73BHW .index-module_item_H3xgY {
    height: 152px
}

.index-module_fileTypeSelector_73BHW .index-module_fileType_l65Jm {
    width: 142px;
    text-align: center;
    padding: 16px 0 20px;
    cursor: pointer;
    transition: all .3s linear
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM {
    width: 70px;
    height: 70px;
    margin: 0 auto
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeIcon_eSmiM img {
    width: 100%
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeMeta_4eYr8 {
    text-align: center;
    line-height: 24px;
    margin-top: 4px
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeName_9B0Rn {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeExt_wTTFB {
    font-size: 12px;
    line-height: 1;
    color: var(--yq-text-caption)
}

.index-module_fileTypeSelector_73BHW .index-module_fileTypeReady_TAUvL {
    filter: grayscale(1);
    cursor: default;
    opacity: .45
}

.EvernoteDirUpload-module_modal_AERn- {
    padding: 16px
}

.EvernoteDirUpload-module_modal_AERn- .ant-upload {
    width: 100%
}

.EvernoteDirUpload-module_modal_AERn- .ant-modal-close-x {
    width: 24px;
    height: 24px;
    line-height: 32px
}

.EvernoteDirUpload-module_dragZone_qrdZx {
    border: dashed 1px var(--yq-border-primary);
    border-radius: 8px;
    width: 100%;
    height: 180px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_tip_FtuKl {
    padding-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption)
}

.EvernoteDirUpload-module_dragZone_qrdZx .EvernoteDirUpload-module_icon_ZYE-k {
    color: var(--yq-text-caption);
    margin-bottom: 12px
}

.EvernoteDirUpload-module_dragZone_qrdZx.EvernoteDirUpload-module_dragOver_0DaAV {
    border-color: var(--yq-blue-5)
}

.EvernoteDirUpload-module_dragZone_qrdZx:hover {
    border-color: var(--yq-border-primary-active)
}

.BookExport-module_docExport_44PUl .BookExport-module_title_Hezdm {
    font-size: 14px;
    line-height: 24px;
    color: var(--yq-text-primary);
    font-weight: 700
}

.BookExport-module_docExport_44PUl .BookExport-module_tips_VJJKo {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.BookExport-module_setting_PXAtw .BookExport-module_settingTitle_pEiZb {
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookExport-module_setting_PXAtw .ant-upload-select,.BookExport-module_setting_PXAtw .BookExport-module_settingUpload_sNiNV {
    width: 100%
}

.index-module_content_v7lvp,.index-module_form_5k71F .larkui-form-item {
    margin-bottom: 16px
}

.index-module_form_5k71F .larkui-form-item:last-child {
    margin-bottom: 0
}

.index-module_tip_yNrai {
    line-height: 40px;
    color: var(--yq-text-primary)
}

.BookAction-module_wrap_AiMmc .ant-menu {
    min-width: 89px;
    padding: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.BookAction-module_wrap_AiMmc .ant-menu-item-divider {
    margin: 4px 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item {
    color: var(--yq-text-body);
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:not(:last-child) {
    margin: 0
}

.BookAction-module_wrap_AiMmc .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_bookTitle_lUUrB {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB {
    padding-left: 12px;
    flex: 1;
    overflow: hidden
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_group_xaGsx {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-caption);
    font-size: 13px;
    line-height: 18px
}

.index-module_bookTitle_lUUrB .index-module_name_OMCDB .index-module_text_UrMgl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
    line-height: 21px;
    padding-bottom: 4px
}

.index-module_bookTitle_lUUrB .index-module_checked_3fCif {
    padding: 0 4px;
    color: var(--yq-text-link);
    font-size: 20px;
    width: 28px
}

.index-module_moreActions_YYACs {
    margin-left: 8px
}

.book-link,.book-name,.lark-book-title {
    width: 100%;
    display: flex;
    align-items: center;
    line-height: 1.35
}

.lark-book-title .book-icon {
    margin-right: 8px
}

.book-name {
    display: flex;
    align-items: center
}

.book-name .book-name-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.book-name .book-name-scope {
    position: relative
}

.book-name .book-name-scope a {
    pointer-events: none
}

.book-name .book-name-scope .icon-svg {
    margin-left: 5px
}

.book-name .book-name-orglabel {
    margin-left: 12px
}

.book-name .icon-svg {
    display: block
}

.book-name-split {
    margin: 0 4px
}

.permission__public__tip {
    margin-top: 12px;
    line-height: 22px
}

.permission__public__tip .highlight {
    color: var(--yq-function-error)
}

.belong-icon {
    font-size: 12px;
    margin-left: 4px
}

.BookSelector-module_books_ZtLiq .ant-modal-body {
    padding: 0
}

.BookSelector-module_books_ZtLiq .ant-list {
    height: 416px;
    overflow: auto;
    padding: 8px 24px 0;
    border-radius: 6px
}

.BookSelector-module_books_ZtLiq .ant-list-item:last-child {
    border-bottom: none!important;
    margin-bottom: 16px
}

.BookSelector-module_books_ZtLiq .book-link {
    max-width: 360px
}

.BookSelector-module_books_ZtLiq .book-name {
    color: var(--yq-text-body)
}

.BookSelector-module_books_ZtLiq .ant-list-item-meta-description {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 360px;
    font-size: 12px
}

.BookSelector-module_search_oB4DW {
    margin: 16px 24px 8px
}

.BookSelector-module_loadMore_\+6Skv {
    background: linear-gradient(var(--yq-white),var(--yq-yuque-grey-2));
    border-radius: 4px;
    border: 1px solid var(--yq-yuque-grey-5);
    text-align: center;
    height: 40px;
    line-height: 40px;
    margin-top: 16px;
    margin-bottom: 24px
}

.index-module_revertLink_csROb {
    margin-left: 4px
}

.BaseSettings-module_baseSettingsMenu_dc9ae .ant-menu-item {
    display: flex;
    align-items: center
}

.BaseSettings-module_baseSettingsMenu_dc9ae .ant-menu-item-icon+span {
    margin-left: 8px
}

.BaseSettings-module_moreActionIcon_lbrJG {
    color: var(--yq-icon-secondary)
}

.BaseSettings-module_moreActionDelete_BUd4F {
    color: var(--yq-red-6)
}

.BookSettingOptions-module_extra_ofpiD {
    top: 12px
}

.BookSettingOptions-module_extra_ofpiD,.BookSettingOptions-module_extraBottom_ru3CD {
    visibility: hidden;
    position: absolute;
    right: 12px;
    border-radius: 8px;
    border: 1px solid var(--yq-bg-primary-hover-light)
}

.BookSettingOptions-module_extraBottom_ru3CD {
    bottom: 32px;
    cursor: pointer
}

.BookSettingOptions-module_popoverMoreAction_dSGp7 .ant-popover-inner-content {
    padding: 0
}

.BookSettingOptions-module_moveBook_j4HnN:hover {
    background-color: var(--yq-bg-primary-hover)
}

.BookSettingOptions-module_subMenu_2ijVT .ant-menu-item {
    color: var(--yq-text-primary)
}

.BookSettingOptions-module_subMenu_2ijVT .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.BookSettingOptions-module_moreActionIcon_6U9hi {
    color: var(--yq-icon-secondary)
}

.BookSettingOptions-module_pinTitleItem_PMzZV {
    display: inline-block;
    width: 100%
}

.BookSettingOptions-module_pinTitleItem_PMzZV div {
    display: inline-block;
    line-height: 38px;
    height: 38px;
    position: relative;
    color: var(--yq-text-primary)
}

.BookSettingOptions-module_pinTitleItem_PMzZV svg {
    margin-right: 9px;
    margin-left: -1px;
    position: relative;
    color: var(--yq-yuque-grey-8)
}

.index-module_container_6I\+xv {
    width: 100%;
    white-space: nowrap
}

.index-module_container_6I\+xv>span {
    display: inline-block
}

.index-module_container_6I\+xv>span.index-module_truncated_D2TlC {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis
}

.Thumbnail-module_thumbnail_OF1hP {
    width: 30px;
    height: 30px;
    background-size: cover;
    background-position: 50%;
    border-radius: 2px;
    flex-shrink: 0
}

.ResourceItemName-module_name_oHtOd {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ResourceItemName-module_name_oHtOd>a,.ResourceItemName-module_name_oHtOd>span {
    display: flex;
    align-items: center
}

.ResourceItemName-module_name_oHtOd.ResourceItemName-module_mobile_hKJhc>a {
    height: 40px
}

.ResourceItemName-module_name_oHtOd>a {
    color: var(--yq-text-body);
    transition: color .35s ease
}

.ResourceItemName-module_name_oHtOd:hover>a {
    color: var(--yq-text-primary)
}

.ResourceItemName-module_nameText_S6lyp {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 12px;
    height: 32px;
    line-height: 32px
}

.ResourceItemName-module_nameText_S6lyp.ResourceItemName-module_mobile_hKJhc {
    display: block;
    top: -10px;
    position: relative;
    font-size: 15px
}

.ResourceItemName-module_editing_\+nmFh {
    display: flex;
    align-items: center
}

.ResourceItemName-module_editing_\+nmFh .ant-input {
    margin-left: 8px
}

.BookSummary-module_column_K18G-,.BookSummary-module_default_WpxoC,.BookSummary-module_design_N-A39,.BookSummary-module_empty_1DTzr,.BookSummary-module_resource_tasct {
    min-height: 95px
}

.BookSummary-module_default_WpxoC {
    padding-left: 16px;
    padding-top: 0;
    margin-right: 4px;
    margin-left: 4px
}

.BookSummary-module_default_WpxoC>li {
    position: relative
}

.BookSummary-module_default_WpxoC>li:before {
    content: "";
    width: 4px;
    height: 4px;
    border-radius: 4px;
    background-color: var(--yq-text-caption);
    position: absolute;
    left: -16px;
    top: 50%;
    margin-top: -2px
}

.BookSummary-module_default_WpxoC .BookSummary-module_item_J-WKN {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 21px;
    color: var(--yq-text-caption)
}

.BookSummary-module_default_WpxoC .BookSummary-module_item_J-WKN:hover {
    color: var(--yq-text-body)
}

.BookSummary-module_default_WpxoC .BookSummary-module_itemText_1czVk {
    flex: auto;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BookSummary-module_default_WpxoC .BookSummary-module_itemText_1czVk>.doc-title {
    color: var(--yq-text-caption)
}

.BookSummary-module_default_WpxoC .BookSummary-module_itemTime_\+aQcL {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 80px;
    margin-left: 8px;
    font-size: 12px;
    line-height: 21px;
    text-align: right;
    color: var(--yq-text-caption)
}

.BookSummary-module_column_K18G- .BookSummary-module_item_J-WKN {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    line-height: 21px;
    color: var(--yq-text-caption)
}

.BookSummary-module_column_K18G- .BookSummary-module_item_J-WKN:hover {
    color: var(--yq-text-body)
}

.BookSummary-module_column_K18G- .BookSummary-module_itemTitle_KGrLO {
    display: -webkit-box;
    max-height: 63px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 21px;
    word-break: break-word;
    max-width: 100%
}

.BookSummary-module_column_K18G- .BookSummary-module_itemCover_6sg8F {
    width: 128px;
    height: 90px;
    border-radius: 6px;
    overflow: hidden;
    margin-left: 16px;
    background-size: cover;
    background-position: 50%
}

.BookSummary-module_column_K18G- .BookSummary-module_itemTime_\+aQcL {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 100%;
    margin-top: 6px;
    font-size: 12px;
    line-height: 21px;
    color: var(--yq-text-caption)
}

.BookSummary-module_design_N-A39 {
    display: flex;
    margin-left: -2px;
    margin-right: -2px
}

.BookSummary-module_design_N-A39>li {
    width: 33.3%;
    margin: 0 2px
}

.BookSummary-module_design_N-A39 .BookSummary-module_item_J-WKN {
    width: 100%;
    height: 95px;
    overflow: hidden
}

.BookSummary-module_design_N-A39 .BookSummary-module_item_J-WKN>a {
    display: block;
    width: 100%;
    height: 100%;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover
}

.BookSummary-module_thumbDesign_IeA8m {
    width: 100%;
    height: 100%;
    background-color: var(--yq-bg-tertiary)
}

.BookSummary-module_empty_1DTzr {
    display: flex;
    flex-flow: column;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BookSummary-module_empty_1DTzr .BookSummary-module_emptyText_\+yZ7q {
    line-height: 80px
}

.BookSummary-module_empty_1DTzr .BookSummary-module_img_M46rn {
    margin-bottom: 12px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: 50%
}

.BookSummary-module_empty_1DTzr .BookSummary-module_imgBook_C9eto,.BookSummary-module_empty_1DTzr .BookSummary-module_imgColumn_I4Ai3,.BookSummary-module_empty_1DTzr .BookSummary-module_imgThread_LV4MR {
    width: 42px;
    height: 56px;
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/xfgHVOXrEezlrZPRFbYN.svg)
}

.BookSummary-module_empty_1DTzr .BookSummary-module_imgSheet_LIiHK {
    width: 46px;
    height: 56px;
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/YIubDDxxziszEIlJxaLh.svg)
}

.BookSummary-module_empty_1DTzr .BookSummary-module_imgDesign_EA7JK {
    width: 56px;
    height: 56px;
    background-image: url(https://gw.alipayobjects.com/zos/rmsportal/MMTBHJunhJPBopgBVGoy.svg)
}

.BookSummary-module_resource_tasct .BookSummary-module_item_J-WKN {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 21px;
    color: var(--yq-text-caption)
}

.BookSummary-module_resource_tasct .BookSummary-module_item_J-WKN:hover {
    color: var(--yq-text-body)
}

.BookSummary-module_resource_tasct .BookSummary-module_itemText_1czVk {
    flex: auto;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BookSummary-module_resource_tasct .BookSummary-module_itemText_1czVk>.doc-title {
    color: var(--yq-text-caption)
}

.BookSummary-module_resource_tasct .BookSummary-module_itemTime_\+aQcL {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 80px;
    margin-left: 8px;
    font-size: 12px;
    line-height: 21px;
    text-align: right;
    color: var(--yq-text-caption)
}

.index-module_coverUploaderWrapper_JM8NQ {
    display: flex;
    font-size: 14px;
    color: var(--yq-text-body);
    line-height: 1.5;
    position: relative
}

.index-module_miniCoverUploaderWrapper_VBkGu {
    display: block
}

.index-module_cropper_FJZMe {
    position: relative;
    overflow: hidden;
    width: 240px;
    min-width: 240px;
    min-height: 148px;
    background-color: var(--yq-bg-tertiary);
    border-radius: 6px
}

.index-module_cropper_FJZMe .ant-upload.ant-upload-drag .ant-upload {
    padding: 0
}

.index-module_cropper_FJZMe .ant-upload.ant-upload-drag {
    border-color: transparent
}

.index-module_miniCropperWrapper_kKArf {
    display: flex;
    align-items: center;
    flex-direction: column
}

.index-module_miniCropper_x-fcL {
    width: 100%;
    min-height: 148px;
    position: relative;
    overflow: hidden;
    background-color: var(--yq-bg-tertiary);
    border-radius: 6px
}

.index-module_miniCropper_x-fcL .ant-upload.ant-upload-drag .ant-upload {
    padding: 0
}

.index-module_miniCropper_x-fcL .ant-upload.ant-upload-drag {
    border-color: transparent
}

.index-module_cropperInner_cE6wj,.index-module_miniCropperInner_rEny\+ {
    min-width: 238px;
    min-height: 143px
}

.index-module_coverThumb_aqkDl {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain
}

.index-module_placeholderWrapper_I8JnT {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: var(--yq-bg-tertiary)
}

.index-module_miniPlaceholderWrapper_CXNui {
    position: relative;
    width: 100%;
    height: 148px;
    background-color: var(--yq-bg-tertiary)
}

.index-module_placeholder_fRP2- {
    position: absolute;
    top: 74px;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    font-size: 12px;
    color: var(--yq-text-body);
    line-height: 1.5;
    text-align: center
}

.index-module_placeholderImg_RI3Xy {
    margin: 0 auto 16px;
    width: 72px;
    height: 72px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*o8puS5nC5Z4AAAAAAAAAAABkARQnAQ) no-repeat 50%;
    background-size: 100%
}

.index-module_preview_0S38r {
    width: 153px;
    height: 99px;
    overflow: hidden;
    border-radius: 6px
}

.index-module_miniPreview_siGvw {
    width: 100%;
    height: 99px;
    overflow: hidden;
    border-radius: 6px
}

.index-module_uploadWrapper_Lddj1 {
    margin-left: 16px
}

.index-module_miniUploadWrapper_kA5F2,.index-module_uploadWrapper_Lddj1 {
    display: flex;
    flex-flow: column;
    justify-content: flex-end;
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_uploadTip_-PrVc {
    margin-top: 12px
}

.index-module_uploadAction_IvY3J {
    margin-top: 16px;
    display: flex;
    align-items: center;
    color: var(--yq-text-caption)
}

.index-module_miniUploadAction_zkhyc {
    margin-top: 14px;
    display: flex;
    justify-content: center
}

.index-module_miniUploadAction_zkhyc .index-module_uploadBtn_3NoLt {
    width: 197px
}

.index-module_miniUploadAction_zkhyc .index-module_clearBtn_Jvd1P {
    width: 60px;
    margin-left: 8px
}

.index-module_btnRemove_ewCeL {
    margin-left: 8px;
    transition: color .35s ease;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--yq-text-link)
}

.index-module_btnRemove_ewCeL .index-module_btnRemoveIcon_Kaa0G {
    margin-right: 4px;
    transition: all .35s ease;
    fill: var(--yq-ant-link-color)
}

.index-module_btnRemove_ewCeL .index-module_btnRemoveIcon_Kaa0G svg {
    position: relative;
    top: -1px;
    display: inline-block;
    vertical-align: middle
}

.index-module_btnRemove_ewCeL:hover {
    color: var(--yq-ant-link-hover-color)
}

.index-module_btnRemove_ewCeL:hover .index-module_btnRemoveIcon_Kaa0G {
    fill: var(--yq-ant-link-hover-color)
}

@media only screen and (max-width: 576px) {
    .index-module_coverUploaderWrapper_JM8NQ {
        flex-direction:column
    }

    .index-module_uploadWrapper_Lddj1 {
        margin-left: 0;
        margin-top: 16px
    }
}

.Books-module_books_0wdlm {
    margin: 0;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap
}

.Books-module_books_0wdlm .Books-module_overlayBook_Ot5kV {
    margin: 0 0 16px 0
}

.Books-module_books_0wdlm.Books-module_twice_JVRN2 .Books-module_overlayBook_Ot5kV {
    width: calc(50% - 16px)
}

.Books-module_books_0wdlm.Books-module_thrice_17RTp .Books-module_overlayBook_Ot5kV {
    width: calc(33.33333% - 10px)
}

.Books-module_books_0wdlm .Books-module_cardMetaIcon_KJzga {
    margin-right: -6px
}

.Books-module_books_0wdlm .Books-module_cardTitleInput_NbgQc {
    width: auto
}

.Books-module_books_0wdlm .ant-card {
    margin-bottom: 16px
}

.Books-module_books_0wdlm .placeholder {
    height: 112px;
    pointer-events: none;
    background: var(--yq-bg-tertiary)
}

.Books-module_books_0wdlm .placeholder.ant-card {
    background: none;
    border: none
}

.Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_once_oA7gD .Books-module_book_wAfDM.ant-card,.Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_once_oA7gD .placeholder {
    width: 100%
}

@media only screen and (max-width: 991px) {
    .Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_thrice_17RTp .Books-module_book_wAfDM.ant-card,.Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_thrice_17RTp .placeholder,.Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_twice_JVRN2 .Books-module_book_wAfDM.ant-card,.Books-module_books_0wdlm:not(.Books-module_list_W-wMF).Books-module_twice_JVRN2 .placeholder {
        width:100%
    }
}

.Books-module_books_0wdlm:empty {
    margin-top: 0
}

.Books-module_books_0wdlm:empty:before {
    content: attr(data-empty);
    display: block;
    width: 100%;
    height: 112px;
    line-height: 112px;
    text-align: center;
    color: var(--yq-text-caption)
}

.Books-module_books_0wdlm .Books-module_book_wAfDM .Books-module_settingOptions_ibBTw {
    top: 14px
}

.Books-module_description_iLF02 {
    display: block;
    margin-top: 12px;
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 50%;
    font-size: 12px;
    font-weight: 400
}

.Books-module_description_iLF02 :active,.Books-module_description_iLF02:hover,.Books-module_time_kYBV6 {
    color: var(--yq-text-caption)
}

.Books-module_time_kYBV6 {
    margin-left: 16px
}

.Books-module_book_wAfDM.larkui-card-transparent-head {
    background-color: var(--yq-bg-primary)
}

.Books-module_book_wAfDM.ant-card {
    transition: none
}

.Books-module_book_wAfDM.ant-card .ant-card-meta {
    margin-top: 0
}

.Books-module_book_wAfDM.ant-card .ant-card-meta-title {
    padding-right: 32px;
    margin-bottom: 20px;
    height: 54px
}

.Books-module_book_wAfDM.ant-card .book-name {
    line-height: 1.5;
    color: var(--yq-text-primary)
}

.Books-module_book_wAfDM.ant-card .name-lock {
    color: var(--yq-yuque-grey-7)
}

.Books-module_book_wAfDM.ant-card .ant-card-body {
    position: relative;
    padding: 18px;
    background: var(--yq-bg-primary);
    border-radius: 8px
}

html[data-kumuhana=pouli] .Books-module_book_wAfDM.ant-card .ant-card-body {
    background: var(--yq-bg-secondary);
    border: none
}

.Books-module_book_wAfDM.ant-card .ant-card-cover {
    margin-right: 0;
    margin-left: 0;
    transform: translateY(0);
    border: 1px solid var(--yq-border-primary);
    border-bottom: 0;
    border-radius: 8px 8px 0 0
}

.Books-module_book_wAfDM.ant-card .ant-card-meta-description {
    font-size: 12px;
    display: -webkit-box;
    max-height: 21px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 21px
}

.Books-module_book_wAfDM.ant-card .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.Books-module_alwaysShow_suY05,.Books-module_book_wAfDM:hover .Books-module_hoverShow_m6d7- {
    visibility: visible
}

.Books-module_coverStyleCard_e0cdn.ant-card .ant-card-body {
    padding: 16px;
    background: var(--yq-bg-primary);
    border-radius: 0 0 8px 8px;
    border-top: 0
}

.Books-module_edit_YZaVS {
    position: absolute;
    top: 16px;
    right: 16px;
    background-color: rgba(0,0,0,.5);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 6px;
    color: var(--yq-white);
    cursor: pointer
}

.Books-module_edit_YZaVS .larkicon {
    font-size: 12px
}

.Books-module_coverContainer_StMxh {
    display: flex;
    border-bottom: 1px solid var(--yq-border-primary);
    border-radius: 4px 4px 0 0
}

.Books-module_coverContainer_StMxh img {
    flex-grow: 1;
    width: 100%;
    height: 100%
}

.Books-module_counts_IaX\+P {
    margin-left: 34px;
    margin-top: 12px;
    color: var(--yq-text-caption);
    font-size: 12px
}

.Books-module_count_vUxY7 {
    display: inline-block;
    margin-right: 16px
}

.Books-module_books_0wdlm.Books-module_lite_ZcJtH .ant-card-body {
    padding: 24px
}

.Books-module_books_0wdlm.Books-module_lite_ZcJtH .Books-module_settingOptions_ibBTw {
    top: 20px
}

.Books-module_books_0wdlm.Books-module_summary_GuXTU .Books-module_description_iLF02 {
    margin-top: 4px
}

.Books-module_books_0wdlm.Books-module_summary_GuXTU .placeholder {
    height: 218px
}

.Books-module_books_0wdlm.Books-module_summary_GuXTU .ant-card-body {
    border-radius: 8px
}

.Books-module_books_0wdlm.Books-module_cover_tjoxr .Books-module_description_iLF02 {
    margin-top: 4px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF {
    margin: 0
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM {
    width: 100%;
    display: block;
    margin-top: 0
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM:last-child .ant-card-body {
    border-bottom: 1px solid var(--yq-yuque-grey-2)
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .Books-module_description_iLF02 {
    margin-top: 0;
    line-height: 28px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta-avatar {
    transform: translateY(4px)
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-body {
    border-width: 0 0 1px;
    border-radius: 0;
    padding: 18px;
    border: none;
    border-top: 1px solid var(--yq-yuque-grey-2);
    margin: 0
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta {
    height: 28px;
    display: flex
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta .ant-card-meta-detail {
    display: flex;
    width: 100%;
    justify-content: space-between
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta .ant-card-meta-detail .ant-card-meta-title {
    display: flex;
    min-width: 400px;
    font-size: 14px;
    font-weight: 400;
    overflow: hidden;
    flex: 1;
    max-width: calc(100% - 160px);
    height: 100%
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta .ant-card-meta-detail .ant-card-meta-title .book-link {
    width: 50%
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM .ant-card-meta .ant-card-meta-detail .ant-card-meta-description {
    margin-left: 12px;
    display: flex;
    justify-content: space-between;
    width: 160px;
    flex-shrink: 0
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM.ant-card {
    border: none;
    border-radius: 0;
    padding: 0;
    margin-bottom: 0;
    overflow: hidden
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM.ant-card .ant-card-body {
    padding: 18px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_book_wAfDM.ant-card:last-child {
    border: none;
    margin-bottom: 16px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .placeholder {
    width: 100%;
    height: 110px;
    margin-top: 0
}

.Books-module_books_0wdlm.Books-module_list_W-wMF:empty {
    border: none
}

.Books-module_books_0wdlm.Books-module_list_W-wMF .Books-module_extra_zIQX- {
    top: 14px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF.Books-module_empty_6lsoP {
    border: 2px dashed var(--yq-border-primary);
    border-radius: 6px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF.Books-module_empty_6lsoP .placeholder {
    height: 108px
}

.Books-module_books_0wdlm.Books-module_list_W-wMF.Books-module_empty_6lsoP .ant-card-body {
    padding: 0;
    border-top: none
}

.Books-module_books_0wdlm.Books-module_list_W-wMF.Books-module_empty_6lsoP:last-child .ant-card-body {
    border-bottom: none
}

.Books-module_books_0wdlm.Books-module_list_W-wMF.Books-module_empty_6lsoP a {
    height: 60px
}

.Books-module_newBookBtn_FVxFF.ant-card {
    border: none;
    width: 33%
}

.Books-module_newBookBtn_FVxFF.ant-card .ant-card-body {
    border-style: dashed;
    border-width: 1px;
    background: none;
    padding: 0;
    border-radius: 6px;
    border-color: var(--yq-border-primary)
}

.Books-module_newBookBtn_FVxFF.ant-card a {
    display: flex;
    width: 100%;
    height: 108px;
    color: var(--yq-text-caption);
    justify-content: center;
    align-items: center;
    flex-direction: column
}

.Books-module_newBookBtn_FVxFF.ant-card a:hover {
    color: var(--yq-text-body)
}

.Books-module_newBookBtn_FVxFF.ant-card .larkicon:before {
    margin-bottom: 8px
}

.Books-module_books_0wdlm.Books-module_summary_GuXTU .Books-module_newBookBtn_FVxFF a {
    height: 214px
}

.Books-module_books_0wdlm.droppable .Books-module_newBookBtn_FVxFF {
    display: none
}

.Books-module_books_0wdlm.droppable .Books-module_book_wAfDM.placeholder {
    background: var(--yq-bg-tertiary)
}

@media only screen and (max-width: 991px) {
    .Books-module_books_0wdlm,.Books-module_books_0wdlm.Books-module_list_W-wMF {
        margin-top:0;
        border: none;
        border-width: 1px 0
    }

    .Books-module_books_0wdlm.Books-module_list_W-wMF:empty,.Books-module_books_0wdlm:empty {
        border: none
    }

    .Books-module_book_wAfDM.ant-card .larkui-card-transparent-head {
        background-color: var(--yq-bg-tertiary)
    }

    .Books-module_book_wAfDM.ant-card .ant-card-body {
        border-width: 0 0 1px;
        border-radius: 6px;
        background: var(--yq-bg-secondary)
    }

    .Books-module_book_wAfDM.ant-card:last-child .ant-card-body {
        border-bottom: none
    }

    .Books-module_coverStyleCard_e0cdn.ant-card .ant-card-body {
        margin: 0
    }
}

@media only screen and (min-width: 768px) {
    .Books-module_books_0wdlm.Books-module_list_W-wMF .book-name {
        max-width:580px
    }
}

.Books-module_booksEmpty_XShYO {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption);
    flex-direction: column
}

.Books-module_booksEmpty_XShYO .Books-module_defaultPlaceholder_K4je- {
    width: 120px;
    margin-bottom: 13px
}

.Books-module_booksEmpty_XShYO p {
    color: var(--yq-text-caption)
}

.Books-module_countValue_EcgYz {
    color: var(--yq-text-body)
}

@font-face {
    font-family: larkicon;
    src: url(https://at.alicdn.com/t/font_227976_pllep6r382.eot);
    src: url(https://at.alicdn.com/t/font_227976_pllep6r382.eot#iefix) format("embedded-opentype"),url(https://at.alicdn.com/t/font_227976_pllep6r382.woff2) format("woff2"),url(https://at.alicdn.com/t/font_227976_pllep6r382.woff) format("woff"),url(https://at.alicdn.com/t/font_227976_pllep6r382.ttf) format("truetype"),url(https://at.alicdn.com/t/font_227976_pllep6r382.svg#iconfont) format("svg")
}

@font-face {
    font-family: Chinese Quote;
    src: local("PingFang SC"),local("SimSun");
    unicode-range: u+2018,u+2019,u+201c,u+201d
}

.BlockName-module_larkicon_qBdH0 {
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    line-height: 1;
    text-rendering: auto
}

.BlockName-module_larkicon_qBdH0:before {
    display: block;
    font-family: larkicon,sans-serif!important
}

.BlockName-module_larkicon-contents_a2Bmt:before {
    content: "\e952"
}

.BlockName-module_larkicon-zoom_UXRxm:before {
    content: "\e961"
}

.BlockName-module_larkicon-fullscreen_BQqef:before {
    content: "\e95e"
}

.BlockName-module_larkicon-insert_LSHV0:before {
    content: "\e976"
}

.BlockName-module_larkicon-mail_tmn9N:before {
    content: "\e950"
}

.BlockName-module_larkicon-publish_MhOzo:before {
    content: "\e963"
}

.BlockName-module_larkicon-earth_Cql7q:before {
    content: "\e96d"
}

.BlockName-module_larkicon-clock_Oc2Sg:before {
    content: "\e95b"
}

.BlockName-module_larkicon-arrowsalt_SikhG:before {
    content: "\e95f"
}

.BlockName-module_larkicon-upload_RpTst:before {
    content: "\e964"
}

.BlockName-module_larkicon-lark_Dhf\+w:before {
    content: "\e974"
}

.BlockName-module_larkicon-app_G0PqY:before {
    content: "\e954"
}

.BlockName-module_larkicon-file_QeOzU:before {
    content: "\e957"
}

.BlockName-module_larkicon-diamond_vcjvA:before {
    content: "\e965"
}

.BlockName-module_larkicon-private_QLbUm:before {
    content: "\e962"
}

.BlockName-module_larkicon-group_PETa0:before {
    content: "\e968"
}

.BlockName-module_larkicon-download_f4jmS:before {
    content: "\e975"
}

.BlockName-module_larkicon-storehouse_9NfP9:before {
    content: "\e955"
}

.BlockName-module_larkicon-team_XKRbg:before {
    content: "\e956"
}

.BlockName-module_larkicon-enterprise_4QzL1:before {
    content: "\e966"
}

.BlockName-module_larkicon-document_SurRc:before {
    content: "\e987"
}

.BlockName-module_larkicon-setting_La1tV:before {
    content: "\e953"
}

.BlockName-module_larkicon-delete_v6KVC:before {
    content: "\e951"
}

.BlockName-module_larkicon-write_Me5hL:before {
    content: "\e963"
}

.BlockName-module_larkicon-save_p5m00:before {
    content: "\e6a1"
}

.BlockName-module_larkicon-checkbox_WBqii:before {
    content: "\e6a2"
}

.BlockName-module_larkicon-check-o_ZjIP2:before {
    content: "\e98d"
}

.BlockName-module_larkicon-error_WGsK4:before {
    content: "\e692"
}

.BlockName-module_larkicon-line_Qk1Cf:before {
    content: "\e93d"
}

.BlockName-module_larkicon-eye_6TQHJ:before {
    content: "\e973"
}

.BlockName-module_larkicon-lock_qsykP:before {
    content: "\ea3b"
}

.BlockName-module_larkicon-unlock_gwYTS:before {
    content: "\e96c"
}

.BlockName-module_larkicon-arrow-down_MC02T:before {
    content: "\e983"
}

.BlockName-module_larkicon-arrow-down-sm_rTpFM:before {
    content: "\e983";
    transform: scale(.86)
}

.BlockName-module_larkicon-arrow-up_IR-ax:before {
    content: "\e983";
    transform: rotate(180deg)
}

.BlockName-module_larkicon-arrow-right_I4h2D:before {
    content: "\e95c"
}

.BlockName-module_larkicon-arrow-left_jw46s:before {
    content: "\e95c";
    transform: rotate(180deg) translateY(1px)
}

.BlockName-module_larkicon-add_7\+Wc4:before {
    content: "\e627"
}

.BlockName-module_larkicon-draft_DNmvX:before {
    content: "\e972"
}

.BlockName-module_larkicon-search_OVid4:before {
    content: "\e97f"
}

.BlockName-module_larkicon-rank_I7pVD:before {
    content: "\e6a5"
}

.BlockName-module_larkicon-bold_8P5s3:before {
    content: "\e694"
}

.BlockName-module_larkicon-italic_0J3tA:before {
    content: "\e6a0"
}

.BlockName-module_larkicon-through_2pgn1:before {
    content: "\e69b"
}

.BlockName-module_larkicon-quote_7afNf:before {
    content: "\e69e"
}

.BlockName-module_larkicon-underline_KC5dv:before {
    content: "\e93f"
}

.BlockName-module_larkicon-next_3r2P5:before {
    content: "\e69c"
}

.BlockName-module_larkicon-back_BcFNw:before {
    content: "\e691"
}

.BlockName-module_larkicon-picture_QGg6s:before {
    content: "\e69a"
}

.BlockName-module_larkicon-attachment_0XWNK:before {
    content: "\e690"
}

.BlockName-module_larkicon-link_WMQrp:before {
    content: "\e697"
}

.BlockName-module_larkicon-code_ALNPA:before {
    content: "\e695"
}

.BlockName-module_larkicon-bars_tRTqD:before {
    content: "\e6a5"
}

.BlockName-module_larkicon-addcomment_UKRaw:before {
    content: "\e95d"
}

.BlockName-module_larkicon-comments_3wsy6:before {
    content: "\e98e"
}

.BlockName-module_larkicon-comments-o_YtZ0a:before {
    content: "\e98f"
}

.BlockName-module_larkicon-plus_\+KuXN:before {
    content: "\ea38"
}

.BlockName-module_larkicon-cross_8obAB:before {
    content: "\e984";
    transform: rotate(45deg)
}

.BlockName-module_larkicon-close_0oZcC:before {
    content: "\e982"
}

.BlockName-module_larkicon-notification_CQI5y:before {
    content: "\e9d8"
}

.BlockName-module_larkicon-user_LHmOm:before {
    content: "\e96e"
}

.BlockName-module_larkicon-more_w71cJ:before {
    content: "\e944"
}

.BlockName-module_larkicon-tips_ZdPLc:before {
    content: "\e971"
}

.BlockName-module_larkicon-list_5j1BP:before {
    content: "\e6a6"
}

.BlockName-module_larkicon-grid_OJ-dv:before {
    content: "\e97e"
}

.BlockName-module_larkicon-star_XRtoI:before {
    content: "\e970"
}

.BlockName-module_larkicon-complete_c51cQ:before {
    content: "\e96b"
}

.BlockName-module_larkicon-user-h_Sb6r4:before {
    content: "\e959"
}

.BlockName-module_larkicon-star-o_\+6qEW:before {
    content: "\e76b"
}

.BlockName-module_larkicon-like_RYujQ:before {
    content: "\e969"
}

.BlockName-module_larkicon-like-o_BGkQm:before {
    content: "\e96a"
}

.BlockName-module_larkicon-minus-o_Ea1We:before {
    content: "\e978"
}

.BlockName-module_larkicon-plus-o_aTbDN:before {
    content: "\e979"
}

.BlockName-module_larkicon-organization_lQgOn:before {
    content: "\e94e"
}

.BlockName-module_larkicon-location_kV1ny:before {
    content: "\e98a"
}

.BlockName-module_larkicon-job_WJab0:before {
    content: "\e619"
}

.BlockName-module_larkicon-plus-square_nFPTV:before {
    content: "\e988"
}

.BlockName-module_larkicon-minus-square_OpzBY:before {
    content: "\e977"
}

.BlockName-module_larkicon-top_kJSTL:before {
    content: "\e97a"
}

.BlockName-module_larkicon-successful_2BLRz:before {
    content: "\e97b"
}

.BlockName-module_larkicon-exclamation_0udHv:before {
    content: "\e960"
}

.BlockName-module_larkicon-exclamation-circle_zFozI:before {
    content: "\e62d"
}

.BlockName-module_larkicon-read_-Xko1:before {
    content: "\e98c"
}

.BlockName-module_larkicon-eye-2_4R910:before {
    content: "\e640"
}

.BlockName-module_larkicon-download-2_Dhm0u:before {
    content: "\e63e"
}

.BlockName-module_larkicon-triangle-up_1bklm:before {
    content: "\e642"
}

.BlockName-module_larkicon-triangle-up-sw_NNyMx:before {
    content: "\e642";
    transform: scale(.5)
}

.BlockName-module_larkicon-triangle-down_K0BAF:before {
    content: "\e641"
}

.BlockName-module_larkicon-triangle-down-sw_iHRID:before {
    content: "\e641";
    transform: scale(.5)
}

.BlockName-module_larkicon-triangle-right_MdzaN:before {
    content: "\e642";
    transform: rotate(90deg)
}

.BlockName-module_larkicon-triangle-right-sw_MBw29:before {
    content: "\e642";
    transform: rotate(90deg) scale(.5)
}

.BlockName-module_larkicon-cleanup_Yu80v:before {
    content: "\e6ca"
}

.BlockName-module_larkicon-topics_-SuV8:before {
    content: "\e997"
}

.BlockName-module_larkicon-topics-open_x5wW7:before {
    content: "\e999"
}

.BlockName-module_larkicon-topics-closed_MLRa-:before {
    content: "\e998"
}

.BlockName-module_larkicon-subscribe_NzuTH:before {
    content: "\e677"
}

.BlockName-module_larkicon-edit_uPUlm:before {
    content: "\e75c"
}

.BlockName-module_larkicon-file-add_wAExY:before {
    content: "\e798"
}

.BlockName-module_larkicon-zoom-x_i15O0:before {
    content: "\e9a9"
}

.BlockName-module_larkicon-zoom-y_vz5nr:before {
    content: "\e9a8"
}

.BlockName-module_larkicon-addcomments_nJ3vX:before {
    content: "\e9aa"
}

.BlockName-module_larkicon-hotmap_QM\+nb:before {
    content: "\e9a2"
}

.BlockName-module_larkicon-slice_Ra\+M6:before {
    content: "\e9bf"
}

.BlockName-module_larkicon-measure_Ieq6E:before {
    content: "\e9ca"
}

.BlockName-module_larkicon-hotspot_WZLdO:before {
    content: "\e9c2"
}

.BlockName-module_larkicon-areacomments_\+H\+ao:before {
    content: "\e9c5"
}

.BlockName-module_larkicon-more-h_mTWuT:before {
    content: "\e9c9"
}

.BlockName-module_larkicon-comment-reply_N19tR:before {
    content: "\e6c7"
}

.BlockName-module_larkicon-topic-sharp_KOVkZ:before {
    content: "\e9ab"
}

.BlockName-module_larkicon-edit-contents_WQqkY:before {
    content: "\e9ac"
}

.BlockName-module_larkicon-symbol_FUBUJ:before {
    content: "\e9ba"
}

.BlockName-module_larkicon-upload-file_51W6b:before {
    content: "\e6cf"
}

.BlockName-module_larkicon-editor-back_GtNco:before {
    content: "\e9c0"
}

.BlockName-module_larkicon-launch_ujB2U:before {
    content: "\e9cb"
}

.BlockName-module_larkicon-share_jDoUh:before {
    content: "\e768"
}

.BlockName-module_larkicon-sharing_DZNUD:before {
    content: "\e9cd"
}

.BlockName-module_larkicon-info_F7UUf:before {
    content: "\e629"
}

.BlockName-module_larkicon-logout_b7Zb6:before {
    content: "\e65a"
}

.BlockName-module_larkicon-sort_bi6wW:before {
    content: "\e9a8"
}

.BlockName-module_larkicon-public_k907k:before {
    content: "\e9cf"
}

.BlockName-module_larkicon-dashboard_ipPrf:before {
    content: "\e9d0"
}

.BlockName-module_larkicon-help_Mr0\+6:before {
    content: "\e63c"
}

.BlockName-module_larkicon-admin_E06aC:before {
    content: "\e9d2";
    transform: scale(.86)
}

.BlockName-module_larkicon-quit_SNIfd:before {
    content: "\e9d3";
    transform: scale(.86)
}

.BlockName-module_larkicon-filter_rxyFz:before {
    content: "\e6b6"
}

.BlockName-module_larkicon-toc_nmH0p:before {
    content: "\e6bb"
}

.BlockName-module_larkicon-tocclose_r8uKj:before {
    content: "\e6bc"
}

.BlockName-module_larkicon-space-public_J7Aka:before {
    content: "\e9d7"
}

.BlockName-module_larkicon-space-enterprise_2\+qDF:before {
    content: "\e9d5"
}

.BlockName-module_larkicon-theme-checked_-aun0:before {
    content: "\e998"
}

.BlockName-module_larkicon-topic-pinned_DFLMk:before {
    content: "\e63e";
    transform: rotate(180deg)
}

.BlockName-module_larkicon-back-to-list_lxqlz:before {
    content: "\ea21"
}

.BlockName-module_larkicon-back-to-team_NkIeU:before {
    content: "\ea23"
}

.BlockName-module_larkicon-topic-close_eP3Ce:before {
    content: "\ea06"
}

.BlockName-module_larkicon-topic-reopen_99oRO:before {
    content: "\ea07"
}

.BlockName-module_larkicon-board_jiF2g:before {
    content: "\ea0a"
}

.BlockName-module_larkicon-label_Iz9PO:before {
    content: "\ea0b"
}

.BlockName-module_larkicon-disdain_fBpby:before {
    content: "\ea0c"
}

.BlockName-module_larkicon-smile_hGdwA:before {
    content: "\ea0d"
}

.BlockName-module_larkicon-kitchen_jYscT:before {
    content: "\ea1a"
}

.BlockName-module_larkicon-shortcut-collection_MPKxA:before {
    content: "\e970";
    color: #ffc53d
}

.BlockName-module_larkicon-shortcut-heart_SdCGG:before {
    content: "\e6d0";
    color: #ff4d4f
}

.BlockName-module_larkicon-shortcut-subscribe_5qvTy:before {
    content: "\ea26";
    color: #2495ff
}

.BlockName-module_larkicon-shortcut-topics_qv3ib:before {
    content: "\ea27";
    color: #597ef7
}

.BlockName-module_larkicon-new-headlines_OGqvd:before {
    content: "\ea28"
}

.BlockName-module_larkicon-event-follow_ljcDZ:before {
    content: "\e76d";
    color: #3bd17c
}

.BlockName-module_larkicon-event-like_BOyLE:before {
    content: "\ea20";
    color: #ff5c5f
}

.BlockName-module_larkicon-event-wheat_aTVj4:before {
    content: "\ea22";
    color: #ffc53d
}

.BlockName-module_larkicon-event-watch_7Vu9G:before {
    content: "\ea1f";
    color: #2495ff
}

.BlockName-module_larkicon-move_TQ7Jn:before {
    content: "\ea70"
}

.BlockName-module_larkicon-copy_PfkCC:before {
    content: "\e9bc"
}

.BlockName-module_larkicon-copy-to_-nyMh:before {
    content: "\ea6f"
}

.BlockName-module_larkicon-swap_xsXV\+:before {
    content: "\e643"
}

.BlockName-module_larkicon-group-lock_kq7-J:before {
    content: "\e757"
}

.BlockName-module_larkicon-group-avatar_3nGzl:before {
    content: "\e758"
}

.BlockName-module_larkicon-bell_3PhPZ:before {
    content: "\e602"
}

.BlockName-module_larkicon-header-new_DWym8:before {
    content: "\e601"
}

.BlockName-module_larkicon-members_zHt4U:before {
    content: "\e7e7"
}

.BlockName-module_larkicon-book_UguDB:before {
    content: "\e789"
}

.BlockName-module_larkicon-share-earth_gPirf:before {
    content: "\e681"
}

.BlockName-module_larkicon-hotkeys_4TUm3:before {
    content: "\ea08"
}

.BlockName-module_larkicon-permission-lock_MNscz:before {
    content: "\ea3b"
}

.BlockName-module_larkicon-permission-lock-o_sg3hP:before {
    content: "\e784"
}

.BlockName-module_larkicon-permission-eye_Vk7x3:before {
    content: "\ea3a"
}

.BlockName-module_larkicon-permission-eye-o_ZHSs\+:before {
    content: "\e80c"
}

.BlockName-module_larkicon-archive_YFIkk:before {
    content: "\ea3c"
}

.BlockName-module_larkicon-group-member_vLgZ2:before {
    content: "\ea3d"
}

.BlockName-module_larkicon-menu_QQ11L:before {
    content: "\e755"
}

.BlockName-module_larkicon-fold_5qk2d:before {
    content: "\ea3e"
}

.BlockName-module_larkicon-docbook_IqjkU:before {
    content: "\ea16";
    color: #444f59
}

.BlockName-module_larkicon-artboard_UZlzc:before {
    content: "\ea15";
    color: #40a9ff
}

.BlockName-module_larkicon-sheetsbook_S3Qyf:before {
    content: "\ea14";
    color: #25b864
}

.BlockName-module_larkicon-doc_2-pMk:before {
    content: "\e957"
}

.BlockName-module_larkicon-sheet_WaALm:before {
    content: "\ea19"
}

.BlockName-module_larkicon-sheet-primary_vH7Qi:before {
    content: "\ea19";
    color: #25b864
}

.BlockName-module_larkicon-doc-app_DcQ3k:before {
    content: "\ea5d"
}

.BlockName-module_larkicon-sheet-app_ifZpb:before {
    content: "\ea19"
}

.BlockName-module_larkicon-list-view_gGClW:before {
    content: "\ea17"
}

.BlockName-module_larkicon-grid-view_GX9dX:before {
    content: "\ea18"
}

.BlockName-module_larkicon-department_-jhmH:before {
    content: "\e759"
}

.BlockName-module_larkicon-subordinate_mRNZO:before {
    content: "\e753"
}

.BlockName-module_larkicon-cursor_Sv-Vi:before {
    content: "\ea39"
}

.BlockName-module_larkicon-linkto_bxJx1:before {
    content: "\e7ee"
}

.BlockName-module_larkicon-bar-chart_x76ax:before {
    content: "\e7af"
}

.BlockName-module_larkicon-up_gsNuk:before {
    content: "\ea1c"
}

.BlockName-module_larkicon-down_5iKVa:before {
    content: "\ea1b"
}

.BlockName-module_larkicon-book-card_3L7g2:before {
    content: "\e731"
}

.BlockName-module_larkicon-book-summary_mIsgO:before {
    content: "\e730"
}

.BlockName-module_larkicon-book-list_yYbvb:before {
    content: "\e60e"
}

.BlockName-module_larkicon-book-pic_u\+81-:before {
    content: "\e606"
}

.BlockName-module_larkicon-doc-detail_J0A-6:before {
    content: "\e608"
}

.BlockName-module_larkicon-doc-basic_8p5q5:before {
    content: "\e60b"
}

.BlockName-module_larkicon-fire_yBfNN:before {
    content: "\e60a"
}

.BlockName-module_larkicon-vertical_ztnq4:before {
    content: "\e60d"
}

.BlockName-module_larkicon-horizontal_8QqWG:before {
    content: "\e60c"
}

.BlockName-module_edit-icon_ZLsro {
    position: relative;
    display: inline-block;
    font-style: normal;
    vertical-align: baseline;
    text-align: center;
    text-transform: none;
    text-rendering: auto;
    line-height: 1
}

.BlockName-module_edit-icon_ZLsro:before {
    display: block;
    font-family: larkicon,sans-serif!important
}

.BlockName-module_edit-icon-bold_Ep1yg:before {
    content: "\e694";
    transform: scale(.9)
}

.BlockName-module_edit-icon-link_YHIet:before {
    content: "\e697"
}

.BlockName-module_edit-icon-code_Dsguo:before {
    content: "\e695"
}

.BlockName-module_edit-icon-eyes_mFbXv:before {
    content: "\e973";
    transform: scale(.9)
}

.BlockName-module_edit-icon-eyes-slash_pnOfq:before {
    content: "\e973"
}

.BlockName-module_edit-icon-h1_UUuVo:before {
    content: "\e696"
}

.BlockName-module_edit-icon-h2_FuK32:before {
    content: "\e6a7"
}

.BlockName-module_edit-icon-h3_M32rF:before {
    content: "\e6a9"
}

.BlockName-module_edit-icon-image_vMpuO:before {
    content: "\e69a"
}

.BlockName-module_edit-icon-italic_C-wpi:before {
    content: "\e6a0";
    transform: scale(.9)
}

.BlockName-module_edit-icon-oList_RdHF0:before {
    content: "\e6a6"
}

.BlockName-module_edit-icon-uList_C6Oa9:before {
    content: "\e6a5"
}

.BlockName-module_edit-icon-tList_R2vue:before {
    content: "\e6a2"
}

.BlockName-module_edit-icon-redo_iLPch:before {
    content: "\e69c"
}

.BlockName-module_edit-icon-undo_Vu2J8:before {
    content: "\e691"
}

.BlockName-module_edit-icon-quote_-aJxe:before {
    content: "\e69e"
}

.BlockName-module_edit-icon-del_CXTWR:before {
    content: "\e69b";
    transform: scale(.9)
}

.BlockName-module_edit-icon-attachment_uKXj\+:before {
    content: "\e690"
}

.BlockName-module_edit-icon-hr_YszfV:before {
    content: "\e93d"
}

.BlockName-module_edit-icon-table_sS\+IK:before {
    content: "\e69f"
}

.BlockName-module_edit-icon-save_SK22K:before {
    content: "\e6a1"
}

.BlockName-module_edit-icon-pdf_O6k1z:before {
    content: "\e636"
}

.BlockName-module_edit-icon-video_cvxnD:before {
    content: "\e6a3"
}

.BlockName-module_edit-icon-emoji_6gldC:before {
    content: "\e6c9"
}

.BlockName-module_larkicon-svg-check_f4WMM {
    display: block;
    width: 11px;
    height: 9px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/OvLdkKwArTJKCMxndXbO.svg) no-repeat 50%
}

.BlockName-module_larkicon-svg-check-blue_jhLJj {
    display: block;
    width: 14px;
    height: 11px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/uvYlejSGEMoXjpQFmtxp.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-asset_QrZVc {
    display: block;
    width: 18px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/JZJTDvlUkhjQNMyTvQFR.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-group-lock_gNATI {
    display: block;
    width: 14px;
    height: 14px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LcFKqQaIWEfEGOsEWcyq.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-group-intranet_ChGCG {
    display: block;
    width: 14px;
    height: 14px;
    background: url(https://gw.alipayobjects.com/zos/basement_prod/2e32b49e-7d1c-44e0-8743-d487f15cccd3.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-toplines_A2UYS {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/BEvCYPkRACgoeUBwupav.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-toplines-o_jIhrJ {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/uRqNsoHnGyBMUfDTHymD.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-groups_4Loer {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/grDGDjMhcIBxndgBzbIc.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-groups-o_v68OA {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/EvBXjsQGWEyqHtvEEOmd.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-knowledges_AG-Wo {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/nIgSuCzFZrQVTaPKaPjo.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-explore-knowledges-o_lKhJw {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/RRepkaYrykMzalaySygw.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-file-doc_8BIZB {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/TscdKjyuTOHNAmegoizz.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-file-artboard_yoBK2 {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/dWBdhAhvftROoQPxwfGQ.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-file-sheet_Hjh-1 {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/DssHOIWjbBfhYeHLCKaq.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-doc_CfTJZ {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/EUSInMsrjBczCIkrMBnr.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-artboard_Sys4i {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/UhXAKkByeKQZHddCaQmW.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-sheet_auiNk {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/zDFkQSAWCQZaLZbVCxfy.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-doc-private_D2e3Q {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/XbyfwGZYHQtIfrytznIO.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-artboard-private_SuBdj {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/dpxMcNHUOVkijrOVVOHi.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-repo-sheet-private_IAunc {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/IUlzuhOXDbYDgvkHzDCR.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-group_S\+tdI {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/mdn/prod_resource/afts/img/A*_BDhR52Eec4AAAAAAAAAAABjAQAAAQ/original) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-koji_OKOrW {
    display: block;
    width: 42px;
    height: 13px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/CQQUpPramUPjPoPzGfzS.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-earth_Dc\+tb {
    display: block;
    width: 32px;
    height: 32px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/CZZPgYfmDpHnPajGsozC.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-earth-green_9YcBj {
    display: block;
    width: 32px;
    height: 32px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/MbUAkZfNTIbrbXEKgpTE.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-group-members_NrVrk {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LcQmqEXzBqREnMSbotFF.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-group-home_GXvx\+ {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/RerOeQKsNsKaTMnyoSEq.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-doc_bEeNW {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/LVRBJVaLOXQqgCzvgovy.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-sheet_jePa- {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/nUUWnJcaCTrIBCtEcFbk.svg) no-repeat 50%;
    background-size: 100%
}

.BlockName-module_larkicon-svg-artboard_pPI7Q {
    display: block;
    width: 24px;
    height: 24px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/SUUUinUjkyKgxPYJJnkL.svg) no-repeat 50%;
    background-size: 100%
}

@font-face {
    font-family: webfont;
    src: url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.eot);
    src: url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.eot#iefix) format("embedded-opentype"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.woff) format("woff"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.ttf) format("truetype"),url(https://at.alicdn.com/t/8fm6xy90ebs54s4i.svg#FZQKBYSJW--GB1-0) format("svg")
}

@media only screen and (min-width: 1600px) {
    .BlockName-module_lark_tYdJG .BlockName-module_main-typo_Vad9y {
        width:960px;
        padding: 90px 100px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-meta-item_VP2pX .BlockName-module_directory-affixed_qrHHy,.BlockName-module_lark_tYdJG .BlockName-module_main-meta_HLnCi {
        right: calc(50% - 720px)
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: none
    }
}

@media only screen and (min-width: 1201px) and (max-width:1279px) {
    .BlockName-module_lark_tYdJG .BlockName-module_doc-presenter_cQiFd {
        padding-left:0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-hide_4Lj1j {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: block
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-list_7Ii4F {
        width: 180px
    }
}

@media only screen and (min-width: 1024px) and (max-width:1200px) {
    .BlockName-module_lark_tYdJG .BlockName-module_header-container_MQXzS,.BlockName-module_lark_tYdJG .BlockName-module_main-cover_qFkeP,.BlockName-module_lark_tYdJG .BlockName-module_main-crumb-mid_r3LDC,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-group_jSmUs .BlockName-module_group_jRdT0 {
        width:1024px;
        padding-left: 24px;
        padding-right: 24px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_doc-presenter_cQiFd {
        padding-left: 0!important
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-main-wrapper_R5eX9 {
        padding-left: 0;
        padding-right: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group_2C3y9 {
        padding-left: 10px;
        padding-right: 10px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-list_7Ii4F {
        width: 180px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-hide_4Lj1j {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: block
    }
}

@media only screen and (min-width: 848px) and (max-width:1023px) {
    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-main-wrapper-middle_flb42 {
        width:100%;
        max-width: 800px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-list_7Ii4F {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-wrapper_JNyNS {
        margin-left: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-hide_4Lj1j {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: block
    }
}

@media only screen and (max-width: 847px) {
    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books_Q5-DZ,.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users_fWTq7 {
        width:100%;
        padding-left: 5%;
        padding-right: 5%
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books-item_jA4bc:nth-child(5n),.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users-item_cEtK2:nth-child(5n) {
        margin-right: 30px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_doc_fiCo5 {
        width: 100%;
        padding-top: 0;
        padding-left: 0;
        padding-right: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_typo-catalog-empty_PgTcp {
        width: 100%
    }

    .BlockName-module_lark_tYdJG .BlockName-module_header-nav-item_IDyHD {
        padding-left: 5px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_header-nav-item-text_MFcJz a {
        padding: 0 6px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-group_jSmUs .BlockName-module_group-action_mSQXK {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_editor-tool-wrapper-middle_tzoOR {
        width: auto;
        text-align: left;
        padding-left: 12px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon_tLtXp {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group_2C3y9 {
        padding-left: 0;
        padding-right: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group_2C3y9:after {
        border-right: 1px solid transparent
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-h1_lJA5L,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-h2_ITGVt,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-h3_Cvdq1,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-image_lAY8h,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-save_Wm\+eN,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool_zXufz .BlockName-module_editor-tool-icon-tList_ALunt {
        display: inline-block
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_editor-main-wrapper-middle_flb42,.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_editor-paper-wrapper_\+E5Xm {
        width: 100%
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-list_7Ii4F {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-wrapper_JNyNS {
        margin-left: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-main-wrapper_R5eX9 {
        padding: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-paper_fInIK {
        padding: 30px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-hide_4Lj1j {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: block
    }

    .BlockName-module_lark_tYdJG .BlockName-module_explore-body_wKcZb .BlockName-module_categories-item_36SqA {
        width: 100%
    }
}

@media only screen and (max-width: 768px) {
    .BlockName-module_lark_tYdJG .BlockName-module_ant-card_e0DmO {
        margin-bottom:10px
    }
}

@media only screen and (max-width: 420px) {
    .BlockName-module_lark_tYdJG .BlockName-module_header-nav-item-text_MFcJz {
        display:none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_header_o4CJ6 .BlockName-module_logo_KW1QN {
        margin-right: auto
    }

    .BlockName-module_lark_tYdJG .BlockName-module_header_o4CJ6 .BlockName-module_logo_KW1QN .BlockName-module_logo-link_IE2pr .BlockName-module_text_BLOMo {
        max-width: 92px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books_Q5-DZ,.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users_fWTq7 {
        padding-top: 32px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books-item_jA4bc,.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users-item_cEtK2 {
        float: none;
        margin: 0 auto 32px auto
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books_Q5-DZ .BlockName-module_link_oGv2c,.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users-item_cEtK2 .BlockName-module_link_oGv2c {
        margin: 0 auto
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_book-info_2NGGy {
        margin-bottom: 16px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_book-action_plFCv {
        left: 0;
        margin-top: -8px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_favor-wrapper_hd-ll>.BlockName-module_btn-group-info_smgYW {
        margin-left: 0;
        margin-right: 8px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_doc-create-entry_rx1E1 {
        float: right;
        margin-left: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_doc-create-entry_rx1E1 .BlockName-module_ant-dropdown-trigger_E8jmo,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_doc-create-entry_rx1E1 .BlockName-module_larkicon_qBdH0 {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-book_u6pZm .BlockName-module_book_odg8c .BlockName-module_doc-create-entry_rx1E1 .BlockName-module_ant-btn-primary_2ydsI {
        border-bottom-right-radius: 4px;
        border-top-right-radius: 4px
    }

    .BlockName-module_lark_tYdJG .BlockName-module_doc_fiCo5 {
        padding-left: 0;
        padding-right: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_books-item_jA4bc:nth-child(5n),.BlockName-module_lark_tYdJG .BlockName-module_main_O7zra .BlockName-module_users-item_cEtK2:nth-child(5n) {
        margin: 0 auto 32px auto
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-doc_bfG1N .BlockName-module_header-crumb_A7LPQ .BlockName-module_doc-title_7kyI0,.BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-list_7Ii4F,.BlockName-module_lark_tYdJG .BlockName-module_typo-catalog-detail_j2bcU li a .BlockName-module_slug_7ZsjV {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-wrapper_JNyNS {
        margin-left: 0
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-hide_4Lj1j {
        display: none
    }

    .BlockName-module_lark_tYdJG .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-tool-list-group-show_4oaIO {
        display: block
    }
}

@media only screen and (max-width: 992px) {
    .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-wrapper_JNyNS {
        margin-left:180px
    }

    .BlockName-module_main-wrapper-editor_BP6Cb .BlockName-module_editor-wrapper_JNyNS.BlockName-module_editor-wrapper-share_W3brB {
        margin-left: 0
    }
}

.BlockName-module_layout-container_6Xciv {
    max-width: 1056px;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    height: 100%
}

.BlockName-module_layout-container_6Xciv.BlockName-module_layout-container-full_pV4dh {
    max-width: 100%;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto
}

.BlockName-module_layout-container-wider_fNpvY {
    max-width: 1440px;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    height: 100%
}

.BlockName-module_layout-container-middle_wxFlM {
    max-width: 1216px;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    height: 100%
}

.BlockName-module_layout-container-main_FAtCV {
    padding-top: 24px;
    padding-bottom: 32px
}

.BlockName-module_lark_tYdJG {
    position: relative;
    background: var(--yq-bg-primary);
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column
}

.BlockName-module_main-wrapper_UD32e {
    flex: 1 0;
    width: 100%
}

.BlockName-module_main-container_TU8h2 {
    max-width: 1056px;
    padding-top: 22px
}

.BlockName-module_main-container-wider_vJmMX,.BlockName-module_main-container_TU8h2 {
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
    padding-bottom: 32px
}

.BlockName-module_main-container-wider_vJmMX {
    max-width: 1440px;
    padding-top: 24px
}

.BlockName-module_name_UcUL6 {
    font-weight: 500;
    font-size: 14px;
    min-width: 10px;
    color: var(--yq-text-primary);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BlockName-module_name_UcUL6 span {
    border-bottom: 1px solid transparent
}

.BlockName-module_name_UcUL6 .BlockName-module_editableSpan_1fgjy:hover {
    border-bottom: 1px solid var(--yq-theme)
}

.BlockName-module_smallSize_5KI6y {
    max-width: 154px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BlockName-module_nameInput_k1Fe\+ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px;
    outline-color: var(--yq-yuque-green-600)
}

.BlockName-module_warning_UJxaD {
    display: flex;
    align-items: center
}

.BlockName-module_warning_UJxaD .icon-svg {
    margin-right: 8px;
    color: var(--yq-function-warning)
}

.BlockName-module_title_\+alSc {
    max-width: 600px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

@media only screen and (max-width: 991px) {
    .BlockName-module_title_\+alSc {
        padding:0 16px
    }
}

@media only screen and (max-width: 480px) {
    .BlockName-module_title_\+alSc {
        padding-left:0;
        color: var(--yq-yuque-grey-8)
    }
}

.BookStack-module_stack_tkNcb {
    position: relative;
    margin-bottom: 4px
}

.BookStack-module_stack_tkNcb.BookStack-module_highlight_Kul9w:before {
    content: " ";
    display: block;
    width: calc(100% + 16px);
    height: 100%;
    padding: 8px;
    position: absolute;
    top: 8px;
    left: -8px;
    background: var(--yq-bg-tertiary)
}

.BookStack-module_header_Kp1v5 {
    display: flex;
    align-items: center;
    height: 40px;
    position: relative;
    margin-bottom: 8px;
    padding-top: 8px
}

.BookStack-module_name_2pDDR {
    font-weight: 500;
    font-size: 14px;
    max-width: 280px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--yq-text-body)
}

.BookStack-module_name_2pDDR span {
    border-bottom: 1px solid transparent
}

.BookStack-module_nameInput_O6v55 {
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px;
    outline-color: var(--yq-yuque-green-600)
}

.BookStack-module_bar_ldb0k {
    flex-grow: 1;
    background: var(--yq-border-primary);
    height: 1px
}

.BookStack-module_actions_L08nH {
    flex-grow: 1;
    display: none
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ {
    height: 32px;
    border-radius: 6px;
    border: 1px solid var(--yq-border-primary);
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--yq-bg-primary)
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ button {
    padding: 0
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ button:disabled {
    border: none;
    outline: none;
    margin-right: 4px;
    cursor: not-allowed;
    background: transparent
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ button:disabled .BookStack-module_actionIcon_UDob8 {
    color: var(--yq-yuque-grey-6)
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .larkicon {
    width: 16px;
    height: 16px;
    font-size: 16px
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .display-type {
    margin-top: 3px
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG {
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    cursor: pointer;
    margin-right: 4px;
    color: var(--yq-text-primary);
    background: none
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG:last-child {
    margin-right: 0
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG:focus {
    outline: none
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG:hover {
    background: var(--yq-bg-primary-hover)
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG .BookStack-module_actionIcon_UDob8 {
    margin-top: 4px;
    color: var(--yq-text-primary)
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG .BookStack-module_actionIconUp_bO5DG {
    margin-top: 3px;
    transform: rotate(180deg)
}

.BookStack-module_actions_L08nH .BookStack-module_operations_rxIpZ .BookStack-module_optBtn_H4EcG .BookStack-module_actionIconDown_CRV27 {
    margin-top: 2px
}

.BookStack-module_actions_L08nH>* {
    margin-left: 8px
}

.BookStack-module_empty_9DxI9 {
    padding: 16px;
    background: var(--yq-bg-primary);
    text-align: center;
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px
}

.BookStack-module_confirmDelete_Lkkh7 {
    max-width: 280px
}

.BookStack-module_overlay_lwM1C .ant-dropdown-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.BookStack-module_overlay_lwM1C .BookStack-module_icon_C\+XhS {
    margin-right: 8px;
    color: var(--yq-text-caption)
}

.BookStack-module_addNewBook_iQLbQ:hover {
    color: var(--yq-text-body)
}

@media only screen and (min-width: 768px) {
    .BookStack-module_actions_L08nH.active,.BookStack-module_header_Kp1v5:hover .BookStack-module_actions_L08nH {
        display:flex;
        align-items: center
    }

    .BookStack-module_editable_RB2OG .BookStack-module_name_2pDDR:hover span {
        border-bottom: 1px solid var(--yq-yuque-green-600)
    }
}

@media only screen and (max-width: 991px) {
    .BookStack-module_header_Kp1v5 {
        margin-top:8px;
        padding-top: 8px;
        margin-bottom: 8px;
        height: auto
    }
}

.BookStack-module_customToolTipDisabled_ooMfP {
    overflow: hidden
}

.BookStack-module_customToolTipDisabled_ooMfP button {
    background: var(--yq-background-base);
    pointer-events: none;
    width: 24px;
    height: 24px;
    border-radius: 6px
}

.BookStack-module_bookStyleConfigWrapper_RdUe3 .ant-popover-inner-content {
    padding: 0
}

.BookStack-module_bookStyleConfig_OJnSD {
    padding: 12px;
    width: 256px;
    font-size: 12px
}

.BookStack-module_bookStyleConfig_OJnSD h3 {
    font-weight: 500;
    color: var(--yq-text-primary)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT {
    margin: 14px 0 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ {
    width: calc(50% - 4px);
    height: 78px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    border: 1px solid var(--yq-sheetborder);
    border-radius: 8px;
    cursor: pointer;
    transition: all .2s
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutCard_R1ket .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*lc-rR7BNq9MAAAAAAAAAAAAADvuFAQ/original)
}

html[data-kumuhana=pouli] .BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutCard_R1ket .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*iE6UQoF4oFMAAAAAAAAAAAAADvuFAQ/original)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutCard_R1ket.BookStack-module_active_NXCKy .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*E8HwRLySF8gAAAAAAAAAAAAADvuFAQ/original)
}

html[data-kumuhana=pouli] .BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutCard_R1ket.BookStack-module_active_NXCKy .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*V7p8T7TaalUAAAAAAAAAAAAADvuFAQ/original)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutList_TQE-a .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*eFtETZ0QtkIAAAAAAAAAAAAADvuFAQ/original)
}

html[data-kumuhana=pouli] .BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutList_TQE-a .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*RqYRRYOqHhcAAAAAAAAAAAAADvuFAQ/original)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutList_TQE-a.BookStack-module_active_NXCKy .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*GzMvTZMWfQcAAAAAAAAAAAAADvuFAQ/original)
}

html[data-kumuhana=pouli] .BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_layoutList_TQE-a.BookStack-module_active_NXCKy .BookStack-module_layoutIcon_SzNN4 {
    background-image: url(https://mdn.alipayobjects.com/huamei_0prmtq/afts/img/A*XjimSaARFfEAAAAAAAAAAAAADvuFAQ/original)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ .BookStack-module_layoutIcon_SzNN4 {
    margin-bottom: 4px;
    width: 56px;
    height: 34px;
    background-size: 100% 100%
}

html[data-kumuhana=pouli] .BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ .BookStack-module_layoutIcon_SzNN4 {
    opacity: .8
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ span {
    font-size: 14px
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleLayout_\+qmPT .BookStack-module_layout_bwCOZ.BookStack-module_active_NXCKy {
    border-color: var(--yq-theme)
}

.BookStack-module_bookStyleConfig_OJnSD h4 {
    margin: 16px 0 8px 0;
    color: var(--yq-text-caption)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleDisplay_v0nSX {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleDisplay_v0nSX .BookStack-module_display_cDZyV {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(25% - 6px);
    height: 52px;
    border: 1px solid var(--yq-sheetborder);
    border-radius: 6px;
    flex-direction: column;
    cursor: pointer;
    transition: all .2s
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookStyleDisplay_v0nSX .BookStack-module_display_cDZyV.BookStack-module_active_NXCKy {
    border-color: var(--yq-theme)
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookExtraConfig_e7oBE {
    margin: 12px 0;
    display: flex;
    justify-content: space-between
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_bookExtraConfig_e7oBE .larkui-tooltip {
    line-height: 1
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_orderType_3we4s {
    display: flex;
    align-items: center;
    color: var(--yq-text-caption);
    cursor: pointer
}

.BookStack-module_bookStyleConfig_OJnSD .BookStack-module_orderType_3we4s.BookStack-module_disabled_hqw6P {
    color: var(--yq-text-disable);
    cursor: not-allowed
}

.BookStack-module_orderDropdown_Jan9d .ant-dropdown-menu {
    padding: 0
}

.BookStack-module_orderDropdown_Jan9d .ant-dropdown-menu-item {
    margin: 6px;
    border-radius: 6px
}

.BookStacksNav-module_navContainer_9yXU- {
    display: flex;
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    padding: 8px 16px;
    background-color: var(--yq-bg-primary);
    margin-top: 8px;
    overflow-x: auto
}

.BookStacksNav-module_navContainer_9yXU- .BookStacksNav-module_itemContainer_06WZ3 {
    display: flex;
    align-items: center;
    font-size: 12px
}

.BookStacksNav-module_navContainer_9yXU- .BookStacksNav-module_itemContainer_06WZ3:last-child .BookStacksNav-module_dot_XM0ju {
    display: none
}

.BookStacksNav-module_navContainer_9yXU- .BookStacksNav-module_itemContainer_06WZ3 .BookStacksNav-module_dot_XM0ju {
    display: block;
    background-color: var(--yq-bg-primary-hover-light);
    width: 4px;
    height: 4px;
    border-radius: 2px;
    margin-right: 16px
}

.BookStacksNav-module_item_GFrrp {
    font-size: 12px;
    cursor: pointer;
    float: 1;
    margin-right: 16px;
    max-width: 154px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.BookStacksNav-module_item_GFrrp:last-child {
    margin-right: 0
}

.BookStacks-module_booksEmpty_gy5b\+ {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 70vh;
    max-height: 100vh;
    color: var(--yq-text-caption)
}

.BookStacks-module_booksEmpty_gy5b\+ .BookStacks-module_defaultPlaceholder_JQlpA {
    width: 120px;
    margin-bottom: 13px
}

.index-module_searchModalWrapper_vtDAq .ant-modal-content {
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-modal-close {
    display: none
}

.index-module_searchModalWrapper_vtDAq .search-dropdown ul {
    padding-top: 0;
    box-shadow: none;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-top: 1px solid var(--yq-yuque-grey-3)
}

.index-module_searchModalWrapper_vtDAq .search-dropdown:before {
    display: none
}

.index-module_searchModalWrapper_vtDAq .ant-modal-body {
    padding: 0;
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 12px
}

.index-module_searchModalWrapper_vtDAq .ant-input-affix-wrapper {
    border: 0!important;
    box-shadow: none!important
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 {
    padding: 15px;
    font-size: 16px;
    line-height: 24px;
    box-shadow: none
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 input {
    color: var(--yq-text-primary);
    margin-right: 32px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 svg {
    font-size: 16px
}

.index-module_searchModalWrapper_vtDAq .index-module_searchInput_MvuM1 .ant-input {
    padding-left: 2px!important
}

.index-module_searchInput_MvuM1+div {
    position: relative!important
}

.index-module_header_QthL\+ {
    height: 32px;
    width: 100%;
    display: flex;
    justify-content: space-between
}

.index-module_headerLeft_Q9zG4 {
    display: flex
}

.index-module_headerLeft_Q9zG4 .index-module_headerTitle_X5-EP {
    color: var(--yq-black);
    font-weight: 500;
    font-size: 18px;
    line-height: 32px
}

.index-module_header_QthL\+ .index-module_headerSwitch_UUPln {
    display: flex;
    margin-left: 12px;
    margin-top: 3px
}

.index-module_header_QthL\+ .index-module_headerSwitch_UUPln .index-module_switchBtn_GBLyP {
    color: var(--yq-yuque-grey-7);
    cursor: pointer;
    width: 26px;
    height: 26px;
    border-radius: 6px
}

.index-module_header_QthL\+ .index-module_headerSwitch_UUPln .index-module_switchBtn_GBLyP:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_header_QthL\+ .index-module_headerSwitch_UUPln .index-module_switchBtn_GBLyP svg {
    position: relative;
    top: 3px;
    left: 5px
}

.index-module_header_QthL\+ .index-module_headerSwitch_UUPln .index-module_switchBtnSelected_pTVQh {
    color: var(--yq-yuque-grey-9);
    background-color: var(--yq-bg-primary-hover)
}

.index-module_header_QthL\+ .index-module_switchLine_KPyXg {
    border-right: 1px solid var(--yq-yuque-grey-4);
    height: 16px;
    position: relative;
    top: 6px;
    margin: 0 5px
}

.index-module_headerRight_\+9hBZ {
    display: flex
}

.index-module_headerRight_\+9hBZ.index-module_isH5_ROjHT {
    display: none
}

.index-module_headerRight_\+9hBZ .index-module_create_IdkWj {
    height: 32px;
    width: 64px;
    border-radius: 6px;
    display: flex;
    padding: 0 12px;
    justify-content: space-between;
    line-height: 32px
}

.index-module_headerRight_\+9hBZ .index-module_createBook_zexUk {
    width: 120px
}

.index-module_headerRight_\+9hBZ .index-module_createIcon_HKiKN {
    width: 16px;
    height: 16px;
    margin-top: 7px
}

.index-module_headerRight_\+9hBZ .index-module_createText_n3kYa {
    margin-left: 5px;
    font-size: 14px;
    line-height: 30px
}

.index-module_headerRight_\+9hBZ .index-module_overlay_FrHTW .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-secondary)
}

.index-module_headerRight_\+9hBZ .index-module_overlay_FrHTW .ant-dropdown-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_headerRight_\+9hBZ .index-module_overlay_FrHTW .index-module_icon_xtw4G {
    margin-right: 8px;
    color: var(--yq-text-caption)
}

.index-module_headerRight_\+9hBZ .index-module_headerSearch_XF1A2 {
    width: 160px
}

.index-module_headerRight_\+9hBZ .ant-btn {
    margin-left: 12px!important
}

.index-module_headerRightH5_0W9Jy {
    display: none
}

@media only screen and (max-width: 991px) {
    .index-module_header_QthL\+.index-module_invisible_YZi21 {
        display:inline-flex;
        margin-left: 16px
    }

    .index-module_headerRight_\+9hBZ {
        display: none
    }

    .index-module_headerRightH5_0W9Jy {
        display: block
    }

    .index-module_headerTitle_X5-EP {
        margin-top: 12px;
        font-size: 14px!important
    }
}

.index-module_isDarkMode_A-6Bn .index-module_switchBtnBg_kzYPj {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_h5DropdownBtn_pdUr2 {
    margin-top: 16px
}

.Search-module_wrapper_Scz\+2 {
    display: flex;
    flex-flow: column;
    justify-content: center;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.Search-module_wrapper_Scz\+2 .Search-module_img_CAujI {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.index-module_ellipsis-text_uE36D {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.index-module_container_Xq-rh,.index-module_listContainer_66Z4j {
    position: relative;
    width: 100%;
    max-width: 1200px;
    min-width: 949px
}

.index-module_avatar_35CdA {
    position: absolute;
    left: 4px;
    top: 20px
}

.index-module_itemContainer_Fe5tc {
    position: relative;
    margin-left: 48px;
    width: 100%
}

.index-module_issueContainer_XAysk {
    align-items: center
}

.index-module_itemHeader_r7wAF {
    display: flex;
    align-items: center;
    gap: 6px
}

.index-module_headerContainer_Ig91a {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_cover_qgv7P {
    max-width: 220px;
    border-radius: 4px;
    border: 1px solid var(--yq-border-primary)
}

.index-module_docContainer_QQazt {
    display: flex;
    flex-direction: column
}

.index-module_docContent_05rd\+ {
    display: flex
}

.index-module_docLeft_-RZ8\+ {
    flex: 1
}

.index-module_docRight_uMfni {
    width: 162px;
    border-radius: 8px;
    height: 106px;
    overflow: hidden
}

.index-module_docCover_GAdUI {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    border: 1px solid var(--yq-border-primary);
    border-radius: 8px
}

.index-module_docTitle_co0hG {
    display: flex;
    align-items: center;
    flex: 1
}

.index-module_docDesc_d45Ff {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    display: -webkit-box
}

.index-module_docDescText_ehjco {
    flex: 1;
    -webkit-line-clamp: 2;
    white-space: break-spaces;
    -webkit-box-orient: vertical;
    word-break: break-all
}

.index-module_threadRightSmall_jF6Da {
    height: 50px!important;
    transform: translateY(-10px)
}

.index-module_threadRightSmall_jF6Da .index-module_threadCollaborators_t-ghc {
    transform: translateY(-26px)
}

.index-module_threadCollaborators_t-ghc {
    width: 162px;
    height: 106px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.index-module_threadCollaboratorsIcon_DTzOp {
    opacity: .5
}

.index-module_alertEmptyContainer_Sr7\+r {
    cursor: pointer
}

.index-module_alertEmptyContainer_Sr7\+r:hover {
    background-color: var(--yq-bg-secondary)
}

.index-module_alertEmpty_lDspG {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed var(--yq-yuque-grey-5)
}

.index-module_alertContainer_3BelF {
    min-height: 58px;
    border-radius: 8px;
    margin-bottom: 32px;
    padding-top: 0;
    width: 100%;
    position: relative
}

.index-module_alertContainer_3BelF .ne-doc-minor-editor .ne-simple-ui .ne-engine {
    min-height: 58px
}

.index-module_alertContainer_3BelF .ne-simple-ui.ne-editor {
    margin-top: 5px;
    flex-direction: column
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW {
    min-height: 280px
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW .index-module_lakeEditor_5cigT {
    position: relative;
    min-height: 280px;
    margin-bottom: 8px
}

.index-module_alertContainer_3BelF.index-module_alertEditing_gtNSW .ne-doc-minor-editor .ne-simple-ui .ne-engine {
    min-height: 280px
}

.index-module_alertContainer_3BelF .index-module_docletHistoryViewerBtn_kqCNK {
    position: absolute;
    bottom: 5px;
    right: 0;
    padding: 0
}

.index-module_alertEditAble_tXlfM:after {
    content: "";
    background-color: var(--yq-bg-secondary);
    width: calc(100% + 16px);
    height: 100%;
    border-radius: 8px;
    left: -8px;
    top: 0;
    visibility: hidden;
    position: absolute
}

.index-module_alertEditAble_tXlfM:hover .index-module_alertActions_-zAYM,.index-module_alertEditAble_tXlfM:hover:after {
    visibility: visible
}

.index-module_lakeEditor_5cigT {
    position: relative;
    min-height: 58px;
    margin-bottom: 8px
}

.index-module_lakeCreateGuide_IMfDM {
    position: absolute;
    top: 50px;
    padding: 0 20px
}

.index-module_showEditorBtn_MJMEj {
    cursor: pointer;
    border-radius: 6px;
    border: 1px dashed var(--yq-yuque-grey-6);
    height: 44px;
    width: 100%;
    font-style: 14px;
    color: var(--yq-yuque-grey-700);
    line-height: 22px;
    padding: 11px;
    text-align: center
}

.index-module_showEditorBtn_MJMEj .larkui-icon-add {
    margin-right: 8px
}

.index-module_lakeViewer_ajTsr {
    position: relative;
    border-radius: 8px
}

.index-module_hidden_7DOP3 {
    display: none
}

.index-module_alertActions_-zAYM {
    visibility: hidden;
    z-index: 2;
    height: 24px;
    position: absolute;
    top: -29px;
    right: -8px;
    border-radius: 12px;
    background-color: var(--yq-white);
    padding: 0 12px;
    display: flex;
    justify-content: space-between;
    border-top: 1px var(--yq-border-light) solid;
    border-bottom: 1px var(--yq-border-light) solid;
    border-right: 1px var(--yq-border-light) solid;
    border-left: 1px var(--yq-border-light) solid
}

.index-module_alertActions_-zAYM svg {
    fill: var(--yq-yuque-grey-700)
}

.index-module_alertActions_-zAYM button {
    line-height: 1;
    color: var(--yq-yuque-grey-700)
}

.index-module_alertPlaceholder_dgtKD {
    height: 58px;
    border-color: transparent
}

.index-module_alertHidden_rUwfY {
    visibility: hidden
}

.index-module_editorHidden_ka-XF {
    position: absolute;
    visibility: hidden
}

.index-module_emptyImg_Vaj3r {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.index-module_emptyImg_Vaj3r.index-module_showMargin_QM4JP {
    margin: 100px auto auto auto
}

.index-module_emptyImg_Vaj3r img {
    width: 130px
}

.index-module_titleSkeleton_H4gMM {
    width: 200px
}

.index-module_boardSkeleton_E5cho {
    height: 58px
}

.index-module_boardSkeleton_E5cho .ant-skeleton-title {
    height: 58px!important;
    width: 100%!important
}

.index-module_issueContent_cG6V0 {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.index-module_placeholder_tr85j {
    flex: 1
}

.index-module_issueFields_Bvp63 {
    display: flex;
    gap: 8px
}

.index-module_issueFieldsItem_Wi\+r\+ {
    border: 1px solid var(--yq-yuque-grey-2);
    padding: 1px 6px;
    border-radius: 4px;
    display: flex;
    white-space: pre
}

.index-module_issueNameContent_rg92j {
    flex: 1;
    width: 0;
    max-width: 616px;
    min-width: 0;
    display: flex;
    margin-right: 32px;
    align-items: center;
    overflow: hidden
}

.index-module_issueNameContent_rg92j span {
    flex: 1
}

.index-module_listItem_GBepM {
    padding-top: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--yq-yuque-grey-2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative
}

.index-module_listItem_GBepM:last-child,.index-module_noBorder_la5s1 {
    border-bottom-width: 0
}

.index-module_count30D_BSOrD {
    position: relative
}

.index-module_count30DList_EcML9 {
    display: flex;
    gap: 16px
}

.index-module_count30DCard_2vBi8 {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-primary);
    position: relative;
    overflow: hidden
}

.index-module_count30DImg_gKp0F {
    position: absolute;
    z-index: 0;
    right: 16px;
    bottom: 16px;
    width: 20%;
    max-width: 104px
}

.index-module_count30DIconContainer_weVXI {
    display: flex;
    align-items: center
}

.index-module_count30DIcon_qvpVH {
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center
}

.index-module_count30DtitleIcon_o5J4I {
    transform: translateY(2px)
}

.index-module_docsContent_SAECI {
    display: flex;
    justify-content: space-between
}

.index-module_docsTitle_T4R0q {
    display: block;
    width: 400px
}

.index-module_docsDocAction_wrXea {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-7)!important
}

.index-module_titleIcon_ZSDhk {
    transform: translateY(2px)
}

.index-module_popover_cIXI4 {
    max-width: 330px;
    min-width: 280px;
    display: flex
}

.index-module_membersBorder_WAeEb {
    transform: translate(-.5px,13.5px);
    position: absolute;
    bottom: 0;
    width: calc(100% + 1px);
    left: 0
}

.index-module_membersList_PdWUB {
    display: flex;
    width: 100%;
    gap: 32px;
    position: relative;
    z-index: 1
}

.index-module_membersUser_PNWbC,.index-module_membersUserAvatar_Y\+fmq {
    position: relative
}

.index-module_membersImg1_XxUqA,.index-module_membersUserName_bMXEE {
    position: absolute;
    left: 50%;
    transform: translateX(-50%)
}

.index-module_membersImg1_XxUqA {
    bottom: 0;
    height: 100%;
    z-index: 0
}

html[data-kumuhana=pouli] .index-module_membersImg1_XxUqA {
    opacity: .1
}

.index-module_membersImg2_OrF0P {
    width: 62px;
    position: absolute;
    top: 6px;
    right: 8px;
    z-index: 0
}

.index-module_system_40Zuc {
    width: 100%;
    display: flex;
    gap: 17px
}

.index-module_system_40Zuc a:hover {
    color: var(--yq-text-link)
}

.index-module_system_40Zuc .index-module_count30DCard_2vBi8 {
    flex: 1;
    width: 0
}

.index-module_width100_Uc8N0 {
    width: 100%
}

.index-module_divider_enpVz {
    border-color: var(--yq-yuque-grey-2);
    color: var(--yq-yuque-grey-7)!important
}

.index-module_hotIcon_0r\+fa path {
    fill: var(--yq-yuque-grey-7)
}

.index-module_boardIcon_\+v94I,.index-module_sheetIcon_9ij0U,.index-module_tableIcon_8f30d,.index-module_threadIcon_7xy9b {
    transform: translateY(2px)
}

.index-module_gridTitle_TMQ0E {
    font-weight: 600
}

.index-module_filterContainer_VJ\+Ox {
    min-width: 103px
}

.index-module_filterIsActive_5IZFW {
    background-color: var(--yq-yuque-grey-3)
}

.index-module_filterItem_bqxF2 {
    display: flex;
    justify-content: center;
    position: relative
}

.index-module_filterItemIcon_TOBA8 {
    position: absolute;
    top: 7px;
    left: 13px
}

.index-module_descriptionMoreThanTwoLine_EaDyE {
    transform: translateY(-20px)
}

@media only screen and (max-width: 768px) {
    .index-module_container_Xq-rh {
        padding:12px 12px 0 12px;
        max-width: 100vw;
        min-width: 100vw
    }

    .index-module_listContainer_66Z4j {
        max-width: calc(100vw - 24px);
        min-width: calc(100vw - 24px)
    }

    .index-module_issueNameContent_rg92j {
        width: 100%
    }

    .index-module_issueFields_Bvp63 {
        width: 100%;
        overflow: hidden;
        margin-top: 8px
    }

    .index-module_threadCollaborators_t-ghc {
        width: 83px;
        height: 55px
    }

    .index-module_docRight_uMfni {
        width: 83px;
        height: 55px;
        margin-right: 24px
    }

    .index-module_docLeft_-RZ8\+ {
        flex: 1
    }

    .index-module_docDesc_d45Ff {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        width: calc(100% - 24px)
    }

    .index-module_gridTitle_TMQ0E {
        font-size: 14px;
        font-weight: 400;
        color: var(--yq-text-caption)
    }
}

.index-module_backTop_D0Kau {
    position: relative!important;
    right: inherit!important;
    bottom: inherit!important;
    width: inherit!important;
    height: inherit!important
}

@media screen and (max-width: 768px) {
    .index-module_system_40Zuc {
        flex-direction:column;
        width: calc(100vw - 60px)
    }

    .index-module_system_40Zuc .index-module_count30DCard_2vBi8 {
        width: calc(100% - 48px);
        flex: auto
    }

    .index-module_count30DImg_gKp0F {
        display: none
    }

    .index-module_itemContainer_Fe5tc {
        overflow: hidden
    }
}

.BookList-module_mytable_8Ktrz {
    margin-bottom: 48px
}

.BookList-module_mytable_8Ktrz .ant-card-head {
    padding: 0 16px
}

.BookList-module_mytable_8Ktrz .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.BookList-module_mytable_8Ktrz .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.BookList-module_mytable_8Ktrz .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BookList-module_mytable_8Ktrz .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.BookList-module_mytable_8Ktrz .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.BookList-module_mytable_8Ktrz .ant-card-body {
    position: relative;
    padding: 0
}

.BookList-module_mytable_8Ktrz .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.BookList-module_mytable_8Ktrz .ant-table a {
    color: var(--yq-text-body)
}

.BookList-module_mytable_8Ktrz .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.BookList-module_mytable_8Ktrz .ant-table-thead>tr:first-child>th:first-child,.BookList-module_mytable_8Ktrz .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.BookList-module_mytable_8Ktrz .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.BookList-module_mytable_8Ktrz .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.BookList-module_mytable_8Ktrz .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.BookList-module_mytable_8Ktrz .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.BookList-module_mytable_8Ktrz .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.BookList-module_mytable_8Ktrz .BookList-module_activeRow_J3ty7 {
    background: var(--yq-yuque-green-1)
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 a,.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .book-icon,.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .BookList-module_titleIcon_cVGZW,.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .doc-icon {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .doc-link {
    display: block
}

.BookList-module_mytable_8Ktrz .ant-table .BookList-module_columnsTitle_P5EN5 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsTitleLabel_MZN-P {
    margin-left: 12px
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsBelong_rvsP3 {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsDesc_sJnz5 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsDesc_sJnz5.BookList-module_mini_x\+OxF {
    max-width: 200px
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsTime_4w9Hu {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsAction_CFJm3 {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsAction_CFJm3.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.BookList-module_mytable_8Ktrz .BookList-module_columnsAction_CFJm3 .ant-btn {
    border-radius: 4px
}

.BookList-module_mytable_8Ktrz .BookList-module_emptyView_zZrcU {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.BookList-module_mytable_8Ktrz .BookList-module_emptyView_zZrcU .BookList-module_emptyViewImg_4a\+mg {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.BookList-module_mytable_8Ktrz .BookList-module_emptyView_zZrcU a {
    color: var(--yq-text-link)
}

.BookList-module_mytable_8Ktrz .BookList-module_emptyView_zZrcU a:hover {
    color: var(--yq-ant-link-hover-color)
}

.BookList-module_noPaddingTable_OAoJd .ant-card-head {
    padding: 0 16px
}

.BookList-module_noPaddingTable_OAoJd .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.BookList-module_noPaddingTable_OAoJd .ant-card-head .ant-card-head-title,.BookList-module_noPaddingTable_OAoJd .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.BookList-module_noPaddingTable_OAoJd .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.BookList-module_noPaddingTable_OAoJd .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.BookList-module_noPaddingTable_OAoJd .ant-card-body {
    position: relative;
    padding: 0
}

.BookList-module_noPaddingTable_OAoJd .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.BookList-module_noPaddingTable_OAoJd .ant-table a {
    color: var(--yq-text-caption)
}

.BookList-module_noPaddingTable_OAoJd .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.BookList-module_noPaddingTable_OAoJd .ant-table-thead>tr:first-child>th:first-child,.BookList-module_noPaddingTable_OAoJd .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.BookList-module_noPaddingTable_OAoJd .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.BookList-module_noPaddingTable_OAoJd .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.BookList-module_noPaddingTable_OAoJd .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.BookList-module_noPaddingTable_OAoJd .ant-table-footer {
    padding: 0;
    border-top: 0
}

.BookList-module_noPaddingTable_OAoJd .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.BookList-module_noPaddingTable_OAoJd .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_activeRow_J3ty7 {
    background: var(--yq-yuque-green-1)
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5.BookList-module_mini_x\+OxF {
    max-width: 300px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 a,.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 .book-icon,.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 .BookList-module_titleIcon_cVGZW,.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 .doc-icon {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitle_P5EN5 .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTitleLabel_MZN-P {
    margin-left: 12px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsBelong_rvsP3 {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsDesc_sJnz5 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsDesc_sJnz5.BookList-module_mini_x\+OxF {
    max-width: 200px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsTime_4w9Hu {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsAction_CFJm3 {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsAction_CFJm3 a {
    color: var(--yq-text-link);
    font-size: 14px
}

.BookList-module_noPaddingTable_OAoJd .BookList-module_columnsAction_CFJm3 .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .BookList-module_mytable_8Ktrz .BookList-module_columnsDesc_sJnz5,.BookList-module_mytable_8Ktrz .BookList-module_columnsTime_4w9Hu {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .BookList-module_mytable_8Ktrz .BookList-module_columnsBelong_rvsP3 {
        display:none
    }
}

.BookList-module_mytable_8Ktrz .ant-card {
    border: none
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.BookList-module_mytable_8Ktrz .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.BookList-module_mytable_8Ktrz .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.BookList-module_mytable_8Ktrz .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.BookList-module_mytable_8Ktrz .ant-table-tbody>tr.ant-table-row-selected>td,.BookList-module_mytable_8Ktrz td.ant-table-column-sort {
    background: none
}

.BookList-module_mytable_8Ktrz .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.BookList-module_mytable_8Ktrz .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.BookList-module_mytable_8Ktrz .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.BookList-module_mytable_8Ktrz .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .BookList-module_mytable_8Ktrz .ant-card-extra .ant-btn,.BookList-module_mytable_8Ktrz .ant-card-head-title {
        display:none
    }

    .BookList-module_mytable_8Ktrz .ant-card-extra {
        float: none;
        flex: auto
    }

    .BookList-module_mytable_8Ktrz .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.BookList-module_wrapper_j65iP {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .BookList-module_wrapper_j65iP {
        padding:0
    }
}

.BookList-module_bookList_1Sdzr {
    width: 100%
}

.BookList-module_bookList_1Sdzr .BookList-module_columnsTitleBook_KNuhy {
    display: flex;
    align-items: center;
    overflow: hidden
}

.BookList-module_bookList_1Sdzr .BookList-module_columnsTitleBook_KNuhy .book-name-text {
    max-width: 80%
}

.BookList-module_bookList_1Sdzr .BookList-module_columnsTitleBook_KNuhy .book-name-scope {
    margin-left: 0
}

.BookList-module_bookList_1Sdzr .BookList-module_columnsTitleBook_KNuhy .name-lock {
    color: var(--yq-yuque-grey-7)
}

.BookList-module_bookList_1Sdzr td.BookList-module_description_UZAGI {
    color: var(--yq-text-caption);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.BookList-module_bookList_1Sdzr .ant-table-row:hover .BookList-module_actionsIcon_sFa1r {
    opacity: 1
}

.BookList-module_columnsEditTitle_wxMgj {
    display: flex;
    align-items: center
}

.BookList-module_columnsTime_4w9Hu {
    width: 160px
}

@media only screen and (max-width: 1100px) {
    .BookList-module_columnsTime_4w9Hu {
        display:none
    }
}

.BookList-module_actions_-zdXO {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.BookList-module_btnPin_WZXvK {
    padding: 0 6px;
    height: 28px;
    width: 28px;
    line-height: 28px;
    border-radius: 6px
}

.BookList-module_btnPin_WZXvK:hover {
    background: var(--yq-yuque-grey-4)
}

.BookList-module_btnPin_WZXvK .BookList-module_icon_3t2l9 {
    color: var(--yq-yuque-grey-7)
}

.BookList-module_actionsIcon_sFa1r {
    opacity: 0;
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    border-radius: 6px;
    font-size: 16px;
    padding: 0 6px;
    cursor: pointer;
    margin-left: 10px;
    color: var(--yq-yuque-grey-9)
}

.BookList-module_actionsIcon_sFa1r:hover {
    color: var(--yq-yuque-grey-9);
    background: var(--yq-yuque-grey-4)
}

.BookList-module_actionsIconPopover_OIXUA {
    z-index: 1000
}

.BookList-module_actionsIconPopover_OIXUA .ant-popover-inner-content {
    padding: 0
}

.BookList-module_settingWrap_p5ZVm {
    width: 100%;
    display: inline-block
}

.BookList-module_skeletonRow_3m7Li {
    border-top: 1px solid var(--yq-border-light);
    padding: 24px 8px
}

.BookList-module_skeletonRow_3m7Li .ant-skeleton-content .ant-skeleton-title {
    margin-top: 0
}

.BookList-module_skeletonColName_Tl5\+N .ant-skeleton-title {
    max-width: 360px
}

.BookList-module_skeletonRowEven_G9Z9m .BookList-module_skeletonColName_Tl5\+N .ant-skeleton-title {
    width: 80%;
    max-width: 242px
}

.BookList-module_skeletonColBelong_eAYoF .ant-skeleton-title {
    max-width: 228px
}

.BookList-module_skeletonRowEven_G9Z9m .BookList-module_skeletonColBelong_eAYoF .ant-skeleton-title {
    width: 80%;
    max-width: 152px
}

.BookList-module_skeletonColTime_heH7- .ant-skeleton-title {
    max-width: 129px
}

.GroupBookList-module_mytable_MOKCH {
    margin-bottom: 48px
}

.GroupBookList-module_mytable_MOKCH .ant-card-head {
    padding: 0 16px
}

.GroupBookList-module_mytable_MOKCH .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_mytable_MOKCH .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.GroupBookList-module_mytable_MOKCH .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.GroupBookList-module_mytable_MOKCH .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.GroupBookList-module_mytable_MOKCH .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.GroupBookList-module_mytable_MOKCH .ant-card-body {
    position: relative;
    padding: 0
}

.GroupBookList-module_mytable_MOKCH .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.GroupBookList-module_mytable_MOKCH .ant-table a {
    color: var(--yq-text-body)
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead>tr:first-child>th:first-child,.GroupBookList-module_mytable_MOKCH .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.GroupBookList-module_mytable_MOKCH .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.GroupBookList-module_mytable_MOKCH .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.GroupBookList-module_mytable_MOKCH .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.GroupBookList-module_mytable_MOKCH .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_activeRow_RM-\+- {
    background: var(--yq-yuque-green-1)
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn a,.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .book-icon,.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .doc-icon,.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .GroupBookList-module_titleIcon_2F\+xr {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .doc-link {
    display: block
}

.GroupBookList-module_mytable_MOKCH .ant-table .GroupBookList-module_columnsTitle_wW2Nn .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsTitleLabel_cIWh9 {
    margin-left: 12px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsBelong_rFxOy {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsDesc_ohGs4 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsDesc_ohGs4.GroupBookList-module_mini_hyfsc {
    max-width: 200px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsTime_J\+j0I {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsAction_bOCWc {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsAction_bOCWc.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsAction_bOCWc .ant-btn {
    border-radius: 4px
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_emptyView_KGtyP {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_emptyView_KGtyP .GroupBookList-module_emptyViewImg_2jMYA {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_emptyView_KGtyP a {
    color: var(--yq-text-link)
}

.GroupBookList-module_mytable_MOKCH .GroupBookList-module_emptyView_KGtyP a:hover {
    color: var(--yq-ant-link-hover-color)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head {
    padding: 0 16px
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head .ant-card-head-title,.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card-body {
    position: relative;
    padding: 0
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table a {
    color: var(--yq-text-caption)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-thead>tr:first-child>th:first-child,.GroupBookList-module_noPaddingTable_JSl6J .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-footer {
    padding: 0;
    border-top: 0
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.GroupBookList-module_noPaddingTable_JSl6J .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_activeRow_RM-\+- {
    background: var(--yq-yuque-green-1)
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn.GroupBookList-module_mini_hyfsc {
    max-width: 300px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn a,.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn .book-icon,.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn .doc-icon,.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn .GroupBookList-module_titleIcon_2F\+xr {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitle_wW2Nn .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTitleLabel_cIWh9 {
    margin-left: 12px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsBelong_rFxOy {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsDesc_ohGs4 {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsDesc_ohGs4.GroupBookList-module_mini_hyfsc {
    max-width: 200px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsTime_J\+j0I {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsAction_bOCWc {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsAction_bOCWc a {
    color: var(--yq-text-link);
    font-size: 14px
}

.GroupBookList-module_noPaddingTable_JSl6J .GroupBookList-module_columnsAction_bOCWc .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsDesc_ohGs4,.GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsTime_J\+j0I {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .GroupBookList-module_mytable_MOKCH .GroupBookList-module_columnsBelong_rFxOy {
        display:none
    }
}

.GroupBookList-module_mytable_MOKCH .ant-card {
    border: none
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.GroupBookList-module_mytable_MOKCH .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.GroupBookList-module_mytable_MOKCH .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.GroupBookList-module_mytable_MOKCH .ant-table-tbody>tr.ant-table-row-selected>td,.GroupBookList-module_mytable_MOKCH td.ant-table-column-sort {
    background: none
}

.GroupBookList-module_mytable_MOKCH .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.GroupBookList-module_mytable_MOKCH .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.GroupBookList-module_mytable_MOKCH .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .GroupBookList-module_mytable_MOKCH .ant-card-extra .ant-btn,.GroupBookList-module_mytable_MOKCH .ant-card-head-title {
        display:none
    }

    .GroupBookList-module_mytable_MOKCH .ant-card-extra {
        float: none;
        flex: auto
    }

    .GroupBookList-module_mytable_MOKCH .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.GroupBookList-module_wrapper_EWzOh {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .GroupBookList-module_wrapper_EWzOh {
        padding:0
    }
}

.GroupBookList-module_booksEmpty_pI5GY {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    max-height: 100vh
}

.GroupBookList-module_loadMore_AqF7F {
    cursor: pointer;
    padding: 16px
}

.GroupBookList-module_loadMore_AqF7F.GroupBookList-module_loading_9uXhT {
    text-align: center
}

.GroupBookList-module_btnPin_m3GRx {
    padding: 0 6px;
    height: 28px;
    width: 28px;
    line-height: 28px;
    border-radius: 6px
}

.GroupBookList-module_btnPin_m3GRx:hover {
    background: var(--yq-yuque-grey-4)
}

.GroupBookList-module_btnPin_m3GRx .GroupBookList-module_icon_-nWYg {
    color: var(--yq-yuque-grey-7)
}

.GroupBookList-module_bookList_LXHWG {
    width: 100%
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_emptyView_KGtyP {
    width: 100%;
    min-height: 70vh;
    max-height: 100vh;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    margin-top: -63px
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_emptyView_KGtyP .GroupBookList-module_defaultPlaceholder_HpbMS {
    width: 120px;
    margin-bottom: 13px
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_columnsTitleBook_G2vM- {
    display: flex;
    align-items: center;
    overflow: hidden
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_columnsTitleBook_G2vM- .book-name-text {
    max-width: 80%
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_columnsTitleBook_G2vM- .book-name-scope {
    margin-left: 0
}

.GroupBookList-module_bookList_LXHWG .GroupBookList-module_columnsTitleBook_G2vM- .name-lock {
    color: var(--yq-yuque-grey-7)
}

.GroupBookList-module_bookList_LXHWG td.GroupBookList-module_description_FI5O9 {
    color: var(--yq-text-caption);
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.GroupBookList-module_bookList_LXHWG .ant-table-row:hover .GroupBookList-module_actionsIcon_bQLlA {
    opacity: 1
}

.GroupBookList-module_bookList_LXHWG .ant-table-tbody>tr.ant-table-placeholder:hover>td {
    background: var(--yq-white)!important
}

.GroupBookList-module_actions_8wmBk {
    display: flex;
    align-items: center;
    justify-content: flex-end
}

.GroupBookList-module_actionsIcon_bQLlA {
    opacity: 0;
    display: inline-block;
    width: 28px;
    height: 28px;
    line-height: 28px;
    border-radius: 6px;
    font-size: 16px;
    padding: 0 6px;
    cursor: pointer;
    margin-left: 10px;
    color: var(--yq-yuque-grey-9)
}

.GroupBookList-module_actionsIcon_bQLlA:hover {
    color: var(--yq-yuque-grey-9)
}

.GroupBookList-module_actionsIconPopover_iLfgV {
    z-index: 1000
}

.GroupBookList-module_actionsIconPopover_iLfgV .ant-popover-inner-content {
    padding: 0
}

.WikiBookStacks-module_mytable_x1L-C {
    margin-bottom: 48px
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head {
    padding: 0 16px
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card-body {
    position: relative;
    padding: 0
}

.WikiBookStacks-module_mytable_x1L-C .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table a {
    color: var(--yq-text-body)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead>tr:first-child>th:first-child,.WikiBookStacks-module_mytable_x1L-C .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_activeRow_8o\+w7 {
    background: var(--yq-yuque-green-1)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS a,.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .book-icon,.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .doc-icon,.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .WikiBookStacks-module_titleIcon_0gGxJ {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .doc-link {
    display: block
}

.WikiBookStacks-module_mytable_x1L-C .ant-table .WikiBookStacks-module_columnsTitle_5TzxS .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsTitleLabel_4vSHe {
    margin-left: 12px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsBelong_dc116 {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsDesc_27k2p {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsDesc_27k2p.WikiBookStacks-module_mini_bOl4P {
    max-width: 200px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsTime_vOmhj {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsAction_n6NPX {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsAction_n6NPX.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsAction_n6NPX .ant-btn {
    border-radius: 4px
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_emptyView_B94y7 {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_emptyView_B94y7 .WikiBookStacks-module_emptyViewImg_H9OvG {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_emptyView_B94y7 a {
    color: var(--yq-text-link)
}

.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_emptyView_B94y7 a:hover {
    color: var(--yq-ant-link-hover-color)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head {
    padding: 0 16px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head .ant-card-head-title,.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card-body {
    position: relative;
    padding: 0
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table a {
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-thead>tr:first-child>th:first-child,.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-footer {
    padding: 0;
    border-top: 0
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_activeRow_8o\+w7 {
    background: var(--yq-yuque-green-1)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS.WikiBookStacks-module_mini_bOl4P {
    max-width: 300px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS a,.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS .book-icon,.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS .doc-icon,.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS .WikiBookStacks-module_titleIcon_0gGxJ {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitle_5TzxS .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTitleLabel_4vSHe {
    margin-left: 12px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsBelong_dc116 {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsDesc_27k2p {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsDesc_27k2p.WikiBookStacks-module_mini_bOl4P {
    max-width: 200px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsTime_vOmhj {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsAction_n6NPX {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsAction_n6NPX a {
    color: var(--yq-text-link);
    font-size: 14px
}

.WikiBookStacks-module_noPaddingTable_aFVBj .WikiBookStacks-module_columnsAction_n6NPX .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsDesc_27k2p,.WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsTime_vOmhj {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .WikiBookStacks-module_mytable_x1L-C .WikiBookStacks-module_columnsBelong_dc116 {
        display:none
    }
}

.WikiBookStacks-module_mytable_x1L-C .ant-card {
    border: none
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-tbody>tr.ant-table-row-selected>td,.WikiBookStacks-module_mytable_x1L-C td.ant-table-column-sort {
    background: none
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.WikiBookStacks-module_mytable_x1L-C .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .WikiBookStacks-module_mytable_x1L-C .ant-card-extra .ant-btn,.WikiBookStacks-module_mytable_x1L-C .ant-card-head-title {
        display:none
    }

    .WikiBookStacks-module_mytable_x1L-C .ant-card-extra {
        float: none;
        flex: auto
    }

    .WikiBookStacks-module_mytable_x1L-C .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

.WikiBookStacks-module_wrapper_2K8qq {
    padding: 24px 36px
}

@media only screen and (max-width: 480px) {
    .WikiBookStacks-module_wrapper_2K8qq {
        padding:0
    }
}

.WikiBookStacks-module_container_DYv-p {
    position: relative;
    width: 100%
}

.WikiBookStacks-module_container_DYv-p .larkui-spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-70%)
}

.WikiBookStacks-module_container_DYv-p .ant-list-item-meta-title a:hover {
    color: var(--yq-text-body)
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh {
    display: flex;
    margin-top: 16px;
    flex-wrap: wrap
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .ant-table-row .ant-table-cell:first-child,.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .ant-table-thead .ant-table-cell:first-child {
    padding-left: 0
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh:first-child {
    margin-top: 0
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_placement_Xc1FM {
    flex: 2;
    margin-left: 16px;
    width: 100%
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_placement_Xc1FM:first-child {
    margin-left: 0
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_once_q5rOi {
    width: 32.23%
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_block_wLOkg {
    padding-top: 16px
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_block_wLOkg:first-child {
    padding-top: 0
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_layoutH5_1qxEW {
    display: flex;
    flex-direction: column
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_pageEmpty_S0kFj {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-height: 304px;
    color: var(--yq-text-disable)
}

.WikiBookStacks-module_container_DYv-p .WikiBookStacks-module_pageEmpty_S0kFj .WikiBookStacks-module_emptyTip_Bu2lK {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

.WikiBookStacks-module_content_boLoS.WikiBookStacks-module_invisible_zT4S0 {
    display: none
}

.WikiBookStacks-module_content_boLoS.WikiBookStacks-module_contentH5_6GVyk {
    padding: 0 16px
}

@media only screen and (max-width: 480px) {
    .WikiBookStacks-module_content_boLoS.WikiBookStacks-module_invisible_zT4S0 {
        display:none
    }

    .WikiBookStacks-module_content_boLoS.WikiBookStacks-module_bookList_KrkrK.WikiBookStacks-module_isWiki_tKSxl {
        padding: 0 16px
    }

    .WikiBookStacks-module_content_boLoS.WikiBookStacks-module_bookList_KrkrK.WikiBookStacks-module_isWiki_tKSxl.WikiBookStacks-module_invisible_zT4S0 {
        display: none
    }

    .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_placement_Xc1FM {
        margin-left: 0
    }

    .WikiBookStacks-module_layout_murBh .WikiBookStacks-module_block_wLOkg {
        padding-top: 0
    }
}

.index-module_tipContainer_lImj0 {
    margin: 16px;
    background-color: var(--yq-bg-secondary);
    border-radius: 6px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 213px
}

.index-module_innerTip_WS8uR {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    word-break: break-all;
    padding: 16px;
    color: var(--yq-text-caption);
    cursor: pointer
}

.index-module_innerTip_WS8uR:hover {
    color: var(--yq-text-body)
}

.index-module_tipBtn_ovSC9 {
    width: 16px;
    height: 16px;
    margin-bottom: 8px
}

.ant-carousel {
    flex: 1;
    max-width: 100%
}

.ant-carousel .slick-dots {
    position: absolute;
    z-index: 15;
    display: flex!important;
    justify-content: flex-end;
    margin-left: auto;
    padding-left: 0;
    list-style: none;
    left: auto;
    bottom: 24px;
    right: 24px;
    margin-right: 0
}

.ant-carousel .slick-dots li.slick-active {
    width: 6px;
    opacity: 1
}

.ant-carousel .slick-dots li.slick-active button {
    opacity: 1
}

.ant-carousel .slick-dots li {
    position: relative;
    display: inline-block;
    flex: 0 1 auto;
    box-sizing: content-box;
    width: 6px;
    height: 6px;
    margin: 0 2px;
    margin-right: 3px;
    margin-left: 3px;
    padding: 0;
    text-align: center;
    text-indent: -999px;
    vertical-align: top;
    transition: all .5s
}

.ant-carousel .slick-dots li button {
    display: block;
    width: 6px;
    height: 6px;
    padding: 0;
    color: transparent;
    font-size: 0;
    background: var(--yq-bg-primary);
    border: 0;
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    opacity: .3;
    transition: all .5s
}

.index-module_settingContainer_4K2Vn {
    display: flex;
    min-height: 395px
}

.index-module_settingContainer_4K2Vn .index-module_listContainer_eOsEd {
    max-height: 200px;
    overflow: auto
}

.index-module_settingContainer_4K2Vn .index-module_loadingContainer_t-xsp {
    width: 100%;
    text-align: center
}

.index-module_settingContainer_4K2Vn .ant-list-item {
    padding-bottom: 0;
    padding-top: 0
}

.index-module_settingContainer_4K2Vn .ant-list-item:hover {
    background-color: var(--yq-bg-tertiary)
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW {
    flex: 2;
    background-color: var(--yq-bg-secondary);
    padding: 16px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_titleRow_LgUYF {
    margin-bottom: 40px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_urlRow_G1eRU {
    margin-bottom: 8px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_errHint_aWaY- {
    color: var(--yq-function-error);
    margin-bottom: 10px;
    height: 22px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_label_gTua4 {
    margin-bottom: 8px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_cropper_nBxaN {
    background: var(--yq-bg-primary-hover);
    width: 250px;
    height: 170px;
    min-width: 200px;
    min-height: 120px
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_previewer_eyJfk {
    float: right
}

.index-module_settingContainer_4K2Vn .index-module_detail_EDyDW .index-module_coverPreview_HOQy2 {
    width: 285px;
    height: 102px;
    background: var(--yq-bg-primary-hover);
    margin-bottom: 16px;
    background-size: cover;
    position: absolute;
    top: 0
}

.index-module_listItem_I8oQc {
    display: flex;
    padding: 16px;
    padding-left: 4px;
    align-items: center;
    justify-content: center;
    cursor: pointer
}

.index-module_listItem_I8oQc .index-module_title_cZ3H8 {
    flex: 1;
    margin-left: 4px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 225px
}

.index-module_listItem_I8oQc .index-module_icon_Ak\+W3,.index-module_listItem_I8oQc button {
    display: none
}

.index-module_listItem_I8oQc.index-module_select_ICeYk {
    padding-left: 2px;
    border: 0 solid var(--yq-yuque-green-600);
    border-left-width: 2px;
    background-color: var(--yq-bg-secondary)
}

.index-module_listItem_I8oQc.index-module_select_ICeYk button {
    cursor: pointer;
    outline: none;
    border: none;
    background-color: var(--yq-bg-secondary);
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px
}

.index-module_listItem_I8oQc.index-module_select_ICeYk button:hover {
    background-color: var(--yq-bg-primary-hover-light)
}

.index-module_listItem_I8oQc.index-module_select_ICeYk .index-module_icon_Ak\+W3 {
    display: block
}

.index-module_listItem_I8oQc.index-module_select_ICeYk .index-module_drag_HC29i {
    cursor: -webkit-grab;
    cursor: grab;
    display: flex;
    background-color: var(--yq-bg-secondary);
    height: 20px;
    width: 20px;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 2px
}

.index-module_listItem_I8oQc.index-module_select_ICeYk .index-module_drag_HC29i:hover {
    background-color: var(--yq-bg-primary-hover-light)
}

.index-module_drag_HC29i {
    height: 20px;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 2px
}

.index-module_drag_HC29i:hover {
    cursor: -webkit-grab;
    cursor: grab;
    background-color: var(--yq-bg-primary-hover-light)
}

.index-module_drag_HC29i:hover .index-module_icon_Ak\+W3 {
    display: block
}

.index-module_addBtn_UnQgn {
    border: 1px dashed var(--yq-border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-disable);
    margin: 12px 24px;
    border-radius: 6px;
    padding: 1px 0;
    height: 24px
}

.index-module_addBtn_UnQgn span {
    font-size: 20px;
    margin-right: 4px
}

.index-module_addBtn_UnQgn:hover {
    opacity: .5
}

.index-module_dropdown_pvyql {
    width: 300px;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px
}

.index-module_recommend_22-IL {
    position: relative;
    display: flex;
    align-items: center;
    padding-left: 16px;
    padding-right: 16px;
    height: 56px;
    flex: 1;
    cursor: pointer
}

.index-module_recommend_22-IL .index-module_linkIcon_A8tN5 {
    width: 40px;
    float: left
}

.index-module_recommend_22-IL .index-module_titleWrap_9Ap2Z {
    overflow: hidden
}

.index-module_recommend_22-IL .index-module_title_cZ3H8 {
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 1.6;
    max-width: 320px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_recommend_22-IL .index-module_title_cZ3H8:hover {
    color: var(--yq-text-primary)
}

.index-module_recommend_22-IL .index-module_meta_VLygg {
    display: flex;
    align-items: center;
    line-height: 1.6
}

.index-module_recommend_22-IL .index-module_belong_1G-sj {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 8px;
    max-width: 200px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_recommend_22-IL .index-module_belong_1G-sj a {
    color: var(--yq-text-caption)
}

.index-module_recommend_22-IL .index-module_belong_1G-sj a:hover {
    color: var(--yq-text-body)
}

.index-module_recommend_22-IL .index-module_belong_1G-sj .index-module_split_k1zzo {
    padding: 0 4px
}

.index-module_recommend_22-IL .index-module_action_iPFEe {
    width: 30px
}

.index-module_larkIcon_UwKCx {
    vertical-align: middle
}

@media only screen and (max-width: 575px) {
    .index-module_btn_KlM3h .ant-btn {
        margin-right:8px;
        margin-left: 0
    }

    .index-module_cropper_nBxaN {
        float: none;
        max-width: 100%
    }

    .index-module_previewer_eyJfk {
        float: none;
        margin-top: 16px
    }

    .index-module_coverPreview_HOQy2 {
        width: 100%
    }
}

.index-module_bannerContainer_dgnyR {
    position: relative
}

.index-module_bannerContainer_dgnyR .index-module_banner_vIwM2 {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    position: relative;
    border-radius: 3px;
    border: 1px solid var(--yq-border-primary)
}

.index-module_bannerContainer_dgnyR .index-module_banner_vIwM2 .index-module_bannerTitle_401gu {
    color: var(--yq-white);
    font-size: 18px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-image: linear-gradient(180deg,transparent,rgba(0,0,0,.6));
    height: 100px;
    padding: 24px;
    border-radius: 6px;
    font-weight: 600
}

.index-module_bannerContainer_dgnyR .index-module_banner_vIwM2 .index-module_bannerTitle_401gu span {
    word-break: break-all;
    margin-right: 72px;
    display: -webkit-box;
    max-height: 40px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 20px;
    position: absolute;
    bottom: 24px;
    left: 24px
}

.index-module_carousel_r716J {
    flex: 1;
    width: 100%;
    height: 100%
}

.index-module_wrapper_54OP9 {
    width: 100%;
    max-width: 100vw
}

.index-module_mobile_hq\+jw {
    width: 100vw
}

.index-module_edit_10gtb {
    position: absolute;
    top: 16px;
    right: 16px;
    background-color: rgba(0,0,0,.5);
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    border-radius: 6px;
    color: var(--yq-white);
    cursor: pointer
}

.index-module_content_gvmT1 {
    border-radius: 0
}

.BlockContainer-module_header_w\+Ahq {
    height: 32px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    margin-top: 8px
}

.BlockContainer-module_content_j11Q8 {
    display: flex;
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    background-color: var(--yq-bg-primary);
    font-size: 14px;
    color: var(--yq-text-disable);
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    min-height: 80px
}

.BlockContainer-module_content_j11Q8 .larkui-spin {
    position: relative;
    top: 0;
    left: 0;
    transform: translate(0)
}

.BlockContainer-module_content_j11Q8 .BlockContainer-module_loading_ZMt4n {
    flex: 1;
    background-color: var(--yq-bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    border-radius: 6px
}

.note-status-module_dev_g73YZ {
    background-color: var(--yq-yuque-grey-6);
    padding: 0 4px;
    position: absolute;
    right: 0;
    top: 0;
    opacity: .5;
    pointer-events: none
}

.note-status-module_publicInfo_8wLHo {
    background: var(--yq-bg-secondary);
    color: var(--yq-text-body);
    border: 1px solid var(--yq-yuque-grey-5);
    padding: 2px 4px;
    border-radius: 4px;
    margin-left: 8px;
    font-size: 12px;
    cursor: pointer
}

.note-status-module_text_ZLRRs {
    font-size: 12px;
    padding-left: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    color: var(--yq-text-body);
    background-color: var(--yq-bg-secondary);
    border-color: var(--yq-border-primary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border: 1px solid var(--yq-blue-1);
    color: var(--yq-blue-9)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    color: var(--yq-icon-secondary)
}

html[data-kumuhana=pouli] .TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-icon-primary)
}

.TagList-module_wrapper_wAu1a {
    padding: 12px 0 0 16px;
    position: relative;
    font-size: 0
}

.TagList-module_wrapper_wAu1a.TagList-module_hideMore_2Kx5n {
    height: 50px;
    overflow: hidden
}

.TagList-module_wrapper_wAu1a.TagList-module_expand_J-EtW {
    height: auto;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 0 0 4px 4px
}

.TagList-module_wrapper_wAu1a.TagList-module_expandForMini_G9K8m {
    height: 40px!important;
    width: auto;
    display: flex
}

.TagList-module_list_g-vtQ {
    margin-bottom: 0
}

.TagList-module_list_g-vtQ .TagList-module_line_pgNjs {
    margin-right: 8px;
    width: 1px;
    height: 16px;
    background-color: var(--yq-yuque-grey-4);
    display: inline-block;
    align-items: center;
    vertical-align: middle;
    margin-bottom: 8px
}

.TagList-module_list_g-vtQ .TagList-module_addTag_P9XTi {
    display: inline-block;
    height: 30px;
    margin-bottom: 8px;
    vertical-align: middle
}

.TagList-module_list_g-vtQ .TagList-module_rightSide_65\+zS {
    float: right;
    display: flex
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8 {
    vertical-align: middle;
    display: inline-flex;
    max-width: 100%;
    border-radius: 15px;
    margin-bottom: 8px;
    height: 30px;
    color: var(--yq-text-body);
    padding: 0 8px;
    background-color: var(--yq-bg-primary);
    margin-right: 8px;
    border: 1px solid var(--yq-border-primary);
    justify-content: center;
    align-items: center
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg {
    margin-right: 4px
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_noTag_XGuDy svg path {
    fill: currentColor
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8:hover {
    color: var(--yq-text-body)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_active_EPfJt {
    background: var(--yq-blue-1);
    border-color: var(--yq-blue-1);
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_item_bUgF8.TagList-module_canSelect_XVnT1 {
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ {
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    height: 30px;
    line-height: 30px
}

.TagList-module_list_g-vtQ .TagList-module_name_-HZCJ.TagList-module_highLight_Fx\+g3 {
    color: var(--yq-red-6)
}

.TagList-module_list_g-vtQ .TagList-module_count_JG6aO {
    margin-left: 4px;
    font-size: 14px;
    height: 30px;
    line-height: 28px
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 {
    width: 18px;
    position: relative;
    display: inline-block;
    left: 8px;
    margin-right: 4px;
    border-radius: 4px;
    text-align: center;
    cursor: pointer
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0:hover {
    color: var(--yq-text-link)
}

.TagList-module_list_g-vtQ .TagList-module_delete_NtLM0 svg {
    height: 12px;
    width: 12px
}

.TagList-module_listForMini_n1Fjy {
    display: flex;
    width: 415px;
    overflow-x: auto;
    overflow-y: hidden
}

.TagList-module_expandBtn_kA52P {
    padding: 0 12px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.TagList-module_expandBtn_kA52P.TagList-module_expanded_tVFvP .TagList-module_icon_zLTbc {
    transform: rotate(-90deg)
}

.TagList-module_expandBtn_kA52P .TagList-module_icon_zLTbc {
    transform: rotate(90deg);
    font-size: 12px;
    transition: transform .3s ease-in
}

html[data-kumuhana=pouli] .index-module_toolBarBtn_kG8Cd,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_viewBlank_ecM7l,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J,html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_noteItem_dVp5J .ant-card-body {
    background: var(--yq-bg-secondary)
}

html[data-kumuhana=pouli] .index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_tagPinned_--NI2 {
    color: var(--yq-theme)
}

.index-module_toolBarBtn_kG8Cd {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_descMoreIcon_wlBa3 {
    color: var(--yq-blue-4)
}

.index-module_wrapper_8ethE {
    min-height: calc(100vh - 178px)
}

.index-module_wrapper_8ethE .ne-editor-wrap-content {
    max-height: none!important
}

.index-module_wrapper_8ethE .ne-card-video [data-testid=controls] [data-testid=time] {
    display: none
}

.index-module_wrapper_8ethE .view-more-mask .yuque-doc-content:after {
    content: "";
    display: block;
    position: absolute;
    bottom: 7px;
    z-index: 1;
    height: 26px;
    width: 100%;
    background-image: linear-gradient(180deg,hsla(0,0%,100%,0),var(--yq-white))
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m {
    font-size: 14px;
    color: var(--yq-yuque-grey-7);
    display: flex;
    align-items: center;
    margin-bottom: 16px
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m:hover {
    cursor: pointer;
    background: transparent!important
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowUp_lRGKc {
    color: var(--yq-yuque-grey-7);
    margin-left: 8px;
    transform: rotate(0deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tab_zkK5m .index-module_arrowDown_3-EN3 {
    transform: rotate(-90deg);
    transition: transform .3s ease-in
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj {
    margin-top: 16px
}

.index-module_wrapper_8ethE .index-module_tabNote_8V7gj:hover {
    cursor: auto
}

.index-module_wrapper_8ethE .index-module_hidden_M8z9j {
    display: none
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT {
    width: 30px;
    height: 30px;
    position: absolute;
    border-radius: 0 8px 0 0;
    top: 0;
    right: 0;
    overflow: hidden
}

.index-module_wrapper_8ethE .index-module_tagPinnedContainer_hB8jT .index-module_tagPinned_--NI2 {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--yq-yuque-green-2)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 {
    padding: 0 64px;
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItem_dVp5J {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .index-module_noteItemExpand_PjrCj {
    padding-bottom: 64px
}

.index-module_wrapper_8ethE .index-module_note_geZ56.index-module_selected_aA0w- .note-list-toolbar,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_leftToolBar_JMyy5,.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_rightToolBar_e1gvN {
    opacity: 1
}

.index-module_wrapper_8ethE .index-module_note_geZ56:hover .index-module_noteItem_dVp5J {
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 2px 8px 0 rgba(0,0,0,.08),0 8px 16px 4px rgba(0,0,0,.04)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm {
    position: relative
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_loading_uVMCS {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    text-align: center;
    padding-top: 80px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .index-module_mask_-nOYf {
    position: absolute;
    left: 0;
    top: 0;
    background-color: hsla(0,0%,100%,.8);
    height: 100%;
    width: 100%
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list {
    margin-top: 4px;
    padding: 0;
    background: transparent
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_reading_dfUvx .note-tag-list li {
    background-color: var(--yq-bg-primary-hover);
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm .ant-card-body .yuque-doc-content {
    min-height: 30px;
    padding-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewBlank_ecM7l,.index-module_wrapper_8ethE .index-module_note_geZ56 .index-module_content_Yf3Cm.index-module_isTemplate_7o8Q- .index-module_viewMore_gsQbm {
    visibility: hidden
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J {
    padding: 20px 12px 8px;
    position: relative;
    box-shadow: 0 0 1px -1px rgba(0,0,0,.08),0 1px 2px 0 rgba(0,0,0,.04),0 2px 4px 1px rgba(0,0,0,.02);
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--yq-bg-primary);
    transition: box-shadow .2s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_searchMode_jG3fm {
    padding-left: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL {
    font-size: 12px;
    color: var(--yq-text-caption);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    min-height: 24px;
    justify-content: space-between;
    position: relative;
    line-height: 24px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-checked:after,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_meta_B\+ClL .ant-checkbox-inner {
    border-radius: 50%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_radio_2HGaM {
    margin-right: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm {
    border: none;
    min-height: 40px;
    position: relative;
    transition: box-shadow .5s ease-in-out
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .ant-card-body {
    padding: 8px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI {
    max-height: none!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content>div {
    max-height: none!important;
    overflow: auto!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_contentExpanded_lWgEI .yuque-doc-content:after {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ {
    max-height: none;
    overflow: visible;
    background-color: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor {
    min-height: 30px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-editor-wrap-content,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor .ne-engine {
    min-height: 30px!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-editor-body {
    background-color: var(--yq-bg-secondary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ne-ui-fullscreen .ne-engine {
    min-height: 100vh!important
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-editor-wrapper {
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_editing_nW8gJ .note-tag-add {
    margin-bottom: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm.index-module_hasTag_wtzsZ {
    max-height: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewBlank_ecM7l {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 16px;
    z-index: 9;
    background: var(--yq-bg-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 8px 0 0 12px;
    cursor: pointer;
    z-index: 9;
    font-size: 12px;
    color: var(--yq-text-body)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMore_gsQbm svg {
    position: relative;
    top: 2px;
    right: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .index-module_content_Yf3Cm .index-module_viewMoreShow_pN6H7 {
    display: block
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view div[data-card-type=block] .lake-card-toolbar,.index-module_wrapper_8ethE .index-module_noteItem_dVp5J .lake-engine-view span[data-card-type=inline] .lake-card-toolbar {
    display: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp {
    margin: 0 64px 12px;
    padding: 20px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_date_095hy {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-bottom: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 {
    display: flex;
    flex-wrap: wrap
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU {
    width: 80px;
    height: 80px;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_images_fAoGp .index-module_imagesContent_t0q35 .index-module_item_2FEiU img {
    width: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU {
    margin-bottom: 8px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU a {
    margin-top: 8px;
    color: var(--yq-text-primary);
    display: flex;
    justify-content: space-between;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_title_6RJyp {
    font-size: 16px;
    font-weight: 600;
    color: var(--yq-text-primary)
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_desc_WTwbK {
    color: var(--yq-text-caption);
    font-size: 14px;
    margin-top: 8px;
    white-space: nowrap;
    word-break: break-all;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b {
    color: var(--yq-text-body);
    font-size: 12px;
    margin-top: 14px;
    display: flex;
    align-items: center;
    min-height: 18px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b img {
    width: 12px;
    height: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_belong_Csy5b .index-module_text_bro5m {
    margin-left: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_left_s-MXM {
    width: calc(100% - 140px);
    padding: 11px 18px 11px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX {
    width: 116px;
    height: 76px;
    border-radius: 4px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_bookmark_-\+dy\+ .index-module_item_2FEiU .index-module_right_RJVuX img {
    height: 100%
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm {
    margin: 0 64px 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU {
    margin-bottom: 4px;
    overflow: hidden;
    border: none
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 10px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM {
    display: flex;
    align-items: center
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_name_fKT1c {
    color: var(--yq-text-primary);
    font-size: 14px;
    margin-left: 12px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_left_s-MXM .index-module_size_xQNXJ {
    color: var(--yq-text-disable);
    padding-left: 8px
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .index-module_file_e9W5F .index-module_right_RJVuX a {
    color: #3f4b63
}

.index-module_wrapper_8ethE .index-module_noteItem_dVp5J.index-module_attachment_H4LKm .index-module_item_2FEiU .ant-card-body {
    padding: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    text-align: center;
    padding-top: calc(50vh - 200px);
    overflow: hidden;
    font-size: 14px;
    color: var(--yq-text-caption);
    margin-top: 0
}

.index-module_wrapper_8ethE.index-module_empty_cBT2k img {
    margin-bottom: 8px;
    width: 120px
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W {
    cursor: pointer
}

.index-module_wrapper_8ethE .index-module_loadMoreBtn_t6j9W,.index-module_wrapper_8ethE .index-module_theEndTips_tq\+NY {
    text-align: center;
    padding: 16px 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_8ethE .index-module_blank_2HJdh {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 16px 0 16px;
    color: var(--yq-text-caption);
    text-align: center
}

.index-module_wrapper_8ethE.index-module_desktop_oahLD {
    min-height: calc(100vh - 96px)
}

.note-public-modal .ant-modal-body {
    padding: 0!important
}

.index-module_leftToolBar_JMyy5,.index-module_toolbarStyle_cNHWw {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out
}

.index-module_leftToolBar_JMyy5 {
    left: 0
}

.index-module_leftToolBar_JMyy5.index-module_batchProcess_BqhA4 {
    opacity: 1
}

.index-module_leftToolBar_JMyy5 .index-module_checkBox_mz\+g\+ {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    margin-left: 30px
}

.index-module_rightToolBar_e1gvN {
    position: absolute;
    top: 0;
    opacity: 0;
    height: 100%;
    z-index: 200;
    transition: opacity .5s ease-in-out;
    width: 64px;
    right: 0
}

.index-module_rightToolBar_e1gvN.index-module_batchProcess_BqhA4 {
    display: none
}

.index-module_rightToolBar_e1gvN .note-list-toolbar {
    opacity: 1
}

.index-module_rightToolBarCollapse_cPTfb {
    color: var(--yq-text-body);
    top: calc(100% - 70px)
}

.index-module_rightToolBarCollapse_cPTfb,.index-module_rightToolBarSave_vhDcz {
    width: 32px;
    height: 32px;
    position: -webkit-sticky;
    position: sticky;
    display: none;
    margin-left: 8px;
    border-radius: 8px;
    background: var(--yq-bg-primary);
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,.08),0 2px 6px 0 rgba(0,0,0,.04),0 4px 8px 1px rgba(0,0,0,.02);
    cursor: pointer
}

.index-module_rightToolBarSave_vhDcz {
    color: var(--yq-yuque-green-6);
    top: 0
}

.index-module_tagContainer_nVqVt {
    width: 1px;
    height: 1px;
    position: absolute;
    right: 0;
    top: 80px
}

.index-module_tagContainer_nVqVt .note-tag-popover .ant-popover-inner {
    margin-top: -170px
}

.search-module_wrapper_S445p {
    display: flex;
    align-items: center;
    position: relative
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl {
    width: 64px;
    opacity: 0
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_active_eYHT\+ {
    transition: width .15s ease;
    width: 200px;
    opacity: 1
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper {
    transition: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl .ant-input-affix-wrapper-focused {
    border-color: none;
    box-shadow: none
}

.search-module_wrapper_S445p .search-module_searchBar_x3Krl.search-module_isLiteMode_C4hZN {
    width: 100%
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h {
    font-size: 14px;
    text-align: right;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: -1px;
    padding: 6px 0 6px 4px;
    opacity: 0;
    pointer-events: none;
    min-width: 76px
}

.search-module_wrapper_S445p .search-module_searchMiniShow_xFcgm {
    transition: opacity .3s ease-in;
    opacity: 1;
    pointer-events: auto
}

.search-module_wrapper_S445p .search-module_searchMini_V\+K7h .larkui-icon-help-search {
    color: var(--yq-icon-primary)!important;
    font-size: 16px;
    transform: translateY(2px)
}

.sidebar-toolbar-module_wrapper_oE4lW {
    padding: 0;
    cursor: pointer;
    transition: opacity .5s ease-in-out
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--yq-text-link)
}

.sidebar-toolbar-module_wrapper_oE4lW .sidebar-toolbar-module_insertBtn_RY-1c svg {
    margin-right: 4px
}

.index-module_wrapper_kkqhF {
    overflow-x: hidden
}

.index-module_wrapper_kkqhF .note-list {
    height: calc(100vh - 320px);
    overflow-y: scroll;
    min-height: 0
}

.index-module_wrapper_kkqhF .note-list-item {
    padding: 0;
    margin-bottom: 12px;
    background: var(--yq-bg-secondary);
    border: 1px solid var(--yq-yuque-grey-1);
    border-radius: 4px
}

.index-module_wrapper_kkqhF .note-item-normal {
    background: var(--yq-bg-secondary)
}

.index-module_wrapper_kkqhF .note-item {
    box-shadow: none!important
}

.index-module_wrapper_kkqhF .note-item,.index-module_wrapper_kkqhF .view-blank {
    background: var(--yq-bg-secondary)!important
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 {
    height: 77px;
    overflow-y: hidden;
    margin-top: 8px;
    display: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4.index-module_show_oAR0z {
    display: block
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch {
    height: auto
}

.index-module_wrapper_kkqhF .index-module_tagContainerExpand_7mxch .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transform: rotate(-180deg)
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD {
    cursor: pointer;
    margin-top: 2px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.index-module_wrapper_kkqhF .index-module_tagContainer_TFFU4 .index-module_tagTipBar_g7usD .index-module_icon_nCjEV {
    transition: transform .3s ease-in;
    position: relative;
    left: 5px;
    top: 3px
}

.index-module_wrapper_kkqhF .index-module_notesList_xu6a8 {
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B {
    padding: 0;
    margin-top: 16px
}

.index-module_liteWrapper_fYo2B .note-tag-list {
    background-color: transparent!important;
    padding: 0
}

.material-library-view {
    width: 100%
}

.material-library-view .ant-tabs {
    height: 100%
}

.material-library-view .ant-tabs .ant-tabs-tab {
    padding-top: 0
}

.material-library-view .ant-tabs .ant-tabs-ink-bar {
    background: var(--yq-yuque-grey-9)
}

.material-library-view .ant-tabs .ant-tabs-content-holder {
    display: flex;
    flex: 1
}

.styles-module_modal_8nT\+9 .ant-modal-body {
    padding: 0!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-btns {
    display: none!important
}

.styles-module_modal_8nT\+9 .ant-modal-confirm-content {
    margin-top: 0!important
}

.TemplateTreeItem-module_item_rGFbi {
    height: 38px
}

.TemplateTreeItem-module_title_15bPv {
    height: 100%;
    display: flex;
    align-items: center
}

.TemplateTreeItem-module_docIcon_494bD {
    margin-right: 12px
}

.ant-tree-treenode-selected .TemplateTreeItem-module_title_15bPv {
    font-weight: 700
}

.TemplateTreeItem-module_name_kpLbs {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateTreeItem-module_menuItem_FWIgC {
    display: flex;
    align-items: center
}

.TemplateTreeItem-module_more_ikuvF {
    white-space: nowrap;
    width: 24px;
    height: 24px;
    display: none;
    justify-content: center;
    align-items: center;
    border-radius: 4px
}

.TemplateTreeItem-module_more_ikuvF:hover {
    background-color: var(--yq-yuque-grey-5)
}

.ant-popover-open.TemplateTreeItem-module_more_ikuvF,.TemplateTreeItem-module_item_rGFbi:hover .TemplateTreeItem-module_more_ikuvF {
    display: flex
}

.ant-popover-open.TemplateTreeItem-module_more_ikuvF {
    background-color: var(--yq-yuque-grey-5)
}

.TemplateTreeItem-module_actionIcon_haD5C {
    display: inline-block!important;
    margin: 0 8px 0 0!important;
    padding: 0!important
}

.TemplateList-module_list_7Gkfr {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start
}

.TemplateList-module_item_SpAc7 {
    width: 215px;
    height: 67px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 8px;
    cursor: pointer;
    transform: translateY(0);
    transition: transform .25s cubic-bezier(.645,.045,.355,1);
    will-change: transform,opacity,margin-top;
    border: 1px solid var(--yq-border-light);
    border-radius: 4px;
    background-color: var(--yq-bg-primary);
    display: flex;
    align-items: center
}

.TemplateList-module_item_SpAc7:hover {
    transform: translateY(-6px);
    box-shadow: 0 4px 5px -5px rgba(0,0,0,.1),0 6px 7px 0 rgba(0,0,0,.06)
}

.TemplateList-module_tip_UaJ8v {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateList-module_name_ywSQW {
    color: var(--yq-text-body);
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.TemplateList-module_desc_hb3sC {
    font-size: 12px;
    color: var(--yq-text-disable);
    line-height: 17px;
    margin-top: 4px
}

.TemplateList-module_docIconWrapper_Gvaw8 {
    margin-right: 16px;
    line-height: 40px;
    width: 51px;
    height: 51px;
    border-radius: 4px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 1px 4px -2px rgba(0,0,0,.13),0 0 6px 2px rgba(0,0,0,.05);
    display: flex;
    justify-content: center;
    align-items: center
}

.TemplateList-module_moreImg_12JlL {
    margin-right: 16px;
    width: 80px;
    height: 67px;
    position: relative
}

.TemplateList-module_moreImg_12JlL img {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%
}

.RecentDocs-module_tip_n1Nmq {
    font-size: 15px;
    color: var(--yq-yuque-grey-5)
}

.RecentDocs-module_docItem_GCIP4 {
    width: 194px;
    padding: 10px 12px;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-light);
    border-radius: 4px;
    height: 42px;
    margin-top: 16px;
    cursor: pointer;
    transition: transform .25s cubic-bezier(.645,.045,.355,1),display .25s ease-in-out;
    display: flex;
    align-items: center
}

.RecentDocs-module_docItem_GCIP4:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 5px -5px rgba(0,0,0,.1),0 6px 7px 0 rgba(0,0,0,.06)
}

.RecentDocs-module_title_O3oxy {
    flex: 1;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.RecentDocs-module_icon_e5am\+ {
    margin-right: 8px
}

.EditorBase-module_init_eZbjJ {
    border: 1px solid var(--yq-border-primary);
    border-radius: 3px 3px;
    height: 222px;
    padding: 16px;
    font-size: 14px
}

.EditorBase-module_init_eZbjJ pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 8px 0;
    font-size: 12px
}

.EditorBase-module_editor_zrggr {
    position: relative;
    height: 100%;
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px
}

.ne-ui-fullscreen.EditorBase-module_editor_zrggr {
    border: none
}

.index-module_lakeCreateGuide_VutDm {
    position: absolute;
    top: 90px;
    padding: 0 20px;
    z-index: 1
}

.index-module_wrap_4GDfH {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body)
}

.index-module_wrap_4GDfH:hover .index-module_close_27uFj {
    display: flex
}

.index-module_icon_RfM4L {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: none
}

.index-module_close_27uFj {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_closeIcon_6sSTs {
    font-size: 16px
}

.ReaderAudit-module_closeBtn_hHU\+3 {
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    padding: 8px
}

.ReaderAudit-module_detail_mWCgN {
    margin-top: 8px;
    font-size: 12px;
    color: var(--yq-text-caption);
    padding: 12px;
    background-color: var(--yq-bg-tertiary);
    border-radius: 4px
}

.ReaderAudit-module_toggleDetail_pq7CA {
    display: block;
    color: var(--yq-text-body);
    font-size: 12px;
    margin-top: 8px
}

.ReaderAudit-module_toggleDetail_pq7CA:hover {
    color: var(--yq-text-primary)
}

.ReaderAudit-module_moreLink_oxcCo {
    font-size: 12px;
    display: inline-block;
    margin-left: 8px
}

.ReaderAudit-module_tagModeWrapper_xupeI {
    padding: 6px 8px;
    height: 24px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    position: relative;
    margin-left: 8px
}

.ReaderAudit-module_tagModeWrapper_xupeI .ReaderAudit-module_tagText_Womh9 {
    font-size: 12px
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_pending_m3C28 {
    background-color: var(--yq-yuque-grey-3)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_pending_m3C28 .ReaderAudit-module_tagText_Womh9 {
    color: var(--yq-yuque-grey-7)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_illegal_B8aJ7 {
    background-color: var(--yq-red-1)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_illegal_B8aJ7 .ReaderAudit-module_tagText_Womh9 {
    color: var(--yq-red-7)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_banned_sHuxy {
    background-color: var(--yq-red-1)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_banned_sHuxy .ReaderAudit-module_tagText_Womh9 {
    color: var(--yq-red-7)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_isIntranet_WPBjb {
    background-color: var(--yq-yellow-1)
}

.ReaderAudit-module_tagModeWrapper_xupeI.ReaderAudit-module_isIntranet_WPBjb .ReaderAudit-module_tagText_Womh9 {
    color: var(--yq-yellow-9)
}

.ReaderAudit-module_tagModeWrapper_xupeI .ReaderAudit-module_tagClose_YIxR7 {
    margin-left: 5px;
    width: 10px;
    height: 10px;
    display: none
}

.ReaderAudit-module_tagModeWrapper_xupeI:hover .ReaderAudit-module_tagClose_YIxR7 {
    display: flex
}

.index-module_container_B7usD {
    min-width: 450px;
    overscroll-behavior: contain
}

.index-module_container_B7usD .ant-modal-body {
    padding: 0
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 {
    position: relative;
    display: flex;
    max-height: 70vh;
    border-radius: 6px;
    border: 1px solid var(--yq-border-primary)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU {
    width: 200px;
    overflow: auto;
    overscroll-behavior: contain;
    flex-shrink: 0;
    border-right: 1px solid var(--yq-border-heavy)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU::-webkit-scrollbar-track {
    background-color: transparent
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU::-webkit-scrollbar-thumb {
    background-color: var(--yq-yuque-grey-4);
    border-radius: 6px;
    -webkit-transition: background-color .2s;
    transition: background-color .2s
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU::-webkit-scrollbar-thumb:hover {
    background-color: var(--yq-yuque-grey-5)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .ant-list-item {
    padding: 0;
    border: none
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .index-module_item_K9Srx {
    width: 100%;
    cursor: pointer;
    padding: 10px
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .index-module_item_K9Srx.index-module_active_VUsIa,.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .index-module_item_K9Srx:hover {
    background-color: var(--yq-bg-primary-hover)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .index-module_item_K9Srx .index-module_time_Zizyw {
    color: var(--yq-text-primary);
    font-weight: 500
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_list_ga1IU .index-module_item_K9Srx .index-module_name_3YWlA {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ {
    padding-bottom: 60px;
    flex: 1;
    overflow-x: auto;
    overscroll-behavior: contain
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ::-webkit-scrollbar-track {
    background-color: transparent
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ::-webkit-scrollbar-thumb {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 6px;
    -webkit-transition: background-color .2s;
    transition: background-color .2s
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ::-webkit-scrollbar-thumb:hover {
    background-color: var(--yq-yuque-grey-6)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editorContent_3pGpc {
    padding: 20px;
    height: 100%;
    border-bottom: 1px solid var(--yq-border-heavy);
    background-color: var(--yq-bg-foreground);
    overflow: auto;
    overscroll-behavior: contain
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editorContent_3pGpc::-webkit-scrollbar {
    width: 6px;
    height: 6px
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editorContent_3pGpc::-webkit-scrollbar-track {
    background-color: transparent
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editorContent_3pGpc::-webkit-scrollbar-thumb {
    background-color: var(--yq-yuque-grey-5);
    border-radius: 6px;
    -webkit-transition: background-color .2s;
    transition: background-color .2s
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editorContent_3pGpc::-webkit-scrollbar-thumb:hover {
    background-color: var(--yq-yuque-grey-6)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editor_uy0Bi {
    position: relative;
    min-width: 300px;
    height: 100%
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_editorContainer_ccRjJ .index-module_editor_uy0Bi .index-module_spin_JlGzR {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%)
}

.index-module_container_B7usD .index-module_modalBody_IEcG0 .index-module_submitBtn_4cR4m {
    position: absolute;
    bottom: 15px;
    right: 20px
}

.Custom-module_title_QV87C {
    display: flex;
    align-items: center;
    height: 40px;
    position: relative;
    z-index: 1
}

.Custom-module_viewerContent_OdqeD {
    align-items: inherit
}

.Custom-module_content_tXt6m {
    background-color: var(--yq-bg-primary);
    flex: 1;
    border: 0
}

.Custom-module_content_tXt6m .Custom-module_actions_MqK5Q {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 7px;
    background-color: var(--yq-bg-primary)
}

.Custom-module_content_tXt6m .Custom-module_cancel_Dtvga {
    color: var(--yq-text-body);
    margin-left: 8px
}

.Custom-module_content_tXt6m .Custom-module_editContainer_UWcpO {
    position: relative;
    flex: 1;
    margin-top: 20px
}

.Custom-module_content_tXt6m .Custom-module_editContainer_UWcpO .ne-simple-ui.ne-editor {
    margin-top: 5px;
    flex-direction: column
}

.Custom-module_content_tXt6m .Custom-module_editContainer_UWcpO .card-resize-button-ud {
    z-index: 9
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa {
    flex: 1;
    margin-top: -20px;
    padding-top: 20px;
    position: relative;
    cursor: pointer;
    max-width: 100%
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa:hover .Custom-module_hoverActions_GO21c {
    visibility: visible;
    position: absolute
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j {
    margin-top: 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0;
    flex: 1;
    border-radius: 6px
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j .Custom-module_customContent_7LHBv {
    flex: 1;
    z-index: 1
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j:after {
    content: "";
    background-color: var(--yq-bg-secondary);
    width: calc(100% + 16px);
    height: 100%;
    border-radius: 8px;
    left: -8px;
    top: 0;
    visibility: hidden;
    position: absolute
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j:hover {
    background-color: var(--yq-bg-secondary);
    color: var(--yq-text-disable)
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j:hover:after {
    visibility: visible
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_hoverActions_GO21c {
    cursor: pointer;
    background-color: var(--yq-bg-primary);
    position: absolute;
    top: 12px;
    right: 0;
    z-index: 100;
    border: 1px solid var(--yq-border-primary);
    border-radius: 16px;
    width: 70px;
    height: 25px;
    padding: 4px 12px;
    visibility: hidden;
    color: var(--yq-text-body)
}

.Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_hoverActions_GO21c>span:first-child {
    margin-right: 10px
}

.Custom-module_wikiBlockName_jfUrx {
    display: none;
    margin-top: 30px
}

.Custom-module_empty_nHM5b {
    margin-top: 20px;
    display: flex;
    flex: 1;
    height: 57px;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: var(--yq-text-disable);
    border: 1px dashed var(--yq-yuque-grey-5);
    cursor: pointer;
    position: relative
}

.Custom-module_empty_nHM5b:hover {
    background-color: var(--yq-bg-secondary)
}

.Custom-module_empty_nHM5b .Custom-module_moreLinkBtn_75bDE {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-disable);
    height: 32px;
    border-radius: 6px;
    padding: 0 0 0 6px
}

.Custom-module_empty_nHM5b .Custom-module_moreLinkBtn_75bDE:active {
    color: var(--yq-text-disable)
}

.Custom-module_empty_nHM5b .Custom-module_moreLinkBtn_75bDE:hover {
    color: var(--yq-text-disable);
    background-color: var(--yq-bg-primary-hover)
}

.Custom-module_empty_nHM5b .Custom-module_borderLeft_jIHtT {
    width: 1px;
    position: absolute;
    left: 0;
    top: 4px;
    bottom: 4px;
    background-image: linear-gradient(0deg,var(--yq-yuque-grey-5) 0,var(--yq-yuque-grey-5) 50%,transparent 0);
    background-size: 1px 14px
}

.Custom-module_empty_nHM5b .Custom-module_borderRight_JiW7a {
    width: 1px;
    position: absolute;
    right: 0;
    top: 6px;
    bottom: 6px;
    background-image: linear-gradient(180deg,var(--yq-yuque-grey-5) 0,var(--yq-yuque-grey-5) 50%,transparent 0);
    background-size: 1px 16px
}

.Custom-module_empty_nHM5b .Custom-module_borderTop_eI4fZ {
    height: 1px;
    position: absolute;
    left: 14px;
    right: 8px;
    top: 0;
    background-image: linear-gradient(90deg,var(--yq-yuque-grey-5) 0,var(--yq-yuque-grey-5) 50%,transparent 0);
    background-size: 18px 1px
}

.Custom-module_empty_nHM5b .Custom-module_borderBottom_l42NI {
    height: 1px;
    position: absolute;
    left: 8px;
    right: 8px;
    bottom: 0;
    background-image: linear-gradient(270deg,var(--yq-yuque-grey-5) 0,var(--yq-yuque-grey-5) 50%,transparent 0);
    background-size: 18px 1px
}

.Custom-module_empty_nHM5b .Custom-module_borderTopRadius_pp0j8 {
    position: absolute;
    left: 0;
    top: 0;
    width: 8px;
    height: 8px;
    border-top: 1px solid var(--yq-yuque-grey-5);
    border-left: 1px solid var(--yq-yuque-grey-5);
    border-top-left-radius: 100%
}

.Custom-module_empty_nHM5b .Custom-module_borderRightRadius_q401q {
    position: absolute;
    right: 0;
    top: 0;
    width: 8px;
    height: 8px;
    border-top: 1px solid var(--yq-yuque-grey-5);
    border-right: 1px solid var(--yq-yuque-grey-5);
    border-top-right-radius: 100%
}

.Custom-module_empty_nHM5b .Custom-module_borderBottomRadius_sXrE3 {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    border-right: 1px solid var(--yq-yuque-grey-5);
    border-bottom-right-radius: 100%
}

.Custom-module_empty_nHM5b .Custom-module_borderLeftRadius_mrYwc {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 8px;
    height: 8px;
    border-bottom: 1px solid var(--yq-yuque-grey-5);
    border-left: 1px solid var(--yq-yuque-grey-5);
    border-bottom-left-radius: 100%
}

.Custom-module_once_WE5rA,.Custom-module_twice_BeDIb {
    width: 100%
}

.Custom-module_thrice_14ugY {
    max-width: 100%
}

html[data-kumuhana=pouli] .Custom-module_moreLinkBtn_75bDE:hover {
    color: var(--yq-yuque-grey-9)!important
}

@media only screen and (max-width: 991px) {
    .Custom-module_h5Container_EGo4C {
        max-width:100%
    }

    .Custom-module_content_tXt6m .Custom-module_viewerContainer_EjgFa .Custom-module_viewer_R5J6j {
        margin-top: 0
    }

    .Custom-module_viewerContent_OdqeD {
        align-items: inherit;
        padding: 0 16px
    }

    .Custom-module_empty_nHM5b.Custom-module_isWiki_5fwKV {
        padding-left: 10px;
        padding-right: 10px;
        margin: 0 16px
    }
}

.btn-follow .hover-text,.btn-follow:hover .default-text {
    display: none
}

.btn-follow:hover .hover-text {
    display: inline
}

.btn-follow.ant-btn-default:focus,.btn-follow.ant-btn-default:hover {
    color: var(--yq-text-body);
    border-color: var(--yq-border-primary);
    outline: none
}

.btn-follow.ant-btn-clicked:after {
    display: none
}

.btn-plain-follow {
    color: var(--yq-text-caption)
}

.btn-plain-follow .larkui-icon {
    margin-right: 4px;
    color: var(--yq-text-caption)
}

.btn-plain-follow>a,.btn-plain-follow>a .larkui-icon {
    color: var(--yq-text-caption)
}

.btn-plain-follow>a:hover,.btn-plain-follow>a:hover .larkui-icon {
    color: var(--yq-text-body)
}

.index-module_count_pDHQn {
    margin-left: 8px;
    font-weight: 500
}

.index-module_offlineButton_XZkWh {
    cursor: not-allowed
}

.index-module_disabledStyle_MeBOz {
    opacity: .4;
    cursor: not-allowed;
    pointer-events: none
}

.OrgUserInfo-module_departmentInfo_3udmp {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--yq-border-light)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_cardHeader_502Md {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq {
    margin: 0 24px 0 0
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: center;
    margin-right: 24px;
    line-height: 26px;
    color: var(--yq-text-body)
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq .OrgUserInfo-module_icon_okrek,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz .OrgUserInfo-module_icon_okrek {
    margin-right: 8px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_fullWidth_zv0Qo {
    width: 100%;
    line-height: 32px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 {
    display: flex
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6:nth-child(n+2) {
    margin-top: 16px
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97 {
    display: inline-block;
    margin-left: 4px;
    width: 100%;
    font-weight: 500
}

.OrgUserInfo-module_departmentInfo_3udmp .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_departmentName_fuR97.OrgUserInfo-module_h5_M\+nrk {
    margin-left: 8px;
    font-weight: 400;
    font-size: 16px
}

.OrgUserInfo-module_divider_igZMz {
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background-color: var(--yq-border-light)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or {
    padding: 16px;
    width: 280px;
    background-color: var(--yq-bg-primary);
    box-shadow: 0 8px 16px 4px rgba(0,0,0,.04);
    border-radius: 8px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 {
    position: relative;
    padding-left: 56px;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_avatar_iMqQ\+ {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_wrapper_cpsZr {
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    min-height: 44px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_nameWrapper_u01vS {
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_name_9rYrf {
    max-width: 100%;
    color: var(--yq-text-primary);
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_tag_dWOJO {
    margin-left: 4px;
    padding: 0 4px;
    background-color: var(--yq-bg-secondary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 4px;
    color: var(--yq-text-body);
    font-size: 12px;
    height: 20px;
    line-height: 20px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_headerInfo_5B731 .OrgUserInfo-module_description_4Q94H {
    margin-top: 4px;
    font-size: 14px;
    line-height: 18px;
    color: var(--yq-text-caption);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_icon_okrek {
    color: var(--yq-icon-secondary)
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc {
    display: flex;
    flex-wrap: wrap
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    display: flex;
    align-items: flex-start;
    width: 100%
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6 span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq span,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz span {
    margin-left: 8px;
    color: var(--yq-text-body);
    font-size: 14px;
    line-height: 22px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_job_erqLq,.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_location_9-cQz {
    margin-top: 12px;
    align-items: center
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_details_hR4Nc .OrgUserInfo-module_department_MJvg6:nth-child(n+3) {
    margin-top: 12px
}

.OrgUserInfo-module_orgUserInfoCard_7n-Or .OrgUserInfo-module_department_MJvg6 .OrgUserInfo-module_icon_okrek {
    transform: translateY(4px)
}

.index-module_userCard_NOTbr {
    width: 330px
}

.index-module_userCard_NOTbr .index-module_body_20bgh {
    display: flex;
    padding: 20px 18px 20px 24px
}

.index-module_userCard_NOTbr img.index-module_avatar_OBHFJ {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    flex-shrink: 0
}

.index-module_userCard_NOTbr .index-module_userInfos_B16Pa {
    margin-left: 20px;
    width: 220px
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    word-break: break-all;
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_name_QeZm4 {
    color: var(--yq-text-primary)
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_dingtalk_kYXBj {
    height: 22px;
    vertical-align: middle;
    display: inline-block
}

.index-module_userCard_NOTbr .index-module_nameContainer_elI96 .index-module_badge_lsJBP {
    top: -2px;
    height: 22px
}

.index-module_userCard_NOTbr .index-module_dingding_l7pi0 {
    margin-left: 4px;
    width: 18px;
    height: 18px
}

.index-module_userCard_NOTbr .index-module_infoWithBg_JGcdP {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    color: var(--yq-text-body);
    background-color: var(--yq-bg-tertiary);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 400;
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_signature_aQ\+Wz {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--yq-text-body);
    margin-top: 8px;
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_infoList_Y58li {
    margin-top: 10px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 {
    display: flex;
    margin-bottom: 8px;
    min-height: 20px;
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7:last-child {
    margin-bottom: 0
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 .index-module_icon_MGwU8 {
    margin-top: 2px;
    margin-right: 8px;
    color: var(--yq-icon-primary)
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 span {
    color: var(--yq-text-primary);
    line-height: 20px
}

.index-module_userCard_NOTbr .index-module_infoItem_same7 a {
    margin-left: 8px
}

.index-module_userCard_NOTbr .index-module_footer_FNHCP {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid var(--yq-border-light);
    padding: 12px 18px 12px 24px
}

.index-module_userCard_NOTbr .index-module_follow_1HgLb {
    display: flex
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j {
    margin-right: 24px;
    color: var(--yq-text-disable);
    margin-top: 6px
}

.index-module_userCard_NOTbr .index-module_footerItem_H0y7j:last-child {
    margin-right: 0
}

.index-module_userCard_NOTbr .index-module_number_NahUE {
    margin-left: 5px;
    color: var(--yq-text-primary);
    word-break: break-all
}

.index-module_userCard_NOTbr .index-module_userLink_i0XYI {
    float: right
}

.index-module_userCard_NOTbr .index-module_skeleton_Z4M4v {
    width: 100%;
    height: 50px;
    margin-top: 16px;
    overflow: hidden
}

.index-module_userCard_NOTbr .index-module_infoDetail_dKXCO {
    word-break: break-all
}

.index-module_popover_Xyidp {
    display: inline-block
}

.index-module_overlay_A0ouW .ant-popover-inner-content {
    padding: 0
}

.Events-module_container_nKcdw {
    display: flex;
    margin-top: 8px;
    background-color: transparent;
    border: 0
}

.Events-module_container_nKcdw .Events-module_emptyTip_1iCvJ {
    display: flex;
    flex: 1;
    height: 80px;
    align-items: center;
    justify-content: center;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px
}

.Events-module_container_nKcdw .Events-module_list_XR289 {
    width: 100%;
    background-color: transparent
}

.Events-module_container_nKcdw .ant-list-item {
    padding-top: 0;
    padding-bottom: 8px
}

.Events-module_container_nKcdw .Events-module_loadMore_Ld95K {
    display: flex;
    height: 48px;
    align-items: center;
    justify-content: center;
    background-color: var(--yq-bg-primary);
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    cursor: pointer
}

.Events-module_timer_EBqdH {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-top: 8px
}

.Events-module_bookName_G61l7 {
    max-width: 234px;
    word-break: break-all
}

.Events-module_bookName_G61l7 .Events-module_bookNameText_cR-Tw {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Events-module_item_XdCH9 {
    display: flex;
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    padding: 24px;
    background-color: var(--yq-bg-primary);
    width: 100%
}

.Events-module_item_XdCH9 .Events-module_subTitle_5p7jX {
    display: flex
}

.Events-module_item_XdCH9 .Events-module_left_MszVn {
    margin-right: 12px
}

.Events-module_item_XdCH9 .Events-module_content_j0PbF {
    flex: 1;
    width: calc(100% - 36px)
}

.Events-module_item_XdCH9 .Events-module_checkAll_IfTrt {
    font-size: 14px;
    margin-top: 8px
}

.Events-module_item_XdCH9 .Events-module_user_myuJC {
    display: inline-block;
    max-width: 200px;
    vertical-align: bottom;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 4px;
    color: var(--yq-text-body)
}

.Events-module_item_XdCH9 .Events-module_user_myuJC a {
    color: var(--yq-text-body)
}

.Events-module_item_XdCH9 .Events-module_opDesc_JMZtB {
    color: var(--yq-text-caption);
    display: flex
}

.Events-module_item_XdCH9 .Events-module_opDesc_JMZtB a {
    margin: 0 2px;
    color: var(--yq-text-body)
}

.Events-module_item_XdCH9 .Events-module_bookContainer_1htQs {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 16px;
    border: 1px solid var(--yq-yuque-grey-1);
    border-radius: 6px;
    padding: 16px;
    background-color: var(--yq-bg-secondary);
    font-size: 14px
}

.Events-module_item_XdCH9 .Events-module_bookContainer_1htQs .Events-module_bookContent_LtPuS {
    flex: 1;
    color: var(--yq-text-body);
    margin-left: 8px;
    width: calc(100% - 16px)
}

.Events-module_item_XdCH9 .Events-module_bookContainer_1htQs .Events-module_bookContent_LtPuS a {
    color: var(--yq-text-body)
}

.Events-module_item_XdCH9 .Events-module_bookContainer_1htQs .book-name {
    max-width: 100%;
    line-height: 2
}

.Events-module_item_XdCH9 .Events-module_srcContainer_7Mofj {
    display: flex;
    margin-top: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--yq-yuque-grey-1);
    border-radius: 6px;
    background-color: var(--yq-bg-secondary);
    color: var(--yq-text-body);
    font-size: 14px;
    align-items: center;
    padding: 0 16px;
    height: 48px
}

.Events-module_item_XdCH9 .Events-module_srcContainer_7Mofj:last-child {
    margin-bottom: 0
}

.Events-module_item_XdCH9 .Events-module_srcContainer_7Mofj .Events-module_resName_om6pj {
    flex: 1;
    margin: 0 8px;
    max-width: 100%;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Events-module_item_XdCH9 .Events-module_srcContainer_7Mofj .Events-module_fileSize_Zcc8Q {
    color: var(--yq-text-caption)
}

.Events-module_item_XdCH9 .Events-module_docContainer_NFGsG {
    margin-top: 16px;
    margin-bottom: 8px
}

.Events-module_item_XdCH9 .Events-module_docContainer_NFGsG a {
    color: var(--yq-text-body)
}

.Events-module_item_XdCH9 .Events-module_docContainer_NFGsG .Events-module_docTitle_61f3U {
    color: var(--yq-text-primary);
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;
    word-break: break-all;
    display: -webkit-box;
    max-height: 44px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px
}

.Events-module_item_XdCH9 .Events-module_thumbContainer_LFBcA {
    display: flex
}

.Events-module_item_XdCH9 .Events-module_thumbnail_qiSUB {
    width: 143px;
    height: 90px;
    background-color: var(--yq-bg-secondary);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    border: 1px solid var(--yq-yuque-grey-1);
    margin-right: 8px
}

.Events-module_smallSize_qnkx1 {
    font-size: 12px;
    padding: 16px
}

.Events-module_smallSize_qnkx1 .Events-module_content_j0PbF .Events-module_docContainer_NFGsG .Events-module_docTitle_61f3U {
    font-size: 14px
}

.Events-module_smallSize_qnkx1 .Events-module_user_myuJC {
    max-width: 80px
}

.Events-module_smallSize_qnkx1 .Events-module_opDesc_JMZtB .Events-module_bookName_G61l7 {
    max-width: 60px
}

.Events-module_at_SP3kA {
    margin-left: 2px
}

.Events-module_desc_\+TREk {
    word-break: break-all;
    color: var(--yq-text-caption)
}

.QuickLinkAction-module_quickLinkIcon_Ri9-R .QuickLinkAction-module_iconActive_YXfCw {
    display: none
}

.QuickLinkAction-module_quickLinkActive_8vJkU .QuickLinkAction-module_quickLinkIcon_Ri9-R .QuickLinkAction-module_iconActive_YXfCw {
    display: inline-flex
}

.QuickLinkAction-module_quickLinkIcon_Ri9-R .QuickLinkAction-module_icon_baoTe {
    color: var(--yq-icon-secondary)
}

.QuickLinkAction-module_quickLinkIcon_Ri9-R .QuickLinkAction-module_icon_baoTe:hover {
    color: var(--yq-text-body)
}

.QuickLinkAction-module_quickLinkActive_8vJkU .QuickLinkAction-module_quickLinkIcon_Ri9-R .QuickLinkAction-module_icon_baoTe {
    display: none
}

.QuickLinkAction-module_restore_Vuntp {
    margin-left: 8px
}

.QuickLinkForm-module_dropdown_cR9iu {
    width: 100%
}

.QuickLinkForm-module_dropdown_cR9iu .ant-menu {
    max-height: 370px;
    overflow-y: auto;
    position: relative;
    margin: 0;
    padding: 4px 0;
    text-align: left;
    list-style-type: none;
    background-color: var(--yq-bg-primary);
    background-clip: padding-box;
    border-radius: 4px;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,.15)
}

.QuickLinkForm-module_dropdown_cR9iu .ant-menu-item {
    height: auto
}

.QuickLinkForm-module_dropdown_cR9iu .ant-menu-item:hover {
    background-color: var(--yq-bg-primary-hover)
}

.QuickLinkForm-module_btn_Chn\+w {
    text-align: right
}

.QuickLinkForm-module_btn_Chn\+w .ant-btn {
    margin-left: 8px
}

.QuickLinkForm-module_recommend_AS7gl {
    position: relative;
    min-height: 64px
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_icon_lI42q {
    width: 40px;
    float: left
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_titleWrap_JVfk2 {
    overflow: hidden
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_title_\+8iCl {
    color: var(--yq-text-primary);
    font-size: 14px;
    line-height: 1.6;
    max-width: 320px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_title_\+8iCl:hover {
    color: var(--yq-text-primary)
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_meta_H955L {
    display: flex;
    align-items: center;
    margin-top: 4px;
    line-height: 1.6
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_belong_WIp6G {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 8px;
    max-width: 200px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_belong_WIp6G a {
    color: var(--yq-text-caption)
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_belong_WIp6G a:hover {
    color: var(--yq-text-body)
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_belong_WIp6G .QuickLinkForm-module_split_QhgIo {
    padding: 0 4px
}

.QuickLinkForm-module_recommend_AS7gl .QuickLinkForm-module_action_M8yXe {
    width: 30px
}

.QuickLinkForm-module_form_iEUab .QuickLinkForm-module_input_RO4bf {
    cursor: text
}

.QuickLinkForm-module_larkIcon_Uufm3 {
    vertical-align: middle
}

@media only screen and (max-width: 575px) {
    .QuickLinkForm-module_btn_Chn\+w .ant-btn {
        margin-right:8px;
        margin-left: 0
    }
}

.SearchInput-module_container_GtgUm {
    width: 100%;
    background: url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*08dJRYWJN14AAAAAAAAAAAAAARQnAQ),url(https://gw.alipayobjects.com/mdn/prod_resou/afts/img/A*7Pn0TrQdTFIAAAAAAAAAAAAAARQnAQ);
    background-repeat: no-repeat;
    background-position: top -30px right -30px,bottom 0 left 0;
    background-size: contain,contain;
    background-color: var(--yq-bg-primary);
    min-height: 152px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 6px
}

[data-kumuhana=pouli] .SearchInput-module_container_GtgUm {
    background: none
}

.SearchInput-module_inputContainer_\+TawN {
    width: 66%;
    min-width: 246px;
    height: 40px;
    margin-top: 40px;
    position: relative
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_input_qq\+u1 {
    width: 100%;
    height: 100%
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_input_qq\+u1.ant-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    background-color: var(--yq-bg-secondary)
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_input_qq\+u1.ant-input-affix-wrapper {
    border: 1px solid var(--yq-border-primary)
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_input_qq\+u1.ant-input-affix-wrapper>input.ant-input {
    padding: 0;
    border: none;
    outline: none
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_btn_7Z7g3 {
    position: absolute;
    width: 100px;
    right: 1px;
    top: 1px;
    bottom: 1px;
    height: 38px;
    border-top-color: transparent!important;
    border-right-color: transparent!important;
    border-bottom-color: transparent!important;
    border-left-color: var(--yq-border-primary);
    border-bottom-left-radius: 0;
    background-color: var(--yq-bg-primary)!important;
    border-top-left-radius: 0;
    border-width: 0 0 0 1px;
    background-image: none;
    z-index: 1
}

.SearchInput-module_inputContainer_\+TawN .SearchInput-module_input_qq\+u1+.SearchInput-module_btn_7Z7g3:hover {
    border-color: transparent;
    border-left-color: var(--yq-yuque-grey-5)!important
}

.SearchInput-module_inputContainer_\+TawN .ant-input-affix-wrapper .ant-input-suffix {
    right: 112px!important
}

.SearchInput-module_onelineConatiner_Fykue {
    align-items: center
}

.SearchInput-module_recommendContainer_WUyBI {
    display: flex;
    width: 66%;
    min-width: 246px;
    justify-content: flex-start;
    margin: 10px auto;
    padding: 6px 0;
    line-height: 22px;
    flex-direction: row;
    align-items: baseline
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_label_hhhd5 {
    color: var(--yq-text-caption);
    margin-right: 12px;
    line-height: 26px
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_label-zh-cn_iVuZD {
    min-width: 34px
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_addInfo_dRDln {
    color: var(--yq-text-caption);
    margin-left: -8px;
    margin-right: 4px
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_linksContainer_Nv0Vp {
    display: flex;
    flex-wrap: wrap;
    align-items: center
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_link_I7nih {
    word-wrap: break-word;
    margin-right: 8px;
    position: relative
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_breakLink_UCjYv {
    margin-right: 0
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_hoverLink_o86Rn {
    color: var(--yq-blue-5)
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_plusBtn_1KbB1 {
    cursor: pointer;
    font-size: 12px;
    font-weight: 700;
    color: var(--yq-text-caption);
    background-color: var(--yq-bg-primary);
    border: var(--yq-border-primary) solid 1px;
    border-radius: 2px;
    outline: none;
    margin-left: 4px
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_plusBtn_1KbB1:active,.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_plusBtn_1KbB1:hover {
    background-color: var(--yq-bg-tertiary);
    border: var(--yq-border-primary) solid 1px
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_delete_Vx9q- {
    width: 16px;
    height: 16px;
    border: solid var(--yq-yuque-grey-5) 1px;
    border-radius: 8px;
    font-size: 6px;
    color: var(--yq-text-disable);
    position: absolute;
    top: -8px;
    right: -10px;
    background-color: var(--yq-bg-primary);
    display: none;
    cursor: pointer;
    outline: none
}

.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_delete_Vx9q-:active,.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_delete_Vx9q-:focus,.SearchInput-module_recommendContainer_WUyBI .SearchInput-module_delete_Vx9q-:hover {
    border-color: var(--yq-yuque-grey-7);
    color: var(--yq-text-caption)
}

.SearchInput-module_linkContainer_Nfd56 {
    display: flex
}

@media only screen and (min-width: 768px) {
    .SearchInput-module_delete_Vx9q-.active,.SearchInput-module_delete_Vx9q-:hover {
        display:flex;
        align-items: center;
        justify-content: center
    }
}

@media only screen and (max-width: 575px) {
    .SearchInput-module_container_GtgUm {
        padding:0 16px
    }

    .SearchInput-module_inputContainer_\+TawN,.SearchInput-module_recommendContainer_WUyBI {
        width: 100%
    }

    .SearchInput-module_recommendContainer_WUyBI .SearchInput-module_label_hhhd5 {
        margin-right: 5px
    }

    .SearchInput-module_recommendContainer_WUyBI .SearchInput-module_label-zh-cn_iVuZD {
        width: 56px
    }
}

.HeadlineForm-module_headlineForm_vNDjg .ant-legacy-form-explain {
    font-size: 12px
}

.HeadlineForm-module_cropper_mku7y {
    float: left;
    background: var(--yq-bg-primary-hover);
    width: 360px;
    height: 204px
}

.HeadlineForm-module_previewer_Soz5O {
    float: right
}

.HeadlineForm-module_coverPreview_qRvFk {
    width: 285px;
    height: 102px;
    background: var(--yq-bg-primary-hover);
    margin-bottom: 16px;
    background-size: cover
}

@media only screen and (max-width: 991px) {
    .HeadlineForm-module_cropper_mku7y {
        float:none;
        max-width: 100%
    }

    .HeadlineForm-module_previewer_Soz5O {
        float: none;
        margin-top: 16px
    }

    .HeadlineForm-module_coverPreview_qRvFk {
        width: 100%
    }
}

.Headlines-module_headlines_W3Q6o {
    border: 1px solid var(--yq-border-primary);
    border-width: 1px 0
}

.Headlines-module_headline_w2QVC {
    background: var(--yq-bg-primary);
    padding: 16px;
    border-top: 1px solid var(--yq-border-primary);
    position: relative
}

.Headlines-module_headline_w2QVC .ant-card-body {
    padding: 0
}

.Headlines-module_headline_w2QVC h4 {
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0
}

.Headlines-module_headline_w2QVC button {
    position: absolute;
    top: 4px;
    right: 20px;
    width: 24px;
    height: 24px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 2px;
    background: var(--yq-bg-primary);
    padding: 0;
    cursor: pointer
}

.Headlines-module_headline_w2QVC button:focus {
    outline: none
}

.Headlines-module_headline_w2QVC:first-child {
    border-top: none
}

.Headlines-module_banner_TnEw7 h4 {
    margin-top: 12px
}

.Headlines-module_banner_TnEw7 h4.Headlines-module_placeholder_Zg4mc {
    display: none
}

.Headlines-module_banner_TnEw7 button {
    top: 16px;
    right: 16px
}

.Headlines-module_cover_CqW3S {
    height: 122px;
    background-color: var(--yq-bg-secondary);
    background-size: cover
}

.Headlines-module_description_4\+9No {
    color: var(--yq-text-caption);
    margin-top: 8px;
    display: -webkit-box;
    max-height: 63px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 21px
}

.Headlines-module_itemPlaceholder_LNoRv {
    background: var(--yq-bg-secondary);
    border-radius: 2px;
    width: 100%;
    height: 100%;
    text-align: center;
    color: var(--yq-yuque-grey-5);
    padding: 30px 0
}

.Headlines-module_itemPlaceholder_LNoRv .larkicon {
    font-weight: 700;
    font-size: 20px
}

.Headlines-module_itemPlaceholder_LNoRv .Headlines-module_empty_YlJ7k {
    line-height: 40px
}

@media only screen and (min-width: 768px) {
    .Headlines-module_headlines_W3Q6o {
        border:none
    }

    .Headlines-module_headlines_W3Q6o .ant-card-body {
        height: 0;
        padding-bottom: 24.12109375%;
        position: relative;
        overflow: hidden;
        background: var(--yq-bg-primary)
    }

    .Headlines-module_headlines_W3Q6o.Headlines-module_once_IpmY4 .ant-card-body {
        padding-bottom: 74.24242424%
    }

    .Headlines-module_headlines_W3Q6o.Headlines-module_twice_1PP2H .ant-card-body {
        padding-bottom: 36.13569322%
    }

    .Headlines-module_headline_w2QVC {
        position: absolute;
        top: 0;
        right: 0;
        width: 32%;
        height: 50%;
        padding: 0 16px;
        margin-top: 16px;
        border: none
    }

    .Headlines-module_headline_w2QVC:last-child {
        top: auto;
        bottom: 0
    }

    .Headlines-module_headline_w2QVC.Headlines-module_editable_3uRwr:hover .Headlines-module_placeholder_Zg4mc {
        display: block
    }

    .Headlines-module_headline_w2QVC.Headlines-module_editable_3uRwr:hover .Headlines-module_itemPlaceholder_LNoRv {
        cursor: pointer;
        color: var(--yq-text-caption)
    }

    .Headlines-module_banner_TnEw7 {
        position: absolute;
        top: 0;
        left: 0;
        padding: 0;
        margin: 0;
        height: 100%;
        width: 68%;
        box-shadow: 0 1px 4px rgba(0,0,0,.08);
        background: var(--yq-bg-primary-hover);
        overflow: hidden;
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        z-index: 1
    }

    .Headlines-module_banner_TnEw7 button {
        top: 12px;
        right: 12px
    }

    .Headlines-module_banner_TnEw7 h4 {
        position: absolute;
        bottom: 0;
        font-size: 20px;
        color: var(--yq-white);
        background: linear-gradient(transparent,rgba(51,51,51,.5));
        left: 0;
        right: 0;
        padding: 8px 16px
    }

    .Headlines-module_cover_CqW3S {
        height: 100%;
        background-color: transparent
    }
}

@media screen and (max-width: 991px) {
    .Headlines-module_headline_w2QVC .Headlines-module_editBtn_zA2Sg {
        top:22px;
        right: 22px
    }
}

.BookList-module_list_SfgYP {
    flex: 1;
    padding: 0 24px;
    border-radius: 6px
}

.BookList-module_list_SfgYP .ant-list-item-meta {
    margin-bottom: 0
}

.BookList-module_list_SfgYP .ant-list-item-meta-title {
    margin-bottom: 8px
}

.BookList-module_list_SfgYP .ant-list-item-meta-content {
    width: 100%
}

.BookList-module_list_SfgYP .ant-list-item-meta-description {
    font-size: 12px;
    white-space: nowrap
}

.BookList-module_list_SfgYP .book-name {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: var(--yq-text-primary);
    word-break: break-all
}

.BookList-module_list_SfgYP .book-name .book-name-text {
    display: -webkit-box;
    max-height: 44px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px
}

.BookList-module_list_SfgYP .BookList-module_count_YoOc\+ {
    margin-right: 24px
}

.BookList-module_countValue_2MzBu {
    color: var(--yq-text-body)
}

.BookList-module_list-twice_Km64g .book-name .book-name-text {
    max-width: 550px
}

.BookList-module_list-thrice_COvZT .book-name .book-name-text {
    max-width: 800px
}

.BookList-module_list-once_6PcnE {
    padding: 0 16px
}

.BookList-module_list-once_6PcnE .book-name .book-name-text {
    max-width: 225px;
    font-size: 14px
}

.thread-user {
    color: var(--yq-text-primary)
}

.thread-user .thread-user-name {
    margin-left: 8px
}

.thread-icon {
    margin-right: 8px;
    vertical-align: middle;
    margin-bottom: 3px
}

.thread-creator {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.thread-creator img {
    display: inline-block;
    margin-right: 6px
}

.thread-created-at {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.thread-participant {
    cursor: pointer
}

.thread-title {
    font-size: 14px;
    color: var(--yq-text-body);
    text-overflow: ellipsis
}

.ThreadList-module_list_9-aUo {
    padding: 0 24px;
    flex: 1;
    border-radius: 4px
}

.ThreadList-module_list_9-aUo .ant-list-item-meta-avatar {
    margin-right: 8px
}

.ThreadList-module_list_9-aUo .ant-list-item-meta {
    margin-bottom: 0;
    flex: 1
}

.ThreadList-module_list_9-aUo .ant-list-item-meta-title {
    margin-bottom: 8px
}

.ThreadList-module_list_9-aUo .ant-list-item-meta-content {
    width: 100%
}

.ThreadList-module_list_9-aUo .ant-list-item-meta-description {
    display: flex;
    font-size: 12px;
    white-space: nowrap
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .thread-title {
    font-size: 16px;
    color: var(--yq-text-primary);
    max-width: 800px;
    word-break: break-all;
    display: -webkit-box;
    max-height: 44px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .ThreadList-module_user_UPssk {
    margin-right: 8px;
    max-width: 300px;
    color: var(--yq-text-body)
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .ThreadList-module_user_UPssk a {
    color: var(--yq-text-body)
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .ThreadList-module_right_Zr\+Zs {
    display: flex;
    align-items: center;
    margin-left: 16px;
    justify-content: center
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .ThreadList-module_right_Zr\+Zs .ThreadList-module_comment_v3FjC {
    margin-top: 2px;
    color: var(--yq-text-caption)
}

.ThreadList-module_list_9-aUo .ThreadList-module_item_SJF5Q .ThreadList-module_right_Zr\+Zs span:last-child {
    margin-left: 8px
}

.ThreadList-module_list_9-aUo .ThreadList-module_item-once_VyTh5 .ThreadList-module_user_UPssk {
    max-width: 60px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ThreadList-module_list_9-aUo .ThreadList-module_item-once_VyTh5 .thread-title {
    font-size: 14px;
    max-width: 300px;
    word-break: break-all
}

.ThreadList-module_list_9-aUo .ThreadList-module_item-twice_iR1wR .thread-title {
    max-width: 450px;
    word-break: break-all
}

.ThreadList-module_list_9-aUo .ThreadList-module_item-twice_iR1wR .ThreadList-module_user_UPssk {
    max-width: 200px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.ThreadList-module_desc_1ML\+O {
    font-size: 12px;
    display: flex
}

.ThreadList-module_desc_1ML\+O .ThreadList-module_timer_WOUyu {
    margin-left: 8px
}

.ThreadList-module_desc_1ML\+O a {
    font-size: 12px;
    color: var(--yq-text-body)
}

.ThreadList-module_desc_1ML\+O .book-link {
    max-width: 200px;
    width: auto;
    margin-left: 4px
}

.ThreadList-module_desc_1ML\+O .lark-book-title {
    max-width: 200px;
    width: auto
}

.ThreadList-module_desc_1ML\+O .book-name {
    width: auto;
    max-width: 200px;
    display: flex;
    align-items: center;
    font-size: 12px;
    word-wrap: break-word
}

.ThreadList-module_desc-twice_kub-v .book-link,.ThreadList-module_desc-twice_kub-v .book-name,.ThreadList-module_desc-twice_kub-v .lark-book-title {
    max-width: 160px
}

.ThreadList-module_small-en-us_YeJSU .book-link,.ThreadList-module_small-en-us_YeJSU .lark-book-title {
    max-width: 50px;
    width: auto
}

.ThreadList-module_small-en-us_YeJSU .book-name {
    width: auto;
    max-width: 50px;
    display: flex;
    align-items: center;
    font-size: 12px
}

.ThreadList-module_small-en-us_YeJSU .ThreadList-module_user_UPssk {
    max-width: 55px
}

.ThreadList-module_small-zh-cn_uLiSl .book-link,.ThreadList-module_small-zh-cn_uLiSl .lark-book-title {
    max-width: 65px;
    width: auto
}

.ThreadList-module_small-zh-cn_uLiSl .book-name {
    width: auto;
    max-width: 65px;
    display: flex;
    align-items: center;
    font-size: 12px
}

.ThreadList-module_small-zh-cn_uLiSl .ThreadList-module_user_UPssk {
    max-width: 60px
}

.ThreadList-module_listOnce_WW8Io {
    padding: 0 16px
}

.ThreadList-module_empty_UGsRe {
    padding: 8px;
    word-break: break-all
}

.AddThread-module_newThreadModal_6v\+8G .ant-modal-header {
    border-bottom: none;
    padding-top: 20px;
    padding-bottom: 0
}

.AddThread-module_newThreadModal_6v\+8G .ant-modal-body {
    padding-bottom: 0
}

.AddThread-module_newThreadModal_6v\+8G .ant-form-vertical .ant-form-item-label>label {
    font-weight: 500
}

.AddThread-module_newThreadModal_6v\+8G .ant-modal-footer {
    text-align: center;
    border-top: none;
    padding: 0 24px 24px 24px
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_title_BpkRF {
    font-size: 16px
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_title_BpkRF .AddThread-module_titleDesc_ek6G\+ {
    margin-top: 8px;
    color: var(--yq-text-caption);
    font-weight: 400;
    font-size: 14px
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_titleWrapper_BmSeo {
    height: 40px;
    display: flex;
    margin-bottom: -12px
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_titleWrapper_BmSeo .AddThread-module_bookIcon_MjPC3 {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    padding: 6px;
    width: 40px;
    height: 40px;
    margin-right: 6px;
    position: relative;
    line-height: 20px;
    transition: all .3s;
    text-align: center
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_permission_44HQS {
    display: flex;
    align-items: center;
    height: 22px;
    line-height: 22px;
    color: var(--yq-text-body)
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_permission_44HQS .AddThread-module_permissionIcon_Mq\+pf {
    margin-right: 8px
}

.AddThread-module_newThreadModal_6v\+8G .AddThread-module_submitBtn_mwWZa {
    width: 384px;
    height: 40px
}

.styles-module_selector_Z3\+Wx {
    padding: 24px;
    position: relative
}

.styles-module_title_Misjv {
    font-size: 16px;
    line-height: 24px
}

.index-module_wrap_iKZPE {
    background-color: var(--yq-bg-secondary);
    border-radius: 4px;
    padding: 12px 40px 12px 20px;
    display: flex;
    position: relative;
    color: var(--yq-text-body)
}

.index-module_wrap_iKZPE:hover .index-module_close_mZbMN {
    display: flex
}

.index-module_icon_gxtpV {
    margin-top: 2px;
    width: 18px;
    height: 18px;
    margin-right: 8px;
    flex: none
}

.index-module_close_mZbMN {
    display: none;
    position: absolute;
    top: 8px;
    right: 6px;
    height: 28px;
    width: 28px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--yq-bg-primary-hover-light);
    border-radius: 4px
}

.index-module_closeIcon_kM1zW {
    font-size: 16px
}

.doc-draft-tip {
    font-weight: 400
}

.doc-draft-tip-content .update-info {
    color: var(--yq-text-caption);
    font-size: 12px;
    margin-top: 4px
}

.doc-draft-tip-content .update-info a {
    color: var(--yq-text-body)
}

.ant-tag.doc-template-tag {
    margin: 0 0 0 8px;
    background-color: transparent;
    border-color: var(--yq-function-info);
    color: var(--yq-function-info);
    height: 20px;
    line-height: 18px
}

.doc-title {
    font-size: 14px;
    line-height: 21px;
    text-overflow: ellipsis;
    color: var(--yq-text-body);
    font-family: Chinese Quote,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji
}

.doc-title-draft {
    font-size: 12px;
    color: var(--yq-text-caption);
    margin-right: 4px;
    font-weight: 400
}

.doc-icon {
    margin-right: 4px
}

.doc-access-scope {
    margin-left: 8px
}

.doc-belong,.doc-belong a {
    color: var(--yq-text-caption)
}

.doc-belong a {
    margin: 0 4px
}

.doc-belong a:first-child {
    margin-left: 0
}

.doc-contributors,.doc-contributors span a {
    color: var(--yq-text-caption)
}

.index-module_articleTitle_VJTLJ {
    word-break: break-word
}

.index-module_popover_nfMC3 {
    display: inline
}

.index-module_belongMenu_2QmLB {
    outline: none;
    cursor: pointer
}

.index-module_belongMenu_2QmLB .larkui-icon {
    display: inline-block;
    font-size: 12px;
    margin-left: 4px
}

.index-module_belongText_TkCAl {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu {
    min-width: 188px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item {
    display: flex;
    align-items: center
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .larkui-icon-check-outlined {
    margin-right: 6px;
    visibility: hidden;
    font-size: 16px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 280px
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>.larkui-icon,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q>span {
    display: inline-block;
    vertical-align: middle
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active,.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item:hover {
    background-color: var(--yq-bg-tertiary);
    font-weight: 400
}

.index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item.ant-cascader-menu-item-active .larkui-icon-check-outlined {
    visibility: visible
}

@media only screen and (max-width: 575px) {
    .index-module_belongMenuOverlay_ua9Uh .ant-cascader-menu-item .index-module_belongMenuLabel_Eor0q {
        max-width:140px
    }
}

.index-module_privacy_QkaFB {
    display: inline-block;
    padding: 3px 5px;
    border: 1px solid var(--yq-yuque-grey-5);
    border-radius: 4px;
    color: var(--yq-text-body);
    line-height: 14px;
    font-size: 10px;
    font-weight: 400;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    white-space: nowrap;
    margin: 0 6px 0
}

.GlobalDocCreate-module_menuItemContainer_\+DgHG {
    display: flex;
    align-items: center
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu {
    padding: 8px 0;
    min-width: 110px
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-menu .ant-menu-item {
    height: 32px;
    line-height: 32px
}

.GlobalDocCreate-module_popoverContainer_zQaDv .ant-popover-inner-content {
    padding: 0
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-popover-inner-content {
    padding: 8px
}

.GlobalDocCreate-module_dashboardDocCreatePopoverContainer_zzl\+8 .ant-menu-item {
    border-radius: 8px
}

.GlobalDocCreate-module_iconContainer_eX6jK {
    margin-right: 12px
}

.GlobalDocCreate-module_iconBeta_hDUuJ {
    background-color: var(--yq-yellow-4);
    display: inline-block;
    margin-left: 3px;
    color: var(--yq-white);
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    text-align: center;
    border-radius: 8px;
    padding: 0 5px;
    position: absolute;
    top: 6px;
    left: 50%
}

.GlobalDocCreate-module_menuList_s1r7J {
    display: flex;
    justify-content: space-between
}

.GlobalDocCreate-module_menuListItem_hC68r {
    text-align: center;
    cursor: pointer
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    background: none!important
}

.GlobalDocCreate-module_templateCenterButton_Rrl2q.GlobalDocCreate-module_btnBlock_W3wwE>svg {
    margin-right: 8px
}

.GlobalDocCreate-module_desc_6gQhM {
    color: var(--yq-text-caption);
    margin-left: 16px
}

.DocList-module_list_Ef83V {
    max-width: 100%;
    flex: 1;
    padding: 0 24px;
    border-radius: 6px
}

.DocList-module_list_Ef83V .ant-list-item {
    padding-top: 24px;
    padding-bottom: 24px
}

.DocList-module_list_Ef83V .ant-list-item-meta-avatar {
    margin-right: 8px
}

.DocList-module_list_Ef83V .ant-list-item-meta {
    margin-bottom: 0;
    flex: 1
}

.DocList-module_list_Ef83V .ant-list-item-meta-title {
    margin-bottom: 8px
}

.DocList-module_list_Ef83V .ant-list-item-meta-content {
    width: 100%
}

.DocList-module_list_Ef83V .ant-list-item-meta-description {
    font-size: 12px;
    white-space: nowrap
}

.DocList-module_list_Ef83V .thread-title {
    line-height: 2;
    display: flex;
    align-items: center;
    font-size: 14px
}

.DocList-module_list_Ef83V .DocList-module_user_gLNDA {
    margin-right: 8px;
    max-width: 200px;
    color: var(--yq-text-body);
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.DocList-module_list_Ef83V .DocList-module_user_gLNDA a {
    color: var(--yq-text-body)
}

.DocList-module_list_Ef83V .DocList-module_item_6QJc3 {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center
}

.DocList-module_list_Ef83V .DocList-module_item_6QJc3 .doc-title {
    color: var(--yq-text-primary);
    font-size: 16px;
    max-width: 600px;
    word-break: break-all;
    display: -webkit-box;
    max-height: 44px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .doc-title {
    color: var(--yq-text-primary);
    font-size: 16px;
    word-break: break-all
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_content_wwOga {
    display: flex;
    justify-content: center
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_content_wwOga .DocList-module_description_feO3M {
    flex: 1;
    margin-bottom: 4px;
    min-width: 0
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_content_wwOga .DocList-module_cover_yaSsO {
    align-self: flex-start;
    align-items: center;
    margin-left: 8px;
    margin-top: 4px
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_content_wwOga .DocList-module_thumbnail_XHUmw {
    width: 160px;
    height: 110px;
    background-color: var(--yq-bg-secondary);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    border: 1px solid var(--yq-yuque-grey-1);
    margin-right: 4px
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_smallContent_d9By5 .DocList-module_thumbnail_XHUmw {
    width: 96px;
    height: 60px;
    background-color: var(--yq-bg-secondary);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    border: 1px solid var(--yq-yuque-grey-1);
    margin-right: 8px
}

.DocList-module_list_Ef83V .DocList-module_itemDetail_1skXg .DocList-module_smallContent_d9By5 .DocList-module_description_feO3M .DocList-module_descriptionContent_4BjH4 {
    max-width: 255px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical
}

.DocList-module_list_Ef83V .DocList-module_item-once_IXmf5 .doc-title {
    max-width: 300px;
    font-size: 14px
}

.DocList-module_list_Ef83V .DocList-module_item-once_IXmf5 .DocList-module_content_wwOga .DocList-module_description_feO3M {
    font-size: 12px
}

.DocList-module_list_Ef83V .DocList-module_item-twice_jGOD7 .doc-title {
    max-width: 600px;
    font-size: 16px
}

.DocList-module_list_Ef83V .DocList-module_item-thrice_cgwEP .doc-title {
    max-width: 800px;
    font-size: 16px
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- {
    font-size: 12px;
    display: flex
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- .DocList-module_timer_0emWt {
    margin-left: 8px
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- a {
    font-size: 12px;
    color: var(--yq-text-body)
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- .book-link {
    max-width: 200px;
    width: auto;
    margin-left: 4px
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- .lark-book-title {
    max-width: 200px;
    width: auto
}

.DocList-module_list_Ef83V .DocList-module_desc_npwC- .book-name {
    width: auto;
    max-width: 200px;
    display: flex;
    align-items: center;
    font-size: 12px;
    word-wrap: break-word
}

.DocList-module_list_Ef83V .DocList-module_desc-twice_UmYJn .book-link,.DocList-module_list_Ef83V .DocList-module_desc-twice_UmYJn .book-name,.DocList-module_list_Ef83V .DocList-module_desc-twice_UmYJn .lark-book-title {
    max-width: 160px
}

.DocList-module_list_Ef83V .DocList-module_small-en-us_S7Fuj .book-link,.DocList-module_list_Ef83V .DocList-module_small-en-us_S7Fuj .lark-book-title {
    max-width: 50px;
    width: auto
}

.DocList-module_list_Ef83V .DocList-module_small-en-us_S7Fuj .book-name {
    width: auto;
    max-width: 50px;
    display: flex;
    align-items: center;
    font-size: 12px
}

.DocList-module_list_Ef83V .DocList-module_small-en-us_S7Fuj .DocList-module_user_gLNDA {
    max-width: 55px
}

.DocList-module_list_Ef83V .DocList-module_small-zh-cn_XPnHr .book-link,.DocList-module_list_Ef83V .DocList-module_small-zh-cn_XPnHr .lark-book-title {
    max-width: 65px;
    width: auto
}

.DocList-module_list_Ef83V .DocList-module_small-zh-cn_XPnHr .book-name {
    width: auto;
    max-width: 65px;
    display: flex;
    align-items: center;
    font-size: 12px
}

.DocList-module_list_Ef83V .DocList-module_small-zh-cn_XPnHr .DocList-module_user_gLNDA {
    max-width: 60px
}

.DocList-module_listOnce_yF\+v- {
    padding: 0 16px
}

.DocList-module_listOnce_yF\+v- .ant-list-item {
    padding-top: 16px;
    padding-bottom: 16px
}

.DocList-module_listBasic_s4IMT {
    padding: 0 16px
}

.DocList-module_listBasic_s4IMT .ant-list-item {
    padding-top: 16px;
    padding-bottom: 16px
}

@media only screen and (max-width: 991px) {
    .DocList-module_list_Ef83V {
        padding:0;
        border-radius: 0
    }

    .DocList-module_list_Ef83V .ant-list-item {
        padding: 16px
    }

    .DocList-module_list_Ef83V .DocList-module_item-twice_jGOD7 .doc-title {
        font-size: 16px
    }
}

.DocList-module_bar_emrs7 {
    flex-grow: 1;
    background: var(--yq-border-primary);
    height: 1px
}

.DocList-module_actions_XSfNO {
    flex-grow: 1;
    display: none
}

.DocList-module_actions_XSfNO .DocList-module_operations_UBfDO {
    height: 26px;
    border-radius: 13px;
    border: 1px solid var(--yq-border-primary);
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-bg-tertiary)
}

.DocList-module_actions_XSfNO .DocList-module_operations_UBfDO button {
    border: none;
    cursor: pointer;
    padding: 0;
    background: var(--yq-bg-tertiary);
    color: var(--yq-text-caption);
    font-size: 16px
}

.DocList-module_actions_XSfNO .DocList-module_operations_UBfDO button:disabled {
    border: none;
    background: var(--yq-bg-tertiary);
    outline: none
}

.DocList-module_actions_XSfNO .DocList-module_operations_UBfDO button:focus {
    outline: none
}

.DocList-module_actions_XSfNO>* {
    margin-left: 8px
}

@media only screen and (min-width: 768px) {
    .DocList-module_actions_XSfNO.active {
        display:flex;
        align-items: center
    }
}

.DocList-module_overlay_-gNHi {
    font-size: 16px
}

.DocList-module_overlay_-gNHi .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-secondary)
}

.DocList-module_overlay_-gNHi .ant-dropdown-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.DocList-module_overlay_-gNHi .DocList-module_icon_tCT3V {
    margin-right: 8px;
    color: var(--yq-text-caption)
}

.DocList-module_right_kBzW5 {
    margin-left: 16px
}

.DocList-module_right_kBzW5 span {
    margin-left: 8px
}

.DocList-module_right_kBzW5 .larkicon {
    color: var(--yq-text-caption)
}

.DocList-module_content_wwOga {
    margin-top: 0
}

.MemberList-module_content_7PyCz {
    display: flex;
    margin-bottom: 16px;
    border: 1px solid var(--yq-border-primary);
    border-radius: 6px;
    padding: 16px;
    background-color: var(--yq-bg-primary);
    font-size: 14px;
    color: var(--yq-text-disable);
    align-items: center;
    justify-content: center;
    flex-wrap: wrap
}

.MemberList-module_content_7PyCz .MemberList-module_member_R2WR0 {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start
}

.MemberList-module_content_7PyCz .MemberList-module_memberOnce_Spbnf {
    flex: 1;
    display: grid;
    grid-template-columns: 30% 30% 30%;
    grid-row-gap: 25px;
    row-gap: 25px;
    grid-column-gap: 15px;
    -moz-column-gap: 15px;
    column-gap: 15px
}

.MemberList-module_content_7PyCz .MemberList-module_memberOnce_Spbnf .MemberList-module_userContainer_j7VQ4 {
    margin-right: 0
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 44px
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4:last-child {
    margin-right: 0
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_avatarContainer_-yw-a {
    display: flex;
    flex-direction: column;
    align-items: center
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_avatarContainer_-yw-a .MemberList-module_numContainer_InqGb {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--yq-bg-tertiary);
    border: 1px solid var(--yq-border-light);
    height: 24px;
    border-radius: 12px;
    padding: 0 8px;
    position: relative;
    top: -4px;
    font-size: 12px;
    font-weight: 700;
    min-width: 52px
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_avatarContainer_-yw-a .MemberList-module_numContainer_InqGb span {
    margin-left: 8px
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_avatarContainer_-yw-a .MemberList-module_numContainer_InqGb span:first-child {
    margin-left: 0
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 a {
    color: var(--yq-text-body)
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_userName_FHIKs {
    margin-top: 4px;
    line-height: 22px;
    width: 70px;
    text-align: center;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.MemberList-module_content_7PyCz .MemberList-module_userContainer_j7VQ4 .MemberList-module_userName_FHIKs .MemberList-module_a_L8f4X {
    max-width: 70px
}

.LinkList-module_list_q4p5R {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    width: 100%
}

.LinkList-module_listHorizontal_TGfqL {
    padding-right: 16px
}

.LinkList-module_once_vrtQo {
    width: 50%
}

.LinkList-module_twice_uL6O2 {
    width: 33.33333%
}

.LinkList-module_thrice_PDbm- {
    width: 20%
}

.LinkList-module_itemContainer_zFewJ {
    width: 100%
}

.LinkList-module_listItem_bFqea {
    flex: 1;
    display: flex;
    box-sizing: border-box;
    margin-right: 8px;
    width: 100%;
    position: relative;
    align-items: center;
    height: 40px;
    padding-right: 4px;
    color: var(--yq-text-body)
}

.LinkList-module_listItem_bFqea .LinkList-module_optIconContainer_rupId {
    padding: 2px;
    margin-right: 2px;
    margin-left: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center
}

.LinkList-module_listItem_bFqea .LinkList-module_optIconContainer_rupId:hover {
    background-color: var(--yq-bg-primary-hover-light)
}

.LinkList-module_listItem_bFqea .LinkList-module_itemTailOpt_b3-4n {
    display: flex
}

.LinkList-module_listItem_bFqea:hover {
    color: var(--yq-text-caption);
    background-color: var(--yq-bg-tertiary)
}

.LinkList-module_listItem_bFqea:hover .LinkList-module_optIcon_wqQG6 {
    display: block
}

.LinkList-module_listItem_bFqea .LinkList-module_optIcon_wqQG6 {
    display: none
}

.LinkList-module_listItem_bFqea .LinkList-module_icon_EyLyT {
    float: left;
    border-radius: 100%;
    margin-right: 8px
}

.LinkList-module_listItem_bFqea .LinkList-module_icon_EyLyT .larkui-icon-link {
    color: var(--yq-text-link);
    width: 24px;
    height: 24px
}

.LinkList-module_listItem_bFqea .LinkList-module_icon_EyLyT svg {
    display: block;
    width: 20px;
    height: 20px
}

.LinkList-module_listItem_bFqea .LinkList-module_icon_EyLyT img {
    width: 20px;
    height: 20px;
    border-radius: 100%
}

.LinkList-module_text_tnY8\+ {
    flex: 1;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.LinkList-module_actions_DncGu {
    flex-grow: 1;
    display: none
}

.LinkList-module_actions_DncGu .LinkList-module_operations_E05AY {
    height: 26px;
    border-radius: 13px;
    border: 1px solid var(--yq-border-primary);
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-bg-tertiary)
}

.LinkList-module_actions_DncGu .LinkList-module_operations_E05AY .larkicon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-right: 8px
}

.LinkList-module_actions_DncGu .LinkList-module_operations_E05AY .LinkList-module_optBtn_qzErW {
    border: none;
    padding: 0;
    cursor: pointer;
    background: var(--yq-bg-tertiary);
    color: var(--yq-text-caption)
}

.LinkList-module_actions_DncGu .LinkList-module_operations_E05AY .LinkList-module_optBtn_qzErW:focus {
    outline: none
}

.LinkList-module_actions_DncGu>* {
    margin-left: 8px
}

.QuickLink-module_content_1LXkJ {
    padding: 16px 0;
    background-color: var(--yq-bg-primary);
    display: flex;
    margin-top: 0
}

.QuickLink-module_content_1LXkJ .QuickLink-module_empty_guBvu {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--yq-text-caption);
    min-height: 48px
}

.QuickLink-module_content_1LXkJ .QuickLink-module_empty_guBvu a {
    margin-left: 4px
}

.QuickLink-module_list_CQOHW {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    width: 100%
}

.QuickLink-module_once_M0eUt {
    width: 50%
}

.QuickLink-module_twice_Wg9Dx {
    width: 33.33333%
}

.QuickLink-module_thrice_av14a {
    width: 20%
}

.QuickLink-module_listItem_eX51z {
    flex: 1;
    display: flex;
    box-sizing: border-box;
    margin-right: 8px;
    width: 100%;
    position: relative;
    align-items: center;
    height: 40px;
    padding-right: 4px
}

.QuickLink-module_listItem_eX51z .QuickLink-module_optIconContainer_Gjfbd {
    padding: 2px;
    margin-right: 2px;
    margin-left: 2px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center
}

.QuickLink-module_listItem_eX51z .QuickLink-module_optIconContainer_Gjfbd:hover {
    background-color: var(--yq-bg-primary-hover-light)
}

.QuickLink-module_listItem_eX51z:hover {
    background-color: var(--yq-bg-tertiary)
}

.QuickLink-module_listItem_eX51z:hover .QuickLink-module_optIcon_009zs {
    display: block
}

.QuickLink-module_listItem_eX51z .QuickLink-module_optIcon_009zs {
    display: none
}

.QuickLink-module_listItem_eX51z .QuickLink-module_item_P0Mr- {
    flex: 1;
    width: 60%
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M {
    color: var(--yq-text-body)
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M .QuickLink-module_text_-Lz-m {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M .QuickLink-module_icon_a7zJn {
    float: left;
    border-radius: 100%;
    margin-right: 8px
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M .QuickLink-module_icon_a7zJn .larkui-icon-link {
    color: var(--yq-text-link);
    width: 24px;
    height: 24px
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M .QuickLink-module_icon_a7zJn svg {
    display: block;
    width: 20px;
    height: 20px
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M .QuickLink-module_icon_a7zJn img {
    width: 20px;
    height: 20px;
    border-radius: 100%
}

.QuickLink-module_listItem_eX51z .QuickLink-module_linkTitle_xK19M:hover {
    color: var(--yq-text-caption)
}

.QuickLink-module_listItem_eX51z a {
    color: var(--yq-text-body)
}

.QuickLink-module_text-thrice_LI5Hl {
    width: 140px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_textHover-thrice_\+Tp4x {
    width: 90px
}

.QuickLink-module_text-twice_0T6Ke {
    width: 160px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_textHover-twice_5wKpq {
    width: 110px
}

.QuickLink-module_text-once_cMyQ8 {
    width: 100px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_textHover-once_QTbik {
    width: 50px
}

.QuickLink-module_onceVertical_UNI2x {
    width: 250px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_onceVerticalHover_kMO2u {
    width: 200px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.QuickLink-module_actions_tKZJ\+ {
    flex-grow: 1;
    display: none
}

.QuickLink-module_actions_tKZJ\+ .QuickLink-module_operations_Ztk0e {
    height: 26px;
    border-radius: 13px;
    border: 1px solid var(--yq-border-primary);
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--yq-bg-tertiary)
}

.QuickLink-module_actions_tKZJ\+ .QuickLink-module_operations_Ztk0e .larkicon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-right: 8px
}

.QuickLink-module_actions_tKZJ\+ .QuickLink-module_operations_Ztk0e .QuickLink-module_optBtn_8lJ7d {
    border: none;
    padding: 0;
    cursor: pointer;
    background: var(--yq-bg-tertiary);
    color: var(--yq-text-caption)
}

.QuickLink-module_actions_tKZJ\+ .QuickLink-module_operations_Ztk0e .QuickLink-module_optBtn_8lJ7d:focus {
    outline: none
}

.QuickLink-module_actions_tKZJ\+>* {
    margin-left: 8px
}

@media only screen and (min-width: 768px) {
    .QuickLink-module_actions_tKZJ\+.active {
        display:flex;
        align-items: center
    }
}

.QuickLink-module_bar_ocI4F {
    flex-grow: 1;
    background: var(--yq-border-primary);
    height: 1px
}

.QuickLink-module_overlay_xdi8- {
    font-size: 16px
}

.QuickLink-module_overlay_xdi8- .ant-dropdown-menu-item-selected {
    background-color: var(--yq-bg-secondary)
}

.QuickLink-module_overlay_xdi8- .ant-dropdown-menu-item-selected:hover {
    background-color: var(--yq-bg-primary-hover)
}

.QuickLink-module_overlay_xdi8- .QuickLink-module_icon_a7zJn {
    width: 24px;
    font-size: 16px;
    margin-right: 8px;
    color: var(--yq-text-caption)
}

.GroupHomeV3-module_layout_r0RbW {
    display: flex;
    margin-top: 16px;
    flex-wrap: wrap
}

.GroupHomeV3-module_layout_r0RbW:first-child {
    margin-top: 0
}

.GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_placement_reijc {
    min-width: 0;
    margin-left: 16px
}

.GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_placement_reijc:first-child {
    margin-left: 0
}

.GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_once_XzqOe {
    width: 32.23%
}

.GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_block_oDtEW {
    padding-top: 32px
}

.GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_block_oDtEW:first-child {
    padding-top: 0
}

.GroupHomeV3-module_layoutH5_idBKX {
    display: flex;
    flex-direction: column
}

.GroupHomeV3-module_pageEmpty_BG3Wd {
    min-height: 304px;
    color: var(--yq-text-disable)
}

.GroupHomeV3-module_pageEmpty_BG3Wd,.GroupHomeV3-module_pageEmpty_BG3Wd .GroupHomeV3-module_emptyTip_HqOAj {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column
}

@media only screen and (max-width: 991px) {
    .GroupHomeV3-module_h5Container_3pgJa {
        max-width:100vw
    }

    .GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_placement_reijc {
        margin-left: 0;
        padding: 10px
    }

    .GroupHomeV3-module_layout_r0RbW .GroupHomeV3-module_block_oDtEW {
        padding-top: 0
    }
}

.OrgWiki-module_mytable_umKHn {
    margin-bottom: 48px
}

.OrgWiki-module_mytable_umKHn .ant-card-head {
    padding: 0 16px
}

.OrgWiki-module_mytable_umKHn .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_mytable_umKHn .ant-card-head .ant-card-head-title {
    font-size: 18px;
    color: var(--yq-text-primary);
    font-weight: 500
}

.OrgWiki-module_mytable_umKHn .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.OrgWiki-module_mytable_umKHn .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.OrgWiki-module_mytable_umKHn .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.OrgWiki-module_mytable_umKHn .ant-card-body {
    position: relative;
    padding: 0
}

.OrgWiki-module_mytable_umKHn .ant-table {
    font-size: 14px;
    color: var(--yq-text-body)
}

.OrgWiki-module_mytable_umKHn .ant-table a {
    color: var(--yq-text-body)
}

.OrgWiki-module_mytable_umKHn .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.OrgWiki-module_mytable_umKHn .ant-table-thead>tr:first-child>th:first-child,.OrgWiki-module_mytable_umKHn .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.OrgWiki-module_mytable_umKHn .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.OrgWiki-module_mytable_umKHn .ant-table-tbody>tr.ant-table-placeholder>td {
    border-bottom: none
}

.OrgWiki-module_mytable_umKHn .ant-table-tbody tr td {
    color: var(--yq-text-body);
    padding: 16px;
    padding-left: 12px
}

.OrgWiki-module_mytable_umKHn .ant-table-footer {
    padding: 0;
    border-top: 0;
    background: none
}

.OrgWiki-module_mytable_umKHn .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-extra .ant-btn-group {
    margin-left: 8px;
    display: inline-block;
    vertical-align: top
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-extra .ant-btn-group .ant-btn {
    margin-left: 0
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-extra .ant-btn-group .ant-btn+.ant-btn {
    margin-left: -1px;
    padding-left: 8px;
    padding-right: 8px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_activeRow_sXOQl {
    background: var(--yq-yuque-green-1)
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U {
    width: 284px;
    white-space: nowrap;
    max-width: 284px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U a,.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U strong {
    color: var(--yq-text-primary);
    font-weight: 400;
    vertical-align: middle
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .book-icon,.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .doc-icon,.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .OrgWiki-module_titleIcon_C\+Fw7 {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .doc-link {
    display: block
}

.OrgWiki-module_mytable_umKHn .ant-table .OrgWiki-module_columnsTitle_7Ah7U .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400;
    line-height: 24px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsTitleLabel_nAGJS {
    margin-left: 12px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsBelong_l7Cei {
    font-size: 14px;
    width: 150px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsDesc_nqimn {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsDesc_nqimn.OrgWiki-module_mini_dc11y {
    max-width: 200px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsTime_6SQy\+ {
    width: 100px;
    max-width: 120px;
    white-space: nowrap;
    text-align: left
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsAction_amLhw {
    width: 100px;
    max-width: 160px;
    white-space: nowrap
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsAction_amLhw.ant-table-cell a {
    color: var(--yq-text-link);
    font-size: 14px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsAction_amLhw .ant-btn {
    border-radius: 4px
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_emptyView_Y3AK- {
    margin: 0 auto;
    width: 152px;
    padding: 160px 16px;
    font-size: 14px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_emptyView_Y3AK- .OrgWiki-module_emptyViewImg_fSmzu {
    margin: 0 auto 16px;
    width: 120px;
    background-size: 100%;
    background-position: 50%;
    background-repeat: no-repeat
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_emptyView_Y3AK- a {
    color: var(--yq-text-link)
}

.OrgWiki-module_mytable_umKHn .OrgWiki-module_emptyView_Y3AK- a:hover {
    color: var(--yq-ant-link-hover-color)
}

.dashboard-dropdown-menu {
    max-height: 500px;
    overflow: auto
}

.dashboard-dropdown-menu .ant-dropdown-menu-item {
    padding: 0 20px;
    line-height: 40px;
    min-width: 120px
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a {
    padding: 0;
    margin: 0
}

.dashboard-dropdown-menu .ant-dropdown-menu-item a:hover {
    color: var(--yq-text-primary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected {
    background: var(--yq-bg-tertiary)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-selected a {
    background: transparent;
    color: var(--yq-text-body)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active {
    background: var(--yq-yuque-green-1)
}

.dashboard-dropdown-menu .ant-dropdown-menu-item-active a {
    background: transparent;
    color: var(--yq-text-body)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head {
    padding: 0 16px
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head .larkicon-arrow-down {
    margin-left: 4px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head .ant-card-head-title,.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head .ant-card-head-title h4 {
    font-size: 16px;
    color: var(--yq-text-primary)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head .ant-input-affix-wrapper {
    width: 190px;
    border-color: var(--yq-border-primary)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-head .ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
    border-color: var(--yq-theme)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card-body {
    position: relative;
    padding: 0
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table {
    font-size: 12px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table a {
    color: var(--yq-text-caption)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-thead tr>th {
    border-bottom: 1px solid var(--yq-border-primary);
    border-top: 1px solid var(--yq-border-primary);
    color: var(--yq-text-caption);
    font-size: 14px;
    font-weight: 400;
    border-radius: 0
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-thead>tr:first-child>th:first-child,.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-thead>tr:first-child>th:last-child {
    border-radius: 0
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-thead .larkicon-arrow-down {
    margin-left: 4px
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-tbody>tr:not(.ant-table-placeholder):hover>td {
    background: none
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-tbody tr td {
    color: var(--yq-text-caption);
    padding-top: 14px;
    padding-bottom: 14px
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-footer {
    padding: 0;
    border-top: 0
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-table-empty .ant-table-placeholder {
    border-bottom: 0;
    min-height: 49px;
    color: var(--yq-text-caption)
}

.OrgWiki-module_noPaddingTable_Xm-Op .ant-card .ant-card-extra .ant-btn {
    vertical-align: top
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_activeRow_sXOQl {
    background: var(--yq-yuque-green-1)
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U {
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--yq-text-primary)
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U.OrgWiki-module_mini_dc11y {
    max-width: 300px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U a,.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U strong {
    color: var(--yq-text-primary);
    font-weight: 400
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U .book-icon,.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U .doc-icon,.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U .OrgWiki-module_titleIcon_C\+Fw7 {
    display: inline-block;
    background-size: 100%;
    width: 24px;
    vertical-align: middle;
    height: 24px;
    margin-right: 8px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U .avatar {
    margin-right: 8px;
    vertical-align: middle;
    width: 20px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitle_7Ah7U .doc-title {
    color: var(--yq-text-primary);
    font-weight: 400
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTitleLabel_nAGJS {
    margin-left: 12px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsBelong_l7Cei {
    font-size: 14px;
    width: 160px;
    max-width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsDesc_nqimn {
    width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsDesc_nqimn.OrgWiki-module_mini_dc11y {
    max-width: 200px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsTime_6SQy\+ {
    width: 100px;
    max-width: 100px;
    white-space: nowrap;
    text-align: left
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsAction_amLhw {
    width: 160px;
    max-width: 160px;
    text-align: right;
    white-space: nowrap
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsAction_amLhw a {
    color: var(--yq-text-link);
    font-size: 14px
}

.OrgWiki-module_noPaddingTable_Xm-Op .OrgWiki-module_columnsAction_amLhw .ant-btn {
    border-radius: 4px
}

@media only screen and (max-width: 1200px) {
    .OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsDesc_nqimn,.OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsTime_6SQy\+ {
        display:none
    }
}

@media only screen and (max-width: 992px) {
    .OrgWiki-module_mytable_umKHn .OrgWiki-module_columnsBelong_l7Cei {
        display:none
    }
}

.OrgWiki-module_mytable_umKHn .ant-card {
    border: none
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-head {
    padding: 0;
    border-bottom: none;
    margin-top: -10px
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-head .ant-input-affix-wrapper {
    width: 200px;
    padding: 5.5px 12px;
    background: var(--yq-bg-secondary);
    border: none;
    outline: none;
    box-shadow: none
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-head .ant-input-affix-wrapper .larkui-icon-help-search {
    color: var(--yq-text-body)
}

.OrgWiki-module_mytable_umKHn .ant-card .ant-card-head .ant-input-affix-wrapper .ant-input {
    background: var(--yq-bg-secondary)
}

.OrgWiki-module_mytable_umKHn .ant-table-thead>tr>th {
    background: none;
    border-top: none;
    border-color: var(--yq-border-light)
}

.OrgWiki-module_mytable_umKHn .ant-table-tbody>tr>td {
    border-color: var(--yq-border-light)
}

.OrgWiki-module_mytable_umKHn .ant-table-tbody>tr.ant-table-row-selected>td,.OrgWiki-module_mytable_umKHn td.ant-table-column-sort {
    background: none
}

.OrgWiki-module_mytable_umKHn .ant-table-column-sorters {
    padding: 16px 16px 16px 12px
}

.OrgWiki-module_mytable_umKHn .ant-table-column-sorters>span {
    color: var(--yq-text-caption)
}

.OrgWiki-module_mytable_umKHn .ant-table-thead th.ant-table-column-has-sorters {
    padding: 8px 0
}

.OrgWiki-module_mytable_umKHn .ant-table-thead th.ant-table-column-has-sorters:hover {
    background: none
}

@media only screen and (max-width: 575px) {
    .OrgWiki-module_mytable_umKHn .ant-card-extra .ant-btn,.OrgWiki-module_mytable_umKHn .ant-card-head-title {
        display:none
    }

    .OrgWiki-module_mytable_umKHn .ant-card-extra {
        float: none;
        flex: auto
    }

    .OrgWiki-module_mytable_umKHn .ant-card .ant-card-head .ant-input-affix-wrapper {
        width: 100%
    }
}

@media only screen and (max-width: 480px) {
    .OrgWiki-module_wrapper_rORmD {
        padding:0
    }
}

.OrgWiki-module_wrapper_rORmD {
    max-width: 1920px;
    min-width: 1080px;
    padding: 24px 36px
}

.OrgWiki-module_loadingContainer_8P5eJ {
    min-width: 1080px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 70vh
}

.OrgWiki-module_title_KCGfr {
    font-size: 18px;
    font-weight: 600;
    color: var(--yq-text-primary)
}

.OrgWiki-module_header_YHdB4 {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.OrgWiki-module_header_YHdB4 .OrgWiki-module_setHomePageBtnWrapper_ob0lf {
    display: flex;
    align-items: center;
    color: var(--yq-yuque-grey-9)
}

.OrgWiki-module_header_YHdB4 .OrgWiki-module_setHomePageBtn_nffbU {
    font-weight: 500;
    color: var(--yq-yuque-grey-9);
    margin-left: 4px
}

.OrgWiki-module_label_9f-e- {
    margin-left: 16px;
    border-radius: 6px;
    background-color: var(--yq-bg-tertiary);
    font-size: 12px;
    padding: 5px;
    display: inline-flex;
    line-height: 12px;
    height: 22px;
    cursor: pointer
}

.OrgWiki-module_label_9f-e- span {
    margin-left: 4px
}

.OrgWiki-module_more_0kRSp {
    color: var(--yq-ant-link-hover-color)
}

@media only screen and (max-width: 991px) {
    .OrgWiki-module_wrapper_rORmD {
        max-width:100%;
        min-width: 100%;
        padding: 0
    }

    .OrgWiki-module_header_YHdB4 {
        display: none
    }
}

@media only screen and (max-width: 576px) {
    .OrgWiki-module_wrapper_rORmD {
        max-width:100vw;
        min-width: 100vw;
        padding: 0
    }

    .OrgWiki-module_header_YHdB4 {
        display: none
    }
}
