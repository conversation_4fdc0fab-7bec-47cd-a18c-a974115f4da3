<template>
  <div class="designView">
    <div class="ruleBody">
      <!-- 根据 activeShow 属性决定显示 ActionList 还是 ConditionList -->
      <ActionList
        v-if="activeShow === 'action'"
        v-bind="actionProps"
        @addActionItem="addActionItem"
        @deleteActionItem="deleteActionItem"
        @logicBtnClick="onLogicBtnClick"
        @actionComChange="actionComChange"
        @replaceItem="replaceItem"
      />
      <ConditionList
        v-else
        v-bind="conditionProps"
        @addRule="addRule"
        @addUp="addUp"
        @addChildCondition="addChildCondition"
        @addTailItem="addTailItem"
        @decreaseRule="decreaseRule"
        @logicBtnClick="onLogicBtnClick"
        @conditionChange="onConditionChange"
        @conditionInit="conditionInit"
        @replaceItem="replaceItem"
      />
    </div>
  </div>
</template>

<script setup>
import ConditionList from "@/components/ruleEditCom/condition/conditionList.vue";
import ActionList from "@/components/ruleEditCom/action/actionList.vue";

// 定义props
const props = defineProps({
  ruleData: {
    type: Object,
    default: () => ({}),
  },
  locked: Boolean,
  isTrack: Boolean,
  validateResult: {
    type: Object,
    default: () => ({}),
  },
  toAnalysis: {
    type: Function,
  },
  viewStatus: {
    type: String,
    default: "",
  },
  activeShow: {
    type: String,
    default: "",
  },
});

const emit = defineEmits([
  "addRule",
  "addUp",
  "addChildCondition",
  "addTailItem",
  "replaceItem",
  "decreaseRule",
  "switcherChange",
  "logicBtnClick",
  "conditionChange",
  "conditionInit",
  "addActionItem",
  "deleteActionItem",
  "actionComChange",
]);
const conditionProps = ref({});
const actionProps = ref({});

// 定义 setProps 方法
const setProps = (newValue) => {
  // 解构 props
  const { ruleData = {}, isTrack, validateResult = {} } = toRefs(props);
  // 解构 ruleData 和 validateResult
  const { conditionData, actionData } = ruleData.value;
  const {
    conditionValids: conditionValidList = [],
    actionValids: actionValidList = [],
  } = validateResult.value;

  // 定义条件列表的属性
  conditionProps.value = {
    pos: "r",
    conditionData,
    validList: conditionValidList,
    locked: false,
    isTrack: isTrack.value,
  };
  // 定义 actionProps 和 actionMethods
  actionProps.value = {
    pos: "r",
    actionData,
    validList: actionValidList,
    locked: false,
    isTrack: isTrack.value,
  };
};

// 监听 ruleData 变化，当 ruleData 发生变化时获取数据
watch(
  () => props,
  (newValue) => {
    setProps(newValue); // 调用 setProps 方法获取数据
  },
  { deep: true, immediate: true } // 深度监听
);

// 定义方法
const addRule = (pos, conditionId, layer) => {
  emit("addRule", pos, conditionId, layer);
};

const addUp = (pos, conditionId, layer) => {
  emit("addUp", pos, conditionId, layer);
};

const addChildCondition = (pos, conditionId, layer) => {
  emit("addChildCondition", pos, conditionId, layer);
};

const addTailItem = (pos, conditionId) => {
  emit("addTailItem", pos, conditionId);
};

const decreaseRule = ({ pos, conditionId, layer }) => {
  emit("decreaseRule",{ pos, conditionId, layer });
};

const onLogicBtnClick = (pos, logicalSymbol) => {
  emit("onLogicBtnClick", pos, logicalSymbol);
};

const onConditionChange = (pos, newContents) => {
  emit("conditionChange", pos, newContents);
};

const conditionInit = () => {
  emit("conditionInit");
};
const addActionItem = (flag, pos) => {
  emit("addActionItem",flag, pos);
};
const deleteActionItem = (flag, pos) => {
  emit("deleteActionItem",flag, pos);
};
const actionComChange = (flag, pos, data, oldData) => {
  emit("actionComChange",flag, pos, data, oldData);
};

const replaceItem = (pos, conditionId, layer) => {
  emit("replaceItem", pos, conditionId, layer);
};
</script>
