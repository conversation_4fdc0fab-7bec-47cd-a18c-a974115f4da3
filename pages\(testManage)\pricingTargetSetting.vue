<template>
  <div class="pricing-target-setting">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <a-form :model="formData" layout="horizontal">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item required label="发布环境">
                <a-select v-model:value="formData.publishEnvir" placeholder="请选择发布环境" @change="handlePublishEnvChange">
                  <a-select-option v-for="item in publishEnvir" :key="item.id" :value="item.id">
                    {{ item.environmentName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <template v-for="(item, index) in formItems" :key="index">
              <a-col :span="6">
                <a-form-item :label="item.name">
                  <!-- 根据inputType确定输入框类型 -->
                  <!-- 输入框类型 -->
                  <a-input
                    v-if="item.inputType === '1'"
                    v-model:value="formData[item.key]"
                    :placeholder="`请输入${item.name}`"
                  />

                  <!-- 下拉框类型 -->
                  <a-select
                    v-else-if="item.inputType === '2'"
                    v-model:value="formData[item.key]"
                    :placeholder="`请选择${item.name}`"
                    style="width: 100%"
                    :allow-clear="true"
                  >
                    <a-select-option
                      v-for="option in selectOptions[item.key]"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>

                  <!-- 多选下拉框类型 -->
                  <a-select
                    v-else-if="item.inputType === '3'"
                    v-model:value="formData[item.key]"
                    :placeholder="`请选择${item.name}`"
                    mode="multiple"
                    style="width: 100%"
                    :allow-clear="true"
                  >
                    <a-select-option
                      v-for="option in selectOptions[item.key]"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>

                  <!-- 默认为输入框 -->
                  <a-input
                    v-else
                    v-model:value="formData[item.key]"
                    :placeholder="`请输入${item.name}`"
                  />
                </a-form-item>
              </a-col>
              <!-- 每四个字段换行 -->
              <a-col :span="24" v-if="(index + 1) % 4 === 0 && index !== formItems.length - 1" />
            </template>
          </a-row>
        </a-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button type="primary" @click="getPricingTarget">
          <template #icon><SearchOutlined /></template>
          搜索
        </a-button>
        <a-button type="primary" @click="handleSave(saveType='1')" class="ml-16">
          <template #icon><SaveOutlined /></template>
          保存定价信息
        </a-button>
        <a-button @click="handleSave(saveType='2')" class="ml-16">
          <template #icon><CheckCircleOutlined /></template>
          生效
        </a-button>
        <a-button @click="handleReset" class="ml-16">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
      </div>

      <!-- 设置利润率 -->
      <div class="profit-rate-section">
        <h3>设置利润率</h3>
        <a-table
          :dataSource="profitRateData"
          :columns="profitRateColumns"
          :pagination="false"
          bordered
          size="middle"
          class="custom-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex && typeof column.dataIndex === 'string' && column.dataIndex.startsWith('rate')">
              <div class="rate-input-wrapper">
                <a-input
                  v-model:value="record[column.dataIndex]"
                  class="rate-input"
                  @input="(e) => validateNumberInput(e, record, column.dataIndex, 0, 100)"
                />
                <span class="rate-suffix">%</span>
              </div>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 费用率类型 -->
      <div class="expense-rate-section">
        <h3>费用率类型</h3>

        <!-- 固定费用率部分 -->
        <div class="expense-card fixed-expense">
          <div class="card-header">固定费用率</div>
          <div class="expense-total">
            <span class="label" style="font-weight: bold;">固定费用率</span>
            <span class="value" style="text-align: center; width: 100px; display: inline-block;">{{ calculateFixedExpenseTotal() }} %</span>
          </div>
          <div class="expense-items">
            <a-row :gutter="24">
              <a-col :span="8" v-for="(item, index) in fixedExpenseItems" :key="index">
                <div class="expense-item">
                  <span class="label">{{ item.label }}</span>
                  <div class="input-wrapper">
                    <a-input
                      v-model:value="fixedExpenseForm[item.key]"
                      class="expense-input"
                      @input="(e) => validateNumberInput(e, fixedExpenseForm, item.key, Number(item.minLowerLimit), Number(item.maxUpperLimit))"
                    />
                    <span class="suffix">%</span>
                  </div>
                  <span class="limit-info">({{ item.minLowerLimit }}% - {{ item.maxUpperLimit }}%)</span>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 变动费用率部分 -->
        <div class="expense-card variable-expense" style="margin-top: 24px;">
          <div class="card-header">变动费用率</div>
          <div class="expense-total">
            <span class="label" style="font-weight: bold;">变动费用率</span>
            <span class="value" style="text-align: center; width: 100px; display: inline-block;">{{ calculateVariableExpenseTotal() }} %</span>
          </div>
          <div class="expense-items">
            <a-row :gutter="24">
              <a-col :span="8" v-for="(item, index) in variableExpenseItems" :key="index">
                <div class="expense-item">
                  <span class="label">{{ item.label }}</span>
                  <div class="input-wrapper">
                    <a-input
                      v-model:value="variableExpenseForm[item.key]"
                      class="expense-input"
                      @input="(e) => validateNumberInput(e, variableExpenseForm, item.key, Number(item.minLowerLimit), Number(item.maxUpperLimit))"
                    />
                    <span class="suffix">%</span>
                  </div>
                  <span class="limit-info">({{ item.minLowerLimit }}% - {{ item.maxUpperLimit }}%)</span>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { SelectValue } from 'ant-design-vue/es/select'
import { getPublishEnvir } from "@/api/pub_environment"
import { loginUserInfo } from "@/api/userManagement"
import { getSearchDictionaryByEnvironment, getDictionary, saveFixPrice } from '@/api/fixPrice'
import { dictionParentsel } from "@/api/diction_entry";

definePageMeta({
  title: '定价目标设置'
})
const message = inject('message')
const publishEnvir = ref([])
const loading = ref(false)

const formItems = ref([])
const formData = ref({
  publishEnvir: ''
})
const loginId = ref('');
const profitRateData = ref([{
  key: '1',
  segment: '利润率',
  rate1: 0,
  rate2: 0,
  rate3: 0,
  rate4: 0,
  rate5: 0,
  rate6: 0,
  rate7: 0,
  rate8: 0,
  rate9: 0,
  rate10: 0,
  updater: loginId.value
}])

const profitRateColumns = [
  {
    title: '模型评分等级',
    dataIndex: 'segment',
    width: 120,
    fixed: 'left'
  },
  ...Array.from({ length: 10 }, (_, i) => ({
    title: `等级${i + 1}`,
    dataIndex: 'rate' + (i + 1),
    width: 100
  })),
  {
    title: '更新人',
    dataIndex: 'updater',
    width: 100
  }
]

const fixedExpenseItems = ref([])
const variableExpenseItems = ref([])

const fixedExpenseForm = ref({})
const variableExpenseForm = ref({})

// 存储每个下拉字段的选项
const selectOptions = ref<Record<string, any[]>>({})

// 根据发布环境获取搜索字段
const searchParams = async () => {
  try {
    const queryParams = {
      environment: formData.value.publishEnvir
    }
    await getSearchDictionaryByEnvironment(queryParams)
      .then((res) => {
        if (res?.data) {
          const dictionaryData = res.data
          formItems.value = res.data
          // 初始化表单数据
          const initialFormData = {
            publishEnvir: formData.value.publishEnvir
          }
          formItems.value.forEach(item => {
            // 如果是多选下拉框，初始值为空数组
            if (item.inputType === '3') {
              initialFormData[item.key] = []
            } else {
              initialFormData[item.key] = ''
            }
            // 如果是下拉框类型，获取选项数据
            if (item.inputType === '2' || item.inputType === '3') {
              getSelectParams(item.key)
            }
          })
          formData.value = initialFormData
          // 重置费用表单
          resetExpenseForms()
        }
      })
  } catch (error) {
    console.error('搜索查询参数失败:', error)
  }
}

// 重置费用表单
const resetExpenseForms = () => {
  // 重置固定费用表单
  const fixedExpenseFormData = {}
  fixedExpenseItems.value.forEach(item => {
    fixedExpenseFormData[item.key] = 0
  })
  fixedExpenseForm.value = fixedExpenseFormData

  // 重置变动费用表单
  const variableExpenseFormData = {}
  variableExpenseItems.value.forEach(item => {
    variableExpenseFormData[item.key] = 0
  })
  variableExpenseForm.value = variableExpenseFormData
}
// 获取定价目标上下限
const getPricingTarget = async () => {
  if (!formData.value.publishEnvir) {
    message.warning('请先选择发布环境')
    return
  }
  try {
    // 构建查询参数，包含所有表单字段，并确保所有值都是字符串类型
    // 过滤掉空值字段
    const queryParams = {
      releaseEnvironment: String(formData.value.publishEnvir),
      ...Object.fromEntries(
        formItems.value
          .map(item => [item.key, String(formData.value[item.key] || '')])
          .filter(([_, value]) => value !== '')
      )
    }
    // 将参数转换为JSON字符串
    const jsonParams = JSON.stringify(queryParams)
    const Params = {
      inputStr: jsonParams
    }
    const res = await getDictionary(Params)
    if (res?.data) {
      // 根据type区分固定费用率和变动费用率，并处理显示字段
      fixedExpenseItems.value = res.data
        .filter(item => item.type === '3')
        .map(item => ({
          label: item.name,
          key: item.key,
          maxUpperLimit: item.maxUpperLimit,
          minLowerLimit: item.minLowerLimit
        }))

      variableExpenseItems.value = res.data
        .filter(item => item.type === '4')
        .map(item => ({
          label: item.name,
          key: item.key,
          maxUpperLimit: item.maxUpperLimit,
          minLowerLimit: item.minLowerLimit
        }))
      loginId.value = res.data[0].loginId
      profitRateData.value.forEach(item => {
        item.updater = loginId.value
      })
      // 初始化费用表单数据
      resetExpenseForms()
    }
  } catch (error) {
    console.error('获取定价目标失败:', error)
  } finally {
    loading.value = false
  }
}
// 保存定价信息
const handleSave = async (saveType) => {
  //获取登录用户
  const res1 = await loginUserInfo()
  if (res1?.data){
    loginId.value = res1.data.loginId
  }
  try {
    // 构建groupId参数
    const groupId = {
      releaseEnvironment: String(formData.value.publishEnvir),
      ...Object.fromEntries(
        formItems.value
          .map(item => [item.key, String(formData.value[item.key] || '')])
          .filter(([_, value]) => value !== '')
      )
    }

    // 构建params数组
    const params = [
      // 添加固定费用率参数
      ...fixedExpenseItems.value.map(item => ({
        [item.key]: String(fixedExpenseForm.value[item.key] || 0),
        type: '3'
      })),
      // 添加变动费用率参数
      ...variableExpenseItems.value.map(item => ({
        [item.key]: String(variableExpenseForm.value[item.key] || 0),
        type: '4'
      })),
      // 添加查询参数
      ...formItems.value.map(item => ({
        [item.key]: String(formData.value[item.key] || ''),
        type: '1'
      })).filter(item => {
        const key = Object.keys(item).find(k => k !== 'type');
        return key && item[key] !== '';
      }),
      // 添加1-10等级参数
      ...profitRateData.value.flatMap(record =>
        Array.from({ length: 10 }, (_, i) => ({
          ['section'+`${i + 1}`]: String(record[`rate${i + 1}`] || 0),
          type: '2'
        }))
      )
    ]

    // 构建完整的保存参数
    const saveParams = {
      saveType: saveType,
      releaseEnvironment: String(formData.value.publishEnvir),
      groupId: groupId,
      params: params,
      loginId:loginId.value
    }
    // 将参数转换为JSON字符串
    const jsonParams = encodeURIComponent(JSON.stringify(saveParams))
    const res = await saveFixPrice(jsonParams)
    if (res?.data) {
      message.success(res.data)
    }
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}

// 重置表单
const handleReset = () => {
  const initialFormData = {
    publishEnvir: ''
  }
  formData.value = initialFormData

  // 清空费用表单
  fixedExpenseForm.value = {}
  variableExpenseForm.value = {}
  
  // 清空费用项
  fixedExpenseItems.value = []
  variableExpenseItems.value = []
  
  // 清空表单项
  formItems.value = []

  profitRateData.value = [{
    key: '1',
    segment: '利润率',
    rate1: 0,
    rate2: 0,
    rate3: 0,
    rate4: 0,
    rate5: 0,
    rate6: 0,
    rate7: 0,
    rate8: 0,
    rate9: 0,
    rate10: 0,
    updater: ''
  }]
  message.success('重置成功')
}

// 获取发布环境
const getEnvInfo = async () => {
  try {
    const res = await getPublishEnvir()
    if (res?.data) {
      publishEnvir.value = res.data
    }
  } catch (err) {
    console.error('获取发布环境失败:', err)
  }
}

// 处理发布环境变化
const handlePublishEnvChange = (value: SelectValue) => {
  if (value) {
    searchParams()
  }
}

// 验证数字输入
const validateNumberInput = (e: Event, form: any, key: string, min: number, max: number) => {
  const input = e.target as HTMLInputElement
  let value = input.value

  // 只允许输入数字和小数点
  value = value.replace(/[^\d.]/g, '')

  // 确保只有一个小数点
  const parts = value.split('.')
  if (parts.length > 2) {
    value = parts[0] + '.' + parts.slice(1).join('')
  }

  // 限制小数位数为2位
  if (parts.length === 2 && parts[1].length > 2) {
    value = parts[0] + '.' + parts[1].slice(0, 2)
  }

  // 检查是否超出范围
  const numValue = parseFloat(value)
  if (!isNaN(numValue)) {
    if (numValue < min || numValue > max) {
      message.warning(`输入值应在 ${min}% 到 ${max}% 之间`)
    }
  }

  // 更新表单值
  if (form === fixedExpenseForm.value) {
    fixedExpenseForm.value[key] = value;
  } else if (form === variableExpenseForm.value) {
    variableExpenseForm.value[key] = value;
  } else {
    form[key] = value;
  }
}

/**
 * 计算固定费用率总和
 */
const calculateFixedExpenseTotal = () => {
  let total = 0;
  // 遍历所有固定费用率项
  for (const key in fixedExpenseForm.value) {
    // 如果值存在且是有效数字，则加到总和中
    if (fixedExpenseForm.value[key] && !isNaN(parseFloat(fixedExpenseForm.value[key]))) {
      total += parseFloat(fixedExpenseForm.value[key]);
    }
  }
  // 保留两位小数
  return total.toFixed(2);
};

/**
 * 计算变动费用率总和
 */
const calculateVariableExpenseTotal = () => {
  let total = 0;
  // 遍历所有变动费用率项
  for (const key in variableExpenseForm.value) {
    // 如果值存在且是有效数字，则加到总和中
    if (variableExpenseForm.value[key] && !isNaN(parseFloat(variableExpenseForm.value[key]))) {
      total += parseFloat(variableExpenseForm.value[key]);
    }
  }
  // 保留两位小数
  return total.toFixed(2);
};
//获取查询参数字典
const getSelectParams = (parentCode: string) => {
  dictionParentsel({
    parentCode: parentCode, // 动态传入查询参数
    parentTypeCode: 'selectParams', // 定价查询参数
    typeCode: 'selectParamsSon', // 定价查询子项参数
  }).then((res) => {
    if (res.data) {
      // 存储下拉选项到对应字段
      selectOptions.value[parentCode] = res.data.map(item => ({
        label: item.name,
        value: item.code
      }))
    }
  });
};

// 初始化时获取发布环境
onMounted(() => {
  getEnvInfo()
})
</script>

<style lang="scss" scoped>
.pricing-target-setting {
  padding: 16px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .main-content {
    background: #fff;
    padding: 24px;
    border-radius: 4px;
  }

  .page-title {
    font-size: 14px;
    color: #1890ff;
    margin-bottom: 24px;
    font-weight: normal;
  }

  .form-section {
    padding: 24px;
    background: #f8f9fc;
    border-radius: 4px;
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;

      .ant-form-item-required {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }

      .ant-form-item-label {
        text-align: left;
        padding-right: 8px;
        min-width: 90px;

        label {
          color: #606266;
          font-size: 14px;
        }
      }

      .ant-radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
      }

      .ant-radio-wrapper {
        margin-right: 0;
        font-size: 14px;
        color: #606266;
        white-space: nowrap;
      }

      .ant-select {
        width: 100%;
      }

      .ant-input {
        &:hover, &:focus {
          border-color: #409EFF;
        }
      }

      .ant-select-focused {
        .ant-select-selector {
          border-color: #409EFF !important;
        }
      }
    }
  }

  .action-buttons {
    margin: 24px 0;
    text-align: left;

    .ml-16 {
      margin-left: 16px;
    }

    .ant-btn {
      margin: 0 8px;

      .anticon {
        margin-right: 4px;
      }
    }
  }

  .profit-rate-section {
    h3 {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;
      font-weight: 500;
    }

    .custom-table {
      :deep(.ant-table-thead > tr > th) {
        background: #f5f7fa;
        color: #606266;
        font-weight: 500;
        text-align: center;
        padding: 8px;
      }

      :deep(.ant-table-tbody > tr > td) {
        padding: 4px 8px;
      }

      .rate-input-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;

        .rate-input {
          width: 80px;
          text-align: right;
        }

        .rate-suffix {
          color: #606266;
          font-size: 14px;
          min-width: 12px;
        }
      }
    }
  }

  .expense-rate-section {
    margin-top: 24px;

    .expense-card {
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .card-header {
        padding: 12px 16px;
        background: #f2f4f6;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }

      .expense-total {
        padding: 16px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          color: #606266;
          font-size: 14px;
        }

        .value {
          color: #333;
          font-size: 14px;
          font-weight: bold;
        }
      }

      .expense-items {
        padding: 16px;

        .expense-item {
          display: flex;
          flex-direction: column;
          padding: 12px;
          margin-bottom: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          background-color: #fafafa;

          .label {
            color: #606266;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .input-wrapper {
            display: flex;
            align-items: center;
            width: 100%;
            margin-bottom: 4px;

            .expense-input {
              flex: 1;
              width: 100%;
              text-align: right;
            }

            .suffix {
              color: #909399;
              margin-left: 8px;
              font-size: 14px;
            }
          }

          .limit-info {
            color: #909399;
            font-size: 12px;
            align-self: flex-end;
          }
        }
      }
    }
  }
}
</style>


