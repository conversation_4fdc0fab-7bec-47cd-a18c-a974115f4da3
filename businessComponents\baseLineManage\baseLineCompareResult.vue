<template>
    <div>
        <!-- 搜索区域 -->
        <a-form class="baseline-form">
            <a-row>
                <a-col :span="8">
                    <a-form-item label="规则库名称 :">
                        <span>{{ snapComparedData_2.engChineseName }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="基线版本 ：">
                        <span>{{ "( 版本：" + curSnapEdition + "对比" + compareSnapEdition + " )" }}</span>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <a-tabs v-model:activeKey="activeName" @change="handleClick">
            <a-tab-pane key="update" tab="规则变更">
                <div ref="basePoint">
                    <!-- 骨架屏 -->
                    <TableSkeleton
                        v-if="loading"
                        :columns="columns"
                        :limit="pagination.limit"
                        :scrollY="scrollY-50"
                    />
                    <!-- 数据表格 -->
                    <a-table
                            v-else
                            :columns="columns"
                            :data-source="updateData"
                            :loading="loading"
                            :pagination="pagination"
                            size="small"
                            :scroll="{y: scrollY-50}"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'type'">
                                <span v-if="record.type === '1'">普通规则</span>
                                <span v-if="record.type === '2'">决策表</span>
                                <span v-if="record.type === '4'">决策树</span>
                                <span v-if="record.type === '5'">规则流</span>
                            </template>
                            <template v-if="column.key === 'packageNameAll'">
                                <RulePath
                                        :path="record.packageNameAll"
                                        showCopyButton
                                />
                            </template>

                            <template v-if="column.key === 'status'">
                                <span v-if="record.status === '0'">新增</span>
                                <span v-if="record.status === '1'">变更</span>
                                <span v-if="record.status === '2'">已删除</span>
                            </template>
                            <template v-if="column.key === 'action'">
                                <a @click="btn_details(record)">详情</a>
                            </template>
                        </template>
                    </a-table>
                </div>
            </a-tab-pane>
            <a-tab-pane key="add" tab="规则新增">
                <div ref="basePoint">
                    <!-- 骨架屏 -->
                    <TableSkeleton
                        v-if="loading"
                        :columns="columns"
                        :limit="pagination.limit"
                        :scrollY="scrollY-50"
                    />
                    <!-- 数据表格 -->
                    <a-table
                            v-else
                            :columns="columns"
                            :data-source="addData"
                            :loading="loading"
                            :pagination="pagination"
                            :scroll="{y: scrollY-50}"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'type'">
                                <span v-if="record.type === '1'">普通规则</span>
                                <span v-if="record.type === '2'">决策表</span>
                                <span v-if="record.type === '4'">决策树</span>
                                <span v-if="record.type === '5'">规则流</span>
                            </template>
                            <template v-if="column.key === 'status'">
                                <span v-if="record.status === '0'">新增</span>
                                <span v-if="record.status === '1'">变更</span>
                                <span v-if="record.status === '2'">已删除</span>
                            </template>
                            <template v-if="column.key === 'action'">
                                <a @click="btn_details(record)">详情</a>
                            </template>
                        </template>
                    </a-table>
                </div>
            </a-tab-pane>
            <a-tab-pane key="delete" tab="规则删除">
                <div ref="basePoint">
                    <!-- 骨架屏 -->
                    <TableSkeleton
                        v-if="loading"
                        :columns="columns"
                        :limit="pagination.limit"
                        :scrollY="scrollY-50"
                    />
                    <!-- 数据表格 -->
                    <a-table
                            v-else
                            :columns="columns"
                            :data-source="deleteData"
                            :loading="loading"
                            :pagination="pagination"
                            :scroll="{y: scrollY-50}"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'type'">
                                <span v-if="record.type === '1'">普通规则</span>
                                <span v-if="record.type === '2'">决策表</span>
                                <span v-if="record.type === '4'">决策树</span>
                                <span v-if="record.type === '5'">规则流</span>
                            </template>
                            <template v-if="column.key === 'status'">
                                <span v-if="record.status === '0'">新增</span>
                                <span v-if="record.status === '1'">变更</span>
                                <span v-if="record.status === '2'">已删除</span>
                            </template>
                            <template v-if="column.key === 'action'">
                                <a @click="btn_details(record)">详情</a>
                            </template>
                        </template>
                    </a-table>
                </div>
            </a-tab-pane>
            <a-tab-pane key="noChange" tab="规则未变">
                <div ref="basePoint">
                    <!-- 骨架屏 -->
                    <TableSkeleton
                        v-if="loading"
                        :columns="columns"
                        :limit="pagination.limit"
                        :scrollY="scrollY-50"
                    />
                    <!-- 数据表格 -->
                    <a-table
                            v-else
                            :columns="columns"
                            :data-source="noChangeRulesData"
                            :loading="loading"
                            :pagination="pagination"
                            :scroll="{y: scrollY-50}"
                    >
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'type'">
                                <span v-if="record.type === '1'">普通规则</span>
                                <span v-if="record.type === '2'">决策表</span>
                                <span v-if="record.type === '4'">决策树</span>
                                <span v-if="record.type === '5'">规则流</span>
                            </template>
                            <template v-if="column.key === 'status'">
                                <span>未变</span>
                            </template>
                            <template v-if="column.key === 'action'">
                                <a @click="btn_details(record)">详情</a>
                            </template>
                        </template>
                    </a-table>
                </div>
            </a-tab-pane>
        </a-tabs>

        <!-- 规则详情抽屉组件 -->
        <FlexDrawer
            :visible="detailDrawerVisible"
            @close="closeDetailDrawer"
            title="基线对比规则详情"
            :width="1100"
            :showCloseBtn="false"
        >
            <!-- 使用RuleCompareContent组件展示规则详情 -->
            <div class="rule_compareData">
                <RuleCompareContent v-if="ruleCompareData.ruleHisVO1.type!=='规则流'&&ruleCompareData.ruleHisVO1.type!=='决策树'&&ruleCompareData.ruleHisVO2.type!=='规则流'&&ruleCompareData.ruleHisVO2.type!=='决策树'" :ruleCompareData="ruleCompareData" :tabHeight="500"/>
                <div v-else>
                    <a-form-item style="font-weight: 700" label="规则内容：">
                      <span v-if="ruleCompareData?.ruleHisVO1?.edition && ruleCompareData?.ruleHisVO2?.edition">
                        (版本：{{ ruleCompareData?.ruleHisVO1?.edition || '' }}对比{{ ruleCompareData?.ruleHisVO2?.edition || '' }})
                      </span>
                    </a-form-item>
                    <div>版本{{ruleCompareData.ruleHisVO1.edition}}</div>
                    <div v-if="ruleCompareData.ruleHisVO1.type == '决策树'">
                        <DecisionTreeDetail :ruleInfo="ruleCompareData.ruleHisVO1" :baseLineFlag="true"/>
                    </div>
                    <div v-if="ruleCompareData.ruleHisVO1.type == '规则流'">
                        <RuleFlowDetail :ruleInfo="ruleCompareData.ruleHisVO1" :baseLineFlag="true"/>
                    </div>
                    <div style="margin-top: 20px">版本{{ruleCompareData.ruleHisVO2.edition}}</div>
                    <div v-if="ruleCompareData.ruleHisVO2.type == '决策树'">
                        <DecisionTreeDetail :ruleInfo="ruleCompareData.ruleHisVO1" :baseLineFlag="true"/>
                    </div>
                    <div v-if="ruleCompareData.ruleHisVO2.type == '规则流'">
                        <RuleFlowDetail :ruleInfo="ruleCompareData.ruleHisVO1" :baseLineFlag="true"/>
                    </div>
                </div>
            </div>
        </FlexDrawer>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, watchEffect, reactive, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { Card, Form, Row, Col, Tabs, Table, Button } from 'ant-design-vue';
import { snapShootCompare, getRuleHisListByUuids, getCopyDatails } from '@/api/baseline_Management';
import RuleCompareContent from '@/components/ruleDetailCom/RuleCompareContent.vue';
import useResize from '@/composables/useResize';
import TableSkeleton from '@/components/TableSkeleton.vue';
import RulePath from '@/components/ruleDetailCom/RulePath.vue';
import RuleFlowDetail from "@/businessComponents/ruleAudit/RuleFlowDetail.vue";
import DecisionTreeDetail from "@/businessComponents/ruleAudit/DecisionTreeDetail.vue";

const props = defineProps({
    compareParams: {
        type: Object,
        required: true
    }
});

const router = useRouter();

interface Rule {
    uuid: string;
    snapRuleHisUuid: string;
    ruleName: string;
    packageNameAll: string;
    type: string;
    status: string;
}

const snapComparedData_1 = ref<any>({});
const snapComparedData_2 = ref<any>({});
const curSnapUuid = ref<string>('');
const compareSnapUuid = ref<string>('');
const curSnapEdition = ref<string>('');
const compareSnapEdition = ref<string>('');
const addData = ref<Rule[]>([]);
const updateData = ref<Rule[]>([]);
const deleteData = ref<Rule[]>([]);
const noChangeRuleHisUuids = ref<string>('');
const noChangeRulesData = ref<Rule[]>([]);
const activeName = ref<string>('update');
const loading = ref<boolean>(false);

// 抽屉相关状态
const detailDrawerVisible = ref<boolean>(false);
const currentActiveFlag = ref<string>('');
const currentRuleHisUuid = ref<string>('');
const currentSnapRuleHisUuid = ref<string>('');

// 创建符合RuleCompareContent组件需要的数据结构
const ruleCompareData = reactive({
    ruleHisVO1: {
        edition: '',
        textDtl: '',
        dtl:'',
        dtlDrl:'',
        type: '',
        uuid: '',
        tableContentHis: '',
        validStatus: '',
        ruleUuid: ''
    },
    ruleHisVO2: {
        edition: '',
        textDtl: '',
        dtl:'',
        dtlDrl:'',
        type: '',
        uuid: '',
        tableContentHis: '',
        validStatus: '',
        ruleUuid: ''
    },
    uuid1: '',
    uuid2: ''
});

const columns = [
    { title: '序号', dataIndex: 'index', key: 'index',width:80,align:'center', customRender: ({ index }: { index: number }) => index + 1 },
    { title: '规则名称', dataIndex: 'ruleName', key: 'ruleName', ellipsis: true },
    { title: '规则路径', dataIndex: 'packageNameAll', key: 'packageNameAll',width:340 },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '变更状态', dataIndex: 'status', key: 'status' },
    { title: '操作', dataIndex: '', key: 'action',width:80 },
];
const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    showSizeChanger:true,
    isLoading :false,
    showQuickJumper:true,
    showTotal: (total: number) => `共 ${total} 条数据`,
});

const basePoint = ref();
const { scrollY } = useResize(basePoint);

const getSnapCompareResult = () => {
    const edition1 = snapComparedData_1.value.edition;
    const edition2 = snapComparedData_2.value.edition;
    if (Number(edition1) > Number(edition2)) {
        curSnapUuid.value = snapComparedData_1.value.uuid;
        compareSnapUuid.value = snapComparedData_2.value.uuid;
        curSnapEdition.value = edition1;
        compareSnapEdition.value = edition2;
    } else {
        curSnapUuid.value = snapComparedData_2.value.uuid;
        compareSnapUuid.value = snapComparedData_1.value.uuid;
        curSnapEdition.value = edition2;
        compareSnapEdition.value = edition1;
    }
    const pars = {
        curSnapUuid: curSnapUuid.value,
        compareSnapUuid: compareSnapUuid.value,
    };
    loading.value = true;
    snapShootCompare(pars).then((res) => {
        const data = res.data;
        pagination.value.total = res.data.totalCount;
        addData.value = data.add;
        updateData.value = data.update;
        deleteData.value = data.delete;
        noChangeRuleHisUuids.value = data.noChangeRuleHisUuids;
    }).finally(() => {
        loading.value = false;
    });
};

watchEffect(() => {
    if (props.compareParams) {
        snapComparedData_1.value = props.compareParams.compareData_1;
        snapComparedData_2.value = props.compareParams.compareData_2;
        getSnapCompareResult();
    }
});

const handleClick = (key: string | number) => {
    if (key === 'noChange' && noChangeRuleHisUuids.value !== '' && noChangeRulesData.value.length === 0) {
        loading.value = true;
        getRuleHisListByUuids({
            uuids: noChangeRuleHisUuids.value,
        }).then((res) => {
            noChangeRulesData.value = res.data.map((rule: Rule) => ({
                ...rule,
                snapRuleHisUuid: rule.uuid
            }));
            pagination.value.total = res.data.totalCount;
        }).finally(() => {
            loading.value = false;
        });
    }
};

// 获取规则比对数据
const snapCompareRule = async () => {
    try {
        // 清空数据
        ruleCompareData.ruleHisVO1 = {
            edition: '',
            textDtl: '',
            dtl:'',
            dtlDrl:'',
            type: '',
            uuid: '',
            tableContentHis: '',
            validStatus: '',
            ruleUuid: ''
        };
        ruleCompareData.ruleHisVO2 = {
            edition: '',
            textDtl: '',
            dtl:'',
            dtlDrl:'',
            type: '',
            uuid: '',
            tableContentHis: '',
            validStatus: '',
            ruleUuid: ''
        };

        const res = await getCopyDatails({
            uuid1: currentRuleHisUuid.value || '',
            uuid2: currentSnapRuleHisUuid.value || '',
        });

        if (res && res.data) {

            // 更新组件所需数据
            Object.assign(ruleCompareData.ruleHisVO1, res.data.ruleHisVO1 || {});
            Object.assign(ruleCompareData.ruleHisVO2, res.data.ruleHisVO2 || {});

            // 保证两边都有类型信息
            if (!ruleCompareData.ruleHisVO1.type && ruleCompareData.ruleHisVO2.type) {
                ruleCompareData.ruleHisVO1.type = ruleCompareData.ruleHisVO2.type;
            }
            if (!ruleCompareData.ruleHisVO2.type && ruleCompareData.ruleHisVO1.type) {
                ruleCompareData.ruleHisVO2.type = ruleCompareData.ruleHisVO1.type;
            }
        }
    } catch (error) {
        console.error('获取规则比对数据失败:', error);
    }
};

// 关闭详情抽屉
const closeDetailDrawer = () => {
    detailDrawerVisible.value = false;
};

const btn_details = (ruleHisData: any) => {
    // 设置当前选中的规则状态
    currentActiveFlag.value = activeName.value;
    currentRuleHisUuid.value = activeName.value === 'delete' ? '' : ruleHisData.uuid;
    currentSnapRuleHisUuid.value = ruleHisData.snapRuleHisUuid;

    // 设置uuid值用于组件内部处理
    ruleCompareData.uuid1 = currentRuleHisUuid.value || '';
    ruleCompareData.uuid2 = currentSnapRuleHisUuid.value || '';

    // 加载规则比对数据
    snapCompareRule();

    // 显示抽屉
    detailDrawerVisible.value = true;
};
</script>

<style lang="scss" scoped>
p {
    font-weight: bold;
}

.rule_compareData {
    width: 100%;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
}

.baseline-form {
    :deep(.ant-form-item) {
        .ant-form-item-label > label {
            font-weight: 600;
            font-size: 14px;
        }
    }
}
</style>
